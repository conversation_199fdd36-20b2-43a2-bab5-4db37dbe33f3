repos:
  # Prettier for code formatting
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.0.3
    hooks:
      - id: prettier
        name: 🎨 Format with Prettier
        files: \.(ts|tsx|js|jsx|json|md|yml|yaml)$
        exclude: |
          (?x)^(
            .*\.min\.(js|css)$|
            package-lock\.json$|
            \.expo/.*|
            android/.*|
            ios/.*|
            web-build/.*|
            reports/.*
          )$

  # ESLint for TypeScript/JavaScript linting
  - repo: https://github.com/pre-commit/mirrors-eslint
    rev: v8.50.0
    hooks:
      - id: eslint
        name: 🔍 Lint with ESLint
        files: \.(ts|tsx|js|jsx)$
        exclude: |
          (?x)^(
            \.expo/.*|
            android/.*|
            ios/.*|
            web-build/.*|
            node_modules/.*|
            scripts/.*\.js$
          )$
        additional_dependencies:
          - '@typescript-eslint/eslint-plugin@^6.0.0'
          - '@typescript-eslint/parser@^6.0.0'
          - 'eslint-plugin-react@^7.33.0'
          - 'eslint-plugin-react-hooks@^4.6.0'
          - 'eslint-plugin-react-native@^4.1.0'
          - 'eslint-plugin-import@^2.28.0'
          - '@expo/eslint-config@^0.1.0'

  # TypeScript type checking
  - repo: local
    hooks:
      - id: typescript-check
        name: 🔧 TypeScript Type Check
        entry: npx tsc --noEmit
        language: system
        files: \.(ts|tsx)$
        pass_filenames: false

  # WeRoomies-specific design system compliance
  - repo: local
    hooks:
      - id: design-system-check
        name: 🎨 Design System Compliance
        entry: bash -c 'if grep -r "backgroundColor.*#[0-9A-Fa-f]" src/ --include="*.ts" --include="*.tsx" | head -5; then echo "❌ Hardcoded colors found! Use theme tokens instead."; exit 1; else echo "✅ Design system compliance OK"; fi'
        language: system
        files: \.(ts|tsx)$
        pass_filenames: false

  # Security checks
  - repo: https://github.com/Yelp/detect-secrets
    rev: v1.4.0
    hooks:
      - id: detect-secrets
        name: 🔒 Detect Secrets
        args: ['--baseline', '.secrets.baseline']
        exclude: |
          (?x)^(
            \.expo/.*|
            android/.*|
            ios/.*|
            package-lock\.json$|
            \.secrets\.baseline$
          )$

  # General file checks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
        name: 🧹 Remove Trailing Whitespace
        exclude: |
          (?x)^(
            .*\.md$|
            \.expo/.*|
            android/.*|
            ios/.*
          )$
      
      - id: end-of-file-fixer
        name: 📝 Fix End of Files
        exclude: |
          (?x)^(
            \.expo/.*|
            android/.*|
            ios/.*|
            \.png$|
            \.jpg$|
            \.jpeg$|
            \.gif$
          )$
      
      - id: check-yaml
        name: ✅ Check YAML Syntax
        files: \.(yml|yaml)$
        exclude: |
          (?x)^(
            \.expo/.*|
            android/.*|
            ios/.*
          )$
      
      - id: check-json
        name: ✅ Check JSON Syntax
        files: \.json$
        exclude: |
          (?x)^(
            package-lock\.json$|
            \.expo/.*|
            android/.*|
            ios/.*
          )$
      
      - id: check-merge-conflict
        name: 🔀 Check for Merge Conflicts
      
      - id: check-added-large-files
        name: 📦 Check for Large Files
        args: ['--maxkb=1000']
        exclude: |
          (?x)^(
            \.expo/.*|
            android/.*|
            ios/.*|
            assets/.*\.(png|jpg|jpeg|gif|mp4|mov)$
          )$

  # WeRoomies-specific React Native checks
  - repo: local
    hooks:
      - id: react-native-console-check
        name: 🚫 Check for console.log
        entry: bash -c 'if grep -r "console\.log" src/ --include="*.ts" --include="*.tsx" | head -5; then echo "❌ console.log statements found! Remove before committing."; exit 1; else echo "✅ No console.log statements found"; fi'
        language: system
        files: \.(ts|tsx)$
        pass_filenames: false

      - id: react-native-any-type-check
        name: 🔍 Check for any types
        entry: bash -c 'any_count=$(grep -r ": any" src/ --include="*.ts" --include="*.tsx" | wc -l); if [ $any_count -gt 10 ]; then echo "❌ Too many any types found ($any_count). Please add proper typing."; exit 1; else echo "✅ Any type usage acceptable ($any_count)"; fi'
        language: system
        files: \.(ts|tsx)$
        pass_filenames: false

      - id: expo-compatibility-check
        name: 📱 Expo Compatibility Check
        entry: bash -c 'if npx expo doctor --fix-dependencies=false > /dev/null 2>&1; then echo "✅ Expo compatibility OK"; else echo "⚠️ Expo compatibility issues detected. Run: npx expo doctor"; fi'
        language: system
        files: package\.json$
        pass_filenames: false

# Configuration
default_stages: [commit]
fail_fast: false
minimum_pre_commit_version: 3.0.0 