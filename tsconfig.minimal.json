{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": false, "noImplicitAny": false, "skipLibCheck": true, "allowJs": true, "checkJs": false, "noEmit": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "jsx": "react-native", "lib": ["dom", "esnext"], "moduleResolution": "node", "noUnusedLocals": false, "noUnusedParameters": false, "resolveJsonModule": true}, "include": ["src/app/tabs/index.tsx"], "exclude": ["**/*"]}