# 🚀 Production Deployment Guide

## Overview

This guide walks you through deploying WeRoomies to production with zero-cost
verification, ensuring optimal performance and security.

## Pre-Deployment Checklist

### ✅ Code Quality

- [ ] All tests passing (`npm test`)
- [ ] Linting clean (`npm run lint`)
- [ ] TypeScript compilation successful (`npm run type-check`)
- [ ] No console errors in development
- [ ] Performance optimizations applied

### ✅ Environment Configuration

- [ ] Production Supabase project created
- [ ] Environment variables configured
- [ ] SSL certificates installed
- [ ] Domain names configured
- [ ] CDN setup (optional)

### ✅ Database Setup

- [ ] Production database migrated
- [ ] RLS policies enabled
- [ ] Storage buckets configured
- [ ] Backup strategy implemented

## 1. Supabase Production Setup

### Create Production Project

```bash
# 1. Create new Supabase project at https://supabase.com
# 2. Note down your project URL and keys
# 3. Configure your production database
```

### Database Migration

```sql
-- Run all migrations in order:
-- 1. Initial schema
-- 2. RLS policies
-- 3. Storage policies
-- 4. Functions and triggers
```

### Storage Configuration

```sql
-- Create storage buckets
INSERT INTO storage.buckets (id, name, public) VALUES
('avatars', 'avatars', true),
('documents', 'documents', false),
('listings', 'listings', true);

-- Set up RLS policies for storage
CREATE POLICY "Avatar images are publicly accessible" ON storage.objects
FOR SELECT USING (bucket_id = 'avatars');

CREATE POLICY "Users can upload their own avatar" ON storage.objects
FOR INSERT WITH CHECK (bucket_id = 'avatars' AND auth.uid()::text = (storage.foldername(name))[1]);
```

## 2. Environment Configuration

### Production Environment Variables

Copy `env.production.template` to `.env.local` and configure:

```env
# Required Production Values
EXPO_PUBLIC_SUPABASE_URL=https://your-prod-project.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_production_anon_key
EXPO_SUPABASE_SERVER_ROLE=your_production_service_role

# Optional Services (for enhanced features)
EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_key
EXPO_PUBLIC_OPENAI_API_KEY=sk-your_openai_key
```

### App Configuration

Update `app.json` for production:

```json
{
  "expo": {
    "name": "WeRoomies",
    "version": "1.0.0",
    "ios": {
      "bundleIdentifier": "com.weroomies.app",
      "buildNumber": "1"
    },
    "android": {
      "package": "com.weroomies.app",
      "versionCode": 1
    }
  }
}
```

## 3. Mobile App Deployment

### iOS App Store

1. **Prepare for App Store**

```bash
# Install EAS CLI
npm install -g @expo/eas-cli

# Configure EAS
eas build:configure

# Create production build
eas build --platform ios --profile production
```

2. **App Store Connect Setup**

- Create app in App Store Connect
- Upload build using Transporter or EAS
- Configure app metadata
- Submit for review

### Android Play Store

1. **Create Production Build**

```bash
# Create Android production build
eas build --platform android --profile production

# Or create AAB for Play Store
eas build --platform android --profile production --format aab
```

2. **Play Console Setup**

- Create app in Google Play Console
- Upload AAB/APK
- Configure store listing
- Submit for review

## 4. Web Deployment

### Build for Web

```bash
# Create web build
npm run build:web

# Output will be in dist/ directory
```

### Deploy to Vercel

```bash
# Install Vercel CLI
npm install -g vercel

# Deploy
vercel --prod

# Configure custom domain
vercel domains add weroomies.com
```

### Deploy to Netlify

```bash
# Build and deploy
npm run build:web
netlify deploy --prod --dir=dist
```

## 5. Domain and SSL Setup

### Custom Domain Configuration

```bash
# Configure DNS records
weroomies.com    A     192.168.1.1
www.weroomies.com CNAME weroomies.com
api.weroomies.com CNAME your-supabase-project.supabase.co
```

### SSL Certificate

- Use Let's Encrypt for free SSL
- Configure automatic renewal
- Update app.json with HTTPS URLs

## 6. Monitoring and Analytics

### Error Tracking with Sentry

```bash
# Install Sentry
npx @sentry/wizard -i reactNative

# Configure in app.json
"plugins": [
  ["@sentry/react-native/expo", {
    "organization": "your-org",
    "project": "weroomies"
  }]
]
```

### Performance Monitoring

```typescript
// Add to App.tsx
import * as Sentry from '@sentry/react-native';

Sentry.init({
  dsn: 'your-sentry-dsn',
  environment: 'production',
});
```

## 7. Zero-Cost Verification Setup

### Manual Review Workflow

```sql
-- Create admin notification system
CREATE OR REPLACE FUNCTION notify_admin_verification()
RETURNS TRIGGER AS $$
BEGIN
  -- Send email to admin when verification needed
  PERFORM pg_notify('admin_verification',
    json_build_object('user_id', NEW.user_id, 'type', 'document_review')::text
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

### Cost Savings Dashboard

```typescript
// Track cost savings in production
const trackVerificationSavings = async (userId: string) => {
  await supabase.from('cost_savings_analytics').insert({
    user_id: userId,
    identity_verification_saved: 7,
    background_check_saved: 35,
    reference_verification_saved: 15,
    total_saved: 57,
    date: new Date().toISOString(),
  });
};
```

## 8. Security Hardening

### Supabase Security

```sql
-- Enable RLS on all tables
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE verification_status ENABLE ROW LEVEL SECURITY;

-- Create security policies
CREATE POLICY "Users can only see own profile" ON user_profiles
FOR ALL USING (auth.uid() = user_id);
```

### API Security

- Enable CORS restrictions
- Configure rate limiting
- Set up request validation
- Enable audit logging

## 9. Backup and Recovery

### Database Backups

```bash
# Automated daily backups
pg_dump $DATABASE_URL > backup_$(date +%Y%m%d).sql

# Store in secure cloud storage
aws s3 cp backup_$(date +%Y%m%d).sql s3://your-backup-bucket/
```

### Disaster Recovery Plan

1. Database restoration procedures
2. File storage recovery
3. DNS failover configuration
4. Emergency contact procedures

## 10. Performance Optimization

### Image Optimization

```typescript
// Configure image optimization
const imageConfig = {
  quality: 80,
  format: 'webp',
  resize: {
    width: 800,
    height: 600,
  },
};
```

### Caching Strategy

```typescript
// Implement caching layers
const cacheConfig = {
  ttl: 3600, // 1 hour
  maxSize: 100, // 100MB
  strategy: 'lru',
};
```

## 11. Post-Deployment Verification

### Health Checks

```bash
# Test critical endpoints
curl https://api.weroomies.com/health
curl https://weroomies.com/api/status

# Verify SSL certificates
openssl s_client -connect weroomies.com:443
```

### User Acceptance Testing

- [ ] Registration flow works
- [ ] Image upload functions
- [ ] Verification system active
- [ ] Payment processing (if enabled)
- [ ] Mobile app store listings live

## 12. Maintenance and Updates

### Regular Maintenance

- Weekly security updates
- Monthly dependency updates
- Quarterly performance reviews
- Annual security audits

### Update Deployment

```bash
# Deploy updates with zero downtime
eas update --branch production --message "Bug fixes and improvements"
```

## 13. Cost Monitoring

### Zero-Cost Verification Metrics

Track monthly savings:

- Identity verification: $7 × users = $X saved
- Background checks: $35 × users = $Y saved
- Reference verification: $15 × users = $Z saved
- **Total monthly savings: $X + $Y + $Z**

### Infrastructure Costs

Monitor actual costs:

- Supabase usage
- CDN bandwidth
- Storage costs
- Third-party integrations

## Support and Troubleshooting

### Common Issues

1. **Build failures**: Check environment variables
2. **Database connection**: Verify Supabase configuration
3. **Image upload issues**: Check storage policies
4. **Authentication problems**: Verify JWT configuration

### Emergency Contacts

- Technical Lead: <EMAIL>
- DevOps: <EMAIL>
- Security: <EMAIL>

---

## 🎯 Production Success Metrics

### Performance Targets

- App load time: < 3 seconds
- Image upload: < 10 seconds
- Registration completion: < 5 minutes
- 99.9% uptime SLA

### Cost Optimization Goals

- $0 verification costs per user
- < $50/month infrastructure costs
- 90%+ cost savings vs traditional platforms

### User Experience Goals

- > 4.5 star app store rating
- < 15% registration abandonment
- > 80% verification completion rate

**🚀 Ready for Production!** Your zero-cost verification platform is now live
and saving money while providing excellent user experience.
