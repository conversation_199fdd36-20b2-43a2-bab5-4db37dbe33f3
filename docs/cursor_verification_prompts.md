# Cursor Prompts: Zero-Cost Verification System Implementation

## 🎯 Project Overview

Replace expensive verification APIs (Onfido $7, Checkr $35, etc.) with free
alternatives using existing Supabase infrastructure. Target: $0 monthly
verification costs while maintaining functionality.

## 📋 Implementation Roles

### **Role 1: Free Verification Service Architecture**

```
You are implementing a zero-cost verification service to replace expensive APIs.

**CONTEXT:**
- Existing Supabase authentication system
- Current verification tables already exist (analyze existing schema)
- Replace: Onfido ($7/check), Checkr ($35/check), expensive phone/email verification
- Target: $0 cost using free tiers and manual processes

**TASK 1: Analyze Existing Database**
1. Examine current verification-related tables in the Supabase database
2. Identify existing verification types and status enums
3. Map current verification flow and identify integration points
4. Document existing verification service structure

**TASK 2: Create Free Verification Service**
Create `src/services/freeVerificationService.ts` that:
1. Uses Supabase Auth for phone verification (free tier)
2. Implements manual identity verification with document upload
3. Uses Google Maps API for address verification (40k free/month)
4. Integrates public APIs for background checks
5. Maintains compatibility with existing database schema

**REQUIREMENTS:**
- Work with existing database tables (don't create new ones)
- Use Supabase storage for document uploads
- Implement proper error handling
- Follow existing service patterns in the codebase
- Ensure TypeScript compatibility

**OUTPUT:** Complete service file with all verification methods
```

### **Role 2: Supabase Auth Phone Verification**

```
You are implementing FREE phone verification using Supabase Auth to replace expensive SMS services.

**CONTEXT:**
- Existing Supabase authentication system
- Current app uses expensive phone verification
- Replace with Supabase built-in phone auth (generous free tier)

**TASK 1: Implement Supabase Phone Verification**
1. Update existing phone verification in `src/services/` to use Supabase Auth
2. Create phone verification flow using `supabase.auth.signInWithOtp()`
3. Handle OTP verification with `supabase.auth.verifyOtp()`
4. Update existing verification tables with results
5. Maintain backward compatibility with current verification flow

**TASK 2: Update Verification UI Components**
1. Find existing phone verification components
2. Update to use new Supabase phone verification service
3. Add proper loading states and error handling
4. Ensure consistent UI with existing verification screens

**REQUIREMENTS:**
- Use existing authentication context
- Work with current verification database schema
- Maintain existing user experience
- Add proper TypeScript interfaces
- Follow existing component patterns

**OUTPUT:** Updated phone verification service and components
```

### **Role 3: Manual Identity Verification System**

```
You are implementing a FREE manual identity verification system to replace expensive automated services (Onfido $7/check).

**CONTEXT:**
- Replace expensive identity verification APIs
- Use manual review process for $0 cost
- Store documents in Supabase storage (free 1GB)
- Admin dashboard for manual verification review

**TASK 1: Document Upload System**
1. Create document upload component using Supabase storage
2. Update existing identity verification service to handle manual review
3. Create document types and validation
4. Store verification requests in existing database tables
5. Implement proper file handling and security

**TASK 2: Admin Review Dashboard**
1. Create admin interface for document review
2. Implement approve/reject functionality
3. Add notification system for status changes
4. Create admin authentication and permissions
5. Build document viewer with zoom/annotation capabilities

**TASK 3: Integration with Existing System**
1. Update existing verification status enums to include manual review states
2. Ensure compatibility with current verification flow
3. Add proper status tracking and notifications
4. Maintain existing verification completion logic

**REQUIREMENTS:**
- Use Supabase storage for documents
- Work with existing verification tables
- Implement proper security for admin access
- Follow existing UI/UX patterns
- Add comprehensive error handling

**OUTPUT:** Complete manual verification system with admin dashboard
```

### **Role 4: Free Address & Email Verification**

```
You are implementing FREE address and email verification to replace paid services.

**CONTEXT:**
- Replace expensive address verification APIs
- Use Google Maps Geocoding API (40k free requests/month)
- Implement DNS-based email verification
- Fallback to OpenStreetMap for address verification

**TASK 1: Free Address Verification**
1. Create address verification service using Google Maps API
2. Implement OpenStreetMap fallback for over-limit cases
3. Add address validation and formatting
4. Store results in existing verification tables
5. Add confidence scoring for address matches

**TASK 2: Free Email Verification**
1. Implement DNS/MX record checking for email validation
2. Create email verification via Supabase Auth magic links
3. Add disposable email detection
4. Update existing email verification flow
5. Maintain compatibility with current user registration

**TASK 3: Integration & Optimization**
1. Add rate limiting for API calls to stay within free tiers
2. Create caching mechanism for repeated verification requests
3. Update existing verification components to use new services
4. Add proper analytics and monitoring

**REQUIREMENTS:**
- Stay within Google Maps free tier (40k requests/month)
- Use existing email verification tables
- Implement proper caching and rate limiting
- Follow existing service architecture
- Add comprehensive logging

**OUTPUT:** Complete address and email verification services
```

### **Role 5: Free Background Check System**

```
You are implementing FREE background checks using public APIs and manual processes to replace expensive services (Checkr $35/check).

**CONTEXT:**
- Replace expensive background check APIs
- Use free government APIs and public records
- Implement social media verification
- Create manual reference check system

**TASK 1: Public Records Integration**
1. Integrate free government APIs (sex offender registry, court records)
2. Create social media profile verification workflow
3. Implement manual reference check via email
4. Store results in existing background check tables
5. Add comprehensive reporting system

**TASK 2: Reference Check System**
1. Create email-based reference check workflow
2. Build reference response forms and tracking
3. Add automated follow-up system
4. Implement reference verification scoring
5. Create reference analytics and insights

**TASK 3: Background Check Dashboard**
1. Create comprehensive background check results display
2. Add risk assessment scoring
3. Implement manual review workflow for flagged results
4. Create background check history and audit trail
5. Add export functionality for results

**REQUIREMENTS:**
- Use only free public APIs
- Work with existing background check tables
- Implement proper data privacy and security
- Follow existing verification flow patterns
- Add comprehensive audit logging

**OUTPUT:** Complete free background check system
```

### **Role 6: Admin Dashboard & Analytics**

```
You are creating an admin dashboard for managing the free verification system and monitoring costs/usage.

**CONTEXT:**
- Need admin interface for manual verification reviews
- Monitor free tier usage to avoid overages
- Analytics dashboard for verification metrics
- Cost tracking to ensure $0 monthly verification costs

**TASK 1: Verification Management Dashboard**
1. Create admin authentication and role-based access
2. Build pending verification queue interface
3. Implement bulk actions for verification management
4. Add verification history and audit trails
5. Create user verification status overview

**TASK 2: Usage Monitoring & Analytics**
1. Track Google Maps API usage (stay under 40k/month)
2. Monitor Supabase storage usage for documents
3. Create verification completion rate analytics
4. Add performance metrics and response time tracking
5. Implement alerts for approaching free tier limits

**TASK 3: Cost Optimization Dashboard**
1. Display current month verification costs ($0 target)
2. Show savings compared to paid services
3. Track efficiency metrics (manual review time, etc.)
4. Create optimization recommendations
5. Add budget alerts and usage projections

**REQUIREMENTS:**
- Secure admin authentication
- Real-time usage monitoring
- Integration with existing verification tables
- Mobile-responsive design
- Comprehensive analytics and reporting

**OUTPUT:** Complete admin dashboard with analytics and monitoring
```

## 🔧 Implementation Sequence

### **Phase 1: Foundation (Week 1)**

1. **Role 1**: Analyze existing database and create free verification service
   architecture
2. **Role 2**: Implement Supabase phone verification
3. **Role 4**: Set up free email verification

### **Phase 2: Core Features (Week 2)**

1. **Role 3**: Build manual identity verification system
2. **Role 4**: Complete address verification implementation
3. **Role 5**: Create basic background check system

### **Phase 3: Advanced Features (Week 3)**

1. **Role 5**: Complete background check with reference system
2. **Role 6**: Build admin dashboard
3. **All Roles**: Integration testing and optimization

### **Phase 4: Polish & Monitoring (Week 4)**

1. **Role 6**: Complete analytics and monitoring
2. **All Roles**: Performance optimization
3. **All Roles**: Documentation and training

## 📊 Success Metrics

### **Cost Targets**

- Monthly verification cost: **$0** (vs. $4,245 with paid services)
- Google Maps API: Stay under 40k requests/month (free)
- Supabase storage: Stay under 1GB for documents (free)
- Phone verification: Use Supabase Auth free tier

### **Performance Targets**

- Manual verification review: <24 hours
- Automated verifications: <30 seconds
- Address verification: <5 seconds
- Email verification: <10 seconds

### **Quality Targets**

- Address verification accuracy: >95%
- Manual verification consistency: >98%
- False positive rate: <2%
- User satisfaction: >4.5/5

## 🛡️ Security & Compliance

### **Data Protection**

- Encrypt all stored documents
- Implement proper access controls
- Regular security audits
- GDPR/CCPA compliance

### **Privacy Requirements**

- Minimal data collection
- Clear user consent
- Data retention policies
- Right to deletion

## 📱 Mobile Integration

### **React Native Components**

- Document camera integration
- Offline verification queue
- Real-time status updates
- Push notifications for status changes

### **User Experience**

- Smooth verification flows
- Clear progress indicators
- Helpful error messages
- Accessibility compliance

## 🔄 Maintenance & Updates

### **Monitoring**

- API usage tracking
- Performance monitoring
- Error rate tracking
- User feedback collection

### **Optimization**

- Regular performance reviews
- API efficiency improvements
- Database query optimization
- User experience enhancements

---

**Remember**: Each role should work with EXISTING database tables and follow
current architectural patterns. The goal is to replace expensive services with
free alternatives while maintaining the same functionality and user experience.
