# User Journey Diagram for AI-Powered Roommate Matching App

```mermaid
flowchart TD
    %% Main User Journey Stages
    Start([App Download]) --> Onboarding
    Onboarding --> ProfileCreation
    ProfileCreation --> Verification
    Verification --> MatchingPhase
    MatchingPhase --> Communication
    Communication --> Agreement
    Agreement --> Payment
    Payment --> Living
    Living --> Feedback

    %% Detailed Breakdown of Each Stage

    %% Onboarding Details
    Onboarding --> |Choose Login Method| SocialLogin[Social Login]
    Onboarding --> |Enter Email & Password| EmailSignup[Email Signup]
    SocialLogin --> AcceptTerms[Accept Terms & Privacy Policy]
    EmailSignup --> Verification[Email Verification]
    Verification --> AcceptTerms
    AcceptTerms --> ProfileCreation

    %% Profile Creation Details
    ProfileCreation --> BasicInfo[Basic Personal Information]
    BasicInfo --> HousingPrefs[Housing Preferences]
    HousingPrefs --> LifestyleProfile[Lifestyle & Habits Profile]
    LifestyleProfile --> AITags[AI-Suggested Lifestyle Tags]
    AITags --> MediaUpload[Upload Photos & Video Intro]
    MediaUpload --> Verification

    %% Verification Details
    Verification --> IDVerify[Government ID Upload]
    IDVerify --> SelfieVerify[Selfie Verification]
    SelfieVerify --> OptionalBG[Optional Background Check]
    OptionalBG --> VerificationBadge[Receive Verification Badge]
    VerificationBadge --> MatchingPhase

    %% Matching Phase Details
    MatchingPhase --> AIRecommendations[AI-Generated Recommendations]
    AIRecommendations --> BrowseMatches[Browse Potential Roommates]
    BrowseMatches --> FilterResults[Apply Custom Filters]
    FilterResults --> SwipeUI[Swipe Through Matches]
    SwipeUI --> ViewProfiles[View Detailed Profiles]
    ViewProfiles --> SaveFavorites[Save Favorites to Shortlist]
    SaveFavorites --> Communication

    %% Communication Details
    Communication --> SendRequest[Send Match Request]
    SendRequest --> WaitResponse[Wait for Response]
    WaitResponse --> MatchConfirmed[Match Confirmed]
    MatchConfirmed --> Messaging[In-App Messaging]
    Messaging --> ScheduleMeet[Schedule Meetup]
    ScheduleMeet --> VirtualTour[Virtual Room Tour]
    VirtualTour --> InPersonMeet[In-Person Meeting]
    InPersonMeet --> MutualInterest[Confirm Mutual Interest]
    MutualInterest --> Agreement

    %% Agreement Details
    Agreement --> LeaseTemplate[Access Digital Lease Templates]
    LeaseTemplate --> CustomizeTerms[Customize Agreement Terms]
    CustomizeTerms --> DigitalSign[Digital Signatures]
    DigitalSign --> StoreContract[Store Agreement In-App]
    StoreContract --> Payment

    %% Payment Details
    Payment --> PaymentMethod[Connect Payment Methods]
    PaymentMethod --> RentSplitting[Configure Rent Splitting]
    RentSplitting --> RecurringPayments[Set Up Recurring Payments]
    RecurringPayments --> BillSchedule[Establish Bill Payment Schedule]
    BillSchedule --> Living

    %% Living Together Details
    Living --> MoveInDate[Schedule Move-In Date]
    MoveInDate --> ChecklistComplete[Complete Move-In Checklist]
    ChecklistComplete --> HouseholdCalendar[Shared Household Calendar]
    HouseholdCalendar --> ChoreTracking[Chore Scheduling & Tracking]
    ChoreTracking --> ExpenseSplitting[Ongoing Expense Splitting]
    ExpenseSplitting --> ConflictResolution[Conflict Resolution Tools]
    ConflictResolution --> Feedback

    %% Feedback Details
    Feedback --> RateRoommate[Rate Roommate Experience]
    RateRoommate --> AiFeedback[Provide AI Feedback]
    AiFeedback --> BuildTrust[Build Trust Score]
    BuildTrust --> AppFeedback[Rate App Experience]
    AppFeedback --> Referral[Refer Friends]
    Referral --> PremiumFeatures[Access Premium Features]

    %% Special Circumstances
    Living --> SafetySupport[Safety & Support]
    SafetySupport --> ReportBehavior[Report Inappropriate Behavior]
    ReportBehavior --> BlockUser[Block User]
    Living --> MoveOut[Move-Out Process]
    MoveOut --> FinalSettlement[Final Payment Settlement]
    FinalSettlement --> CloseRelationship[Close Roommate Relationship]
    CloseRelationship --> NewSearch[Start New Roommate Search]
    NewSearch --> MatchingPhase

    %% Community Features
    Communication --> JoinGroups[Join Interest Groups]
    JoinGroups --> ForumParticipation[Participate in Forums]
    ForumParticipation --> AttendEvents[Attend Local Meetups]

    %% Premium Path
    MatchingPhase --> UpgradeToPremium[Upgrade to Premium]
    UpgradeToPremium --> AdvancedMatching[Access Advanced Matching]
    AdvancedMatching --> PrioritySupport[Priority Customer Support]
    PrioritySupport --> ExclusiveListings[View Exclusive Listings]

    %% Visual Styling
    classDef primary fill:#4285F4,color:white,stroke-width:2px
    classDef secondary fill:#34A853,color:white,stroke-width:2px
    classDef tertiary fill:#FBBC05,color:white,stroke-width:2px
    classDef quaternary fill:#EA4335,color:white,stroke-width:2px
    classDef default fill:#f9f9f9,stroke:#333,stroke-width:1px

    class Start,Onboarding,ProfileCreation,Verification,MatchingPhase,Communication,Agreement,Payment,Living,Feedback primary
    class SocialLogin,EmailSignup,BasicInfo,AIRecommendations,SendRequest,LeaseTemplate,PaymentMethod,MoveInDate,RateRoommate secondary
    class VerificationBadge,SaveFavorites,MutualInterest,DigitalSign,RecurringPayments,ConflictResolution,BuildTrust tertiary
    class OptionalBG,InPersonMeet,SafetySupport,MoveOut,BlockUser,UpgradeToPremium quaternary
```

## Key Journey Phases Explanation

1. **Onboarding & Registration** →

   - User enters the app and chooses their login method
   - Completes registration and accepts terms
   - Receives email verification if needed

2. **Profile Creation** →

   - User provides basic personal information
   - Enters housing preferences and budget
   - Creates lifestyle profile with habits and preferences
   - Receives AI-suggested lifestyle tags
   - Uploads photos and optional video introduction

3. **Verification** →

   - User completes identity verification
   - Uploads government ID and selfie verification
   - Optionally enrolls in background check
   - Receives verification badge upon completion

4. **AI Matching** →

   - User receives AI-generated roommate recommendations
   - Browses potential matches with compatibility scores
   - Applies custom filters and preferences
   - Uses swipe-based UI to quickly evaluate matches
   - Saves favorites to a shortlist

5. **Communication** →

   - User sends match request to potential roommates
   - Receives match confirmation and begins messaging
   - Schedules meetups (virtual or in-person)
   - Participates in virtual room tours when applicable
   - Confirms mutual interest after meeting

6. **Agreement** →

   - Users access digital lease templates
   - Customize agreement terms together
   - Complete digital signatures
   - Store finalized agreement in the app

7. **Payment Setup** →

   - Connect payment methods
   - Configure rent splitting between roommates
   - Set up recurring payment schedule
   - Establish bill payment protocols

8. **Living Together** →

   - Schedule move-in date and complete checklist
   - Use shared household calendar
   - Track chores and responsibilities
   - Split expenses through the app
   - Access conflict resolution tools as needed

9. **Feedback & Rating** →

   - Rate roommate experience
   - Provide feedback to improve AI matching
   - Build personal trust score
   - Rate app experience and refer friends

10. **Special Paths** →
    - Safety & support features
    - Move-out process and relationship closure
    - Community engagement features
    - Premium upgrade options
