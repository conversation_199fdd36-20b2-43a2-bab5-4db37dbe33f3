# Authentication Hook Usage Guide

## 🎯 **Overview**

This guide explains the correct usage of authentication hooks in the
RoommatesGate app to prevent `authState` property errors and ensure consistent
auth state access.

## 🔧 **Available Auth Hooks**

### **1. useAuthCompat() - Recommended for Most Components**

```typescript
import { useAuthCompat } from '@hooks/useAuthCompat';

const MyComponent = () => {
  const { authState } = useAuthCompat();
  const user = authState.user;

  // ✅ CORRECT: authState is properly provided
  if (authState.isAuthenticated) {
    // Use user data
  }
};
```

**Provides:**

- `authState.user` - Current user object
- `authState.isAuthenticated` - Authentication status
- `authState.isLoading` - Loading state
- `authState.error` - Error state
- Full compatibility with legacy auth patterns

### **2. useAuth() from AuthContext - For New Components**

```typescript
import { useAuth } from '@context/AuthContext';

const MyComponent = () => {
  const { user, isAuthenticated, state, actions } = useAuth();

  // ✅ CORRECT: Direct access to auth properties
  if (isAuthenticated) {
    // Use user data directly
  }

  // OR use state object
  if (state.isAuthenticated) {
    // Use state.user
  }
};
```

**Provides:**

- Direct auth properties: `user`, `isAuthenticated`, `isLoading`
- `state` object with auth state
- `actions` object with auth methods
- `authState` compatibility property

### **3. useAuthUnified() - For Internal Use**

```typescript
import { useAuthUnified } from '@hooks/useAuthUnified';

// ⚠️ INTERNAL USE ONLY - Use useAuthCompat or useAuth instead
```

## 🚫 **Common Mistakes to Avoid**

### **❌ Wrong: Accessing authState without proper destructuring**

```typescript
const { state, actions } = useAuth();
// ❌ This will fail: authState is not destructured
if (authState.user) { ... }
```

### **✅ Correct: Proper destructuring**

```typescript
const { state, actions, authState, user } = useAuth();
// ✅ Now authState is available
if (authState.user) { ... }

// OR use direct properties
if (user) { ... }
```

### **❌ Wrong: Mixing auth hook patterns**

```typescript
import { useAuth } from '@context/AuthContext';
import { useAuthCompat } from '@hooks/useAuthCompat';

// ❌ Don't use multiple auth hooks in same component
const auth1 = useAuth();
const auth2 = useAuthCompat();
```

### **✅ Correct: Use one auth hook consistently**

```typescript
import { useAuthCompat } from '@hooks/useAuthCompat';

const { authState } = useAuthCompat();
```

## 📋 **Migration Patterns**

### **For Components Using authState Property**

If your component has:

```typescript
const { authState } = useAuth();
```

**Option 1: Switch to useAuthCompat**

```typescript
import { useAuthCompat } from '@hooks/useAuthCompat';
const { authState } = useAuthCompat();
```

**Option 2: Update destructuring**

```typescript
import { useAuth } from '@context/AuthContext';
const { authState, user, isAuthenticated } = useAuth();
```

**Option 3: Use direct properties**

```typescript
import { useAuth } from '@context/AuthContext';
const { user, isAuthenticated } = useAuth();
// Replace authState.user with user
// Replace authState.isAuthenticated with isAuthenticated
```

## 🎯 **Best Practices**

### **1. Choose the Right Hook**

- **New components**: Use `useAuth()` from AuthContext
- **Legacy components**: Use `useAuthCompat()`
- **Components with authState pattern**: Use `useAuthCompat()`

### **2. Consistent Destructuring**

```typescript
// ✅ GOOD: Destructure what you need
const { user, isAuthenticated } = useAuth();

// ✅ GOOD: Use authState pattern consistently
const { authState } = useAuthCompat();

// ❌ BAD: Partial destructuring then accessing non-destructured properties
const { state } = useAuth();
if (authState.user) { ... } // authState not destructured!
```

### **3. Type Safety**

```typescript
import type { User } from '@supabase/supabase-js';

const { user } = useAuth();
// ✅ TypeScript will help catch property access errors
```

## 🔍 **Debugging Auth Issues**

### **Error: "Property 'authState' doesn't exist"**

**Cause**: Trying to access `authState` without proper destructuring **Fix**:

```typescript
// Change this:
const { state } = useAuth();
if (authState.user) { ... }

// To this:
const { authState } = useAuth();
if (authState.user) { ... }

// Or this:
const { authState } = useAuthCompat();
if (authState.user) { ... }
```

### **Error: "Cannot read property 'user' of undefined"**

**Cause**: Auth state not loaded or hook not properly initialized **Fix**:

```typescript
const { authState } = useAuthCompat();

// ✅ Add loading check
if (!authState || authState.isLoading) {
  return <LoadingSpinner />;
}

if (authState.user) {
  // Safe to use user
}
```

## 📊 **Component Examples**

### **Trust Score Component (Fixed)**

```typescript
export default function TrustScoreScreen() {
  const { user, isAuthenticated } = useAuth();

  const fetchTrustData = async () => {
    if (user) {
      const response =
        await profileCompletionService.calculateProfileCompletion(user.id);
      // Use response data
    }
  };
}
```

### **Verification Dashboard**

```typescript
export default function VerificationDashboard() {
  const { authState } = useAuthCompat();
  const user = authState.user;

  useEffect(() => {
    if (!user || !authState.isAuthenticated) return;
    // Load verification data
  }, [user, authState.isAuthenticated]);
}
```

### **Profile Component**

```typescript
export default function ProfileComponent() {
  const { user, isAuthenticated, actions } = useAuth();

  const handleUpdateProfile = async (data: any) => {
    if (user) {
      await actions.updateUser(data);
    }
  };
}
```

## ⚡ **Quick Reference**

| Hook             | Import                  | Usage                       | Best For             |
| ---------------- | ----------------------- | --------------------------- | -------------------- |
| `useAuthCompat`  | `@hooks/useAuthCompat`  | `{ authState }`             | Legacy components    |
| `useAuth`        | `@context/AuthContext`  | `{ user, isAuthenticated }` | New components       |
| `useAuthUnified` | `@hooks/useAuthUnified` | Internal only               | Hook implementations |

## 🎯 **Success Checklist**

- [ ] No "Property 'authState' doesn't exist" errors
- [ ] Consistent auth hook usage per component
- [ ] Proper loading state handling
- [ ] TypeScript compilation without auth-related errors
- [ ] User authentication flows working correctly

---

**Remember**: When in doubt, use `useAuthCompat()` for components that need
`authState` pattern, and `useAuth()` for new components that can use direct
property access.
