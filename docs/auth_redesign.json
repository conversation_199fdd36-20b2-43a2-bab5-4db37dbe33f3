{"designSystem": {"name": "Modern Auth Interface", "version": "1.0.0", "description": "Clean, mobile-first authentication design system with curved headers and modern form styling"}, "colorPalette": {"primary": {"main": "#1E6FBF", "dark": "#1557A0", "light": "#4A8ED8", "description": "Deep blue used for headers, primary buttons, and brand elements"}, "neutral": {"white": "#FFFFFF", "gray100": "#F8F9FA", "gray200": "#E9ECEF", "gray300": "#DEE2E6", "gray400": "#CED4DA", "gray500": "#6C757D", "gray800": "#343A40"}, "text": {"primary": "#343A40", "secondary": "#6C757D", "onPrimary": "#FFFFFF"}}, "typography": {"fontFamily": {"primary": "system-ui, -apple-system, 'Segoe UI', Roboto, sans-serif", "brand": "Custom script/handwritten style for logo"}, "fontSize": {"xs": "12px", "sm": "14px", "base": "16px", "lg": "18px", "xl": "24px", "2xl": "32px"}, "fontWeight": {"normal": 400, "medium": 500, "semibold": 600, "bold": 700}, "lineHeight": {"tight": 1.2, "normal": 1.5, "relaxed": 1.75}}, "spacing": {"xs": "4px", "sm": "8px", "md": "16px", "lg": "24px", "xl": "32px", "2xl": "48px", "3xl": "64px"}, "borderRadius": {"sm": "4px", "md": "8px", "lg": "12px", "xl": "16px", "2xl": "24px", "full": "9999px"}, "shadows": {"sm": "0 1px 3px rgba(0, 0, 0, 0.1)", "md": "0 4px 12px rgba(0, 0, 0, 0.1)", "lg": "0 8px 24px rgba(0, 0, 0, 0.12)"}, "layout": {"structure": {"type": "single-column", "maxWidth": "400px", "containerPadding": "24px", "verticalAlignment": "center"}, "header": {"type": "curved-bottom", "backgroundColor": "primary.main", "height": "40vh", "minHeight": "280px", "padding": {"top": "64px", "horizontal": "24px"}, "curve": {"type": "elliptical", "depth": "60px"}}, "content": {"backgroundColor": "neutral.white", "padding": "32px 24px", "marginTop": "-40px", "borderRadius": "24px 24px 0 0", "position": "relative", "zIndex": 10}}, "components": {"brandLogo": {"fontSize": "2xl", "fontWeight": "normal", "color": "text.onPrimary", "fontFamily": "brand", "textAlign": "center", "marginBottom": "xl"}, "pageTitle": {"fontSize": "xl", "fontWeight": "semibold", "color": "text.primary", "marginBottom": "2xl", "textAlign": "left"}, "formField": {"marginBottom": "lg", "label": {"fontSize": "base", "fontWeight": "medium", "color": "text.primary", "marginBottom": "sm", "display": "block"}, "input": {"width": "100%", "padding": "16px", "fontSize": "base", "borderRadius": "lg", "border": "1px solid", "borderColor": "neutral.gray300", "backgroundColor": "neutral.white", "transition": "border-color 0.2s ease", "placeholder": {"color": "neutral.gray400"}, "focus": {"borderColor": "primary.main", "outline": "none", "boxShadow": "0 0 0 3px rgba(30, 111, 191, 0.1)"}}}, "primaryButton": {"width": "100%", "padding": "16px", "fontSize": "base", "fontWeight": "semibold", "color": "text.onPrimary", "backgroundColor": "primary.main", "border": "none", "borderRadius": "lg", "cursor": "pointer", "transition": "background-color 0.2s ease", "marginTop": "xl", "hover": {"backgroundColor": "primary.dark"}, "active": {"transform": "translateY(1px)"}}, "divider": {"marginTop": "lg", "marginBottom": "lg", "text": {"fontSize": "sm", "color": "text.secondary", "textAlign": "center", "position": "relative"}, "line": {"height": "1px", "backgroundColor": "neutral.gray300"}}, "socialLoginContainer": {"display": "flex", "justifyContent": "center", "gap": "lg", "marginBottom": "lg"}, "socialLoginButton": {"width": "48px", "height": "48px", "borderRadius": "full", "border": "1px solid", "borderColor": "neutral.gray300", "backgroundColor": "neutral.white", "display": "flex", "alignItems": "center", "justifyContent": "center", "cursor": "pointer", "transition": "all 0.2s ease", "hover": {"borderColor": "neutral.gray400", "boxShadow": "sm"}}, "textLink": {"fontSize": "sm", "color": "text.secondary", "textAlign": "center", "textDecoration": "none", "hover": {"color": "primary.main", "textDecoration": "underline"}}, "accountPrompt": {"display": "flex", "alignItems": "center", "justifyContent": "center", "gap": "sm", "marginTop": "lg", "text": {"fontSize": "sm", "color": "text.secondary"}, "link": {"fontSize": "sm", "color": "text.primary", "fontWeight": "medium", "textDecoration": "none", "hover": {"color": "primary.main"}}}}, "animations": {"transitions": {"fast": "0.15s ease", "normal": "0.2s ease", "slow": "0.3s ease"}, "transforms": {"buttonPress": "translateY(1px)", "hover": "translateY(-1px)"}}, "responsive": {"breakpoints": {"mobile": "320px", "tablet": "768px", "desktop": "1024px"}, "rules": {"mobile": {"containerPadding": "16px", "header.padding.horizontal": "16px", "content.padding": "24px 16px"}, "tablet": {"layout.maxWidth": "480px"}}}, "accessibility": {"focusIndicator": {"outline": "2px solid", "outlineColor": "primary.main", "outlineOffset": "2px"}, "colorContrast": {"minimumRatio": 4.5, "preferredRatio": 7}}, "patterns": {"authFlow": {"structure": ["curved header with branding", "overlapping content container", "form fields with consistent spacing", "primary action button", "optional divider", "alternative authentication methods", "account creation/login toggle"], "visualHierarchy": ["brand identity (header)", "page title", "primary form", "primary action", "secondary actions", "tertiary links"]}}}