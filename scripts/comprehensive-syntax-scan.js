#!/usr/bin/env node

const fs = require('fs');
const { execSync } = require('child_process');

console.log('🔍 COMPREHENSIVE SYNTAX ERROR DETECTION');
console.log('=======================================\n');

let totalIssues = 0;

// 1. Check for malformed JSX props with inline comments
console.log('1. Checking for malformed JSX props with inline comments...');
try {
  const result = execSync(
    'find src/ -name "*.tsx" | xargs grep -n "//.*[a-zA-Z].*=" | grep -v "^[[:space:]]*//.*"',
    { encoding: 'utf8' }
  );
  if (result.trim()) {
    console.log('❌ Found potential JSX prop comment issues:');
    console.log(result.trim().split('\n').slice(0, 5).join('\n'));
    totalIssues += result.trim().split('\n').length;
  } else {
    console.log('✅ No JSX prop comment issues found');
  }
} catch (error) {
  console.log('✅ No JSX prop comment issues found');
}

// 2. Check for unclosed JSX tags
console.log('\n2. Checking for unclosed JSX tags...');
try {
  const result = execSync(
    'find src/ -name "*.tsx" | xargs grep -n "<[A-Z][a-zA-Z]*[^>]*$" | grep -v "/>" | head -5',
    { encoding: 'utf8' }
  );
  if (result.trim()) {
    console.log('⚠️  Found potential unclosed JSX tags (manual review needed):');
    console.log(result.trim());
  } else {
    console.log('✅ No obvious unclosed JSX tags found');
  }
} catch (error) {
  console.log('✅ No obvious unclosed JSX tags found');
}

// 3. Check for missing semicolons in critical places
console.log('\n3. Checking for missing semicolons...');
try {
  const result = execSync(
    'find src/ -name "*.tsx" -o -name "*.ts" | xargs grep -n "import.*from.*[^;]$" | head -5',
    { encoding: 'utf8' }
  );
  if (result.trim()) {
    console.log('⚠️  Found imports without semicolons:');
    console.log(result.trim());
  } else {
    console.log('✅ No missing semicolons in imports found');
  }
} catch (error) {
  console.log('✅ No missing semicolons in imports found');
}

// 4. Check for malformed arrow functions
console.log('\n4. Checking for malformed arrow functions...');
try {
  const result = execSync(
    'find src/ -name "*.tsx" -o -name "*.ts" | xargs grep -n "=> {[^}]*$" | head -5',
    { encoding: 'utf8' }
  );
  if (result.trim()) {
    console.log('ℹ️  Found arrow functions (normal, but checking for issues):');
    const lines = result.trim().split('\n');
    for (const line of lines.slice(0, 3)) {
      console.log(line);
    }
  } else {
    console.log('✅ No arrow function issues found');
  }
} catch (error) {
  console.log('✅ No arrow function issues found');
}

// 5. Check for unmatched parentheses in function calls
console.log('\n5. Checking for unmatched parentheses...');
try {
  const result = execSync(
    'find src/ -name "*.tsx" -o -name "*.ts" | xargs grep -n "([^)]*$" | grep -v "function\\|interface\\|type\\|=>" | head -3',
    { encoding: 'utf8' }
  );
  if (result.trim()) {
    console.log('ℹ️  Found lines with open parentheses (checking for issues):');
    console.log(result.trim());
  } else {
    console.log('✅ No obvious parentheses issues found');
  }
} catch (error) {
  console.log('✅ No obvious parentheses issues found');
}

// 6. Check for malformed template literals
console.log('\n6. Checking for malformed template literals...');
try {
  const result = execSync(
    'find src/ -name "*.tsx" -o -name "*.ts" | xargs grep -n "`[^`]*$" | head -3',
    { encoding: 'utf8' }
  );
  if (result.trim()) {
    console.log('⚠️  Found potential unclosed template literals:');
    console.log(result.trim());
  } else {
    console.log('✅ No unclosed template literals found');
  }
} catch (error) {
  console.log('✅ No unclosed template literals found');
}

// 7. Check for missing commas in object literals
console.log('\n7. Checking for missing commas in objects...');
try {
  const result = execSync(
    'find src/ -name "*.tsx" -o -name "*.ts" | xargs grep -n "}[[:space:]]*[a-zA-Z]" | head -3',
    { encoding: 'utf8' }
  );
  if (result.trim()) {
    console.log('ℹ️  Found potential missing commas (manual review needed):');
    console.log(result.trim());
  } else {
    console.log('✅ No obvious missing commas found');
  }
} catch (error) {
  console.log('✅ No obvious missing commas found');
}

// 8. Check for TypeScript syntax errors
console.log('\n8. Checking for TypeScript syntax patterns...');
try {
  const result = execSync(
    'find src/ -name "*.tsx" -o -name "*.ts" | xargs grep -n ": [a-zA-Z]*<[^>]*$" | head -3',
    { encoding: 'utf8' }
  );
  if (result.trim()) {
    console.log('ℹ️  Found generic type patterns (checking for completeness):');
    console.log(result.trim());
  } else {
    console.log('✅ No incomplete generic types found');
  }
} catch (error) {
  console.log('✅ No incomplete generic types found');
}

console.log('\n🎯 SYNTAX SCAN SUMMARY');
console.log('=====================');
if (totalIssues === 0) {
  console.log('✅ No critical syntax errors detected!');
  console.log('✅ Codebase appears syntactically clean');
  console.log('✅ Ready for development and deployment');
} else {
  console.log(`⚠️  Found ${totalIssues} potential syntax issues`);
  console.log('📝 Manual review recommended for flagged items');
}

console.log('\n🚀 EXPO SERVER STATUS CHECK');
console.log('===========================');
try {
  const serverCheck = execSync('curl -s -I http://localhost:8082 | head -1', { encoding: 'utf8' });
  if (serverCheck.includes('200 OK')) {
    console.log('✅ Expo development server is running successfully');
  } else {
    console.log('⚠️  Expo server status unclear');
  }
} catch (error) {
  console.log('❌ Could not check Expo server status');
}

console.log('\n🎉 SYNTAX DETECTION COMPLETE!');
