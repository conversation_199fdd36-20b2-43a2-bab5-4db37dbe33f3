#!/usr/bin/env node

/**
 * Open Supabase Dashboard for Manual Migration
 *
 * This script opens the Supabase SQL Editor and prepares
 * the migration SQL for easy copy-paste execution.
 */

const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');

console.log('🚀 Supabase Dashboard Migration Helper');
console.log('');

async function openSupabaseMigration() {
  try {
    const migrationFile = path.join(process.cwd(), 'simplified_auth_migration.sql');

    // Check if migration file exists
    if (!fs.existsSync(migrationFile)) {
      throw new Error(`Migration file not found: ${migrationFile}`);
    }

    // Read the migration SQL
    const migrationSQL = fs.readFileSync(migrationFile, 'utf8');

    console.log('📄 Migration SQL loaded from:', migrationFile);
    console.log('📏 SQL Size:', `${Math.round(migrationSQL.length / 1024)}KB`);
    console.log('');

    // Copy SQL to clipboard (macOS)
    try {
      await new Promise((resolve, reject) => {
        const proc = exec('pbcopy', error => {
          if (error) reject(error);
          else resolve();
        });
        proc.stdin.write(migrationSQL);
        proc.stdin.end();
      });
      console.log('📋 Migration SQL copied to clipboard!');
    } catch (clipboardError) {
      console.log('⚠️  Could not copy to clipboard:', clipboardError.message);
    }

    // Open Supabase dashboard
    const supabaseUrl = 'https://supabase.com/dashboard/project/zmocagflbbrmjgqsmqgn/sql/new';

    try {
      exec(`open "${supabaseUrl}"`, error => {
        if (error) {
          console.log('⚠️  Could not open browser automatically');
          console.log('🔗 Please open manually:', supabaseUrl);
        } else {
          console.log('🌐 Opening Supabase SQL Editor...');
        }
      });
    } catch (browserError) {
      console.log('🔗 Please open:', supabaseUrl);
    }

    console.log('');
    console.log('🔧 Manual Migration Steps:');
    console.log('');
    console.log('1. 🌐 Supabase SQL Editor should open in your browser');
    console.log(
      '   If not, go to: https://supabase.com/dashboard/project/zmocagflbbrmjgqsmqgn/sql/new'
    );
    console.log('');
    console.log('2. 📋 The migration SQL is copied to your clipboard');
    console.log('   If not copied, you can find it in: simplified_auth_migration.sql');
    console.log('');
    console.log('3. 📝 In the SQL Editor:');
    console.log('   • Paste the SQL (Cmd+V on macOS)');
    console.log('   • Click "Run" button to execute');
    console.log('   • Wait for completion confirmation');
    console.log('');
    console.log('4. ✅ Verify success:');
    console.log('   • Check for "Success" message');
    console.log(
      '   • Verify tables created: profiles, verification_submissions, cost_savings_analytics'
    );
    console.log('');

    // Show a preview of the SQL
    const sqlPreview =
      migrationSQL.substring(0, 500) +
      (migrationSQL.length > 500
        ? '\n...\n[SQL continues for ' + Math.round((migrationSQL.length - 500) / 1024) + 'KB more]'
        : '');

    console.log('📋 SQL Preview:');
    console.log('═'.repeat(80));
    console.log(sqlPreview);
    console.log('═'.repeat(80));
    console.log('');

    console.log('💰 Expected Results After Migration:');
    console.log('✓ Enhanced profiles table with simplified auth columns');
    console.log('✓ Created verification_submissions table for manual review');
    console.log('✓ Created cost_savings_analytics table');
    console.log('✓ Added RLS policies for security');
    console.log('✓ Created triggers for automatic profile completion');
    console.log('✓ Added auto-profile creation on user signup');
    console.log('');
    console.log('🎯 Cost Savings Enabled:');
    console.log('• Identity Verification: $7 saved per user vs Jumio');
    console.log('• Background Check: $35 saved per user vs traditional services');
    console.log('• Reference Verification: $15 saved per user vs verification services');
    console.log('• Total: $57 saved per user verification cycle');
    console.log('');
    console.log('🚀 After Migration Success:');
    console.log('1. Test user registration with the new 3-step flow');
    console.log('2. Verify admin verification dashboard works');
    console.log('3. Check cost savings analytics tracking');
    console.log('4. Monitor profile completion calculations');
    console.log('');

    // Wait for user confirmation
    console.log('⏳ Waiting for you to complete the migration...');
    console.log("Press any key after you've successfully run the SQL in Supabase...");

    // Wait for user input
    process.stdin.setRawMode(true);
    process.stdin.resume();
    process.stdin.on('data', () => {
      console.log('');
      console.log('🎉 Great! Migration should be complete.');
      console.log('');
      console.log('🔍 Quick Verification:');
      console.log('In Supabase dashboard, go to Table Editor and verify these tables exist:');
      console.log('• profiles (with new columns: auth_flow_version, verification_level, etc.)');
      console.log('• verification_submissions');
      console.log('• cost_savings_analytics');
      console.log('');
      console.log('✅ If tables are visible, migration was successful!');
      console.log('❌ If tables are missing, please re-run the SQL in Supabase SQL Editor');
      console.log('');
      console.log('🚀 Next: Test the simplified authentication flow in your app!');
      process.exit(0);
    });
  } catch (error) {
    console.error('❌ Migration helper failed:', error.message);
    console.error('');
    console.error('🔧 Manual Process:');
    console.error('1. Open: https://supabase.com/dashboard/project/zmocagflbbrmjgqsmqgn/sql/new');
    console.error('2. Copy content from: simplified_auth_migration.sql');
    console.error('3. Paste into SQL Editor and click "Run"');
    process.exit(1);
  }
}

// Run the helper
if (require.main === module) {
  openSupabaseMigration();
}

module.exports = { openSupabaseMigration };
