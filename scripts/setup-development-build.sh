#!/bin/bash

# WeRoomies Development Build Setup Script
# This script helps set up development builds for push notification testing

echo "🚀 WeRoomies Development Build Setup"
echo "====================================="
echo ""

# Check if EAS CLI is installed
if ! command -v eas &> /dev/null; then
    echo "📦 Installing EAS CLI..."
    npm install -g @expo/eas-cli
else
    echo "✅ EAS CLI already installed"
fi

# Check if logged in to EAS
echo ""
echo "🔐 Checking EAS login..."
if ! eas whoami &> /dev/null; then
    echo "Please login to EAS:"
    eas login
else
    echo "✅ Already logged in to EAS"
fi

# Check current EAS configuration
echo ""
echo "📋 Current EAS Configuration:"
echo "Project: $(cat app.json | grep '"name"' | head -1 | cut -d'"' -f4)"
echo "Bundle ID: $(cat app.json | grep '"bundleIdentifier"' | cut -d'"' -f4)"
echo "Package: $(cat app.json | grep '"package"' | cut -d'"' -f4)"

# Show build options
echo ""
echo "🔨 Available Build Options:"
echo ""
echo "1. Android Development Build"
echo "   Command: eas build --platform android --profile development"
echo ""
echo "2. iOS Development Build (requires Apple Developer Account)"
echo "   Command: eas build --platform ios --profile development"
echo ""
echo "3. Both Platforms"
echo "   Command: eas build --profile development"
echo ""

# Check for notification libraries
echo "📚 Checking notification libraries..."
if grep -q "expo-notifications" package.json; then
    echo "✅ expo-notifications installed"
else
    echo "📦 Installing expo-notifications..."
    npx expo install expo-notifications expo-device expo-constants
fi

# Show next steps
echo ""
echo "🎯 Next Steps for Push Notification Testing:"
echo ""
echo "1. CREDENTIAL SETUP:"
echo "   • Android: Configure Firebase Cloud Messaging (FCM)"
echo "   • iOS: Requires paid Apple Developer Account"
echo ""
echo "2. BUILD COMMAND:"
echo "   Choose one of the build commands above"
echo ""
echo "3. TESTING:"
echo "   • Install development build on physical device"
echo "   • Run: npx expo start --dev-client"
echo "   • Test push notifications with: https://expo.dev/notifications"
echo ""
echo "4. CURRENT STATUS:"
echo "   • Expo Go testing: ✅ (95% features work)"
echo "   • Push notifications: ❌ (requires development build)"
echo "   • Production builds: ✅ (will have full push notification support)"
echo ""
echo "💡 RECOMMENDATION:"
echo "Continue with Expo Go testing for now. The push notification error"
echo "you're seeing is expected and documented by Expo. Set up development"
echo "builds only if you specifically need to test push notifications."
echo ""
echo "📖 More info: https://docs.expo.dev/push-notifications/push-notifications-setup/" 