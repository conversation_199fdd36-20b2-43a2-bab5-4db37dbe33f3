const fs = require('fs');
const path = require('path');

console.log('🔧 FIXING MALFORMED CONST PATTERNS');
console.log('=================================');

// Files with malformed const patterns
const MALFORMED_FILES = [
  'src/app/chat/index.tsx',
  'src/app/(tabs)/create.tsx',
  'src/app/(tabs)/saved.tsx',
  'src/app/(tabs)/services.tsx',
  'src/app/(tabs)/profile/unified-cultural.tsx',
];

function fixMalformedConstPatterns(content) {
  return (
    content
      // Fix malformed const assignments like: const isActive={selectedTab} tab.id;
      .replace(/const\s+(\w+)=\{([^}]+)\}\s+([^;]+);/g, 'const $1 = $2 === $3;')

      // Fix malformed const assignments like: const isActive={activeTab} tab.id;
      .replace(/const\s+(\w+)=\{([^}]+)\}\s+(\w+)\.(\w+);/g, 'const $1 = $2 === $3.$4;')

      // Fix malformed function calls like: const navigateToCategory={category} > {
      .replace(/const\s+(\w+)=\{([^}]+)\}\s*>\s*\{/g, 'const $1 = ($2) => {')

      // Fix malformed const with complex patterns
      .replace(
        /const\s+isEditMode=\{([^}]+)\}\s+([^;]+);/g,
        "const isEditMode = $1 === '$2' && id;"
      )

      // Fix malformed const with missing operators
      .replace(/const\s+(\w+)=\{([^}]+)\}\s+(\w+);/g, 'const $1 = $2 === $3;')

      // Fix missing semicolons after function calls
      .replace(/(\w+)\(\)\s*\n/g, '$1();\n')

      // Fix malformed arrow functions
      .replace(/=\s*\{([^}]+)\}\s*>\s*\{/g, '= ($1) => {')
  );
}

function processFile(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  File not found: ${filePath}`);
      return false;
    }

    const content = fs.readFileSync(filePath, 'utf8');
    const fixedContent = fixMalformedConstPatterns(content);

    if (content !== fixedContent) {
      fs.writeFileSync(filePath, fixedContent);
      console.log(`✅ Fixed: ${filePath}`);
      return true;
    } else {
      console.log(`ℹ️  No changes needed: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Process all malformed files
console.log('\\nProcessing malformed const patterns...');
let fixedCount = 0;

MALFORMED_FILES.forEach(file => {
  if (processFile(file)) {
    fixedCount++;
  }
});

console.log(`\\n🎉 MALFORMED CONST FIX COMPLETE`);
console.log(`📊 Files processed: ${MALFORMED_FILES.length}`);
console.log(`✅ Files fixed: ${fixedCount}`);
console.log(`ℹ️  Files unchanged: ${MALFORMED_FILES.length - fixedCount}`);
