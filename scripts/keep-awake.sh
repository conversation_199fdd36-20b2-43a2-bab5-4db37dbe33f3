#!/bin/bash

# Keep Computer Awake for WeRoomies Testing
# Prevents computer from sleeping while tunnel is running

echo "☕ Keeping computer awake for WeRoomies testing..."
echo "🔄 Tunnel must stay running for testers to connect"
echo "⏹️  Press Ctrl+C to stop and allow sleep"
echo ""

# Check if caffeinate is available (macOS)
if command -v caffeinate &> /dev/null; then
    echo "🍵 Using caffeinate to prevent sleep..."
    echo "💻 Computer will stay awake until you stop this script"
    echo "🌐 Tunnel URL: exp://9dshbj0-liyat-8081.exp.direct"
    echo ""
    
    # Prevent system sleep indefinitely
    caffeinate -d -i -m -s
else
    echo "⚠️  caffeinate not found (not macOS?)"
    echo "💡 Manually adjust power settings:"
    echo "   - System Preferences → Energy Saver"
    echo "   - Set 'Turn display off after' to Never"
    echo "   - Set 'Prevent computer from sleeping' to Always"
    echo ""
    
    # Keep script running as a reminder
    while true; do
        echo "⏰ $(date): Computer should stay awake for testing..."
        sleep 3600  # Check every hour
    done
fi 