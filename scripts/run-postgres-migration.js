#!/usr/bin/env node

/**
 * Execute PostgreSQL Migration using psql
 *
 * This script executes the simplified auth flow migration
 * directly against PostgreSQL using the psql command.
 */

const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

// Supabase PostgreSQL connection details
const SUPABASE_HOST = 'db.zmocagflbbrmjgqsmqgn.supabase.co';
const SUPABASE_PORT = '5432';
const SUPABASE_DATABASE = 'postgres';
const SUPABASE_USER = 'postgres';
const SUPABASE_PASSWORD = process.env.SUPABASE_DB_PASSWORD || process.env.DB_PASSWORD;

console.log('🚀 PostgreSQL Migration Executor');
console.log('📍 Target Database:', `${SUPABASE_HOST}:${SUPABASE_PORT}/${SUPABASE_DATABASE}`);

async function executeMigration() {
  try {
    const migrationFile = path.join(process.cwd(), 'simplified_auth_migration.sql');

    // Check if migration file exists
    if (!fs.existsSync(migrationFile)) {
      throw new Error(`Migration file not found: ${migrationFile}`);
    }

    console.log('📄 Migration file found:', migrationFile);

    // Check if psql is available
    console.log('🔍 Checking psql availability...');

    await new Promise((resolve, reject) => {
      exec('psql --version', (error, stdout, stderr) => {
        if (error) {
          console.log('⚠️  psql not found. Installing PostgreSQL client...');
          console.log('');
          console.log('📋 To install psql:');
          console.log('• macOS: brew install postgresql');
          console.log('• Ubuntu/Debian: sudo apt-get install postgresql-client');
          console.log('• Windows: Download from https://www.postgresql.org/download/');
          console.log('');
          reject(new Error('psql not available'));
        } else {
          console.log('✅ psql found:', stdout.trim());
          resolve();
        }
      });
    });

    // Prepare psql command
    const psqlCommand = [
      'psql',
      `-h ${SUPABASE_HOST}`,
      `-p ${SUPABASE_PORT}`,
      `-U ${SUPABASE_USER}`,
      `-d ${SUPABASE_DATABASE}`,
      `-f "${migrationFile}"`,
      '--set ON_ERROR_STOP=on',
      '--quiet',
    ].join(' ');

    console.log('🔄 Executing migration...');
    console.log('📝 Command:', psqlCommand.replace(SUPABASE_PASSWORD || '', '***'));

    // Set password environment variable for psql
    const env = { ...process.env };
    if (SUPABASE_PASSWORD) {
      env.PGPASSWORD = SUPABASE_PASSWORD;
    }

    // Execute migration
    await new Promise((resolve, reject) => {
      const childProcess = exec(psqlCommand, { env }, (error, stdout, stderr) => {
        if (error) {
          console.error('❌ Migration failed:', error.message);
          console.error('');
          console.error('🔧 Troubleshooting:');
          console.error('1. Verify your database password is set in environment variables');
          console.error('2. Check your internet connection');
          console.error('3. Ensure Supabase database is accessible');
          console.error('4. Try running the SQL manually in Supabase dashboard');
          console.error('');
          console.error('💡 Alternative: Use Supabase SQL Editor');
          console.error('1. Go to https://supabase.com/dashboard/project/zmocagflbbrmjgqsmqgn');
          console.error('2. Navigate to SQL Editor');
          console.error('3. Copy and paste the content from simplified_auth_migration.sql');
          console.error('4. Click "Run" to execute');
          reject(error);
        } else {
          console.log('✅ Migration executed successfully!');
          if (stdout) console.log('📄 Output:', stdout);
          if (stderr) console.log('⚠️  Warnings:', stderr);
          resolve();
        }
      });

      // Handle password prompt if needed
      if (!SUPABASE_PASSWORD) {
        console.log('🔐 You may be prompted for the database password...');
      }
    });

    // Verify migration success
    console.log('🔍 Verifying migration...');

    const verifyCommand = [
      'psql',
      `-h ${SUPABASE_HOST}`,
      `-p ${SUPABASE_PORT}`,
      `-U ${SUPABASE_USER}`,
      `-d ${SUPABASE_DATABASE}`,
      `-c "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name IN ('profiles', 'verification_submissions', 'cost_savings_analytics');"`,
      '--quiet',
      '--tuples-only',
    ].join(' ');

    await new Promise((resolve, reject) => {
      exec(verifyCommand, { env }, (error, stdout, stderr) => {
        if (error) {
          console.log('⚠️  Verification failed:', error.message);
          resolve(); // Don't fail the whole process
        } else {
          const tables = stdout
            .trim()
            .split('\n')
            .map(line => line.trim())
            .filter(line => line);
          console.log('✅ Verified tables created:', tables.join(', '));
          resolve();
        }
      });
    });

    console.log('');
    console.log('🎉 PostgreSQL Migration Complete!');
    console.log('');
    console.log('📊 Migration Summary:');
    console.log('✓ Enhanced profiles table with simplified auth columns');
    console.log('✓ Created verification_submissions table for manual review');
    console.log('✓ Created cost_savings_analytics table');
    console.log('✓ Added RLS policies for security');
    console.log('✓ Created triggers for automatic profile completion calculation');
    console.log('✓ Added auto-profile creation on user signup');
    console.log('');
    console.log('💰 Cost Savings Enabled:');
    console.log('• Identity Verification: $7 saved per user');
    console.log('• Background Check: $35 saved per user');
    console.log('• Reference Verification: $15 saved per user');
    console.log('• Total: $57 saved per user verification');
    console.log('');
    console.log('🚀 Next Steps:');
    console.log('1. Test the simplified auth flow in your app');
    console.log('2. Monitor the admin verification dashboard');
    console.log('3. Track cost savings in the analytics');
    console.log('4. Configure SMS service for phone verification');
  } catch (error) {
    console.error('❌ Migration execution failed:', error.message);
    console.error('');
    console.error('🔧 Manual Execution Required:');
    console.error('');
    console.error('📋 Option 1: Supabase Dashboard (Recommended)');
    console.error('1. Go to https://supabase.com/dashboard/project/zmocagflbbrmjgqsmqgn');
    console.error('2. Navigate to SQL Editor');
    console.error('3. Copy and paste the content from simplified_auth_migration.sql');
    console.error('4. Click "Run" to execute the migration');
    console.error('');
    console.error('📋 Option 2: Direct psql (if you have credentials)');
    console.error(
      `psql -h ${SUPABASE_HOST} -U ${SUPABASE_USER} -d ${SUPABASE_DATABASE} -f simplified_auth_migration.sql`
    );
    console.error('');
    process.exit(1);
  }
}

// Environment variable setup instructions
function showEnvironmentSetup() {
  console.log('');
  console.log('🔧 Environment Setup Required:');
  console.log('');
  console.log('Add your Supabase database password to environment variables:');
  console.log('');
  console.log('Option 1: .env file');
  console.log('SUPABASE_DB_PASSWORD=your_database_password');
  console.log('');
  console.log('Option 2: Environment variable');
  console.log('export SUPABASE_DB_PASSWORD=your_database_password');
  console.log('');
  console.log('💡 Find your database password in:');
  console.log('Supabase Dashboard → Settings → Database → Connection string');
  console.log('');
}

// Check if password is available
if (!SUPABASE_PASSWORD) {
  console.log('⚠️  Database password not found in environment variables');
  showEnvironmentSetup();
  console.log('🔄 Proceeding with interactive password prompt...');
  console.log('');
}

// Run migration
if (require.main === module) {
  executeMigration();
}

module.exports = { executeMigration };
