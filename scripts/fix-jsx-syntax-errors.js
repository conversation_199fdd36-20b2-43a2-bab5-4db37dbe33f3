#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔧 Fixing JSX syntax errors...\\n');

// Files with JSX syntax errors
const filesToFix = [
  'src/app/(tabs)/profile/property-manager-dashboard.tsx'
];

let totalFilesProcessed = 0;
let totalFixesApplied = 0;

function fixJSXSyntaxErrors(filePath) {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️  File not found: ${filePath}`);
    return;
  }

  console.log(`🔍 Processing: ${filePath}`);
  let content = fs.readFileSync(filePath, 'utf8');
  let fixesInFile = 0;
  const originalContent = content;

  // Fix 1: Remove extra braces in style arrays
  const extraBracesRegex = /}, \\{\\s*\\]\\}\\} \\{/g;
  const extraBracesMatches = content.match(extraBracesRegex);
  if (extraBracesMatches) {
    content = content.replace(extraBracesRegex, '}]}');
    fixesInFile += extraBracesMatches.length;
    console.log(`   ✅ Fixed ${extraBracesMatches.length} extra braces in style arrays`);
  }

  // Fix 2: Remove extra > { patterns
  const extraGTRegex = /\\]\\}\\} \\{\\s*> \\{/g;
  const extraGTMatches = content.match(extraGTRegex);
  if (extraGTMatches) {
    content = content.replace(extraGTRegex, ']}\\n                  >');
    fixesInFile += extraGTMatches.length;
    console.log(`   ✅ Fixed ${extraGTMatches.length} malformed JSX opening tags`);
  }

  // Fix 3: Remove extra braces in JSX elements
  const extraJSXBracesRegex = /<Text \\{/g;
  const extraJSXBracesMatches = content.match(extraJSXBracesRegex);
  if (extraJSXBracesMatches) {
    content = content.replace(extraJSXBracesRegex, '<Text');
    fixesInFile += extraJSXBracesMatches.length;
    console.log(`   ✅ Fixed ${extraJSXBracesMatches.length} malformed Text elements`);
  }

  // Fix 4: Fix malformed StyleSheet.flatten patterns
  const malformedStyleRegex = /StyleSheet\\.flatten\\(\\[([^\\]]+)\\]\\}\\} \\{/g;
  const malformedStyleMatches = content.match(malformedStyleRegex);
  if (malformedStyleMatches) {
    content = content.replace(malformedStyleRegex, '[');
    fixesInFile += malformedStyleMatches.length;
    console.log(`   ✅ Fixed ${malformedStyleMatches.length} malformed StyleSheet.flatten patterns`);
  }

  // Fix 5: Fix broken View closing tags
  const brokenViewRegex = /<\\/View>\\s*\\{/g;
  const brokenViewMatches = content.match(brokenViewRegex);
  if (brokenViewMatches) {
    content = content.replace(brokenViewRegex, '</View>');
    fixesInFile += brokenViewMatches.length;
    console.log(`   ✅ Fixed ${brokenViewMatches.length} broken View closing tags`);
  }

  // Fix 6: Replace StyleSheet.flatten with simple array syntax
  const styleSheetFlattenRegex = /StyleSheet\\.flatten\\(\\[([^\\]]+)\\]\\)/g;
  const styleSheetFlattenMatches = content.match(styleSheetFlattenRegex);
  if (styleSheetFlattenMatches) {
    content = content.replace(styleSheetFlattenRegex, '[$1]');
    fixesInFile += styleSheetFlattenMatches.length;
    console.log(`   ✅ Simplified ${styleSheetFlattenMatches.length} StyleSheet.flatten calls`);
  }

  // Fix 7: Fix malformed key prop patterns
  const malformedKeyRegex = /key=\\{([^}]+)\\}\\s*style=\\{StyleSheet\\.flatten\\(\\[/g;
  const malformedKeyMatches = content.match(malformedKeyRegex);
  if (malformedKeyMatches) {
    content = content.replace(malformedKeyRegex, 'key={$1} style={[');
    fixesInFile += malformedKeyMatches.length;
    console.log(`   ✅ Fixed ${malformedKeyMatches.length} malformed key prop patterns`);
  }

  // Fix 8: Fix extra spaces and formatting
  content = content.replace(/\\s+>/g, '>');
  content = content.replace(/\\{\\s+/g, '{');
  content = content.replace(/\\s+\\}/g, '}');

  if (fixesInFile > 0) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`   📝 Applied ${fixesInFile} fixes to ${filePath}`);
    totalFixesApplied += fixesInFile;
  } else {
    console.log(`   ✨ No JSX syntax errors found in ${filePath}`);
  }

  totalFilesProcessed++;
}

// Process each file
filesToFix.forEach(fixJSXSyntaxErrors);

console.log(`\\n📊 Summary:`);
console.log(`   Files processed: ${totalFilesProcessed}`);
console.log(`   Total fixes applied: ${totalFixesApplied}`);
console.log(`   Status: ${totalFixesApplied > 0 ? 'Fixed JSX syntax errors' : 'No JSX syntax errors found'}`);

console.log('\\n🎯 JSX syntax error fixes completed!'); 