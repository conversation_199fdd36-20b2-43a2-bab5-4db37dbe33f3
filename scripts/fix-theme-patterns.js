#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Fix theme.theme.colors patterns in payment-methods.tsx
function fixThemePatterns() {
  const filePath = 'src/app/(tabs)/profile/payment-methods.tsx';

  if (!fs.existsSync(filePath)) {
    console.log(`File ${filePath} not found`);
    return;
  }

  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let changesMade = 0;

    // Fix theme.theme.colors -> theme.colors
    const themePattern = /theme\.theme\.colors/g;
    const themeMatches = content.match(themePattern);
    if (themeMatches) {
      content = content.replace(themePattern, 'theme.colors');
      changesMade += themeMatches.length;
      console.log(`Fixed ${themeMatches.length} theme.theme.colors patterns`);
    }

    // Fix malformed optional chaining (already fixed but double-check)
    const optionalChainingPattern = /\)\s*\?\s*\./g;
    const optionalMatches = content.match(optionalChainingPattern);
    if (optionalMatches) {
      content = content.replace(optionalChainingPattern, ')?.');
      changesMade += optionalMatches.length;
      console.log(`Fixed ${optionalMatches.length} malformed optional chaining patterns`);
    }

    // Fix any remaining spacing issues in style objects
    const styleSpacingPattern = /{\s*color\s*:\s*theme\.colors/g;
    content = content.replace(styleSpacingPattern, '{ color: theme.colors');

    if (changesMade > 0) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed ${changesMade} patterns in ${filePath}`);
    } else {
      console.log(`✅ No patterns found to fix in ${filePath}`);
    }

    return changesMade;
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return 0;
  }
}

// Run the fixes
console.log('🔧 Starting theme pattern fixes...');
const totalFixed = fixThemePatterns();
console.log(`🎉 Total patterns fixed: ${totalFixed}`);
