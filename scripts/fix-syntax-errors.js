#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 Starting comprehensive syntax error fixes...\n');

// Files with known syntax errors
const filesToFix = [
  'src/app/(tabs)/create.tsx',
  'src/app/(tabs)/my-listings.tsx',
  'src/app/verification/simple-flow.tsx',
  'src/app/+not-found.tsx',
  'src/app/chat-participants.tsx',
  'src/app/(tabs)/search/ai-search-dashboard.tsx',
  'src/app/admin/behavioral-analytics.tsx',
  'src/app/agreement/create.tsx',
  'src/app/admin/user/[id].tsx',
  'src/features/home/<USER>/EnhancedHousemateCard.tsx',
  'src/components/debug/SafetySystemDebugger.tsx',
  'src/components/monitoring/PerformanceMonitor.tsx',
  'src/components/monitoring/AlertsDashboard.tsx',
  'src/components/profile/PersonaOptimizedOnboarding.tsx',
  'src/components/auth/EnhancedPasswordResetNotification.tsx',
  'src/components/agreement/DisputesList.tsx',
  'src/components/agreement/AgreementAmendment.tsx',
  'src/components/agreement/AgreementReminders.tsx',
  'src/components/admin/AdminAccessGuard.tsx',
  'src/components/chat/ConversationTopicSuggestions.tsx',
  'src/components/chat/AgreementActions.tsx',
  'src/components/matching/MatchCelebrationModal.tsx',
  'src/components/matching/EnhancedMatchToMessageTransition.tsx',
];

let totalFilesProcessed = 0;
let totalFixesApplied = 0;

function fixSyntaxErrors(filePath) {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️  File not found: ${filePath}`);
    return;
  }

  console.log(`🔍 Processing: ${filePath}`);
  let content = fs.readFileSync(filePath, 'utf8');
  let fixesInFile = 0;
  const originalContent = content;

  // Fix 1: Malformed ternary operator syntax
  const ternaryRegex = /\)\s*:\s*\(\s*\{/g;
  const ternaryMatches = content.match(ternaryRegex);
  if (ternaryMatches) {
    content = content.replace(ternaryRegex, ') : (');
    fixesInFile += ternaryMatches.length;
    console.log(`  ✅ Fixed ${ternaryMatches.length} malformed ternary operators`);
  }

  // Fix 2: Error in DisputesList.tsx - specific pattern
  if (filePath.includes('DisputesList.tsx')) {
    const disputesPattern = /\{error \? renderError\(\)\s*:\s*\(\s*\{/g;
    if (content.match(disputesPattern)) {
      content = content.replace(disputesPattern, '{error ? renderError() : (');
      fixesInFile++;
      console.log(`  ✅ Fixed DisputesList specific syntax error`);
    }
  }

  // Fix 3: Remove extra spaces in JSX props (from previous formatting issues)
  const extraSpacesRegex = /(\w+)=\{([^}]+)\}\s+(\w+)=/g;
  const spaceMatches = content.match(extraSpacesRegex);
  if (spaceMatches) {
    content = content.replace(extraSpacesRegex, '$1={$2} $3=');
    console.log(`  ✅ Fixed JSX prop spacing issues`);
  }

  // Fix 4: Handle broken JSX formatting from previous fixes
  content = content
    // Fix broken attribute formatting
    .replace(/(\w+)=\{([^}]+)\}\s+(\w+)=\{([^}]+)\}\s+(\w+)=/g, '$1={$2} $3={$4} $5=')
    // Fix multiple consecutive spaces in JSX
    .replace(/\s{3,}/g, ' ')
    // Fix broken onPress handlers
    .replace(/onPress=\{([^}]+)\}\s+(\w+)=/g, 'onPress={$1} $2=')
    // Fix style prop issues
    .replace(/style=\{([^}]+)\}\s+(\w+)=/g, 'style={$1} $2=');

  // Fix 5: Specific issues in create.tsx and my-listings.tsx
  if (filePath.includes('create.tsx') || filePath.includes('my-listings.tsx')) {
    // Fix JSX closing tag issues
    content = content.replace(/\)\s*:\s*\(\s*\{/g, ') : (');

    // Ensure proper JSX structure
    if (content.includes('}: error ? ( {')) {
      content = content.replace('}: error ? ( {', '} : error ? (');
      fixesInFile++;
    }
  }

  // Write the file if changes were made
  if (content !== originalContent) {
    fs.writeFileSync(filePath, content, 'utf8');
    totalFilesProcessed++;
    totalFixesApplied += fixesInFile;
    console.log(`  ✅ Applied ${fixesInFile} fixes to ${filePath}\n`);
  } else {
    console.log(`  ℹ️  No syntax errors found in ${filePath}\n`);
  }
}

// Process all files
console.log('🚀 Processing files with syntax errors...\n');

filesToFix.forEach(filePath => {
  fixSyntaxErrors(filePath);
});

// Additional: Search for any remaining syntax issues
console.log('🔍 Searching for any remaining syntax issues...\n');

try {
  // Run a quick TypeScript check to see remaining errors
  console.log('📋 Running TypeScript compilation check...');
  const tscOutput = execSync('npx tsc --noEmit --skipLibCheck', {
    encoding: 'utf8',
    timeout: 30000,
    cwd: process.cwd(),
  });
  console.log('✅ TypeScript compilation successful!');
} catch (error) {
  console.log('⚠️  TypeScript compilation still has errors:');
  const errorLines = error.stdout.split('\n').slice(0, 10);
  errorLines.forEach(line => {
    if (line.trim()) console.log(`  ${line}`);
  });
  console.log('  (Showing first 10 errors)');
}

console.log('\n📊 Summary:');
console.log(`  Files processed: ${totalFilesProcessed}`);
console.log(`  Total fixes applied: ${totalFixesApplied}`);

if (totalFixesApplied > 0) {
  console.log('\n✅ Syntax error fixes completed!');
  console.log('🔄 You may want to run the app again to check for any remaining issues.');
} else {
  console.log('\n⚠️  No syntax errors were found to fix.');
  console.log('   The errors might be in different files or have different patterns.');
}

console.log('\n🎯 Next steps:');
console.log('  1. Test the app: npx expo start');
console.log('  2. Check for any remaining TypeScript errors: npx tsc --noEmit');
console.log('  3. If issues persist, check the specific error messages for patterns');
