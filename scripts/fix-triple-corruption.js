const fs = require('fs');
const path = require('path');

console.log('🔧 FIXING TRIPLE METHOD CORRUPTION');
console.log('===================================');

// All files with triple corruption patterns
const CORRUPTED_FILES = [
  'src/contexts/FavoritesContext.tsx',
  'src/core/middleware/auth/rbacMiddleware.ts',
  'src/core/middleware/auth/authProtection.ts',
  'src/core/middleware/auth/routeProtection.ts',
  'src/core/hooks/usePermissions.ts',
  'src/app/(tabs)/profile/personality.tsx',
  'src/app/admin/analytics.tsx',
  'src/app/admin/suspicious-profiles.tsx',
  'src/app/agreement/index.tsx',
  'src/app/agreement/dashboard.tsx',
  'src/app/agreement/details/[id].tsx',
  'src/app/agreement/history.tsx',
  'src/app/provider/bookings.tsx',
  'src/app/provider/packages.tsx',
  'src/app/api/run-migration/video-thumbnail-fields.tsx',
  'src/utils/notificationUtils.ts',
  'src/utils/database/seedAgreementTemplates.ts',
  'src/utils/testing/profileDataflowTest.ts',
  'src/utils/databaseDebugger.ts',
  'src/utils/checkServiceProviders.ts',
  'src/components/chat/AgreementActions.tsx',
  'src/components/admin/EnhancedVerificationAdminDashboard.tsx',
  'src/components/agreement/AgreementCreationWizard.tsx',
  'src/components/profile/analytics/ProfileAnalyticsDashboard.tsx',
  'src/components/debug/ChatDiagnosticTool.tsx',
  'src/hooks/useDisputeResolution.ts',
  'src/hooks/useAgreementCollaboration.ts',
  'src/hooks/useSavedItems.ts',
  'src/hooks/useMessages.ts',
  'src/services/disputeService.ts',
  'src/services/fixes/RoomListingStatusFix.ts',
  'src/services/fixes/MessagingTableFix.ts',
  'src/services/matching/matchingCore.ts',
  'src/services/matching/matchingAnalytics.ts',
  'src/services/matching/MatchingServiceConsolidated.ts',
  'src/services/moderationService.ts',
  'src/services/zeroVerificationService.ts',
  'src/services/pricingConfigService.ts',
  'src/services/chat/ChatRoomService.ts',
  'src/services/propertyOwnerService.ts',
  'src/services/matchQueueService.ts',
  'src/services/payment/ExistingPaymentMethodService.ts',
  'src/services/payment/PaymentSystemEnhancer.ts',
  'src/services/providerPerformanceService.ts',
  'src/services/communityService.ts',
  'src/services/expenseService.ts',
  'src/services/reviewService.ts',
  'src/services/fraudDetectionService.ts',
  'src/services/enhancedMaintenanceService.ts',
  'src/services/unified/UnifiedAgreementService.ts',
  'src/services/unified/UnifiedChatService.ts',
  'src/services/admin/AdminSystemEnhancer.ts',
  'src/services/agreement/AgreementSystemEnhancer.ts',
  'src/services/enhancedModerationService.ts',
  'src/services/MatchStatisticsService.ts',
  'src/services/serviceProvider/ServiceProviderSystemEnhancer.ts',
  'src/services/availabilityService.ts',
  'src/services/MatchService.ts',
  'src/services/bookingService.ts',
  'src/services/enhancedSocialVerificationService.ts',
  'src/services/profile/SmartProfileCompletion.ts',
  'src/services/MovingServiceFlow.ts',
  'src/services/socialMediaService.ts',
  'src/services/providerVerificationService.ts',
  'src/services/agreementService.ts',
  'src/services/recurringExpenseService.ts',
  'src/services/MoveInChecklistService.ts',
  'src/services/serviceProviderService.ts',
  'src/services/api/matchingApi.ts',
  'src/services/personalityService.ts',
  'src/services/reviewRequestService.ts',
  'src/services/providerDashboardService.ts',
  'src/services/roomListingRefreshService.ts',
  'src/services/enhanced/EnhancedAgreementService.ts',
  'src/services/enhanced/UnifiedNotificationService.ts',
  'src/services/rooms/RoomChatService.ts',
  'src/services/standardized/BookingService.ts',
  'src/services/standardized/MoveInChecklistService.ts',
  'src/services/standardized/ReviewRequestService.ts',
  'src/services/moveOutService.ts',
  'src/services/adminService.ts',
  'src/services/relationshipClosureService.ts',
  'src/services/providerPaymentService.ts',
  'src/services/messaging/ReadTrackingService.ts',
  'src/services/MoveInScheduleService.ts',
  'src/services/profileBoostService.ts',
  'src/services/socialInteractionService.ts',
  'src/services/profileCompletionService.ts',
  'src/services/deviceFingerprintService.ts',
  'src/services/favoritesService.ts',
  'src/store/serviceStore.ts',
  'src/store/notificationStore.ts',
  'src/store/chatStore.ts',
];

function fixTripleCorruption(content) {
  return (
    content
      // Fix triple method corruption patterns
      .replace(/\.eq\)\.eq\)\.eq\(/g, '.eq(')
      .replace(/\.is\)\.is\)\.is\(/g, '.is(')
      .replace(/\.neq\)\.neq\)\.neq\(/g, '.neq(')
      .replace(/\.gt\)\.gt\)\.gt\(/g, '.gt(')
      .replace(/\.lt\)\.lt\)\.lt\(/g, '.lt(')
      .replace(/\.gte\)\.gte\)\.gte\(/g, '.gte(')
      .replace(/\.lte\)\.lte\)\.lte\(/g, '.lte(')
      .replace(/\.like\)\.like\)\.like\(/g, '.like(')
      .replace(/\.ilike\)\.ilike\)\.ilike\(/g, '.ilike(')
      .replace(/\.in\)\.in\)\.in\(/g, '.in(')
      .replace(/\.contains\)\.contains\)\.contains\(/g, '.contains(')
      .replace(/\.match\)\.match\)\.match\(/g, '.match(')
      .replace(/\.filter\)\.filter\)\.filter\(/g, '.filter(')
      .replace(/\.order\)\.order\)\.order\(/g, '.order(')
      .replace(/\.limit\)\.limit\)\.limit\(/g, '.limit(')
      .replace(/\.range\)\.range\)\.range\(/g, '.range(')
      .replace(/\.single\)\.single\)\.single\(/g, '.single(')
      .replace(/\.maybeSingle\)\.maybeSingle\)\.maybeSingle\(/g, '.maybeSingle(')

      // Fix broken method chaining with semicolons
      .replace(/\.select\s*\([^)]*\)\s*;\s*\.eq\s*\(/g, '.select($1).eq(')
      .replace(/\.select\s*\([^)]*\)\s*;\s*\.is\s*\(/g, '.select($1).is(')
      .replace(/\.select\s*\([^)]*\)\s*;\s*\.neq\s*\(/g, '.select($1).neq(')
      .replace(/\.select\s*\([^)]*\)\s*;\s*\.gt\s*\(/g, '.select($1).gt(')
      .replace(/\.select\s*\([^)]*\)\s*;\s*\.lt\s*\(/g, '.select($1).lt(')
      .replace(/\.select\s*\([^)]*\)\s*;\s*\.gte\s*\(/g, '.select($1).gte(')
      .replace(/\.select\s*\([^)]*\)\s*;\s*\.lte\s*\(/g, '.select($1).lte(')
      .replace(/\.select\s*\([^)]*\)\s*;\s*\.like\s*\(/g, '.select($1).like(')
      .replace(/\.select\s*\([^)]*\)\s*;\s*\.ilike\s*\(/g, '.select($1).ilike(')
      .replace(/\.select\s*\([^)]*\)\s*;\s*\.in\s*\(/g, '.select($1).in(')
      .replace(/\.select\s*\([^)]*\)\s*;\s*\.contains\s*\(/g, '.select($1).contains(')
      .replace(/\.select\s*\([^)]*\)\s*;\s*\.match\s*\(/g, '.select($1).match(')
      .replace(/\.select\s*\([^)]*\)\s*;\s*\.filter\s*\(/g, '.select($1).filter(')
      .replace(/\.select\s*\([^)]*\)\s*;\s*\.order\s*\(/g, '.select($1).order(')
      .replace(/\.select\s*\([^)]*\)\s*;\s*\.limit\s*\(/g, '.select($1).limit(')
      .replace(/\.select\s*\([^)]*\)\s*;\s*\.range\s*\(/g, '.select($1).range(')
      .replace(/\.select\s*\([^)]*\)\s*;\s*\.single\s*\(/g, '.select($1).single(')
      .replace(/\.select\s*\([^)]*\)\s*;\s*\.maybeSingle\s*\(/g, '.select($1).maybeSingle(')

      // Fix insert/update/upsert method chaining
      .replace(
        /\.insert\s*\(\s*[^)]+\s*\)\s*;\s*\.select\s*\(\s*\)\s*;\s*\.single\s*\(\s*\)/g,
        '.insert($1).select().single()'
      )
      .replace(/\.update\s*\(\s*[^)]+\s*\)\s*;\s*\.eq\s*\(/g, '.update($1).eq(')
      .replace(
        /\.upsert\s*\(\s*[^)]+\s*\)\s*;\s*\.select\s*\(\s*\)\s*;\s*\.single\s*\(\s*\)/g,
        '.upsert($1).select().single()'
      )

      // Fix specific broken patterns
      .replace(/onConflict:\s*'[^']*'\s*;\s*\}/g, "onConflict: '$1' }")

      // Fix the personality.tsx specific issue
      .replace(
        /\.select\('profile_data, completion_percentage'\)\s*\.eq\('user_id', user\.id\);\s*\.maybeSingle\)\.maybeSingle\)\.maybeSingle\(\);/g,
        ".select('profile_data, completion_percentage').eq('user_id', user.id).maybeSingle();"
      )

      .replace(
        /\.select\('question_id, response_value'\);\s*\.eq\)\.eq\)\.eq\('user_id', user\.id\);/g,
        ".select('question_id, response_value').eq('user_id', user.id);"
      )

      .replace(
        /\.from\('user_personality_responses'\);\s*\.insert\(responsesToInsert\);/g,
        ".from('user_personality_responses').insert(responsesToInsert);"
      )

      // Fix general method chaining restoration
      .replace(/(\.\w+\([^)]*\))\s*;\s*(\.\w+\([^)]*\))/g, '$1$2')

      // Remove duplicate method calls
      .replace(/(\.\w+\([^)]*\))\1+/g, '$1')
  );
}

function processFile(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  File not found: ${filePath}`);
      return false;
    }

    const content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;

    const fixed = fixTripleCorruption(content);

    if (fixed !== originalContent) {
      fs.writeFileSync(filePath, fixed, 'utf8');
      console.log(`✅ Fixed corruption in: ${filePath}`);
      return true;
    } else {
      console.log(`📝 No corruption found in: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Error processing ${filePath}: ${error.message}`);
    return false;
  }
}

// Main execution
console.log(`\n🔧 Processing ${CORRUPTED_FILES.length} files with triple corruption...`);
let fixedCount = 0;

CORRUPTED_FILES.forEach(file => {
  if (processFile(file)) {
    fixedCount++;
  }
});

console.log(`\n🎉 TRIPLE CORRUPTION FIX COMPLETED!`);
console.log(`   - Files processed: ${CORRUPTED_FILES.length}`);
console.log(`   - Files fixed: ${fixedCount}`);
console.log(`   - Files unchanged: ${CORRUPTED_FILES.length - fixedCount}`);

if (fixedCount > 0) {
  console.log('\n✅ Supabase method chaining has been restored!');
  console.log('🚀 Try running the app again with: npm run start');
} else {
  console.log('\n⚠️  No corruption patterns were fixed. Manual inspection may be needed.');
}
