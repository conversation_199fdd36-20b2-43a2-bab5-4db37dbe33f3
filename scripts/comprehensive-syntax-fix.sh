#!/bin/bash

# WeRoomies Comprehensive Syntax Error Fix Script
# This script systematically fixes all critical syntax errors

echo "🔧 Starting comprehensive syntax error fixes..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Counter for fixes
fixes_applied=0

# Function to apply fix and count
apply_fix() {
    local file="$1"
    local description="$2"
    echo -e "${YELLOW}Fixing: ${description} in ${file}${NC}"
    fixes_applied=$((fixes_applied + 1))
}

echo -e "${BLUE}=== Phase 1: Critical Syntax Fixes ===${NC}"

# 1. Fix missing opening braces after function signatures
echo -e "${GREEN}1. Fixing missing opening braces after function signatures...${NC}"
find src/utils -name "*.ts" -exec sed -i '' 's/): Promise<[^>]*>$/&{/g' {} \;
find src/utils -name "*.ts" -exec sed -i '' 's/): Promise<[^>]*>\s*$/&{/g' {} \;
apply_fix "src/utils/*.ts" "missing opening braces after Promise returns"

# 2. Fix missing opening braces after regular function signatures
find src/utils -name "*.ts" -exec sed -i '' 's/): [A-Z][a-zA-Z]*\s*$/&{/g' {} \;
find src/utils -name "*.ts" -exec sed -i '' 's/): void\s*$/&{/g' {} \;
apply_fix "src/utils/*.ts" "missing opening braces after function signatures"

# 3. Fix missing commas in object literals
echo -e "${GREEN}2. Fixing missing commas in object literals...${NC}"
find src/utils -name "*.ts" -exec sed -i '' 's/;$/,/g' {} \;
apply_fix "src/utils/*.ts" "semicolons to commas in object literals"

# 4. Fix specific files with known issues
echo -e "${GREEN}3. Fixing specific known issues...${NC}"

# Fix dateUtils.ts - add missing closing brace if needed
if [ -f "src/utils/dateUtils.ts" ]; then
    # Check if file ends properly
    if ! tail -1 "src/utils/dateUtils.ts" | grep -q "}"; then
        echo "}" >> src/utils/dateUtils.ts
        apply_fix "src/utils/dateUtils.ts" "missing closing brace"
    fi
fi

# Fix compatibilityLayer.ts - add missing opening brace
if [ -f "src/utils/compatibilityLayer.ts" ]; then
    sed -i '' 's/getUsageStats(): Record<string, number>/getUsageStats(): Record<string, number> {/g' src/utils/compatibilityLayer.ts
    apply_fix "src/utils/compatibilityLayer.ts" "missing opening brace in getUsageStats"
fi

# Fix createCreateListingImageBucket.ts - change semicolon to comma
if [ -f "src/utils/createCreateListingImageBucket.ts" ]; then
    sed -i '' "s/allowedMimeTypes: \['image\/jpeg', 'image\/png', 'image\/webp', 'image\/gif'\];/allowedMimeTypes: ['image\/jpeg', 'image\/png', 'image\/webp', 'image\/gif'],/g" src/utils/createCreateListingImageBucket.ts
    apply_fix "src/utils/createCreateListingImageBucket.ts" "semicolon to comma in allowedMimeTypes"
fi

# Fix createSupabaseBucket.ts - change semicolon to comma
if [ -f "src/utils/createSupabaseBucket.ts" ]; then
    sed -i '' 's/allowedMimeTypes: config.allowedMimeTypes;/allowedMimeTypes: config.allowedMimeTypes,/g' src/utils/createSupabaseBucket.ts
    apply_fix "src/utils/createSupabaseBucket.ts" "semicolon to comma in allowedMimeTypes"
fi

# Fix seedAgreementTemplates.ts - remove duplicate property
if [ -f "src/utils/database/seedAgreementTemplates.ts" ]; then
    sed -i '' 's/template_category: string;,/template_category: string,/g' src/utils/database/seedAgreementTemplates.ts
    apply_fix "src/utils/database/seedAgreementTemplates.ts" "duplicate comma in template_category"
fi

# Fix databaseHealthCheck.ts - change semicolon to comma
if [ -f "src/utils/databaseHealthCheck.ts" ]; then
    sed -i '' 's/{ single: true };/{ single: true }/g' src/utils/databaseHealthCheck.ts
    apply_fix "src/utils/databaseHealthCheck.ts" "semicolon to comma in query options"
fi

# Fix EdgeCaseTestingSuite.ts - remove duplicate comma
if [ -f "src/utils/EdgeCaseTestingSuite.ts" ]; then
    sed -i '' "s/category: 'data' | 'ui' | 'network' | 'auth' | 'performance' | 'storage';,/category: 'data' | 'ui' | 'network' | 'auth' | 'performance' | 'storage',/g" src/utils/EdgeCaseTestingSuite.ts
    apply_fix "src/utils/EdgeCaseTestingSuite.ts" "duplicate comma in category type"
fi

# Fix enhancedErrorHandler.ts - remove duplicate comma
if [ -f "src/utils/enhancedErrorHandler.ts" ]; then
    sed -i '' 's/private getUserMessage(category: ErrorCategory): string {,/private getUserMessage(category: ErrorCategory): string {/g' src/utils/enhancedErrorHandler.ts
    apply_fix "src/utils/enhancedErrorHandler.ts" "duplicate comma in function signature"
fi

# Fix errorBoundary.tsx - add missing opening brace
if [ -f "src/utils/errorBoundary.tsx" ]; then
    sed -i '' 's/class ErrorBoundaryClass extends Component<ErrorBoundaryClassProps, State>/class ErrorBoundaryClass extends Component<ErrorBoundaryClassProps, State> {/g' src/utils/errorBoundary.tsx
    apply_fix "src/utils/errorBoundary.tsx" "missing opening brace in class declaration"
fi

echo -e "${BLUE}=== Phase 2: Method Chain Fixes ===${NC}"

# 5. Fix broken method chains
echo -e "${GREEN}4. Fixing broken method chains...${NC}"

# Fix databaseDebugger.ts - remove extra dot
if [ -f "src/utils/databaseDebugger.ts" ]; then
    sed -i '' 's/\.eq('\''table_name'\'', tableName\.toLowerCase());\s*\.limit(1);/.eq('\''table_name'\'', tableName.toLowerCase()).limit(1);/g' src/utils/databaseDebugger.ts
    apply_fix "src/utils/databaseDebugger.ts" "broken method chain"
fi

# Fix databaseSchemaAdapter.ts - remove extra dot
if [ -f "src/utils/databaseSchemaAdapter.ts" ]; then
    sed -i '' 's/\.eq('\''table_name'\'', tableName);\s*\.single()/.eq('\''table_name'\'', tableName).single()/g' src/utils/databaseSchemaAdapter.ts
    apply_fix "src/utils/databaseSchemaAdapter.ts" "broken method chain"
fi

# Fix profileAutoCreation.ts - remove extra dot
if [ -f "src/utils/profileAutoCreation.ts" ]; then
    sed -i '' 's/\.eq('\''id'\'', userId);\s*\.maybeSingle();/.eq('\''id'\'', userId).maybeSingle();/g' src/utils/profileAutoCreation.ts
    apply_fix "src/utils/profileAutoCreation.ts" "broken method chain"
fi

# Fix supabaseStorageService.ts - fix broken method chain
if [ -f "src/utils/supabaseStorageService.ts" ]; then
    sed -i '' 's/const { data: urlData } = supabase\.storage\.from(options\.bucket);\s*\.getPublicUrl(data\.path);/const { data: urlData } = supabase.storage.from(options.bucket).getPublicUrl(data.path);/g' src/utils/supabaseStorageService.ts
    apply_fix "src/utils/supabaseStorageService.ts" "broken method chain"
fi

# Fix supabaseUtils.ts - remove extra dot
if [ -f "src/utils/supabaseUtils.ts" ]; then
    sed -i '' 's/\.limit(limit);\s*\.range(offset, offset + limit - 1);/.limit(limit).range(offset, offset + limit - 1);/g' src/utils/supabaseUtils.ts
    apply_fix "src/utils/supabaseUtils.ts" "broken method chain"
fi

echo -e "${BLUE}=== Phase 3: Function Parameter Fixes ===${NC}"

# 6. Fix function parameter issues
echo -e "${GREEN}5. Fixing function parameter issues...${NC}"

# Fix withErrorHandling.tsx - add missing comma
if [ -f "src/utils/withErrorHandling.tsx" ]; then
    sed -i '' 's/Component: React\.ComponentType<P>\s*options: WithErrorHandlingOptions = {},/Component: React.ComponentType<P>, options: WithErrorHandlingOptions = {},/g' src/utils/withErrorHandling.tsx
    apply_fix "src/utils/withErrorHandling.tsx" "missing comma in function parameters"
fi

# Fix unifiedErrorHandler.ts - add missing comma
if [ -f "src/utils/unifiedErrorHandler.ts" ]; then
    sed -i '' 's/severity: UnifiedError\['\''severity'\''\]\s*context: ErrorContext/severity: UnifiedError['\''severity'\''], context: ErrorContext,/g' src/utils/unifiedErrorHandler.ts
    apply_fix "src/utils/unifiedErrorHandler.ts" "missing comma in function parameters"
fi

# Fix validationErrorUtils.ts - remove duplicate parameter
if [ -f "src/utils/validationErrorUtils.ts" ]; then
    sed -i '' 's/fieldErrors: FieldValidationErrors\s*fieldErrors: FieldValidationErrors,/fieldErrors: FieldValidationErrors,/g' src/utils/validationErrorUtils.ts
    apply_fix "src/utils/validationErrorUtils.ts" "duplicate parameter"
fi

echo -e "${BLUE}=== Phase 4: Try-Catch and Control Flow Fixes ===${NC}"

# 7. Fix try-catch blocks and control flow
echo -e "${GREEN}6. Fixing try-catch blocks and control flow...${NC}"

# Fix errorTrackerFix.ts - add missing try
if [ -f "src/utils/errorTrackerFix.ts" ]; then
    sed -i '' 's/errorTracker\.captureMessage(message, level, safeContext); {/try { errorTracker.captureMessage(message, level, safeContext);/g' src/utils/errorTrackerFix.ts
    apply_fix "src/utils/errorTrackerFix.ts" "missing try block"
fi

# Fix format.ts - add missing catch
if [ -f "src/utils/format.ts" ]; then
    sed -i '' 's/return String(date);\s*}\s*}/return String(date); } catch (error) { return String(date); } }/g' src/utils/format.ts
    apply_fix "src/utils/format.ts" "missing catch block"
fi

echo -e "${BLUE}=== Phase 5: Object and Array Fixes ===${NC}"

# 8. Fix object and array syntax issues
echo -e "${GREEN}7. Fixing object and array syntax issues...${NC}"

# Fix validation.ts - add missing comma
if [ -f "src/utils/validation.ts" ]; then
    sed -i '' 's/isValid: true\s*message: '\''Password must be at least 6 characters'\'',/isValid: true, message: '\''Password must be at least 6 characters'\'',/g' src/utils/validation.ts
    apply_fix "src/utils/validation.ts" "missing comma in object literal"
fi

# Fix verificationImageUpload.ts - remove duplicate property
if [ -f "src/utils/verificationImageUpload.ts" ]; then
    sed -i '' "s/passport: 'passport'\s*passport: 'passport',/passport: 'passport',/g" src/utils/verificationImageUpload.ts
    apply_fix "src/utils/verificationImageUpload.ts" "duplicate passport property"
fi

# Fix uploadToSupabase.ts - fix object syntax
if [ -f "src/utils/uploadToSupabase.ts" ]; then
    sed -i '' 's/contentType\s*error: '\''User not authenticated'\'',/contentType, }); if (!user) { throw new Error('\''User not authenticated'\''); } const { data, error } = await supabase.storage.from(bucket).upload(path, uploadData, { contentType,/g' src/utils/uploadToSupabase.ts
    apply_fix "src/utils/uploadToSupabase.ts" "broken object syntax"
fi

echo -e "${BLUE}=== Phase 6: TypeScript and Interface Fixes ===${NC}"

# 9. Fix TypeScript-specific issues
echo -e "${GREEN}8. Fixing TypeScript-specific issues...${NC}"

# Fix databaseValidation.ts - add missing opening brace
if [ -f "src/utils/databaseValidation.ts" ]; then
    sed -i '' 's/): Promise<ValidationResult<T>>\s*try {/): Promise<ValidationResult<T>> { try {/g' src/utils/databaseValidation.ts
    apply_fix "src/utils/databaseValidation.ts" "missing opening brace in function"
fi

# Fix indexOptimization.ts - add missing opening brace
if [ -f "src/utils/indexOptimization.ts" ]; then
    sed -i '' 's/async generateIndexHealthReport(): Promise<IndexHealthReport>{/async generateIndexHealthReport(): Promise<IndexHealthReport> {/g' src/utils/indexOptimization.ts
    apply_fix "src/utils/indexOptimization.ts" "missing space before opening brace"
fi

# Fix connectionPoolTest.ts - add missing opening brace
if [ -f "src/utils/connectionPoolTest.ts" ]; then
    sed -i '' 's/const startTime = Date\.now();\s*try {/const startTime = Date.now(); try {/g' src/utils/connectionPoolTest.ts
    apply_fix "src/utils/connectionPoolTest.ts" "missing function structure"
fi

echo -e "${BLUE}=== Phase 7: Final Cleanup ===${NC}"

# 10. Final cleanup and validation
echo -e "${GREEN}9. Performing final cleanup...${NC}"

# Remove any remaining trailing semicolons in object literals within functions
find src/utils -name "*.ts" -exec sed -i '' 's/\([^;]\);$/\1,/g' {} \;
apply_fix "src/utils/*.ts" "trailing semicolons to commas"

# Fix any remaining double commas
find src/utils -name "*.ts" -exec sed -i '' 's/,,/,/g' {} \;
apply_fix "src/utils/*.ts" "double commas"

# Fix any remaining double semicolons
find src/utils -name "*.ts" -exec sed -i '' 's/;;/;/g' {} \;
apply_fix "src/utils/*.ts" "double semicolons"

echo -e "${GREEN}=== Summary ===${NC}"
echo -e "${BLUE}Total fixes applied: ${fixes_applied}${NC}"
echo -e "${GREEN}✅ Comprehensive syntax fixes completed!${NC}"
echo ""
echo -e "${YELLOW}Next steps:${NC}"
echo "1. Run: npm run type-check"
echo "2. Run: npm run lint:fix"
echo "3. Review any remaining errors manually" 