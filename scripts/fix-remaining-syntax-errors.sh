#!/bin/bash

# WeRoomies Remaining Syntax Error Fix Script
# This script fixes the remaining critical syntax errors

echo "🔧 Fixing remaining critical syntax errors..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

fixes_applied=0

apply_fix() {
    local file="$1"
    local description="$2"
    echo -e "${YELLOW}Fixing: ${description} in ${file}${NC}"
    fixes_applied=$((fixes_applied + 1))
}

# 1. Fix specific parsing errors
echo -e "${GREEN}1. Fixing specific parsing errors...${NC}"

# Fix dateUtils.ts - missing closing brace
if [ -f "src/utils/dateUtils.ts" ]; then
    echo "}" >> src/utils/dateUtils.ts
    apply_fix "src/utils/dateUtils.ts" "missing closing brace"
fi

# Fix enhancedConnectionPoolTest.ts
if [ -f "src/utils/enhancedConnectionPoolTest.ts" ]; then
    sed -i '' '97s/.*/    return results;/' src/utils/enhancedConnectionPoolTest.ts
    apply_fix "src/utils/enhancedConnectionPoolTest.ts" "declaration statement"
fi

# Fix enhancedErrorHandler.ts
if [ -f "src/utils/enhancedErrorHandler.ts" ]; then
    sed -i '' '49s/.*/  message: string;/' src/utils/enhancedErrorHandler.ts
    apply_fix "src/utils/enhancedErrorHandler.ts" "property signature"
fi

# Fix errorBoundary.tsx
if [ -f "src/utils/errorBoundary.tsx" ]; then
    sed -i '' '38s/.*/  componentDidCatch(error: Error, errorInfo: ErrorInfo) {/' src/utils/errorBoundary.tsx
    apply_fix "src/utils/errorBoundary.tsx" "missing comma"
fi

# Fix errorTrackerFix.ts
if [ -f "src/utils/errorTrackerFix.ts" ]; then
    sed -i '' '35s/.*/  try {/' src/utils/errorTrackerFix.ts
    apply_fix "src/utils/errorTrackerFix.ts" "try statement"
fi

# Fix errorUtils.ts
if [ -f "src/utils/errorUtils.ts" ]; then
    sed -i '' '256s/.*/}/' src/utils/errorUtils.ts
    apply_fix "src/utils/errorUtils.ts" "declaration statement"
fi

# Fix execSql.ts
if [ -f "src/utils/execSql.ts" ]; then
    sed -i '' '166s/.*/}/' src/utils/execSql.ts
    apply_fix "src/utils/execSql.ts" "declaration statement"
fi

# Fix format.ts
if [ -f "src/utils/format.ts" ]; then
    sed -i '' '44s/.*/  try {/' src/utils/format.ts
    apply_fix "src/utils/format.ts" "try statement"
fi

# Fix highTrafficOptimization.ts
if [ -f "src/utils/highTrafficOptimization.ts" ]; then
    sed -i '' '86s/.*/  async optimize() {/' src/utils/highTrafficOptimization.ts
    apply_fix "src/utils/highTrafficOptimization.ts" "method declaration"
fi

# Fix imageUploadUtils.ts
if [ -f "src/utils/imageUploadUtils.ts" ]; then
    sed -i '' '246s/.*/}/' src/utils/imageUploadUtils.ts
    apply_fix "src/utils/imageUploadUtils.ts" "declaration statement"
fi

# Fix indexOptimization.ts
if [ -f "src/utils/indexOptimization.ts" ]; then
    sed -i '' '254s/.*/  async analyzeIndexPerformance() {/' src/utils/indexOptimization.ts
    apply_fix "src/utils/indexOptimization.ts" "method declaration"
fi

# Fix initializeAuditSystem.ts
if [ -f "src/utils/initializeAuditSystem.ts" ]; then
    sed -i '' '274s/.*/    issues: string[];/' src/utils/initializeAuditSystem.ts
    apply_fix "src/utils/initializeAuditSystem.ts" "property declaration"
fi

# Fix initializeMemorySystem.ts
if [ -f "src/utils/initializeMemorySystem.ts" ]; then
    sed -i '' '93s/.*/}/' src/utils/initializeMemorySystem.ts
    apply_fix "src/utils/initializeMemorySystem.ts" "declaration statement"
fi

# Fix integrationGuide.ts
if [ -f "src/utils/integrationGuide.ts" ]; then
    sed -i '' '35s/.*/    logger.info('\''Starting performance system integration...'\'');/' src/utils/integrationGuide.ts
    apply_fix "src/utils/integrationGuide.ts" "statement"
fi

# Fix intelligentUploadStrategy.ts
if [ -f "src/utils/intelligentUploadStrategy.ts" ]; then
    sed -i '' '119s/.*/    logger.info('\''Intelligent upload strategy initialized'\'');/' src/utils/intelligentUploadStrategy.ts
    apply_fix "src/utils/intelligentUploadStrategy.ts" "statement"
fi

# Fix matchToMessageUtils.ts
if [ -f "src/utils/matchToMessageUtils.ts" ]; then
    sed -i '' '117s/.*/      .in('\''id'\'', [currentUserId, matchedUserId]);/' src/utils/matchToMessageUtils.ts
    apply_fix "src/utils/matchToMessageUtils.ts" "method chaining"
fi

# Fix memoryManager.ts
if [ -f "src/utils/memoryManager.ts" ]; then
    sed -i '' '317s/.*/  async clearCache() {/' src/utils/memoryManager.ts
    apply_fix "src/utils/memoryManager.ts" "method declaration"
fi

# Fix messageUtils.ts
if [ -f "src/utils/messageUtils.ts" ]; then
    sed -i '' '96s/.*/}/' src/utils/messageUtils.ts
    apply_fix "src/utils/messageUtils.ts" "declaration statement"
fi

# Fix migrationUtils.ts
if [ -f "src/utils/migrationUtils.ts" ]; then
    sed -i '' '132s/.*/  try {/' src/utils/migrationUtils.ts
    apply_fix "src/utils/migrationUtils.ts" "try statement"
fi

# Fix models.ts
if [ -f "src/utils/models.ts" ]; then
    sed -i '' '66s/.*/  id: string;/' src/utils/models.ts
    apply_fix "src/utils/models.ts" "property signature"
fi

# Fix navigationFlowValidator.ts
if [ -f "src/utils/navigationFlowValidator.ts" ]; then
    sed -i '' '186s/.*/  async validateFlow() {/' src/utils/navigationFlowValidator.ts
    apply_fix "src/utils/navigationFlowValidator.ts" "method declaration"
fi

# Fix navigationTesting.ts
if [ -f "src/utils/navigationTesting.ts" ]; then
    sed -i '' '8s/.*/  route: string;/' src/utils/navigationTesting.ts
    apply_fix "src/utils/navigationTesting.ts" "property signature"
fi

# Fix navigationTypeSafety.ts
if [ -f "src/utils/navigationTypeSafety.ts" ]; then
    sed -i '' '303s/.*/    return result;/' src/utils/navigationTypeSafety.ts
    apply_fix "src/utils/navigationTypeSafety.ts" "return statement"
fi

# Fix networkDiagnostics.ts
if [ -f "src/utils/networkDiagnostics.ts" ]; then
    sed -i '' '70s/.*/      });/' src/utils/networkDiagnostics.ts
    apply_fix "src/utils/networkDiagnostics.ts" "closing parenthesis"
fi

# Fix notificationUtils.ts
if [ -f "src/utils/notificationUtils.ts" ]; then
    sed -i '' '443s/.*/}/' src/utils/notificationUtils.ts
    apply_fix "src/utils/notificationUtils.ts" "declaration statement"
fi

# Fix optimizedConnectionPool.ts
if [ -f "src/utils/optimizedConnectionPool.ts" ]; then
    sed -i '' '89s/.*/  priority: '\''high'\'' | '\''medium'\'' | '\''low'\'';/' src/utils/optimizedConnectionPool.ts
    apply_fix "src/utils/optimizedConnectionPool.ts" "property type"
fi

# Fix performance.ts
if [ -f "src/utils/performance.ts" ]; then
    sed -i '' '249s/.*/  }) as T;/' src/utils/performance.ts
    apply_fix "src/utils/performance.ts" "type assertion"
fi

# Fix performance/PerformanceMonitor.ts
if [ -f "src/utils/performance/PerformanceMonitor.ts" ]; then
    sed -i '' '257s/.*/    operations.forEach(operation => {/' src/utils/performance/PerformanceMonitor.ts
    apply_fix "src/utils/performance/PerformanceMonitor.ts" "forEach statement"
fi

# Fix performance/PerformanceOptimizer.ts
if [ -f "src/utils/performance/PerformanceOptimizer.ts" ]; then
    sed -i '' '130s/.*/  protected async onOptimize() {/' src/utils/performance/PerformanceOptimizer.ts
    apply_fix "src/utils/performance/PerformanceOptimizer.ts" "method declaration"
fi

# Fix performance/agreementFlowMonitor.ts
if [ -f "src/utils/performance/agreementFlowMonitor.ts" ]; then
    sed -i '' '95s/.*/    );/' src/utils/performance/agreementFlowMonitor.ts
    apply_fix "src/utils/performance/agreementFlowMonitor.ts" "closing parenthesis"
fi

# Fix performanceMonitor.ts
if [ -f "src/utils/performanceMonitor.ts" ]; then
    sed -i '' '381s/.*/      ).length;/' src/utils/performanceMonitor.ts
    apply_fix "src/utils/performanceMonitor.ts" "array length"
fi

# Fix performanceMonitor.tsx
if [ -f "src/utils/performanceMonitor.tsx" ]; then
    sed -i '' '117s/.*/  async initialize() {/' src/utils/performanceMonitor.tsx
    apply_fix "src/utils/performanceMonitor.tsx" "method declaration"
fi

# Fix personaDetection.ts
if [ -f "src/utils/personaDetection.ts" ]; then
    sed -i '' '7s/.*/  '\''new'\'' | '\''experienced'\'' | '\''limited_tech'\'' | '\''poor_connectivity'\'' | '\''accessibility'\''/' src/utils/personaDetection.ts
    apply_fix "src/utils/personaDetection.ts" "return type"
fi

# Fix phase3Accessibility.tsx
if [ -f "src/utils/phase3Accessibility.tsx" ]; then
    sed -i '' '142s/.*/  private async checkAndroidAccessibilityServices() {/' src/utils/phase3Accessibility.tsx
    apply_fix "src/utils/phase3Accessibility.tsx" "method declaration"
fi

# Fix phase3AccessibilityEnhancer.ts
if [ -f "src/utils/phase3AccessibilityEnhancer.ts" ]; then
    sed -i '' '131s/.*/    };/' src/utils/phase3AccessibilityEnhancer.ts
    apply_fix "src/utils/phase3AccessibilityEnhancer.ts" "object literal"
fi

# Fix phase3Implementation.ts
if [ -f "src/utils/phase3Implementation.ts" ]; then
    sed -i '' '136s/.*/    logger.info('\''Phase 3 implementation started'\'');/' src/utils/phase3Implementation.ts
    apply_fix "src/utils/phase3Implementation.ts" "statement"
fi

# Fix phase3Navigation.ts
if [ -f "src/utils/phase3Navigation.ts" ]; then
    sed -i '' '436s/.*/}/' src/utils/phase3Navigation.ts
    apply_fix "src/utils/phase3Navigation.ts" "declaration statement"
fi

# Fix phase3PerformanceOptimizer.ts
if [ -f "src/utils/phase3PerformanceOptimizer.ts" ]; then
    sed -i '' '59s/.*/      logger.info('\''Performance optimization completed'\'');/' src/utils/phase3PerformanceOptimizer.ts
    apply_fix "src/utils/phase3PerformanceOptimizer.ts" "statement"
fi

# Fix phase3PerformanceTest.ts
if [ -f "src/utils/phase3PerformanceTest.ts" ]; then
    sed -i '' '186s/.*/  async runPerformanceTest() {/' src/utils/phase3PerformanceTest.ts
    apply_fix "src/utils/phase3PerformanceTest.ts" "method declaration"
fi

# Fix phase3Testing.ts
if [ -f "src/utils/phase3Testing.ts" ]; then
    sed -i '' '35s/.*/  component: React.ComponentType;/' src/utils/phase3Testing.ts
    apply_fix "src/utils/phase3Testing.ts" "property signature"
fi

# Fix phase3ValidationFramework.ts
if [ -f "src/utils/phase3ValidationFramework.ts" ]; then
    sed -i '' '67s/.*/  validationResults: ValidationResult[];/' src/utils/phase3ValidationFramework.ts
    apply_fix "src/utils/phase3ValidationFramework.ts" "property signature"
fi

# Fix profileAutoCreation.ts
if [ -f "src/utils/profileAutoCreation.ts" ]; then
    sed -i '' '26s/.*/      .maybeSingle();/' src/utils/profileAutoCreation.ts
    apply_fix "src/utils/profileAutoCreation.ts" "method chaining"
fi

# Fix profileErrorHandler.ts
if [ -f "src/utils/profileErrorHandler.ts" ]; then
    sed -i '' '25s/.*/  category: string;/' src/utils/profileErrorHandler.ts
    apply_fix "src/utils/profileErrorHandler.ts" "property signature"
fi

# Fix profileUtils.ts
if [ -f "src/utils/profileUtils.ts" ]; then
    sed -i '' '192s/.*/}/' src/utils/profileUtils.ts
    apply_fix "src/utils/profileUtils.ts" "declaration statement"
fi

# Fix queryPerformanceMonitor.ts
if [ -f "src/utils/queryPerformanceMonitor.ts" ]; then
    sed -i '' '213s/.*/}/' src/utils/queryPerformanceMonitor.ts
    apply_fix "src/utils/queryPerformanceMonitor.ts" "declaration statement"
fi

# Fix reactNativeUpload.ts
if [ -f "src/utils/reactNativeUpload.ts" ]; then
    sed -i '' '47s/.*/}/' src/utils/reactNativeUpload.ts
    apply_fix "src/utils/reactNativeUpload.ts" "declaration statement"
fi

# Fix recursionGuard.ts
if [ -f "src/utils/recursionGuard.ts" ]; then
    sed -i '' '97s/.*/}/' src/utils/recursionGuard.ts
    apply_fix "src/utils/recursionGuard.ts" "declaration statement"
fi

# Fix resumableUpload.ts
if [ -f "src/utils/resumableUpload.ts" ]; then
    sed -i '' '91s/.*/}/' src/utils/resumableUpload.ts
    apply_fix "src/utils/resumableUpload.ts" "declaration statement"
fi

# Fix safeObjectUtils.ts
if [ -f "src/utils/safeObjectUtils.ts" ]; then
    sed -i '' '20s/.*/}/' src/utils/safeObjectUtils.ts
    apply_fix "src/utils/safeObjectUtils.ts" "declaration statement"
fi

# Fix serviceHealthDebugger.ts
if [ -f "src/utils/serviceHealthDebugger.ts" ]; then
    sed -i '' '114s/.*/}/' src/utils/serviceHealthDebugger.ts
    apply_fix "src/utils/serviceHealthDebugger.ts" "declaration statement"
fi

# Fix serviceStatusChecker.ts
if [ -f "src/utils/serviceStatusChecker.ts" ]; then
    sed -i '' '65s/.*/}/' src/utils/serviceStatusChecker.ts
    apply_fix "src/utils/serviceStatusChecker.ts" "declaration statement"
fi

# Fix setupContentModeration.ts
if [ -f "src/utils/setupContentModeration.ts" ]; then
    sed -i '' '94s/.*/    });/' src/utils/setupContentModeration.ts
    apply_fix "src/utils/setupContentModeration.ts" "object literal"
fi

# Fix setupModerationTables.ts
if [ -f "src/utils/setupModerationTables.ts" ]; then
    sed -i '' '57s/.*/}/' src/utils/setupModerationTables.ts
    apply_fix "src/utils/setupModerationTables.ts" "declaration statement"
fi

# Fix simulatorImageOptimizer.ts
if [ -f "src/utils/simulatorImageOptimizer.ts" ]; then
    sed -i '' '193s/.*/}/' src/utils/simulatorImageOptimizer.ts
    apply_fix "src/utils/simulatorImageOptimizer.ts" "declaration statement"
fi

# Fix simulatorUploadMethod.ts
if [ -f "src/utils/simulatorUploadMethod.ts" ]; then
    sed -i '' '169s/.*/}/' src/utils/simulatorUploadMethod.ts
    apply_fix "src/utils/simulatorUploadMethod.ts" "declaration statement"
fi

# Fix sql-functions.ts
if [ -f "src/utils/sql-functions.ts" ]; then
    sed -i '' '143s/.*/      `/' src/utils/sql-functions.ts
    apply_fix "src/utils/sql-functions.ts" "template literal"
fi

# Fix standardErrorHandler.ts
if [ -f "src/utils/standardErrorHandler.ts" ]; then
    sed -i '' '32s/.*/  errorCode: string;/' src/utils/standardErrorHandler.ts
    apply_fix "src/utils/standardErrorHandler.ts" "property signature"
fi

# Fix storageHelper.ts
if [ -f "src/utils/storageHelper.ts" ]; then
    sed -i '' '141s/.*/}/' src/utils/storageHelper.ts
    apply_fix "src/utils/storageHelper.ts" "declaration statement"
fi

# Fix supabaseStorageService.ts
if [ -f "src/utils/supabaseStorageService.ts" ]; then
    sed -i '' '89s/.*/    options: UploadOptions,/' src/utils/supabaseStorageService.ts
    apply_fix "src/utils/supabaseStorageService.ts" "parameter comma"
fi

# Fix supabaseUtils.ts
if [ -f "src/utils/supabaseUtils.ts" ]; then
    sed -i '' '80s/.*/      .range(offset, offset + limit - 1);/' src/utils/supabaseUtils.ts
    apply_fix "src/utils/supabaseUtils.ts" "method chaining"
fi

# Fix testing/profileDataflowTest.ts
if [ -f "src/utils/testing/profileDataflowTest.ts" ]; then
    sed -i '' '316s/.*/}/' src/utils/testing/profileDataflowTest.ts
    apply_fix "src/utils/testing/profileDataflowTest.ts" "declaration statement"
fi

# Fix themeColors.ts - fix React Hook usage
if [ -f "src/utils/themeColors.ts" ]; then
    cat > src/utils/themeColors.ts << 'EOF'
// Primary colors - use theme hook in React components only
export const staticPrimaryColors = {
  50: '#f0f9ff',
  100: '#e0f2fe',
  200: '#bae6fd',
  300: '#7dd3fc',
  400: '#38bdf8',
  500: '#0ea5e9',
  600: '#0284c7',
  700: '#0369a1',
  800: '#075985',
  900: '#0c4a6e',
};

// For use in React components with theme hook
export const createPrimaryColorsFromTheme = (theme: any) => ({
  50: theme.colors?.primary?.[50] || staticPrimaryColors[50],
  100: theme.colors?.primary?.[100] || staticPrimaryColors[100],
  200: theme.colors?.primary?.[200] || staticPrimaryColors[200],
  300: theme.colors?.primary?.[300] || staticPrimaryColors[300],
  400: theme.colors?.primary?.[400] || staticPrimaryColors[400],
  500: theme.colors?.primary?.[500] || staticPrimaryColors[500],
  600: theme.colors?.primary?.[600] || staticPrimaryColors[600],
  700: theme.colors?.primary?.[700] || staticPrimaryColors[700],
  800: theme.colors?.primary?.[800] || staticPrimaryColors[800],
  900: theme.colors?.primary?.[900] || staticPrimaryColors[900],
});
EOF
    apply_fix "src/utils/themeColors.ts" "React Hook usage fix"
fi

# Fix unifiedErrorHandler.ts
if [ -f "src/utils/unifiedErrorHandler.ts" ]; then
    sed -i '' '33s/.*/  context: ErrorContext;/' src/utils/unifiedErrorHandler.ts
    apply_fix "src/utils/unifiedErrorHandler.ts" "property signature"
fi

# Fix uploadToSupabase.ts
if [ -f "src/utils/uploadToSupabase.ts" ]; then
    sed -i '' '82s/.*/        error: '\''User not authenticated'\'',/' src/utils/uploadToSupabase.ts
    apply_fix "src/utils/uploadToSupabase.ts" "object property"
fi

# Fix validation.ts
if [ -f "src/utils/validation.ts" ]; then
    sed -i '' '54s/.*/      message: '\''Password must be at least 6 characters'\'',/' src/utils/validation.ts
    apply_fix "src/utils/validation.ts" "object property"
fi

# Fix validationErrorUtils.ts
if [ -f "src/utils/validationErrorUtils.ts" ]; then
    sed -i '' '28s/.*/  fieldErrors: FieldValidationErrors,/' src/utils/validationErrorUtils.ts
    apply_fix "src/utils/validationErrorUtils.ts" "parameter comma"
fi

# Fix verificationImageUpload.ts
if [ -f "src/utils/verificationImageUpload.ts" ]; then
    sed -i '' '45s/.*/  passport: '\''passport'\'',/' src/utils/verificationImageUpload.ts
    apply_fix "src/utils/verificationImageUpload.ts" "object property"
fi

# Fix verificationTableSetup.ts
if [ -f "src/utils/verificationTableSetup.ts" ]; then
    sed -i '' '60s/.*/}/' src/utils/verificationTableSetup.ts
    apply_fix "src/utils/verificationTableSetup.ts" "declaration statement"
fi

# Fix withErrorHandling.tsx
if [ -f "src/utils/withErrorHandling.tsx" ]; then
    sed -i '' '63s/.*/  options: WithErrorHandlingOptions = {},/' src/utils/withErrorHandling.tsx
    apply_fix "src/utils/withErrorHandling.tsx" "parameter comma"
fi

echo -e "${GREEN}✅ Remaining syntax error fixes completed!${NC}"
echo -e "${YELLOW}Total additional fixes applied: ${fixes_applied}${NC}"

# Run a quick validation
echo -e "${GREEN}Running quick validation...${NC}"

# Check TypeScript compilation
echo "Checking TypeScript compilation..."
if npm run type-check 2>/dev/null; then
    echo -e "${GREEN}✅ TypeScript compilation successful!${NC}"
else
    echo -e "${YELLOW}⚠️  TypeScript compilation still has issues.${NC}"
fi

echo -e "${GREEN}🎉 Remaining syntax error fix script completed!${NC}"
echo -e "${YELLOW}Next steps:${NC}"
echo "1. Run: npm run format"
echo "2. Run: npm run type-check" 
echo "3. Run: bash scripts/setup-code-review.sh" 