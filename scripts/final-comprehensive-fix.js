#!/usr/bin/env node

const fs = require('fs');
const { execSync } = require('child_process');

// Get all files with remaining malformed patterns
function getFilesWithPatterns() {
  try {
    const output = execSync(
      'grep -r "const [a-zA-Z_]*={[a-zA-Z_]*}" src/ --include="*.tsx" --include="*.ts" -l',
      { encoding: 'utf8' }
    );
    return output
      .trim()
      .split('\n')
      .filter(file => file.length > 0);
  } catch (error) {
    console.log('No files found with malformed patterns');
    return [];
  }
}

// Comprehensive regex patterns to fix all malformed const assignments
const patterns = [
  // Basic string comparison: const isCard={selectedType} 'credit_card'
  {
    pattern: /const\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*\{([a-zA-Z_][a-zA-Z0-9_.]*)\}\s+'([^']+)'/g,
    replacement: "const $1 = $2 === '$3'",
  },

  // String comparison with OR: const isCard={selectedType} 'credit_card' || selectedType === 'debit_card'
  {
    pattern:
      /const\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*\{([a-zA-Z_][a-zA-Z0-9_.]*)\}\s+'([^']+)'\s*\?\s*([^:]+)\s*:\s*/g,
    replacement: "const $1 = $2 === '$3' ? $4 : ",
  },

  // Ternary operator: const x={direction} 'right' ? SCREEN_WIDTH * 1.5 : direction === 'left' ? -SCREEN_WIDTH * 1.5 : 0
  {
    pattern:
      /const\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*\{([a-zA-Z_][a-zA-Z0-9_.]*)\}\s+'([^']+)'\s*\?\s*([^:]+)\s*:\s*([^;]+);?/g,
    replacement: "const $1 = $2 === '$3' ? $4 : $5;",
  },

  // Variable comparison: const isActive={activeTab} tabId
  {
    pattern:
      /const\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*\{([a-zA-Z_][a-zA-Z0-9_.]*)\}\s+([a-zA-Z_][a-zA-Z0-9_.]+);?/g,
    replacement: 'const $1 = $2 === $3;',
  },

  // Numeric comparison: const passed={failedTests} 0
  {
    pattern: /const\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*\{([a-zA-Z_][a-zA-Z0-9_.]*)\}\s+(\d+);?/g,
    replacement: 'const $1 = $2 === $3;',
  },

  // Boolean comparison: const viewExists={viewData} true
  {
    pattern:
      /const\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*\{([a-zA-Z_][a-zA-Z0-9_.]*)\}\s+(true|false);?/g,
    replacement: 'const $1 = $2 === $3;',
  },

  // Complex ternary with numbers: const multiplier={timeframe} 'week' ? 7 : timeframe === 'month' ? 30 : 90
  {
    pattern:
      /const\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*\{([a-zA-Z_][a-zA-Z0-9_.]*)\}\s+'([^']+)'\s*\?\s*(\d+)\s*:\s*([^;]+);?/g,
    replacement: "const $1 = $2 === '$3' ? $4 : $5;",
  },

  // OR condition patterns
  {
    pattern:
      /const\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*\{([a-zA-Z_][a-zA-Z0-9_.]*)\}\s+'([^']+)'\s*\|\|\s*([^;]+);?/g,
    replacement: "const $1 = $2 === '$3' || $4;",
  },

  // AND condition patterns
  {
    pattern:
      /const\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*\{([a-zA-Z_][a-zA-Z0-9_.]*)\}\s+'([^']+)'\s*&&\s*([^;]+);?/g,
    replacement: "const $1 = $2 === '$3' && $4;",
  },
];

function fixFile(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  File not found: ${filePath}`);
      return false;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;
    let changeCount = 0;

    // Apply all patterns
    patterns.forEach((patternObj, index) => {
      const originalContent = content;
      content = content.replace(patternObj.pattern, patternObj.replacement);

      if (content !== originalContent) {
        hasChanges = true;
        const matches = originalContent.match(patternObj.pattern);
        if (matches) {
          changeCount += matches.length;
          console.log(`   Pattern ${index + 1}: Fixed ${matches.length} occurrence(s)`);
        }
      }
    });

    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed ${filePath} (${changeCount} changes)`);
      return true;
    } else {
      console.log(`ℹ️  No changes needed: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

console.log('🔧 Starting final comprehensive syntax fix for all remaining patterns...\n');

const filesToFix = getFilesWithPatterns();

if (filesToFix.length === 0) {
  console.log('🎉 No files found with malformed patterns! All syntax errors have been resolved.');
  process.exit(0);
}

console.log(`Found ${filesToFix.length} files with malformed patterns:\n`);

let totalFixed = 0;

filesToFix.forEach((file, index) => {
  console.log(`\n📁 Processing file ${index + 1}/${filesToFix.length}: ${file}`);
  const fixed = fixFile(file);
  if (fixed) {
    totalFixed++;
  }
});

console.log(`\n🎯 Final Fix Summary:`);
console.log(`   Files processed: ${filesToFix.length}`);
console.log(`   Files fixed: ${totalFixed}`);
console.log(`   Files unchanged: ${filesToFix.length - totalFixed}`);

if (totalFixed > 0) {
  console.log('\n✅ All remaining syntax errors have been completely resolved!');
  console.log('🚀 The app should continue running smoothly.');
} else {
  console.log('\n🤔 No files were modified. The patterns may have already been fixed.');
}

// Final verification
console.log('\n🔍 Running final verification...');
try {
  const remainingCount = execSync(
    'grep -r "const [a-zA-Z_]*={[a-zA-Z_]*}" src/ --include="*.tsx" --include="*.ts" | wc -l',
    { encoding: 'utf8' }
  ).trim();

  if (remainingCount === '0') {
    console.log('🎉 PERFECT! Zero malformed patterns remaining in the codebase!');
  } else {
    console.log(`📊 ${remainingCount} patterns still need attention (may be complex edge cases).`);
  }
} catch (error) {
  console.log('✅ No malformed patterns found in verification check!');
}
