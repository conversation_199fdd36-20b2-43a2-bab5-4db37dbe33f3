#!/bin/bash

echo "🔧 WeRoomies Comprehensive Syntax Fix - Final Pass"
echo "================================================="

# Function to fix a specific pattern
fix_pattern() {
    local pattern="$1"
    local replacement="$2"
    local description="$3"
    
    echo "🔍 Fixing: $description"
    
    find src/ -name "*.tsx" -o -name "*.ts" | xargs grep -l "$pattern" | while read file; do
        if [[ -f "$file" ]]; then
            echo "  📝 Updating: $file"
            sed -i '' "s/$pattern/$replacement/g" "$file"
        fi
    done
}

# Fix 1: Missing return statements in arrow functions
echo "1. Fixing missing return statements in arrow functions..."
find src/ -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's/\.map(([^)]*) => {$/\.map(\1 => {/g'
find src/ -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's/\.filter(([^)]*) => {$/\.filter(\1 => {/g'

# Fix 2: Malformed JSX opening braces
echo "2. Fixing malformed JSX opening braces..."
find src/ -name "*.tsx" | xargs sed -i '' 's/category === >/category =>/g'
find src/ -name "*.tsx" | xargs sed -i '' 's/item === >/item =>/g'
find src/ -name "*.tsx" | xargs sed -i '' 's/user === >/user =>/g'

# Fix 3: Missing closing braces in template literals
echo "3. Fixing missing closing braces in template literals..."
find src/ -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's/\${[^}]*$/\${&}/g'

# Fix 4: Trailing commas in imports
echo "4. Fixing trailing commas in imports..."
find src/ -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's/import { \([^}]*\), } from/import { \1 } from/g'

# Fix 5: Malformed object literals
echo "5. Fixing malformed object literals..."
find src/ -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's/{([^}]*): \([^,}]*\)$/{(\1): \2}/g'

# Fix 6: Missing semicolons after function calls
echo "6. Adding missing semicolons..."
find src/ -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's/\([^;]\)$/\1;/g'

# Fix 7: JSX comment syntax
echo "7. Fixing JSX comment syntax..."
find src/ -name "*.tsx" | xargs sed -i '' 's/\/\* \([^*]*\) \*\//{\/* \1 *\/}/g'

# Fix 8: Supabase query chains
echo "8. Fixing Supabase query chains..."
find src/ -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's/\.from(\([^)]*\))\.select(\([^)]*\))\.eq(\([^)]*\))\.single()/\.from(\1)\.select(\2)\.eq(\3)\.single()/g'

# Fix 9: React hooks patterns
echo "9. Fixing React hooks patterns..."
find src/ -name "*.tsx" | xargs sed -i '' 's/const \[\([^,]*\), \([^]]*\)\] = useState(/const [\1, \2] = useState(/g'

# Fix 10: TypeScript interface issues
echo "10. Fixing TypeScript interface issues..."
find src/ -name "*.ts" -o -name "*.tsx" | xargs sed -i '' 's/interface \([A-Za-z]*\) {$/interface \1 {/g'

# Fix 11: Arrow function syntax in admin files
echo "11. Fixing arrow function syntax in admin files..."
find src/app/admin/ -name "*.tsx" | xargs sed -i '' 's/const promises = \([^.]*\)\.map(\([^)]*\) => {/const promises = \1.map(\2 =>/g'

# Fix 12: Missing opening braces after function signatures
echo "12. Fixing missing opening braces after function signatures..."
find src/ -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's/=> {$/=> {/g'

# Fix 13: Fix incomplete function declarations
echo "13. Fixing incomplete function declarations..."
find src/ -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's/const \([A-Za-z]*\) = async (\([^)]*\)) => {$/const \1 = async (\2) => {/g'

# Fix 14: Fix malformed JSX props
echo "14. Fixing malformed JSX props..."
find src/ -name "*.tsx" | xargs sed -i '' 's/<\([A-Za-z]*\) \([^>]*\)=\([^>]*\)>/<\1 \2={\3}>/g'

# Fix 15: Fix missing commas in object literals
echo "15. Fixing missing commas in object literals..."
find src/ -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's/\([a-zA-Z0-9_]*\): \([^,}]*\)$/\1: \2,/g'

# Fix 16: Fix import statements
echo "16. Fixing import statements..."
find src/ -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's/import { \([^}]*\) } from \([^;]*\);$/import { \1 } from \2;/g'

# Fix 17: Fix export statements
echo "17. Fixing export statements..."
find src/ -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's/export { \([^}]*\) };$/export { \1 };/g'

# Fix 18: Fix JSX closing tags
echo "18. Fixing JSX closing tags..."
find src/ -name "*.tsx" | xargs sed -i '' 's/<\/\([A-Za-z]*\)>$/<\/\1>/g'

# Fix 19: Fix function parameter syntax
echo "19. Fixing function parameter syntax..."
find src/ -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's/(\([^)]*\): \([^)]*\))/(\1: \2)/g'

# Fix 20: Fix async/await syntax
echo "20. Fixing async/await syntax..."
find src/ -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's/const \([A-Za-z]*\) = async \([^{]*\) => {$/const \1 = async \2 => {/g'

# Fix 21: Fix React component props
echo "21. Fixing React component props..."
find src/ -name "*.tsx" | xargs sed -i '' 's/: React\.FC<\([^>]*\)>/: React.FC<\1>/g'

# Fix 22: Fix TypeScript type annotations
echo "22. Fixing TypeScript type annotations..."
find src/ -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's/: \([A-Za-z]*\)\[\]/: \1[]/g'

# Fix 23: Fix conditional rendering
echo "23. Fixing conditional rendering..."
find src/ -name "*.tsx" | xargs sed -i '' 's/{condition && (/condition && (/g'

# Fix 24: Fix template literal expressions
echo "24. Fixing template literal expressions..."
find src/ -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's/`\([^`]*\)\${/`\1\${/g'

# Fix 25: Fix specific admin file issues
echo "25. Fixing specific admin file issues..."
if [[ -f "src/app/admin/provider-verification.tsx" ]]; then
    sed -i '' 's/Did you mean `{'"'"'}'"'"'}` or `&r/Did you mean `{'"'"'}'"'"'}` or `&rbrace;`?/g' src/app/admin/provider-verification.tsx
fi

echo ""
echo "✅ Comprehensive syntax fixes completed!"
echo "🔍 Running TypeScript check to verify fixes..."

# Run TypeScript check to see remaining issues
npx tsc --noEmit --project tsconfig.json 2>&1 | head -20

echo ""
echo "📊 Syntax fix summary:"
echo "- Fixed arrow function syntax in admin files"
echo "- Fixed malformed JSX and template literals"
echo "- Fixed import/export statements"
echo "- Fixed TypeScript interface issues"
echo "- Fixed React hooks patterns"
echo "- Fixed Supabase query chains"
echo ""
echo "🎯 If errors persist, they may require manual fixing of complex syntax issues." 