const fs = require('fs');

console.log('🔧 FINAL MALFORMED PATTERN FIX');
console.log('=============================');

// Additional files with malformed patterns
const FINAL_FILES = [
  'src/core/errors/index.ts',
  'src/core/services/BaseService.ts',
  'src/core/services/AdvancedCacheManager.ts',
  'src/app/(tabs)/create.tsx',
  'src/app/(tabs)/profile/unified-service-provider.tsx',
  'src/app/(tabs)/profile/payment-methods.tsx',
  'src/app/(tabs)/profile/profile-performance.tsx',
  'src/app/(tabs)/profile/notifications.tsx',
];

function fixFinalMalformedPatterns(content) {
  return (
    content
      // Fix: const isProduction={environment} 'production';
      .replace(/const\s+(\w+)=\{([^}]+)\}\s+'([^']+)';/g, "const $1 = $2 === '$3';")

      // Fix: const waitTime={backoff} 'exponential' ? delay * Math.pow(2, attempt - 1) : delay * attempt;
      .replace(
        /const\s+(\w+)=\{([^}]+)\}\s+'([^']+)'\s*\?\s*([^:]+)\s*:\s*([^;]+);/g,
        "const $1 = $2 === '$3' ? $4 : $5;"
      )

      // Fix: const cache={layer} CacheLayer.MEMORY ? this.memoryCache : this.persistentCache;
      .replace(
        /const\s+(\w+)=\{([^}]+)\}\s+([^?]+)\?\s*([^:]+)\s*:\s*([^;]+);/g,
        'const $1 = $2 === $3 ? $4 : $5;'
      )

      // Fix: const isCard={selectedType} 'credit_card' || selectedType === 'debit_card';
      .replace(
        /const\s+(\w+)=\{([^}]+)\}\s+'([^']+)'\s*\|\|\s*([^;]+);/g,
        "const $1 = $2 === '$3' || $4;"
      )

      // Fix: const isActive={selectedTab} tab.id;
      .replace(/const\s+(\w+)=\{([^}]+)\}\s+(\w+)\.(\w+);/g, 'const $1 = $2 === $3.$4;')

      // Fix: const isActive={activeTab} tab.id;
      .replace(/const\s+(\w+)=\{([^}]+)\}\s+(\w+);/g, 'const $1 = $2 === $3;')

      // Fix complex malformed patterns with multiple conditions
      .replace(
        /const\s+(\w+)=\{([^}]+)\}\s+'([^']+)'\s*\|\|\s*(\w+)\s*===\s*'([^']+)'/g,
        "const $1 = $2 === '$3' || $4 === '$5'"
      )

      // Fix JSX style patterns that got corrupted
      .replace(/<Text style=\s*\(([^)]+)\)\s*=>\s*\{([^}]+)\}/g, '<Text style={[$1, { $2 }]}')
      .replace(
        /<SafeAreaView style=\s*\(([^)]+)\)\s*=>\s*\{([^}]+)\}/g,
        '<SafeAreaView style={[$1, { $2 }]}'
      )

      // Fix return statement patterns that got corrupted
      .replace(
        /return\s*\(\s*<SafeAreaView style=\s*\(([^)]+)\)\s*=>/g,
        'return (\\n    <SafeAreaView style={$1}>'
      )

      // Fix general malformed object destructuring patterns
      .replace(/const\s+(\w+)=\{([^}]+)\}\s+([^=;]+)=/g, 'const $1 = $2 === $3 =')

      // Fix missing spaces and operators
      .replace(/const\s+(\w+)=\{([^}]+)\}([^=;]+);/g, 'const $1 = $2 === $3;')
  );
}

function processFile(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  File not found: ${filePath}`);
      return false;
    }

    const content = fs.readFileSync(filePath, 'utf8');
    const fixedContent = fixFinalMalformedPatterns(content);

    if (content !== fixedContent) {
      fs.writeFileSync(filePath, fixedContent);
      console.log(`✅ Fixed: ${filePath}`);
      return true;
    } else {
      console.log(`ℹ️  No changes needed: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Process all final files
console.log('\\nProcessing final malformed patterns...');
let fixedCount = 0;

FINAL_FILES.forEach(file => {
  if (processFile(file)) {
    fixedCount++;
  }
});

console.log(`\\n🎉 FINAL MALFORMED PATTERN FIX COMPLETE`);
console.log(`📊 Files processed: ${FINAL_FILES.length}`);
console.log(`✅ Files fixed: ${fixedCount}`);
console.log(`ℹ️  Files unchanged: ${FINAL_FILES.length - fixedCount}`);
