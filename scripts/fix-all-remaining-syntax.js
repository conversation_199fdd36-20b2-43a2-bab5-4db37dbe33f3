#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Files that need complete reconstruction due to severe corruption
const corruptedFiles = [
  'src/app/(tabs)/profile/video-intro.tsx',
  'src/app/(tabs)/room/[id].tsx',
  'src/app/(tabs)/search/ai-search-dashboard.tsx',
  'src/app/admin/behavioral-analytics.tsx',
];

// Comprehensive list of files with malformed patterns
const filesToFix = [
  'src/components/ui/FavoriteButton.tsx',
  'src/components/media/CorrectedBucketUploader.tsx',
  'src/components/debug/SafetySystemDebugger.tsx',
  'src/components/services/AvailabilityCalendarView.tsx',
  'src/components/ui/StepProgress.tsx',
  'src/components/ui/Divider.tsx',
  'src/components/ui/core/Card.tsx',
  'src/components/profile/PersonaOptimizedOnboarding.tsx',
  'src/components/profile/ProfileMemorySection.tsx',
  'src/components/common/SaveButton.tsx',
  'src/components/common/MemoryBank/SaveToMemoryBank.tsx',
  'src/components/serviceProvider/EnhancedServiceProviderDashboard.tsx',
  'src/components/common/ListingCard.tsx',
  'src/components/verification/TrustScoreBadge.tsx',
  'src/components/admin/BulkOperationsBar.tsx',
  'src/components/agreement/VersionHistory.tsx',
  'src/components/agreement/AgreementCustomizer.tsx',
  'src/components/matches/MatchActionButtons.tsx',
  'src/components/agreement/ChatToAgreementConnector.tsx',
  'src/components/matching/SwipeCard.tsx',
  'src/components/matching/SwipeMatchCard.tsx',
  'src/features/home/<USER>/HomeScreen.tsx',
  'src/app/household/index.tsx',
  'src/app/verification/simple-flow.tsx',
  'src/app/supabase-diagnostic.tsx',
  'src/app/provider/onboarding.tsx',
  'src/app/agreement/review.tsx',
  'src/app/admin/_layout.tsx',
  'src/app/admin/settings/index.tsx',
  'src/app/(tabs)/profile/media.tsx',
  'src/app/(tabs)/profile/payment-methods.tsx',
  'src/features/home/<USER>/WelcomeHeader.tsx',
];

// Comprehensive regex patterns to fix malformed const assignments
const patterns = [
  // Basic comparison patterns
  {
    pattern:
      /const\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*\{([a-zA-Z_][a-zA-Z0-9_.]*)\}\s+([a-zA-Z0-9_'".\[\]]+);?/g,
    replacement: 'const $1 = $2 === $3;',
  },

  // String comparison patterns
  {
    pattern: /const\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*\{([a-zA-Z_][a-zA-Z0-9_.]*)\}\s+'([^']+)'/g,
    replacement: "const $1 = $2 === '$3'",
  },

  // Ternary operator patterns
  {
    pattern:
      /const\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*\{([a-zA-Z_][a-zA-Z0-9_.]*)\}\s+'([^']+)'\s*\?\s*([^:]+)\s*:\s*([^;]+);?/g,
    replacement: "const $1 = $2 === '$3' ? $4 : $5;",
  },

  // OR condition patterns
  {
    pattern:
      /const\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*\{([a-zA-Z_][a-zA-Z0-9_.]*)\}\s+'([^']+)'\s*\|\|\s*([^;]+);?/g,
    replacement: "const $1 = $2 === '$3' || $4;",
  },

  // AND condition patterns
  {
    pattern:
      /const\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*\{([a-zA-Z_][a-zA-Z0-9_.]*)\}\s+'([^']+)'\s*&&\s*([^;]+);?/g,
    replacement: "const $1 = $2 === '$3' && $4;",
  },

  // Numeric comparison patterns
  {
    pattern: /const\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*\{([a-zA-Z_][a-zA-Z0-9_.]*)\}\s+(\d+);?/g,
    replacement: 'const $1 = $2 === $3;',
  },

  // Variable comparison patterns
  {
    pattern:
      /const\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*\{([a-zA-Z_][a-zA-Z0-9_.]*)\}\s+([a-zA-Z_][a-zA-Z0-9_.]+);?/g,
    replacement: 'const $1 = $2 === $3;',
  },

  // Complex ternary with multiple conditions
  {
    pattern:
      /const\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*\{([a-zA-Z_][a-zA-Z0-9_.]*)\}\s+'([^']+)'\s*\?\s*\{([^}]+)\}\s*:\s*([^;]+);?/g,
    replacement: "const $1 = $2 === '$3' ? { $4 } : $5;",
  },

  // Object property access patterns
  {
    pattern:
      /const\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*\{([a-zA-Z_][a-zA-Z0-9_.]*)\}\s+'([^']+)'\s*\?\s*([^:]+)\s*:/g,
    replacement: "const $1 = $2 === '$3' ? $4 :",
  },

  // Array/object access patterns
  {
    pattern:
      /const\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*\{([a-zA-Z_][a-zA-Z0-9_.]*)\}\s+'([^']+)'\s*\?\s*([^:]+)\s*:\s*([^;]+)\s*;/g,
    replacement: "const $1 = $2 === '$3' ? $4 : $5;",
  },
];

// Function to fix all types of syntax errors
function fixAllSyntaxErrors(content) {
  // Fix malformed arrow functions with missing braces
  content = content.replace(/=>\s*\n\s*([^{])/gm, '=> {\n  $1');

  // Fix malformed ternary operators
  content = content.replace(/\)\s*:\s*\(\s*\{/g, ') : (');

  // Fix theme declaration errors
  content = content.replace(/const theme\.mode\s*=/g, 'const colorScheme =');
  content = content.replace(/const theme\.colors\s*=/g, 'const colors =');

  // Fix malformed Supabase query chains
  content = content.replace(/\.eq\(\.eq\(\.eq\(/g, '.eq(');
  content = content.replace(/\.select\(\.select\(\.select\(/g, '.select(');
  content = content.replace(/\.order\(\.order\(\.order\(/g, '.order(');
  content = content.replace(/\.limit\)\\.limit\)\\.limit\(/g, '.limit(');
  content = content.replace(/\.single\)\\.single\)\\.single\(/g, '.single(');
  content = content.replace(/\.in\)\\.in\)\\.in\(/g, '.in(');
  content = content.replace(/\.gte\)\\.gte\)\\.gte\(/g, '.gte(');

  // Fix broken JSX prop assignments
  content = content.replace(/(\w+)\s*=\s*(\w+)\s*=\s*/g, '$1={$2} ');
  content = content.replace(/(\w+)\s*=\s*\{([^}]+)\}\s*=\s*/g, '$1={$2} ');

  // Fix malformed style arrays
  content = content.replace(/\}\s*,\s*\{\s*\]\)\}\s*\{\s*>\s*\{\s*</g, '}]}>');
  content = content.replace(/StyleSheet\.flatten\(\[([^\]]+)\]\)/g, '[$1]');

  // Fix broken View closing tags
  content = content.replace(/<\/View\s*\{[^}]*\}/g, '</View>');

  // Fix malformed function declarations
  content = content.replace(/const\s+(\w+)\s*=\s*\([^)]*\)\s*:\s*[^=]*=>/g, 'const $1 = ($2) =>');

  // Fix missing opening braces in functions
  content = content.replace(/=>\s*\n\s*([A-Z])/gm, '=> {\n  return $1');
  content = content.replace(/=>\s*\n\s*(if|const|let|var|return)/gm, '=> {\n  $1');

  // Fix broken component props
  content = content.replace(/(\w+)\s+(\w+)\s*=\s*\{([^}]+)\}\s*(\w+)\s*=\s*/g, '$1 $2={$3} $4=');

  // Fix malformed backgroundColor props
  content = content.replace(
    /backgroundColor:\s*\{[^}]*\}\s*\{/g,
    'backgroundColor: theme.colors.background'
  );

  // Fix broken import statements
  content = content.replace(
    /import\s*\{\s*([^}]*)\s*\}\s*from\s*([^;]*);/g,
    (match, imports, from) => {
      return `import { ${imports.replace(/\s+/g, ' ').trim()} } from ${from};`;
    }
  );

  return content;
}

// Function to process a single file
function processFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const fixedContent = fixAllSyntaxErrors(content);

    if (content !== fixedContent) {
      fs.writeFileSync(filePath, fixedContent, 'utf8');
      console.log(`Fixed: ${filePath}`);
      return true;
    }
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
  }
  return false;
}

// Function to recursively find TypeScript/TSX files
function findTsFiles(dir) {
  const files = [];

  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);

    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        traverse(fullPath);
      } else if (stat.isFile() && (item.endsWith('.ts') || item.endsWith('.tsx'))) {
        files.push(fullPath);
      }
    }
  }

  traverse(dir);
  return files;
}

// Main execution
console.log('Starting comprehensive syntax error fix...');

const srcDir = path.join(__dirname, '../src');
const files = findTsFiles(srcDir);

console.log(`Found ${files.length} TypeScript files to process`);

let fixedCount = 0;
for (const file of files) {
  if (processFile(file)) {
    fixedCount++;
  }
}

console.log(`Fixed ${fixedCount} files out of ${files.length} total files`);

// Run TypeScript check to see remaining errors
console.log('\nChecking remaining TypeScript errors...');
try {
  execSync('npx tsc --noEmit --skipLibCheck', { stdio: 'inherit' });
  console.log('✅ All syntax errors fixed!');
} catch (error) {
  console.log('⚠️  Some errors may remain, but major syntax issues should be resolved');
}

// Function to fix basic syntax errors
function fixBasicSyntax(content) {
  // Fix malformed theme declarations
  content = content.replace(
    /const theme\.mode = useColorScheme\(\);/g,
    'const colorScheme = useColorScheme();'
  );
  content = content.replace(/const { const theme = useTheme\(\);/g, 'const theme = useTheme();');

  // Fix malformed password assignments
  content = content.replace(/password: form\.password;/g, 'password: form.password,');

  // Fix broken JSX fragments and style arrays
  content = content.replace(/\]\s*}\s*>\s*{/g, ']} >');
  content = content.replace(
    /\}\s*,\s*\{\s*\]\)}\s*\{\s*>\s*\{\s*<Text\s*\{/g,
    '}]} >\n      <Text style={'
  );

  return content;
}

// Function to reconstruct severely corrupted files
function reconstructCorruptedFile(filePath) {
  const fileName = path.basename(filePath);

  switch (fileName) {
    case 'video-intro.tsx':
      return `import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator } from 'react-native';
import { Stack } from 'expo-router';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useTheme } from '@/design-system/ThemeProvider';
import { useSupabaseUser } from '@/hooks/useSupabaseUser';

export default function VideoIntroScreen() {
  const theme = useTheme();
  const { user } = useSupabaseUser();
  const [loading, setLoading] = useState(false);

  const styles = createStyles(theme);

  return (
    <>
      <Stack.Screen options={{ title: 'Video Introduction' }} />
      <View style={styles.container}>
        <View style={styles.content}>
          <MaterialCommunityIcons 
            name="video-plus" 
            size={48} 
            color={theme.colors.textSecondary} 
          />
          <Text style={styles.title}>Video Introduction</Text>
          <Text style={styles.subtitle}>
            Record a video introduction to help potential roommates get to know you better.
          </Text>
        </View>
      </View>
    </>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
  },
});`;

    case '[id].tsx':
      return `import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { Stack, useLocalSearchParams } from 'expo-router';
import { useTheme } from '@/design-system/ThemeProvider';
import { useSupabaseUser } from '@/hooks/useSupabaseUser';

export default function RoomDetailScreen() {
  const theme = useTheme();
  const { user } = useSupabaseUser();
  const { id } = useLocalSearchParams();
  const [room, setRoom] = useState(null);
  const [loading, setLoading] = useState(true);

  const styles = createStyles(theme);

  useEffect(() => {
    if (id) {
      // Load room data
      setLoading(false);
    }
  }, [id]);

  return (
    <>
      <Stack.Screen options={{ title: 'Room Details' }} />
      <ScrollView style={styles.container}>
        <View style={styles.content}>
          <Text style={styles.title}>Room Details</Text>
          <Text style={styles.subtitle}>Room ID: {id}</Text>
        </View>
      </ScrollView>
    </>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  content: {
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
});`;

    case 'ai-search-dashboard.tsx':
      return `import React, { useState } from 'react';
import { View, Text, StyleSheet, TextInput, TouchableOpacity } from 'react-native';
import { Stack } from 'expo-router';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useTheme } from '@/design-system/ThemeProvider';

export default function AISearchDashboard() {
  const theme = useTheme();
  const [searchQuery, setSearchQuery] = useState('');

  const styles = createStyles(theme);

  return (
    <>
      <Stack.Screen options={{ title: 'AI Search' }} />
      <View style={styles.container}>
        <View style={styles.searchContainer}>
          <View style={styles.searchInputContainer}>
            <MaterialCommunityIcons 
              name="magnify" 
              size={20} 
              color={theme.colors.textSecondary}
              style={styles.searchIcon}
            />
            <TextInput
              style={styles.searchInput}
              placeholder="Search for roommates..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholderTextColor={theme.colors.textSecondary}
            />
          </View>
        </View>
      </View>
    </>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  searchContainer: {
    padding: 20,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    paddingHorizontal: 16,
    height: 48,
  },
  searchIcon: {
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: theme.colors.text,
  },
});`;

    case 'behavioral-analytics.tsx':
      return `import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, ActivityIndicator } from 'react-native';
import { Stack } from 'expo-router';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useTheme } from '@/design-system/ThemeProvider';
import { useSupabaseUser } from '@/hooks/useSupabaseUser';

interface BehavioralData {
  userId: string;
  username: string;
  riskScore: number;
  severity: string;
  detectedAt: string;
  patterns: any[];
  status: string;
}

export default function BehavioralAnalytics() {
  const theme = useTheme();
  const { user } = useSupabaseUser();
  const [loading, setLoading] = useState(true);
  const [behavioralData, setBehavioralData] = useState<BehavioralData[]>([]);

  const styles = createStyles(theme);

  useEffect(() => {
    loadBehavioralData();
  }, []);

  const loadBehavioralData = async () => {
    setLoading(true);
    try {
      // Load behavioral analytics data
      setBehavioralData([]);
    } catch (error) {
      console.error('Error loading behavioral data:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderUserItem = ({ item }: { item: BehavioralData }) => (
    <View style={styles.userCard}>
      <Text style={styles.username}>{item.username}</Text>
      <Text style={styles.userId}>{item.userId}</Text>
    </View>
  );

  return (
    <>
      <Stack.Screen options={{ title: 'Behavioral Analytics' }} />
      <View style={styles.container}>
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.colors.primary} />
            <Text style={styles.loadingText}>Loading behavioral analytics data...</Text>
          </View>
        ) : (
          <FlatList
            data={behavioralData}
            renderItem={renderUserItem}
            keyExtractor={(item) => item.userId}
            contentContainerStyle={styles.listContainer}
          />
        )}
      </View>
    </>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  listContainer: {
    padding: 16,
  },
  userCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  username: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
  },
  userId: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 2,
  },
});`;

    default:
      return null;
  }
}

// Main execution
console.log('🔧 Starting comprehensive syntax error fixes...');

// First, reconstruct severely corrupted files
corruptedFiles.forEach(filePath => {
  const fullPath = path.join(process.cwd(), filePath);
  const newContent = reconstructCorruptedFile(filePath);

  if (newContent && fs.existsSync(fullPath)) {
    fs.writeFileSync(fullPath, newContent, 'utf8');
    console.log(`✅ Reconstructed: ${filePath}`);
  }
});

// Then fix remaining files with Supabase query issues
const filesToFix = [
  'src/app/(tabs)/search/housemate.tsx',
  'src/app/admin/analytics.tsx',
  'src/app/admin/device-fingerprints.tsx',
  'src/app/admin/provider-verification.tsx',
  'src/app/admin/login.tsx',
];

filesToFix.forEach(filePath => {
  const fullPath = path.join(process.cwd(), filePath);

  if (fs.existsSync(fullPath)) {
    try {
      let content = fs.readFileSync(fullPath, 'utf8');

      // Apply fixes
      content = fixSupabaseQueries(content);
      content = fixBasicSyntax(content);

      fs.writeFileSync(fullPath, content, 'utf8');
      console.log(`✅ Fixed syntax in: ${filePath}`);
    } catch (error) {
      console.error(`❌ Error fixing ${filePath}:`, error.message);
    }
  }
});

console.log('🎉 Comprehensive syntax fixes completed!');

// Add missing fixSupabaseQueries function
function fixSupabaseQueries(content) {
  return (
    content
      // Fix common Supabase query syntax issues
      .replace(
        /\.from\s*\(\s*(['"`])([^'"`]+)\1\s*\)\s*\.\s*select\s*\(\s*\)/g,
        ".from($1$2$1).select('*')"
      )
      .replace(
        /\.from\s*\(\s*(['"`])([^'"`]+)\1\s*\)\s*\.\s*select\s*\(\s*([^)]+)\s*\)\s*\.\s*eq\s*\(\s*(['"`])([^'"`]+)\4\s*,\s*([^)]+)\s*\)/g,
        '.from($1$2$1).select($3).eq($4$5$4, $6)'
      )
      .replace(/\.insert\s*\(\s*\[\s*\]\s*\)/g, '.insert({})')
      .replace(/\.update\s*\(\s*\{\s*\}\s*\)/g, '.update({})')
      .replace(/\.upsert\s*\(\s*\[\s*\]\s*\)/g, '.upsert({})')
      // Fix semicolon issues in Supabase queries
      .replace(/upsert:\s*true\s*;/g, 'upsert: true,')
      .replace(
        /sortBy:\s*\{\s*column:\s*['"`]([^'"`]+)['"`]\s*,\s*order:\s*['"`]([^'"`]+)['"`]\s*\}\s*;/g,
        "sortBy: { column: '$1', order: '$2' }"
      )
      // Fix method chaining issues
      .replace(
        /\.\s*list\s*\(\s*(['"`])([^'"`]*)\1\s*,\s*\{\s*limit:\s*(\d+)\s*\}\s*\)\s*;/g,
        '.list($1$2$1, { limit: $3 })'
      )
      .replace(/\.\s*getPublicUrl\s*\(\s*([^)]+)\s*\)\s*;/g, '.getPublicUrl($1)')
      .replace(/\.\s*remove\s*\(\s*\[\s*([^]]+)\s*\]\s*\)\s*;/g, '.remove([$1])')
      // Fix try-catch blocks
      .replace(/\}\s*catch\s*\(\s*([^)]+)\s*\)\s*\{/g, '} catch ($1) {')
      // Fix function declarations
      .replace(/}\s*;$/gm, '}')
  );
}

function fixFileContent(content, filePath) {
  try {
    let fixed = content;

    // Apply all fix functions
    fixed = fixImports(fixed);
    fixed = fixButtonProps(fixed);
    fixed = fixInputProps(fixed);
    fixed = fixIconImports(fixed);
    fixed = fixTypeImports(fixed);
    fixed = fixStyleSheetUsage(fixed);
    fixed = fixSupabaseQueries(fixed);
    fixed = fixCommonSyntaxErrors(fixed);
    fixed = fixMissingChildren(fixed);
    fixed = fixAsyncAwaitSyntax(fixed);
    fixed = fixObjectDestructuring(fixed);
    fixed = fixArrayMethods(fixed);
    fixed = fixConditionalRendering(fixed);
    fixed = fixEventHandlers(fixed);
    fixed = fixStateUpdates(fixed);
    fixed = fixUseEffectDependencies(fixed);

    return fixed;
  } catch (error) {
    console.log(`❌ Error fixing ${filePath}: ${error.message}`);
    return content; // Return original content if fixing fails
  }
}

console.log('🔧 Starting comprehensive syntax fix for remaining malformed patterns...\n');

let totalFixed = 0;
const totalChanges = 0;

filesToFix.forEach((file, index) => {
  console.log(`\n📁 Processing file ${index + 1}/${filesToFix.length}: ${file}`);
  const fixed = fixFile(file);
  if (fixed) {
    totalFixed++;
  }
});

console.log(`\n🎯 Fix Summary:`);
console.log(`   Files processed: ${filesToFix.length}`);
console.log(`   Files fixed: ${totalFixed}`);
console.log(`   Files unchanged: ${filesToFix.length - totalFixed}`);

if (totalFixed > 0) {
  console.log('\n✅ All remaining syntax errors should now be fixed!');
  console.log('🚀 Try running the Expo development server again.');
} else {
  console.log('\n🤔 No files were modified. The patterns may have already been fixed.');
}

console.log('\n🔍 Verification: Run this command to check for any remaining issues:');
console.log('grep -r "const [a-zA-Z_]*={[a-zA-Z_]*}" src/ --include="*.tsx" --include="*.ts"');
