#!/bin/bash

echo "🚀 Starting WeRoomies Expo Tunnel for Testing"
echo "=============================================="

# Kill any existing processes on port 8081
echo "🔄 Cleaning up existing processes..."
lsof -ti:8081 | xargs kill -9 2>/dev/null || true

# Clear Expo cache
echo "🧹 Clearing Expo cache..."
npx expo start --clear --tunnel

echo "📱 INSTRUCTIONS FOR TESTERS:"
echo "============================="
echo "1. Install 'Expo Go' app from App Store or Google Play"
echo "2. Open Expo Go app on your phone"
echo "3. Scan the QR code shown above"
echo "4. The WeRoomies app will load directly in Expo Go"
echo ""
echo "💡 If QR code doesn't work:"
echo "- Make sure you're using Expo Go app (not camera app)"
echo "- Try typing the exp:// URL manually in Expo Go"
echo "- Contact developer if issues persist" 