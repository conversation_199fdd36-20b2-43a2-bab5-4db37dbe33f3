#!/bin/bash

# WeRoomies iOS Clean Script
# This script cleans iOS build artifacts and pod cache to fix build issues

echo "🧹 Cleaning iOS build artifacts..."

# Clean iOS directory
echo "📱 Cleaning iOS build folder..."
rm -rf ios/build
rm -rf ios/DerivedData

# Clean Pods
echo "🏗️ Cleaning CocoaPods cache..."
rm -rf ios/Pods
rm -rf ios/Podfile.lock

# Clean npm/yarn cache
echo "📦 Cleaning npm cache..."
npm cache clean --force

# Clean Expo cache
echo "🚀 Cleaning Expo cache..."
npx expo install --fix

echo "✅ iOS cleanup completed!"
echo ""
echo "Now you can run:"
echo "  eas build --platform ios --profile preview-simulator"
echo "" 