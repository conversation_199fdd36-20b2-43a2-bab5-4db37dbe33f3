#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔍 COMPREHENSIVE SYNTAX ERROR SCANNER');
console.log('=====================================\n');

// Define patterns to search for common syntax errors
const syntaxErrorPatterns = [
  {
    name: 'Missing Comma in Object Literals',
    pattern: /:\s*[^,}\n]*[;}]\s*$/gm,
    description: 'Lines ending with ; instead of , in object literals',
  },
  {
    name: 'Malformed JSX Style Props',
    pattern: /style=\s*\([^)]*\)\s*=>/g,
    description: 'Malformed JSX style prop patterns',
  },
  {
    name: 'Missing Opening Braces in Functions',
    pattern: /=>\s*$/gm,
    description: 'Arrow functions missing opening brace',
  },
  {
    name: 'Unclosed Array/Object Literals',
    pattern: /\[\s*$/gm,
    description: 'Unclosed array or object literals',
  },
  {
    name: 'Missing Return in Map Functions',
    pattern: /\.map\([^)]*\)\s*=>\s*\{[^}]*prev\.map/g,
    description: 'Map functions missing return statement',
  },
  {
    name: 'Semicolon Instead of Comma',
    pattern: /:\s*[^,}\n]*;\s*$/gm,
    description: 'Semicolon used instead of comma in object properties',
  },
  {
    name: 'Missing Closing Parentheses',
    pattern: /\([^)]*$/gm,
    description: 'Unclosed parentheses',
  },
  {
    name: 'Template Literal Syntax Errors',
    pattern: />\s*\{[^}]*\}\s*</g,
    description: 'Potential template literal syntax issues',
  },
];

// Files to scan
const srcDirectory = path.join(process.cwd(), 'src');
const extensions = ['.ts', '.tsx', '.js', '.jsx'];

function getAllFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);

  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory()) {
      getAllFiles(filePath, fileList);
    } else if (extensions.some(ext => file.endsWith(ext))) {
      fileList.push(filePath);
    }
  });

  return fileList;
}

function scanFileForErrors(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    const errors = [];

    syntaxErrorPatterns.forEach(pattern => {
      const matches = content.match(pattern.pattern);
      if (matches) {
        matches.forEach(match => {
          // Find line number
          const lineIndex = content.indexOf(match);
          const lineNumber = content.substring(0, lineIndex).split('\n').length;

          errors.push({
            pattern: pattern.name,
            description: pattern.description,
            line: lineNumber,
            match: match.trim(),
            context: lines[lineNumber - 1],
          });
        });
      }
    });

    return errors;
  } catch (error) {
    console.error(`❌ Error reading file ${filePath}:`, error.message);
    return [];
  }
}

function main() {
  console.log('📁 Scanning source directory:', srcDirectory);
  console.log('🔍 File extensions:', extensions.join(', '));
  console.log('');

  const allFiles = getAllFiles(srcDirectory);
  console.log(`📊 Found ${allFiles.length} files to scan\n`);

  let totalErrors = 0;
  const errorsByFile = {};

  allFiles.forEach(filePath => {
    const errors = scanFileForErrors(filePath);
    if (errors.length > 0) {
      const relativePath = path.relative(process.cwd(), filePath);
      errorsByFile[relativePath] = errors;
      totalErrors += errors.length;
    }
  });

  console.log('🚨 SYNTAX ERROR SUMMARY');
  console.log('=======================\n');

  if (totalErrors === 0) {
    console.log('✅ No syntax errors detected!');
    return;
  }

  console.log(
    `❌ Found ${totalErrors} potential syntax errors in ${Object.keys(errorsByFile).length} files\n`
  );

  // Group errors by pattern
  const errorsByPattern = {};
  Object.entries(errorsByFile).forEach(([file, errors]) => {
    errors.forEach(error => {
      if (!errorsByPattern[error.pattern]) {
        errorsByPattern[error.pattern] = [];
      }
      errorsByPattern[error.pattern].push({ file, ...error });
    });
  });

  // Display errors by pattern
  Object.entries(errorsByPattern).forEach(([pattern, errors]) => {
    console.log(`🔍 ${pattern} (${errors.length} instances):`);
    console.log('─'.repeat(50));

    errors.slice(0, 10).forEach(error => {
      // Show first 10 instances
      console.log(`📁 ${error.file}:${error.line}`);
      console.log(`   ${error.context}`);
      console.log(`   Match: "${error.match}"`);
      console.log('');
    });

    if (errors.length > 10) {
      console.log(`   ... and ${errors.length - 10} more instances\n`);
    }
  });

  console.log('🎯 PRIORITY FILES TO FIX:');
  console.log('=========================');

  // Sort files by error count
  const sortedFiles = Object.entries(errorsByFile)
    .sort(([, a], [, b]) => b.length - a.length)
    .slice(0, 10);

  sortedFiles.forEach(([file, errors]) => {
    console.log(`📁 ${file} (${errors.length} errors)`);
  });

  console.log('\n🔧 RECOMMENDED NEXT STEPS:');
  console.log('===========================');
  console.log('1. Fix immediate blocking errors in property-manager-dashboard.tsx');
  console.log('2. Fix verification/identity.tsx semicolon error');
  console.log('3. Systematically address files with highest error counts');
  console.log('4. Run comprehensive validation after each batch of fixes');
  console.log('5. Test bundle generation after each major fix');
}

main();
