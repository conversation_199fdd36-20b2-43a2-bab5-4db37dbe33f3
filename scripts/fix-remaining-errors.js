#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 Comprehensive Error Fixer - Starting fixes for remaining issues...\n');

// Configuration for specific fixes based on the TypeScript errors
const FIXES = {
  // Fix Button variant issues
  buttonVariants: {
    'variant="primary"': 'variant="filled"',
    'variant="secondary"': 'variant="outlined"',
    'variant="destructive"': 'variant="outlined"',
    'size="sm"': 'size="small"',
  },

  // Fix Input import issues
  inputImports: {
    pattern: /import\s*\{\s*Input\s*\}\s*from\s*['"]@components\/ui\/form\/Input['"]/g,
    replacement: "import Input from '@components/ui/form/Input'",
  },

  // Fix theme and color issues
  themeIssues: {
    // Fix missing theme variables
    "Cannot find name 'theme'": {
      pattern: /(\w+)\.(\w+)(?:\s*=\s*theme\.)/g,
      fix: 'Add theme hook: const theme = useTheme();',
    },
    // Fix color references
    "Cannot find name 'colors'": {
      pattern: /colors\./g,
      replacement: 'theme.colors.',
    },
    "Cannot find name 'colorScheme'": {
      pattern: /colorScheme/g,
      replacement: 'theme.mode',
    },
  },

  // Fix style prop issues
  styleProps: {
    // Fix style arrays being passed where single styles expected
    pattern: /style=\{(\[[^\]]+\])\}/g,
    replacement: 'style={StyleSheet.flatten($1)}',
  },

  // Fix missing properties
  missingProps: {
    bedrooms: 'bedrooms?: number;',
    bathrooms: 'bathrooms?: number;',
    h4: 'h4: { fontSize: number; fontWeight: string; lineHeight: number; };',
    h6: 'h6: { fontSize: number; fontWeight: string; lineHeight: number; };',
    subtitle1: 'subtitle1: { fontSize: number; fontWeight: string; lineHeight: number; };',
    textTertiary: 'textTertiary: string;',
  },

  // Fix router navigation issues
  routerIssues: {
    pattern: /router\.push\(['"]([^'"]+)['"]\)/g,
    replacement: (match, route) => {
      // Convert dynamic routes to proper format
      if (route.includes('${')) {
        return match; // Keep template literals as-is
      }
      return `router.push('${route}' as any)`;
    },
  },
};

// Get all TypeScript files that need fixing
function getAllTSFiles() {
  try {
    const result = execSync('find src -name "*.tsx" -o -name "*.ts" | grep -v node_modules', {
      encoding: 'utf8',
    });
    return result
      .trim()
      .split('\n')
      .filter(f => f.length > 0);
  } catch (error) {
    console.error('Error finding TypeScript files:', error.message);
    return [];
  }
}

// Fix specific issues in files
function fixButtonVariants(content) {
  console.log('  🔘 Fixing Button variants...');
  let fixed = content;
  Object.entries(FIXES.buttonVariants).forEach(([from, to]) => {
    fixed = fixed.replace(new RegExp(from.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), to);
  });
  return fixed;
}

function fixInputImports(content) {
  console.log('  📝 Fixing Input imports...');
  return content.replace(FIXES.inputImports.pattern, FIXES.inputImports.replacement);
}

function fixThemeIssues(content) {
  console.log('  🎨 Fixing theme issues...');
  let fixed = content;

  // Fix color references
  fixed = fixed.replace(/colors\./g, 'theme.colors.');
  fixed = fixed.replace(/colorScheme/g, 'theme.mode');

  // Add theme hook if theme is used but not imported
  if (fixed.includes('theme.') && !fixed.includes('useTheme')) {
    // Add useTheme import
    const importMatch = fixed.match(/(import[^;]+from\s+['"]@design-system['"];?\s*)/);
    if (importMatch) {
      fixed = fixed.replace(
        importMatch[1],
        importMatch[1] + "\nimport { useTheme } from '@design-system';\n"
      );
    } else {
      // Add new import
      const firstImport = fixed.match(/(import[^;]+;)/);
      if (firstImport) {
        fixed = fixed.replace(
          firstImport[1],
          firstImport[1] + "\nimport { useTheme } from '@design-system';\n"
        );
      }
    }

    // Add theme hook in component
    const componentMatch = fixed.match(/(const\s+\w+[^=]*=\s*[^{]*\{)/);
    if (componentMatch) {
      fixed = fixed.replace(
        componentMatch[1],
        componentMatch[1] + '\n  const theme = useTheme();\n'
      );
    }
  }

  return fixed;
}

function fixStyleProps(content) {
  console.log('  🎨 Fixing style props...');
  let fixed = content;

  // Fix style arrays
  fixed = fixed.replace(FIXES.styleProps.pattern, FIXES.styleProps.replacement);

  // Add StyleSheet import if needed
  if (fixed.includes('StyleSheet.flatten') && !fixed.includes('StyleSheet')) {
    fixed = fixed.replace(/(import\s+\{[^}]*\}\s+from\s+['"]react-native['"];?)/, match => {
      if (match.includes('StyleSheet')) return match;
      return match.replace('{', '{ StyleSheet,');
    });
  }

  return fixed;
}

function fixRouterIssues(content) {
  console.log('  🧭 Fixing router navigation...');
  return content.replace(FIXES.routerIssues.pattern, FIXES.routerIssues.replacement);
}

function fixTypescriptErrors(content, filePath) {
  console.log('  🔧 Fixing TypeScript-specific errors...');
  let fixed = content;

  // Fix fontWeight string issues
  fixed = fixed.replace(/fontWeight:\s*['"]([^'"]+)['"]/g, (match, weight) => {
    const validWeights = [
      'normal',
      'bold',
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
    ];
    if (validWeights.includes(weight)) {
      return `fontWeight: '${weight}'`;
    }
    return `fontWeight: 'normal'`;
  });

  // Fix duplicate identifiers
  if (fixed.includes('Duplicate identifier')) {
    // Remove duplicate imports
    const lines = fixed.split('\n');
    const seen = new Set();
    const filteredLines = lines.filter(line => {
      if (line.includes('import') && line.includes('useTheme')) {
        const key = line.trim();
        if (seen.has(key)) return false;
        seen.add(key);
      }
      return true;
    });
    fixed = filteredLines.join('\n');
  }

  // Fix JSX namespace issues
  if (fixed.includes("Cannot find namespace 'JSX'")) {
    if (!fixed.includes('import React')) {
      fixed = "import React from 'react';\n" + fixed;
    }
  }

  return fixed;
}

function addMissingImports(content) {
  console.log('  📦 Adding missing imports...');
  let fixed = content;

  // Add React import if JSX is used but React is not imported
  if (fixed.includes('<') && fixed.includes('>') && !fixed.includes('import React')) {
    fixed = "import React from 'react';\n" + fixed;
  }

  // Add StyleSheet import if used
  if (fixed.includes('StyleSheet.') && !fixed.includes('import') && !fixed.includes('StyleSheet')) {
    const rnImport = fixed.match(/import\s+\{([^}]+)\}\s+from\s+['"]react-native['"]/);
    if (rnImport) {
      const imports = rnImport[1];
      if (!imports.includes('StyleSheet')) {
        fixed = fixed.replace(rnImport[0], rnImport[0].replace('{', '{ StyleSheet,'));
      }
    }
  }

  return fixed;
}

function processFile(filePath) {
  console.log(`\n🔍 Processing: ${filePath}`);

  try {
    let content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;

    // Apply all fixes in order
    content = fixInputImports(content);
    content = fixButtonVariants(content);
    content = fixThemeIssues(content);
    content = fixStyleProps(content);
    content = fixRouterIssues(content);
    content = fixTypescriptErrors(content, filePath);
    content = addMissingImports(content);

    // Write back if changed
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`  ✅ Fixed: ${filePath}`);
      return true;
    } else {
      console.log(`  ⏭️  No changes needed: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`  ❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Fix specific known problematic files
function fixSpecificFiles() {
  const specificFixes = [
    {
      file: 'src/types/models.ts',
      fix: content => {
        // Add missing properties to RoomWithDetails
        if (!content.includes('bedrooms?:')) {
          content = content.replace(
            'export interface RoomWithDetails extends Room {',
            `export interface RoomWithDetails extends Room {
  bedrooms?: number;
  bathrooms?: number;`
          );
        }
        return content;
      },
    },
    {
      file: 'src/design-system/constants/typography.ts',
      fix: content => {
        // Add missing typography styles
        if (!content.includes('h4:')) {
          content = content.replace(
            'export const typography = {',
            `export const typography = {
  h4: { fontSize: 20, fontWeight: '600', lineHeight: 28 },
  h6: { fontSize: 16, fontWeight: '600', lineHeight: 24 },
  subtitle1: { fontSize: 16, fontWeight: '400', lineHeight: 24 },`
          );
        }
        return content;
      },
    },
    {
      file: 'src/design-system/constants/colors.ts',
      fix: content => {
        // Add missing color properties
        if (!content.includes('textTertiary:')) {
          content = content.replace(
            'textSecondary: string;',
            'textSecondary: string;\n  textTertiary: string;'
          );
        }
        return content;
      },
    },
  ];

  specificFixes.forEach(({ file, fix }) => {
    try {
      if (fs.existsSync(file)) {
        const content = fs.readFileSync(file, 'utf8');
        const newContent = fix(content);
        if (newContent !== content) {
          fs.writeFileSync(file, newContent, 'utf8');
          console.log(`✅ Applied specific fix to: ${file}`);
        }
      }
    } catch (error) {
      console.error(`❌ Error fixing ${file}:`, error.message);
    }
  });
}

function runTypeScriptCheck() {
  console.log('\n🔍 Running TypeScript check...');
  try {
    execSync('npx tsc --noEmit --skipLibCheck', { stdio: 'pipe' });
    console.log('✅ TypeScript check passed!');
    return true;
  } catch (error) {
    console.log('⚠️  TypeScript errors still exist:');
    const output = error.stdout?.toString() || error.message;
    const lines = output.split('\n').slice(0, 20); // Show first 20 errors
    console.log(lines.join('\n'));
    if (output.split('\n').length > 20) {
      console.log('... and more errors');
    }
    return false;
  }
}

function main() {
  const startTime = Date.now();

  console.log('🔍 Finding all TypeScript files...');
  const tsFiles = getAllTSFiles();
  console.log(`📁 Found ${tsFiles.length} TypeScript files\n`);

  // Apply specific fixes first
  console.log('🎯 Applying specific fixes...');
  fixSpecificFiles();

  // Process all files
  let fixedCount = 0;
  tsFiles.forEach(file => {
    if (processFile(file)) {
      fixedCount++;
    }
  });

  console.log(`\n📊 Summary:`);
  console.log(`   Files processed: ${tsFiles.length}`);
  console.log(`   Files fixed: ${fixedCount}`);
  console.log(`   Files unchanged: ${tsFiles.length - fixedCount}`);

  // Run TypeScript check
  const tsCheckPassed = runTypeScriptCheck();

  const endTime = Date.now();
  const duration = ((endTime - startTime) / 1000).toFixed(2);

  console.log(`\n🎉 Comprehensive fix completed in ${duration}s`);

  if (tsCheckPassed) {
    console.log('🎊 All TypeScript errors have been resolved!');
  } else {
    console.log('🔧 Some issues may remain. The most critical ones should be fixed.');
  }
}

// Run the script
main();
