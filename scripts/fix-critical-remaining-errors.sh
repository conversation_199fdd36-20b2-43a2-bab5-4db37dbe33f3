#!/bin/bash

echo "🚨 WeRoomies Critical Syntax Error Fix"
echo "======================================"

# WeRoomies Critical Remaining Errors Fix Script
# This script fixes the most critical remaining syntax errors

echo "🔧 Fixing critical remaining syntax errors..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

fixes_applied=0

apply_fix() {
    local file="$1"
    local description="$2"
    echo -e "${YELLOW}Fixing: ${description} in ${file}${NC}"
    fixes_applied=$((fixes_applied + 1))
}

echo -e "${BLUE}=== Critical Fixes Phase 1: Import/Export Issues ===${NC}"

# Fix import statements with trailing commas
echo -e "${GREEN}1. Fixing import statements with trailing commas...${NC}"
find src -name "*.ts" -o -name "*.tsx" | xargs sed -i '' "s/import React from 'react',/import React from 'react';/g"
find src -name "*.ts" -o -name "*.tsx" | xargs sed -i '' "s/from '@utils\/supabaseUtils',/from '@utils\/supabaseUtils';/g"
find src -name "*.ts" -o -name "*.tsx" | xargs sed -i '' "s/from '@services\/loggerService',/from '@services\/loggerService';/g"
apply_fix "src/**/*.{ts,tsx}" "import statement trailing commas"

# Fix verificationTableSetup.ts specifically
if [ -f "src/utils/verificationTableSetup.ts" ]; then
    cat > src/utils/verificationTableSetup.ts << 'EOF'
import React from 'react';
import { supabase } from '@utils/supabaseUtils';
import { logger } from '@services/loggerService';

/**
 * Set up verification tables in Supabase
 */
export async function setupVerificationTables(): Promise<boolean> {
  try {
    logger.info('Setting up verification tables...', 'verificationTableSetup');
    
    const { error } = await supabase.rpc('create_verification_tables');
    
    if (error) {
      logger.error('Error creating verification tables:', error);
      
      if (error.message.includes('function "create_verification_tables" does not exist')) {
        logger.info('Creating verification tables function...', 'verificationTableSetup');
        
        const response = await fetch('/supabase/functions/create_verification_tables.sql');
        
        if (!response.ok) {
          logger.error('Failed to fetch verification tables SQL', 'verificationTableSetup');
          return false;
        }
        
        const sql = await response.text();
        const { error: sqlError } = await supabase.rpc('exec_sql', { sql });
        
        if (sqlError) {
          logger.error('Error executing verification tables SQL:', sqlError);
          return false;
        }
        
        // Try again after creating the function
        const { error: retryError } = await supabase.rpc('create_verification_tables');
        
        if (retryError) {
          logger.error('Error creating verification tables on retry:', retryError);
          return false;
        }
      } else {
        return false;
      }
    }
    
    logger.info('Verification tables setup completed', 'verificationTableSetup');
    return true;
  } catch (error) {
    logger.error(
      'Unexpected error setting up verification tables',
      'verificationTableSetup',
      { error: error instanceof Error ? error.message : String(error) }
    );
    return false;
  }
}
EOF
    apply_fix "src/utils/verificationTableSetup.ts" "complete file rewrite"
fi

echo -e "${BLUE}=== Critical Fixes Phase 2: Function Parameter Issues ===${NC}"

# Fix withErrorHandling.tsx
if [ -f "src/utils/withErrorHandling.tsx" ]; then
    cat > src/utils/withErrorHandling.tsx << 'EOF'
import React, { Component, ErrorInfo, ReactNode } from 'react';
import { View, Text } from 'react-native';

export interface WithErrorHandlingOptions {
  componentName?: string;
  severity?: 'low' | 'medium' | 'high' | 'critical';
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  retryOnError?: boolean;
  errorContext?: Record<string, any>;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

export function withErrorHandling<P extends object>(
  Component: React.ComponentType<P>,
  options: WithErrorHandlingOptions = {}
): React.ComponentType<P> {
  const {
    componentName = Component.displayName || Component.name || 'UnknownComponent',
    severity = 'medium',
    fallback,
    onError,
    retryOnError = true,
    errorContext = {}
  } = options;

  class ErrorBoundaryWrapper extends Component<P, ErrorBoundaryState> {
    constructor(props: P) {
      super(props);
      this.state = { hasError: false };
    }

    static getDerivedStateFromError(error: Error): ErrorBoundaryState {
      return { hasError: true, error };
    }

    componentDidCatch(error: Error, errorInfo: ErrorInfo) {
      const enhancedContext = {
        ...errorContext,
        componentName,
        componentStack: errorInfo.componentStack,
        severity,
        componentProps: JSON.stringify(
          Object.keys(this.props).reduce((acc, key) => {
            try {
              acc[key] = (this.props as any)[key];
              return acc;
            } catch {
              acc[key] = '[Unserializable]';
              return acc;
            }
          }, {} as Record<string, any>)
        )
      };

      onError?.(error.originalError || error, errorInfo);
    }

    render() {
      if (this.state.hasError) {
        if (fallback) {
          return fallback;
        }

        return (
          <View style={{ padding: 20, alignItems: 'center' }}>
            <Text style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 10 }}>
              Something went wrong
            </Text>
            <Text style={{ textAlign: 'center', color: '#666' }}>
              An error occurred in {componentName}. Please try again.
            </Text>
            {retryOnError && (
              <Text 
                style={{ marginTop: 15, color: '#007AFF', textDecorationLine: 'underline' }}
                onPress={() => this.setState({ hasError: false, error: undefined, errorInfo: undefined })}
              >
                Try Again
              </Text>
            )}
          </View>
        );
      }

      return <Component {...this.props} />;
    }
  }

  ErrorBoundaryWrapper.displayName = `withErrorHandling(${componentName})`;
  return ErrorBoundaryWrapper;
}

export function withCriticalErrorHandling<P extends object>(
  Component: React.ComponentType<P>,
  options: Omit<WithErrorHandlingOptions, 'severity'> = {}
): React.ComponentType<P> {
  return withErrorHandling(Component, {
    ...options,
    severity: 'critical',
    retryOnError: true
  });
}

export function withScreenErrorHandling<P extends object>(
  Component: React.ComponentType<P>,
  screenName: string,
  options: Omit<WithErrorHandlingOptions, 'componentName' | 'errorContext'> = {}
): React.ComponentType<P> {
  return withErrorHandling(Component, {
    ...options,
    componentName: screenName,
    errorContext: { screen: screenName },
    severity: 'high',
    retryOnError: true
  });
}

export function withFormErrorHandling<P extends object>(
  Component: React.ComponentType<P>,
  formName: string,
  options: Omit<WithErrorHandlingOptions, 'componentName' | 'severity'> = {}
): React.ComponentType<P> {
  return withErrorHandling(Component, {
    ...options,
    componentName: formName,
    severity: 'high',
    retryOnError: true
  });
}
EOF
    apply_fix "src/utils/withErrorHandling.tsx" "complete file rewrite"
fi

echo -e "${BLUE}=== Critical Fixes Phase 3: Object Syntax Issues ===${NC}"

# Fix verificationStorageHelper.ts
if [ -f "src/utils/verificationStorageHelper.ts" ]; then
    # Fix the specific syntax error around line 316-325
    sed -i '' '316,325c\
  } catch (error) {\
    logger.error("💥 Failed to get verification folder structure:", error);\
    return {\
      success: false,\
      folders: [],\
      error: error instanceof Error ? error.message : String(error)\
    };\
  }' src/utils/verificationStorageHelper.ts
    apply_fix "src/utils/verificationStorageHelper.ts" "catch block syntax"
fi

echo -e "${BLUE}=== Critical Fixes Phase 4: Return Statement Issues ===${NC}"

# Fix return statements with commas instead of semicolons
echo -e "${GREEN}4. Fixing return statements with commas...${NC}"
find src -name "*.ts" -o -name "*.tsx" | xargs sed -i '' 's/return false,/return false;/g'
find src -name "*.ts" -o -name "*.tsx" | xargs sed -i '' 's/return true,/return true;/g'
find src -name "*.ts" -o -name "*.tsx" | xargs sed -i '' 's/return null,/return null;/g'
find src -name "*.ts" -o -name "*.tsx" | xargs sed -i '' 's/return undefined,/return undefined;/g'
apply_fix "src/**/*.{ts,tsx}" "return statement commas to semicolons"

echo -e "${BLUE}=== Critical Fixes Phase 5: Try-Catch Block Issues ===${NC}"

# Fix missing try blocks
echo -e "${GREEN}5. Fixing missing try blocks...${NC}"
find src -name "*.ts" -o -name "*.tsx" | xargs sed -i '' 's/logger\.info.*catch.*expected/try { logger.info/g'
apply_fix "src/**/*.{ts,tsx}" "missing try blocks"

echo -e "${BLUE}=== Critical Fixes Phase 6: Function Declaration Issues ===${NC}"

# Fix function declarations missing opening braces
echo -e "${GREEN}6. Fixing function declarations...${NC}"
find src -name "*.ts" -o -name "*.tsx" | xargs sed -i '' 's/): Promise<[^>]*>\s*try {/): Promise<any> { try {/g'
find src -name "*.ts" -o -name "*.tsx" | xargs sed -i '' 's/): void\s*try {/): void { try {/g'
apply_fix "src/**/*.{ts,tsx}" "function declaration braces"

echo -e "${BLUE}=== Critical Fixes Phase 7: Object Property Issues ===${NC}"

# Fix object properties with missing commas
echo -e "${GREEN}7. Fixing object property syntax...${NC}"
find src -name "*.ts" -o -name "*.tsx" | xargs sed -i '' 's/severity\s*componentProps:/severity, componentProps:/g'
find src -name "*.ts" -o -name "*.tsx" | xargs sed -i '' 's/componentStack: errorInfo\.componentStack\s*severity/componentStack: errorInfo.componentStack, severity/g'
apply_fix "src/**/*.{ts,tsx}" "object property commas"

echo -e "${GREEN}=== Summary ===${NC}"
echo -e "${BLUE}Total critical fixes applied: ${fixes_applied}${NC}"
echo -e "${GREEN}✅ Critical syntax fixes completed!${NC}"
echo ""
echo -e "${YELLOW}Next steps:${NC}"
echo "1. Run: npm run type-check"
echo "2. Review remaining errors"
echo "3. Run: npm run lint:fix"

# Function to remove extra semicolons and commas from files
fix_semicolon_comma_errors() {
    local file="$1"
    echo "🔧 Fixing semicolon/comma errors in: $file"
    
    # Remove trailing semicolons after opening braces
    sed -i '' 's/{;$/{/g' "$file"
    
    # Remove trailing commas after semicolons
    sed -i '' 's/;,$/;/g' "$file"
    
    # Remove semicolons after closing braces
    sed -i '' 's/};$/}/g' "$file"
    
    # Remove commas after property declarations
    sed -i '' 's/: [^,;]*;,$/: &;/g' "$file"
    
    # Fix interface/class declarations
    sed -i '' 's/interface [A-Za-z]* {;$/interface & {/g' "$file"
    sed -i '' 's/class [A-Za-z]* {;$/class & {/g' "$file"
    
    # Fix method declarations
    sed -i '' 's/) {;,$/) {/g' "$file"
    
    # Remove extra semicolons in comments
    sed -i '' 's/\*\/;$/\*\//g' "$file"
}

# Fix 1: Critical import/export issues
echo "1. Fixing import/export syntax errors..."
find src/ -name "*.ts" -o -name "*.tsx" | while read file; do
    if [[ -f "$file" ]]; then
        # Fix malformed import statements
        sed -i '' 's/import { \([^}]*\), } from/import { \1 } from/g' "$file"
        
        # Fix export statements
        sed -i '' 's/export { \([^}]*\), }/export { \1 }/g' "$file"
    fi
done

# Fix 2: Missing return statements in map functions
echo "2. Fixing missing return statements in map functions..."
find src/ -name "*.tsx" -o -name "*.ts" | while read file; do
    if [[ -f "$file" ]]; then
        # Fix map functions without return
        sed -i '' 's/\.map(\([^)]*\) => {$/\.map(\1 => (/g' "$file"
        sed -i '' 's/\.filter(\([^)]*\) => {$/\.filter(\1 => (/g' "$file"
        sed -i '' 's/\.sort(\([^)]*\) => {$/\.sort(\1 => (/g' "$file"
    fi
done

# Fix 3: JSX syntax issues
echo "3. Fixing JSX syntax issues..."
find src/ -name "*.tsx" | while read file; do
    if [[ -f "$file" ]]; then
        # Fix malformed JSX opening braces
        sed -i '' 's/=== >/ =>/g' "$file"
        
        # Fix JSX comment syntax
        sed -i '' 's/\/\* \([^*]*\) \*\//{\/* \1 *\/}/g' "$file"
        
        # Fix closing tags
        sed -i '' 's/<\/\([A-Za-z]*\) >/<\/\1>/g' "$file"
    fi
done

# Fix 4: Function parameter and signature issues
echo "4. Fixing function parameter syntax..."
find src/ -name "*.tsx" -o -name "*.ts" | while read file; do
    if [[ -f "$file" ]]; then
        # Fix function parameter syntax
        sed -i '' 's/(\([^)]*\): \([^)]*\),)/(\1: \2)/g' "$file"
        
        # Fix async function syntax
        sed -i '' 's/= async (\([^)]*\)) => {,$/= async (\1) => {/g' "$file"
    fi
done

# Fix 5: Template literal issues
echo "5. Fixing template literal syntax..."
find src/ -name "*.tsx" -o -name "*.ts" | while read file; do
    if [[ -f "$file" ]]; then
        # Fix incomplete template literals
        sed -i '' 's/`\([^`]*\)\${[^}]*$/`\1\${}/g' "$file"
        
        # Fix template literal expressions
        sed -i '' 's/\${[^}]*,}/\${}/g' "$file"
    fi
done

# Fix 6: Supabase query chain issues
echo "6. Fixing Supabase query chains..."
find src/ -name "*.tsx" -o -name "*.ts" | while read file; do
    if [[ -f "$file" ]]; then
        # Fix broken query chains
        sed -i '' 's/\.from(\([^)]*\));$/\.from(\1)/g' "$file"
        sed -i '' 's/\.select(\([^)]*\));$/\.select(\1)/g' "$file"
        sed -i '' 's/\.eq(\([^)]*\));$/\.eq(\1)/g' "$file"
        sed -i '' 's/\.neq(\([^)]*\));$/\.neq(\1)/g' "$file"
    fi
done

# Fix 7: Object literal issues
echo "7. Fixing object literal syntax..."
find src/ -name "*.tsx" -o -name "*.ts" | while read file; do
    if [[ -f "$file" ]]; then
        # Fix object property syntax
        sed -i '' 's/\([a-zA-Z0-9_]*\): \([^,}]*\),;$/\1: \2,/g' "$file"
        
        # Fix trailing commas in objects
        sed -i '' 's/,;$/,/g' "$file"
    fi
done

# Fix 8: Specific file pattern fixes
echo "8. Applying file-specific fixes..."

# Fix all API files with semicolon/comma issues
find src/api/ -name "*.ts" | while read file; do
    if [[ -f "$file" ]]; then
        fix_semicolon_comma_errors "$file"
    fi
done

# Fix all service files with semicolon/comma issues  
find src/services/ -name "*.ts" | while read file; do
    if [[ -f "$file" ]]; then
        fix_semicolon_comma_errors "$file"
    fi
done

echo ""
echo "✅ Critical syntax fixes completed!"
echo "🔍 Running TypeScript check to verify fixes..."

# Run TypeScript check to see remaining issues
npx tsc --noEmit --project tsconfig.json 2>&1 | head -15

echo ""
echo "📊 Critical fix summary:"
echo "- Fixed import/export statement syntax"
echo "- Fixed missing return statements in map functions"
echo "- Fixed JSX syntax issues"
echo "- Fixed function parameter syntax"
echo "- Fixed template literal issues"
echo "- Fixed Supabase query chain syntax"
echo "- Fixed object literal syntax"
echo "- Applied file-specific semicolon/comma fixes"
echo ""
echo "🎯 Most critical syntax errors should now be resolved." 