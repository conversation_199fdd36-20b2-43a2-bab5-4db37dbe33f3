const fs = require('fs');

console.log('🔧 FIXING REMAINING MALFORMED PATTERNS');
console.log('====================================');

// Files with remaining malformed patterns
const REMAINING_FILES = [
  'src/contexts/FavoritesContext.tsx',
  'src/context/ChatContext.tsx',
  'src/context/NotificationContext.tsx',
  'src/core/middleware/auth/providerProtection.tsx',
  'src/core/middleware/auth/routeCheck.tsx',
];

function fixRemainingMalformedPatterns(content) {
  return (
    content
      // Fix: const relevantSet={type} 'provider' ? favorites.providers :
      .replace(/const\s+(\w+)=\{([^}]+)\}\s+'([^']+)'\s*\?/g, "const $1 = $2 === '$3' ?")

      // Fix: const v={c} 'x' ? r : (r & 0x3) | 0x8;
      .replace(
        /const\s+(\w+)=\{([^}]+)\}\s+'([^']+)'\s*\?\s*([^:]+)\s*:\s*([^;]+);/g,
        "const $1 = $2 === '$3' ? $4 : $5;"
      )

      // Fix: const granted={status} 'granted';
      .replace(/const\s+(\w+)=\{([^}]+)\}\s+'([^']+)';/g, "const $1 = $2 === '$3';")

      // Fix: const isExemptPath={pathname} '/provider/onboarding' || pathname.includes('/provider/onboarding');
      .replace(
        /const\s+(\w+)=\{([^}]+)\}\s+'([^']+)'\s*\|\|\s*([^;]+);/g,
        "const $1 = $2 === '$3' || $4;"
      )

      // Fix: const isInAuthGroup={firstSegment} '(auth)';
      .replace(/const\s+(\w+)=\{([^}]+)\}\s+'([^']+)';/g, "const $1 = $2 === '$3';")

      // Additional patterns for complex expressions
      .replace(/const\s+(\w+)=\{([^}]+)\}\s+([^=]+)=/g, 'const $1 = $2 === $3 =')
      .replace(/const\s+(\w+)=\{([^}]+)\}\s+([^;]+);/g, 'const $1 = $2 === $3;')
  );
}

function processFile(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  File not found: ${filePath}`);
      return false;
    }

    const content = fs.readFileSync(filePath, 'utf8');
    const fixedContent = fixRemainingMalformedPatterns(content);

    if (content !== fixedContent) {
      fs.writeFileSync(filePath, fixedContent);
      console.log(`✅ Fixed: ${filePath}`);
      return true;
    } else {
      console.log(`ℹ️  No changes needed: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Process all remaining files
console.log('\\nProcessing remaining malformed patterns...');
let fixedCount = 0;

REMAINING_FILES.forEach(file => {
  if (processFile(file)) {
    fixedCount++;
  }
});

console.log(`\\n🎉 REMAINING MALFORMED PATTERN FIX COMPLETE`);
console.log(`📊 Files processed: ${REMAINING_FILES.length}`);
console.log(`✅ Files fixed: ${fixedCount}`);
console.log(`ℹ️  Files unchanged: ${REMAINING_FILES.length - fixedCount}`);
