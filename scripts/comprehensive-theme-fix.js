#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Get all files with theme.theme patterns
function getFilesWithThemePatterns() {
  try {
    const output = execSync(
      'find src/ -name "*.tsx" -o -name "*.ts" | xargs grep -l "theme\\.theme\\."',
      { encoding: 'utf8' }
    );
    return output
      .trim()
      .split('\n')
      .filter(file => file.length > 0);
  } catch (error) {
    console.log('No files found with theme.theme patterns');
    return [];
  }
}

// Fix theme patterns in a single file
function fixFileThemePatterns(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let changesMade = 0;

    // Fix theme.theme.colors -> theme.colors
    const themePattern = /theme\.theme\.colors/g;
    const themeMatches = content.match(themePattern);
    if (themeMatches) {
      content = content.replace(themePattern, 'theme.colors');
      changesMade += themeMatches.length;
    }

    // Fix theme.theme.spacing -> theme.spacing
    const spacingPattern = /theme\.theme\.spacing/g;
    const spacingMatches = content.match(spacingPattern);
    if (spacingMatches) {
      content = content.replace(spacingPattern, 'theme.spacing');
      changesMade += spacingMatches.length;
    }

    // Fix theme.theme.typography -> theme.typography
    const typographyPattern = /theme\.theme\.typography/g;
    const typographyMatches = content.match(typographyPattern);
    if (typographyMatches) {
      content = content.replace(typographyPattern, 'theme.typography');
      changesMade += typographyMatches.length;
    }

    // Fix theme.theme.borderRadius -> theme.borderRadius
    const borderRadiusPattern = /theme\.theme\.borderRadius/g;
    const borderRadiusMatches = content.match(borderRadiusPattern);
    if (borderRadiusMatches) {
      content = content.replace(borderRadiusPattern, 'theme.borderRadius');
      changesMade += borderRadiusMatches.length;
    }

    // Fix any other theme.theme.* patterns
    const generalThemePattern = /theme\.theme\./g;
    const generalMatches = content.match(generalThemePattern);
    if (generalMatches) {
      content = content.replace(generalThemePattern, 'theme.');
      changesMade += generalMatches.length;
    }

    if (changesMade > 0) {
      fs.writeFileSync(filePath, content, 'utf8');
      return changesMade;
    }

    return 0;
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return 0;
  }
}

// Main execution
console.log('🔧 Starting comprehensive theme pattern fixes...');

const files = getFilesWithThemePatterns();
console.log(`📁 Found ${files.length} files with theme.theme patterns`);

let totalFixed = 0;
let filesProcessed = 0;
let filesWithChanges = 0;

for (const file of files) {
  const changes = fixFileThemePatterns(file);
  if (changes > 0) {
    console.log(`✅ ${file}: Fixed ${changes} patterns`);
    filesWithChanges++;
    totalFixed += changes;
  }
  filesProcessed++;

  // Progress indicator
  if (filesProcessed % 50 === 0) {
    console.log(`📊 Progress: ${filesProcessed}/${files.length} files processed`);
  }
}

console.log('\n🎉 COMPREHENSIVE THEME FIX COMPLETE!');
console.log(`📊 Files processed: ${filesProcessed}`);
console.log(`📝 Files with changes: ${filesWithChanges}`);
console.log(`🔧 Total patterns fixed: ${totalFixed}`);
console.log(`💯 Success rate: ${((filesWithChanges / filesProcessed) * 100).toFixed(1)}%`);
