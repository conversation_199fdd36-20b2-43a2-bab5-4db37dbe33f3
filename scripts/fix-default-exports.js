#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 Default Export Fixer - Starting automated fixes...\n');

// Configuration for fixes
const FIXES = {
  // Input component import fixes
  inputImports: {
    pattern: /import\s*\{\s*Input\s*\}\s*from\s*['"]@components\/ui\/form\/Input['"]/g,
    replacement: "import Input from '@components/ui/form/Input'",
  },

  // Button prop fixes
  buttonProps: {
    // Fix loading prop to isLoading
    loadingProp: {
      pattern: /loading=\{([^}]+)\}/g,
      replacement: 'isLoading={$1}',
    },
    // Fix title prop to children
    titleToChildren: {
      pattern: /<Button([^>]*)\s+title=["']([^"']+)["']([^>]*?)(?:\s*\/>|>\s*<\/Button>)/g,
      replacement: '<Button$1$3>$2</Button>',
    },
    // Fix variant names
    variants: {
      'variant="primary"': 'variant="filled"',
      'variant="outline"': 'variant="outlined"',
      'variant="destructive"': 'variant="outlined" color="error"',
    },
  },

  // Input component prop fixes
  inputProps: {
    // Fix style prop to containerStyle
    styleProp: {
      pattern: /(<Input[^>]*)\s+style=\{([^}]+)\}/g,
      replacement: '$1 containerStyle={$2}',
    },
  },

  // Icon import fixes
  iconImports: {
    // Fix string icon references to proper imports
    stringIcons: {
      'leftIcon="mail"': 'leftIcon={Mail}',
      'leftIcon="user"': 'leftIcon={User}',
      'leftIcon="lock"': 'leftIcon={Lock}',
      'leftIcon="phone"': 'leftIcon={Phone}',
      'leftIcon="map-pin"': 'leftIcon={MapPin}',
      'rightIcon="check"': 'rightIcon={Check}',
    },
  },

  // Type import fixes
  typeImports: {
    pattern: /import\s*\{[^}]*\}\s*from\s*['"]@types\/([^'"]+)['"]/g,
    replacement: (match, typePath) => {
      return match.replace('@types/', '../../../types/');
    },
  },
};

// Files to process (focusing on auth and admin files that had issues)
const TARGET_FILES = [
  'src/app/(auth)/profile-setup.tsx',
  'src/app/(auth)/quick-register.tsx',
  'src/app/(auth)/register-keyboard-test.tsx',
  'src/app/(auth)/verification-notice.tsx',
  'src/app/(auth)/unified-onboarding.tsx',
  'src/app/(admin)/verification-queue.tsx',
];

// Additional files that might have similar issues
const ADDITIONAL_PATTERNS = [
  'src/app/(auth)/**/*.tsx',
  'src/app/(admin)/**/*.tsx',
  'src/components/**/*.tsx',
];

function findFilesWithPattern(pattern) {
  try {
    const result = execSync(`find . -name "${pattern}" -type f`, { encoding: 'utf8' });
    return result
      .trim()
      .split('\n')
      .filter(f => f.length > 0);
  } catch (error) {
    return [];
  }
}

function getAllTargetFiles() {
  let allFiles = [...TARGET_FILES];

  // Add files from patterns
  ADDITIONAL_PATTERNS.forEach(pattern => {
    const globPattern = pattern.replace('**/', '').replace('*', '');
    const foundFiles = findFilesWithPattern(globPattern);
    allFiles = [...allFiles, ...foundFiles];
  });

  // Remove duplicates and filter existing files
  const uniqueFiles = [...new Set(allFiles)];
  return uniqueFiles.filter(file => {
    try {
      return fs.existsSync(file);
    } catch {
      return false;
    }
  });
}

function fixInputImports(content) {
  console.log('  📝 Fixing Input component imports...');
  return content.replace(FIXES.inputImports.pattern, FIXES.inputImports.replacement);
}

function fixButtonProps(content) {
  console.log('  🔘 Fixing Button component props...');
  let fixed = content;

  // Fix loading prop
  fixed = fixed.replace(
    FIXES.buttonProps.loadingProp.pattern,
    FIXES.buttonProps.loadingProp.replacement
  );

  // Fix title to children
  fixed = fixed.replace(
    FIXES.buttonProps.titleToChildren.pattern,
    FIXES.buttonProps.titleToChildren.replacement
  );

  // Fix variant names
  Object.entries(FIXES.buttonProps.variants).forEach(([from, to]) => {
    fixed = fixed.replace(new RegExp(from.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), to);
  });

  return fixed;
}

function fixInputProps(content) {
  console.log('  📋 Fixing Input component props...');
  return content.replace(
    FIXES.inputProps.styleProp.pattern,
    FIXES.inputProps.styleProp.replacement
  );
}

function fixIconImports(content) {
  console.log('  🎨 Fixing icon imports...');
  let fixed = content;

  // Fix string icon references
  Object.entries(FIXES.iconImports.stringIcons).forEach(([from, to]) => {
    fixed = fixed.replace(new RegExp(from.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), to);
  });

  // Add necessary icon imports if they're used but not imported
  const usedIcons = [];
  if (fixed.includes('leftIcon={Mail}') || fixed.includes('rightIcon={Mail}'))
    usedIcons.push('Mail');
  if (fixed.includes('leftIcon={User}') || fixed.includes('rightIcon={User}'))
    usedIcons.push('User');
  if (fixed.includes('leftIcon={Lock}') || fixed.includes('rightIcon={Lock}'))
    usedIcons.push('Lock');
  if (fixed.includes('leftIcon={Phone}') || fixed.includes('rightIcon={Phone}'))
    usedIcons.push('Phone');
  if (fixed.includes('leftIcon={MapPin}') || fixed.includes('rightIcon={MapPin}'))
    usedIcons.push('MapPin');
  if (fixed.includes('leftIcon={Check}') || fixed.includes('rightIcon={Check}'))
    usedIcons.push('Check');

  if (usedIcons.length > 0) {
    // Check if lucide-react-native import exists
    const hasLucideImport = fixed.includes("from 'lucide-react-native'");
    if (!hasLucideImport) {
      // Add import after other imports
      const importMatch = fixed.match(/(import[^;]+;[\s\n]*)+/);
      if (importMatch) {
        const importSection = importMatch[0];
        const newImport = `import { ${usedIcons.join(', ')} } from 'lucide-react-native';\n`;
        fixed = fixed.replace(importSection, importSection + newImport);
      }
    } else {
      // Update existing import
      fixed = fixed.replace(
        /import\s*\{([^}]+)\}\s*from\s*['"]lucide-react-native['"]/,
        (match, existingIcons) => {
          const existing = existingIcons.split(',').map(s => s.trim());
          const allIcons = [...new Set([...existing, ...usedIcons])];
          return `import { ${allIcons.join(', ')} } from 'lucide-react-native'`;
        }
      );
    }
  }

  return fixed;
}

function fixTypeImports(content) {
  console.log('  📚 Fixing type imports...');
  return content.replace(FIXES.typeImports.pattern, FIXES.typeImports.replacement);
}

function fixMissingButtonChildren(content) {
  console.log('  👶 Fixing missing Button children...');
  // Fix buttons that have title but no children
  return content.replace(
    /<Button([^>]*)\s+title=["']([^"']+)["']([^>]*)\s*\/>/g,
    '<Button$1$3>$2</Button>'
  );
}

function processFile(filePath) {
  console.log(`\n🔍 Processing: ${filePath}`);

  try {
    let content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;

    // Apply all fixes
    content = fixInputImports(content);
    content = fixButtonProps(content);
    content = fixInputProps(content);
    content = fixIconImports(content);
    content = fixTypeImports(content);
    content = fixMissingButtonChildren(content);

    // Write back if changed
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`  ✅ Fixed: ${filePath}`);
      return true;
    } else {
      console.log(`  ⏭️  No changes needed: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`  ❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

function runTypeScriptCheck() {
  console.log('\n🔍 Running TypeScript check...');
  try {
    execSync('npx tsc --noEmit --skipLibCheck', { stdio: 'pipe' });
    console.log('✅ TypeScript check passed!');
    return true;
  } catch (error) {
    console.log('⚠️  TypeScript errors still exist:');
    console.log(error.stdout?.toString() || error.message);
    return false;
  }
}

function main() {
  const startTime = Date.now();

  console.log('🔍 Finding all target files...');
  const targetFiles = getAllTargetFiles();
  console.log(`📁 Found ${targetFiles.length} files to process\n`);

  let fixedCount = 0;

  targetFiles.forEach(file => {
    if (processFile(file)) {
      fixedCount++;
    }
  });

  console.log(`\n📊 Summary:`);
  console.log(`   Files processed: ${targetFiles.length}`);
  console.log(`   Files fixed: ${fixedCount}`);
  console.log(`   Files unchanged: ${targetFiles.length - fixedCount}`);

  // Run TypeScript check
  const tsCheckPassed = runTypeScriptCheck();

  const endTime = Date.now();
  const duration = ((endTime - startTime) / 1000).toFixed(2);

  console.log(`\n🎉 Automated fix completed in ${duration}s`);

  if (tsCheckPassed) {
    console.log('🎊 All default export issues have been resolved!');
  } else {
    console.log('🔧 Some issues may remain. Check the TypeScript output above.');
  }
}

// Run the script
main();
