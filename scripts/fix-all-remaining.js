#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 Final Comprehensive Fixer - Fixing all remaining syntax issues...\n');

// Get all TypeScript files
function getAllTsFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);

  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
      getAllTsFiles(filePath, fileList);
    } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
      fileList.push(filePath);
    }
  });

  return fileList;
}

// Comprehensive syntax fixes
function fixAllSyntaxIssues(content, filePath) {
  let fixed = content;
  const changes = [];

  // Fix 1: Fix method chains that are split incorrectly (most common issue)
  fixed = fixed.replace(/(\w+)\s*\n\s*\.(\w+)/g, (match, obj, method) => {
    changes.push(`Fixed method chain: ${obj}.${method}`);
    return `${obj}.${method}`;
  });

  // Fix 2: Fix incomplete destructuring assignments
  fixed = fixed.replace(
    /const\s*\{\s*([^}]+)\s*\}\s*\n\s*=\s*([^;]+);?/g,
    (match, destructured, assignment) => {
      changes.push(`Fixed destructuring assignment`);
      return `const { ${destructured.trim()} } = ${assignment.trim()};`;
    }
  );

  // Fix 3: Fix function parameter destructuring
  fixed = fixed.replace(
    /\(\s*\{\s*([^}]+)\s*\}\s*\n\s*:\s*([^)]+)\s*\)/g,
    (match, params, type) => {
      changes.push(`Fixed function parameter destructuring`);
      return `({ ${params.trim()} }: ${type.trim()})`;
    }
  );

  // Fix 4: Fix JSX syntax issues
  fixed = fixed.replace(/\):\s*\(\s*\{/g, ') : (');
  fixed = fixed.replace(/return\s*\(\s*<([^>]+)>/g, 'return (\n    <$1>');

  // Fix 5: Fix object property syntax
  fixed = fixed.replace(/(\w+):\s*([^,]+),(\s*);/g, '$1: $2$3;');

  // Fix 6: Fix array/object trailing commas before semicolons
  fixed = fixed.replace(/,(\s*);/g, '$1;');

  // Fix 7: Fix incomplete try-catch blocks
  fixed = fixed.replace(/\}\s*catch\s*\(\s*([^)]+)\s*\)\s*\{/g, '} catch ($1) {');

  // Fix 8: Fix export syntax issues
  fixed = fixed.replace(/export\s+function\s+if\s*\(/g, 'if (');
  fixed = fixed.replace(/export\s+function\s+for\s*\(/g, 'for (');
  fixed = fixed.replace(/export\s+function\s+constructor\s*\(/g, 'constructor(');

  // Fix 9: Fix incomplete arrow functions
  fixed = fixed.replace(/const\s+(\w+)\s*=\s*\(\s*([^)]*)\s*\)\s*=>\s*$/gm, 'const $1 = ($2) => {');

  // Fix 10: Fix incomplete class methods
  fixed = fixed.replace(
    /(\s+)(static\s+)?(async\s+)?(\w+)\s*\(\s*([^)]*)\s*\)\s*:\s*([^{]+)\s*$/gm,
    '$1$2$3$4($5): $6 {'
  );

  return { content: fixed, changes };
}

// Fix specific file patterns
function fixSpecificPatterns(content, filePath) {
  let fixed = content;
  const fileName = path.basename(filePath);

  // Fix Supabase query chains
  if (content.includes('supabase.from(')) {
    fixed = fixed.replace(/\.select\([^)]+\)\s*;\s*\.(\w+)/g, '.select($&).$1');
    fixed = fixed.replace(/\.eq\([^)]+\)\s*;\s*\.(\w+)/g, '.eq($&).$1');
    fixed = fixed.replace(/\.gte\([^)]+\)\s*;\s*\.(\w+)/g, '.gte($&).$1');
    fixed = fixed.replace(/\.lte\([^)]+\)\s*;\s*\.(\w+)/g, '.lte($&).$1');
    fixed = fixed.replace(/\.order\([^)]+\)\s*;\s*\.(\w+)/g, '.order($&).$1');
    fixed = fixed.replace(/\.limit\([^)]+\)\s*;\s*\.(\w+)/g, '.limit($&).$1');
  }

  // Fix React component patterns
  if (fileName.endsWith('.tsx')) {
    // Fix return statements
    fixed = fixed.replace(/return\s*\(\s*<([^>]+)>\s*\n/g, 'return (\n    <$1>\n');

    // Fix JSX props
    fixed = fixed.replace(/(\w+)=\{([^}]+)\}\s*\n\s*(\w+)=/g, '$1={$2} $3=');
  }

  return fixed;
}

// Process all files
function processAllFiles() {
  const srcDir = path.join(process.cwd(), 'src');
  const files = getAllTsFiles(srcDir);
  let totalChanges = 0;
  let processedFiles = 0;

  console.log(`Found ${files.length} TypeScript files to process...\n`);

  files.forEach(filePath => {
    try {
      const content = fs.readFileSync(filePath, 'utf8');

      // Apply syntax fixes
      const { content: fixedContent, changes } = fixAllSyntaxIssues(content, filePath);

      // Apply specific pattern fixes
      const finalContent = fixSpecificPatterns(fixedContent, filePath);

      if (finalContent !== content) {
        fs.writeFileSync(filePath, finalContent);
        processedFiles++;
        totalChanges += changes.length;

        if (changes.length > 0) {
          console.log(
            `✅ Fixed ${path.relative(process.cwd(), filePath)} (${changes.length} changes)`
          );
        }
      }
    } catch (error) {
      console.error(`❌ Error processing ${filePath}:`, error.message);
    }
  });

  console.log(
    `\n📊 Processed ${processedFiles}/${files.length} files with ${totalChanges} total fixes`
  );
}

// Main execution
try {
  console.log('Starting comprehensive syntax fixes...\n');

  processAllFiles();

  console.log('\n✅ All syntax fixes completed!');
  console.log('🔍 Running final TypeScript check...\n');

  // Final TypeScript check
  try {
    const output = execSync('npx tsc --noEmit --skipLibCheck 2>&1', { encoding: 'utf8' });
    const errorLines = output.split('\n').filter(line => line.includes('error TS'));

    if (errorLines.length === 0) {
      console.log('🎉 All TypeScript errors resolved!');
    } else {
      console.log(`📊 Reduced to ${errorLines.length} remaining errors`);
      console.log('Most common remaining issues:');
      errorLines.slice(0, 5).forEach(line => console.log(`  - ${line}`));

      if (errorLines.length > 5) {
        console.log(`  ... and ${errorLines.length - 5} more`);
      }
    }
  } catch (error) {
    console.log('TypeScript check completed with some remaining issues.');
  }
} catch (error) {
  console.error('❌ Script execution failed:', error.message);
  process.exit(1);
}
