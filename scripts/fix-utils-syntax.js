const fs = require('fs');
const path = require('path');

// Files with specific syntax issues
const TARGET_FILES = [
  'src/utils/uploadToSupabase.ts',
  'src/utils/verificationImageUpload.ts',
  'src/utils/verificationStorageHelper.ts',
  'src/utils/validationErrorUtils.ts',
  'src/utils/unifiedErrorHandler.ts',
  'src/utils/validation.ts',
  'src/utils/verificationTableSetup.ts',
  'src/utils/withErrorHandling.tsx',
];

function fixSupabaseMethodChaining(content) {
  return (
    content
      // Fix semicolons in object properties
      .replace(/upsert:\s*true\s*;/g, 'upsert: true,')
      .replace(/upsert:\s*true\s*\s;/g, 'upsert: true,')
      .replace(
        /sortBy:\s*\{\s*column:\s*['"`]([^'"`]+)['"`]\s*,\s*order:\s*['"`]([^'"`]+)['"`]\s*\}\s*;/g,
        "sortBy: { column: '$1', order: '$2' },"
      )

      // Fix method chaining issues - remove trailing semicolons
      .replace(
        /\.list\s*\(\s*(['"`])([^'"`]*)\1\s*,\s*\{\s*limit:\s*(\d+)\s*\}\s*\)\s*;/g,
        '.list($1$2$1, { limit: $3 })'
      )
      .replace(/\.getPublicUrl\s*\(\s*([^)]+)\s*\)\s*;/g, '.getPublicUrl($1)')
      .replace(/\.remove\s*\(\s*\[\s*([^]]+)\s*\]\s*\)\s*;/g, '.remove([$1])')
      .replace(/\.list\s*\(\s*([^)]+)\s*\)\s*;/g, '.list($1)')

      // Fix try-catch blocks
      .replace(/\}\s*catch\s*\(\s*([^)]+)\s*\)\s*\{/g, '} catch ($1) {')

      // Fix function endings
      .replace(/}\s*;$/gm, '}')
  );
}

function fixObjectSyntax(content) {
  return (
    content
      // Fix object property syntax
      .replace(/,\s*\}\s*,/g, '}')
      .replace(/,\s*\},$/gm, '}')

      // Fix arrow function syntax in objects
      .replace(/:\s*\(\s*([^)]*)\s*\)\s*:\s*([^=]+)\s*=>\s*\{/g, ': ($1): $2 => {')

      // Fix missing commas in object properties
      .replace(/\}\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*:/g, '},\n  $1:')

      // Fix trailing commas in objects
      .replace(/,(\s*\})/g, '$1')
  );
}

function fixFunctionDeclarations(content) {
  return (
    content
      // Fix function declarations that end with semicolons
      .replace(
        /^(\s*)(export\s+)?(async\s+)?function\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\([^)]*\)\s*:\s*[^{]+\s*\{[\s\S]*?\}\s*;$/gm,
        '$1$2$3function $4(...): ... { ... }'
      )

      // Fix arrow function exports
      .replace(/^(\s*export\s+const\s+[a-zA-Z_][a-zA-Z0-9_]*\s*=\s*[^;]+)\s*;$/gm, '$1')

      // Fix interface/type declarations
      .replace(/^(\s*export\s+(?:interface|type)\s+[^{]+\{[\s\S]*?\})\s*;$/gm, '$1')
  );
}

function fixTryCatchBlocks(content) {
  return (
    content
      // Fix orphaned catch blocks
      .replace(/^\s*\}\s*catch\s*\(\s*([^)]+)\s*\)\s*\{/gm, '  } catch ($1) {')

      // Fix try blocks without proper structure
      .replace(/try\s*\{([^}]*)\}\s*catch/g, 'try {\n$1\n  } catch')

      // Fix missing try keyword
      .replace(/\}\s*catch\s*\(\s*([^)]+)\s*\)\s*\{/g, '} catch ($1) {')
  );
}

function fixValidationErrorUtils(content) {
  // Specific fix for validationErrorUtils.ts
  return (
    content
      // Fix the validation object structure
      .replace(
        /min:\s*\(\s*min:\s*number[^{]*\{[^}]*$/gm,
        'min: (min: number, message = `Must be at least ${min}`) => (value: string | number): string | undefined => {'
      )
      .replace(/^\s*\},\s*$/gm, '  },')
      .replace(/^\s*\},\s*$/gm, '  }')

      // Ensure proper closing
      .replace(/(\w+:\s*\([^}]+\})\s*,?\s*$/gm, '$1,')
      .replace(/,(\s*)$/gm, '$1')
  );
}

function reconstructFileIfNeeded(filePath, content) {
  const fileName = path.basename(filePath);

  // If file is severely corrupted, provide a basic reconstruction
  if (fileName === 'validationErrorUtils.ts' && content.includes('The parser expected to find')) {
    return `// Validation Error Utils - Reconstructed
export const validationRules = {
  required: (message = 'This field is required') => (value: any): string | undefined => {
    if (!value || (typeof value === 'string' && !value.trim())) {
      return message;
    }
    return undefined;
  },
  
  min: (min: number, message = \`Must be at least \${min}\`) => (value: string | number): string | undefined => {
    const numValue = typeof value === 'string' ? value.length : value;
    if (numValue < min) {
      return message;
    }
    return undefined;
  },
  
  max: (max: number, message = \`Must be at most \${max}\`) => (value: string | number): string | undefined => {
    const numValue = typeof value === 'string' ? value.length : value;
    if (numValue > max) {
      return message;
    }
    return undefined;
  },
  
  email: (message = 'Invalid email format') => (value: string): string | undefined => {
    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;
    if (!emailRegex.test(value)) {
      return message;
    }
    return undefined;
  }
};

export default validationRules;
`;
  }

  return content;
}

function fixSpecificFile(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  File not found: ${filePath}`);
      return;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;

    // Apply all fixes
    content = fixSupabaseMethodChaining(content);
    content = fixObjectSyntax(content);
    content = fixFunctionDeclarations(content);
    content = fixTryCatchBlocks(content);
    content = fixValidationErrorUtils(content);
    content = reconstructFileIfNeeded(filePath, content);

    // Write back if changed
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed: ${filePath}`);
    } else {
      console.log(`📝 No changes needed: ${filePath}`);
    }
  } catch (error) {
    console.log(`❌ Error fixing ${filePath}: ${error.message}`);
  }
}

// Main execution
console.log('🔧 Fixing specific utility file syntax errors...');

TARGET_FILES.forEach(fixSpecificFile);

console.log('🎉 Utility file syntax fixes completed!');
