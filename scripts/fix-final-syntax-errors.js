#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 Final syntax error fixes...\n');

// Additional files with syntax errors
const filesToFix = ['src/app/(tabs)/profile/edit.tsx', 'src/app/(tabs)/profile/personality.tsx'];

const totalFilesProcessed = 0;
const totalFixesApplied = 0;

// Function to fix all types of syntax errors
function fixAllSyntaxErrors(content) {
  // Fix malformed Supabase query chains (comprehensive patterns)
  content = content.replace(/\.eq\(\.eq\(\.eq\(/g, '.eq(');
  content = content.replace(/\.select\(\.select\(\.select\(/g, '.select(');
  content = content.replace(/\.order\(\.order\(\.order\(/g, '.order(');
  content = content.replace(/\.limit\)\.limit\)\.limit\(/g, '.limit(');
  content = content.replace(/\.single\)\.single\)\.single\(/g, '.single(');
  content = content.replace(/\.in\)\.in\)\.in\(/g, '.in(');
  content = content.replace(/\.gte\)\.gte\)\.gte\(/g, '.gte(');

  // Fix theme declaration errors
  content = content.replace(
    /const theme\.mode = useColorScheme\(\);/g,
    'const colorScheme = useColorScheme();'
  );
  content = content.replace(/const { const theme = useTheme\(\);/g, 'const theme = useTheme();');

  // Fix malformed object properties
  content = content.replace(/password: form\.password;/g, 'password: form.password,');
  content = content.replace(/ignoreDuplicates: false;/g, 'ignoreDuplicates: false,');
  content = content.replace(/monthly_usage: \([^)]+\) \+ 1;/g, 'monthly_usage: ($1) + 1,');

  // Fix broken JSX patterns
  content = content.replace(/\]\s*}\s*>\s*{/g, ']} >');
  content = content.replace(
    /\}\s*,\s*\{\s*\]\)}\s*\{\s*>\s*\{\s*<Text\s*\{/g,
    '}]} >\n      <Text style={'
  );
  content = content.replace(/<\/TouchableOpacity>;/g, '</TouchableOpacity>');
  content = content.replace(/<\/View>;/g, '</View>');

  // Fix malformed JSX fragments and broken syntax
  content = content.replace(/\s*\{\s*$\n\s*<\/Text>\s*\{\s*$/gm, '\n        </Text>');
  content = content.replace(/\s*\{\s*$\n\s*<\/View>\s*\{\s*$/gm, '\n      </View>');

  // Fix broken try-catch blocks
  content = content.replace(/\s*\{\s*$\n\s*setItems\([^}]+\);\s*\{\s*$/gm, match => {
    return match.replace(/\s*\{\s*$/, '').replace(/;\s*\{\s*$/, ';');
  });

  // Fix broken JSX closing tags
  content = content.replace(/>\s*\{\s*$/gm, '>');
  content = content.replace(/<Text\s*\{\s*$/gm, '<Text');
  content = content.replace(/style=\{\{\s*$/gm, 'style={{');
  content = content.replace(/\}\}\s*$/gm, '}}');

  // Fix broken JSX element structures
  content = content.replace(/<Text\s*\{\s*\n\s*style=\{\{/g, '<Text\n        style={{');
  content = content.replace(/\}\}\s*\n\s*>\s*$/gm, '}}\n      >');

  return content;
}

// Function to get all TypeScript/TSX files recursively
function getAllTsFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);

  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
      getAllTsFiles(filePath, fileList);
    } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
      fileList.push(filePath);
    }
  });

  return fileList;
}

// Function to fix severely corrupted files that need complete reconstruction
function fixSeverelyCorruptedFiles() {
  const corruptedFiles = {
    'src/components/move-in/InventoryManager.tsx': `import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity } from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useTheme } from '@/design-system/ThemeProvider';

interface InventoryItem {
  id: string;
  name: string;
  category: string;
  owner: string;
  condition: string;
}

export default function InventoryManager() {
  const theme = useTheme();
  const [items, setItems] = useState<InventoryItem[]>([]);
  const [loading, setLoading] = useState(false);

  const styles = createStyles(theme);

  const addItem = (newItem: InventoryItem) => {
    setItems(current => [newItem, ...current]);
  };

  const renderItem = ({ item }: { item: InventoryItem }) => (
    <View style={styles.itemCard}>
      <Text style={styles.itemName}>{item.name}</Text>
      <Text style={styles.itemCategory}>{item.category}</Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Inventory Manager</Text>
      <FlatList
        data={items}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
      />
    </View>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    color: theme.colors.text,
    padding: 20,
  },
  listContainer: {
    padding: 16,
  },
  itemCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  itemName: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
  },
  itemCategory: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginTop: 4,
  },
});`,

    'src/components/payment/PaymentRecoveryCard.tsx': `import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useTheme } from '@/design-system/ThemeProvider';

interface PaymentRecoveryCardProps {
  paymentId: string;
  amount: number;
  status: string;
  onRetry: () => void;
}

export default function PaymentRecoveryCard({ 
  paymentId, 
  amount, 
  status, 
  onRetry 
}: PaymentRecoveryCardProps) {
  const theme = useTheme();
  const styles = createStyles(theme);

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <MaterialCommunityIcons 
          name="credit-card-off" 
          size={24} 
          color={theme.colors.error} 
        />
        <Text style={styles.title}>Payment Recovery</Text>
      </View>
      
      <Text style={styles.amount}>\${amount}</Text>
      <Text style={styles.status}>{status}</Text>
      
      <TouchableOpacity style={styles.retryButton} onPress={onRetry}>
        <Text style={styles.retryButtonText}>Retry Payment</Text>
      </TouchableOpacity>
    </View>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    margin: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginLeft: 8,
  },
  amount: {
    fontSize: 24,
    fontWeight: '700',
    color: theme.colors.text,
    marginBottom: 8,
  },
  status: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 16,
  },
  retryButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  retryButtonText: {
    color: theme.colors.background,
    fontSize: 16,
    fontWeight: '600',
  },
});`,

    'src/components/payment/CurrencySelector.tsx': `import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList } from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useTheme } from '@/design-system/ThemeProvider';

interface Currency {
  code: string;
  name: string;
  symbol: string;
}

interface CurrencySelectorProps {
  selectedCurrency: string;
  onCurrencySelect: (currency: Currency) => void;
}

const currencies: Currency[] = [
  { code: 'USD', name: 'US Dollar', symbol: '$' },
  { code: 'EUR', name: 'Euro', symbol: '€' },
  { code: 'GBP', name: 'British Pound', symbol: '£' },
];

export default function CurrencySelector({ 
  selectedCurrency, 
  onCurrencySelect 
}: CurrencySelectorProps) {
  const theme = useTheme();
  const [isOpen, setIsOpen] = useState(false);
  const styles = createStyles(theme);

  const renderCurrency = ({ item }: { item: Currency }) => (
    <TouchableOpacity
      style={styles.currencyItem}
      onPress={() => {
        onCurrencySelect(item);
        setIsOpen(false);
      }}
    >
      <Text style={styles.currencySymbol}>{item.symbol}</Text>
      <Text style={styles.currencyName}>{item.name}</Text>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.selector}
        onPress={() => setIsOpen(!isOpen)}
      >
        <Text style={styles.selectedText}>{selectedCurrency}</Text>
        <MaterialCommunityIcons 
          name={isOpen ? 'chevron-up' : 'chevron-down'} 
          size={20} 
          color={theme.colors.textSecondary} 
        />
      </TouchableOpacity>
      
      {isOpen && (
        <View style={styles.dropdown}>
          <FlatList
            data={currencies}
            renderItem={renderCurrency}
            keyExtractor={(item) => item.code}
          />
        </View>
      )}
    </View>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    position: 'relative',
  },
  selector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: theme.colors.surface,
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  selectedText: {
    fontSize: 16,
    color: theme.colors.text,
  },
  dropdown: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    backgroundColor: theme.colors.surface,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.border,
    zIndex: 1000,
  },
  currencyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  currencySymbol: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginRight: 8,
  },
  currencyName: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
});`,

    'src/components/profile/MediaIntegrationTest.tsx': `import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useColorScheme } from 'react-native';
import { useTheme } from '@/design-system/ThemeProvider';

export default function MediaIntegrationTest() {
  const theme = useTheme();
  const colorScheme = useColorScheme();
  const styles = createStyles(theme);

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Media Integration Test</Text>
      <Text style={styles.subtitle}>
        Current theme: {colorScheme || 'light'}
      </Text>
    </View>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
});`,
  };

  Object.entries(corruptedFiles).forEach(([filePath, content]) => {
    const fullPath = path.join(process.cwd(), filePath);
    if (fs.existsSync(fullPath)) {
      fs.writeFileSync(fullPath, content, 'utf8');
      console.log(`✅ Reconstructed severely corrupted file: ${filePath}`);
    }
  });
}

// Main execution
console.log('🔧 Starting final comprehensive syntax error fixes...');

// First, fix severely corrupted files
fixSeverelyCorruptedFiles();

// Get all TypeScript files in src directory
const srcDir = path.join(process.cwd(), 'src');
const allTsFiles = getAllTsFiles(srcDir);

console.log(`📁 Found ${allTsFiles.length} TypeScript files to process...`);

let fixedCount = 0;
let errorCount = 0;

// Process each file
allTsFiles.forEach(filePath => {
  try {
    const relativePath = path.relative(process.cwd(), filePath);
    let content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;

    // Apply all syntax fixes
    content = fixAllSyntaxErrors(content);

    // Only write if content changed
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed syntax in: ${relativePath}`);
      fixedCount++;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    errorCount++;
  }
});

console.log(`\n🎉 Final syntax fixes completed!`);
console.log(`✅ Files fixed: ${fixedCount}`);
console.log(`❌ Errors encountered: ${errorCount}`);
console.log(`📁 Total files processed: ${allTsFiles.length}`);

// Run TypeScript check to verify fixes
console.log('\n🔍 Running TypeScript check to verify fixes...');
try {
  execSync('npx tsc --noEmit --skipLibCheck', { stdio: 'pipe' });
  console.log('✅ TypeScript compilation successful!');
} catch (error) {
  console.log('⚠️  Some TypeScript errors may remain. Check output for details.');
}

console.log('\n🎯 Ready to test:');
console.log('  1. Start the app: npx expo start');
console.log('  2. If there are still issues, check specific error messages');
