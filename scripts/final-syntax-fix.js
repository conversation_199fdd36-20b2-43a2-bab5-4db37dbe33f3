#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Files with remaining malformed patterns
const filesToFix = [
  'src/components/ui/FavoriteButton.tsx',
  'src/components/media/CorrectedBucketUploader.tsx',
  'src/components/debug/SafetySystemDebugger.tsx',
  'src/components/services/AvailabilityCalendarView.tsx',
  'src/components/ui/StepProgress.tsx',
  'src/components/ui/Divider.tsx',
  'src/components/ui/core/Card.tsx',
  'src/components/profile/PersonaOptimizedOnboarding.tsx',
  'src/components/profile/ProfileMemorySection.tsx',
  'src/components/common/SaveButton.tsx',
  'src/components/common/MemoryBank/SaveToMemoryBank.tsx',
  'src/components/serviceProvider/EnhancedServiceProviderDashboard.tsx',
  'src/components/common/ListingCard.tsx',
  'src/components/verification/TrustScoreBadge.tsx',
  'src/components/admin/BulkOperationsBar.tsx',
  'src/components/agreement/VersionHistory.tsx',
  'src/components/agreement/AgreementCustomizer.tsx',
  'src/components/matches/MatchActionButtons.tsx',
  'src/components/agreement/ChatToAgreementConnector.tsx',
  'src/components/matching/SwipeCard.tsx',
  'src/components/matching/SwipeMatchCard.tsx',
  'src/features/home/<USER>/HomeScreen.tsx',
  'src/app/household/index.tsx',
  'src/app/verification/simple-flow.tsx',
  'src/app/supabase-diagnostic.tsx',
  'src/app/provider/onboarding.tsx',
  'src/app/agreement/review.tsx',
  'src/app/admin/_layout.tsx',
  'src/app/admin/settings/index.tsx',
  'src/app/(tabs)/profile/media.tsx',
  'src/app/(tabs)/profile/payment-methods.tsx',
  'src/features/home/<USER>/WelcomeHeader.tsx',
];

// Comprehensive regex patterns to fix malformed const assignments
const patterns = [
  // Basic string comparison: const isCard={selectedType} 'credit_card'
  {
    pattern: /const\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*\{([a-zA-Z_][a-zA-Z0-9_.]*)\}\s+'([^']+)'/g,
    replacement: "const $1 = $2 === '$3'",
  },

  // String comparison with OR: const isCard={selectedType} 'credit_card' || selectedType === 'debit_card'
  {
    pattern:
      /const\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*\{([a-zA-Z_][a-zA-Z0-9_.]*)\}\s+'([^']+)'\s*\|\|\s*([^;]+);?/g,
    replacement: "const $1 = $2 === '$3' || $4;",
  },

  // Ternary operator: const x={direction} 'right' ? SCREEN_WIDTH * 1.5 : direction === 'left' ? -SCREEN_WIDTH * 1.5 : 0
  {
    pattern:
      /const\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*\{([a-zA-Z_][a-zA-Z0-9_.]*)\}\s+'([^']+)'\s*\?\s*([^:]+)\s*:\s*([^;]+);?/g,
    replacement: "const $1 = $2 === '$3' ? $4 : $5;",
  },

  // Variable comparison: const isActive={activeTab} tabId
  {
    pattern:
      /const\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*\{([a-zA-Z_][a-zA-Z0-9_.]*)\}\s+([a-zA-Z_][a-zA-Z0-9_.]+);?/g,
    replacement: 'const $1 = $2 === $3;',
  },

  // Numeric comparison: const isCurrent={index} currentStep - 1
  {
    pattern:
      /const\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*\{([a-zA-Z_][a-zA-Z0-9_.]*)\}\s+([a-zA-Z_][a-zA-Z0-9_.\-\s]+);?/g,
    replacement: 'const $1 = $2 === $3;',
  },

  // Complex ternary with object: const baseStyle={orientation} 'horizontal' ? {...} : {...}
  {
    pattern:
      /const\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*\{([a-zA-Z_][a-zA-Z0-9_.]*)\}\s+'([^']+)'\s*\?\s*\{([^}]+)\}\s*:\s*\{([^}]+)\};?/g,
    replacement: "const $1 = $2 === '$3' ? { $4 } : { $5 };",
  },

  // AND condition patterns
  {
    pattern:
      /const\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*\{([a-zA-Z_][a-zA-Z0-9_.]*)\}\s+'([^']+)'\s*&&\s*([^;]+);?/g,
    replacement: "const $1 = $2 === '$3' && $4;",
  },

  // Numeric comparison: const allSelected={selectedCount} totalItems
  {
    pattern:
      /const\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*\{([a-zA-Z_][a-zA-Z0-9_.]*)\}\s+([a-zA-Z_][a-zA-Z0-9_.]+)\s*&&\s*([^;]+);?/g,
    replacement: 'const $1 = $2 === $3 && $4;',
  },
];

function fixFile(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  File not found: ${filePath}`);
      return false;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;
    let changeCount = 0;

    // Apply all patterns
    patterns.forEach((patternObj, index) => {
      const originalContent = content;
      content = content.replace(patternObj.pattern, patternObj.replacement);

      if (content !== originalContent) {
        hasChanges = true;
        const matches = originalContent.match(patternObj.pattern);
        if (matches) {
          changeCount += matches.length;
          console.log(`   Pattern ${index + 1}: Fixed ${matches.length} occurrence(s)`);
        }
      }
    });

    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed ${filePath} (${changeCount} changes)`);
      return true;
    } else {
      console.log(`ℹ️  No changes needed: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

console.log('🔧 Starting final syntax fix for remaining malformed patterns...\n');

let totalFixed = 0;

filesToFix.forEach((file, index) => {
  console.log(`\n📁 Processing file ${index + 1}/${filesToFix.length}: ${file}`);
  const fixed = fixFile(file);
  if (fixed) {
    totalFixed++;
  }
});

console.log(`\n🎯 Fix Summary:`);
console.log(`   Files processed: ${filesToFix.length}`);
console.log(`   Files fixed: ${totalFixed}`);
console.log(`   Files unchanged: ${filesToFix.length - totalFixed}`);

if (totalFixed > 0) {
  console.log('\n✅ All remaining syntax errors should now be fixed!');
  console.log('🚀 Try running the Expo development server again.');
} else {
  console.log('\n🤔 No files were modified. The patterns may have already been fixed.');
}

console.log('\n🔍 Verification: Run this command to check for any remaining issues:');
console.log('grep -r "const [a-zA-Z_]*={[a-zA-Z_]*}" src/ --include="*.tsx" --include="*.ts"');
