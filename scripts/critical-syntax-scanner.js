#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🚨 CRITICAL SYNTAX ERROR SCANNER');
console.log('=================================\n');

// Define patterns for critical syntax errors that actually break compilation
const criticalErrorPatterns = [
  {
    name: 'Semicolon Instead of Comma in Object Properties',
    pattern: /:\s*[^,}\n]*;\s*$/gm,
    description: 'Object properties ending with ; instead of ,',
  },
  {
    name: 'Missing Comma in Object Literals',
    pattern: /:\s*[^,}\n]*\n\s*[a-zA-Z_$][a-zA-Z0-9_$]*\s*:/gm,
    description: 'Missing comma between object properties',
  },
  {
    name: 'Malformed Chain Calls',
    pattern: /\.\w+\(\)[^.;\n]*\n\s*\.\w+\(/gm,
    description: 'Method chain calls with missing dots or semicolons',
  },
  {
    name: 'Unclosed JSX Tags',
    pattern: /<[A-Z][a-zA-Z0-9]*[^>]*$/gm,
    description: 'JSX tags that are not properly closed',
  },
  {
    name: 'Malformed Arrow Functions',
    pattern: /=>\s*\{[^}]*prev\.map[^}]*\}/g,
    description: 'Arrow functions with map that may be missing return',
  },
];

// Files to scan - focus on recently problematic files
const targetFiles = [
  'src/app/(tabs)/profile/property-manager-dashboard.tsx',
  'src/app/(tabs)/profile/verification/identity.tsx',
  'src/app/(tabs)/profile/roommate-relations.tsx',
  'src/app/(tabs)/profile/payment-methods.tsx',
  'src/app/(tabs)/profile/edit.tsx',
  'src/app/(tabs)/saved.tsx',
  'src/app/chat/index.tsx',
  'src/app/(tabs)/services.tsx',
  'src/app/(tabs)/profile/unified-cultural.tsx',
  'src/api/HttpClient.ts',
  'src/api/matchingApi.ts',
  'src/api/OpenAIClient.ts',
  'src/api/sentimentApi.ts',
  'src/utils/verificationImageUpload.ts',
  'src/utils/verificationStorageHelper.ts',
];

function scanFileForCriticalErrors(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  File not found: ${filePath}`);
      return [];
    }

    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    const errors = [];

    criticalErrorPatterns.forEach(pattern => {
      let match;
      const regex = new RegExp(pattern.pattern.source, pattern.pattern.flags);

      while ((match = regex.exec(content)) !== null) {
        // Find line number
        const beforeMatch = content.substring(0, match.index);
        const lineNumber = beforeMatch.split('\n').length;

        errors.push({
          pattern: pattern.name,
          description: pattern.description,
          line: lineNumber,
          match: match[0].trim(),
          context: lines[lineNumber - 1]?.trim() || 'N/A',
        });
      }
    });

    return errors;
  } catch (error) {
    console.error(`❌ Error reading file ${filePath}:`, error.message);
    return [];
  }
}

function main() {
  console.log('🎯 Scanning critical files for blocking syntax errors...\n');

  let totalCriticalErrors = 0;
  const errorsByFile = {};

  targetFiles.forEach(filePath => {
    const fullPath = path.join(process.cwd(), filePath);
    const errors = scanFileForCriticalErrors(fullPath);

    if (errors.length > 0) {
      errorsByFile[filePath] = errors;
      totalCriticalErrors += errors.length;

      console.log(`🔍 ${filePath} (${errors.length} critical errors):`);
      errors.forEach(error => {
        console.log(`   Line ${error.line}: ${error.pattern}`);
        console.log(`   Context: ${error.context}`);
        console.log(`   Match: "${error.match}"`);
        console.log('');
      });
    }
  });

  console.log('\n📊 CRITICAL ERROR SUMMARY');
  console.log('=========================');

  if (totalCriticalErrors === 0) {
    console.log('✅ No critical syntax errors found in target files!');
    console.log('🎉 All scanned files should compile successfully.');
  } else {
    console.log(
      `❌ Found ${totalCriticalErrors} critical errors in ${Object.keys(errorsByFile).length} files`
    );
    console.log('\n🚨 IMMEDIATE ACTION REQUIRED:');
    console.log('============================');

    Object.entries(errorsByFile).forEach(([file, errors]) => {
      console.log(`📁 ${file}:`);
      errors.forEach(error => {
        console.log(`   • Line ${error.line}: ${error.pattern}`);
      });
    });
  }

  console.log('\n🔧 NEXT STEPS:');
  console.log('===============');
  console.log('1. Fix all critical errors listed above');
  console.log(
    '2. Test bundle generation: curl -s "http://localhost:8081/index.bundle?platform=ios&dev=true"'
  );
  console.log('3. Run comprehensive validation after fixes');
  console.log('4. If no critical errors remain, app should compile successfully');
}

main();
