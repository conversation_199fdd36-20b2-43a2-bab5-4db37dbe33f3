#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔧 COMPREHENSIVE DEBUGGING STRATEGY');
console.log('===================================\n');

// Phase 1: Critical Syntax Error Detection
console.log('📋 PHASE 1: CRITICAL SYNTAX ERROR DETECTION');
console.log('============================================\n');

const criticalPatterns = [
  {
    name: 'Malformed JSX Props (style= pattern)',
    pattern: 'style= \\(',
    description: 'Detects style= (pattern) syntax errors',
  },
  {
    name: 'Malformed Arrow Functions (=> {text})',
    pattern: '=> {[a-zA-Z]',
    description: 'Detects arrow functions with immediate text',
  },
  {
    name: 'Missing Return Statements in Map Functions',
    pattern: '\\.map\\([^)]*=> {[^}]*$',
    description: 'Detects map functions without proper returns',
  },
  {
    name: 'Unclosed JSX Tags',
    pattern: '<[A-Z][a-zA-Z]*[^>]*$',
    description: 'Detects potentially unclosed JSX tags',
  },
  {
    name: 'Malformed State Setters',
    pattern: 'set[A-Z][a-zA-Z]*\\(prev => {[^}]*$',
    description: 'Detects state setters without proper returns',
  },
  {
    name: 'Malformed Function Parameters',
    pattern: '\\([^)]*;[^)]*\\)',
    description: 'Detects semicolons in function parameters',
  },
  {
    name: 'Incorrect Prop Assignments',
    pattern: '[a-zA-Z]+= \\([^)]*\\) =>',
    description: 'Detects prop= (value) => patterns',
  },
];

let totalErrors = 0;
const errorsByFile = {};

console.log('🔍 Scanning for critical syntax patterns...\n');

criticalPatterns.forEach(pattern => {
  try {
    const result = execSync(
      `find src/ -name "*.tsx" -o -name "*.ts" | xargs grep -n "${pattern.pattern}"`,
      { encoding: 'utf8' }
    );

    if (result.trim()) {
      const lines = result.trim().split('\n');
      console.log(`❌ ${pattern.name}: ${lines.length} issues found`);
      console.log(`   Description: ${pattern.description}`);

      lines.slice(0, 3).forEach(line => {
        console.log(`   ${line}`);
        const filePath = line.split(':')[0];
        if (!errorsByFile[filePath]) errorsByFile[filePath] = [];
        errorsByFile[filePath].push(pattern.name);
      });

      if (lines.length > 3) {
        console.log(`   ... and ${lines.length - 3} more`);
      }
      console.log('');
      totalErrors += lines.length;
    } else {
      console.log(`✅ ${pattern.name}: No issues found`);
    }
  } catch (error) {
    console.log(`✅ ${pattern.name}: No issues found`);
  }
});

// Phase 2: Bundle Generation Testing
console.log('\n📱 PHASE 2: BUNDLE GENERATION TESTING');
console.log('=====================================\n');

const platforms = ['ios', 'android'];
const bundleResults = {};

platforms.forEach(platform => {
  console.log(`Testing ${platform.toUpperCase()} bundle generation...`);
  try {
    const bundleTest = execSync(
      `curl -s "http://localhost:8082/index.bundle?platform=${platform}&dev=true&minify=false" | head -c 200`,
      { encoding: 'utf8', timeout: 10000 }
    );

    if (bundleTest.includes('__BUNDLE_START_TIME__')) {
      console.log(`✅ ${platform.toUpperCase()} bundle: Success`);
      bundleResults[platform] = 'success';
    } else if (bundleTest.includes('SyntaxError')) {
      console.log(`❌ ${platform.toUpperCase()} bundle: Syntax Error Detected`);
      bundleResults[platform] = 'syntax_error';
    } else {
      console.log(`⚠️  ${platform.toUpperCase()} bundle: Unknown Issue`);
      bundleResults[platform] = 'unknown';
    }
  } catch (error) {
    console.log(`❌ ${platform.toUpperCase()} bundle: Network/Server Error`);
    bundleResults[platform] = 'network_error';
  }
});

// Phase 3: File-by-File Analysis
console.log('\n📁 PHASE 3: FILE-BY-FILE ANALYSIS');
console.log('==================================\n');

const problematicFiles = Object.keys(errorsByFile);
if (problematicFiles.length > 0) {
  console.log(`Found ${problematicFiles.length} files with potential issues:\n`);

  problematicFiles.forEach(filePath => {
    console.log(`📄 ${filePath}`);
    console.log(`   Issues: ${errorsByFile[filePath].join(', ')}`);

    // Try to read the file and show context
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const lines = content.split('\n');
      console.log(`   Lines: ${lines.length}`);

      // Look for common problematic patterns
      const problematicLines = [];
      lines.forEach((line, index) => {
        if (line.includes('=> {') && !line.includes('return')) {
          problematicLines.push(`Line ${index + 1}: ${line.trim()}`);
        }
      });

      if (problematicLines.length > 0) {
        console.log(`   Potential issues:`);
        problematicLines.slice(0, 2).forEach(issue => {
          console.log(`     ${issue}`);
        });
      }
    } catch (e) {
      console.log(`   Error reading file: ${e.message}`);
    }
    console.log('');
  });
} else {
  console.log('✅ No files with critical syntax issues detected');
}

// Phase 4: TypeScript Compilation Check
console.log('\n📝 PHASE 4: TYPESCRIPT COMPILATION CHECK');
console.log('========================================\n');

try {
  console.log('Running TypeScript compilation check...');
  const tscResult = execSync('npx tsc --noEmit --skipLibCheck', {
    encoding: 'utf8',
    timeout: 30000,
  });
  console.log('✅ TypeScript compilation: Success');
} catch (error) {
  console.log('❌ TypeScript compilation: Errors found');
  const errorOutput = error.stdout || error.stderr || error.message;
  const errorLines = errorOutput.split('\n').slice(0, 10);
  errorLines.forEach(line => {
    if (line.trim()) {
      console.log(`   ${line}`);
    }
  });
}

// Phase 5: Metro Bundler Health Check
console.log('\n🚇 PHASE 5: METRO BUNDLER HEALTH CHECK');
console.log('======================================\n');

try {
  const serverStatus = execSync('curl -s -o /dev/null -w "%{http_code}" http://localhost:8082', {
    encoding: 'utf8',
  });
  console.log(`Metro Server Status: HTTP ${serverStatus}`);

  if (serverStatus.trim() === '200') {
    console.log('✅ Metro bundler is healthy');
  } else {
    console.log('⚠️  Metro bundler may have issues');
  }
} catch (error) {
  console.log('❌ Metro bundler is not accessible');
}

// Phase 6: Recommendations
console.log('\n💡 PHASE 6: DEBUGGING RECOMMENDATIONS');
console.log('=====================================\n');

if (totalErrors === 0 && bundleResults.ios === 'success' && bundleResults.android === 'success') {
  console.log('🎉 EXCELLENT! No critical syntax errors detected!');
  console.log('✅ All bundle generations successful');
  console.log('✅ App is ready for development and testing');
} else {
  console.log('🔧 ISSUES DETECTED - Action Required:');

  if (totalErrors > 0) {
    console.log(`\n1. Fix ${totalErrors} syntax errors in ${problematicFiles.length} files`);
    console.log('   Priority files to fix:');
    problematicFiles.slice(0, 5).forEach(file => {
      console.log(`   - ${file}`);
    });
  }

  if (bundleResults.ios !== 'success' || bundleResults.android !== 'success') {
    console.log('\n2. Bundle generation issues detected');
    platforms.forEach(platform => {
      if (bundleResults[platform] !== 'success') {
        console.log(`   - ${platform.toUpperCase()}: ${bundleResults[platform]}`);
      }
    });
  }

  console.log('\n📋 IMMEDIATE NEXT STEPS:');
  console.log('1. Fix syntax errors in priority files');
  console.log('2. Test bundle generation after each fix');
  console.log('3. Run TypeScript compilation check');
  console.log('4. Test app functionality on simulators');
  console.log('5. Implement automated syntax checking in CI/CD');
}

console.log('\n🎯 DEBUGGING STRATEGY COMPLETE');
console.log('==============================');
console.log(`Total syntax errors found: ${totalErrors}`);
console.log(`Files requiring attention: ${problematicFiles.length}`);
console.log(
  `Bundle status: iOS=${bundleResults.ios || 'unknown'}, Android=${bundleResults.android || 'unknown'}`
);

if (totalErrors === 0) {
  console.log('\n✨ Your app is syntax-error free and ready for development! ✨');
} else {
  console.log('\n🔧 Use this report to systematically fix remaining issues.');
}
