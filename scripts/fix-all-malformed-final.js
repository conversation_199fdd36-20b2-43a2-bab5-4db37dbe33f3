const fs = require('fs');

console.log('🔧 COMPREHENSIVE FINAL MALFORMED PATTERN FIX');
console.log('============================================');

// All files with malformed patterns
const ALL_MALFORMED_FILES = [
  'src/app/(auth)/register.tsx',
  'src/app/(tabs)/profile/property-manager-dashboard.tsx',
  'src/app/(tabs)/profile/unified-account.tsx',
  'src/app/(tabs)/profile/unified-dashboard.tsx',
  'src/app/(tabs)/profile/unified-preferences.tsx',
  'src/app/(tabs)/profile/unified-settings.tsx',
  'src/app/admin/emergency/index.tsx',
  'src/app/admin/finance/index.tsx',
  'src/app/admin/matching/index.tsx',
  'src/app/admin/reports/index.tsx',
  'src/app/household/communication.tsx',
  'src/app/household/settings.tsx',
  'src/components/analytics/PredictiveAnalyticsTabBar.tsx',
  'src/components/feedback/ProfileFeedbackSystem.tsx',
  'src/components/location/AddLocationForm.tsx',
  'src/components/matching/SwipeMatchDeck.tsx',
  'src/components/messaging/SmartConversationThreads.tsx',
  'src/components/navigation/BottomTabBar.tsx',
  'src/components/payment/SavedCardsView.tsx',
  'src/components/ui/core/Button.tsx',
  'src/features/home/<USER>/useHomeData.ts',
  'src/features/home/<USER>/homeService.ts',
  'src/screens/EnhancedFavoritesScreen.tsx',
  'src/services/unified/repositories/ProfileVerificationRepository.ts',
  'src/utils/navigationFlowValidator.ts',
  'src/utils/phase3Accessibility.tsx',
];

function fixAllMalformedPatterns(content) {
  return (
    content
      // Primary pattern: const isActive={activeTab} tab.id;
      .replace(/const\s+(\w+)=\{([^}]+)\}\s+(\w+)\.(\w+);/g, 'const $1 = $2 === $3.$4;')

      // Pattern: const isActive={selectedTab} tab.key;
      .replace(/const\s+(\w+)=\{([^}]+)\}\s+(\w+)\.(\w+);/g, 'const $1 = $2 === $3.$4;')

      // Pattern: const variable={param} value;
      .replace(/const\s+(\w+)=\{([^}]+)\}\s+(\w+);/g, 'const $1 = $2 === $3;')

      // Pattern with strings: const isActive={param} 'value';
      .replace(/const\s+(\w+)=\{([^}]+)\}\s+'([^']+)';/g, "const $1 = $2 === '$3';")

      // Pattern with ternary: const value={param} 'test' ? a : b;
      .replace(
        /const\s+(\w+)=\{([^}]+)\}\s+'([^']+)'\s*\?\s*([^:]+)\s*:\s*([^;]+);/g,
        "const $1 = $2 === '$3' ? $4 : $5;"
      )

      // Pattern with OR: const value={param} 'test' || other;
      .replace(
        /const\s+(\w+)=\{([^}]+)\}\s+'([^']+)'\s*\|\|\s*([^;]+);/g,
        "const $1 = $2 === '$3' || $4;"
      )

      // Pattern with complex conditions
      .replace(
        /const\s+(\w+)=\{([^}]+)\}\s+'([^']+)'\s*\|\|\s*(\w+)\s*===\s*'([^']+)'/g,
        "const $1 = $2 === '$3' || $4 === '$5'"
      )

      // Pattern without quotes: const value={param} OTHER_CONST;
      .replace(/const\s+(\w+)=\{([^}]+)\}\s+([A-Z_][A-Z_0-9]*);/g, 'const $1 = $2 === $3;')

      // Pattern with numbers: const value={param} 123;
      .replace(/const\s+(\w+)=\{([^}]+)\}\s+(\d+);/g, 'const $1 = $2 === $3;')

      // Pattern with boolean: const value={param} true;
      .replace(/const\s+(\w+)=\{([^}]+)\}\s+(true|false);/g, 'const $1 = $2 === $3;')

      // Fix any remaining malformed patterns
      .replace(/const\s+(\w+)=\{([^}]+)\}\s+([^=;]+);/g, 'const $1 = $2 === $3;')
  );
}

function processFile(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  File not found: ${filePath}`);
      return false;
    }

    const content = fs.readFileSync(filePath, 'utf8');
    const fixedContent = fixAllMalformedPatterns(content);

    if (content !== fixedContent) {
      fs.writeFileSync(filePath, fixedContent);
      console.log(`✅ Fixed: ${filePath}`);
      return true;
    } else {
      console.log(`ℹ️  No changes needed: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Process all files
console.log('\\nProcessing all malformed patterns...');
let fixedCount = 0;

ALL_MALFORMED_FILES.forEach(file => {
  if (processFile(file)) {
    fixedCount++;
  }
});

console.log(`\\n🎉 COMPREHENSIVE MALFORMED PATTERN FIX COMPLETE`);
console.log(`📊 Files processed: ${ALL_MALFORMED_FILES.length}`);
console.log(`✅ Files fixed: ${fixedCount}`);
console.log(`ℹ️  Files unchanged: ${ALL_MALFORMED_FILES.length - fixedCount}`);
