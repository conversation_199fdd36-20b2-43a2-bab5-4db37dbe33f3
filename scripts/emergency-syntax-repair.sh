#!/bin/bash

echo "🚨 EMERGENCY SYNTAX REPAIR - WeRoomies Codebase"
echo "================================================"

# Function to fix specific patterns
fix_pattern() {
    local pattern="$1"
    local replacement="$2"
    local description="$3"
    
    echo "🔧 Fixing: $description"
    
    find src/ -name "*.tsx" -o -name "*.ts" | while read file; do
        if [[ -f "$file" ]]; then
            # Use perl for more complex regex replacements
            perl -i -pe "$pattern" "$file" 2>/dev/null || true
        fi
    done
}

# 1. Fix extra semicolons after function declarations
echo "1. Removing extra semicolons after function declarations..."
fix_pattern 's/\) \{;$/\) \{/g' "" "function declaration semicolons"

# 2. Fix extra semicolons after return statements
echo "2. Fixing return statement syntax..."
fix_pattern 's/return \(;$/return \(/g' "" "return statement semicolons"

# 3. Fix malformed JSX opening tags
echo "3. Fixing JSX opening tags..."
fix_pattern 's/<([A-Za-z][A-Za-z0-9]*);$/<$1/g' "" "JSX opening tag semicolons"

# 4. Fix malformed JSX closing tags
echo "4. Fixing JSX closing tags..."
fix_pattern 's/<\/([A-Za-z][A-Za-z0-9]*);$/<\/$1/g' "" "JSX closing tag semicolons"

# 5. Fix triple braces in JSX comments
echo "5. Fixing JSX comments..."
fix_pattern 's/\{\{\{\/\* (.*?) \*\/\}\}\}/\{\/\* $1 \*\/\}/g' "" "JSX comment braces"

# 6. Fix malformed style props
echo "6. Fixing style props..."
fix_pattern 's/style=\{\{([^}]+)\}\}/style=\{$1\}/g' "" "style prop braces"

# 7. Fix malformed arrow functions
echo "7. Fixing arrow functions..."
fix_pattern 's/\(\) =\{\}>/\(\) =>/g' "" "arrow function syntax"

# 8. Fix extra commas after semicolons
echo "8. Removing extra commas..."
fix_pattern 's/;,$/;/g' "" "semicolon comma combinations"

# 9. Fix interface/type declarations
echo "9. Fixing interface declarations..."
fix_pattern 's/interface ([A-Za-z]+) \{;,$/interface $1 \{/g' "" "interface declarations"

# 10. Fix object literal syntax
echo "10. Fixing object literals..."
fix_pattern 's/: \{;,$/: \{/g' "" "object literal syntax"

# 11. Fix function parameter syntax
echo "11. Fixing function parameters..."
fix_pattern 's/\(([^)]+)\) => \{;,$/\($1\) => \{/g' "" "function parameters"

# 12. Fix JSX prop syntax
echo "12. Fixing JSX props..."
fix_pattern 's/([a-zA-Z]+)=\{([^}]+)\} \/\};$/\1=\{\2\} \/>/g' "" "JSX prop syntax"

# 13. Fix import/export statements
echo "13. Fixing import/export statements..."
fix_pattern 's/import type \{;,$/import type \{/g' "" "import statements"

# 14. Fix useState and other hooks
echo "14. Fixing React hooks..."
fix_pattern 's/useState<([^>]+)>\(\{;,$/useState<$1>\(\{/g' "" "useState syntax"

# 15. Fix useCallback syntax
echo "15. Fixing useCallback..."
fix_pattern 's/useCallback\(([^,]+), \[;,$/useCallback\($1, \[/g' "" "useCallback syntax"

# Now fix specific file patterns that are causing issues
echo ""
echo "🎯 Fixing specific problematic patterns..."

# Fix admin users file specifically
if [[ -f "src/app/admin/users.tsx" ]]; then
    echo "Fixing admin users file..."
    perl -i -pe 's/\} \{;,$/\} \{/g' "src/app/admin/users.tsx"
    perl -i -pe 's/\);,$/\);/g' "src/app/admin/users.tsx"
    perl -i -pe 's/\};,$/\};/g' "src/app/admin/users.tsx"
fi

# Fix all files with malformed JSX
echo "Fixing malformed JSX across all files..."
find src/ -name "*.tsx" | xargs perl -i -pe 's/\{([^}]+)\} \/\};$/\{$1\} \/>/g'
find src/ -name "*.tsx" | xargs perl -i -pe 's/>\};$/>/g'
find src/ -name "*.tsx" | xargs perl -i -pe 's/\/>;,$/\/>/g'

# Fix function call syntax
echo "Fixing function calls..."
find src/ -name "*.tsx" -o -name "*.ts" | xargs perl -i -pe 's/\(\) =\{\}>/\(\) =>/g'
find src/ -name "*.tsx" -o -name "*.ts" | xargs perl -i -pe 's/\) \{;$/\) \{/g'

# Fix object destructuring
echo "Fixing object destructuring..."
find src/ -name "*.tsx" -o -name "*.ts" | xargs perl -i -pe 's/\{ ([^}]+) \};,$/\{ $1 \};/g'

# Fix conditional statements
echo "Fixing conditional statements..."
find src/ -name "*.tsx" -o -name "*.ts" | xargs perl -i -pe 's/if \(([^)]+)\) \{;$/if \($1\) \{/g'

# Fix try-catch blocks
echo "Fixing try-catch blocks..."
find src/ -name "*.tsx" -o -name "*.ts" | xargs perl -i -pe 's/try \{;$/try \{/g'
find src/ -name "*.tsx" -o -name "*.ts" | xargs perl -i -pe 's/catch \(([^)]+)\) \{;$/catch \($1\) \{/g'

# Fix array methods
echo "Fixing array methods..."
find src/ -name "*.tsx" -o -name "*.ts" | xargs perl -i -pe 's/\.map\(([^)]+)\) => \{;$/\.map\($1\) => \{/g'
find src/ -name "*.tsx" -o -name "*.ts" | xargs perl -i -pe 's/\.filter\(([^)]+)\) => \{;$/\.filter\($1\) => \{/g'

echo ""
echo "✅ Emergency syntax repair completed!"
echo "🔍 Running quick validation..."

# Quick validation
if command -v npx &> /dev/null; then
    echo "Checking TypeScript compilation..."
    npx tsc --noEmit --project tsconfig.json 2>&1 | head -10
else
    echo "TypeScript not available for validation"
fi

echo ""
echo "🚀 Attempting to start Expo server..."
echo "If successful, the app should now build without syntax errors." 