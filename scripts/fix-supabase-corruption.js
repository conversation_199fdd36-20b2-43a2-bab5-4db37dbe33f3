const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 COMPREHENSIVE SUPABASE CORRUPTION FIX');
console.log('========================================');

// Find all files with Supabase corruption patterns
function findCorruptedFiles() {
  const patterns = [
    '\\.is\\)\\.is\\)\\.is\\(',
    '\\.eq\\)\\.eq\\)\\.eq\\(',
    '\\.select\\([^)]*\\)\\s*;\\s*\\.',
    '\\.update\\([^)]*\\)\\s*;\\s*\\.',
    '\\.insert\\([^)]*\\)\\s*;\\s*\\.',
    '\\.delete\\(\\)\\s*;\\s*\\.',
  ];

  const files = new Set();

  patterns.forEach(pattern => {
    try {
      const result = execSync(`grep -r -l "${pattern}" src/`, { encoding: 'utf8' });
      result
        .split('\n')
        .filter(f => f.trim())
        .forEach(file => files.add(file.trim()));
    } catch (error) {
      // Pattern not found, continue
    }
  });

  return Array.from(files);
}

function fixSupabaseCorruption(content) {
  let fixed = content;

  // Fix triple method corruption patterns
  fixed = fixed
    // Fix .is).is).is patterns
    .replace(/\.is\)\.is\)\.is\(/g, '.is(')
    .replace(/\.eq\)\.eq\)\.eq\(/g, '.eq(')
    .replace(/\.neq\)\.neq\)\.neq\(/g, '.neq(')
    .replace(/\.gt\)\.gt\)\.gt\(/g, '.gt(')
    .replace(/\.lt\)\.lt\)\.lt\(/g, '.lt(')
    .replace(/\.gte\)\.gte\)\.gte\(/g, '.gte(')
    .replace(/\.lte\)\.lte\)\.lte\(/g, '.lte(')
    .replace(/\.like\)\.like\)\.like\(/g, '.like(')
    .replace(/\.ilike\)\.ilike\)\.ilike\(/g, '.ilike(')
    .replace(/\.in\)\.in\)\.in\(/g, '.in(')
    .replace(/\.contains\)\.contains\)\.contains\(/g, '.contains(')

    // Fix broken method chaining with semicolons
    .replace(/\.select\s*\([^)]*\)\s*;\s*\./g, '.select($1).')
    .replace(/\.update\s*\(\s*\{[^}]*\}\s*\)\s*;\s*\./g, '.update({...}).')
    .replace(/\.insert\s*\(\s*\{[^}]*\}\s*\)\s*;\s*\./g, '.insert({...}).')
    .replace(/\.insert\s*\(\s*\[[^\]]*\]\s*\)\s*;\s*\./g, '.insert([...]).')
    .replace(/\.delete\s*\(\s*\)\s*;\s*\./g, '.delete().')
    .replace(/\.upsert\s*\(\s*\{[^}]*\}\s*\)\s*;\s*\./g, '.upsert({...}).')
    .replace(/\.upsert\s*\(\s*\[[^\]]*\]\s*\)\s*;\s*\./g, '.upsert([...]).')

    // Fix specific broken patterns found in files
    .replace(/\.select\(\s*\)\s*;\s*\.single\(\s*\)\s*;/g, '.select().single()')
    .replace(/\.select\(\s*'[^']*'\s*\)\s*;\s*\.single\(\s*\)\s*;/g, ".select('$1').single()")
    .replace(/\.select\(\s*"[^"]*"\s*\)\s*;\s*\.single\(\s*\)\s*;/g, '.select("$1").single()')

    // Fix method chaining restoration
    .replace(/(\.\w+\([^)]*\))\s*;\s*(\.\w+\([^)]*\))/g, '$1$2')

    // Fix specific file corruptions
    .replace(
      /\.select\('id, first_name, email'\)\s*;\s*\.is\)\.is\)\.is\('email', null\);/g,
      ".select('id, first_name, email').is('email', null)"
    )

    .replace(
      /\.update\(\s*\{\s*email:\s*authUser\.user\.email,\s*updated_at:\s*new Date\(\)\.toISOString\(\)\s*\}\s*\)\s*;\s*\.eq\('id', profile\.id\);/g,
      ".update({ email: authUser.user.email, updated_at: new Date().toISOString() }).eq('id', profile.id)"
    )

    // Fix common Supabase patterns
    .replace(
      /\.from\s*\(\s*'([^']+)'\s*\)\s*\.select\s*\(\s*'([^']*)'\s*\)\s*;\s*\.eq\s*\(\s*'([^']+)'\s*,\s*([^)]+)\s*\)/g,
      ".from('$1').select('$2').eq('$3', $4)"
    )

    .replace(
      /\.from\s*\(\s*'([^']+)'\s*\)\s*\.update\s*\(\s*\{([^}]+)\}\s*\)\s*;\s*\.eq\s*\(\s*'([^']+)'\s*,\s*([^)]+)\s*\)/g,
      ".from('$1').update({$2}).eq('$3', $4)"
    )

    .replace(
      /\.from\s*\(\s*'([^']+)'\s*\)\s*\.insert\s*\(\s*\{([^}]+)\}\s*\)\s*;\s*\.select\s*\(\s*\)\s*\.single\s*\(\s*\)/g,
      ".from('$1').insert({$2}).select().single()"
    )

    // Remove duplicate method calls
    .replace(/(\.\w+\([^)]*\))\1+/g, '$1')

    // Fix trailing semicolons in method chains
    .replace(/(\.\w+\([^)]*\));(\s*$)/gm, '$1$2');

  return fixed;
}

function fixSpecificFilePatterns(content, filePath) {
  if (filePath.includes('fix-missing-emails/route.ts')) {
    return content
      .replace(
        /\.select\('id, first_name, email'\)\s*;\s*\.is\)\.is\)\.is\('email', null\);/g,
        ".select('id, first_name, email').is('email', null)"
      )
      .replace(
        /\.update\(\s*\{\s*email:\s*authUser\.user\.email,\s*updated_at:\s*new Date\(\)\.toISOString\(\)\s*\}\s*\)\s*;\s*\.eq\('id', profile\.id\);/g,
        ".update({ email: authUser.user.email, updated_at: new Date().toISOString() }).eq('id', profile.id)"
      );
  }

  if (filePath.includes('repair-emails.ts')) {
    return content.replace(/\.is\)\.is\)\.is\('email', null\);/g, ".is('email', null)");
  }

  return content;
}

function processFile(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  File not found: ${filePath}`);
      return false;
    }

    const content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;

    let fixed = fixSupabaseCorruption(content);
    fixed = fixSpecificFilePatterns(fixed, filePath);

    if (fixed !== originalContent) {
      fs.writeFileSync(filePath, fixed, 'utf8');
      console.log(`✅ Fixed corruption in: ${filePath}`);
      return true;
    } else {
      console.log(`📝 No corruption found in: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Error processing ${filePath}: ${error.message}`);
    return false;
  }
}

// Main execution
const corruptedFiles = findCorruptedFiles();
console.log(`\n🔍 Found ${corruptedFiles.length} potentially corrupted files:`);
corruptedFiles.forEach(file => console.log(`   - ${file}`));

console.log('\n🔧 Processing files...');
let fixedCount = 0;

corruptedFiles.forEach(file => {
  if (processFile(file)) {
    fixedCount++;
  }
});

console.log(`\n🎉 CORRUPTION FIX COMPLETED!`);
console.log(`   - Files processed: ${corruptedFiles.length}`);
console.log(`   - Files fixed: ${fixedCount}`);
console.log(`   - Files unchanged: ${corruptedFiles.length - fixedCount}`);

if (fixedCount > 0) {
  console.log('\n✅ Supabase method chaining has been restored!');
  console.log('🚀 Try running the app again with: npm run start');
} else {
  console.log('\n⚠️  No corruption patterns were fixed. Manual inspection may be needed.');
}
