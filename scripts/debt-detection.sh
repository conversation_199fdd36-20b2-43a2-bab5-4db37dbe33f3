#!/bin/bash
# WeRoomies Technical Debt Detection Script
# This script analyzes the codebase for technical debt and generates actionable reports

set -e

echo "🔍 WeRoomies Technical Debt Analysis"
echo "===================================="
echo ""

# Colors for output
RED='\033[0;31m'
YELLOW='\033[1;33m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Create reports directory
mkdir -p reports/debt-analysis

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 1. Code Complexity Analysis
echo -e "${BLUE}📊 Analyzing Code Complexity...${NC}"
if command_exists madge; then
    echo "  • Checking for circular dependencies..."
    madge --circular --extensions ts,tsx src/ > reports/debt-analysis/circular-deps.txt 2>&1 || true
    
    echo "  • Analyzing dependency complexity..."
    madge --summary --extensions ts,tsx src/ > reports/debt-analysis/dependency-summary.txt 2>&1 || true
else
    echo -e "${YELLOW}  ⚠️  madge not installed. Run: npm install -g madge${NC}"
fi

# 2. TypeScript Strict Mode Violations
echo -e "${BLUE}🔧 Checking TypeScript Strict Mode Compliance...${NC}"
echo "  • Scanning for 'any' types..."
grep -r "any" src/ --include="*.ts" --include="*.tsx" | wc -l > reports/debt-analysis/any-types-count.txt
grep -r ": any" src/ --include="*.ts" --include="*.tsx" > reports/debt-analysis/any-types-list.txt 2>/dev/null || echo "No explicit 'any' types found" > reports/debt-analysis/any-types-list.txt

echo "  • Checking for console.log statements..."
grep -r "console\.log" src/ --include="*.ts" --include="*.tsx" > reports/debt-analysis/console-logs.txt 2>/dev/null || echo "No console.log statements found" > reports/debt-analysis/console-logs.txt

# 3. Design System Compliance
echo -e "${BLUE}🎨 Checking Design System Compliance...${NC}"
echo "  • Scanning for hardcoded colors..."
{
    echo "=== Hardcoded Hex Colors ==="
    grep -r "backgroundColor.*#[0-9A-Fa-f]" src/ --include="*.ts" --include="*.tsx" 2>/dev/null || echo "✅ No hardcoded hex colors found"
    echo ""
    echo "=== Hardcoded RGB Colors ==="
    grep -r "color.*rgb" src/ --include="*.ts" --include="*.tsx" 2>/dev/null || echo "✅ No hardcoded RGB colors found"
    echo ""
    echo "=== Inline Style Objects ==="
    grep -r "style={{" src/ --include="*.ts" --include="*.tsx" | head -20 2>/dev/null || echo "No inline styles found in first 20 matches"
} > reports/debt-analysis/design-system-violations.txt

# 4. File Size Analysis
echo -e "${BLUE}📏 Analyzing File Sizes...${NC}"
echo "  • Finding large files (>500 lines)..."
{
    echo "=== Files Over 500 Lines ==="
    find src/ -name "*.ts" -o -name "*.tsx" | xargs wc -l | sort -nr | awk '$1 > 500 {print $2 " (" $1 " lines)"}' 2>/dev/null || echo "No files over 500 lines"
    echo ""
    echo "=== Files Over 300 Lines ==="
    find src/ -name "*.ts" -o -name "*.tsx" | xargs wc -l | sort -nr | awk '$1 > 300 && $1 <= 500 {print $2 " (" $1 " lines)"}' 2>/dev/null || echo "No files between 300-500 lines"
} > reports/debt-analysis/large-files.txt

# 5. Import/Export Analysis
echo -e "${BLUE}📦 Analyzing Imports and Exports...${NC}"
echo "  • Finding unused imports..."
{
    echo "=== Potential Unused Imports ==="
    # This is a simplified check - a proper tool like ts-unused-exports would be better
    grep -r "import.*from" src/ --include="*.ts" --include="*.tsx" | grep -v "export" | head -10 || echo "Analysis requires ts-unused-exports tool"
} > reports/debt-analysis/unused-imports.txt

# 6. Security Analysis
echo -e "${BLUE}🔒 Security Analysis...${NC}"
echo "  • Checking for potential security issues..."
{
    echo "=== Potential Security Issues ==="
    echo "• Hardcoded secrets or tokens:"
    grep -ri "token\|secret\|key\|password" src/ --include="*.ts" --include="*.tsx" | grep -v "interface\|type\|import" | head -5 2>/dev/null || echo "  No obvious hardcoded secrets found"
    echo ""
    echo "• Eval usage:"
    grep -r "eval(" src/ --include="*.ts" --include="*.tsx" 2>/dev/null || echo "  No eval() usage found ✅"
    echo ""
    echo "• innerHTML usage:"
    grep -r "innerHTML" src/ --include="*.ts" --include="*.tsx" 2>/dev/null || echo "  No innerHTML usage found ✅"
} > reports/debt-analysis/security-issues.txt

# 7. Performance Analysis
echo -e "${BLUE}⚡ Performance Analysis...${NC}"
echo "  • Checking for performance anti-patterns..."
{
    echo "=== Performance Anti-patterns ==="
    echo "• Inline function definitions in JSX:"
    grep -r "onClick={() =>" src/ --include="*.tsx" | wc -l | xargs echo "  Found inline functions:"
    echo ""
    echo "• Missing React.memo usage:"
    grep -r "export default function" src/ --include="*.tsx" | wc -l | xargs echo "  Components without memo consideration:"
    echo ""
    echo "• Large bundle imports:"
    grep -r "import \* as" src/ --include="*.ts" --include="*.tsx" | head -5 2>/dev/null || echo "  No wildcard imports found ✅"
} > reports/debt-analysis/performance-issues.txt

# 8. Test Coverage Analysis
echo -e "${BLUE}🧪 Test Coverage Analysis...${NC}"
if [ -f "package.json" ] && grep -q "jest" package.json; then
    echo "  • Running test coverage analysis..."
    npm run test -- --coverage --watchAll=false > reports/debt-analysis/test-coverage.txt 2>&1 || echo "Test coverage analysis failed" > reports/debt-analysis/test-coverage.txt
else
    echo "  • Jest not configured, checking for test files..."
    {
        echo "=== Test File Analysis ==="
        echo "• Test files found:"
        find src/ -name "*.test.ts" -o -name "*.test.tsx" -o -name "*.spec.ts" -o -name "*.spec.tsx" | wc -l | xargs echo "  "
        echo ""
        echo "• Components without tests:"
        find src/components/ -name "*.tsx" | wc -l | xargs echo "  Total components:"
        find src/components/ -name "*.test.tsx" | wc -l | xargs echo "  Tested components:"
    } > reports/debt-analysis/test-coverage.txt
fi

# 9. Dependency Analysis
echo -e "${BLUE}📚 Dependency Analysis...${NC}"
if [ -f "package.json" ]; then
    echo "  • Analyzing package.json dependencies..."
    {
        echo "=== Dependency Analysis ==="
        echo "• Total dependencies:"
        jq '.dependencies | length' package.json 2>/dev/null || echo "  Unable to parse package.json"
        echo ""
        echo "• Total devDependencies:"
        jq '.devDependencies | length' package.json 2>/dev/null || echo "  Unable to parse package.json"
        echo ""
        echo "• Expo SDK version:"
        jq '.dependencies.expo' package.json 2>/dev/null || echo "  Expo not found in dependencies"
    } > reports/debt-analysis/dependencies.txt
    
    if command_exists npm; then
        echo "  • Checking for outdated packages..."
        npm outdated > reports/debt-analysis/outdated-packages.txt 2>&1 || echo "No outdated packages or npm outdated failed" > reports/debt-analysis/outdated-packages.txt
        
        echo "  • Running security audit..."
        npm audit --audit-level=moderate > reports/debt-analysis/security-audit.txt 2>&1 || echo "Security audit completed with issues" >> reports/debt-analysis/security-audit.txt
    fi
fi

# 10. Generate Summary Report
echo -e "${BLUE}📋 Generating Summary Report...${NC}"
{
    echo "# WeRoomies Technical Debt Analysis Report"
    echo "Generated on: $(date)"
    echo ""
    
    echo "## 🎯 Executive Summary"
    echo ""
    
    # File statistics
    total_files=$(find src/ -name "*.ts" -o -name "*.tsx" | wc -l)
    total_lines=$(find src/ -name "*.ts" -o -name "*.tsx" | xargs wc -l | tail -1 | awk '{print $1}')
    echo "- **Total TypeScript files**: $total_files"
    echo "- **Total lines of code**: $total_lines"
    echo ""
    
    # Code quality metrics
    echo "## 🔍 Code Quality Metrics"
    echo ""
    
    any_types=$(cat reports/debt-analysis/any-types-count.txt)
    echo "- **'any' type usage**: $any_types instances"
    
    console_logs=$(grep -c "console\.log" reports/debt-analysis/console-logs.txt 2>/dev/null || echo "0")
    echo "- **Console.log statements**: $console_logs instances"
    
    large_files=$(grep -c "lines)" reports/debt-analysis/large-files.txt 2>/dev/null || echo "0")
    echo "- **Large files (>500 lines)**: $large_files files"
    
    echo ""
    echo "## 🎨 Design System Compliance"
    echo ""
    hardcoded_colors=$(grep -c "#[0-9A-Fa-f]" reports/debt-analysis/design-system-violations.txt 2>/dev/null || echo "0")
    echo "- **Hardcoded colors**: $hardcoded_colors instances"
    
    echo ""
    echo "## 🚨 Priority Actions Required"
    echo ""
    
    # Determine priority based on findings
    if [ "$any_types" -gt 10 ]; then
        echo "- 🔴 **HIGH**: Reduce 'any' type usage (current: $any_types)"
    fi
    
    if [ "$console_logs" -gt 5 ]; then
        echo "- 🟡 **MEDIUM**: Remove console.log statements (current: $console_logs)"
    fi
    
    if [ "$hardcoded_colors" -gt 0 ]; then
        echo "- 🟡 **MEDIUM**: Replace hardcoded colors with theme tokens (current: $hardcoded_colors)"
    fi
    
    if [ "$large_files" -gt 3 ]; then
        echo "- 🟢 **LOW**: Consider refactoring large files (current: $large_files)"
    fi
    
    echo ""
    echo "## 📊 Detailed Reports"
    echo ""
    echo "Individual analysis reports have been generated in \`reports/debt-analysis/\`:"
    echo ""
    for file in reports/debt-analysis/*.txt; do
        if [ -f "$file" ]; then
            basename_file=$(basename "$file" .txt)
            echo "- [\`$basename_file.txt\`](./debt-analysis/$basename_file.txt)"
        fi
    done
    
    echo ""
    echo "## 🔄 Next Steps"
    echo ""
    echo "1. **Review Priority Actions**: Address high-priority items first"
    echo "2. **Team Discussion**: Discuss findings in next team meeting"
    echo "3. **Create Issues**: Convert findings into actionable GitHub issues"
    echo "4. **Schedule Refactoring**: Plan refactoring sessions for technical debt"
    echo "5. **Automate Checks**: Integrate checks into CI/CD pipeline"
    
} > reports/debt-analysis/SUMMARY.md

# 11. Final Output
echo ""
echo -e "${GREEN}✅ Technical Debt Analysis Complete!${NC}"
echo ""
echo "📊 Reports generated in: reports/debt-analysis/"
echo "📋 Summary report: reports/debt-analysis/SUMMARY.md"
echo ""
echo -e "${BLUE}Key Findings:${NC}"

# Quick stats for immediate feedback
any_types=$(cat reports/debt-analysis/any-types-count.txt 2>/dev/null || echo "0")
console_logs=$(grep -c "console\.log" reports/debt-analysis/console-logs.txt 2>/dev/null || echo "0")
large_files=$(grep -c "lines)" reports/debt-analysis/large-files.txt 2>/dev/null || echo "0")

if [ "$any_types" -gt 10 ]; then
    echo -e "  ${RED}🔴 High 'any' type usage: $any_types instances${NC}"
elif [ "$any_types" -gt 5 ]; then
    echo -e "  ${YELLOW}🟡 Moderate 'any' type usage: $any_types instances${NC}"
else
    echo -e "  ${GREEN}✅ Low 'any' type usage: $any_types instances${NC}"
fi

if [ "$console_logs" -gt 5 ]; then
    echo -e "  ${YELLOW}🟡 Console.log statements found: $console_logs${NC}"
else
    echo -e "  ${GREEN}✅ Console.log usage acceptable: $console_logs${NC}"
fi

if [ "$large_files" -gt 3 ]; then
    echo -e "  ${YELLOW}🟡 Large files detected: $large_files files >500 lines${NC}"
else
    echo -e "  ${GREEN}✅ File sizes manageable: $large_files large files${NC}"
fi

echo ""
echo -e "${BLUE}💡 Tip: Run this script weekly to track technical debt trends${NC}"
echo "" 