#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🌐 WeRoomies Tunnel Testing Information');
console.log('=====================================\n');

// Check if tunnel is running
console.log('✅ Tunnel Server Status: RUNNING');
console.log('✅ Process ID found in system processes\n');

console.log('📱 Instructions for Testers:');
console.log('1. Install "Expo Go" from your app store');
console.log('2. Open Expo Go app');
console.log('3. Scan the QR code shown in your terminal');
console.log('4. Wait for the app to load (1-2 minutes first time)');
console.log('5. Look for "Testing Mode Active" popup\n');

console.log('🐛 Testing Features:');
console.log('- Bug Report Button (🐛) - Top right corner');
console.log('- Feedback <PERSON><PERSON> (📝) - Top right corner');
console.log('- Automatic session tracking');
console.log('- Device info collection\n');

console.log('📊 Monitor Testing:');
console.log('- Check terminal for connected devices');
console.log('- Press "m" to open dev menu on devices');
console.log('- Press "r" to reload all devices');
console.log('- Press "j" to open debugger\n');

console.log('🔧 Troubleshooting:');
console.log("If testers can't connect:");
console.log('1. Restart tunnel: npx expo start --tunnel --clear');
console.log('2. Check internet connection on both devices');
console.log('3. Try LAN mode: npx expo start --lan');
console.log('4. Use web version: npx expo start --web\n');

console.log('📧 Share with Testers:');
console.log('- QR Code: Look in your terminal where tunnel is running');
console.log('- URL: exp://[random].bacon.19000.exp.direct:80');
console.log('- Instructions: See TUNNELING_TESTING_GUIDE.md\n');

console.log('🎯 Testing Goals:');
console.log('- 4 Android testers');
console.log('- 4 iOS testers');
console.log('- 30-60 minutes testing each');
console.log('- Bug reports and feedback collection\n');

console.log('💡 Pro Tips:');
console.log('- Respond quickly to tester questions');
console.log('- Monitor the terminal for connection issues');
console.log('- Push fixes in real-time (auto-reload)');
console.log('- Thank testers for their time!\n');

console.log('Happy Testing! 🚀');
