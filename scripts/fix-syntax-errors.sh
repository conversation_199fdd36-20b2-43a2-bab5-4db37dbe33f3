#!/bin/bash

# WeRoomies Syntax Error Fix Script
# This script fixes critical syntax errors in the utils directory

echo "🔧 Starting comprehensive syntax error fixes..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Counter for fixes
fixes_applied=0

# Function to apply fix and count
apply_fix() {
    local file="$1"
    local description="$2"
    echo -e "${YELLOW}Fixing: ${description} in ${file}${NC}"
    fixes_applied=$((fixes_applied + 1))
}

# 1. Fix missing opening braces after function signatures
echo -e "${GREEN}1. Fixing missing opening braces after function signatures...${NC}"

# Fix async functions with missing opening braces
find src/utils -name "*.ts" -exec sed -i '' 's/): Promise<[^>]*>$/&{/g' {} \;

# More specific fixes for common patterns
find src/utils -name "*.ts" -exec sed -i '' 's/): Promise<[^>]*>\n    try {/): Promise<any> {\n    try {/g' {} \;
find src/utils -name "*.ts" -exec sed -i '' 's/): Promise<[^>]*>\n  try {/): Promise<any> {\n  try {/g' {} \;

apply_fix "src/utils/*.ts" "missing opening braces after async functions"

# 2. Fix missing commas in object literals and function parameters
echo -e "${GREEN}2. Fixing missing commas in object literals...${NC}"

# Fix semicolons that should be commas in object literals
find src/utils -name "*.ts" -exec sed -i '' 's/upsert: true;/upsert: true,/g' {} \;
find src/utils -name "*.ts" -exec sed -i '' 's/: true;/: true,/g' {} \;
find src/utils -name "*.ts" -exec sed -i '' 's/: false;/: false,/g' {} \;

apply_fix "src/utils/*.ts" "semicolons to commas in object literals"

# 3. Fix malformed method chaining
echo -e "${GREEN}3. Fixing malformed method chaining...${NC}"

# Fix broken method chains
find src/utils -name "*.ts" -exec sed -i '' 's/\.select();/\.select()/g' {} \;
find src/utils -name "*.ts" -exec sed -i '' 's/\.single();/\.single()/g' {} \;
find src/utils -name "*.ts" -exec sed -i '' 's/\.maybeSingle);/\.maybeSingle()/g' {} \;
find src/utils -name "*.ts" -exec sed -i '' 's/\.range);/\.range(/g' {} \;
find src/utils -name "*.ts" -exec sed -i '' 's/\.limit(.limit(.limit(/\.limit(/g' {} \;

apply_fix "src/utils/*.ts" "malformed method chaining"

# 4. Fix missing commas in function parameters
echo -e "${GREEN}4. Fixing missing commas in function parameters...${NC}"

# Fix function parameters missing commas
find src/utils -name "*.ts" -exec sed -i '' 's/message: string$/message: string,/g' {} \;
find src/utils -name "*.ts" -exec sed -i '' 's/code: string$/code: string,/g' {} \;
find src/utils -name "*.ts" -exec sed -i '' 's/category: [^,]*$/&,/g' {} \;

apply_fix "src/utils/*.ts" "missing commas in function parameters"

# 5. Fix interface definitions missing opening braces
echo -e "${GREEN}5. Fixing interface definitions...${NC}"

# Fix interfaces missing opening braces
find src/utils -name "*.ts" -exec sed -i '' 's/interface [A-Za-z0-9_<>]*$/&{/g' {} \;

apply_fix "src/utils/*.ts" "interface definitions missing braces"

# 6. Fix specific file issues
echo -e "${GREEN}6. Fixing specific file issues...${NC}"

# Fix specific issues in known problematic files
if [ -f "src/utils/initializeErrorHandling.ts" ]; then
    sed -i '' 's/trackPatchedMethodUsage = true;/trackPatchedMethodUsage = true,/g' src/utils/initializeErrorHandling.ts
    apply_fix "src/utils/initializeErrorHandling.ts" "trackPatchedMethodUsage syntax"
fi

if [ -f "src/utils/personaDetection.ts" ]; then
    sed -i '' "s/'new' | 'experienced' | 'limited_tech' | 'poor_connectivity' | 'accessibility' {/'new' | 'experienced' | 'limited_tech' | 'poor_connectivity' | 'accessibility'> {/g" src/utils/personaDetection.ts
    apply_fix "src/utils/personaDetection.ts" "return type syntax"
fi

if [ -f "src/utils/themeColors.ts" ]; then
    # Fix the theme colors file structure
    cat > src/utils/themeColors.ts << 'EOF'
import { useTheme } from '@design-system/ThemeProvider';

// Primary colors as strings - use theme hook in components
export const getPrimaryColors = () => {
  const theme = useTheme();
  
  return {
    50: theme.colors.primary[50] || '#f0f9ff',
    100: theme.colors.primary[100] || '#e0f2fe',
    200: theme.colors.primary[200] || '#bae6fd',
    300: theme.colors.primary[300] || '#7dd3fc',
    400: theme.colors.primary[400] || '#38bdf8',
    500: theme.colors.primary[500] || '#0ea5e9',
    600: theme.colors.primary[600] || '#0284c7',
    700: theme.colors.primary[700] || '#0369a1',
    800: theme.colors.primary[800] || '#075985',
    900: theme.colors.primary[900] || '#0c4a6e',
  };
};

// For static usage (use sparingly)
export const staticPrimaryColors = {
  50: '#f0f9ff',
  100: '#e0f2fe',
  200: '#bae6fd',
  300: '#7dd3fc',
  400: '#38bdf8',
  500: '#0ea5e9',
  600: '#0284c7',
  700: '#0369a1',
  800: '#075985',
  900: '#0c4a6e',
};
EOF
    apply_fix "src/utils/themeColors.ts" "complete file rewrite"
fi

# 7. Fix validation.ts file
if [ -f "src/utils/validation.ts" ]; then
    sed -i '' 's/isValid: false$/isValid: false,/g' src/utils/validation.ts
    sed -i '' 's/message: '\''Password must be at least 6 characters'\''$/message: '\''Password must be at least 6 characters'\'',/g' src/utils/validation.ts
    apply_fix "src/utils/validation.ts" "missing commas in return objects"
fi

# 8. Fix uploadToSupabase.ts
if [ -f "src/utils/uploadToSupabase.ts" ]; then
    sed -i '' 's/success: false$/success: false,/g' src/utils/uploadToSupabase.ts
    sed -i '' 's/error: '\''User not authenticated'\''$/error: '\''User not authenticated'\'',/g' src/utils/uploadToSupabase.ts
    apply_fix "src/utils/uploadToSupabase.ts" "missing commas in return objects"
fi

# 9. Fix verificationImageUpload.ts
if [ -f "src/utils/verificationImageUpload.ts" ]; then
    sed -i '' 's/state_id: '\''state_id'\''$/state_id: '\''state_id'\'',/g' src/utils/verificationImageUpload.ts
    sed -i '' 's/passport: '\''passport'\''$/passport: '\''passport'\'',/g' src/utils/verificationImageUpload.ts
    apply_fix "src/utils/verificationImageUpload.ts" "missing commas in object literal"
fi

# 10. Fix setupModerationTables.ts
if [ -f "src/utils/setupModerationTables.ts" ]; then
    sed -i '' 's/console.log('\''Error creating moderation_rules table(may already exist): '\'', rulesError); {/console.log('\''Error creating moderation_rules table(may already exist): '\'', rulesError);/g' src/utils/setupModerationTables.ts
    apply_fix "src/utils/setupModerationTables.ts" "malformed console.log statement"
fi

# 11. Fix setupContentModeration.ts
if [ -f "src/utils/setupContentModeration.ts" ]; then
    sed -i '' 's/sql: createProcedure;/sql: createProcedure,/g' src/utils/setupContentModeration.ts
    apply_fix "src/utils/setupContentModeration.ts" "missing comma in object literal"
fi

# 12. Fix sql-functions.ts
if [ -f "src/utils/sql-functions.ts" ]; then
    sed -i '' 's/\${createCheckMutualMatchFunction}$/\${createCheckMutualMatchFunction}/g' src/utils/sql-functions.ts
    apply_fix "src/utils/sql-functions.ts" "template literal syntax"
fi

# 13. Fix networkDiagnostics.ts
if [ -f "src/utils/networkDiagnostics.ts" ]; then
    sed -i '' 's/setTimeout(() => reject(new Error('\''Network connectivity timeout'\'')), 5000)$/setTimeout(() => reject(new Error('\''Network connectivity timeout'\'')), 5000);/g' src/utils/networkDiagnostics.ts
    apply_fix "src/utils/networkDiagnostics.ts" "setTimeout syntax"
fi

# 14. Fix s3Upload.ts
if [ -f "src/utils/s3Upload.ts" ]; then
    sed -i '' 's/const { data: urlData } = supabase.storage.from(bucket);$/const { data: urlData } = supabase.storage.from(bucket)/g' src/utils/s3Upload.ts
    sed -i '' 's/\.getPublicUrl(path);$/\.getPublicUrl(path);/g' src/utils/s3Upload.ts
    apply_fix "src/utils/s3Upload.ts" "method chaining syntax"
fi

# 15. Add missing package.json scripts
echo -e "${GREEN}7. Adding missing package.json scripts...${NC}"

# Create a temporary file with the debt-analysis script
cat >> package_scripts_addition.json << 'EOF'
{
  "debt-analysis": "bash scripts/debt-detection.sh",
  "fix-syntax": "bash scripts/fix-syntax-errors.sh",
  "setup-code-review": "bash scripts/setup-code-review.sh"
}
EOF

# Use node to merge the scripts
node -e "
const fs = require('fs');
const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const newScripts = JSON.parse(fs.readFileSync('package_scripts_addition.json', 'utf8'));
pkg.scripts = { ...pkg.scripts, ...newScripts };
fs.writeFileSync('package.json', JSON.stringify(pkg, null, 2));
"

# Clean up temporary file
rm package_scripts_addition.json

apply_fix "package.json" "missing scripts (debt-analysis, fix-syntax, setup-code-review)"

# 16. Fix TypeScript configuration issues
echo -e "${GREEN}8. Fixing TypeScript configuration issues...${NC}"

# Create a temporary tsconfig fix
if [ -f "tsconfig.json" ]; then
    # Remove problematic entries from include array
    sed -i '' '/"#"/d' tsconfig.json
    sed -i '' '/"TypeScript"/d' tsconfig.json  
    sed -i '' '/"validation"/d' tsconfig.json
    sed -i '' '/"Code"/d' tsconfig.json
    sed -i '' '/"formatting"/d' tsconfig.json
    apply_fix "tsconfig.json" "removed invalid include entries"
fi

echo -e "${GREEN}✅ Syntax error fixes completed!${NC}"
echo -e "${YELLOW}Total fixes applied: ${fixes_applied}${NC}"

# Run a quick validation
echo -e "${GREEN}Running quick validation...${NC}"

# Check if TypeScript compilation works now
echo "Checking TypeScript compilation..."
if npm run type-check > /dev/null 2>&1; then
    echo -e "${GREEN}✅ TypeScript compilation successful!${NC}"
else
    echo -e "${RED}⚠️  TypeScript compilation still has issues. Check output above.${NC}"
fi

# Check if linting works
echo "Checking ESLint..."
if npm run lint > /dev/null 2>&1; then
    echo -e "${GREEN}✅ ESLint check successful!${NC}"
else
    echo -e "${YELLOW}⚠️  ESLint still has warnings. Run 'npm run lint:fix' to auto-fix.${NC}"
fi

echo -e "${GREEN}🎉 Syntax error fix script completed!${NC}"
echo -e "${YELLOW}Next steps:${NC}"
echo "1. Run: npm run lint:fix"
echo "2. Run: npm run format" 
echo "3. Run: npm run type-check"
echo "4. Run: bash scripts/setup-code-review.sh" 