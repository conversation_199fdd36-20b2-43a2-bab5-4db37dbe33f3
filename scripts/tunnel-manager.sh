#!/bin/bash

# WeRoomies Tunnel Manager
# Manages the Expo tunnel for long-term testing

TUNNEL_LOG="tunnel.log"
TUNNEL_PID_FILE="tunnel.pid"

case "$1" in
  start)
    echo "🚀 Starting WeRoomies tunnel..."
    
    # Check if tunnel is already running
    if [ -f "$TUNNEL_PID_FILE" ]; then
      PID=$(cat "$TUNNEL_PID_FILE")
      if ps -p $PID > /dev/null 2>&1; then
        echo "⚠️  Tunnel is already running (PID: $PID)"
        echo "📱 URL: Check $TUNNEL_LOG for tunnel URL"
        exit 1
      else
        echo "🧹 Cleaning up stale PID file..."
        rm "$TUNNEL_PID_FILE"
      fi
    fi
    
    # Start tunnel in background
    echo "🔄 Starting tunnel in background..."
    nohup npx expo start --tunnel > "$TUNNEL_LOG" 2>&1 &
    TUNNEL_PID=$!
    echo $TUNNEL_PID > "$TUNNEL_PID_FILE"
    
    echo "✅ Tunnel started successfully!"
    echo "📋 Process ID: $TUNNEL_PID"
    echo "📄 Log file: $TUNNEL_LOG"
    echo "⏱️  Waiting 10 seconds for tunnel to initialize..."
    
    sleep 10
    
    # Extract tunnel URL from log
    echo "🔍 Checking tunnel status..."
    if grep -q "Metro waiting on exp://" "$TUNNEL_LOG"; then
      TUNNEL_URL=$(grep "Metro waiting on exp://" "$TUNNEL_LOG" | tail -1 | sed 's/.*Metro waiting on //')
      echo "🎉 Tunnel is ready!"
      echo "📱 Share this with testers: $TUNNEL_URL"
      echo ""
      echo "📧 QR Code: Check your terminal or take screenshot from log"
      echo "🔄 Monitor: tail -f $TUNNEL_LOG"
    else
      echo "⚠️  Tunnel may still be starting. Check log: tail -f $TUNNEL_LOG"
    fi
    ;;
    
  stop)
    echo "🛑 Stopping WeRoomies tunnel..."
    
    if [ -f "$TUNNEL_PID_FILE" ]; then
      PID=$(cat "$TUNNEL_PID_FILE")
      if ps -p $PID > /dev/null 2>&1; then
        kill $PID
        echo "✅ Tunnel stopped (PID: $PID)"
        rm "$TUNNEL_PID_FILE"
      else
        echo "⚠️  Tunnel process not found"
        rm "$TUNNEL_PID_FILE"
      fi
    else
      echo "⚠️  No tunnel PID file found"
    fi
    
    # Kill any remaining expo processes
    pkill -f "expo start --tunnel" 2>/dev/null || true
    echo "🧹 Cleaned up any remaining processes"
    ;;
    
  status)
    echo "📊 WeRoomies Tunnel Status"
    echo "=========================="
    
    if [ -f "$TUNNEL_PID_FILE" ]; then
      PID=$(cat "$TUNNEL_PID_FILE")
      if ps -p $PID > /dev/null 2>&1; then
        echo "✅ Status: RUNNING"
        echo "📋 Process ID: $PID"
        echo "⏱️  Started: $(ps -p $PID -o lstart= | xargs)"
        
        # Get tunnel URL from log
        if [ -f "$TUNNEL_LOG" ] && grep -q "Metro waiting on exp://" "$TUNNEL_LOG"; then
          TUNNEL_URL=$(grep "Metro waiting on exp://" "$TUNNEL_LOG" | tail -1 | sed 's/.*Metro waiting on //')
          echo "📱 Tunnel URL: $TUNNEL_URL"
        fi
        
        # Show recent connections
        echo ""
        echo "📱 Recent Activity:"
        if [ -f "$TUNNEL_LOG" ]; then
          tail -5 "$TUNNEL_LOG" | grep -E "(Connected|Disconnected|Error)" || echo "No recent activity"
        fi
      else
        echo "❌ Status: STOPPED (stale PID file)"
        rm "$TUNNEL_PID_FILE"
      fi
    else
      echo "❌ Status: STOPPED"
    fi
    
    echo ""
    echo "🔄 Monitor live: tail -f $TUNNEL_LOG"
    echo "📊 Full log: cat $TUNNEL_LOG"
    ;;
    
  restart)
    echo "🔄 Restarting WeRoomies tunnel..."
    $0 stop
    sleep 3
    $0 start
    ;;
    
  log)
    echo "📄 WeRoomies Tunnel Log (live):"
    echo "==============================="
    if [ -f "$TUNNEL_LOG" ]; then
      tail -f "$TUNNEL_LOG"
    else
      echo "❌ No log file found. Is the tunnel running?"
    fi
    ;;
    
  url)
    echo "📱 Getting tunnel URL..."
    if [ -f "$TUNNEL_LOG" ] && grep -q "Metro waiting on exp://" "$TUNNEL_LOG"; then
      TUNNEL_URL=$(grep "Metro waiting on exp://" "$TUNNEL_LOG" | tail -1 | sed 's/.*Metro waiting on //')
      echo "🔗 Tunnel URL: $TUNNEL_URL"
      echo ""
      echo "📧 Send this to your testers:"
      echo "1. Install 'Expo Go' from app store"
      echo "2. Open Expo Go and enter this URL: $TUNNEL_URL"
      echo "3. Or scan QR code from terminal"
    else
      echo "❌ Tunnel URL not found. Is the tunnel running?"
      echo "💡 Try: ./scripts/tunnel-manager.sh status"
    fi
    ;;
    
  *)
    echo "🌐 WeRoomies Tunnel Manager"
    echo "=========================="
    echo ""
    echo "Usage: $0 {start|stop|restart|status|log|url}"
    echo ""
    echo "Commands:"
    echo "  start   - Start tunnel in background"
    echo "  stop    - Stop tunnel"
    echo "  restart - Restart tunnel"
    echo "  status  - Show tunnel status and URL"
    echo "  log     - Show live tunnel log"
    echo "  url     - Get tunnel URL for testers"
    echo ""
    echo "Examples:"
    echo "  $0 start     # Start tunnel for testing"
    echo "  $0 status    # Check if tunnel is running"
    echo "  $0 url       # Get URL to send to testers"
    echo "  $0 log       # Monitor tunnel activity"
    echo ""
    exit 1
    ;;
esac 