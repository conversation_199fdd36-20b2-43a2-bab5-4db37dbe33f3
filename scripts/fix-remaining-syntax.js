#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔧 Fixing remaining syntax errors...\n');

// Files with remaining syntax errors
const filesToFix = [
  'src/app/(tabs)/profile/edit.tsx',
  'src/app/(tabs)/profile/media.tsx',
  'src/app/(tabs)/profile/notifications.tsx',
];

let totalFilesProcessed = 0;
let totalFixesApplied = 0;

function fixRemainingErrors(filePath) {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️  File not found: ${filePath}`);
    return;
  }

  console.log(`🔍 Processing: ${filePath}`);
  let content = fs.readFileSync(filePath, 'utf8');
  let fixesInFile = 0;
  const originalContent = content;

  // Fix 1: Fix the theme.mode = useColorScheme() pattern
  const themePatternRegex = /const theme\.mode = useColorScheme\(\);/g;
  const themeMatches = content.match(themePatternRegex);
  if (themeMatches) {
    content = content.replace(themePatternRegex, 'const colorScheme = useColorScheme();');
    fixesInFile += themeMatches.length;
    console.log(`  ✅ Fixed ${themeMatches.length} theme.mode declarations`);
  }

  // Fix 2: Fix any remaining malformed ternary operators
  const ternaryRegex = /\)\s*:\s*\(\s*\{/g;
  const ternaryMatches = content.match(ternaryRegex);
  if (ternaryMatches) {
    content = content.replace(ternaryRegex, ') : (');
    fixesInFile += ternaryMatches.length;
    console.log(`  ✅ Fixed ${ternaryMatches.length} malformed ternary operators`);
  }

  // Fix 3: Fix JSX prop spacing issues
  const extraSpacesRegex = /(\w+)=\{([^}]+)\}\s+(\w+)=/g;
  const spaceMatches = content.match(extraSpacesRegex);
  if (spaceMatches) {
    content = content.replace(extraSpacesRegex, '$1={$2} $3=');
    console.log(`  ✅ Fixed JSX prop spacing issues`);
  }

  // Fix 4: Handle broken JSX formatting
  content = content
    // Fix broken attribute formatting
    .replace(/(\w+)=\{([^}]+)\}\s+(\w+)=\{([^}]+)\}\s+(\w+)=/g, '$1={$2} $3={$4} $5=')
    // Fix multiple consecutive spaces in JSX
    .replace(/\s{3,}/g, ' ')
    // Fix broken onPress handlers
    .replace(/onPress=\{([^}]+)\}\s+(\w+)=/g, 'onPress={$1} $2=')
    // Fix style prop issues
    .replace(/style=\{([^}]+)\}\s+(\w+)=/g, 'style={$1} $2=');

  // Write the file if changes were made
  if (content !== originalContent) {
    fs.writeFileSync(filePath, content, 'utf8');
    totalFilesProcessed++;
    totalFixesApplied += fixesInFile;
    console.log(`  ✅ Applied ${fixesInFile} fixes to ${filePath}\n`);
  } else {
    console.log(`  ℹ️  No syntax errors found in ${filePath}\n`);
  }
}

// Process all files
console.log('🚀 Processing remaining files with syntax errors...\n');

filesToFix.forEach(filePath => {
  fixRemainingErrors(filePath);
});

console.log('\n📊 Summary:');
console.log(`  Files processed: ${totalFilesProcessed}`);
console.log(`  Total fixes applied: ${totalFixesApplied}`);

if (totalFixesApplied > 0) {
  console.log('\n✅ Remaining syntax error fixes completed!');
  console.log('🔄 The app should now compile without major syntax errors.');
} else {
  console.log('\n⚠️  No syntax errors were found to fix.');
}

console.log('\n🎯 Next steps:');
console.log('  1. Test the app: npx expo start');
console.log('  2. Check for any remaining TypeScript errors: npx tsc --noEmit');
