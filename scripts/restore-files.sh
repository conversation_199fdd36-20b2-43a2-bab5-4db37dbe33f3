#!/bin/bash

# Restore backed up files
echo "🔄 RESTORING: Bringing back advanced features"

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}Restoring advanced profile features...${NC}"

# Restore problematic files
if [ -f ".backup/problematic-files/smart-matching-dashboard.tsx" ]; then
    mv ".backup/problematic-files/smart-matching-dashboard.tsx" "src/app/(tabs)/profile/"
    echo -e "${GREEN}✅ Restored smart-matching-dashboard.tsx${NC}"
fi

if [ -f ".backup/problematic-files/unified-preferences.tsx" ]; then
    mv ".backup/problematic-files/unified-preferences.tsx" "src/app/(tabs)/profile/"
    echo -e "${GREEN}✅ Restored unified-preferences.tsx${NC}"
fi

if [ -f ".backup/problematic-files/unified-cultural.tsx" ]; then
    mv ".backup/problematic-files/unified-cultural.tsx" "src/app/(tabs)/profile/"
    echo -e "${GREEN}✅ Restored unified-cultural.tsx${NC}"
fi

echo -e "${YELLOW}⚠️  Note: You may need to fix syntax errors in restored files${NC}"
echo -e "${BLUE}🔧 Run 'npx expo start' to test with restored features${NC}" 