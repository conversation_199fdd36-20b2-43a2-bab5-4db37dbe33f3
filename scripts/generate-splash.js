const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

async function generateSplashScreen() {
  try {
    // Read the SVG file
    const svgPath = path.join(__dirname, '../assets/images/splash-organic.svg');
    const svgBuffer = fs.readFileSync(svgPath);

    // Generate PNG splash screen
    const outputPath = path.join(__dirname, '../assets/images/splash-organic.png');

    await sharp(svgBuffer)
      .resize(1080, 1920) // Full HD portrait resolution
      .png({
        quality: 95,
        compressionLevel: 6,
        progressive: true,
      })
      .toFile(outputPath);

    console.log('✅ Organic splash screen generated successfully!');
    console.log(`📁 Output: ${outputPath}`);

    // Generate different sizes for various screen densities
    const sizes = [
      { width: 540, height: 960, suffix: '@1x' },
      { width: 1080, height: 1920, suffix: '@2x' },
      { width: 1620, height: 2880, suffix: '@3x' },
    ];

    for (const size of sizes) {
      const sizePath = path.join(__dirname, `../assets/images/splash-organic${size.suffix}.png`);
      await sharp(svgBuffer)
        .resize(size.width, size.height)
        .png({
          quality: 95,
          compressionLevel: 6,
          progressive: true,
        })
        .toFile(sizePath);

      console.log(`✅ Generated ${size.suffix}: ${size.width}x${size.height}`);
    }

    // Also generate a square icon version for app icon
    const iconPath = path.join(__dirname, '../assets/images/icon-organic.png');

    // Create a square version focused on the official logo design
    const iconSvg = `
      <svg width="1024" height="1024" viewBox="0 0 1024 1024" fill="none" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#6366f1;stop-opacity:1" />
            <stop offset="50%" style="stop-color:#4f46e5;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#4338ca;stop-opacity:1" />
          </linearGradient>
          
          <radialGradient id="organicGradient1" cx="50%" cy="50%" r="50%">
            <stop offset="0%" style="stop-color:#a5b4fc;stop-opacity:0.3" />
            <stop offset="100%" style="stop-color:#4f46e5;stop-opacity:0.1" />
          </radialGradient>
        </defs>
        
        <!-- Background with rounded corners -->
        <rect width="1024" height="1024" fill="url(#primaryGradient)" rx="180"/>
        
        <!-- Subtle organic accent shapes -->
        <circle cx="200" cy="200" r="80" fill="url(#organicGradient1)" opacity="0.4"/>
        <circle cx="800" cy="300" r="60" fill="url(#organicGradient1)" opacity="0.3"/>
        <circle cx="300" cy="750" r="70" fill="url(#organicGradient1)" opacity="0.35"/>
        
        <!-- Central Logo Design -->
        <g transform="translate(512, 512)">
          <!-- Two people figures matching the official logo -->
          <g transform="scale(2.8)">
            <!-- Left person -->
            <g transform="translate(-35, 0)">
              <!-- Head -->
              <circle cx="0" cy="-25" r="18" fill="#ffffff" stroke="none"/>
              <!-- Body -->
              <path d="M-20,15 Q-20,-8 0,-8 Q20,-8 20,15 L20,45 Q20,52 12,52 L-12,52 Q-20,52 -20,45 Z" 
                    fill="#ffffff" stroke="none"/>
            </g>
            
            <!-- Right person -->
            <g transform="translate(35, 0)">
              <!-- Head -->
              <circle cx="0" cy="-25" r="18" fill="#ffffff" stroke="none"/>
              <!-- Body -->
              <path d="M-20,15 Q-20,-8 0,-8 Q20,-8 20,15 L20,45 Q20,52 12,52 L-12,52 Q-20,52 -20,45 Z" 
                    fill="#ffffff" stroke="none"/>
            </g>
            
            <!-- House roof/frame outline -->
            <path d="M-80,-45 L0,-85 L80,-45 L65,-45 L0,-70 L-65,-45 Z" 
                  fill="none" stroke="#ffffff" stroke-width="4" opacity="0.9"/>
            
            <!-- Connection/unity element between people -->
            <path d="M-15,20 Q0,8 15,20" 
                  fill="none" stroke="#ffffff" stroke-width="3" opacity="0.8"/>
          </g>
        </g>
        
        <!-- Subtle highlight on top -->
        <path d="M180,180 Q512,120 844,180 Q512,240 180,180" 
              fill="#ffffff" opacity="0.1"/>
      </svg>
    `;

    await sharp(Buffer.from(iconSvg))
      .resize(1024, 1024)
      .png({
        quality: 95,
        compressionLevel: 6,
      })
      .toFile(iconPath);

    console.log('✅ Organic app icon generated successfully!');

    // Generate standard icon sizes
    const iconSizes = [192, 512, 1024];
    for (const size of iconSizes) {
      const iconSizePath = path.join(__dirname, `../assets/images/icon-${size}.png`);
      await sharp(Buffer.from(iconSvg))
        .resize(size, size)
        .png({
          quality: 95,
          compressionLevel: 6,
        })
        .toFile(iconSizePath);

      console.log(`✅ Generated icon ${size}x${size}`);
    }
  } catch (error) {
    console.error('❌ Error generating splash screen:', error);
  }
}

// Run the script
generateSplashScreen();
