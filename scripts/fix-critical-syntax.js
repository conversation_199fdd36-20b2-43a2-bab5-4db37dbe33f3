#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 Critical Syntax Fixer - Fixing the most critical syntax issues...\n');

// Critical fixes for common patterns
function fixCriticalSyntax(content, filePath) {
  let fixed = content;
  const changes = [];

  // Fix 1: Fix trailing commas in object properties (remove extra commas)
  fixed = fixed.replace(/,(\s*);/g, (match, whitespace) => {
    changes.push(`Fixed trailing comma before semicolon in ${path.basename(filePath)}`);
    return ';';
  });

  // Fix 2: Fix incomplete method chains that are split incorrectly
  fixed = fixed.replace(/(\w+)\s*\n\s*\.(\w+)/g, (match, obj, method) => {
    changes.push(`Fixed method chain continuation in ${path.basename(filePath)}`);
    return `${obj}.${method}`;
  });

  // Fix 3: Fix function declarations that are incomplete
  fixed = fixed.replace(
    /export\s+async\s+function\s+(\w+)\s*\(\s*([^)]*)\s*\)\s*:\s*([^{]+)\s*$/gm,
    (match, name, params, returnType) => {
      changes.push(`Fixed incomplete function declaration ${name} in ${path.basename(filePath)}`);
      return `export async function ${name}(${params}): ${returnType} {`;
    }
  );

  // Fix 4: Fix const declarations with destructuring that are split across lines
  fixed = fixed.replace(
    /const\s*\{\s*([^}]+)\s*\}\s*\n\s*=\s*([^;]+);?/g,
    (match, destructured, assignment) => {
      changes.push(`Fixed destructuring assignment in ${path.basename(filePath)}`);
      return `const { ${destructured.trim()} } = ${assignment.trim()};`;
    }
  );

  // Fix 5: Fix try-catch blocks that are malformed
  fixed = fixed.replace(/\}\s*catch\s*\(\s*([^)]+)\s*\)\s*\{/g, (match, errorVar) => {
    changes.push(`Fixed catch block in ${path.basename(filePath)}`);
    return `} catch (${errorVar}) {`;
  });

  // Fix 6: Fix incomplete class method declarations
  fixed = fixed.replace(
    /(\s+)(static\s+)?(async\s+)?(\w+)\s*\(\s*([^)]*)\s*\)\s*:\s*([^{]+)\s*$/gm,
    (match, indent, staticKeyword, asyncKeyword, methodName, params, returnType) => {
      changes.push(`Fixed incomplete method ${methodName} in ${path.basename(filePath)}`);
      return `${indent}${staticKeyword || ''}${asyncKeyword || ''}${methodName}(${params}): ${returnType} {`;
    }
  );

  // Fix 7: Fix object property assignments with trailing commas
  fixed = fixed.replace(/(\w+):\s*([^,]+),(\s*);/g, (match, prop, value, whitespace) => {
    changes.push(`Fixed object property assignment in ${path.basename(filePath)}`);
    return `${prop}: ${value}${whitespace};`;
  });

  // Fix 8: Fix arrow functions that are incomplete
  fixed = fixed.replace(
    /const\s+(\w+)\s*=\s*\(\s*([^)]*)\s*\)\s*=>\s*$/gm,
    (match, funcName, params) => {
      changes.push(`Fixed incomplete arrow function ${funcName} in ${path.basename(filePath)}`);
      return `const ${funcName} = (${params}) => {`;
    }
  );

  return { content: fixed, changes };
}

// Fix specific problematic files
function fixSpecificFiles() {
  const problematicFiles = [
    'src/utils/supabaseStorageService.ts',
    'src/utils/supabaseUtils.ts',
    'src/utils/uploadToSupabase.ts',
    'src/utils/verificationImageUpload.ts',
    'src/utils/verificationStorageHelper.ts',
    'src/utils/testing/profileDataflowTest.ts',
    'src/utils/themeColors.ts',
  ];

  problematicFiles.forEach(file => {
    const filePath = path.join(process.cwd(), file);
    if (fs.existsSync(filePath)) {
      try {
        let content = fs.readFileSync(filePath, 'utf8');

        // Specific fixes for each file type
        if (file.includes('supabaseStorageService')) {
          // Fix the specific class method issues
          content = content.replace(
            /static async deleteFile\(bucket: string, path: string\): Promise<boolean> \{[\s\S]*?\n\s*\}/g,
            `static async deleteFile(bucket: string, path: string): Promise<boolean> {
    try {
      const { error } = await supabase.storage
        .from(bucket)
        .remove([path]);

      if (error) {
        logger.error('Failed to delete file', 'SupabaseStorageService', { error, path });
        return false;
      }

      logger.info(\`🗑️ File deleted: \${path}\`);
      return true;
    } catch (error) {
      logger.error('Error deleting file', 'SupabaseStorageService', { error, path });
      return false;
    }
  }`
          );
        }

        if (file.includes('supabaseUtils')) {
          // Fix specific method chain issues
          content = content.replace(
            /\.range\(offset, offset \+ limit - 1\);[\s\S]*?catch/g,
            `.range(offset, offset + limit - 1);

    if (error) {
      logger.error('Error fetching paginated data', 'supabaseUtils', { error });
      return { data: [], error };
    }

    return { data: data || [], error: null };
  } catch`
          );
        }

        if (file.includes('themeColors')) {
          // Fix the useTheme hook usage
          content = content.replace(
            /const theme = useTheme\(\);[\s\S]*?export/g,
            `const theme = useTheme();
  
  return {
    primary: theme.colors.primary,
    secondary: theme.colors.secondary,
    background: theme.colors.background,
    surface: theme.colors.surface,
    text: theme.colors.text,
    error: theme.colors.error,
    success: theme.colors.success,
    warning: theme.colors.warning,
  };
};

export`
          );
        }

        const { content: fixedContent, changes } = fixCriticalSyntax(content, filePath);

        if (changes.length > 0 || fixedContent !== content) {
          fs.writeFileSync(filePath, fixedContent);
          console.log(`✅ Fixed ${file}`);
          changes.forEach(change => console.log(`   - ${change}`));
        }
      } catch (error) {
        console.error(`❌ Error fixing ${file}:`, error.message);
      }
    }
  });
}

// Process all files with critical fixes
function processAllFiles() {
  const srcDir = path.join(process.cwd(), 'src');

  function getAllTsFiles(dir, fileList = []) {
    const files = fs.readdirSync(dir);

    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);

      if (stat.isDirectory() && !file.startsWith('.')) {
        getAllTsFiles(filePath, fileList);
      } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
        fileList.push(filePath);
      }
    });

    return fileList;
  }

  const files = getAllTsFiles(srcDir);
  let totalChanges = 0;

  files.forEach(filePath => {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const { content: fixedContent, changes } = fixCriticalSyntax(content, filePath);

      if (changes.length > 0) {
        fs.writeFileSync(filePath, fixedContent);
        totalChanges += changes.length;
      }
    } catch (error) {
      // Skip files that can't be processed
    }
  });

  console.log(`\n📊 Processed ${files.length} files with ${totalChanges} total fixes`);
}

// Main execution
try {
  console.log('Starting critical syntax fixes...\n');

  // Fix specific problematic files first
  console.log('Fixing specific problematic files...');
  fixSpecificFiles();

  // Apply general fixes to all files
  console.log('\nApplying general fixes...');
  processAllFiles();

  console.log('\n✅ Critical syntax fixes completed!');
  console.log('🔍 Running TypeScript check...\n');

  // Quick TypeScript check
  try {
    const output = execSync('npx tsc --noEmit --skipLibCheck 2>&1 | head -10', {
      encoding: 'utf8',
    });
    if (output.trim()) {
      console.log('Remaining issues:');
      console.log(output);
    } else {
      console.log('🎉 No TypeScript errors found!');
    }
  } catch (error) {
    console.log('TypeScript check completed with some remaining issues.');
  }
} catch (error) {
  console.error('❌ Script execution failed:', error.message);
  process.exit(1);
}
