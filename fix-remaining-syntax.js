#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Function to fix specific syntax patterns
function fixAdvancedSyntaxErrors(content) {
  let fixed = content;

  // Fix class property declarations with commas instead of semicolons
  fixed = fixed.replace(/(\s+[a-zA-Z_][a-zA-Z0-9_]*\s*:\s*[^,;]+),(\s*$)/gm, '$1;$2');
  
  // Fix incomplete function parameters with leading commas
  fixed = fixed.replace(/\(\s*,\s*([^)]+)\)/g, '($1)');
  
  // Fix trailing semicolons in comments
  fixed = fixed.replace(/\*\/\s*;/g, '*/');
  
  // Fix incomplete logger calls
  fixed = fixed.replace(/logger\.(error|warn|info|debug)\(\s*([^,)]+),\s*([^,)]+)\s*;\s*\)/g, 'logger.$1($2, $3)');
  
  // Fix incomplete variable declarations
  fixed = fixed.replace(/let\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*:\s*([^,;=]+),(\s*$)/gm, 'let $1: $2;$3');
  
  // Fix incomplete const declarations
  fixed = fixed.replace(/const\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*:\s*([^,;=]+),(\s*$)/gm, 'const $1: $2;$3');
  
  // Fix incomplete array/object destructuring
  fixed = fixed.replace(/const\s*\{\s*([^}]*)\s*\}\s*,(\s*$)/gm, 'const { $1 };$2');
  fixed = fixed.replace(/const\s*\[\s*([^\]]*)\s*\]\s*,(\s*$)/gm, 'const [$1];$2');
  
  // Fix incomplete method calls with missing closing parentheses
  fixed = fixed.replace(/\.([a-zA-Z_][a-zA-Z0-9_]*)\(\s*([^)]*)\s*;(\s*$)/gm, '.$1($2);$3');
  
  // Fix incomplete object property assignments
  fixed = fixed.replace(/(\s+[a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*([^,;]+),(\s*$)/gm, '$1 = $2;$3');
  
  // Fix incomplete type annotations
  fixed = fixed.replace(/:\s*([A-Za-z_][A-Za-z0-9_<>[\]|&]*)\s*,(\s*$)/gm, ': $1;$2');
  
  // Fix incomplete generic type parameters
  fixed = fixed.replace(/<([^>]*),(\s*>)/g, '<$1$2');
  
  // Fix incomplete template literals
  fixed = fixed.replace(/`([^`]*)\$\{([^}]*)\}\s*;/g, '`$1\${$2}`');
  
  // Fix incomplete conditional expressions
  fixed = fixed.replace(/\?\s*([^:]+)\s*:\s*([^;]+)\s*;/g, '? $1 : $2');
  
  // Fix incomplete arrow functions
  fixed = fixed.replace(/=>\s*\{\s*;/g, '=> {');
  
  // Fix incomplete switch statements
  fixed = fixed.replace(/case\s+([^:]+)\s*;/g, 'case $1:');
  
  // Fix incomplete try-catch blocks
  fixed = fixed.replace(/\}\s*catch\s*\(\s*([^)]*)\s*\)\s*\{\s*;/g, '} catch ($1) {');
  
  // Fix incomplete for loops
  fixed = fixed.replace(/for\s*\(\s*([^)]*)\s*\)\s*\{\s*;/g, 'for ($1) {');
  
  // Fix incomplete while loops
  fixed = fixed.replace(/while\s*\(\s*([^)]*)\s*\)\s*\{\s*;/g, 'while ($1) {');
  
  // Fix incomplete if statements
  fixed = fixed.replace(/if\s*\(\s*([^)]*)\s*\)\s*\{\s*;/g, 'if ($1) {');
  
  return fixed;
}

// Function to recursively find and fix files
function fixFilesInDirectory(dir) {
  const files = fs.readdirSync(dir);
  
  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      // Skip node_modules and .git directories
      if (file !== 'node_modules' && file !== '.git' && file !== '.expo') {
        fixFilesInDirectory(filePath);
      }
    } else if (file.endsWith('.ts') || file.endsWith('.tsx')) {
      try {
        const content = fs.readFileSync(filePath, 'utf8');
        const fixed = fixAdvancedSyntaxErrors(content);
        
        if (fixed !== content) {
          fs.writeFileSync(filePath, fixed, 'utf8');
          console.log(`✅ Fixed advanced syntax: ${filePath}`);
        }
      } catch (error) {
        console.error(`❌ Error fixing ${filePath}:`, error.message);
      }
    }
  }
}

// Start fixing from src directory
const srcDir = path.join(process.cwd(), 'src');
if (fs.existsSync(srcDir)) {
  console.log('🔧 Starting advanced syntax error fixes...');
  fixFilesInDirectory(srcDir);
  console.log('✅ Advanced syntax error fixes completed!');
} else {
  console.error('❌ src directory not found');
}
