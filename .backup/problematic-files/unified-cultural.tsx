/**
 * Unified Cultural Management - PHASE 2 CONSOLIDATION
 * 
 * This component consolidates 4 cultural/accessibility routes into a single interface:
 * - cultural-settings.tsx (Cultural Adaptation)
 * - language-settings.tsx (Language & Localization)
 * - regional-market-settings.tsx (Regional Market Settings)
 * - voice-motor-settings.tsx (Voice & Motor Accessibility)
 * 
 * Result: 4 routes → 1 route (75% reduction)
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { View, Text, StyleSheet, ScrollView, ActivityIndicator, TouchableOpacity, useColorScheme, Alert, RefreshControl, Switch, TextInput, Modal, Dimensions, } from 'react-native';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from '@context/AuthContext';
import { Card } from '@components/ui';
import { Button } from '@design-system';
import { useToast } from '@components/ui/Toast';
import Slider from '@react-native-community/slider';
import { Globe, Languages, MapPin, Mic, ChevronLeft, Users, MessageCircle, DollarSign, Accessibility, Volume2, Hand, Eye, Settings, Flag, Calendar, Star, CheckCircle, Monitor, Headphones, Keyboard, Navigation, Brain, Target, BarChart3, Save, RefreshCw } from 'lucide-react-native';

import { supabase } from '@utils/supabaseUtils';
import { logger } from '@utils/logger';
import { useTheme } from '@design-system';
import { colorWithOpacity } from '@design-system';

const { width } = Dimensions.get('window');

// React Native compatible color system using centralized utility

// Cultural tab interface
interface CulturalTab {
  id: string;
  title: string;
  icon: React.ComponentType<any>;
  description: string;
  component: React.ComponentType<any>;
}

// Data interfaces
interface CulturalSettings {
  primary_culture: string;
  secondary_cultures: string[];
  communication_style: 'direct' | 'indirect' | 'balanced';
  hierarchy_preference: 'formal' | 'informal' | 'contextual';
  time_orientation: 'punctual' | 'flexible' | 'event_based';
  social_distance: 'close' | 'moderate' | 'formal';
  conflict_resolution: 'direct' | 'mediated' | 'avoidance';
  personal_space: 'close' | 'moderate' | 'distant';
}

interface LanguageSettings {
  primary_language: string;
  secondary_languages: string[];
  regional_variant: string;
  date_format: string;
  time_format: '12h' | '24h';
  number_format: string;
  currency_display: 'symbol' | 'code' | 'name';
  rtl_support: boolean;
  font_scaling: number;
}

interface RegionalSettings {
  current_region: string;
  payment_methods: string[];
  currency_preference: string;
  legal_compliance: boolean;
  market_adaptations: string[];
  business_hours_format: string;
  holiday_calendar: string;
  tax_display: boolean;
}

interface AccessibilitySettings {
  voice_commands_enabled: boolean;
  voice_sensitivity: number;
  motor_assistance: boolean;
  gesture_alternatives: boolean;
  switch_control: boolean;
  voice_feedback: boolean;
  command_timeout: number;
  error_handling: 'repeat' | 'skip' | 'ask';
}

// Default values
const defaultCulturalSettings: CulturalSettings = {
  primary_culture: 'Western',
  secondary_cultures: [],
  communication_style: 'balanced',
  hierarchy_preference: 'contextual',
  time_orientation: 'punctual',
  social_distance: 'moderate',
  conflict_resolution: 'mediated',
  personal_space: 'moderate',
};

const defaultLanguageSettings: LanguageSettings = {
  primary_language: 'en',
  secondary_languages: [],
  regional_variant: 'US',
  date_format: 'MM/DD/YYYY',
  time_format: '12h',
  number_format: '1,234.56',
  currency_display: 'symbol',
  rtl_support: false,
  font_scaling: 1.0,
};

const defaultRegionalSettings: RegionalSettings = {
  current_region: 'US',
  payment_methods: ['card', 'digital'],
  currency_preference: 'USD',
  legal_compliance: true,
  market_adaptations: [],
  business_hours_format: '9-5',
  holiday_calendar: 'US',
  tax_display: true,
};

const defaultAccessibilitySettings: AccessibilitySettings = {
  voice_commands_enabled: false,
  voice_sensitivity: 0.7,
  motor_assistance: false,
  gesture_alternatives: false,
  switch_control: false,
  voice_feedback: false,
  command_timeout: 3000,
  error_handling: 'repeat',
};

// Cultural Tab Component
const CulturalTab = ({ settings, updateSettings }: any) => {
  const theme = useTheme();
  
  return (
    <ScrollView style={styles.tabContent}>
      <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.cardTitle, { color: theme.colors.text }]}>Cultural Identity</Text>
        
        <View style={styles.settingSection}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Primary Culture</Text>
          <View style={styles.optionGroup}>
            {[
              'Western', 'East Asian', 'Southeast Asian', 'South Asian', 
              'Middle Eastern', 'African', 'Latin American', 'European'
            ].map((culture) => (
              <TouchableOpacity key={culture} style={[
                  styles.optionButton,
                  { 
                    backgroundColor: settings.primary_culture === culture ? theme.colors.primary : theme.colors.surface,
                    borderColor: theme.colors.border
                  }
                ]}
                onPress={() => updateSettings('cultural', 'primary_culture', culture)}
              >
                <Text style={[
                  styles.optionText,
                  { color: settings.primary_culture === culture ? theme.colors.white : theme.colors.text }
                ]}>
                  {culture}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.settingSection}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Communication Style</Text>
          <View style={styles.optionGroup}>
            {[
              { value: 'direct', label: 'Direct Communication' },
              { value: 'indirect', label: 'Indirect Communication' },
              { value: 'balanced', label: 'Balanced Approach' },
            ].map((option) => (
              <TouchableOpacity key={option.value} style={[
                  styles.optionButton,
                  { 
                    backgroundColor: settings.communication_style === option.value ? theme.colors.primary : theme.colors.surface,
                    borderColor: theme.colors.border
                  }
                ]}
                onPress={() => updateSettings('cultural', 'communication_style', option.value)}
              >
                <Text style={[
                  styles.optionText,
                  { color: settings.communication_style === option.value ? theme.colors.white : theme.colors.text }
                ]}>
                  {option.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.settingSection}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Social Preferences</Text>
          
          <View style={styles.sliderSection}>
            <Text style={[styles.sliderLabel, { color: theme.colors.text }]}>Social Distance</Text>
            <Text style={[styles.sliderValue, { color: theme.colors.textSecondary }]}>
              {settings.social_distance}
            </Text>
          </View>

          <View style={styles.sliderSection}>
            <Text style={[styles.sliderLabel, { color: theme.colors.text }]}>Personal Space</Text>
            <Text style={[styles.sliderValue, { color: theme.colors.textSecondary }]}>
              {settings.personal_space}
            </Text>
          </View>

          <View style={styles.sliderSection}>
            <Text style={[styles.sliderLabel, { color: theme.colors.text }]}>Time Orientation</Text>
            <Text style={[styles.sliderValue, { color: theme.colors.textSecondary }]}>
              {settings.time_orientation}
            </Text>
          </View>
        </View>
      </Card>

      <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.cardTitle, { color: theme.colors.text }]}>Cultural Analytics</Text>
        
        <View style={styles.analyticsContainer}>
          <View style={styles.analyticsItem}>
            <Users size={24} color={theme.colors.primary} />
            <View style={styles.analyticsDetails}>
              <Text style={[styles.analyticsTitle, { color: theme.colors.text }]}>Adaptation Score</Text>
              <Text style={[styles.analyticsValue, { color: theme.colors.success }]}>87%</Text>
            </View>
          </View>
          
          <View style={styles.analyticsItem}>
            <MessageCircle size={24} color={theme.colors.primary} />
            <View style={styles.analyticsDetails}>
              <Text style={[styles.analyticsTitle, { color: theme.colors.text }]}>Communication</Text>
              <Text style={[styles.analyticsValue, { color: theme.colors.success }]}>92%</Text>
            </View>
          </View>
        </View>
      </Card>
    </ScrollView>
  );
};

// Language Tab Component
const LanguageTab = ({ settings, updateSettings }: any) => {
  const theme = useTheme();
  
  return (
    <ScrollView style={styles.tabContent}>
      <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.cardTitle, { color: theme.colors.text }]}>Language Settings</Text>
        
        <View style={styles.settingSection}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Primary Language</Text>
          <TouchableOpacity style={[styles.languageSelector, { borderColor: theme.colors.border }]}>
            <Globe size={20} color={theme.colors.primary} />
            <Text style={[styles.languageSelectorText, { color: theme.colors.text }]}>
              {settings.primary_language === 'en' ? 'English (US)' : settings.primary_language}
            </Text>
            <Text style={[styles.languageSelectorSubtext, { color: theme.colors.textSecondary }]}>
              Default app language
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.settingSection}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Format Preferences</Text>
          
          <View style={styles.formatItem}>
            <Text style={[styles.formatLabel, { color: theme.colors.text }]}>Time Format</Text>
            <View style={styles.formatOptions}>
              {['12h', '24h'].map((format) => (
                <TouchableOpacity key={format} style={[
                    styles.formatOption,
                    { 
                      backgroundColor: settings.time_format === format ? theme.colors.primary : theme.colors.surface,
                      borderColor: theme.colors.border
                    }
                  ]}
                  onPress={() => updateSettings('language', 'time_format', format)}
                >
                  <Text style={[
                    styles.formatOptionText,
                    { color: settings.time_format === format ? theme.colors.white : theme.colors.text }
                  ]}>
                    {format}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <View style={styles.formatItem}>
            <Text style={[styles.formatLabel, { color: theme.colors.text }]}>Currency Display</Text>
            <View style={styles.formatOptions}>
              {[
                { value: 'symbol', label: '$' },
                { value: 'code', label: 'USD' },
                { value: 'name', label: 'Dollar' }
              ].map((option) => (
                <TouchableOpacity key={option.value} style={[
                    styles.formatOption,
                    { 
                      backgroundColor: settings.currency_display === option.value ? theme.colors.primary : theme.colors.surface,
                      borderColor: theme.colors.border
                    }
                  ]}
                  onPress={() => updateSettings('language', 'currency_display', option.value)}
                >
                  <Text style={[
                    styles.formatOptionText,
                    { color: settings.currency_display === option.value ? theme.colors.white : theme.colors.text }
                  ]}>
                    {option.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>

        <View style={styles.settingSection}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Text & Display</Text>
          
          <View style={styles.sliderSection}>
            <Text style={[styles.sliderLabel, { color: theme.colors.text }]}>Font Scaling</Text>
            <Slider style={styles.slider} minimumValue={0.8} maximumValue={1.5} step={0.1} value={settings.font_scaling} onValueChange={(value) => updateSettings('language', 'font_scaling', value)} minimumTrackTintColor={theme.colors.primary} maximumTrackTintColor={theme.colors.border}
            />
            <Text style={[styles.sliderValue, { color: theme.colors.textSecondary }]}>
              {Math.round(settings.font_scaling * 100)}%
            </Text>
          </View>

          <View style={styles.switchItem}>
            <Text style={[styles.switchLabel, { color: theme.colors.text }]}>RTL Text Support</Text>
            <Switch value={settings.rtl_support} onValueChange={(value) => updateSettings('language', 'rtl_support', value)} thumbColor={theme.colors.primary} trackColor={{ false: theme.colors.border, true: colorWithOpacity(theme.colors.primary, 0.5) }}
            />
          </View>
        </View>
      </Card>

      <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.cardTitle, { color: theme.colors.text }]}>Format Preview</Text>
        
        <View style={styles.previewContainer}>
          <View style={styles.previewItem}>
            <Text style={[styles.previewLabel, { color: theme.colors.textSecondary }]}>Date:</Text>
            <Text style={[styles.previewValue, { color: theme.colors.text }]}>
              {new Date().toLocaleDateString()}
            </Text>
          </View>
          
          <View style={styles.previewItem}>
            <Text style={[styles.previewLabel, { color: theme.colors.textSecondary }]}>Time:</Text>
            <Text style={[styles.previewValue, { color: theme.colors.text }]}>
              {new Date().toLocaleTimeString([], { hour12: settings.time_format === '12h' })}
            </Text>
          </View>
          
          <View style={styles.previewItem}>
            <Text style={[styles.previewLabel, { color: theme.colors.textSecondary }]}>Currency:</Text>
            <Text style={[styles.previewValue, { color: theme.colors.text }]}>
              {settings.currency_display === 'symbol' ? '$29.99' : 
               settings.currency_display === 'code' ? 'USD 29.99' : '29.99 Dollars'}
            </Text>
          </View>
        </View>
      </Card>
    </ScrollView>
  );
};

// Regional Tab Component
const RegionalTab = ({ settings, updateSettings }: any) => {
  const theme = useTheme();
  
  return (
    <ScrollView style={styles.tabContent}>
      <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.cardTitle, { color: theme.colors.text }]}>Regional Configuration</Text>
        
        <View style={styles.settingSection}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Current Region</Text>
          <TouchableOpacity style={[styles.regionSelector, { borderColor: theme.colors.border }]}>
            <Flag size={20} color={theme.colors.primary} />
            <View style={styles.regionInfo}>
              <Text style={[styles.regionName, { color: theme.colors.text }]}>
                {settings.current_region === 'US' ? 'United States' : settings.current_region}
              </Text>
              <Text style={[styles.regionCode, { color: theme.colors.textSecondary }]}>
                {settings.current_region}
              </Text>
            </View>
          </TouchableOpacity>
        </View>

        <View style={styles.settingSection}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Payment Methods</Text>
          <View style={styles.paymentMethodsGrid}>
            {[
              { id: 'card', label: 'Credit/Debit Cards', icon: DollarSign },
              { id: 'digital', label: 'Digital Wallets', icon: Monitor },
              { id: 'bank', label: 'Bank Transfer', icon: Flag },
              { id: 'cash', label: 'Cash on Delivery', icon: DollarSign },
            ].map((method) => {
              const IconComponent = method.icon;
              const isSelected = settings.payment_methods.includes(method.id);
              
              return (
                <TouchableOpacity 
                  key={method.id} 
                  style={[
                    styles.paymentMethodCard,
                    { 
                      backgroundColor: isSelected ? theme.colors.primary : theme.colors.surface,
                      borderColor: isSelected ? theme.colors.primary : theme.colors.border
                    }
                  ]}
                  onPress={() => {
                    const newMethods = isSelected 
                      ? settings.payment_methods.filter((m: string) => m !== method.id)
                      : [...settings.payment_methods, method.id];
                    updateSettings('regional', 'payment_methods', newMethods);
                  }}
                >
                  <IconComponent 
                    size={20} 
                    color={isSelected ? theme.colors.white : theme.colors.primary}
                  />
                  <Text style={[
                    styles.paymentMethodText,
                    { color: isSelected ? theme.colors.white : theme.colors.text }
                  ]}>
                    {method.label}
                  </Text>
                </TouchableOpacity>
              );
            })}
          </View>
        </View>

        <View style={styles.settingSection}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Compliance & Legal</Text>
          
          <View style={styles.switchItem}>
            <View style={styles.switchItemInfo}>
              <Text style={[styles.switchLabel, { color: theme.colors.text }]}>Legal Compliance</Text>
              <Text style={[styles.switchDescription, { color: theme.colors.textSecondary }]}>
                Enable regional legal requirements
              </Text>
            </View>
            <Switch value={settings.legal_compliance} onValueChange={(value) => updateSettings('regional', 'legal_compliance', value)} thumbColor={theme.colors.primary} trackColor={{ false: theme.colors.border, true: colorWithOpacity(theme.colors.primary, 0.5) }}
            />
          </View>

          <View style={styles.switchItem}>
            <View style={styles.switchItemInfo}>
              <Text style={[styles.switchLabel, { color: theme.colors.text }]}>Tax Display</Text>
              <Text style={[styles.switchDescription, { color: theme.colors.textSecondary }]}>
                Show taxes separately in pricing
              </Text>
            </View>
            <Switch value={settings.tax_display} onValueChange={(value) => updateSettings('regional', 'tax_display', value)} thumbColor={theme.colors.primary} trackColor={{ false: theme.colors.border, true: colorWithOpacity(theme.colors.primary, 0.5) }}
            />
          </View>
        </View>
      </Card>

      <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.cardTitle, { color: theme.colors.text }]}>Market Analytics</Text>
        
        <View style={styles.analyticsContainer}>
          <View style={styles.analyticsItem}>
            <BarChart3 size={24} color={theme.colors.primary} />
            <View style={styles.analyticsDetails}>
              <Text style={[styles.analyticsTitle, { color: theme.colors.text }]}>Market Penetration</Text>
              <Text style={[styles.analyticsValue, { color: theme.colors.success }]}>78%</Text>
            </View>
          </View>
          
          <View style={styles.analyticsItem}>
            <Target size={24} color={theme.colors.primary} />
            <View style={styles.analyticsDetails}>
              <Text style={[styles.analyticsTitle, { color: theme.colors.text }]}>Compliance Score</Text>
              <Text style={[styles.analyticsValue, { color: theme.colors.success }]}>95%</Text>
            </View>
          </View>
        </View>
      </Card>
    </ScrollView>
  );
};

// Accessibility Tab Component
const AccessibilityTab = ({ settings, updateSettings }: any) => {
  const theme = useTheme();
  
  return (
    <ScrollView style={styles.tabContent}>
      <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.cardTitle, { color: theme.colors.text }]}>Voice Commands</Text>
        
        <View style={styles.switchItem}>
          <View style={styles.switchItemInfo}>
            <Text style={[styles.switchLabel, { color: theme.colors.text }]}>Enable Voice Commands</Text>
            <Text style={[styles.switchDescription, { color: theme.colors.textSecondary }]}>
              Control the app using voice commands
            </Text>
          </View>
          <Switch value={settings.voice_commands_enabled} onValueChange={(value) => updateSettings('accessibility', 'voice_commands_enabled', value)} thumbColor={theme.colors.primary} trackColor={{ false: theme.colors.border, true: colorWithOpacity(theme.colors.primary, 0.5) }}
          />
        </View>

        {settings.voice_commands_enabled && (
          <>
            <View style={styles.sliderSection}>
              <Text style={[styles.sliderLabel, { color: theme.colors.text }]}>Voice Sensitivity</Text>
              <Slider style={styles.slider} minimumValue={0.1} maximumValue={1.0} step={0.1} value={settings.voice_sensitivity} onValueChange={(value) => updateSettings('accessibility', 'voice_sensitivity', value)} minimumTrackTintColor={theme.colors.primary} maximumTrackTintColor={theme.colors.border}
              />
              <Text style={[styles.sliderValue, { color: theme.colors.textSecondary }]}>
                {Math.round(settings.voice_sensitivity * 100)}%
              </Text>
            </View>

            <View style={styles.switchItem}>
              <Text style={[styles.switchLabel, { color: theme.colors.text }]}>Voice Feedback</Text>
              <Switch value={settings.voice_feedback} onValueChange={(value) => updateSettings('accessibility', 'voice_feedback', value)} thumbColor={theme.colors.primary} trackColor={{ false: theme.colors.border, true: colorWithOpacity(theme.colors.primary, 0.5) }}
              />
            </View>
          </>
        )}
      </Card>

      <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.cardTitle, { color: theme.colors.text }]}>Motor Accessibility</Text>
        
        <View style={styles.switchItem}>
          <View style={styles.switchItemInfo}>
            <Text style={[styles.switchLabel, { color: theme.colors.text }]}>Motor Assistance</Text>
            <Text style={[styles.switchDescription, { color: theme.colors.textSecondary }]}>
              Enhanced touch targets and gesture alternatives
            </Text>
          </View>
          <Switch value={settings.motor_assistance} onValueChange={(value) => updateSettings('accessibility', 'motor_assistance', value)} thumbColor={theme.colors.primary} trackColor={{ false: theme.colors.border, true: colorWithOpacity(theme.colors.primary, 0.5) }}
          />
        </View>

        <View style={styles.switchItem}>
          <View style={styles.switchItemInfo}>
            <Text style={[styles.switchLabel, { color: theme.colors.text }]}>Gesture Alternatives</Text>
            <Text style={[styles.switchDescription, { color: theme.colors.textSecondary }]}>
              Alternative methods for complex gestures
            </Text>
          </View>
          <Switch value={settings.gesture_alternatives} onValueChange={(value) => updateSettings('accessibility', 'gesture_alternatives', value)} thumbColor={theme.colors.primary} trackColor={{ false: theme.colors.border, true: colorWithOpacity(theme.colors.primary, 0.5) }}
          />
        </View>

        <View style={styles.switchItem}>
          <View style={styles.switchItemInfo}>
            <Text style={[styles.switchLabel, { color: theme.colors.text }]}>Switch Control</Text>
            <Text style={[styles.switchDescription, { color: theme.colors.textSecondary }]}>
              Navigate using external switch devices
            </Text>
          </View>
          <Switch value={settings.switch_control} onValueChange={(value) => updateSettings('accessibility', 'switch_control', value)} thumbColor={theme.colors.primary} trackColor={{ false: theme.colors.border, true: colorWithOpacity(theme.colors.primary, 0.5) }}
          />
        </View>
      </Card>

      <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.cardTitle, { color: theme.colors.text }]}>Accessibility Analytics</Text>
        
        <View style={styles.analyticsContainer}>
          <View style={styles.analyticsItem}>
            <Mic size={24} color={theme.colors.primary} />
            <View style={styles.analyticsDetails}>
              <Text style={[styles.analyticsTitle, { color: theme.colors.text }]}>Voice Usage</Text>
              <Text style={[styles.analyticsValue, { color: theme.colors.success }]}>
                {settings.voice_commands_enabled ? '45%' : '0%'}
              </Text>
            </View>
          </View>
          
          <View style={styles.analyticsItem}>
            <Hand size={24} color={theme.colors.primary} />
            <View style={styles.analyticsDetails}>
              <Text style={[styles.analyticsTitle, { color: theme.colors.text }]}>Motor Efficiency</Text>
              <Text style={[styles.analyticsValue, { color: theme.colors.success }]}>
                {settings.motor_assistance ? '82%' : '65%'}
              </Text>
            </View>
          </View>
        </View>
      </Card>
    </ScrollView>
  );
};

export default function UnifiedCulturalScreen() {
  const colorScheme = useColorScheme();
  const theme = useTheme();
  const router = useRouter();
  const { toast } = useToast();
  const { user } = useAuth();
  const params = useLocalSearchParams();

  // Tab state
  const [activeTab, setActiveTab] = useState<string>('cultural');
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  // Settings state
  const [culturalSettings, setCulturalSettings] = useState<CulturalSettings>(defaultCulturalSettings);
  const [languageSettings, setLanguageSettings] = useState<LanguageSettings>(defaultLanguageSettings);
  const [regionalSettings, setRegionalSettings] = useState<RegionalSettings>(defaultRegionalSettings);
  const [accessibilitySettings, setAccessibilitySettings] = useState<AccessibilitySettings>(defaultAccessibilitySettings);

  // Cultural tabs configuration with enhanced descriptions
  const culturalTabsConfig: CulturalTab[] = useMemo(() => [
    {
      id: 'cultural',
      title: 'Cultural Identity',
      icon: Globe,
      description: 'Set your cultural background and communication preferences',
      component: CulturalTab,
    },
    {
      id: 'language',
      title: 'Language & Localization',
      icon: Languages,
      description: 'Configure language, date formats, and regional settings',
      component: LanguageTab,
    },
    {
      id: 'regional',
      title: 'Regional Market',
      icon: MapPin,
      description: 'Set regional preferences for payments and compliance',
      component: RegionalTab,
    },
    {
      id: 'accessibility',
      title: 'Accessibility',
      icon: Accessibility,
      description: 'Voice commands and motor assistance options',
      component: AccessibilityTab,
    },
  ], []);

  // Load settings on mount
  useEffect(() => {
    if (user?.id) {
      loadSettings();
    }
  }, [user?.id]);

  // Set active tab from params
  useEffect(() => {
    if (params.tab && typeof params.tab === 'string') {
      setActiveTab(params.tab);
    }
  }, [params.tab]);

  const loadSettings = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('profiles')
        .select('cultural_settings, language_settings, regional_settings, accessibility_settings')
        .eq('id', user?.id)
        .single();

      if (error) throw error;

      if (data?.cultural_settings) {
        setCulturalSettings({ ...defaultCulturalSettings, ...data.cultural_settings });
      }
      if (data?.language_settings) {
        setLanguageSettings({ ...defaultLanguageSettings, ...data.language_settings });
      }
      if (data?.regional_settings) {
        setRegionalSettings({ ...defaultRegionalSettings, ...data.regional_settings });
      }
      if (data?.accessibility_settings) {
        setAccessibilitySettings({ ...defaultAccessibilitySettings, ...data.accessibility_settings });
      }
    } catch (error) {
      logger.error('Error loading cultural settings:', error);
      toast?.show('Failed to load settings', 'error');
    } finally {
      setLoading(false);
    }
  };

  const saveSettings = async () => {
    try {
      setSaving(true);
      const { error } = await supabase.from('profiles')
        .update({
          cultural_settings: culturalSettings,
          language_settings: languageSettings,
          regional_settings: regionalSettings,
          accessibility_settings: accessibilitySettings,
          updated_at: new Date().toISOString(),
        })
        .eq('id', user?.id);

      if (error) throw error;
      toast?.show('Cultural settings saved successfully!', 'success');
    } catch (error) {
      logger.error('Error saving cultural settings:', error);
      toast?.show('Failed to save settings', 'error');
    } finally {
      setSaving(false);
    }
  };

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadSettings();
    setRefreshing(false);
  }, []);

  const updateSettings = useCallback((category: string, key: string, value: any) => {
    switch (category) {
      case 'cultural':
        setCulturalSettings(prev => ({ ...prev, [key]: value }));
        break;
      case 'language':
        setLanguageSettings(prev => ({ ...prev, [key]: value }));
        break;
      case 'regional':
        setRegionalSettings(prev => ({ ...prev, [key]: value }));
        break;
      case 'accessibility':
        setAccessibilitySettings(prev => ({ ...prev, [key]: value }));
        break;
    }
  }, []);

  const getCurrentSettings = () => {
    switch (activeTab) {
      case 'cultural':
        return culturalSettings;
      case 'language':
        return languageSettings;
      case 'regional':
        return regionalSettings;
      case 'accessibility':
        return accessibilitySettings;
      default:
        return {};
    }
  };

  const getCurrentTabConfig = () => culturalTabsConfig.find(tab => tab.id === activeTab);

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={[styles.loadingText, { color: theme.colors.text }]}>
            Loading cultural settings...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Stack.Screen
        options={{
          title: 'Cultural & Accessibility',
          headerStyle: { backgroundColor: theme.colors.surface },
          headerTintColor: theme.colors.text,
          headerTitleStyle: { fontWeight: '600' },
          headerLeft: () => (
            <TouchableOpacity onPress={() => router.back()} style={styles.headerButton}
            >
              <ChevronLeft size={24} color={theme.colors.text} />
            </TouchableOpacity>
          ),
          headerRight: () => (
            <Button title={saving ? 'Saving...' : 'Save'} onPress={saveSettings} disabled={saving} style={[styles.saveButton, { backgroundColor: theme.colors.primary }]}
              titleStyle={{ fontSize: 16, fontWeight: '600' }}
            />
          ),
        }}
      />

      {/* Enhanced Tab Header */}
      <View style={[styles.tabHeader, { 
        backgroundColor: theme.colors.surface,
        shadowColor: theme.colors.text,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.05,
        shadowRadius: 8,
        elevation: 3,
      }]}>
        <ScrollView
          horizontal 
          showsHorizontalScrollIndicator={false} 
          contentContainerStyle={styles.tabScrollContent}
        >
          {culturalTabsConfig.map((tab) => {
            const isActive = activeTab === tab.id;
            const IconComponent = tab.icon;

            return (
              <TouchableOpacity 
                key={tab.id} 
                style={[
                  styles.tab,
                  {
                    backgroundColor: isActive ? theme.colors.primary : 'transparent',
                    borderColor: isActive ? theme.colors.primary : 'transparent',
                    shadowColor: isActive ? theme.colors.primary : 'transparent',
                    shadowOffset: { width: 0, height: 4 },
                    shadowOpacity: isActive ? 0.3 : 0,
                    shadowRadius: 8,
                    elevation: isActive ? 6 : 0,
                  },
                ]}
                onPress={() => setActiveTab(tab.id)}
              >
                <View style={[
                  styles.tabIconContainer,
                  {
                    backgroundColor: isActive 
                      ? 'rgba(255, 255, 255, 0.2)' 
                      : colorWithOpacity(theme.colors.primary, 0.15),
                  }
                ]}>
                  <IconComponent 
                    size={20} 
                    color={isActive ? 'white' : theme.colors.primary}
                  />
                </View>
                <Text
                  style={[
                    styles.tabText,
                    {
                      color: isActive ? 'white' : theme.colors.text,
                      fontWeight: isActive ? '700' : '500',
                    },
                  ]}
                >
                  {tab.title}
                </Text>
                {isActive && (
                  <Text
                    style={[
                      styles.tabDescription,
                      { color: 'rgba(255, 255, 255, 0.8)' },
                    ]}
                  >
                    {tab.description}
                  </Text>
                )}
              </TouchableOpacity>
            );
          })}
        </ScrollView>
      </View>

      {/* Tab Content */}
      <View style={styles.tabContainer}>
        {(() => {
          const tabConfig = getCurrentTabConfig();
          if (tabConfig?.component) {
            const TabComponent = tabConfig.component;
            return (
              <TabComponent 
                settings={getCurrentSettings()} 
                updateSettings={updateSettings} 
                refreshControl={
                  <RefreshControl 
                    refreshing={refreshing} 
                    onRefresh={onRefresh} 
                    colors={[theme.colors.primary]} 
                    tintColor={theme.colors.primary}
                  />
                }
              />
            );
          }
          return null;
        })()}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  headerButton: {
    padding: 8,
  },
  saveButton: {
    padding: 16,
    borderRadius: 8,
  },
  tabHeader: {
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  tabScrollContent: {
    paddingHorizontal: 16,
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginRight: 8,
    borderBottomWidth: 2,
  },
  tabIconContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    marginRight: 8,
  },
  tabText: {
    marginLeft: 8,
    fontSize: 14,
    fontWeight: '500',
  },
  tabDescription: {
    marginTop: 4,
    fontSize: 12,
    fontWeight: '500',
  },
  tabContainer: {
    flex: 1,
    padding: 16,
  },
  card: {
    marginBottom: 16,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  settingSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  optionGroup: {
    gap: 8,
  },
  optionButton: {
    padding: 12,
    borderWidth: 1,
    borderRadius: 8,
    alignItems: 'center',
  },
  optionText: {
    fontSize: 14,
    fontWeight: '500',
  },
  sliderSection: {
    marginVertical: 8,
  },
  sliderLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  slider: {
    height: 40,
    marginVertical: 8,
  },
  sliderValue: {
    fontSize: 14,
    textAlign: 'center',
    marginTop: 4,
  },
  switchItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  switchItemInfo: {
    flex: 1,
    marginRight: 16,
  },
  switchLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
  switchDescription: {
    fontSize: 14,
    marginTop: 2,
  },
  analyticsContainer: {
    gap: 16,
  },
  analyticsItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  analyticsDetails: {
    marginLeft: 12,
    flex: 1,
  },
  analyticsTitle: {
    fontSize: 14,
    fontWeight: '500',
  },
  analyticsValue: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 2,
  },
  languageSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderWidth: 1,
    borderRadius: 8,
  },
  languageSelectorText: {
    marginLeft: 12,
    fontSize: 16,
    fontWeight: '500',
    flex: 1,
  },
  languageSelectorSubtext: {
    fontSize: 14,
    marginTop: 2,
  },
  formatItem: {
    marginVertical: 8,
  },
  formatLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  formatOptions: {
    flexDirection: 'row',
    gap: 8,
  },
  formatOption: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderWidth: 1,
    borderRadius: 6,
  },
  formatOptionText: {
    fontSize: 14,
    fontWeight: '500',
  },
  previewContainer: {
    gap: 12,
  },
  previewItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  previewLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  previewValue: {
    fontSize: 14,
  },
  regionSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderWidth: 1,
    borderRadius: 8,
  },
  regionInfo: {
    marginLeft: 12,
    flex: 1,
  },
  regionName: {
    fontSize: 16,
    fontWeight: '500',
  },
  regionCode: {
    fontSize: 14,
    marginTop: 2,
  },
  paymentMethodsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  paymentMethodCard: {
    flex: 1,
    minWidth: '45%',
    padding: 12,
    borderWidth: 1,
    borderRadius: 8,
    alignItems: 'center',
    gap: 8,
  },
  paymentMethodText: {
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
  },
}); 