/**
 * Unified Profile Preferences - PHASE 2 CONSOLIDATION
 * 
 * This component consolidates 4 preference routes into a single interface:
 * - living-preferences.tsx (Living Preferences)
 * - matching-preferences.tsx (Matching Preferences)
 * - lifestyle.tsx (Lifestyle Preferences)
 * - preferences.tsx (General Preferences)
 * 
 * Result: 4 routes → 1 route (75% reduction)
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { View, Text, StyleSheet, ScrollView, ActivityIndicator, TouchableOpacity, useColorScheme, Alert, Switch, } from 'react-native';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from '@context/AuthContext';
import { Card } from '@components/ui';
import { Button } from '@design-system';
import { useToast } from '@components/ui/Toast';
import Slider from '@react-native-community/slider';
import { TimePicker } from '@components/ui/TimePicker';
import { Home, Heart, Users, Settings, ChevronLeft, Clock, MapPin, Shield, Target, Coffee, Sun, Moon, Volume2 } from 'lucide-react-native';

import { supabase } from '@utils/supabaseUtils';
import { logger } from '@utils/logger';
import { useTheme } from '@design-system';
import { colorWithOpacity } from '@design-system';

// Preferences tab interface
interface PreferencesTab {
  id: string;
  title: string;
  icon: React.ComponentType<any>;
  description: string;
  component: React.ComponentType<any>;
}

// Living Preferences Interface
interface LivingPreferences {
  cleanliness: number;
  noise_level: number;
  guests_frequency: number;
  wake_time: string;
  sleep_time: string;
  pets_allowed: boolean;
  smoking_allowed: boolean;
  alcohol_allowed: boolean;
  shared_food: boolean;
  shared_chores: boolean;
  shared_groceries: boolean;
  shared_bathroom: boolean;
  shared_common_areas: boolean;
}

// Matching Preferences Interface
interface MatchingPreferences {
  age_range: { min: number; max: number };
  gender_preferences: string[];
  location_radius: number;
  budget_range: { min: number; max: number };
  lifestyle_compatibility: {
    smoking_tolerance: string;
    pet_tolerance: string;
    noise_tolerance: string;
    guest_tolerance: string;
    cleanliness_importance: number;
  };
  verification_requirements: {
    require_identity_verification: boolean;
    require_background_check: boolean;
    minimum_trust_score: number;
  };
}

// Lifestyle Preferences Interface
interface LifestylePreferences {
  daily_routine: {
    wake_time: 'early' | 'moderate' | 'late';
    sleep_time: 'early' | 'moderate' | 'late';
    meal_schedule: 'regular' | 'flexible' | 'irregular';
    exercise_routine: 'morning' | 'evening' | 'flexible' | 'none';
  };
  work_habits: {
    work_location: 'office' | 'home' | 'hybrid' | 'varies';
    work_schedule: 'traditional' | 'flexible' | 'night_shift' | 'irregular';
    video_calls: 'frequent' | 'occasional' | 'rare';
    workspace_needs: 'quiet' | 'moderate' | 'flexible';
  };
  social_preferences: {
    guest_frequency: 'never' | 'rarely' | 'occasionally' | 'frequently';
    parties_events: 'never' | 'small_gatherings' | 'medium_parties' | 'large_events';
  };
  environment: {
    noise_tolerance: 'very_quiet' | 'quiet' | 'moderate' | 'tolerant';
    temperature_preference: 'cool' | 'moderate' | 'warm';
    cleanliness_level: 'very_clean' | 'clean' | 'moderate' | 'relaxed';
  };
}

// General Preferences Interface
interface GeneralPreferences {
  roommate_gender_preference: 'any' | 'same' | 'opposite' | 'non_binary';
  age_range_min: number;
  age_range_max: number;
  max_roommates: number;
  smoking_preference: 'no_smoking' | 'outdoor_only' | 'smoking_ok';
  drinking_preference: 'no_drinking' | 'social_drinking' | 'regular_drinking';
  pets_preference: 'no_pets' | 'cats_only' | 'dogs_only' | 'all_pets';
  noise_tolerance: 'quiet' | 'moderate' | 'lively';
  cleanliness_level: 'very_clean' | 'clean' | 'moderate' | 'relaxed';
  guest_policy: 'no_guests' | 'occasional' | 'frequent' | 'anytime';
  communication_style: 'direct' | 'diplomatic' | 'minimal';
  budget_sharing: 'split_everything' | 'shared_utilities_only' | 'separate_everything';
  social_activities: boolean;
  cooking_together: boolean;
}

// Default values
const defaultLivingPreferences: LivingPreferences = {
  cleanliness: 3,
  noise_level: 3,
  guests_frequency: 3,
  wake_time: '',
  sleep_time: '',
  pets_allowed: false,
  smoking_allowed: false,
  alcohol_allowed: false,
  shared_food: false,
  shared_chores: true,
  shared_groceries: false,
  shared_bathroom: true,
  shared_common_areas: true,
};

const defaultMatchingPreferences: MatchingPreferences = {
  age_range: { min: 18, max: 35 },
  gender_preferences: ['any'],
  location_radius: 10,
  budget_range: { min: 500, max: 2000 },
  lifestyle_compatibility: {
    smoking_tolerance: 'no_preference',
    pet_tolerance: 'no_preference',
    noise_tolerance: 'moderate',
    guest_tolerance: 'moderate',
    cleanliness_importance: 7,
  },
  verification_requirements: {
    require_identity_verification: true,
    require_background_check: false,
    minimum_trust_score: 6,
  },
};

const defaultLifestylePreferences: LifestylePreferences = {
  daily_routine: {
    wake_time: 'moderate',
    sleep_time: 'moderate',
    meal_schedule: 'flexible',
    exercise_routine: 'flexible',
  },
  work_habits: {
    work_location: 'hybrid',
    work_schedule: 'traditional',
    video_calls: 'occasional',
    workspace_needs: 'moderate',
  },
  social_preferences: {
    guest_frequency: 'occasionally',
    parties_events: 'small_gatherings',
  },
  environment: {
    noise_tolerance: 'moderate',
    temperature_preference: 'moderate',
    cleanliness_level: 'clean',
  },
};

const defaultGeneralPreferences: GeneralPreferences = {
  roommate_gender_preference: 'any',
  age_range_min: 18,
  age_range_max: 35,
  max_roommates: 2,
  smoking_preference: 'no_smoking',
  drinking_preference: 'social_drinking',
  pets_preference: 'no_pets',
  noise_tolerance: 'moderate',
  cleanliness_level: 'clean',
  guest_policy: 'occasional',
  communication_style: 'diplomatic',
  budget_sharing: 'shared_utilities_only',
  social_activities: true,
  cooking_together: true,
};

// Individual tab components
const LivingPreferencesTab = ({ colors, preferences, updatePreferences }: any) => (
  <ScrollView style={styles.tabContent}>
    <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
      {/* Modern card header with icon */}
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 24,
      }}>
        <View style={{
          backgroundColor: colorWithOpacity(theme.colors.primary, 0.15),
          borderRadius: 12,
          padding: 8,
          marginRight: 12,
        }}>
          <Home size={24} color={theme.colors.primary} />
        </View>
        <Text style={[styles.cardTitle, { color: theme.colors.text }]}>Living Preferences</Text>
      </View>

      {/* Environment Settings */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <View style={{
            width: 3,
            height: 16,
            backgroundColor: theme.colors.primary,
            borderRadius: 2,
            marginRight: 12,
          }} />
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Environment</Text>
        </View>

        {/* Cleanliness Level */}
        <View style={[styles.preferenceItem, {
          backgroundColor: 'rgba(248, 250, 252, 0.4)',
          borderRadius: 12,
          padding: 16,
          marginBottom: 16,
          borderWidth: 1,
          borderColor: 'rgba(0, 0, 0, 0.03)',
        }]}>
          <View style={styles.preferenceHeader}>
            <Text style={[styles.preferenceLabel, { color: theme.colors.text }]}>Cleanliness Level</Text>
            <Text style={[styles.preferenceValue, { color: theme.colors.primary }]}>
              {preferences.cleanliness}/5
            </Text>
          </View>
          <Text style={[styles.preferenceDescription, { color: theme.colors.textSecondary }]}>
            How clean do you like your living space?
          </Text>
          <Slider style={styles.slider} minimumValue={1} maximumValue={5} step={1} value={preferences.cleanliness} onValueChange={(value) => updatePreferences('cleanliness', value)} minimumTrackTintColor={theme.colors.primary} maximumTrackTintColor={theme.colors.border} thumbStyle={{ backgroundColor: theme.colors.primary, width: 20, height: 20 }}
          />
        </View>

        {/* Noise Level */}
        <View style={[styles.preferenceItem, {
          backgroundColor: 'rgba(248, 250, 252, 0.4)',
          borderRadius: 12,
          padding: 16,
          marginBottom: 16,
          borderWidth: 1,
          borderColor: 'rgba(0, 0, 0, 0.03)',
        }]}>
          <View style={styles.preferenceHeader}>
            <Text style={[styles.preferenceLabel, { color: theme.colors.text }]}>Noise Tolerance</Text>
            <Text style={[styles.preferenceValue, { color: theme.colors.warning }]}>
              {preferences.noise_level}/5
            </Text>
          </View>
          <Text style={[styles.preferenceDescription, { color: theme.colors.textSecondary }]}>
            How much noise are you comfortable with?
          </Text>
          <Slider style={styles.slider} minimumValue={1} maximumValue={5} step={1} value={preferences.noise_level} onValueChange={(value) => updatePreferences('noise_level', value)} minimumTrackTintColor={theme.colors.warning} maximumTrackTintColor={theme.colors.border} thumbStyle={{ backgroundColor: theme.colors.warning, width: 20, height: 20 }}
          />
        </View>

        {/* Guest Frequency */}
        <View style={[styles.preferenceItem, {
          backgroundColor: 'rgba(248, 250, 252, 0.4)',
          borderRadius: 12,
          padding: 16,
          marginBottom: 16,
          borderWidth: 1,
          borderColor: 'rgba(0, 0, 0, 0.03)',
        }]}>
          <View style={styles.preferenceHeader}>
            <Text style={[styles.preferenceLabel, { color: theme.colors.text }]}>Guest Frequency</Text>
            <Text style={[styles.preferenceValue, { color: theme.colors.success }]}>
              {preferences.guests_frequency}/5
            </Text>
          </View>
          <Text style={[styles.preferenceDescription, { color: theme.colors.textSecondary }]}>
            How often do you have guests over?
          </Text>
          <Slider style={styles.slider} minimumValue={1} maximumValue={5} step={1} value={preferences.guests_frequency} onValueChange={(value) => updatePreferences('guests_frequency', value)} minimumTrackTintColor={theme.colors.success} maximumTrackTintColor={theme.colors.border} thumbStyle={{ backgroundColor: theme.colors.success, width: 20, height: 20 }}
          />
        </View>
      </View>

      {/* Lifestyle Toggles */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <View style={{
            width: 3,
            height: 16,
            backgroundColor: theme.colors.success,
            borderRadius: 2,
            marginRight: 12,
          }} />
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Lifestyle</Text>
        </View>

        {/* Toggle Items */}
        {[
          { key: 'pets_allowed', label: 'Pets Allowed', icon: '🐕', description: 'Allow pets in the living space' },
          { key: 'smoking_allowed', label: 'Smoking Allowed', icon: '🚬', description: 'Allow smoking indoors' },
          { key: 'alcohol_allowed', label: 'Alcohol Allowed', icon: '🍺', description: 'Allow alcohol consumption' },
          { key: 'shared_food', label: 'Shared Food', icon: '🍽️', description: 'Share meals and groceries' },
          { key: 'shared_chores', label: 'Shared Chores', icon: '🧹', description: 'Share household responsibilities' },
          { key: 'shared_groceries', label: 'Shared Groceries', icon: '🛒', description: 'Share grocery shopping and costs' },
        ].map((item, index) => (
          <View key={item.key} style={[styles.toggleItem, {
            backgroundColor: 'rgba(248, 250, 252, 0.4)',
            borderRadius: 12,
            padding: 16,
            marginBottom: 12,
            borderWidth: 1,
            borderColor: 'rgba(0, 0, 0, 0.03)',
          }]}>
            <View style={styles.toggleContent}>
              <View style={styles.toggleInfo}>
                <View style={{
                  backgroundColor: colorWithOpacity(theme.colors.primary, 0.15),
                  borderRadius: 8,
                  padding: 6,
                  marginRight: 12,
                  width: 32,
                  height: 32,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                  <Text style={{ fontSize: 16 }}>{item.icon}</Text>
                </View>
                <View style={{ flex: 1 }}>
                  <Text style={[styles.toggleLabel, { color: theme.colors.text }]}>{item.label}</Text>
                  <Text style={[styles.toggleDescription, { color: theme.colors.textSecondary }]}>
                    {item.description}
                  </Text>
                </View>
              </View>
              <Switch value={preferences[item.key]} onValueChange={(value) => updatePreferences(item.key, value)} thumbColor={theme.colors.primary} trackColor={{ false: theme.colors.border, true: colorWithOpacity(theme.colors.primary, 0.5) }}
              />
            </View>
          </View>
        ))}
      </View>
    </Card>
  </ScrollView>
);

const MatchingPreferencesTab = ({ colors, preferences, updatePreferences }: any) => (
  <ScrollView style={styles.tabContent}>
    <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
      {/* Modern card header with icon */}
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 24,
      }}>
        <View style={{
          backgroundColor: colorWithOpacity(theme.colors.warning, 0.15),
          borderRadius: 12,
          padding: 8,
          marginRight: 12,
        }}>
          <Target size={24} color={theme.colors.warning} />
        </View>
        <Text style={[styles.cardTitle, { color: theme.colors.text }]}>Matching Preferences</Text>
      </View>

      {/* Age Range Section */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <View style={{
            width: 3,
            height: 16,
            backgroundColor: theme.colors.warning,
            borderRadius: 2,
            marginRight: 12,
          }} />
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Age Range</Text>
        </View>

        <View style={[styles.preferenceItem, {
          backgroundColor: 'rgba(248, 250, 252, 0.4)',
          borderRadius: 12,
          padding: 16,
          marginBottom: 16,
          borderWidth: 1,
          borderColor: 'rgba(0, 0, 0, 0.03)',
        }]}>
          <View style={styles.preferenceHeader}>
            <Text style={[styles.preferenceLabel, { color: theme.colors.text }]}>Preferred Age Range</Text>
            <Text style={[styles.preferenceValue, { color: theme.colors.warning }]}>
              {preferences.age_range?.min || 18} - {preferences.age_range?.max || 35} years
            </Text>
          </View>
          <Text style={[styles.preferenceDescription, { color: theme.colors.textSecondary }]}>
            Age range for potential roommates
          </Text>
          
          <View style={{ marginTop: 12 }}>
            <Text style={[styles.sliderLabel, { color: theme.colors.textSecondary, marginBottom: 8 }]}>
              Minimum Age: {preferences.age_range?.min || 18}
            </Text>
            <Slider style={styles.slider} minimumValue={18} maximumValue={65} step={1} value={preferences.age_range?.min || 18} onValueChange={(value) => updatePreferences('age_range', { 
                ...preferences.age_range, 
                min: value 
              })}
              minimumTrackTintColor={theme.colors.warning} maximumTrackTintColor={theme.colors.border} thumbStyle={{ backgroundColor: theme.colors.warning, width: 20, height: 20 }}
            />
            
            <Text style={[styles.sliderLabel, { color: theme.colors.textSecondary, marginBottom: 8, marginTop: 12 }]}>
              Maximum Age: {preferences.age_range?.max || 35}
            </Text>
            <Slider style={styles.slider} minimumValue={18} maximumValue={65} step={1} value={preferences.age_range?.max || 35} onValueChange={(value) => updatePreferences('age_range', { 
                ...preferences.age_range, 
                max: value 
              })}
              minimumTrackTintColor={theme.colors.warning} maximumTrackTintColor={theme.colors.border} thumbStyle={{ backgroundColor: theme.colors.warning, width: 20, height: 20 }}
            />
          </View>
        </View>
      </View>

      {/* Location Section */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <View style={{
            width: 3,
            height: 16,
            backgroundColor: theme.colors.success,
            borderRadius: 2,
            marginRight: 12,
          }} />
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Location</Text>
        </View>

        <View style={[styles.preferenceItem, {
          backgroundColor: 'rgba(248, 250, 252, 0.4)',
          borderRadius: 12,
          padding: 16,
          marginBottom: 16,
          borderWidth: 1,
          borderColor: 'rgba(0, 0, 0, 0.03)',
        }]}>
          <View style={styles.preferenceHeader}>
            <Text style={[styles.preferenceLabel, { color: theme.colors.text }]}>Search Radius</Text>
            <Text style={[styles.preferenceValue, { color: theme.colors.success }]}>
              {preferences.location_radius || 10} miles
            </Text>
          </View>
          <Text style={[styles.preferenceDescription, { color: theme.colors.textSecondary }]}>
            Maximum distance to search for roommates
          </Text>
          <Slider style={styles.slider} minimumValue={1} maximumValue={50} step={1} value={preferences.location_radius || 10} onValueChange={(value) => updatePreferences('location_radius', value)} minimumTrackTintColor={theme.colors.success} maximumTrackTintColor={theme.colors.border} thumbStyle={{ backgroundColor: theme.colors.success, width: 20, height: 20 }}
          />
        </View>
      </View>

      {/* Budget Range Section */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <View style={{
            width: 3,
            height: 16,
            backgroundColor: theme.colors.primary,
            borderRadius: 2,
            marginRight: 12,
          }} />
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Budget Range</Text>
        </View>

        <View style={[styles.preferenceItem, {
          backgroundColor: 'rgba(248, 250, 252, 0.4)',
          borderRadius: 12,
          padding: 16,
          marginBottom: 16,
          borderWidth: 1,
          borderColor: 'rgba(0, 0, 0, 0.03)',
        }]}>
          <View style={styles.preferenceHeader}>
            <Text style={[styles.preferenceLabel, { color: theme.colors.text }]}>Monthly Budget</Text>
            <Text style={[styles.preferenceValue, { color: theme.colors.primary }]}>
              ${preferences.budget_range?.min || 500} - ${preferences.budget_range?.max || 2000}
            </Text>
          </View>
          <Text style={[styles.preferenceDescription, { color: theme.colors.textSecondary }]}>
            Your monthly housing budget range
          </Text>
          
          <View style={{ marginTop: 12 }}>
            <Text style={[styles.sliderLabel, { color: theme.colors.textSecondary, marginBottom: 8 }]}>
              Minimum: ${preferences.budget_range?.min || 500}
            </Text>
            <Slider style={styles.slider} minimumValue={200} maximumValue={5000} step={50} value={preferences.budget_range?.min || 500} onValueChange={(value) => updatePreferences('budget_range', { 
                ...preferences.budget_range, 
                min: value 
              })}
              minimumTrackTintColor={theme.colors.primary} maximumTrackTintColor={theme.colors.border} thumbStyle={{ backgroundColor: theme.colors.primary, width: 20, height: 20 }}
            />
            
            <Text style={[styles.sliderLabel, { color: theme.colors.textSecondary, marginBottom: 8, marginTop: 12 }]}>
              Maximum: ${preferences.budget_range?.max || 2000}
            </Text>
            <Slider style={styles.slider} minimumValue={200} maximumValue={5000} step={50} value={preferences.budget_range?.max || 2000} onValueChange={(value) => updatePreferences('budget_range', { 
                ...preferences.budget_range, 
                max: value 
              })}
              minimumTrackTintColor={theme.colors.primary} maximumTrackTintColor={theme.colors.border} thumbStyle={{ backgroundColor: theme.colors.primary, width: 20, height: 20 }}
            />
          </View>
        </View>
      </View>

      {/* Verification Requirements */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <View style={{
            width: 3,
            height: 16,
            backgroundColor: theme.colors.error,
            borderRadius: 2,
            marginRight: 12,
          }} />
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Verification</Text>
        </View>

        {[
          { 
            key: 'require_identity_verification', 
            label: 'Require Identity Verification', 
            icon: '🆔', 
            description: 'Only show verified users' 
          },
          { 
            key: 'require_background_check', 
            label: 'Require Background Check', 
            icon: '🔍', 
            description: 'Only show users with background checks' 
          },
        ].map((item) => (
          <View key={item.key} style={[styles.toggleItem, {
            backgroundColor: 'rgba(248, 250, 252, 0.4)',
            borderRadius: 12,
            padding: 16,
            marginBottom: 12,
            borderWidth: 1,
            borderColor: 'rgba(0, 0, 0, 0.03)',
          }]}>
            <View style={styles.toggleContent}>
              <View style={styles.toggleInfo}>
                <View style={{
                  backgroundColor: colorWithOpacity(theme.colors.error, 0.15),
                  borderRadius: 8,
                  padding: 6,
                  marginRight: 12,
                  width: 32,
                  height: 32,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                  <Text style={{ fontSize: 16 }}>{item.icon}</Text>
                </View>
                <View style={{ flex: 1 }}>
                  <Text style={[styles.toggleLabel, { color: theme.colors.text }]}>{item.label}</Text>
                  <Text style={[styles.toggleDescription, { color: theme.colors.textSecondary }]}>
                    {item.description}
                  </Text>
                </View>
              </View>
              <Switch value={preferences.verification_requirements?.[item.key] || false} onValueChange={(value) => updatePreferences('verification_requirements', {
                  ...preferences.verification_requirements,
                  [item.key]: value
                })}
                thumbColor={theme.colors.error} trackColor={{ false: theme.colors.border, true: colorWithOpacity(theme.colors.error, 0.5) }}
              />
            </View>
          </View>
        ))}
      </View>
    </Card>
  </ScrollView>
);

const LifestylePreferencesTab = ({ colors, preferences, updatePreferences }: any) => (
  <ScrollView style={styles.tabContent}>
    <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
      {/* Modern card header with icon */}
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 24,
      }}>
        <View style={{
          backgroundColor: colorWithOpacity(theme.colors.success, 0.15),
          borderRadius: 12,
          padding: 8,
          marginRight: 12,
        }}>
          <Coffee size={24} color={theme.colors.success} />
        </View>
        <Text style={[styles.cardTitle, { color: theme.colors.text }]}>Lifestyle Preferences</Text>
      </View>

      {/* Daily Routine Section */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <View style={{
            width: 3,
            height: 16,
            backgroundColor: theme.colors.success,
            borderRadius: 2,
            marginRight: 12,
          }} />
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Daily Routine</Text>
        </View>

        {[
          { 
            key: 'wake_time', 
            label: 'Wake Time', 
            icon: '🌅', 
            options: ['early', 'moderate', 'late'],
            descriptions: ['6:00-7:00 AM', '7:00-9:00 AM', '9:00+ AM']
          },
          { 
            key: 'sleep_time', 
            label: 'Sleep Time', 
            icon: '🌙', 
            options: ['early', 'moderate', 'late'],
            descriptions: ['9:00-10:00 PM', '10:00-12:00 AM', '12:00+ AM']
          },
          { 
            key: 'meal_schedule', 
            label: 'Meal Schedule', 
            icon: '🍽️', 
            options: ['regular', 'flexible', 'irregular'],
            descriptions: ['Fixed meal times', 'Somewhat flexible', 'Very irregular']
          },
          { 
            key: 'exercise_routine', 
            label: 'Exercise Routine', 
            icon: '💪', 
            options: ['morning', 'evening', 'flexible', 'none'],
            descriptions: ['Morning workouts', 'Evening workouts', 'Anytime', 'No regular exercise']
          },
        ].map((item) => (
          <View key={item.key} style={[styles.preferenceItem, {
            backgroundColor: 'rgba(248, 250, 252, 0.4)',
            borderRadius: 12,
            padding: 16,
            marginBottom: 16,
            borderWidth: 1,
            borderColor: 'rgba(0, 0, 0, 0.03)',
          }]}>
            <View style={styles.preferenceHeader}>
              <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                flex: 1,
              }}>
                <View style={{
                  backgroundColor: colorWithOpacity(theme.colors.success, 0.15),
                  borderRadius: 8,
                  padding: 6,
                  marginRight: 12,
                  width: 32,
                  height: 32,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                  <Text style={{ fontSize: 16 }}>{item.icon}</Text>
                </View>
                <Text style={[styles.preferenceLabel, { color: theme.colors.text }]}>{item.label}</Text>
              </View>
            </View>
            
            <View style={{
              flexDirection: 'row',
              flexWrap: 'wrap',
              marginTop: 12,
            }}>
              {item.options.map((option, index) => (
                <TouchableOpacity key={option} style={{
                    backgroundColor: preferences.daily_routine?.[item.key] === option 
                      ? theme.colors.success 
                      : colorWithOpacity(theme.colors.success, 0.1),
                    borderRadius: 20,
                    paddingHorizontal: 16,
                    paddingVertical: 8,
                    marginRight: 8,
                    marginBottom: 8,
                    borderWidth: 1,
                    borderColor: preferences.daily_routine?.[item.key] === option 
                      ? theme.colors.success 
                      : colorWithOpacity(theme.colors.success, 0.3),
                  }}
                  onPress={() => updatePreferences('daily_routine', {
                    ...preferences.daily_routine,
                    [item.key]: option
                  })}
                >
                  <Text style={{
                    color: preferences.daily_routine?.[item.key] === option 
                      ? 'white' 
                      : theme.colors.success,
                    fontSize: 14,
                    fontWeight: '500',
                  }}>
                    {option.charAt(0).toUpperCase() + option.slice(1)}
                  </Text>
                  <Text style={{
                    color: preferences.daily_routine?.[item.key] === option 
                      ? 'rgba(255, 255, 255, 0.8)' 
                      : theme.colors.textSecondary,
                    fontSize: 12,
                    marginTop: 2,
                  }}>
                    {item.descriptions[index]}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        ))}
      </View>

      {/* Work Habits Section */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <View style={{
            width: 3,
            height: 16,
            backgroundColor: theme.colors.primary,
            borderRadius: 2,
            marginRight: 12,
          }} />
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Work Habits</Text>
        </View>

        {[
          { 
            key: 'work_location', 
            label: 'Work Location', 
            icon: '💼', 
            options: ['office', 'home', 'hybrid', 'varies'],
            descriptions: ['Always in office', 'Always remote', 'Mixed schedule', 'Variable locations']
          },
          { 
            key: 'work_schedule', 
            label: 'Work Schedule', 
            icon: '⏰', 
            options: ['traditional', 'flexible', 'night_shift', 'irregular'],
            descriptions: ['9-5 weekdays', 'Flexible hours', 'Night shifts', 'Irregular schedule']
          },
          { 
            key: 'video_calls', 
            label: 'Video Calls', 
            icon: '📹', 
            options: ['frequent', 'occasional', 'rare'],
            descriptions: ['Daily calls', 'Few times a week', 'Rarely']
          },
          { 
            key: 'workspace_needs', 
            label: 'Workspace Needs', 
            icon: '🖥️', 
            options: ['quiet', 'moderate', 'flexible'],
            descriptions: ['Very quiet space', 'Some noise OK', 'Any environment']
          },
        ].map((item) => (
          <View key={item.key} style={[styles.preferenceItem, {
            backgroundColor: 'rgba(248, 250, 252, 0.4)',
            borderRadius: 12,
            padding: 16,
            marginBottom: 16,
            borderWidth: 1,
            borderColor: 'rgba(0, 0, 0, 0.03)',
          }]}>
            <View style={styles.preferenceHeader}>
              <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                flex: 1,
              }}>
                <View style={{
                  backgroundColor: colorWithOpacity(theme.colors.primary, 0.15),
                  borderRadius: 8,
                  padding: 6,
                  marginRight: 12,
                  width: 32,
                  height: 32,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                  <Text style={{ fontSize: 16 }}>{item.icon}</Text>
                </View>
                <Text style={[styles.preferenceLabel, { color: theme.colors.text }]}>{item.label}</Text>
              </View>
            </View>
            
            <View style={{
              flexDirection: 'row',
              flexWrap: 'wrap',
              marginTop: 12,
            }}>
              {item.options.map((option, index) => (
                <TouchableOpacity key={option} style={{
                    backgroundColor: preferences.work_habits?.[item.key] === option 
                      ? theme.colors.primary 
                      : colorWithOpacity(theme.colors.primary, 0.1),
                    borderRadius: 20,
                    paddingHorizontal: 16,
                    paddingVertical: 8,
                    marginRight: 8,
                    marginBottom: 8,
                    borderWidth: 1,
                    borderColor: preferences.work_habits?.[item.key] === option 
                      ? theme.colors.primary 
                      : colorWithOpacity(theme.colors.primary, 0.3),
                  }}
                  onPress={() => updatePreferences('work_habits', {
                    ...preferences.work_habits,
                    [item.key]: option
                  })}
                >
                  <Text style={{
                    color: preferences.work_habits?.[item.key] === option 
                      ? 'white' 
                      : theme.colors.primary,
                    fontSize: 14,
                    fontWeight: '500',
                  }}>
                    {option.charAt(0).toUpperCase() + option.slice(1).replace('_', ' ')}
                  </Text>
                  <Text style={{
                    color: preferences.work_habits?.[item.key] === option 
                      ? 'rgba(255, 255, 255, 0.8)' 
                      : theme.colors.textSecondary,
                    fontSize: 12,
                    marginTop: 2,
                  }}>
                    {item.descriptions[index]}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        ))}
      </View>

      {/* Social Preferences Section */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <View style={{
            width: 3,
            height: 16,
            backgroundColor: theme.colors.warning,
            borderRadius: 2,
            marginRight: 12,
          }} />
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Social Preferences</Text>
        </View>

        {[
          { 
            key: 'guest_frequency', 
            label: 'Guest Frequency', 
            icon: '👥', 
            options: ['never', 'rarely', 'occasionally', 'frequently'],
            descriptions: ['No guests', 'Very rarely', 'Sometimes', 'Often']
          },
          { 
            key: 'parties_events', 
            label: 'Parties & Events', 
            icon: '🎉', 
            options: ['never', 'small_gatherings', 'medium_parties', 'large_events'],
            descriptions: ['No parties', 'Small groups', 'Medium parties', 'Large events']
          },
        ].map((item) => (
          <View key={item.key} style={[styles.preferenceItem, {
            backgroundColor: 'rgba(248, 250, 252, 0.4)',
            borderRadius: 12,
            padding: 16,
            marginBottom: 16,
            borderWidth: 1,
            borderColor: 'rgba(0, 0, 0, 0.03)',
          }]}>
            <View style={styles.preferenceHeader}>
              <View style={{
                flexDirection: 'row',
                alignItems: 'center',
                flex: 1,
              }}>
                <View style={{
                  backgroundColor: colorWithOpacity(theme.colors.warning, 0.15),
                  borderRadius: 8,
                  padding: 6,
                  marginRight: 12,
                  width: 32,
                  height: 32,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                  <Text style={{ fontSize: 16 }}>{item.icon}</Text>
                </View>
                <Text style={[styles.preferenceLabel, { color: theme.colors.text }]}>{item.label}</Text>
              </View>
            </View>
            
            <View style={{
              flexDirection: 'row',
              flexWrap: 'wrap',
              marginTop: 12,
            }}>
              {item.options.map((option, index) => (
                <TouchableOpacity key={option} style={{
                    backgroundColor: preferences.social_preferences?.[item.key] === option 
                      ? theme.colors.warning 
                      : colorWithOpacity(theme.colors.warning, 0.1),
                    borderRadius: 20,
                    paddingHorizontal: 16,
                    paddingVertical: 8,
                    marginRight: 8,
                    marginBottom: 8,
                    borderWidth: 1,
                    borderColor: preferences.social_preferences?.[item.key] === option 
                      ? theme.colors.warning 
                      : colorWithOpacity(theme.colors.warning, 0.3),
                  }}
                  onPress={() => updatePreferences('social_preferences', {
                    ...preferences.social_preferences,
                    [item.key]: option
                  })}
                >
                  <Text style={{
                    color: preferences.social_preferences?.[item.key] === option 
                      ? 'white' 
                      : theme.colors.warning,
                    fontSize: 14,
                    fontWeight: '500',
                  }}>
                    {option.charAt(0).toUpperCase() + option.slice(1).replace('_', ' ')}
                  </Text>
                  <Text style={{
                    color: preferences.social_preferences?.[item.key] === option 
                      ? 'rgba(255, 255, 255, 0.8)' 
                      : theme.colors.textSecondary,
                    fontSize: 12,
                    marginTop: 2,
                  }}>
                    {item.descriptions[index]}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        ))}
      </View>
    </Card>
  </ScrollView>
);

const GeneralPreferencesTab = ({ colors, preferences, updatePreferences }: any) => (
  <ScrollView style={styles.tabContent}>
    <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
      <Text style={[styles.cardTitle, { color: theme.colors.text }]}>General Preferences</Text>
      
      <View style={styles.preferenceSection}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Roommate Gender Preference</Text>
        <View style={styles.optionGroup}>
          {[
            { value: 'any', label: 'Any Gender' },
            { value: 'same', label: 'Same Gender' },
            { value: 'opposite', label: 'Opposite Gender' },
            { value: 'non_binary', label: 'Non-Binary' },
          ].map((option) => (
            <TouchableOpacity key={option.value} style={[
                styles.optionButton,
                { 
                  backgroundColor: preferences.roommate_gender_preference === option.value ? theme.colors.primary : theme.colors.surface,
                  borderColor: theme.colors.border
                }
              ]}
              onPress={() => updatePreferences('general', 'roommate_gender_preference', option.value)}
            >
              <Text style={[
                styles.optionText,
                { color: preferences.roommate_gender_preference === option.value ? theme.colors.white : theme.colors.text }
              ]}>
                {option.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <View style={styles.preferenceSection}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Maximum Roommates</Text>
        <Slider style={styles.slider} minimumValue={1} maximumValue={5} step={1} value={preferences.max_roommates} onValueChange={(value) => updatePreferences('general', 'max_roommates', value)} minimumTrackTintColor={theme.colors.primary} maximumTrackTintColor={theme.colors.border}
        />
        <Text style={[styles.sliderLabel, { color: theme.colors.textSecondary }]}>
          {preferences.max_roommates} roommates
        </Text>
      </View>

      <View style={styles.preferenceSection}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Lifestyle Preferences</Text>
        {[
          { key: 'social_activities', label: 'Enjoy Social Activities Together' },
          { key: 'cooking_together', label: 'Cook Together Sometimes' },
        ].map((item) => (
          <View key={item.key} style={styles.switchItem}>
            <Text style={[styles.switchLabel, { color: theme.colors.text }]}>{item.label}</Text>
            <Switch value={preferences[item.key]} onValueChange={(value) => updatePreferences('general', item.key, value)} thumbColor={theme.colors.primary} trackColor={{ false: theme.colors.border, true: colorWithOpacity(theme.colors.primary, 0.5) }}
            />
          </View>
        ))}
      </View>
    </Card>
  </ScrollView>
);

export default function UnifiedPreferencesScreen() {
  const theme = useTheme();
  const colorScheme = useColorScheme();
  const router = useRouter();
  const { toast } = useToast();
  const { user } = useAuth();
  const params = useLocalSearchParams();

  // Tab state
  const [activeTab, setActiveTab] = useState<string>('living');
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  // Preferences state
  const [livingPreferences, setLivingPreferences] = useState<LivingPreferences>(defaultLivingPreferences);
  const [matchingPreferences, setMatchingPreferences] = useState<MatchingPreferences>(defaultMatchingPreferences);
  const [lifestylePreferences, setLifestylePreferences] = useState<LifestylePreferences>(defaultLifestylePreferences);
  const [generalPreferences, setGeneralPreferences] = useState<GeneralPreferences>({
    roommate_gender_preference: 'any',
    age_range_min: 18,
    age_range_max: 35,
    max_roommates: 3,
    smoking_preference: 'no_smoking',
    drinking_preference: 'social_drinking',
    pets_preference: 'no_pets',
    noise_tolerance: 'moderate',
    cleanliness_level: 'clean',
    guest_policy: 'occasional',
    communication_style: 'diplomatic',
    budget_sharing: 'shared_utilities_only',
    social_activities: true,
    cooking_together: false,
  });

  // Tab configuration with modern styling
  const preferencesTabsConfig: PreferencesTab[] = useMemo(() => [
    {
      id: 'living',
      title: 'Living Environment',
      icon: Home,
      description: 'Set your living space preferences and shared arrangements',
      component: LivingPreferencesTab,
    },
    {
      id: 'matching',
      title: 'Matching Criteria',
      icon: Target,
      description: 'Define your ideal roommate characteristics',
      component: MatchingPreferencesTab,
    },
    {
      id: 'lifestyle',
      title: 'Lifestyle',
      icon: Coffee,
      description: 'Share your daily routines and work habits',
      component: LifestylePreferencesTab,
    },
    {
      id: 'general',
      title: 'General',
      icon: Settings,
      description: 'Overall preferences and communication style',
      component: GeneralPreferencesTab,
    },
  ], []);

  // Load preferences on mount
  useEffect(() => {
  if (user?.id) {
      loadPreferences();
    }
  }, [user?.id]);

  // Set active tab from params
  useEffect(() => {
  if (params.tab && typeof params.tab === 'string') {
      setActiveTab(params.tab);
    }
  }, [params.tab]);

  const loadPreferences = async () => {
  try {
      setLoading(true);
      const { data, error } = await supabase.from('profiles')
        .select('living_preferences, matching_preferences, lifestyle_preferences, general_preferences')
        .eq('id', user?.id);
        .single();

      if (error) throw error;

      if (data?.living_preferences) {
        setLivingPreferences({ ...defaultLivingPreferences, ...data.living_preferences });
      }
      if (data?.matching_preferences) {
        setMatchingPreferences({ ...defaultMatchingPreferences, ...data.matching_preferences });
      }
      if (data?.lifestyle_preferences) {
        setLifestylePreferences({ ...defaultLifestylePreferences, ...data.lifestyle_preferences });
      }
      if (data?.general_preferences) {
        setGeneralPreferences(prev => ({ ...prev, ...data.general_preferences }));
      }
    } catch (error) {
      logger.error('Error loading preferences:', error);
      toast?.show('Failed to load preferences', 'error');
    } finally {
      setLoading(false);
    }
  };

  const savePreferences = async () => {
  try {
      setSaving(true);
      const { error } = await supabase.from('profiles')
        .update({
          living_preferences: livingPreferences,
          matching_preferences: matchingPreferences,
          lifestyle_preferences: lifestylePreferences,
          general_preferences: generalPreferences,
          updated_at: new Date().toISOString(),
        });
        .eq('id', user?.id);

      if (error) throw error;
      toast?.show('Preferences saved successfully!', 'success');
    } catch (error) {
      logger.error('Error saving preferences:', error);
      toast?.show('Failed to save preferences', 'error');
    } finally {
      setSaving(false);
    }
  };

  const updatePreferences = useCallback((key: string, value: any) => {
  switch (activeTab) {
      case 'living':
        setLivingPreferences(prev => ({ ...prev, [key]: value }));
        break;
      case 'matching':
        setMatchingPreferences(prev => ({ ...prev, [key]: value }));
        break;
      case 'lifestyle':
        setLifestylePreferences(prev => ({ ...prev, [key]: value }));
        break;
      case 'general':
        setGeneralPreferences(prev => ({ ...prev, [key]: value }));
        break;
    }
  }, [activeTab]);

  const getCurrentPreferences = () => {
  switch (activeTab) {
      case 'living':
        return livingPreferences;
      case 'matching':
        return matchingPreferences;
      case 'lifestyle':
        return lifestylePreferences;
      case 'general':
        return generalPreferences;
      default:
        return {};
    }
  };

  const getCurrentTabConfig = () => preferencesTabsConfig.find(tab => tab.id === activeTab);

  if (loading) {
    return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={[styles.loadingText, { color: theme.colors.text }]}>
            Loading preferences...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Stack.Screen
        options={{
          title: 'Preferences',
          headerStyle: { backgroundColor: theme.colors.surface },
          headerTintColor: theme.colors.text,
          headerTitleStyle: { fontWeight: '600' },
          headerLeft: () => (
            <TouchableOpacity onPress={() => router.back()} style={styles.headerButton}
            >
              <ChevronLeft size={24} color={theme.colors.text} />
            </TouchableOpacity>
          ),
          headerRight: () => (
            <Button title={saving ? 'Saving...' : 'Save'} onPress={savePreferences} disabled={saving} style={[styles.saveButton, { backgroundColor: theme.colors.primary }]}
              titleStyle={{ fontSize: 16, fontWeight: '600' }}
            />
          ),
        }}
      />

      {/* Enhanced Tab Header */}
      <View style={[styles.tabHeader, { 
        backgroundColor: theme.colors.surface,
        shadowColor: theme.colors.text,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.05,
        shadowRadius: 8,
        elevation: 3,
      }]}>
        <ScrollView
          horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.tabScrollContent}
        >
          {preferencesTabsConfig.map((tab) => {
            const isActive = activeTab === tab.id;
            const IconComponent = tab.icon;

            return (
              <TouchableOpacity 
                key={tab.id} 
                style={[
                  styles.tab,
                  {
                    backgroundColor: isActive ? theme.colors.primary : 'transparent',
                    borderColor: isActive ? theme.colors.primary : 'transparent',
                    shadowColor: isActive ? theme.colors.primary : 'transparent',
                    shadowOffset: { width: 0, height: 4 },
                    shadowOpacity: isActive ? 0.3 : 0,
                    shadowRadius: 8,
                    elevation: isActive ? 6 : 0,
                  },
                ]}
                onPress={() => setActiveTab(tab.id)}
              >
                <View style={[
                  styles.tabIconContainer,
                  {
                    backgroundColor: isActive 
                      ? 'rgba(255, 255, 255, 0.2)' 
                      : colorWithOpacity(theme.colors.primary, 0.15),
                  }
                ]}>
                  <IconComponent 
                    size={20} 
                    color={isActive ? 'white' : theme.colors.primary}
                  />
                </View>
                <Text
                  style={[
                    styles.tabText,
                    {
                      color: isActive ? 'white' : theme.colors.text,
                      fontWeight: isActive ? '700' : '500',
                    },
                  ]}
                >
                  {tab.title}
                </Text>
                {isActive && (
                  <Text
                    style={[
                      styles.tabDescription,
                      { color: 'rgba(255, 255, 255, 0.8)' },
                    ]}
                  >
                    {tab.description}
                  </Text>
                )}
              </TouchableOpacity>
            );
          })}
        </ScrollView>
      </View>

      {/* Tab Content */}
      <View style={styles.tabContainer}>
        {(() => {
          const tabConfig = getCurrentTabConfig();
          if (tabConfig?.component) {
            const TabComponent = tabConfig.component;
            return (
              <TabComponent 
                colors={colors} 
                preferences={getCurrentPreferences()} 
                updatePreferences={updatePreferences}
              />
            );
          }
          return null;
        })()}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    fontWeight: '500',
  },
  headerButton: {
    padding: 8,
  },
  saveButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  tabHeader: {
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.05)',
    paddingVertical: 16,
  },
  tabScrollContent: {
    paddingHorizontal: 16,
    gap: 12,
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    minHeight: 56,
    borderWidth: 1,
  },
  tabIconContainer: {
    borderRadius: 8,
    padding: 6,
    marginRight: 8,
  },
  tabText: {
    fontSize: 16,
    fontWeight: '500',
  },
  tabContainer: {
    flex: 1,
  },
  tabContent: {
    flex: 1,
    padding: 20,
  },
  tabDescription: {
    fontSize: 12,
    marginTop: 4,
  },
  card: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: '700',
    letterSpacing: 0.5,
  },
  preferenceSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    letterSpacing: 0.3,
  },
  slider: {
    width: '100%',
    height: 40,
    marginVertical: 8,
  },
  sliderLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  switchItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  switchLabel: {
    fontSize: 16,
    flex: 1,
    marginRight: 16,
  },
  rangeContainer: {
    gap: 16,
  },
  rangeItem: {
    flex: 1,
  },
  rangeLabel: {
    fontSize: 14,
    marginBottom: 8,
  },
  optionGroup: {
    gap: 8,
  },
  optionButton: {
    padding: 12,
    borderWidth: 1,
    borderRadius: 8,
    alignItems: 'center',
  },
  optionText: {
    fontSize: 14,
    fontWeight: '500',
  },
  sectionContainer: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  preferenceItem: {
    backgroundColor: 'rgba(248, 250, 252, 0.4)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.03)',
  },
  preferenceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  preferenceLabel: {
    fontSize: 16,
    fontWeight: '600',
  },
  preferenceValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  preferenceDescription: {
    fontSize: 14,
    marginBottom: 8,
  },
  toggleItem: {
    backgroundColor: 'rgba(248, 250, 252, 0.4)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.03)',
  },
  toggleContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  toggleInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  toggleLabel: {
    fontSize: 16,
    fontWeight: '600',
  },
  toggleDescription: {
    fontSize: 14,
    marginTop: 2,
  },
}); 