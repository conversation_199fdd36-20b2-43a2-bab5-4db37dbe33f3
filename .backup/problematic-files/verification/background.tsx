import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  SafeAreaView,
  Modal,
  TextInput,
} from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useTheme } from '@design-system';
import { useAuth } from '@context/AuthContext';
import { logger } from '@utils/logger';
import { zeroVerificationService } from '@services/zeroVerificationService';

interface VerificationMethod {
  id: string;
  title: string;
  description: string;
  icon: string;
  time: string;
  isFree: boolean;
  isComingSoon?: boolean;
  originalCost?: string;
  savings?: string;
}

interface ReferenceData {
  name: string;
  email: string;
  relationship: string;
  duration: string;
}

export default function BackgroundCheckScreen() {
  const theme = useTheme();
  const router = useRouter();
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(0);
  const [selectedMethod, setSelectedMethod] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [completedChecks, setCompletedChecks] = useState<string[]>([]);
  const [showReferenceForm, setShowReferenceForm] = useState(false);
  const [references, setReferences] = useState<ReferenceData[]>([]);
  const [newReference, setNewReference] = useState<ReferenceData>({
    name: '',
    email: '',
    relationship: '',
    duration: '',
  });

  // Zero-cost verification methods
  const verificationMethods: VerificationMethod[] = [
    {
      id: 'sex_offender_check',
      title: 'Sex Offender Registry Check',
      description: 'FREE search of National Sex Offender Public Website (NSOPW)',
      icon: 'shield',
      time: 'Instant',
      isFree: true,
    },
    {
      id: 'reference_verification',
      title: 'Reference Verification',
      description: 'FREE email-based reference collection and verification',
      icon: 'users',
      time: '1-3 business days',
      isFree: true,
    },
    {
      id: 'social_media_check',
      title: 'Social Media Verification',
      description: 'FREE manual review of public social media profiles',
      icon: 'eye',
      time: '24-48 hours',
      isFree: true,
    },
    {
      id: 'public_records_search',
      title: 'Public Records Search',
      description: 'FREE search of available public court and government records',
      icon: 'file-text',
      time: '2-5 business days',
      isFree: true,
    },
  ];

  // Premium services (disabled with "coming soon" messages)
  const premiumServices: VerificationMethod[] = [
    {
      id: 'comprehensive_criminal',
      title: 'Comprehensive Criminal Check',
      description: 'Professional criminal background screening',
      icon: 'alert-triangle',
      time: '2-3 business days',
      isFree: false,
      isComingSoon: true,
      originalCost: '$35',
      savings: 'saves $35 per check',
    },
    {
      id: 'employment_verification',
      title: 'Employment Verification',
      description: 'Professional employment history verification',
      icon: 'briefcase',
      time: '3-5 business days',
      isFree: false,
      isComingSoon: true,
      originalCost: '$25',
      savings: 'saves $25 per check',
    },
    {
      id: 'credit_history',
      title: 'Credit History Check',
      description: 'Professional credit report and score verification',
      icon: 'credit-card',
      time: 'Instant',
      isFree: false,
      isComingSoon: true,
      originalCost: '$20',
      savings: 'saves $20 per check',
    },
    {
      id: 'eviction_history',
      title: 'Eviction History Check',
      description: 'Professional eviction and rental history verification',
      icon: 'home',
      time: '1-2 business days',
      isFree: false,
      isComingSoon: true,
      originalCost: '$15',
      savings: 'saves $15 per check',
    },
  ];

  const handleMethodSelection = (methodId: string) => {
    const method = verificationMethods.find(m => m.id === methodId);
    if (method) {
      setSelectedMethod(methodId);
      setCurrentStep(1);
    }
  };

  const handlePremiumServicePress = (service: VerificationMethod) => {
    Alert.alert(
      'Coming Soon - Premium Feature',
      `${service.title} will be available in a future update.\n\nThis premium feature ${service.savings} compared to traditional verification services.\n\nFor now, please use our free verification options.`,
      [{ text: 'OK' }]
    );
  };

  const handleSexOffenderCheck = async () => {
    setIsProcessing(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate API call

      const result = await zeroVerificationService.performSexOffenderCheck({
        firstName: user?.user_metadata?.first_name || '',
        lastName: user?.user_metadata?.last_name || '',
        state: 'All States',
      });

      if (result.success) {
        setCompletedChecks(prev => [...prev, 'sex_offender_check']);
        Alert.alert(
          'Check Complete',
          'Sex offender registry check completed successfully. No matches found.',
          [{ text: 'OK', onPress: () => setCurrentStep(2) }]
        );
      } else {
        Alert.alert('Error', result.error || 'Failed to complete check');
      }
    } catch (error) {
      logger.error('Sex offender check failed', 'BackgroundCheck', { error });
      Alert.alert('Error', 'Failed to complete sex offender check');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleReferenceVerification = () => {
    setShowReferenceForm(true);
  };

  const handleAddReference = () => {
    if (!newReference.name || !newReference.email) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    setReferences(prev => [...prev, newReference]);
    setNewReference({ name: '', email: '', relationship: '', duration: '' });
    setShowReferenceForm(false);

    if (references.length >= 1) {
      // At least 2 references total
      setCompletedChecks(prev => [...prev, 'reference_verification']);
      Alert.alert(
        'References Added',
        'Reference verification emails have been sent. You will be notified when responses are received.',
        [{ text: 'OK', onPress: () => setCurrentStep(2) }]
      );
    }
  };

  const handleSocialMediaCheck = async () => {
    setIsProcessing(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1500));

      setCompletedChecks(prev => [...prev, 'social_media_check']);
      Alert.alert(
        'Social Media Check Submitted',
        'Your social media verification has been submitted for manual review. Results will be available within 24-48 hours.',
        [{ text: 'OK', onPress: () => setCurrentStep(2) }]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to submit social media check');
    } finally {
      setIsProcessing(false);
    }
  };

  const handlePublicRecordsSearch = async () => {
    setIsProcessing(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));

      setCompletedChecks(prev => [...prev, 'public_records_search']);
      Alert.alert(
        'Public Records Search Initiated',
        'Public records search has been initiated. Results will be available within 2-5 business days.',
        [{ text: 'OK', onPress: () => setCurrentStep(2) }]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to initiate public records search');
    } finally {
      setIsProcessing(false);
    }
  };

  const renderMethodSelection = () => (
    <View style={styles.stepContent}>
      <Text style={[styles.stepTitle, { color: theme.colors.text }]}>
        Zero-Cost Background Verification
      </Text>
      <Text style={[styles.stepDescription, { color: theme.colors.textSecondary }]}>
        Choose from our free verification options. Save hundreds compared to traditional services.
      </Text>

      {/* Cost Savings Banner */}
      <View style={[styles.savingsBanner, { backgroundColor: theme.colors.success + '15' }]}>
        <Feather name='dollar-sign' size={20} color={theme.colors.success} />
        <Text style={[styles.savingsText, { color: theme.colors.success }]}>
          Save $95+ per verification cycle with our zero-cost system
        </Text>
      </View>

      {/* Free Verification Methods */}
      <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
        Free Verification Options
      </Text>

      {verificationMethods.map(method => (
        <TouchableOpacity
          key={method.id}
          style={[styles.methodCard, { backgroundColor: theme.colors.surface }]}
          onPress={() => handleMethodSelection(method.id)}
        >
          <View style={styles.methodHeader}>
            <View style={[styles.methodIcon, { backgroundColor: theme.colors.primary + '15' }]}>
              <Feather name={method.icon as any} size={20} color={theme.colors.primary} />
            </View>
            <View style={styles.freeBadge}>
              <Text style={styles.freeBadgeText}>FREE</Text>
            </View>
          </View>

          <Text style={[styles.methodTitle, { color: theme.colors.text }]}>{method.title}</Text>
          <Text style={[styles.methodDescription, { color: theme.colors.textSecondary }]}>
            {method.description}
          </Text>

          <View style={styles.methodFooter}>
            <View style={styles.timeContainer}>
              <Feather name='clock' size={14} color={theme.colors.textSecondary} />
              <Text style={[styles.timeText, { color: theme.colors.textSecondary }]}>
                {method.time}
              </Text>
            </View>
            <Feather name='chevron-right' size={16} color={theme.colors.textSecondary} />
          </View>
        </TouchableOpacity>
      ))}

      {/* Premium Services (Coming Soon) */}
      <Text style={[styles.sectionTitle, { color: theme.colors.text, marginTop: 24 }]}>
        Premium Services (Coming Soon)
      </Text>

      {premiumServices.map(service => (
        <TouchableOpacity
          key={service.id}
          style={[styles.premiumCard, { backgroundColor: theme.colors.surface, opacity: 0.7 }]}
          onPress={() => handlePremiumServicePress(service)}
        >
          <View style={styles.methodHeader}>
            <View
              style={[styles.methodIcon, { backgroundColor: theme.colors.textSecondary + '15' }]}
            >
              <Feather name={service.icon as any} size={20} color={theme.colors.textSecondary} />
            </View>
            <View style={[styles.comingSoonBadge, { backgroundColor: theme.colors.warning }]}>
              <Text style={styles.comingSoonText}>COMING SOON</Text>
            </View>
          </View>

          <Text style={[styles.methodTitle, { color: theme.colors.textSecondary }]}>
            {service.title}
          </Text>
          <Text style={[styles.methodDescription, { color: theme.colors.textSecondary }]}>
            {service.description}
          </Text>

          <View style={styles.methodFooter}>
            <Text style={[styles.savingsLabel, { color: theme.colors.warning }]}>
              {service.savings}
            </Text>
            <Text style={[styles.originalCost, { color: theme.colors.textSecondary }]}>
              was {service.originalCost}
            </Text>
          </View>
        </TouchableOpacity>
      ))}

      <TouchableOpacity
        style={[styles.continueButton, { backgroundColor: theme.colors.primary }]}
        onPress={() => setCurrentStep(2)}
      >
        <Text style={styles.continueButtonText}>Review Completed Checks</Text>
      </TouchableOpacity>
    </View>
  );

  const renderVerificationProcess = () => {
    const method = verificationMethods.find(m => m.id === selectedMethod);
    if (!method) return null;

    return (
      <View style={styles.stepContent}>
        <Text style={[styles.stepTitle, { color: theme.colors.text }]}>{method.title}</Text>
        <Text style={[styles.stepDescription, { color: theme.colors.textSecondary }]}>
          {method.description}
        </Text>

        {selectedMethod === 'sex_offender_check' && (
          <View style={styles.processContainer}>
            <View style={styles.infoCard}>
              <Feather name='info' size={20} color={theme.colors.primary} />
              <Text style={[styles.infoText, { color: theme.colors.text }]}>
                This check searches the National Sex Offender Public Website (NSOPW) database. This
                is a free government service.
              </Text>
            </View>
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: theme.colors.primary }]}
              onPress={handleSexOffenderCheck}
              disabled={isProcessing}
            >
              {isProcessing ? (
                <ActivityIndicator color='white' />
              ) : (
                <Text style={styles.actionButtonText}>Run Sex Offender Check</Text>
              )}
            </TouchableOpacity>
          </View>
        )}

        {selectedMethod === 'reference_verification' && (
          <View style={styles.processContainer}>
            <View style={styles.infoCard}>
              <Feather name='info' size={20} color={theme.colors.primary} />
              <Text style={[styles.infoText, { color: theme.colors.text }]}>
                Add references who can vouch for your character and rental history. We'll send them
                a verification email.
              </Text>
            </View>

            {references.map((ref, index) => (
              <View
                key={index}
                style={[styles.referenceItem, { backgroundColor: theme.colors.surface }]}
              >
                <Text style={[styles.referenceName, { color: theme.colors.text }]}>{ref.name}</Text>
                <Text style={[styles.referenceEmail, { color: theme.colors.textSecondary }]}>
                  {ref.email}
                </Text>
              </View>
            ))}

            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: theme.colors.primary }]}
              onPress={handleReferenceVerification}
            >
              <Text style={styles.actionButtonText}>Add Reference</Text>
            </TouchableOpacity>
          </View>
        )}

        {selectedMethod === 'social_media_check' && (
          <View style={styles.processContainer}>
            <View style={styles.infoCard}>
              <Feather name='info' size={20} color={theme.colors.primary} />
              <Text style={[styles.infoText, { color: theme.colors.text }]}>
                Our team will manually review your public social media profiles to verify your
                identity and check for any concerning content.
              </Text>
            </View>
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: theme.colors.primary }]}
              onPress={handleSocialMediaCheck}
              disabled={isProcessing}
            >
              {isProcessing ? (
                <ActivityIndicator color='white' />
              ) : (
                <Text style={styles.actionButtonText}>Submit for Social Media Review</Text>
              )}
            </TouchableOpacity>
          </View>
        )}

        {selectedMethod === 'public_records_search' && (
          <View style={styles.processContainer}>
            <View style={styles.infoCard}>
              <Feather name='info' size={20} color={theme.colors.primary} />
              <Text style={[styles.infoText, { color: theme.colors.text }]}>
                We'll search available public records including court records, property records, and
                other government databases.
              </Text>
            </View>
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: theme.colors.primary }]}
              onPress={handlePublicRecordsSearch}
              disabled={isProcessing}
            >
              {isProcessing ? (
                <ActivityIndicator color='white' />
              ) : (
                <Text style={styles.actionButtonText}>Start Public Records Search</Text>
              )}
            </TouchableOpacity>
          </View>
        )}
      </View>
    );
  };

  const renderSummary = () => (
    <View style={styles.stepContent}>
      <Text style={[styles.stepTitle, { color: theme.colors.text }]}>Background Check Summary</Text>
      <Text style={[styles.stepDescription, { color: theme.colors.textSecondary }]}>
        Review your completed background verification checks.
      </Text>

      <View style={[styles.summaryCard, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.summaryTitle, { color: theme.colors.text }]}>
          Completed Verifications
        </Text>

        {completedChecks.length === 0 ? (
          <Text style={[styles.noChecksText, { color: theme.colors.textSecondary }]}>
            No background checks completed yet. Please select and complete verification methods.
          </Text>
        ) : (
          completedChecks.map(checkId => {
            const method = verificationMethods.find(m => m.id === checkId);
            if (!method) return null;

            return (
              <View key={checkId} style={styles.completedItem}>
                <View
                  style={[styles.completedIcon, { backgroundColor: theme.colors.success + '15' }]}
                >
                  <Feather name='check' size={16} color={theme.colors.success} />
                </View>
                <View style={styles.completedContent}>
                  <Text style={[styles.completedTitle, { color: theme.colors.text }]}>
                    {method.title}
                  </Text>
                  <Text style={[styles.completedStatus, { color: theme.colors.success }]}>
                    Completed Successfully
                  </Text>
                </View>
              </View>
            );
          })
        )}

        <View style={[styles.costSavingsCard, { backgroundColor: theme.colors.primary + '10' }]}>
          <Feather name='dollar-sign' size={20} color={theme.colors.primary} />
          <Text style={[styles.costSavingsText, { color: theme.colors.primary }]}>
            You saved $95+ by using our zero-cost verification system
          </Text>
        </View>

        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: theme.colors.primary }]}
          onPress={() => router.back()}
        >
          <Text style={styles.actionButtonText}>Return to Profile</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderReferenceForm = () => (
    <Modal visible={showReferenceForm} animationType='slide' presentationStyle='pageSheet'>
      <SafeAreaView style={[styles.modalContainer, { backgroundColor: theme.colors.background }]}>
        <View style={styles.modalHeader}>
          <TouchableOpacity onPress={() => setShowReferenceForm(false)}>
            <Text style={[styles.modalCancel, { color: theme.colors.textSecondary }]}>Cancel</Text>
          </TouchableOpacity>
          <Text style={[styles.modalTitle, { color: theme.colors.text }]}>Add Reference</Text>
          <TouchableOpacity onPress={handleAddReference}>
            <Text style={[styles.modalSave, { color: theme.colors.primary }]}>Add</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.modalContent}>
          <View style={styles.formGroup}>
            <Text style={[styles.formLabel, { color: theme.colors.text }]}>Full Name *</Text>
            <TextInput
              style={[
                styles.formInput,
                { backgroundColor: theme.colors.surface, color: theme.colors.text },
              ]}
              value={newReference.name}
              onChangeText={text => setNewReference(prev => ({ ...prev, name: text }))}
              placeholder="Enter reference's full name"
              placeholderTextColor={theme.colors.textSecondary}
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.formLabel, { color: theme.colors.text }]}>Email Address *</Text>
            <TextInput
              style={[
                styles.formInput,
                { backgroundColor: theme.colors.surface, color: theme.colors.text },
              ]}
              value={newReference.email}
              onChangeText={text => setNewReference(prev => ({ ...prev, email: text }))}
              placeholder="Enter reference's email"
              placeholderTextColor={theme.colors.textSecondary}
              keyboardType='email-address'
              autoCapitalize='none'
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.formLabel, { color: theme.colors.text }]}>Relationship</Text>
            <TextInput
              style={[
                styles.formInput,
                { backgroundColor: theme.colors.surface, color: theme.colors.text },
              ]}
              value={newReference.relationship}
              onChangeText={text => setNewReference(prev => ({ ...prev, relationship: text }))}
              placeholder='e.g., Previous roommate, landlord'
              placeholderTextColor={theme.colors.textSecondary}
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.formLabel, { color: theme.colors.text }]}>
              Duration of Relationship
            </Text>
            <TextInput
              style={[
                styles.formInput,
                { backgroundColor: theme.colors.surface, color: theme.colors.text },
              ]}
              value={newReference.duration}
              onChangeText={text => setNewReference(prev => ({ ...prev, duration: text }))}
              placeholder='e.g., 2 years, 6 months'
              placeholderTextColor={theme.colors.textSecondary}
            />
          </View>
        </ScrollView>
      </SafeAreaView>
    </Modal>
  );

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 0:
        return renderMethodSelection();
      case 1:
        return renderVerificationProcess();
      case 2:
        return renderSummary();
      default:
        return renderMethodSelection();
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Feather name='arrow-left' size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>Background Check</Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderCurrentStep()}
      </ScrollView>

      {renderReferenceForm()}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  headerRight: {
    width: 32,
  },
  content: {
    flex: 1,
  },
  stepContent: {
    padding: 16,
  },
  stepTitle: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 8,
  },
  stepDescription: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 24,
  },
  savingsBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 24,
  },
  savingsText: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  methodCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  premiumCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderStyle: 'dashed',
  },
  methodHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  methodIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  freeBadge: {
    backgroundColor: '#10B981',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  freeBadgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '700',
  },
  comingSoonBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  comingSoonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '700',
  },
  methodTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  methodDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  methodFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeText: {
    fontSize: 12,
    marginLeft: 4,
  },
  savingsLabel: {
    fontSize: 12,
    fontWeight: '600',
  },
  originalCost: {
    fontSize: 12,
    textDecorationLine: 'line-through',
  },
  continueButton: {
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 24,
  },
  continueButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  processContainer: {
    marginTop: 16,
  },
  infoCard: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: '#EBF8FF',
    borderRadius: 8,
    marginBottom: 16,
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    lineHeight: 20,
    marginLeft: 12,
  },
  actionButton: {
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  actionButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  referenceItem: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  referenceName: {
    fontSize: 16,
    fontWeight: '600',
  },
  referenceEmail: {
    fontSize: 14,
    marginTop: 2,
  },
  summaryCard: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  noChecksText: {
    fontSize: 14,
    textAlign: 'center',
    marginVertical: 20,
  },
  completedItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  completedIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  completedContent: {
    marginLeft: 12,
    flex: 1,
  },
  completedTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  completedStatus: {
    fontSize: 14,
    marginTop: 2,
  },
  costSavingsCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginVertical: 16,
  },
  costSavingsText: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalCancel: {
    fontSize: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  modalSave: {
    fontSize: 16,
    fontWeight: '600',
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  formLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  formInput: {
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    fontSize: 16,
  },
});
