import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  TextInput,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Feather } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useTheme } from '@design-system';
import { useAuth } from '@context/AuthContext';
import { getSupabaseClient } from '@services/supabaseService';
import { logger } from '@utils/logger';

interface EmailVerificationData {
  id?: string;
  user_id: string;
  email: string;
  is_verified: boolean;
  verification_code?: string;
  verification_sent_at?: string;
  verified_at?: string;
  created_at: string;
  updated_at: string;
}

export default function EmailVerificationScreen() {
  const theme = useTheme();
  const router = useRouter();
  const { state } = useAuth();
  const supabase = getSupabaseClient();

  const [isLoading, setIsLoading] = useState(false);
  const [isSendingCode, setIsSendingCode] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const [currentStep, setCurrentStep] = useState(0); // 0: Check status, 1: Send code, 2: Verify code, 3: Complete

  const [emailData, setEmailData] = useState<EmailVerificationData | null>(null);
  const [verificationCode, setVerificationCode] = useState('');
  const [timeLeft, setTimeLeft] = useState(0);

  const steps = [
    { id: 0, title: 'Email Status', description: 'Check your email verification status' },
    { id: 1, title: 'Send Code', description: 'Send verification code to your email' },
    { id: 2, title: 'Enter Code', description: 'Enter the verification code' },
    { id: 3, title: 'Complete', description: 'Email verification completed' },
  ];

  useEffect(() => {
    loadEmailVerificationStatus();
  }, []);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (timeLeft > 0) {
      interval = setInterval(() => {
        setTimeLeft(time => time - 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [timeLeft]);

  const loadEmailVerificationStatus = async () => {
    if (!state.user?.id) return;

    try {
      setIsLoading(true);
      logger.info('Loading email verification status', { userId: state.user.id });

      // Check if email is already verified in auth
      const authEmailVerified = !!state.user.email_confirmed_at;

      if (authEmailVerified) {
        setCurrentStep(3);
        setEmailData({
          user_id: state.user.id,
          email: state.user.email || '',
          is_verified: true,
          verified_at: state.user.email_confirmed_at,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        });
        return;
      }

      // Try to check verification record in database (gracefully handle if table doesn't exist)
      try {
        const { data, error } = await supabase
          .from('email_verifications')
          .select('*')
          .eq('user_id', state.user.id)
          .single();

        if (error && error.code !== 'PGRST116') {
          // If it's not a "not found" error, check if it's a table missing error
          if (error.message?.includes('does not exist')) {
            logger.info('Email verifications table does not exist, using auth-only verification');
            setCurrentStep(1);
            return;
          }
          throw error;
        }

        if (data) {
          setEmailData(data);
          if (data.is_verified) {
            setCurrentStep(3);
          } else if (data.verification_sent_at) {
            setCurrentStep(2);
            // Calculate time left for resend
            const sentAt = new Date(data.verification_sent_at).getTime();
            const now = new Date().getTime();
            const elapsed = Math.floor((now - sentAt) / 1000);
            const remaining = Math.max(0, 300 - elapsed); // 5 minute cooldown
            setTimeLeft(remaining);
          } else {
            setCurrentStep(1);
          }
        } else {
          setCurrentStep(1);
        }
      } catch (dbError) {
        // If database operations fail, continue with auth-only verification
        logger.info('Database verification tracking unavailable, using auth-only mode', {
          error: dbError,
        });
        setCurrentStep(1);
      }
    } catch (error) {
      logger.error('Error loading email verification status', error as Error);
      // Don't show alert for database errors, just use fallback mode
      setCurrentStep(1);
    } finally {
      setIsLoading(false);
    }
  };

  const sendVerificationCode = async () => {
    if (!state.user?.email) {
      Alert.alert('Error', 'No email address found');
      return;
    }

    try {
      setIsSendingCode(true);
      logger.info('Sending email verification code', { email: state.user.email });

      // Send verification email through Supabase Auth
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: state.user.email,
      });

      if (error) {
        throw error;
      }

      // Record the verification attempt (gracefully handle if table doesn't exist)
      const verificationData = {
        user_id: state.user.id,
        email: state.user.email,
        is_verified: false,
        verification_sent_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      try {
        const { error: dbError } = await supabase
          .from('email_verifications')
          .upsert(verificationData, { onConflict: 'user_id' });

        if (dbError) {
          if (dbError.message?.includes('does not exist')) {
            logger.info('Email verifications table does not exist, skipping database record');
          } else {
            logger.error('Error saving verification record', dbError);
          }
        }
      } catch (dbError) {
        logger.info('Database verification tracking unavailable, continuing without record', {
          error: dbError,
        });
      }

      setEmailData(prev => ({ ...prev, ...verificationData }) as EmailVerificationData);
      setCurrentStep(2);
      setTimeLeft(300); // 5 minute cooldown

      Alert.alert(
        'Verification Email Sent!',
        'Please check your email and click the verification link.',
        [{ text: 'OK' }]
      );
    } catch (error) {
      logger.error('Error sending verification email', error as Error);
      Alert.alert('Error', 'Failed to send verification email. Please try again.');
    } finally {
      setIsSendingCode(false);
    }
  };

  const resendVerificationCode = async () => {
    if (timeLeft > 0) {
      Alert.alert('Please Wait', `You can resend the code in ${timeLeft} seconds`);
      return;
    }
    await sendVerificationCode();
  };

  const checkVerificationStatus = async () => {
    try {
      setIsVerifying(true);

      // Refresh the user session to check if email is verified
      const {
        data: { user },
        error,
      } = await supabase.auth.getUser();

      if (error) {
        throw error;
      }

      if (user?.email_confirmed_at) {
        // Update database record (gracefully handle if table doesn't exist)
        try {
          await supabase.from('email_verifications').upsert(
            {
              user_id: user.id,
              email: user.email || '',
              is_verified: true,
              verified_at: user.email_confirmed_at,
              updated_at: new Date().toISOString(),
            },
            { onConflict: 'user_id' }
          );
        } catch (dbError) {
          logger.info(
            'Database verification tracking unavailable, continuing without record update',
            { error: dbError }
          );
        }

        setEmailData(
          prev =>
            ({
              ...prev,
              is_verified: true,
              verified_at: user.email_confirmed_at,
            }) as EmailVerificationData
        );

        setCurrentStep(3);

        Alert.alert('Email Verified!', 'Your email address has been successfully verified.', [
          { text: 'Continue' },
        ]);
      } else {
        Alert.alert(
          'Not Verified Yet',
          'Please check your email and click the verification link before checking again.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      logger.error('Error checking verification status', error as Error);
      Alert.alert('Error', 'Failed to check verification status');
    } finally {
      setIsVerifying(false);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const renderStepIndicator = () => (
    <View style={styles.stepIndicator}>
      {steps.map((step, index) => (
        <View key={step.id} style={styles.stepContainer}>
          <View
            style={[
              styles.stepCircle,
              {
                backgroundColor:
                  currentStep >= step.id ? theme.colors.primary : theme.colors.surface,
              },
            ]}
          >
            {currentStep > step.id ? (
              <Feather name='check' size={16} color='#fff' />
            ) : (
              <Text
                style={[
                  styles.stepNumber,
                  { color: currentStep >= step.id ? '#fff' : theme.colors.textSecondary },
                ]}
              >
                {index + 1}
              </Text>
            )}
          </View>
          {index < steps.length - 1 && (
            <View
              style={[
                styles.stepConnector,
                {
                  backgroundColor:
                    currentStep > step.id ? theme.colors.primary : theme.colors.surface,
                },
              ]}
            />
          )}
        </View>
      ))}
    </View>
  );

  const renderEmailStatus = () => (
    <View style={styles.stepContent}>
      <Text style={[styles.stepTitle, { color: theme.colors.text }]}>
        Email Verification Status
      </Text>
      <Text style={[styles.stepDescription, { color: theme.colors.textSecondary }]}>
        Verify your email address to increase account security and trustworthiness
      </Text>

      <View style={[styles.emailCard, { backgroundColor: theme.colors.surface }]}>
        <Feather name='mail' size={24} color={theme.colors.primary} />
        <View style={styles.emailInfo}>
          <Text style={[styles.emailAddress, { color: theme.colors.text }]}>
            {state.user?.email || 'No email address'}
          </Text>
          <Text style={[styles.emailStatusText, { color: theme.colors.textSecondary }]}>
            {emailData?.is_verified ? 'Verified' : 'Not verified'}
          </Text>
        </View>
        {emailData?.is_verified ? (
          <Feather name='check-circle' size={20} color={theme.colors.success} />
        ) : (
          <Feather name='x-circle' size={20} color={theme.colors.error} />
        )}
      </View>

      {!emailData?.is_verified && (
        <TouchableOpacity
          style={[styles.primaryButton, { backgroundColor: theme.colors.primary }]}
          onPress={() => setCurrentStep(1)}
        >
          <Text style={styles.primaryButtonText}>Start Verification</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  const renderSendCode = () => (
    <View style={styles.stepContent}>
      <Text style={[styles.stepTitle, { color: theme.colors.text }]}>Send Verification Email</Text>
      <Text style={[styles.stepDescription, { color: theme.colors.textSecondary }]}>
        We'll send a verification link to your email address
      </Text>

      <View style={[styles.infoCard, { backgroundColor: theme.colors.surface }]}>
        <Feather name='info' size={20} color={theme.colors.primary} />
        <View style={styles.infoContent}>
          <Text style={[styles.infoTitle, { color: theme.colors.text }]}>What happens next?</Text>
          <Text style={[styles.infoText, { color: theme.colors.textSecondary }]}>
            • We'll send a verification link to {state.user?.email}
            {'\n'}• Check your inbox and spam folder{'\n'}• Click the verification link to confirm
            your email{'\n'}• Return here to complete the process{'\n'}
            {'\n'}
            📧 Using simplified email verification mode
          </Text>
        </View>
      </View>

      <TouchableOpacity
        style={[styles.primaryButton, { backgroundColor: theme.colors.primary }]}
        onPress={sendVerificationCode}
        disabled={isSendingCode}
      >
        {isSendingCode ? (
          <ActivityIndicator color='#fff' size='small' />
        ) : (
          <Text style={styles.primaryButtonText}>Send Verification Email</Text>
        )}
      </TouchableOpacity>
    </View>
  );

  const renderVerifyCode = () => (
    <View style={styles.stepContent}>
      <Text style={[styles.stepTitle, { color: theme.colors.text }]}>Check Your Email</Text>
      <Text style={[styles.stepDescription, { color: theme.colors.textSecondary }]}>
        We've sent a verification link to {state.user?.email}
      </Text>

      <View style={[styles.instructionCard, { backgroundColor: theme.colors.surface }]}>
        <Feather name='mail' size={48} color={theme.colors.primary} />
        <Text style={[styles.instructionTitle, { color: theme.colors.text }]}>
          Check Your Inbox
        </Text>
        <Text style={[styles.instructionText, { color: theme.colors.textSecondary }]}>
          Click the verification link in the email we sent you. Don't forget to check your spam
          folder!
        </Text>
      </View>

      <TouchableOpacity
        style={[styles.primaryButton, { backgroundColor: theme.colors.primary }]}
        onPress={checkVerificationStatus}
        disabled={isVerifying}
      >
        {isVerifying ? (
          <ActivityIndicator color='#fff' size='small' />
        ) : (
          <Text style={styles.primaryButtonText}>I've Clicked the Link</Text>
        )}
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.secondaryButton, { borderColor: theme.colors.border }]}
        onPress={resendVerificationCode}
        disabled={timeLeft > 0}
      >
        <Text style={[styles.secondaryButtonText, { color: theme.colors.text }]}>
          {timeLeft > 0 ? `Resend in ${formatTime(timeLeft)}` : 'Resend Email'}
        </Text>
      </TouchableOpacity>
    </View>
  );

  const renderComplete = () => (
    <View style={styles.stepContent}>
      <Text style={[styles.stepTitle, { color: theme.colors.text }]}>Email Verified!</Text>
      <Text style={[styles.stepDescription, { color: theme.colors.textSecondary }]}>
        Your email address has been successfully verified
      </Text>

      <View style={[styles.successCard, { backgroundColor: theme.colors.surface }]}>
        <Feather name='check-circle' size={48} color={theme.colors.success} />
        <Text style={[styles.successTitle, { color: theme.colors.text }]}>
          Verification Complete
        </Text>
        <Text style={[styles.successDescription, { color: theme.colors.textSecondary }]}>
          Your email {state.user?.email} is now verified and secure
        </Text>
      </View>

      <View style={[styles.benefitsCard, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.benefitsTitle, { color: theme.colors.text }]}>Benefits Unlocked</Text>

        <View style={styles.benefitItem}>
          <Feather name='shield' size={16} color={theme.colors.success} />
          <Text style={[styles.benefitText, { color: theme.colors.text }]}>
            Enhanced account security
          </Text>
        </View>

        <View style={styles.benefitItem}>
          <Feather name='trending-up' size={16} color={theme.colors.success} />
          <Text style={[styles.benefitText, { color: theme.colors.text }]}>
            Increased profile trustworthiness
          </Text>
        </View>

        <View style={styles.benefitItem}>
          <Feather name='bell' size={16} color={theme.colors.success} />
          <Text style={[styles.benefitText, { color: theme.colors.text }]}>
            Email notifications enabled
          </Text>
        </View>
      </View>

      <TouchableOpacity
        style={[styles.primaryButton, { backgroundColor: theme.colors.primary }]}
        onPress={() => router.back()}
      >
        <Text style={styles.primaryButtonText}>Complete Verification</Text>
      </TouchableOpacity>
    </View>
  );

  const renderCurrentStep = () => {
    if (isLoading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size='large' color={theme.colors.primary} />
          <Text style={[styles.loadingText, { color: theme.colors.textSecondary }]}>
            Loading verification status...
          </Text>
        </View>
      );
    }

    switch (currentStep) {
      case 0:
        return renderEmailStatus();
      case 1:
        return renderSendCode();
      case 2:
        return renderVerifyCode();
      case 3:
        return renderComplete();
      default:
        return renderEmailStatus();
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <Feather name='arrow-left' size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>Email Verification</Text>
        <View style={{ width: 24 }} />
      </View>

      {renderStepIndicator()}

      <KeyboardAvoidingView
        style={styles.content}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView style={styles.scrollContent} showsVerticalScrollIndicator={false}>
          {renderCurrentStep()}
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  stepIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  stepContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  stepCircle: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  stepNumber: {
    fontSize: 14,
    fontWeight: '600',
  },
  stepConnector: {
    width: 30,
    height: 2,
    marginHorizontal: 8,
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 20,
  },
  stepContent: {
    paddingBottom: 40,
  },
  stepTitle: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 8,
  },
  stepDescription: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 32,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  emailCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
  },
  emailInfo: {
    flex: 1,
    marginLeft: 12,
  },
  emailAddress: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  emailStatusText: {
    fontSize: 14,
  },
  infoCard: {
    flexDirection: 'row',
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
  },
  infoContent: {
    flex: 1,
    marginLeft: 12,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    lineHeight: 20,
  },
  instructionCard: {
    alignItems: 'center',
    padding: 24,
    borderRadius: 12,
    marginBottom: 24,
  },
  instructionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  instructionText: {
    fontSize: 14,
    lineHeight: 20,
    textAlign: 'center',
  },
  successCard: {
    alignItems: 'center',
    padding: 24,
    borderRadius: 12,
    marginBottom: 24,
  },
  successTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  successDescription: {
    fontSize: 14,
    lineHeight: 20,
    textAlign: 'center',
  },
  benefitsCard: {
    padding: 20,
    borderRadius: 12,
    marginBottom: 24,
  },
  benefitsTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  benefitText: {
    marginLeft: 12,
    fontSize: 14,
  },
  primaryButton: {
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 16,
  },
  primaryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButton: {
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    borderWidth: 1,
  },
  secondaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});
