import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, StyleSheet, TouchableOpacity, Alert, TextInput, ActivityIndicator, SafeAreaView, Modal, } from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useTheme, type Theme } from '@design-system';
import { useColorScheme } from 'react-native';
import { logger } from '@utils/logger';

interface Reference {
  id: string;
  name: string;
  email: string;
  phone: string;
  relationship: string;
  duration: string;
  status: 'pending' | 'sent' | 'responded' | 'verified' | 'failed';
  notes?: string;
}

interface ReferenceTemplate {
  id: string;
  title: string;
  description: string;
  icon: string;
  relationship: string;
}

export default function ReferencesVerificationScreen() {
  const theme = useTheme();
  const router = useRouter();
  const styles = createStyles(theme);
  
  const [references, setReferences] = useState<Reference[]>([]);
  const [showAddModal, setShowAddModal] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [newReference, setNewReference] = useState({
    name: '',
    email: '',
    phone: '',
    relationship: '',
    duration: '',
    notes: '',
  });

  const referenceTemplates: ReferenceTemplate[] = [
    {
      id: 'previous_roommate',
      title: 'Previous Roommate',
      description: 'Someone you\'ve lived with before',
      icon: 'users',
      relationship: 'Previous Roommate',
    },
    {
      id: 'landlord',
      title: 'Previous Landlord',
      description: 'A landlord or property manager',
      icon: 'home',
      relationship: 'Previous Landlord',
    },
    {
      id: 'employer',
      title: 'Employer/Supervisor',
      description: 'Current or former employer',
      icon: 'briefcase',
      relationship: 'Employer/Supervisor',
    },
    {
      id: 'colleague',
      title: 'Work Colleague',
      description: 'Someone you\'ve worked with',
      icon: 'users',
      relationship: 'Work Colleague',
    },
    {
      id: 'friend',
      title: 'Personal Reference',
      description: 'Close friend or family member',
      icon: 'heart',
      relationship: 'Personal Reference',
    },
    {
      id: 'professor',
      title: 'Professor/Teacher',
      description: 'Academic reference',
      icon: 'book',
      relationship: 'Professor/Teacher',
    },
  ];

  const durationOptions = [
    '3-6 months',
    '6-12 months',
    '1-2 years',
    '2-3 years',
    '3+ years',
  ];

  const handleTemplateSelect = (templateId: string) => {
  const template = referenceTemplates.find(t => t.id === templateId);
    if (template) {
      setSelectedTemplate(templateId);
      setNewReference(prev => ({
        ...prev,
        relationship: template.relationship,
      }));
    }
  };

  const validateReference = () => {
  if (!newReference.name.trim()) {
      Alert.alert('Missing Information', 'Please enter the reference name');
      return false;
    }
    if (!newReference.email.trim()) {
      Alert.alert('Missing Information', 'Please enter the reference email');
      return false;
    }
    if (!newReference.relationship.trim()) {
      Alert.alert('Missing Information', 'Please select a relationship type');
      return false;
    }
    if (!newReference.duration.trim()) {
      Alert.alert('Missing Information', 'Please select duration');
      return false;
    }
    
    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(newReference.email)) {
      Alert.alert('Invalid Email', 'Please enter a valid email address');
      return false;
    }

    return true;
  };

  const handleAddReference = async () => {
  if (!validateReference()) return;

    setIsLoading(true);
    try {
      const reference: Reference = {
        id: Date.now().toString(),
        name: newReference.name.trim(),
        email: newReference.email.trim().toLowerCase(),
        phone: newReference.phone.trim(),
        relationship: newReference.relationship,
        duration: newReference.duration,
        status: 'pending',
        notes: newReference.notes.trim(),
      };

      setReferences(prev => [...prev, reference]);
      setNewReference({
        name: '',
        email: '',
        phone: '',
        relationship: '',
        duration: '',
        notes: '',
      });
      setSelectedTemplate('');
      setShowAddModal(false);
      
      logger.info('Reference added', { referenceId: reference.id, relationship: reference.relationship });
    } catch (error) {
      logger.error('Failed to add reference', error as Error);
      Alert.alert('Error', 'Failed to add reference. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSendRequest = async (referenceId: string) => {
  setIsLoading(true);
    try {
      // Simulate sending reference request
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setReferences(prev => prev.map(ref => {
        return ref.id === referenceId ? { ...ref, status: 'sent' } : ref;
      }));
      
      Alert.alert(
        'Request Sent',
        'Your reference request has been sent successfully. We\'ll notify you when they respond.'
      );
      
      logger.info('Reference request sent', { referenceId });
    } catch (error) {
      logger.error('Failed to send reference request', error as Error);
      Alert.alert('Error', 'Failed to send reference request. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemoveReference = (referenceId: string) => {
  Alert.alert(
      'Remove Reference',
      'Are you sure you want to remove this reference?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => {
  setReferences(prev => prev.filter(ref => ref.id !== referenceId));
            logger.info('Reference removed', { referenceId });
          },
        },
      ]
    );
  };

  const getStatusColor = (status: Reference['status']) => {
  switch (status) {
      case 'pending':
        return theme.colors.textSecondary;
      case 'sent':
        return theme.colors.warning;
      case 'responded':
        return theme.colors.info;
      case 'verified':
        return theme.colors.success;
      case 'failed':
        return theme.colors.error;
      default:
        return theme.colors.textSecondary;
    }
  };

  const getStatusText = (status: Reference['status']) => {
  switch (status) {
      case 'pending':
        return 'Not Sent';
      case 'sent':
        return 'Request Sent';
      case 'responded':
        return 'Response Received';
      case 'verified':
        return 'Verified';
      case 'failed':
        return 'Failed';
      default:
        return 'Unknown';
    }
  };

  const renderReference = (reference: Reference) => (
    <View key={reference.id} style={[styles.referenceCard, { backgroundColor: theme.colors.surface }]}>
      <View style={styles.referenceHeader}>
        <View style={styles.referenceInfo}>
          <Text style={[styles.referenceName, { color: theme.colors.text }]}>
            {reference.name}
          </Text>
          <Text style={[styles.referenceRelationship, { color: theme.colors.textSecondary }]}>
            {reference.relationship} • {reference.duration}
          </Text>
        </View>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(reference.status) }]}>
          <Text style={styles.statusText}>{getStatusText(reference.status)}</Text>
        </View>
      </View>

      <View style={styles.referenceDetails}>
        <View style={styles.contactItem}>
          <Feather name="mail" size={14} color={theme.colors.textSecondary} />
          <Text style={[styles.contactText, { color: theme.colors.textSecondary }]}>
            {reference.email}
          </Text>
        </View>
        {reference.phone && (
          <View style={styles.contactItem}>
            <Feather name="phone" size={14} color={theme.colors.textSecondary} />
            <Text style={[styles.contactText, { color: theme.colors.textSecondary }]}>
              {reference.phone}
            </Text>
          </View>
        )}
        {reference.notes && (
          <View style={styles.contactItem}>
            <Feather name="message-circle" size={14} color={theme.colors.textSecondary} />
            <Text style={[styles.contactText, { color: theme.colors.textSecondary }]}>
              {reference.notes}
            </Text>
          </View>
        )}
      </View>

      <View style={styles.referenceActions}>
        {reference.status === 'pending' && (
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.colors.primary }]}
            onPress={() => handleSendRequest(reference.id)} disabled={isLoading}
          >
            <Feather name="send" size={16} color={theme.colors.surface} />
            <Text style={styles.actionButtonText}>Send Request</Text>
          </TouchableOpacity>
        )}
        
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: theme.colors.error }]}
          onPress={() => handleRemoveReference(reference.id)}
        >
          <Feather name="trash-2" size={16} color={theme.colors.surface} />
          <Text style={styles.actionButtonText}>Remove</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderAddReferenceModal = () => (
    <Modal visible={showAddModal} animationType="slide" transparent>
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContent, { backgroundColor: theme.colors.background }]}>
          <View style={styles.modalHeader}>
            <Text style={[styles.modalTitle, { color: theme.colors.text }]}>
              Add Reference
            </Text>
            <TouchableOpacity onPress={() => setShowAddModal(false)}>
              <Feather name="x" size={24} color={theme.colors.text} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalBody} showsVerticalScrollIndicator={false}>
            {/* Relationship Type Selection */}
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              Relationship Type
            </Text>
            <View style={styles.templateGrid}>
              {referenceTemplates.map((template) => (
                <TouchableOpacity key={template.id} style={[
                    styles.templateCard,
                    {
                      backgroundColor: selectedTemplate === template.id ? theme.colors.primary : theme.colors.surface,
                      borderColor: selectedTemplate === template.id ? theme.colors.primary : theme.colors.border,
                    },
                  ]}
                  onPress={() => handleTemplateSelect(template.id)}
                >
                  <Feather name={template.icon as any} size={20} color={selectedTemplate === template.id ? theme.colors.surface : theme.colors.textSecondary}
                  />
                  <Text
                    style={[
                      styles.templateTitle,
                      { color: selectedTemplate === template.id ? theme.colors.surface : theme.colors.text },
                    ]}
                  >
                    {template.title}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            {/* Reference Details */}
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              Reference Details
            </Text>
            
            <Text style={[styles.inputLabel, { color: theme.colors.text }]}>
              Full Name *
            </Text>
            <TextInput
              style={[styles.textInput, { backgroundColor: theme.colors.surface, color: theme.colors.text }]}
              placeholder="Enter reference name"
              placeholderTextColor={theme.colors.textSecondary} value={newReference.name} onChangeText={(text) => setNewReference(prev => ({ ...prev, name: text }))}
            />

            <Text style={[styles.inputLabel, { color: theme.colors.text }]}>
              Email Address *
            </Text>
            <TextInput
              style={[styles.textInput, { backgroundColor: theme.colors.surface, color: theme.colors.text }]}
              placeholder="Enter email address"
              placeholderTextColor={theme.colors.textSecondary} value={newReference.email} onChangeText={(text) => setNewReference(prev => ({ ...prev, email: text }))}
              keyboardType="email-address"
              autoCapitalize="none"
            />

            <Text style={[styles.inputLabel, { color: theme.colors.text }]}>
              Phone Number (Optional)
            </Text>
            <TextInput
              style={[styles.textInput, { backgroundColor: theme.colors.surface, color: theme.colors.text }]}
              placeholder="Enter phone number"
              placeholderTextColor={theme.colors.textSecondary} value={newReference.phone} onChangeText={(text) => setNewReference(prev => ({ ...prev, phone: text }))}
              keyboardType="phone-pad"
            />

            <Text style={[styles.inputLabel, { color: theme.colors.text }]}>
              Duration of Relationship *
            </Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.durationList}>
              {durationOptions.map((duration) => (
                <TouchableOpacity key={duration} style={[
                    styles.durationOption,
                    {
                      backgroundColor: newReference.duration === duration ? theme.colors.primary : theme.colors.surface,
                      borderColor: newReference.duration === duration ? theme.colors.primary : theme.colors.border,
                    },
                  ]}
                  onPress={() => setNewReference(prev => ({ ...prev, duration }))}
                >
                  <Text
                    style={[
                      styles.durationText,
                      { color: newReference.duration === duration ? theme.colors.surface : theme.colors.text },
                    ]}
                  >
                    {duration}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>

            <Text style={[styles.inputLabel, { color: theme.colors.text }]}>
              Additional Notes (Optional)
            </Text>
            <TextInput
              style={[styles.textInput, styles.notesInput, { backgroundColor: theme.colors.surface, color: theme.colors.text }]}
              placeholder="Any additional context about this reference..."
              placeholderTextColor={theme.colors.textSecondary} value={newReference.notes} onChangeText={(text) => setNewReference(prev => ({ ...prev, notes: text }))}
              multiline
              numberOfLines={3}
            />

            <TouchableOpacity
              style={[
                styles.addButton,
                { 
                  backgroundColor: newReference.name && newReference.email && newReference.relationship && newReference.duration
                    ? theme.colors.primary 
                    : theme.colors.backgroundSecondary,
                },
                isLoading && styles.addButtonDisabled,
              ]}
              onPress={handleAddReference} disabled={!newReference.name || !newReference.email || !newReference.relationship || !newReference.duration || isLoading}
            >
              {isLoading ? (
                <ActivityIndicator color={theme.colors.surface} />
              ) : (
                <Text style={styles.addButtonText}>Add Reference</Text>
              )}
            </TouchableOpacity>
          </ScrollView>
        </View>
      </View>
    </Modal>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <Feather name="arrow-left" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>References</Text>
        <TouchableOpacity onPress={() => setShowAddModal(true)}>
          <Feather name="plus" size={24} color={theme.colors.primary} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.stepContent}>
          <Text style={[styles.stepTitle, { color: theme.colors.text }]}>
            Reference Verification
          </Text>
          <Text style={[styles.stepDescription, { color: theme.colors.textSecondary }]}>
            Add references who can vouch for your character and reliability as a roommate.
          </Text>

          {references.length === 0 ? (
            <View style={[styles.emptyState, { backgroundColor: theme.colors.backgroundSecondary }]}>
              <Feather name="users" size={48} color={theme.colors.textSecondary} />
              <Text style={[styles.emptyTitle, { color: theme.colors.text }]}>
                No References Added
              </Text>
              <Text style={[styles.emptyDescription, { color: theme.colors.textSecondary }]}>
                Add references from previous roommates, landlords, or employers to build trust with potential roommates.
              </Text>
              <TouchableOpacity
                style={[styles.addFirstButton, { backgroundColor: theme.colors.primary }]}
                onPress={() => setShowAddModal(true)}
              >
                <Feather name="plus" size={20} color="#fff" />
                <Text style={styles.addFirstButtonText}>Add Your First Reference</Text>
              </TouchableOpacity>
            </View>
          ) : (
            <View style={styles.referencesList}>
              <View style={[styles.statsCard, { backgroundColor: theme.colors.surface }]}>
                <View style={styles.statItem}>
                  <Text style={[styles.statNumber, { color: theme.colors.primary }]}>
                    {references.length}
                  </Text>
                  <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
                    Total References
                  </Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={[styles.statNumber, { color: theme.colors.success }]}>
                    {references.filter(ref => ref.status === 'verified').length}
                  </Text>
                  <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
                    Verified
                  </Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={[styles.statNumber, { color: theme.colors.warning }]}>
                    {references.filter(ref => ref.status === 'pending' || ref.status === 'sent').length}
                  </Text>
                  <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
                    Pending
                  </Text>
                </View>
              </View>

              {references.map(renderReference)}
            </View>
          )}

          <View style={[styles.tipsCard, { backgroundColor: theme.colors.backgroundSecondary }]}>
            <Feather name="info" size={16} color={theme.colors.primary} />
            <View style={styles.tipsContent}>
              <Text style={[styles.tipsTitle, { color: theme.colors.text }]}>
                Reference Tips:
              </Text>
              <Text style={[styles.tipsText, { color: theme.colors.textSecondary }]}>
                • Choose references who know you well and can speak to your reliability
              </Text>
              <Text style={[styles.tipsText, { color: theme.colors.textSecondary }]}>
                • Reach out to them first to let them know a request is coming
              </Text>
              <Text style={[styles.tipsText, { color: theme.colors.textSecondary }]}>
                • Previous roommates and landlords are especially valuable
              </Text>
              <Text style={[styles.tipsText, { color: theme.colors.textSecondary }]}>
                • Include a mix of personal and professional references
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>

      {renderAddReferenceModal()}
    </SafeAreaView>
  );
}

const createStyles = (theme: Theme) => StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  stepContent: {
    paddingBottom: 40,
  },
  stepTitle: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 8,
  },
  stepDescription: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 32,
  },
  emptyState: {
    alignItems: 'center',
    padding: 40,
    borderRadius: 16,
    marginBottom: 24,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  addFirstButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 12,
  },
  addFirstButtonText: {
    color: theme.colors.surface,
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  referencesList: {
    marginBottom: 24,
  },
  statsCard: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 20,
    borderRadius: 16,
    marginBottom: 24,
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
  referenceCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  referenceHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  referenceInfo: {
    flex: 1,
  },
  referenceName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  referenceRelationship: {
    fontSize: 14,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
    color: theme.colors.surface,
  },
  referenceDetails: {
    marginBottom: 16,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  contactText: {
    fontSize: 14,
    marginLeft: 8,
    flex: 1,
  },
  referenceActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
  },
  actionButtonText: {
    color: theme.colors.surface,
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 4,
  },
  tipsCard: {
    flexDirection: 'row',
    padding: 16,
    borderRadius: 12,
  },
  tipsContent: {
    marginLeft: 12,
    flex: 1,
  },
  tipsTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  tipsText: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingTop: 20,
    maxHeight: '90%',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  modalBody: {
    paddingHorizontal: 20,
    paddingBottom: 40,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
    marginTop: 8,
  },
  templateGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 24,
  },
  templateCard: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  templateTitle: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
    marginTop: 16,
  },
  textInput: {
    padding: 16,
    borderRadius: 12,
    fontSize: 16,
    marginBottom: 8,
  },
  notesInput: {
    height: 80,
    textAlignVertical: 'top',
  },
  durationList: {
    marginBottom: 8,
  },
  durationOption: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    borderWidth: 1,
    marginRight: 8,
  },
  durationText: {
    fontSize: 14,
    fontWeight: '500',
  },
  addButton: {
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginTop: 24,
  },
  addButtonDisabled: {
    opacity: 0.6,
  },
  addButtonText: {
    color: theme.colors.surface,
    fontSize: 16,
    fontWeight: '600',
  },
}); 