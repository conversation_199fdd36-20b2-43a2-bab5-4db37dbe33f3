
-- =====================================================
-- SIMPLIFIED AUTH FLOW MIGRATION
-- =====================================================

-- Create profiles table if it doesn't exist
CREATE TABLE IF NOT EXISTS profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT,
    username TEXT UNIQUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add simplified authentication flow columns
DO $$ 
BEGIN
    -- Add columns only if they don't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'auth_flow_version') THEN
        ALTER TABLE profiles ADD COLUMN auth_flow_version TEXT DEFAULT 'simplified_v1';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'verification_level') THEN
        ALTER TABLE profiles ADD COLUMN verification_level INTEGER DEFAULT 0;
    END IF;
    
    -- Step 1: Phone Verification & Basic Info
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'phone') THEN
        ALTER TABLE profiles ADD COLUMN phone TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'phone_verified') THEN
        ALTER TABLE profiles ADD COLUMN phone_verified BOOLEAN DEFAULT FALSE;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'first_name') THEN
        ALTER TABLE profiles ADD COLUMN first_name TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'last_name') THEN
        ALTER TABLE profiles ADD COLUMN last_name TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'role') THEN
        ALTER TABLE profiles ADD COLUMN role TEXT CHECK (role IN ('roommate_seeker', 'property_owner', 'service_provider'));
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'location') THEN
        ALTER TABLE profiles ADD COLUMN location TEXT;
    END IF;
    
    -- Step 2: Profile Setup
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'profile_photo_url') THEN
        ALTER TABLE profiles ADD COLUMN profile_photo_url TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'bio') THEN
        ALTER TABLE profiles ADD COLUMN bio TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'preferences') THEN
        ALTER TABLE profiles ADD COLUMN preferences JSONB DEFAULT '{}';
    END IF;
    
    -- Step 3: ID Verification (Manual Review)
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'id_verification_status') THEN
        ALTER TABLE profiles ADD COLUMN id_verification_status TEXT DEFAULT 'not_started' 
            CHECK (id_verification_status IN ('not_started', 'under_review', 'approved', 'rejected'));
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'profile_completion') THEN
        ALTER TABLE profiles ADD COLUMN profile_completion INTEGER DEFAULT 0;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'trust_score') THEN
        ALTER TABLE profiles ADD COLUMN trust_score INTEGER DEFAULT 0;
    END IF;
END $$;

-- Create verification submissions table for manual review
CREATE TABLE IF NOT EXISTS verification_submissions (
    id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::TEXT,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    document_type TEXT NOT NULL CHECK (document_type IN ('drivers_license', 'passport', 'national_id')),
    document_front_url TEXT NOT NULL,
    document_back_url TEXT,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'requires_resubmission')),
    submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    reviewed_at TIMESTAMP WITH TIME ZONE NULL,
    reviewed_by UUID NULL REFERENCES auth.users(id),
    rejection_reason TEXT NULL,
    notes TEXT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create cost savings analytics table
CREATE TABLE IF NOT EXISTS cost_savings_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    verification_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    identity_verification_cost_saved INTEGER DEFAULT 7, -- $7 saved vs Jumio
    background_check_cost_saved INTEGER DEFAULT 35, -- $35 saved vs traditional
    reference_verification_cost_saved INTEGER DEFAULT 15, -- $15 saved vs services
    total_saved INTEGER DEFAULT 57, -- Total $57 saved
    verification_method TEXT DEFAULT 'manual_review',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_profiles_verification_level ON profiles(verification_level);
CREATE INDEX IF NOT EXISTS idx_profiles_auth_flow_version ON profiles(auth_flow_version);
CREATE INDEX IF NOT EXISTS idx_profiles_phone ON profiles(phone) WHERE phone IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_profiles_role ON profiles(role);
CREATE INDEX IF NOT EXISTS idx_profiles_id_verification_status ON profiles(id_verification_status);
CREATE INDEX IF NOT EXISTS idx_verification_submissions_status ON verification_submissions(status);
CREATE INDEX IF NOT EXISTS idx_verification_submissions_user_id ON verification_submissions(user_id);

-- Enable RLS
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE verification_submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE cost_savings_analytics ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
CREATE POLICY "Users can view own profile" ON profiles
    FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
CREATE POLICY "Users can update own profile" ON profiles
    FOR UPDATE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert own profile" ON profiles;
CREATE POLICY "Users can insert own profile" ON profiles
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Verification submissions policies
DROP POLICY IF EXISTS "Users can view own verification submissions" ON verification_submissions;
CREATE POLICY "Users can view own verification submissions" ON verification_submissions
    FOR SELECT USING (user_id = auth.uid());

DROP POLICY IF EXISTS "Users can insert own verification submissions" ON verification_submissions;
CREATE POLICY "Users can insert own verification submissions" ON verification_submissions
    FOR INSERT WITH CHECK (user_id = auth.uid());

-- Cost savings policies
DROP POLICY IF EXISTS "Users can view own cost savings" ON cost_savings_analytics;
CREATE POLICY "Users can view own cost savings" ON cost_savings_analytics
    FOR SELECT USING (user_id = auth.uid());

DROP POLICY IF EXISTS "Users can insert own cost savings" ON cost_savings_analytics;
CREATE POLICY "Users can insert own cost savings" ON cost_savings_analytics
    FOR INSERT WITH CHECK (user_id = auth.uid());

-- Create trigger function for profile completion
CREATE OR REPLACE FUNCTION update_profile_completion()
RETURNS TRIGGER AS $$
BEGIN
    -- Calculate completion based on verification level and filled fields
    NEW.profile_completion := (
        CASE WHEN NEW.email IS NOT NULL AND NEW.username IS NOT NULL THEN 20 ELSE 0 END +
        CASE WHEN NEW.phone_verified = TRUE THEN 5 ELSE 0 END +
        CASE WHEN NEW.first_name IS NOT NULL AND NEW.last_name IS NOT NULL THEN 10 ELSE 0 END +
        CASE WHEN NEW.location IS NOT NULL THEN 10 ELSE 0 END +
        CASE WHEN NEW.profile_photo_url IS NOT NULL THEN 10 ELSE 0 END +
        CASE WHEN NEW.bio IS NOT NULL THEN 10 ELSE 0 END +
        CASE WHEN NEW.preferences IS NOT NULL AND NEW.preferences != '{}' THEN 5 ELSE 0 END +
        CASE 
            WHEN NEW.id_verification_status = 'under_review' THEN 15
            WHEN NEW.id_verification_status = 'approved' THEN 30
            ELSE 0 
        END
    );
    
    NEW.updated_at := NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger
DROP TRIGGER IF EXISTS trigger_update_profile_completion ON profiles;
CREATE TRIGGER trigger_update_profile_completion
    BEFORE UPDATE ON profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_profile_completion();

-- Create function to auto-create profile on user signup
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO profiles (user_id, email, auth_flow_version, verification_level, profile_completion)
    VALUES (
        NEW.id,
        NEW.email,
        'simplified_v1',
        0,
        20 -- Start with 20% for having email
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for new user signup
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION handle_new_user();
