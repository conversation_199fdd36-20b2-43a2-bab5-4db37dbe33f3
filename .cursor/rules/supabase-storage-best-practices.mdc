---
description:
globs:
alwaysApply: false
---
# Supabase Storage Best Practices

## Overview
Comprehensive guide for setting up and managing Supabase storage buckets with proper security, performance, and integration patterns.

## Bucket Management

### Naming Conventions
```
- avatars          // User profile images
- createlisting    // Room listing images  
- service-images   // Service provider images
- documents        // Document uploads
- videos           // Video content
- verification     // Identity verification docs
```

### Bucket Configuration Template
```sql
-- Standard bucket setup with security
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'bucket-name',
  'bucket-name',
  true,                    -- Public access for web viewing
  52428800,               -- 50MB limit (adjust as needed)
  array[
    'image/jpeg', 
    'image/png', 
    'image/webp', 
    'image/gif'
  ]
);
```

### Required RLS Policies Pattern
```sql
-- 1. Public read access (required for public URLs)
CREATE POLICY "Public Read Access" 
ON storage.objects FOR SELECT
USING (bucket_id = 'your-bucket-name');

-- 2. Authenticated upload
CREATE POLICY "Authenticated Upload" 
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'your-bucket-name' 
  AND auth.role() = 'authenticated'
);

-- 3. User owns file update/delete
CREATE POLICY "Users manage own files" 
ON storage.objects FOR UPDATE
USING (
  bucket_id = 'your-bucket-name' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

CREATE POLICY "Users delete own files" 
ON storage.objects FOR DELETE  
USING (
  bucket_id = 'your-bucket-name'
  AND auth.uid()::text = (storage.foldername(name))[1]
);
```

## Storage Access Patterns

### Bucket Validation
Always validate bucket access before uploads:

```typescript
import { listAccessibleBuckets, isBucketAccessible } from '@utils/storageHelper';

// Check bucket accessibility
const accessibleBuckets = await listAccessibleBuckets();
if (!accessibleBuckets.includes('your-bucket')) {
  throw new Error('Bucket not accessible');
}

// Test specific bucket
const canAccess = await isBucketAccessible('your-bucket');
if (!canAccess) {
  throw new Error('Cannot access bucket');
}
```

### File Path Structure
```typescript
// User-scoped files
const userPath = `${userId}/avatar-${timestamp}.jpg`;

// Feature-scoped files  
const featurePath = `feature_name/${timestamp}-${randomId}.jpg`;

// Shared/public files
const publicPath = `public/${category}/${filename}`;
```

## Integration with Upload System

### Standard Upload Service
```typescript
import { getSupabaseClient } from '@services/supabaseService';
import { intelligentUploader } from '@utils/intelligentUploadStrategy';

export class FeatureStorageService {
  private bucket = 'your-feature-bucket';
  
  async uploadImage(uri: string, userId: string): Promise<UploadResult> {
    // Generate path with user scope
    const fileName = `${Date.now()}-${Math.floor(Math.random() * 1000)}.jpg`;
    const path = `${userId}/${fileName}`;
    
    // Use intelligent upload strategy
    return intelligentUploader.smartUpload(uri, {
      bucket: this.bucket,
      path,
      contentType: 'image/jpeg',
      enableOptimization: true,
    });
  }
  
  async deleteImage(path: string): Promise<boolean> {
    const supabase = getSupabaseClient();
    const { error } = await supabase.storage
      .from(this.bucket)
      .remove([path]);
    
    return !error;
  }
  
  getPublicUrl(path: string): string {
    const supabase = getSupabaseClient();
    const { data } = supabase.storage
      .from(this.bucket)
      .getPublicUrl(path);
    
    return data.publicUrl;
  }
}
```

## Security Best Practices

### RLS Policy Validation
Before implementing any bucket, verify RLS policies:

```sql
-- Check existing policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename = 'objects' AND schemaname = 'storage';

-- Test policy with user context
SELECT * FROM storage.objects 
WHERE bucket_id = 'your-bucket' 
LIMIT 1;
```

### Authentication Requirements
```typescript
// Always verify authentication before storage operations
import { ensureSupabaseSession } from '@utils/authUtils';

const sessionResult = await ensureSupabaseSession();
if (!sessionResult.success || !sessionResult.user) {
  throw new Error('Authentication required');
}
```

### File Size Limits
```typescript
// Implement client-side validation
const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB

const validateFileSize = async (uri: string): Promise<boolean> => {
  try {
    const response = await fetch(uri);
    const blob = await response.blob();
    return blob.size <= MAX_FILE_SIZE;
  } catch {
    return false;
  }
};
```

## Performance Optimization

### Bucket Organization
```
bucket-name/
├── users/
│   ├── {userId}/
│   │   ├── avatar.jpg
│   │   └── documents/
├── listings/
│   ├── {listingId}/
│   │   ├── image1.jpg
│   │   └── image2.jpg
└── public/
    ├── thumbnails/
    └── defaults/
```

### CDN Integration
```typescript
// Configure CDN transform parameters
const getCdnUrl = (path: string, transforms?: {
  width?: number;
  height?: number;
  quality?: number;
}) => {
  const baseUrl = supabase.storage
    .from(bucket)
    .getPublicUrl(path).data.publicUrl;
    
  if (!transforms) return baseUrl;
  
  const params = new URLSearchParams();
  if (transforms.width) params.set('width', transforms.width.toString());
  if (transforms.height) params.set('height', transforms.height.toString());
  if (transforms.quality) params.set('quality', transforms.quality.toString());
  
  return `${baseUrl}?${params.toString()}`;
};
```

## Error Handling

### Storage Error Types
```typescript
export interface StorageError {
  type: 'bucket_not_found' | 'permission_denied' | 'file_too_large' | 'network_error';
  message: string;
  originalError?: any;
}

export const handleStorageError = (error: any): StorageError => {
  if (error.message?.includes('Bucket not found')) {
    return { type: 'bucket_not_found', message: 'Storage bucket not accessible' };
  }
  
  if (error.message?.includes('new row violates row-level security')) {
    return { type: 'permission_denied', message: 'Insufficient permissions' };
  }
  
  if (error.message?.includes('file size')) {
    return { type: 'file_too_large', message: 'File exceeds size limit' };
  }
  
  if (error.message?.includes('Network request failed')) {
    return { type: 'network_error', message: 'Network connectivity issue' };
  }
  
  return { type: 'network_error', message: error.message || 'Unknown storage error' };
};
```

## Testing & Validation

### Bucket Health Check
```typescript
export const validateBucketHealth = async (bucketName: string) => {
  const results = {
    exists: false,
    accessible: false,
    canUpload: false,
    canList: false,
    policies: [],
  };
  
  try {
    // Test bucket access
    results.accessible = await isBucketAccessible(bucketName);
    
    // Test upload capability
    const testData = new TextEncoder().encode('test');
    const testPath = `health-check/${Date.now()}.txt`;
    
    const { error: uploadError } = await supabase.storage
      .from(bucketName)
      .upload(testPath, testData, { upsert: true });
      
    results.canUpload = !uploadError;
    
    // Test list capability
    const { error: listError } = await supabase.storage
      .from(bucketName)
      .list('', { limit: 1 });
      
    results.canList = !listError;
    
    // Cleanup test file
    if (results.canUpload) {
      await supabase.storage.from(bucketName).remove([testPath]);
    }
    
  } catch (error) {
    console.error(`Bucket health check failed:`, error);
  }
  
  return results;
};
```

## Migration Patterns

### Bucket Creation Script
```typescript
export const createFeatureBucket = async (
  bucketId: string,
  config: {
    sizeLimit?: number;
    allowedTypes?: string[];
    isPublic?: boolean;
  } = {}
) => {
  const {
    sizeLimit = 52428800, // 50MB
    allowedTypes = ['image/jpeg', 'image/png', 'image/webp'],
    isPublic = true
  } = config;
  
  // Create bucket
  const { data, error } = await supabase.storage.createBucket(bucketId, {
    public: isPublic,
    fileSizeLimit: sizeLimit,
    allowedMimeTypes: allowedTypes,
  });
  
  if (error && !error.message.includes('already exists')) {
    throw error;
  }
  
  return { success: true, bucket: data };
};
```

This guide ensures consistent, secure, and performant storage implementation across all app features.
