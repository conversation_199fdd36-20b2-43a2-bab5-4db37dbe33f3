---
description: 
globs: 
alwaysApply: true
---
# Systematic Theming Implementation Plan

## Phase 1: Foundation (CRITICAL)
1. Consolidate [src/design-system/constants/colors.ts](mdc:src/design-system/constants/colors.ts) as single source
2. Enhance [src/design-system/ThemeProvider.tsx](mdc:src/design-system/ThemeProvider.tsx) with full theme support
3. Create theme hook and utilities
4. Update [src/design-system/constants/index.ts](mdc:src/design-system/constants/index.ts) to export unified theme

## Phase 2: Core Components (HIGH PRIORITY)
Update these component categories in order:
1. UI components: [src/components/ui/](mdc:src/components/ui)
2. Matching components: [src/components/matching/](mdc:src/components/matching)
3. Browse components: [src/components/browse/](mdc:src/components/browse)
4. Authentication: [src/components/auth/](mdc:src/components/auth)

## Phase 3: Feature Components (MEDIUM PRIORITY)
1. Services: [src/components/services/](mdc:src/components/services)
2. Analytics: [src/components/analytics/](mdc:src/components/analytics)
3. Notifications: [src/components/notifications/](mdc:src/components/notifications)
4. Messaging: [src/components/messaging/](mdc:src/components/messaging)

## Phase 4: Screens & Debug (LOW PRIORITY)
1. Main screens in [src/app/](mdc:src/app)
2. Debug components: [src/components/debug/](mdc:src/components/debug)
3. Test files and examples

## Validation Checklist
- [ ] No grep results for hardcoded hex colors: `#[0-9A-Fa-f]{3,6}`
- [ ] All components use useTheme() hook
- [ ] Dark mode functions correctly
- [ ] No import conflicts between color systems
- [ ] Performance remains optimal
- [ ] Expo compatibility maintained

## Migration Commands
Use these patterns for systematic updates:
- Find hardcoded colors: `grep -r "backgroundColor.*#[0-9A-Fa-f]" src/`
- Find color imports: `grep -r "import.*colors.*from" src/`
- Validate theme usage: `grep -r "useTheme()" src/`
