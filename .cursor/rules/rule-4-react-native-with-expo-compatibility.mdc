---
description: 
globs: 
alwaysApply: false
---
# React Native + Expo Theming Compatibility

## Framework Requirements
- MUST maintain Expo managed workflow compatibility
- MUST support Expo Router file-based routing
- MUST use React Native compatible color formats (hex preferred)
- MUST work with expo-system-ui for status bar theming
- MUST integrate with useColorScheme() hook

## Color Format Standards
- Primary format: Hex colors (`#2563EB`)
- Opacity: RGBA format (`rgba(37, 99, 235, 0.1)`)
- No CSS color names or other web-specific formats
- All colors MUST work on iOS and Android

## Theme Integration Points
- Status bar styling via expo-system-ui
- Navigation theme integration with Expo Router
- Safe area handling with react-native-safe-area-context
- Platform-specific adaptations when necessary

## Performance Requirements
- Theme changes MUST not cause unnecessary re-renders
- Use React.memo() for theme-dependent components
- Optimize StyleSheet.create() calls with theme factory pattern
- Minimize inline style objects

## Key Integration Files
- [app.json](mdc:app.json) - Expo configuration for theme support
- [src/app/_layout.tsx](mdc:src/app/_layout.tsx) - Root layout with theme provider
- Theme provider integration with existing navigation structure
