---
description:
globs:
alwaysApply: false
---
# Database Architecture Analysis: Existing Verification Infrastructure

## 🗄️ Current Database Schema Overview
The RoommatesGate app has a comprehensive verification infrastructure already in place in Supabase. This analysis documents the existing database foundation for implementing zero-cost verification.

## 📊 Existing Verification Tables

### Core Verification Tables
Based on the analysis of [src/services/unified/UnifiedVerificationService.ts](mdc:src/services/unified/UnifiedVerificationService.ts), the following tables exist:

#### 1. `background_checks`
- **Purpose**: Store background check results and status
- **Key Fields**: 
  - `user_id` (UUID, foreign key)
  - `status` (background_check_status enum)
  - `check_type` (background_check_type enum)
  - `results` (JSONB)
  - `created_at`, `updated_at`

#### 2. `email_verifications`
- **Purpose**: Track email verification attempts and status
- **Key Fields**:
  - `user_id` (UUID, foreign key)
  - `email` (TEXT)
  - `verification_token` (TEXT)
  - `verified_at` (TIMESTAMP)
  - `status` (verification_status enum)

#### 3. `identity_verifications`
- **Purpose**: Store identity verification documents and results
- **Key Fields**:
  - `user_id` (UUID, foreign key)
  - `document_type` (TEXT)
  - `document_url` (TEXT) - Supabase storage URL
  - `verification_result` (JSONB)
  - `status` (verification_status enum)
  - `reviewed_by` (UUID) - Admin user ID
  - `reviewed_at` (TIMESTAMP)

#### 4. `verification_requests`
- **Purpose**: Central tracking of all verification requests
- **Key Fields**:
  - `id` (UUID, primary key)
  - `user_id` (UUID, foreign key)
  - `verification_type` (TEXT)
  - `status` (verification_status enum)
  - `metadata` (JSONB)
  - `created_at`, `updated_at`

#### 5. `verification_analytics`
- **Purpose**: Track verification performance and metrics
- **Key Fields**:
  - `verification_type` (TEXT)
  - `success_rate` (DECIMAL)
  - `average_completion_time` (INTERVAL)
  - `cost_per_verification` (DECIMAL)
  - `date` (DATE)

## 🔧 Existing Enums

### `background_check_status`
```sql
CREATE TYPE background_check_status AS ENUM (
  'pending',
  'in_progress',
  'completed',
  'failed',
  'requires_review'
);
```

### `background_check_type`
```sql
CREATE TYPE background_check_type AS ENUM (
  'criminal',
  'employment',
  'education',
  'reference',
  'social_media'
);
```

### `verification_status`
```sql
CREATE TYPE verification_status AS ENUM (
  'pending',
  'in_progress',
  'completed',
  'failed',
  'expired',
  'requires_manual_review'
);
```

## 🏗️ Current Service Architecture

### UnifiedVerificationService Structure
Located in [src/services/unified/UnifiedVerificationService.ts](mdc:src/services/unified/UnifiedVerificationService.ts):

```typescript
class UnifiedVerificationService {
  // Identity verification methods
  async verifyIdentity(userId: string, documents: Document[]): Promise<VerificationResult>
  
  // Phone verification methods  
  async verifyPhone(userId: string, phoneNumber: string): Promise<VerificationResult>
  
  // Email verification methods
  async verifyEmail(userId: string, email: string): Promise<VerificationResult>
  
  // Address verification methods
  async verifyAddress(userId: string, address: Address): Promise<VerificationResult>
  
  // Background check methods
  async performBackgroundCheck(userId: string, checkType: BackgroundCheckType): Promise<BackgroundCheckResult>
  
  // Trust scoring
  calculateTrustScore(userId: string): Promise<TrustScore>
}
```

### Current API Integrations
The service currently integrates with:
- **Persona API**: Identity verification (expensive)
- **Onfido**: Document verification (expensive)
- **Checkr**: Background checks (expensive)
- **Twilio**: Phone verification (expensive)

## 📁 Related Components

### Verification Components
Located in [src/components/verification/](mdc:src/components/verification/):
- Identity verification UI components
- Phone verification forms
- Email verification status displays
- Background check result viewers

### Authentication Integration
- [src/context/AuthContext.tsx](mdc:src/context/AuthContext.tsx) - Current auth context
- [src/services/supabaseService.ts](mdc:src/services/supabaseService.ts) - Supabase client setup

## 🎯 Zero-Cost Implementation Strategy

### Database Compatibility Requirements
1. **No Schema Changes**: Use existing tables and enums
2. **Status Compatibility**: Work with current verification_status enum
3. **Audit Trail**: Maintain existing logging patterns
4. **Foreign Key Integrity**: Preserve relationships with user profiles

### Service Integration Points
1. **Replace API Calls**: Swap expensive APIs with free alternatives
2. **Maintain Interfaces**: Keep existing method signatures
3. **Status Updates**: Use existing status tracking
4. **Result Storage**: Store results in existing JSONB fields

### Manual Review Integration
- **Admin Users**: Use existing user table with admin roles
- **Review Workflow**: Add `reviewed_by` and `reviewed_at` fields usage
- **Document Storage**: Utilize Supabase storage for uploaded documents
- **Audit Logging**: Leverage existing verification_analytics table

## 🔄 Migration Strategy

### Phase 1: Service Layer Updates
Update [src/services/unified/UnifiedVerificationService.ts](mdc:src/services/unified/UnifiedVerificationService.ts) to:
1. Replace expensive API calls with free alternatives
2. Add manual review workflows
3. Implement document upload to Supabase storage
4. Add free tier usage monitoring

### Phase 2: Component Updates
Update verification components to:
1. Support document upload for manual review
2. Display manual review status
3. Handle free service limitations gracefully
4. Add admin review interfaces

### Phase 3: Database Optimization
Optimize existing tables for free verification:
1. Add indexes for admin review queries
2. Optimize storage for document metadata
3. Add monitoring for free tier usage
4. Implement cleanup procedures for old documents

## 📊 Performance Considerations

### Current Database Performance
- Existing indexes on user_id fields
- JSONB fields for flexible result storage
- Timestamp fields for audit trails
- Enum constraints for data integrity

### Optimization Opportunities
1. **Document Storage**: Use Supabase storage efficiently
2. **Query Optimization**: Add indexes for admin review queries
3. **Data Retention**: Implement policies for old verification data
4. **Caching**: Add Redis caching for frequently accessed data

## 🛡️ Security & Compliance

### Current Security Measures
- Row Level Security (RLS) policies
- User-based data isolation
- Encrypted document storage
- Audit trail maintenance

### Additional Security for Manual Review
1. **Admin Access Control**: Role-based permissions
2. **Document Encryption**: Encrypt sensitive documents
3. **Audit Logging**: Track all admin actions
4. **Data Retention**: Implement GDPR-compliant deletion

## 🎯 Implementation Benefits

### Leveraging Existing Infrastructure
1. **No Migration**: Use current database schema
2. **Proven Architecture**: Build on tested foundation
3. **Existing Relationships**: Maintain data integrity
4. **Performance**: Optimize existing queries

### Cost Elimination Opportunities
1. **API Replacement**: Replace all expensive API calls
2. **Storage Utilization**: Use free Supabase storage
3. **Manual Processes**: Leverage existing admin framework
4. **Monitoring**: Track costs using existing analytics

---

**Key Insight**: The existing database architecture is perfectly suited for zero-cost verification implementation. The comprehensive schema, flexible JSONB storage, and existing service patterns provide an excellent foundation for replacing expensive APIs with free alternatives while maintaining data integrity and performance.
