---
description: 
globs: 
alwaysApply: false
---
# Project Cursor Rules - React Native + Expo + TypeScript

## 🔒 STRICT TECH STACK REQUIREMENTS - DO NOT CHANGE

### Core Technology Stack (IMMUTABLE)
- **Framework**: React Native with Expo
- **Router**: Expo Router (file-based routing)
- **Language**: TypeScript (strict mode)
- **Project Structure**: Top-Level-src-directory
- **Package Manager**: npm (unless explicitly specified otherwise)

### 🚫 FORBIDDEN ACTIONS
- DO NOT suggest migrating to Next.js, React (web), Vue, Angular, or any other framework
- DO NOT recommend removing Expo or switching to bare React Native
- DO NOT suggest changing from Expo Router to React Navigation or any other routing solution
- DO NOT propose switching from TypeScript to JavaScript
- DO NOT modify the top-level src directory structure
- DO NOT suggest using Metro bundler configurations that conflict with Expo

## 📁 PROJECT STRUCTURE REQUIREMENTS

### Mandatory Directory Structure
```
project-root/
├── src/
│   ├── app/                 # Expo Router pages (file-based routing)
│   ├── components/          # Reusable UI components
│   ├── hooks/              # Custom React hooks
│   ├── services/           # API calls and external services
│   ├── utils/              # Helper functions and utilities
│   ├── types/              # TypeScript type definitions
│   ├── constants/          # App constants and configuration
│   └── assets/             # Images, fonts, and other static assets
├── app.json                # Expo configuration
├── package.json
├── tsconfig.json
└── .cursorrules            # This file
```

### File Naming Conventions
- Use kebab-case for file names: `user-profile.tsx`, `auth-service.ts`
- Use PascalCase for component files: `UserProfile.tsx`, `AuthModal.tsx`
- Use camelCase for utility files: `dateUtils.ts`, `apiHelpers.ts`
## 🔍 THIRD-PARTY INTEGRATION RULES

### MANDATORY EXPO COMPATIBILITY CHECK
Before suggesting ANY third-party library or package:

1. **FIRST**: Check the official Expo documentation at https://docs.expo.dev/versions/latest/
2. **VERIFY**: Library compatibility with Expo managed workflow
3. **CONFIRM**: No native code requirements that need ejecting
4. **VALIDATE**: Expo SDK version compatibility

### Approved Integration Sources (in order of preference)
1. Expo SDK packages (expo-*)
2. Libraries explicitly listed in Expo docs as compatible
3. Pure JavaScript/TypeScript libraries with no native dependencies
4. React Native community packages verified as Expo-compatible

### Third-Party Library Requirements
- Must be Expo-compatible (managed workflow)
- Must support TypeScript
- Must have active maintenance (updated within last 6 months)
- Must not require ejecting from Expo managed workflow

## 📝 CODING STANDARDS

### TypeScript Requirements
- Use strict TypeScript configuration
- Define interfaces for all props and state
- Use proper type annotations for functions
- Avoid `any` type unless absolutely necessary
- Use enums for constants with multiple values

### React Native/Expo Specific Rules
- Use Expo Router for all navigation
- Prefer Expo SDK components over third-party alternatives
- Use StyleSheet.create() for styling
- Follow React Native performance best practices
- Use Expo's asset handling for images and fonts

### Code Organization
- Keep components small and focused (max 200 lines)
- Use custom hooks for complex logic
- Separate business logic from UI components
- Use barrel exports (index.ts) in directories
- Keep inline styles minimal, prefer StyleSheet

## 🛠 DEVELOPMENT PRACTICES

### File Structure Rules
- Place reusable components in `/src/components/`
- Place page components in `/src/app/` (Expo Router structure)
- Place types in `/src/types/` with descriptive names
- Place utilities in `/src/utils/` with single responsibility
- Place constants in `/src/constants/` grouped by feature

### Import/Export Guidelines
- Use relative imports for local files: `../components/Button`
- Use absolute imports for src-level: `@/components/Button` (if configured)
- Group imports: React/React Native → Third-party → Local
- Use named exports unless default export is more appropriate

### Performance Considerations
- Use React.memo() for expensive components
- Implement proper FlatList optimization for lists
- Use Expo's asset optimization features
- Avoid deep component nesting
- Use proper key props for dynamic lists

## 🔧 CONFIGURATION REQUIREMENTS

### Required Configuration Files
- `app.json`: Expo configuration (required)
- `tsconfig.json`: TypeScript configuration (strict mode)
- `package.json`: Dependencies and scripts
- `.env`: Environment variables (if needed)

### Expo Configuration Guidelines
- Keep expo SDK version updated to stable releases
- Configure proper app icons and splash screens
- Set appropriate permissions in app.json
- Use Expo build service for deployment

## 🚨 ERROR HANDLING AND DEBUGGING

### Debugging Preferences
- Use Expo development tools first
- Prefer Expo DevTools over Chrome DevTools when possible
- Use Flipper only if specifically required and Expo-compatible
- Use Expo's error reporting and analytics when available

### Error Handling Standards
- Use try-catch blocks for async operations
- Implement proper error boundaries
- Use Expo's crash reporting if available
- Provide user-friendly error messages

## ✅ APPROVAL PROCESS FOR CHANGES

### Before Making Any Architectural Changes:
1. Verify change maintains Expo managed workflow compatibility
2. Check if change affects TypeScript configuration
3. Ensure change doesn't break Expo Router functionality
4. Confirm change aligns with top-level src directory structure

### Before Adding Dependencies:
1. Check Expo documentation compatibility: https://docs.expo.dev/versions/latest/
2. Verify TypeScript support
3. Check bundle size impact
4. Ensure no native code dependencies (unless Expo SDK)

## 📚 DOCUMENTATION REQUIREMENTS

### Code Documentation
- Use JSDoc comments for complex functions
- Document all custom hooks with usage examples
- Include prop documentation for reusable components
- Maintain README.md with setup and development instructions

### Comments Guidelines
- Explain WHY, not WHAT
- Use TODO comments for future improvements
- Include links to relevant Expo documentation when applicable
- Document any workarounds with explanation

## 🎯 SUMMARY

This project is strictly a **React Native + Expo + TypeScript** application using **Expo Router** with a **top-level src directory structure**. 

**DO NOT DEVIATE** from this tech stack. Any suggestions for libraries, tools, or architectural changes must be compatible with Expo's managed workflow and should be verified against the official Expo documentation at https://docs.expo.dev/versions/latest/.

When in doubt, prefer Expo SDK solutions over third-party alternatives.
