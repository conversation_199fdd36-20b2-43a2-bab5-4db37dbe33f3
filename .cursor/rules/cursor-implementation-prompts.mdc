---
description:
globs:
alwaysApply: false
---
# Cursor Implementation Prompts: Zero-Cost Verification

## 🎯 Overview
This file contains all the specific Cursor prompts for implementing the zero-cost verification system. Use these prompts sequentially for systematic implementation.

## 📋 Pre-Implementation Setup

### Environment Configuration Prompt
```
Update my environment configuration to support free verification services.

CURRENT: Expensive APIs (Onfido, Checkr, Twilio Premium)
TARGET: Free alternatives using Supabase + Google Maps + manual processes

TASKS:
1. Add Google Maps API key (free 40k requests/month)
2. Remove expensive API configurations from [src/core/config/envConfig.ts](mdc:src/core/config/envConfig.ts)
3. Add admin dashboard configuration variables
4. Set up free tier monitoring variables

ENVIRONMENT VARIABLES TO ADD:
```bash
GOOGLE_MAPS_API_KEY=your_free_google_maps_key
ADMIN_EMAIL=<EMAIL>
VERIFICATION_MANUAL_REVIEW_HOURS=24
VERIFICATION_DOCUMENT_MAX_SIZE_MB=10
```

ENVIRONMENT VARIABLES TO REMOVE:
- PERSONA_API_KEY (expensive)
- CHECKR_API_KEY (expensive) 
- TWILIO_PREMIUM_KEY (expensive)

Update the configuration to support free-tier-only verification services.
```

## 📅 Week 1: Foundation Implementation

### Day 1-2: Database Analysis & Core Service
```
Analyze my existing Supabase database and create a zero-cost verification service.

ANALYSIS REQUIRED:
1. Examine all verification-related tables in my Supabase database
2. Map existing verification services in [src/services/unified/UnifiedVerificationService.ts](mdc:src/services/unified/UnifiedVerificationService.ts)
3. Identify current verification components and their integration points
4. Document existing verification flow and status enums

CREATE NEW SERVICE:
File: [src/services/freeVerificationService.ts](mdc:src/services/freeVerificationService.ts)

REQUIREMENTS:
- Use existing database tables (no schema changes)
- Replace expensive APIs with free alternatives:
  * Supabase Auth for phone verification
  * Manual process for identity verification  
  * Google Maps API for address verification
  * DNS checking for email verification
- Maintain compatibility with existing verification flow
- Include proper TypeScript interfaces
- Add comprehensive error handling

Show me the current verification architecture first, then provide the complete free verification service implementation.
```

### Day 3: Phone Verification Replacement
```
Replace my current phone verification system with FREE Supabase Auth phone verification.

CURRENT SYSTEM: [Analyze existing phone verification implementation]
NEW SYSTEM: Supabase Auth phone verification (free tier)

IMPLEMENTATION:
1. Find existing phone verification service/components
2. Replace with Supabase Auth:
   ```typescript
   // Use these Supabase Auth methods
   supabase.auth.signInWithOtp({ phone: phoneNumber })
   supabase.auth.verifyOtp({ phone, token, type: 'sms' })
   ```
3. Update existing phone verification UI components
4. Store results in existing verification tables
5. Maintain backward compatibility

REQUIREMENTS:
- Use current authentication context from [src/context/AuthContext.tsx](mdc:src/context/AuthContext.tsx)
- Work with existing verification database schema
- Follow existing component patterns
- Add proper loading and error states
- Ensure mobile-friendly OTP input

Show me current phone verification implementation, then provide the updated free version.
```

### Day 4-5: Email & Address Verification
```
Implement FREE email and address verification to replace paid services.

EMAIL VERIFICATION TASKS:
1. DNS/MX record validation (completely free)
2. Supabase Auth magic link verification
3. Disposable email detection using free APIs
4. Update existing email verification flow in components

ADDRESS VERIFICATION TASKS:
1. Google Maps Geocoding API (40,000 free requests/month)
2. OpenStreetMap fallback for over-limit scenarios
3. Address validation and confidence scoring
4. Usage tracking to stay within free tier

IMPLEMENTATION REQUIREMENTS:
- Create address verification service with Google Maps integration
- Add rate limiting and caching for API efficiency
- Work with existing verification tables
- Include comprehensive error handling and fallbacks
- Follow existing service architecture patterns
- Add monitoring for free tier usage

FILES TO UPDATE:
- Email verification service
- Address verification service  
- Existing verification components
- [src/core/config/envConfig.ts](mdc:src/core/config/envConfig.ts) for API keys

Provide complete implementation for both email and address verification using only free services.
```

## 📅 Week 2: Advanced Features

### Day 6-8: Manual Identity Verification System
```
Create a comprehensive manual identity verification system to replace expensive automated services (Onfido $7/check).

SYSTEM COMPONENTS:

1. DOCUMENT UPLOAD SYSTEM:
   - React Native camera integration for document photos
   - Supabase storage integration (free 1GB)
   - Document type validation (passport, ID, driver's license)
   - File compression and security measures
   - Progress tracking for uploads

2. ADMIN REVIEW INTERFACE:
   - Document viewer with zoom capabilities
   - Approve/reject workflow with comments
   - Admin authentication and role-based access
   - Bulk actions for efficiency
   - Review history and audit trail

3. STATUS TRACKING SYSTEM:
   - Real-time status updates for users
   - Push notifications for status changes
   - Verification history display
   - Appeal process for rejections

TECHNICAL REQUIREMENTS:
- Use existing identity verification tables
- Follow current UI/UX patterns from [src/components/verification/](mdc:src/components/verification/)
- Implement proper security for admin access
- Add comprehensive audit logging
- Mobile-optimized document capture
- Offline capability for document queue

Create the complete manual verification system that provides the same user experience as automated verification but at $0 cost.
```

### Day 9-10: Free Background Check System
```
Implement a comprehensive background check system using public APIs and manual processes.

BACKGROUND CHECK FEATURES:

1. PUBLIC RECORDS INTEGRATION:
   - Free government APIs (sex offender registry, court records)
   - Social media profile verification
   - Manual reference check system via email
   - Public criminal record databases

2. REFERENCE CHECK WORKFLOW:
   - Email-based reference forms with custom templates
   - Automated follow-up reminders
   - Response tracking and scoring system
   - Reference verification analytics
   - Fraud detection for fake references

3. RESULTS DASHBOARD:
   - Comprehensive background check display
   - Risk assessment scoring algorithm
   - Manual review workflow for flagged results
   - Background check history and trends
   - Export functionality for compliance

TECHNICAL IMPLEMENTATION:
- Use only free public APIs and databases
- Work with existing background check tables
- Implement proper data privacy controls (GDPR/CCPA)
- Follow existing verification patterns
- Add comprehensive audit trails
- Create scoring algorithm for risk assessment

Create a complete background check system that provides the same insights as paid services ($35/check) at $0 cost.
```

## 📅 Week 3: Integration & Admin Tools

### Day 11-13: Admin Dashboard Creation
```
Create a comprehensive admin dashboard for managing the free verification system.

DASHBOARD FEATURES:

1. VERIFICATION MANAGEMENT:
   - Pending verification queue with priority sorting
   - Document review interface with annotation tools
   - Bulk actions and approval workflow
   - User verification status overview
   - Performance metrics for admin efficiency

2. ANALYTICS & MONITORING:
   - Google Maps API usage tracking (stay under 40k/month)
   - Supabase storage monitoring for documents
   - Verification completion rates and trends
   - Performance metrics and response times
   - User satisfaction scores

3. COST OPTIMIZATION:
   - Monthly verification cost display (target: $0)
   - Savings comparison vs paid services
   - Usage alerts for approaching free tier limits
   - Optimization recommendations
   - ROI tracking and reporting

TECHNICAL REQUIREMENTS:
- Secure admin authentication with role-based access
- Real-time usage monitoring and alerts
- Mobile-responsive design for admin mobile access
- Integration with existing verification tables
- Comprehensive analytics and reporting
- Export capabilities for business intelligence

Create a professional admin interface that ensures the verification system stays within free tier limits while providing excellent oversight.
```

### Day 14-15: Integration Testing & Optimization
```
Integrate all free verification services with the existing app and perform comprehensive testing.

INTEGRATION TASKS:
1. Update existing verification flow to use new free services
2. Ensure backward compatibility with current user data
3. Test all verification types end-to-end
4. Verify database operations work correctly
5. Test admin dashboard functionality with real data

COMPREHENSIVE TESTING CHECKLIST:
- Phone verification using Supabase Auth (test with real phone numbers)
- Email verification with DNS checking and magic links
- Address verification with Google Maps API (test various addresses)
- Manual identity verification workflow (upload, review, approve/reject)
- Background check with public APIs and reference system
- Admin review and approval process (test bulk operations)
- Error handling and edge cases (network failures, invalid data)
- Performance under load (simulate high verification volume)

OPTIMIZATION REQUIREMENTS:
- Ensure API usage stays within free tiers
- Optimize database queries for performance
- Add caching where appropriate (Redis if available)
- Implement proper error recovery mechanisms
- Add comprehensive logging for debugging
- Performance monitoring and alerts

Create a complete testing strategy and fix any integration issues found. Provide performance benchmarks and optimization recommendations.
```

## 📅 Week 4: Production Readiness

### Day 16-18: Performance Optimization
```
Optimize the free verification system for production deployment.

OPTIMIZATION AREAS:

1. PERFORMANCE OPTIMIZATION:
   - Database query optimization for verification tables
   - API call efficiency and batching
   - Image compression for document uploads
   - Caching strategies for repeated verifications
   - Memory management for mobile app

2. MONITORING IMPLEMENTATION:
   - Usage tracking for all free tier services
   - Performance monitoring with alerts
   - Error rate tracking and automatic recovery
   - User experience metrics collection
   - Cost monitoring to ensure $0 monthly spend

3. ALERT SYSTEM:
   - Free tier usage warnings (80% threshold)
   - System performance alerts
   - Failed verification notifications
   - Admin action required alerts
   - Security incident detection

PRODUCTION READINESS CHECKLIST:
- Comprehensive error handling for all edge cases
- Graceful degradation when services are unavailable
- Security audit of manual verification processes
- Performance testing under expected load
- Documentation completion for operations team
- Backup and recovery procedures
- Monitoring dashboard setup

Ensure the system is production-ready and will maintain $0 monthly verification costs under normal operation.
```

### Day 19-21: Documentation & Training
```
Create comprehensive documentation and training materials for the free verification system.

DOCUMENTATION REQUIREMENTS:

1. USER GUIDES:
   - Step-by-step verification process for each type
   - Troubleshooting guide for common issues
   - Privacy and security information for users
   - FAQ section addressing user concerns
   - Mobile app usage instructions

2. ADMIN GUIDES:
   - Admin dashboard operation manual
   - Manual review best practices and standards
   - Monitoring and maintenance procedures
   - Emergency procedures and escalation
   - Performance optimization guidelines

3. DEVELOPER DOCUMENTATION:
   - API integration guide for future developers
   - Database schema documentation with relationships
   - Service architecture overview and patterns
   - Troubleshooting guide for technical issues
   - Code comments and inline documentation

4. TRAINING MATERIALS:
   - Admin training videos or interactive guides
   - User onboarding flow documentation
   - Customer support scripts and procedures
   - Technical team handover documentation
   - System maintenance schedules

DELIVERABLES:
- Complete user manual with screenshots
- Admin training program
- Developer onboarding guide
- Operations runbook
- Emergency response procedures

Create complete documentation that enables smooth operation of the zero-cost verification system without external dependencies.
```

## 🚨 Emergency & Troubleshooting Prompts

### Database Issues
```
Fix database-related issues in the verification system.

COMMON ISSUES:
1. Verification table connection problems
2. Status enum mismatches
3. Foreign key constraint violations
4. Performance issues with large datasets

DIAGNOSTIC STEPS:
1. Check database connection health
2. Verify table schemas match expectations
3. Analyze slow queries
4. Check for data inconsistencies

Provide SQL queries to diagnose and fix common database issues.
```

### API Limit Exceeded
```
Handle free tier API limit exceeded scenarios.

SCENARIOS:
1. Google Maps API approaching 40k monthly limit
2. Supabase storage approaching 1GB limit
3. Rate limiting on free services

SOLUTIONS:
1. Implement fallback services (OpenStreetMap for Google Maps)
2. Add compression for document storage
3. Queue requests during rate limits
4. Alert admins before limits reached

Create emergency procedures for when free tier limits are exceeded.
```

### Performance Issues
```
Diagnose and fix performance issues in the verification system.

PERFORMANCE AREAS:
1. Slow document uploads
2. Admin dashboard loading times
3. Verification status updates
4. Database query optimization

OPTIMIZATION TECHNIQUES:
1. Image compression and resizing
2. Database indexing
3. Caching strategies
4. Lazy loading for admin interface

Provide specific performance optimization implementations.
```

## 📊 Success Validation Prompts

### Cost Verification
```
Validate that the verification system maintains $0 monthly costs.

VALIDATION CHECKLIST:
1. Confirm all paid APIs have been removed
2. Verify free tier usage is within limits
3. Check for any hidden costs or overages
4. Calculate actual monthly savings vs previous system

Provide a comprehensive cost analysis showing $0 monthly verification costs.
```

### Performance Benchmarking
```
Benchmark the free verification system against previous paid system.

METRICS TO COMPARE:
1. Verification completion rates
2. User satisfaction scores
3. Processing times
4. Error rates
5. Admin efficiency

Provide performance comparison showing the free system matches or exceeds paid system performance.
```

---

**Usage Instructions**: Use these prompts sequentially with Cursor, providing the specific context and requirements for each implementation phase. Each prompt is designed to work with the existing codebase structure and maintain compatibility with current systems.
