---
description: 
globs: 
alwaysApply: true
---
# Component Styling Best Practices

## Required Component Pattern
Every component MUST follow this pattern:

```typescript
import { useTheme } from '@design-system/ThemeProvider';
import { StyleSheet } from 'react-native';

const MyComponent = () => {
  const theme = useTheme();
  const styles = createStyles(theme);
  
  return <View style={styles.container}>...</View>;
};

const createStyles = (theme: Theme) => StyleSheet.create({
  container: {
    backgroundColor: theme.colors.background,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
  }
});
```

## Styling Requirements
- All StyleSheet.create() MUST use theme-dependent factory functions
- No inline styles with hardcoded values
- Use design system tokens for: colors, spacing, typography, borders, shadows
- Support both light and dark themes automatically

## Legacy Pattern Elimination
Remove these patterns from existing components:
- `import { colors } from '@constants/colors'`
- `import { colors } from '@utils/themeUtils'`
- Direct style objects with hardcoded values
- Mixed import sources for design tokens

## Files Needing Systematic Updates
Components with StyleSheet.create requiring theme integration:
- [src/components/ui/EnhancedCard.tsx](mdc:src/components/ui/EnhancedCard.tsx)
- [src/components/browse/HousemateCard.tsx](mdc:src/components/browse/HousemateCard.tsx)
- [src/components/badges/VerificationBadgeGroup.tsx](mdc:src/components/badges/VerificationBadgeGroup.tsx)
- All components in matching, services, and analytics directories
