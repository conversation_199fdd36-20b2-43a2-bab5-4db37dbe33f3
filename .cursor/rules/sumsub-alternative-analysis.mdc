---
description:
globs:
alwaysApply: false
---
# Sumsub vs Onfido: Verification API Comparison

## 🔍 Initial Analysis Context
User inquired about **Sumsub** as an alternative to **Onfido** for identity verification in their roommate matching app. This analysis compares both services and provides recommendations.

## 📊 Cost Comparison

### Onfido (Current Service)
- **Per Check Cost**: $7+ per verification
- **Monthly Estimate**: ~$700/month (100 verifications)
- **Annual Cost**: ~$8,400
- **Free Trial**: None available
- **Minimum Commitment**: Pay-per-use

### Sumsub (Recommended Alternative)
- **Per Check Cost**: $1.35 per verification
- **Monthly Estimate**: ~$135/month (100 verifications)
- **Annual Cost**: ~$1,620
- **Free Trial**: 14 days with 50 free checks
- **Cost Savings**: **81% reduction** ($6,780 annual savings)

## 🚀 Feature Comparison

### Onfido Capabilities
- Identity document verification
- Facial biometric verification
- Document authenticity checks
- Basic fraud detection
- Limited global coverage (2,500+ documents from 195+ countries)
- Pass rate: ~87.2% (US market)

### Sumsub Advantages
- **Comprehensive Suite**: Identity, document, address, phone/email verification
- **Superior Coverage**: 6,500+ documents from 220+ countries
- **Higher Pass Rates**: 91.64% (US market) vs Onfido's 87.2%
- **All-in-One Platform**: Reduces need for multiple verification services
- **Advanced Fraud Prevention**: AI-powered risk assessment
- **Better API Documentation**: More developer-friendly integration
- **Marketplace Focus**: Specifically designed for platforms like roommate apps

## 🏗️ Technical Integration

### React Native SDK Support
- **Sumsub**: Excellent React Native SDK with comprehensive documentation
- **Onfido**: Basic React Native support, limited documentation
- **Migration Effort**: Moderate (2-3 days for basic integration)

### Current Integration Points
Update these files when migrating from Onfido to Sumsub:
- [src/services/unified/UnifiedVerificationService.ts](mdc:src/services/unified/UnifiedVerificationService.ts)
- [src/components/verification/](mdc:src/components/verification/) - UI components
- [src/core/config/envConfig.ts](mdc:src/core/config/envConfig.ts) - API configuration

## 🎯 Roommate App Suitability

### Why Sumsub is Ideal for Roommate Platforms
1. **Trust & Safety Focus**: Designed for marketplace/sharing economy platforms
2. **Comprehensive Verification**: All verification types in one service
3. **Global User Base**: Better international document support
4. **Cost Efficiency**: 81% cost reduction enables more verification features
5. **Compliance Ready**: Built-in AML/KYC compliance for rental platforms

### Verification Flow for Roommate App
```typescript
// Sumsub integration example
const sumsub = new SumsubSDK({
  apiKey: process.env.SUMSUB_API_KEY,
  secretKey: process.env.SUMSUB_SECRET_KEY
});

// Identity + Address verification in one flow
const verification = await sumsub.createApplicant({
  externalUserId: userId,
  requiredIdDocs: ['PASSPORT', 'ID_CARD', 'DRIVERS'],
  requiredSteps: ['identity', 'address-verification']
});
```

## 🔒 Security & Compliance

### Data Protection
- **Sumsub**: GDPR, CCPA, SOC 2 Type II compliant
- **Onfido**: GDPR, ISO 27001 compliant
- **Winner**: Tie - both meet enterprise security standards

### Fraud Prevention
- **Sumsub**: AI-powered risk scoring, device fingerprinting, behavioral analysis
- **Onfido**: Basic fraud detection, document authenticity checks
- **Winner**: Sumsub - more comprehensive fraud prevention

## 💡 Implementation Recommendation

### Immediate Action: Sumsub Trial
1. **Start Free Trial**: 14 days + 50 free verifications
2. **Parallel Testing**: Run alongside existing Onfido integration
3. **Performance Comparison**: Compare pass rates, user experience, costs
4. **Migration Planning**: Prepare for full transition if trial successful

### Migration Strategy
```bash
# Phase 1: Setup (Week 1)
- Create Sumsub account and get API keys
- Implement parallel verification system
- Test with small user subset

# Phase 2: Comparison (Week 2) 
- A/B test Sumsub vs Onfido
- Measure pass rates, user satisfaction, costs
- Gather user feedback on experience

# Phase 3: Migration (Week 3-4)
- Full migration to Sumsub if metrics favorable
- Update all verification components
- Monitor performance and costs
```

## ⚠️ Important Considerations

### Background Checks Limitation
- **Critical**: Sumsub does NOT provide criminal background checks
- **Solution**: Continue using **Checkr** ($35/check) for criminal screening
- **Alternative**: Implement [zero-cost background check system](mdc:.cursor/rules/zero-cost-verification-system.mdc) using public APIs

### Hybrid Approach Options
1. **Sumsub + Checkr**: Identity verification ($1.35) + Background checks ($35)
2. **Sumsub + Free Background**: Identity verification ($1.35) + Public API background ($0)
3. **Full Zero-Cost**: Manual verification + Public API background ($0)

## 📈 ROI Analysis

### Cost Savings Scenarios

#### Scenario 1: Sumsub + Checkr
- **Identity**: $135/month (vs $700 Onfido) = $565 savings
- **Background**: $3,500/month (unchanged)
- **Total Savings**: $6,780/year

#### Scenario 2: Sumsub + Free Background
- **Identity**: $135/month (vs $700 Onfido) = $565 savings  
- **Background**: $0/month (vs $3,500 Checkr) = $3,500 savings
- **Total Savings**: $48,780/year

#### Scenario 3: Full Zero-Cost System
- **All Verification**: $0/month (vs $4,310 total)
- **Total Savings**: $51,720/year

## 🎯 Final Recommendation

### Short-term (Immediate)
**Migrate to Sumsub** for 81% cost reduction while maintaining quality:
- Start 14-day free trial immediately
- Implement parallel testing
- Migrate identity verification from Onfido to Sumsub

### Long-term (3-6 months)
**Implement Zero-Cost System** for maximum savings:
- Use Sumsub trial period to design manual verification system
- Transition to [zero-cost verification architecture](mdc:.cursor/rules/zero-cost-verification-system.mdc)
- Achieve $0 monthly verification costs

### Best of Both Worlds
1. **Phase 1**: Migrate to Sumsub (immediate 81% savings)
2. **Phase 2**: Implement zero-cost system (100% savings)
3. **Fallback**: Keep Sumsub as backup for high-volume periods

---

**Bottom Line**: Sumsub offers immediate significant cost savings (81%) with better features, making it an excellent stepping stone toward a completely free verification system.
