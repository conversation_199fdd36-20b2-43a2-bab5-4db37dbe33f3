---
description: 
globs: 
alwaysApply: true
---
# Design System & Theming Architecture

## Core Principle
The app MUST use a unified design system with consistent theming throughout all components. No hardcoded colors or styling values are permitted.

## Design System Structure
- **Theme Provider**: [src/design-system/ThemeProvider.tsx](mdc:src/design-system/ThemeProvider.tsx) - Central theme management
- **Theme Constants**: [src/design-system/constants/index.ts](mdc:src/design-system/constants/index.ts) - All design tokens
- **Color System**: [src/design-system/constants/colors.ts](mdc:src/design-system/constants/colors.ts) - Unified color palette
- **Spacing Scale**: [src/design-system/constants/spacing.ts](mdc:src/design-system/constants/spacing.ts) - Consistent spacing tokens
- **Typography**: [src/design-system/constants/typography.ts](mdc:src/design-system/constants/typography.ts) - Text styling system

## Required Implementation
1. Consolidate existing color utilities from [src/utils/themeUtils.ts](mdc:src/utils/themeUtils.ts) and [src/utils/colorUtils.ts](mdc:src/utils/colorUtils.ts)
2. Create single source of truth for all design tokens
3. Implement theme provider with light/dark mode support
4. Ensure React Native + Expo compatibility for all design tokens
