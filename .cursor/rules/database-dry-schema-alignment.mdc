---
description:
globs:
alwaysApply: false
---
# Database DRY Principles & Schema Alignment

## 🚫 ABSOLUTE PROHIBITIONS

### Database Creation Restrictions
- **NEVER create tables, indexes, functions, or views without MCP analysis**
- **NEVER duplicate existing database artifacts** 
- **NEVER alter database schema without explicit approval**
- **NEVER ignore existing 41+ indexes on user_profiles table**
- **NEVER create functions that duplicate existing 29+ profile functions**

## 📊 EXISTING DATABASE INVENTORY (VERIFIED)

### Core Profile Tables (USE THESE - DO NOT RECREATE)
- `user_profiles` - Main profile data (41 indexes already exist)
- `user_personality_profiles` - Personality completion tracking
- `user_personality_responses` - Individual personality answers
- `profile_change_log` - Profile modification history
- `social_media_profiles` - Social platform connections
- `identity_verifications` - Verification status tracking
- `query_performance_log` - Performance monitoring (existing schema)

### Existing Functions (LEVERAGE - DO NOT DUPLICATE)
```sql
-- Profile Management (29+ functions exist)
update_profile_batch_atomic()
update_profile_preferences_safe()
get_complete_user_profile()
calculate_profile_completion()
verify_profile_integrity()

-- Personality Functions
calculate_personality_compatibility()
update_personality_responses()
get_personality_summary()

-- Performance Functions
log_query_performance()
analyze_slow_queries()
cleanup_performance_data()
```

### Existing Indexes (41+ ON user_profiles - USE THESE)
```sql
-- Primary & Unique Indexes
user_profiles_pkey (id)
idx_user_profiles_email_unique (email)
idx_user_profiles_phone_unique (phone_number)

-- Role & Location Indexes  
idx_user_profiles_role (role)
idx_user_profiles_location (location)
idx_user_profiles_role_location (role, location)

-- Completion & Verification Indexes
idx_user_profiles_completion (profile_completion)
idx_user_profiles_verified (is_verified)
idx_user_profiles_completion_verified (profile_completion, is_verified)

-- Performance Indexes
idx_user_profiles_updated_at (updated_at)
idx_user_profiles_created_at (created_at)
idx_user_profiles_active_users (updated_at) WHERE is_verified = true

-- Text Search Indexes
idx_user_profiles_bio_fts (to_tsvector('english', bio))
idx_user_profiles_occupation_fts (to_tsvector('english', occupation))

-- Composite Indexes for Common Queries
idx_user_profiles_search_optimized (role, location, profile_completion, is_verified)
```

## ✅ REQUIRED DEVELOPMENT PATTERNS

### 1. Schema First Development
```typescript
// ✅ CORRECT: Use existing schema
interface UserProfile {
  id: string;
  email: string;
  role: 'TENANT' | 'LANDLORD';
  profile_completion: number;
  is_verified: boolean;
  // ... use exact database column names
}

// ❌ WRONG: Create new schema
interface NewUserProfile {
  userId: string;
  completion: number;
  // ... different naming
}
```

### 2. Function Reuse Pattern
```typescript
// ✅ CORRECT: Use existing functions
const profileData = await db.query(`
  SELECT * FROM get_complete_user_profile($1)
`, [userId]);

// ❌ WRONG: Create duplicate logic
const profileData = await db.query(`
  SELECT up.*, upp.completion_percentage 
  FROM user_profiles up 
  LEFT JOIN user_personality_profiles upp...
  -- Duplicating existing function logic
`);
```

### 3. Index Utilization Pattern
```typescript
// ✅ CORRECT: Leverage existing indexes
const searchQuery = `
  SELECT * FROM user_profiles 
  WHERE role = $1 
    AND location ILIKE $2 
    AND profile_completion >= $3
    AND is_verified = true
  ORDER BY updated_at DESC
`; // Uses idx_user_profiles_search_optimized

// ❌ WRONG: Query patterns that miss indexes
const badQuery = `
  SELECT * FROM user_profiles 
  WHERE LOWER(first_name) = $1
  OR bio LIKE '%' || $2 || '%'
`; // Creates full table scan
```

## 🔍 MANDATORY VERIFICATION STEPS

### Before Any Database Code
1. **Check Existing Tables**: Query `information_schema.tables`
2. **Verify Indexes**: Query `pg_indexes WHERE tablename = 'target_table'`
3. **List Functions**: Query `pg_proc` for existing functions
4. **Validate Schema**: Use existing column names and types

### Development Workflow
```typescript
// 1. Schema Discovery
const existingColumns = await db.query(`
  SELECT column_name, data_type 
  FROM information_schema.columns 
  WHERE table_name = 'user_profiles'
`);

// 2. Index Utilization Check
const indexUsage = await db.query(`
  SELECT indexname, indexdef 
  FROM pg_indexes 
  WHERE tablename = 'user_profiles'
`);

// 3. Function Reuse Check
const existingFunctions = await db.query(`
  SELECT routine_name, routine_type 
  FROM information_schema.routines 
  WHERE routine_schema = 'public'
    AND routine_name LIKE '%profile%'
`);
```

## 📋 CODE ALIGNMENT REQUIREMENTS

### Service Layer Alignment
Reference: [src/services/profile/UnifiedProfileService.ts](mdc:src/services/profile/UnifiedProfileService.ts)
```typescript
// ✅ CORRECT: Align with existing schema
class ProfileService {
  async updateProfile(data: UserProfileUpdate) {
    // Use existing update_profile_batch_atomic function
    return this.db.query(`
      SELECT update_profile_batch_atomic($1, $2)
    `, [userId, data]);
  }

  async searchProfiles(filters: ProfileFilters) {
    // Use existing indexes and functions
    return this.db.query(`
      SELECT * FROM search_profiles_optimized_v2($1, $2, $3, $4)
    `, [filters.query, filters.role, filters.location, filters.limit]);
  }
}
```

### Component Alignment
Reference: [src/components/profile/](mdc:src/components/profile/)
```typescript
// ✅ CORRECT: Use exact database field names
interface ProfileProps {
  profile: {
    id: string;
    first_name: string;      // Exact DB column
    last_name: string;       // Exact DB column  
    profile_completion: number; // Exact DB column
    is_verified: boolean;    // Exact DB column
  };
}

// ❌ WRONG: Different naming convention
interface ProfileProps {
  profile: {
    userId: string;
    firstName: string;       // Differs from DB
    lastName: string;        // Differs from DB
    completionPercent: number; // Differs from DB
    verified: boolean;       // Differs from DB
  };
}
```

### Migration Alignment
Reference: [src/migrations/](mdc:src/migrations/)
```sql
-- ✅ CORRECT: Extend existing without duplication
CREATE INDEX IF NOT EXISTS idx_new_feature_specific 
ON user_profiles(new_column) 
WHERE new_column IS NOT NULL;

-- ❌ WRONG: Duplicate existing functionality  
CREATE INDEX idx_profiles_role_duplicate 
ON user_profiles(role); -- Already exists!
```

## 🎯 PERFORMANCE OPTIMIZATION GUIDELINES

### 1. Use Existing Materialized Views
```sql
-- ✅ CORRECT: Query existing materialized views
SELECT * FROM mv_profile_completion_stats WHERE role = 'TENANT';
SELECT * FROM mv_personality_trait_stats WHERE question_id = 1;
SELECT * FROM mv_active_matching_pool WHERE profile_completion >= 80;
```

### 2. Leverage Existing Performance Functions
```sql
-- ✅ CORRECT: Use existing monitoring
SELECT log_slow_query_v2('profile_search', 150.5, 'SELECT...', 'user_profiles', user_id);
SELECT * FROM analyze_profile_performance_v2();
SELECT cleanup_old_performance_data_v2();
```

### 3. Query Pattern Optimization
```typescript
// ✅ CORRECT: Index-aware queries
const optimizedQuery = `
  SELECT up.id, up.first_name, up.profile_completion
  FROM user_profiles up
  WHERE up.role = $1                    -- Uses idx_user_profiles_role
    AND up.is_verified = true           -- Uses idx_user_profiles_verified  
    AND up.profile_completion >= $2     -- Uses idx_user_profiles_completion
  ORDER BY up.updated_at DESC           -- Uses idx_user_profiles_updated_at
  LIMIT $3
`;
```

## 🚦 APPROVAL REQUIREMENTS

### Code Review Checklist
- [ ] **Schema Alignment**: Code uses exact database column names
- [ ] **Function Reuse**: Leverages existing 29+ profile functions  
- [ ] **Index Utilization**: Queries use existing 41+ indexes
- [ ] **No Duplication**: No new tables/functions that duplicate existing ones
- [ ] **Performance**: Uses materialized views and optimized queries

### Deployment Validation
```sql
-- Verify no duplicate artifacts created
SELECT 'Duplicate Tables' as check_type, COUNT(*) as violations
FROM information_schema.tables 
WHERE table_schema = 'public' 
  AND table_name LIKE '%profile%'
  AND table_name NOT IN ('user_profiles', 'user_personality_profiles', 'social_media_profiles')

UNION ALL

SELECT 'Duplicate Indexes' as check_type, COUNT(*) as violations  
FROM pg_indexes
WHERE schemaname = 'public'
  AND tablename = 'user_profiles'
  AND indexname LIKE '%duplicate%'

UNION ALL

SELECT 'Duplicate Functions' as check_type, COUNT(*) as violations
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid  
WHERE n.nspname = 'public'
  AND p.proname LIKE '%profile%'
  AND p.proname NOT LIKE '%_v2';
```

## 📚 REFERENCE DOCUMENTATION

### Key Files to Reference
- **Database Schema**: Use MCP `mcp_roommate_dbl_query` for current state
- **Service Layer**: [src/services/profile/UnifiedProfileService.ts](mdc:src/services/profile/UnifiedProfileService.ts)
- **Profile Components**: [src/components/profile/](mdc:src/components/profile/)  
- **Migration History**: [src/migrations/](mdc:src/migrations/)
- **Types**: [src/types/profile.ts](mdc:src/types/profile.ts)

### MCP Analysis Commands
```typescript
// Always verify before coding
await mcp_roommate_dbl_query(`
  SELECT table_name FROM information_schema.tables 
  WHERE table_schema = 'public' AND table_name LIKE '%profile%'
`);

await mcp_roommate_dbl_query(`
  SELECT indexname FROM pg_indexes 
  WHERE tablename = 'user_profiles'
`);

await mcp_roommate_dbl_query(`
  SELECT routine_name FROM information_schema.routines 
  WHERE routine_schema = 'public' AND routine_name LIKE '%profile%'
`);
```

## 🎯 SUCCESS METRICS

- **Zero Duplicate Database Artifacts**: No redundant tables, indexes, or functions
- **100% Schema Alignment**: All code uses exact database field names
- **Optimal Query Performance**: All queries utilize existing indexes
- **Function Reuse Rate**: 90%+ of operations use existing database functions
- **Migration Efficiency**: New features extend existing schema without duplication

**REMEMBER: The database is complete and optimized. Your role is to ALIGN code with existing schema, not CREATE new database artifacts.**
