---
description:
globs:
alwaysApply: false
---
# Payment Method Implementation Guide - roommatesGate

## 🚫 CRITICAL PAYMENT SECURITY RESTRICTIONS

### ABSOLUTE PROHIBITIONS
- **NEVER store credit card numbers, CVV, or sensitive payment data in app state or database**
- **NEVER process actual payments in development/testing environments without proper sandbox setup**
- **NEVER skip payment validation or bypass authentication checks**
- **NEVER ignore PCI compliance requirements for payment handling**

## 📋 MANDATORY PAYMENT ARCHITECTURE

### Required File Structure
```
src/
├── services/
│   ├── payment/
│   │   ├── ExistingPaymentMethodService.ts    # Core payment database operations
│   │   ├── PaymentService.ts                  # Legacy service (use ExistingPaymentMethodService)
│   │   └── index.ts                          # Service exports
├── components/
│   ├── payment/
│   │   ├── PaymentMethodModal.tsx            # Main payment flow component
│   │   ├── CardInputForm.tsx                 # Card input with validation
│   │   ├── SavedCardsView.tsx               # Existing cards management
│   │   └── PaymentSuccess.tsx               # Success confirmation
├── utils/
│   ├── authUtils.ts                         # Authentication helpers
│   └── supabaseUtils.ts                    # Database client
└── types/
    └── payment.ts                           # Payment type definitions
```

## 🔐 AUTHENTICATION & SECURITY PATTERNS

### Required Authentication Pattern
```typescript
// ✅ CORRECT: Always verify user authentication
import { getCurrentUser } from '@utils/authUtils';

const authenticatedUser = await getCurrentUser();
if (!authenticatedUser?.id) {
  throw new Error('User must be authenticated for payment operations');
}

// ✅ CORRECT: Use authenticated user ID for database operations
const paymentData = {
  user_id: authenticatedUser.id,
  amount: parseFloat(amount.toString()),
  status: 'completed', // Use database-compliant status values
  payment_method: selectedMethod,
};
```

### Security Validation Requirements
```typescript
// ✅ REQUIRED: Validate all payment inputs
const validatePaymentData = (data: PaymentData) => {
  if (!data.user_id || !data.amount || !data.payment_method) {
    throw new Error('Missing required payment fields');
  }
  
  if (data.amount <= 0 || data.amount > 50000) {
    throw new Error('Invalid payment amount');
  }
  
  if (!['pending', 'completed', 'failed', 'refunded'].includes(data.status)) {
    throw new Error('Invalid payment status');
  }
};
```

## 🗄️ DATABASE INTEGRATION PATTERNS

### Payment Service Implementation
Reference: [ExistingPaymentMethodService.ts](mdc:src/services/payment/ExistingPaymentMethodService.ts)

```typescript
// ✅ CORRECT: Use existing payment infrastructure
import { ExistingPaymentMethodService } from '@services/payment/ExistingPaymentMethodService';

class PaymentFlow {
  private paymentService = new ExistingPaymentMethodService();
  
  async processPayment(cardData: CardData, amount: number) {
    try {
      // 1. Validate authentication
      const user = await getCurrentUser();
      if (!user?.id) throw new Error('Authentication required');
      
      // 2. Create payment method record
      const paymentMethod = await this.paymentService.createPaymentMethod({
        user_id: user.id,
        type: 'card',
        card_details: {
          last_four: cardData.number.slice(-4),
          brand: this.detectCardBrand(cardData.number),
          exp_month: cardData.exp_month,
          exp_year: cardData.exp_year,
        },
        is_default: true,
      });
      
      // 3. Process payment record
      const payment = await this.paymentService.createPayment({
        user_id: user.id,
        amount: parseFloat(amount.toString()),
        status: 'completed',
        payment_method: paymentMethod.id,
        payment_method_id: paymentMethod.id,
      });
      
      return { success: true, payment, paymentMethod };
    } catch (error) {
      console.error('Payment processing failed:', error);
      return { success: false, error: error.message };
    }
  }
}
```

### Database Schema Alignment
```typescript
// ✅ REQUIRED: Use exact database column names and constraints
interface PaymentRecord {
  id: string;
  user_id: string;                    // FK to user_profiles.id
  amount: number;                     // Numeric type, positive values
  status: 'pending' | 'completed' | 'failed' | 'refunded'; // Exact constraint values
  payment_method: string;             // Method identifier
  payment_method_id?: string;         // FK to payment_methods.id
  created_at: string;                 // Timestamp with timezone
  updated_at: string;                 // Timestamp with timezone
}

interface PaymentMethod {
  id: string;
  user_id: string;                    // FK to user_profiles.id
  type: 'card' | 'bank' | 'digital_wallet';
  card_details?: {
    last_four: string;
    brand: string;
    exp_month: number;
    exp_year: number;
  };
  is_default: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}
```

## 🎨 UI COMPONENT PATTERNS

### Payment Modal Architecture
Reference: [PaymentMethodModal.tsx](mdc:src/components/payment/PaymentMethodModal.tsx)

```typescript
// ✅ CORRECT: Multi-step payment flow with proper state management
const PaymentMethodModal = ({ isVisible, planDetails, onSuccess, onClose }) => {
  const [currentStep, setCurrentStep] = useState<'method' | 'card' | 'payment'>('method');
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedMethod, setSelectedMethod] = useState<string | null>(null);
  
  const handlePaymentSubmission = async (cardData: CardData) => {
    setIsProcessing(true);
    try {
      const result = await paymentService.processPayment(cardData, planDetails.price);
      
      if (result.success) {
        setCurrentStep('payment');
        // Show success state
        setTimeout(() => {
          onSuccess(result.payment);
          onClose();
        }, 2000);
      } else {
        Alert.alert('Payment Failed', result.error);
      }
    } catch (error) {
      Alert.alert('Payment Error', 'An unexpected error occurred');
      console.error('Payment submission error:', error);
    } finally {
      setIsProcessing(false);
    }
  };
  
  return (
    <Modal visible={isVisible} animationType="slide">
      {currentStep === 'method' && (
        <PaymentMethodSelection onSelect={setSelectedMethod} onNext={() => setCurrentStep('card')} />
      )}
      {currentStep === 'card' && (
        <CardInputForm onSubmit={handlePaymentSubmission} isProcessing={isProcessing} />
      )}
      {currentStep === 'payment' && (
        <PaymentSuccess planDetails={planDetails} />
      )}
    </Modal>
  );
};
```

### Card Input Validation
Reference: [CardInputForm.tsx](mdc:src/components/payment/CardInputForm.tsx)

```typescript
// ✅ REQUIRED: Comprehensive input validation
const validateCardData = (cardData: CardData): string[] => {
  const errors: string[] = [];
  
  // Card number validation
  if (!cardData.number || cardData.number.replace(/\s/g, '').length < 13) {
    errors.push('Invalid card number');
  }
  
  // Expiry validation
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth() + 1;
  
  if (!cardData.exp_month || cardData.exp_month < 1 || cardData.exp_month > 12) {
    errors.push('Invalid expiry month');
  }
  
  if (!cardData.exp_year || cardData.exp_year < currentYear) {
    errors.push('Invalid expiry year');
  }
  
  if (cardData.exp_year === currentYear && cardData.exp_month < currentMonth) {
    errors.push('Card has expired');
  }
  
  // CVV validation
  if (!cardData.cvv || cardData.cvv.length < 3 || cardData.cvv.length > 4) {
    errors.push('Invalid CVV');
  }
  
  return errors;
};
```

## 🚨 ERROR HANDLING PATTERNS

### Comprehensive Error Management
```typescript
// ✅ REQUIRED: Structured error handling with user-friendly messages
class PaymentErrorHandler {
  static handlePaymentError(error: any): { userMessage: string; logData: any } {
    const errorPatterns = {
      'PGRST116': {
        userMessage: 'Payment method not found. Please add a payment method.',
        action: 'redirect_to_add_card'
      },
      'getCurrentUserId is not a function': {
        userMessage: 'Authentication required. Please sign in again.',
        action: 'redirect_to_login'
      },
      'Cannot read property': {
        userMessage: 'Payment service temporarily unavailable. Please try again.',
        action: 'retry_payment'
      },
      'Network request failed': {
        userMessage: 'Connection error. Please check your internet and try again.',
        action: 'retry_payment'
      }
    };
    
    const errorString = error?.message || String(error);
    const matchedPattern = Object.entries(errorPatterns).find(([pattern]) => 
      errorString.includes(pattern)
    );
    
    if (matchedPattern) {
      return {
        userMessage: matchedPattern[1].userMessage,
        logData: { error: errorString, pattern: matchedPattern[0], action: matchedPattern[1].action }
      };
    }
    
    return {
      userMessage: 'An unexpected error occurred. Please try again.',
      logData: { error: errorString, type: 'unknown_payment_error' }
    };
  }
}
```

### Logging Standards
```typescript
// ✅ REQUIRED: Comprehensive logging without sensitive data exposure
const logPaymentOperation = (operation: string, data: any, error?: any) => {
  const sanitizedData = {
    ...data,
    // Remove sensitive information
    card_number: data.card_number ? `****${data.card_number.slice(-4)}` : undefined,
    cvv: data.cvv ? '***' : undefined,
    // Keep safe information
    amount: data.amount,
    user_id: data.user_id,
    payment_method_type: data.type,
    timestamp: new Date().toISOString(),
  };
  
  if (error) {
    console.error(`[Payment ${operation}] Error:`, {
      ...sanitizedData,
      error: error.message || String(error),
      stack: error.stack?.split('\n').slice(0, 3), // Limited stack trace
    });
  } else {
    console.log(`[Payment ${operation}] Success:`, sanitizedData);
  }
};
```

## 🧪 TESTING REQUIREMENTS

### Payment Flow Testing
```typescript
// ✅ REQUIRED: Test all payment scenarios
describe('Payment Method Implementation', () => {
  describe('Authentication Integration', () => {
    it('should require user authentication', async () => {
      // Mock unauthenticated state
      jest.spyOn(authUtils, 'getCurrentUser').mockResolvedValue(null);
      
      const result = await paymentService.processPayment(mockCardData, 100);
      expect(result.success).toBe(false);
      expect(result.error).toContain('authentication');
    });
  });
  
  describe('Database Integration', () => {
    it('should create payment and payment_method records', async () => {
      const result = await paymentService.processPayment(mockCardData, 100);
      
      expect(result.success).toBe(true);
      expect(result.payment.status).toBe('completed');
      expect(result.paymentMethod.type).toBe('card');
    });
    
    it('should handle database constraint violations', async () => {
      // Test invalid status values
      const invalidPayment = { ...mockPaymentData, status: 'invalid_status' };
      
      await expect(paymentService.createPayment(invalidPayment))
        .rejects.toThrow('Invalid payment status');
    });
  });
  
  describe('Error Handling', () => {
    it('should handle missing payment methods gracefully', async () => {
      // Mock empty payment methods
      jest.spyOn(paymentService, 'getPaymentMethods').mockResolvedValue([]);
      
      const result = await paymentService.loadPaymentMethods(userId);
      expect(result.methods).toEqual([]);
      expect(result.hasExistingCards).toBe(false);
    });
  });
});
```

## 📊 PERFORMANCE OPTIMIZATION

### Caching and State Management
```typescript
// ✅ RECOMMENDED: Efficient payment data caching
class PaymentDataManager {
  private static paymentMethodsCache: Map<string, PaymentMethod[]> = new Map();
  private static cacheTimeout = 5 * 60 * 1000; // 5 minutes
  
  static async getPaymentMethods(userId: string): Promise<PaymentMethod[]> {
    const cacheKey = `payment_methods_${userId}`;
    const cached = this.paymentMethodsCache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    
    const methods = await paymentService.getPaymentMethods(userId);
    this.paymentMethodsCache.set(cacheKey, {
      data: methods,
      timestamp: Date.now()
    });
    
    return methods;
  }
  
  static invalidateCache(userId: string) {
    this.paymentMethodsCache.delete(`payment_methods_${userId}`);
  }
}
```

## 🔄 INTEGRATION PATTERNS

### Service Integration
```typescript
// ✅ REQUIRED: Clean service integration with existing systems
import { ExistingPaymentMethodService } from '@services/payment/ExistingPaymentMethodService';
import { getCurrentUser } from '@utils/authUtils';
import { supabase } from '@utils/supabaseUtils';

// Always use the existing payment infrastructure
const paymentService = new ExistingPaymentMethodService();

// Follow established authentication patterns
const authenticatedPaymentOperation = async (operation: () => Promise<any>) => {
  const user = await getCurrentUser();
  if (!user?.id) {
    throw new Error('Authentication required for payment operations');
  }
  
  return await operation();
};
```

## 🎯 SUCCESS VALIDATION

### Payment Flow Completion Checklist
- [ ] **Authentication Integration**: Uses `getCurrentUser()` correctly
- [ ] **Database Compliance**: Follows exact schema constraints
- [ ] **Error Handling**: Comprehensive error patterns covered
- [ ] **Security Validation**: No sensitive data exposure
- [ ] **UI Feedback**: Clear user experience flow
- [ ] **Testing Coverage**: All scenarios tested
- [ ] **Performance**: Efficient caching and state management
- [ ] **Logging**: Comprehensive without sensitive data

### Final Validation Commands
```typescript
// Test authentication integration
const user = await getCurrentUser();
console.log('Auth test:', !!user?.id);

// Test database constraints
const paymentStatuses = ['pending', 'completed', 'failed', 'refunded'];
console.log('Valid statuses:', paymentStatuses);

// Test service integration
const service = new ExistingPaymentMethodService();
console.log('Service instance:', !!service);
```

## 📚 REFERENCE FILES

### Core Payment Files
- [ExistingPaymentMethodService.ts](mdc:src/services/payment/ExistingPaymentMethodService.ts) - Primary service layer
- [PaymentMethodModal.tsx](mdc:src/components/payment/PaymentMethodModal.tsx) - Main UI component
- [CardInputForm.tsx](mdc:src/components/payment/CardInputForm.tsx) - Card input with validation
- [SavedCardsView.tsx](mdc:src/components/payment/SavedCardsView.tsx) - Existing cards management

### Supporting Infrastructure
- [authUtils.ts](mdc:src/utils/authUtils.ts) - Authentication utilities
- [supabaseUtils.ts](mdc:src/utils/supabaseUtils.ts) - Database client configuration

### Database Schema
- Existing `payments` table with proper constraints
- Existing `payment_methods` table with user relationships
- RLS policies for secure data access

**REMEMBER: Always leverage existing payment infrastructure, never bypass authentication, and maintain comprehensive error handling for production-ready payment implementations.**
