---
description:
globs:
alwaysApply: false
---
# Image Upload Implementation Rules

## Overview
Clean, error-free image upload implementation based on the proven create listing feature architecture. This guide ensures consistent, reliable image uploads across all app features.

## Core Architecture

### Foundation Files
- **Main Upload Utility**: [src/utils/imageUploadUtils.ts](mdc:src/utils/imageUploadUtils.ts) - Primary image upload functions
- **Intelligent Strategy**: [src/utils/intelligentUploadStrategy.ts](mdc:src/utils/intelligentUploadStrategy.ts) - Smart upload logic with iOS Simulator handling
- **Storage Service**: [src/utils/supabaseStorageService.ts](mdc:src/utils/supabaseStorageService.ts) - Direct Supabase storage operations
- **Storage Helper**: [src/utils/storageHelper.ts](mdc:src/utils/storageHelper.ts) - Bucket validation and connectivity testing
- **Image Optimization**: [src/utils/ultraImageOptimizer.ts](mdc:src/utils/ultraImageOptimizer.ts) - Smart compression strategies

### Required Support Files
- **Supabase Client**: [src/utils/supabase.ts](mdc:src/utils/supabase.ts) - Standardized client access
- **Auth Utilities**: [src/utils/authUtils.ts](mdc:src/utils/authUtils.ts) - Authentication validation
- **Debug Upload**: [src/utils/debugUpload.ts](mdc:src/utils/debugUpload.ts) - Testing and validation

## Implementation Steps

### Step 1: Bucket Configuration
```sql
-- Required bucket setup (run in Supabase SQL Editor)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'your-feature-bucket',
  'your-feature-bucket', 
  true,
  52428800, -- 50MB limit
  array['image/jpeg', 'image/png', 'image/webp', 'image/gif']
);

-- RLS Policies
CREATE POLICY "Public Access" ON storage.objects FOR SELECT
USING (bucket_id = 'your-feature-bucket');

CREATE POLICY "Authenticated users can upload" ON storage.objects FOR INSERT
WITH CHECK (bucket_id = 'your-feature-bucket' AND auth.role() = 'authenticated');

CREATE POLICY "Users can update own files" ON storage.objects FOR UPDATE
USING (bucket_id = 'your-feature-bucket' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can delete own files" ON storage.objects FOR DELETE
USING (bucket_id = 'your-feature-bucket' AND auth.uid()::text = (storage.foldername(name))[1]);
```

### Step 2: Modern ImagePicker Implementation
```typescript
import * as ImagePicker from 'expo-image-picker';

// ✅ CORRECT - Modern API usage
const result = await ImagePicker.launchImageLibraryAsync({
  mediaTypes: ['images'], // Use array syntax
  allowsMultiple: true,
  quality: 0.7,
  allowsEditing: false,
  exif: false,
});

// ❌ DEPRECATED - Avoid this
// const result = await ImagePicker.launchImageLibraryAsync({
//   mediaTypes: ImagePicker.MediaTypeOptions.Images, // Old enum syntax
// });
```

### Step 3: Import Pattern (Required)
```typescript
import { getSupabaseClient } from '@services/supabaseService';
import { uploadImage, testSupabaseStorageConnectivity } from '@utils/imageUploadUtils';
import { intelligentUploader } from '@utils/intelligentUploadStrategy';
import { logger } from '@utils/logger';
```

### Step 4: Intelligent Upload Usage
```typescript
/**
 * Recommended upload pattern for all features
 */
export const uploadFeatureImages = async (options: {
  bucket: string;
  folderPath: string;
  allowsMultiple?: boolean;
  quality?: number;
}) => {
  try {
    // 1. Request permissions
    const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (!permissionResult.granted) {
      throw new Error('Media library permission required');
    }

    // 2. Launch image picker with modern API
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ['images'],
      allowsMultiple: options.allowsMultiple || false,
      quality: options.quality || 0.7,
      allowsEditing: false,
      exif: false,
    });

    if (result.canceled || !result.assets?.length) {
      return { success: false, error: 'Selection cancelled' };
    }

    // 3. Upload using intelligent strategy
    const uploadResults = [];
    for (const asset of result.assets) {
      const fileName = `${Date.now()}-${Math.floor(Math.random() * 1000)}.jpg`;
      
      const uploadResult = await intelligentUploader.smartUpload(asset.uri, {
        bucket: options.bucket,
        path: `${options.folderPath}/${fileName}`,
        contentType: 'image/jpeg',
        enableOptimization: true,
      });

      uploadResults.push(uploadResult);
    }

    return {
      success: uploadResults.every(r => r.success),
      results: uploadResults
    };

  } catch (error) {
    logger.error('Feature image upload failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Upload failed'
    };
  }
};
```

## Key Features & Benefits

### Intelligent Upload Strategy
- **Automatic Environment Detection**: iOS Simulator vs Production
- **Smart Compression**: Ultra-optimization for simulator limitations
- **Fallback Support**: Mock uploads when network fails
- **Threshold Detection**: Dynamic size limits based on environment

### Error-Free Components
- **Modern ImagePicker API**: No deprecation warnings
- **Standardized Imports**: Consistent client access patterns
- **Bucket Validation**: Pre-upload connectivity testing
- **RLS Policy Handling**: Works with existing security policies

### Performance Optimizations
- **File Size Detection**: Automatic strategy selection
- **Compression Strategies**: Multiple optimization levels
- **Retry Logic**: Built-in upload resilience
- **Batch Processing**: Efficient multi-file uploads

## Required Folder Structure
```
your-feature/
├── components/
│   ├── ImageUploadComponent.tsx
│   └── ImageGallery.tsx
├── services/
│   └── YourFeatureImageService.ts
└── utils/
    └── yourFeatureImageHelpers.ts
```

## Validation & Testing

### Pre-Implementation Checklist
- [ ] Bucket created with proper RLS policies
- [ ] Modern ImagePicker API implemented
- [ ] Standardized import patterns used
- [ ] Intelligent upload strategy integrated
- [ ] Error handling implemented
- [ ] Debug upload tested successfully

### Testing Commands
```typescript
// Test connectivity
const connectivityResult = await testSupabaseStorageConnectivity();
console.log('Storage connectivity:', connectivityResult);

// Test upload strategy
const stats = await intelligentUploader.getStats();
console.log('Upload strategy stats:', stats);

// Debug upload
await debugUpload.runDebugUploadTest();
```

## Error Prevention Rules

### NEVER Do These
- ❌ Use deprecated `MediaTypeOptions.Images` enum
- ❌ Import from multiple supabase client sources
- ❌ Skip bucket validation
- ❌ Hardcode bucket names in components
- ❌ Use direct storage uploads without optimization
- ❌ Ignore iOS Simulator limitations

### ALWAYS Do These  
- ✅ Use modern `['images']` array syntax
- ✅ Import from standardized `@services/supabaseService`
- ✅ Test bucket accessibility before uploads
- ✅ Use intelligent upload strategy
- ✅ Implement proper error handling
- ✅ Add compression for large files

## Common Integration Patterns

### Avatar Upload
```typescript
const avatarResult = await intelligentUploader.smartUpload(uri, {
  bucket: 'avatars',
  path: `${userId}/avatar-${Date.now()}.jpg`,
  contentType: 'image/jpeg',
  enableOptimization: true,
});
```

### Room Listing Images
```typescript
const listingResult = await intelligentUploader.batchSmartUpload(uris, {
  bucket: 'createlisting',
  pathPrefix: 'create_listing_image',
  contentType: 'image/jpeg',
  enableOptimization: true,
});
```

### Service Images
```typescript
const serviceResult = await intelligentUploader.smartUpload(uri, {
  bucket: 'service-images',
  path: `services/${serviceId}/${Date.now()}.jpg`,
  contentType: 'image/jpeg',
  enableOptimization: true,
});
```

## Monitoring & Debugging

### Success Indicators
- ✅ No "Network request failed" errors
- ✅ No ImagePicker deprecation warnings
- ✅ Clean TypeScript compilation
- ✅ Successful file uploads with public URLs
- ✅ Proper compression ratios (90%+ for simulator)

### Debug Information
```typescript
// Check upload strategy stats
const stats = await intelligentUploader.getStats();
// Monitor compression ratios
// Validate bucket accessibility
// Test threshold detection
```

This architecture provides a robust, error-free foundation for image uploads that can be safely replicated across all app features while maintaining consistent performance and reliability.
