---
description: 
globs: 
alwaysApply: true
---
# Color Usage & Theming Restrictions

## PROHIBITED Patterns
- ❌ Hardcoded hex colors: `backgroundColor: '#FFFFFF'`
- ❌ Inline RGB/RGBA values: `color: 'rgb(255, 255, 255)'`
- ❌ Direct color imports from multiple sources
- ❌ Platform-specific color values
- ❌ Any color not defined in the design system

## REQUIRED Patterns
- ✅ Theme hook usage: `const theme = useTheme()`
- ✅ Semantic color tokens: `theme.colors.primary`
- ✅ Dynamic styles: `const styles = createStyles(theme)`
- ✅ Consistent import: `import { useTheme } from '@design-system/ThemeProvider'`

## Color Token Mapping
Replace all hardcoded colors with semantic tokens:
- `#FFFFFF` → `theme.colors.background`
- `#F8F9FA` → `theme.colors.surface`
- `#333333` → `theme.colors.text`
- `#666666` → `theme.colors.textSecondary`
- `#E2E8F0` → `theme.colors.border`
- `#2563EB` → `theme.colors.primary`
- `#10B981` → `theme.colors.success`
- `#EF4444` → `theme.colors.error`

## Components Requiring Updates
Priority files with hardcoded colors identified:
- [src/components/matching/EnhancedMatchCard.tsx](mdc:src/components/matching/EnhancedMatchCard.tsx)
- [src/components/moderation/ModerationInfoPanel.tsx](mdc:src/components/moderation/ModerationInfoPanel.tsx)
- [src/components/debug/ServiceProviderDebugger.tsx](mdc:src/components/debug/ServiceProviderDebugger.tsx)
- [src/screens/PersonalityQuestionnaireScreen.tsx](mdc:src/screens/PersonalityQuestionnaireScreen.tsx)
