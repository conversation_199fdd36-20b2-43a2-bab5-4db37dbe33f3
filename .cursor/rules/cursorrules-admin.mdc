---
description: 
globs: 
alwaysApply: true
---
# Cursor Rules for RoommatesGate Admin Implementation

## 🎯 **Project Context**
You are working on **RoommatesGate**, a React Native + Expo Router + Supabase roommate matching platform. You're implementing a comprehensive admin system for managing users, listings, service providers, and platform operations.

## 📁 **File Structure Patterns**
- Follow existing `src/` directory structure
- Admin routes go in `src/app/(admin)/` with file-based routing
- Admin components in `src/components/admin/`
- Admin services in `src/services/admin/`
- Admin types in `src/types/admin.ts`
- Admin hooks in `src/hooks/admin/`

## 🏗️ **Architecture Guidelines**

### **Routing & Navigation**
- Use Expo Router v4 file-based routing
- Implement route groups with `(admin)` for admin-only areas
- Use `_layout.tsx` for admin-specific layouts
- Implement proper route protection with role-based access

### **State Management**
- Use existing patterns: React Context for auth, React Query for server state
- Admin state should use React Query for caching and real-time updates
- Follow existing Zustand patterns for local admin state if needed

### **Supabase Integration**
- Use existing Supabase client configuration
- Implement Row Level Security (RLS) policies for admin access
- Use Supabase real-time for admin notifications and live updates
- Follow existing service patterns in `src/services/`

## 🎨 **UI/UX Standards**

### **Design System**
- Use existing design system from `src/design-system/`
- Follow semantic color tokens (no hardcoded colors)
- Use existing component patterns and spacing
- Implement responsive design for different screen sizes

### **Components**
- Create reusable admin components following existing patterns
- Use React Native Paper components when available
- Implement proper loading states and error handling
- Follow accessibility guidelines (a11y)

## 🔒 **Security Requirements**

### **Authentication & Authorization**
- Implement role-based access control (RBAC)
- Use existing auth context and extend with admin roles
- Validate admin permissions on both client and server
- Implement audit logging for admin actions

### **Data Protection**
- Follow existing data encryption patterns
- Implement proper input validation
- Use existing error handling patterns
- Respect GDPR compliance requirements

## 📱 **Mobile-First Development**

### **React Native Specific**
- Use React Native components (not web components)
- Implement proper navigation with Expo Router
- Use existing performance optimization patterns
- Follow existing image handling and caching

### **Cross-Platform**
- Ensure admin features work on iOS, Android, and Web
- Use existing responsive design patterns
- Test on different screen sizes and orientations

## 🛠️ **Code Quality Standards**
### **TypeScript**
- Use strict TypeScript configuration
- Define proper interfaces and types in `src/types/admin.ts`
- Follow existing type patterns and naming conventions
- Use generics where appropriate for reusability

### **Code Organization**
- Follow existing service layer patterns
- Implement proper error boundaries
- Use existing logging and monitoring patterns
- Follow existing testing patterns with Jest

### **Performance**
- Implement proper memoization for admin components
- Use existing virtualization for large lists
- Follow existing caching strategies
- Implement proper loading states

## 📊 **Data Patterns**

### **Database Operations**
- Use existing Supabase query patterns
- Implement proper pagination for admin lists
- Use existing real-time subscription patterns
- Follow existing transaction patterns

### **API Integration**
- Follow existing service patterns in `src/services/`
- Implement proper error handling and retry logic
- Use existing caching strategies with React Query
- Follow existing authentication patterns

## 🔧 **Development Workflow**

### **File Naming**
- Use camelCase for files and components
- Use kebab-case for routes and directories
- Follow existing naming conventions
- Use descriptive names for admin-specific files

### **Import Patterns**
- Use existing import patterns and path aliases
- Group imports: React, libraries, local components, types
- Use existing barrel exports where appropriate
- Follow existing dependency injection patterns

## 🎯 **Admin-Specific Guidelines**

### **User Management**
- Implement comprehensive user search and filtering
- Create user detail views with full profile information
- Implement bulk actions for user management
- Add user activity tracking and audit logs

### **Content Moderation**
- Create moderation queues for different content types
- Implement approval/rejection workflows
- Add bulk moderation capabilities
- Create content flagging and reporting systems

### **Analytics & Reporting**
- Implement real-time dashboards with key metrics
- Create exportable reports for business intelligence
- Add filtering and date range capabilities
- Implement performance monitoring for admin actions

### **Safety & Security**
- Create emergency response capabilities
- Implement suspicious activity detection
- Add safety incident management
- Create escalation procedures for critical issues

## 🚀 **Implementation Priority**

1. **Foundation**: Auth, routing, basic layout
2. **User Management**: Core user admin functionality
3. **Content Moderation**: Listing and message moderation
4. **Analytics**: Dashboard and reporting
5. **Advanced Features**: AI integration, bulk operations

## 📝 **Code Comments**
- Add JSDoc comments for admin functions
- Document complex business logic
- Add TODO comments for future enhancements
- Follow existing documentation patterns

## 🧪 **Testing**
- Write unit tests for admin services
- Create integration tests for admin workflows
- Follow existing testing patterns with Jest
- Test role-based access controls thoroughly

Remember: **Maintain consistency with existing codebase patterns** while implementing new admin functionality. Always consider mobile-first design and cross-platform compatibility.
