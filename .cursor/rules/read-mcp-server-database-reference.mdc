---
description: 
globs: 
alwaysApply: true
---
# .cursorrules - Strict Database Migration Protocol

## 🚫 CRITICAL DATABASE MIGRATION RESTRICTIONS

### ABSOLUTE PROHIBITIONS
- **<PERSON><PERSON><PERSON> create any SQL migration file without completing the mandatory database analysis protocol**
- **<PERSON><PERSON><PERSON> generate CREATE TABLE, CREATE INDEX, CREATE FUNCTION without DRY validation**
- **<PERSON><PERSON><PERSON> proceed with schema changes without MCP Roommate_dbl server examination**
- **NEVER ignore existing database artifacts that could cause redundancy**

## 📋 MANDATORY PRE-MIGRATION PROTOCOL

### STEP 1: MCP Database Analysis (REQUIRED)
Before ANY database-related code generation:

```markdown
1. CONNECT to MCP Roommate_dbl server
2. EXECUTE comprehensive database scan:
   - List all existing tables with their schemas
   - Identify all indexes and their purposes
   - Catalog all stored procedures/functions
   - Map all foreign key relationships
   - Document all constraints and triggers
3. GENERATE database artifact inventory report
4. WAIT for explicit approval to proceed
```

### STEP 2: DRY Principle Validation (MANDATORY)
For each proposed database change:

```sql
-- REQUIRED CHECKS BEFORE CREATING ANYTHING:

-- Table Creation Check:
SELECT table_name, column_name, data_type 
FROM information_schema.columns 
WHERE table_schema = 'your_schema' 
AND (column_name LIKE '%similar_pattern%' OR table_name LIKE '%similar_concept%');

-- Index Redundancy Check:
SELECT indexname, tablename, indexdef 
FROM pg_indexes 
WHERE schemaname = 'your_schema' 
AND (indexname LIKE '%similar%' OR indexdef LIKE '%similar_columns%');

-- Function Duplication Check:
SELECT routine_name, routine_type, specific_name 
FROM information_schema.routines 
WHERE routine_schema = 'your_schema' 
AND routine_name LIKE '%similar_functionality%';
```

## 🔍 DATABASE ANALYSIS REQUIREMENTS

### Before Creating Tables:
1. **Scan for Similar Entities**: Check if tables with similar purpose/structure exist
2. **Analyze Column Overlap**: Identify potential for table consolidation
3. **Review Naming Conventions**: Ensure consistency with existing schema
4. **Validate Relationships**: Check if FK relationships can reuse existing tables

### Before Creating Indexes:
1. **Index Coverage Analysis**: Check if existing indexes cover the same columns
2. **Composite Index Optimization**: Verify if existing composite indexes can be extended
3. **Performance Impact Assessment**: Analyze maintenance overhead of new indexes
4. **Query Pattern Mapping**: Ensure index aligns with actual query patterns

### Before Creating Functions:
1. **Functionality Overlap Scan**: Search for existing functions with similar logic
2. **Parameter Pattern Analysis**: Check if existing functions can be modified/overloaded
3. **Return Type Consistency**: Ensure consistent return patterns across schema
4. **Performance Baseline**: Compare with existing function performance patterns

## 🛠️ MCP ROOMMATE_DBL INTEGRATION PROTOCOL

### Required MCP Commands Sequence:
```bash
# 1. Database Discovery
mcp_roommate_dbl.scan_schema()
mcp_roommate_dbl.list_tables_detailed()
mcp_roommate_dbl.analyze_indexes()
mcp_roommate_dbl.catalog_functions()

# 2. Redundancy Detection
mcp_roommate_dbl.find_similar_tables(proposed_table_schema)
mcp_roommate_dbl.detect_index_overlap(proposed_indexes)
mcp_roommate_dbl.identify_function_duplicates(proposed_function)

# 3. DRY Validation Report
mcp_roommate_dbl.generate_dry_report()
mcp_roommate_dbl.suggest_consolidation_opportunities()
```

### MCP Response Requirements:
- **Green Light**: Only proceed if MCP confirms no redundancy detected
- **Yellow Warning**: Require explicit justification for potential redundancy
- **Red Flag**: Mandatory refactoring before any new database artifacts

## 📝 MIGRATION FILE CREATION PROTOCOL

### File Header Requirements:
```sql
-- Migration File: [YYYY-MM-DD-HHMMSS]_descriptive_name.sql
-- MCP Analysis Completed: [TIMESTAMP]
-- DRY Validation: [PASSED/FAILED/JUSTIFIED]
-- Redundancy Check: [CLEAN/WARNINGS/CONSOLIDATED]
-- Reviewed By: [MCP_ROOMMATE_DBL_VERSION]
-- 
-- EXISTING ARTIFACTS ANALYSIS:
-- Similar Tables: [LIST OR 'NONE']
-- Overlapping Indexes: [LIST OR 'NONE'] 
-- Related Functions: [LIST OR 'NONE']
--
-- CONSOLIDATION OPPORTUNITIES:
-- [LIST ANY IDENTIFIED OPTIMIZATIONS]
--
-- JUSTIFICATION FOR NEW ARTIFACTS:
-- [DETAILED EXPLANATION IF CREATING NEW ITEMS]
```

### Migration Content Structure:
```sql
-- =====================================================
-- PRE-MIGRATION SAFETY CHECKS
-- =====================================================
DO $$
BEGIN
    -- Verify no conflicting artifacts exist
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'proposed_table') THEN
        RAISE EXCEPTION 'Table already exists - DRY principle violation detected';
    END IF;
    
    -- Additional safety checks based on MCP analysis
    -- [INSERT SPECIFIC CHECKS FROM MCP REPORT]
END $$;

-- =====================================================
-- MIGRATION CONTENT (ONLY AFTER MCP APPROVAL)
-- =====================================================
-- [ACTUAL MIGRATION CONTENT HERE]

-- =====================================================
-- POST-MIGRATION VALIDATION
-- =====================================================
-- Verify created artifacts align with DRY principles
-- [INSERT VALIDATION QUERIES]
```

## 🚦 APPROVAL WORKFLOW

### Required Checkpoints:
1. **MCP Analysis Complete**: ✅ Database scan finished
2. **DRY Validation Passed**: ✅ No redundancy detected or justified
3. **Performance Review**: ✅ Impact assessment completed
4. **Documentation Updated**: ✅ Schema docs reflect changes
5. **Rollback Plan Ready**: ✅ Reversion strategy documented

### Escalation Triggers:
- **Redundancy Detected**: Must justify or consolidate existing artifacts
- **Performance Concerns**: Require optimization analysis
- **Schema Complexity**: Consider normalization/denormalization review

## 🔧 DEVELOPMENT GUIDELINES

### Code Generation Rules:
```typescript
// BEFORE generating any database code:
interface DatabaseChangeRequest {
  mcpAnalysisComplete: boolean;
  dryValidationStatus: 'PASSED' | 'JUSTIFIED' | 'FAILED';
  redundancyReport: RedundancyReport;
  consolidationOpportunities: ConsolidationSuggestion[];
  approvalTimestamp: Date;
}

// ONLY proceed if all validations pass:
function validateDatabaseChange(request: DatabaseChangeRequest): boolean {
  return request.mcpAnalysisComplete && 
         request.dryValidationStatus !== 'FAILED' &&
         request.approvalTimestamp !== null;
}
```

### Refactoring Requirements:
- **Before Adding**: Always check if existing artifacts can be modified/extended
- **Before Duplicating**: Mandatory consolidation analysis
- **Before Indexing**: Verify query patterns actually require new indexes
- **Before Function Creation**: Check if existing functions can be parameterized

## 📊 MONITORING & COMPLIANCE

### Daily Checks:
- Review all migrations for DRY compliance
- Validate MCP integration is functioning
- Audit for any unauthorized database changes
- Check schema evolution aligns with consolidation goals

### Weekly Reviews:
- Comprehensive redundancy analysis across entire schema
- Performance impact assessment of recent changes
- Documentation synchronization with actual schema
- Consolidation opportunity identification

### Monthly Optimization:
- Full schema refactoring analysis
- Index optimization review
- Function consolidation opportunities
- Table normalization assessment
## ⚡ EMERGENCY PROCEDURES

### If MCP Server Unavailable:
1. **HALT all database migrations immediately**
2. **Document emergency justification**
3. **Create manual redundancy check protocol**
4. **Schedule post-emergency MCP validation**

### Critical System Requirements:
- Database migrations are **BLOCKED** without MCP validation
- Manual overrides require **executive approval**
- All emergency changes must be **retroactively validated**
- Schema changes without MCP analysis are **prohibited**

---

## 🎯 SUCCESS METRICS

- **Zero Redundant Tables**: No tables with overlapping purposes
- **Optimized Index Coverage**: Minimal indexes with maximum query coverage
- **Consolidated Functions**: No duplicate business logic in database layer
- **Schema Clarity**: Clear, consistent naming and structure
- **Performance Stability**: No degradation from redundant artifacts

**REMEMBER: Every database change is a long-term architectural decision. MCP analysis and DRY principles are not optional - they are mandatory for maintainable, performant database design.**
