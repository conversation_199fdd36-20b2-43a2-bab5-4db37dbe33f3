<svg width="1080" height="1920" viewBox="0 0 1080 1920" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background Gradient -->
  <defs>
    <!-- Main background gradient -->
    <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f0f9ff;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e0f2fe;stop-opacity:1" />
    </linearGradient>
    
    <!-- Primary brand gradient -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#38bdf8;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#0ea5e9;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0284c7;stop-opacity:1" />
    </linearGradient>
    
    <!-- Secondary accent gradient -->
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#597EF7;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#2F54EB;stop-opacity:0.6" />
    </linearGradient>
    
    <!-- Organic shape gradients -->
    <radialGradient id="organicGradient1" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#bae6fd;stop-opacity:0.4" />
      <stop offset="100%" style="stop-color:#0ea5e9;stop-opacity:0.1" />
    </radialGradient>
    
    <radialGradient id="organicGradient2" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#D6E4FF;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#597EF7;stop-opacity:0.1" />
    </radialGradient>
  </defs>
  
  <!-- Background -->
  <rect width="1080" height="1920" fill="url(#backgroundGradient)"/>
  
  <!-- Organic Background Shapes -->
  <!-- Large organic blob - top left -->
  <path d="M-50,200 C150,100 300,150 400,300 C500,450 450,600 300,650 C150,700 50,550 -50,400 Z" 
        fill="url(#organicGradient1)" opacity="0.6"/>
  
  <!-- Medium organic blob - top right -->
  <path d="M800,50 C950,80 1050,200 1100,350 C1150,500 1000,580 850,550 C700,520 650,350 700,200 C720,150 760,70 800,50 Z" 
        fill="url(#organicGradient2)" opacity="0.5"/>
  
  <!-- Large organic blob - bottom -->
  <path d="M200,1400 C400,1350 600,1380 800,1500 C1000,1620 950,1800 750,1850 C550,1900 350,1850 200,1700 C50,1550 100,1450 200,1400 Z" 
        fill="url(#organicGradient1)" opacity="0.4"/>
  
  <!-- Small accent shapes -->
  <circle cx="150" cy="800" r="60" fill="url(#accentGradient)" opacity="0.3"/>
  <ellipse cx="900" cy="1200" rx="80" ry="50" fill="url(#organicGradient2)" opacity="0.4"/>
  <path d="M750,400 C800,380 850,420 870,470 C890,520 850,560 800,550 C750,540 720,480 730,430 C735,405 745,385 750,400 Z" 
        fill="url(#primaryGradient)" opacity="0.2"/>
  
  <!-- Central Logo Container -->
  <g transform="translate(540, 800)">
    <!-- Logo background circle with subtle shadow -->
    <circle cx="0" cy="0" r="120" fill="#ffffff" opacity="0.95" filter="drop-shadow(0 8px 32px rgba(14, 165, 233, 0.15))"/>
    
    <!-- Official WeRoomies Logo -->
    <!-- Blue gradient background matching the logo -->
    <circle cx="0" cy="0" r="100" fill="url(#primaryGradient)"/>
    
    <!-- Two people figures (simplified representation of the logo) -->
    <g transform="scale(1.2)">
      <!-- Left person -->
      <g transform="translate(-25, 0)">
        <!-- Head -->
        <circle cx="0" cy="-20" r="12" fill="#ffffff" stroke="none"/>
        <!-- Body -->
        <path d="M-15,10 Q-15,-5 0,-5 Q15,-5 15,10 L15,35 Q15,40 10,40 L-10,40 Q-15,40 -15,35 Z" 
              fill="#ffffff" stroke="none"/>
      </g>
      
      <!-- Right person -->
      <g transform="translate(25, 0)">
        <!-- Head -->
        <circle cx="0" cy="-20" r="12" fill="#ffffff" stroke="none"/>
        <!-- Body -->
        <path d="M-15,10 Q-15,-5 0,-5 Q15,-5 15,10 L15,35 Q15,40 10,40 L-10,40 Q-15,40 -15,35 Z" 
              fill="#ffffff" stroke="none"/>
      </g>
      
      <!-- House roof/frame outline -->
      <path d="M-60,-35 L0,-65 L60,-35 L50,-35 L0,-55 L-50,-35 Z" 
            fill="none" stroke="#ffffff" stroke-width="3" opacity="0.9"/>
      
      <!-- Connection/unity element between people -->
      <path d="M-10,15 Q0,5 10,15" 
            fill="none" stroke="#ffffff" stroke-width="2" opacity="0.8"/>
    </g>
  </g>
  
  <!-- App Name -->
  <g transform="translate(540, 1000)">
    <!-- Main title -->
    <text x="0" y="0" text-anchor="middle" 
          font-family="system-ui, -apple-system, sans-serif" 
          font-size="72" 
          font-weight="700" 
          fill="url(#primaryGradient)"
          letter-spacing="-2px">WeRoomies</text>
    
    <!-- Subtitle -->
    <text x="0" y="50" text-anchor="middle" 
          font-family="system-ui, -apple-system, sans-serif" 
          font-size="24" 
          font-weight="400" 
          fill="#64748b"
          letter-spacing="1px">Find Your Perfect Match</text>
  </g>
  
  <!-- Decorative Elements -->
  <!-- Floating particles -->
  <circle cx="200" cy="600" r="4" fill="#38bdf8" opacity="0.6">
    <animate attributeName="opacity" values="0.6;1;0.6" dur="3s" repeatCount="indefinite"/>
  </circle>
  <circle cx="850" cy="700" r="3" fill="#597EF7" opacity="0.5">
    <animate attributeName="opacity" values="0.5;0.9;0.5" dur="4s" repeatCount="indefinite"/>
  </circle>
  <circle cx="300" cy="1100" r="5" fill="#0ea5e9" opacity="0.4">
    <animate attributeName="opacity" values="0.4;0.8;0.4" dur="2.5s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Organic connecting lines -->
  <path d="M100,900 Q300,850 500,900 T900,950" 
        stroke="url(#primaryGradient)" 
        stroke-width="2" 
        fill="none" 
        opacity="0.3"
        stroke-dasharray="5,10">
    <animate attributeName="stroke-dashoffset" values="0;15;0" dur="6s" repeatCount="indefinite"/>
  </path>
  
  <!-- Bottom accent wave -->
  <path d="M0,1700 Q270,1650 540,1700 T1080,1700 L1080,1920 L0,1920 Z" 
        fill="url(#organicGradient1)" 
        opacity="0.3"/>
</svg> 