#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Function to fix syntax errors in a file
function fixSyntaxErrors(content) {
  let fixed = content;

  // Fix 1: Remove semicolons after opening braces in object literals and interfaces
  fixed = fixed.replace(/\{\s*;/g, '{');
  
  // Fix 2: Fix malformed function parameters like "$2 =>"
  fixed = fixed.replace(/\$2\s*=>/g, '() =>');
  fixed = fixed.replace(/\$(\d+)\s*=>/g, '(param$1) =>');
  
  // Fix 3: Fix incomplete import statements
  fixed = fixed.replace(/import\s*\{\s*;/g, 'import {');
  
  // Fix 4: Fix semicolons after closing braces in JSX
  fixed = fixed.replace(/>\s*;\s*$/gm, '>');
  fixed = fixed.replace(/\/>\s*;\s*$/gm, '/>');
  fixed = fixed.replace(/}\s*;\s*$/gm, '}');
  
  // Fix 5: Fix malformed JSX style props
  fixed = fixed.replace(/style=\{\{([^}]+)\}\}/g, 'style={[$1]}');
  fixed = fixed.replace(/color=\{\{([^}]+)\}\}/g, 'color={$1}');
  
  // Fix 6: Fix semicolons in object properties
  fixed = fixed.replace(/:\s*([^,}\n]*)\s*;\s*$/gm, ': $1,');
  
  // Fix 7: Fix arrow function syntax
  fixed = fixed.replace(/=>\s*\{\s*;/g, '=> {');
  
  // Fix 8: Fix StyleSheet.create syntax
  fixed = fixed.replace(/StyleSheet\.create\(\{\s*;/g, 'StyleSheet.create({');
  
  // Fix 9: Fix function return type syntax
  fixed = fixed.replace(/\)\s*=>\s*;/g, ') =>');
  
  // Fix 10: Fix logger.error calls with incomplete parentheses
  fixed = fixed.replace(/logger\.error\(\s*;/g, 'logger.error(');
  
  // Fix 11: Fix incomplete array/object destructuring
  fixed = fixed.replace(/const\s+\{\s*([^}]*)\s*\}\s*,\s*$/gm, 'const { $1 };');
  fixed = fixed.replace(/const\s+\[\s*([^\]]*)\s*\]\s*,\s*$/gm, 'const [$1];');
  
  // Fix 12: Fix incomplete Promise return types
  fixed = fixed.replace(/Promise<([^>]*)>\s*=>\s*\{\s*;/g, 'Promise<$1> => {');
  
  // Fix 13: Fix malformed try-catch blocks
  fixed = fixed.replace(/\}\s*catch\s*\(\s*error\s*\)\s*\{\s*;/g, '} catch (error) {');
  
  // Fix 14: Fix incomplete function calls
  fixed = fixed.replace(/\(\s*;/g, '(');
  fixed = fixed.replace(/,\s*;/g, ',');
  
  // Fix 15: Fix incomplete conditional statements
  fixed = fixed.replace(/if\s*\(\s*;/g, 'if (');
  
  return fixed;
}

// Function to recursively find and fix files
function fixFilesInDirectory(dir) {
  const files = fs.readdirSync(dir);
  
  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      // Skip node_modules and .git directories
      if (file !== 'node_modules' && file !== '.git' && file !== '.expo') {
        fixFilesInDirectory(filePath);
      }
    } else if (file.endsWith('.ts') || file.endsWith('.tsx')) {
      try {
        const content = fs.readFileSync(filePath, 'utf8');
        const fixed = fixSyntaxErrors(content);
        
        if (fixed !== content) {
          fs.writeFileSync(filePath, fixed, 'utf8');
          console.log(`✅ Fixed: ${filePath}`);
        }
      } catch (error) {
        console.error(`❌ Error fixing ${filePath}:`, error.message);
      }
    }
  }
}

// Start fixing from src directory
const srcDir = path.join(process.cwd(), 'src');
if (fs.existsSync(srcDir)) {
  console.log('🔧 Starting syntax error fixes...');
  fixFilesInDirectory(srcDir);
  console.log('✅ Syntax error fixes completed!');
} else {
  console.error('❌ src directory not found');
}
