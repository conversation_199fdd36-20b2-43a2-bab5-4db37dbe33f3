{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": false, "noImplicitAny": false, "skipLibCheck": true, "allowJs": true, "checkJs": false, "noEmit": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "jsx": "react-native", "lib": ["dom", "esnext"], "moduleResolution": "node", "noUnusedLocals": false, "noUnusedParameters": false, "resolveJsonModule": true}, "exclude": ["node_modules", "src/utils/**/*", "src/services/**/*", "src/components/debug/**/*", "src/components/admin/**/*", "src/app/admin/**/*", "src/core/**/*", "**/*.test.ts", "**/*.test.tsx"], "include": ["src/app/tabs/**/*", "src/app/(auth)/**/*", "src/components/ui/**/*", "src/components/auth/**/*", "src/types/index.ts"]}