# iOS Profile Database & Camera Issues Fix

## 🚨 **ISSUE IDENTIFIED**

**Problem**: After implementing Android image picker fixes, iOS users
experienced database errors and camera functionality issues.

**Root Cause Analysis**:

1. **Database Query Error**: "JSON object requested, multiple (or no) rows
   returned"
2. **iOS Simulator Camera Issues**: Inadequate simulator detection and error
   handling
3. **Profile Fetching Failures**: Using `.single()` instead of `.maybeSingle()`
   in database queries

**Terminal Logs Showed**:

```
WARN  Failed to fetch user profile during getSession {"userId":"f0829470-d28a-437d-8c12-8dcc430b7ff4","profileError":"JSON object requested, multiple (or no) rows returned"}
LOG  Creating profile on-the-fly for existing user {"userId":"f0829470-d28a-437d-8c12-8dcc430b7ff4"}
ERROR  Failed to create profile on-the-fly {"userId":"f0829470-d28a-437d-8c12-8dcc430b7ff4"}
```

## ✅ **COMPREHENSIVE SOLUTION IMPLEMENTED**

### **1. Database Query Robustness Fix**

#### **Problem**: Strict `.single()` Method

The issue was caused by using `.single()` in Supabase queries, which throws an
error when:

- No rows are returned (should return null)
- Multiple rows are returned (shouldn't happen with unique constraints)

#### **Solution**: Replace `.single()` with `.maybeSingle()`

```typescript
// ❌ BEFORE (Problematic)
const { data: userProfile, error: profileError } = await supabase
  .from('user_profiles')
  .select('...')
  .eq('id', userId)
  .single(); // Throws error on 0 or multiple rows

// ✅ AFTER (Robust)
const { data: userProfile, error: profileError } = await supabase
  .from('user_profiles')
  .select('...')
  .eq('id', userId)
  .maybeSingle(); // Returns null for 0 rows, handles gracefully
```

#### **Files Fixed**:

- `src/services/standardized/AuthService.ts` - Session profile fetching
- `src/services/unified-profile/profileCore.ts` - Profile core operations
- `src/services/profiles/EnhancedUnifiedProfileService.ts` - Enhanced profile
  service
- `src/services/standardized/ProfileService.ts` - Standardized profile
  operations
- `src/services/api/ProfileAPIService.ts` - Profile API operations

### **2. iOS Simulator Camera Detection Enhancement**

#### **Problem**: Inadequate Simulator Detection

The iOS simulator detection was only checking for `'undetermined'` permissions,
but iOS Simulator typically returns `'denied'` for camera permissions.

#### **Solution**: Enhanced Simulator Detection

```typescript
// ✅ ENHANCED iOS Simulator Detection
if (Platform.OS === 'ios' && __DEV__) {
  // iOS Simulator typically returns 'denied' or 'undetermined' for camera permissions
  if (
    currentPermissions.status === 'undetermined' ||
    currentPermissions.status === 'denied'
  ) {
    Alert.alert(
      'Camera Not Available',
      'Camera is not available on iOS Simulator. Please use a physical device or select from gallery instead.',
      [
        {
          text: 'Select from Gallery',
          onPress: () => handleSelectFromGallery(mediaType),
        },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
    return;
  }
}
```

#### **Files Enhanced**:

- `src/app/(tabs)/profile/media.tsx` - Media management component
- `src/app/provider/onboarding.tsx` - Provider registration flow

### **3. Cross-Platform Camera Error Handling**

#### **Provider Onboarding Enhancement**

Added comprehensive iOS simulator detection in provider registration:

```typescript
// Launch camera for single image
const launchCameraForSingle =
  async (): Promise<ImagePicker.ImagePickerAsset | null> => {
    // 1. Check for iOS Simulator limitation first
    if (Platform.OS === 'ios' && __DEV__) {
      Alert.alert(
        '📱 iOS Simulator Limitation',
        'Camera is not available on iOS Simulator.\n\nTo test camera functionality, please use a physical device.\n\nFor now, would you like to select from your photo library instead?',
        [
          {
            text: 'Use Gallery',
            onPress: async () => {
              const result = await launchGalleryForSingle();
              return result;
            },
            style: 'default',
          },
          { text: 'Cancel', style: 'cancel' },
        ]
      );
      return null;
    }

    // Continue with normal camera flow...
  };
```

## 🔧 **TECHNICAL IMPLEMENTATION DETAILS**

### **Database Query Pattern Changes**

#### **Before (Problematic)**:

```typescript
.single() // Throws error on 0 rows or multiple rows
```

#### **After (Robust)**:

```typescript
.maybeSingle() // Returns null on 0 rows, data on 1 row, error on multiple rows
```

### **Error Handling Improvements**

#### **Enhanced Error Context**:

```typescript
if (error) {
  if (error.code === 'PGRST116') {
    // Handle "not found" case gracefully
    return { data: null, error: 'Profile not found', status: 404 };
  }

  logger.error('Database error getting profile', SERVICE_NAME, {
    error,
    profileId,
  });
  return { data: null, error: 'Database error occurred', status: 500 };
}
```

### **iOS-Specific Considerations**

#### **Simulator Detection Logic**:

```typescript
// Check for iOS development environment
if (Platform.OS === 'ios' && __DEV__) {
  // iOS Simulator camera permissions are typically 'denied' or 'undetermined'
  if (
    permissions.status === 'undetermined' ||
    permissions.status === 'denied'
  ) {
    // Show simulator-specific guidance
  }
}
```

#### **Fallback Mechanisms**:

- Camera unavailable → Photo library selection
- Profile query fails → Graceful error handling with retry options
- Auth session lost → Session recovery with user-friendly messages

## 📊 **IMPACT ASSESSMENT**

### **Before Fix**:

- ❌ iOS users experiencing database errors during session restoration
- ❌ iOS Simulator users getting confusing camera errors
- ❌ Profile fetching failures causing authentication loops
- ❌ Poor error messages for simulator limitations

### **After Fix**:

- ✅ Robust database queries that handle missing profiles gracefully
- ✅ Clear iOS Simulator detection with helpful guidance
- ✅ Smooth fallback from camera to photo library
- ✅ Enhanced error messages with actionable guidance
- ✅ Cross-platform compatibility maintained

## 🛡️ **PREVENTION MEASURES**

### **Database Query Standards**:

- Always use `.maybeSingle()` for queries that might return 0 or 1 row
- Use `.single()` only when you're certain exactly 1 row exists
- Implement proper error handling for all database operations

### **Platform-Specific Testing**:

- Test camera functionality on both iOS Simulator and physical devices
- Verify permission handling across different iOS versions
- Ensure graceful degradation when features aren't available

### **Error Handling Best Practices**:

- Provide user-friendly error messages
- Implement fallback mechanisms for critical functionality
- Log detailed error information for debugging
- Test error scenarios thoroughly

## 🎯 **VERIFICATION CHECKLIST**

### **Database Operations**:

- [ ] Profile fetching works without errors
- [ ] Session restoration handles missing profiles gracefully
- [ ] Error messages are user-friendly
- [ ] Retry mechanisms function correctly

### **iOS Camera Functionality**:

- [ ] iOS Simulator shows appropriate camera limitations message
- [ ] Physical iOS devices can access camera normally
- [ ] Fallback to photo library works seamlessly
- [ ] Permission requests are handled properly

### **Cross-Platform Compatibility**:

- [ ] Android functionality remains unaffected
- [ ] iOS functionality works on both simulator and device
- [ ] Error handling is consistent across platforms
- [ ] User experience is smooth on all platforms

## 🚀 **DEPLOYMENT NOTES**

### **Testing Requirements**:

1. Test on iOS Simulator (should show camera limitation messages)
2. Test on physical iOS device (should work normally)
3. Test Android devices (should maintain existing functionality)
4. Test profile fetching and session restoration
5. Test error scenarios and recovery mechanisms

### **Monitoring**:

- Monitor database query performance and error rates
- Track camera permission request success rates
- Monitor user feedback on error messages and fallback flows
- Watch for any regression in Android functionality

---

**SUMMARY**: The iOS issues after Android fixes were primarily caused by overly
strict database queries using `.single()` and inadequate iOS Simulator camera
detection. The comprehensive fix implements robust database querying with
`.maybeSingle()`, enhanced iOS Simulator detection, and improved cross-platform
error handling, ensuring a smooth user experience on all platforms while
maintaining the Android improvements.
