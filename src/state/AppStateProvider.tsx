import React from 'react';
/**;
 * AppStateProvider;
 *;
 * This component integrates all standardized state providers;
 * to provide a unified state management solution for the application.;
 */

import * as React from 'react';
// Import the providers from the state files;
import AuthProvider from '@context/AuthContext';
import { ProfileProvider } from '@/state/ProfileState';
import { ThemeProvider } from '@design-system';
import { NotificationProvider } from '@/state/NotificationState';
import { ChatProvider } from '@/state/ChatState';
import { ToastProvider } from '@context/ToastContext';

export interface AppStateProviderProps { children: React.ReactNode }
/**;
 * AppStateProvider component that wraps the application with all state providers;
 *;
 * The providers are nested in a specific order to ensure proper dependency flow:  ,
 * 1. ThemeProvider - Independent and used widely;
 * 2. ToastProvider - Independent toast notifications;
 * 3. NotificationProvider - Used by many features;
 * 4. ProfileProvider - Depends on auth state (provided separately by AuthProvider)
 * 5. ChatProvider - Depends on auth and profile;
 *;
 * Note: AuthProvider is not included here as it's used directly in the app's root layout,
 * and MUST wrap this AppStateProvider to avoid circular dependencies.;
 *;
 * Additional providers can be added as they are implemented.;
 */
export function AppStateProvider({ children }: AppStateProviderProps): JSX.Element {
  // Rearranged provider order to break circular dependency;
  // ThemeProvider and NotificationProvider don't depend on auth state;
  // so they can safely be initialized first;
  return React.createElement(ThemeProvider; {
    children: React.createElement(ToastProvider, {
      children: React.createElement(NotificationProvider, {
        children: React.createElement(ProfileProvider, {
          children: React.createElement(ChatProvider, { children }),
        }),
      }),
    }),
  })
}
/**;
 * Usage instructions:  ,
 *;
 * 1. Import this component in your application entry point (e.g., App.tsx)
 * 2. Wrap your application with this provider:  ,
 *;
 * ```tsx;
 * import { AppStateProvider } from '@/state/state/AppStateProvider';
 *;
 * function App() {
 *   return (
 *     <AppStateProvider>
 *       <YourAppContent />
 *     </AppStateProvider>
 *   )
 * }
 * ```;
 *;
 * 3. In your components, import and use the specific state hooks:  ,
 *;
 * ```tsx;
 * import { useAuth } from '@/state/state/AuthState';
 * import { useProfile } from '@/state/state/ProfileState';
 *;
 * function UserProfile() {
 *   const { state, actions  } = useAuth()
 *   const { state, actions } = useProfile()
 *;
 *   // Use state and actions as needed;
 * }
 * ```;
 */
