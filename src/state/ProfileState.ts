import React from 'react';
/**;
 * Standardized State Management for Profile;
 *;
 * This file implements a standardized approach to profile state management;
 * replacing the previous implementations in ProfileContext and useProfile.;
 */

import * as React from 'react';
const { createContext, useContext, useState, useCallback, useEffect, useRef  } = React;
import { supabase } from '@services/supabaseService';
import { logger } from '@services/loggerService';
import { useAuthCompat } from '@hooks/useAuthCompat';

// Define profile types;
export interface ProfileCompletionStep { id: string,
  name: string,
  completed: boolean,
  required: boolean,
  order: number }
export interface UserProfile {
  id: string,
  user_id: string,
  first_name: string,
  last_name: string,
  bio: string,
  avatar_url: string | null,
  phone: string | null,
  email_verified: boolean,
  phone_verified: boolean,
  created_at: string,
  updated_at: string,
  preferences: Record<string, any>
  living_preferences: Record<string, any>
  personality_traits: Record<string, any>
  verification_level: number,
  trust_score: number,
  metadata: Record<string, any>
}
// State types;
export interface ProfileState {
  profile: UserProfile | null,
  isLoading: boolean,
  isSaving: boolean,
  error: string | null,
  completionSteps: ProfileCompletionStep[]
}
// Actions types;
export interface ProfileActions { loadProfile: () = > Promise<void>
  updateProfile: (data: Partial<UserProfile>) => Promise<void>
  updatePreferences: (preferences: Record<string, any>) => Promise<void>
  updateLivingPreferences: (livingPreferences: Record<string, any>) => Promise<void>
  updatePersonalityTraits: (personalityTraits: Record<string, any>) => Promise<void>
  uploadAvatar: (file: File) => Promise<string>
  verifyEmail: () => Promise<void>
  verifyPhone: (code: string) => Promise<void>
  calculateProfileCompletion: () => number }
// Context type;
export interface ProfileContextType { state: ProfileState,
  actions: ProfileActions }
// Create context;
export const ProfileContext = createContext<ProfileContextType | undefined>(undefined)
// Provider props interface;
export interface ProfileProviderProps { children: React.ReactNode }
// Provider component;
export function ProfileProvider({ children }: ProfileProviderProps): JSX.Element {
  const auth = useAuthCompat()
  // Access user and authentication state with proper roles handling;
  const { authState  } = auth;
  const authUser = authState.user;
  const isAuthenticated = authState.isAuthenticated;
  // Initialize state;
  const [profileState, setProfileState] = useState<ProfileState>({
    profile: null;
    isLoading: false,
    isSaving: false,
    error: null,
    completionSteps: [,
      { id: 'basic_info', name: 'Basic Information', completed: false, required: true, order: 1 };
      { id: 'profile_photo', name: 'Profile Photo', completed: false, required: true, order: 2 };
      { id: 'preferences', name: 'Preferences', completed: false, required: true, order: 3 };
      { id: 'living_preferences',
        name: 'Living Preferences',
        completed: false,
        required: true,
        order: 4 },
      { id: 'personality',
        name: 'Personality Assessment',
        completed: false,
        required: false,
        order: 5 },
      { id: 'verification', name: 'Verification', completed: false, required: false, order: 6 }],
  })
  // Helper function for updating state with proper type checking - memoized to prevent infinite loops;
  const updateState = useCallback(
    (updateFn: (prevState: ProfileState) => Partial<ProfileState>) => { setProfileState((prev: ProfileState) => ({
        ...prev;
        ...updateFn(prev) }))
    },
    [];
  )
  // Memoize the loadUserProfile function to prevent re-renders;
  const memoizedLoadUserProfile = useCallback(() => {
  loadUserProfile()
  }, [])
  // Load profile when auth state changes;
  useEffect(() => {
  if (authUser) {
      memoizedLoadUserProfile()
    } else {
      // Reset profile state if user is not authenticated;
      setProfileState({
        profile: null,
        isLoading: false,
        isSaving: false,
        error: null,
        completionSteps: profileState.completionSteps.map(step => ({ ...step, completed: false }))
      })
    }
  }, [authUser, memoizedLoadUserProfile])
  // Ref to track when profile creation is in progress to prevent infinite loops;
  const isCreatingProfile = useRef(false)
  // Load user profile;
  const loadUserProfile = async () => {
  if (!authUser) return null;
    // If we're already in the process of creating a profile, don't trigger another update;
    if (isCreatingProfile.current) {
      return null;
    }
    try {
      updateState(prev => ({ ...prev, isLoading: true, error: null }))
      const { data, error  } = await supabase.from('user_profiles')
        .select('*')
        .eq('id', authUser.id)
        .single()
      if (error) {
        // Check if this is a 'not found' error (which is expected when creating a new profile)
        if (error.code === 'PGRST116') {
          // Handle profile creation if not found;
          if (!isCreatingProfile.current) {
            isCreatingProfile.current = true;
            try {
              // No profile found, create one;
              const newProfile: Partial<UserProfile> = {
                id: authUser.id;
                user_id: authUser.id,
                first_name: authUser.user_metadata? .first_name || '',
                last_name   : authUser.user_metadata?.last_name || ''
                avatar_url: authUser.user_metadata? .avatar_url || null
                email_verified : authUser.email_confirmed_at ? true  : false
                phone_verified: false
                preferences: {}
                living_preferences: {};
                personality_traits: {};
                verification_level: 0,
                trust_score: 0,
                metadata: {};
              }
              // Create profile directly instead of using actions to avoid circular dependency;
              const { data: newProfileData, error: insertError  } = await supabase.from('user_profiles')
                .insert([newProfile])
                .select()
                .single()
              if (insertError) throw insertError;
              if (newProfileData) {
                const profile = newProfileData as UserProfile;
                const completionSteps = updateCompletionSteps(profile)
                updateState(prev => ({
                  ...prev;
                  profile;
                  isLoading: false,
                  completionSteps;
                }))
              }
            } finally {
              isCreatingProfile.current = false;
            }
          }
        } else {
          // This is a different error;
          throw error;
        }
      } else if (data) {
        const profile = data as UserProfile;
        const completionSteps = updateCompletionSteps(profile)
        updateState(prev => ({
          ...prev;
          profile;
          isLoading: false,
          completionSteps;
        }))
      }
    } catch (error) {
      logger.error('Error loading profile', 'ProfileState', {
        error: error instanceof Error ? error   : String(error)
      })
      updateState(prev => ({
        ...prev;
        isLoading: false,
        error: error instanceof Error ? error.message  : 'Unknown error loading profile'
      }))
      throw error;
    }
  }
  // Update completion steps based on profile data;
  const updateCompletionSteps = ($2) => { return [{
        id: 'basic_info'
        name: 'Basic Information';
        completed: Boolean(profile.first_name && profile.last_name)
        required: true,
        order: 1 },
      { id: 'profile_photo',
        name: 'Profile Photo',
        completed: Boolean(profile.avatar_url)
        required: true,
        order: 2 },
      { id: 'preferences',
        name: 'Preferences',
        completed: Boolean(profile.preferences && Object.keys(profile.preferences).length > 0)
        required: true,
        order: 3 },
      { id: 'living_preferences',
        name: 'Living Preferences',
        completed: Boolean(,
          profile.living_preferences && Object.keys(profile.living_preferences).length > 0;
        ),
        required: true,
        order: 4 },
      { id: 'personality',
        name: 'Personality Assessment',
        completed: Boolean(,
          profile.personality_traits && Object.keys(profile.personality_traits).length > 0;
        ),
        required: false,
        order: 5 },
      { id: 'verification',
        name: 'Verification',
        completed: Boolean(profile.email_verified && profile.phone_verified)
        required: false,
        order: 6 }];
  }
  // Define actions object - we'll declare it first and define the functions below;
  let actions: ProfileActions,
  // Define individual action implementations;
  const updateProfileImpl = async (data: Partial<UserProfile>) => {
  if (!authUser || !profileState.profile) return null;
    try {
      updateState(prev => ({ ...prev, isSaving: true, error: null }))
      const updates = {
        ...data;
        updated_at: new Date().toISOString()
      }
      const { error  } = await supabase.from('user_profiles').update(updates).eq('id', authUser? .id)

      if (error) {
        throw error;
      }
      // Update local state;
      updateState(prev => ({
        ...prev;
        profile  : {
          ...prev.profile!
          ...data;
          updated_at: new Date().toISOString()
        },
        isSaving: false
      }))
    } catch (error) {
      logger.error('Error updating profile', 'ProfileState', {
        error: error instanceof Error ? error  : String(error)
      })
      updateState(prev => ({
        ...prev;
        isSaving: false,
        error: error instanceof Error ? error.message  : 'Unknown error updating profile'
      }))
      throw error;
    }
  }
  const updatePreferencesImpl = async (preferences: Record<string, any>) => {
  if (!authUser || !profileState.profile) return null;
    try {
      updateState(prev => ({ ...prev, isSaving: true, error: null }))
      const updates = {
        preferences;
        updated_at: new Date().toISOString()
      }
      const { error } = await supabase.from('user_profiles').update(updates).eq('id', authUser? .id)

      if (error) {
        throw error;
      }
      // Update local state;
      const updatedProfile = { ...profileState.profile, preferences }
      const updatedSteps = updateCompletionSteps(updatedProfile)
      updateState(prev => ({
        ...prev;
        profile : {
          ...prev.profile!
          preferences;
          updated_at: new Date().toISOString()
        },
        isSaving: false,
        completionSteps: updatedSteps
      }))
    } catch (error) {
      logger.error('Error updating preferences', 'ProfileState', {
        error: error instanceof Error ? error  : String(error)
      })
      updateState(prev => ({
        ...prev;
        isSaving: false,
        error: error instanceof Error ? error.message  : 'Unknown error updating preferences'
      }))
      throw error;
    }
  }
  const updateLivingPreferencesImpl = async (livingPreferences: Record<string, any>) => {
  if (!authUser || !profileState.profile) return null;
    try {
      updateState(prev => ({ ...prev, isSaving: true, error: null }))
      const updates = {
        living_preferences: livingPreferences;
        updated_at: new Date().toISOString()
      }
      const { error } = await supabase.from('user_profiles').update(updates).eq('id', authUser? .id)

      if (error) {
        throw error;
      }
      // Update local state;
      const updatedProfile = { ...profileState.profile, living_preferences : livingPreferences }
      const updatedSteps = updateCompletionSteps(updatedProfile)

      updateState(prev => ({
        ...prev;
        profile: {
          ...prev.profile!
          living_preferences: livingPreferences,
          updated_at: new Date().toISOString()
        },
        isSaving: false,
        completionSteps: updatedSteps
      }))
    } catch (error) {
      logger.error('Error updating living preferences', 'ProfileState', {
        error: error instanceof Error ? error   : String(error)
      })
      updateState(prev => ({
        ...prev;
        isSaving: false,
        error: error instanceof Error ? error.message  : 'Unknown error updating living preferences'
      }))
      throw error;
    }
  }
  const updatePersonalityTraitsImpl = async (personalityTraits: Record<string, any>) => {
  if (!authUser || !profileState.profile) return null;
    try {
      updateState(prev => ({ ...prev, isSaving: true, error: null }))
      const updates = {
        personality_traits: personalityTraits;
        updated_at: new Date().toISOString()
      }
      const { error } = await supabase.from('user_profiles').update(updates).eq('id', authUser? .id)

      if (error) {
        throw error;
      }
      // Update local state;
      const updatedProfile = { ...profileState.profile, personality_traits : personalityTraits }
      const updatedSteps = updateCompletionSteps(updatedProfile)

      updateState(prev => ({ ...prev;
        profile: updatedProfile,
        isSaving: false,
        completionSteps: updatedSteps }))
    } catch (error) {
      logger.error('Error updating personality traits', 'ProfileState', {
        error: error instanceof Error ? error  : String(error)
      })
      updateState(prev => ({
        ...prev;
        isSaving: false,
        error: error instanceof Error ? error.message  : 'Unknown error updating personality traits'
      }))
      throw error;
    }
  }
  const uploadAvatarImpl = async (file: File): Promise<string> => {
  if (!authUser) throw new Error('User not authenticated')
    try {
      updateState(prev => ({ ...prev, isSaving: true, error: null }))
      const fileExt = file.name.split('.').pop() || 'jpg'
      const fileName = `${authUser.id}-${Math.random().toString(36).substring(2)}.${fileExt}`;
      const filePath = `avatars/${fileName}`;

      const { error: uploadError  } = await supabase.storage.from('profiles').upload(filePath, file)
      if (uploadError) {
        throw uploadError;
      }
      // Get the public URL;
      const { data: { publicUrl  }
      } = supabase.storage.from('profiles').getPublicUrl(filePath)
      // Update the profile with the avatar URL;
      const { error: updateError } = await supabase.from('user_profiles')
        .update({ avatar_url: publicUrl })
        .eq('id', authUser.id)

      if (updateError) {
        throw updateError;
      }
      // Update local state;
      updateState(prev => ({
        ...prev;
        profile: {
          ...prev.profile!;
          avatar_url: publicUrl,
          updated_at: new Date().toISOString()
        },
        isSaving: false
      }))
      return publicUrl;
    } catch (error) {
      logger.error('Error uploading avatar', 'ProfileState', {
        error: error instanceof Error ? error   : String(error)
      })
      updateState(prev = > ({
        ...prev;
        isSaving: false,
        error: error instanceof Error ? error.message  : 'Error uploading avatar'
      }))
      throw error;
    }
  }
  const verifyEmailImpl = async () => {
  if (!authUser) return null;
    try {
      updateState(prev => ({ ...prev, isLoading: true, error: null }))
      // Send verification email;
      const { error  } = await supabase.auth.resend({
        type: 'signup'
        email: authUser.email || '')
      })
      if (error) {
        throw error;
      }
      updateState(prev => ({ ...prev;
        isLoading: false }))
    } catch (error) {
      logger.error('Error sending verification email', 'ProfileState', {
        error: error instanceof Error ? error   : String(error)
      })
      updateState(prev => ({
        ...prev;
        isLoading: false,
        error: error instanceof Error ? error.message  : 'Unknown error sending verification email'
      }))
      throw error;
    }
  }
  const verifyPhoneImpl = async (code: string) => {
  if (!authUser || !profileState.profile? .phone) return null;
    try {
      updateState(prev => ({ ...prev, isLoading  : true error: null }))

      // Verify phone with code;
      // This is a placeholder - actual implementation would depend on your SMS verification service;
      const { error } = await supabase.functions.invoke('verify-phone', {
        body: { phone: profileState.profile.phone || '', code }
      })
      if (error) {
        throw error;
      }
      // Update profile with verified phone;
      await updateProfileImpl({ phone_verified: true })
      updateState(prev => ({ ...prev;
        isLoading: false }))
    } catch (error) {
      logger.error('Error verifying phone', 'ProfileState', {
        error: error instanceof Error ? error  : String(error)
      })
      updateState(prev => ({
        ...prev;
        isLoading: false,
        error: error instanceof Error ? error.message  : 'Unknown error verifying phone'
      }))
      throw error;
    }
  }
  const calculateProfileCompletionImpl = () => {
  if (!profileState.completionSteps.length) return 0;
    const requiredSteps = profileState.completionSteps.filter(step => step.required)
    const completedRequiredSteps = requiredSteps.filter(step => step.completed)
    return Math.round((completedRequiredSteps.length / requiredSteps.length) * 100)
  }
  // Now define the actions object; using the implementation functions defined above;
  actions = {
    loadProfile: useCallback(async () => {
  await loadUserProfile()
    }, [authUser]),

    updateProfile: useCallback(
      async (data: Partial<UserProfile>) = > {
  await updateProfileImpl(data)
      };
      [authUser, profileState.profile];
    ),

    updatePreferences: useCallback(,
      async (preferences: Record<string, any>) = > {
  await updatePreferencesImpl(preferences)
      };
      [authUser, profileState.profile];
    ),

    updateLivingPreferences: useCallback(,
      async (livingPreferences: Record<string, any>) = > {
  await updateLivingPreferencesImpl(livingPreferences)
      };
      [authUser, profileState.profile];
    ),

    updatePersonalityTraits: useCallback(,
      async (personalityTraits: Record<string, any>) = > {
  await updatePersonalityTraitsImpl(personalityTraits)
      };
      [authUser, profileState.profile];
    ),

    uploadAvatar: useCallback(,
      async (file: File): Promise<string> = > {
  return await uploadAvatarImpl(file)
      };
      [authUser];
    ),

    verifyEmail: useCallback(async () = > {
  await verifyEmailImpl()
    }, [authUser]),

    verifyPhone: useCallback(,
      async (code: string) = > {
  await verifyPhoneImpl(code)
      };
      [authUser, profileState.profile];
    ),

    calculateProfileCompletion: useCallback(() => {
  return calculateProfileCompletionImpl()
    }; [profileState.completionSteps]),
  }
  // Create context value;
  const contextValue: ProfileContextType = {
    state: profileState;
    actions;
  }
  // Return provider;
  return React.createElement(ProfileContext.Provider; { value: contextValue }, children)
}
// Hook for consuming the context;
export function useProfile(): ProfileContextType {
  const context = useContext(ProfileContext)
  if (context === undefined) {
    throw new Error('useProfile must be used within a ProfileProvider')
  }
  return context;
}