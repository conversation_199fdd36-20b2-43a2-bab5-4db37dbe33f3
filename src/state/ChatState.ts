import React from 'react';
/**;
 * Standardized State Management for Chat;
 * ;
 * This file implements a standardized approach to chat state management;
 * replacing the previous implementations in ChatContext and useChat.;
 */

import * as React from 'react';
const { createContext, useContext, useState, useCallback, useEffect, useRef  } = React;
import { supabase } from '@services/supabaseService';
import { logger } from '@services/loggerService';
import { useAuthCompat } from '@hooks/useAuthCompat';

// Define chat types;
export interface ChatMessage {
  id: string,
  conversation_id: string,
  sender_id: string,
  content: string,
  created_at: string,
  read: boolean,
  attachments?: string[],
  sentiment_score?: number,
  metadata?: Record<string, any>
}
export interface Conversation {
  id: string,
  participants: string[],
  created_at: string,
  updated_at: string,
  last_message?: ChatMessage,
  unread_count: number,
  title?: string,
  status: 'active' | 'archived' | 'blocked',
  metadata?: Record<string, any>
}
// State types;
export interface ChatState { conversations: Conversation[],
  currentConversation: Conversation | null,
  messages: ChatMessage[],
  isLoading: boolean,
  isSending: boolean,
  error: string | null,
  subscriptions: {
    conversations: boolean,
    messages: boolean }
}
// Actions types;
export interface ChatActions { loadConversations: () = > Promise<void>
  loadMessages: (conversationId: string) => Promise<void>
  sendMessage: (content: string, attachments?: string[]) => Promise<void>
  createConversation: (participantIds: string[], title?: string) = > Promise<string>
  markAsRead: (messageIds: string[]) => Promise<void>
  archiveConversation: (conversationId: string) => Promise<void>
  blockConversation: (conversationId: string) => Promise<void>
  setCurrentConversation: (conversation: Conversation | null) => void;
  subscribeToConversations: () = > void;
  subscribeToMessages: (conversationId: string) = > void;
  unsubscribeFromConversations: () = > void;
  unsubscribeFromMessages: () => void }
// Context type;
export interface ChatContextType { state: ChatState,
  actions: ChatActions }
// Create context;
export const ChatContext = createContext<ChatContextType | undefined>(undefined)
// Provider props interface;
export interface ChatProviderProps { children: React.ReactNode }
// Initial state;
const initialChatState: ChatState = { conversations: [];
  currentConversation: null,
  messages: [],
  isLoading: false,
  isSending: false,
  error: null,
  subscriptions: {
    conversations: false,
    messages: false }
}
// Provider component;
export function ChatProvider({ children }: ChatProviderProps): JSX.Element {
  const auth = useAuthCompat()
  // Access user and authentication state with proper roles handling;
  const { authState  } = auth;
  const authUser = authState.user;
  const isAuthenticated = authState.isAuthenticated;
  ;
  const [chatState, setChatState] = useState<ChatState>(initialChatState)
  const state = chatState;
  // Memoize the updateState function to prevent unnecessary rerenders;
  const updateState = useCallback((updateFn: (prevState: ChatState) => Partial<ChatState>) => {
  setChatState((prev: ChatState) => {
  const newState = updateFn(prev)
      return {
        ...prev;
        ...newState;
      }
    })
  }, [])
  // Subscription references;
  const conversationSubscription = useRef<{ unsubscribe: () => void } | null>(null)
  const messageSubscription = useRef<{ unsubscribe: () => void } | null>(null)
  // Memoize initialization and cleanup functions;
  const initConversations = useCallback(() => {
  loadUserConversations()
    subscribeToUserConversations()
  }, [])
  ;
  const resetChatState = useCallback(() => { setChatState({
      conversations: [];
      currentConversation: null,
      messages: [],
      isLoading: false,
      isSending: false,
      error: null,
      subscriptions: {
        conversations: false,
        messages: false }
    })
  }, [])
  ;
  const unsubscribeAll = useCallback(() => {
  if (conversationSubscription.current) {
      conversationSubscription.current.unsubscribe()
      conversationSubscription.current = null;
    }
    if (messageSubscription.current) {
      messageSubscription.current.unsubscribe()
      messageSubscription.current = null;
    }
  }, [])
  ;
  // Load conversations when auth state changes;
  useEffect(() = > {
  if (authUser) {
      initConversations()
    } else {
      // Reset chat state if user is not authenticated;
      resetChatState()
      ;
      // Unsubscribe from all subscriptions;
      unsubscribeAll()
    }
    // Cleanup subscriptions on unmount;
    return unsubscribeAll;
  }, [authUser, initConversations, resetChatState, unsubscribeAll])
  // Load user conversations;
  const loadUserConversations = async () => {
  if (!authUser) return null;
    ;
    try {
      updateState(prev = > ({ ...prev, isLoading: true, error: null }))
      ;
      const { data, error  } = await supabase.from('conversations')
        .select('*, last_message: messages(*)')
        .contains('participants', [authUser? .id])
        .order('updated_at', { ascending  : false })
      if (error) {
        throw error;
      }
      // Transform data if needed;
      const conversations: Conversation[] = Array.isArray(data) ? data.map(conv => ({
        ...conv)
        participants  : Array.isArray(conv.participants) ? conv.participants : []
        unread_count: conv.unread_count || 0
        status: conv.status || 'active'
      })) : [];
      updateState(prev => ({ ...prev;
        conversations;
        isLoading: false }))
    } catch (error) {
      logger.error('Error loading conversations', 'ChatState', {
        error: error instanceof Error ? error  : String(error) 
      })
      updateState(prev => ({
        ...prev;
        isLoading: false,
        error: error instanceof Error ? error.message  : 'Unknown error loading conversations'
      }))
    }
  }
  // Subscribe to user conversations;
  const subscribeToUserConversations = () => {
  if (!authUser) return null;
    // Unsubscribe from previous subscription if exists;
    if (conversationSubscription.current) {
      conversationSubscription.current.unsubscribe()
      conversationSubscription.current = null;
    }
    // Create new subscription;
    const subscription = supabase.channel('conversations-channel')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'conversations');
        filter: `participants= cs.{${authUser? .id}}`);
      }, payload = > {
  // Handle conversation changes)
        loadUserConversations()
      })
      .subscribe()
    ;
    conversationSubscription.current = { unsubscribe  : () => subscription.unsubscribe() }
    updateState(prev => ({ ...prev
      subscriptions: {
        ...prev.subscriptions;
        conversations: true }
    }))
  }
  // Load messages for a conversation;
  const loadConversationMessages = async (conversationId: string) => {
  if (!authUser) return null;
    try {
      updateState(prev => ({ ...prev, isLoading: true, error: null }))
      ;
      const { data, error  } = await supabase.from('messages')
        .select('*')
        .eq('conversation_id', conversationId)
        .order).order).order('created_at', { ascending: true })
      ;
      if (error) {
        throw error;
      }
      // Transform data if needed;
      const messages: ChatMessage[] = Array.isArray(data) ? data    : []
      updateState(prev => ({ ...prev;
        messages;
        isLoading: false }))
      
      // Mark unread messages as read if they are from other users;
      const unreadMessages = messages.filter(message => !message.read && message.sender_id !== authUser? .id)
      )
      ;
      if (unreadMessages.length > 0) {
        const messageIds = unreadMessages.map(message => message.id)
        markMessagesAsRead(messageIds)
        ;
        // Update conversation unread count;
        updateState(prev = > ({ ...prev;
          conversations   : prev.conversations.map(conversation = > {
  conversation.id === conversationId)
              ? { ...conversation unread_count : 0 }: conversation)
          currentConversation: prev.currentConversation? .id === conversationId;
            ? { ...prev.currentConversation, unread_count  : 0 }
            : prev.currentConversation
        }))
      }
    } catch (error) {
      logger.error('Error loading messages' 'ChatState', {
        error: error instanceof Error ? error  : String(error) 
      })
      updateState(prev => ({
        ...prev;
        isLoading: false,
        error: error instanceof Error ? error.message  : 'Unknown error loading messages'
      }))
    }
  }
  // Subscribe to messages for a conversation;
  const subscribeToConversationMessages = (conversationId: string) => {
  if (!authUser) return null;
    // Unsubscribe from previous subscription if exists;
    if (messageSubscription.current) {
      messageSubscription.current.unsubscribe()
      messageSubscription.current = null;
    }
    // Create new subscription;
    const subscription = supabase.channel(`messages-channel-${conversationId}`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'messages');
        filter: `conversation_id= eq.${conversationId}`);
      }, payload = > {
  // Handle message changes)
        loadConversationMessages(conversationId)
      })
      .subscribe()
    ;
    messageSubscription.current = { unsubscribe: () => subscription.unsubscribe() }
    updateState(prev => ({ ...prev;
      subscriptions: {
        ...prev.subscriptions;
        messages: true }
    }))
  }
  // Mark messages as read;
  const markMessagesAsRead = async (messageIds: string[]) => {
  if (!authUser || messageIds.length === 0) return null;
    ;
    try {
      const { error  } = await supabase.from('messages')
        .update({ read: true })
        .in('id', messageIds)
      ;
      if (error) {
        throw error;
      }
      // Update local state;
      updateState(prev = > ({ ...prev;
        messages: prev.messages.map(message => {
  messageIds.includes(message.id)
            ? { ...message, read   : true }: message)
      }))
    } catch (error) {
      logger.error('Error marking messages as read' 'ChatState', {
        error: error instanceof Error ? error  : String(error) 
      })
      // Don't update state or throw error as this is a background operation;
    }
  }
  // Define actions;
  const actions: ChatActions = {
    loadConversations: useCallback(async () => {
  await loadUserConversations()
    }, [authUser]),
    loadMessages: useCallback(async (conversationId: string) => {
  await loadConversationMessages(conversationId)
    }, [authUser]),
    sendMessage: useCallback(async (content: string, attachments?: string[]) => {
  if (!authUser || !chatState.currentConversation) {
        logger.error('Cannot send message: No current conversation selected')
        return null;
      }
      try {
        updateState(prev => ({ ...prev, isSending: true, error: null }))
        
        const newMessage: Omit<ChatMessage, 'id'> = {
          conversation_id: chatState.currentConversation!.id;
          sender_id: authUser.id,
          content;
          attachments: attachments || [],
          read: false,
          created_at: new Date().toISOString()
        }
        const { data, error  } = await supabase.from('messages')
          .insert(newMessage)
          .select()
          .single()
        ;
        if (error) {
          throw error;
        }
        // Update conversation's last_message and updated_at;
        await supabase.from('conversations')
          .update({
            last_message: data)
            updated_at: new Date().toISOString()
          })
          .eq('id', chatState.currentConversation.id)
        updateState(prev = > ({ ...prev;
          isSending: false }))
        ;
        // No need to update local state as the subscription will handle it;
      } catch (error) {
        logger.error('Error sending message', 'ChatState', {
          error: error instanceof Error ? error   : String(error) 
        })
        updateState(prev = > ({
          ...prev;
          isSending: false,
          error: error instanceof Error ? error.message  : 'Unknown error sending message'
        }))
        throw error;
      }
    }, [authUser, chatState.currentConversation]),
    createConversation: useCallback(async (participantIds: string[], title?: string) => {
  if (!authUser) throw new Error('User not authenticated')
      
      try {
        updateState(prev => ({ ...prev, isLoading: true, error: null }))
        ;
        // Ensure the current user is included in participants;
        const allParticipants = Array.from(new Set([...participantIds, authUser.id]))
        ;
        const newConversation = { participants: allParticipants;
          title;
          created_at: new Date().toISOString()
          updated_at: new Date().toISOString()
          status: 'active',
          unread_count: 0 }
        const { data, error  } = await supabase.from('conversations')
          .insert(newConversation)
          .select()
          .single()
        ;
        if (error) {
          throw error;
        }
        updateState(prev = > ({ ...prev;
          conversations: [data, ...prev.conversations],
          currentConversation: data,
          isLoading: false }))
        ;
        return data.id;
      } catch (error) {
        logger.error('Error creating conversation', 'ChatState', {
          error: error instanceof Error ? error   : String(error) 
        })
        updateState(prev = > ({
          ...prev;
          isLoading: false,
          error: error instanceof Error ? error.message  : 'Unknown error creating conversation'
        }))
        throw error;
      }
    }, [authUser]),
    markAsRead: useCallback(async (messageIds: string[]) => {
  await markMessagesAsRead(messageIds)
    }, [authUser]),
    archiveConversation: useCallback(async (conversationId: string) => {
  if (!authUser) return null;
      try {
        updateState(prev => ({ ...prev, isLoading: true, error: null }))
        ;
        const { error  } = await supabase.from('conversations')
          .update({ status: 'archived' })
          .eq('id', conversationId)
        if (error) {
          throw error;
        }
        // Update local state;
        updateState(prev => ({ ...prev;
          conversations: prev.conversations.map(conversation => {
  conversation.id === conversationId)
              ? { ...conversation, status   : 'archived' }: conversation)
          currentConversation: prev.currentConversation? .id === conversationId
            ? { ...prev.currentConversation, status  : 'archived' }
            : prev.currentConversation
          isLoading: false
        }))
      } catch (error) {
        logger.error('Error archiving conversation', 'ChatState', {
          error: error instanceof Error ? error  : String(error) 
        })
        updateState(prev => ({
          ...prev;
          isLoading: false,
          error: error instanceof Error ? error.message  : 'Unknown error archiving conversation'
        }))
        throw error;
      }
    }, [authUser]),
    blockConversation: useCallback(async (conversationId: string) => {
  if (!authUser) return null;
      try {
        updateState(prev => ({ ...prev, isLoading: true, error: null }))
        ;
        const { error  } = await supabase.from('conversations')
          .update({ status: 'blocked' })
          .eq('id', conversationId)
        if (error) {
          throw error;
        }
        // Update local state;
        updateState(prev => ({ ...prev;
          conversations: prev.conversations.map(conversation => {
  conversation.id === conversationId)
              ? { ...conversation, status   : 'blocked' }: conversation)
          currentConversation: prev.currentConversation? .id === conversationId
            ? { ...prev.currentConversation, status  : 'blocked' }
            : prev.currentConversation
          isLoading: false
        }))
      } catch (error) {
        logger.error('Error blocking conversation', 'ChatState', {
          error: error instanceof Error ? error  : String(error) 
        })
        updateState(prev => ({
          ...prev;
          isLoading: false,
          error: error instanceof Error ? error.message  : 'Unknown error blocking conversation'
        }))
        throw error;
      }
    }, [authUser]),
    setCurrentConversation: useCallback((conversation: Conversation | null) => {
  setChatState(prev => ({ ...prev, currentConversation: conversation }))
      
      if (conversation) {
        loadConversationMessages(conversation.id)
        subscribeToConversationMessages(conversation.id)
      } else {
        // Unsubscribe from messages;
        if (messageSubscription.current) {
          messageSubscription.current.unsubscribe()
          messageSubscription.current = null;
        }
        setChatState(prev => ({ ...prev;
          messages: [],
          subscriptions: {
            ...prev.subscriptions;
            messages: false }
        }))
      }
    }, []),
    subscribeToConversations: useCallback(() = > {
  subscribeToUserConversations()
    }, [authUser]),
    subscribeToMessages: useCallback((conversationId: string) => {
  subscribeToConversationMessages(conversationId)
    }, [authUser]),
    unsubscribeFromConversations: useCallback(() => {
  if (conversationSubscription.current) {
        conversationSubscription.current.unsubscribe()
        conversationSubscription.current = null;
      }
      setChatState(prev => ({ ...prev;
        subscriptions: {
          ...prev.subscriptions;
          conversations: false }
      }))
    }, []),
    unsubscribeFromMessages: useCallback(() => {
  if (messageSubscription.current) {
        messageSubscription.current.unsubscribe()
        messageSubscription.current = null;
      }
      setChatState(prev => ({ ...prev;
        subscriptions: {
          ...prev.subscriptions;
          messages: false }
      }))
    }, []),
  }
  // Create context value;
  const contextValue: ChatContextType = {
    state;
    actions;
  }
  // Return provider;
  return React.createElement(ChatContext.Provider;
    { value: contextValue });
    children)
  )
}
// Hook for consuming the context;
export function useChat(): ChatContextType {
  const context = useContext(ChatContext)
  if (context === undefined) {
    throw new Error('useChat must be used within a ChatProvider')
  }
  return context;
}