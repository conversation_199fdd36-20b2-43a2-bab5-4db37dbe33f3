import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack' // Import debug screens;
import DebugScreen from '@screens/debug/DebugScreen';
import CompatibilityLayerDashboard from '@components/debug/CompatibilityLayerDashboard';
import EnvSecurityTestScreen from '@screens/debug/EnvSecurityTestScreen';
import ConnectionPoolMonitor from '@components/monitoring/ConnectionPoolMonitor';
import ConnectionPoolDashboard from '@components/monitoring/ConnectionPoolDashboard' // Define the debug navigator param list;
export type DebugStackParamList = { DebugHome: undefined,
  CompatibilityDashboard: undefined,
  EnvSecurityTest: undefined,
  ConnectionPoolMonitor: undefined,
  ConnectionPoolDashboard: undefined,
  NetworkInspector: undefined,
  PerformanceMonitor: undefined,
  FeatureFlags: undefined }
// Create the stack navigator;
const Stack = createNativeStackNavigator<DebugStackParamList>()
/**;
 * Debug Stack Navigator;
 * Contains all debug-related screens that should only be accessible in development;
 */
export function DebugNavigator() { return (
    <Stack.Navigator;
      initialRouteName= 'DebugHome';
      screenOptions= {{ {
        headerShown: true,
        headerStyle: {
          backgroundColor: '#1E293B'  }},
        headerTintColor: '#FFFFFF',
        headerTitleStyle: {
          fontWeight: 'bold'
        },
        animation: 'slide_from_right'
      }}
    >
      <Stack.Screen;
        name='DebugHome';
        component= "DebugScreen"
        options={{ title: 'Developer Tools'   }}
      />
      <Stack.Screen;
        name='CompatibilityDashboard';
        component= "CompatibilityLayerDashboard"
        options={{ title: 'Compatibility Layer'   }}
      />
      <Stack.Screen;
        name='EnvSecurityTest';
        component= "EnvSecurityTestScreen"
        options={{ title: 'Environment Security'   }}
      />
      <Stack.Screen;
        name='ConnectionPoolMonitor';
        component= "ConnectionPoolMonitor"
        options={{ title: 'Connection Pool Monitor'   }}
      />
      <Stack.Screen;
        name='ConnectionPoolDashboard';
        component= "ConnectionPoolDashboard"
        options={{ title: 'Connection Pool Dashboard'   }}
      />
      {/* Other debug screens will be added here as they're implemented */}
      {/* These screens are commented out until they're implemented */}
      { /*;
      <Stack.Screen;
        name= "NetworkInspector";
        component= "NetworkInspector" options={{ title: 'Network Inspector'   }}
      />
      <Stack.Screen;
        name="PerformanceMonitor";
        component= "PerformanceMonitor" options={{ title: 'Performance Monitor'   }}
      />
      <Stack.Screen;
        name="FeatureFlags";
        component= "FeatureFlags" options={{ title: 'Feature Flags'   }}
      />
      */}
    </Stack.Navigator>
  )
}
export default DebugNavigator;