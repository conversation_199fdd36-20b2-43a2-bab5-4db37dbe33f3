import type { ReactNode } from 'react';
import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';

import { useToast } from '@core/errors';
import { unifiedRoomService } from '@services/enhanced/UnifiedRoomService';
import type { RoomWithDetails, RoomFormData } from '@services/enhanced/UnifiedRoomService';
import type { RoomState } from '@types/models';

import { useAuth } from '@context/AuthContext';

interface RoomContextType { roomState: RoomState,
  fetchRooms: (,
    limit?: number,
    offset?: number,
    filters?: {
      minPrice?: number,
      maxPrice?: number,
      location?: string,
      roomType?: string,
      bedrooms?: number,
      bathrooms?: number,
      furnished?: boolean,
      petsAllowed?: boolean }
  ) = > Promise<void>
  fetchRoomById: (id: string) => Promise<RoomWithDetails | null>
  createRoom: (data: RoomFormData) => Promise<RoomWithDetails | null>
  updateRoom: (id: string, data: Partial<RoomFormData>) => Promise<RoomWithDetails | null>
  deleteRoom: (id: string) => Promise<boolean>
  saveRoom: (id: string) => Promise<boolean>
  unsaveRoom: (id: string) => Promise<boolean>
  fetchSavedRooms: (limit?: number, offset?: number) = > Promise<void>
  clearRoomState: () => void
}
const initialRoomState: RoomState = { rooms: [],
  savedRooms: [],
  currentRoom: null,
  isLoading: false,
  error: null }
// Create the context;
const RoomContext = createContext<RoomContextType | undefined>(undefined)
// Provider component;
interface RoomProviderProps { children: ReactNode }
export function RoomProvider({ children }: RoomProviderProps) {
  const [roomState, setRoomState] = useState<RoomState>(initialRoomState)
  const toast = useToast()
  const { state: authState } = useAuth()
  // Create a memoized wrapper for showToast to prevent render loops;
  const showToast = useCallback(
    (message: string, type: 'success' | 'error' | 'warning' | 'info') => {
      try {
        if (toast? .showToast) {
          toast.showToast(type, message)
        } else {
          console.warn(`Toast message (${type})   : ${message}`)
        }
      } catch (err) {
        console.error('Error showing toast:' err)
      }
    },
    [toast]
  )
  // Clear room state when user logs out;
  useEffect(() => {
    if (!authState.isAuthenticated) {
      clearRoomState()
    }
  }, [authState.isAuthenticated])
  // Fetch rooms with optional filters;
  const fetchRooms = async (
    limit = 20;
    offset = 0;
    filters?: { minPrice?: number,
      maxPrice?: number,
      location?: string,
      roomType?: string,
      bedrooms?: number,
      bathrooms?: number,
      furnished?: boolean,
      petsAllowed?: boolean }
  ) => {
    setRoomState(prev => ({ ...prev, isLoading: true, error: null }))
    try {
      // MIGRATION: Convert legacy filters to UnifiedRoomService format,
      const unifiedFilters = filters;
        ? {
            minPrice   : filters.minPrice
            maxPrice: filters.maxPrice
            location: filters.location,
            roomType: filters.roomType,
            amenities: [], // Legacy filters don't map directly to amenities;
            preferences: [], // Legacy filters don't map directly to preferences;
          }
        : undefined,
      const page = Math.floor(offset / limit) + 1;
      const userId = authState.user? .id;
      const result = await unifiedRoomService.searchRooms(undefined;
        unifiedFilters;
        page;
        limit;
        userId)
      )
      if (result.error) {
        setRoomState(prev => ({ ...prev, isLoading  : false error: result.error }))
        showToast(result.error, 'error')
        return null;
      }
      const rooms = result.data? .data || []
      setRoomState(prev => ({
        ...prev;
        rooms  : offset = == 0 ? rooms  : [...prev.rooms ...rooms];
        isLoading: false
      }))
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message   : 'Failed to fetch rooms'
      setRoomState(prev => ({ ...prev isLoading: false, error: errorMessage }))
      showToast(errorMessage, 'error')
    }
  }
  // Fetch a single room by ID;
  const fetchRoomById = async (id: string): Promise<RoomWithDetails | null> => {
    setRoomState(prev => ({ ...prev, isLoading: true, error: null }))
    try {
      const userId = authState.user? .id;
      const result = await unifiedRoomService.getRoomById(id, userId)
      if (result.error) {
        setRoomState(prev => ({ ...prev, isLoading  : false error: result.error }))
        showToast(result.error, 'error')
        return null;
      }
      setRoomState(prev => ({ ...prev;
        currentRoom: result.data || null,
        isLoading: false }))
      return result.data || null;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message   : 'Failed to fetch room'
      setRoomState(prev => ({ ...prev isLoading: false, error: errorMessage }))
      showToast(errorMessage, 'error')
      return null;
    }
  }
  // Create a new room;
  const createRoom = async (data: RoomFormData): Promise<RoomWithDetails | null> => {
    setRoomState(prev => ({ ...prev, isLoading: true, error: null }))
    try {
      const userId = authState.user? .id;
      if (!userId) {
        throw new Error('User must be authenticated to create a room')
      }
      const result = await unifiedRoomService.createRoom(data, userId)
      if (result.error) {
        setRoomState(prev => ({ ...prev, isLoading  : false error: result.error }))
        showToast(result.error, 'error')
        return null;
      }
      const roomWithDetails = result.data!

      setRoomState(prev => ({ ...prev;
        rooms: [roomWithDetails, ...prev.rooms],
        currentRoom: roomWithDetails,
        isLoading: false }))
      showToast('Room created successfully', 'success')
      return roomWithDetails;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message    : 'Failed to create room'
      setRoomState(prev => ({ ...prev isLoading: false, error: errorMessage }))
      showToast(errorMessage, 'error')
      return null;
    }
  }
  // Update a room;
  const updateRoom = async (
    id: string,
    data: Partial<RoomFormData>
  ): Promise<RoomWithDetails | null> => {
    setRoomState(prev => ({ ...prev, isLoading: true, error: null }))
    try {
      const userId = authState.user? .id;
      if (!userId) {
        throw new Error('User must be authenticated to update a room')
      }
      const result = await unifiedRoomService.updateRoom(id, data, userId)
      if (result.error) {
        setRoomState(prev => ({ ...prev, isLoading  : false error: result.error }))
        showToast(result.error, 'error')
        return null;
      }
      const updatedRoom = result.data!

      setRoomState(prev => ({ ...prev;
        rooms: prev.rooms.map(room => (room.id === id ? updatedRoom    : room))
        currentRoom: prev.currentRoom? .id === id ? updatedRoom  : prev.currentRoom
        isLoading: false }))
      showToast('Room updated successfully' 'success')
      return updatedRoom;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message   : 'Failed to update room'
      setRoomState(prev => ({ ...prev isLoading: false, error: errorMessage }))
      showToast(errorMessage, 'error')
      return null;
    }
  }
  // Delete a room;
  const deleteRoom = async (id: string): Promise<boolean> => {
    setRoomState(prev => ({ ...prev, isLoading: true, error: null }))
    try {
      const userId = authState.user? .id;
      if (!userId) {
        throw new Error('User must be authenticated to delete a room')
      }
      const result = await unifiedRoomService.deleteRoom(id, userId)
      if (result.error) {
        setRoomState(prev => ({ ...prev, isLoading  : false error: result.error }))
        showToast(result.error, 'error')
        return false;
      }
      setRoomState(prev => ({ ...prev;
        rooms: prev.rooms.filter(room => room.id !== id)
        currentRoom: prev.currentRoom? .id === id ? null   : prev.currentRoom
        isLoading: false }))
      showToast('Room deleted successfully' 'success')
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message   : 'Failed to delete room'
      setRoomState(prev => ({ ...prev isLoading: false, error: errorMessage }))
      showToast(errorMessage, 'error')
      return false;
    }
  }
  // Save a room;
  const saveRoom = async (id: string): Promise<boolean> => {
    try {
      const userId = authState.user? .id;
      if (!userId) {
        showToast('You must be logged in to save rooms', 'error')
        return false;
      }
      const result = await unifiedRoomService.saveRoom(id, userId)
      if (result.error) {
        showToast(result.error, 'error')
        return false;
      }
      showToast('Room saved successfully', 'success')
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message   : 'Failed to save room'
      showToast(errorMessage 'error')
      return false;
    }
  }
  // Unsave a room;
  const unsaveRoom = async (id: string): Promise<boolean> => {
    try {
      const userId = authState.user? .id;
      if (!userId) {
        showToast('You must be logged in to unsave rooms', 'error')
        return false;
      }
      const result = await unifiedRoomService.unsaveRoom(id, userId)
      if (result.error) {
        showToast(result.error, 'error')
        return false;
      }
      showToast('Room removed from saved list', 'success')
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message   : 'Failed to unsave room'
      showToast(errorMessage 'error')
      return false;
    }
  }
  // Fetch saved rooms;
  const fetchSavedRooms = async (limit = 20, offset = 0) => {
    setRoomState(prev => ({ ...prev, isLoading: true, error: null }))
    try {
      const userId = authState.user? .id;
      if (!userId) {
        setRoomState(prev => ({ ...prev, isLoading  : false error: 'User not authenticated' }))
        return null;
      }
      const page = Math.floor(offset / limit) + 1;
      const result = await unifiedRoomService.getSavedRooms(userId, page, limit)
      if (result.error) {
        setRoomState(prev => ({ ...prev, isLoading: false, error: result.error }))
        showToast(result.error, 'error')
        return null;
      }
      const savedRooms = result.data? .data || []
      setRoomState(prev => ({
        ...prev;
        savedRooms  : offset = == 0 ? savedRooms : [...prev.savedRooms ...savedRooms];
        isLoading: false
      }))
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message   : 'Failed to fetch saved rooms'
      setRoomState(prev => ({ ...prev isLoading: false, error: errorMessage }))
      showToast(errorMessage, 'error')
    }
  }
  // Clear room state;
  const clearRoomState = () => {
    setRoomState(initialRoomState)
  }
  const value: RoomContextType = {
    roomState;
    fetchRooms;
    fetchRoomById;
    createRoom;
    updateRoom;
    deleteRoom;
    saveRoom;
    unsaveRoom;
    fetchSavedRooms;
    clearRoomState;
  }
  return <RoomContext.Provider value={value}>{children}</RoomContext.Provider>
}
// Hook to use the room context;
export function useRoom(): RoomContextType {
  const context = useContext(RoomContext)
  if (!context) {
    throw new Error('useRoom must be used within a RoomProvider')
  }
  return context;
}
export default RoomProvider,