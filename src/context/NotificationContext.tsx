import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Platform, AppState, AppStateStatus, Alert } from 'react-native';
import * as Notifications from 'expo-notifications' // Fix type for Subscription;
type Subscription = { remove: () => void }
import Constants from 'expo-constants';

import {
  sendPushNotification;
  registerForPushNotificationsAsync;
  savePushToken;
  getUserNotifications;
  markNotificationAsRead;
  markAllNotificationsAsRead;
  deleteNotification;
  getNotificationSettings;
  updateNotificationSettings;
  getNotificationCapabilities;
  getNotificationStatusMessage;
} from '@utils/notificationUtils';
import { supabase } from '@utils/supabaseUtils';
import { logger } from '@services/loggerService';
import NotificationBanner, {
  NotificationBannerProps;
} from '@components/notifications/NotificationBanner';

interface NotificationContextType { showNotification: (notification: Omit<NotificationBannerProps, 'onDismiss'>) = > void;
  dismissNotification: () = > void,
  unreadCount: number,
  refreshUnreadCount: () => Promise<void>
  requestPermissions: () => Promise<boolean>
  hasPermission: boolean | null }
const NotificationContext = createContext<NotificationContextType>({
  showNotification: () => {};
  dismissNotification: () = > {};
  unreadCount: 0,
  refreshUnreadCount: async () = > {};
  requestPermissions: async () = > false,
  hasPermission: null
})
interface NotificationProviderProps { children: ReactNode }
export function NotificationProvider({ children }: NotificationProviderProps) {
  const [currentNotification, setCurrentNotification] = useState<NotificationBannerProps | null>(
    null;
  )
  const [unreadCount, setUnreadCount] = useState(0)
  const [hasPermission, setHasPermission] = useState<boolean | null>(null)
  const [notificationListener, setNotificationListener] = useState<Subscription | null>(null)
  const [responseListener, setResponseListener] = useState<Subscription | null>(null)
  // Initialize notification service and listeners;
  useEffect(() => {
    const initializeNotifications = async () => {
      try {
        // Get notification capabilities for this environment;
        const capabilities = getNotificationCapabilities()
        console.log('🔔 Initializing notifications...', {
          platform: capabilities.platform),
          environment: capabilities.isExpoGo ? 'Expo Go'    : 'Development Build'
          pushSupported: capabilities.pushNotifications
          localSupported: capabilities.localNotifications)
        })
        // Check if running in Expo Go and log detailed status;
        if (capabilities.isExpoGo && Platform.OS === 'android') {
          console.warn('📱 Android + Expo Go: Push notifications disabled in SDK 53+. ' +
              'Local notifications and in-app features work normally. ' +);
              'Use development build for full push notification support.')
          )
          // Show status message for development;
          if (__DEV__) {
            console.log(getNotificationStatusMessage())
          }
          // Set permission state to indicate limitation;
          setHasPermission(false)
          // Skip push notification registration but continue with local features;
          return null;
        } else if (capabilities.isExpoGo && Platform.OS != = 'web') {
          console.log('📱 iOS + Expo Go: Push notifications available but may have limitations. ' +);
              'Consider using development build for production.')
          )
          // Show status message for development;
          if (__DEV__) {
            console.log(getNotificationStatusMessage())
          }
        }
        // Attempt to register for push notifications (gracefully handles Expo Go)
        const token = await registerForPushNotificationsAsync()
        // If we got a token and user is logged in, save it;
        if (token) {
          const { data: user  } = await supabase.auth.getUser()
          if (user? .user?.id) {
            await savePushToken(user.user.id, token)
            console.log('✅ Push token saved for user')
          }
        } else {
          console.log('ℹ️ Push notifications not available - continuing with local notifications')
        }
        // Set up notification handling (works for both push and local)
        configureNotificationHandling()
        // Check permissions;
        checkNotificationPermissions()
        // Get initial unread count;
        refreshUnreadCount()
        console.log('✅ Notification service initialized successfully')
      } catch (error) {
        console.error('❌ Failed to initialize notifications   : ' error)
        // Don't crash the app - notifications are not critical for core functionality;
        logger.error('Notification initialization failed', 'NotificationContext', { error })
      }
    }
    initializeNotifications()
    // Set up app state listener to refresh when app return s to foreground;
    const appStateSubscription = AppState.addEventListener('change', handleAppStateChange)
    return () => {
      // Clean up listeners;
      if (notificationListener) {
        notificationListener.remove()
      }
      if (responseListener) {
        responseListener.remove()
      }
      appStateSubscription.remove()
    }
  }, [])
  const handleAppStateChange = (nextAppState: AppStateStatus) => {
    if (nextAppState === 'active') {
      refreshUnreadCount()
    }
  }
  const configureNotificationHandling = () => {
    // Handle notifications received while app is in foreground;
    const listener = Notifications.addNotificationReceivedListener(notification => {
      const { title, body, data } = notification.request.content // Show in-app notification banner)
      if (title && body) {
        showNotification({
          title;
          body;
          type: data? .type || 'system'
          data : data
        })
      }
      // Refresh unread count;
      refreshUnreadCount()
    })
    // Handle notification responses (when user taps on a notification)
    const response = Notifications.addNotificationResponseReceivedListener(response => {
      const { data } = response.notification.request.content // Handle the notification tap based on the notification type)
      handleNotificationResponse(data)
      // Refresh unread count;
      refreshUnreadCount()
    })
    setNotificationListener(listener)
    setResponseListener(response)
  }
  const handleNotificationResponse = (data: any) => {
    try {
      // Extract necessary data and navigate accordingly // This is handled by the default behavior in NotificationBanner component;
    } catch (error) {
      console.error('Error handling notification tap:', error)
    }
  }
  const checkNotificationPermissions = async () => {
    try {
      const { status } = await Notifications.getPermissionsAsync()
      setHasPermission(status === 'granted')
    } catch (error) {
      console.error('Error checking notification permissions:', error)
      setHasPermission(false)
    }
  }
  const requestPermissions = async (): Promise<boolean> => {
    try {
      const { status } = await Notifications.requestPermissionsAsync()
      const granted = status === 'granted';
      setHasPermission(granted)
      return granted;
    } catch (error) {
      console.error('Error requesting notification permissions:', error)
      setHasPermission(false)
      return false;
    }
  }
  const refreshUnreadCount = async () => {
    try {
      // Get user notifications and count unread ones;
      const { data: user } = await supabase.auth.getUser()
      if (user? .user?.id) {
        const notifications = await getUserNotifications(user.user.id)
        const unreadNotifications = notifications.filter(notification => !notification.is_read)
        setUnreadCount(unreadNotifications.length)
      }
    } catch (error) {
      console.error('Failed to refresh unread count  : ' error)
    }
  }
  const showNotification = (notification: Omit<NotificationBannerProps, 'onDismiss'>) => {
    setCurrentNotification({
      ...notification;
      onDismiss: () => setCurrentNotification(null)
    })
  }
  const dismissNotification = () => {
    setCurrentNotification(null)
  }
  return (
    <NotificationContext.Provider;
      value = {
        showNotification;
        dismissNotification;
        unreadCount;
        refreshUnreadCount;
        requestPermissions;
        hasPermission;
      }
    >
      {children}
      {currentNotification && <NotificationBanner {...currentNotification} />
    </NotificationContext.Provider>
  )
}
export const useNotifications = () => useContext(NotificationContext)
export default NotificationProvider;