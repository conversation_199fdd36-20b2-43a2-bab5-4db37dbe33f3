import React, { createContext, useContext, ReactNode } from 'react';
import Toast from 'react-native-toast-message';

interface ToastContextType { showToast: (type: 'success' | 'error' | 'info', message: string, description?: string) = > void }
const ToastContext = createContext<ToastContextType | undefined>(undefined)
export function useToast() {
  const context = useContext(ToastContext)
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider')
  }
  return context;
}
interface ToastProviderProps { children: ReactNode }
export function ToastProvider({ children }: ToastProviderProps) {
  const showToast = (type: 'success' | 'error' | 'info', message: string, description?: string) => {
    Toast.show({
      type;
      text1: message,
      text2: description),
      position: 'bottom'),
      visibilityTime: 4000,
      autoHide: true,
      topOffset: 30,
      bottomOffset: 40)
    })
  }
  return (
    <ToastContext.Provider value={ showToast }>
      {children}
      <Toast />
    </ToastContext.Provider>
  )
}
export default useToast;