/**;
 * MessagingContext.tsx;
 * Simplified context for messaging functionality;
 */
import React, {
  createContext;
  useContext;
  useReducer;
  useEffect;
  useState;
  useCallback;
} from 'react';
import { Message, ChatRoom } from '@services/unified/types';
import { unifiedChatService } from '@services/unified/UnifiedChatService';
import { useAuth } from '@context/AuthContext' // Context state interface;
interface MessagingState {
  rooms: ChatRoom[],
  messages: Record<string, Message[]>
  loading: {
    rooms: boolean,
    messages: Record<string, boolean>
  }
  error: string | null,
}
// Context value interface;
interface MessagingContextValue extends MessagingState {
  fetchRooms: () = > Promise<void>
  fetchMessages: (roomId: string) => Promise<void>
  sendMessage: (roomId: string, content: string) => Promise<boolean>
  createChatRoom: (otherUserId: string) => Promise<string | null>
  markAsRead: (roomId: string) => Promise<void>
}
// Create the context;
const MessagingContext = createContext<MessagingContextValue | undefined>(undefined)
// Action types for reducer;
type Action =;
  | { type: 'SET_ROOMS', payload: ChatRoom[] }
  | { type: 'SET_MESSAGES', payload: { roomId: string, messages: Message[] } }
  | { type: 'ADD_MESSAGE', payload: { roomId: string, message: Message } }
  | { type: 'SET_LOADING_ROOMS', payload: boolean }
  | { type: 'SET_LOADING_MESSAGES', payload: { roomId: string, loading: boolean } }
  | { type: 'SET_ERROR', payload: string | null }
  | { type: 'MARK_AS_READ', payload: { roomId: string } }
// Reducer function for state updates;
const messagingReducer = ($2) => { switch (action.type) {
    case 'SET_ROOMS':  ;
      return {
        ...state;
        rooms: action.payload }
    case 'SET_MESSAGES':  ,
      return { ...state;
        messages: {
          ...state.messages;
          [action.payload.roomId]: action.payload.messages },
      }
    case 'ADD_MESSAGE':  ,
      return { ...state;
        messages: {
          ...state.messages;
          [action.payload.roomId]: [,
            ...(state.messages[action.payload.roomId] || []),
            action.payload.message;
          ] },
        // Update room's last message if this room exists;
        rooms: state.rooms.map(room = > { room.id === action.payload.roomId;
            ? {
                ...room;
                last_message   : action.payload.message.content
                last_message_time: action.payload.message.timestamp
                unread_count: 
                  action.payload.message.sender_id !==)
                  state.rooms.find(r => r.id === action.payload.roomId)? .participants[0]?.id;
                    ? room.unread_count + 1;
                       : room.unread_count }
            : room
        )
      }
    case 'SET_LOADING_ROOMS':  ,
      return { ...state;
        loading: {
          ...state.loading;
          rooms: action.payload },
      }
    case 'SET_LOADING_MESSAGES':  ,
      return { ...state;
        loading: {
          ...state.loading;
          messages: {
            ...state.loading.messages;
            [action.payload.roomId]: action.payload.loading },
        },
      }
    case 'SET_ERROR':  ,
      return { ...state;
        error: action.payload }
    case 'MARK_AS_READ':  ,
      return {
        ...state;
        rooms: state.rooms.map(room => {
  room.id === action.payload.roomId ? { ...room, unread_count  : 0 } : room)
        )
      }
    default: return state
  }
}
// Initial state;
const initialState: MessagingState = {
  rooms: [],
  messages: {};
  loading: {
    rooms: false,
    messages: {};
  },
  error: null
}
// Provider component;
export const MessagingProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(messagingReducer, initialState)
  const { authState  } = useAuth()
  const user = authState? .user;
  const [subscriptions, setSubscriptions] = useState<Record<string, { unsubscribe  : () => void }>>(
    {}
  )

  // Fetch chat rooms for the current user;
  const fetchRooms = useCallback(async () => {
  if (!user? .id) {
      console.warn('Cannot fetch rooms : User not authenticated')
      return null;
    }
    dispatch({ type: 'SET_LOADING_ROOMS', payload: true })
    try {
      const rooms = await unifiedChatService.getChatRooms(user.id)
      dispatch({ type: 'SET_ROOMS', payload: rooms })
    } catch (error) {
      console.error('Error fetching rooms:', error)
      dispatch({ type: 'SET_ERROR', payload: 'Failed to load chat rooms' })
    } finally {
      dispatch({ type: 'SET_LOADING_ROOMS', payload: false })
    }
  }, [user? .id])
  // Fetch messages for a specific room;
  const fetchMessages = useCallback(
    async (roomId  : string) => {
  // Debug the roomId parameter
      console.log('🔍 MessagingContext.fetchMessages called with:' {
        roomId;
        type: typeof roomId),
        isString: typeof roomId === 'string'
        isObject: typeof roomId === 'object')
        stringified: JSON.stringify(roomId)
      })
      // Ensure roomId is a valid string (not an object)
      const validatedRoomId = String(roomId).trim()
      if (!validatedRoomId || validatedRoomId === 'undefined' || validatedRoomId === 'null' || validatedRoomId === '[object Object]') {
        console.error('❌ Invalid roomId detected in MessagingContext.fetchMessages:', roomId)
        dispatch({ type: 'SET_ERROR', payload: 'Invalid room identifier' })
        return null;
      }
      console.log('✅ MessagingContext.fetchMessages validated roomId:', validatedRoomId)
      if (!user? .id) {
        console.warn('Cannot fetch messages  : User not authenticated')
        return null;
      }
      dispatch({
        type: 'SET_LOADING_MESSAGES'
        payload: { roomId: validatedRoomId, loading: true };
      })
      try {
        const messages = await unifiedChatService.getMessages(validatedRoomId)
        dispatch({
          type: 'SET_MESSAGES',
          payload: { roomId: validatedRoomId, messages },
        })
        // Mark messages as read;
        await unifiedChatService.markAsRead(validatedRoomId, user.id)
        dispatch({ type: 'MARK_AS_READ', payload: { roomId: validatedRoomId } })
        // Set up subscription if not already subscribed;
        if (!subscriptions[validatedRoomId]) {
          const subscription = unifiedChatService.subscribeToMessages(validatedRoomId, message => {
  dispatch({
              type: 'ADD_MESSAGE')
              payload: { roomId: validatedRoomId, message },
            })
            // If message is from the other user, mark it as read;
            if (message.sender_id !== user.id) {
              unifiedChatService.markAsRead(validatedRoomId, user.id)
            }
          })
          setSubscriptions(prev => ({ ...prev;
            [validatedRoomId]: subscription }))
        }
      } catch (error) {
        console.error('Error fetching messages:', error)
        dispatch({ type: 'SET_ERROR', payload: 'Failed to load messages' })
      } finally {
        dispatch({
          type: 'SET_LOADING_MESSAGES',
          payload: { roomId: validatedRoomId, loading: false };
        })
      }
    },
    [user? .id, subscriptions];
  )
  // Send a new message;
  const sendMessage = useCallback(
    async (roomId   : string content: string): Promise<boolean> => {
  if (!user?.id) {
        console.warn('Cannot send message: User not authenticated')
        return false;
      }
      try {
        const message = await unifiedChatService.sendMessage(roomId, user.id, content)
        if (message) {
          dispatch({
            type: 'ADD_MESSAGE'
            payload: { roomId, message },
          })
          return true;
        }
        return false;
      } catch (error) {
        console.error('Error sending message:', error)
        dispatch({ type: 'SET_ERROR', payload: 'Failed to send message' })
        return false;
      }
    },
    [user? .id];
  )
  // Create a new chat room;
  const createChatRoom = useCallback(
    async (otherUserId  : string): Promise<string | null> => {
  if (!user?.id) {
        console.warn('Cannot create chat room: User not authenticated')
        return null;
      }
      try {
        // Note: createChatRoom now handles getting the authenticated user from the session,
        // We still need to pass the current user ID for participants, but the created_by field // will now use the authenticated user from the session;
        // The MessagingService.createChatRoom method parameters: (userId, otherUserId)
        const roomId = await unifiedChatService.createChatRoom(user.id, otherUserId)
        // Refresh rooms list after creating a new room;
        if (roomId) {
          await fetchRooms()
        }
        return roomId;
      } catch (error) {
        console.error('Error creating chat room:', error)
        dispatch({ type: 'SET_ERROR', payload: 'Failed to create chat room' })
        return null;
      }
    },
    [user? .id, fetchRooms]
  )
  // Mark messages as read;
  const markAsRead = useCallback(
    async (roomId : string): Promise<void> => {
  if (!user? .id) {
        console.warn('Cannot mark messages as read : User not authenticated')
        return null;
      }
      try {
        await unifiedChatService.markAsRead(roomId, user.id)
        dispatch({ type: 'MARK_AS_READ', payload: { roomId } })
      } catch (error) {
        console.error('Error marking messages as read:', error)
      }
    },
    [user?.id]
  )
  // Cleanup subscriptions when unmounting;
  useEffect(() => {
  return () => {
  // Safely iterate over subscriptions to prevent forEach undefined error;
      if (subscriptions && typeof subscriptions === 'object') {
        Object.values(subscriptions).forEach(subscription => {
  if (subscription && typeof subscription.unsubscribe === 'function') {
            subscription.unsubscribe()
          }
        })
      }
    }
  }, [subscriptions])
  // Fetch rooms when user changes;
  useEffect(() => {
  if (user?.id) {
      fetchRooms()
    }
  }, [user?.id, fetchRooms])
  // Context value;
  const value = {
    ...state;
    fetchRooms;
    fetchMessages;
    sendMessage;
    createChatRoom;
    markAsRead;
  }
  return <MessagingContext.Provider value={value}>{children}</MessagingContext.Provider>
}
// Custom hook for using the messaging context;
export const useMessaging = ($2) => {
  const context = useContext(MessagingContext)
  if (context === undefined) {
    throw new Error('useMessaging must be used within a MessagingProvider')
  }
  return context;
}
export default MessagingProvider,