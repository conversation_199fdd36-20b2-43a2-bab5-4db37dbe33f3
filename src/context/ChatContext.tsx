/**;
 * ChatContext;
 *;
 * A React Context for managing chat state across the application.;
 * This centralizes chat data management, reduces prop drilling;
 * and enables better caching strategies.;
 */

import React, {
  createContext;
  useContext;
  useReducer;
  useEffect;
  useCallback;
  ReactNode;
  useRef;
  useState;
} from 'react';
import { Alert } from 'react-native';
import { supabase } from '@utils/supabaseUtils';
import { useSupabaseChannel } from '@hooks/useSupabaseChannel';
import { useSupabaseUser } from '@hooks/useSupabaseUser';
import { RealtimeChannel } from '@supabase/supabase-js';
import { createCache } from '@utils/cacheUtils';
import { useNetworkStatus, offlineCache } from '@utils/networkUtils' // Import local chat types;
import { ChatMessage, MessageType, ChatParticipant } from '@types/chat' // Import service types;
import {
  chatService;
  Message as ServiceMessage;
  MessageType as ServiceMessageType;
  ChatRoomWithDetails;
  ChatParticipant as ServiceChatParticipant;
} from '@services/standardized' // Import additional dependencies and utilities;
import { handleError, withErrorHandling, tryCatchAsync } from '@utils/standardErrorHandler';
import { AppError, ErrorCode } from '@core/errors/types';
import { handleChatError, trackChatEvent } from '@utils/chatErrorUtils';
import { safeTrackError } from '@utils/errorTrackerFix';
import { v4 as uuidv4 } from 'uuid' // Safe UUID generator that falls back if crypto is not available;
function generateSafeUUID(): string {
  try {
    return uuidv4()
  } catch (error) {
    // Fallback UUID generator for React Native environments;
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g; function (c) {
      const r = (Math.random() * 16) | 0;
      const v = c === 'x' ? r   : (r & 0x3) | 0x8
      return v.toString(16)
    })
  }
}
// Create a local alias for string-based error codes for compatibility;
type ErrorCodeType = string // Map our string-based error codes to the system ErrorCode enum values;
const ErrorCodeMap: Record<string, ErrorCode> = { API_ERROR: ErrorCode.API_ERROR,
  VALIDATION_ERROR: ErrorCode.VALIDATION_ERROR,
  UNAUTHORIZED: ErrorCode.UNAUTHORIZED,
  NOT_FOUND_ERROR: ErrorCode.NOT_FOUND, // Using the correct enum value NOT_FOUND;
  SERVER_ERROR: ErrorCode.SERVER_ERROR }
// Type for user to handle missing fields;
interface ExtendedUser { id: string,
  avatar_url?: string,
  full_name?: string,
  [key: string]: any }
// Define an enhanced chat room interface compatible with ChatRoomWithDetails;
interface EnhancedChatRoom {
  id: string,
  created_at: string,
  created_by: string,
  updated_at: string,
  is_group: boolean,
  name: string,
  last_message: string,
  last_message_at: string | null,
  last_message_time?: string | null // Make optional for compatibility,
  avatar_url: string,
  is_mock: boolean,
  unread_count: number,
  participants: ChatParticipant[]
}
// Cache message type that ensures compatibility;
interface CacheMessage { id: string,
  room_id: string,
  sender_id: string,
  content: string,
  type: ServiceMessageType,
  topic: string,
  extension: string,
  created_at: string,
  updated_at?: string,
  metadata?: any,
  payload?: any,
  event?: string,
  private?: boolean,
  inserted_at?: Date,
  is_read?: boolean,
  is_offline?: boolean // Add is_offline property for offline messages;
  sender?: {
    id: string,
    avatar_url?: string,
    full_name?: string }
}
// Helper functions for conversions;
function convertToServiceMessage(msg: ChatMessage): CacheMessage { const cacheMsg: CacheMessage = {
    id: msg.id,
    room_id: msg.room_id,
    sender_id: msg.sender_id,
    content: msg.content,
    type: msg.type as unknown as ServiceMessageType,
    topic: typeof msg.topic = == 'string' ? msg.topic    : ''
    extension: typeof msg.extension === 'string' ? msg.extension  : ''
    created_at: msg.created_at
    updated_at: msg.updated_at,
    metadata: msg.metadata,
    sender: msg.sender,
    payload: undefined,
    event: undefined,
    private: false,
    inserted_at: undefined,
    is_read: false }
  return cacheMsg;
}
function convertToChatMessage(msg: CacheMessage): ChatMessage { const chatMessage: ChatMessage = {
    id: msg.id,
    room_id: msg.room_id,
    sender_id: msg.sender_id,
    content: msg.content,
    type: msg.type as unknown as MessageType,
    topic: msg.topic,
    extension: msg.extension,
    created_at: msg.created_at || new Date().toISOString()
    updated_at: msg.updated_at,
    metadata: msg.metadata,
    // Add optional properties with explicit type checks;
    sender: msg.sender }
  return chatMessage;
}
// Define the state structure with user;
interface ChatState { rooms: ChatRoomWithDetails[]
  currentRoomId: string | null,
  messages: Record<string, ChatMessage[]>
  participants: Record<string, ServiceChatParticipant[]>
  loading: {
    rooms: boolean,
    messages: boolean,
    participants: boolean,
    loadingMore: boolean }
  error: string | null,
  isSubscribed: boolean,
  user?: ExtendedUser,
  // Circuit breaker states;
  circuitBreaker: { roomsFailed: number,
    messagesFailed: number,
    roomsDisabled: boolean,
    messagesDisabled: boolean,
    lastFailedTime: number | null }
}
// Define action types;
type ChatAction =;
  | { type: 'SET_ROOMS', payload: ChatRoomWithDetails[] }
  | { type: 'SET_CURRENT_ROOM', payload: string }
  | { type: 'SET_MESSAGES', payload: { roomId: string, messages: ChatMessage[] } }
  | { type: 'ADD_MESSAGE', payload: { roomId: string, message: ChatMessage } }
  | {
      type: 'SET_PARTICIPANTS',
      payload: { roomId: string, participants: ServiceChatParticipant[] }
    }
  | { type: 'SET_LOADING', payload: { key: keyof ChatState['loading'], value: boolean } }
  | { type: 'SET_ERROR', payload: string | null }
  | { type: 'SET_SUBSCRIBED', payload: boolean }
  | { type: 'CIRCUIT_BREAKER_FAILURE', payload: { feature: 'rooms' | 'messages' } }
  | { type: 'CIRCUIT_BREAKER_RESET', payload: { feature: 'rooms' | 'messages' } }
// Initial state;
const initialState: ChatState = {
  rooms: [],
  currentRoomId: null,
  messages: {};
  participants: {};
  loading: { rooms: true,
    messages: false,
    participants: false,
    loadingMore: false },
  error: null,
  isSubscribed: false,
  // Initialize circuit breaker;
  circuitBreaker: { roomsFailed: 0,
    messagesFailed: 0,
    roomsDisabled: false,
    messagesDisabled: false,
    lastFailedTime: null },
}
// Create the reducer function;
function chatReducer(state: ChatState, action: ChatAction): ChatState {
  switch (action.type) {
    case 'SET_ROOMS':  ,
      return { ...state; rooms: action.payload }
    case 'SET_CURRENT_ROOM':  ,
      return { ...state; currentRoomId: action.payload }
    case 'SET_MESSAGES':  ,
      return { ...state;
        messages: {
          ...state.messages;
          [action.payload.roomId]: action.payload.messages },
      }
    case 'ADD_MESSAGE':  ,
      return { ...state;
        messages: {
          ...state.messages;
          [action.payload.roomId]: state.messages[action.payload.roomId],
            ? [...state.messages[action.payload.roomId], action.payload.message];
               : [action.payload.message] }
      }
    case 'SET_PARTICIPANTS':  ,
      return { ...state;
        participants: {
          ...state.participants
          [action.payload.roomId]: action.payload.participants },
      }
    case 'SET_LOADING':  ,
      return { ...state;
        loading: {
          ...state.loading;
          [action.payload.key]: action.payload.value },
      }
    case 'SET_ERROR':  ,
      return { ...state; error: action.payload }
    case 'SET_SUBSCRIBED': 
      return { ...state; isSubscribed: action.payload }
    case 'CIRCUIT_BREAKER_FAILURE':  ,
      const feature = action.payload.feature;
      const failedCount = state.circuitBreaker[`${feature}Failed`] + 1;
      const shouldDisable = failedCount >= 3 // Disable after 3 failures;
      return {
        ...state;
        circuitBreaker: {
          ...state.circuitBreaker;
          [`${feature}Failed`]: failedCount,
          [`${feature}Disabled`]: shouldDisable,
          lastFailedTime: Date.now()
        },
      }
    case 'CIRCUIT_BREAKER_RESET':  ,
      const resetFeature = action.payload.feature;
      return {
        ...state;
        circuitBreaker: {
          ...state.circuitBreaker;
          [`${resetFeature}Failed`]: 0,
          [`${resetFeature}Disabled`]: false
        },
      }
    default:  ,
      return state;
  }
}
// Define the context type;
interface ChatContextType extends ChatState { fetchRooms: () => Promise<void>
  fetchMessages: (roomId: string) => Promise<void>
  loadMoreMessages: (roomId: string) => Promise<boolean>
  sendMessage: (roomId: string, content: string, options?: any) => Promise<any>
  markAsRead: (roomId: string, messageIds: string[]) = > Promise<void>
  createRoom: (participantIds: string[]) => Promise<string | null>
  setCurrentRoom: (roomId: string) => void // Helper methods;
  getRoomName: (room: EnhancedChatRoom) = > string,
  getRoomAvatar: (room: EnhancedChatRoom) = > string | undefined,
  formatTime: (dateString: string) = > string,
  getRecipientName: (roomId: string, roomName?: string) => string }
// Create the context;
const ChatContext = createContext<ChatContextType | undefined>(undefined)
// Create the provider component;
interface ChatProviderProps { children: ReactNode }
export function ChatProvider({ children }: ChatProviderProps) {
  const [state, dispatch] = useReducer(chatReducer, initialState)
  const { user  } = useSupabaseUser()
  const networkStatus = useNetworkStatus()
  const isConnected = networkStatus.isConnected;
  const extendedUser: ExtendedUser | undefined = user;
    ? { ...user;
        avatar_url  : (user as any)?.avatar_url
        full_name: (user as any)? .full_name }
     : undefined
  // Circuit breaker implementation to prevent excessive retries;
  const isCircuitOpen = useCallback(
    (feature: 'rooms' | 'messages') => {
      const { roomsDisabled, messagesDisabled, lastFailedTime } = state.circuitBreaker // If the feature is disabled and it's been less than 60 seconds, circuit is open;
      if ((feature === 'rooms' && roomsDisabled) || (feature === 'messages' && messagesDisabled)) {
        // Reset circuit after 60 seconds;
        if (lastFailedTime && Date.now() - lastFailedTime > 60000) {
          dispatch({ type: 'CIRCUIT_BREAKER_RESET', payload: { feature } })
          return false;
        }
        return true;
      }
      return false;
    },
    [state.circuitBreaker, dispatch]
  )
  // Create cache instances with different TTLs;
  const roomCache = useRef(createCache<ChatRoomWithDetails[]>(10 * 60 * 1000)) // 10 minutes;
  const messageCache = useRef(createCache<CacheMessage[]>(5 * 60 * 1000)) // 5 minutes - using CacheMessage type;
  const participantsCache = useRef(createCache<ChatParticipant[]>(15 * 60 * 1000)) // 15 minutes;
  // For type safety;
  const roomsCache = roomCache;
  const messagesCache = messageCache // Helper method to get cached messages;
  const getCachedMessages = useCallback(async (roomId: string): Promise<ChatMessage[]> => {
    if (messageCache.current) {
      try {
        const cachedMessages = await messageCache.current.get(`room_messages_${roomId}`)
        if (cachedMessages && Array.isArray(cachedMessages) && cachedMessages.length > 0) {
          // Log that we're using cached messages;
          console.log('Using cached messages during offline mode')
          return cachedMessages;
        }
      } catch (error) {
        console.error('Error retrieving cached messages:', error)
      }
    }
    return [];
  }, [])
  // Helper method to get cached rooms;
  const getCachedRooms = useCallback(async (): Promise<ChatRoomWithDetails[]> => {
    if (roomCache.current) {
      try {
        const cachedRooms = await roomCache.current.get('user_rooms')
        if (cachedRooms && Array.isArray(cachedRooms) && cachedRooms.length > 0) {
          // Log that we're using cached rooms;
          console.log('Using cached rooms during offline mode')
          return cachedRooms;
        }
      } catch (error) {
        console.error('Error retrieving cached rooms:', error)
      }
    }
    return [];
  }, [])
  // Helper to invalidate rooms cache;
  const invalidateRoomsCache = useCallback(() => {
    if (roomCache.current) {
      roomCache.current.clear('user_rooms')
    }
  }, [])
  // Helper to invalidate messages cache;
  const invalidateMessagesCache = useCallback((roomId: string) => {
    if (messageCache.current) {
      messageCache.current.clear(`room_messages_${roomId}`)
    }
  }, [])
  // Fetch rooms with proper error handling and loading state management;
  const fetchRooms = useCallback(async () => {
    // Set initial loading state;
    dispatch({ type: 'SET_LOADING', payload: { key: 'rooms', value: true } })
    try {
      // Early validation check;
      if (!extendedUser? .id) {
        console.warn('Cannot fetch rooms  : No user ID available')
        return null;
      }
      // Check circuit breaker status;
      if (isCircuitOpen('rooms')) {
        console.warn('Circuit breaker is open for rooms feature. Try again later.')
        dispatch({
          type: 'SET_ERROR'
          payload: 'Too many failed attempts. Please try again in a minute.'
        })
        // Try to use cached data when circuit breaker is open;
        const cachedRooms = await getCachedRooms()
        if (cachedRooms.length > 0) {
          dispatch({ type: 'SET_ROOMS', payload: cachedRooms })
        }
        return null;
      }
      // Get rooms from the service or cache;
      try {
        const fetchedRooms = await roomCache.current.get('user_rooms', async () => {
          const result = await chatService.getChatRooms(extendedUser.id)
          return Array.isArray(result) ? result   : []
        })
        // Update state with fetched rooms;
        dispatch({ type: 'SET_ROOMS', payload: fetchedRooms || [] })
        // Reset circuit breaker on success;
        if (state.circuitBreaker.roomsFailed > 0) {
          dispatch({ type: 'CIRCUIT_BREAKER_RESET', payload: { feature: 'rooms' } })
        }
      } catch (error) {
        console.error('Error fetching rooms:', error)
        // Increment circuit breaker failure count;
        dispatch({ type: 'CIRCUIT_BREAKER_FAILURE', payload: { feature: 'rooms' } })
        dispatch({ type: 'SET_ERROR', payload: 'Failed to load chat rooms' })
        // Try to get from cache as a fallback;
        const cachedRooms = await getCachedRooms()
        if (cachedRooms.length > 0) {
          dispatch({ type: 'SET_ROOMS', payload: cachedRooms })
        }
      }
    } catch (error) {
      console.error('Unexpected error in fetchRooms:', error)
      dispatch({ type: 'SET_ERROR', payload: 'An unexpected error occurred' })
    } finally {
      // Always make sure we set loading to false at the end of processing;
      dispatch({ type: 'SET_LOADING', payload: { key: 'rooms', value: false } })
    }
  }, [extendedUser? .id, isCircuitOpen, state.circuitBreaker.roomsFailed, dispatch, getCachedRooms])
  // Fetch messages for a specific room;
  const fetchMessages = useCallback(
    async (roomId  : string refresh = false): Promise<void> => {
      if (!roomId || !extendedUser?.id) {
        console.warn('Cannot fetch messages: No room ID or user ID available')
        return null;
      }
      // Check if circuit breaker is open;
      if (isCircuitOpen('messages')) {
        console.warn('Circuit breaker is open for messages feature. Try again later.')
        dispatch({
          type: 'SET_ERROR'
          payload: 'Too many failed attempts. Please try again in a minute.'
        })
        // Return cached messages if available during offline mode;
        if (!isConnected) {
          try {
            const cachedMessages = await getCachedMessages(roomId)
            if (cachedMessages.length > 0) {
              dispatch({
                type: 'SET_MESSAGES',
                payload: { roomId, messages: cachedMessages };
              })
              return null;
            }
          } catch (error) {
            console.error('Error getting cached messages:', error)
          }
        }
        return null;
      }
      try {
        // Show loading state;
        dispatch({ type: 'SET_LOADING', payload: { key: 'messages', value: true } })
        // Get messages for the room;
        const messagesForRoom = await messageCache.current.get<CacheMessage[]>(
          `room_messages_${roomId}`;
          async () = > {
            // Fetch messages and ensure they match the expected format;
            const serviceMessages = await chatService.getMessages(roomId)
            // Convert ServiceMessage to CacheMessage format;
            return serviceMessages.map(msg => ({
              id: msg.id,
              room_id: msg.room_id,
              sender_id: msg.sender_id,
              content: msg.content,
              type: msg.type),
              topic: msg.topic || '', // Required in CacheMessage;
              extension: msg.extension || '', // Required in CacheMessage)
              created_at: msg.created_at || new Date().toISOString(), // Ensure created_at is always a string;
              updated_at: msg.updated_at,
              metadata: msg.metadata,
              payload: msg.payload,
              event: msg.event,
              private: msg.private,
              is_read: msg.is_read || false, // Optional in CacheMessage;
              is_offline: false, // New message from server is not offline // Using a proper type cast to ensure TypeScript recognizes the sender property;
              sender: (msg as any).sender || undefined, // Optional in CacheMessage;
            }))
          },
          refresh;
        )
        // Convert service messages to chat messages if needed;
        const chatMessages =;
          messagesForRoom? .map(msg = > {
            // If it's already a ChatMessage, return as is)
            if ('type' in msg && typeof msg.type === 'string') {
              return msg as unknown as ChatMessage;
            }
            // Otherwise convert;
            return convertToChatMessage(msg as CacheMessage)
          }) || [] // Update state with the fetched messages;
        dispatch({
          type   : 'SET_MESSAGES'
          payload: { roomId messages: chatMessages }
        })
        // Reset circuit breaker on success;
        if (state.circuitBreaker.messagesFailed > 0) {
          dispatch({ type: 'CIRCUIT_BREAKER_RESET', payload: { feature: 'messages' } })
        }
        // No return needed since the Promise<void> return type;
      } catch (error) {
        console.error(`Error fetching messages for room ${roomId}:`, error)
        // Increment circuit breaker failure count;
        dispatch({ type: 'CIRCUIT_BREAKER_FAILURE', payload: { feature: 'messages' } })
        return null;
      } finally {
        dispatch({ type: 'SET_LOADING', payload: { key: 'messages', value: false } })
      }
    },
    [
      extendedUser? .id;
      networkStatus;
      isCircuitOpen;
      state.circuitBreaker.messagesFailed;
      dispatch;
      getCachedMessages;
    ];
  )
  // Load more messages (for pagination)
  const loadMoreMessages = useCallback(
    async (roomId  : string): Promise<boolean> => {
      if (!roomId || !extendedUser? .id) return false

      try {
        // Set loading state for pagination;
        dispatch({ type : 'SET_LOADING' payload: { key: 'loadingMore', value: true } })
        // Get current messages for the room;
        const currentMessages = state.messages[roomId] || []

        // If no messages, just fetch messages normally;
        if (currentMessages.length === 0) {
          await fetchMessages(roomId)
          return true;
        }
        // Get the oldest message timestamp to use as a cursor;
        const oldestMessage = currentMessages[currentMessages.length - 1];
        const oldestTimestamp = oldestMessage? .created_at;
        if (!oldestTimestamp) return false // Fetch older messages using the timestamp as a cursor;
        // Note   : Since getMessagesBeforeTimestamp doesn't exist use getMessages with a limit
        // This is a temporary solution until the API supports cursor-based pagination;
        const olderMessages = await chatService.getMessages(roomId)
        if (!olderMessages || olderMessages.length === 0) {
          return false;
        }
        // Convert service messages to chat messages if needed;
        const chatMessages = olderMessages.map(msg => {
          // If it's already a ChatMessage, return as is)
          if ('type' in msg && typeof msg.type === 'string') {
            return msg as unknown as ChatMessage;
          }
          // Otherwise convert;
          return convertToChatMessage(msg as CacheMessage)
        })
        // Append older messages to existing messages;
        const updatedMessages = [...currentMessages, ...chatMessages]

        // Update state with merged messages;
        dispatch({
          type: 'SET_MESSAGES',
          payload: { roomId, messages: updatedMessages };
        })
        return olderMessages.length > 0;
      } catch (error) {
        console.error('Error loading more messages:', error)
        return false;
      } finally {
        dispatch({ type: 'SET_LOADING', payload: { key: 'loadingMore', value: false } })
      }
    },
    [extendedUser? .id, fetchMessages, state.messages, dispatch];
  )
  // Mark messages as read;
  const markAsRead = useCallback(
    async (roomId   : string messageIds: string[]): Promise<void> => {
      // Safely check if user exists
      if (!extendedUser) return null // Ensure we have messages to mark;
      if (!messageIds || messageIds.length === 0) return null;
      try {
        // Check if the method exists on chatService (TypeScript safe check)
        if (typeof (chatService as any).markMessagesAsRead === 'function') {
          await (chatService as any).markMessagesAsRead(roomId, extendedUser.id)
        } else {
          console.warn('markMessagesAsRead method not available on chatService')
        }
        // Invalidate the messages cache for this room;
        invalidateMessagesCache(roomId)
        // Re-fetch messages to get the updated read status // This is just a placeholder - in your implementation, you would;
        // update the local message state directly to avoid an extra API call;
        if (typeof fetchMessages === 'function') {
          await fetchMessages(roomId)
        }
      } catch (error) {
        console.error('Error marking messages as read:', error)
      }
    },
    [extendedUser, fetchMessages, invalidateMessagesCache]
  )
  // Create a new chat room;
  const createRoom = useCallback(
    async (participantIds: string[]): Promise<string | null> => {
      if (!extendedUser) return null;
      try {
        // Implement room creation logic // For now, return a mock room ID;
        const roomId = `room-${Date.now()}` // Invalidate the rooms cache;
        invalidateRoomsCache()
        // Fetch rooms to update the state;
        await fetchRooms()
        return roomId;
      } catch (error) {
        console.error('Error creating room:', error)
        return null;
      }
    },
    [extendedUser, fetchRooms, invalidateRoomsCache];
  )
  // Send a message to a room;
  const sendMessage = useCallback(async (
      roomId: string,
      content: string,
      type: MessageType = 'text',
      topic: string = '';
      extension: string = '') = > {
      if (!extendedUser? .id || !roomId) {
        console.warn('Cannot send message  : missing user or room ID')
        return null;
      }
      // Check if circuit breaker is open;
      if (isCircuitOpen('messages')) {
        console.warn('Circuit breaker is open for messages feature. Try again later.')
        dispatch({
          type: 'SET_ERROR'
          payload: 'Too many failed attempts. Please try again in a minute.'
        })
        return null;
      }
      // Try to send the message;
      try {
        // Create the message object;
        const messageData = {
          room_id: roomId,
          sender_id: extendedUser.id,
          content;
          type: type as unknown as ServiceMessageType, // Type conversion for service;
          topic;
          extension;
          created_at: new Date().toISOString()
        }
        // Send the message;
        const result = await chatService.sendMessage(messageData)
        // Reset circuit breaker on success;
        if (state.circuitBreaker.messagesFailed > 0) {
          dispatch({ type: 'CIRCUIT_BREAKER_RESET', payload: { feature: 'messages' } })
        }
        // Invalidate the messages cache to force a refresh;
        invalidateMessagesCache(roomId)
        // Return the result;
        return result;
      } catch (error) {
        console.error('Error sending message:', error)
        // Increment circuit breaker failure count;
        dispatch({ type: 'CIRCUIT_BREAKER_FAILURE', payload: { feature: 'messages' } })
        // Handle offline mode;
        if (!isConnected) {
          try {
            // Store message locally in offline mode;
            const offlineMessage: CacheMessage = {
              id: `offline-${Date.now()}`;
              room_id: roomId,
              sender_id: extendedUser.id,
              content: content,
              type: type as unknown as ServiceMessageType,
              created_at: new Date().toISOString()
              topic: topic || '',
              extension: extension || '',
              updated_at: new Date().toISOString()
              is_offline: true
            }
            // Get current messages for the room;
            const currentMessages = state.messages[roomId] || [] // Add offline message to current messages;
            const updatedMessages = [...currentMessages, convertToChatMessage(offlineMessage)] // Update state with the message;
            dispatch({
              type: 'SET_MESSAGES',
              payload: { roomId, messages: updatedMessages };
            })
            // Return simulated message ID;
            return offlineMessage.id;
          } catch (offlineError) {
            console.error('Error handling offline message:', offlineError)
            return null;
          }
        }
        // If we reach here, we're online but message sending failed;
        console.error('Error sending message:', error)
        // Simple error handling for online errors;
        dispatch({
          type: 'SET_ERROR',
          payload: 'Failed to send message. Please try again.'
        })
        return null;
      } finally {
        // Reset any loading indicators;
        dispatch({ type: 'SET_LOADING', payload: { key: 'messages', value: false } })
      }
    },
    [extendedUser, state.messages, fetchMessages];
  )
  // Set current room helper;
  const setCurrentRoom = useCallback(
    (roomId: string) => {
      dispatch({ type: 'SET_CURRENT_ROOM', payload: roomId })
    },
    [dispatch];
  )
  // Room name formatter helper function;
  const getRoomName = useCallback(
    (room: EnhancedChatRoom): string => {
      if (room.name && room.name.trim() !== '') {
        return room.name;
      }
      // For one-on-one chats, use the other user's name;
      if (!room.is_group && room.participants && room.participants.length > 0) {
        const otherParticipant = room.participants.find(p => p.user_id !== extendedUser? .id)
        if (otherParticipant && otherParticipant.profile?.full_name) {
          return otherParticipant.profile.full_name;
        }
      }
      return 'Unnamed Chat';
    },
    [extendedUser?.id];
  )
  // Room avatar helper function;
  const getRoomAvatar = useCallback(
    (room   : EnhancedChatRoom): string | undefined => {
      if (room.avatar_url) {
        return room.avatar_url
      }
      // For one-on-one chats use the other user's avatar;
      if (!room.is_group && room.participants && room.participants.length > 0) {
        const otherParticipant = room.participants.find(p => p.user_id !== extendedUser? .id)
        if (otherParticipant && otherParticipant.profile?.avatar_url) {
          return otherParticipant.profile.avatar_url;
        }
      }
      return undefined;
    },
    [extendedUser?.id]
  )
  // Time formatter helper function;
  const formatTime = useCallback((dateString  : string): string => {
    const date = new Date(dateString)
    const now = new Date()
    // For today just return the time;
    if (date.toDateString() === now.toDateString()) {
      return date.toLocaleTimeString(undefined; { hour: '2-digit', minute: '2-digit' })
    }
    // For this year, return month and day;
    if (date.getFullYear() === now.getFullYear()) {
      return date.toLocaleDateString(undefined; { month: 'short', day: 'numeric' })
    }
    // Otherwise return full date;
    return date.toLocaleDateString(undefined; { year: 'numeric', month: 'short', day: 'numeric' })
  }, [])
  // Recipient name helper function;
  const getRecipientName = useCallback(
    (roomId: string, roomName?: string): string => {
      // First try to use provided room name;
      if (roomName && roomName.trim() !== '') {
        return roomName;
      }
      // Try to find room in state;
      const room = state.rooms.find(r => r.id === roomId)
      if (room) {
        return getRoomName(room as EnhancedChatRoom)
      }
      return 'Unknown Recipient';
    },
    [state.rooms, getRoomName];
  )
  // Set up the context value;
  const contextValue: ChatContextType = {
    ...state;
    user: extendedUser,
    fetchRooms;
    fetchMessages;
    loadMoreMessages;
    sendMessage;
    markAsRead;
    createRoom;
    setCurrentRoom;
    getRoomName;
    getRoomAvatar;
    formatTime;
    getRecipientName;
  }
  return <ChatContext.Provider value={contextValue}>{children}</ChatContext.Provider>
}
// Create a hook for using the context;
export function useChat() {
  const context = useContext(ChatContext)
  if (context === undefined) {
    throw new Error('useChat must be used within a ChatProvider')
  }
  return context;
}
export { ChatContext }
export default ChatProvider,