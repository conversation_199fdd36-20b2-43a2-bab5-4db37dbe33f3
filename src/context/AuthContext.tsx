/**;
 * Authentication Context - Compatibility Layer;
 *;
 * This now serves as a compatibility wrapper around AuthContextAdapter;
 * to prevent breaking changes while transitioning to the new auth system.;
 */

import React, { createContext, useContext } from 'react';
import { useAuthAdapter } from '@context/AuthContextAdapter';
import type { Session, User } from '@supabase/supabase-js';

export interface Profile {
  id: string,
  email?: string,
  name?: string,
  avatar_url?: string,
  phone?: string,
  created_at?: string,
  updated_at?: string,
  verified?: boolean,
  [key: string]: any // For additional profile fields;
}
export interface AuthState { // Core auth data;
  user: User | null,
  profile: Profile | null,
  session: Session | null,
  // Loading states;
  isLoading: boolean,
  isProfileLoading: boolean,
  isInitialized: boolean,
  // Error states;
  error: string | null,
  profileError: string | null,
  // Auth status;
  isAuthenticated: boolean,
  needsProfileCreation: boolean }
export interface AuthActions { // Profile management;
  refreshProfile: () => Promise<Profile | null>
  createProfile: (profileData: Partial<Profile>) => Promise<Profile | null>
  // Session management;
  refreshSession: () => Promise<Session | null>
  signOut: () => Promise<void>
  // Error handling;
  clearError: () = > void,
  clearProfileError: () => void }
interface AuthContextValue extends AuthState, AuthActions { // Additional compatibility properties;
  authState?: any,
  authLoaded?: boolean,
  state?: AuthState,
  actions?: AuthActions }
const AuthContext = createContext<AuthContextValue | null>(null)
interface AuthProviderProps { children: React.ReactNode }
export function AuthProvider({ children }: AuthProviderProps) {
  // Use the new AuthContextAdapter under the hood;
  const authAdapter = useAuthAdapter()
  // Transform AuthContextAdapter data to match old AuthContext interface;
  const user = authAdapter.authState.user;
  const profile = user;
    ? {
        id   : user.id
        email: user.email
        name: user.user_metadata? .name || user.user_metadata?.full_name,
        avatar_url  : user.user_metadata?.avatar_url
        phone: user.phone
        created_at: user.created_at,
        updated_at: user.updated_at,
        verified: user.email_confirmed_at ? true   : false
        ...user.user_metadata // Include any additional metadata;
      }
    : null,
  // Create compatibility state;
  const authState: AuthState = { user;
    profile;
    session: null, // We don't expose session in the new system;
    isLoading: authAdapter.authState.isLoading,
    isProfileLoading: false, // Profile loading is handled by the new system;
    isInitialized: authAdapter.authLoaded || false,
    error: authAdapter.authState.error,
    profileError: null,
    isAuthenticated: authAdapter.authState.isAuthenticated,
    needsProfileCreation: authAdapter.authState.isAuthenticated && !profile }
  // Create compatibility actions;
  const authActions: AuthActions = {
    refreshProfile: async () => {
      // This is handled automatically by the new system;
      return profile;
    },
    createProfile: async (profileData: Partial<Profile>) => {
      await authAdapter.updateUser(profileData)
      return profile;
    },
    refreshSession: async () => {
      await authAdapter.refreshSession()
      return null // We don't expose session;
    },
    signOut: authAdapter.signOut,
    clearError: authAdapter.clearErrors,
    clearProfileError: () => {
      // No-op since profile errors are handled by the new system;
    },
  }
  // Combine state and actions for compatibility;
  const contextValue: AuthContextValue = { ...authState;
    ...authActions // Additional compatibility properties;
    authState: authAdapter.authState,
    authLoaded: authAdapter.authLoaded,
    state: authState,
    actions: authActions }
  return <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
}
export function useAuth(): AuthContextValue {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context;
}
// Export default provider for consistency;
export default AuthProvider,