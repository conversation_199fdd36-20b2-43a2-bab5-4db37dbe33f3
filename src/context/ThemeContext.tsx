/**;
 * Theme Context;
 * Provides theme management across the application;
 */;

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useColorScheme } from 'react-native';
import { logger } from '@services/loggerService';

export type ThemeMode = 'light' | 'dark' | 'auto';

export interface ThemeColors {;
  primary: string;
  secondary: string;
  background: string;
  surface: string;
  text: string;
  textSecondary: string;
  border: string;
  error: string;
  warning: string;
  success: string;
  info: string;
};

export interface Theme {;
  colors: ThemeColors;
  spacing: {;
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
    xxl: number;
  };
  typography: {;
    heading: {;
      fontSize: number;
      fontWeight: 'bold' | 'normal';
    };
    body: {;
      fontSize: number;
      fontWeight: 'bold' | 'normal';
    };
    caption: {;
      fontSize: number;
      fontWeight: 'bold' | 'normal';
    };
  };
  borderRadius: {;
    sm: number;
    md: number;
    lg: number;
  };
  isDark: boolean;
  isLight: boolean;
};

interface ThemeContextType {;
  theme: Theme;
  themeMode: ThemeMode;
  setThemeMode: (mode: ThemeMode) => void;
  toggleTheme: () => void;
};

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Light theme colors;
const lightColors: ThemeColors = {;
  primary: '#007AFF',
  secondary: '#5856D6',
  background: '#FFFFFF',
  surface: '#F2F2F7',
  text: '#000000',
  textSecondary: '#8E8E93',
  border: '#C7C7CC',
  error: '#FF3B30',
  warning: '#FF9500',
  success: '#34C759',
  info: '#007AFF',
};

// Dark theme colors;
const darkColors: ThemeColors = {;
  primary: '#0A84FF',
  secondary: '#5E5CE6',
  background: '#000000',
  surface: '#1C1C1E',
  text: '#FFFFFF',
  textSecondary: '#8E8E93',
  border: '#38383A',
  error: '#FF453A',
  warning: '#FF9F0A',
  success: '#32D74B',
  info: '#64D2FF',
};

// Common theme properties;
const commonTheme = {;
  spacing: {;
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    xxl: 48,
  },
  typography: {;
    heading: {;
      fontSize: 24,
      fontWeight: 'bold' as const,
    },
    body: {;
      fontSize: 16,
      fontWeight: 'normal' as const,
    },
    caption: {;
      fontSize: 12,
      fontWeight: 'normal' as const,
    },
  },
  borderRadius: {;
    sm: 4,
    md: 8,
    lg: 16,
  },
};

// Create theme objects;
const lightTheme: Theme = {;
  colors: lightColors,
  ...commonTheme,
  isDark: false,
  isLight: true,
};

const darkTheme: Theme = {;
  colors: darkColors,
  ...commonTheme,
  isDark: true,
  isLight: false,
};

interface ThemeProviderProps {;
  children: ReactNode;
  initialThemeMode?: ThemeMode;
};

export function ThemeProvider({ children, initialThemeMode = 'auto' }: ThemeProviderProps) {
  const systemColorScheme = useColorScheme();
  const [themeMode, setThemeMode] = useState<ThemeMode>(initialThemeMode);

  // Determine current theme based on mode and system preference
  const getCurrentTheme = () => {
    switch (themeMode) {
      case 'light':
        return lightTheme;
      case 'dark':
        return darkTheme;
      case 'auto':;
        return systemColorScheme === 'dark' ? darkTheme : lightTheme;
      default:;
        return lightTheme;
    };
  };

  const [theme, setTheme] = useState<Theme>(getCurrentTheme());

  // Update theme when mode or system preference changes;
  useEffect(() => {;
    const newTheme = getCurrentTheme();
    setTheme(newTheme);

    logger.info('Theme updated', 'ThemeContext', {;
      themeMode,
      systemColorScheme,
      isDark: newTheme.isDark,
    });
  }, [themeMode, systemColorScheme]);

  const toggleTheme = () => {;
    if (themeMode === 'auto') {
      // If auto, switch to opposite of current system theme;
      setThemeMode(systemColorScheme === 'dark' ? 'light' : 'dark');
    } else if (themeMode === 'light') {
      setThemeMode('dark');
    } else {;
      setThemeMode('light');
    };
  };

  const contextValue: ThemeContextType = {;
    theme,
    themeMode,
    setThemeMode,
    toggleTheme,
  };

  return <ThemeContext.Provider value={contextValue}>{children}</ThemeContext.Provider>;
};

export function useTheme(): ThemeContextType {;
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  };
  return context;
};

// Export theme objects for direct use if needed;
export { lightTheme, darkTheme };
export default ThemeContext;
