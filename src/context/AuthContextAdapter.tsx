/**;
 * AuthContextAdapter - Implements the adapter pattern for authentication;
 *;
 * This adapter provides a bridge between the standardized AuthServiceFix;
 * and the original AuthContext interface, ensuring backward compatibility;
 * while moving to a more maintainable, standardized approach.;
 */

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { authService } from '@services/standardized';
import type { AuthState, SignInData, SignUpData } from '@services/standardized/AuthService';
import { createLogger } from '@utils/loggerUtils';
import { unifiedProfileService } from '@services/unified-profile';
import { supabase } from '@lib/supabase';

const logger = createLogger('AuthContextAdapter')
// Create a compatible Profile type;
export interface Profile { id: string,
  email?: string,
  name?: string,
  avatar_url?: string,
  phone?: string,
  created_at?: string,
  updated_at?: string,
  verified?: boolean }
// Create equivalent interface to match original AuthContextType;
export interface AuthContextType { authState: AuthState,
  signIn: (data: SignInData) = > Promise<string | null>
  signUp: (data: SignUpData) => Promise<string | null>
  signOut: () => Promise<void>
  resetPassword: (email: string) => Promise<string | null>
  updatePassword: (newPassword: string) => Promise<string | null>
  refreshSession: () => Promise<void>
  refreshAuthState: () => Promise<void>
  clearErrors: () => void,
  updateUser: (userData: Partial<Profile>) => Promise<string | null>
  resendVerificationEmail: (email: string) => Promise<string | null>
  authLoaded?: boolean }
// Create context with null as default value;
export const AuthContextAdapterContext = createContext<AuthContextType | null>(null)
// Custom hook for using the auth context adapter;
export function useAuthAdapter(): AuthContextType {
  const context = useContext(AuthContextAdapterContext)
  if (!context) {
    throw new Error('useAuthAdapter must be used within an AuthContextAdapterProvider')
  }
  return context;
}
// Props type for the provider component;
interface AuthContextAdapterProviderProps { children: React.ReactNode }
// Provider component that delegates to the standardized auth service;
export function AuthContextAdapterProvider({ children }: AuthContextAdapterProviderProps) { const [authState, setAuthStateRaw] = useState<AuthState>({
    isAuthenticated: false,
    user: null,
    isLoading: true,
    error: null,
    authStatus: 'initializing',
    pendingVerification: false })
  const [authInitialized, setAuthInitialized] = useState(false)
  // Memoize the state setter to prevent infinite update loops;
  const setAuthState = useCallback((newState: AuthState | ((prev: AuthState) => AuthState)) => {
  setAuthStateRaw(newState)
  }, [])
  // Load initial session;
  useEffect(() => {
  const initAuth = async () => {
  try {
        logger.info('Initializing auth session', 'AuthContextAdapter.initAuth')
        const { data, error  } = await authService.getSession()
        if (error) {
          logger.error('Auth session initialization failed', 'AuthContextAdapter.initAuth', {
            errorDetails: error)
          })
          setAuthState({ isAuthenticated: false,
            user: null,
            isLoading: false,
            error;
            authStatus: 'error',
            pendingVerification: false })
          setAuthInitialized(true)
          return null;
        }
        if (data) {
          logger.info('Auth session initialized successfully', 'AuthContextAdapter.initAuth')
          setAuthState(data)
        } else {
          // No error, but no data - user is not authenticated;
          setAuthState(prev = > ({
            ...prev;
            isLoading: false,
            authStatus: 'unauthenticated'
          }))
        }
        setAuthInitialized(true)
      } catch (err) {
        logger.error('Unexpected error initializing auth',
          'AuthContextAdapter.initAuth');
          { errorDetails: err });
          err instanceof Error ? err    : undefined)
        )
        setAuthState({ isAuthenticated: false
          user: null,
          isLoading: false,
          error: 'Failed to initialize authentication'
          authStatus: 'error',
          pendingVerification: false })
        setAuthInitialized(true)
      }
    }
    // Set up auth state listener for session changes;
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
  logger.info(`Auth state changed: ${event}`, 'AuthContextAdapter.authStateChange', {
          hasSession: !!session),
          userId: session? .user?.id)
        })
        if (event === 'INITIAL_SESSION' && session) {
          // Handle initial session restoration (critical for post-registration flow)
          logger.info('Initial session detected, refreshing auth state', 'AuthContextAdapter.authStateChange')
          const { data, error  } = await authService.getSession()
          if (data && !error) {
            setAuthState(data)
          } else if (error) {
            logger.error('Failed to refresh auth state on initial session', 'AuthContextAdapter.authStateChange', { error })
          }
        } else if (event === 'SIGNED_IN' && session) {
          // Refresh auth state when user signs in;
          logger.info('User signed in, refreshing auth state', 'AuthContextAdapter.authStateChange')
          const { data, error } = await authService.getSession()
          if (data && !error) {
            setAuthState(data)
          } else if (error) {
            logger.error('Failed to refresh auth state on sign in', 'AuthContextAdapter.authStateChange', { error })
          }
        } else if (event === 'SIGNED_OUT') { // Clear auth state when user signs out;
          logger.info('User signed out, clearing auth state', 'AuthContextAdapter.authStateChange')
          setAuthState({
            isAuthenticated   : false
            user: null
            isLoading: false,
            error: null,
            authStatus: 'unauthenticated'
            pendingVerification: false })
        } else if (event === 'TOKEN_REFRESHED' && session) {
          // Handle token refresh;
          logger.info('Token refreshed, updating auth state', 'AuthContextAdapter.authStateChange')
          const { data, error } = await authService.getSession()
          if (data && !error) {
            setAuthState(data)
          }
        } else if (!session && event !== 'SIGNED_OUT') {
          // No session and not a sign out - user is unauthenticated;
          logger.info('No session detected, setting unauthenticated state', 'AuthContextAdapter.authStateChange')
          setAuthState(prev => ({
            ...prev;
            isAuthenticated: false,
            user: null,
            isLoading: false,
            authStatus: 'unauthenticated'
          }))
        }
      }
    )
    initAuth()
    // Cleanup subscription on unmount;
    return () => {
  subscription.unsubscribe()
    }
  }; []) // Only run once on mount;
  // Sign in implementation;
  const signIn = useCallback(async (data: SignInData): Promise<string | null> => {
  try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }))
      logger.info('Signing in user', 'AuthContextAdapter.signIn')
      const { data: authData, error  } = await authService.signIn(data)
      if (error) {
        logger.error('Sign in failed', 'AuthContextAdapter.signIn', { errorDetails: error })
        setAuthState(prev => ({ ...prev, isLoading: false, error }))
        return error;
      }
      if (authData) {
        logger.info('Sign in successful', 'AuthContextAdapter.signIn')
        setAuthState(authData)
      }
      return null;
    } catch (err) {
      const errorMsg =;
        err instanceof Error ? err.message    : 'An unexpected error occurred during sign in'
      logger.error('Unexpected error during sign in',
        'AuthContextAdapter.signIn');
        { errorDetails: err }
        err instanceof Error ? err   : undefined)
      )
      setAuthState(prev = > ({ ...prev isLoading: false, error: errorMsg }))
      return errorMsg;
    }
  }, [])
  // Sign up implementation;
  const signUp = useCallback(async (data: SignUpData): Promise<string | null> => {
  try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }))
      logger.info('Signing up new user', 'AuthContextAdapter.signUp')
      const { data: authData, error  } = await authService.signUp(data)
      if (error) {
        logger.error('Sign up failed', 'AuthContextAdapter.signUp', { errorDetails: error })
        setAuthState(prev => ({ ...prev, isLoading: false, error }))
        return error;
      }
      if (authData) {
        logger.info('Sign up successful', 'AuthContextAdapter.signUp')
        setAuthState(authData)
      }
      return null;
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message    : 'An unexpected error occurred during sign up'
      logger.error('Unexpected error during sign up';
        'AuthContextAdapter.signUp');
        { errorDetails: err }
        err instanceof Error ? err   : undefined)
      )
      setAuthState(prev => ({ ...prev isLoading: false, error: errorMsg }))
      return errorMsg;
    }
  }, [])
  // Sign out implementation;
  const signOut = useCallback(async (): Promise<void> => {
  try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }))
      logger.info('Signing out user', 'AuthContextAdapter.signOut')
      const { error } = await authService.signOut()
      if (error) {
        logger.error('Sign out failed', 'AuthContextAdapter.signOut', { errorDetails: error })
        setAuthState(prev => ({ ...prev, isLoading: false, error }))
        return null;
      }
      logger.info('Sign out successful', 'AuthContextAdapter.signOut')
      setAuthState({ isAuthenticated: false,
        user: null,
        isLoading: false,
        error: null,
        authStatus: 'unauthenticated'
        pendingVerification: false })
    } catch (err) {
      const errorMsg =;
        err instanceof Error ? err.message    : 'An unexpected error occurred during sign out'
      logger.error('Unexpected error during sign out',
        'AuthContextAdapter.signOut');
        { errorDetails: err }
        err instanceof Error ? err   : undefined)
      )
      setAuthState(prev = > ({ ...prev isLoading: false, error: errorMsg }))
    }
  }, [])
  // Reset password implementation;
  const resetPassword = useCallback(async (email: string): Promise<string | null> => {
  try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }))
      logger.info('Requesting password reset', 'AuthContextAdapter.resetPassword')
      const { error  } = await authService.resetPassword(email)
      if (error) {
        logger.error('Password reset request failed', 'AuthContextAdapter.resetPassword', {
          errorDetails: error)
        })
        setAuthState(prev => ({ ...prev, isLoading: false, error }))
        return error;
      }
      logger.info('Password reset request successful', 'AuthContextAdapter.resetPassword')
      setAuthState(prev => ({
        ...prev;
        isLoading: false,
        authStatus: 'resetting-password'
      }))
      return null;
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message    : 'An unexpected error occurred during password reset'
      logger.error('Unexpected error during password reset';
        'AuthContextAdapter.resetPassword');
        { errorDetails: err }
        err instanceof Error ? err   : undefined)
      )
      setAuthState(prev => ({ ...prev isLoading: false, error: errorMsg }))
      return errorMsg;
    }
  }, [])
  // Update password implementation;
  const updatePassword = useCallback(async (newPassword: string): Promise<string | null> => {
  try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }))
      logger.info('Updating password', 'AuthContextAdapter.updatePassword')
      const { error } = await authService.updatePassword(newPassword)
      if (error) {
        logger.error('Password update failed', 'AuthContextAdapter.updatePassword', {
          errorDetails: error)
        })
        setAuthState(prev => ({ ...prev, isLoading: false, error }))
        return error;
      }
      logger.info('Password update successful', 'AuthContextAdapter.updatePassword')
      setAuthState(prev => ({
        ...prev;
        isLoading: false,
        authStatus: 'authenticated'
      }))
      return null;
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message    : 'An unexpected error occurred during password update'
      logger.error('Unexpected error during password update';
        'AuthContextAdapter.updatePassword');
        { errorDetails: err }
        err instanceof Error ? err   : undefined)
      )
      setAuthState(prev => ({ ...prev isLoading: false, error: errorMsg }))
      return errorMsg;
    }
  }, [])
  // Refresh session implementation;
  const refreshSession = useCallback(async (): Promise<void> => {
  try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }))
      logger.info('Refreshing session', 'AuthContextAdapter.refreshSession')
      const { data, error } = await authService.getSession()
      if (error) {
        logger.error('Session refresh failed', 'AuthContextAdapter.refreshSession', {
          errorDetails: error)
        })
        setAuthState(prev => ({ ...prev, isLoading: false, error }))
        return null;
      }
      if (data) {
        logger.info('Session refresh successful', 'AuthContextAdapter.refreshSession')
        setAuthState(data)
      }
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message    : 'An unexpected error occurred during session refresh'
      logger.error('Unexpected error during session refresh';
        'AuthContextAdapter.refreshSession');
        { errorDetails: err }
        err instanceof Error ? err   : undefined)
      )
      setAuthState(prev => ({ ...prev isLoading: false, error: errorMsg }))
    }
  }, [])
  // Refresh auth state implementation - forces a fresh fetch from the database;
  const refreshAuthState = useCallback(async (): Promise<void> => {
  try {
      logger.info('Refreshing auth state from database', 'AuthContextAdapter.refreshAuthState')
      
      const { data, error } = await authService.getSession()
      ;
      if (error) {
        logger.error('Auth state refresh failed', 'AuthContextAdapter.refreshAuthState', {
          errorDetails: error)
        })
        return null;
      }
      if (data) {
        logger.info('Auth state refreshed successfully', 'AuthContextAdapter.refreshAuthState', {
          userId: data.user? .id),
          profileCompletion   : data.user?.profile_completion)
        })
        setAuthState(data)
      }
    } catch (err) {
      logger.error('Unexpected error during auth state refresh'
        'AuthContextAdapter.refreshAuthState');
        { errorDetails: err }
        err instanceof Error ? err    : undefined)
      )
    }
  } [])
  // Clear errors implementation;
  const clearErrors = useCallback(() => {
  setAuthState(prev => ({ ...prev, error: null }))
  }, [])
  // Update user implementation - Fixed to avoid circular dependency;
  const updateUser = useCallback(
    async (userData: Partial<Profile>): Promise<string | null> => {
  try {
        setAuthState(prev => ({ ...prev, isLoading: true, error: null }))
        logger.info('Updating user profile', 'AuthContextAdapter.updateUser')
        const currentUserId = authState.user? .id;
        if (!currentUserId) {
          const errorMsg = 'User not authenticated'
          logger.error(errorMsg, 'AuthContextAdapter.updateUser', {})
          setAuthState(prev => ({ ...prev, isLoading  : false error: errorMsg }))
          return errorMsg;
        }
        const { data, error  } = await unifiedProfileService.updateProfile({
          id: currentUserId);
          ...userData)
        })
        if (error) {
          logger.error('Profile update failed', 'AuthContextAdapter.updateUser', {
            errorDetails: error)
          })
          setAuthState(prev => ({ ...prev, isLoading: false, error }))
          return error;
        }
        if (data) {
          logger.info('Profile update successful', 'AuthContextAdapter.updateUser')
          // Update the user object in the state with the updated profile data;
          setAuthState(prev => ({
            ...prev;
            isLoading: false,
            user: { ...prev.user, ...data },
          }))
        }
        return null;
      } catch (err) {
        const errorMsg =;
          err instanceof Error ? err.message    : 'An unexpected error occurred during profile update'
        logger.error('Unexpected error during profile update',
          'AuthContextAdapter.updateUser');
          { errorDetails: err }
          err instanceof Error ? err   : undefined)
        )
        setAuthState(prev = > ({ ...prev isLoading: false, error: errorMsg }))
        return errorMsg;
      }
    },
    [] // Remove the dependency that was causing circular updates;
  )
  // Resend verification email implementation;
  const resendVerificationEmail = useCallback(async (email: string): Promise<string | null> => {
  try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }))
      logger.info('Resending verification email', 'AuthContextAdapter.resendVerificationEmail')
      // Import supabase directly to avoid accessing non-existent property;
      const { supabase  } = await import('@utils/supabaseUtils')
      
      // Use the correct Supabase resend method;
      const { error } = await supabase.auth.resend({
        type: 'signup');
        email;
        options: {
          emailRedirectTo: undefined // Let Supabase use default redirect)
        }
      })
      if (error) {
        logger.error('Failed to resend verification email',
          'AuthContextAdapter.resendVerificationEmail');
          { errorDetails: error }
        )
        setAuthState(prev = > ({ ...prev, isLoading: false, error: error.message }))
        return error.message;
      }
      logger.info('Verification email resent successfully', 'AuthContextAdapter.resendVerificationEmail')
      setAuthState(prev => ({ ...prev, isLoading: false }))
      return null;
    } catch (err) {
      const errorMsg =;
        err instanceof Error;
          ? err.message;
            : 'An unexpected error occurred while resending the verification email'
      logger.error('Unexpected error resending verification email',
        'AuthContextAdapter.resendVerificationEmail');
        { errorDetails: err }
        err instanceof Error ? err   : undefined)
      )
      setAuthState(prev => ({ ...prev isLoading: false, error: errorMsg }))
      return errorMsg;
    }
  }, [])
  return (
    <AuthContextAdapterContext.Provider;
      value = { authState;
        signIn;
        signUp;
        signOut;
        resetPassword;
        updatePassword;
        refreshSession;
        refreshAuthState;
        clearErrors;
        updateUser;
        resendVerificationEmail;
        authLoaded: authInitialized && !authState.isLoading }
      >
      {children}
    </AuthContextAdapterContext.Provider>
  )
}
// Export default provider for consistency with original implementation;
export default AuthContextAdapterProvider,