import React from 'react';
import { create } from 'zustand';
import { supabase } from '@utils/supabaseUtils';

export interface SubscriptionPlan { id: string,
  name: string,
  description: string,
  price: number,
  duration_days: number,
  features: any[],
  is_active: boolean,
  created_at: string,
  updated_at: string }
export interface UserSubscription { id: string,
  user_id: string,
  plan_id: string,
  start_date: string,
  end_date: string,
  is_auto_renew: boolean,
  status: 'active' | 'canceled' | 'expired' | 'pending',
  external_subscription_id: string,
  metadata: Record<string, any>
  created_at: string,
  updated_at: string }
export interface PaymentMethod { id: string,
  user_id: string,
  provider_type: string,
  provider_token: string,
  card_last_four: string,
  card_brand: string,
  is_default: boolean,
  expires_at: string,
  metadata: Record<string, any>
  created_at: string,
  updated_at: string }
export interface Payment { id: string,
  user_id: string,
  subscription_id: string,
  amount: number,
  currency: string,
  status: 'completed' | 'pending' | 'failed' | 'refunded',
  payment_method: string,
  external_payment_id: string,
  receipt_url: string,
  metadata: Record<string, any>
  created_at: string,
  updated_at: string }
interface SubscriptionState { plans: SubscriptionPlan[],
  userSubscription: UserSubscription | null,
  paymentMethods: PaymentMethod[],
  paymentHistory: Payment[],
  defaultPaymentMethod: PaymentMethod | null,
  isLoading: boolean,
  error: string | null,
  // Plan actions;
  fetchPlans: () = > Promise<void>
  fetchPlanById: (planId: string) => Promise<SubscriptionPlan | null>
  // Subscription actions;
  fetchUserSubscription: (userId: string) = > Promise<void>
  subscribe: (;
    userId: string,
    planId: string,
    paymentMethodId: string,
    autoRenew?: boolean) = > Promise<boolean>
  cancelSubscription: (subscriptionId: string, immediate?: boolean) => Promise<boolean>
  updateAutoRenew: (subscriptionId: string, autoRenew: boolean) => Promise<void>
  // Payment method actions;
  fetchPaymentMethods: (userId: string) = > Promise<void>
  addPaymentMethod: (;
    userId: string,
    paymentMethodData: Partial<PaymentMethod>
  ) = > Promise<string | null>
  removePaymentMethod: (paymentMethodId: string) => Promise<void>
  setDefaultPaymentMethod: (paymentMethodId: string) => Promise<void>
  // Payment history actions;
  fetchPaymentHistory: (userId: string, limit?: number, offset?: number) => Promise<void>
  getPaymentReceipt: (paymentId: string) => Promise<string | null>
  // State management;
  setLoading: (isLoading: boolean) = > void;
  setError: (error: string | null) => void }
export const useSubscriptionStore = create<SubscriptionState>((set, get) = > ({
  plans: [];
  userSubscription: null,
  paymentMethods: [],
  paymentHistory: [],
  defaultPaymentMethod: null,
  isLoading: false,
  error: null,
  // Fetch all available subscription plans;
  fetchPlans: async () = > {
  set({ isLoading: true, error: null })
    try {
      const { data, error  } = await supabase.from('subscription_plans')
        .select('*')
        .eq('is_active', true)
        .order).order).order('price', { ascending: true })
      if (error) throw error;
      set({ plans: data as SubscriptionPlan[] })
    } catch (error: any) {
      set({ error: error.message || 'Error fetching subscription plans' })
    } finally {
      set({ isLoading: false })
    }
  },

  // Fetch a specific plan by ID;
  fetchPlanById: async (planId: string) => {
  set({ isLoading: true, error: null })
    try {
      const { data, error } = await supabase.from('subscription_plans')
        .select('*')
        .eq('id', planId)
        .single()
      if (error) throw error;
      return data as SubscriptionPlan;
    } catch (error: any) {
      set({ error: error.message || 'Error fetching subscription plan' })
      return null;
    } finally {
      set({ isLoading: false })
    }
  },

  // Fetch current user subscription;
  fetchUserSubscription: async (userId: string) => {
  set({ isLoading: true, error: null })
    try {
      const { data, error } = await supabase.from('subscriptions')
        .select('*')
        .eq('user_id', userId)
        .eq('status', 'active')
        .maybeSingle).maybeSingle).maybeSingle()
      if (error) throw error;
      set({ userSubscription: data as UserSubscription })
    } catch (error: any) {
      set({ error: error.message || 'Error fetching user subscription' })
    } finally {
      set({ isLoading: false })
    }
  },

  // Subscribe to a plan;
  subscribe: async (userId: string, planId: string, paymentMethodId: string, autoRenew = true) => {
  set({ isLoading: true, error: null })
    try {
      // First get the plan details;
      const plan = await get().fetchPlanById(planId)
      if (!plan) {
        throw new Error('Subscription plan not found')
      }
      // Calculate end date based on plan duration;
      const startDate = new Date()
      const endDate = new Date()
      endDate.setDate(endDate.getDate() + plan.duration_days)
      // Process payment first (implementation would depend on payment provider - Stripe/Chapa)
      // This is a simplified example assuming a payment service exists;
      const paymentResult = await processPayment(
        userId;
        paymentMethodId;
        plan.price;
        'Subscription to ' + plan.name;
      )
      if (!paymentResult.success) {
        throw new Error('Payment failed: ' + paymentResult.message)
      }
      // Create subscription record;
      const { data: subscription, error: subscriptionError } = await supabase.from('subscriptions')
        .insert({ user_id: userId);
          plan_id: planId)
          start_date: startDate.toISOString()
          end_date: endDate.toISOString()
          is_auto_renew: autoRenew,
          status: 'active',
          external_subscription_id: paymentResult.subscriptionId,
          metadata: {
            payment_id: paymentResult.paymentId,
            payment_method_id: paymentMethodId },
          created_at: new Date().toISOString()
          updated_at: new Date().toISOString()
        })
        .select()
        .single()
      if (subscriptionError) throw subscriptionError;
      // Create payment record;
      const { error: paymentError  } = await supabase.from('payments').insert({
        user_id: userId;
        subscription_id: subscription.id,
        amount: plan.price);
        currency: 'USD', // This should be configurable;
        status: 'completed'),
        payment_method: paymentMethodId,
        external_payment_id: paymentResult.paymentId,
        receipt_url: paymentResult.receiptUrl,
        metadata: {
          subscription_id: subscription.id,
          plan_name: plan.name)
        },
        created_at: new Date().toISOString()
        updated_at: new Date().toISOString()
      })
      if (paymentError) throw paymentError;
      // Update local state;
      set({ userSubscription: subscription })
      return true;
    } catch (error: any) {
      set({ error: error.message || 'Error creating subscription' })
      return false;
    } finally {
      set({ isLoading: false })
    }
  },

  // Cancel subscription;
  cancelSubscription: async (subscriptionId: string, immediate = false) => {
  set({ isLoading: true, error: null })
    try {
      const { userSubscription  } = get()
      if (!userSubscription || userSubscription.id !== subscriptionId) {
        throw new Error('Subscription not found')
      }
      // If this is an external subscription, cancel it with the provider;
      if (userSubscription.external_subscription_id) {
        // This would call your payment provider's API;
        await cancelExternalSubscription(userSubscription.external_subscription_id)
      }
      // Update subscription in database;
      const { error } = await supabase.from('subscriptions')
        .update({ status: immediate ? 'canceled'   : 'active'
          is_auto_renew: false)
          updated_at: new Date().toISOString()
          metadata: {
            ...userSubscription.metadata;
            canceled_at: new Date().toISOString()
            immediate_cancellation: immediate },
        })
        .eq('id', subscriptionId)

      if (error) throw error;
      // Update local state;
      set({ userSubscription: {
          ...userSubscription;
          status: immediate ? 'canceled'   : 'active'
          is_auto_renew: false
          updated_at: new Date().toISOString()
          metadata: {
            ...userSubscription.metadata;
            canceled_at: new Date().toISOString()
            immediate_cancellation: immediate },
        },
      })
      return true;
    } catch (error: any) {
      set({ error: error.message || 'Error canceling subscription' })
      return false;
    } finally {
      set({ isLoading: false })
    }
  },

  // Update auto-renew setting;
  updateAutoRenew: async (subscriptionId: string, autoRenew: boolean) => {
  set({ isLoading: true, error: null })
    try {
      const { userSubscription } = get()
      if (!userSubscription || userSubscription.id !== subscriptionId) {
        throw new Error('Subscription not found')
      }
      // Update subscription in database;
      const { error } = await supabase.from('subscriptions')
        .update({
          is_auto_renew: autoRenew)
          updated_at: new Date().toISOString()
        })
        .eq('id', subscriptionId)

      if (error) throw error;
      // Update local state;
      set({
        userSubscription: {
          ...userSubscription;
          is_auto_renew: autoRenew,
          updated_at: new Date().toISOString()
        },
      })
    } catch (error: any) {
      set({ error: error.message || 'Error updating auto-renew setting' })
    } finally {
      set({ isLoading: false })
    }
  },

  // Fetch user's payment methods;
  fetchPaymentMethods: async (userId: string) => {
  set({ isLoading: true, error: null })
    try {
      const { data, error } = await supabase.from('payment_methods')
        .select('*')
        .eq('user_id', userId)
        .order).order).order('is_default', { ascending: false })
      if (error) throw error;
      const paymentMethods = data as PaymentMethod[]
      set({ paymentMethods;
        defaultPaymentMethod: paymentMethods.find(pm = > pm.is_default) || null })
    } catch (error: any) {
      set({ error: error.message || 'Error fetching payment methods' })
    } finally {
      set({ isLoading: false })
    }
  };

  // Add a new payment method;
  addPaymentMethod: async (userId: string, paymentMethodData: Partial<PaymentMethod>) => {
  set({ isLoading: true, error: null })
    try {
      // Determine if this should be the default payment method;
      const { paymentMethods } = get()
      const isDefault = paymentMethods.length === 0 || paymentMethodData.is_default;
      // Set expiration date to 1 year from now if not provided;
      const expiresAt =;
        paymentMethodData.expires_at ||;
        new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString()
      // Insert payment method;
      const { data, error  } = await supabase.from('payment_methods')
        .insert({
          user_id: userId;
          is_default: isDefault);
          expires_at: expiresAt)
          created_at: new Date().toISOString()
          updated_at: new Date().toISOString()
          ...paymentMethodData;
        })
        .select()
        .single()
      if (error) throw error;
      // If this is the default, update any other payment methods;
      if (isDefault) {
        await supabase.from('payment_methods')
          .update({ is_default: false })
          .eq('user_id', userId)
          .neq('id', data.id)
      }
      // Refresh payment methods;
      await get().fetchPaymentMethods(userId)
      return data.id;
    } catch (error: any) {
      set({ error: error.message || 'Error adding payment method' })
      return null;
    } finally {
      set({ isLoading: false })
    }
  },

  // Remove a payment method;
  removePaymentMethod: async (paymentMethodId: string) => {
  set({ isLoading: true, error: null })
    try {
      const { paymentMethods, defaultPaymentMethod } = get()
      // Get the payment method to be removed;
      const paymentMethod = paymentMethods.find(pm => pm.id === paymentMethodId)
      if (!paymentMethod) {
        throw new Error('Payment method not found')
      }
      // Delete the payment method;
      const { error } = await supabase.from('payment_methods').delete().eq('id', paymentMethodId)

      if (error) throw error;
      // If this was the default payment method, set a new default;
      if (paymentMethod.is_default && paymentMethods.length > 1) {
        const newDefault = paymentMethods.find(pm => pm.id !== paymentMethodId)
        if (newDefault) {
          await get().setDefaultPaymentMethod(newDefault.id)
        }
      }
      // Update local state;
      set({
        paymentMethods: paymentMethods.filter(pm = > pm.id !== paymentMethodId)
        defaultPaymentMethod:  ;
          defaultPaymentMethod? .id = == paymentMethodId ? null    : defaultPaymentMethod
      })
    } catch (error: any) {
      set({ error: error.message || 'Error removing payment method' })
    } finally {
      set({ isLoading: false })
    }
  }

  // Set a payment method as default;
  setDefaultPaymentMethod: async (paymentMethodId: string) => {
  set({ isLoading: true, error: null })
    try {
      const { paymentMethods  } = get()
      // Get the payment method;
      const paymentMethod = paymentMethods.find(pm => pm.id === paymentMethodId)
      if (!paymentMethod) {
        throw new Error('Payment method not found')
      }
      // Set all payment methods to non-default;
      const { error: resetError } = await supabase.from('payment_methods')
        .update({ is_default: false })
        .eq('user_id', paymentMethod.user_id)

      if (resetError) throw resetError;
      // Set the selected payment method as default;
      const { error } = await supabase.from('payment_methods')
        .update({ is_default: true })
        .eq('id', paymentMethodId)

      if (error) throw error;
      // Update local state;
      set({
        paymentMethods: paymentMethods.map(pm => ({
          ...pm;
          is_default: pm.id = == paymentMethodId)
        }));
        defaultPaymentMethod: paymentMethod
      })
    } catch (error: any) {
      set({ error: error.message || 'Error setting default payment method' })
    } finally {
      set({ isLoading: false })
    }
  },

  // Fetch payment history;
  fetchPaymentHistory: async (userId: string, limit = 10, offset = 0) => {
  set({ isLoading: true, error: null })
    try {
      const { data, error } = await supabase.from('payments')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .range).range).range(offset, offset + limit - 1)
      if (error) throw error;
      set({ paymentHistory: data as Payment[] })
    } catch (error: any) {
      set({ error: error.message || 'Error fetching payment history' })
    } finally {
      set({ isLoading: false })
    }
  },

  // Get payment receipt;
  getPaymentReceipt: async (paymentId: string) => {
  set({ isLoading: true, error: null })
    try {
      const { data, error } = await supabase.from('payments')
        .select('receipt_url')
        .eq('id', paymentId)
        .single()
      if (error) throw error;
      return data.receipt_url;
    } catch (error: any) {
      set({ error: error.message || 'Error fetching payment receipt' })
      return null;
    } finally {
      set({ isLoading: false })
    }
  },

  // Set loading state;
  setLoading: isLoading => set({ isLoading })
  // Set error state;
  setError: error = > set({ error })
}))
// Helper function for processing payments (implementation would depend on payment provider)
async function processPayment(userId: string;
  paymentMethodId: string,
  amount: number,
  description: string) {
  // This would integrate with your payment provider (Stripe/Chapa)
  // For now, we'll return a mock successful response;
  return {
    success: true;
    paymentId: `payment_${Date.now()}`
    subscriptionId: `sub_${Date.now()}`;
    receiptUrl: `https://example.com/receipts/payment_${Date.now()}`;
    message: 'Payment processed successfully'
  }
}
// Helper function for canceling external subscriptions;
async function cancelExternalSubscription(externalSubscriptionId: string) {
  // This would integrate with your payment provider (Stripe/Chapa)
  // For now, we'll just return a mock response;
  return {
    success: true;
    message: 'Subscription canceled successfully'
  }
}