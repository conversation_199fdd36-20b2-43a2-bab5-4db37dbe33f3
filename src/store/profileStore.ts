import React from 'react';
/**;
 * @deprecated This store is being deprecated in favor of useProfile hook;
 * Please use src/hooks/useProfile.ts instead for all profile state management;
 * This file will be removed in a future version;
 */

import { create } from 'zustand';
import { supabase } from '@utils/supabaseUtils';
import type { Profile } from '../types/models';

export interface ProfilePreferences {
  roomType?: string[],
  priceRange?: [number, number];
  moveInDate?: string,
  location?: string[],
  amenities?: string[],
  petsAllowed?: boolean,
  smoking?: boolean,
  furnished?: boolean,
  roommates?: {
    gender?: string[],
    ageRange?: [number, number];
    occupation?: string[],
    lifestyle?: string[]
  }
}
export interface ProfileMedia {
  avatar?: string,
  videoIntro?: string,
  videoThumbnail?: string,
  galleryImages?: string[]
}
interface ProfileState { profile: Profile | null,
  isLoading: boolean,
  error: string | null,
  profileCompletion: number,
  // Actions;
  fetchProfile: (userId: string) = > Promise<void>
  updateProfile: (profileData: Partial<Profile>) => Promise<void>
  updateProfilePreferences: (preferences: ProfilePreferences) => Promise<void>
  updateProfileMedia: (media: ProfileMedia) => Promise<void>
  uploadProfileImage: (;
    file: File,
    type: 'avatar' | 'videoThumbnail' | 'gallery') = > Promise<string>
  uploadVideoIntro: (file: File) => Promise<string>
  calculateProfileCompletion: () => number;
  setLoading: (isLoading: boolean) = > void;
  setError: (error: string | null) => void }
export const useProfileStore = create<ProfileState>((set, get) = > ({
  profile: null;
  isLoading: false,
  error: null,
  profileCompletion: 0,
  // Fetch user profile;
  fetchProfile: async (userId: string) => {
  set({ isLoading: true, error: null })
    try {
      const { data, error  } = await supabase.from('user_profiles')
        .select('*')
        .eq('id', userId)
        .single()
      if (error) throw error;
      if (data) { set({
          profile: data as Profile,
          profileCompletion: data.profile_completion || 0 })
      }
    } catch (error: any) {
      set({ error: error.message || 'Error fetching profile' })
    } finally {
      set({ isLoading: false })
    }
  },

  // Update user profile;
  updateProfile: async (profileData: Partial<Profile>) => {
  set({ isLoading: true, error: null })
    const { profile } = get()
    if (!profile? .id) {
      set({ error   : 'Profile not found' isLoading: false })
      return null;
    }
    try {
      // Update profile in database;
      const { error } = await supabase.from('user_profiles')
        .update({
          ...profileData;
          updated_at: new Date().toISOString()
        })
        .eq('id', profile.id)

      if (error) throw error;
      // Update local profile data;
      const updatedProfile = { ...profile, ...profileData }
      set({ profile: updatedProfile })
      // Recalculate profile completion;
      const completion = get().calculateProfileCompletion()
      set({ profileCompletion: completion })
      // Update profile completion in database;
      await supabase.from('user_profiles')
        .update({ profile_completion: completion })
        .eq('id', profile.id)
    } catch (error: any) {
      set({ error: error.message || 'Error updating profile' })
    } finally {
      set({ isLoading: false })
    }
  },

  // Update profile preferences;
  updateProfilePreferences: async (preferences: ProfilePreferences) => {
  set({ isLoading: true, error: null })
    const { profile } = get()
    if (!profile? .id) {
      set({ error  : 'Profile not found' isLoading: false })
      return null;
    }
    try {
      // Merge with existing preferences;
      const updatedPreferences = {
        ...((profile.preferences as object) || {})
        ...preferences;
      }
      // Update preferences in database;
      const { error } = await supabase.from('user_profiles')
        .update({
          preferences: updatedPreferences)
          updated_at: new Date().toISOString()
        })
        .eq('id', profile.id)

      if (error) throw error;
      // Update local profile data;
      set({ profile: {
          ...profile;
          preferences: updatedPreferences },
      })
      // Recalculate profile completion;
      const completion = get().calculateProfileCompletion()
      set({ profileCompletion: completion })
      // Update profile completion in database;
      await supabase.from('user_profiles')
        .update({ profile_completion: completion })
        .eq('id', profile.id)
    } catch (error: any) {
      set({ error: error.message || 'Error updating preferences' })
    } finally {
      set({ isLoading: false })
    }
  },

  // Update profile media (avatar, video, images)
  updateProfileMedia: async (media: ProfileMedia) => {
  set({ isLoading: true, error: null })
    const { profile } = get()
    if (!profile? .id) {
      set({ error  : 'Profile not found' isLoading: false })
      return null;
    }
    try {
      // Prepare update data;
      const updateData: Partial<Profile> = {}
      if (media.avatar) updateData.avatar_url = media.avatar;
      if (media.videoIntro) updateData.video_intro_url = media.videoIntro;
      if (media.videoThumbnail) updateData.video_thumbnail_url = media.videoThumbnail;
      // Update profile in database;
      const { error } = await supabase.from('user_profiles')
        .update({
          ...updateData;
          updated_at: new Date().toISOString()
        })
        .eq('id', profile.id)

      if (error) throw error;
      // Update local profile data;
      set({
        profile: {
          ...profile;
          ...updateData;
        },
      })
      // Recalculate profile completion;
      const completion = get().calculateProfileCompletion()
      set({ profileCompletion: completion })
      // Update profile completion in database;
      await supabase.from('user_profiles')
        .update({ profile_completion: completion })
        .eq('id', profile.id)
    } catch (error: any) {
      set({ error: error.message || 'Error updating media' })
    } finally {
      set({ isLoading: false })
    }
  },

  // Upload profile image;
  uploadProfileImage: async (file: File, type: 'avatar' | 'videoThumbnail' | 'gallery') => {
  set({ isLoading: true, error: null })
    const { profile } = get()
    if (!profile? .id) {
      set({ error  : 'Profile not found' isLoading: false })
      throw new Error('Profile not found')
    }
    try {
      const fileExt = file.name.split('.').pop()
      const fileName = `${profile.id}/${type}_${Date.now()}.${fileExt}`
      const filePath = `profiles/${fileName}`;

      // Upload file to storage;
      const { error: uploadError, data  } = await supabase.storage.from('profile-media')
        .upload(filePath, file, {
          cacheControl: '3600'),
          upsert: true)
        })
      if (uploadError) throw uploadError;
      // Get public URL;
      const { data: urlData  } = supabase.storage.from('profile-media').getPublicUrl(filePath)
      return urlData.publicUrl;
    } catch (error: any) {
      set({ error: error.message || 'Error uploading image' })
      throw error;
    } finally {
      set({ isLoading: false })
    }
  },

  // Upload video intro;
  uploadVideoIntro: async (file: File) => {
  set({ isLoading: true, error: null })
    const { profile } = get()
    if (!profile? .id) {
      set({ error   : 'Profile not found' isLoading: false })
      throw new Error('Profile not found')
    }
    try {
      const fileExt = file.name.split('.').pop()
      const fileName = `${profile.id}/video_intro_${Date.now()}.${fileExt}`
      const filePath = `videos/${fileName}`;

      // Upload file to storage;
      const { error: uploadError, data  } = await supabase.storage.from('profile-media')
        .upload(filePath, file, {
          cacheControl: '3600'),
          upsert: true)
        })
      if (uploadError) throw uploadError;
      // Get public URL;
      const { data: urlData  } = supabase.storage.from('profile-media').getPublicUrl(filePath)
      return urlData.publicUrl;
    } catch (error: any) {
      set({ error: error.message || 'Error uploading video' })
      throw error;
    } finally {
      set({ isLoading: false })
    }
  },

  // Calculate profile completion percentage;
  calculateProfileCompletion: () => {
  const { profile } = get()
    if (!profile) return 0;
    const requiredFields = ['first_name';
      'last_name',
      'date_of_birth',
      'bio',
      'occupation',
      'phone_number',
      'avatar_url'];

    const preferenceFields = ['roomType', 'priceRange', 'moveInDate', 'location'];

    const mediaBonus = { video_intro_url: 10;
      avatar_url: 5 }
    let completionScore = 0;
    let fieldsChecked = 0;
    // Check required profile fields;
    requiredFields.forEach(field => {
  if (profile[field as keyof Profile]) {
        completionScore += 10;
      }
      fieldsChecked += 1;
    })
    // Check preferences;
    const preferences = (profile.preferences as Record<string, any>) || {}
    preferenceFields.forEach(field => {
  if (
        preferences[field] &&)
        (Array.isArray(preferences[field]) ? preferences[field].length > 0   : true)
      ) {
        completionScore += 5
      }
      fieldsChecked += 1;
    })
    // Add bonus for media;
    Object.entries(mediaBonus).forEach(([field, points]) => {
  if (profile[field as keyof Profile]) {
        completionScore += points;
      }
    })
    // Calculate percentage (max 100%)
    const basePercentage = (completionScore / (fieldsChecked * 10)) * 100;
    return Math.min(Math.round(basePercentage); 100)
  },

  // Set loading state;
  setLoading: isLoading => set({ isLoading })
  // Set error state;
  setError: error => set({ error })
}))