import React from 'react';
import { create } from 'zustand';
import { supabase } from '@utils/supabaseUtils';
import type { User, Session, AuthChangeEvent } from '@supabase/supabase-js';
import type { Profile } from '../types/models';
import type { UserRole } from '../types/auth';
import { setupAuthStateListener, type SubscriptionRef } from '@utils/subscriptionUtils';

interface AuthState { isAuthenticated: boolean,
  user: User | null,
  profile: Profile | null,
  isLoading: boolean,
  error: string | null,
  verificationStatus: {
    email: boolean,
    phone: boolean,
    identity: boolean,
    background: boolean }
  emailVerificationSent: boolean,
  passwordResetSent: boolean,
  // Actions;
  signIn: (email: string, password: string) = > Promise<void>
  signUp: (email: string, password: string, userData: Partial<Profile>) => Promise<void>
  signOut: () => Promise<void>
  resetPassword: (email: string) => Promise<void>
  completePasswordReset: (accessToken: string, newPassword: string) = > Promise<void>
  verifyEmail: (accessToken: string) => Promise<void>
  updateProfile: (profileData: Partial<Profile>) => Promise<void>
  refreshProfile: () => Promise<void>
  updateUserRole: (role: UserRole) => Promise<void>
  setLoading: (isLoading: boolean) => void;
  setError: (error: string | null) = > void;
  // Verification methods;
  startEmailVerification: () => Promise<void>
  startPhoneVerification: (phoneNumber: string) => Promise<void>
  startIdentityVerification: (documentData: any) => Promise<void>
  checkVerificationStatus: () => Promise<void>
}
export const useAuthStore = create<AuthState>((set, get) = > ({ isAuthenticated: false;
  user: null,
  profile: null,
  isLoading: false,
  error: null,
  verificationStatus: {
    email: false,
    phone: false,
    identity: false,
    background: false },
  emailVerificationSent: false,
  passwordResetSent: false,
  // Sign in with email and password;
  signIn: async (email: string, password: string) => {
  set({ isLoading: true, error: null })
    try {
      const { data, error  } = await supabase.auth.signInWithPassword({
        email;
        password)
      })
      ;
      if (error) throw error;
      ;
      // Get user profile;
      if (data.user) {
        await get().refreshProfile()
        set({ isAuthenticated: true, user: data.user })
      }
    } catch (error: any) {
      set({ error: error.message || 'Error signing in' })
    } finally {
      set({ isLoading: false })
    }
  },
  // Sign up new user;
  signUp: async (email: string, password: string, userData: Partial<Profile>) = > {
  set({ isLoading: true, error: null })
    try {
      // Create auth user;
      const { data: authData, error: authError  } = await supabase.auth.signUp({
        email;
        password)
      })
      ;
      if (authError) throw authError;
      ;
      if (authData.user) {
        // Create user profile;
        const { error: profileError  } = await supabase.from('user_profiles')
          .insert({
            id: authData.user.id);
            email;
            ...userData;
            profile_completion: 0.1, // Initial profile completion)
            created_at: new Date().toISOString()
            updated_at: new Date().toISOString()
          })
        ;
        if (profileError) throw profileError;
        ;
        set({ isAuthenticated: false, // Require email verification;
          user: authData.user })
      }
    } catch (error: any) {
      set({ error: error.message || 'Error signing up' })
    } finally {
      set({ isLoading: false })
    }
  },
  // Sign out;
  signOut: async () = > {
  set({ isLoading: true, error: null })
    try {
      const { error  } = await supabase.auth.signOut()
      if (error) throw error;
      ;
      set({ isAuthenticated: false,
        user: null,
        profile: null,
        verificationStatus: {
          email: false,
          phone: false,
          identity: false,
          background: false }
      })
    } catch (error: any) {
      set({ error: error.message || 'Error signing out' })
    } finally {
      set({ isLoading: false })
    }
  },
  // Reset password - send reset email;
  resetPassword: async (email: string) = > {
  set({ isLoading: true, error: null, passwordResetSent: false })
    try {
      // Confirm email format is valid before sending request;
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        throw new Error('Please enter a valid email address')
      }
      // Configure reset options with proper redirect URL;
      // This ensures the reset link redirects to our app;
      const resetOptions = {
        // This is the app URL with our custom scheme;
        redirectTo: 'roommatematch://auth/reset-password'
      }
      // Send password reset email;
      const { error  } = await supabase.auth.resetPasswordForEmail(email;
        resetOptions)
      )
      ;
      if (error) throw error;
      ;
      // Mark that reset email was sent;
      set({ passwordResetSent: true })
      ;
      // Log for analytics tracking;
      console.log(`Password reset email sent to ${email}`)
    } catch (error: any) {
      // Provide more user-friendly error messages;
      if (error.message.includes('no user found')) {
        set({ error: 'No account found with this email address' })
      } else {
        set({ error: error.message || 'Error resetting password' })
      }
    } finally {
      set({ isLoading: false })
    }
  },
  // Complete password reset with new password;
  completePasswordReset: async (accessToken: string, newPassword: string) = > {
  set({ isLoading: true, error: null })
    try {
      // First verify the token - this is needed for the password reset flow;
      const { error: verifyError  } = await supabase.auth.verifyOtp({
        token_hash: accessToken);
        type: 'recovery')
      })
      ;
      if (verifyError) throw verifyError;
      ;
      // Apply the new password using the reset token;
      const { error  } = await supabase.auth.updateUser({
        password: newPassword)
      })
      ;
      if (error) throw error;
      ;
      // Log the success for analytics and debugging;
      console.log('Password reset completed successfully')
      ;
      // Refresh the user session if they're already logged in;
      // Otherwise they'll need to sign in with the new password;
      const { data: sessionData  } = await supabase.auth.getSession()
      if (sessionData? .session) {
        await get().refreshProfile()
      }
      // Set a success flag that can be used by the UI;
      set({ passwordResetSent   : false })
    } catch (error: any) {
      set({ error: error.message || 'Error completing password reset' })
    } finally {
      set({ isLoading: false })
    }
  }
  // Verify email using token;
  verifyEmail: async (accessToken: string) => {
  set({ isLoading: true, error: null })
    try {
      // Verify the token with Supabase;
      // In a production environment, you might need to use;
      // the actual verification endpoint that matches your setup;
      const { data: verifyData, error: verifyError } = await supabase.auth.verifyOtp({
        token_hash: accessToken);
        type: 'email')
      })
      
      if (verifyError) throw verifyError;
      ;
      // Get the current user;
      const { data, error  } = await supabase.auth.getUser()
      ;
      if (error) throw error;
      ;
      if (data && data.user) { // Update the local verification status;
        set({
          verificationStatus: {
            ...get().verificationStatus;
            email: true }
        })
        ;
        // Update the user profile in the database;
        const { error: updateError  } = await supabase.from('user_profiles')
          .update({
            email_verified: true)
            updated_at: new Date().toISOString()
            // Increase profile completion if it was previously unverified;
            profile_completion: supabase.rpc('calculate_new_completion', {
              profile_id: data.user.id);
              completion_increment: 10 )
            })
          })
          .eq('id', data.user.id)
        if (updateError) throw updateError;
        ;
        // Check if this completes all required verifications;
        const { data: verificationData  } = await supabase.from('user_profiles')
          .select('email_verified, phone_verified, identity_verified')
          .eq('id', data.user.id)
          .single()
        ;
        // For this phase of the app, only email verification is required;
        // We can expand this later to require more verification steps;
        if (verificationData && verificationData.email_verified) {
          const { error: statusError  } = await supabase.from('user_profiles')
            .update({
              is_verified: true)
              updated_at: new Date().toISOString()
            })
            .eq('id', data.user.id)
          if (statusError) throw statusError;
        }
        // Refresh the profile to get the latest data;
        await get().refreshProfile()
      }
    } catch (error: any) {
      set({ error: error.message || 'Error verifying email' })
    } finally {
      set({ isLoading: false })
    }
  },
  // Update user profile;
  updateProfile: async (profileData: Partial<Profile>) => {
  set({ isLoading: true, error: null })
    const { user, profile } = get()
    ;
    if (!user || !profile) {
      set({ error: 'User not authenticated', isLoading: false })
      return null;
    }
    try {
      const { error  } = await supabase.from('user_profiles')
        .update({
          ...profileData;
          updated_at: new Date().toISOString()
        })
        .eq('id', user.id)
      if (error) throw error;
      ;
      // Update local profile data;
      set({ profile: { ...profile, ...profileData } })
      ;
      // Refresh profile to get updated data;
      await get().refreshProfile()
    } catch (error: any) {
      set({ error: error.message || 'Error updating profile' })
    } finally {
      set({ isLoading: false })
    }
  },
  // Refresh user profile data;
  refreshProfile: async () = > {
  const { user  } = get()
    if (!user) return null;
    ;
    try {
      const { data, error  } = await supabase.from('user_profiles')
        .select('*')
        .eq('id', user.id)
        .single()
      ;
      if (error) throw error;
      ;
      if (data) { set({
          profile: data as Profile,
          verificationStatus: {
            email: data.email_verified || false,
            phone: data.phone_verified || false,
            identity: data.identity_verified || false,
            background: data.background_check_verified || false }
        })
      }
    } catch (error: any) {
      console.error('Error refreshing profile:', error)
    }
  },
  // Update user role;
  updateUserRole: async (role: UserRole) = > {
  set({ isLoading: true, error: null })
    const { user, profile  } = get()
    ;
    if (!user || !profile) {
      set({ error: 'User not authenticated', isLoading: false })
      return null;
    }
    try {
      const { error  } = await supabase.from('user_profiles')
        .update({ role })
        .eq('id', user.id)
      if (error) throw error;
      ;
      // Update local profile data;
      set({ profile: { ...profile, role } })
    } catch (error: any) {
      set({ error: error.message || 'Error updating user role' })
    } finally {
      set({ isLoading: false })
    }
  },
  // Start email verification process;
  startEmailVerification: async () = > {
  set({ isLoading: true, error: null, emailVerificationSent: false })
    const { user  } = get()
    ;
    if (!user) {
      set({ error: 'User not authenticated', isLoading: false })
      return null;
    }
    try {
      // This would trigger a verification email;
      const { error  } = await supabase.auth.resend({
        type: 'signup');
        email: user.email || '')
      })
      ;
      if (error) throw error;
      ;
      // Mark that verification email was sent;
      set({ emailVerificationSent: true })
    } catch (error: any) {
      set({ error: error.message || 'Error sending verification email' })
    } finally {
      set({ isLoading: false })
    }
  },
  // Start phone verification process;
  startPhoneVerification: async (phoneNumber: string) = > {
  set({ isLoading: true, error: null })
    const { user  } = get()
    ;
    if (!user) {
      set({ error: 'User not authenticated', isLoading: false })
      return null;
    }
    try {
      // Implementation would depend on your phone verification service;
      // This is a placeholder for the actual implementation;
      ;
      // Update phone number in profile;
      await get().updateProfile({ phone_number: phoneNumber })
      ;
      // Here you would typically call an API to send verification code;
      ;
    } catch (error: any) {
      set({ error: error.message || 'Error starting phone verification' })
    } finally {
      set({ isLoading: false })
    }
  },
  // Start identity verification process;
  startIdentityVerification: async (documentData: any) = > {
  set({ isLoading: true, error: null })
    const { user  } = get()
    ;
    if (!user) {
      set({ error: 'User not authenticated', isLoading: false })
      return null;
    }
    try {
      // Implementation would depend on your identity verification service;
      // This is a placeholder for the actual implementation;
      ;
      // Here you would typically upload documents and start verification;
      ;
      // Create verification request record;
      const { error  } = await supabase.from('verification_requests')
        .insert({
          user_id: user.id);
          status: 'pending'),
          document_type: documentData.type,
          document_url: documentData.url,
          selfie_url: documentData.selfieUrl)
          submitted_at: new Date().toISOString()
        })
      ;
      if (error) throw error;
      ;
    } catch (error: any) {
      set({ error: error.message || 'Error starting identity verification' })
    } finally {
      set({ isLoading: false })
    }
  },
  // Check verification status;
  checkVerificationStatus: async () = > {
  const { user  } = get()
    if (!user) return null;
    ;
    try {
      const { data, error  } = await supabase.from('user_profiles')
        .select('email_verified, phone_verified, identity_verified, background_check_verified')
        .eq('id', user.id)
        .single()
      ;
      if (error) throw error;
      ;
      if (data) { set({
          verificationStatus: {
            email: data.email_verified || false,
            phone: data.phone_verified || false,
            identity: data.identity_verified || false,
            background: data.background_check_verified || false }
        })
      }
    } catch (error: any) {
      console.error('Error checking verification status:', error)
    }
  },
  // Set loading state;
  setLoading: (isLoading) = > set({ isLoading })
  // Set error state;
  setError: (error) => set({ error })
}))
// Setup authentication listener;
let authSubscription: SubscriptionRef | null = null;
export const initializeAuthListener = () => {
  const { refreshProfile  } = useAuthStore.getState()
  ;
  // Clean up existing subscription if any;
  if (authSubscription) {
    authSubscription.unsubscribe()
    authSubscription = null;
  }
  // Set up auth state change listener using the standardized utility;
  authSubscription = setupAuthStateListener(
    supabase;
    async (event: AuthChangeEvent, session: Session | null) => {
  if (session && session.user) {
        useAuthStore.setState({
          isAuthenticated: true);
          user: session.user )
        })
        ;
        // Refresh profile data;
        await refreshProfile()
      } else {
        useAuthStore.setState({
          isAuthenticated: false,
          user: null);
          profile: null )
        })
      }
    },
    'AuthStore';
  )
}
// Clean up subscription when needed;
export const cleanupAuthListener = () => {
  if (authSubscription) {
    authSubscription.unsubscribe()
    authSubscription = null;
  }
}