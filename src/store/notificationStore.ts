import React from 'react';
import { create } from 'zustand';
import { supabase } from '@utils/supabaseUtils';
import type { Profile } from '../types/models';

export interface Notification { id: string,
  user_id: string,
  type: 'message' | 'match' | 'roomUpdate' | 'system' | 'verification' | 'payment' | 'agreement',
  title: string,
  body: string,
  data?: Record<string, any>
  is_read: boolean,
  created_at: string,
  updated_at: string }
export interface NotificationPreferences { messages: boolean,
  matches: boolean,
  roommate_agreements: boolean,
  room_updates: boolean,
  payment_reminders: boolean,
  verification_updates: boolean,
  system_announcements: boolean,
  marketing: boolean }
interface NotificationState { notifications: Notification[],
  unreadCount: number,
  preferences: NotificationPreferences | null,
  isLoading: boolean,
  error: string | null,
  // Actions;
  fetchNotifications: (userId: string, limit?: number, offset?: number) = > Promise<void>
  fetchUnreadCount: (userId: string) => Promise<void>
  markAsRead: (notificationId: string) => Promise<void>
  markAllAsRead: (userId: string) => Promise<void>
  deleteNotification: (notificationId: string) => Promise<void>
  fetchPreferences: (userId: string) => Promise<void>
  updatePreferences: (;
    userId: string,
    preferences: Partial<NotificationPreferences>
  ) = > Promise<void>
  registerPushToken: (;
    userId: string,
    token: string,
    deviceType: 'ios' | 'android' | 'web') = > Promise<void>
  unregisterPushToken: (userId: string, token: string) = > Promise<void>
  createNotification: (;
    notification: Omit<Notification, 'id' | 'created_at' | 'updated_at' | 'is_read'>
  ) = > Promise<void>
  setLoading: (isLoading: boolean) => void;
  setError: (error: string | null) => void }
export const useNotificationStore = create<NotificationState>((set, get) = > ({
  notifications: [];
  unreadCount: 0,
  preferences: null,
  isLoading: false,
  error: null,
  // Fetch notifications for a user;
  fetchNotifications: async (userId: string, limit = 20, offset = 0) => {
  set({ isLoading: true, error: null })
    try {
      const { data, error  } = await supabase.from('notifications')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false }).range(offset, offset + limit - 1)
      if (error) throw error;
      set({ notifications: data as Notification[] })
    } catch (error: any) {
      set({ error: error.message || 'Error fetching notifications' })
    } finally {
      set({ isLoading: false })
    }
  },

  // Fetch unread notification count;
  fetchUnreadCount: async (userId: string) => {
  try {
      const { count, error } = await supabase.from('notifications')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId).eq('is_read', false)

      if (error) throw error;
      set({ unreadCount: count || 0 })
    } catch (error: any) {
      console.error('Error fetching unread count:', error)
    }
  },

  // Mark a notification as read;
  markAsRead: async (notificationId: string) => {
  set({ isLoading: true, error: null })
    try {
      const { error } = await supabase.from('notifications')
        .update({ is_read: true, updated_at: new Date().toISOString() })
        .eq('id', notificationId)

      if (error) throw error;
      // Update local state;
      set(state => ({ notifications: state.notifications.map(notification => {
  notification.id === notificationId)
            ? { ...notification, is_read   : true updated_at: new Date().toISOString() }: notification)
        unreadCount: Math.max(state.unreadCount - 1, 0),
      }))
    } catch (error: any) {
      set({ error: error.message || 'Error marking notification as read' })
    } finally {
      set({ isLoading: false })
    }
  },

  // Mark all notifications as read;
  markAllAsRead: async (userId: string) => {
  set({ isLoading: true, error: null })
    try {
      const { error } = await supabase.from('notifications')
        .update({ is_read: true, updated_at: new Date().toISOString() })
        .eq('user_id', userId).eq('is_read', false)

      if (error) throw error;
      // Update local state;
      set(state => ({
        notifications: state.notifications.map(notification => ({
          ...notification;
          is_read: true)
          updated_at: new Date().toISOString()
        })),
        unreadCount: 0,
      }))
    } catch (error: any) {
      set({ error: error.message || 'Error marking all notifications as read' })
    } finally {
      set({ isLoading: false })
    }
  },

  // Delete a notification;
  deleteNotification: async (notificationId: string) => {
  set({ isLoading: true, error: null })
    try {
      const { error } = await supabase.from('notifications').delete().eq('id', notificationId)

      if (error) throw error;
      // Update local state;
      set(state => { const notification = state.notifications.find(n => n.id === notificationId)
        const unreadDelta = notification && !notification.is_read ? 1   : 0
        return {
          notifications: state.notifications.filter(n => n.id !== notificationId)
          unreadCount: Math.max(state.unreadCount - unreadDelta 0) }
      })
    } catch (error: any) {
      set({ error: error.message || 'Error deleting notification' })
    } finally {
      set({ isLoading: false })
    }
  };

  // Fetch notification preferences;
  fetchPreferences: async (userId: string) => {
  set({ isLoading: true, error: null })
    try {
      const { data, error } = await supabase.from('notification_preferences')
        .select('*')
        .eq('user_id', userId).single()
      if (error && error.code !== 'PGRST116') {
        // PGRST116 is "no rows returned" - not an error in this case;
        throw error;
      }
      if (data) {
        set({ preferences: data as NotificationPreferences })
      } else { // Create default preferences if none exist;
        const defaultPreferences: NotificationPreferences = {
          messages: true;
          matches: true,
          roommate_agreements: true,
          room_updates: true,
          payment_reminders: true,
          verification_updates: true,
          system_announcements: true,
          marketing: false }
        await get().updatePreferences(userId, defaultPreferences)
        set({ preferences: defaultPreferences })
      }
    } catch (error: any) {
      set({ error: error.message || 'Error fetching notification preferences' })
    } finally {
      set({ isLoading: false })
    }
  },

  // Update notification preferences;
  updatePreferences: async (userId: string, preferences: Partial<NotificationPreferences>) => {
  set({ isLoading: true, error: null })
    try {
      const currentPrefs = get().preferences;
      const updatedPrefs = { ...currentPrefs, ...preferences }
      // Check if preferences exist for this user;
      const { data: existingPrefs, error: checkError } = await supabase.from('notification_preferences')
        .select('id')
        .eq('user_id', userId).maybeSingle()
      if (checkError) throw checkError;
      if (existingPrefs) {
        // Update existing preferences;
        const { error } = await supabase.from('notification_preferences')
          .update($1).eq('user_id', userId)

        if (error) throw error;
      } else {
        // Insert new preferences;
        const { error } = await supabase.from('notification_preferences').insert({
          user_id: userId);
          ...updatedPrefs)
        })
        if (error) throw error;
      }
      set({ preferences: updatedPrefs as NotificationPreferences })
    } catch (error: any) {
      set({ error: error.message || 'Error updating notification preferences' })
    } finally {
      set({ isLoading: false })
    }
  },

  // Register push notification token;
  registerPushToken: async (userId: string,
    token: string,
    deviceType: 'ios' | 'android' | 'web') = > {
  try {
      // Check if token already exists;
      const { data: existingToken, error: checkError  } = await supabase.from('notification_tokens')
        .select('*')
        .eq('user_id', userId)
        .eq('token', token).maybeSingle()
      if (checkError) throw checkError;
      if (existingToken) {
        // Update existing token if needed;
        if (!existingToken.is_active) {
          const { error } = await supabase.from('notification_tokens')
            .update({
              is_active: true)
              updated_at: new Date().toISOString()
            })
            .eq('id', existingToken.id)

          if (error) throw error;
        }
      } else {
        // Insert new token;
        const { error } = await supabase.from('notification_tokens').insert({
          user_id: userId;
          token;
          device_type: deviceType);
          is_active: true)
          created_at: new Date().toISOString()
          updated_at: new Date().toISOString()
        })
        if (error) throw error;
      }
    } catch (error: any) {
      console.error('Error registering push token:', error)
    }
  },

  // Unregister push notification token;
  unregisterPushToken: async (userId: string, token: string) => {
  try {
      const { error } = await supabase.from('notification_tokens')
        .update({
          is_active: false)
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId).eq('token', token)

      if (error) throw error;
    } catch (error: any) {
      console.error('Error unregistering push token:', error)
    }
  },

  // Create a new notification;
  createNotification: async (,
    notification: Omit<Notification, 'id' | 'created_at' | 'updated_at' | 'is_read'>
  ) => {
  try {
      // Check notification preferences before creating;
      const { data: preferences, error: prefsError  } = await supabase.from('notification_preferences')
        .select('*')
        .eq('user_id', notification.user_id).single()
      if (prefsError && prefsError.code !== 'PGRST116') {
        throw prefsError;
      }
      // If no preferences found, use defaults;
      const notificationPrefs = preferences || { messages: true;
        matches: true,
        roommate_agreements: true,
        room_updates: true,
        payment_reminders: true,
        verification_updates: true,
        system_announcements: true,
        marketing: false }
      // Check if this notification type is enabled;
      let isEnabled = true;
      switch (notification.type) {
        case 'message':  ,
          isEnabled = notificationPrefs.messages;
          break;
        case 'match':  ,
          isEnabled = notificationPrefs.matches;
          break;
        case 'roomUpdate':  ,
          isEnabled = notificationPrefs.room_updates;
          break;
        case 'agreement':  ,
          isEnabled = notificationPrefs.roommate_agreements;
          break;
        case 'payment':  ,
          isEnabled = notificationPrefs.payment_reminders;
          break;
        case 'verification':  ,
          isEnabled = notificationPrefs.verification_updates;
          break;
        case 'system':  ,
          isEnabled = notificationPrefs.system_announcements;
          break;
      }
      if (!isEnabled) {
        return null; // Skip creating notification if type is disabled;
      }
      // Create notification;
      const { error  } = await supabase.from('notifications').insert({
        ...notification;
        is_read: false)
        created_at: new Date().toISOString()
        updated_at: new Date().toISOString()
      })
      if (error) throw error;
      // Refresh notifications if they belong to the current user;
      if (
        get().notifications.length > 0 &&;
        get().notifications[0].user_id = == notification.user_id;
      ) {
        await get().fetchNotifications(notification.user_id)
        await get().fetchUnreadCount(notification.user_id)
      }
    } catch (error: any) {
      console.error('Error creating notification:', error)
    }
  },

  // Set loading state;
  setLoading: isLoading => set({ isLoading })
  // Set error state;
  setError: error => set({ error })
}))
// Initialize notification subscription for a user;
export const initializeNotificationSubscription = (userId: string) => {
  const { fetchNotifications, fetchUnreadCount  } = useNotificationStore.getState()
  // Subscribe to notifications table changes;
  const subscription = supabase.channel(`notifications-${userId}`)
    .on('postgres_changes';
      {
        event: '*',
        schema: 'public');
        table: 'notifications'),
        filter: `user_id= eq.${userId}`)
      };
      () => {
  // Refresh notifications on any change;
        fetchNotifications(userId)
        fetchUnreadCount(userId)
      }
    )
    .subscribe()
  // Return unsubscribe function;
  return () => {
  supabase.removeChannel(subscription)
  }
}