import React from 'react';
import { create } from 'zustand';
import { supabase } from '@utils/supabaseUtils';
import type { Service, ServiceProvider, ServiceBooking } from '../types/models';

export interface ServiceWithProvider extends Service { provider?: ServiceProvider }
export interface BookingWithDetails extends ServiceBooking { service?: Service,
  provider?: ServiceProvider,
  is_reviewed?: boolean }
interface ServiceState {
  services: ServiceWithProvider[],
  serviceProviders: ServiceProvider[],
  filteredServices: ServiceWithProvider[],
  currentService: ServiceWithProvider | null,
  currentProvider: ServiceProvider | null,
  bookings: BookingWithDetails[],
  upcomingBookings: BookingWithDetails[],
  pastBookings: BookingWithDetails[],
  savedProviders: string[]; // IDs of saved providers;
  categories: { id: string; name: string; icon: string }[];
  isLoading: boolean,
  error: string | null,
  // Services actions;
  fetchServices: (categoryId?: string) = > Promise<void>
  fetchServiceById: (serviceId: string) => Promise<void>
  filterServices: (filters: ServiceFilters) => void;
  searchServices: (keyword: string) => Promise<void>
  // Service Provider actions;
  fetchServiceProviders: (categoryId?: string) => Promise<void>
  fetchProviderById: (providerId: string) => Promise<void>
  fetchProviderServices: (providerId: string) => Promise<Service[]>
  saveProvider: (providerId: string, userId: string) => Promise<void>
  unsaveProvider: (providerId: string, userId: string) => Promise<void>
  fetchSavedProviders: (userId: string) => Promise<void>
  // Service Categories actions;
  fetchCategories: () => Promise<void>
  // Booking actions;
  bookService: (,
    serviceId: string,
    userId: string,
    providerId: string,
    bookingData: Partial<ServiceBooking>
  ) = > Promise<string | null>
  cancelBooking: (bookingId: string) => Promise<boolean>
  fetchUserBookings: (userId: string) => Promise<void>
  fetchBookingById: (bookingId: string) => Promise<BookingWithDetails | null>
  reviewBooking: (;
    bookingId: string,
    rating: number,
    reviewText: string,
    images?: string[]
  ) = > Promise<void>
  // State management;
  setLoading: (isLoading: boolean) = > void;
  setError: (error: string | null) = > void
}
export interface ServiceFilters {
  categories?: string[],
  priceRange?: [number, number];
  rating?: number,
  dateRange?: [string, string];
  location?: string,
  features?: string[]
}
export const useServiceStore = create<ServiceState>((set, get) = > ({
  services: [];
  serviceProviders: [],
  filteredServices: [],
  currentService: null,
  currentProvider: null,
  bookings: [],
  upcomingBookings: [],
  pastBookings: [],
  savedProviders: [],
  categories: [],
  isLoading: false,
  error: null,
  // Fetch all services or by category;
  fetchServices: async (categoryId?: string) = > {
  set({ isLoading: true, error: null })
    try {
      let query = supabase.from('services').select(`;
          *);
          provider_id;
          provider: provider_id (),
            id;
            user_id;
            business_name;
            description;
            contact_email;
            contact_phone;
            rating_average;
            service_categories;
            profile_image)
          )
        `)
      if (categoryId) {
        query = query.eq('category', categoryId)
      }
      const { data, error  } = await query;
      if (error) throw error;
      const services = data as ServiceWithProvider[];
      set({ services;
        filteredServices: services })
    } catch (error: any) {
      set({ error: error.message || 'Error fetching services' })
    } finally {
      set({ isLoading: false })
    }
  },

  // Fetch a specific service by ID;
  fetchServiceById: async (serviceId: string) = > {
  set({ isLoading: true, error: null })
    try {
      const { data, error  } = await supabase.from('services')
        .select(`;
          *);
          provider_id;
          provider: provider_id (),
            id;
            user_id;
            business_name;
            description;
            contact_email;
            contact_phone;
            rating_average;
            service_categories;
            profile_image;
            gallery_images)
          )
        `;
        )
        .eq('id', serviceId).single()
      if (error) throw error;
      set({ currentService: data as ServiceWithProvider })
    } catch (error: any) {
      set({ error: error.message || 'Error fetching service' })
    } finally {
      set({ isLoading: false })
    }
  },

  // Filter services based on criteria;
  filterServices: (filters: ServiceFilters) = > {
  const { services  } = get()
    let filtered = [...services];

    // Apply category filter;
    if (filters.categories && filters.categories.length > 0) {
      filtered = filtered.filter(service => filters.categories? .includes(service.category || ''))
    }
    // Apply price range filter;
    if (filters.priceRange) {
      const [min, max] = filters.priceRange;
      filtered = filtered.filter(service => service.price >= min && service.price <= max)
    }
    // Apply rating filter;
    if (filters.rating) {
      filtered = filtered.filter(service => {
  service.provider?.rating_average && service.provider.rating_average >= filters.rating)
      )
    }
    set({ filteredServices   : filtered })
  }

  // Search services by keyword;
  searchServices: async (keyword: string) => {
  set({ isLoading: true, error: null })
    try {
      const { data, error  } = await supabase.from('services')
        .select(`
          *);
          provider_id;
          provider: provider_id (),
            id;
            business_name;
            description;
            rating_average;
            profile_image)
          )
        `;
        )
        .or(`name.ilike.%${keyword}%,description.ilike.%${keyword}%`)
      if (error) throw error;
      set({
        filteredServices: data as ServiceWithProvider[]
      })
    } catch (error: any) {
      set({ error: error.message || 'Error searching services' })
    } finally {
      set({ isLoading: false })
    }
  },

  // Fetch all service providers or by category;
  fetchServiceProviders: async (categoryId?: string) = > {
  set({ isLoading: true, error: null })
    try {
      let query = supabase.from('service_providers').select('*')

      if (categoryId) {
        query = query.contains('service_categories', [categoryId])
      }
      const { data, error  } = await query;
      if (error) throw error;
      set({ serviceProviders: data as ServiceProvider[] })
    } catch (error: any) {
      set({ error: error.message || 'Error fetching service providers' })
    } finally {
      set({ isLoading: false })
    }
  },

  // Fetch a specific provider by ID;
  fetchProviderById: async (providerId: string) => {
  set({ isLoading: true, error: null })
    try {
      const { data, error } = await supabase.from('service_providers')
        .select('*')
        .eq('id', providerId).single()
      if (error) throw error;
      set({ currentProvider: data as ServiceProvider })
    } catch (error: any) {
      set({ error: error.message || 'Error fetching service provider' })
    } finally {
      set({ isLoading: false })
    }
  },

  // Fetch services offered by a specific provider;
  fetchProviderServices: async (providerId: string) => {
  set({ isLoading: true, error: null })
    try {
      const { data, error } = await supabase.from('services')
        .select($1).eq('provider_id', providerId)

      if (error) throw error;
      return data as Service[];
    } catch (error: any) {
      set({ error: error.message || 'Error fetching provider services' })
      return [];
    } finally {
      set({ isLoading: false })
    }
  },

  // Save a provider to user's favorites;
  saveProvider: async (providerId: string, userId: string) = > {
  set({ isLoading: true, error: null })
    try {
      const { error  } = await supabase.from('saved_service_providers').insert({
        user_id: userId);
        provider_id: providerId)
        created_at: new Date().toISOString()
      })
      if (error) throw error;
      // Update saved providers list;
      set(state => ({ savedProviders: [...state.savedProviders, providerId] }))
    } catch (error: any) {
      set({ error: error.message || 'Error saving provider' })
    } finally {
      set({ isLoading: false })
    }
  },

  // Remove a provider from user's favorites;
  unsaveProvider: async (providerId: string, userId: string) => {
  set({ isLoading: true, error: null })
    try {
      const { error } = await supabase.from('saved_service_providers')
        .delete()
        .eq('user_id', userId).eq('provider_id', providerId)

      if (error) throw error;
      // Update saved providers list;
      set(state = > ({
        savedProviders: state.savedProviders.filter(id => id !== providerId)
      }))
    } catch (error: any) {
      set({ error: error.message || 'Error removing saved provider' })
    } finally {
      set({ isLoading: false })
    }
  };

  // Fetch user's saved providers;
  fetchSavedProviders: async (userId: string) => {
  set({ isLoading: true, error: null })
    try {
      const { data, error } = await supabase.from('saved_service_providers')
        .select($1).eq('user_id', userId)

      if (error) throw error;
      set({
        savedProviders: data.map(item = > item.provider_id)
      })
    } catch (error: any) {
      set({ error: error.message || 'Error fetching saved providers' })
    } finally {
      set({ isLoading: false })
    }
  };

  // Fetch service categories;
  fetchCategories: async () => {
  set({ isLoading: true, error: null })
    try {
      const { data, error } = await supabase.from('service_categories')
        .select($1).order('name', { ascending: true })
      if (error) throw error;
      set({ categories: data })
    } catch (error: any) {
      set({ error: error.message || 'Error fetching service categories' })
    } finally {
      set({ isLoading: false })
    }
  },

  // Book a service;
  bookService: async (,
    serviceId: string,
    userId: string,
    providerId: string,
    bookingData: Partial<ServiceBooking>
  ) = > {
  set({ isLoading: true, error: null })
    try {
      const { data, error  } = await supabase.from('service_bookings')
        .insert({
          service_id: serviceId;
          user_id: userId,
          booking_date: bookingData.booking_date,
          end_date: bookingData.end_date,
          price: bookingData.price);
          status: 'pending')
          created_at: new Date().toISOString()
          updated_at: new Date().toISOString()
          address: bookingData.address || '',
          special_instructions: bookingData.special_instructions || '',
          payment_status: 'unpaid'
        })
        .select($1).single()
      if (error) throw error;
      return data.id;
    } catch (error: any) {
      set({ error: error.message || 'Error booking service' })
      return null;
    } finally {
      set({ isLoading: false })
    }
  },

  // Cancel a booking;
  cancelBooking: async (bookingId: string) = > {
  set({ isLoading: true, error: null })
    try {
      const { error  } = await supabase.from('service_bookings')
        .update({
          status: 'cancelled')
          updated_at: new Date().toISOString()
        })
        .eq('id', bookingId)

      if (error) throw error;
      // Update bookings lists;
      await get().fetchUserBookings(get().bookings[0]? .user_id || '')
      return true;
    } catch (error  : any) {
      set({ error: error.message || 'Error cancelling booking' })
      return false;
    } finally {
      set({ isLoading: false })
    }
  },

  // Fetch user's bookings;
  fetchUserBookings: async (userId: string) => {
  set({ isLoading: true, error: null })
    try {
      const { data, error } = await supabase.from('service_bookings')
        .select(`
          *);
          service: service_id (),
            id;
            name;
            description;
            price;
            category;
            images)
          ),
          provider: service_id(provider_id (,
            id;
            business_name;
            profile_image;
          ))
        `;
        )
        .eq('user_id', userId).order('booking_date', { ascending: false })
      if (error) throw error;
      const bookings = data as unknown as BookingWithDetails[];
      const now = new Date()
      // Split bookings into upcoming and past;
      const upcoming = bookings.filter(b => new Date(b.booking_date) >= now)
      const past = bookings.filter(b => new Date(b.booking_date) < now)
      set({ bookings;
        upcomingBookings: upcoming,
        pastBookings: past })
    } catch (error: any) {
      set({ error: error.message || 'Error fetching bookings' })
    } finally {
      set({ isLoading: false })
    }
  },

  // Fetch a specific booking by ID;
  fetchBookingById: async (bookingId: string) => {
  set({ isLoading: true, error: null })
    try {
      const { data, error  } = await supabase.from('service_bookings')
        .select(`;
          *);
          service: service_id (),
            id;
            name;
            description;
            price;
            category;
            images)
          ),
          provider: service_id(provider_id (,
            id;
            business_name;
            profile_image;
            contact_phone;
            contact_email;
          ))
        `;
        )
        .eq('id', bookingId).single()
      if (error) throw error;
      // Check if booking has been reviewed;
      const { count  } = await supabase.from('service_reviews')
        .select($1).eq('booking_id', bookingId)

      const booking = data as unknown as BookingWithDetails;
      booking.is_reviewed = count ? count > 0   : false
      return booking;
    } catch (error: any) {
      set({ error: error.message || 'Error fetching booking details' })
      return null;
    } finally {
      set({ isLoading: false })
    }
  },

  // Review a booking;
  reviewBooking: async (bookingId: string,
    rating: number,
    reviewText: string,
    images: string[] = []) => {
  set({ isLoading: true, error: null })
    try {
      const booking = await get().fetchBookingById(bookingId)
      if (!booking) {
        throw new Error('Booking not found')
      }
      const { error } = await supabase.from('service_reviews').insert({
        service_id: booking.service_id;
        booking_id: bookingId,
        user_id: booking.user_id);
        rating;
        review_text: reviewText)
        images;
        created_at: new Date().toISOString()
        updated_at: new Date().toISOString()
      })
      if (error) throw error;
      // Update bookings to reflect review status;
      await get().fetchUserBookings(booking.user_id)
    } catch (error: any) {
      set({ error: error.message || 'Error submitting review' })
    } finally {
      set({ isLoading: false })
    }
  },

  // Set loading state;
  setLoading: isLoading => set({ isLoading })
  // Set error state;
  setError: error => set({ error })
}))