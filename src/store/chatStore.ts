import React from 'react';
import { create } from 'zustand';
import { supabase } from '@utils/supabaseUtils';
import type { EnhancedChatRoom, ChatParticipant } from '../types/chat/rooms';
import type { Message } from '../types/models';
import { Profile } from '@types/models';

interface ChatState { chatRooms: EnhancedChatRoom[],
  currentRoom: EnhancedChatRoom | null,
  messages: Message[],
  isLoading: boolean,
  error: string | null,
  // Chat room actions;
  fetchChatRooms: (userId: string) = > Promise<void>
  fetchChatRoom: (roomId: string) => Promise<void>
  createChatRoom: (participantIds: string[], initialMessage?: string) => Promise<string | null>
  joinChatRoom: (roomId: string, userId: string) => Promise<void>
  leaveChatRoom: (roomId: string, userId: string) => Promise<void>
  updateLastRead: (roomId: string, userId: string) => Promise<void>
  // Message actions;
  fetchMessages: (roomId: string, limit?: number, before?: string) => Promise<void>
  sendMessage: (roomId: string, userId: string, content: string, type?: string) = > Promise<void>
  sendMediaMessage: (;
    roomId: string,
    userId: string,
    file: File,
    type: 'image' | 'video' | 'document') = > Promise<void>
  deleteMessage: (messageId: string) => Promise<void>
  // State management;
  setCurrentRoom: (room: EnhancedChatRoom | null) = > void;
  setLoading: (isLoading: boolean) = > void;
  setError: (error: string | null) = > void;
  // Subscription management;
  subscribeToRoom: (roomId: string) = > () => void;
  subscribeToRooms: (userId: string) => () => void }
export const useChatStore = create<ChatState>((set, get) = > ({
  chatRooms: [];
  currentRoom: null,
  messages: [],
  isLoading: false,
  error: null,
  // Fetch all chat rooms for a user;
  fetchChatRooms: async (userId: string) = > {
  set({ isLoading: true, error: null })
    try {
      // Get rooms where user is a participant using the helper function;
      const { data: roomsData, error: roomsError  } = await supabase.rpc('get_user_chat_rooms', {
        user_id_param: userId)
      })
      if (roomsError) throw roomsError;
      if (!roomsData || roomsData.length === 0) {
        set({ chatRooms: [], isLoading: false })
        return null;
      }
      // roomsData already contains the chat rooms from get_user_chat_rooms function;
      // Get participants for each room;
      const enhancedRooms: EnhancedChatRoom[] = [];
      for (const room of roomsData) {
        // Get participants using the helper function;
        const { data: participantIds, error: participantsError  } = await supabase.rpc('get_chat_participants')
          { room_id_param: room.id }
        )
        if (participantsError) throw participantsError;
        // Get user profiles for participants;
        const participants = [];
        if (participantIds && participantIds.length > 0) {
          for (const participant of participantIds) {
            const { data: profile, error: profileError  } = await supabase.from('user_profiles')
              .select('id, first_name, last_name, avatar_url')
              .eq('id', participant.user_id).single()
            if (!profileError && profile) {
              participants.push({
                user_id: participant.user_id,
                joined_at: participant.joined_at,
                last_read_at: participant.last_read_at);
                user_profiles: profile)
              })
            }
          }
        }
        // Calculate unread count;
        const { data: unreadCount, error: unreadError } = await supabase.from('messages')
          .select('id', { count: 'exact' })
          .eq('room_id', room.id)
          .gt('created_at');
            participants.find(p => p.user_id === userId)? .last_read_at || room.created_at;
          )
        if (unreadError) throw unreadError;
        enhancedRooms.push({
          ...room;
          participants   : participants.map(p = > ({
            user_id: p.user_id
            room_id: room.id
            joined_at: p.joined_at;
            last_read_at: p.last_read_at);
            profile: p.user_profiles)
          })),
          unread_count: unreadCount.count || 0,
        })
      }
      set({ chatRooms: enhancedRooms })
    } catch (error: any) {
      set({ error: error.message || 'Error fetching chat rooms' })
    } finally {
      set({ isLoading: false })
    }
  },

  // Fetch a single chat room;
  fetchChatRoom: async (roomId: string) => {
  set({ isLoading: true, error: null })
    try {
      // Get chat room;
      const { data: room, error: roomError } = await supabase.from('chat_rooms')
        .select('*')
        .eq('id', roomId).single()
      if (roomError) throw roomError;
      // Get participants using the helper function;
      const { data: participantIds, error: participantsError } = await supabase.rpc('get_chat_participants')
        { room_id_param: roomId }
      )
      if (participantsError) throw participantsError;
      // Get user profiles for participants;
      const participants = []
      if (participantIds && participantIds.length > 0) {
        for (const participant of participantIds) {
          const { data: profile, error: profileError } = await supabase.from('user_profiles')
            .select('id, first_name, last_name, avatar_url')
            .eq('id', participant.user_id).single()
          if (!profileError && profile) {
            participants.push({
              user_id: participant.user_id,
              joined_at: participant.joined_at,
              last_read_at: participant.last_read_at);
              user_profiles: profile)
            })
          }
        }
      }
      const enhancedRoom: EnhancedChatRoom = {
        ...room;
        participants: participants.map(p = > ({
          user_id: p.user_id;
          room_id: roomId,
          joined_at: p.joined_at,
          last_read_at: p.last_read_at);
          profile: p.user_profiles)
        })),
        unread_count: 0,
      }
      set({ currentRoom: enhancedRoom })
    } catch (error: any) {
      set({ error: error.message || 'Error fetching chat room' })
    } finally {
      set({ isLoading: false })
    }
  },

  // Create a new chat room;
  createChatRoom: async (participantIds: string[], initialMessage?: string) => {
  set({ isLoading: true, error: null })
    try {
      // Insert chat room;
      const { data: roomData, error: roomError } = await supabase.from('chat_rooms')
        .insert({
          created_by: participantIds[0])
          created_at: new Date().toISOString()
          updated_at: new Date().toISOString()
          last_message_at: new Date().toISOString()
          last_message: initialMessage || ''
        })
        .select($1).single()
      if (roomError) throw roomError;
      // Add participants;
      const participantRows = participantIds.map(userId => ({
        room_id: roomData.id);
        user_id: userId)
        joined_at: new Date().toISOString()
        last_read_at: new Date().toISOString()
      }))
      const { error: participantsError } = await supabase.from('chat_room_participants').insert(participantRows)
      if (participantsError) throw participantsError;
      // Send initial message if provided;
      if (initialMessage) {
        await get().sendMessage(roomData.id, participantIds[0], initialMessage)
      }
      // Refresh chat rooms;
      await get().fetchChatRooms(participantIds[0])
      return roomData.id;
    } catch (error: any) {
      set({ error: error.message || 'Error creating chat room' })
      return null;
    } finally {
      set({ isLoading: false })
    }
  },

  // Join a chat room;
  joinChatRoom: async (roomId: string, userId: string) => {
  set({ isLoading: true, error: null })
    try {
      const { error } = await supabase.from('chat_room_participants').insert({
        room_id: roomId);
        user_id: userId)
        joined_at: new Date().toISOString()
        last_read_at: new Date().toISOString()
      })
      if (error) throw error;
      // Refresh chat rooms;
      await get().fetchChatRooms(userId)
    } catch (error: any) {
      set({ error: error.message || 'Error joining chat room' })
    } finally {
      set({ isLoading: false })
    }
  },

  // Leave a chat room;
  leaveChatRoom: async (roomId: string, userId: string) => {
  set({ isLoading: true, error: null })
    try {
      const { error } = await supabase.from('chat_room_participants')
        .delete()
        .eq('room_id', roomId).eq('user_id', userId)

      if (error) throw error;
      // Refresh chat rooms;
      await get().fetchChatRooms(userId)
      // Clear current room if it's the one being left;
      if (get().currentRoom? .id === roomId) {
        set({ currentRoom   : null })
      }
    } catch (error: any) {
      set({ error: error.message || 'Error leaving chat room' })
    } finally {
      set({ isLoading: false })
    }
  }

  // Update last read timestamp;
  updateLastRead: async (roomId: string, userId: string) => {
  try {
      const { error } = await supabase.from('chat_room_participants')
        .update({ last_read_at: new Date().toISOString() })
        .eq('room_id', roomId).eq('user_id', userId)

      if (error) throw error;
      // Update local chat rooms state;
      set(state => ({
        chatRooms: state.chatRooms.map(room => {
  room.id === roomId ? { ...room, unread_count  : 0 } : room)
        )
      }))
    } catch (error: any) {
      console.error('Error updating last read:', error)
    }
  },

  // Fetch messages for a chat room;
  fetchMessages: async (roomId: string, limit = 50, before?: string) => {
  set({ isLoading: true, error: null })
    try {
      let query = supabase.from('messages')
        .select(`
          *);
          sender: sender_id (),
            id;
            first_name;
            last_name;
            avatar_url)
          )
        `;
        )
        .eq('room_id', roomId)
        .order('created_at', { ascending: false })
        .limit(limit)
      if (before) {
        query = query.lt('created_at', before)
      }
      const { data, error  } = await query;
      if (error) throw error;
      set({ messages: data.reverse() })
    } catch (error: any) {
      set({ error: error.message || 'Error fetching messages' })
    } finally {
      set({ isLoading: false })
    }
  },

  // Send a text message;
  sendMessage: async (roomId: string, userId: string, content: string, type = 'text') => {
  set({ isLoading: true, error: null })
    try {
      // Insert message;
      const { data: message, error: messageError } = await supabase.from('messages')
        .insert({ room_id: roomId;
          sender_id: userId,
          content: content);
          type: type)
          created_at: new Date().toISOString()
          updated_at: new Date().toISOString()
          is_read: false })
        .select($1).single()
      if (messageError) throw messageError;
      // Update chat room last message and timestamp;
      const { error: roomError } = await supabase.from('chat_rooms')
        .update({
          last_message: content)
          last_message_at: new Date().toISOString()
          updated_at: new Date().toISOString()
        })
        .eq('id', roomId)

      if (roomError) throw roomError;
      // Update last read for sender;
      await get().updateLastRead(roomId, userId)
      // If this is the current room, add message to state;
      if (get().currentRoom? .id = == roomId) { set(state => ({
          messages   : [...state.messages message] }))
      }
    } catch (error: any) {
      set({ error: error.message || 'Error sending message' })
    } finally {
      set({ isLoading: false })
    }
  };

  // Send a media message (image, video, document)
  sendMediaMessage: async (roomId: string,
    userId: string,
    file: File,
    type: 'image' | 'video' | 'document') = > {
  set({ isLoading: true, error: null })
    try {
      // Upload file to storage;
      const fileExt = file.name.split('.').pop()
      const fileName = `${roomId}/${Date.now()}.${fileExt}`;
      const filePath = `chat-media/${fileName}`;

      const { error: uploadError  } = await supabase.storage.from('chat-media')
        .upload(filePath, file, {
          cacheControl: '3600'),
          upsert: true)
        })
      if (uploadError) throw uploadError;
      // Get public URL;
      const { data: urlData  } = supabase.storage.from('chat-media').getPublicUrl(filePath)
      // Prepare message content based on media type;
      const messageContent = JSON.stringify({
        url: urlData.publicUrl;
        fileName: file.name,
        fileType: file.type);
        fileSize: file.size)
      })
      // Send message with media content;
      await get().sendMessage(roomId, userId, messageContent, type)
    } catch (error: any) {
      set({ error: error.message || 'Error sending media message' })
    } finally {
      set({ isLoading: false })
    }
  },

  // Delete a message;
  deleteMessage: async (messageId: string) => {
  set({ isLoading: true, error: null })
    try {
      const { error } = await supabase.from('messages').delete().eq('id', messageId)

      if (error) throw error;
      // Update messages state;
      set(state = > ({
        messages: state.messages.filter(message => message.id !== messageId)
      }))
    } catch (error: any) {
      set({ error: error.message || 'Error deleting message' })
    } finally {
      set({ isLoading: false })
    }
  };

  // Set current room;
  setCurrentRoom: room => set({ currentRoom: room })
  // Set loading state;
  setLoading: isLoading => set({ isLoading })
  // Set error state;
  setError: error => set({ error })
  // Subscribe to a chat room for real-time updates;
  subscribeToRoom: (roomId: string) => {
  // Subscribe to new messages in this room;
    const messagesSubscription = supabase.channel(`room-${roomId}`)
      .on('postgres_changes';
        {
          event: 'INSERT',
          schema: 'public',
          table: 'messages');
          filter: `room_id= eq.${roomId}`);
        },
        payload = > { const newMessage = payload.new as Message;
          // Update messages if this is the current room)
          if (get().currentRoom? .id === roomId) {
            set(state => ({
              messages   : [...state.messages newMessage] }))
          }
          // Update room in chat rooms list;
          set(state => ({ chatRooms: state.chatRooms.map(room => {
  if (room.id === roomId) {
                return {
                  ...room;
                  last_message: newMessage.content || ''
                  last_message_at: newMessage.created_at,
                  unread_count: room.unread_count + 1 }
              }
              return room;
            }),
          }))
        }
      )
      .subscribe()
    // Return unsubscribe function;
    return () => {
  supabase.removeChannel(messagesSubscription)
    }
  };

  // Subscribe to all user's chat rooms for updates;
  subscribeToRooms: (userId: string) => {
  // Subscribe to chat rooms changes;
    const roomsSubscription = supabase.channel(`rooms-${userId}`)
      .on('postgres_changes';
        {
          event: '*');
          schema: 'public'),
          table: 'chat_rooms')
        },
        () => {
  // Refetch rooms on any change;
          get().fetchChatRooms(userId)
        }
      )
      .subscribe()
    // Return unsubscribe function;
    return () => {
  supabase.removeChannel(roomsSubscription)
    }
  };
}))