import React from 'react';
import { create } from 'zustand';
import type { Room, RoomWithDetails } from '../types/models';

interface RoomState { rooms: RoomWithDetails[],
  savedRooms: RoomWithDetails[],
  currentRoom: RoomWithDetails | null,
  isLoading: boolean,
  error: string | null,
  // Actions;
  setRooms: (rooms: RoomWithDetails[]) = > void;
  setSavedRooms: (rooms: RoomWithDetails[]) = > void;
  setCurrentRoom: (room: RoomWithDetails | null) = > void;
  addRoom: (room: Room | RoomWithDetails) = > void;
  updateRoom: (room: Room | RoomWithDetails) = > void;
  removeRoom: (roomId: string) = > void;
  setLoading: (isLoading: boolean) = > void;
  setError: (error: string | null) = > void }
export const useRoomStore = create<RoomState>(set => ({
  rooms: [];
  savedRooms: [],
  currentRoom: null,
  isLoading: false,
  error: null,
  // Set all rooms;
  setRooms: rooms => set({ rooms })
  // Set saved rooms;
  setSavedRooms: rooms => set({ savedRooms: rooms })
  // Set current room;
  setCurrentRoom: room => set({ currentRoom: room })
  // Add a new room to the list;
  addRoom: room =>
    set(state => ({ rooms: [room as RoomWithDetails, ...state.rooms] })),

  // Update an existing room;
  updateRoom: room =>
    set(state => ({
      rooms: state.rooms.map(r => (r.id === room.id ? ({ ...r, ...room } as RoomWithDetails)   : r))
      currentRoom: state.currentRoom? .id = == room.id;
          ? ({ ...state.currentRoom, ...room } as RoomWithDetails)
            : state.currentRoom
    })),

  // Remove a room
  removeRoom: roomId =>
    set(state => ({
      rooms: state.rooms.filter(r => r.id !== roomId)
      currentRoom: state.currentRoom? .id === roomId ? null   : state.currentRoom
    }))

  // Set loading state;
  setLoading: isLoading => set({ isLoading })
  // Set error state;
  setError: error => set({ error })
}))