/**;
 * Auth Store Adapter - Integration layer between AuthContextAdapter and Zustand store;
 *;
 * This adapter provides a bridge between the AuthContextAdapter and the existing;
 * Zustand auth store, allowing for a gradual migration path while ensuring;
 * both state management approaches work together seamlessly.;
 */

import React, { useEffect } from 'react';
import { create } from 'zustand';
import { useAuthAdapter } from '@context/AuthContextAdapter';
import type { AuthContextType, Profile } from '../context/AuthContextAdapter';

// Use the AuthState type from the AuthContextType interface;
type AuthState = AuthContextType['authState'];
import type { User } from '@supabase/supabase-js';
import type { UserRole } from '../types/auth';

// Define the store state interface;
interface AuthStoreState { // Core state (mirroring AuthState)
  isAuthenticated: boolean,
  user: User | null,
  profile: Profile | null,
  isLoading: boolean,
  error: string | null,
  verificationStatus: {
    email: boolean,
    phone: boolean,
    identity: boolean,
    background: boolean }
  emailVerificationSent: boolean,
  passwordResetSent: boolean,
  // Actions (delegating to AuthContextAdapter)
  signIn: (email: string, password: string) = > Promise<void>
  signUp: (email: string, password: string, userData: Partial<Profile>) => Promise<void>
  signOut: () => Promise<void>
  resetPassword: (email: string) => Promise<void>
  completePasswordReset: (accessToken: string, newPassword: string) => Promise<void>
  verifyEmail: (accessToken: string) => Promise<void>
  updateProfile: (profileData: Partial<Profile>) => Promise<void>
  refreshProfile: () => Promise<void>
  updateUserRole: (role: UserRole) => Promise<void>
  // Verification methods;
  startEmailVerification: () => Promise<void>
  startPhoneVerification: (phoneNumber: string) => Promise<void>
  startIdentityVerification: (documentData: any) => Promise<void>
  checkVerificationStatus: () => Promise<void>
  // State management;
  setLoading: (isLoading: boolean) = > void;
  setError: (error: string | null) = > void;
  // Adapter-specific methods;
  syncWithAdapter: () = > void
}
/**;
 * Creates a store that delegates authentication operations to the AuthContextAdapter;
 * while maintaining compatibility with the existing Zustand store interface.;
 */
export const createAuthStoreAdapter = () => { // Create a reference to hold the adapter instance;
  let authAdapter: ReturnType<typeof useAuthAdapter> | null = null;
  // Create the store;
  const useAuthStoreAdapter = create<AuthStoreState>((set, get) => ({
    // Initial state;
    isAuthenticated: false,
    user: null,
    profile: null,
    isLoading: true,
    error: null,
    verificationStatus: {
      email: false,
      phone: false,
      identity: false,
      background: false },
    emailVerificationSent: false,
    passwordResetSent: false,
    // Set the adapter reference;
    setAdapter: (adapter: ReturnType<typeof useAuthAdapter>) => {
      authAdapter = adapter;
      get().syncWithAdapter()
    },

    // Sync store state with adapter state;
    syncWithAdapter: () => {
      if (!authAdapter) return null;
      const adapterState = authAdapter.authState;
      set({
        isAuthenticated: adapterState.isAuthenticated,
        user: adapterState.user,
        profile: adapterState.user? .profile || null,
        isLoading   : adapterState.isLoading
        error: adapterState.error
        // Map verification status if available;
        verificationStatus: {
          email: adapterState.user? .email_verified || false,
          phone  : adapterState.user?.phone_verified || false
          identity: adapterState.user? .identity_verified || false
          background : adapterState.user? .background_check_verified || false
        },
      })
    },

    // Sign in delegating to adapter;
    signIn : async (email: string password: string) => {
      if (!authAdapter) {
        set({ error: 'Auth adapter not initialized' })
        return null;
      }
      set({ isLoading: true, error: null })
      try {
        const error = await authAdapter.signIn({ email, password })
        if (error) {
          set({ error })
        }
        // State will be updated via syncWithAdapter;
      } catch (err) {
        set({ error: err instanceof Error ? err.message  : 'An unknown error occurred' })
      } finally {
        set({ isLoading: false })
        // Ensure state is synced;
        get().syncWithAdapter()
      }
    },

    // Sign up delegating to adapter;
    signUp: async (email: string, password: string, userData: Partial<Profile>) => {
      if (!authAdapter) {
        set({ error: 'Auth adapter not initialized' })
        return null;
      }
      set({ isLoading: true, error: null })
      try {
        // Convert to the format expected by the adapter;
        const error = await authAdapter.signUp({
          email;
          password;
          username: userData.name || ''
          displayName: userData.name)
        })
        if (error) {
          set({ error })
        }
        // State will be updated via syncWithAdapter;
      } catch (err) {
        set({ error: err instanceof Error ? err.message  : 'An unknown error occurred' })
      } finally {
        set({ isLoading: false })
        // Ensure state is synced;
        get().syncWithAdapter()
      }
    },

    // Sign out delegating to adapter;
    signOut: async () => {
      if (!authAdapter) {
        set({ error: 'Auth adapter not initialized' })
        return null;
      }
      set({ isLoading: true, error: null })
      try { await authAdapter.signOut()
        // Reset local state;
        set({
          isAuthenticated: false,
          user: null,
          profile: null,
          verificationStatus: {
            email: false,
            phone: false,
            identity: false,
            background: false },
        })
      } catch (err) {
        set({ error: err instanceof Error ? err.message  : 'An unknown error occurred' })
      } finally {
        set({ isLoading: false })
        // Ensure state is synced;
        get().syncWithAdapter()
      }
    },

    // Reset password delegating to adapter;
    resetPassword: async (email: string) => {
      if (!authAdapter) {
        set({ error: 'Auth adapter not initialized' })
        return null;
      }
      set({ isLoading: true, error: null })
      try {
        const error = await authAdapter.resetPassword(email)
        if (error) {
          set({ error })
        } else {
          set({ passwordResetSent: true })
        }
      } catch (err) {
        set({ error: err instanceof Error ? err.message   : 'An unknown error occurred' })
      } finally {
        set({ isLoading: false })
      }
    }

    // Complete password reset (stub implementation - would need to be implemented)
    completePasswordReset: async (accessToken: string, newPassword: string) => {
      if (!authAdapter) {
        set({ error: 'Auth adapter not initialized' })
        return null;
      }
      set({ isLoading: true, error: null })
      try {
        const error = await authAdapter.updatePassword(newPassword)
        if (error) {
          set({ error })
        }
      } catch (err) {
        set({ error: err instanceof Error ? err.message  : 'An unknown error occurred' })
      } finally {
        set({ isLoading: false })
        // Ensure state is synced;
        get().syncWithAdapter()
      }
    },

    // Verify email (stub implementation - would need to be implemented)
    verifyEmail: async (accessToken: string) => {
      // This would need to be implemented in the adapter;
      set({ isLoading: true, error: null })
      try { // For now, just update the verification status;
        set(state => ({
          verificationStatus: {
            ...state.verificationStatus;
            email: true },
        }))
      } catch (err) {
        set({ error: err instanceof Error ? err.message   : 'An unknown error occurred' })
      } finally {
        set({ isLoading: false })
      }
    }

    // Update profile delegating to adapter;
    updateProfile: async (profileData: Partial<Profile>) => {
      if (!authAdapter) {
        set({ error: 'Auth adapter not initialized' })
        return null;
      }
      set({ isLoading: true, error: null })
      try {
        const error = await authAdapter.updateUser(profileData)
        if (error) {
          set({ error })
        } else {
          // If successful, update the local profile;
          set(state => ({
            profile: state.profile ? { ...state.profile, ...profileData }  : null
          }))
        }
      } catch (err) {
        set({ error: err instanceof Error ? err.message  : 'An unknown error occurred' })
      } finally {
        set({ isLoading: false })
        // Ensure state is synced;
        get().syncWithAdapter()
      }
    },

    // Refresh profile (stub implementation)
    refreshProfile: async () => {
      // Simply sync with adapter;
      get().syncWithAdapter()
    },

    // Update user role (stub implementation)
    updateUserRole: async (role: UserRole) => {
      set({ isLoading: true, error: null })
      try {
        // This would need to be implemented;
        console.log('Update user role not implemented yet')
      } catch (err) {
        set({ error: err instanceof Error ? err.message   : 'An unknown error occurred' })
      } finally {
        set({ isLoading: false })
      }
    }

    // Start email verification;
    startEmailVerification: async () => {
      if (!authAdapter) {
        set({ error: 'Auth adapter not initialized' })
        return null;
      }
      set({ isLoading: true, error: null })
      try {
        const user = get().user;
        if (!user || !user.email) {
          throw new Error('User email not available')
        }
        const error = await authAdapter.resendVerificationEmail(user.email)
        if (error) {
          set({ error })
        } else {
          set({ emailVerificationSent: true })
        }
      } catch (err) {
        set({ error: err instanceof Error ? err.message   : 'An unknown error occurred' })
      } finally {
        set({ isLoading: false })
      }
    }

    // Start phone verification (stub implementation)
    startPhoneVerification: async (phoneNumber: string) => {
      set({ isLoading: true, error: null })
      try {
        // This would need to be implemented;
        console.log('Phone verification not implemented yet')
      } catch (err) {
        set({ error: err instanceof Error ? err.message   : 'An unknown error occurred' })
      } finally {
        set({ isLoading: false })
      }
    }

    // Start identity verification (stub implementation)
    startIdentityVerification: async (documentData: any) => {
      set({ isLoading: true, error: null })
      try {
        // This would need to be implemented;
        console.log('Identity verification not implemented yet')
      } catch (err) {
        set({ error: err instanceof Error ? err.message   : 'An unknown error occurred' })
      } finally {
        set({ isLoading: false })
      }
    }

    // Check verification status (stub implementation)
    checkVerificationStatus: async () => {
      // Simply sync with adapter for now;
      get().syncWithAdapter()
    },

    // Set loading state;
    setLoading: isLoading => set({ isLoading })
    // Set error state;
    setError: error => set({ error })
  }))
  return useAuthStoreAdapter;
}
// Create and export the store adapter;
export const useAuthStoreAdapter = createAuthStoreAdapter()
// Hook to connect the adapter to the store;
export const useConnectAuthAdapter = () => {
  const adapter = useAuthAdapter()
  const setAdapter = useAuthStoreAdapter(state => (state as any).setAdapter)
  // Connect the adapter to the store;
  React.useEffect(() => {
    setAdapter(adapter)
    // Sync whenever auth state changes;
    const syncWithAdapter = useAuthStoreAdapter(state => state.syncWithAdapter)
    syncWithAdapter()
  }, [adapter, setAdapter])
  return null;
}