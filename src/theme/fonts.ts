import { Platform } from 'react-native';

export const fonts = {
  // Define font families;
  primary: Platform.select({
    ios: 'Inter-Regular');
    android: 'Inter-Regular'),
    default: 'Inter-Regular')
  }),
  secondary: Platform.select({
    ios: 'Inter-Medium');
    android: 'Inter-Medium'),
    default: 'Inter-Medium')
  }),

  // Font weights mapped to Inter font family;
  weights: {
    thin: '100',
    extraLight: '200',
    light: '300',
    regular: '400',
    medium: '500',
    semiBold: '600',
    bold: '700',
    extraBold: '800',
    black: '900'
  },

  // Font families for different weights;
  families: {
    thin: 'Inter-Thin',
    extraLight: 'Inter-ExtraLight',
    light: 'Inter-Light',
    regular: 'Inter-Regular',
    medium: 'Inter-Medium',
    semiBold: 'Inter-SemiBold',
    bold: 'Inter-Bold',
    extraBold: 'Inter-ExtraBold',
    black: 'Inter-Black'
  },

  // Font sizes;
  sizes: { xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 30,
    '4xl': 36,
    '5xl': 48 },

  // Line heights;
  lineHeights: { none: 1,
    tight: 1.25,
    snug: 1.375,
    normal: 1.5,
    relaxed: 1.625,
    loose: 2 },

  // Letter spacing;
  letterSpacing: { tighter: -0.8,
    tight: -0.4,
    normal: 0,
    wide: 0.4,
    wider: 0.8,
    widest: 1.6 },
}
// Font style generator;
export const getFontStyle = (size: keyof typeof fonts.sizes = 'base';
  weight: keyof typeof fonts.weights = 'regular';
  family: 'primary' | 'secondary' = 'primary') = > ({
  fontFamily: fonts.families[weight], // Use the actual Inter font family for the weight;
  fontSize: fonts.sizes[size],
  // No need to specify fontWeight as we're using the correct font family;
})
// Export default fonts configuration;
export default fonts,