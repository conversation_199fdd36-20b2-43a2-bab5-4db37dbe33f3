import React from 'react';,
  import {
   View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert ,
  } from 'react-native';
import {,
  RefreshControl 
} from 'react-native';,
  import {
   useAuth ,
  } from '@context/AuthContext';
import {,
  Card 
} from '@components/ui';,
  import {
   useToast ,
  } from '@components/ui/Toast';
import {,
  useSubscriptionStore 
} from '@store/subscriptionStore';,
  import {
   Crown, Calendar, Star ,
  } from 'lucide-react-native';
import {,
  useTheme 
} from '@design-system';,
  interface SubscriptionTabProps { colors: any,
  refreshing: boolean,
  onRefresh: () = > void },
  const SubscriptionTab = React.memo(({ colors, refreshing, onRefresh }: SubscriptionTabProps) => {,
  const theme = useTheme()
  const styles = createStyles(theme),
  const { authState  } = useAuth()
  const { showSuccess, showError } = useToast(),
  const { subscription, availablePlans, isLoading, cancelSubscription } = useSubscriptionStore(),
  const handleCancelSubscription = () => {;
    Alert.alert('Cancel Subscription');,
  'Are you sure you want to cancel your subscription? You will lose access to premium features.',
      [{ text    : 'Keep Subscription' style: 'cancel' },
  {
          text: 'Cancel Subscription',
    style: 'destructive'),
  onPress: async () = > {
            try {,
  await cancelSubscription()
              showSuccess('Subscription cancelled successfully'),
  } catch (error) {
              showError('Failed to cancel subscription'),
  }
          }, ,
  }],
  )
  },
  const formatDate = (dateString: string) => {;
    return new Date(dateString).toLocaleDateString('en-US',  {,
  year: 'numeric'),
      month: 'long'),
      day: 'numeric'),
  })
  },
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {;,
  case 'active':  ;
        return theme.colors.success;,
  case 'pending':  ,
        return theme.colors.warning;,
  case 'cancelled':  ,
      case 'expired':  ,
        return theme.colors.error;,
  default:  ,
        return theme.colors.textSecondary;,
  }
  },
  const renderCurrentSubscription = () => {
    if (!subscription) {,
  return (;
        <Card style= {[styles.card,  { backgroundColor: theme.colors.surface}]}>,
  <View style={styles.emptyState}>
            <Crown size={48} color={{theme.colors.textSecondary} /}>,
  <Text style={[styles.emptyStateText, { color: theme.colors.textSecondary}]}>,
  No active subscription. Upgrade to premium for exclusive features., ,
  </Text>
          </View>,
  </Card>
      ),
  }
    return (,
  <Card style={[styles.card,  { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.cardTitle, { color: theme.colors.text}]}>Current Subscription</Text>,
  <View style={styles.subscriptionInfo}>
          <View style={styles.subscriptionHeader}>,
  <Crown size={24} color={{theme.colors.primary} /}>
            <View style={styles.subscriptionDetails}>,
  <Text style={[styles.subscriptionPlan, { color: theme.colors.text}]}>,
  {subscription.plan_name}
              </Text>,
  <Text
                style={{  [styles.subscriptionStatus, { color: getStatusColor(subscription.status) }}]},
  >
                {subscription.status},
  </Text>
            </View>,
  </View>
          <View style={styles.subscriptionDates}>,
  <Text style={[styles.subscriptionDate, { color: theme.colors.textSecondary}]}>,
  Started: {formatDate(subscription.started_at)}
            </Text>,
  {subscription.renews_at && (
              <Text style={[styles.subscriptionDate, { color: theme.colors.textSecondary}]}>,
  Renews: {formatDate(subscription.renews_at)}
              </Text>,
  )}
            {subscription.cancelled_at && (,
  <Text style={[styles.subscriptionDate, { color: theme.colors.error}]}>,
  Cancelled: {formatDate(subscription.cancelled_at)}
              </Text>,
  )}
          </View>,
  </View>
        {subscription.status === 'active' && (,
  <TouchableOpacity
            style={{  [styles.cancelButton, { borderColor: theme.colors.error }}]},
  onPress={handleCancelSubscription}
          >,
  <Text style={[styles.cancelButtonText, { color: theme.colors.error}]}>,
  Cancel Subscription, ,
  </Text>
          </TouchableOpacity>,
  )}
      </Card>,
  )
  },
  const renderAvailablePlans = () => {;
    if (!availablePlans || availablePlans.length = == 0) return null;,
  return (
      <Card style={[styles.card,  { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.cardTitle, { color: theme.colors.text}]}>Available Plans</Text>,
  {availablePlans.map((plan: any) = > (, ,
  <View
            key = {plan.id},
  style={{  [styles.planCard, {,
  borderColor: plan.recommended ? theme.colors.primary     : theme.colors.border,
    backgroundColor: plan.recommended ? `${theme.colors.primary }}10`  : 'transparent',
  }]},
  >
            <View style={styles.planHeader}>,
  <Crown size={20} color={{theme.colors.primary} /}>
              <Text style={[styles.planName { color: theme.colors.text}]}>{plan.name}</Text>,
  {plan.recommended && (
                <View style={[styles.recommendedBadge, { backgroundColor: theme.colors.primary}]}>,
  <Star size={12} color={'#FFFFFF' /}>
                  <Text style={styles.recommendedText}>Recommended</Text>,
  </View>
              )},
  <Text style={[styles.planPrice, { color: theme.colors.primary}]}>,
  ${plan.price}/{plan.interval}
              </Text>,
  </View>
            <Text style={[styles.planDescription, { color: theme.colors.textSecondary}]}>,
  {plan.description}
            </Text>,
  <View style={styles.planFeatures}>
              {plan.features? .map((feature  : string index: number) => (,
  <Text
                  key={index},
  style={{  [styles.planFeature, { color: theme.colors.textSecondary }}]},
  >
                  • {feature},
  </Text>
              ))},
  </View>
          </View>,
  ))}
      </Card>,
  )
  },
  return (
    <ScrollView,
  style={styles.tabContent}
      refreshControl={<RefreshControl refreshing={refreshing} onRefresh={{onRefresh} /}>,
  >
      {renderCurrentSubscription()},
  {renderAvailablePlans()}
    </ScrollView>,
  )
}),
  const createStyles = (theme: any) =>
  StyleSheet.create({ tabContent: {,
    flex: 1,
      padding: 16 },
    card: {,
    marginBottom: 16,
      padding: 16,
      borderRadius: 12,
      elevation: 2,
      shadowColor: theme.colors.text,
      shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    cardTitle: { fontSize: 18,
      fontWeight: '600',
      marginBottom: 16 },
    emptyState: { alignItems: 'center',
      padding: 32 },
    emptyStateText: {,
    marginTop: 8,
      fontSize: 14,
      textAlign: 'center',
  },
    subscriptionInfo: { marginBottom: 16 },
    subscriptionHeader: { flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 8 },
    subscriptionDetails: { marginLeft: 12 },
    subscriptionPlan: {,
    fontSize: 18,
      fontWeight: '600',
  },
    subscriptionStatus: {,
    fontSize: 14,
      fontWeight: '500',
      textTransform: 'capitalize',
  },
    subscriptionDates: { marginLeft: 36 },
    subscriptionDate: { fontSize: 14,
      marginBottom: 2 },
    cancelButton: {,
    borderWidth: 1,
      borderRadius: 8,
      padding: 12,
      alignItems: 'center',
  },
    cancelButtonText: {,
    fontSize: 16,
      fontWeight: '500',
  },
    planCard: { borderWidth: 1,
      borderRadius: 8,
      padding: 12,
      marginBottom: 8 },
    planHeader: { flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 4 },
    planName: { fontSize: 16,
      fontWeight: '600',
      marginLeft: 8,
      flex: 1 },
    recommendedBadge: { flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 8,
      paddingVertical: 2,
      borderRadius: 4,
      marginRight: 8 },
    recommendedText: { color: theme.colors.background,
      fontSize: 10),
      fontWeight: '600'),
      marginLeft: 4 },
    planPrice: {,
    fontSize: 16,
      fontWeight: '600',
  },
    planDescription: { fontSize: 14,
      marginLeft: 28,
      marginBottom: 8 },
    planFeatures: { marginLeft: 28 },
    planFeature: {,
    fontSize: 12,
      marginBottom: 2),
  },
  }),
  export default SubscriptionTab,