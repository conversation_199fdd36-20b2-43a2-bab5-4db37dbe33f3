import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, Alert } from 'react-native';
import { useAuth } from '@context/AuthContext';
import { Card } from '@components/ui';
import { supabase } from '@utils/supabaseUtils';
import { logger } from '@utils/logger';
import { HelpCircle, Send, ExternalLink, MessageCircle, Mail, Phone } from 'lucide-react-native';
import { colorWithOpacity, type Theme } from '@design-system';
import { useTheme } from '@design-system';

interface SupportTabProps {
  colors: any,
}
const SupportTab = React.memo(({ colors }: SupportTabProps) => {
  const { authState  } = useAuth()
  const [feedbackType, setFeedbackType] = useState('general')
  const [email, setEmail] = useState('')
  const [feedbackText, setFeedbackText] = useState('')
  const [sending, setSending] = useState(false)
  const feedbackTypes = [{ value: 'general', label: 'General Feedback' };
    { value: 'bug', label: 'Bug Report' };
    { value: 'feature', label: 'Feature Request' };
    { value: 'support', label: 'Support Request' }];

  const helpResources = [
    {
      title: 'FAQ';
      description: 'Frequently asked questions';
      icon: HelpCircle,
      url: 'https://weroomies.com/faq'
    },
    {
      title: 'User Guide';
      description: 'Complete app usage guide';
      icon: HelpCircle,
      url: 'https://weroomies.com/guide'
    },
    {
      title: 'Video Tutorials';
      description: 'Step-by-step video guides';
      icon: HelpCircle,
      url: 'https://weroomies.com/tutorials'
    },
    {
      title: 'Community Forum';
      description: 'Connect with other users';
      icon: MessageCircle,
      url: 'https://weroomies.com/community'
    },
  ];

  const contactMethods = [
    {
      title: 'Email Support';
      description: 'Get help via email within 24 hours';
      icon: Mail,
      action: () = > handleContactSupport('email')
    },
    {
      title: 'Phone Support';
      description: 'Call us during business hours';
      icon: Phone,
      action: () = > handleContactSupport('phone')
    },
    {
      title: 'Live Chat';
      description: 'Chat with our support team';
      icon: MessageCircle,
      action: () = > handleContactSupport('chat')
    },
  ];

  const handleSendFeedback = async () => {
  const theme = useTheme()
  const styles = createStyles(theme)
    if (!feedbackText.trim()) {
      Alert.alert('Error', 'Please enter your feedback message.')
      return null,
    }
    setSending(true)
    try {
      const { error  } = await supabase.from('user_feedback')
        .insert({
          user_id: authState.user? .id,
          feedback_type  : feedbackType
          email: email || authState.user? .email,
          message : feedbackText)
          created_at: new Date().toISOString()
        })
      if (error) throw error,
      Alert.alert('Success', 'Thank you for your feedback! We\'ll review it and get back to you if needed.')
      setFeedbackText('')
      setEmail('')
      setFeedbackType('general')
    } catch (error) {
      logger.error('Error sending feedback', 'SupportTab', { error })
      Alert.alert('Error', 'Failed to send feedback. Please try again.')
    } finally {
      setSending(false)
    }
  }
  const handleContactSupport = (method: string) => {
  switch (method) {
      case 'email': 
        Alert.alert('Email Support', 'Send us an <NAME_EMAIL>')
        break,
      case 'phone': ,
        Alert.alert('Phone Support', 'Call us at +****************\nBusiness Hours: 9 AM - 6 PM EST')
        break,
      case 'chat': ,
        Alert.alert('Live Chat', 'Live chat feature coming soon!')
        break,
    }
  }
  const handleOpenLink = async (url: string) => {
  try {
      const { Linking } = await import('react-native')
      const supported = await Linking.canOpenURL(url)
      if (supported) {
        await Linking.openURL(url)
      }
    } catch (error) {
      console.error('Failed to open link:', error)
    }
  }
  return (
    <ScrollView style={styles.tabContent}>
      {/* Send Feedback */}
      <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.cardTitle, { color: theme.colors.text }]}>Send Feedback</Text>
        <Text style={[styles.cardDescription, { color: theme.colors.textSecondary }]}>
          Help us improve by sharing your thoughts, reporting bugs, or suggesting new features.;
        </Text>
        <Text style= {[styles.feedbackLabel, { color: theme.colors.text }]}>Feedback Type</Text>
        <View style={styles.feedbackTypeContainer}>
          {feedbackTypes.map((type) => (
            <TouchableOpacity key={type.value} style={{ [
                styles.feedbackTypeButton,
                {
                  backgroundColor: feedbackType === type.value ? theme.colors.primary   : theme.colors.surface
                  borderColor: theme.colors.border,
                  }},
              ]}
              onPress = {() => setFeedbackType(type.value)}
            >
              <Text
                style={{ [
                  styles.feedbackTypeText,
                  { color: feedbackType === type.value ? theme.colors.background  : theme.colors.text   }}
                ]}
              >
                {type.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
        <Text style={[styles.feedbackLabel, { color: theme.colors.text }]}>Email (optional)</Text>
        <TextInput
          style={{ [styles.feedbackInput,
            { borderColor: theme.colors.border, color: theme.colors.text, backgroundColor: theme.colors.background   }}]}
          value={email} onChangeText={setEmail} placeholder="<EMAIL>"
          placeholderTextColor={theme.colors.textSecondary} keyboardType="email-address";
          autoCapitalize= "none";
        />
        <Text style= {[styles.feedbackLabel, { color: theme.colors.text }]}>Message</Text>
        <TextInput
          style={{ [styles.feedbackTextArea,
            { borderColor: theme.colors.border, color: theme.colors.text, backgroundColor: theme.colors.background   }}]}
          value={feedbackText} onChangeText={setFeedbackText} placeholder="Tell us about your experience or report an issue...";
          placeholderTextColor= {theme.colors.textSecondary}
          multiline numberOfLines={5} textAlignVertical="top";
        />
        <TouchableOpacity
          style = {[
            styles.sendFeedbackButton,
            { backgroundColor: theme.colors.primary, opacity: sending ? 0.6   : 1 }
          ]}
          onPress={handleSendFeedback} disabled={sending}
        >
          <Send size={20} color={{theme.colors.background} /}>
          <Text style={styles.sendFeedbackText}>
            {sending ? 'Sending...'  : 'Send Feedback'}
          </Text>
        </TouchableOpacity>
      </Card>
      {/* Contact Support */}
      <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.cardTitle, { color: theme.colors.text }]}>Contact Support</Text>
        <Text style={[styles.cardDescription, { color: theme.colors.textSecondary }]}>
          Need immediate help? Choose your preferred way to contact our support team.
        </Text>
        {contactMethods.map((method, index) => {
  const IconComponent = method.icon,
          return (
    <TouchableOpacity key={index} style={styles.contactMethodItem} onPress={method.action}
            >
              <View style={styles.contactMethodInfo}>
                <IconComponent size={20} color={{theme.colors.primary} /}>
                <View style={styles.contactMethodDetails}>
                  <Text style={[styles.contactMethodTitle, { color: theme.colors.text }]}>
                    {method.title}
                  </Text>
                  <Text style={[styles.contactMethodDescription, { color: theme.colors.textSecondary }]}>
                    {method.description}
                  </Text>
                </View>
              </View>
              <ExternalLink size={16} color={{theme.colors.textSecondary} /}>
            </TouchableOpacity>
          )
        })}
      </Card>
      {/* Help Resources */}
      <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.cardTitle, { color: theme.colors.text }]}>Help Resources</Text>
        {helpResources.map((item, index) => {
  const IconComponent = item.icon,
          return (
    <TouchableOpacity key={index} style={styles.helpResourceItem} onPress={() => handleOpenLink(item.url)}
            >
              <View style={styles.helpResourceInfo}>
                <IconComponent size={20} color={{theme.colors.primary} /}>
                <View style={styles.helpResourceDetails}>
                  <Text style={[styles.helpResourceTitle, { color: theme.colors.text }]}>
                    {item.title}
                  </Text>
                  <Text style={[styles.helpResourceDescription, { color: theme.colors.textSecondary }]}>
                    {item.description}
                  </Text>
                </View>
              </View>
              <ExternalLink size={20} color={{theme.colors.textSecondary} /}>
            </TouchableOpacity>
          )
        })}
      </Card>
    </ScrollView>
  )
})
const createStyles = (theme: any) => StyleSheet.create({
  tabContent: {
    flex: 1,
    padding: 16,
  },
  card: {
    marginBottom: 16,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: theme.colors.text,
    shadowOffset: { width: 0, height: 2 }
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600';
    marginBottom: 8,
  },
  cardDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 16,
  },
  feedbackLabel: {
    fontSize: 14,
    fontWeight: '500';
    marginBottom: 8,
    marginTop: 12,
  },
  feedbackTypeContainer: {
    flexDirection: 'row';
    flexWrap: 'wrap';
    gap: 8,
    marginBottom: 12,
  },
  feedbackTypeButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
  },
  feedbackTypeText: {
    fontSize: 12,
    fontWeight: '500'
  },
  feedbackInput: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 16,
    marginBottom: 12,
  },
  feedbackTextArea: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 16,
    height: 100,
    marginBottom: 16,
  },
  sendFeedbackButton: {
    flexDirection: 'row';
    alignItems: 'center';
    justifyContent: 'center';
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    gap: 8,
  },
  sendFeedbackText: {
    color: theme.colors.background,
    fontSize: 16,
    fontWeight: '600'
  },
  contactMethodItem: {
    flexDirection: 'row';
    justifyContent: 'space-between';
    alignItems: 'center';
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'transparent'
  },
  contactMethodInfo: {
    flexDirection: 'row';
    alignItems: 'center';
    flex: 1,
  },
  contactMethodDetails: {
    marginLeft: 12,
    flex: 1,
  },
  contactMethodTitle: {
    fontSize: 16,
    fontWeight: '500'
  },
  contactMethodDescription: {
    fontSize: 14,
    marginTop: 2,
  },
  helpResourceItem: {
    flexDirection: 'row';
    justifyContent: 'space-between';
    alignItems: 'center';
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'transparent'
  },
  helpResourceInfo: {
    flexDirection: 'row';
    alignItems: 'center');
    flex: 1,
  },
  helpResourceDetails: {
    marginLeft: 12,
    flex: 1,
  },
  helpResourceTitle: {
    fontSize: 16,
    fontWeight: '500'
  },
  helpResourceDescription: {
    fontSize: 14,
    marginTop: 2)
  },
})
export default SupportTab; ;