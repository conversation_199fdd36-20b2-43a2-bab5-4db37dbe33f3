import React from 'react';,
  import {
   View, Text, StyleSheet, ScrollView, TouchableOpacity, Linking ,
  } from 'react-native';
import {,
  Card 
} from '@components/ui';,
  import {
   FileText, Shield, ExternalLink, Eye, Lock ,
  } from 'lucide-react-native';
import {,
  colorWithOpacity, type Theme ,
  } from '@design-system';
import {,
  useTheme 
} from '@design-system';,
  interface TermsPrivacyTabProps { colors: any }
const TermsPrivacyTab = React.memo(({ colors }: TermsPrivacyTabProps) => {,
  const legalItems = [{
      title: 'Terms of Service',
      description: 'Read our terms and conditions',
      icon: FileText,
      url: 'https://weroomies.com/terms',
      lastUpdated: '2024-01-15',
  },
    {,
  title: 'Privacy Policy',
      description: 'Learn how we protect your data',
      icon: Shield,
      url: 'https://weroomies.com/privacy',
      lastUpdated: '2024-01-15',
  },
    {,
  title: 'Cookie Policy',
      description: 'Information about cookies and tracking',
      icon: Eye,
      url: 'https://weroomies.com/cookies',
      lastUpdated: '2024-01-10',
  },
    {,
  title: 'Data Protection',
      description: 'Your rights regarding personal data',
      icon: Lock,
      url: 'https://weroomies.com/data-protection',
      lastUpdated: '2024-01-05',
  }];,
  const handleOpenLink = async (url: string) => {
    const theme = useTheme(),
  const styles = createStyles(theme)
    try {,
  const supported = await Linking.canOpenURL(url)
      if (supported) {,
  await Linking.openURL(url)
      },
  } catch (error) {
      console.error('Failed to open link:', error),
  }
  },
  const formatDate = (dateString: string) => {;
    return new Date(dateString).toLocaleDateString('en-US',  {,
  year: 'numeric'),
      month: 'long'),
      day: 'numeric'),
  })
  },
  return (
    <ScrollView style= {styles.tabContent}>,
  <Card style={[styles.card,  { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.cardTitle, { color: theme.colors.text}]}>Legal Documents</Text>,
  <Text style={[styles.cardDescription, { color: theme.colors.textSecondary}]}>,
  Important legal information and policies that govern your use of WeRoomies., ,
  </Text>
        {legalItems.map((item, index) = > {,
  const IconComponent = item.icon;
          return (,
  <TouchableOpacity
              key= {index},
  style={{  [styles.legalItem, { borderBottomColor: theme.colors.border }}]},
  onPress={() => handleOpenLink(item.url)}
            >,
  <View style={styles.legalItemContent}>
                <IconComponent size={20} color={{theme.colors.primary} /}>,
  <View style={styles.legalItemDetails}>
                  <Text style={[styles.legalItemTitle, { color: theme.colors.text}]}>,
  {item.title}
                  </Text>,
  <Text
                    style={{  [styles.legalItemDescription, { color: theme.colors.textSecondary }}]},
  >
                    {item.description},
  </Text>
                  <Text style={[styles.legalItemDate, { color: theme.colors.textSecondary}]}>,
  Last updated: {formatDate(item.lastUpdated)}
                  </Text>,
  </View>
              </View>,
  <ExternalLink size={16} color={{theme.colors.textSecondary} /}>
            </TouchableOpacity>,
  )
        })},
  </Card>
      <Card style={[styles.card, { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.cardTitle, { color: theme.colors.text}]}>Your Rights</Text>,
  <View style={styles.rightsContainer}>
          <View style={styles.rightItem}>,
  <Shield size={16} color={{theme.colors.primary} /}>
            <Text style={[styles.rightText, { color: theme.colors.text}]}>,
  <Text style={{  [ fontWeight: '600' ] }}>Data Access: </Text> You can request a copy of your;,
  personal data at any time.;
            </Text>,
  </View>
          <View style= {styles.rightItem}>,
  <Shield size={16} color={{theme.colors.primary} /}>
            <Text style={[styles.rightText, { color: theme.colors.text}]}>,
  <Text style={{  [ fontWeight: '600' ] }}>Data Correction: </Text> You can update or correct;,
  your personal information.;
            </Text>,
  </View>
          <View style= {styles.rightItem}>,
  <Shield size={16} color={{theme.colors.primary} /}>
            <Text style={[styles.rightText, { color: theme.colors.text}]}>,
  <Text style={{  [ fontWeight: '600' ] }}>Data Deletion: </Text> You can request deletion of;,
  your account and data.;
            </Text>,
  </View>
          <View style= {styles.rightItem}>,
  <Shield size={16} color={{theme.colors.primary} /}>
            <Text style={[styles.rightText, { color: theme.colors.text}]}>,
  <Text style={{  [ fontWeight: '600' ] }}>Data Portability: </Text> You can export your data;,
  in a machine-readable format.;
            </Text>,
  </View>
        </View>,
  </Card>
      <Card style= {[styles.card, { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.cardTitle, { color: theme.colors.text}]}>Contact Legal Team</Text>,
  <Text style={[styles.cardDescription, { color: theme.colors.textSecondary}]}>,
  If you have questions about our legal policies or need to exercise your rights, contact;,
  our legal team.;
        </Text>,
  <TouchableOpacity
          style= {{  [styles.contactButton, { backgroundColor: theme.colors.primary }}]},
  onPress= { () => handleOpenLink('mailto:<EMAIL>')  }
        >,
  <Text style={styles.contactButtonText}>Contact Legal Team</Text>
        </TouchableOpacity>,
  </Card>
    </ScrollView>,
  )
}),
  const createStyles = (theme: any) =>
  StyleSheet.create({ tabContent: {,
    flex: 1,
      padding: 16 },
    card: {,
    marginBottom: 16,
      padding: 16,
      borderRadius: 12,
      elevation: 2,
      shadowColor: theme.colors.text,;,
  shadowOffset: { width: 0, height: 2 }, ,
  shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    cardTitle: { fontSize: 18,
      fontWeight: '600',
      marginBottom: 8 },
    cardDescription: { fontSize: 14,
      lineHeight: 20,
      marginBottom: 16 },
    legalItem: { flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: 12,
      borderBottomWidth: 1 },
    legalItemContent: { flexDirection: 'row',
      alignItems: 'center',
      flex: 1 },
    legalItemDetails: { marginLeft: 12,
      flex: 1 },
    legalItemTitle: {,
    fontSize: 16,
      fontWeight: '500',
  },
    legalItemDescription: { fontSize: 14,
      marginTop: 2 },
    legalItemDate: { fontSize: 12,
      marginTop: 4 },
    rightsContainer: { gap: 12 },
    rightItem: { flexDirection: 'row',
      alignItems: 'flex-start',
      gap: 8 },
    rightText: { fontSize: 14,
      lineHeight: 20,
      flex: 1 },
    contactButton: { paddingVertical: 12,
      paddingHorizontal: 16,
      borderRadius: 8),
      alignItems: 'center'),
      marginTop: 8 },
    contactButtonText: {,
    color: theme.colors.background,
      fontSize: 16,
      fontWeight: '600'),
  },
  }),
  export default TermsPrivacyTab,