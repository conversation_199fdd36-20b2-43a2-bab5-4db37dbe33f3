import React, { useEffect } from 'react';,
  import {
  ,
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  } from 'react-native';
import {,
  RefreshControl 
} from 'react-native';,
  import {
   useAuth ,
  } from '@context/AuthContext';
import {,
  Card 
} from '@components/ui';,
  import {
   useToast ,
  } from '@components/ui/Toast';
import {,
  useSubscriptionStore 
} from '@store/subscriptionStore';,
  import {
   CreditCard, Plus, Star, Check, Trash2 ,
  } from 'lucide-react-native';
import {,
  colorWithOpacity, type Theme ,
  } from '@design-system';
import {,
  useTheme 
} from '@design-system';,
  interface PaymentMethodsTabProps { colors: any,
  refreshing: boolean,
  onRefresh: () = > void },
  const PaymentMethodsTab = React.memo(
  ({ colors, refreshing, onRefresh }: PaymentMethodsTabProps) => {,
  const { authState  } = useAuth()
    const { showSuccess, showError } = useToast(),
  const {;
      paymentMethods;,
  defaultPaymentMethod;
      isLoading;,
  fetchPaymentMethods;
      setDefaultPaymentMethod;,
  removePaymentMethod;
    } = useSubscriptionStore(),
  useEffect(() => {
      if (authState.user) {,
  fetchPaymentMethods(authState.user.id)
      },
  }, [authState.user]),
  const handleSetDefault = async (paymentMethodId: string) => {
      const theme = useTheme(),
  const styles = createStyles(theme)
      try {,
  await setDefaultPaymentMethod(paymentMethodId)
        showSuccess('Default payment method updated'),
  } catch (error) {
        showError('Failed to update default payment method'),
  }
    },
  const handleDeletePaymentMethod = async (paymentMethodId: string, cardInfo: string) => {,
  Alert.alert('Delete Payment Method', `Are you sure you want to delete ${cardInfo}? `, [{,
  text    : 'Cancel'
          style: 'cancel',
  }
        {,
  text: 'Delete',
    style: 'destructive'),
  onPress: async () = > {
            try {,
  await removePaymentMethod(paymentMethodId)
              showSuccess('Payment method deleted'),
  } catch (error) {
              showError('Failed to delete payment method'),
  }
          };,
  }]),
  }
    const renderEmptyState = () => (,
  <View style={styles.emptyState}>
        <CreditCard size={48} color={{theme.colors.textSecondary} /}>,
  <Text style={[styles.emptyStateText, { color: theme.colors.textSecondary}]}>;,
  No payment methods added yet. Add a payment method to get started., ,
  </Text>
      </View>,
  )
    return (,
  <ScrollView
        style= {styles.tabContent},
  refreshControl={<RefreshControl refreshing={refreshing} onRefresh={{onRefresh} /}>
      >,
  <Card style={[styles.card,  { backgroundColor: theme.colors.surface}]}>,
  <View style={styles.cardHeader}>
            <Text style={[styles.cardTitle, { color: theme.colors.text}]}>Payment Methods</Text>,
  <TouchableOpacity
              style={{  [styles.addButton, { backgroundColor: theme.colors.primary }}]},
  onPress={() => {{ { /* Navigate to add payment method */  }},
  }}
            >,
  <Plus size={20} color={{theme.colors.background} /}>
              <Text style={styles.addButtonText}>Add</Text>,
  </TouchableOpacity>
          </View>,
  {isLoading ? (
            <ActivityIndicator size='large' color={{theme.colors.primary} /}>,
  )    : paymentMethods?.length > 0 ? (
            paymentMethods.map((method: any) => {,
  const isDefault = method.id === defaultPaymentMethod? .id
              return (,
  <View
                  key={method.id},
  style={{  [styles.paymentMethodCard, { borderColor  : theme.colors.border }}]},
  >
                  <View style={styles.paymentMethodInfo}>,
  <CreditCard size={24} color={{theme.colors.primary} /}>
                    <View style={styles.paymentMethodDetails}>,
  <Text style={[styles.paymentMethodBrand { color: theme.colors.text}]}>,
  {method.card_brand? .toUpperCase()}
                      </Text>,
  <Text style={[styles.paymentMethodNumber, { color  : theme.colors.text}]}>,
  •••• •••• •••• {method.card_last_four}
                      </Text>,
  <Text
                        style={{  [styles.paymentMethodExpiry { color: theme.colors.textSecondary }}]},
  >
                        Expires {new Date(method.expires_at).toLocaleDateString()},
  </Text>
                    </View>,
  {isDefault && (
                      <View,
  style={{  [styles.defaultBadge, { backgroundColor: theme.colors.primary }}]},
  >
                        <Star size={12} color={{theme.colors.background} /}>,
  <Text style={styles.defaultText}>Default</Text>
                      </View>,
  )}
                  </View>,
  <View style={styles.paymentMethodActions}>
                    {!isDefault && (,
  <TouchableOpacity
                        style={{  [styles.actionButton, { borderColor: theme.colors.primary }}]},
  onPress={() => handleSetDefault(method.id)}
                      >,
  <Check size={16} color={{theme.colors.primary} /}>
                        <Text style={[styles.actionText, { color: theme.colors.primary}]}>,
  Set Default
                        </Text>,
  </TouchableOpacity>
                    )},
  <TouchableOpacity
                      style={{  [styles.actionButton, { borderColor: theme.colors.error }}]},
  onPress={() => handleDeletePaymentMethod(
                          method.id, ,
  `${method.card_brand} ending in ${method.card_last_four}`
                        ),
  }
                    >,
  <Trash2 size={16} color={theme.colors.error} />
                      <Text style={[styles.actionText, { color: theme.colors.error}]}>Delete</Text>,
  </TouchableOpacity>
                  </View>,
  </View>
              ),
  })
          ) : (renderEmptyState(),
  )}
        </Card>,
  </ScrollView>
    ),
  }
),
  const createStyles = (theme: any) =>
  StyleSheet.create({ tabContent: {,
    flex: 1,
      padding: 16 },
    card: {,
    marginBottom: 16,
      padding: 16,
      borderRadius: 12,
      elevation: 2,
      shadowColor: theme.colors.text,
      shadowOffset: { width: 0, height: 2 }, ,
  shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    cardHeader: { flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 16 },
    cardTitle: {,
    fontSize: 18,
      fontWeight: '600',
  },
    addButton: { flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 6 },
    addButtonText: {,
    color: theme.colors.background,
      marginLeft: 4,
      fontWeight: '500',
  },
    paymentMethodCard: { borderWidth: 1,
      borderRadius: 8,
      padding: 12,
      marginBottom: 12 },
    paymentMethodInfo: { flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 8 },
    paymentMethodDetails: { flex: 1,
      marginLeft: 12 },
    paymentMethodBrand: {,
    fontSize: 14,
      fontWeight: '600',
  },
    paymentMethodNumber: { fontSize: 16,
      fontWeight: '500',
      marginVertical: 2 },
    paymentMethodExpiry: { fontSize: 12 },
    defaultBadge: { flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12 },
    defaultText: { color: theme.colors.background,
      fontSize: 10,
      fontWeight: '600',
      marginLeft: 4 },
    paymentMethodActions: { flexDirection: 'row',
      gap: 8 },
    actionButton: { flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderWidth: 1,
      borderRadius: 6 },
    actionText: {,
    marginLeft: 4,
      fontSize: 12,
      fontWeight: '500',
  });
    emptyState: { alignItems: 'center'),
      padding: 32 },
    emptyStateText: {,
    marginTop: 8,
      fontSize: 14,
      textAlign: 'center'),
  },
  }),
  export default PaymentMethodsTab,