import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { useColorScheme } from 'react-native';
import { useTheme } from '@design-system';
import {
  Phone;
  Mail;
  Star;
  Calendar;
  DollarSign;
  CheckCircle;
  AlertTriangle;
  Clock;
} from 'lucide-react-native';

interface Tenant { id: string,
  name: string,
  email: string,
  phone: string,
  avatar_url?: string,
  move_in_date: string,
  lease_end_date: string,
  rent_amount: number,
  payment_status: 'current' | 'late' | 'overdue',
  last_payment_date: string,
  rating: number,
  is_verified: boolean,
  property_title?: string }
interface TenantCardProps { tenant: Tenant,
  onPress: (tenant: Tenant) = > void;
  onContact?: (tenant: Tenant) => void }
const TenantCard = React.memo(({ tenant, onPress, onContact }: TenantCardProps) => {
  const isDark = useColorScheme() === 'dark';
  const colors = getColors(isDark)
  const getPaymentStatusColor = (status: string) => {
    const theme = useTheme()
    const styles = createStyles(theme)
    switch (status) {
      case 'current':  ;
        return theme.colors.success;
      case 'late':  ,
        return theme.colors.warning;
      case 'overdue':  ,
        return theme.colors.error;
      default:  ,
        return theme.colors.textSecondary;
    }
  }
  const getPaymentStatusIcon = (status: string) => {
    switch (status) {
      case 'current':  ;
        return CheckCircle;
      case 'late':  ,
        return Clock;
      case 'overdue':  ,
        return AlertTriangle;
      default:  ,
        return Clock;
    }
  }
  const getPaymentStatusText = (status: string) => {
    switch (status) {
      case 'current':  ;
        return 'Current';
      case 'late':  ,
        return 'Late';
      case 'overdue':  ,
        return 'Overdue';
      default:  ,
        return status;
    }
  }
  const PaymentIcon = getPaymentStatusIcon(tenant.payment_status)
  return (
    <TouchableOpacity
      style={{ [styles.tenantCard, { backgroundColor: theme.colors.surface  }}]}
      onPress={() => onPress(tenant)}
      activeOpacity={0.7}
    >
      <View style={styles.tenantHeader}>
        <View style={styles.tenantInfo}>
          {tenant.avatar_url ? (
            <Image source={{ uri  : tenant.avatar_url    }} style={{styles.tenantAvatar} /}>
          ) : (
            <View
              style = {[styles.tenantAvatarPlaceholder;
                { backgroundColor: theme.colors.primary + '20' }]}
            >
              <Text style={[styles.tenantInitials, { color: theme.colors.primary }]}>
                {tenant.name;
                  .split(' ')
                  .map(n => n[0])
                  .join('')}
              </Text>
            </View>
          )}
          <View style={styles.tenantDetails}>
            <View style={styles.tenantNameRow}>
              <Text style={[styles.tenantName, { color: theme.colors.text }]}>{tenant.name}</Text>
              {tenant.is_verified && (
                <CheckCircle size={16} color={theme.colors.success} style={{styles.verifiedIcon} /}>
              )}
            </View>
            {tenant.property_title && (
              <Text style={[styles.propertyTitle, { color: theme.colors.textSecondary }]}>
                {tenant.property_title}
              </Text>
            )}
            <View style={styles.tenantRating}>
              <Star size={14} color={{theme.colors.warning} /}>
              <Text style={[styles.ratingText, { color: theme.colors.textSecondary }]}>
                {tenant.rating.toFixed(1)}
              </Text>
            </View>
          </View>
        </View>
        <View style={styles.paymentStatus}>
          <View
            style={{ [styles.paymentBadge, { backgroundColor: getPaymentStatusColor(tenant.payment_status) + '20'  }}]}
          >
            <PaymentIcon size={12} color={{getPaymentStatusColor(tenant.payment_status)} /}>
            <Text
              style={{ [styles.paymentText, { color: getPaymentStatusColor(tenant.payment_status)    }}]}
            >
              {getPaymentStatusText(tenant.payment_status)}
            </Text>
          </View>
        </View>
      </View>
      <View style={styles.tenantStats}>
        <View style={styles.statItem}>
          <DollarSign size={14} color={{theme.colors.primary} /}>
          <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>Rent:</Text>
          <Text style={[styles.statValue, { color: theme.colors.text }]}>
            ${tenant.rent_amount}/month;
          </Text>
        </View>
        <View style={styles.statItem}>
          <Calendar size={14} color={{theme.colors.textSecondary} /}>
          <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>Lease ends:</Text>
          <Text style={[styles.statValue, { color: theme.colors.text }]}>
            {new Date(tenant.lease_end_date).toLocaleDateString()}
          </Text>
        </View>
      </View>
      <View style={styles.tenantActions}>
        <TouchableOpacity
          style={{ [styles.actionButton, { backgroundColor: theme.colors.primary + '10'    }}]}
          onPress={() => onContact? .(tenant)}
        >
          <Phone size={16} color={{theme.colors.primary} /}>
          <Text style={[styles.actionText, { color  : theme.colors.primary }]}>Contact</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={{ [styles.actionButton { backgroundColor: theme.colors.textSecondary + '10'    }}]}
          onPress={() => onPress(tenant)}
        >
          <Text style={[styles.actionText, { color: theme.colors.text }]}>View Details</Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  )
})
const createStyles = (theme: any) =>
  StyleSheet.create({
    tenantCard: {
      borderRadius: 12,
      padding: 16,
      marginBottom: 16,
      elevation: 2,
      shadowColor: theme.colors.text,
      shadowOffset: { width: 0, height: 2 }
      shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    tenantHeader: { flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginBottom: 12 },
    tenantInfo: { flexDirection: 'row',
      alignItems: 'center',
      flex: 1 },
    tenantAvatar: { width: 48,
      height: 48,
      borderRadius: 24,
      marginRight: 12 },
    tenantAvatarPlaceholder: { width: 48,
      height: 48,
      borderRadius: 24,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 12 },
    tenantInitials: {
      fontSize: 18,
      fontWeight: '600'
    },
    tenantDetails: { flex: 1 },
    tenantNameRow: { flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 4 },
    tenantName: {
      fontSize: 16,
      fontWeight: '600'
    },
    verifiedIcon: { marginLeft: 6 },
    propertyTitle: { fontSize: 14,
      marginBottom: 4 },
    tenantRating: { flexDirection: 'row',
      alignItems: 'center',
      gap: 4 },
    ratingText: {
      fontSize: 14,
      fontWeight: '500'
    },
    paymentStatus: {
      alignItems: 'flex-end'
    },
    paymentBadge: { flexDirection: 'row',
      alignItems: 'center',
      gap: 4,
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12 },
    paymentText: {
      fontSize: 12,
      fontWeight: '600'
    },
    tenantStats: { gap: 8,
      marginBottom: 12 },
    statItem: { flexDirection: 'row',
      alignItems: 'center',
      gap: 6 },
    statLabel: { fontSize: 14 },
    statValue: {
      fontSize: 14,
      fontWeight: '500'
    },
    tenantActions: { flexDirection: 'row',
      gap: 8 },
    actionButton: {
      flexDirection: 'row'),
      alignItems: 'center'),
      gap: 6,
      paddingHorizontal: 12,
      paddingVertical: 8,
      borderRadius: 8,
      flex: 1,
      justifyContent: 'center'
    },
    actionText: {
      fontSize: 14,
      fontWeight: '500')
    },
  })
export default TenantCard,