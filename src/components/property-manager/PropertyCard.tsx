import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { useColorScheme } from 'react-native';
import { useTheme } from '@design-system';
import { MapPin, Users, DollarSign, Eye, Star } from 'lucide-react-native';

interface Property { id: string,
  title: string,
  description: string,
  location: string,
  price: number,
  room_type: string,
  status: 'available' | 'occupied' | 'maintenance' | 'pending',
  images: string[],
  views: number,
  tenant?: {
    name: string,
    rating: number }
}
interface PropertyCardProps { property: Property,
  onPress: (property: Property) = > void;
  onEdit?: (property: Property) => void }
const PropertyCard = React.memo(({ property, onPress, onEdit }: PropertyCardProps) => {
  const isDark = useColorScheme() === 'dark';
  const colors = getColors(isDark)
  const getStatusColor = (status: string) => {
    const theme = useTheme()
    const styles = createStyles(theme)
    switch (status) {
      case 'available':  ;
        return theme.colors.success;
      case 'occupied':  ,
        return theme.colors.primary;
      case 'maintenance':  ,
        return theme.colors.warning;
      case 'pending':  ,
        return theme.colors.textSecondary;
      default:  ,
        return theme.colors.textSecondary;
    }
  }
  const getStatusText = (status: string) => {
    switch (status) {
      case 'available':  ;
        return 'Available';
      case 'occupied':  ,
        return 'Occupied';
      case 'maintenance':  ,
        return 'Maintenance';
      case 'pending':  ,
        return 'Pending';
      default:  ,
        return status;
    }
  }
  return (
    <TouchableOpacity
      style= {{ [styles.propertyCard; { backgroundColor: theme.colors.surface    }}]}
      onPress={() => onPress(property)}
      activeOpacity={0.7}
    >
      {property.images && property.images.length > 0 && (
        <Image
          source={{ uri: property.images[0]    }}
          style={styles.propertyImage}
          resizeMode='cover';
        />
      )}
      <View style= {styles.propertyContent}>
        <View style={styles.propertyHeader}>
          <Text style={{ [styles.propertyTitle, { color: theme.colors.text    }}]} numberOfLines={1}>
            {property.title}
          </Text>
          <View
            style={{ [styles.statusBadge, { backgroundColor: getStatusColor(property.status) + '20'  }}]}
          >
            <Text style={[styles.statusText, { color: getStatusColor(property.status) }]}>
              {getStatusText(property.status)}
            </Text>
          </View>
        </View>
        <View style={styles.propertyLocation}>
          <MapPin size={14} color={{theme.colors.textSecondary} /}>
          <Text
            style={{ [styles.locationText, { color: theme.colors.textSecondary    }}]}
            numberOfLines={1}
          >
            {property.location}
          </Text>
        </View>
        <Text
          style={{ [styles.propertyDescription, { color: theme.colors.textSecondary    }}]}
          numberOfLines={2}
        >
          {property.description}
        </Text>
        <View style={styles.propertyFooter}>
          <View style={styles.priceContainer}>
            <DollarSign size={16} color={{theme.colors.primary} /}>
            <Text style={[styles.priceText, { color: theme.colors.primary }]}>
              ${property.price}/month;
            </Text>
          </View>
          <View style={styles.propertyStats}>
            <View style={styles.statItem}>
              <Eye size={12} color={{theme.colors.textSecondary} /}>
              <Text style={[styles.statText, { color: theme.colors.textSecondary }]}>
                {property.views}
              </Text>
            </View>
            {property.tenant && (
              <View style={styles.statItem}>
                <Star size={12} color={{theme.colors.warning} /}>
                <Text style={[styles.statText, { color: theme.colors.textSecondary }]}>
                  {property.tenant.rating.toFixed(1)}
                </Text>
              </View>
            )}
          </View>
        </View>
      </View>
    </TouchableOpacity>
  )
})
const createStyles = (theme: any) =>
  StyleSheet.create({
    propertyCard: {
      borderRadius: 12,
      marginBottom: 16,
      overflow: 'hidden',
      elevation: 2,
      shadowColor: theme.colors.text,
      shadowOffset: { width: 0, height: 2 };
      shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    propertyImage: { width: '100%',
      height: 200 },
    propertyContent: { padding: 16 },
    propertyHeader: { flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 8 },
    propertyTitle: { fontSize: 18,
      fontWeight: '600',
      flex: 1,
      marginRight: 8 },
    statusBadge: { paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12 },
    statusText: {
      fontSize: 12,
      fontWeight: '600'
    },
    propertyLocation: { flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 8 },
    locationText: { fontSize: 14,
      marginLeft: 4,
      flex: 1 },
    propertyDescription: { fontSize: 14,
      lineHeight: 20,
      marginBottom: 12 },
    propertyFooter: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center'
    },
    priceContainer: {
      flexDirection: 'row',
      alignItems: 'center'
    },
    priceText: { fontSize: 16,
      fontWeight: '700',
      marginLeft: 4 },
    propertyStats: { flexDirection: 'row',
      gap: 16 },
    statItem: { flexDirection: 'row'),
      alignItems: 'center'),
      gap: 4 },
    statText: {
      fontSize: 12,
      fontWeight: '500')
    },
  })
export default PropertyCard,