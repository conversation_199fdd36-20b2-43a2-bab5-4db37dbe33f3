import React, { useState, useCallback } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, Alert, Platform, Image, ActivityIndicator } from 'react-native';
import { useTheme } from '@design-system';
import Input from '@components/ui/form/Input';
import { Button } from '@design-system';
import { logger } from '@utils/logger';
import { supabase } from '@lib/supabase';
import { useAuth } from '@context/AuthContext';
import { Home, MapPin, DollarSign, Users, Clock, Shield, Star, Camera, User, CheckCircle, Building } from 'lucide-react-native';
import * as ImagePicker from 'expo-image-picker';
import { intelligentUploader } from '@utils/intelligentUploadStrategy';

interface PropertyOwnerOnboardingProps {
  onComplete: (data: any) => void,
  initialData: any,
}
interface OnboardingData {
  // Basic Info;
  bio: string,
  management_experience: string;,
  // Property Details;
  property_types: string[],
  management_style: 'hands_on' | 'professional' | 'minimal',
  portfolio_size: number,
  property_locations: string[];,
  // Tenant Preferences;
  preferred_tenant_types: string[],
  screening_criteria: string[],
  rental_requirements: string[];,
  // Management Approach;
  maintenance_approach: 'diy' | 'professional' | 'mixed',
  lease_preferences: string[],
  rental_philosophy: string;,
  // Media Upload;
  property_photos: string[],
  video_tour_url: string | null,
}
const STEP_TITLES = [
  'Property management profile',
  'Property portfolio details',
  'Tenant preferences',
  'Management approach',
  'Property Photos & Video Tour';
];

const PROPERTY_TYPES = [
  'Single Family Home', 'Apartment', 'Condo', 'Townhouse', 'Studio',
  'Room Rental', 'Duplex', 'Multi-Family', 'Luxury Property', 'Student Housing';
];

const TENANT_TYPES = [
  'Young Professionals', 'Students', 'Families', 'Seniors', 'Pet Owners',
  'Non-Smokers', 'Long-term Renters', 'Short-term Renters', 'First-time Renters';
];

const SCREENING_CRITERIA = [
  'Credit Score Check', 'Employment Verification', 'Income Requirements',
  'References Check', 'Background Check', 'Previous Landlord Contact',
  'Pet Policy Compliance', 'No Smoking Policy';
];

const LEASE_PREFERENCES = [
  '6 Month Lease', '1 Year Lease', '2+ Year Lease', 'Month-to-Month',
  'Furnished Options', 'Utilities Included', 'Pet-Friendly', 'No Pets';
];

export default function PropertyOwnerOnboarding({ onComplete, initialData }: PropertyOwnerOnboardingProps) {
  const theme = useTheme();
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<OnboardingData>({
    bio: '',
    management_experience: '',
    property_types: [],
    management_style: 'hands_on',
    portfolio_size: 1,
    property_locations: [],
    preferred_tenant_types: [],
    screening_criteria: [],
    rental_requirements: [],
    maintenance_approach: 'mixed',
    lease_preferences: [],
    rental_philosophy: '',
    property_photos: [],
    video_tour_url: null,
    ...initialData,
  });

  const [uploadingMedia, setUploadingMedia] = useState(false);

  // Safe theme access with fallbacks;
  const safeTheme = {
    colors: {
      background: theme?.colors?.background || '#FFFFFF',
      surface: theme?.colors?.surface || '#F8F9FA',
      text: theme?.colors?.text || '#333333',
      textSecondary: theme?.colors?.textSecondary || '#666666',
      textOnPrimary: theme?.colors?.textOnPrimary || '#FFFFFF',
      border: theme?.colors?.border || '#E2E8F0',
      primary: theme?.colors?.primary || '#2563EB',
      success: theme?.colors?.success || '#10B981',
      error: theme?.colors?.error || '#EF4444',
    }
  }
  const updateFormData = (field: keyof OnboardingData, value: any) => {
  setFormData(prev => ({ ...prev, [field]: value }));
  }
  const toggleArrayItem = (array: string[], item: string, field: keyof OnboardingData) => {
  const currentArray = formData[field] as string[];
    const newArray = currentArray.includes(item);
      ? currentArray.filter(i => i !== item);
      : [...currentArray, item];
    updateFormData(field, newArray);
  }
  const validateCurrentStep = ($2) => {
  switch (currentStep) {
      case 0: // Basic Info,
        if (!formData.bio || formData.bio.length < 50) {
          Alert.alert('Bio Required', 'Please write at least 50 characters about your management approach');
          return false;
        }
        if (!formData.management_experience) {
          Alert.alert('Experience Required', 'Please enter your management experience');
          return false;
        }
        break;
      case 1: // Property Details,
        if (formData.property_types.length === 0) {
          Alert.alert('Property Types Required', 'Please select at least one property type you manage');
          return false;
        }
        if (!formData.portfolio_size) {
          Alert.alert('Portfolio Size Required', 'Please specify your portfolio size');
          return false;
        }
        break;
      case 2: // Tenant Preferences,
        if (formData.preferred_tenant_types.length === 0) {
          Alert.alert('Tenant Preferences Required', 'Please select your preferred tenant types');
          return false;
        }
        break;
      case 3: // Management Approach,
        if (!formData.rental_philosophy) {
          Alert.alert('Rental Philosophy Required', 'Please describe your rental philosophy');
          return false;
        }
        break;
      case 4: // Media Upload,
        if (formData.property_photos.length === 0) {
          Alert.alert('Property Photos Required', 'Please upload at least one property photo to showcase your properties');
          return false;
        }
        break;
    }
    return true;
  }
  const handleNext = () => {
  if (validateCurrentStep()) {
      if (currentStep < STEP_TITLES.length - 1) {
        setCurrentStep(currentStep + 1);
      } else {
        handleComplete();
      }
    }
  }
  const handleBack = () => {
  if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  }
  const handleComplete = async () => {
  setLoading(true);
    try {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }
      // Save property owner profile data;
      const profileUpdate = {
        bio: formData.bio,
        management_experience: formData.management_experience,
        property_management: {
          property_types: formData.property_types,
          management_style: formData.management_style,
          portfolio_size: formData.portfolio_size,
          property_locations: formData.property_locations,
        },
        tenant_preferences: {
          preferred_tenant_types: formData.preferred_tenant_types,
          screening_criteria: formData.screening_criteria,
          rental_requirements: formData.rental_requirements,
        },
        management_approach: {
          maintenance_approach: formData.maintenance_approach,
          lease_preferences: formData.lease_preferences,
          rental_philosophy: formData.rental_philosophy,
        },
        property_photos: formData.property_photos,
        video_tour_url: formData.video_tour_url,
        profile_completion: 80, // Increased from 70% after adding media;
        updated_at: new Date().toISOString(),
      }
      const { error } = await supabase.from('user_profiles')
        .update(profileUpdate);
        .eq('id', user.id)

      if (error) {
        throw error;
      }
      logger.info('Property owner profile creation completed', 'PropertyOwnerOnboarding', {
        userId: user.id,
        dataFields: Object.keys(profileUpdate),
        mediaUploaded: {
          photos: formData.property_photos.length,
          video: formData.video_tour_url ? 1 : 0,
        },
      });

      onComplete(profileUpdate);

    } catch (error) {
      logger.error('Failed to save property owner profile', 'PropertyOwnerOnboarding', {}, error as Error);
      Alert.alert('Error', 'Failed to save your profile. Please try again.');
    } finally {
      setLoading(false);
    }
  }
  // Media upload functions;
  const handleUploadPhotos = async () => {
  try {
      setUploadingMedia(true);

      // Request permissions;
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (!permissionResult.granted) {
        Alert.alert('Permission Required', 'We need access to your photos to upload property pictures.');
        return null;
      }
      // Launch image picker;
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'],
        allowsMultiple: true,
        quality: 0.7,
        allowsEditing: false,
        exif: false,
      });

      if (result.canceled || !result.assets?.length) {
        return null;
      }
      // Upload images using intelligent uploader;
      const uploadPromises = result.assets.map(async (asset) => {
  const fileName = `property-${user?.id}-${Date.now()}-${Math.floor(Math.random() * 1000)}.jpg`;
        const uploadResult = await intelligentUploader.smartUpload(asset.uri, {
          bucket: 'property-photos',
          path: `${user?.id}/${fileName}`,
          contentType: 'image/jpeg',
          enableOptimization: true,
        });

        if (uploadResult.success && uploadResult.url) {
          return uploadResult.url;
        }
        throw new Error(uploadResult.error || 'Upload failed');
      });

      const uploadedUrls = await Promise.all(uploadPromises);
      ;
      // Update form data;
      updateFormData('property_photos', [...formData.property_photos, ...uploadedUrls]);

      Alert.alert('Success', `${uploadedUrls.length} property photo(s) uploaded successfully!`);

    } catch (error) {
      logger.error('Failed to upload property photos', 'PropertyOwnerOnboarding', {}, error as Error);
      Alert.alert('Error', 'Failed to upload property photos. Please try again.');
    } finally {
      setUploadingMedia(false);
    }
  }
  const handleUploadVideo = async () => {
  try {
      setUploadingMedia(true);

      // Request permissions;
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (!permissionResult.granted) {
        Alert.alert('Permission Required', 'We need access to your media library to upload video tour.');
        return null;
      }
      // Launch video picker;
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['videos'],
        allowsEditing: true,
        aspect: [16, 9],
        quality: 0.8,
        videoMaxDuration: 120, // 2 minutes max for property tours;
      });

      if (result.canceled || !result.assets?.length) {
        return null;
      }
      // Upload video using intelligent uploader;
      const asset = result.assets[0];
      const fileName = `property-tour-${user?.id}-${Date.now()}.mp4`;
      const uploadResult = await intelligentUploader.smartUpload(asset.uri, {
        bucket: 'property-tours',
        path: `${user?.id}/${fileName}`,
        contentType: 'video/mp4',
        enableOptimization: true,
      });

      if (uploadResult.success && uploadResult.url) {
        updateFormData('video_tour_url', uploadResult.url);
        Alert.alert('Success', 'Property video tour uploaded successfully!');
      } else {
        throw new Error(uploadResult.error || 'Upload failed');
      }
    } catch (error) {
      logger.error('Failed to upload video tour', 'PropertyOwnerOnboarding', {}, error as Error);
      Alert.alert('Error', 'Failed to upload video tour. Please try again.');
    } finally {
      setUploadingMedia(false);
    }
  }
  const removePhoto = (index: number) => {
  const updatedPhotos = formData.property_photos.filter((_, i) => i !== index);
    updateFormData('property_photos', updatedPhotos);
  }
  const removeVideo = () => {
  updateFormData('video_tour_url', null);
  }
  const renderStepContent = () => {
  switch (currentStep) {
      case 0: // Basic Info,
        return (
    <View style={styles.stepContent}>
            <View style={styles.stepHeader}>
              <Building size={32} color={{safeTheme.theme.colors.primary} /}>
              <Text style={styles.stepTitle}>Property management profile</Text>
              <Text style={styles.stepDescription}>
                Tell us about your approach to property management;
              </Text>
            </View>
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>About Your Management Approach</Text>
              <TextInput style={styles.textArea} value={formData.bio} onChangeText={(text) ={}> updateFormData('bio', text)} placeholder="Describe your property management philosophy, experience, and what makes you a great landlord...";
                multiline numberOfLines={4} maxLength={500}
              />
              <Text style={styles.characterCount}>{formData.bio.length}/500</Text>
            </View>
            <Input
              label="Management Experience";
              value={formData.management_experience} onChangeText={(text) => updateFormData('management_experience', text)} placeholder="How long have you been managing properties? Any relevant background?";
            />
          </View>
        );

      case 1: // Property Details,
        return (
    <View style={styles.stepContent}>
            <View style={styles.stepHeader}>
              <Home size={32} color={{safeTheme.theme.colors.primary} /}>
              <Text style={styles.stepTitle}>Property portfolio details</Text>
              <Text style={styles.stepDescription}>
                Help us understand the types of properties you manage;
              </Text>
            </View>
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Property Types You Manage</Text>
              <View style={styles.tagGrid}>
                {PROPERTY_TYPES.map((type) => (
                  <TouchableOpacity key={type} style={[
                      styles.tag,
                      formData.property_types.includes(type) && styles.tagSelected;
                    ]} onPress={() => toggleArrayItem(formData.property_types, type, 'property_types')}
                  >
                    <Text style={[
                      styles.tagText,
                      formData.property_types.includes(type) && styles.tagTextSelected;
                    ]}>
                      {type}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Management Style</Text>
              <View style={styles.optionGrid}>
                {[
                  { key: 'hands_on', label: 'Hands-On', desc: 'Direct involvement in all aspects' },
                  { key: 'professional', label: 'Professional', desc: 'Structured, business approach' },
                  { key: 'minimal', label: 'Minimal', desc: 'Low-maintenance, tenant independence' },
                ].map((option) => (
                  <TouchableOpacity key={option.key} style={[
                      styles.styleOption,
                      formData.management_style === option.key && styles.styleOptionSelected;
                    ]} onPress={() => updateFormData('management_style', option.key)}
                  >
                    <Text style={[
                      styles.styleOptionTitle,
                      formData.management_style === option.key && styles.styleOptionTitleSelected;
                    ]}>
                      {option.label}
                    </Text>
                    <Text style={[
                      styles.styleOptionDesc,
                      formData.management_style === option.key && styles.styleOptionDescSelected;
                    ]}>
                      {option.desc}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Portfolio Size</Text>
              <View style={styles.portfolioSelector}>
                {[1, 2, 3, 4, 5, '6+'].map((size) => (
                  <TouchableOpacity key={size} style={[
                      styles.portfolioOption,
                      formData.portfolio_size === (typeof size === 'string' ? 6 : size) && styles.portfolioOptionSelected,
                    ]} onPress={() => updateFormData('portfolio_size', typeof size === 'string' ? 6 : size)}
                  >
                    <Text style={[
                      styles.portfolioOptionText,
                      formData.portfolio_size === (typeof size === 'string' ? 6 : size) && styles.portfolioOptionTextSelected,
                    ]}>
                      {size}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </View>
        );

      case 2: // Tenant Preferences,
        return (
    <View style={styles.stepContent}>
            <View style={styles.stepHeader}>
              <Users size={32} color={{safeTheme.theme.colors.primary} /}>
              <Text style={styles.stepTitle}>Tenant preferences</Text>
              <Text style={styles.stepDescription}>
                Define your ideal tenant profile and screening approach;
              </Text>
            </View>
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Preferred Tenant Types</Text>
              <View style={styles.tagGrid}>
                {TENANT_TYPES.map((type) => (
                  <TouchableOpacity key={type} style={[
                      styles.tag,
                      formData.preferred_tenant_types.includes(type) && styles.tagSelected;
                    ]} onPress={() => toggleArrayItem(formData.preferred_tenant_types, type, 'preferred_tenant_types')}
                  >
                    <Text style={[
                      styles.tagText,
                      formData.preferred_tenant_types.includes(type) && styles.tagTextSelected;
                    ]}>
                      {type}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Screening Criteria</Text>
              <View style={styles.tagGrid}>
                {SCREENING_CRITERIA.map((criteria) => (
                  <TouchableOpacity key={criteria} style={[
                      styles.tag,
                      formData.screening_criteria.includes(criteria) && styles.tagSelected;
                    ]} onPress={() => toggleArrayItem(formData.screening_criteria, criteria, 'screening_criteria')}
                  >
                    <Text style={[
                      styles.tagText,
                      formData.screening_criteria.includes(criteria) && styles.tagTextSelected;
                    ]}>
                      {criteria}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Communication Style</Text>
              <View style={styles.optionGrid}>
                {[
                  { key: 'frequent', label: 'Frequent Check-ins' },
                  { key: 'regular', label: 'Regular Updates' },
                  { key: 'minimal', label: 'As-Needed Only' },
                ].map((option) => (
                  <TouchableOpacity key={option.key} style={[
                      styles.optionButton,
                      formData.communication_style === option.key && styles.optionButtonSelected;
                    ]} onPress={() => updateFormData('communication_style', option.key)}
                  >
                    <Text style={[
                      styles.optionButtonText,
                      formData.communication_style === option.key && styles.optionButtonTextSelected;
                    ]}>
                      {option.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </View>
        );

      case 3: // Management Approach,
        return (
    <View style={styles.stepContent}>
            <View style={styles.stepHeader}>
              <Shield size={32} color={{safeTheme.theme.colors.primary} /}>
              <Text style={styles.stepTitle}>Management approach</Text>
              <Text style={styles.stepDescription}>
                Define your operational preferences and philosophy;
              </Text>
            </View>
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Maintenance Approach</Text>
              <View style={styles.optionGrid}>
                {[
                  { key: 'diy', label: 'DIY Focused', desc: 'Handle most repairs yourself' },
                  { key: 'professional', label: 'Professional Services', desc: 'Hire qualified contractors' },
                  { key: 'mixed', label: 'Mixed Approach', desc: 'Combination based on complexity' },
                ].map((option) => (
                  <TouchableOpacity key={option.key} style={[
                      styles.styleOption,
                      formData.maintenance_approach === option.key && styles.styleOptionSelected;
                    ]} onPress={() => updateFormData('maintenance_approach', option.key)}
                  >
                    <Text style={[
                      styles.styleOptionTitle,
                      formData.maintenance_approach === option.key && styles.styleOptionTitleSelected;
                    ]}>
                      {option.label}
                    </Text>
                    <Text style={[
                      styles.styleOptionDesc,
                      formData.maintenance_approach === option.key && styles.styleOptionDescSelected;
                    ]}>
                      {option.desc}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Lease Preferences</Text>
              <View style={styles.tagGrid}>
                {LEASE_PREFERENCES.map((pref) => (
                  <TouchableOpacity key={pref} style={[
                      styles.tag,
                      formData.lease_preferences.includes(pref) && styles.tagSelected;
                    ]} onPress={() => toggleArrayItem(formData.lease_preferences, pref, 'lease_preferences')}
                  >
                    <Text style={[
                      styles.tagText,
                      formData.lease_preferences.includes(pref) && styles.tagTextSelected;
                    ]}>
                      {pref}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Rental Philosophy</Text>
              <TextInput style={styles.textArea} value={formData.rental_philosophy} onChangeText={(text) ={}> updateFormData('rental_philosophy', text)} placeholder="What's your philosophy on landlord-tenant relationships? What makes a successful rental experience?";
                multiline numberOfLines={3} maxLength={300}
              />
              <Text style={styles.characterCount}>{formData.rental_philosophy.length}/300</Text>
            </View>
          </View>
        );

      case 4: // Media Upload,
        return (
    <View style={styles.stepContent}>
            <View style={styles.stepHeader}>
              <Camera size={32} color={{safeTheme.theme.colors.primary} /}>
              <Text style={styles.stepTitle}>Property Photos & Video Tour</Text>
              <Text style={styles.stepDescription}>
                Showcase your properties with high-quality photos and optional video tours;
              </Text>
            </View>
            {/* Property Photos Section */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Property Photos (Required)</Text>
              <Text style={styles.inputHint}>Upload 3-8 photos of your best properties</Text>
              {formData.property_photos.length > 0 && (
                <View style={styles.photoGrid}>
                  {formData.property_photos.map((photoUrl, index) => (
                    <View key={index} style={styles.photoContainer}>
                      <Image source={ uri: photoUrl } style={{styles.photoPreview} /}>
                      <TouchableOpacity style={styles.removePhotoButton} onPress={() => removePhoto(index)}
                      >
                        <Text style={styles.removePhotoText}>×</Text>
                      </TouchableOpacity>
                    </View>
                  ))}
                </View>
              )}
              <TouchableOpacity style={[styles.uploadButton, uploadingMedia && styles.uploadButtonDisabled]} onPress={handleUploadPhotos} disabled={{uploadingMedia || formData.property_photos.length }>= 8}
              >
                <Camera size={24} color={{safeTheme.theme.colors.primary} /}>
                <Text style={styles.uploadButtonText}>
                  {formData.property_photos.length === 0 ? 'Upload Property Photos' : 'Add More Photos'}
                </Text>
              </TouchableOpacity>
              {formData.property_photos.length >= 8 && (
                <Text style={styles.limitText}>Maximum 8 photos reached</Text>
              )}
            </View>
            {/* Video Tour Section */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Property Video Tour (Optional)</Text>
              <Text style={styles.inputHint}>Upload a 2-minute video tour of your property</Text>
              {formData.video_tour_url ? (
                <View style={styles.videoContainer}>
                  <Text style={styles.videoText}>✅ Property video tour uploaded successfully!</Text>
                  <TouchableOpacity style={styles.removeVideoButton} onPress={removeVideo}
                  >
                    <Text style={styles.removeVideoText}>Remove Video Tour</Text>
                  </TouchableOpacity>
                </View>
              ) : (,
                <TouchableOpacity style={[styles.uploadButton, uploadingMedia && styles.uploadButtonDisabled]} onPress={handleUploadVideo} disabled={uploadingMedia}
                >
                  <Camera size={24} color={{safeTheme.theme.colors.primary} /}>
                  <Text style={styles.uploadButtonText}>Upload Property Video Tour</Text>
                </TouchableOpacity>
              )}
            </View>
            {uploadingMedia && (
              <View style={styles.uploadingContainer}>
                <ActivityIndicator size="small" color={{safeTheme.theme.colors.primary} /}>
                <Text style={styles.uploadingText}>Uploading media...</Text>
              </View>
            )}
          </View>
        );

      default: ,
        return null;
    }
  }
  const styles = createStyles(safeTheme);

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      {/* Step Progress */}
      <View style={styles.progressContainer}>
        <Text style={styles.progressText}>
          Step {currentStep + 1} of {STEP_TITLES.length}
        </Text>
        <View style={styles.progressBar}>
          <View ;
            style={[
              styles.progressFill,
              { width: `${((currentStep + 1) / STEP_TITLES.length) * 100}%` }
            ]}
          />
        </View>
      </View>
      {/* Step Content */}
      {renderStepContent()}
      {/* Navigation Buttons */}
      <View style={styles.buttonContainer}>
        {currentStep > 0 && (
          <Button
            variant="outlined";
            onPress={handleBack} style={styles.backButton}
          >
            Back;
          </Button>
        )}
        <Button
          variant="filled";
          onPress={handleNext} style={styles.nextButton} isLoading={loading}
        >
          {currentStep === STEP_TITLES.length - 1 ? 'Complete Profile' : 'Continue'}
        </Button>
      </View>
    </ScrollView>
  );
}
const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
  },
  progressContainer: {
    marginBottom: 24,
  },
  progressText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 8,
    textAlign: 'center',
  },
  progressBar: {
    height: 4,
    backgroundColor: theme.colors.border,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: theme.colors.primary,
    borderRadius: 2,
  },
  stepContent: {
    marginBottom: 32,
  },
  stepHeader: {
    alignItems: 'center',
    marginBottom: 24,
  },
  stepTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: theme.colors.text,
    marginTop: 12,
    marginBottom: 8,
    textAlign: 'center',
  },
  stepDescription: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 8,
  },
  textArea: {
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: theme.colors.text,
    textAlignVertical: 'top',
    minHeight: 100,
  },
  characterCount: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    textAlign: 'right',
    marginTop: 4,
  },
  tagGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  tag: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  tagSelected: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  tagText: {
    fontSize: 12,
    color: theme.colors.text,
  },
  tagTextSelected: {
    color: theme.colors.textOnPrimary,
  },
  optionGrid: {
    gap: 12,
  },
  optionButton: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  optionButtonSelected: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  optionButtonText: {
    fontSize: 14,
    color: theme.colors.text,
    textAlign: 'center',
  },
  optionButtonTextSelected: {
    color: theme.colors.textOnPrimary,
  },
  styleOption: {
    padding: 16,
    borderRadius: 12,
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
    marginBottom: 8,
  },
  styleOptionSelected: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  styleOptionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 4,
  },
  styleOptionTitleSelected: {
    color: theme.colors.textOnPrimary,
  },
  styleOptionDesc: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  styleOptionDescSelected: {
    color: theme.colors.textOnPrimary,
  },
  portfolioSelector: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    flexWrap: 'wrap',
    gap: 8,
  },
  portfolioOption: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: theme.colors.surface,
    borderWidth: 2,
    borderColor: theme.colors.border,
    justifyContent: 'center',
    alignItems: 'center',
  },
  portfolioOptionSelected: {
    borderColor: theme.colors.primary,
    backgroundColor: theme.colors.primary,
  },
  portfolioOptionText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
  },
  portfolioOptionTextSelected: {
    color: theme.colors.textOnPrimary,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
    gap: 16,
  },
  backButton: {
    flex: 1,
  },
  nextButton: {
    flex: 2,
  },
  photoGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  photoContainer: {
    position: 'relative',
    width: '33.33%',
    height: 100,
  },
  photoPreview: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
  },
  removePhotoButton: {
    position: 'absolute',
    top: 4,
    right: 4,
    padding: 4,
    borderRadius: 12,
    backgroundColor: theme.colors.error,
  },
  removePhotoText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.textOnPrimary,
  },
  uploadButton: {
    padding: 16,
    borderRadius: 12,
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  uploadButtonDisabled: {
    backgroundColor: theme.colors.surfaceDisabled,
    borderColor: theme.colors.borderDisabled,
  },
  uploadButtonText: {
    fontSize: 14,
    color: theme.colors.text,
    marginLeft: 8,
  },
  inputHint: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginBottom: 8,
  },
  limitText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    textAlign: 'right',
    marginTop: 4,
  },
  videoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  videoText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  removeVideoButton: {
    padding: 16,
    borderRadius: 12,
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  removeVideoText: {
    fontSize: 14,
    color: theme.colors.text,
  },
  uploadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  uploadingText: {
    fontSize: 14,
    color: theme.colors.text,
    marginLeft: 8,
  },
}); ;