import React, { useState, useCallback } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, Alert, Platform, Image, ActivityIndicator } from 'react-native';
import { useTheme } from '@design-system';
import Input from '@components/ui/form/Input';
import { Button } from '@design-system';
import { logger } from '@utils/logger';
import { supabase } from '@lib/supabase';
import { useAuth } from '@context/AuthContext';
import { Briefcase, MapPin, DollarSign, Users, Clock, Shield, Star, Camera, User, CheckCircle, Building, Settings, Award } from 'lucide-react-native';
import * as ImagePicker from 'expo-image-picker';
import { intelligentUploader } from '@utils/intelligentUploadStrategy';

interface ServiceProviderOnboardingProps {
  onComplete: (data: any) => void,
  initialData: any,
}
interface OnboardingData {
  // Basic Info;
  bio: string,
  business_name: string,
  business_description: string,
  contact_phone: string,
  business_address: string;,
  // Business Details;
  service_categories: string[],
  years_experience: number,
  business_type: 'individual' | 'small_business' | 'company';,
  // Service Offerings;
  specializations: string[],
  service_areas: string[],
  pricing_model: 'hourly' | 'project' | 'subscription' | 'custom';,
  // Portfolio & Credentials;
  certifications: string[],
  portfolio_highlights: string,
  availability_schedule: string[];,
  // Media Upload;
  portfolio_photos: string[],
  business_video_url: string | null,
}
const STEP_TITLES = [
  'Business profile setup',
  'Service categories & expertise',
  'Service areas & pricing',
  'Portfolio & credentials',
  'Portfolio Photos & Business Video';
];

const SERVICE_CATEGORIES = [
  'Cleaning Services', 'Maintenance & Repairs', 'Moving Services', 'Interior Design',
  'Home Security', 'Landscaping', 'Property Management', 'Real Estate Services',
  'Legal Services', 'Financial Services', 'Insurance', 'Utilities Setup',
  'Home Inspection', 'Pest Control', 'HVAC Services', 'Plumbing',
  'Electrical Services', 'Painting', 'Carpentry', 'Photography';
];

const SPECIALIZATIONS = [
  'Emergency Services', 'Eco-Friendly Options', 'Budget-Friendly', 'Premium Service',
  'Same-Day Service', 'Weekend Availability', 'Licensed & Bonded', 'Insured',
  'Background Checked', 'Student Discounts', 'Senior Discounts', 'Bulk Pricing';
];

const CERTIFICATIONS = [
  'Licensed Professional', 'Bonded & Insured', 'Background Checked', 'Better Business Bureau',
  'Industry Certification', 'Safety Training', 'Environmental Compliance', 'Quality Assurance',
  'Customer Service Training', 'Professional Association Member';
];

const AVAILABILITY_OPTIONS = [
  'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday',
  'Evenings', 'Weekends', 'Emergency Calls', 'Flexible Schedule';
];

export default function ServiceProviderOnboarding({ onComplete, initialData }: ServiceProviderOnboardingProps) {
  const theme = useTheme();
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<OnboardingData>({
    bio: '',
    business_name: '',
    business_description: '',
    contact_phone: '',
    business_address: '',
    service_categories: [],
    years_experience: 1,
    business_type: 'individual',
    specializations: [],
    service_areas: [],
    pricing_model: 'hourly',
    certifications: [],
    portfolio_highlights: '',
    availability_schedule: [],
    portfolio_photos: [],
    business_video_url: null,
    ...initialData,
  });

  const [uploadingMedia, setUploadingMedia] = useState(false);

  // Safe theme access with fallbacks;
  const safeTheme = {
    colors: {
      background: theme?.colors?.background || '#FFFFFF',
      surface: theme?.colors?.surface || '#F8F9FA',
      text: theme?.colors?.text || '#333333',
      textSecondary: theme?.colors?.textSecondary || '#666666',
      textOnPrimary: theme?.colors?.textOnPrimary || '#FFFFFF',
      border: theme?.colors?.border || '#E2E8F0',
      primary: theme?.colors?.primary || '#2563EB',
      success: theme?.colors?.success || '#10B981',
      error: theme?.colors?.error || '#EF4444',
    }
  }
  const updateFormData = (field: keyof OnboardingData, value: any) => {
  setFormData(prev => ({ ...prev, [field]: value }));
  }
  const toggleArrayItem = (array: string[], item: string, field: keyof OnboardingData) => {
  const currentArray = formData[field] as string[];
    const newArray = currentArray.includes(item);
      ? currentArray.filter(i => i !== item);
      : [...currentArray, item];
    updateFormData(field, newArray);
  }
  const validateCurrentStep = ($2) => {
  switch (currentStep) {
      case 0: // Basic Info,
        if (!formData.bio || formData.bio.length < 50) {
          Alert.alert('Bio Required', 'Please write at least 50 characters about your business');
          return false;
        }
        if (!formData.business_name) {
          Alert.alert('Business Name Required', 'Please enter your business name');
          return false;
        }
        if (!formData.business_description) {
          Alert.alert('Business Description Required', 'Please describe your business');
          return false;
        }
        break;
      case 1: // Service Categories,
        if (formData.service_categories.length === 0) {
          Alert.alert('Service Categories Required', 'Please select at least one service category');
          return false;
        }
        break;
      case 2: // Service Areas,
        if (formData.service_areas.length === 0) {
          Alert.alert('Service Areas Required', 'Please specify your service areas');
          return false;
        }
        break;
      case 3: // Portfolio,
        if (!formData.portfolio_highlights) {
          Alert.alert('Portfolio Required', 'Please describe your key achievements or portfolio highlights');
          return false;
        }
        break;
      case 4: // Media Upload,
        if (formData.portfolio_photos.length === 0) {
          Alert.alert('Portfolio Photos Required', 'Please upload at least one photo showcasing your work');
          return false;
        }
        break;
    }
    return true;
  }
  const handleNext = () => {
  if (validateCurrentStep()) {
      if (currentStep < STEP_TITLES.length - 1) {
        setCurrentStep(currentStep + 1);
      } else {
        handleComplete();
      }
    }
  }
  const handleBack = () => {
  if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  }
  const handleComplete = async () => {
  setLoading(true);
    try {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }
      // Save service provider profile data;
      const profileUpdate = {
        bio: formData.bio,
        business_name: formData.business_name,
        business_description: formData.business_description,
        contact_phone: formData.contact_phone,
        business_address: formData.business_address,
        business_details: {
          service_categories: formData.service_categories,
          years_experience: formData.years_experience,
          business_type: formData.business_type,
          pricing_model: formData.pricing_model,
        },
        service_offerings: {
          specializations: formData.specializations,
          service_areas: formData.service_areas,
          availability_schedule: formData.availability_schedule,
        },
        credentials: {
          certifications: formData.certifications,
          portfolio_highlights: formData.portfolio_highlights,
        },
        portfolio_photos: formData.portfolio_photos,
        business_video_url: formData.business_video_url,
        profile_completion: 85, // Increased from 75% after adding media;
        updated_at: new Date().toISOString(),
      }
      // Update user profile;
      const { error: profileError } = await supabase.from('user_profiles')
        .update(profileUpdate);
        .eq('id', user.id)

      if (profileError) {
        throw profileError;
      }
      // Update service provider specific data;
      const serviceProviderData = {
        user_id: user.id,
        business_name: formData.business_name,
        business_description: formData.business_description,
        contact_phone: formData.contact_phone,
        business_address: formData.business_address,
        service_categories: formData.service_categories,
        service_areas: formData.service_areas,
        years_experience: formData.years_experience,
        business_type: formData.business_type,
        pricing_model: formData.pricing_model,
        specializations: formData.specializations,
        certifications: formData.certifications,
        availability_schedule: formData.availability_schedule,
        portfolio_photos: formData.portfolio_photos,
        business_video_url: formData.business_video_url,
        is_verified: false,
        verification_status: 'pending',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }
      const { error: providerError } = await supabase.from('service_providers')
        .upsert(serviceProviderData, {
          onConflict: 'user_id',
          ignoreDuplicates: false,
        });

      if (providerError) {
        throw providerError;
      }
      logger.info('Service provider profile creation completed', 'ServiceProviderOnboarding', {
        userId: user.id,
        businessName: formData.business_name,
        serviceCategories: formData.service_categories,
        mediaUploaded: {
          photos: formData.portfolio_photos.length,
          video: formData.business_video_url ? 1 : 0,
        },
      });

      onComplete(profileUpdate);

    } catch (error) {
      logger.error('Failed to save service provider profile', 'ServiceProviderOnboarding', {}, error as Error);
      Alert.alert('Error', 'Failed to save your profile. Please try again.');
    } finally {
      setLoading(false);
    }
  }
  // Media upload functions;
  const handleUploadPhotos = async () => {
  try {
      setUploadingMedia(true);

      // Request permissions;
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (!permissionResult.granted) {
        Alert.alert('Permission Required', 'We need access to your photos to upload portfolio pictures.');
        return null;
      }
      // Launch image picker;
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'],
        allowsMultiple: true,
        quality: 0.7,
        allowsEditing: false,
        exif: false,
      });

      if (result.canceled || !result.assets?.length) {
        return null;
      }
      // Upload images using intelligent uploader;
      const uploadPromises = result.assets.map(async (asset) => {
  const fileName = `portfolio-${user?.id}-${Date.now()}-${Math.floor(Math.random() * 1000)}.jpg`;
        const uploadResult = await intelligentUploader.smartUpload(asset.uri, {
          bucket: 'service-portfolio',
          path: `${user?.id}/${fileName}`,
          contentType: 'image/jpeg',
          enableOptimization: true,
        });

        if (uploadResult.success && uploadResult.url) {
          return uploadResult.url;
        }
        throw new Error(uploadResult.error || 'Upload failed');
      });

      const uploadedUrls = await Promise.all(uploadPromises);
      ;
      // Update form data;
      updateFormData('portfolio_photos', [...formData.portfolio_photos, ...uploadedUrls]);

      Alert.alert('Success', `${uploadedUrls.length} portfolio photo(s) uploaded successfully!`);

    } catch (error) {
      logger.error('Failed to upload portfolio photos', 'ServiceProviderOnboarding', {}, error as Error);
      Alert.alert('Error', 'Failed to upload portfolio photos. Please try again.');
    } finally {
      setUploadingMedia(false);
    }
  }
  const handleUploadVideo = async () => {
  try {
      setUploadingMedia(true);

      // Request permissions;
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (!permissionResult.granted) {
        Alert.alert('Permission Required', 'We need access to your media library to upload business video.');
        return null;
      }
      // Launch video picker;
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['videos'],
        allowsEditing: true,
        aspect: [16, 9],
        quality: 0.8,
        videoMaxDuration: 90, // 90 seconds max for business videos;
      });

      if (result.canceled || !result.assets?.length) {
        return null;
      }
      // Upload video using intelligent uploader;
      const asset = result.assets[0];
      const fileName = `business-video-${user?.id}-${Date.now()}.mp4`;
      const uploadResult = await intelligentUploader.smartUpload(asset.uri, {
        bucket: 'business-videos',
        path: `${user?.id}/${fileName}`,
        contentType: 'video/mp4',
        enableOptimization: true,
      });

      if (uploadResult.success && uploadResult.url) {
        updateFormData('business_video_url', uploadResult.url);
        Alert.alert('Success', 'Business video uploaded successfully!');
      } else {
        throw new Error(uploadResult.error || 'Upload failed');
      }
    } catch (error) {
      logger.error('Failed to upload business video', 'ServiceProviderOnboarding', {}, error as Error);
      Alert.alert('Error', 'Failed to upload business video. Please try again.');
    } finally {
      setUploadingMedia(false);
    }
  }
  const removePhoto = (index: number) => {
  const updatedPhotos = formData.portfolio_photos.filter((_, i) => i !== index);
    updateFormData('portfolio_photos', updatedPhotos);
  }
  const removeVideo = () => {
  updateFormData('business_video_url', null);
  }
  const renderStepContent = () => {
  switch (currentStep) {
      case 0: // Basic Info,
        return (
    <View style={styles.stepContent}>
            <View style={styles.stepHeader}>
              <Briefcase size={32} color={{safeTheme.theme.colors.primary} /}>
              <Text style={styles.stepTitle}>Business profile setup</Text>
              <Text style={styles.stepDescription}>
                Tell us about your business and what services you provide;
              </Text>
            </View>
            <Input
              label="Business Name";
              value={formData.business_name} onChangeText={(text) => updateFormData('business_name', text)} placeholder="Your business or professional name";
            />
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Business Description</Text>
              <TextInput style={styles.textArea} value={formData.business_description} onChangeText={(text) ={}> updateFormData('business_description', text)} placeholder="Brief description of your business and main services...";
                multiline numberOfLines={3} maxLength={300}
              />
              <Text style={styles.characterCount}>{formData.business_description.length}/300</Text>
            </View>
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>About Your Business (Bio)</Text>
              <TextInput style={styles.textArea} value={formData.bio} onChangeText={(text) ={}> updateFormData('bio', text)} placeholder="Tell potential clients about your experience, approach, and what makes your business special...";
                multiline numberOfLines={4} maxLength={500}
              />
              <Text style={styles.characterCount}>{formData.bio.length}/500</Text>
            </View>
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Business Type</Text>
              <View style={styles.optionGrid}>
                {[
                  { key: 'individual', label: 'Individual/Freelancer' },
                  { key: 'small_business', label: 'Small Business' },
                  { key: 'company', label: 'Company/Corporation' },
                ].map((option) => (
                  <TouchableOpacity key={option.key} style={[
                      styles.optionButton,
                      formData.business_type === option.key && styles.optionButtonSelected;
                    ]} onPress={() => updateFormData('business_type', option.key)}
                  >
                    <Text style={[
                      styles.optionButtonText,
                      formData.business_type === option.key && styles.optionButtonTextSelected;
                    ]}>
                      {option.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </View>
        );

      case 1: // Service Categories,
        return (
    <View style={styles.stepContent}>
            <View style={styles.stepHeader}>
              <Settings size={32} color={{safeTheme.theme.colors.primary} /}>
              <Text style={styles.stepTitle}>Service categories & expertise</Text>
              <Text style={styles.stepDescription}>
                Select your service categories and areas of expertise;
              </Text>
            </View>
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Service Categories</Text>
              <View style={styles.tagGrid}>
                {SERVICE_CATEGORIES.map((category) => (
                  <TouchableOpacity key={category} style={[
                      styles.tag,
                      formData.service_categories.includes(category) && styles.tagSelected;
                    ]} onPress={() => toggleArrayItem(formData.service_categories, category, 'service_categories')}
                  >
                    <Text style={[
                      styles.tagText,
                      formData.service_categories.includes(category) && styles.tagTextSelected;
                    ]}>
                      {category}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Years of Experience</Text>
              <View style={styles.experienceSelector}>
                {[1, 2, 3, 4, 5, '6+'].map((years) => (
                  <TouchableOpacity key={years} style={[
                      styles.experienceOption,
                      formData.years_experience === (typeof years === 'string' ? 6 : years) && styles.experienceOptionSelected,
                    ]} onPress={() => updateFormData('years_experience', typeof years === 'string' ? 6 : years)}
                  >
                    <Text style={[
                      styles.experienceOptionText,
                      formData.years_experience === (typeof years === 'string' ? 6 : years) && styles.experienceOptionTextSelected,
                    ]}>
                      {years}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Specializations</Text>
              <View style={styles.tagGrid}>
                {SPECIALIZATIONS.map((spec) => (
                  <TouchableOpacity key={spec} style={[
                      styles.tag,
                      formData.specializations.includes(spec) && styles.tagSelected;
                    ]} onPress={() => toggleArrayItem(formData.specializations, spec, 'specializations')}
                  >
                    <Text style={[
                      styles.tagText,
                      formData.specializations.includes(spec) && styles.tagTextSelected;
                    ]}>
                      {spec}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </View>
        );

      case 2: // Service Areas & Pricing,
        return (
    <View style={styles.stepContent}>
            <View style={styles.stepHeader}>
              <MapPin size={32} color={{safeTheme.theme.colors.primary} /}>
              <Text style={styles.stepTitle}>Service areas & pricing</Text>
              <Text style={styles.stepDescription}>
                Define where you serve and your pricing approach;
              </Text>
            </View>
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Service Areas</Text>
              <Text style={styles.inputDescription}>
                Enter cities, neighborhoods, or regions you serve (one per line);
              </Text>
              <TextInput style={styles.textArea} value={formData.service_areas.join('\n')} onChangeText={(text) ={}> updateFormData('service_areas', text.split('\n').filter(area => area.trim()))} placeholder="Downtown\nMidtown\nSuburbs\nWithin 25 miles of city center";
                multiline;
                numberOfLines={4}
              />
            </View>
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Pricing Model</Text>
              <View style={styles.optionGrid}>
                {[
                  { key: 'hourly', label: 'Hourly Rate', desc: 'Charge by the hour' },
                  { key: 'project', label: 'Project-Based', desc: 'Fixed price per project' },
                  { key: 'subscription', label: 'Subscription', desc: 'Monthly/recurring service' },
                  { key: 'custom', label: 'Custom Quote', desc: 'Quote based on requirements' },
                ].map((option) => (
                  <TouchableOpacity key={option.key} style={[
                      styles.pricingOption,
                      formData.pricing_model === option.key && styles.pricingOptionSelected;
                    ]} onPress={() => updateFormData('pricing_model', option.key)}
                  >
                    <Text style={[
                      styles.pricingOptionTitle,
                      formData.pricing_model === option.key && styles.pricingOptionTitleSelected;
                    ]}>
                      {option.label}
                    </Text>
                    <Text style={[
                      styles.pricingOptionDesc,
                      formData.pricing_model === option.key && styles.pricingOptionDescSelected;
                    ]}>
                      {option.desc}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Availability</Text>
              <View style={styles.tagGrid}>
                {AVAILABILITY_OPTIONS.map((time) => (
                  <TouchableOpacity key={time} style={[
                      styles.tag,
                      formData.availability_schedule.includes(time) && styles.tagSelected;
                    ]} onPress={() => toggleArrayItem(formData.availability_schedule, time, 'availability_schedule')}
                  >
                    <Text style={[
                      styles.tagText,
                      formData.availability_schedule.includes(time) && styles.tagTextSelected;
                    ]}>
                      {time}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </View>
        );

      case 3: // Portfolio & Credentials,
        return (
    <View style={styles.stepContent}>
            <View style={styles.stepHeader}>
              <Award size={32} color={{safeTheme.theme.colors.primary} /}>
              <Text style={styles.stepTitle}>Portfolio & credentials</Text>
              <Text style={styles.stepDescription}>
                Showcase your work and professional credentials;
              </Text>
            </View>
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Portfolio Highlights</Text>
              <TextInput style={styles.textArea} value={formData.portfolio_highlights} onChangeText={(text) ={}> updateFormData('portfolio_highlights', text)} placeholder="Describe your best work, notable projects, client testimonials, or achievements...";
                multiline numberOfLines={4} maxLength={500}
              />
              <Text style={styles.characterCount}>{formData.portfolio_highlights.length}/500</Text>
            </View>
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Certifications & Credentials</Text>
              <View style={styles.tagGrid}>
                {CERTIFICATIONS.map((cert) => (
                  <TouchableOpacity key={cert} style={[
                      styles.tag,
                      formData.certifications.includes(cert) && styles.tagSelected;
                    ]} onPress={() => toggleArrayItem(formData.certifications, cert, 'certifications')}
                  >
                    <Text style={[
                      styles.tagText,
                      formData.certifications.includes(cert) && styles.tagTextSelected;
                    ]}>
                      {cert}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </View>
        );

      case 4: // Media Upload,
        return (
    <View style={styles.stepContent}>
            <View style={styles.stepHeader}>
              <Camera size={32} color={{safeTheme.theme.colors.primary} /}>
              <Text style={styles.stepTitle}>Portfolio Photos & Business Video</Text>
              <Text style={styles.stepDescription}>
                Showcase your work and business with high-quality photos and video;
              </Text>
            </View>
            {/* Portfolio Photos Section */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Portfolio Photos (Required)</Text>
              <Text style={styles.inputHint}>Upload 3-10 photos showcasing your best work</Text>
              {formData.portfolio_photos.length > 0 && (
                <View style={styles.photoGrid}>
                  {formData.portfolio_photos.map((photoUrl, index) => (
                    <View key={index} style={styles.photoContainer}>
                      <Image source={ uri: photoUrl } style={{styles.photoPreview} /}>
                      <TouchableOpacity style={styles.removePhotoButton} onPress={() => removePhoto(index)}
                      >
                        <Text style={styles.removePhotoText}>×</Text>
                      </TouchableOpacity>
                    </View>
                  ))}
                </View>
              )}
              <TouchableOpacity style={[styles.uploadButton, uploadingMedia && styles.uploadButtonDisabled]} onPress={handleUploadPhotos} disabled={{uploadingMedia || formData.portfolio_photos.length }>= 10}
              >
                <Camera size={24} color={{safeTheme.theme.colors.primary} /}>
                <Text style={styles.uploadButtonText}>
                  {formData.portfolio_photos.length === 0 ? 'Upload Portfolio Photos' : 'Add More Photos'}
                </Text>
              </TouchableOpacity>
              {formData.portfolio_photos.length >= 10 && (
                <Text style={styles.limitText}>Maximum 10 photos reached</Text>
              )}
            </View>
            {/* Business Video Section */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Business Video (Optional)</Text>
              <Text style={styles.inputHint}>Upload a 90-second video introducing your business</Text>
              {formData.business_video_url ? (
                <View style={styles.videoContainer}>
                  <Text style={styles.videoText}>✅ Business video uploaded successfully!</Text>
                  <TouchableOpacity style={styles.removeVideoButton} onPress={removeVideo}
                  >
                    <Text style={styles.removeVideoText}>Remove Business Video</Text>
                  </TouchableOpacity>
                </View>
              ) : (,
                <TouchableOpacity style={[styles.uploadButton, uploadingMedia && styles.uploadButtonDisabled]} onPress={handleUploadVideo} disabled={uploadingMedia}
                >
                  <Camera size={24} color={{safeTheme.theme.colors.primary} /}>
                  <Text style={styles.uploadButtonText}>Upload Business Video</Text>
                </TouchableOpacity>
              )}
            </View>
            {uploadingMedia && (
              <View style={styles.uploadingContainer}>
                <ActivityIndicator size="small" color={{safeTheme.theme.colors.primary} /}>
                <Text style={styles.uploadingText}>Uploading media...</Text>
              </View>
            )}
          </View>
        );

      default: ,
        return null;
    }
  }
  const styles = createStyles(safeTheme);

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      {/* Step Progress */}
      <View style={styles.progressContainer}>
        <Text style={styles.progressText}>
          Step {currentStep + 1} of {STEP_TITLES.length}
        </Text>
        <View style={styles.progressBar}>
          <View ;
            style={[
              styles.progressFill,
              { width: `${((currentStep + 1) / STEP_TITLES.length) * 100}%` }
            ]}
          />
        </View>
      </View>
      {/* Step Content */}
      {renderStepContent()}
      {/* Navigation Buttons */}
      <View style={styles.buttonContainer}>
        {currentStep > 0 && (
          <Button
            variant="outlined";
            onPress={handleBack} style={styles.backButton}
          >
            Back;
          </Button>
        )}
        <Button
          variant="filled";
          onPress={handleNext} style={styles.nextButton} isLoading={loading}
        >
          {currentStep === STEP_TITLES.length - 1 ? 'Complete Profile' : 'Continue'}
        </Button>
      </View>
    </ScrollView>
  );
}
const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
  },
  progressContainer: {
    marginBottom: 24,
  },
  progressText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 8,
    textAlign: 'center',
  },
  progressBar: {
    height: 4,
    backgroundColor: theme.colors.border,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: theme.colors.primary,
    borderRadius: 2,
  },
  stepContent: {
    marginBottom: 32,
  },
  stepHeader: {
    alignItems: 'center',
    marginBottom: 24,
  },
  stepTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: theme.colors.text,
    marginTop: 12,
    marginBottom: 8,
    textAlign: 'center',
  },
  stepDescription: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 8,
  },
  inputDescription: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginBottom: 8,
  },
  textArea: {
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: theme.colors.text,
    textAlignVertical: 'top',
    minHeight: 100,
  },
  characterCount: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    textAlign: 'right',
    marginTop: 4,
  },
  tagGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  tag: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  tagSelected: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  tagText: {
    fontSize: 12,
    color: theme.colors.text,
  },
  tagTextSelected: {
    color: theme.colors.textOnPrimary,
  },
  optionGrid: {
    gap: 12,
  },
  optionButton: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  optionButtonSelected: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  optionButtonText: {
    fontSize: 14,
    color: theme.colors.text,
    textAlign: 'center',
  },
  optionButtonTextSelected: {
    color: theme.colors.textOnPrimary,
  },
  experienceSelector: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    flexWrap: 'wrap',
    gap: 8,
  },
  experienceOption: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: theme.colors.surface,
    borderWidth: 2,
    borderColor: theme.colors.border,
    justifyContent: 'center',
    alignItems: 'center',
  },
  experienceOptionSelected: {
    borderColor: theme.colors.primary,
    backgroundColor: theme.colors.primary,
  },
  experienceOptionText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
  },
  experienceOptionTextSelected: {
    color: theme.colors.textOnPrimary,
  },
  pricingOption: {
    padding: 16,
    borderRadius: 12,
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
    marginBottom: 8,
  },
  pricingOptionSelected: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  pricingOptionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 4,
  },
  pricingOptionTitleSelected: {
    color: theme.colors.textOnPrimary,
  },
  pricingOptionDesc: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  pricingOptionDescSelected: {
    color: 'rgba(255, 255, 255, 0.8)',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
    gap: 16,
  },
  backButton: {
    flex: 1,
  },
  nextButton: {
    flex: 2,
  },
  photoGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  photoContainer: {
    position: 'relative',
    width: '33.33%',
    aspectRatio: 1,
  },
  photoPreview: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
  },
  removePhotoButton: {
    position: 'absolute',
    top: 4,
    right: 4,
    padding: 4,
    borderRadius: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  removePhotoText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.textOnPrimary,
  },
  uploadButton: {
    padding: 16,
    borderRadius: 12,
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
    marginTop: 8,
  },
  uploadButtonDisabled: {
    backgroundColor: theme.colors.surfaceDisabled,
    borderColor: theme.colors.borderDisabled,
  },
  uploadButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    textAlign: 'center',
  },
  limitText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginTop: 8,
  },
  videoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  videoText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
  },
  removeVideoButton: {
    padding: 16,
    borderRadius: 12,
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  removeVideoText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
  },
  uploadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  uploadingText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginLeft: 8,
  },
  inputHint: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginBottom: 8,
  },
}); ;