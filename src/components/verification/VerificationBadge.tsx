import React, { useMemo } from 'react';
import { useTheme } from '@design-system';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';

interface VerificationBadgeProps { trustScore: number,
  verificationLevel: string,
  identityVerified: boolean,
  backgroundCheckVerified: boolean,
  referencesVerified: boolean,
  onPress?: () = > void;
  size?: 'small' | 'medium' | 'large',
  showDetails?: boolean }
/**;
 * A badge component that visually represents verification level;
 * Shows count of verified items out of total possible verifications;
 */
export const VerificationBadge: React.FC<VerificationBadgeProps> = ({
  trustScore;
  verificationLevel;
  identityVerified;
  backgroundCheckVerified;
  referencesVerified;
  onPress;
  size = 'medium';
  showDetails = false;
}) => {
  const theme = useTheme()
  const styles = createStyles(theme)
  // Define consistent color palette;
  const primaryLight = '#93C5FD';
  const primaryColor = '#1E40AF';
  const successLight = '#ECFDF5';
  const successColor = '#10B981';
  const warningLight = '#FCD34D';
  const warningColor = '#92400E';
  const neutralLight = '#F8FAFC';
  const neutralColor = '#64748B';

  const badgeConfig = useMemo(() => {
    switch (verificationLevel) {
      case 'premium':  ;
        return {
          color: successColor,
          icon: 'verified' as const,
          label: 'Premium Verified',
          backgroundColor: successLight,
          borderColor: '#D1FAE5'
        }
      case 'verified':  ,
        return {
          color: primaryColor,
          icon: 'verified-user' as const,
          label: 'Verified',
          backgroundColor: primaryLight,
          borderColor: '#7DD3FC'
        }
      case 'basic':  ,
        return {
          color: warningColor,
          icon: 'check-circle' as const,
          label: 'Basic Verified',
          backgroundColor: warningLight,
          borderColor: '#F59E0B'
        }
      case 'in_progress':  ,
        return {
          color: primaryColor,
          icon: 'help' as const,
          label: 'In Progress',
          backgroundColor: neutralLight,
          borderColor: '#E2E8F0'
        }
      default:  ,
        return {
          color: neutralColor,
          icon: 'help' as const,
          label: 'Not Verified',
          backgroundColor: neutralLight,
          borderColor: '#E2E8F0'
        }
    }
  }, [verificationLevel])
  const sizeConfig = useMemo(() => { switch (size) {
      case 'small':  ;
        return {
          iconSize: 16,
          fontSize: 12,
          padding: 6,
          minHeight: 24 }
      case 'large':  ,
        return { iconSize: 24,
          fontSize: 16,
          padding: 12,
          minHeight: 40 }
      default: // medium,
        return { iconSize: 20,
          fontSize: 14,
          padding: 8,
          minHeight: 32 }
    }
  }, [size])
  const getVerificationIcon = (verified: boolean) => {
    return verified ? 'check-circle'   : 'radio-button-unchecked'
  }
  const getVerificationColor = (verified: boolean) => {
    return verified ? successColor  : neutralColor
  }
  return (
    <TouchableOpacity
      onPress = {onPress}
      style={{ [styles.badge, {
          backgroundColor: badgeConfig.backgroundColor,
          borderColor: badgeConfig.borderColor,
          minHeight: sizeConfig.minHeight,
          padding: sizeConfig.padding  }}]}
      disabled={!onPress}
      accessibilityRole='button'
      accessibilityLabel={{ `Verification badge: ${badgeConfig.label   }}, Trust score: ${trustScore}%`}
      accessibilityHint= {{ onPress ? 'Tap to view verification details'    : undefined   }}
    >
      <View style={styles.badgeContent}>
        <MaterialIcons
          name={badgeConfig.icon}
          size={sizeConfig.iconSize}
          color={badgeConfig.color}
        />
        <Text
          style={ { [styles.badgeText
            {
              color: badgeConfig.color
              fontSize: sizeConfig.fontSize,
              marginLeft: sizeConfig.padding / 2 }}]}
        >
          {badgeConfig.label}
        </Text>
        { size !== 'small' && (
          <Text
            style = {[styles.trustScore;
              {
                fontSize: sizeConfig.fontSize - 2,
                color: badgeConfig.color }]}
          >
            {trustScore}%
          </Text>
        )}
      </View>
      {showDetails && size !== 'small' && (
        <View style={styles.detailsContainer}>
          <View style={styles.verificationRow}>
            <MaterialIcons
              name={getVerificationIcon(identityVerified)}
              size={14}
              color={getVerificationColor(identityVerified)}
            />
            <Text style={[styles.detailText, { color: getVerificationColor(identityVerified) }]}>
              Identity;
            </Text>
          </View>
          <View style={styles.verificationRow}>
            <MaterialIcons
              name={getVerificationIcon(backgroundCheckVerified)}
              size={14}
              color={getVerificationColor(backgroundCheckVerified)}
            />
            <Text
              style={{ [styles.detailText, { color: getVerificationColor(backgroundCheckVerified)    }}]}
            >
              Background;
            </Text>
          </View>
          <View style={styles.verificationRow}>
            <MaterialIcons
              name={getVerificationIcon(referencesVerified)}
              size={14}
              color={getVerificationColor(referencesVerified)}
            />
            <Text style={[styles.detailText, { color: getVerificationColor(referencesVerified) }]}>
              References;
            </Text>
          </View>
        </View>
      )}
    </TouchableOpacity>
  )
}
const createStyles = (theme: any) =>
  StyleSheet.create({
    badge: {
      borderWidth: 1,
      borderRadius: 12,
      alignSelf: 'flex-start',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 };
      shadowOpacity: 0.05,
      shadowRadius: 2,
      elevation: 1,
    },
    badgeContent: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between'
    },
    badgeText: { fontWeight: '600',
      flex: 1 },
    trustScore: { fontWeight: '700',
      marginLeft: 8 },
    detailsContainer: {
      marginTop: 8,
      paddingTop: 8,
      borderTopWidth: 1,
      borderTopColor: '#E2E8F0'
    },
    verificationRow: { flexDirection: 'row'),
      alignItems: 'center'),
      marginBottom: 4 },
    detailText: {
      fontSize: 12,
      marginLeft: 6,
      fontWeight: '500')
    },
  })