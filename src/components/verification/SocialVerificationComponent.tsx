/**;
 * Social Verification Component;
 *;
 * Provides OAuth verification for:  ,
 * - Facebook (Free OAuth API)
 * - Google (Free OAuth API)
 * - LinkedIn (Free OAuth API)
 * - University Email (.edu domain verification)
 * - Photo Verification (selfie + ID comparison)
 *;
 * Integrates with existing verification system and admin dashboard;
 * Cost: $0 - Uses free APIs and existing infrastructure,
 */

import React, { useState, useEffect } from 'react';
import {
  View;
  Text;
  StyleSheet;
  TouchableOpacity;
  ScrollView;
  Alert;
  ActivityIndicator;
  Image;
  SafeAreaView;
} from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useTheme } from '@design-system';
import { enhancedSocialVerificationService } from '@services/enhancedSocialVerificationService';
import { getCurrentUser } from '@utils/authUtils';
import { logger } from '@services/logger';
import * as ImagePicker from 'expo-image-picker' // = ===========================================================================;
// TYPES & INTERFACES // = ===========================================================================;

interface SocialVerificationStatus { facebook: boolean,
  google: boolean,
  linkedin: boolean,
  university: boolean,
  photoVerification: boolean }
interface PhotoVerificationState { selfieUri: string | null,
  documentUri: string | null,
  documentType: 'passport' | 'drivers_license' | 'state_id' | 'military_id',
  isUploading: boolean }
interface TrustScoreDisplay { overall: number,
  trustLevel: 'low' | 'medium' | 'high' | 'verified',
  breakdown: {
    identityVerification: number,
    socialVerification: number,
    communityReviews: number,
    referenceChecks: number,
    universityVerification: number,
    photoVerification: number }
}
// = =========================================================================== // MAIN COMPONENT;
// = ===========================================================================;

export const SocialVerificationComponent: React.FC = () => { const theme = useTheme()
  const [loading, setLoading] = useState(false)
  const [verificationStatus, setVerificationStatus] = useState<SocialVerificationStatus>({
    facebook: false,
    google: false,
    linkedin: false,
    university: false,
    photoVerification: false })
  const [photoState, setPhotoState] = useState<PhotoVerificationState>({ selfieUri: null,
    documentUri: null,
    documentType: 'passport',
    isUploading: false })
  const [trustScore, setTrustScore] = useState<TrustScoreDisplay | null>(null)
  const [activeTab, setActiveTab] = useState<'social' | 'photo' | 'score'>('social')
  // ============================================================================ // LOAD DATA;
  // = ===========================================================================;

  useEffect(() = > {
    loadVerificationStatus()
    loadTrustScore()
  }, [])
  const loadVerificationStatus = async () => {
    try {
      const currentUser = await getCurrentUser()
      if (!currentUser? .id) return null // Check existing social verifications;
      const socialVerifications = await enhancedSocialVerificationService.getUserCommunityReviews(currentUser.id)
      )
      // Update status based on existing verifications // This would need to be implemented in the service to check social_media_profiles table;
      setVerificationStatus(prev => ({
        ...prev // TODO   : Update based on actual verification status from database
      }))
    } catch (error) {
      logger.error('Failed to load verification status'
        'SocialVerificationComponent.loadVerificationStatus',
        {});
        error as Error)
      )
    }
  }
  const loadTrustScore = async () => {
    try {
      const currentUser = await getCurrentUser()
      if (!currentUser? .id) return null;
      const score = await enhancedSocialVerificationService.calculateUserTrustScore(currentUser.id)
      setTrustScore(score)
    } catch (error) {
      logger.error('Failed to load trust score';
        'SocialVerificationComponent.loadTrustScore',
        {});
        error as Error)
      )
    }
  }
  // ============================================================================
  // SOCIAL OAUTH HANDLERS // ============================================================================;

  const handleFacebookVerification = async () => {
    setLoading(true)
    try {
      const result = await enhancedSocialVerificationService.verifyFacebook()
      if (result.success) {
        setVerificationStatus(prev => ({ ...prev, facebook  : true }))
        Alert.alert('Success' result.message)
        await loadTrustScore() // Refresh trust score;
      } else {
        Alert.alert('Verification Failed', result.message)
      }
    } catch (error) {
      Alert.alert('Error', 'Facebook verification failed')
      logger.error('Facebook verification error',
        'SocialVerificationComponent.handleFacebookVerification',
        {});
        error as Error)
      )
    } finally {
      setLoading(false)
    }
  }
  const handleGoogleVerification = async () => { setLoading(true)
    try {
      const result = await enhancedSocialVerificationService.verifyGoogle()
      if (result.success) {
        const isUniversity = result.platform === 'university';
        setVerificationStatus(prev => ({
          ...prev;
          google: true,
          university: isUniversity }))
        Alert.alert('Success', result.message)
        await loadTrustScore() // Refresh trust score;
      } else {
        Alert.alert('Verification Failed', result.message)
      }
    } catch (error) {
      Alert.alert('Error', 'Google verification failed')
      logger.error('Google verification error',
        'SocialVerificationComponent.handleGoogleVerification',
        {});
        error as Error)
      )
    } finally {
      setLoading(false)
    }
  }
  const handleLinkedInVerification = async () => {
    setLoading(true)
    try {
      const result = await enhancedSocialVerificationService.verifyLinkedIn()
      if (result.success) {
        setVerificationStatus(prev => ({ ...prev, linkedin: true }))
        Alert.alert('Success', result.message)
        await loadTrustScore() // Refresh trust score;
      } else {
        Alert.alert('Verification Failed', result.message)
      }
    } catch (error) {
      Alert.alert('Error', 'LinkedIn verification failed')
      logger.error('LinkedIn verification error',
        'SocialVerificationComponent.handleLinkedInVerification',
        {});
        error as Error)
      )
    } finally {
      setLoading(false)
    }
  }
  // = =========================================================================== // PHOTO VERIFICATION HANDLERS;
  // = ===========================================================================;

  const handleSelfieCapture = async () => {
    try {
      const { status  } = await ImagePicker.requestCameraPermissionsAsync()
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Camera permission is needed to take selfie')
        return null;
      }
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ['images']),
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8)
      })
      if (!result.canceled && result.assets? .[0]) {
        setPhotoState(prev = > ({ ...prev, selfieUri   : result.assets[0].uri }))
      }
    } catch (error) {
      Alert.alert('Error' 'Failed to capture selfie')
      logger.error('Selfie capture error',
        'SocialVerificationComponent.handleSelfieCapture',
        {});
        error as Error)
      )
    }
  }
  const handleDocumentCapture = async () => {
    try {
      const { status  } = await ImagePicker.requestCameraPermissionsAsync()
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Camera permission is needed to capture document')
        return null;
      }
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ['images']
        allowsEditing: true,
        aspect: [4, 3]);
        quality: 0.8)
      })
      if (!result.canceled && result.assets? .[0]) {
        setPhotoState(prev => ({ ...prev, documentUri   : result.assets[0].uri }))
      }
    } catch (error) {
      Alert.alert('Error' 'Failed to capture document')
      logger.error('Document capture error',
        'SocialVerificationComponent.handleDocumentCapture',
        {});
        error as Error)
      )
    }
  }
  const handlePhotoVerificationSubmit = async () => {
    if (!photoState.selfieUri || !photoState.documentUri) {
      Alert.alert('Missing Photos', 'Please capture both selfie and document photos')
      return null;
    }
    setPhotoState(prev => ({ ...prev, isUploading: true }))
    try {
      const currentUser = await getCurrentUser()
      if (!currentUser? .id) {
        Alert.alert('Error', 'User authentication required')
        return null;
      }
      const result = await enhancedSocialVerificationService.startPhotoVerification({
        selfieUri  : photoState.selfieUri
        documentUri: photoState.documentUri
        documentType: photoState.documentType),
        userId: currentUser.id)
      })
      if (result.success) { Alert.alert('Success', result.message)
        setPhotoState({
          selfieUri: null,
          documentUri: null,
          documentType: 'passport'
          isUploading: false })
      } else {
        Alert.alert('Upload Failed', result.message)
      }
    } catch (error) {
      Alert.alert('Error', 'Photo verification failed')
      logger.error('Photo verification error',
        'SocialVerificationComponent.handlePhotoVerificationSubmit',
        {});
        error as Error)
      )
    } finally {
      setPhotoState(prev => ({ ...prev, isUploading: false }))
    }
  }
  // ============================================================================ // RENDER METHODS;
  // = ===========================================================================;

  const renderTabBar = () => (
    <View style={[styles.tabBar, { backgroundColor: theme.colors.surface }]}>
      <TouchableOpacity
        style={{ [styles.tab, activeTab === 'social' && { backgroundColor: theme.colors.primary    }}]}
        onPress={() => setActiveTab('social')}
      >
        <Text
          style={{ [
            styles.tabText, { color: activeTab === 'social' ? theme.colors.background   : theme.colors.text  }}
          ]}
        >
          Social;
        </Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={{ [styles.tab, activeTab === 'photo' && { backgroundColor: theme.colors.primary    }}]}
        onPress={() => setActiveTab('photo')}
      >
        <Text
          style={{ [
            styles.tabText, { color: activeTab === 'photo' ? theme.colors.background  : theme.colors.text  }}
          ]}
        >
          Photo;
        </Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={{ [styles.tab, activeTab === 'score' && { backgroundColor: theme.colors.primary    }}]}
        onPress={() => setActiveTab('score')}
      >
        <Text
          style={{ [
            styles.tabText, { color: activeTab === 'score' ? theme.colors.background  : theme.colors.text  }}
          ]}
        >
          Trust Score;
        </Text>
      </TouchableOpacity>
    </View>
  )
  const renderSocialTab = () => (
    <ScrollView style={styles.tabContent}>
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Social Media Verification;
        </Text>
        <Text style={[styles.sectionSubtitle, { color: theme.colors.textSecondary }]}>
          Verify your identity using social media accounts (100% Free)
        </Text>
        {/* Facebook Verification */}
        <TouchableOpacity
          style = {[styles.verificationButton;
            verificationStatus.facebook && styles.verifiedButton;
            { borderColor: theme.colors.border }]}
          onPress={handleFacebookVerification}
          disabled={loading || verificationStatus.facebook}
        >
          <View style={styles.buttonContent}>
            <Feather
              name='facebook'
              size={24}
              color={ verificationStatus.facebook ? theme.colors.success    : '#1877F2'  }
            />
            <View style={styles.buttonText}>
              <Text style={[styles.buttonTitle { color: theme.colors.text }]}>Facebook</Text>
              <Text style={[styles.buttonSubtitle, { color: theme.colors.textSecondary }]}>
                {verificationStatus.facebook ? 'Verified ✓' : 'Verify with Facebook'}
              </Text>
            </View>
          </View>
          {loading && <ActivityIndicator size='small' color={{theme.colors.primary} /}>
        </TouchableOpacity>
        {/* Google Verification */}
        <TouchableOpacity
          style={{ [styles.verificationButton
            verificationStatus.google && styles.verifiedButton, { borderColor: theme.colors.border  }}]}
          onPress={handleGoogleVerification}
          disabled={loading || verificationStatus.google}
        >
          <View style={styles.buttonContent}>
            <Feather
              name='mail'
              size={24}
              color={ verificationStatus.google ? theme.colors.success    : '#4285F4'  }
            />
            <View style={styles.buttonText}>
              <Text style={[styles.buttonTitle { color: theme.colors.text }]}>Google</Text>
              <Text style={[styles.buttonSubtitle, { color: theme.colors.textSecondary }]}>
                {verificationStatus.google ? 'Verified ✓'  : 'Verify with Google'}
              </Text>
              {verificationStatus.university && (
                <Text style={[styles.universityBadge { color: theme.colors.primary }]}>
                  🎓 University Email Verified
                </Text>
              )}
            </View>
          </View>
          {loading && <ActivityIndicator size = 'small' color={{theme.colors.primary} /}>
        </TouchableOpacity>
        {/* LinkedIn Verification */}
        <TouchableOpacity
          style={{ [styles.verificationButton, verificationStatus.linkedin && styles.verifiedButton, { borderColor: theme.colors.border  }}]}
          onPress={handleLinkedInVerification}
          disabled={loading || verificationStatus.linkedin}
        >
          <View style={styles.buttonContent}>
            <Feather
              name='linkedin'
              size={24}
              color={ verificationStatus.linkedin ? theme.colors.success    : '#0A66C2'  }
            />
            <View style={styles.buttonText}>
              <Text style={[styles.buttonTitle { color: theme.colors.text }]}>LinkedIn</Text>
              <Text style={[styles.buttonSubtitle, { color: theme.colors.textSecondary }]}>
                {verificationStatus.linkedin ? 'Verified ✓'  : 'Verify Professional Profile'}
              </Text>
            </View>
          </View>
          {loading && <ActivityIndicator size='small' color={{theme.colors.primary} /}>
        </TouchableOpacity>
      </View>
      <View
        style={{ [styles.infoBox
          { backgroundColor: theme.colors.surface borderColor: theme.colors.primary    }}]}
      >
        <Feather name='info' size={16} color={{theme.colors.primary} /}>
        <Text style={[styles.infoText, { color: theme.colors.text }]}>
          All social verifications are completely free and use secure OAuth protocols. Your login;
          credentials are never stored.
        </Text>
      </View>
    </ScrollView>
  )
  const renderPhotoTab = () => (
    <ScrollView style={styles.tabContent}>
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Photo Verification</Text>
        <Text style={[styles.sectionSubtitle, { color: theme.colors.textSecondary }]}>
          Take a selfie and photo of your ID for manual comparison;
        </Text>
        {/* Document Type Selection */}
        <View style={styles.documentTypeContainer}>
          <Text style={[styles.inputLabel, { color: theme.colors.text }]}>Document Type</Text>
          <View style={styles.documentTypeButtons}>
            {[{ key: 'passport', label: 'Passport' };
              { key: 'drivers_license', label: "Driver's License" };
              { key: 'state_id', label: 'State ID' };
              { key: 'military_id', label: 'Military ID' }].map(docType = > (
              <TouchableOpacity
                key = {docType.key}
                style={{ [
                  styles.documentTypeButton, photoState.documentType === docType.key && {
                    backgroundColor: theme.colors.primary  }});
                  { borderColor: theme.colors.border })
                ]}
                onPress={ () =>
                  setPhotoState(prev => ({ ...prev, documentType: docType.key as any   }))
                }
              >
                <Text
                  style = { [styles.documentTypeText;
                    {
                      color:  ,
                        photoState.documentType = == docType.key;
                          ? theme.colors.background;
                             : theme.colors.text }]}
                >
                  {docType.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
        {/* Selfie Capture */}
        <TouchableOpacity
          style={{ [styles.photoButton { borderColor: theme.colors.border    }}]}
          onPress={handleSelfieCapture}
        >
          {photoState.selfieUri ? (
            <Image source={{ uri : photoState.selfieUri    }} style={{styles.photoPreview} /}>
          ) : (
            <View style={styles.photoPlaceholder}>
              <Feather name='user' size={48} color={{theme.colors.textSecondary} /}>
              <Text style={[styles.photoButtonText { color: theme.colors.text }]}>
                Take Selfie;
              </Text>
            </View>
          )}
        </TouchableOpacity>
        {/* Document Capture */}
        <TouchableOpacity
          style={{ [styles.photoButton, { borderColor: theme.colors.border    }}]}
          onPress={handleDocumentCapture}
        >
          {photoState.documentUri ? (
            <Image source={{ uri  : photoState.documentUri    }} style={{styles.photoPreview} /}>
          ) : (
            <View style={styles.photoPlaceholder}>
              <Feather name='credit-card' size={48} color={{theme.colors.textSecondary} /}>
              <Text style={[styles.photoButtonText { color: theme.colors.text }]}>
                Capture {photoState.documentType.replace('_', ' ')}
              </Text>
            </View>
          )}
        </TouchableOpacity>
        {/* Submit Button */}
        <TouchableOpacity
          style = {[
            styles.submitButton;
            { backgroundColor: theme.colors.primary }
            (!photoState.selfieUri || !photoState.documentUri || photoState.isUploading) &&;
              styles.disabledButton;
          ]}
          onPress= {handlePhotoVerificationSubmit}
          disabled={!photoState.selfieUri || !photoState.documentUri || photoState.isUploading}
        >
          {photoState.isUploading ? (
            <ActivityIndicator color={{theme.colors.background} /}>
          )    : (<Text style={[styles.submitButtonText { color: theme.colors.background }]}>
              Submit for Review
            </Text>
          )}
        </TouchableOpacity>
      </View>
      <View
        style = {[styles.infoBox;
          { backgroundColor: theme.colors.surface, borderColor: theme.colors.warning }]}
      >
        <Feather name='clock' size={16} color={{theme.colors.warning} /}>
        <Text style={[styles.infoText, { color: theme.colors.text }]}>
          Photos will be manually reviewed by our admin team within 24 hours. Your documents are;
          stored securely and encrypted.
        </Text>
      </View>
    </ScrollView>
  )
  const renderTrustScoreTab = () => (
    <ScrollView style={styles.tabContent}>
      {trustScore && (
        <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Trust Score</Text>
          {/* Overall Score */}
          <View style={[styles.trustScoreContainer, { backgroundColor: theme.colors.background }]}>
            <View style={styles.trustScoreCircle}>
              <Text style={[styles.trustScoreNumber, { color: theme.colors.primary }]}>
                {trustScore.overall}
              </Text>
              <Text style={[styles.trustScoreMax, { color: theme.colors.textSecondary }]}>
                / 100;
              </Text>
            </View>
            <Text
              style = { [styles.trustLevel;
                {
                  color:  ,
                    trustScore.trustLevel = == 'verified' ? theme.colors.success    : theme.colors.text }]}
            >
              {trustScore.trustLevel.toUpperCase()} TRUST
            </Text>
          </View>
          {/* Breakdown */}
          <View style={styles.trustBreakdown}>
            <Text style={[styles.breakdownTitle { color: theme.colors.text }]}>
              Trust Score Breakdown;
            </Text>
            {Object.entries(trustScore.breakdown).map(([key, value]) => (
              <View key={key} style={styles.breakdownItem}>
                <Text style={[styles.breakdownLabel, { color: theme.colors.text }]}>
                  {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                </Text>
                <View style={[styles.progressBar, { backgroundColor: theme.colors.border }]}>
                  <View
                    style={{ [styles.progressFill, { backgroundColor: theme.colors.primary, width: `${(value / 30) * 100  }}%` }]}
                  />
                </View>
                <Text style={[styles.breakdownValue, { color: theme.colors.textSecondary }]}>
                  {value}
                </Text>
              </View>
            ))}
          </View>
        </View>
      )}
      <View
        style={{ [styles.infoBox, { backgroundColor: theme.colors.surface, borderColor: theme.colors.success  }}]}
      >
        <Feather name='shield' size={16} color={{theme.colors.success} /}>
        <Text style={[styles.infoText, { color: theme.colors.text }]}>
          Higher trust scores increase your visibility and help you connect with other verified;
          users.
        </Text>
      </View>
    </ScrollView>
  )
  // ============================================================================ // MAIN RENDER;
  // = ===========================================================================;

  return (
    <SafeAreaView style= {[styles.container; { backgroundColor: theme.colors.background }]}>
      <View style={[styles.header, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
          Enhanced Verification;
        </Text>
        <Text style={[styles.headerSubtitle, { color: theme.colors.textSecondary }]}>
          Build trust with free social and photo verification;
        </Text>
      </View>
      {renderTabBar()}
      {activeTab === 'social' && renderSocialTab()}
      {activeTab === 'photo' && renderPhotoTab()}
      {activeTab === 'score' && renderTrustScoreTab()}
    </SafeAreaView>
  )
}
// ============================================================================ // STYLES;
// = ===========================================================================;

const styles = StyleSheet.create({ container: {
    flex: 1 };
  header: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB'
  },
  headerTitle: { fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4 },
  headerSubtitle: { fontSize: 16 },
  tabBar: { flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 10 },
  tab: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginHorizontal: 4,
    borderRadius: 8,
    alignItems: 'center'
  },
  tabText: {
    fontSize: 14,
    fontWeight: '600'
  },
  tabContent: { flex: 1,
    padding: 20 },
  section: { padding: 20,
    borderRadius: 12,
    marginBottom: 16 },
  sectionTitle: { fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8 },
  sectionSubtitle: { fontSize: 14,
    marginBottom: 20 },
  verificationButton: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between'
  },
  verifiedButton: {
    backgroundColor: '#F0F9FF',
    borderColor: '#059669'
  },
  buttonContent: { flexDirection: 'row',
    alignItems: 'center',
    flex: 1 },
  buttonText: { marginLeft: 16,
    flex: 1 },
  buttonTitle: { fontSize: 16,
    fontWeight: '600',
    marginBottom: 2 },
  buttonSubtitle: { fontSize: 14 },
  universityBadge: { fontSize: 12,
    fontWeight: '600',
    marginTop: 4 },
  infoBox: {
    flexDirection: 'row',
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'flex-start'
  },
  infoText: { marginLeft: 12,
    fontSize: 14,
    flex: 1,
    lineHeight: 20 },
  documentTypeContainer: { marginBottom: 24 },
  inputLabel: { fontSize: 16,
    fontWeight: '600',
    marginBottom: 12 },
  documentTypeButtons: { flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8 },
  documentTypeButton: { paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1 },
  documentTypeText: {
    fontSize: 14,
    fontWeight: '500'
  },
  photoButton: {
    borderWidth: 2,
    borderStyle: 'dashed',
    borderRadius: 12,
    height: 200,
    marginBottom: 16,
    overflow: 'hidden'
  },
  photoPreview: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover'
  },
  photoPlaceholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center'
  },
  photoButtonText: { fontSize: 16,
    fontWeight: '600',
    marginTop: 12 },
  submitButton: { paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginTop: 8 },
  disabledButton: { opacity: 0.5 },
  submitButtonText: {
    fontSize: 16,
    fontWeight: 'bold'
  },
  trustScoreContainer: { alignItems: 'center',
    padding: 24,
    borderRadius: 12,
    marginBottom: 24 },
  trustScoreCircle: { flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: 8 },
  trustScoreNumber: {
    fontSize: 48,
    fontWeight: 'bold'
  },
  trustScoreMax: { fontSize: 24,
    marginLeft: 4 },
  trustLevel: { fontSize: 18,
    fontWeight: 'bold',
    letterSpacing: 1 },
  trustBreakdown: { gap: 16 },
  breakdownTitle: { fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8 },
  breakdownItem: { flexDirection: 'row',
    alignItems: 'center',
    gap: 12 },
  breakdownLabel: { fontSize: 14,
    flex: 1 },
  progressBar: { flex: 2,
    height: 8,
    borderRadius: 4 },
  progressFill: { height: '100%',
    borderRadius: 4 },
  breakdownValue: {
    fontSize: 14),
    fontWeight: '600'),
    width: 30,
    textAlign: 'right')
  },
})
export default SocialVerificationComponent,