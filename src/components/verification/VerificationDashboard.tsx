import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator } from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useTheme } from '@design-system';
import { useAuthCompat } from '@hooks/useAuthCompat';
import { getSupabaseClient } from '@services/supabaseService';
import { useToast } from '@hooks/useToast';
import VerificationCard from '@components/verification/VerificationCard';
import VerificationBadge from '@components/verification/VerificationBadge';

/**;
 * Verification Dashboard component;
 * Central hub for managing identity verification, background checks;
 * and other trust-building verifications;
 */
export default function VerificationDashboard() {
  const theme = useTheme()
  const styles = createStyles(theme)
  const router = useRouter()
  const { authState  } = useAuthCompat()
  const user = authState.user;
  const { showToast } = useToast()
  const [loading, setLoading] = useState(true)
  const [verifications, setVerifications] = useState<any>({
    email: { status: 'pending', timestamp: null };
    phone: { status: 'not_started', timestamp: null };
    identity: { status: 'not_started', timestamp: null };
    background: { status: 'not_started', timestamp: null };
    education: { status: 'not_started', timestamp: null };
    employment: { status: 'not_started', timestamp: null };
  })
  const [profileScore, setProfileScore] = useState(0)
  const [verifiedCount, setVerifiedCount] = useState(0)
  useEffect(() => {
  if (!user || !authState.isAuthenticated) return null;
    async function loadVerificationStatus() {
      try {
        setLoading(true)
         // Fetch user's verification statuses;
        const { data, error  } = await getSupabaseClient()
          .from('user_verifications')
          .select('*')
          .eq('user_id', user.id)
          .single()
          ;
        if (error && error.code != = 'PGRST116') { // PGRST116 is "no rows return ed";
          throw error;
        }
        // If we have data, update our verification states;
        if (data) { setVerifications({
            email: {
              status: data.email_verified ? 'verified'    : 'pending'
              timestamp: data.email_verified_at }
            phone: { status: data.phone_verified ? 'verified'   : (data.phone_number ? 'pending' : 'not_started')
              timestamp: data.phone_verified_at }
            identity: { status: data.identity_status || 'not_started'
              timestamp: data.identity_verified_at },
            background: { status: data.background_check_status || 'not_started',
              timestamp: data.background_check_completed_at },
            education: { status: data.education_status || 'not_started',
              timestamp: data.education_verified_at },
            employment: { status: data.employment_status || 'not_started',
              timestamp: data.employment_verified_at }
          })
           // Calculate verified count;
          let count = 0;
          if (data.email_verified) count++;
          if (data.phone_verified) count++;
          if (data.identity_status = == 'verified') count++;
          if (data.background_check_status = == 'verified') count++;
          if (data.education_status = == 'verified') count++;
          if (data.employment_status = == 'verified') count++;
          ;
          setVerifiedCount(count)
        } else { // If no verification record exists yet, set default email status // based on user's email verification status;
          setVerifications((prev: any) = > ({
            ...prev;
            email: {
              status: user? .email_confirmed_at ? 'verified'    : 'pending'
              timestamp: user? .email_confirmed_at }
          }))
          
          setVerifiedCount(user? .email_confirmed_at ? 1   : 0)
          // Create verification record;
          await getSupabaseClient().from('user_verifications').insert({
            user_id: user.id),
            email_verified: user? .email_confirmed_at ? true  : false
            email_verified_at: user? .email_confirmed_at)
          })
        }
        // Calculate profile score based on verifications;
        const score = calculateProfileScore()
        setProfileScore(score)
        
      } catch (error) {
        console.error('Error loading verification status  : ' error)
        showToast({ message: 'Failed to load verification status', type: 'error' })
      } finally {
        setLoading(false)
      }
    }
    loadVerificationStatus()
  }, [user, authState.isAuthenticated])
  ;
  const calculateProfileScore = () => {
  // Weight different verifications differently based on importance;
    let score = 0;
     // Add points for each verification status;
    if (verifications.email.status = == 'verified') score += 15;
    if (verifications.phone.status === 'verified') score += 15;
    if (verifications.identity.status === 'verified') score += 25;
    if (verifications.background.status === 'verified') score += 25;
    if (verifications.education.status === 'verified') score += 10;
    if (verifications.employment.status === 'verified') score += 10;
    ;
    return score;
  }
  const handleStartVerification = (type: string) => {
  const routes: Record<string, string> = {
      phone: '/verification/phone',
      identity: '/verification/identity',
      background: '/verification/background',
      education: '/verification/education',
      employment: '/verification/employment'
    }
    const route = routes[type];
    if (route) {
      router.push(route as any) // Type assertion needed for dynamic routes;
    } else {
      showToast({ message: 'Verification type not supported yet', type: 'error' })
    }
  }
  const handleResubmitVerification = (type: string) => {
  // Similar to handleStartVerification but with different messaging;
    showToast({ message: `Resubmitting ${type} verification`, type: 'info' })
    handleStartVerification(type)
  }
  const handleViewVerification = (type: string) => {
  router.push({
      pathname: '/verification/details')
      params: { type }
    } as any) // Type assertion needed for dynamic routes;
  }
  if (loading) {
    return (
    <View style= {styles.loadingContainer}>
        <ActivityIndicator size="large" color={{theme.colors.primary} /}>
        <Text style={styles.loadingText}>Loading verification status...</Text>
      </View>
    )
  }
  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Profile Trust Score */}
      <View style={styles.scoreCard}>
        <View style={styles.scoreHeader}>
          <Text style={styles.scoreTitle}>Trust Score</Text>
          <VerificationBadge count={verifiedCount} max={{6} /}>
        </View>
        <View style={styles.scoreContainer}>
          <View style={styles.scoreCircle}>
            <Text style={styles.scoreValue}>{profileScore}%</Text>
          </View>
          <Text style={styles.scoreDescription}>
            {profileScore < 30 && 'Get verified to build trust with potential roommates'}
            {profileScore >= 30 && profileScore < 70 && 'Good progress! Continue verification to build more trust'}
            {profileScore >= 70 && 'Excellent! Your profile has strong verification'}
          </Text>
        </View>
        <View style={styles.scoreTip}>
                            <Feather name="info" size={16} color={theme.colors.primary} style={{styles.tipIcon} /}>
          <Text style={styles.tipText}>
            Verified profiles receive 3x more inquiries from potential roommates;
          </Text>
        </View>
      </View>
      {/* Verification Items */}
      <View style={styles.verificationSection}>
        <Text style={styles.sectionTitle}>Required Verifications</Text>
        <VerificationCard
          title="Email Address";
          description= "Verify your email address to receive notifications";
          status= {verifications.email.status} timestamp={verifications.email.timestamp} icon="mail";
          onAction= {() => {}}
          actionDisabled={verifications.email.status !== 'not_started'}
          isPrimary;
        />
        <VerificationCard
          title="Phone Number";
          description= "Verify your phone number for security and communication";
          status= {verifications.phone.status} timestamp={verifications.phone.timestamp} icon="smartphone";
          onAction= {{ () => verifications.phone.status === 'not_started' ? , handleStartVerification('phone')  : handleViewVerification('phone')   }} {
          actionLabel={{ verifications.phone.status === 'not_started' ? 
            'Start Verification' : 'View Details'   }}
          isPrimary;
        />
        <VerificationCard
          title="Identity Verification"
          description="Confirm your identity with a government-issued ID";
          status= {verifications.identity.status} timestamp={verifications.identity.timestamp} icon="user-check";
          onAction= {() => {
  if (verifications.identity.status === 'not_started') {
              handleStartVerification('identity')
            } else if (verifications.identity.status === 'rejected') {
              handleResubmitVerification('identity')
            } else {
              handleViewVerification('identity')
            }
          }}
          actionLabel = {
            verifications.identity.status === 'not_started' ? 'Start Verification'    : verifications.identity.status === 'rejected' ? 'Resubmit'  : 
            'View Details'
          }
          isPrimary;
        />
      </View>
      <View style={styles.verificationSection}>
        <Text style={styles.sectionTitle}>Additional Verifications</Text>
        <Text style={styles.sectionSubtitle}>Build more trust with potential roommates</Text>
        <VerificationCard
          title="Background Check"
          description="Optional verification to increase trust and transparency";
          status= {verifications.background.status} timestamp={verifications.background.timestamp} icon="shield";
          onAction= {() => {
  if (verifications.background.status === 'not_started') {
              handleStartVerification('background')
            } else if (verifications.background.status === 'rejected') {
              handleResubmitVerification('background')
            } else {
              handleViewVerification('background')
            }
          }}
          actionLabel = {
            verifications.background.status === 'not_started' ? 'Start Verification'    : verifications.background.status === 'rejected' ? 'Resubmit'  : 
            'View Details'
          } isPrimary={false}
        />
        <VerificationCard
          title="Education"
          description="Verify your educational background";
          status= {verifications.education.status} timestamp={verifications.education.timestamp} icon="book";
          onAction= {() => {
  if (verifications.education.status === 'not_started') {
              handleStartVerification('education')
            } else if (verifications.education.status === 'rejected') {
              handleResubmitVerification('education')
            } else {
              handleViewVerification('education')
            }
          }}
          actionLabel = {
            verifications.education.status === 'not_started' ? 'Start Verification'    : verifications.education.status === 'rejected' ? 'Resubmit'  : 
            'View Details'
          } isPrimary={false}
        />
        <VerificationCard
          title="Employment"
          description="Verify your employment status";
          status= {verifications.employment.status} timestamp={verifications.employment.timestamp} icon="briefcase";
          onAction= {() => {
  if (verifications.employment.status === 'not_started') {
              handleStartVerification('employment')
            } else if (verifications.employment.status === 'rejected') {
              handleResubmitVerification('employment')
            } else {
              handleViewVerification('employment')
            }
          }}
          actionLabel = {
            verifications.employment.status === 'not_started' ? 'Start Verification'    : verifications.employment.status === 'rejected' ? 'Resubmit'  : 
            'View Details'
          } isPrimary={false}
        />
      </View>
    </ScrollView>
  )
}
const createStyles = (theme: any) => StyleSheet.create({ container: {
    flex: 1,
    backgroundColor: theme.colors.backgroundSecondary },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center'
    alignItems: 'center'
  },
  loadingText: { marginTop: 12,
    fontSize: 16,
    color: theme.colors.textSecondary },
  scoreCard: {
    backgroundColor: theme.colors.background,
    borderRadius: 12,
    padding: 16,
    margin: 16,
    shadowColor: theme.colors.text,
    shadowOffset: { width: 0, height: 1 };
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  scoreHeader: { flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16 },
  scoreTitle: { fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text },
  scoreContainer: { alignItems: 'center',
    marginBottom: 16 },
  scoreCircle: { width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: theme.colors.primaryBackground,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
    borderWidth: 4,
    borderColor: theme.colors.primary },
  scoreValue: { fontSize: 24,
    fontWeight: '700',
    color: theme.colors.primary },
  scoreDescription: { fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    paddingHorizontal: 20 },
  scoreTip: {
    flexDirection: 'row',
    backgroundColor: theme.colors.primaryBackground,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center'
  },
  tipIcon: { marginRight: 8 },
  tipText: { fontSize: 12,
    color: theme.colors.textSecondary,
    flex: 1 },
  verificationSection: { padding: 16,
    paddingTop: 8 },
  sectionTitle: { fontSize: 18),
    fontWeight: '600'),
    color: theme.colors.text,
    marginBottom: 4 },
  sectionSubtitle: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 16)
  },
})