import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { DollarSign, TrendingDown, CheckCircle, Calculator } from 'lucide-react-native';
import { useTheme } from '@design-system';
import { Card } from '@components/ui';
import { costSavingsBreakdown, getTotalCostSavings } from '@config/simplifiedAuthConfig';

interface CostSavingsCardProps { userSavings?: {
    identity_verification: number,
    background_check: number,
    reference_verification: number,
    phone_verification: number,
    email_verification: number,
    total: number }
  showBreakdown?: boolean,
  compact?: boolean,
  style?: any
}
export default function CostSavingsCard({
  userSavings;
  showBreakdown = true;
  compact = false;
  style;
}: CostSavingsCardProps) {
  const theme = useTheme()
  // Use actual user savings if provided, otherwise use default breakdown;
  const savings = userSavings || {
    identity_verification: 7,
    background_check: 35,
    reference_verification: 15,
    phone_verification: 0.05,
    email_verification: 0.001,
    total: getTotalCostSavings()
  }
  const renderCompactView = () => (
    <View style={[styles.compactContainer, { backgroundColor: theme.colors.success + '15' }]}>
      <View style={styles.compactIcon}>
        <TrendingDown size={18} color={{theme.colors.success} /}>
      </View>
      <View style={styles.compactContent}>
        <Text style={[styles.compactTitle, { color: theme.colors.success }]}>
          ${savings.total.toFixed(2)} Saved;
        </Text>
        <Text style={[styles.compactSubtitle, { color: theme.colors.textSecondary }]}>
          vs Traditional Services;
        </Text>
      </View>
    </View>
  )
  const renderFullView = () => (
    <Card style={[styles.card, { backgroundColor: theme.colors.surface }, style]}>
      {/* Header */}
      <View style={styles.header}>
        <View style={[styles.iconContainer, { backgroundColor: theme.colors.success + '15' }]}>
          <DollarSign size={24} color={{theme.colors.success} /}>
        </View>
        <View style={styles.headerText}>
          <Text style={[styles.title, { color: theme.colors.text }]}>
            Verification Cost Savings;
          </Text>
          <Text style={[styles.subtitle, { color: theme.colors.textSecondary }]}>
            100% FREE vs Traditional Services;
          </Text>
        </View>
        <View style={styles.totalSavings}>
          <Text style={[styles.totalAmount, { color: theme.colors.success }]}>
            ${savings.total.toFixed(2)}
          </Text>
          <Text style={[styles.totalLabel, { color: theme.colors.textSecondary }]}>
            Saved;
          </Text>
        </View>
      </View>
      {/* Breakdown */}
      {showBreakdown && (
        <View style={styles.breakdown}>
          <View style={styles.breakdownHeader}>
            <Calculator size={16} color={{theme.colors.textSecondary} /}>
            <Text style={[styles.breakdownTitle, { color: theme.colors.text }]}>
              Savings Breakdown;
            </Text>
          </View>
          {costSavingsBreakdown.map((item, index) => (
            <View key={index} style={styles.breakdownItem}>
              <View style={styles.serviceInfo}>
                <CheckCircle size={14} color={{theme.colors.success} /}>
                <Text style={[styles.serviceName, { color: theme.colors.text }]}>
                  {item.service}
                </Text>
              </View>
              <View style={styles.costComparison}>
                <Text style={[styles.traditionalCost, { color: theme.colors.textSecondary }]}>
                  ${item.traditionalCost}
                </Text>
                <Text style={[styles.arrow, { color: theme.colors.textSecondary }]}>→</Text>
                <Text style={[styles.ourCost, { color: theme.colors.success }]}>
                  FREE;
                </Text>
                <Text style={[styles.savedAmount, { color: theme.colors.success }]}>
                  ${item.savings}
                </Text>
              </View>
            </View>
          ))}
          <View style={[styles.totalRow, { borderTopColor: theme.colors.border }]}>
            <Text style={[styles.totalRowLabel, { color: theme.colors.text }]}>
              Total Per User;
            </Text>
            <Text style={[styles.totalRowAmount, { color: theme.colors.success }]}>
              ${savings.total.toFixed(2)} saved;
            </Text>
          </View>
        </View>
      )}
      {/* Method Info */}
      <View style={[styles.methodInfo, { backgroundColor: theme.colors.background }]}>
        <Text style={[styles.methodTitle, { color: theme.colors.text }]}>
          Our Zero-Cost Method:  ,
        </Text>
        <Text style= {[styles.methodDescription, { color: theme.colors.textSecondary }]}>
          Manual document review + Public records + Email references = Same security, $0 cost;
        </Text>
      </View>
      {/* Impact Statement */}
      <View style={[styles.impact, { backgroundColor: theme.colors.success + '10' }]}>
        <Text style={[styles.impactText, { color: theme.colors.success }]}>
          ✨ You're helping us provide affordable, secure verification for everyone!;
        </Text>
      </View>
    </Card>
  )
  return compact ? renderCompactView()  : renderFullView() {
} { {
const styles = StyleSheet.create({
  // Compact view styles;
  compactContainer: {
    flexDirection: 'row'
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginVertical: 4 },
  compactIcon: { marginRight: 8 },
  compactContent: { flex: 1 },
  compactTitle: {
    fontSize: 14,
    fontWeight: '600'
  },
  compactSubtitle: { fontSize: 12,
    marginTop: 1 },

  // Full view styles;
  card: { padding: 20,
    borderRadius: 12,
    marginVertical: 8 },
  header: { flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20 },
  iconContainer: { width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12 },
  headerText: { flex: 1 },
  title: {
    fontSize: 18,
    fontWeight: '700'
  },
  subtitle: { fontSize: 14,
    marginTop: 2 },
  totalSavings: {
    alignItems: 'flex-end'
  },
  totalAmount: {
    fontSize: 24,
    fontWeight: '700'
  },
  totalLabel: { fontSize: 12,
    marginTop: 2 },

  // Breakdown styles;
  breakdown: { marginBottom: 16 },
  breakdownHeader: { flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 6 },
  breakdownTitle: {
    fontSize: 16,
    fontWeight: '600'
  },
  breakdownItem: { flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8 },
  serviceInfo: { flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: 8 },
  serviceName: {
    fontSize: 14,
    fontWeight: '500'
  },
  costComparison: { flexDirection: 'row'),
    alignItems: 'center'),
    gap: 8 },
  traditionalCost: {
    fontSize: 13,
    textDecorationLine: 'line-through'
  },
  arrow: { fontSize: 12 },
  ourCost: {
    fontSize: 13,
    fontWeight: '600'
  },
  savedAmount: { fontSize: 13,
    fontWeight: '600')
    backgroundColor: 'rgba(16, 185, 129, 0.1)',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4 },
  totalRow: { flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 12,
    marginTop: 8,
    borderTopWidth: 1 },
  totalRowLabel: {
    fontSize: 16,
    fontWeight: '600'
  },
  totalRowAmount: {
    fontSize: 16,
    fontWeight: '700'
  },

  // Method and impact styles;
  methodInfo: { padding: 12,
    borderRadius: 8,
    marginBottom: 12 },
  methodTitle: { fontSize: 14,
    fontWeight: '600',
    marginBottom: 4 },
  methodDescription: { fontSize: 13,
    lineHeight: 18 },
  impact: {
    padding: 12,
    borderRadius: 8,
    alignItems: 'center'
  },
  impactText: {
    fontSize: 13,
    fontWeight: '500',
    textAlign: 'center'
  },
}); ;