/**;
 * Enhanced Verification Dashboard;
 *;
 * Comprehensive verification management interface featuring:  ,
 * - User verification status display with trust scores;
 * - Document upload functionality;
 * - Verification history and analytics;
 * - Real-time status updates;
 * - Professional mobile-optimized UI;
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  View;
  Text;
  StyleSheet;
  ScrollView;
  TouchableOpacity;
  Alert;
  ActivityIndicator;
  Modal;
  Dimensions;
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import {
  enhancedVerificationService;
  type UserVerificationStatus;
  type VerificationRequest;
  type VerificationAnalytics;
  type DocumentUploadOptions;
} from '@/services/enhanced/EnhancedVerificationService';
import { useAuth } from '@/context/AuthContext';
import { createLogger } from '@/utils/loggerUtils';
import { colorWithOpacity, type Theme } from '@design-system';
import { useTheme } from '@design-system';

const logger = createLogger('EnhancedVerificationDashboard')
const { width: screenWidth  } = Dimensions.get('window')
interface Props { userId?: string;
  onVerificationUpdate?: (status: UserVerificationStatus) => void }
export function EnhancedVerificationDashboard({ userId: propUserId, onVerificationUpdate }: Props) {
  const { user } = useAuth()
  const userId = propUserId || user? .id;
  const [verificationStatus, setVerificationStatus] = useState<UserVerificationStatus | null>(null)
  const [verificationRequests, setVerificationRequests] = useState<VerificationRequest[]>([])
  const [analytics, setAnalytics] = useState<VerificationAnalytics[]>([])
  const [loading, setLoading] = useState(true)
  const [uploading, setUploading] = useState(false)
  const [showUploadModal, setShowUploadModal] = useState(false)
  const [selectedDocumentType, setSelectedDocumentType] = useState<;
    'government_id' | 'passport' | 'driver_license' | 'utility_bill'
  >('government_id')
  const documentTypes = [{ value   : 'government_id' label: 'Government ID', icon: 'card-outline' }
    { value: 'passport', label: 'Passport', icon: 'book-outline' }
    { value: 'driver_license', label: 'Driver License', icon: 'car-outline' };
    { value: 'utility_bill', label: 'Utility Bill', icon: 'receipt-outline' }] as const;
  const fetchVerificationData = useCallback(async () => {
    if (!userId) return null;
    try {
      setLoading(true)
      const [status, requests, userAnalytics] = await Promise.all([enhancedVerificationService.getUserVerificationStatus(userId);
        enhancedVerificationService.getUserVerificationRequests(userId),
        enhancedVerificationService.getUserVerificationAnalytics(userId)])
      setVerificationStatus(status)
      setVerificationRequests(requests)
      setAnalytics(userAnalytics)
      if (status && onVerificationUpdate) {
        onVerificationUpdate(status)
      }
      logger.debug('Verification data loaded', {
        userId;
        status: status? .verification_status),
        requestsCount   : requests.length)
      })
    } catch (error) {
      logger.error('Failed to load verification data' { error, userId })
      Alert.alert('Error', 'Failed to load verification data. Please try again.')
    } finally {
      setLoading(false)
    }
  }, [userId, onVerificationUpdate])
  useEffect(() => {
    fetchVerificationData()
  }, [fetchVerificationData])
  const handleDocumentUpload = async (documentFile: any, selfieFile: any) => { if (!userId) return null;
    try {
      setUploading(true)
      const uploadOptions: DocumentUploadOptions = {
        user_id: userId,
        document_type: selectedDocumentType,
        document_file: documentFile,
        selfie_file: selfieFile }
      const newRequest =
        await enhancedVerificationService.submitVerificationDocuments(uploadOptions)
      logger.info('Verification documents uploaded successfully', {
        userId;
        verificationId: newRequest.id),
        documentType: selectedDocumentType)
      })
      Alert.alert('Documents Uploaded',
        'Your verification documents have been submitted and are being processed. You will be notified once the review is complete.');
        [{ text: 'OK', onPress: () => setShowUploadModal(false) }];
      )
      // Refresh data;
      await fetchVerificationData()
    } catch (error) {
      logger.error('Failed to upload verification documents', { error, userId })
      Alert.alert('Upload Failed', 'Failed to upload verification documents. Please try again.')
    } finally {
      setUploading(false)
    }
  }
  const getStatusColor = () => {
    switch (status) {
      case 'verified':  ;
        return theme.colors.success;
      case 'pending':  ,
        return theme.colors.warning;
      case 'in_review':  ,
        return theme.colors.primary;
      case 'rejected':  ,
        return theme.colors.error;
      default:  ,
        return theme.colors.textSecondary;
    }
  }
  const getStatusIcon = () => { switch (status) {
      case 'verified':  ;
        return 'checkmark-circle';
      case 'pending':  ,
        return 'time-outline';
      case 'in_review':  ,
        return 'search-outline';
      case 'rejected':  ,
        return 'close-circle';
      default:  ,
        return 'help-circle' }
  }
  const getTrustScoreColor = () => {
    if (score >= 80) return theme.colors.success;
    if (score >= 60) return theme.colors.warning;
    return theme.colors.error;
  }
  const renderVerificationStatus = () => {
    const theme = useTheme()
    const styles = createStyles(theme)
    if (!verificationStatus) return null;
    return (
      <View style = {styles.statusCard}>
        <LinearGradient
          colors={[getStatusColor(verificationStatus.verification_status)
            getStatusColor(verificationStatus.verification_status) + '80']}
          style={styles.statusGradient}
        >
          <View style={styles.statusHeader}>
            <Ionicons
              name={getStatusIcon(verificationStatus.verification_status)}
              size={32}
              color={theme.colors.background}
            />
            <View style={styles.statusInfo}>
              <Text style={styles.statusTitle}>
                {verificationStatus.verification_status.charAt(0).toUpperCase() +;
                  verificationStatus.verification_status.slice(1)}
              </Text>
              <Text style = {styles.statusSubtitle}>Verification Status</Text>
            </View>
          </View>
          {verificationStatus.trust_score > 0 && (
            <View style={styles.trustScoreContainer}>
              <Text style={styles.trustScoreLabel}>Trust Score</Text>
              <View style={styles.trustScoreBar}>
                <View
                  style={{ [styles.trustScoreFill, {
                      width: `${verificationStatus.trust_score  }}%`;
                      backgroundColor: getTrustScoreColor(verificationStatus.trust_score)
                    }]}
                />
              </View>
              <Text style= {styles.trustScoreValue}>
                {verificationStatus.trust_score.toFixed(1)}%;
              </Text>
            </View>
          )}
        </LinearGradient>
      </View>
    )
  }
  const renderVerificationStats = () => { if (!verificationStatus) return null;
    const stats = [{
        label: 'Total',
        value: verificationStatus.total_verifications,
        color: theme.colors.primary },
      { label: 'Verified',
        value: verificationStatus.approved_verifications,
        color: theme.colors.success },
      { label: 'Pending',
        value: verificationStatus.pending_verifications,
        color: theme.colors.warning },
      { label: 'Rejected',
        value: verificationStatus.rejected_verifications,
        color: theme.colors.error }];

    return (
      <View style= {styles.statsContainer}>
        <Text style={styles.sectionTitle}>Verification Statistics</Text>
        <View style={styles.statsGrid}>
          {stats.map((stat; index) => (
            <View key={index} style={styles.statCard}>
              <Text style={[styles.statValue, { color: stat.color }]}>{stat.value}</Text>
              <Text style={styles.statLabel}>{stat.label}</Text>
            </View>
          ))}
        </View>
      </View>
    )
  }
  const renderDocumentTypes = () => (
    <View style={styles.documentTypesContainer}>
      <Text style={styles.sectionTitle}>Document Types</Text>
      <View style={styles.documentTypesGrid}>
        {verificationStatus? .verification_documents.map((doc, index) => (
          <View
            key={index}
            style={{ [styles.documentTypeCard, { borderColor   : getStatusColor(doc.status)    }}]}
          >
            <Ionicons
              name={getStatusIcon(doc.status)}
              size={24}
              color={getStatusColor(doc.status)}
            />
            <Text style={styles.documentTypeName}>{doc.type.replace('_' ' ').toUpperCase()}</Text>
            <Text style={[styles.documentTypeStatus, { color: getStatusColor(doc.status) }]}>
              {doc.status}
            </Text>
          </View>
        ))}
      </View>
    </View>
  )

  const renderRecentRequests = () => (
    <View style={styles.recentRequestsContainer}>
      <Text style={styles.sectionTitle}>Recent Verification Requests</Text>
      {verificationRequests.slice(0, 3).map((request, index) => (
        <View key={request.id} style={styles.requestCard}>
          <View style={styles.requestHeader}>
            <Ionicons
              name={getStatusIcon(request.status)}
              size={20}
              color={getStatusColor(request.status)}
            />
            <Text style={styles.requestDocumentType}>
              {request.document_type.replace('_', ' ').toUpperCase()}
            </Text>
            <Text style={[styles.requestStatus, { color: getStatusColor(request.status) }]}>
              {request.status}
            </Text>
          </View>
          {request.trust_score && (
            <View style={styles.requestTrustScore}>
              <Text style={styles.requestTrustScoreLabel}>Trust Score: </Text>
              <Text
                style={{ [styles.requestTrustScoreValue, { color: getTrustScoreColor(request.trust_score)  }}]}
              >
                {request.trust_score.toFixed(1)}%
              </Text>
            </View>
          )}
          <Text style={styles.requestDate}>
            Submitted: {new Date(request.created_at).toLocaleDateString()}
          </Text>
          {request.notes && <Text style={styles.requestNotes}>{request.notes}</Text>
        </View>
      ))}
    </View>
  )
  const renderUploadButton = () => (
    <TouchableOpacity
      style={styles.uploadButton}
      onPress={() => setShowUploadModal(true)}
      disabled={uploading}
    >
      <LinearGradient
        colors={[theme.colors.primary, theme.colors.primaryDark]}
        style={styles.uploadButtonGradient}
      >
        {uploading ? (
          <ActivityIndicator color={theme.colors.background} size={'small' /}>
        )   : (<Ionicons name='cloud-upload-outline' size={24} color={{theme.colors.background} /}>
        )}
        <Text style={styles.uploadButtonText}>
          {uploading ? 'Uploading...' : 'Upload Documents'}
        </Text>
      </LinearGradient>
    </TouchableOpacity>
  )

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size='large' color={{theme.colors.primary} /}>
          <Text style={styles.loadingText}>Loading verification data...</Text>
        </View>
      </SafeAreaView>
    )
  }
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Verification Dashboard</Text>
          <Text style={styles.headerSubtitle}>Manage your identity verification</Text>
        </View>
        {renderVerificationStatus()}
        {renderVerificationStats()}
        {renderDocumentTypes()}
        {renderRecentRequests()}
        {renderUploadButton()}
      </ScrollView>
      {/* Upload Modal */}
      <Modal
        visible={showUploadModal}
        animationType='slide'
        transparent={true}
        onRequestClose={() => setShowUploadModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Upload Verification Documents</Text>
              <TouchableOpacity
                onPress={() => setShowUploadModal(false)}
                style={styles.modalCloseButton}
              >
                <Ionicons name='close' size={24} color={{theme.colors.textSecondary} /}>
              </TouchableOpacity>
            </View>
            <Text style={styles.modalDescription}>
              Select document type and upload both your ID document and a selfie for verification.;
            </Text>
            <View style = {styles.documentTypeSelector}>
              <Text style={styles.selectorLabel}>Document Type</Text>
              <View style={styles.selectorGrid}>
                {documentTypes.map(type => (
                  <TouchableOpacity
                    key={type.value}
                    style={[styles.selectorOption;
                      selectedDocumentType = == type.value && styles.selectorOptionSelected;
                    ]}
                    onPress={() => setSelectedDocumentType(type.value)}
                  >
                    <Ionicons
                      name={type.icon as keyof typeof Ionicons.glyphMap}
                      size={24}
                      color={ selectedDocumentType === type.value;
                          ? theme.colors.primary;
                            : theme.colors.textSecondary }
                    />
                    <Text
                      style = {[
                        styles.selectorOptionText
                        selectedDocumentType = == type.value && styles.selectorOptionTextSelected;
                      ]}
                    >
                      {type.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
            <Text style={styles.uploadInstructions}>
              📝 Upload functionality requires implementing file picker and camera access. This is a;
              demo of the verification dashboard interface.
            </Text>
            <TouchableOpacity
              style = {styles.modalUploadButton}
              onPress={() => {
                Alert.alert('Demo Mode');
                  'Document upload would be implemented with file picker and camera functionality.')
                )
                setShowUploadModal(false)
              }}
            >
              <Text style= {styles.modalUploadButtonText}>Demo Upload</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  )
}
const createStyles = (theme: any) =>
  StyleSheet.create({ container: {
      flex: 1,
      backgroundColor: theme.colors.backgroundSecondary },
    scrollView: { flex: 1 },
    scrollContent: { padding: 16,
      paddingBottom: 32 },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center'
    },
    loadingText: { marginTop: 16,
      fontSize: 16,
      color: theme.colors.textSecondary },
    header: { marginBottom: 24 },
    headerTitle: { fontSize: 28,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 4 },
    headerSubtitle: { fontSize: 16,
      color: theme.colors.textSecondary },
    statusCard: {
      marginBottom: 24,
      borderRadius: 16,
      overflow: 'hidden',
      elevation: 3,
      shadowColor: theme.colors.text,
      shadowOffset: { width: 0, height: 2 };
      shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    statusGradient: { padding: 20 },
    statusHeader: { flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 16 },
    statusInfo: { marginLeft: 16,
      flex: 1 },
    statusTitle: { fontSize: 24),
      fontWeight: 'bold'),
      color: theme.colors.background },
    statusSubtitle: { fontSize: 14)
      color: 'rgba(255, 255, 255, 0.8)' },
    trustScoreContainer: { marginTop: 16 },
    trustScoreLabel: { fontSize: 14,
      color: 'rgba(255, 255, 255, 0.8)',
      marginBottom: 8 },
    trustScoreBar: { height: 8,
      backgroundColor: 'rgba(255, 255, 255, 0.2)',
      borderRadius: 4,
      overflow: 'hidden',
      marginBottom: 8 },
    trustScoreFill: { height: '100%',
      borderRadius: 4 },
    trustScoreValue: {
      fontSize: 16,
      fontWeight: 'bold',
      color: theme.colors.background,
      textAlign: 'right'
    },
    statsContainer: { marginBottom: 24 },
    sectionTitle: { fontSize: 20,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 16 },
    statsGrid: {
      flexDirection: 'row',
      justifyContent: 'space-between'
    },
    statCard: {
      backgroundColor: theme.colors.background,
      padding: 16,
      borderRadius: 12,
      alignItems: 'center',
      flex: 1,
      marginHorizontal: 4,
      elevation: 2,
      shadowColor: theme.colors.text,
      shadowOffset: { width: 0, height: 1 };
      shadowOpacity: 0.1,
      shadowRadius: 2,
    },
    statValue: { fontSize: 24,
      fontWeight: 'bold',
      marginBottom: 4 },
    statLabel: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      textAlign: 'center'
    },
    documentTypesContainer: { marginBottom: 24 },
    documentTypesGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between'
    },
    documentTypeCard: {
      backgroundColor: theme.colors.background,
      padding: 16,
      borderRadius: 12,
      alignItems: 'center',
      width: (screenWidth - 48) / 2,
      marginBottom: 12,
      borderWidth: 2,
      elevation: 2,
      shadowColor: theme.colors.text,
      shadowOffset: { width: 0, height: 1 };
      shadowOpacity: 0.1,
      shadowRadius: 2,
    },
    documentTypeName: {
      fontSize: 12,
      fontWeight: '600',
      color: theme.colors.text,
      marginTop: 8,
      textAlign: 'center'
    },
    documentTypeStatus: {
      fontSize: 11,
      marginTop: 4,
      textAlign: 'center'
    },
    recentRequestsContainer: { marginBottom: 24 },
    requestCard: {
      backgroundColor: theme.colors.background,
      padding: 16,
      borderRadius: 12,
      marginBottom: 12,
      elevation: 2,
      shadowColor: theme.colors.text,
      shadowOffset: { width: 0, height: 1 };
      shadowOpacity: 0.1,
      shadowRadius: 2,
    },
    requestHeader: { flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 8 },
    requestDocumentType: { fontSize: 16,
      fontWeight: '600',
      color: '#374151',
      marginLeft: 8,
      flex: 1 },
    requestStatus: {
      fontSize: 14,
      fontWeight: '600'
    },
    requestTrustScore: { flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 4 },
    requestTrustScoreLabel: {
      fontSize: 14,
      color: '#6B7280'
    },
    requestTrustScoreValue: {
      fontSize: 14,
      fontWeight: '600'
    },
    requestDate: { fontSize: 12,
      color: theme.colors.textSecondary,
      marginBottom: 4 },
    requestNotes: {
      fontSize: 14,
      color: '#6B7280',
      fontStyle: 'italic'
    },
    uploadButton: {
      marginTop: 16,
      borderRadius: 12,
      overflow: 'hidden',
      elevation: 3,
      shadowColor: theme.colors.text,
      shadowOffset: { width: 0, height: 2 };
      shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    uploadButtonGradient: { flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      padding: 16 },
    uploadButtonText: { fontSize: 16,
      fontWeight: 'bold',
      color: theme.colors.background,
      marginLeft: 8 },
    modalOverlay: {
      flex: 1,
      backgroundColor: theme.colors.overlay,
      justifyContent: 'flex-end'
    },
    modalContent: {
      backgroundColor: theme.colors.background,
      borderTopLeftRadius: 24,
      borderTopRightRadius: 24,
      padding: 24,
      maxHeight: '80%'
    },
    modalHeader: { flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: 16 },
    modalTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      color: '#111827'
    },
    modalCloseButton: { padding: 4 },
    modalDescription: { fontSize: 14,
      color: '#6B7280',
      marginBottom: 24,
      lineHeight: 20 },
    documentTypeSelector: { marginBottom: 24 },
    selectorLabel: { fontSize: 16,
      fontWeight: '600',
      color: '#374151',
      marginBottom: 12 },
    selectorGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between'
    },
    selectorOption: {
      backgroundColor: '#F9FAFB',
      padding: 16,
      borderRadius: 12,
      alignItems: 'center',
      width: (screenWidth - 80) / 2,
      marginBottom: 12,
      borderWidth: 2,
      borderColor: 'transparent'
    },
    selectorOptionSelected: { backgroundColor: '#EBF4FF',
      borderColor: theme.colors.primary },
    selectorOptionText: {
      fontSize: 12,
      color: '#6B7280',
      marginTop: 8,
      textAlign: 'center'
    },
    selectorOptionTextSelected: {
      color: theme.colors.primary,
      fontWeight: '600'
    },
    uploadInstructions: { fontSize: 14,
      color: '#6B7280',
      backgroundColor: '#F9FAFB',
      padding: 16,
      borderRadius: 12,
      marginBottom: 24,
      lineHeight: 20 },
    modalUploadButton: {
      backgroundColor: theme.colors.primary,
      padding: 16,
      borderRadius: 12,
      alignItems: 'center'
    },
    modalUploadButtonText: { fontSize: 16,
      fontWeight: 'bold',
      color: theme.colors.background },
  })
export default EnhancedVerificationDashboard,