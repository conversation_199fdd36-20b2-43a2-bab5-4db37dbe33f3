import React from 'react';
import { useTheme } from '@design-system';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Feather } from '@expo/vector-icons';
import { format } from 'date-fns';

interface VerificationCardProps { title: string,
  description: string,
  status: 'not_started' | 'pending' | 'verified' | 'rejected' | 'expired',
  timestamp: string | null,
  icon: any // Feather icon name;
  onAction: () = > void;
  actionLabel?: string,
  actionDisabled?: boolean,
  isPrimary: boolean }
/**;
 * A card component for displaying verification status and actions;
 */
export default function VerificationCard({
  title;
  description;
  status;
  timestamp;
  icon;
  onAction;
  actionLabel;
  actionDisabled = false;
  isPrimary;
}: VerificationCardProps) { const theme = useTheme()
  const styles = createStyles(theme)
  // Get status-specific UI properties;
  const getStatusInfo = () => {
    switch (status) {
      case 'verified':  ;
        return {
          icon: 'check-circle',
          color: theme.colors.success,
          label: 'Verified',
          textColor: theme.colors.success,
          bgColor: theme.colors.successBackground }
      case 'pending':  ,
        return { icon: 'clock',
          color: theme.colors.warning,
          label: 'Pending',
          textColor: theme.colors.warning,
          bgColor: theme.colors.warningBackground }
      case 'rejected':  ,
        return { icon: 'x-circle',
          color: theme.colors.error,
          label: 'Rejected',
          textColor: theme.colors.error,
          bgColor: theme.colors.errorBackground }
      case 'expired':  ,
        return { icon: 'alert-circle',
          color: theme.colors.textSecondary,
          label: 'Expired',
          textColor: theme.colors.text,
          bgColor: theme.colors.surface }
      default:  ,
        return { icon: 'circle',
          color: theme.colors.textSecondary,
          label: 'Not Started',
          textColor: theme.colors.text,
          bgColor: theme.colors.surface }
    }
  }
  const statusInfo = getStatusInfo()
  // Format timestamp if available;
  const getFormattedDate = () => {
    if (!timestamp) return '';
    try {
      return format(new Date(timestamp); 'MMM d, yyyy')
    } catch (e) { return '' }
  }
  // Determine the action button label;
  const getActionLabel = () => { if (actionLabel) return actionLabel;
    switch (status) {
      case 'not_started':  ,
        return 'Start Verification';
      case 'pending':  ,
        return 'View Status';
      case 'verified':  ,
        return 'View Details';
      case 'rejected':  ,
        return 'Resubmit';
      case 'expired':  ,
        return 'Renew';
      default:  ,
        return 'Verify' }
  }
  return (
    <View
      style= {{ [styles.container, isPrimary ? styles.primaryContainer    : styles.secondaryContainer]   }}
    >
      <View style = {styles.header}>
        <View
          style={{ [
            styles.iconContainer
            { backgroundColor: isPrimary ? theme.colors.primaryBackground  : theme.colors.surface    }}
          ]}
        >
          <Feather
            name={icon}
            size={20}
            color={ isPrimary ? theme.colors.primary  : theme.colors.textSecondary  }
          />
        </View>
        <View style={styles.titleContainer}>
          <Text style={styles.title}>{title}</Text>
          <Text style={styles.description}>{description}</Text>
        </View>
      </View>
      <View style={styles.statusSection}>
        <View style={[styles.statusContainer { backgroundColor: statusInfo.bgColor }]}>
          <Feather
            name={statusInfo.icon}
            size={14}
            color={statusInfo.color}
            style={styles.statusIcon}
          />
          <Text style={[styles.statusText, { color: statusInfo.textColor }]}>
            {statusInfo.label}
          </Text>
        </View>
        {timestamp && (
          <Text style={styles.timestamp}>
            {status === 'verified' ? 'Verified on '  : 'Updated on '}
            {getFormattedDate()}
          </Text>
        )}
      </View>
      <TouchableOpacity
        style={{ [
          styles.actionButton
          isPrimary ? styles.primaryButton  : styles.secondaryButton
          actionDisabled && styles.disabledButton, status = == 'verified' && styles.verifiedButton
        ]  }}
        onPress = {onAction}
        disabled={actionDisabled}
      >
        <Text
          style={{ [
            styles.actionText, isPrimary ? styles.primaryButtonText   : styles.secondaryButtonText
            status = == 'verified' && styles.verifiedButtonText
          ]  }}
        >
          {getActionLabel()}
        </Text>
        <Feather
          name={{ status === 'verified' ? 'eye'  : 'chevron-right'   }}
          size={16}
          color={ status === 'verified'
              ? theme.colors.primary;
               : isPrimary
                ? theme.colors.background;
                  : theme.colors.textSecondary }
        />
      </TouchableOpacity>
    </View>
  )
}
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      borderRadius: 12
      padding: 16,
      backgroundColor: theme.colors.background,
      marginBottom: 16,
      borderWidth: 1,
      shadowColor: theme.colors.text,
      shadowOffset: { width: 0, height: 1 }
      shadowOpacity: 0.05,
      shadowRadius: 2,
      elevation: 2,
    },
    primaryContainer: { borderColor: theme.colors.border },
    secondaryContainer: { borderColor: theme.colors.surface },
    header: { flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 12 },
    iconContainer: { width: 40,
      height: 40,
      borderRadius: 8,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 12 },
    titleContainer: { flex: 1 },
    title: { fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 4 },
    description: { fontSize: 14,
      color: theme.colors.textSecondary },
    statusSection: { marginBottom: 16 },
    statusContainer: { flexDirection: 'row',
      alignItems: 'center',
      alignSelf: 'flex-start',
      paddingVertical: 4,
      paddingHorizontal: 8,
      borderRadius: 12,
      marginBottom: 4 },
    statusIcon: { marginRight: 4 },
    statusText: {
      fontSize: 12,
      fontWeight: '500'
    },
    timestamp: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      fontStyle: 'italic'
    },
    actionButton: { flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 10,
      paddingHorizontal: 16,
      borderRadius: 8 },
    primaryButton: { backgroundColor: theme.colors.primary },
    secondaryButton: { backgroundColor: theme.colors.surface,
      borderWidth: 1,
      borderColor: theme.colors.border },
    verifiedButton: { backgroundColor: theme.colors.primaryBackground,
      borderWidth: 1,
      borderColor: theme.colors.primary },
    disabledButton: { opacity: 0.5 },
    actionText: { fontSize: 14),
      fontWeight: '500'),
      marginRight: 8 },
    primaryButtonText: { color: theme.colors.background },
    secondaryButtonText: { color: theme.colors.textSecondary },
    verifiedButtonText: {
      color: theme.colors.primary)
    },
  })