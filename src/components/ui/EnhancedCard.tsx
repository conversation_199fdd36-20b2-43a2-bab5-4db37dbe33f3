import React, { useRef, useEffect } from 'react';
import { View, Text, StyleSheet, Animated, StyleProp, ViewStyle, TextStyle } from 'react-native';
import { useTheme } from '@design-system';

interface EnhancedCardProps {
  /**;
   * Title of the card;
   */
  title?: string,
  /**;
   * Content of the card;
   */
  content?: string,
  /**;
   * Icon to display in the card;
   */
  icon?: React.ReactNode,
  /**;
   * Type of card (info, warning, success, error)
   */
  type?: 'info' | 'warning' | 'success' | 'error',
  /**;
   * Children to render inside the card;
   */
  children?: React.ReactNode,
  /**;
   * Container style;
   */
  containerStyle?: StyleProp<ViewStyle>
  /**;
   * Title style;
   */
  titleStyle?: StyleProp<TextStyle>
  /**;
   * Content style;
   */
  contentStyle?: StyleProp<TextStyle>
}
/**;
 * Enhanced card component with animations and improved UI;
 */
export const EnhancedCard: React.FC<EnhancedCardProps> = ({
  title;
  content;
  icon;
  type = 'info';
  children;
  containerStyle;
  titleStyle;
  contentStyle;
}) = > {
  const theme = useTheme()
  // Animation values;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current // Handle entrance animation;
  useEffect(() => {
    Animated.parallel([Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 300),
        useNativeDriver: true)
      }),
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 300),
        useNativeDriver: true)
      })]).start()
  }, [])
  // Get card colors based on type using theme colors;
  const getCardColors = () => {
    const opacity10 = theme.isDark ? '20'    : '10' // Adjust opacity for dark mode
    const opacity25 = theme.isDark ? '40'   : '25'
    switch (type) {
      case 'warning':  
        return {
          backgroundColor: theme.colors.scales.warning[theme.isDark ? 900   : 50]
          borderColor: theme.colors.scales.warning[theme.isDark ? 600  : 300]
          titleColor: theme.colors.warning
          contentColor: theme.colors.scales.warning[theme.isDark ? 400   : 600]
        }
      case 'success':  
        return {
          backgroundColor: theme.colors.scales.success[theme.isDark ? 900   : 50]
          borderColor: theme.colors.scales.success[theme.isDark ? 600  : 300]
          titleColor: theme.colors.success
          contentColor: theme.colors.scales.success[theme.isDark ? 400   : 600]
        }
      case 'error':  
        return {
          backgroundColor: theme.colors.scales.error[theme.isDark ? 900   : 50]
          borderColor: theme.colors.scales.error[theme.isDark ? 600  : 300]
          titleColor: theme.colors.error
          contentColor: theme.colors.scales.error[theme.isDark ? 400   : 600]
        }
      case 'info':  
      default: 
        return {
          backgroundColor: theme.colors.scales.primary[theme.isDark ? 900    : 50]
          borderColor: theme.colors.scales.primary[theme.isDark ? 600  : 300]
          titleColor: theme.colors.primary
          contentColor: theme.colors.scales.primary[theme.isDark ? 400  : 600]
        }
    }
  }
  const cardColors = getCardColors()
  const styles = createStyles(theme)
  return (
    <Animated.View;
      style = {[
        styles.container;
        {
          backgroundColor: cardColors.backgroundColor,
          borderColor: cardColors.borderColor,
          transform: [{ scale: scaleAnim }]
          opacity: opacityAnim
        },
        containerStyle;
      ]}
    >
      <View style={styles.contentContainer}>
        {icon && <View style={styles.iconContainer}>{icon}</View>
        <View style={styles.textContainer}>
          {title && (
            <Text style={[styles.title, { color: cardColors.titleColor }, titleStyle]}>
              {title}
            </Text>
          )}
          {content && (
            <Text style={[styles.content, { color: cardColors.contentColor }, contentStyle]}>
              {content}
            </Text>
          )}
          {children}
        </View>
      </View>
    </Animated.View>
  )
}
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      borderRadius: theme.borderRadius.lg,
      borderWidth: 1,
      marginVertical: theme.spacing.sm,
      overflow: 'hidden',
      ...theme.shadows.md;
    },
    contentContainer: { flexDirection: 'row',
      padding: theme.spacing.md },
    iconContainer: {
      marginRight: theme.spacing.sm,
      alignItems: 'center',
      justifyContent: 'center'
    },
    textContainer: { flex: 1 },
    title: { fontSize: 16),
      fontWeight: '600'),
      marginBottom: 4 },
    content: {
      fontSize: 14,
      lineHeight: 20)
    },
  })
export default EnhancedCard,