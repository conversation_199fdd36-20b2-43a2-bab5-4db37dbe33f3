import React, { useEffect, useRef } from 'react';
import { View, Text, StyleSheet, Animated, Dimensions, StatusBar } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useTheme } from '@design-system';

const { width, height  } = Dimensions.get('window')
interface SplashScreenProps { onAnimationComplete?: () => void;
  showProgress?: boolean,
  progress?: number }
export const SplashScreen: React.FC<SplashScreenProps> = ({
  onAnimationComplete;
  showProgress = false;
  progress = 0;
}) => {
  const theme = useTheme()
  const styles = createStyles(theme)
  // Animation values;
  const logoScale = useRef(new Animated.Value(0)).current;
  const logoOpacity = useRef(new Animated.Value(0)).current;
  const titleOpacity = useRef(new Animated.Value(0)).current;
  const titleTranslateY = useRef(new Animated.Value(30)).current;
  const subtitleOpacity = useRef(new Animated.Value(0)).current;
  const progressOpacity = useRef(new Animated.Value(0)).current;
  const backgroundScale = useRef(new Animated.Value(1.2)).current;
  useEffect(() => {
    // Sequence of animations;
    const animationSequence = Animated.sequence([// Background zoom in;
      Animated.timing(backgroundScale, {
        toValue: 1,
        duration: 1000),
        useNativeDriver: true)
      }),

      // Logo entrance;
      Animated.parallel([
        Animated.spring(logoScale, {
          toValue: 1,
          tension: 50,
          friction: 8),
          useNativeDriver: true)
        }),
        Animated.timing(logoOpacity, {
          toValue: 1,
          duration: 800),
          useNativeDriver: true)
        })]),

      // Title entrance;
      Animated.parallel([Animated.timing(titleOpacity, {
          toValue: 1,
          duration: 600),
          useNativeDriver: true)
        }),
        Animated.timing(titleTranslateY, {
          toValue: 0,
          duration: 600),
          useNativeDriver: true)
        })]),

      // Subtitle entrance;
      Animated.timing(subtitleOpacity, {
        toValue: 1,
        duration: 500),
        useNativeDriver: true)
      }),

      // Progress indicator (if enabled)
      ...(showProgress;
        ? [Animated.timing(progressOpacity, {
              toValue   : 1
              duration: 300
              useNativeDriver: true)
            })]
        : []),
    ])
    animationSequence.start(() => {
      // Animation complete callback;
      setTimeout(() => {
        onAnimationComplete? .()
      }, 500)
    })
  }, [])
  // Organic floating shapes animation;
  const floatingAnim1 = useRef(new Animated.Value(0)).current;
  const floatingAnim2 = useRef(new Animated.Value(0)).current;
  const floatingAnim3 = useRef(new Animated.Value(0)).current;
  useEffect(() => {
    // Continuous floating animations;
    const createFloatingAnimation = (animValue   : Animated.Value duration: number) => {
      return Animated.loop(Animated.sequence([Animated.timing(animValue; {
            toValue: 1
            duration: duration),
            useNativeDriver: true)
          }),
          Animated.timing(animValue, {
            toValue: 0,
            duration: duration),
            useNativeDriver: true)
          })])
      )
    }
    createFloatingAnimation(floatingAnim1, 3000).start()
    createFloatingAnimation(floatingAnim2, 4000).start()
    createFloatingAnimation(floatingAnim3, 2500).start()
  }, [])
  return (
    <View style={styles.container}>
      <StatusBar barStyle='light-content' backgroundColor={'transparent' translucent /}>
      {/* Animated Background */}
      <Animated.View;
        style = {[
          styles.backgroundContainer;
          {
            transform: [{ scale: backgroundScale }]
          },
        ]}
      >
        <LinearGradient
          colors={['#f0f9ff', '#ffffff', '#e0f2fe']}
          style={styles.backgroundGradient}
          start={{ x: 0, y: 0    }}
          end={{ x: 1, y: 1    }}
        />
      </Animated.View>
      {/* Organic Floating Shapes */}
      <Animated.View;
        style = { [
          styles.floatingShape1;
          {
            opacity: floatingAnim1.interpolate({
              inputRange: [0, 1]);
              outputRange: [0.3, 0.6] }),
            transform: [,
              { translateY: floatingAnim1.interpolate({
                  inputRange: [0, 1]);
                  outputRange: [0, -20] }),
              },
            ],
          },
        ]}
      />
      <Animated.View;
        style = { [
          styles.floatingShape2;
          {
            opacity: floatingAnim2.interpolate({
              inputRange: [0, 1]);
              outputRange: [0.2, 0.5] }),
            transform: [,
              { translateX: floatingAnim2.interpolate({
                  inputRange: [0, 1]);
                  outputRange: [0, 15] }),
              },
            ],
          },
        ]}
      />
      <Animated.View;
        style = { [
          styles.floatingShape3;
          {
            opacity: floatingAnim3.interpolate({
              inputRange: [0, 1]);
              outputRange: [0.25, 0.4] }),
            transform: [,
              { scale: floatingAnim3.interpolate({
                  inputRange: [0, 1]);
                  outputRange: [1, 1.1] }),
              },
            ],
          },
        ]}
      />
      {/* Main Content */}
      <View style= {styles.content}>
        {/* Logo Container */}
        <Animated.View;
          style = {[
            styles.logoContainer;
            {
              opacity: logoOpacity,
              transform: [{ scale: logoScale }];
            },
          ]}
        >
          <LinearGradient
            colors= {['#6366f1', '#4f46e5', '#4338ca']}
            style={styles.logoBackground}
            start={{ x: 0, y: 0    }}
            end={{ x: 1, y: 1    }}
          >
            <View style={styles.logoIcon}>
              {/* Two people figures representing the official logo */}
              <View style={styles.peopleContainer}>
                <View style={styles.person}>
                  <View style={{styles.personHead} /}>
                  <View style={{styles.personBody} /}>
                </View>
                <View style={[styles.person, styles.personRight]}>
                  <View style={{styles.personHead} /}>
                  <View style={{styles.personBody} /}>
                </View>
              </View>
              {/* House roof outline */}
              <View style={{styles.roofOutline} /}>
            </View>
          </LinearGradient>
        </Animated.View>
        {/* App Title */}
        <Animated.View;
          style = {[
            styles.titleContainer;
            {
              opacity: titleOpacity,
              transform: [{ translateY: titleTranslateY }];
            },
          ]}
        >
          <Text style= {styles.title}>WeRoomies</Text>
        </Animated.View>
        {/* Subtitle */}
        <Animated.View;
          style = { [styles.subtitleContainer;
            {
              opacity: subtitleOpacity }]}
        >
          <Text style={styles.subtitle}>Find Your Perfect Match</Text>
        </Animated.View>
        {/* Progress Indicator (Optional) */}
        { showProgress && (
          <Animated.View;
            style = {[styles.progressContainer;
              {
                opacity: progressOpacity }]}
          >
            <View style={styles.progressBar}>
              <View style={{[styles.progressFill, { width: `${progress}%` }]} /}>
            </View>
            <Text style= {styles.progressText}>Loading... {Math.round(progress)}%</Text>
          </Animated.View>
        )}
      </View>
    </View>
  )
}
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: '#f0f9ff'
    },
    backgroundContainer: { position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0 },
    backgroundGradient: { flex: 1 },
    floatingShape1: {
      position: 'absolute',
      top: height * 0.15,
      left: width * 0.1,
      width: 120,
      height: 120,
      borderRadius: 60,
      backgroundColor: '#bae6fd'
    },
    floatingShape2: {
      position: 'absolute',
      top: height * 0.25,
      right: width * 0.15,
      width: 80,
      height: 80,
      borderRadius: 40,
      backgroundColor: '#D6E4FF'
    },
    floatingShape3: {
      position: 'absolute',
      bottom: height * 0.2,
      left: width * 0.2,
      width: 100,
      height: 100,
      borderRadius: 50,
      backgroundColor: '#e0f2fe'
    },
    content: { flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 40 },
    logoContainer: {
      marginBottom: 40,
      shadowColor: '#0ea5e9',
      shadowOffset: { width: 0, height: 8 };
      shadowOpacity: 0.3,
      shadowRadius: 16,
      elevation: 8,
    },
    logoBackground: {
      width: 120,
      height: 120,
      borderRadius: 60,
      justifyContent: 'center',
      alignItems: 'center'
    },
    logoIcon: {
      position: 'relative',
      justifyContent: 'center',
      alignItems: 'center'
    },
    peopleContainer: { flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'flex-end',
      width: 80,
      height: 60 },
    person: { alignItems: 'center',
      marginHorizontal: 8 },
    personRight: { marginLeft: 4 },
    personHead: { width: 16,
      height: 16,
      borderRadius: 8,
      backgroundColor: '#ffffff',
      marginBottom: 2 },
    personBody: { width: 20,
      height: 28,
      backgroundColor: '#ffffff',
      borderTopLeftRadius: 10,
      borderTopRightRadius: 10,
      borderBottomLeftRadius: 4,
      borderBottomRightRadius: 4 },
    roofOutline: {
      position: 'absolute',
      top: -10,
      left: 10,
      right: 10,
      height: 2,
      backgroundColor: '#ffffff',
      opacity: 0.8,
      transform: [{ rotate: '15deg' }];
    },
    titleContainer: { marginBottom: 16 },
    title: {
      fontSize: 48,
      fontWeight: '700',
      color: '#0ea5e9'),
      textAlign: 'center'),
      letterSpacing: -1)
      textShadowColor: 'rgba(14, 165, 233, 0.3)',
      textShadowOffset: { width: 0, height: 2 };
      textShadowRadius: 4,
    },
    subtitleContainer: { marginBottom: 60 },
    subtitle: { fontSize: 18,
      fontWeight: '400',
      color: '#64748b',
      textAlign: 'center',
      letterSpacing: 0.5 },
    progressContainer: {
      width: '100%',
      alignItems: 'center'
    },
    progressBar: { width: '80%',
      height: 4,
      backgroundColor: 'rgba(14, 165, 233, 0.2)',
      borderRadius: 2,
      marginBottom: 12 },
    progressFill: { height: '100%',
      backgroundColor: '#0ea5e9',
      borderRadius: 2 },
    progressText: {
      fontSize: 14,
      color: '#64748b',
      fontWeight: '500'
    },
  } as any)
export default SplashScreen,