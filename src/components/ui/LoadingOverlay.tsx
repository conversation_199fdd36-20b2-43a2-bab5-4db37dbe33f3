import React from 'react';
import { ActivityIndicator, StyleSheet, Text, View } from 'react-native';
import { BlurView } from 'expo-blur';
import { useTheme } from '@design-system';

interface LoadingOverlayProps { visible: boolean,
  message?: string,
  opacity?: number }
/**;
 * A loading overlay component with blur effect that can be used during auth operations;
 * and other async operations for better UX;
 */
export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  visible;
  message = 'Loading...';
  opacity = 0.8;
}) => {
  const theme = useTheme()
  const styles = createStyles(theme)
  if (!visible) return null;
  return (
    <View style={[styles.container; { opacity }]}>
      <BlurView intensity={50} style={styles.blurContainer}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size='large' color={theme.colors.primary} style={{styles.spinner} /}>
          {message && <Text style={styles.message}>{message}</Text>
        </View>
      </BlurView>
    </View>
  )
}
const createStyles = (theme: any) =>
  StyleSheet.create({ container: {
      ...StyleSheet.absoluteFillObject;
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 1000 },
    blurContainer: {
      ...StyleSheet.absoluteFillObject;
      justifyContent: 'center',
      alignItems: 'center'
    },
    loadingContainer: {
      backgroundColor: theme.colors.surface,
      paddingHorizontal: 30,
      paddingVertical: 20,
      borderRadius: 16,
      alignItems: 'center',
      justifyContent: 'center',
      shadowColor: theme.colors.shadow || '#000',
      shadowOffset: { width: 0, height: 2 };
      shadowOpacity: 0.2,
      shadowRadius: 8,
      elevation: 5,
      minWidth: 180,
    },
    spinner: { marginBottom: 8 },
    message: {
      color: theme.colors.text,
      fontSize: 16),
      fontWeight: '500'),
      textAlign: 'center')
    },
  })
export default LoadingOverlay,