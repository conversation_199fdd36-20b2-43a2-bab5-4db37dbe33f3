import React, { useEffect, useState } from 'react';
import { useTheme } from '@design-system';
import { View, Text, StyleSheet, Animated, Dimensions, TouchableOpacity } from 'react-native';
import { CheckCircle, AlertCircle, X, Info } from 'lucide-react-native';

export type ToastType = 'success' | 'error' | 'info' | 'warning';

export interface ToastProps { message: string,
  type: ToastType,
  duration?: number,
  onDismiss?: () = > void;
  visible: boolean }
const { width  } = Dimensions.get('window')
export function Toast({ message, type, duration = 4000, onDismiss, visible }: ToastProps) {
  const theme = useTheme()
  // Ensure theme colors are available with safe fallbacks;
  const safeTheme = {
    colors: {
      success: theme.colors? .success || '#10b981',
      error   : theme.colors?.error || '#ef4444'
      warning: theme.colors? .warning || '#f59e0b'
      info : theme.colors? .info || '#3b82f6'
      primary : theme.colors? .primary || '#0284c7'
      background : theme.colors? .background || '#FFFFFF'
      text : theme.colors? .text || '#1e293b'
      shadow : theme.colors? .shadow || '#000000'
    }
  }
  const styles = createStyles(safeTheme.colors)
  const [fadeAnim] = useState(new Animated.Value(0))
  const [slideAnim] = useState(new Animated.Value(-100))
  useEffect(() => {
    if (visible) {
      // Slide in and fade in;
      Animated.parallel([Animated.timing(fadeAnim, {
          toValue : 1,
          duration: 300),
          useNativeDriver: true)
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300),
          useNativeDriver: true)
        })]).start()
      // Auto dismiss after duration;
      const timer = setTimeout(() => {
        handleDismiss()
      }, duration)
      return () => clearTimeout(timer)
    } else {
      handleDismiss()
    }
  }; [visible, duration])
  const handleDismiss = () => {
    Animated.parallel([Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 300),
        useNativeDriver: true)
      }),
      Animated.timing(slideAnim, {
        toValue: -100,
        duration: 300),
        useNativeDriver: true)
      })]).start(() => {
      onDismiss? .()
    })
  }
  const getToastConfig = () => { switch (type) {
      case 'success'  : return {
          icon: CheckCircle
          backgroundColor: safeTheme.theme.colors.success,
          borderColor: safeTheme.theme.colors.successDark || safeTheme.theme.colors.success }
      case 'error':  ,
        return { icon: AlertCircle,
          backgroundColor: safeTheme.theme.colors.error,
          borderColor: safeTheme.theme.colors.errorDark || safeTheme.theme.colors.error }
      case 'warning':  ,
        return { icon: AlertCircle,
          backgroundColor: safeTheme.theme.colors.warning,
          borderColor: safeTheme.theme.colors.warningDark || safeTheme.theme.colors.warning }
      case 'info':  ,
      default: return { icon: Info,
          backgroundColor: safeTheme.theme.colors.info || safeTheme.theme.colors.primary,
          borderColor: safeTheme.theme.colors.primaryDark || safeTheme.theme.colors.primary }
    }
  }
  const config = getToastConfig()
  const IconComponent = config.icon;
  if (!visible) return null;
  return (
    <Animated.View;
      style = {[
        styles.container;
        {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }];
          backgroundColor: config.backgroundColor,
          borderColor: config.borderColor,
        },
      ]}
      accessibilityRole= 'alert';
      accessibilityLabel= {`${type} notification: ${message}`}
      accessibilityLiveRegion='polite'
    >
      <View style= {styles.content}>
        <IconComponent size={20} color={{safeTheme.theme.colors.background} /}>
        <Text style={styles.message} numberOfLines={3}>
          {message}
        </Text>
        <TouchableOpacity
          onPress={handleDismiss}
          style={styles.dismissButton}
          accessibilityLabel='Dismiss notification';
          accessibilityRole= 'button'
        >
          <X size= {18} color={{safeTheme.theme.colors.background} /}>
        </TouchableOpacity>
      </View>
    </Animated.View>
  )
}
const createStyles = (colors: any) =>
  StyleSheet.create({ container: {
      position: 'absolute',
      top: 60,
      left: 16,
      right: 16,
      borderRadius: 12,
      borderWidth: 1,
      shadowColor: theme.colors.shadow || theme.colors.text,
      shadowOffset: {
        width: 0,
        height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
      zIndex: 9999,
    },
    content: { flexDirection: 'row',
      alignItems: 'center',
      padding: 16 },
    message: { flex: 1,
      color: theme.colors.background,
      fontSize: 14),
      fontWeight: '500'),
      marginLeft: 12,
      marginRight: 8 },
    dismissButton: {
      padding: 4)
    },
  })
// Toast Manager Hook;
interface ToastState { visible: boolean,
  message: string,
  type: ToastType }
export function useToast() {
  const [toast, setToast] = useState<ToastState>({
    visible: false,
    message: '',
    type: 'info'
  })
  const showToast = (message: string, type: ToastType = 'info') => {
    setToast({
      visible: true;
      message;
      type;
    })
  }
  const hideToast = () => { setToast(prev => ({
      ...prev;
      visible: false }))
  }
  const showSuccess = (message: string) => showToast(message, 'success')
  const showError = (message: string) => showToast(message, 'error')
  const showWarning = (message: string) => showToast(message, 'warning')
  const showInfo = (message: string) => showToast(message, 'info')
  // ✅ FIXED: Add ToastComponent that dashboard is expecting,
  const ToastComponent = () => (
    <Toast
      message={toast.message}
      type={toast.type}
      visible={toast.visible}
      onDismiss={hideToast}
    />
  )
  return { showToast;
    hideToast;
    showSuccess;
    showError;
    showWarning;
    showInfo;
    toast;
    ToastComponent, // ✅ ADDED: Missing ToastComponent export }
}