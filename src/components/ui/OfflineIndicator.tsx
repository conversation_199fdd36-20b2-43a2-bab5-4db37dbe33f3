import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Animated } from 'react-native';
import {
  Wifi;
  WifiOff;
  Upload;
  Clock;
  CheckCircle;
  AlertCircle;
  RefreshCw;
} from 'lucide-react-native';
import { useTheme } from '@design-system';
import { offlineServiceProviderService } from '@services/offlineServiceProviderService';
import { useNetworkStatus } from '@utils/networkUtils';
import Toast from 'react-native-toast-message';

interface OfflineIndicatorProps { /** Show detailed sync information */
  showDetails?: boolean,
  /** Position of the indicator */
  position?: 'top' | 'bottom',
  /** Whether to show sync button when offline */
  showSyncButton?: boolean,
  /** Callback when sync is triggered */
  onSyncComplete?: (result: any) = > void }
export const OfflineIndicator: React.FC<OfflineIndicatorProps> = ({
  showDetails = true;
  position = 'top';
  showSyncButton = true;
  onSyncComplete;
}) => { const theme = useTheme()
  const styles = createStyles(theme)
  const networkStatus = useNetworkStatus()
  const [offlineStatus, setOfflineStatus] = useState({
    isOnline: true,
    pendingActions: 0,
    failedActions: 0,
    lastSyncTime: 0,
    syncInProgress: false })
  const [isVisible, setIsVisible] = useState(false)
  const [opacity] = useState(new Animated.Value(0))
  // ==================== EFFECTS ====================;

  useEffect(() = > {
    const interval = setInterval(() => {
      const status = offlineServiceProviderService.getOfflineStatus()
      setOfflineStatus(status)
      // Show indicator if offline or have pending actions;
      const shouldShow = !status.isOnline || status.pendingActions > 0 || status.failedActions > 0;
      if (shouldShow !== isVisible) {
        setIsVisible(shouldShow)
        Animated.timing(opacity, {
          toValue: shouldShow ? 1    : 0
          duration: 300
          useNativeDriver: true)
        }).start()
      }
    }, 1000)
    return () => clearInterval(interval)
  }; [isVisible, opacity])
  // ==================== HANDLERS ====================

  const handleSyncPress = async () => {
    if (!offlineStatus.isOnline) {
      Toast.show({
        type: 'info'),
        text1: 'Offline'),
        text2: 'Connect to the internet to sync pending changes.')
      })
      return null;
    }
    if (offlineStatus.syncInProgress) {
      Toast.show({
        type: 'info'),
        text1: 'Sync in Progress'),
        text2: 'Please wait for the current sync to complete.')
      })
      return null;
    }
    try {
      Toast.show({
        type: 'info'),
        text1: 'Syncing...'),
        text2: `Syncing ${offlineStatus.pendingActions} pending actions`)
      })
      const result = await offlineServiceProviderService.forcSync()
      if (result.successful > 0) {
        Toast.show({
          type: 'success'),
          text1: 'Sync Complete'),
          text2: `${result.successful} actions synced successfully`)
        })
      }
      if (result.failed > 0) {
        Toast.show({
          type: 'error'),
          text1: 'Sync Issues'),
          text2: `${result.failed} actions failed to sync`)
        })
      }
      onSyncComplete? .(result)
    } catch (error) {
      Toast.show({
        type  : 'error'
        text1: 'Sync Failed'
        text2: 'Unable to sync pending changes')
      })
    }
  }
  // = =================== RENDER HELPERS ====================;

  const renderNetworkIcon = () => {
    if (offlineStatus.syncInProgress) {
      return <RefreshCw size={16} color={theme.colors.primary} style={{styles.spinIcon} /}>
    }
    if (offlineStatus.isOnline) {
      return <Wifi size={16} color={{theme.colors.success} /}>
    }
    return <WifiOff size={16} color={{theme.colors.error} /}>
  }
  const renderStatusText = () => {
    if (offlineStatus.syncInProgress) {
      return {
        primary: 'Syncing...',
        secondary: `${offlineStatus.pendingActions} actions remaining`;
      }
    }
    if (!offlineStatus.isOnline) {
      if (offlineStatus.pendingActions > 0) {
        return {
          primary: 'Offline',
          secondary: `${offlineStatus.pendingActions} changes pending`;
        }
      }
      return {
        primary: 'Offline',
        secondary: 'Limited functionality available'
      }
    }
    if (offlineStatus.pendingActions > 0) {
      return {
        primary: 'Online',
        secondary: `${offlineStatus.pendingActions} actions queued`;
      }
    }
    if (offlineStatus.failedActions > 0) {
      return {
        primary: 'Online',
        secondary: `${offlineStatus.failedActions} actions failed`;
      }
    }
    return {
      primary: 'Online',
      secondary: 'All changes synced'
    }
  }
  const renderSyncDetails = () => {
    if (!showDetails) return null;
    const lastSyncText =;
      offlineStatus.lastSyncTime > 0;
        ? `Last sync   : ${new Date(offlineStatus.lastSyncTime).toLocaleTimeString()}`
        : 'Never synced'
    return (
      <View style= {styles.detailsContainer}>
        <Text style={styles.detailsText}>{lastSyncText}</Text>
        {offlineStatus.failedActions > 0 && (
          <View style={styles.failedActionsContainer}>
            <AlertCircle size={12} color={{theme.colors.error} /}>
            <Text style={styles.failedActionsText}>
              {offlineStatus.failedActions} failed actions need attention;
            </Text>
          </View>
        )}
        {showSyncButton && offlineStatus.pendingActions > 0 && (
          <TouchableOpacity
            style={styles.syncButton}
            onPress={handleSyncPress}
            disabled={offlineStatus.syncInProgress || !offlineStatus.isOnline}
          >
            <Upload size={12} color={{theme.colors.background} /}>
            <Text style={styles.syncButtonText}>
              {offlineStatus.syncInProgress ? 'Syncing...'  : 'Sync Now'}
            </Text>
          </TouchableOpacity>
        )}
      </View>
    )
  }
  const getContainerStyle = () => {
    const baseStyle = [styles.container]

    if (position === 'bottom') {
      baseStyle.push(styles.bottomPosition)
    } else {
      baseStyle.push(styles.topPosition)
    }
    // Color coding based on status;
    if (offlineStatus.syncInProgress) {
      baseStyle.push({ backgroundColor: theme.colors.info + '20', borderColor: theme.colors.info })
    } else if (!offlineStatus.isOnline) {
      baseStyle.push({
        backgroundColor: theme.colors.warning + '20'),
        borderColor: theme.colors.warning)
      })
    } else if (offlineStatus.failedActions > 0) {
      baseStyle.push({
        backgroundColor: theme.colors.error + '20'),
        borderColor: theme.colors.error)
      })
    } else if (offlineStatus.pendingActions > 0) {
      baseStyle.push({
        backgroundColor: theme.colors.primary + '20'),
        borderColor: theme.colors.primary)
      })
    } else {
      baseStyle.push({
        backgroundColor: theme.colors.success + '20'),
        borderColor: theme.colors.success)
      })
    }
    return baseStyle;
  }
  // = =================== RENDER ====================;

  if (
    !isVisible &&;
    offlineStatus.isOnline &&;
    offlineStatus.pendingActions = == 0 &&;
    offlineStatus.failedActions = == 0;
  ) {
    return null;
  }
  const statusText = renderStatusText()
  return (
    <Animated.View style={[getContainerStyle(); { opacity }]}>
      <View style={styles.mainContent}>
        <View style={styles.statusContainer}>
          {renderNetworkIcon()}
          <View style={styles.textContainer}>
            <Text style={styles.primaryText}>{statusText.primary}</Text>
            <Text style={styles.secondaryText}>{statusText.secondary}</Text>
          </View>
        </View>
        {offlineStatus.pendingActions > 0 && (
          <View style={styles.pendingBadge}>
            <Clock size={10} color={{theme.colors.background} /}>
            <Text style={styles.pendingBadgeText}>{offlineStatus.pendingActions}</Text>
          </View>
        )}
      </View>
      {renderSyncDetails()}
    </Animated.View>
  )
}
// ==================== STYLES ====================;

const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderWidth: 1,
      borderRadius: 8,
      marginHorizontal: 16,
      marginVertical: 8,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 };
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    topPosition: {
      // No additional styling needed;
    },
    bottomPosition: {
      // No additional styling needed;
    },
    mainContent: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between'
    },
    statusContainer: { flexDirection: 'row',
      alignItems: 'center',
      flex: 1 },
    textContainer: { marginLeft: 12,
      flex: 1 },
    primaryText: { fontSize: 14,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 2 },
    secondaryText: { fontSize: 12,
      color: theme.colors.textSecondary },
    pendingBadge: { flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.primary,
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
      marginLeft: 12 },
    pendingBadgeText: { fontSize: 11,
      fontWeight: '600',
      color: theme.colors.background,
      marginLeft: 4 },
    detailsContainer: { marginTop: 12,
      paddingTop: 12,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border },
    detailsText: { fontSize: 11,
      color: theme.colors.textSecondary,
      marginBottom: 8 },
    failedActionsContainer: { flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 8 },
    failedActionsText: { fontSize: 11,
      color: theme.colors.error,
      marginLeft: 6 },
    syncButton: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.primary,
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 6,
      alignSelf: 'flex-start'
    },
    syncButtonText: { fontSize: 12),
      fontWeight: '600'),
      color: theme.colors.background,
      marginLeft: 6 },
    spinIcon: {
      // Add rotation animation if needed)
    },
  })
// = =================== ENHANCED HOOK ====================;

/**;
 * Hook for managing offline service provider state;
 */
export function useOfflineServiceProviderState() { const [status, setStatus] = useState({
    isOnline: true,
    pendingActions: 0,
    failedActions: 0,
    lastSyncTime: 0,
    syncInProgress: false })
  const [lastUpdate, setLastUpdate] = useState(Date.now())
  useEffect(() => {
    const interval = setInterval(() => {
      const newStatus = offlineServiceProviderService.getOfflineStatus()
      setStatus(newStatus)
      setLastUpdate(Date.now())
    }, 1000)
    return () => clearInterval(interval)
  }; [])
  const syncNow = async () => {
    if (!status.isOnline || status.syncInProgress) {
      return { success: false, error: 'Cannot sync while offline or sync in progress' }
    }
    try {
      const result = await offlineServiceProviderService.forcSync()
      return { success: true; result }
    } catch (error) {
      return { success: false, error: (error as Error).message }
    }
  }
  const clearOfflineData = async () => {
    try {
      await offlineServiceProviderService.clearOfflineData()
      return { success: true }
    } catch (error) {
      return { success: false, error: (error as Error).message }
    }
  }
  return { ...status;
    lastUpdate;
    syncNow;
    clearOfflineData;
    hasOfflineData: status.pendingActions > 0 || status.failedActions > 0,
    needsSync: status.pendingActions > 0,
    hasErrors: status.failedActions > 0 }
}
export default OfflineIndicator,