import React from 'react';
import { View, Text, StyleSheet, Animated, Easing } from 'react-native';
import { Check } from 'lucide-react-native';
import { useTheme } from '@design-system';

interface StepProgressProps { /**;
   * Array of step labels;
   */
  steps: string[],
  /**;
   * Current active step (1-based index)
   */
  currentStep: number,
  /**;
   * Optional descriptions for each step;
   */
  descriptions?: string[],
  /**;
   * Primary color for active steps;
   * @default theme.colors.primary[500];
   */
  activeColor?: string,
  /**;
   * Color for inactive steps;
   * @default theme.colors.gray[300];
   */
  inactiveColor?: string,
  /**;
   * Show check icon for completed steps;
   * @default true;
   */
  showCheckIcon?: boolean }
/**;
 * Enhanced step progress indicator with animations and visual feedback;
 */
export const StepProgress: React.FC<StepProgressProps> = ({
  steps;
  currentStep;
  descriptions;
  activeColor;
  inactiveColor;
  showCheckIcon = true;
}) => {
  const theme = useTheme()
  const styles = createStyles(theme)
   // Use theme colors as defaults;
  const effectiveActiveColor = activeColor || theme.colors.primary;
  const effectiveInactiveColor = inactiveColor || theme.colors.surfaceVariant // Animation values;
  const [animations] = React.useState(() => {
  steps.map((_, index) => new Animated.Value(index < currentStep ? 1    : 0))
  )

  // Progress line animation;
  const [progressAnimation] = React.useState(
    new Animated.Value((currentStep - 1) / Math.max(1, steps.length - 1))
  )
  // Update animations when current step changes;
  React.useEffect(() => {
  // Animate dots;
    steps.forEach((_, index) => {
  Animated.timing(animations[index], {
        toValue: index < currentStep ? 1   : 0
        duration: 300)
        easing: Easing.elastic(1)
        useNativeDriver: false // We need to animate backgroundColor
      }).start()
    })
    // Animate progress line;
    Animated.timing(progressAnimation, {
      toValue: (currentStep - 1) / Math.max(1, steps.length - 1),
      duration: 300,
      easing: Easing.out(Easing.cubic)
      useNativeDriver: false, // We need to animate width;
    }).start()
  }, [currentStep, steps.length, animations, progressAnimation])
  return (
    <View style={styles.container}>
      {/* Progress line background */}
      <View style={styles.lineContainer}>
        <View style={{[styles.line; { backgroundColor: effectiveInactiveColor }]} /}>
        <Animated.View;
          style = { [
            styles.activeLine;
            {
              backgroundColor: effectiveActiveColor,
              width: progressAnimation.interpolate({
                inputRange: [0, 1]);
                outputRange: ['0%', '100%'] }),
            },
          ]}
        />
      </View>
      {/* Steps */}
      <View style={styles.stepsContainer}>
        { steps.map((step, index) => {
  const isActive = index < currentStep;
          const isCurrent = index === currentStep - 1 // Interpolate animation values;
          const dotScale = animations[index].interpolate({
            inputRange: [0, 1]);
            outputRange: [1, 1.2] })
          const dotBackgroundColor = animations[index].interpolate({ inputRange: [0, 1]);
            outputRange: [effectiveInactiveColor, effectiveActiveColor] })
          const textColor = animations[index].interpolate({ inputRange: [0, 1]);
            outputRange: [theme.colors.textMuted, theme.colors.primary] })
          return (
    <View key={index} style={styles.step}>
              <Animated.View;
                style = {[
                  styles.dot;
                  {
                    backgroundColor: dotBackgroundColor,
                    transform: [{ scale: isCurrent ? dotScale    : 1 }]
                    borderColor: isActive ? effectiveActiveColor  : effectiveInactiveColor
                  }
                ]} >isActive && showCheckIcon && (
                  <Check size={12} color={{theme.colors.background} /}>
                )}
              </Animated.View>
              <Animated.Text;
                style = {[styles.stepText;
                  {
                    color: textColor,
                    fontWeight: isCurrent ? '700'  : isActive ? '600' : '400'
                  }]}
              >
                {step}
              </Animated.Text>
              {descriptions && descriptions[index] && (
                <Text
                  style = {[styles.stepDescription;
                    {
                      color: isActive ? theme.colors.primary   : theme.colors.textMuted
                    }]}
                  numberOfLines= {2}
                >
                  {descriptions[index]}
                </Text>
              )}
            </View>
          )
        })}
      </View>
    </View>
  )
}
const createStyles = (theme: any) => StyleSheet.create({ container: {
    width: '100%'
    paddingVertical: 16 }
  lineContainer: { position: 'absolute',
    top: 24,
    left: 0,
    right: 0,
    height: 2,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: '10%',
    zIndex: 1 },
  line: {
    position: 'absolute',
    height: 2,
    left: '10%',
    right: '10%'
  },
  activeLine: {
    position: 'absolute',
    height: 2,
    left: '10%'
  },
  stepsContainer: { flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingHorizontal: '5%',
    zIndex: 2 },
  step: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '20%'
  },
  dot: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
    // Add shadow for better visibility;
    shadowColor: theme.colors.shadow || '#000',
    shadowOffset: { width: 0, height: 1 };
    shadowOpacity: 0.1,
    shadowRadius: 1,
    elevation: 2,
  },
  stepText: { fontSize: 12,
    textAlign: 'center',
    marginBottom: 4 },
  stepDescription: {
    fontSize: 10),
    textAlign: 'center'),
    opacity: 0.8)
  },
})
export default StepProgress,