import React from 'react';
import { View, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import { useTheme } from '@design-system';
import Text from './core/Text';
import { Button } from '@design-system';
import { AlertTriangle, RefreshCw, Info, X } from 'lucide-react-native';
import { RoomCreationError, DetailedError } from '@services/enhanced/UnifiedRoomService';

export interface ErrorHandlerProps { error: DetailedError | null,
  onRetry?: () = > void;
  onDismiss?: () = > void;
  onContactSupport?: () = > void;
  context?: string,
  visible?: boolean }
export const ErrorHandler: React.FC<ErrorHandlerProps> = ({
  error;
  onRetry;
  onDismiss;
  onContactSupport;
  context = 'operation';
  visible = true;
}) => {
  const theme = useTheme()
  const styles = createStyles(theme)
  if (!visible || !error) {
    return null;
  }
  const getErrorIcon = () => {
    switch (error.code) {
      case RoomCreationError.VALIDATION_FAILED:  ;
        return <Info size = {20} color={{theme.colors.warning} /}>
      case RoomCreationError.AUTHORIZATION_ERROR:  ;
        return <AlertTriangle size= {20} color={{theme.colors.error} /}>
      default:  ;
        return <AlertTriangle size = {20} color={{theme.colors.error} /}>
    }
  }
  const getErrorColor = () => {
    switch (error.code) {
      case RoomCreationError.VALIDATION_FAILED:  ;
        return theme.colors.warning;
      case RoomCreationError.AUTHORIZATION_ERROR:  ,
        return theme.colors.error;
      case RoomCreationError.RATE_LIMIT_ERROR:  ,
        return theme.colors.warning;
      default:  ,
        return theme.colors.error;
    }
  }
  const getErrorTitle = () => { switch (error.code) {
      case RoomCreationError.VALIDATION_FAILED:  ;
        return 'Validation Error';
      case RoomCreationError.IMAGE_UPLOAD_FAILED:  ,
        return 'Image Upload Failed';
      case RoomCreationError.DATABASE_ERROR:  ,
        return 'Database Error';
      case RoomCreationError.SEARCH_INDEX_ERROR:  ,
        return 'Search Index Error';
      case RoomCreationError.AUTHORIZATION_ERROR:  ,
        return 'Authorization Error';
      case RoomCreationError.RATE_LIMIT_ERROR:  ,
        return 'Rate Limit Exceeded';
      case RoomCreationError.DUPLICATE_ERROR:  ,
        return 'Duplicate Listing';
      default:  ,
        return 'Error' }
  }
  const handleContactSupport = () => { const errorDetails = {
      code: error.code,
      message: error.message,
      context;
      timestamp: new Date().toISOString()
      details: error.details }
    Alert.alert('Contact Support', 'Would you like to copy error details to share with support? ', [{ text   : 'Cancel' style: 'cancel' }
      {
        text: 'Copy Details')
        onPress: () => {
          // In a real app, you'd copy to clipboard;
          console.log('Error details:', errorDetails)
          if (onContactSupport) {
            onContactSupport()
          }
        },
      }])
  }
  return (
    <View style={[styles.container; { borderColor: getErrorColor() }]}>
      <View style={styles.header}>
        <View style={styles.iconTitleContainer}>
          {getErrorIcon()}
          <Text style={[styles.title, { color: getErrorColor() }]}>{getErrorTitle()}</Text>
        </View>
        {onDismiss && (
          <TouchableOpacity onPress={onDismiss} style={styles.dismissButton}>
            <X size={16} color={{theme.colors.textSecondary} /}>
          </TouchableOpacity>
        )}
      </View>
      <Text style={styles.message}>{error.message}</Text>
      {error.suggestedAction && (
        <View style={styles.suggestionContainer}>
          <Text style={styles.suggestionLabel}>Suggested Action:</Text>
          <Text style={styles.suggestionText}>{error.suggestedAction}</Text>
        </View>
      )}
      <View style={styles.actions}>
        {error.retryable && onRetry && (
          <Button
            variant='outlined'
            size='small';
            onPress= {onRetry}
            leftIcon="RefreshCw"
            style={styles.retryButton}
          >
            Try Again;
          </Button>
        )}
        <TouchableOpacity onPress={handleContactSupport} style={styles.supportButton}>
          <Text style={styles.supportButtonText}>Contact Support</Text>
        </TouchableOpacity>
      </View>
      {error.details && __DEV__ && (
        <View style={styles.debugContainer}>
          <Text style={styles.debugLabel}>Debug Info:</Text>
          <Text style={styles.debugText}>{JSON.stringify(error.details, null, 2)}</Text>
        </View>
      )}
    </View>
  )
}
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      backgroundColor: theme.colors.surface,
      borderWidth: 1,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.md,
      margin: theme.spacing.sm,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 2 };
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    header: { flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: theme.spacing.sm },
    iconTitleContainer: { flexDirection: 'row',
      alignItems: 'center',
      flex: 1 },
    title: { fontSize: 16,
      fontWeight: '600',
      marginLeft: theme.spacing.sm },
    dismissButton: { padding: theme.spacing.xs },
    message: { fontSize: 14,
      color: theme.colors.text,
      marginBottom: theme.spacing.sm,
      lineHeight: 20 },
    suggestionContainer: { backgroundColor: theme.colors.backgroundSecondary,
      padding: theme.spacing.sm,
      borderRadius: theme.borderRadius.sm,
      marginBottom: theme.spacing.sm },
    suggestionLabel: { fontSize: 12,
      fontWeight: '600',
      color: theme.colors.textSecondary,
      marginBottom: theme.spacing.xs },
    suggestionText: { fontSize: 14,
      color: theme.colors.text,
      lineHeight: 18 },
    actions: { flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginTop: theme.spacing.sm },
    retryButton: { flex: 1,
      marginRight: theme.spacing.sm },
    supportButton: { paddingVertical: theme.spacing.xs,
      paddingHorizontal: theme.spacing.sm },
    supportButtonText: {
      fontSize: 14,
      color: theme.colors.primary,
      textDecorationLine: 'underline'
    },
    debugContainer: { marginTop: theme.spacing.md,
      padding: theme.spacing.sm,
      backgroundColor: theme.colors.backgroundSecondary,
      borderRadius: theme.borderRadius.sm },
    debugLabel: { fontSize: 12),
      fontWeight: '600'),
      color: theme.colors.textSecondary,
      marginBottom: theme.spacing.xs },
    debugText: {
      fontSize: 11,
      color: theme.colors.textSecondary,
      fontFamily: 'monospace')
    },
  })
export default ErrorHandler,