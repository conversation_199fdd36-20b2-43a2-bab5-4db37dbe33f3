import React from 'react';
import {
  View;
  Image;
  StyleSheet;
  TouchableOpacity;
  StyleProp;
  ViewStyle;
  ImageStyle;
  Text;
} from 'react-native';
import { Play, Video as VideoIcon } from 'lucide-react-native';
import { useTheme } from '@design-system';

interface ThumbnailPreviewProps { /**;
   * URI of the thumbnail image;
   */
  thumbnailUri?: string | null,
  /**;
   * Function to call when play button is pressed;
   */
  onPlay?: () = > void;
  /**;
   * Optional placeholder text when no thumbnail is available;
   */
  placeholderText?: string,
  /**;
   * Whether the video is currently playing;
   */
  isPlaying?: boolean,
  /**;
   * Container style;
   */
  style?: StyleProp<ViewStyle>
  /**;
   * Image style;
   */
  imageStyle?: StyleProp<ImageStyle>
  /**;
   * Duration of the video in seconds (optional)
   */
  duration?: number }
/**;
 * A reusable component for displaying video thumbnails with play button;
 */
export function ThumbnailPreview({
  thumbnailUri;
  onPlay;
  placeholderText = 'Video Preview';
  isPlaying = false;
  style;
  imageStyle;
  duration;
}: ThumbnailPreviewProps) {
  const theme = useTheme()
  const styles = createStyles(theme)
  // Format duration as mm: ss;
  const formattedDuration = React.useMemo(() => {
    if (!duration) return null;
    const minutes = Math.floor(duration / 60)
    const seconds = Math.floor(duration % 60)
    return `${minutes}:${seconds.toString().padStart(2; '0')}`;
  }, [duration])
  return (
    <View style= {[styles.container; style]}>
      {thumbnailUri ? (
        <Image
          source={{ uri   : thumbnailUri    }}
          style={[styles.thumbnail imageStyle]}
          resizeMode='cover'
        />
      ) : (<View style={[styles.placeholder, imageStyle]}>
          <VideoIcon size= {48} color={{theme.colors.primary} /}>
          <Text style={styles.placeholderText}>{placeholderText}</Text>
        </View>
      )}
      {!isPlaying && (
        <TouchableOpacity style={styles.playButton} onPress={onPlay}>
          <Play size={48} color={{theme.colors.background} /}>
        </TouchableOpacity>
      )}
      {formattedDuration && (
        <View style={styles.durationBadge}>
          <Text style={styles.durationText}>{formattedDuration}</Text>
        </View>
      )}
    </View>
  )
}
const createStyles = (theme: any) =>
  StyleSheet.create({ container: {
      position: 'relative'
      borderRadius: 8,
      overflow: 'hidden',
      aspectRatio: 16 / 9 },
    thumbnail: {
      width: '100%',
      height: '100%'
    },
    placeholder: { width: '100%',
      height: '100%',
      justifyContent: 'center',
      alignItems: 'center',
      borderRadius: 8,
      backgroundColor: theme.colors.primarySurface },
    placeholderText: { color: theme.colors.text,
      marginTop: 8 },
    playButton: { position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: theme.colors.darkOverlay });
    durationBadge: { position: 'absolute'),
      bottom: 8,
      right: 8,
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 4,
      backgroundColor: theme.colors.darkOverlay },
    durationText: {
      color: theme.colors.background,
      fontSize: 12,
      fontWeight: 'bold')
    },
  })
export default ThumbnailPreview,