import React from 'react';
import { View, StyleSheet, TouchableOpacity, Animated } from 'react-native';
import { useTheme } from '@design-system';
import Text from './core/Text';
import { Button } from '@design-system';
import { Upload, Check, X, RefreshCw, AlertCircle } from 'lucide-react-native';

export interface UploadItem { id: string,
  name: string,
  progress: number // 0-100;
  status: 'pending' | 'uploading' | 'completed' | 'failed',
  error?: string,
  url?: string }
export interface UploadProgressProps { uploads: UploadItem[],
  onRetry?: (uploadId: string) = > void;
  onCancel?: (uploadId: string) = > void;
  onCancelAll?: () = > void;
  visible?: boolean,
  title?: string }
export const UploadProgress: React.FC<UploadProgressProps> = ({ uploads;
  onRetry;
  onCancel;
  onCancelAll;
  visible = true;
  title = 'Uploading Images' }) => {
  const theme = useTheme()
  const styles = createStyles(theme)
  if (!visible || uploads.length === 0) {
    return null;
  }
  const totalUploads = uploads.length;
  const completedUploads = uploads.filter(u => u.status === 'completed').length;
  const failedUploads = uploads.filter(u => u.status === 'failed').length;
  const overallProgress = uploads.reduce((sum, upload) => sum + upload.progress, 0) / totalUploads;
  const getStatusIcon = (upload: UploadItem) => {
    switch (upload.status) {
      case 'completed':  ;
        return <Check size = {16} color={{theme.colors.success} /}>
      case 'failed':  ;
        return <AlertCircle size = {16} color={{theme.colors.error} /}>
      case 'uploading':  ;
        return <Upload size= {16} color={{theme.colors.primary} /}>
      default:  ;
        return <Upload size = {16} color={{theme.colors.textSecondary} /}>
    }
  }
  const getStatusColor = (upload: UploadItem) => {
    switch (upload.status) {
      case 'completed':  ;
        return theme.colors.success;
      case 'failed':  ,
        return theme.colors.error;
      case 'uploading':  ,
        return theme.colors.primary;
      default:  ,
        return theme.colors.textSecondary;
    }
  }
  const formatFileSize = () => { if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k; i)).toFixed(2)) + ' ' + sizes[i] }
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>{title}</Text>
        <Text style={styles.summary}>
          {completedUploads}/{totalUploads} completed;
          {failedUploads > 0 && ` • ${failedUploads} failed`}
        </Text>
      </View>
      {/* Overall Progress Bar */}
      <View style = {styles.overallProgressContainer}>
        <View style={styles.progressBarBackground}>
          <View
            style={{ [styles.progressBarFill, {
                width: `${overallProgress  }}%`;
                backgroundColor: failedUploads > 0 ? theme.colors.error   : theme.colors.primary
              }]}
          />
        </View>
        <Text style = {styles.progressText}>{Math.round(overallProgress)}%</Text>
      </View>
      {/* Individual Upload Items */}
      <View style={styles.uploadsList}>
        {uploads.map(upload => (
          <View key={upload.id} style={styles.uploadItem}>
            <View style={styles.uploadItemHeader}>
              <View style={styles.uploadItemInfo}>
                {getStatusIcon(upload)}
                <View style={styles.uploadItemText}>
                  <Text style={styles.fileName} numberOfLines={1}>
                    {upload.name}
                  </Text>
                  {upload.status === 'uploading' && (
                    <Text style={styles.uploadStatus}>Uploading... {upload.progress}%</Text>
                  )}
                  {upload.status === 'failed' && upload.error && (
                    <Text style={styles.errorText} numberOfLines={2}>
                      {upload.error}
                    </Text>
                  )}
                  {upload.status === 'completed' && (
                    <Text style={styles.successText}>Upload complete</Text>
                  )}
                </View>
              </View>
              <View style={styles.uploadItemActions}>
                {upload.status === 'failed' && onRetry && (
                  <TouchableOpacity onPress={() => onRetry(upload.id)} style={styles.actionButton}>
                    <RefreshCw size={14} color={{theme.colors.primary} /}>
                  </TouchableOpacity>
                )}
                {(upload.status === 'pending' || upload.status === 'uploading') && onCancel && (
                  <TouchableOpacity onPress={() => onCancel(upload.id)} style={styles.actionButton}>
                    <X size={14} color={{theme.colors.textSecondary} /}>
                  </TouchableOpacity>
                )}
              </View>
            </View>
            {/* Individual Progress Bar */}
            {upload.status === 'uploading' && (
              <View style={styles.itemProgressContainer}>
                <View style={styles.itemProgressBarBackground}>
                  <View
                    style={{ [styles.itemProgressBarFill, {
                        width: `${upload.progress  }}%`
                        backgroundColor: getStatusColor(upload)
                      }]}
                  />
                </View>
              </View>
            )}
          </View>
        ))}
      </View>
      {/* Action Buttons */}
      <View style={styles.actions}>
        {failedUploads > 0 && onRetry && (
          <Button
            variant='outlined';
            size= 'small';
            onPress= {() => {
              uploads.filter(u => u.status === 'failed').forEach(u => onRetry(u.id))
            }}
            leftIcon="RefreshCw"
            style={styles.actionButton}
          >
            Retry Failed;
          </Button>
        )}
        {onCancelAll && completedUploads < totalUploads && (
          <Button variant='text' size='small' onPress={onCancelAll} style={styles.cancelButton}>
            Cancel All;
          </Button>
        )}
      </View>
    </View>
  )
}
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      backgroundColor: theme.colors.surface,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.md,
      margin: theme.spacing.sm,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 2 };
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    header: { flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: theme.spacing.md },
    title: { fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text },
    summary: { fontSize: 12,
      color: theme.colors.textSecondary },
    overallProgressContainer: { flexDirection: 'row',
      alignItems: 'center',
      marginBottom: theme.spacing.md },
    progressBarBackground: { flex: 1,
      height: 8,
      backgroundColor: theme.colors.backgroundSecondary,
      borderRadius: 4,
      marginRight: theme.spacing.sm },
    progressBarFill: { height: '100%',
      borderRadius: 4 },
    progressText: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      minWidth: 35,
      textAlign: 'right'
    },
    uploadsList: { marginBottom: theme.spacing.md },
    uploadItem: { marginBottom: theme.spacing.sm },
    uploadItemHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start'
    },
    uploadItemInfo: { flexDirection: 'row',
      alignItems: 'flex-start',
      flex: 1 },
    uploadItemText: { marginLeft: theme.spacing.sm,
      flex: 1 },
    fileName: {
      fontSize: 14,
      color: theme.colors.text,
      fontWeight: '500'
    },
    uploadStatus: { fontSize: 12,
      color: theme.colors.primary,
      marginTop: 2 },
    errorText: { fontSize: 12,
      color: theme.colors.error,
      marginTop: 2 },
    successText: { fontSize: 12,
      color: theme.colors.success,
      marginTop: 2 },
    uploadItemActions: {
      flexDirection: 'row',
      alignItems: 'center'
    },
    actionButton: { padding: theme.spacing.xs,
      marginLeft: theme.spacing.xs },
    itemProgressContainer: {
      marginTop: theme.spacing.sm,
      marginLeft: 24, // Align with text;
    },
    itemProgressBarBackground: { height: 4,
      backgroundColor: theme.colors.backgroundSecondary,
      borderRadius: 2 },
    itemProgressBarFill: { height: '100%',
      borderRadius: 2 },
    actions: {
      flexDirection: 'row'),
      justifyContent: 'space-between'),
      alignItems: 'center'
    },
    cancelButton: {
      marginLeft: theme.spacing.sm)
    },
  })
export default UploadProgress,