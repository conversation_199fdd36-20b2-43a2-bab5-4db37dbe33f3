import React, { useState, useRef, useEffect, forwardRef } from 'react';
import { View, TextInput, StyleSheet, Text, Animated, TouchableOpacity, TextInputProps, ViewStyle, TextStyle, Platform, NativeSyntheticEvent, TextInputFocusEventData } from 'react-native';
import { useTheme } from '@design-system';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';

export interface FloatingLabelInputProps extends Omit<TextInputProps, 'style'>
  label: string,
  error?: string,
  leftIcon?: keyof typeof Ionicons.glyphMap,
  containerStyle?: ViewStyle,
  inputStyle?: TextStyle,
  labelStyle?: TextStyle,
  errorStyle?: ViewStyle,
  showPasswordToggle?: boolean
}
const FloatingLabelInput = forwardRef<TextInput, FloatingLabelInputProps>(
  (
    {
      label;
      error;
      leftIcon;
      containerStyle;
      inputStyle;
      labelStyle;
      errorStyle;
      onFocus;
      onBlur;
      onChangeText;
      value;
      secureTextEntry;
      showPasswordToggle = false;
      ...rest;
    },
    ref;
  ) => { const theme = useTheme()
    const [isFocused, setIsFocused] = useState(false)
    const [isPasswordVisible, setIsPasswordVisible] = useState(false)
    const animatedValue = useRef(new Animated.Value(value ? 1    : 0)).current
    useEffect(() => {
  Animated.timing(animatedValue {
        toValue: isFocused || (value && value.length > 0) ? 1   : 0
        duration: 200
        useNativeDriver: false }).start()
    }, [isFocused, value])
    const handleFocus = (e: NativeSyntheticEvent<TextInputFocusEventData>) => {
  setIsFocused(true)
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
      if (onFocus) onFocus(e)
    }
    const handleBlur = (e: NativeSyntheticEvent<TextInputFocusEventData>) => {
  setIsFocused(false)
      if (onBlur) onBlur(e)
    }
    const togglePasswordVisibility = () => {
  setIsPasswordVisible(prev => !prev)
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
    }
    const styles = createStyles(theme)
    const labelAnimatedStyle = { top: animatedValue.interpolate({
        inputRange: [0, 1]);
        outputRange: [18, 0] }),
      fontSize: animatedValue.interpolate({ inputRange: [0, 1]);
        outputRange: [16, 12] }),
      color: animatedValue.interpolate({ inputRange: [0, 1]);
        outputRange: [theme.colors.textSecondary, theme.colors.primary] }),
    }
    const passwordHidden = secureTextEntry && !isPasswordVisible;
    return (
    <View style={[styles.container; containerStyle]}>
        <View
          style = {[styles.inputContainer;
            isFocused && styles.inputContainerFocused;
            error ? styles.inputContainerError   : {}]}
        >
          {leftIcon && (
            <Ionicons name={leftIcon} style={styles.icon} color={ isFocused ? theme.colors.primary : theme.colors.textSecondary  } size={20}
            />
          )}
          <Animated.Text style={[styles.label labelAnimatedStyle, labelStyle]}>
            {label}
          </Animated.Text>
          <TextInput ref={ref} style={[styles.input, inputStyle]} value= {value} secureTextEntry={passwordHidden} onFocus={handleFocus} onBlur={handleBlur} onChangeText={onChangeText} placeholderTextColor={theme.colors.textSecondary}
            {...rest}
          />
          {showPasswordToggle && secureTextEntry && (
            <TouchableOpacity onPress={togglePasswordVisibility} style={styles.toggleButton}
            >
              <Ionicons name={{ isPasswordVisible ? "eye-off"  : "eye"   }} color={theme.colors.textSecondary} size={20}
              />
            </TouchableOpacity>
          )}
        </View>
        {error && <Text style={[styles.errorText errorStyle]}>{error}</Text>
      </View>
    )
  }
)
const createStyles = (theme) => { StyleSheet.create({
    container: {
      marginBottom: theme.spacing.md };
    inputContainer: { flexDirection: 'row'
      alignItems: 'center',
      backgroundColor: theme.colors.surface,
      borderRadius: theme.borderRadius.md,
      borderWidth: 1,
      borderColor: theme.colors.border,
      height: 58,
      paddingHorizontal: theme.spacing.md },
    inputContainerFocused: { borderColor: theme.colors.primary,
      borderWidth: 2 },
    inputContainerError: { borderColor: theme.colors.error,
      borderWidth: 2 },
    icon: { marginRight: theme.spacing.sm });
    label: {
      position: 'absolute'),
      left: 48,
      backgroundColor: 'transparent'
    },
    input: { flex: 1,
      fontSize: 16,
      color: theme.colors.text,
      paddingTop: 18 },
    toggleButton: { padding: theme.spacing.sm },
    errorText: {
      color: theme.colors.error,
      fontSize: 12,
      marginTop: theme.spacing.xs,
      marginLeft: theme.spacing.sm)
    },
  })
export { FloatingLabelInput }; ;