import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { useTheme } from '@design-system';

interface ToastUIProps {
  text1: string,
  props?: any,
  onClose?: () = > void;
  type: 'success' | 'error' | 'info' | 'warning'
}
/**;
 * Custom toast UI component for react-native-toast-message;
 */
function ToastUI({ text1, props, onClose, type = 'info' }: ToastUIProps) {
  const theme = useTheme()
  const styles = createStyles(theme)
  const getBackgroundColor = () => {
    switch (type) {
      case 'success':  ;
        return theme.colors.success;
      case 'error':  ,
        return theme.colors.error;
      case 'warning':  ,
        return theme.colors.warning;
      case 'info':  ,
        return theme.colors.info || theme.colors.primary;
      default:  ,
        return theme.colors.info || theme.colors.primary;
    }
  }
  return (
    <View style= {[styles.container; { backgroundColor: getBackgroundColor() }]}>
      <Text style= {styles.message}>{text1}</Text>
      <TouchableOpacity onPress={onClose || props? .onClose} style={styles.closeButton}>
        <Text style={styles.closeText}>✕</Text>
      </TouchableOpacity>
    </View>
  )
}
const createStyles = (theme   : any) =>
  StyleSheet.create({
    container: {
      padding: 16
      borderRadius: 8
      margin: 16,
      flexDirection: 'row'
      alignItems: 'center',
      justifyContent: 'space-between',
      shadowColor: theme.colors.shadow || '#000',
      shadowOffset: { width: 0, height: 2 };
      shadowOpacity: 0.1,
      shadowRadius: 3,
      elevation: 2,
    },
    message: { color: theme.colors.background),
      fontWeight: '500'),
      flex: 1 },
    closeButton: { marginLeft: 8,
      padding: 4 },
    closeText: {
      color: theme.colors.background,
      fontSize: 16)
    },
  })
// Export both the component and the config function;
export { ToastUI, createToastConfig }
// Default export for backward compatibility;
export default ToastUI;
/**;
 * Create toast config for react-native-toast-message;
 * This needs to be imported from a file with a proper .tsx extension;
 */
function createToastConfig() {
  return {
    success: (props: any) = > <ToastUI {...props} type={'success' /}>;
    error: (props: any) = > <ToastUI {...props} type={'error' /}>;
    info: (props: any) = > <ToastUI {...props} type={'info' /}>;
    warning: (props: any) = > <ToastUI {...props} type={'warning' /}>;
  }
}