import { StyleSheet } from 'react-native';
import { useTheme } from '@design-system';

export const createAgreementTheme = () => { const theme = useTheme()
  return {
    colors: {
      primary: {
        light: '#EEF2FF',
        main: '#6366F1',
        dark: '#4338CA',
        text: theme.colors.background },
      secondary: { light: '#F1F5F9',
        main: theme.colors.textSecondary,
        dark: '#475569',
        text: theme.colors.text },
      success: { light: '#DCFCE7',
        main: theme.colors.success,
        dark: theme.colors.success,
        text: theme.colors.background },
      error: { light: '#FEE2E2',
        main: theme.colors.error,
        dark: theme.colors.error,
        text: theme.colors.background },
      warning: { light: '#FEF3C7',
        main: theme.colors.warning,
        dark: '#D97706',
        text: theme.colors.background },
      info: { light: '#E0F2FE',
        main: theme.colors.info,
        dark: '#0284C7',
        text: theme.colors.background },
      background: { default: theme.colors.background,
        paper: '#F8FAFC',
        contrast: theme.colors.text },
      border: {
        light: theme.colors.border,
        main: '#CBD5E1',
        dark: '#94A3B8'
      },
      text: {
        primary: theme.colors.text,
        secondary: theme.colors.textSecondary,
        disabled: '#94A3B8',
        hint: '#CBD5E1'
      },
    },
    spacing: { xs: 4,
      sm: 8,
      md: 16,
      lg: 24,
      xl: 32 },
    typography: { h1: {
        fontSize: 24,
        fontWeight: '700',
        lineHeight: 32 },
      h2: { fontSize: 20,
        fontWeight: '600',
        lineHeight: 28 },
      h3: { fontSize: 18,
        fontWeight: '600',
        lineHeight: 26 },
      body1: { fontSize: 16,
        fontWeight: '400',
        lineHeight: 24 },
      body2: { fontSize: 14,
        fontWeight: '400',
        lineHeight: 20 },
      caption: { fontSize: 12,
        fontWeight: '400',
        lineHeight: 16 },
    },
    shadows: { sm: {
        shadowColor: theme.colors.text,
        shadowOffset: {
          width: 0,
          height: 1 },
        shadowOpacity: 0.18,
        shadowRadius: 1.0,
        elevation: 1,
      },
      md: { shadowColor: theme.colors.text,
        shadowOffset: {
          width: 0,
          height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 3,
      },
      lg: { shadowColor: theme.colors.text,
        shadowOffset: {
          width: 0,
          height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 4.65,
        elevation: 5,
      },
    },
    borderRadius: { sm: 4,
      md: 8,
      lg: 12,
      xl: 16,
      full: 9999 },
    animation: { duration: {
        shortest: 150,
        shorter: 200,
        short: 250,
        standard: 300,
        complex: 375,
        enteringScreen: 225,
        leavingScreen: 195 },
      easing: { easeInOut: 'ease-in-out',
        easeOut: 'ease-out',
        easeIn: 'ease-in',
        sharp: 'cubic-bezier(0.4, 0, 0.6, 1)' },
    },
  }
}
// Remove the hook violation - don't call createAgreementTheme at module level // Components should use createAgreementTheme() inside their component body;
export const useAgreementTheme = () => createAgreementTheme()
// For backward compatibility, export a static theme without hooks;
export const AgreementTheme = {
  colors: {
    primary: {
      light: '#EEF2FF',
      main: '#6366F1',
      dark: '#4338CA',
      text: '#FFFFFF'
    },
    secondary: {
      light: '#F1F5F9',
      main: '#64748B',
      dark: '#475569',
      text: '#1E293B'
    },
    success: {
      light: '#DCFCE7',
      main: '#22C55E',
      dark: '#16A34A',
      text: '#FFFFFF'
    },
    error: {
      light: '#FEE2E2',
      main: '#EF4444',
      dark: '#DC2626',
      text: '#FFFFFF'
    },
    warning: {
      light: '#FEF3C7',
      main: '#F59E0B',
      dark: '#D97706',
      text: '#FFFFFF'
    },
    info: {
      light: '#E0F2FE',
      main: '#3B82F6',
      dark: '#0284C7',
      text: '#FFFFFF'
    },
    background: '#FFFFFF',
    surface: '#F8FAFC',
    text: '#1E293B',
    textSecondary: '#64748B',
    textMuted: '#94A3B8',
    border: '#E2E8F0',
    placeholder: '#CBD5E1'
  },
  spacing: { xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32 },
  typography: { h1: {
      fontSize: 24,
      fontWeight: '700',
      lineHeight: 32 },
    h2: { fontSize: 20,
      fontWeight: '600',
      lineHeight: 28 },
    h3: { fontSize: 18,
      fontWeight: '600',
      lineHeight: 26 },
    body1: { fontSize: 16,
      fontWeight: '400',
      lineHeight: 24 },
    body2: { fontSize: 14,
      fontWeight: '400',
      lineHeight: 20 },
    caption: { fontSize: 12,
      fontWeight: '400',
      lineHeight: 16 },
  },
  shadows: { sm: {
      shadowColor: '#1E293B',
      shadowOffset: {
        width: 0,
        height: 1 },
      shadowOpacity: 0.18,
      shadowRadius: 1.0,
      elevation: 1,
    },
    md: { shadowColor: '#1E293B',
      shadowOffset: {
        width: 0,
        height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 3,
    },
    lg: { shadowColor: '#1E293B',
      shadowOffset: {
        width: 0,
        height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 4.65,
      elevation: 5,
    },
  },
  borderRadius: { sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
    full: 9999 },
  animation: { duration: {
      shortest: 150,
      shorter: 200,
      short: 250,
      standard: 300,
      complex: 375,
      enteringScreen: 225,
      leavingScreen: 195 },
    easing: { easeInOut: 'ease-in-out',
      easeOut: 'ease-out',
      easeIn: 'ease-in',
      sharp: 'cubic-bezier(0.4, 0, 0.6, 1)' },
  },
}
export default AgreementTheme,