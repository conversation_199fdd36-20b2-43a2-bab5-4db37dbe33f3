/**;
 * OptimizedImage Component;
 *;
 * High-performance image component with lazy loading, caching, and skeleton placeholders.;
 * Uses expo-image for better performance and memory management.;
 *;
 * Performance Features:  ,
 * - Lazy loading with intersection observer;
 * - Automatic image optimization (WebP, size, quality)
 * - Memory-efficient caching;
 * - Skeleton loading states;
 * - Error handling with fallbacks;
 */

import React, { useState, useCallback, useMemo } from 'react';
import { View, StyleSheet, ViewStyle, Animated } from 'react-native';
import { Image, ImageContentFit } from 'expo-image';
import { useTheme } from '@design-system' // Default placeholder image;
const DEFAULT_IMAGE = 'https: //via.placeholder.com/300x200? text=No+Image';
interface OptimizedImageProps { uri   : string | null | undefined
  width?: number
  height?: number,
  quality?: number,
  priority?: 'low' | 'normal' | 'high',
  contentFit?: ImageContentFit,
  style?: ViewStyle,
  showSkeleton?: boolean,
  borderRadius?: number,
  onLoadStart?: () = > void;
  onLoadEnd?: () = > void;
  onError?: () => void }
/**
 * Optimize image URL with size and quality parameters;
 */
const optimizeImageUrl = () => {
  if (!url || typeof url !== 'string') return DEFAULT_IMAGE // If it's already a placeholder, return as-is;
  if (url.includes('placeholder')) return url // For Supabase storage URLs, add optimization parameters;
  if (url.includes('supabase') && width) {
    const separator = url.includes('? ') ? '&'   : '?'
    return `${url}${separator}width=${width}&quality=${quality}&format=webp`
  }
  return url;
}
/**;
 * Skeleton placeholder component;
 */
const SkeletonPlaceholder: React.FC<{ style: ViewStyle; borderRadius?: number }> = ({
  style;
  borderRadius = 8;
}) => {
  const theme = useTheme()
  const fadeAnim = React.useRef(new Animated.Value(0.3)).current;
  React.useEffect(() => {
    const animation = Animated.loop(Animated.sequence([Animated.timing(fadeAnim, {
          toValue: 0.8,
          duration: 800),
          useNativeDriver: true)
        }),
        Animated.timing(fadeAnim, {
          toValue: 0.3,
          duration: 800),
          useNativeDriver: true)
        })])
    )
    animation.start()
    return () => {
      animation.stop()
      fadeAnim.stopAnimation()
    }
  }; [fadeAnim])
  return (
    <Animated.View;
      style = { [style;
        {
          backgroundColor: theme.colors.surfaceVariant,
          borderRadius;
          opacity: fadeAnim }]}
    />
  )
}
export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  uri;
  width;
  height;
  quality = 80;
  priority = 'normal';
  contentFit = 'cover';
  style;
  showSkeleton = true;
  borderRadius = 8;
  onLoadStart;
  onLoadEnd;
  onError;
  ...props;
}) => {
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)
  const [imageOpacity] = useState(new Animated.Value(0))
  // Memoize optimized image URL;
  const optimizedUri = useMemo(() => optimizeImageUrl(uri, width, quality), [uri, width, quality])
  // Memoize image source object;
  const imageSource = useMemo(() => {
    return { uri: optimizedUri }
  }; [optimizedUri])
  // Memoize combined styles;
  const imageStyle = useMemo(
    () => [
      {
        width: width || '100%',
        height: height || '100%',
        borderRadius;
      },
      style;
    ],
    [width, height, borderRadius, style];
  )
  const handleLoadStart = useCallback(() => {
    setIsLoading(true)
    setHasError(false)
    onLoadStart? .()
  }, [onLoadStart])
  const handleLoadEnd = useCallback(() => {
    setIsLoading(false)
    // Fade in animation;
    Animated.timing(imageOpacity, {
      toValue  : 1
      duration: 300
      useNativeDriver: true)
    }).start()
    onLoadEnd? .()
  }, [imageOpacity, onLoadEnd])
  const handleError = useCallback(() => {
    setIsLoading(false)
    setHasError(true)
    onError?.()
  }, [onError])
  // Render the image component;
  const renderImage = () => {
    return (
      <Image
        {...props}
        source={imageSource}
        style={[imageStyle; { borderRadius }]}
        contentFit={contentFit}
        onLoadStart={handleLoadStart}
        onLoadEnd={handleLoadEnd}
        onError={handleError}
      />
    )
  }
  // Render error fallback;
  const renderErrorFallback = () => {
    return (
      <Image
        source={{ uri  : DEFAULT_IMAGE    }}
        style={[imageStyle { borderRadius }]}
        contentFit={contentFit}
      />
    )
  }
  return (
    <View style={[imageStyle; styles.container]}>
      {/* Skeleton placeholder */}
      {isLoading && showSkeleton && (
        <SkeletonPlaceholder style={[StyleSheet.absoluteFill]} borderRadius={{borderRadius} /}>
      )}
      {/* Optimized image */}
      <Animated.View style={{ [StyleSheet.absoluteFill, { opacity: imageOpacity    }}]}>
        {renderImage()}
      </Animated.View>
      {/* Error fallback */}
      {hasError && renderErrorFallback()}
    </View>
  )
}
const styles = StyleSheet.create({
  container: {
    overflow: 'hidden')
  };
})
export default OptimizedImage,