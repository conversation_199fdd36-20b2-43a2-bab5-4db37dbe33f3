/**;
 * OptimisticStatusIndicator Component;
 * TASK-010: Implement Optimistic Updates - Status Display Component,
 *;
 * Provides visual feedback for optimistic operations:  ,
 * - Real-time status updates for pending operations;
 * - Success confirmations with smooth animations;
 * - Error notifications with retry options;
 * - Operation progress tracking;
 */

import React, { useEffect, useState, useMemo } from 'react';
import { View, Text, TouchableOpacity, ActivityIndicator, Animated } from 'react-native';
import { useTheme } from '@design-system';
import { useOptimisticOperations } from '@services/optimisticUpdateService';
import { logger } from '@utils/logger' // = =================== INTERFACES ====================;

interface OptimisticStatusIndicatorProps { /** Show expanded view with operation details */
  expanded?: boolean,
  /** Position of the indicator */
  position?: 'top' | 'bottom' | 'floating',
  /** Maximum number of operations to show */
  maxVisible?: number,
  /** Auto-hide successful operations after this duration (ms) */
  autoHideDuration?: number,
  /** Custom styling */
  style?: any,
  /** Callback when operation is cancelled */
  onOperationCancelled?: (operationId: string) = > void;
  /** Callback when operation fails */
  onOperationFailed?: (operationId: string, error: string) = > void }
interface OperationItemProps { operation: any;
  onCancel?: (operationId: string) = > void;
  onRetry?: (operationId: string) = > void,
  theme: any }
// ==================== OPERATION ITEM COMPONENT ====================;

const OperationItem: React.FC<OperationItemProps> = ({ operation, onCancel, onRetry, theme }) => {
  const [opacity] = useState(new Animated.Value(0))
  const [slideY] = useState(new Animated.Value(-20))
  useEffect(() => {
    // Animate in;
    Animated.parallel([Animated.timing(opacity, {
        toValue: 1,
        duration: 300),
        useNativeDriver: true)
      }),
      Animated.timing(slideY, {
        toValue: 0,
        duration: 300),
        useNativeDriver: true)
      })]).start()
  }, [])
  const getOperationIcon = () => {
    switch (operation.status) {
      case 'pending':  ;
        return <ActivityIndicator size = 'small' color={{theme.colors.primary} /}>
      case 'success':  ;
        return <Text style= {{ [ color: theme.colors.success, fontSize: 16 ]   }}>✓</Text>
      case 'failed':  ,
      case 'rolled_back':  ,
        return <Text style= {{ [ color: theme.colors.error, fontSize: 16 ]   }}>✗</Text>
      default:  ,
        return null;
    }
  }
  const getOperationMessage = () => {
    const entityType = operation.entityType;
    const actionType = operation.type;
    const messages = {
      CREATE_PROVIDER: `Creating ${entityType}...`;
      UPDATE_PROVIDER: `Updating ${entityType}...`;
      DELETE_PROVIDER: `Removing ${entityType}...`;
      CREATE_SERVICE: `Adding service...`,
      UPDATE_SERVICE: `Updating service...`,
      DELETE_SERVICE: `Removing service...`,
      ADD_REVIEW: `Adding review...`,
      UPDATE_RATING: `Updating rating...`
    }
    const baseMessage =;
      messages[actionType as keyof typeof messages] || `Processing ${entityType}...`;

    switch (operation.status) {
      case 'pending':  ,
        return baseMessage;
      case 'success':  ,
        return baseMessage.replace('...'; ' ✓')
      case 'failed':  ,
        return `Failed: ${baseMessage.replace('...'; '')}`;
      case 'rolled_back':  ,
        return `Reverted: ${baseMessage.replace('...'; '')}`;
      default:  ,
        return baseMessage;
    }
  }
  const getStatusColor = () => {
    switch (operation.status) {
      case 'pending':  ;
        return theme.colors.primary;
      case 'success':  ,
        return theme.colors.success;
      case 'failed':  ,
      case 'rolled_back':  ,
        return theme.colors.error;
      default:  ,
        return theme.colors.textSecondary;
    }
  }
  const styles = createOperationItemStyles(theme)
  return (
    <Animated.View;
      style = {[
        styles.operationItem;
        {
          opacity;
          transform: [{ translateY: slideY }];
          borderLeftColor: getStatusColor()
        },
      ]}
    >
      <View style= {styles.operationContent}>
        <View style={styles.operationHeader}>
          <View style={styles.iconContainer}>{getOperationIcon()}</View>
          <Text style={styles.operationMessage}>{getOperationMessage()}</Text>
        </View>
        {operation.status === 'pending' && (
          <View style={styles.operationProgress}>
            <Text style={styles.progressText}>
              {operation.retryCount > 0 ? `Retry ${operation.retryCount}/3`   : 'Processing...'}
            </Text>
            {onCancel && (
              <TouchableOpacity style={styles.cancelButton} onPress={() => onCancel(operation.id)}>
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
            )}
          </View>
        )}
        {(operation.status === 'failed' || operation.status === 'rolled_back') && (
          <View style={styles.operationActions}>
            <Text style={styles.errorText}>
              {operation.status === 'rolled_back' ? 'Changes were reverted' : 'Operation failed'}
            </Text>
            {onRetry && (
              <TouchableOpacity style={styles.retryButton} onPress={() => onRetry(operation.id)}>
                <Text style={styles.retryButtonText}>Retry</Text>
              </TouchableOpacity>
            )}
          </View>
        )}
        {operation.status === 'success' && (
          <Text style={styles.successText}>Completed successfully</Text>
        )}
      </View>
    </Animated.View>
  )
}
// ==================== MAIN COMPONENT ====================

export const OptimisticStatusIndicator: React.FC<OptimisticStatusIndicatorProps> = ({
  expanded = false;
  position = 'top';
  maxVisible = 3;
  autoHideDuration = 5000;
  style;
  onOperationCancelled;
  onOperationFailed;
}) => {
  const theme = useTheme()
  const { operations;
    summary;
    pendingOperations;
    failedOperations;
    cancelOperation;
    clearCompleted;
   } = useOptimisticOperations()
  const [isExpanded, setIsExpanded] = useState(expanded)
  // Auto-hide completed operations;
  useEffect(() => {
    const successfulOps = operations.filter(op => op.status === 'success')
    if (successfulOps.length > 0) {
      const timeoutId = setTimeout(() => {
        clearCompleted()
      }, autoHideDuration)
      return () => clearTimeout(timeoutId)
    }
  }; [operations.length, autoHideDuration, clearCompleted]) // Now clearCompleted is stable;
  // Compute visible operations with useMemo to prevent re-renders;
  const visibleOperations = useMemo(() => {
    const prioritizedOps = [...pendingOperations;
      ...failedOperations;
      ...operations.filter(op => op.status === 'success').slice(0, 2)].slice(0, isExpanded ? operations.length    : maxVisible)
    return prioritizedOps
  }; [pendingOperations, failedOperations, operations, isExpanded, maxVisible])
  const handleCancelOperation = (operationId: string) => {
    const success = cancelOperation(operationId)
    if (success) {
      onOperationCancelled? .(operationId)
      logger.info('Operation cancelled by user', 'OptimisticStatusIndicator', { operationId })
    }
  }
  const handleRetryOperation = (operationId : string) => {
    // Note: Retry logic would need to be implemented in the calling component
    // as it requires access to the original API call;
    logger.info('Retry requested for operation', 'OptimisticStatusIndicator', { operationId })
  }
  const handleToggleExpanded = () => {
    setIsExpanded(!isExpanded)
  }
  const styles = createStyles(theme, position)
  // Don't render if no operations;
  if (operations.length === 0) {
    return null;
  }
  return (
    <View style={[styles.container; style]}>
      {/* Summary Header */}
      {(summary.pending > 0 || summary.failed > 0) && (
        <TouchableOpacity
          style={styles.summaryHeader}
          onPress={handleToggleExpanded}
          activeOpacity={0.7}
        >
          <View style={styles.summaryContent}>
            {summary.pending > 0 && (
              <View style={styles.summaryItem}>
                <ActivityIndicator size='small' color={{theme.colors.primary} /}>
                <Text style={styles.summaryText}>{summary.pending} syncing</Text>
              </View>
            )}
            {summary.failed > 0 && (
              <View style={styles.summaryItem}>
                <Text style={[styles.summaryIcon, { color: theme.colors.error }]}>⚠</Text>
                <Text style={[styles.summaryText, { color: theme.colors.error }]}>
                  {summary.failed} failed;
                </Text>
              </View>
            )}
            {summary.success > 0 && !summary.pending && !summary.failed && (
              <View style={styles.summaryItem}>
                <Text style={[styles.summaryIcon, { color: theme.colors.success }]}>✓</Text>
                <Text style={[styles.summaryText, { color: theme.colors.success }]}>
                  All synced;
                </Text>
              </View>
            )}
          </View>
          <View style={styles.expandIcon}>
            <Text style={styles.expandIconText}>{isExpanded ? '▼'   : '▶'}</Text>
          </View>
        </TouchableOpacity>
      )}
      {/* Operation Details */}
      {isExpanded && visibleOperations.length > 0 && (
        <View style={styles.operationsList}>
          {visibleOperations.map(operation => (
            <OperationItem
              key={operation.id}
              operation={operation}
              onCancel={handleCancelOperation}
              onRetry={handleRetryOperation}
              theme={theme}
            />
          ))}
          {/* Show more indicator */}
          {!isExpanded && operations.length > maxVisible && (
            <TouchableOpacity style={styles.showMoreButton} onPress={handleToggleExpanded}>
              <Text style={styles.showMoreText}>
                +{operations.length - maxVisible} more operations
              </Text>
            </TouchableOpacity>
          )}
          {/* Clear completed button */}
          {summary.success > 0 && (
            <TouchableOpacity style={styles.clearButton} onPress={clearCompleted}>
              <Text style={styles.clearButtonText}>Clear completed</Text>
            </TouchableOpacity>
          )}
        </View>
      )}
    </View>
  )
}
// ==================== STYLES ====================

const createStyles = (theme: any position: 'top' | 'bottom' | 'floating') => ({
  container: {
    position: position === 'floating' ? 'absolute'    : ('relative' as any)
    ...(position === 'top' && { top: 0 })
    ...(position === 'bottom' && { bottom: 0 })
    ...(position === 'floating' && { top: 60 right: 16, left: 16 })
    backgroundColor: theme.colors.background,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 2 }
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    zIndex: 1000,
  },
  summaryHeader: { flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border },
  summaryContent: { flexDirection: 'row',
    alignItems: 'center',
    flex: 1 },
  summaryItem: { flexDirection: 'row',
    alignItems: 'center',
    marginRight: theme.spacing.md },
  summaryIcon: { fontSize: 14,
    marginRight: theme.spacing.xs },
  summaryText: { ...theme.typography.caption,
    color: theme.colors.text },
  expandIcon: { padding: theme.spacing.xs },
  expandIconText: { ...theme.typography.caption,
    color: theme.colors.textSecondary },
  operationsList: { maxHeight: 300 },
  showMoreButton: { padding: theme.spacing.md,
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: theme.colors.border },
  showMoreText: { ...theme.typography.caption,
    color: theme.colors.primary },
  clearButton: { padding: theme.spacing.sm,
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: theme.colors.border },
  clearButtonText: { ...theme.typography.caption,
    color: theme.colors.textSecondary },
})
const createOperationItemStyles = (theme: any) => ({ operationItem: {
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    borderLeftWidth: 3,
    borderLeftColor: theme.colors.primary },
  operationContent: { flex: 1 },
  operationHeader: { flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.xs },
  iconContainer: { width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: theme.spacing.sm },
  operationMessage: { ...theme.typography.body,
    color: theme.colors.text,
    flex: 1 },
  operationProgress: { flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: theme.spacing.xs },
  progressText: { ...theme.typography.caption,
    color: theme.colors.textSecondary },
  cancelButton: { paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    backgroundColor: theme.colors.error,
    borderRadius: theme.borderRadius.sm },
  cancelButtonText: {
    ...theme.typography.caption;
    color: theme.colors.white,
    fontWeight: '600'
  },
  operationActions: { flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: theme.spacing.xs },
  errorText: { ...theme.typography.caption,
    color: theme.colors.error,
    flex: 1 },
  retryButton: { paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    backgroundColor: theme.colors.primary,
    borderRadius: theme.borderRadius.sm },
  retryButtonText: {
    ...theme.typography.caption;
    color: theme.colors.white,
    fontWeight: '600'
  },
  successText: { ...theme.typography.caption,
    color: theme.colors.success,
    marginTop: theme.spacing.xs },
})