import React from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { colorWithOpacity, type Theme } from '@design-system';
import { useTheme } from '@design-system';

interface ProgressProps { value: number,
  max?: number,
  color?: string,
  backgroundColor?: string,
  height?: number,
  style?: ViewStyle }
export const Progress: React.FC<ProgressProps> = ({
  value;
  max = 100;
  color = '#0284c7', // Default primary color;
  backgroundColor = '#E5E7EB';
  height = 4;
  style;
}) => {
  const theme = useTheme()
  const styles = createStyles(theme)
  const percentage = Math.min(Math.max(0, (value / max) * 100), 100)
  return (
    <View
      style = {[
        styles.container;
        {
          backgroundColor;
          height;
        },
        style;
      ]}
    >
      <View
        style = {[styles.progress;
          {
            width: `${percentage}%`;
            backgroundColor: color,
            height;
          }]}
      />
    </View>
  )
}
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      borderRadius: 2,
      overflow: 'hidden'
    });
    progress: {
      borderRadius: 2)
    },
  })
export default Progress,