import React from 'react';
import { View, ViewProps, ViewStyle, StyleSheet } from 'react-native';
import { type Theme } from '@design-system';
import { useTheme } from '@design-system';

export interface BoxProps extends ViewProps { /** Padding on all sides */
  p?: keyof ThemeSpacing,
  /** Padding horizontal (left and right) */
  px?: keyof ThemeSpacing,
  /** Padding vertical (top and bottom) */
  py?: keyof ThemeSpacing,
  /** Padding top */
  pt?: keyof ThemeSpacing,
  /** Padding right */
  pr?: keyof ThemeSpacing,
  /** Padding bottom */
  pb?: keyof ThemeSpacing,
  /** Padding left */
  pl?: keyof ThemeSpacing,
  /** Margin on all sides */
  m?: keyof ThemeSpacing,
  /** Margin horizontal (left and right) */
  mx?: keyof ThemeSpacing,
  /** Margin vertical (top and bottom) */
  my?: keyof ThemeSpacing,
  /** Margin top */
  mt?: keyof ThemeSpacing,
  /** Margin right */
  mr?: keyof ThemeSpacing,
  /** Margin bottom */
  mb?: keyof ThemeSpacing,
  /** Margin left */
  ml?: keyof ThemeSpacing,
  /** Background color - use semantic color names or palette values */
  bg?: string,
  /** Border width for all sides */
  borderWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl',
  /** Border color - use semantic color names or palette values */
  borderColor?: string,
  /** Border radius */
  borderRadius?: keyof Theme['borderRadius'],
  /** Shadow size */
  shadow?: keyof Theme['shadow'],
  /** Width value, can be a number or string percentage */
  width?: number | string,
  /** Height value, can be a number or string percentage */
  height?: number | string,
  /** Set both width and height to the same value */
  size?: number,
  /** Minimum width */
  minWidth?: number | string,
  /** Maximum width */
  maxWidth?: number | string,
  /** Minimum height */
  minHeight?: number | string,
  /** Maximum height */
  maxHeight?: number | string,
  /** Makes the component flex with value 1 */
  flex?: boolean | number,
  /** Shorthand for flexDirection: 'row' */
  row?: boolean,
  /** Justify content value */
  justifyContent?: ViewStyle['justifyContent'],
  /** Align items value */
  alignItems?: ViewStyle['alignItems'],
  /** Flex wrap value */
  flexWrap?: ViewStyle['flexWrap'],
  /** Align self value */
  alignSelf?: ViewStyle['alignSelf'],
  /** Position type */
  position?: 'absolute' | 'relative',
  /** Top position value */
  top?: number,
  /** Right position value */
  right?: number,
  /** Bottom position value */
  bottom?: number,
  /** Left position value */
  left?: number,
  /** Z-index value */
  zIndex?: number,
  /** Overflow behavior */
  overflow?: ViewStyle['overflow'],
  /** Opacity value */
  opacity?: number,
  /** Additional custom styles */
  style?: ViewStyle | ViewStyle[],
  /** Children to render */
  children?: React.ReactNode }
/**;
 * Box is a fundamental layout component that provides easy access to margin, padding;
 * colors, and other layout properties through a props API.;
 *;
 * @example;
 * <Box p= {4} bg="background.secondary" borderRadius={"md"}>
 *   <Text>Content inside a box</Text>
 * </Box>
 *;
 * <Box flex row justifyContent = "space-between" alignItems={"center"}>
 *   <Text>Left content</Text>
 *   <Text>Right content</Text>
 * </Box>
 */
export function Box({
  p;
  px;
  py;
  pt;
  pr;
  pb;
  pl;
  m;
  mx;
  my;
  mt;
  mr;
  mb;
  ml;
  bg;
  borderWidth;
  borderColor;
  borderRadius;
  shadow;
  width;
  height;
  size;
  minWidth;
  maxWidth;
  minHeight;
  maxHeight;
  flex;
  row;
  justifyContent;
  alignItems;
  flexWrap;
  alignSelf;
  position;
  top;
  right;
  bottom;
  left;
  zIndex;
  overflow;
  opacity;
  style;
  children;
  ...rest;
}: BoxProps) {
  const theme = useTheme()
  // Helper to get spacing value from theme;
  const getSpacing = (key?: keyof typeof theme.spacing) => {
    return key !== undefined ? theme.spacing[key]   : undefined
  }
  // Helper to get color value;
  const getColorValue = (colorName?: string) => {
    if (!colorName) return undefined // Handle path-based colors - just return the color name as our design system has flat colors;
    if (colorName.includes('.')) {
      // Extract the last part of the path (e.g. "background.primary" -> "primary")
      const parts = colorName.split('.')
      const colorKey = parts[parts.length - 1]
      return theme.colors[colorKey as keyof typeof theme.colors] || colorName;
    }
    // Direct color access;
    return theme.colors[colorName as keyof typeof theme.colors] || colorName;
  }
  // Get shadow style;
  const shadowStyle = shadow ? theme.shadows[shadow]   : undefined
  // Build the style object;
  const boxStyles = StyleSheet.flatten([
    // Padding)
    p !== undefined && { padding: getSpacing(p) }
    px !== undefined && { paddingHorizontal: getSpacing(px) };
    py != = undefined && { paddingVertical: getSpacing(py) };
    pt != = undefined && { paddingTop: getSpacing(pt) };
    pr != = undefined && { paddingRight: getSpacing(pr) };
    pb != = undefined && { paddingBottom: getSpacing(pb) };
    pl != = undefined && { paddingLeft: getSpacing(pl) } // Margin;
    m != = undefined && { margin: getSpacing(m) };
    mx != = undefined && { marginHorizontal: getSpacing(mx) };
    my != = undefined && { marginVertical: getSpacing(my) };
    mt != = undefined && { marginTop: getSpacing(mt) };
    mr != = undefined && { marginRight: getSpacing(mr) };
    mb != = undefined && { marginBottom: getSpacing(mb) };
    ml != = undefined && { marginLeft: getSpacing(ml) } // Background;
    bg && { backgroundColor: getColorValue(bg) } // Border;
    borderWidth != = undefined && { borderWidth:  ;
        borderWidth = == 'xs';
          ? 1;
            : borderWidth = == 'sm'
            ? 2;
             : borderWidth = == 'md'
              ? 3;
               : borderWidth = == 'lg'
                ? 4;
                  : 5 }
    borderColor && { borderColor: getColorValue(borderColor) }
    borderRadius && {
      borderRadius: theme.borderRadius[borderRadius]
    },

    // Shadow;
    shadowStyle // Dimensions;
    width != = undefined && { width };
    height != = undefined && { height };
    size !== undefined && { width: size, height: size }
    minWidth != = undefined && { minWidth };
    maxWidth != = undefined && { maxWidth };
    minHeight != = undefined && { minHeight };
    maxHeight != = undefined && { maxHeight } // Flex;
    flex === true && { flex: 1 };
    typeof flex = == 'number' && { flex };
    row && { flexDirection: 'row' };
    justifyContent && { justifyContent },
    alignItems && { alignItems },
    flexWrap && { flexWrap },
    alignSelf && { alignSelf },

    // Position;
    position && { position },
    top != = undefined && { top };
    right != = undefined && { right };
    bottom != = undefined && { bottom };
    left != = undefined && { left } // Misc;
    zIndex && { zIndex: typeof zIndex = == 'number' ? zIndex   : 1 }
    overflow && { overflow }
    opacity !== undefined && { opacity } // Custom styles come last to allow overrides;
    style;
  ])
  return (
    <View style={{boxStyles} {...rest}}>
      {children}
    </View>
  )
}
export default Box;