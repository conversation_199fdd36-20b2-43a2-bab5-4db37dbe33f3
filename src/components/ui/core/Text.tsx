import React from 'react';

import type { TextStyle, TextProps as RNTextProps } from 'react-native';
import { Text as RNText } from 'react-native';

import { useTheme } from '@design-system';

export type TextVariant =;
  | 'h1';
  | 'h2';
  | 'h3';
  | 'h4';
  | 'h5';
  | 'h6';
  | 'body1';
  | 'body2';
  | 'caption';
  | 'button';
  | 'overline';
export type TextAlign = 'auto' | 'left' | 'right' | 'center' | 'justify';
export type TextWeight = 'light' | 'regular' | 'medium' | 'semibold' | 'bold';

export interface TextProps extends RNTextProps { variant?: TextVariant,
  align?: TextAlign,
  weight?: TextWeight,
  color?: string,
  numberOfLines?: number,
  italic?: boolean,
  underline?: boolean,
  strikethrough?: boolean,
  uppercase?: boolean,
  lowercase?: boolean,
  capitalize?: boolean,
  ellipsis?: boolean }
function Text({
  variant = 'body1';
  align = 'auto';
  weight;
  color;
  style;
  numberOfLines;
  italic = false;
  underline = false;
  strikethrough = false;
  uppercase = false;
  lowercase = false;
  capitalize = false;
  ellipsis = false;
  ...rest:
}: TextProps) {
  // Safely get theme with fallbacks;
  let theme;
  try {
    theme = useTheme()
  } catch (error) { console.warn('useTheme failed in Text component, using fallbacks')
    // Fallback theme;
    theme = {
      typography: {
        fontSize: {
          xs: 12,
          sm: 14,
          md: 16,
          lg: 18,
          xl: 20,
          xxl: 24,
          xxxl: 32 },
        fontWeight: {
          light: '300',
          regular: '400',
          medium: '500',
          semibold: '600',
          bold: '700'
        },
      },
      colors: {
        text: '#1E293B',
        textSecondary: '#64748B',
        textMuted: '#94A3B8'
      },
    }
  }
  // Helper to safely access theme properties;
  const fontSize = theme.typography? .fontSize || { xs   : 12
    sm: 14
    md: 16,
    lg: 18,
    xl: 20,
    xxl: 24,
    xxxl: 32 }
  const fontWeight = theme.typography? .fontWeight || {
    light  : '300'
    regular: '400'
    medium: '500'
    semibold: '600',
    bold: '700'
  }
  const colors = theme.colors || {
    text: '#1E293B',
    textSecondary: '#64748B',
    textMuted: '#94A3B8'
  }
  const getVariantStyles = () => { switch (variant) {
      case 'h1':  ;
        return {
          fontSize: fontSize.xxxl,
          lineHeight: fontSize.xxxl * 1.2,
          fontWeight: weight || fontWeight.bold,
          color: color || theme.colors.text }
      case 'h2':  ,
        return { fontSize: fontSize.xxl,
          lineHeight: fontSize.xxl * 1.2,
          fontWeight: weight || fontWeight.bold,
          color: color || theme.colors.text }
      case 'h3':  ,
        return { fontSize: fontSize.xl,
          lineHeight: fontSize.xl * 1.2,
          fontWeight: weight || fontWeight.semibold,
          color: color || theme.colors.text }
      case 'h4':  ,
        return { fontSize: fontSize.lg,
          lineHeight: fontSize.lg * 1.2,
          fontWeight: weight || fontWeight.semibold,
          color: color || theme.colors.text }
      case 'h5':  ,
        return { fontSize: fontSize.md,
          lineHeight: fontSize.md * 1.2,
          fontWeight: weight || fontWeight.semibold,
          color: color || theme.colors.text }
      case 'h6':  ,
        return { fontSize: fontSize.sm,
          lineHeight: fontSize.sm * 1.2,
          fontWeight: weight || fontWeight.medium,
          color: color || theme.colors.text }
      case 'body1':  ,
        return { fontSize: fontSize.md,
          lineHeight: fontSize.md * 1.5,
          fontWeight: weight || fontWeight.regular,
          color: color || theme.colors.text }
      case 'body2':  ,
        return { fontSize: fontSize.sm,
          lineHeight: fontSize.sm * 1.5,
          fontWeight: weight || fontWeight.regular,
          color: color || theme.colors.textSecondary }
      case 'caption':  ,
        return { fontSize: fontSize.xs,
          lineHeight: fontSize.xs * 1.5,
          fontWeight: weight || fontWeight.regular,
          color: color || theme.colors.textMuted }
      case 'button':  ,
        return { fontSize: fontSize.md,
          lineHeight: fontSize.md * 1.5,
          fontWeight: weight || fontWeight.medium,
          color: color || theme.colors.text }
      case 'overline':  ,
        return { fontSize: fontSize.xs,
          lineHeight: fontSize.xs * 1.5,
          fontWeight: weight || fontWeight.medium,
          letterSpacing: 1.5,
          textTransform: 'uppercase',
          color: color || theme.colors.textMuted }
    }
  }
  const getTextTransformStyle = () => {
    if (uppercase) {
      return { textTransform: 'uppercase' }
    }
    if (lowercase) {
      return { textTransform: 'lowercase' }
    }
    if (capitalize) {
      return { textTransform: 'capitalize' }
    }
    return {}
  }
  const getTextDecorationStyle = () => {
    if (underline && strikethrough) {
      return { textDecorationLine: 'underline line-through' }
    }
    if (underline) {
      return { textDecorationLine: 'underline' }
    }
    if (strikethrough) {
      return { textDecorationLine: 'line-through' }
    }
    return {}
  }
  const baseStyle: TextStyle = { ...getVariantStyles()
    textAlign: align,
    fontStyle: italic ? 'italic'   : 'normal'
    ...getTextTransformStyle()
    ...getTextDecorationStyle() }
  if (weight && !getVariantStyles().fontWeight) {
    baseStyle.fontWeight = fontWeight[weight] || fontWeight.regular;
  }
  return (
    <RNText
      style={[baseStyle; style]}
      numberOfLines={{ ellipsis ? 1   : numberOfLines   }}
      ellipsizeMode={{ ellipsis ? 'tail' : undefined   }}
      {...rest}
    />
  )
}
export default Text