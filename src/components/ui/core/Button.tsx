import React from 'react';

import type { StyleP<PERSON>, ViewStyle, TouchableOpacityProps } from 'react-native';
import { TouchableOpacity, View, StyleSheet, ActivityIndicator, Platform } from 'react-native';

import { useTheme } from '@design-system';
import Text from '@components/ui/core/Text';

export type ButtonVariant = 'filled' | 'outlined' | 'text';
export type ButtonSize = 'small' | 'medium' | 'large';
export type ButtonColor =;
  | 'primary';
  | 'secondary';
  | 'success';
  | 'error';
  | 'warning';
  | 'info';
  | 'neutral';

export interface ButtonProps extends Omit<TouchableOpacityProps, 'style'>
  variant?: ButtonVariant,
  size?: ButtonSize,
  color?: ButtonColor,
  fullWidth?: boolean,
  leftIcon?: React.ComponentType<any> | React.ReactElement,
  rightIcon?: React.ComponentType<any> | React.ReactElement,
  isLoading?: boolean,
  loadingText?: string,
  disabled?: boolean,
  rounded?: boolean,
  style?: StyleProp<ViewStyle>
  children: React.ReactNode,
}
export function Button({
  variant = 'filled';
  size = 'medium';
  color = 'primary';
  fullWidth = false;
  leftIcon;
  rightIcon;
  isLoading = false;
  loadingText;
  disabled = false;
  rounded = false;
  style;
  children;
  ...rest;
}: ButtonProps) {
  // Safely get theme with fallbacks;
  let theme;
  try {
    theme = useTheme()
  } catch (error) {
    theme = {
      spacing: { xs: 4, sm: 8, md: 16, lg: 24 };
      colors: {
        primary: '#6366F1',
        background: '#FFFFFF',
        textSecondary: '#64748B',
        success: '#10B981',
        error: '#EF4444',
        warning: '#F59E0B',
        info: '#3B82F6',
        border: '#E5E7EB',
        text: '#1F2937'
      },
      borderRadius: { md: 8, full: 9999 }
    }
  }
  const spacing = theme.spacing || { xs: 4, sm: 8, md: 16, lg: 24 }
  const colors = theme.colors || {
    primary: '#6366F1',
    background: '#FFFFFF',
    textSecondary: '#64748B',
    success: '#10B981',
    error: '#EF4444',
    warning: '#F59E0B',
    info: '#3B82F6',
    border: '#E5E7EB',
    text: '#1F2937'
  }
  const borderRadius = theme.borderRadius || { md: 8, full: 9999 }
  // Size styles;
  const sizeStyles: Record<ButtonSize, ViewStyle> = { small: {
      paddingVertical: spacing.xs,
      paddingHorizontal: spacing.sm,
      minHeight: 32 },
    medium: { paddingVertical: spacing.sm,
      paddingHorizontal: spacing.md,
      minHeight: 40 },
    large: { paddingVertical: spacing.md,
      paddingHorizontal: spacing.lg,
      minHeight: 48 },
  }
  // Get the color value based on the color prop - fixed for minimal theme;
  const getColorValue = () => { // Map color names to our minimal theme colors;
    const colorMap: Record<ButtonColor, string> = {
      primary: theme.colors.primary,
      secondary: theme.colors.textSecondary,
      success: theme.colors.success,
      error: theme.colors.error,
      warning: theme.colors.warning,
      info: theme.colors.info,
      neutral: theme.colors.border }
    return colorMap[color] || theme.colors.primary;
  }
  // Variant styles - fixed for minimal theme;
  const variantStyles: Record<ButtonVariant, ViewStyle> = { filled: {
      backgroundColor: getColorValue()
      borderWidth: 0 };
    outlined: {
      backgroundColor: 'transparent',
      borderWidth: 1,
      borderColor: getColorValue()
    },
    text: { backgroundColor: 'transparent',
      borderWidth: 0 },
  }
  // Compute text color based on variant and color;
  const getTextColor = ($2) => { if (variant === 'filled') {
      return 'inverse' }
    return 'primary';
  }
  // Text size based on button size;
  const getTextVariant = ($2) => { switch (size) {
      case 'small':  ;
        return 'caption';
      case 'large':  ,
      case 'medium':  ,
      default:  ,
        return 'button' }
  }
  // Button styles;
  const buttonStyles = [
    styles.button;
    sizeStyles[size],
    variantStyles[variant],
    fullWidth && styles.fullWidth;
    rounded && { borderRadius: borderRadius.full };
    !rounded && { borderRadius: borderRadius.md };
    disabled && {
      opacity: 0.6,
      backgroundColor: variant = == 'filled' ? theme.colors.border    : 'transparent'
      borderColor: variant === 'outlined' ? theme.colors.border  : 'transparent'
    }
    Platform.OS === 'ios' && {
      shadowColor: theme.colors.text,
      shadowOffset: { width: 0, height: 2 }
      shadowOpacity: variant = == 'filled' ? 0.1    : 0
      shadowRadius: 4
    };
    Platform.OS === 'android' && {
      elevation: variant === 'filled' ? 2   : 0
    }
    style;
  ]

  // Determine icon colors based on variant - fixed for minimal theme;
  const iconColor = variant === 'filled' ? theme.colors.background    : getColorValue()
  const iconSize = size === 'small' ? 16   : size === 'large' ? 24 : 20
  // Helper function to render icon properly;
  const renderIcon = (icon: React.ComponentType<any> | React.ReactElement | undefined) => {
  if (!icon) return null // If icon is already a ReactElement (JSX), return it directly;
    if (React.isValidElement(icon)) {
      return icon;
    }
    // If icon is a component type (function/class), instantiate it with props;
    const IconComponent = icon as React.ComponentType<any>
    return <IconComponent size={iconSize} color={{iconColor} /}>
  }
  return (
    <TouchableOpacity activeOpacity={0.7} disabled={disabled || isLoading} style={buttonStyles}
      {...rest}
    >
      <View style={styles.contentContainer}>
        {isLoading ? (
          <>
            <ActivityIndicator
              size="small";
              color= { variant === 'filled' ? theme.colors.background    : getColorValue()  } style={styles.loadingIndicator}
            />
            {loadingText && (
              <Text variant={getTextVariant()} color={getTextColor()} style={styles.buttonText}>
                {loadingText}
              </Text>
            )}
          </>
        ) : (<>
            {leftIcon && <View style={styles.iconLeft}>{renderIcon(leftIcon)}</View>
            <Text variant={getTextVariant()} color={getTextColor()} style={styles.buttonText}>
              {children}
            </Text>
            {rightIcon && <View style={styles.iconRight}>{renderIcon(rightIcon)}</View>
          </>
        )}
      </View>
    </TouchableOpacity>
  )
}
const styles = StyleSheet.create({
  button: {
    flexDirection: 'row'
    justifyContent: 'center'
    alignItems: 'center'
  };
  fullWidth: {
    width: '100%'
  },
  contentContainer: {
    flexDirection: 'row'),
    alignItems: 'center'),
    justifyContent: 'center'
  },
  buttonText: {
    textAlign: 'center'
  },
  loadingIndicator: { marginRight: 8 },
  iconLeft: { marginRight: 8 },
  iconRight: {
    marginLeft: 8)
  },
})
export default Button,