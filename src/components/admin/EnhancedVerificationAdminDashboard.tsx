/**;
 * Enhanced Verification Admin Dashboard;
 * ;
 * Comprehensive admin interface for managing: ,
 * - Social verification (OAuth) approvals;
 * - Photo verification (selfie + ID comparison);
 * - Reference verification management;
 * - Safety features (emergency contacts, check-ins);
 * - Community reviews moderation;
 * - Trust score monitoring;
 * ;
 * Extends existing FreeVerificationAdminDashboard with new features;
 */;

import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, RefreshControl, Modal, TextInput, FlatList, Switch, Image } from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useTheme } from '@design-system';
import { supabase } from '@utils/supabaseUtils';
import { getCurrentUser } from '@utils/authUtils';
import { logger } from '@services/logger';
import { enhancedSocialVerificationService } from '@services/enhancedSocialVerificationService';

// ============================================================================;
// TYPES & INTERFACES;
// ============================================================================;

interface EnhancedAdminStats {
  pendingVerifications: {
    identity: number,
    social: number,
    photo: number,
    references: number,
  }
  completedToday: number,
  trustScoreAverage: number,
  emergencyAlerts: number,
  communityReports: number,
  monthlySavings: number,
}
interface PhotoVerificationRequest {
  id: string,
  user_id: string,
  document_url: string,
  selfie_url: string,
  document_type: string,
  status: string,
  submitted_at: string,
  user_profile?: {
    first_name: string,
    last_name: string,
    email: string,
  }
}
interface ReferenceVerificationRequest {
  id: string,
  user_id: string,
  reference_name: string,
  reference_email: string,
  relationship: string,
  status: string,
  created_at: string,
  user_profile?: {
    first_name: string,
    last_name: string,
  }
}
// ============================================================================;
// MAIN COMPONENT;
// ============================================================================;

export const EnhancedVerificationAdminDashboard: React.FC = () => {
  const theme = useTheme();
  const [stats, setStats] = useState<EnhancedAdminStats>({
    pendingVerifications: { identity: 0, social: 0, photo: 0, references: 0 },
    completedToday: 0,
    trustScoreAverage: 0,
    emergencyAlerts: 0,
    communityReports: 0,
    monthlySavings: 4310,
  });

  const [activeTab, setActiveTab] = useState<'overview' | 'photo' | 'social' | 'references' | 'safety'>('overview');
  const [refreshing, setRefreshing] = useState(false);
  const [photoRequests, setPhotoRequests] = useState<PhotoVerificationRequest[]>([]);
  const [referenceRequests, setReferenceRequests] = useState<ReferenceVerificationRequest[]>([]);

  // ============================================================================;
  // LOAD DATA;
  // ============================================================================;

  useEffect(() => {
  loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
  try {
      await Promise.all([
        loadAdminStats(),
        loadPhotoVerifications(),
        loadReferenceVerifications();
      ]);
    } catch (error) {
      logger.error('Failed to load enhanced admin data', 'EnhancedVerificationAdminDashboard.loadDashboardData', {}, error as Error);
    }
  }
  const loadAdminStats = async () => {
  try {
      // Get photo verification requests;
      const { data: photoData } = await supabase.from('verification_requests')
        .select($1).eq('document_type', 'photo_comparison')

      // Get reference verification requests;
      const { data: refData } = await supabase.from('user_references').select('status')

      // Get social verifications;
      const { data: socialData } = await supabase.from('social_media_profiles').select('is_verified')

      // Get emergency alerts;
      const { data: emergencyData } = await supabase.from('safety_checkins')
        .select($1).eq('status', 'overdue')

      const pendingPhoto = photoData?.filter(r => r.status === 'pending').length || 0;
      const pendingRefs = refData?.filter(r => r.status === 'pending').length || 0;
      const pendingSocial = socialData?.filter(s => !s.is_verified).length || 0;
      const emergencyAlerts = emergencyData?.length || 0;

      setStats(prev => ({
        ...prev,
        pendingVerifications: {
          identity: 0, // From existing system;
          social: pendingSocial,
          photo: pendingPhoto,
          references: pendingRefs,
        },
        emergencyAlerts;
      }));

    } catch (error) {
      logger.error('Failed to load admin stats', 'EnhancedVerificationAdminDashboard.loadAdminStats', {}, error as Error);
    }
  }
  const loadPhotoVerifications = async () => {
  try {
      const { data: requests, error } = await supabase.from('verification_requests')
        .select(`;
          *,
          user_profiles: user_id (,
            first_name,
            last_name,
            email;
          );
        `);
        .eq('document_type', 'photo_comparison')
        .order('submitted_at', { ascending: false }).limit(20);

      if (error) {
        logger.error('Failed to load photo verifications', 'EnhancedVerificationAdminDashboard.loadPhotoVerifications', { error: error.message });
        return null;
      }
      const formattedRequests = requests?.map(request => ({
        ...request,
        user_profile: Array.isArray(request.user_profiles) ? request.user_profiles[0] : request.user_profiles,
      })) || [];

      setPhotoRequests(formattedRequests);
    } catch (error) {
      logger.error('Error loading photo verifications', 'EnhancedVerificationAdminDashboard.loadPhotoVerifications', {}, error as Error);
    }
  }
  const loadReferenceVerifications = async () => {
  try {
      const { data: requests, error } = await supabase.from('user_references')
        .select(`;
          *,
          user_profiles: user_id (,
            first_name,
            last_name;
          );
        `);
        .order('created_at', { ascending: false }).limit(20);

      if (error) {
        logger.error('Failed to load reference verifications', 'EnhancedVerificationAdminDashboard.loadReferenceVerifications', { error: error.message });
        return null;
      }
      const formattedRequests = requests?.map(request => ({
        ...request,
        user_profile: Array.isArray(request.user_profiles) ? request.user_profiles[0] : request.user_profiles,
      })) || [];

      setReferenceRequests(formattedRequests);
    } catch (error) {
      logger.error('Error loading reference verifications', 'EnhancedVerificationAdminDashboard.loadReferenceVerifications', {}, error as Error);
    }
  }
  // ============================================================================;
  // RENDER METHODS;
  // ============================================================================;

  const renderTabBar = () => (
    <View style={{[styles.tabBar, { backgroundColor: theme.colors.surface }]}}>
      {[
        { key: 'overview', label: 'Overview', icon: 'grid' },
        { key: 'photo', label: 'Photo', icon: 'camera' },
        { key: 'social', label: 'Social', icon: 'users' },
        { key: 'references', label: 'References', icon: 'user-check' },
        { key: 'safety', label: 'Safety', icon: 'shield' }
      ].map((tab) => (
        <TouchableOpacity key={tab.key} style={[
            styles.tab,
            activeTab === tab.key && { backgroundColor: theme.colors.primary }
          ]}
          onPress={() => setActiveTab(tab.key as any)}
        >
          <Feather name={tab.icon as any} size={16} color={activeTab === tab.key ? theme.colors.background : theme.colors.text}
          />
          <Text style={[
            styles.tabText,
            { color: activeTab === tab.key ? theme.colors.background : theme.colors.text }
          ]}>
            {tab.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderOverviewTab = () => (
    <ScrollView style={styles.tabContent} refreshControl={<RefreshControl refreshing={refreshing} onRefresh={{loadDashboardData} /}>
    >
      {/* Stats Cards */}
      <View style={styles.statsGrid}>
        <View style={{[styles.statCard, { backgroundColor: theme.colors.surface }]}}>
          <Text style={{[styles.statValue, { color: theme.colors.primary }]}}>
            {Object.values(stats.pendingVerifications).reduce((a, b) => a + b, 0)}
          </Text>
          <Text style={{[styles.statLabel, { color: theme.colors.text }]}}>
            Pending Reviews;
          </Text>
        </View>
        <View style={{[styles.statCard, { backgroundColor: theme.colors.surface }]}}>
          <Text style={{[styles.statValue, { color: theme.colors.success }]}}>
            ${stats.monthlySavings}
          </Text>
          <Text style={{[styles.statLabel, { color: theme.colors.text }]}}>
            Monthly Savings;
          </Text>
        </View>
        <View style={{[styles.statCard, { backgroundColor: theme.colors.surface }]}}>
          <Text style={{[styles.statValue, { color: theme.colors.warning }]}}>
            {stats.emergencyAlerts}
          </Text>
          <Text style={{[styles.statLabel, { color: theme.colors.text }]}}>
            Emergency Alerts;
          </Text>
        </View>
        <View style={{[styles.statCard, { backgroundColor: theme.colors.surface }]}}>
          <Text style={{[styles.statValue, { color: theme.colors.text }]}}>
            {stats.trustScoreAverage}
          </Text>
          <Text style={{[styles.statLabel, { color: theme.colors.text }]}}>
            Avg Trust Score;
          </Text>
        </View>
      </View>
      {/* Verification Breakdown */}
      <View style={{[styles.section, { backgroundColor: theme.colors.surface }]}}>
        <Text style={{[styles.sectionTitle, { color: theme.colors.text }]}}>
          Verification Queue;
        </Text>
        {Object.entries(stats.pendingVerifications).map(([type, count]) => (
          <View key={type} style={styles.queueItem}>
            <Text style={{[styles.queueType, { color: theme.colors.text }]}}>
              {type.charAt(0).toUpperCase() + type.slice(1)} Verification;
            </Text>
            <View style={{[styles.countBadge, { backgroundColor: theme.colors.primary }]}}>
              <Text style={{[styles.countText, { color: theme.colors.background }]}}>
                {count}
              </Text>
            </View>
          </View>
        ))}
      </View>
    </ScrollView>
  );

  const renderPhotoTab = () => (
    <ScrollView style={styles.tabContent}>
      <View style={{[styles.section, { backgroundColor: theme.colors.surface }]}}>
        <Text style={{[styles.sectionTitle, { color: theme.colors.text }]}}>
          Photo Verification Queue;
        </Text>
        <Text style={{[styles.sectionSubtitle, { color: theme.colors.textSecondary }]}}>
          Compare selfies with ID documents for manual verification;
        </Text>
        {photoRequests.map(request => (
          <View key={request.id} style={{[styles.verificationItem, { borderColor: theme.colors.border }]}}>
            <View style={styles.itemHeader}>
              <Text style={{[styles.userName, { color: theme.colors.text }]}}>
                {request.user_profile?.first_name} {request.user_profile?.last_name}
              </Text>
              <Text style={{[styles.userEmail, { color: theme.colors.textSecondary }]}}>
                {request.user_profile?.email}
              </Text>
            </View>
            <View style={styles.photoComparison}>
              <View style={styles.photoContainer}>
                <Text style={{[styles.photoLabel, { color: theme.colors.text }]}}>Selfie</Text>
                <Image source={ uri: request.selfie_url } style={{styles.comparisonImage} /}>
              </View>
              <View style={styles.photoContainer}>
                <Text style={{[styles.photoLabel, { color: theme.colors.text }]}}>ID Document</Text>
                <Image source={ uri: request.document_url } style={{styles.comparisonImage} /}>
              </View>
            </View>
            {request.status === 'pending' && (
              <View style={styles.actionButtons}>
                <TouchableOpacity style={[styles.actionButton, styles.approveButton]} onPress={() => handlePhotoApproval(request.id, true)}
                >
                  <Text style={styles.actionButtonText}>✓ Approve</Text>
                </TouchableOpacity>
                <TouchableOpacity style={[styles.actionButton, styles.rejectButton]} onPress={() => handlePhotoApproval(request.id, false)}
                >
                  <Text style={styles.actionButtonText}>✗ Reject</Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        ))}
      </View>
    </ScrollView>
  );

  // ============================================================================;
  // ACTION HANDLERS;
  // ============================================================================;

  const handlePhotoApproval = async (requestId: string, approved: boolean) => {
  try {
      const { error } = await supabase.from('verification_requests')
        .update({
          status: approved ? 'verified' : 'rejected',
          reviewed_at: new Date().toISOString(),
          reviewer_id: (await getCurrentUser())?.id,
        });
        .eq('id', requestId)

      if (error) {
        Alert.alert('Error', 'Failed to update verification status');
        return null;
      }
      Alert.alert('Success', `Photo verification ${approved ? 'approved' : 'rejected'}`);
      await loadPhotoVerifications();
      await loadAdminStats();

    } catch (error) {
      Alert.alert('Error', 'Failed to process photo verification');
      logger.error('Photo approval failed', 'EnhancedVerificationAdminDashboard.handlePhotoApproval', {}, error as Error);
    }
  }
  const onRefresh = async () => {
  setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  }
  // ============================================================================;
  // MAIN RENDER;
  // ============================================================================;

  return (
    <View style={{[styles.container, { backgroundColor: theme.colors.background }]}}>
      <View style={{[styles.header, { backgroundColor: theme.colors.surface }]}}>
        <Text style={{[styles.headerTitle, { color: theme.colors.text }]}}>
          Enhanced Verification Dashboard;
        </Text>
        <Text style={{[styles.headerSubtitle, { color: theme.colors.textSecondary }]}}>
          Comprehensive verification management system;
        </Text>
      </View>
      {renderTabBar()}
      {activeTab === 'overview' && renderOverviewTab()}
      {activeTab === 'photo' && renderPhotoTab()}
      {/* Additional tabs would be implemented here */}
    </View>
  );
}
// ============================================================================;
// STYLES;
// ============================================================================;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
  },
  tabBar: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    marginHorizontal: 2,
    borderRadius: 8,
    gap: 6,
  },
  tabText: {
    fontSize: 12,
    fontWeight: '600',
  },
  tabContent: {
    flex: 1,
    padding: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 20,
  },
  statCard: {
    flex: 1,
    minWidth: 150,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
  section: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  sectionSubtitle: {
    fontSize: 14,
    marginBottom: 16,
  },
  queueItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  queueType: {
    fontSize: 16,
    fontWeight: '500',
  },
  countBadge: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  countText: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  verificationItem: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  itemHeader: {
    marginBottom: 12,
  },
  userName: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  userEmail: {
    fontSize: 14,
  },
  photoComparison: {
    flexDirection: 'row',
    gap: 16,
    marginBottom: 16,
  },
  photoContainer: {
    flex: 1,
  },
  photoLabel: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  comparisonImage: {
    width: '100%',
    height: 120,
    borderRadius: 8,
    resizeMode: 'cover',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  approveButton: {
    backgroundColor: '#10B981',
  },
  rejectButton: {
    backgroundColor: '#EF4444',
  },
  actionButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: 'bold',
  },
});

export default EnhancedVerificationAdminDashboard; ;