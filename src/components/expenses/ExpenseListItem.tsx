import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { Feather } from '@expo/vector-icons';
import dayjs from 'dayjs';
import { useColorFix } from '@hooks/useColorFix';
import { useTheme } from '@design-system';

interface ExpenseParticipant {
  user_id: string,
  amount_owed: number,
  amount_paid: number,
  status: 'pending' | 'paid',
}
interface ExpenseItem {
  id: string,
  title: string,
  amount: number,
  date: string,
  created_by: string,
  category: string,
  status: 'active' | 'settled' | 'cancelled',
  expense_participants: ExpenseParticipant[],
  profiles: {
    id: string,
    display_name: string,
    avatar_url: string,
  }
}
interface ExpenseListItemProps {
  expense: ExpenseItem,
  currentUserId: string,
  onPress: () => void,
}
// Helper function to get expense category icon;
const getCategoryIcon = (category: string) => {
  switch (category.toLowerCase()) {
    case 'rent': ,
      return 'home';
    case 'groceries': ,
      return 'shopping-bag';
    case 'utilities': ,
      return 'zap';
    case 'internet': ,
      return 'wifi';
    case 'entertainment': ,
      return 'film';
    case 'transportation': ,
      return 'truck';
    case 'cleaning': ,
      return 'trash-2';
    case 'furniture': ,
      return 'box';
    case 'repairs': ,
      return 'tool';
    default: ,
      return 'dollar-sign';
  }
}
// Helper function to get category color;
const getCategoryColor = (category: string, theme: any) => {
  switch (category.toLowerCase()) {
    case 'rent': ,
      return '#8B5CF6'; // Purple;
    case 'groceries': ,
      return theme.colors.success; // Green;
    case 'utilities': ,
      return theme.colors.warning || '#F59E0B'; // Yellow/Orange;
    case 'internet': ,
      return theme.colors.primary; // Blue;
    case 'entertainment': ,
      return '#EC4899'; // Pink;
    case 'transportation': ,
      return '#6B7280'; // Gray;
    case 'cleaning': ,
      return '#14B8A6'; // Teal;
    case 'furniture': ,
      return '#F97316'; // Orange;
    case 'repairs': ,
      return theme.colors.error; // Red;
    default: ,
      return '#6366F1'; // Indigo;
  }
}
/**;
 * ExpenseListItem Component;
 * Shows individual expense items in a list with user details and amount;
 */;
export default function ExpenseListItem({ expense, currentUserId, onPress }: ExpenseListItemProps) {
  const theme = useTheme();
  const styles = createStyles(theme);
  const { fix } = useColorFix();

  // Helper function to get total amount owed to the creator;
  const getTotalOwed = () => {
    return expense.expense_participants.reduce(
      (sum, p) => sum + (p.amount_owed - p.amount_paid),
      0;
    );
  }
  // Get the current user's participation in this expense;
  const myParticipation = expense.expense_participants.find(p => p.user_id === currentUserId);

  // Calculate amount that I need to pay or get paid;
  const myAmount = myParticipation ? myParticipation.amount_owed - myParticipation.amount_paid : 0,
  // Check if I created this expense;
  const isCreator = expense.created_by === currentUserId;

  // Check if I'm paying or getting paid;
  const isPaying = !isCreator && myAmount > 0;
  const isReceiving =;
    isCreator && expense.expense_participants.some(p => p.amount_owed - p.amount_paid > 0);

  // Format date;
  const formattedDate = dayjs(expense.date).format('MMM D, YYYY');

  return (
    <TouchableOpacity
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      {/* Category Icon */}
      <View
        style={[
          styles.categoryIcon,
          { backgroundColor: getCategoryColor(expense.category, theme) },
        ]}
      >
        <Feather name={getCategoryIcon(expense.category)} size={18} color={'#FFFFFF' /}>
      </View>
      {/* Expense Details */}
      <View style={styles.details}>
        <Text style={[styles.title, { color: theme.colors.text }]} numberOfLines={1}>
          {expense.title}
        </Text>
        <View style={styles.subDetails}>
          {/* Creator Info */}
          <View style={styles.creatorInfo}>
            {expense.profiles.avatar_url ? (
              <Image source={ uri: expense.profiles.avatar_url } style={{styles.avatarImage} /}>
            ) : (,
              <View style={{[styles.avatarPlaceholder, { backgroundColor: theme.colors.surface }]}}>
                <Text style={{[styles.avatarInitial, { color: theme.colors.text }]}}>
                  {expense.profiles.display_name?.[0]?.toUpperCase() || '?'}
                </Text>
              </View>
            )}
            <Text
              style={[styles.creatorName, { color: theme.colors.textSecondary }]}
              numberOfLines={1}
            >
              {isCreator ? 'You' : expense.profiles.display_name}
            </Text>
          </View>
          {/* Date */}
          <Text style={{[styles.date, { color: theme.colors.textSecondary }]}}>{formattedDate}</Text>
        </View>
      </View>
      {/* Amount Info */}
      <View style={styles.amountContainer}>
        {isPaying ? (
          <>
            <Text style={{[styles.amountLabel, { color: theme.colors.textSecondary }]}}>You owe</Text>
            <Text style={{[styles.amountOwed, { color: theme.colors.error }]}}>
              ${myAmount.toFixed(2)}
            </Text>
            {myParticipation?.status === 'paid' && (
              <View style={{[styles.paidBadge, { backgroundColor: theme.colors.success }]}}>
                <Text style={styles.paidText}>PAID</Text>
              </View>
            )}
          </>
        ) : isReceiving ? (,
          <>
            <Text style={{[styles.amountLabel, { color: theme.colors.textSecondary }]}}>
              Owed to you;
            </Text>
            <Text style={{[styles.amountReceiving, { color: theme.colors.success }]}}>
              ${getTotalOwed().toFixed(2)}
            </Text>
          </>
        ) : (,
          <>
            <Text style={{[styles.amountLabel, { color: theme.colors.textSecondary }]}}>Total</Text>
            <Text style={{[styles.amountTotal, { color: theme.colors.text }]}}>
              ${expense.amount.toFixed(2)}
            </Text>
          </>
        )}
      </View>
      {/* Status Indicator */}
      <View style={styles.statusContainer}>
        {expense.status === 'settled' ? (
          <Feather name='check-circle' size={16} color={{theme.colors.success} /}>
        ) : expense.status === 'cancelled' ? (,
          <Feather name='x-circle' size={16} color={{theme.colors.error} /}>
        ) : (,
          <Feather name='chevron-right' size={16} color={{theme.colors.textSecondary} /}>
        )}
      </View>
    </TouchableOpacity>
  );
}
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    categoryIcon: {
      width: 40,
      height: 40,
      borderRadius: 8,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 12,
    },
    details: {
      flex: 1,
      marginRight: 12,
    },
    title: {
      fontSize: 16,
      fontWeight: '500',
      marginBottom: 4,
    },
    subDetails: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    creatorInfo: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
      marginRight: 8,
    },
    avatarImage: {
      width: 20,
      height: 20,
      borderRadius: 10,
      marginRight: 6,
    },
    avatarPlaceholder: {
      width: 20,
      height: 20,
      borderRadius: 10,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 6,
    },
    avatarInitial: {
      fontSize: 10,
      fontWeight: '600',
    },
    creatorName: {
      fontSize: 13,
      flex: 1,
    },
    date: {
      fontSize: 12,
    },
    amountContainer: {
      alignItems: 'flex-end',
      marginRight: 8,
    },
    amountLabel: {
      fontSize: 11,
      marginBottom: 2,
    },
    amountOwed: {
      fontSize: 16,
      fontWeight: '600',
    },
    amountReceiving: {
      fontSize: 16,
      fontWeight: '600',
    },
    amountTotal: {
      fontSize: 16,
      fontWeight: '600',
    },
    paidBadge: {
      paddingHorizontal: 6,
      paddingVertical: 2,
      borderRadius: 4,
      marginTop: 2,
    },
    paidText: {
      color: theme.colors.background,
      fontSize: 9,
      fontWeight: '700',
    },
    statusContainer: {
      justifyContent: 'center',
      alignItems: 'center',
    },
  });
