import React, { useState } from 'react';
import { View, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { Text, Input, Alert } from '@components/ui';
import { Button } from '@design-system';
import { AlertCircle } from 'lucide-react-native';
import { agreementService } from '@services/agreementService';
import { DisputeType } from '@utils/agreement';
import { useColorFix } from '@hooks/useColorFix';
import { colorWithOpacity, type Theme } from '@design-system';
import { useTheme } from '@design-system';

interface DisputeFormProps {
  agreementId: string,
  userId: string,
  onClose: () => void,
  onSuccess: () => void,
}
const disputeTypeOptions: { value: DisputeType; label: string }[] = [
  { value: 'payment', label: 'Payment Issues' },
  { value: 'maintenance', label: 'Maintenance Problems' },
  { value: 'noise', label: 'Noise Complaints' },
  { value: 'cleanliness', label: 'Cleanliness Concerns' },
  { value: 'contract_violation', label: 'Contract Violation' },
  { value: 'other', label: 'Other Issue' },
];

export default function DisputeForm({ agreementId, userId, onClose, onSuccess }: DisputeFormProps) {
  const theme = useTheme();
  const styles = createStyles(theme);
  const { fix } = useColorFix();
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [disputeType, setDisputeType] = useState<DisputeType>('other');
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async () => {
    // Validate form;
    if (!title.trim()) {
      setError('Please provide a title for your dispute');
      return null;
    }
    if (!description.trim()) {
      setError('Please describe the issue in detail');
      return null;
    }
    setIsSubmitting(true);
    setError(null);

    try {
      const disputeId = await agreementService.createDispute({
        agreement_id: agreementId,
        raised_by: userId,
        title: title.trim(),
        description: description.trim(),
        dispute_type: disputeType,
        status: 'open',
      });

      if (disputeId) {
        // Add initial message for context;
        await agreementService.addDisputeMessage({
          dispute_id: disputeId,
          user_id: userId,
          message: description,
        });

        onSuccess();
      } else {
        setError('Failed to create dispute. Please try again.');
      }
    } catch (err) {
      console.error('Error creating dispute:', err);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  }
  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Raise a Dispute</Text>
      <Text style={styles.subtitle}>
        Use this form to raise a dispute about your roommate agreement. Be specific and provide;
        details about the issue you're experiencing.;
      </Text>
      {error && (
        <Alert
          variant='error';
          icon={<AlertCircle size={16} color={'#EF4444' /}>
          title='Error';
          message={error}
          style={styles.alert}
        />
      )}
      <View style={styles.formGroup}>
        <Text style={styles.label}>Issue Type</Text>
        <View style={styles.typeOptions}>
          {disputeTypeOptions.map(option => (
            <TouchableOpacity
              key={option.value}
              style={[styles.typeOption, disputeType === option.value && styles.selectedTypeOption]}
              onPress={() => setDisputeType(option.value)}
            >
              <Text
                style={[
                  styles.typeOptionText,
                  disputeType === option.value && styles.selectedTypeOptionText,
                ]}
              >
                {option.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
      <View style={styles.formGroup}>
        <Text style={styles.label}>Title</Text>
        <Input
          placeholder='Brief title describing the issue';
          value={title}
          onChangeText={setTitle}
          maxLength={100}
        />
      </View>
      <View style={styles.formGroup}>
        <Text style={styles.label}>Description</Text>
        <Input
          placeholder='Provide details about the issue';
          value={description}
          onChangeText={setDescription}
          multiline;
          numberOfLines={5}
          textAlignVertical='top';
          style={styles.textArea}
        />
      </View>
      <View style={styles.actions}>
        <Button title='Cancel' variant='outlined' onPress={onClose} style={{styles.cancelButton} /}>
        <Button
          title={isSubmitting ? 'Submitting...' : 'Submit Dispute'}
          onPress={handleSubmit}
          disabled={isSubmitting}
          style={styles.submitButton}
        />
      </View>
    </ScrollView>
  );
}
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      padding: 16,
    },
    title: {
      fontSize: 20,
      fontWeight: 'bold',
      marginBottom: 8,
      color: theme.colors.text,
    },
    subtitle: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      marginBottom: 20,
    },
    alert: {
      marginBottom: 16,
    },
    formGroup: {
      marginBottom: 20,
    },
    label: {
      fontSize: 14,
      fontWeight: '500',
      marginBottom: 8,
      color: '#334155',
    },
    typeOptions: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginLeft: -8,
      marginTop: -8,
    },
    typeOption: {
      paddingHorizontal: 12,
      paddingVertical: 8,
      borderRadius: 16,
      backgroundColor: '#F1F5F9',
      margin: 8,
    },
    selectedTypeOption: {
      backgroundColor: '#818CF830',
      borderWidth: 1,
      borderColor: '#818CF8',
    },
    typeOptionText: {
      fontSize: 14,
      color: '#475569',
    },
    selectedTypeOptionText: {
      color: '#6366F1',
      fontWeight: '500',
    },
    textArea: {
      minHeight: 100,
      paddingTop: 12,
    },
    actions: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: 12,
    },
    cancelButton: {
      flex: 1,
      marginRight: 8,
    },
    submitButton: {
      flex: 1,
      marginLeft: 8,
    },
  });
