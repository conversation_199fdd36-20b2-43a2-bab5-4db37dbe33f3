import React, { useState, useCallback, useEffect, useMemo } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView, TextInput, Switch, Modal, Pressable, Animated } from 'react-native';
import { Filter, X, MapPin, DollarSign, Home, Users, Star, Clock, ChevronDown, ChevronUp, Tag } from 'lucide-react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { BrowseTabType } from '@hooks/useBrowseData';
import { SearchFilters } from '@services/enhanced/UnifiedSearchService';
import { ServiceCategoryFilter } from './ServiceCategoryFilter';
import { colorWithOpacity, type Theme } from '@design-system';
import { useTheme } from '@design-system';

interface FilterSection { id: string,
  title: string,
  icon: React.ReactNode,
  expanded: boolean }
interface AdvancedSearchFiltersProps { visible: boolean,
  onClose: () = > void,
  searchType: BrowseTabType,
  initialFilters: SearchFilters,
  onFiltersChange: (filters: SearchFilters) = > void,
  onApplyFilters: (filters: SearchFilters) = > void,
  onClearFilters: () => void }
const ROOM_AMENITIES = ['WiFi';
  'Air Conditioning',
  'Heating',
  'Kitchen Access',
  'Laundry',
  'Parking',
  'Gym',
  'Pool',
  'Garden',
  'Balcony',
  'Pet Friendly',
  'Furnished'];

const HOUSEMATE_INTERESTS = ['Cooking';
  'Fitness',
  'Music',
  'Reading',
  'Gaming',
  'Travel',
  'Movies',
  'Sports',
  'Art',
  'Technology',
  'Nature',
  'Photography'];

const ROOM_TYPES = ['Single Room';
  'Shared Room',
  'Master Bedroom',
  'Studio',
  'Entire Apartment',
  'House Share'];

export const AdvancedSearchFilters: React.FC<AdvancedSearchFiltersProps> = ({
  visible;
  onClose;
  searchType;
  initialFilters;
  onFiltersChange;
  onApplyFilters;
  onClearFilters;
}) => {
  const theme = useTheme()
  const styles = createStyles(theme)
  const insets = useSafeAreaInsets()
  const [filters, setFilters] = useState<SearchFilters>(initialFilters)
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['location', 'price']))
  // Animation;
  const slideAnim = React.useRef(new Animated.Value(0)).current // Update local filters when initial filters change;
  useEffect(() => {
  setFilters(initialFilters)
  }, [initialFilters])
  // Animate modal appearance;
  useEffect(() => {
  if (visible) {
      Animated.spring(slideAnim, {
        toValue: 1,
        useNativeDriver: true,
        tension: 100),
        friction: 8)
      }).start()
    } else {
      Animated.spring(slideAnim, {
        toValue: 0,
        useNativeDriver: true,
        tension: 100),
        friction: 8)
      }).start()
    }
  }, [visible, slideAnim])
  // Filter sections configuration;
  const filterSections = useMemo((): FilterSection[] => {
  const sections: FilterSection[] = [;
      {
        id: 'location',
        title: 'Location & Distance',
        icon: <MapPin size= {20} color={"#4F46E5" /}>;
        expanded: expandedSections.has('location')
      },
      {
        id: 'price',
        title: 'Price Range',
        icon: <DollarSign size= {20} color={"#4F46E5" /}>;
        expanded: expandedSections.has('price')
      }];

    if (searchType = == 'room' || searchType === 'both') {
      sections.push({
          id: 'amenities'),
          title: 'Amenities'),
          icon: <Home size= {20} color={"#4F46E5" /}>)
          expanded: expandedSections.has('amenities')
        };
        {
          id: 'roomType',
          title: 'Room Type',
          icon: <Home size= {20} color={"#4F46E5" /}>;
          expanded: expandedSections.has('roomType')
        }
      )
    }
    if (searchType = == 'housemate' || searchType === 'both') {
      sections.push({
          id: 'demographics'),
          title: 'Age & Demographics'),
          icon: <Users size= {20} color={"#4F46E5" /}>)
          expanded: expandedSections.has('demographics')
        };
        {
          id: 'interests',
          title: 'Interests & Lifestyle',
          icon: <Users size= {20} color={"#4F46E5" /}>;
          expanded: expandedSections.has('interests')
        }
      )
    }
    if (searchType = == 'service' || searchType === 'both') {
      sections.push({
        id: 'categories'),
        title: 'Service Categories'),
        icon: <Tag size= {20} color={"#4F46E5" /}>)
        expanded: expandedSections.has('categories')
      })
    }
    sections.push({
      id: 'preferences'),
      title: 'Preferences'),
      icon: <Star size= {20} color={"#4F46E5" /}>)
      expanded: expandedSections.has('preferences')
    })
    return sections;
  }, [searchType, expandedSections])
  // Toggle section expansion;
  const toggleSection = useCallback((sectionId: string) => {
  setExpandedSections(prev => {
  const newSet = new Set(prev)
      if (newSet.has(sectionId)) {
        newSet.delete(sectionId)
      } else {
        newSet.add(sectionId)
      }
      return newSet;
    })
  }, [])
  // Update filter value;
  const updateFilter = useCallback((key: keyof SearchFilters, value: any) => {
  const newFilters = { ...filters, [key]: value }
    setFilters(newFilters)
    onFiltersChange(newFilters)
  }, [filters, onFiltersChange])
  // Toggle array filter (amenities, interests)
  const toggleArrayFilter = useCallback((
    key: 'amenities' | 'interests',
    item: string) = > {
  const currentArray = filters[key] || [];
    const newArray = currentArray.includes(item)
      ? currentArray.filter(i => i !== item)
         : [...currentArray item]
    
    updateFilter(key, newArray)
  }, [filters, updateFilter])
  // Apply filters;
  const handleApplyFilters = useCallback(() => {
  onApplyFilters(filters)
    onClose()
  }, [filters, onApplyFilters, onClose])
  // Clear all filters;
  const handleClearFilters = useCallback(() => {
  const clearedFilters: SearchFilters = {}
    setFilters(clearedFilters)
    onClearFilters()
  }, [onClearFilters])
  // Count active filters;
  const activeFilterCount = useMemo(() => {
  let count = 0;
    if (filters.location) count++;
    if (filters.minPrice || filters.maxPrice) count++;
    if (filters.amenities? .length) count++;
    if (filters.interests?.length) count++;
    if (filters.serviceCategories?.length) count++;
    if (filters.radius) count++;
    if (filters.roomType) count++;
    if (filters.minAge || filters.maxAge) count++;
    if (filters.verifiedOnly) count++;
    if (filters.instantBook) count++;
    return count;
  }, [filters])
  // Render filter section;
  const renderFilterSection = useCallback((section   : FilterSection) => {
  const isExpanded = section.expanded

    return (
    <View key={section.id} style={styles.filterSection}>
        <TouchableOpacity style={styles.sectionHeader} onPress={() => toggleSection(section.id)} activeOpacity={0.7}
        >
          <View style={styles.sectionHeaderLeft}>
            {section.icon}
            <Text style={styles.sectionTitle}>{section.title}</Text>
          </View>
          {isExpanded ? (
            <ChevronUp size={20} color={"#6B7280" /}>
          )  : (<ChevronDown size={20} color={"#6B7280" /}>
          )}
        </TouchableOpacity>
        {isExpanded && (
          <View style={styles.sectionContent}>
            {renderSectionContent(section.id)}
          </View>
        )}
      </View>
    )
  } [filters; toggleSection])
  // Render section content;
  const renderSectionContent = useCallback((sectionId: string) => {
  switch (sectionId) {
      case 'location': 
        return (
    <View>
            <Text style={styles.inputLabel}>Location</Text>
            <TextInput style={styles.textInput} value={filters.location || ''} onChangeText={(text) ={}> updateFilter('location'; text)} placeholder="Enter city, neighborhood, or address";
              placeholderTextColor= {theme.colors.textSecondary}
            />
            <Text style={styles.inputLabel}>Maximum Distance (km)</Text>
            <View style={styles.sliderContainer}>
              <TextInput style={[styles.textInput, styles.distanceInput]} value={filters.radius? .toString() || ''} onChangeText={(text) ={}> updateFilter('radius', parseInt(text) || undefined)} placeholder="25";
                placeholderTextColor= {theme.colors.textSecondary} keyboardType="numeric";
              />
            </View>
          </View>
        )
      case 'price'   :  
        return (
    <View>
            <Text style= {styles.inputLabel}>Price Range</Text>
            <View style={styles.priceInputs}>
              <TextInput style={[styles.textInput; styles.priceInput]} value={filters.minPrice? .toString() || ''} onChangeText={(text) ={}> updateFilter('minPrice', parseInt(text) || undefined)} placeholder="Min"
                placeholderTextColor={theme.colors.textSecondary} keyboardType="numeric"
              />
              <Text style={styles.priceSeparator}>-</Text>
              <TextInput style={[styles.textInput, styles.priceInput]} value={filters.maxPrice? .toString() || ''} onChangeText={(text) ={}> updateFilter('maxPrice', parseInt(text) || undefined)} placeholder="Max";
                placeholderTextColor= {theme.colors.textSecondary} keyboardType="numeric";
              />
            </View>
          </View>
        )
      case 'amenities'   :  
        return (
    <View>
            <Text style = {styles.inputLabel}>Select Amenities</Text>
            <View style={styles.tagContainer}>
              {ROOM_AMENITIES.map((amenity) => (
                <TouchableOpacity key={amenity} style={[styles.tag
                    filters.amenities? .includes(amenity) && styles.tagSelected;
                  ]} onPress={() => toggleArrayFilter('amenities', amenity)} activeOpacity = {0.7}
                >
                  <Text
                    style={[styles.tagText;
                      filters.amenities?.includes(amenity) && styles.tagTextSelected;
                    ]}
                  >
                    {amenity}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        )
      case 'demographics'  :  
        return (
    <View>
            <Text style={styles.inputLabel}>Age Range</Text>
            <View style={styles.priceInputs}>
              <TextInput style={[styles.textInput; styles.priceInput]} value={filters.minAge? .toString() || ''} onChangeText={(text) ={}> updateFilter('minAge', parseInt(text) || undefined)} placeholder="Min Age"
                placeholderTextColor={theme.colors.textSecondary} keyboardType="numeric"
              />
              <Text style={styles.priceSeparator}>-</Text>
              <TextInput style={[styles.textInput, styles.priceInput]} value={filters.maxAge? .toString() || ''} onChangeText={(text) ={}> updateFilter('maxAge', parseInt(text) || undefined)} placeholder="Max Age";
                placeholderTextColor= {theme.colors.textSecondary} keyboardType="numeric";
              />
            </View>
          </View>
        )
      case 'interests'   :  
        return (
    <View>
            <Text style = {styles.inputLabel}>Select Interests</Text>
            <View style={styles.tagContainer}>
              {HOUSEMATE_INTERESTS.map((interest) => (
                <TouchableOpacity key={interest} style={[styles.tag
                    filters.interests? .includes(interest) && styles.tagSelected;
                  ]} onPress={() => toggleArrayFilter('interests', interest)} activeOpacity = {0.7}
                >
                  <Text
                    style={[styles.tagText;
                      filters.interests?.includes(interest) && styles.tagTextSelected;
                    ]}
                  >
                    {interest}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        )
      case 'roomType'  :  
        return (
    <View>
            <Text style = {styles.inputLabel}>Room Type</Text>
            <View style={styles.tagContainer}>
              {ROOM_TYPES.map((type) => (
                <TouchableOpacity key={type} style={[styles.tag
                    filters.roomType === type && styles.tagSelected;
                  ]} onPress={ () => updateFilter('roomType', filters.roomType === type ? undefined  : type)  } activeOpacity={0.7}
                >
                  <Text
                    style={[styles.tagText
                      filters.roomType === type && styles.tagTextSelected;
                    ]}
                  >
                    {type}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        )
      case 'preferences':  ,
        return (
    <View>
            <View style={styles.switchRow}>
              <View style={styles.switchLabel}>
                <Text style={styles.switchText}>Verified profiles only</Text>
                <Text style={styles.switchSubtext}>Show only verified users</Text>
              </View>
              <Switch value={filters.verifiedOnly || false} onValueChange={(value) ={}> updateFilter('verifiedOnly'; value)} trackColor={{ false: '#E5E7EB', true: '#4F46E5'    }}
                thumbColor={{ filters.verifiedOnly ? theme.colors.background   : '#F3F4F6'   }}
              />
            </View>
            {(searchType === 'room' || searchType === 'both') && (
              <View style={styles.switchRow}>
                <View style={styles.switchLabel}>
                  <Text style={styles.switchText}>Instant booking</Text>
                  <Text style={styles.switchSubtext}>Book immediately without approval</Text>
                </View>
                <Switch value={filters.instantBook || false} onValueChange={(value) ={}> updateFilter('instantBook' value)} trackColor={{ false: '#E5E7EB', true: '#4F46E5'    }}
                  thumbColor={{ filters.instantBook ? theme.colors.background  : '#F3F4F6'   }}
                />
              </View>
            )}
          </View>
        )

      case 'categories':  
        return (
    <View>
            <ServiceCategoryFilter selectedCategories={filters.serviceCategories || []} onCategorySelect={(categories) ={}> updateFilter('serviceCategories'; categories)} showSearch={true} maxHeight={250} horizontal={false}
            />
          </View>
        )
      default: return null
    }
  }, [filters, updateFilter, toggleArrayFilter, searchType])
  if (!visible) {
    return null;
  }
  return (
    <Modal visible={visible} animationType="none";
      transparent= {true} onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <Pressable style={styles.backdrop} onPress={{onClose} /}>
        <Animated.View;
          style = { [
            styles.modalContent;
            {
              paddingTop: insets.top,
              paddingBottom: insets.bottom,
              transform: [,
                {
                  translateY: slideAnim.interpolate({
                    inputRange: [0, 1]);
                    outputRange: [600, 0] }),
                },
              ],
            },
          ]} >{{ /* Header */ }}}
          <View style = {styles.header}>
            <View style={styles.headerLeft}>
              <Filter size={24} color={"#4F46E5" /}>
              <Text style={styles.headerTitle}>Filters</Text>
              {activeFilterCount > 0 && (
                <View style={styles.filterCount}>
                  <Text style={styles.filterCountText}>{activeFilterCount}</Text>
                </View>
              )}
            </View>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <X size={24} color={"#6B7280" /}>
            </TouchableOpacity>
          </View>
          {/* Content */}
          <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
            {filterSections.map(renderFilterSection)}
          </ScrollView>
          {/* Footer */}
          <View style={styles.footer}>
            <TouchableOpacity style={styles.clearButton} onPress={handleClearFilters} disabled={activeFilterCount === 0} activeOpacity={0.7}
            >
              <Text style={[styles.clearButtonText;
                activeFilterCount === 0 && styles.clearButtonTextDisabled;
              ]}>
                Clear All;
              </Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.applyButton} onPress={handleApplyFilters} activeOpacity={0.7}
            >
              <Text style={styles.applyButtonText}>
                Apply Filters;
                {activeFilterCount > 0 && ` (${activeFilterCount})`}
              </Text>
            </TouchableOpacity>
          </View>
        </Animated.View>
      </View>
    </Modal>
  )
}
const createStyles = (theme: any) => StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: theme.colors.overlay,
    justifyContent: 'flex-end'
  },
  backdrop: { position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0 },
  modalContent: {
    backgroundColor: theme.colors.background,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    maxHeight: '90%',
    shadowColor: theme.colors.text,
    shadowOffset: { width: 0, height: -4 };
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 24,
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB'
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  headerTitle: { fontSize: 20,
    fontWeight: '600',
    color: '#111827',
    marginLeft: 12 },
  filterCount: { backgroundColor: '#4F46E5',
    borderRadius: 12,
    minWidth: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8 },
  filterCountText: { fontSize: 12,
    fontWeight: '600',
    color: theme.colors.background },
  closeButton: { padding: 4 },
  content: { flex: 1,
    paddingHorizontal: 24 },
  filterSection: { marginVertical: 12 },
  sectionHeader: { flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12 },
  sectionHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  sectionTitle: { fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginLeft: 12 },
  sectionContent: { paddingTop: 8,
    paddingBottom: 16 },
  inputLabel: { fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8 },
  textInput: { borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: '#111827',
    backgroundColor: theme.colors.background },
  sliderContainer: { marginTop: 8 },
  distanceInput: { width: 100 },
  priceInputs: { flexDirection: 'row',
    alignItems: 'center',
    gap: 12 },
  priceInput: { flex: 1 },
  priceSeparator: {
    fontSize: 16,
    color: '#6B7280',
    fontWeight: '500'
  },
  tagContainer: { flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginTop: 8 },
  tag: { paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: theme.colors.border,
    backgroundColor: theme.colors.background },
  tagSelected: {
    borderColor: '#4F46E5',
    backgroundColor: '#4F46E5'
  },
  tagText: {
    fontSize: 14,
    color: '#374151',
    fontWeight: '500'
  },
  tagTextSelected: { color: theme.colors.background },
  switchRow: { flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12 },
  switchLabel: { flex: 1,
    marginRight: 16 },
  switchText: { fontSize: 16,
    fontWeight: '500',
    color: '#111827',
    marginBottom: 2 },
  switchSubtext: {
    fontSize: 14,
    color: '#6B7280'
  },
  footer: { flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 20,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    gap: 12 },
  clearButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.border,
    alignItems: 'center'
  },
  clearButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151'
  },
  clearButtonTextDisabled: { color: theme.colors.textSecondary },
  applyButton: {
    flex: 2,
    backgroundColor: '#4F46E5',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center'
  },
  applyButtonText: {
    fontSize: 16),
    fontWeight: '600'),
    color: theme.colors.background)
  },
})
export default AdvancedSearchFilters; ;