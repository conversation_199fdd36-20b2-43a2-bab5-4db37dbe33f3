import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { View, Text, TextInput, TouchableOpacity, ScrollView, ActivityIndicator, StyleSheet, Dimensions, FlatList, Alert, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Search, Filter, X, MapPin, Clock, TrendingUp, Sliders } from 'lucide-react-native';
import { useRouter } from 'expo-router';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTheme } from '@design-system';

import { unifiedSearchService, SearchQuery, SearchFilters, SearchResult, RoomSearchResult, HousemateSearchResult, ServiceSearchResult } from '@services/enhanced/UnifiedSearchService';
import { AdvancedSearchFilters } from '@components/search/AdvancedSearchFilters';
import { useAuth } from '@context/AuthContext';
import { useDebounce } from '@hooks/useDebounce';
import { startChatWithMatch } from '@utils/chatUtils';
import ListingCard from '@components/common/ListingCard';

const { width, height  } = Dimensions.get('window')
// ============================================================================ // INTERFACES;
// = ===========================================================================;

interface UnifiedSearchInterfaceProps {
  initialSearchType?: 'rooms' | 'housemates' | 'services' | 'all',
  onResultSelect?: (result: RoomSearchResult | HousemateSearchResult | ServiceSearchResult, type: 'room' | 'housemate' | 'service') = > void;
  showFilters?: boolean,
  embedded?: boolean // For embedding in other screens;
  showSearchTypes?: boolean // Whether to show the search type navigation;
}
interface SearchSuggestion { text: string,
  type: 'recent' | 'suggestion' | 'location',
  icon?: string }
// = =========================================================================== // UNIFIED SEARCH INTERFACE;
// = ===========================================================================;

export function UnifiedSearchInterface({
  initialSearchType = 'all';
  onResultSelect;
  showFilters = true;
  embedded = false;
  showSearchTypes = true;
}: UnifiedSearchInterfaceProps) {
  const router = useRouter()
  const insets = useSafeAreaInsets()
  const { authState  } = useAuth()
  const user = authState? .user;
  const theme = useTheme()
  const styles = createStyles(theme)
  // ============================================================================ // STATE MANAGEMENT;
  // = ===========================================================================;

  const [searchQuery, setSearchQuery] = useState('')
  const [searchType, setSearchType] = useState<'rooms' | 'housemates' | 'services' | 'all'>(initialSearchType)
  const [filters, setFilters] = useState<SearchFilters>({})
  const [isSearching, setIsSearching] = useState(false)
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [showFiltersModal, setShowFiltersModal] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [hasSearched, setHasSearched] = useState(false)
  // Search results;
  const [roomResults, setRoomResults] = useState<SearchResult<RoomSearchResult> | null>(null)
  const [housemateResults, setHousemateResults] = useState<SearchResult<HousemateSearchResult> | null>(null)
  const [serviceResults, setServiceResults] = useState<SearchResult<ServiceSearchResult> | null>(null)
   // Suggestions;
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([])
  const [recentSearches, setRecentSearches] = useState<string[]>([])
   // UI state;
  const [isFocused, setIsFocused] = useState(false)
  const [error, setError] = useState<string | null>(null)
  // Refs;
  const searchInputRef = useRef<TextInput>(null)
  // Debounced search query;
  const debouncedSearchQuery = useDebounce(searchQuery, 300)
  // ============================================================================ // MESSAGE HANDLING FOR CHAT → AGREEMENT FLOW;
  // = ===========================================================================;

  const handleMessageRoom = useCallback(async (roomId   : string ownerId: string, roomTitle: string) => {
  if (!user?.id) {
      Alert.alert('Login Required', 'Please log in to message room owners')
      return null;
    }
    try {
      const roomOwnerName = roomTitle ? `${roomTitle} Owner`  : 'Room Owner'
      const initialMessage = `Hi! I'm interested in your room listing: "${roomTitle}". Is it still available? `
      ;
      await startChatWithMatch(user.id, ownerId, roomOwnerName, initialMessage, 'room_inquiry')
    } catch (error) {
      console.error('Failed to start chat with room owner  : ' error)
      Alert.alert('Error', 'Failed to start conversation. Please try again.')
    }
  }, [user? .id])
  const handleMessageHousemate = useCallback(async (housemateId   : string name: string) => {
  if (!user? .id) {
      Alert.alert('Login Required', 'Please log in to message potential housemates')
      return null;
    }
    try {
      const userName = name || 'User'
      const initialMessage = `Hi ${userName}! I saw your profile and would like to connect as potential housemates.`;
      ;
      await startChatWithMatch(user.id, housemateId, userName, initialMessage)
    } catch (error) {
      console.error('Failed to start chat with housemate : ' error)
      Alert.alert('Error', 'Failed to start conversation. Please try again.')
    }
  }, [user? .id])
  const handleLikeHousemate = useCallback((housemateId   : string) => {
  // For now just show a success message
    // This can be expanded to actual like functionality later;
    Alert.alert('Connection Sent', 'Your interest has been noted!')
  }, [])
  // ============================================================================
  // SEARCH EXECUTION // ============================================================================;

  const executeSearch = useCallback(async (
    query: string,
    searchFilters: SearchFilters = {};
    page: number = 1,
    append: boolean = false) => {
  if (!query.trim() && Object.keys(searchFilters).length === 0) {
      setRoomResults(null)
      setHousemateResults(null)
      setServiceResults(null)
      setHasSearched(false)
      return null;
    }
    try { setIsSearching(true)
      setError(null)
      const searchQuery: SearchQuery = {
        text: query.trim()
        type: searchType,
        filters: searchFilters,
        pagination: {
          limit: 20,
          offset: (page - 1) * 20 },
      }
      // Validate query;
      const validation = await unifiedSearchService.validateSearchQuery(searchQuery)
      if (!validation.isValid) {
        setError(validation.errors.join(', '))
        return null;
      }
      let newRoomResults: SearchResult<RoomSearchResult> | null = null,
      let newHousemateResults: SearchResult<HousemateSearchResult> | null = null;
      let newServiceResults: SearchResult<ServiceSearchResult> | null = null;
      if (searchType === 'rooms' || searchType === 'all') {
        newRoomResults = await unifiedSearchService.searchRooms(searchQuery)
      }
      if (searchType === 'housemates' || searchType = == 'all') {
        newHousemateResults = await unifiedSearchService.searchHousemates(searchQuery;
          user? .id)
        )
      }
      if (searchType === 'services' || searchType === 'all') {
        newServiceResults = await unifiedSearchService.searchServices(searchQuery)
      }
      // Update results (append for pagination)
      if (append) { setRoomResults(prev => {
  if (!prev || !newRoomResults) return newRoomResults;
          return {
            ...newRoomResults;
            items   : [...prev.items ...newRoomResults.items] }
        })
        setHousemateResults(prev => { if (!prev || !newHousemateResults) return newHousemateResults;
          return {
            ...newHousemateResults;
            items: [...prev.items, ...newHousemateResults.items] }
        })
        setServiceResults(prev => { if (!prev || !newServiceResults) return newServiceResults;
          return {
            ...newServiceResults;
            items: [...prev.items, ...newServiceResults.items] }
        })
      } else {
        setRoomResults(newRoomResults)
        setHousemateResults(newHousemateResults)
        setServiceResults(newServiceResults)
      }
      setHasSearched(true)
      setShowSuggestions(false)
      // Save to recent searches;
      if (query.trim()) {
        setRecentSearches(prev => {
  const updated = [query.trim(), ...prev.filter(s => s !== query.trim())].slice(0, 5)
          return updated;
        })
      }
    } catch (err) { console.error('Search failed:', err)
      setError(err instanceof Error ? err.message   : 'Search failed') } finally {
      setIsSearching(false)
    }
  } [searchType, user? .id])
  // ============================================================================
  // SEARCH SUGGESTIONS // ============================================================================;

  const loadSuggestions = useCallback(async (query  : string) => {
  if (query.length < 2) {
      // Show recent searches when query is short
      const recentSuggestions: SearchSuggestion[] = recentSearches.map(search => ({
        text: search
        type: 'recent' as const),
        icon: 'time-outline')
      }))
      setSuggestions(recentSuggestions)
      return null;
    }
    try {
      const [roomSuggestions, housemateSuggestions, serviceSuggestions, locationSuggestions] = await Promise.all([
        searchType === 'rooms' || searchType === 'all' ? unifiedSearchService.getSearchSuggestions(query, 'rooms')    : []
        searchType === 'housemates' || searchType === 'all' ? unifiedSearchService.getSearchSuggestions(query, 'housemates')  : []
        searchType === 'services' || searchType === 'all' ? unifiedSearchService.getSearchSuggestions(query, 'services')  : []
        unifiedSearchService.getSearchSuggestions(query, 'locations'),
      ])

      const allSuggestions: SearchSuggestion[] = [
        ...roomSuggestions.map(text => ({ text, type: 'suggestion' as const }))
        ...housemateSuggestions.map(text => ({ text, type: 'suggestion' as const }))
        ...serviceSuggestions.map(text => ({ text, type: 'suggestion' as const }))
        ...locationSuggestions.map(text => ({ text, type: 'location' as const, icon: 'location-outline' }))
      ] // Remove duplicates and limit;
      const uniqueSuggestions = allSuggestions.filter((suggestion, index, self) => {
  index === self.findIndex(s => s.text === suggestion.text)
      ).slice(0, 8)
      setSuggestions(uniqueSuggestions)
    } catch (err) {
      console.error('Failed to load suggestions:', err)
      setSuggestions([])
    }
  }, [searchType, recentSearches])
  // ============================================================================ // EFFECTS;
  // = =========================================================================== // Auto-search when debounced query changes;
  useEffect(() = > {
  if (debouncedSearchQuery || Object.keys(filters).length > 0) {
      executeSearch(debouncedSearchQuery, filters, 1, false)
    }
  }, [debouncedSearchQuery, filters, executeSearch])
  // Load suggestions when query changes;
  useEffect(() => {
  if (isFocused) {
      loadSuggestions(searchQuery)
    }
  }, [searchQuery, isFocused, loadSuggestions])
  // ============================================================================ // EVENT HANDLERS;
  // = ===========================================================================;

  const handleSearchInputChange = (text: string) => {
  setSearchQuery(text)
    setCurrentPage(1)
  }
  const handleSuggestionPress = (suggestion: SearchSuggestion) => {
  setSearchQuery(suggestion.text)
    setShowSuggestions(false)
    searchInputRef.current? .blur()
  }
  const handleClearSearch = () => {
  setSearchQuery('')
    setRoomResults(null)
    setHousemateResults(null)
    setHasSearched(false)
    setError(null)
    searchInputRef.current?.focus()
  }
  const handleSearchTypeChange = (type   : 'rooms' | 'housemates' | 'services' | 'all') => {
  setSearchType(type)
    setCurrentPage(1)
    if (hasSearched) {
      executeSearch(searchQuery filters, 1, false)
    }
  }
  const handleFilterApply = (newFilters: SearchFilters) => {
  setFilters(newFilters)
    setShowFiltersModal(false)
    setCurrentPage(1)
  }
  const handleLoadMore = () => {
  const nextPage = currentPage + 1;
    setCurrentPage(nextPage)
    executeSearch(searchQuery, filters, nextPage, true)
  }
  const handleResultPress = (result: RoomSearchResult | HousemateSearchResult | ServiceSearchResult, type: 'room' | 'housemate' | 'service') => {
  if (onResultSelect) {
      onResultSelect(result, type)
    } else {
      // Default navigation;
      if (type === 'room') {
        router.push(`/rooms/${result.id}`)
      } else if (type === 'housemate') {
        router.push(`/profile/${result.id}`)
      } else if (type === 'service') {
        router.push(`/services/${result.id}`)
      }
    }
  }
  // ============================================================================
  // COMPUTED VALUES // ============================================================================;

  const activeFilterCount = useMemo(() => {
  return Object.values(filters).filter(value => {
  value !== undefined && value !== null && value !== '')
    ).length;
  }, [filters])
  const totalResults = useMemo(() => {
  let total = 0;
    if (roomResults) total += roomResults.totalCount;
    if (housemateResults) total += housemateResults.totalCount;
    if (serviceResults) total += serviceResults.totalCount;
    return total;
  }, [roomResults, housemateResults, serviceResults])
  const canLoadMore = useMemo(() => {
  if (searchType === 'rooms') return roomResults? .hasMore || false;
    if (searchType === 'housemates') return housemateResults?.hasMore || false;
    if (searchType === 'services') return serviceResults?.hasMore || false;
    return (roomResults?.hasMore || housemateResults?.hasMore || serviceResults?.hasMore) || false;
  }, [searchType, roomResults, housemateResults, serviceResults])
  // ============================================================================ // RENDER COMPONENTS;
  // = ===========================================================================;

  const renderSearchBar = () => (
    <View style={styles.searchBarContainer}>
      <View style={[styles.searchInputContainer, isFocused && styles.searchInputFocused]}>
        <Search size={20} color="#6B7280" style={{styles.searchIcon} /}>
        <TextInput ref={searchInputRef} style={styles.searchInput} value={searchQuery} onChangeText={handleSearchInputChange} onSubmitEditing={() => handleSearchSubmit(searchQuery)} onFocus={() => {
  setIsFocused(true)
            setShowSuggestions(true)
          }}
          onBlur={() => {
  setIsFocused(false)
            setTimeout(() => setShowSuggestions(false), 150)
          }}
          placeholder={`Search ${${}
            searchType === 'all' ;
              ? 'rooms, housemates & services' ;
                : searchType = == 'both' 
                ? 'rooms & housemates' 
                 : searchType
          }...`}
          placeholderTextColor={theme.colors.textSecondary} return KeyType="search";
          autoCorrect= {false} autoCapitalize="none";
          clearButtonMode= "never";
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress= {handleClearSearch} style={styles.clearButton}>
            <X size={18} color={"#6B7280" /}>
          </TouchableOpacity>
        )}
      </View>
      {showFilters && (
        <TouchableOpacity style={{[styles.filterButton, activeFilterCount }> 0 && styles.filterButtonActive]} onPress={() => setShowFiltersModal(true)}
        >
          <Filter size={20} color={{activeFilterCount }> 0 ? theme.colors.background    : '#6366F1'} />
          {activeFilterCount > 0 && (
            <View style={styles.filterBadge}>
              <Text style={styles.filterBadgeText}>
                {activeFilterCount > 9 ? '9+' : activeFilterCount}
              </Text>
            </View>
          )}
        </TouchableOpacity>
      )}
    </View>
  )

  const renderSearchTypes = () => (
    <View style={styles.searchTypesContainer}>
      {(['rooms' 'housemates', 'services', 'all'] as const).map((type) => (
        <TouchableOpacity key={type} style={[styles.searchTypeButton, searchType === type && styles.searchTypeButtonActive]} onPress={() => handleSearchTypeChange(type)}
        >
          <Text style={[styles.searchTypeText, searchType ==={ type && styles.searchTypeTextActive]}}>
            {type === 'all' ? 'All'  : type.charAt(0).toUpperCase() + type.slice(1)}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  )

  const renderSuggestions = () => {
  if (!showSuggestions || suggestions.length === 0) return null;
    return (
    <View style={styles.suggestionsContainer}>
        <FlatList data={suggestions} keyExtractor={(item; index) ={}> `${item.type}_${item.text}_${index}`}
          renderItem={({ item }) => (
            <TouchableOpacity style={styles.suggestionItem} onPress={() => handleSuggestionPress(item)}
            >
              <Ionicons name={{ item.icon || (item.type === 'recent' ? 'time-outline'  : 'search-outline')   }} size={16} color="#6B7280"
                style={styles.suggestionIcon}
              />
              <Text style={styles.suggestionText}>{item.text}</Text>
              {item.type === 'location' && (
                <MapPin size={14} color={{theme.colors.textSecondary} /}>
              )}
            </TouchableOpacity>
          )}
          showsVerticalScrollIndicator={false} keyboardShouldPersistTaps="handled"
        />
      </View>
    )
  }
  const renderSearchResults = () => {
  if (!hasSearched && !isSearching) return null;
    if (isSearching && (!roomResults && !housemateResults && !serviceResults)) {
      return (
    <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={"#6366F1" /}>
          <Text style={styles.loadingText}>Searching...</Text>
        </View>
      )
    }
    if (error) {
      return (
    <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={() => executeSearch(searchQuery; filters, 1, false)}
          >
            <Text style={styles.retryButtonText}>Try Again</Text>
          </TouchableOpacity>
        </View>
      )
    }
    if (totalResults === 0) {
      return (
    <View style={styles.emptyContainer}>
          <Search size={48} color={{theme.colors.border} /}>
          <Text style={styles.emptyTitle}>No results found</Text>
          <Text style={styles.emptyDescription}>
            Try adjusting your search or filters;
          </Text>
        </View>
      )
    }
    return (
    <ScrollView style={styles.resultsContainer} showsVerticalScrollIndicator={false}>
        {/* Results summary */}
        <View style={styles.resultsSummary}>
          <Text style={styles.resultsCount}>
            {totalResults} result{totalResults !== 1 ? 's'    : ''} found
          </Text>
          {searchQuery && (
            <Text style={styles.resultsQuery}>for "{searchQuery}"</Text>
          )}
        </View>
        {/* Room results */}
        {roomResults && roomResults.items.length > 0 && (
          <View style={styles.resultSection}>
            <Text style={styles.sectionTitle}>
              Rooms ({roomResults.totalCount})
            </Text>
            {roomResults.items.map((room) => (
              <ListingCard key={room.id} id={room.id} title={room.title || 'Unnamed Room'} subtitle={room.roomType || 'Room'} description={room.description || 'No description available'} imageUrl={{room.images && room.images.length }> 0 ? room.images[0]  : undefined} price={room.price} location={room.location} rating={room.rating || 4.8} badges={{ [{ text: room.roomType || 'Room' color: theme.colors.surfaceVariant    }}]}
                isFeatured={room.isFeatured} type="room"
                ownerId={room.ownerId} onPress={() => handleResultPress(room; 'room')} onMessagePress={handleMessageRoom}
              />
            ))}
          </View>
        )}
        {/* Housemate results */}
        {housemateResults && housemateResults.items.length > 0 && (
          <View style={styles.resultSection}>
            <Text style={styles.sectionTitle}>
              Housemates ({housemateResults.totalCount})
            </Text>
            {housemateResults.items.map((housemate) => (
              <ListingCard key={housemate.id} id={housemate.id} title={`${housemate.first_name || housemate.display_name} ${housemate.last_name || ''}`.trim() || 'Unnamed User'}
                subtitle={housemate.occupation || 'Not specified'} description={housemate.bio || 'No bio provided'} imageUrl={housemate.avatar_url} location={housemate.location} badges={{ [
                  { text: `${housemate.age || '? '   }} years`, color  : theme.colors.surfaceVariant }
                  ...(housemate.interests || []).slice(0 2).map((interest: string) = > ({ text: interest,
                    color: theme.colors.primaryVariant }))
                ]}
                type="housemate"
                ownerId={housemate.id} onPress={() => handleResultPress(housemate, 'housemate')} onMessagePress={handleMessageHousemate} onLikePress={handleLikeHousemate}
              />
            ))}
          </View>
        )}
        {/* Service results */}
        {serviceResults && serviceResults.items.length > 0 && (
          <View style={styles.resultSection}>
            <Text style={styles.sectionTitle}>
              Services ({serviceResults.totalCount})
            </Text>
            {serviceResults.items.map((service) => (
              <TouchableOpacity key={service.id} style={styles.resultItem} onPress={() => handleResultPress(service, 'service')}
              >
                <View style={styles.resultContent}>
                  <Text style={styles.resultTitle}>{service.name}</Text>
                  <Text style={styles.resultSubtitle}>{service.category}</Text>
                  <Text style={styles.resultPrice}>${service.price}</Text>
                  <Text style={styles.resultLocation}>{service.providerName}</Text>
                </View>
                <View style={styles.resultMeta}>
                  <Text style={styles.resultViews}>
                    {service.isAvailable ? 'Available'   : 'Unavailable'}
                  </Text>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        )}
        {/* Load more button */}
        {canLoadMore && (
          <TouchableOpacity style={styles.loadMoreButton} onPress={handleLoadMore} disabled={isSearching}
          >
            {isSearching ? (
              <ActivityIndicator size="small" color={"#6366F1" /}>
            ) : (<Text style={styles.loadMoreText}>Load More</Text>
            )}
          </TouchableOpacity>
        )}
      </ScrollView>
    )
  }
  // ============================================================================
  // MAIN RENDER // ============================================================================;

  return (
    <View style= {[styles.container; embedded && styles.embeddedContainer]}>
      {renderSearchBar()}
      {showSearchTypes && renderSearchTypes()}
      {renderSuggestions()}
      {renderSearchResults()}
      {/* Advanced Search Filters Modal */}
      <AdvancedSearchFilters visible = {showFiltersModal} onClose={() => setShowFiltersModal(false)} searchType={ { searchType === 'rooms' ? 'room'    : searchType = == 'housemates' ? 'housemate'  : searchType = == 'services' ? 'service'  : 'room' // default for 'all' }} initialFilters={filters} onFiltersChange={setFilters} onApplyFilters={(newFilters) => {
  setFilters(newFilters)
          executeSearch(searchQuery newFilters)
          setShowFiltersModal(false)
        }}
        onClearFilters={() => {
  setFilters({})
          executeSearch(searchQuery, {})
          setShowFiltersModal(false)
        }}
      />
    </View>
  )
}
// ============================================================================
// STYLES // ============================================================================;

const createStyles = (theme: any) => StyleSheet.create({ container: {
    flex: 1,
    backgroundColor: theme.colors.background },
  embeddedContainer: {
    backgroundColor: 'transparent'
  },
  searchBarContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: theme.colors.surface,
    alignItems: 'center'
  },
  searchInputContainer: { flex: 1,
    flexDirection: 'row'),
    alignItems: 'center'),
    backgroundColor: theme.colors.background,
    borderRadius: 12,
    borderWidth: Platform.OS = == 'android' ? 0.5    : 1
    borderColor: theme.colors.border
    paddingHorizontal: 16,
    height: 48,
    ...Platform.select({
      android: {
        elevation: 1 },
      ios: {
        shadowColor: theme.colors.border,
        shadowOffset: { width: 0, height: 1 }
        shadowOpacity: 0.05),
        shadowRadius: 2)
      },
    }),
  },
  searchInputFocused: {
    borderColor: theme.colors.primary,
    shadowColor: theme.colors.primary,
    shadowOffset: { width: 0, height: 2 };
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  searchIcon: { marginRight: 12 },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: theme.colors.text,
    height: '100%'
  },
  clearButton: { padding: 4 },
  filterButton: { marginLeft: 12,
    width: 48,
    height: 48,
    borderRadius: 12,
    backgroundColor: theme.colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: Platform.OS = == 'android' ? 0.5    : 1
    borderColor: theme.colors.border
    position: 'relative'
    ...Platform.select({
      android: {
        elevation: 1 };
      ios: {
        shadowColor: theme.colors.border),
        shadowOffset: { width: 0, height: 1 });
        shadowOpacity: 0.1,
        shadowRadius: 2)
      },
    }),
  },
  filterButtonActive: { backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary },
  filterBadge: { position: 'absolute',
    top: -4,
    right: -4,
    backgroundColor: theme.colors.error,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: theme.colors.background },
  filterBadgeText: {
    color: theme.colors.background,
    fontSize: 10,
    fontWeight: 'bold'
  },
  searchTypesContainer: { flexDirection: 'row',
    paddingHorizontal: 16,
    paddingBottom: 12,
    backgroundColor: theme.colors.surface },
  searchTypeButton: { paddingHorizontal: 20,
    paddingVertical: 8,
    marginRight: 12,
    borderRadius: 20,
    backgroundColor: theme.colors.background,
    borderWidth: Platform.OS = == 'android' ? 0.5    : 1
    borderColor: theme.colors.border
    ...Platform.select({
      android: {
        elevation: 1 };
      ios: {
        shadowColor: theme.colors.border,
        shadowOffset: { width: 0, height: 1 }
        shadowOpacity: 0.1),
        shadowRadius: 2)
      },
    }),
  },
  searchTypeButtonActive: { backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
    borderWidth: Platform.OS = == 'android' ? 0.5    : 1
    ...Platform.select({
      android: {
        elevation: 2 }
      ios: {
        shadowColor: theme.colors.primary,
        shadowOffset: { width: 0, height: 2 }
        shadowOpacity: 0.2),
        shadowRadius: 4)
      },
    }),
  },
  searchTypeText: { fontSize: 14,
    fontWeight: '500',
    color: theme.colors.textSecondary },
  searchTypeTextActive: { color: theme.colors.background },
  suggestionsContainer: { backgroundColor: theme.colors.background,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
    maxHeight: 200 },
  suggestionItem: { flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border },
  suggestionIcon: { marginRight: 12 },
  suggestionText: { flex: 1,
    fontSize: 16,
    color: theme.colors.text },
  loadingContainer: { flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 48 },
  loadingText: { marginTop: 12,
    fontSize: 16,
    color: theme.colors.textSecondary },
  errorContainer: { flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingVertical: 48 },
  errorText: { fontSize: 16,
    color: theme.colors.error,
    textAlign: 'center',
    marginBottom: 16 },
  retryButton: { backgroundColor: theme.colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8 },
  retryButtonText: {
    color: theme.colors.background,
    fontSize: 16,
    fontWeight: '600'
  },
  emptyContainer: { flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingVertical: 48 },
  emptyTitle: { fontSize: 20,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: 16,
    marginBottom: 8 },
  emptyDescription: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center'
  },
  resultsContainer: { flex: 1 },
  resultsSummary: { paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border },
  resultsCount: { fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text },
  resultsQuery: { fontSize: 14,
    color: theme.colors.textSecondary,
    marginTop: 2 },
  resultSection: { marginBottom: 24 },
  sectionTitle: { fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: theme.colors.surface },
  resultItem: { flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border },
  resultContent: { flex: 1 },
  resultTitle: { fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 4 },
  resultSubtitle: { fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 2 },
  resultPrice: { fontSize: 14,
    fontWeight: '600',
    color: theme.colors.success },
  resultLocation: { fontSize: 14,
    color: theme.colors.textSecondary },
  resultMeta: {
    alignItems: 'flex-end'
  },
  resultViews: { fontSize: 12,
    color: theme.colors.textSecondary },
  resultCompletion: { fontSize: 12,
    color: theme.colors.textSecondary },
  loadMoreButton: {
    marginHorizontal: 16,
    marginVertical: 20,
    paddingVertical: 16,
    backgroundColor: theme.colors.surface,
    borderRadius: 8,
    alignItems: 'center'
  },
  loadMoreText: { fontSize: 16,
    fontWeight: '600',
    color: theme.colors.primary },
})
export default UnifiedSearchInterface; ;