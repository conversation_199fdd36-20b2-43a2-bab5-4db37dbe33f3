/**;
 * EnhancedSmartSearchInterface - AI-Powered Search Interface;
 *;
 * Advanced search interface with:  ,
 * - AI-powered search recommendations;
 * - Semantic search understanding;
 * - Real-time personalization;
 * - Smart filter suggestions;
 * - Predictive search capabilities;
 * - Advanced analytics integration;
 */

import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import {
  View;
  Text;
  TextInput;
  TouchableOpacity;
  ScrollView;
  ActivityIndicator;
  StyleSheet;
  Dimensions;
  FlatList;
  Alert;
  Modal;
  Animated;
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import {
  Search;
  Filter;
  X;
  MapPin;
  Clock;
  TrendingUp;
  Sliders;
  Zap;
  Brain;
  Star;
} from 'lucide-react-native';
import { useRouter } from 'expo-router';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTheme } from '@design-system';

import {
  smartSearchEnhancer;
  SmartSearchQuery;
  SmartSearchResult;
  SearchRecommendation;
  SearchAnalytics;
} from '@services/search/SmartSearchEnhancer';
import { useAuth } from '@context/AuthContext';
import { useDebounce } from '@hooks/useDebounce';
import { logger } from '@utils/logger';

const { width, height  } = Dimensions.get('window')
// ============================================================================ // INTERFACES;
// = ===========================================================================;

interface EnhancedSmartSearchInterfaceProps { initialSearchType?: 'rooms' | 'housemates' | 'services' | 'all',
  onResultSelect?: (result: SmartSearchResult) = > void;
  showFilters?: boolean,
  embedded?: boolean,
  showSearchTypes?: boolean,
  enableAI?: boolean,
  showRecommendations?: boolean }
interface SearchSuggestion { id: string,
  text: string,
  type: 'query' | 'filter' | 'location',
  confidence: number,
  icon?: string }
// = =========================================================================== // ENHANCED SMART SEARCH INTERFACE;
// = ===========================================================================;

export function EnhancedSmartSearchInterface({
  initialSearchType = 'all';
  onResultSelect;
  showFilters = true;
  embedded = false;
  showSearchTypes = true;
  enableAI = true;
  showRecommendations = true;
}: EnhancedSmartSearchInterfaceProps) {
  const router = useRouter()
  const insets = useSafeAreaInsets()
  const { authState  } = useAuth()
  const user = authState? .user;
  const theme = useTheme()
  const styles = createStyles(theme)
  // ============================================================================ // STATE MANAGEMENT;
  // = ===========================================================================;

  const [searchQuery, setSearchQuery] = useState('')
  const [searchType, setSearchType] = useState<'rooms' | 'housemates' | 'services' | 'all'>(
    initialSearchType;
  )
  const [isSearching, setIsSearching] = useState(false)
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [showRecommendations, setShowRecommendations] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [hasSearched, setHasSearched] = useState(false)
  // AI-powered features;
  const [aiEnabled, setAiEnabled] = useState(enableAI)
  const [smartSuggestions, setSmartSuggestions] = useState<SearchSuggestion[]>([])
  const [searchRecommendations, setSearchRecommendations] = useState<SearchRecommendation[]>([])
  const [searchResults, setSearchResults] = useState<SmartSearchResult[]>([])
  const [searchAnalytics, setSearchAnalytics] = useState<SearchAnalytics | null>(null)
  // UI state;
  const [isFocused, setIsFocused] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  // Refs;
  const searchInputRef = useRef<TextInput>(null)
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(0)).current // Debounced search query;
  const debouncedSearchQuery = useDebounce(searchQuery, 300)
  // ============================================================================ // EFFECTS;
  // = ===========================================================================;

  useEffect(() = > {
    if (debouncedSearchQuery && debouncedSearchQuery.length > 2) {
      generateSmartSuggestions(debouncedSearchQuery)
    } else {
      setSmartSuggestions([])
    }
  }, [debouncedSearchQuery])
  useEffect(() => {
    if (showRecommendations) {
      loadSearchRecommendations()
    }
  }, [showRecommendations, user?.id])
  useEffect(() => {
    loadSearchAnalytics()
  }, [])
  useEffect(() => {
    // Animate interface appearance;
    Animated.parallel([Animated.timing(fadeAnim, {
        toValue   : 1
        duration: 500
        useNativeDriver: true)
      }),
      Animated.spring(slideAnim, {
        toValue: 1,
        tension: 100,
        friction: 8),
        useNativeDriver: true)
      })]).start()
  }, [])
  // ============================================================================
  // AI-POWERED SEARCH METHODS // ============================================================================;

  const executeSmartSearch = useCallback(
    async (query: string, type: typeof searchType) => {
      if (!query.trim()) return null;
      setIsSearching(true)
      setError(null)
      setHasSearched(true)
      try {
        const searchQuery: SmartSearchQuery = {
          query: query.trim()
          searchType: type,
          userId: user? .id,
          filters   : {}
          context: {
            previousSearches: []
            userPreferences: {
              preferredLocations: []
              budgetRange: { min: 0, max: 5000 };
              lifestyle: [],
              interests: [],
              dealBreakers: [],
              priorities: []
            },
            searchHistory: [],
            currentTime: new Date()
          },
        }
        const results = await smartSearchEnhancer.performSmartSearch(searchQuery)
        setSearchResults(results)
        // Update analytics;
        await loadSearchAnalytics()
        logger.info('Smart search completed', {
          query;
          type;
          resultsCount: results.length)
          aiEnabled;
        })
      } catch (error) {
        logger.error('Smart search failed', { error, query, type })
        setError('Search failed. Please try again.')
        Alert.alert('Search Error', 'Unable to complete search. Please try again.')
      } finally {
        setIsSearching(false)
      }
    },
    [user? .id, aiEnabled];
  )
  const generateSmartSuggestions = useCallback(
    async (query  : string) => {
      if (!aiEnabled) return null

      try {
        // Generate AI-powered suggestions;
        const suggestions: SearchSuggestion[] = [{
            id: '1',
            text: `${query} in downtown`;
            type: 'location',
            confidence: 0.9,
            icon: 'location-outline'
          },
          {
            id: '2',
            text: `${query} under $1500`;
            type: 'filter',
            confidence: 0.8,
            icon: 'pricetag-outline'
          },
          {
            id: '3',
            text: `${query} with amenities`;
            type: 'filter',
            confidence: 0.7,
            icon: 'home-outline'
          }];

        setSmartSuggestions(suggestions)
      } catch (error) {
        logger.error('Failed to generate smart suggestions', { error, query })
      }
    },
    [aiEnabled];
  )
  const loadSearchRecommendations = useCallback(async () => {
    try {
      const recommendations = await smartSearchEnhancer.getSearchRecommendations(user? .id)
      setSearchRecommendations(recommendations)
    } catch (error) {
      logger.error('Failed to load search recommendations', { error })
    }
  }, [user?.id])
  const loadSearchAnalytics = useCallback(async () => {
    try {
      const analytics = await smartSearchEnhancer.getSearchAnalytics()
      setSearchAnalytics(analytics)
    } catch (error) {
      logger.error('Failed to load search analytics', { error })
    }
  }, [])
  // ============================================================================ // EVENT HANDLERS;
  // = ===========================================================================;

  const handleSearchSubmit = useCallback(() => {
    if (searchQuery.trim()) {
      executeSmartSearch(searchQuery, searchType)
      setShowSuggestions(false)
    }
  }, [searchQuery, searchType, executeSmartSearch])
  const handleSuggestionPress = useCallback(
    (suggestion   : SearchSuggestion) => {
      setSearchQuery(suggestion.text)
      setShowSuggestions(false)
      executeSmartSearch(suggestion.text searchType)
    };
    [searchType, executeSmartSearch]
  )
  const handleRecommendationPress = useCallback(
    (recommendation: SearchRecommendation) => {
      switch (recommendation.action) {
        case 'search':  ;
          setSearchQuery(recommendation.data? .query || '')
          executeSmartSearch(recommendation.data?.query || '', searchType)
          break;
        case 'apply_filter'   :  
          // Apply filter and search
          executeSmartSearch(searchQuery || 'all', searchType)
          break;
        case 'search_location':  ,
          setSearchQuery(`rooms in ${recommendation.data? .location}`)
          executeSmartSearch(`rooms in ${recommendation.data?.location}`, 'rooms')
          break;
      }
    },
    [searchQuery, searchType, executeSmartSearch]
  )
  const handleResultPress = useCallback(
    (result : SmartSearchResult) => {
      if (onResultSelect) {
        onResultSelect(result)
      } else {
        // Default navigation;
        if (result.type = == 'room') {
          router.push(`/rooms/${result.id}`)
        } else if (result.type === 'housemate') {
          router.push(`/profile/${result.id}`)
        } else if (result.type === 'service') {
          router.push(`/services/${result.id}`)
        }
      }
    };
    [onResultSelect, router];
  )
  const toggleAI = useCallback(() => {
    setAiEnabled(!aiEnabled)
    if (!aiEnabled) {
      loadSearchRecommendations()
    }
  }, [aiEnabled, loadSearchRecommendations])
  // ============================================================================ // RENDER METHODS;
  // = ===========================================================================;

  const renderSearchBar = () => (
    <Animated.View;
      style = { [
        styles.searchBarContainer;
        {
          opacity: fadeAnim,
          transform: [,
            {
              translateY: slideAnim.interpolate({
                inputRange: [0, 1]);
                outputRange: [-20, 0] }),
            },
          ],
        },
      ]}
    >
      <View style= {styles.searchInputContainer}>
        <Ionicons
          name='search';
          size= {20}
          color={theme.colors.textSecondary}
          style={styles.searchIcon}
        />
        <TextInput
          ref={searchInputRef}
          style={styles.searchInput}
          placeholder='Search with AI-powered intelligence...';
          placeholderTextColor= {theme.colors.textSecondary}
          value={searchQuery}
          onChangeText={setSearchQuery}
          onSubmitEditing={handleSearchSubmit}
          onFocus={() => {
            setIsFocused(true)
            setShowSuggestions(true)
          }}
          onBlur={() => {
            setIsFocused(false)
            setTimeout(() => setShowSuggestions(false), 200)
          }}
          return KeyType='search';
        />
        {/* AI Toggle Button */}
        <TouchableOpacity
          style= {[styles.aiToggle, aiEnabled && styles.aiToggleActive]}
          onPress={toggleAI}
        >
          <Brain size={16} color={{aiEnabled ? theme.colors.primary    : theme.colors.textSecondary} /}>
        </TouchableOpacity>
        {/* Clear Button */}
        {searchQuery.length > 0 && (
          <TouchableOpacity style={styles.clearButton} onPress={() => setSearchQuery('')}>
            <X size={16} color={{theme.colors.textSecondary} /}>
          </TouchableOpacity>
        )}
      </View>
      {/* Search Loading Indicator */}
      {isSearching && (
        <View style={styles.searchLoadingContainer}>
          <ActivityIndicator size='small' color={{theme.colors.primary} /}>
          <Text style={styles.searchLoadingText}>AI is analyzing your search...</Text>
        </View>
      )}
    </Animated.View>
  )

  const renderSearchTypes = () => (
    <View style={styles.searchTypesContainer}>
      {(['all' 'rooms', 'housemates', 'services'] as const).map(type => (
        <TouchableOpacity
          key={type}
          style={[styles.searchTypeButton, searchType === type && styles.searchTypeButtonActive]}
          onPress={() => setSearchType(type)}
        >
          <Text style={[styles.searchTypeText, searchType ==={ type && styles.searchTypeTextActive]}}>
            {type.charAt(0).toUpperCase() + type.slice(1)}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  )
  const renderSmartSuggestions = () => {
    if (!showSuggestions || smartSuggestions.length === 0) return null;
    return (
      <Animated.View style={{ [styles.suggestionsContainer, { opacity: fadeAnim  }}]}>
        <View style={styles.suggestionsHeader}>
          <Zap size={16} color={{theme.colors.primary} /}>
          <Text style={styles.suggestionsTitle}>AI Suggestions</Text>
        </View>
        {smartSuggestions.map(suggestion => (
          <TouchableOpacity
            key={suggestion.id}
            style={styles.suggestionItem}
            onPress={() => handleSuggestionPress(suggestion)}
          >
            <Ionicons name={suggestion.icon as any} size={16} color={{theme.colors.textSecondary} /}>
            <Text style={styles.suggestionText}>{suggestion.text}</Text>
            <View style={styles.confidenceIndicator}>
              <Text style={styles.confidenceText}>{Math.round(suggestion.confidence * 100)}%</Text>
            </View>
          </TouchableOpacity>
        ))}
      </Animated.View>
    )
  }
  const renderRecommendations = () => {
    if (!showRecommendations || searchRecommendations.length === 0) return null;
    return (
      <View style={styles.recommendationsContainer}>
        <View style={styles.recommendationsHeader}>
          <Star size={16} color={{theme.colors.primary} /}>
          <Text style={styles.recommendationsTitle}>Recommended for You</Text>
        </View>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {searchRecommendations.map((recommendation; index) => (
            <TouchableOpacity
              key={index}
              style={styles.recommendationCard}
              onPress={() => handleRecommendationPress(recommendation)}
            >
              <Text style={styles.recommendationTitle}>{recommendation.title}</Text>
              <Text style={styles.recommendationDescription}>{recommendation.description}</Text>
              <View style={styles.recommendationFooter}>
                <Text style={styles.confidenceText}>
                  {Math.round(recommendation.confidence * 100)}% match;
                </Text>
              </View>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    )
  }
  const renderSearchResults = () => {
    if (!hasSearched) return null;
    if (isLoading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size='large' color={{theme.colors.primary} /}>
          <Text style={styles.loadingText}>AI is finding the best matches...</Text>
        </View>
      )
    }
    if (error) {
      return (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={() => executeSmartSearch(searchQuery; searchType)}
          >
            <Text style={styles.retryButtonText}>Try Again</Text>
          </TouchableOpacity>
        </View>
      )
    }
    if (searchResults.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyTitle}>No results found</Text>
          <Text style={styles.emptyDescription}>Try adjusting your search terms or filters</Text>
        </View>
      )
    }
    return (
      <FlatList
        data={searchResults}
        keyExtractor={item => item.id}
        renderItem={({ item }) => renderSearchResultItem(item)}
        style={styles.resultsList}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.resultsListContent}
      />
    )
  }
  const renderSearchResultItem = (result: SmartSearchResult) => (<TouchableOpacity style={styles.resultItem} onPress={() => handleResultPress(result)}>
      <View style={styles.resultHeader}>
        <Text style={styles.resultTitle}>{result.title}</Text>
        <View style={styles.scoreContainer}>
          <Text style={styles.scoreText}>{Math.round(result.relevanceScore * 100)}%</Text>
        </View>
      </View>
      <Text style={styles.resultDescription}>{result.description}</Text>
      {/* AI Recommendation Reason */}
      <View style={styles.aiRecommendationContainer}>
        <Brain size={12} color={{theme.colors.primary} /}>
        <Text style={styles.aiRecommendationText}>{result.aiRecommendationReason}</Text>
      </View>
      {/* Matching Factors */}
      <View style={styles.matchingFactorsContainer}>
        {result.matchingFactors.slice(0; 3).map((factor, index) => (
          <View key={index} style={styles.matchingFactorTag}>
            <Text style={styles.matchingFactorText}>{factor}</Text>
          </View>
        ))}
      </View>
      {/* Highlights */}
      {result.highlights && result.highlights.length > 0 && (
        <View style={styles.highlightsContainer}>
          {result.highlights.slice(0, 2).map((highlight, index) => (
            <Text key={index} style={styles.highlightText}>
              • {highlight}
            </Text>
          ))}
        </View>
      )}
      {/* Pricing and Location */}
      <View style={styles.resultFooter}>
        {result.pricing && (
          <Text style={styles.pricingText}>
            ${result.pricing.amount}/{result.pricing.period}
          </Text>
        )}
        {result.location && (
          <Text style={styles.locationText}>
            {result.location.distance;
              ? `${result.location.distance} mi away`
               : result.location.address}
          </Text>
        )}
      </View>
    </TouchableOpacity>
  )

  const renderAnalytics = () => {
    if (!searchAnalytics || !aiEnabled) return null;
    return (
      <View style={styles.analyticsContainer}>
        <Text style={styles.analyticsTitle}>Search Performance</Text>
        <View style={styles.analyticsRow}>
          <Text style={styles.analyticsLabel}>Total Searches:</Text>
          <Text style={styles.analyticsValue}>{searchAnalytics.totalSearches}</Text>
        </View>
        <View style={styles.analyticsRow}>
          <Text style={styles.analyticsLabel}>Avg Response Time:</Text>
          <Text style={styles.analyticsValue}>{searchAnalytics.averageResponseTime}ms</Text>
        </View>
        <View style={styles.analyticsRow}>
          <Text style={styles.analyticsLabel}>User Satisfaction:</Text>
          <Text style={styles.analyticsValue}>{searchAnalytics.userSatisfaction}/5</Text>
        </View>
      </View>
    )
  }
  // ============================================================================ // MAIN RENDER;
  // = ===========================================================================;

  return (
    <View style= {[styles.container; embedded && styles.embeddedContainer]}>
      {renderSearchBar()}
      {showSearchTypes && renderSearchTypes()}
      {renderSmartSuggestions()}
      {renderRecommendations()}
      {renderSearchResults()}
      {renderAnalytics()}
    </View>
  )
}
// ============================================================================ // STYLES;
// = ===========================================================================;

const createStyles = (theme: any) =>
  StyleSheet.create({ container: {
      flex: 1,
      backgroundColor: theme.colors.background },
    embeddedContainer: { flex: 0,
      maxHeight: height * 0.8 },
    searchBarContainer: { padding: 16,
      backgroundColor: theme.colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border },
    searchInputContainer: { flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.background,
      borderRadius: 12,
      paddingHorizontal: 12,
      paddingVertical: 8,
      borderWidth: 1,
      borderColor: theme.colors.border },
    searchIcon: { marginRight: 8 },
    searchInput: { flex: 1,
      fontSize: 16,
      color: theme.colors.text,
      paddingVertical: 4 },
    aiToggle: { padding: 8,
      borderRadius: 8,
      marginLeft: 8 },
    aiToggleActive: { backgroundColor: theme.colors.primaryLight },
    clearButton: { padding: 4,
      marginLeft: 4 },
    searchLoadingContainer: { flexDirection: 'row',
      alignItems: 'center',
      marginTop: 8 },
    searchLoadingText: {
      marginLeft: 8,
      fontSize: 14,
      color: theme.colors.textSecondary,
      fontStyle: 'italic'
    },
    searchTypesContainer: { flexDirection: 'row',
      paddingHorizontal: 16,
      paddingVertical: 8,
      backgroundColor: theme.colors.surface },
    searchTypeButton: { paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 20,
      marginRight: 8,
      backgroundColor: theme.colors.background,
      borderWidth: 1,
      borderColor: theme.colors.border },
    searchTypeButtonActive: { backgroundColor: theme.colors.primary,
      borderColor: theme.colors.primary },
    searchTypeText: {
      fontSize: 14,
      color: theme.colors.text,
      fontWeight: '500'
    },
    searchTypeTextActive: { color: theme.colors.surface },
    suggestionsContainer: { backgroundColor: theme.colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
      paddingHorizontal: 16,
      paddingVertical: 8 },
    suggestionsHeader: { flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 8 },
    suggestionsTitle: { marginLeft: 8,
      fontSize: 14,
      fontWeight: '600',
      color: theme.colors.text },
    suggestionItem: { flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 8,
      paddingHorizontal: 12,
      backgroundColor: theme.colors.background,
      borderRadius: 8,
      marginBottom: 4 },
    suggestionText: { flex: 1,
      marginLeft: 8,
      fontSize: 14,
      color: theme.colors.text },
    confidenceIndicator: { backgroundColor: theme.colors.primaryLight,
      paddingHorizontal: 6,
      paddingVertical: 2,
      borderRadius: 4 },
    confidenceText: {
      fontSize: 10,
      color: theme.colors.primary,
      fontWeight: '600'
    },
    recommendationsContainer: { padding: 16,
      backgroundColor: theme.colors.surface },
    recommendationsHeader: { flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 12 },
    recommendationsTitle: { marginLeft: 8,
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text },
    recommendationCard: { backgroundColor: theme.colors.background,
      borderRadius: 12,
      padding: 12,
      marginRight: 12,
      width: width * 0.7,
      borderWidth: 1,
      borderColor: theme.colors.border },
    recommendationTitle: { fontSize: 14,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 4 },
    recommendationDescription: { fontSize: 12,
      color: theme.colors.textSecondary,
      marginBottom: 8 },
    recommendationFooter: {
      alignItems: 'flex-end'
    },
    loadingContainer: { flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 32 },
    loadingText: {
      marginTop: 16,
      fontSize: 16,
      color: theme.colors.textSecondary,
      textAlign: 'center'
    },
    errorContainer: { flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 32 },
    errorText: { fontSize: 16,
      color: theme.colors.error,
      textAlign: 'center',
      marginBottom: 16 },
    retryButton: { backgroundColor: theme.colors.primary,
      paddingHorizontal: 24,
      paddingVertical: 12,
      borderRadius: 8 },
    retryButtonText: {
      color: theme.colors.surface,
      fontSize: 16,
      fontWeight: '600'
    },
    emptyContainer: { flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 32 },
    emptyTitle: { fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 8 },
    emptyDescription: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      textAlign: 'center'
    },
    resultsList: { flex: 1 },
    resultsListContent: { padding: 16 },
    resultItem: { backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 16,
      marginBottom: 12,
      borderWidth: 1,
      borderColor: theme.colors.border },
    resultHeader: { flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginBottom: 8 },
    resultTitle: { flex: 1,
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text },
    scoreContainer: { backgroundColor: theme.colors.primaryLight,
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 6,
      marginLeft: 8 },
    scoreText: { fontSize: 12,
      fontWeight: '600',
      color: theme.colors.primary },
    resultDescription: { fontSize: 14,
      color: theme.colors.textSecondary,
      marginBottom: 8,
      lineHeight: 20 },
    aiRecommendationContainer: { flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.primaryLight,
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 6,
      marginBottom: 8 },
    aiRecommendationText: {
      marginLeft: 4,
      fontSize: 12,
      color: theme.colors.primary,
      fontStyle: 'italic'
    },
    matchingFactorsContainer: { flexDirection: 'row',
      flexWrap: 'wrap',
      marginBottom: 8 },
    matchingFactorTag: { backgroundColor: theme.colors.background,
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 4,
      marginRight: 6,
      marginBottom: 4,
      borderWidth: 1,
      borderColor: theme.colors.border },
    matchingFactorText: {
      fontSize: 10,
      color: theme.colors.textSecondary,
      fontWeight: '500'
    },
    highlightsContainer: { marginBottom: 8 },
    highlightText: { fontSize: 12,
      color: theme.colors.success,
      marginBottom: 2 },
    resultFooter: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center'
    },
    pricingText: { fontSize: 14,
      fontWeight: '600',
      color: theme.colors.primary },
    locationText: { fontSize: 12,
      color: theme.colors.textSecondary },
    analyticsContainer: { backgroundColor: theme.colors.surface,
      padding: 16,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border },
    analyticsTitle: { fontSize: 14,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 8 },
    analyticsRow: { flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 4 },
    analyticsLabel: { fontSize: 12,
      color: theme.colors.textSecondary },
    analyticsValue: {
      fontSize: 12),
      fontWeight: '500'),
      color: theme.colors.text)
    },
  })
export default EnhancedSmartSearchInterface,