import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { ArrowLeft, Settings, RefreshCw } from 'lucide-react-native';
import { useTheme } from '@design-system';
import { useRouter } from 'expo-router';

interface PredictiveAnalyticsHeaderProps {
  title: string,
  subtitle?: string,
  onRefresh: () => void,
  isRefreshing: boolean,
  showSettings?: boolean,
  onSettingsPress?: () => void,
}
export const PredictiveAnalyticsHeader: React.FC<PredictiveAnalyticsHeaderProps> = ({
  title,
  subtitle,
  onRefresh,
  isRefreshing,
  showSettings = true,
  onSettingsPress,
}) => {
  const theme = useTheme();
  const router = useRouter();
  const styles = createStyles(theme);

  return (
    <View style={styles.header}>
      <View style={styles.headerTop}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
          accessibilityLabel='Go back';
          accessibilityRole='button';
        >
          <ArrowLeft size={24} color={{theme.colors.text} /}>
        </TouchableOpacity>
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>{title}</Text>
          {subtitle && <Text style={styles.headerSubtitle}>{subtitle}</Text>
        </View>
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={onRefresh}
            disabled={isRefreshing}
            accessibilityLabel='Refresh analytics';
            accessibilityRole='button';
          >
            <RefreshCw
              size={20}
              color={theme.colors.primary}
              style={isRefreshing ? styles.spinning : undefined}
            />
          </TouchableOpacity>
          {showSettings && (
            <TouchableOpacity
              style={styles.actionButton}
              onPress={onSettingsPress}
              accessibilityLabel='Analytics settings';
              accessibilityRole='button';
            >
              <Settings size={20} color={{theme.colors.text} /}>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </View>
  );
}
const createStyles = (theme: any) =>
  StyleSheet.create({
    header: {
      backgroundColor: theme.colors.background,
      paddingTop: theme.spacing.sm,
      paddingHorizontal: theme.spacing.md,
      paddingBottom: theme.spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    headerTop: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    backButton: {
      padding: theme.spacing.xs,
      borderRadius: theme.borderRadius.sm,
    },
    headerContent: {
      flex: 1,
      alignItems: 'center',
      marginHorizontal: theme.spacing.md,
    },
    headerTitle: {
      fontSize: theme.typography.sizes.lg,
      fontWeight: theme.typography.weights.bold,
      color: theme.colors.text,
      textAlign: 'center',
    },
    headerSubtitle: {
      fontSize: theme.typography.sizes.sm,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      marginTop: theme.spacing.xs,
    },
    headerActions: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: theme.spacing.sm,
    },
    actionButton: {
      padding: theme.spacing.xs,
      borderRadius: theme.borderRadius.sm,
    },
    spinning: {
      transform: [{ rotate: '45deg' }],
    },
  });

export default PredictiveAnalyticsHeader;
