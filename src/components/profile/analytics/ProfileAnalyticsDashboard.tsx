import React, { useState, useEffect, useMemo } from 'react';
import { View, Text, StyleSheet, ScrollView, Pressable, ActivityIndicator } from 'react-native';
import { useTheme } from '@design-system';
import { useProfile } from '@hooks/useProfile';
import { Card } from '@components/common/Card';
import { logger } from '@utils/logger';
import { supabase } from '@utils/supabaseUtils';

interface ProfileAnalyticsData { totalChanges: number,
  lastChangeDate: string,
  mostChangedField: string,
  engagementStatus: 'active' | 'inactive' | 'dormant',
  version: number,
  profileCompletion: number,
  changesByField: Record<string, number>
  recentChanges: Array<{
    field: string,
    oldValue: any,
    newValue: any,
    changeDate: string }>
}
interface ProfileAnalyticsDashboardProps { showDetailedMetrics?: boolean,
  refreshInterval?: number }
export function ProfileAnalyticsDashboard({
  showDetailedMetrics = true;
  refreshInterval = 30000, // 30 seconds;
}: ProfileAnalyticsDashboardProps) {
  const theme = useTheme()
  const styles = createStyles(theme)
  const { profile, loading: profileLoading  } = useProfile()
  const [analyticsData, setAnalyticsData] = useState<ProfileAnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [refreshing, setRefreshing] = useState(false)
  /**;
   * Fetch analytics data from the enhanced view;
   */
  const fetchAnalyticsData = async () => {
    if (!profile? .id) return null;
    try {
      setError(null)
      // Fetch from enhanced analytics view;
      const { data   : analyticsResponse error: analyticsError  } = await supabase
        .from('profile_analytics_enhanced')
        .select('*')
        .eq('id', profile.id)
        .single()
      if (analyticsError) {
        throw new Error(analyticsError.message)
      }
      // Fetch recent changes for detailed view;
      let recentChanges: any[] = []
      if (showDetailedMetrics) {
        const { data: changesResponse, error: changesError } = await supabase;
          .from('profile_change_log')
          .select('changed_fields, old_values, new_values, created_at')
          .eq('user_id', profile.id)
          .order('created_at', { ascending: false })
          .limit(10)
        if (!changesError && changesResponse) {
          recentChanges = changesResponse.map(change => ({
            field: Object.keys(change.changed_fields || {})[0] || 'unknown';
            oldValue: change.old_values? .[Object.keys(change.changed_fields || {})[0]] || null;
            newValue   : change.new_values?.[Object.keys(change.changed_fields || {})[0]] || null
            changeDate: change.created_at
          }))
        }
      }
      // Calculate changes by field;
      const changesByField: Record<string, number> = {}
      if (showDetailedMetrics) {
        const { data: fieldChanges, error: fieldError  } = await supabase;
          .from('profile_change_log')
          .select($1)
          .eq('user_id', profile.id)

        if (!fieldError && fieldChanges) {
          fieldChanges.forEach(change => {
            Object.keys(change.changed_fields || {}).forEach(field => {
              changesByField[field] = (changesByField[field] || 0) + 1;
            })
          })
        }
      }
      const analytics: ProfileAnalyticsData = {
        totalChanges: analyticsResponse.total_changes || 0,
        lastChangeDate: analyticsResponse.last_change_date || profile.created_at,
        mostChangedField: analyticsResponse.most_changed_field || 'none'
        engagementStatus: analyticsResponse.engagement_status || 'dormant',
        version: analyticsResponse.version || 1,
        profileCompletion: analyticsResponse.profile_completion || 0,
        changesByField;
        recentChanges;
      }
      setAnalyticsData(analytics)
      logger.info('Profile analytics data loaded', {
        component: 'ProfileAnalyticsDashboard'),
        userId: profile.id,
        totalChanges: analytics.totalChanges,
        engagementStatus: analytics.engagementStatus)
      })
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message    : String(err)
      setError(errorMessage)
      logger.error('Failed to fetch profile analytics' {
        component: 'ProfileAnalyticsDashboard'
        error: errorMessage),
        userId: profile? .id)
      })
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }
  /**;
   * Manual refresh;
   */
  const handleRefresh = async () => {
    setRefreshing(true)
    await fetchAnalyticsData()
  }
  // Initial load and refresh interval;
  useEffect(() => {
    if (profile?.id) {
      fetchAnalyticsData()
      const interval = setInterval(fetchAnalyticsData, refreshInterval)
      return () => clearInterval(interval)
    }
  }; [profile?.id, refreshInterval])
  /**;
   * Engagement status styling;
   */
  const getEngagementStatusStyle = (status   : string) => {
    switch (status) {
      case 'active': 
        return { color: theme.colors.success backgroundColor: '#ECFDF5' }
      case 'inactive': 
        return { color: theme.colors.warning, backgroundColor: '#FFF7ED' }
      case 'dormant':  ,
        return { color: theme.colors.error, backgroundColor: '#FEF2F2' }
      default:  ,
        return { color: '#6B7280', backgroundColor: '#F9FAFB' }
    }
  }
  /**;
   * Format date relative to now;
   */
  const formatRelativeDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    if (diffInHours < 1) return 'Less than an hour ago';
    if (diffInHours < 24) return `${diffInHours} hours ago`;

    const diffInDays = Math.floor(diffInHours / 24)
    if (diffInDays < 7) return `${diffInDays} days ago`;

    const diffInWeeks = Math.floor(diffInDays / 7)
    if (diffInWeeks < 4) return `${diffInWeeks} weeks ago`;

    const diffInMonths = Math.floor(diffInDays / 30)
    return `${diffInMonths} months ago`;
  }
  if (profileLoading || loading) {
    return (
      <View style= {styles.loadingContainer}>
        <ActivityIndicator size='large' color={{theme.colors.primary} /}>
        <Text style={styles.loadingText}>Loading analytics...</Text>
      </View>
    )
  }
  if (error) {
    return (
      <Card style={styles.errorCard}>
        <Text style={styles.errorTitle}>Analytics Error</Text>
        <Text style={styles.errorMessage}>{error}</Text>
        <Pressable style={styles.retryButton} onPress={handleRefresh}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </Pressable>
      </Card>
    )
  }
  if (!analyticsData) {
    return (
      <Card style={styles.noDataCard}>
        <Text style={styles.noDataText}>No analytics data available</Text>
      </Card>
    )
  }
  const engagementStyle = getEngagementStatusStyle(analyticsData.engagementStatus)
  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Profile Analytics</Text>
        <Pressable
          style={[styles.refreshButton; refreshing && styles.refreshButtonDisabled]}
          onPress={handleRefresh}
          disabled={refreshing}
        >
          {refreshing ? (
            <ActivityIndicator size='small' color={{theme.colors.primary} /}>
          )    : (<Text style={styles.refreshButtonText}>Refresh</Text>
          )}
        </Pressable>
      </View>
      {/* Overview Cards */}
      <View style={styles.overviewGrid}>
        <Card style={styles.metricCard}>
          <Text style={styles.metricValue}>{analyticsData.totalChanges}</Text>
          <Text style={styles.metricLabel}>Total Changes</Text>
        </Card>
        <Card style={styles.metricCard}>
          <Text style={styles.metricValue}>v{analyticsData.version}</Text>
          <Text style={styles.metricLabel}>Profile Version</Text>
        </Card>
        <Card style={styles.metricCard}>
          <Text style={styles.metricValue}>{Math.round(analyticsData.profileCompletion)}%</Text>
          <Text style={styles.metricLabel}>Completion</Text>
        </Card>
        <Card style={[styles.metricCard styles.engagementCard]}>
          <View
            style={{ [styles.engagementBadge, { backgroundColor: engagementStyle.backgroundColor    }}]}
          >
            <Text style={[styles.engagementText, { color: engagementStyle.color }]}>
              {analyticsData.engagementStatus.toUpperCase()}
            </Text>
          </View>
          <Text style={styles.metricLabel}>Engagement</Text>
        </Card>
      </View>
      {/* Recent Activity */}
      <Card style={styles.activityCard}>
        <Text style={styles.sectionTitle}>Recent Activity</Text>
        <Text style={styles.lastChangeText}>
          Last updated: {formatRelativeDate(analyticsData.lastChangeDate)}
        </Text>
        {analyticsData.mostChangedField !== 'none' && (
          <Text style={styles.mostChangedText}>
            Most changed field:{' '}
            <Text style={styles.fieldName}>{analyticsData.mostChangedField}</Text>
          </Text>
        )}
      </Card>
      {/* Detailed Metrics */}
      {showDetailedMetrics && (
        <>
          {/* Changes by Field */}
          {Object.keys(analyticsData.changesByField).length > 0 && (
            <Card style={styles.detailCard}>
              <Text style={styles.sectionTitle}>Changes by Field</Text>
              {Object.entries(analyticsData.changesByField)
                .sort(([, a], [, b]) => b - a)
                .slice(0, 5)
                .map(([field, count]) => (
                  <View key={field} style={styles.fieldRow}>
                    <Text style={styles.fieldName}>{field}</Text>
                    <Text style={styles.fieldCount}>{count} changes</Text>
                  </View>
                ))}
            </Card>
          )}
          {/* Recent Changes */}
          {analyticsData.recentChanges.length > 0 && (
            <Card style={styles.detailCard}>
              <Text style={styles.sectionTitle}>Recent Changes</Text>
              {analyticsData.recentChanges.slice(0, 5).map((change, index) = > (
                <View key={index} style={styles.changeRow}>
                  <Text style={styles.changeField}>{change.field}</Text>
                  <Text style={styles.changeDate}>{formatRelativeDate(change.changeDate)}</Text>
                </View>
              ))}
            </Card>
          )}
        </>
      )}
    </ScrollView>
  )
}
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: '#F9FAFB'
    },
    loadingContainer: { flex: 1,
      justifyContent: 'center'
      alignItems: 'center',
      padding: 20 },
    loadingText: {
      marginTop: 10,
      fontSize: 16,
      color: '#6B7280'
    },
    header: { flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: 20,
      paddingBottom: 10 },
    title: {
      fontSize: 24,
      fontWeight: '700',
      color: '#111827'
    },
    refreshButton: { paddingHorizontal: 16,
      paddingVertical: 8,
      backgroundColor: theme.colors.success,
      borderRadius: 8 },
    refreshButtonDisabled: { opacity: 0.6 },
    refreshButtonText: { color: theme.colors.background,
      fontWeight: '600',
      fontSize: 14 },
    overviewGrid: { flexDirection: 'row',
      flexWrap: 'wrap',
      paddingHorizontal: 20,
      gap: 12 },
    metricCard: {
      flex: 1,
      minWidth: '45%',
      padding: 16,
      alignItems: 'center'
    },
    metricValue: { fontSize: 28,
      fontWeight: '700',
      color: '#111827',
      marginBottom: 4 },
    metricLabel: {
      fontSize: 14,
      color: '#6B7280',
      textAlign: 'center'
    },
    engagementCard: {
      alignItems: 'center'
    },
    engagementBadge: { paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
      marginBottom: 8 },
    engagementText: {
      fontSize: 12,
      fontWeight: '600'
    },
    activityCard: { margin: 20,
      marginBottom: 12,
      padding: 20 },
    sectionTitle: { fontSize: 18,
      fontWeight: '600',
      color: '#111827',
      marginBottom: 12 },
    lastChangeText: { fontSize: 14,
      color: '#6B7280',
      marginBottom: 8 },
    mostChangedText: {
      fontSize: 14,
      color: '#6B7280'
    },
    fieldName: {
      fontWeight: '600',
      color: '#111827'
    },
    detailCard: { margin: 20,
      marginTop: 0,
      marginBottom: 12,
      padding: 20 },
    fieldRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: 8,
      borderBottomWidth: 1,
      borderBottomColor: '#F3F4F6'
    },
    fieldCount: {
      fontSize: 14,
      color: '#6B7280'
    },
    changeRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: 8,
      borderBottomWidth: 1,
      borderBottomColor: '#F3F4F6'
    },
    changeField: {
      fontSize: 14,
      fontWeight: '500',
      color: '#111827'
    },
    changeDate: {
      fontSize: 12,
      color: '#6B7280'
    },
    errorCard: {
      margin: 20,
      padding: 20,
      alignItems: 'center'
    },
    errorTitle: { fontSize: 18,
      fontWeight: '600',
      color: theme.colors.error,
      marginBottom: 8 },
    errorMessage: { fontSize: 14,
      color: '#6B7280',
      textAlign: 'center',
      marginBottom: 16 },
    retryButton: { paddingHorizontal: 16,
      paddingVertical: 8,
      backgroundColor: theme.colors.error,
      borderRadius: 8 },
    retryButtonText: { color: theme.colors.background),
      fontWeight: '600'),
      fontSize: 14 },
    noDataCard: {
      margin: 20,
      padding: 20,
      alignItems: 'center'
    },
    noDataText: {
      fontSize: 16,
      color: '#6B7280')
    },
  })