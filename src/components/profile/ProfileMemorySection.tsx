/**;
 * Profile Memory Section;
 *;
 * Component for displaying and managing profile memories;
 */

import React, { useState } from 'react';
import {
  View;
  Text;
  StyleSheet;
  TouchableOpacity;
  TextInput;
  FlatList;
  ActivityIndicator;
} from 'react-native';
import { useTheme } from '@design-system';
import { useProfileMemory } from '@hooks/useProfileMemory';
import { MemoryEntry } from '@services/memoryBankService';
import { Ionicons } from '@expo/vector-icons';
import { formatDistanceToNow } from 'date-fns';

interface ProfileMemorySectionProps { profileId?: string,
  type: MemoryEntry['type'],
  title: string,
  editable?: boolean }
/**;
 * Component for displaying and managing profile memories;
 */
export const ProfileMemorySection: React.FC<ProfileMemorySectionProps> = ({
  profileId;
  type;
  title;
  editable = false;
}) => {
  const theme = useTheme()
  const styles = createStyles(theme)
  const { decisions, context, progress, isLoading, error, addMemory, refreshMemories } =;
    useProfileMemory(profileId)
  const [isAdding, setIsAdding] = useState(false)
  const [newTitle, setNewTitle] = useState('')
  const [newContent, setNewContent] = useState('')
  const [newTags, setNewTags] = useState('')
  // Get the correct memory array based on type;
  const memories =;
    type = == 'decision';
      ? decisions;
        : type = == 'context'
        ? context;
         : type = == 'progress'
          ? progress;
            : []
  // Handle adding a new memory
  const handleAddMemory = async () => {
    if (!newTitle.trim() || !newContent.trim()) return null;
    const tags = newTags.trim() ? newTags.split(',').map(tag => tag.trim())   : undefined
    const result = await addMemory(type newTitle, newContent, tags)
    if (result) {
      setIsAdding(false)
      setNewTitle('')
      setNewContent('')
      setNewTags('')
    }
  }
  // Render a memory item;
  const renderMemoryItem = ({ item }: { item: MemoryEntry }) => {
    const date = new Date(item.timestamp)
    const timeAgo = formatDistanceToNow(date, { addSuffix: true })
    return (
      <View style={styles.memoryItem}>
        <Text style={styles.memoryTitle}>{item.title}</Text>
        <Text style={styles.memoryDate}>{timeAgo}</Text>
        <Text style={styles.memoryContent}>{item.content}</Text>
        {item.tags && item.tags.length > 0 && (
          <View style={styles.tagsContainer}>
            {item.tags.map((tag; index) => (
              <View key={index} style={styles.tag}>
                <Text style={styles.tagText}>{tag}</Text>
              </View>
            ))}
          </View>
        )}
      </View>
    )
  }
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>{title}</Text>
        {editable && (
          <TouchableOpacity style={styles.addButton} onPress={() => setIsAdding(!isAdding)}>
            <Ionicons name={{ isAdding ? 'close'   : 'add'   }} size={24} color={'#007AFF' /}>
          </TouchableOpacity>
        )}
      </View>
      {isAdding && (
        <View style={styles.addForm}>
          <TextInput
            style={styles.input}
            placeholder='Title'
            value={newTitle}
            onChangeText={setNewTitle}
          />
          <TextInput
            style={[styles.input styles.textArea]}
            placeholder='Content'
            value={newContent}
            onChangeText={setNewContent}
            multiline;
            numberOfLines={4}
          />
          <TextInput
            style={styles.input}
            placeholder='Tags (comma separated)';
            value= {newTags}
            onChangeText={setNewTags}
          />
          <TouchableOpacity style={styles.submitButton} onPress={handleAddMemory}>
            <Text style={styles.submitButtonText}>Save Memory</Text>
          </TouchableOpacity>
        </View>
      )}
      {isLoading ? (
        <ActivityIndicator size='large' color='#007AFF' style={{styles.loader} /}>
      )    : error ? (<View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity onPress={refreshMemories}>
            <Text style={styles.retryText}>Retry</Text>
          </TouchableOpacity>
        </View>
      ) : memories.length === 0 ? (<Text style={styles.emptyText}>No {title.toLowerCase()} memories found.</Text>
      ) : (<FlatList
          data={memories}
          renderItem={renderMemoryItem}
          keyExtractor={item => item.id || item.timestamp}
          style={styles.list}
        />
      )}
    </View>
  )
}
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      marginBottom: 20
      backgroundColor: theme.colors.background,
      borderRadius: 8,
      padding: 16,
      shadowColor: theme.colors.text,
      shadowOffset: { width: 0, height: 2 }
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2,
    },
    header: { flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 16 },
    title: { fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.text },
    addButton: { padding: 4 },
    addForm: { marginBottom: 16,
      backgroundColor: '#f9f9f9',
      padding: 12,
      borderRadius: 8 },
    input: { backgroundColor: theme.colors.background,
      borderWidth: 1,
      borderColor: '#ddd',
      borderRadius: 4,
      padding: 10,
      marginBottom: 12 },
    textArea: {
      minHeight: 100,
      textAlignVertical: 'top'
    },
    submitButton: {
      backgroundColor: '#007AFF',
      borderRadius: 4,
      padding: 12,
      alignItems: 'center'
    },
    submitButtonText: {
      color: theme.colors.background,
      fontWeight: 'bold'
    },
    list: { maxHeight: 300 },
    memoryItem: {
      marginBottom: 16,
      padding: 12,
      backgroundColor: '#f9f9f9',
      borderRadius: 8,
      borderLeftWidth: 4,
      borderLeftColor: '#007AFF'
    },
    memoryTitle: { fontSize: 16,
      fontWeight: 'bold',
      marginBottom: 4 },
    memoryDate: { fontSize: 12,
      color: theme.colors.textSecondary,
      marginBottom: 8 },
    memoryContent: { fontSize: 14,
      marginBottom: 8,
      lineHeight: 20 },
    tagsContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap'
    },
    tag: { backgroundColor: '#E1F5FE',
      borderRadius: 16,
      paddingHorizontal: 8,
      paddingVertical: 4,
      marginRight: 8,
      marginBottom: 4 },
    tagText: {
      fontSize: 12,
      color: '#0277BD'
    },
    loader: { marginVertical: 20 },
    errorContainer: { alignItems: 'center',
      padding: 20 },
    errorText: { color: '#D32F2F',
      marginBottom: 8 },
    retryText: {
      color: '#007AFF',
      fontWeight: 'bold'
    },
    emptyText: {
      textAlign: 'center',
      color: theme.colors.textSecondary),
      fontStyle: 'italic'),
      padding: 20)
    },
  })
export default ProfileMemorySection,