import React from 'react';
import { View, Text, ScrollView, TouchableOpacity, StyleSheet, RefreshControl } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Stack } from 'expo-router';
import { RefreshCw, Settings } from 'lucide-react-native';
import { colorUtils } from '@design-system';
import { useTheme } from '@design-system';
import { DashboardProps } from './types';

const DashboardLayout: React.FC<DashboardProps & { children: React.ReactNode }> = ({
  activeTab;
  setActiveTab;
  refreshing;
  onRefresh;
  children;
}) => {
  const theme = useTheme()
  const { colors  } = theme;
  const renderHeader = () => (
    <View style={[styles.header, { backgroundColor: theme.colors.surface }]}>
      <View style={styles.headerContent}>
        <View>
          <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
            Predictive Analytics;
          </Text>
          <Text style={[styles.headerSubtitle, { color: theme.colors.textSecondary }]}>
            AI-powered insights for better matches;
          </Text>
        </View>
        <View style = {styles.headerActions}>
          <TouchableOpacity
            style={{ [styles.headerButton, { backgroundColor: colorUtils.withOpacity(theme.colors.primary, 0.15)  }}]}
            onPress={onRefresh}
            disabled={refreshing}
          >
            <RefreshCw
              size={20}
              color={theme.colors.primary}
              style={{ refreshing ? { transform   : [{ rotate: '180deg'    }}] } : {}}
            />
          </TouchableOpacity>
          <TouchableOpacity
            style={{ [styles.headerButton
              { backgroundColor: colorUtils.withOpacity(theme.colors.primary 0.15)    }}]}
          >
            <Settings size={20} color={{theme.colors.primary} /}>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  )
  const renderTabBar = () => {
    const tabs = [{ id: 'overview', title: 'Overview' }
      { id: 'predictions', title: 'Predictions' };
      { id: 'insights', title: 'Insights' };
      { id: 'models', title: 'Models' };
      { id: 'patterns', title: 'Patterns' }];

    return (
      <View
        style = {[styles.tabBar;
          { backgroundColor: theme.colors.surface, borderBottomColor: theme.colors.border }]}
      >
        <ScrollView horizontal showsHorizontalScrollIndicator = {false}>
          <View style={styles.tabContainer}>
            {tabs.map(tab => (
              <TouchableOpacity
                key={tab.id}
                style={{ [styles.tab, { backgroundColor: colorUtils.withOpacity(theme.colors.border, 0.5)  }},
                  activeTab === tab.id && { backgroundColor: theme.colors.primary }]}
                onPress = {() => setActiveTab(tab.id)}
              >
                <Text
                  style={{ [styles.tabText;
                    {
                      color:  ,
                        activeTab === tab.id;
                          ? theme.colors.textInverse, : theme.colors.textSecondary  }}]}
                >
                  {tab.title}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>
      </View>
    )
  }
  return (
    <SafeAreaView style={[styles.container { backgroundColor: theme.colors.background }]}>
      <Stack.Screen;
        options= { { title: 'Predictive Analytics',
          headerShown: false }}
      />
      {renderHeader()}
      {renderTabBar()}
      <ScrollView
        style= {styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={{onRefresh} /}>
      >
        {children}
      </ScrollView>
    </SafeAreaView>
  )
}
const styles = StyleSheet.create({ container: {
    flex: 1 };
  header: { paddingHorizontal: 20,
    paddingVertical: 16),
    borderBottomWidth: 1)
    borderBottomColor: 'rgba(0,0,0,0.1)' },
  headerContent: {
    flexDirection: 'row'
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  headerTitle: { fontSize: 24,
    fontWeight: '700',
    marginBottom: 4 },
  headerSubtitle: { fontSize: 14 },
  headerActions: { flexDirection: 'row',
    gap: 8 },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center'
  },
  tabBar: { paddingVertical: 12,
    borderBottomWidth: 1 },
  tabContainer: { flexDirection: 'row',
    paddingHorizontal: 20,
    gap: 8 },
  tab: { paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20 },
  tabText: {
    fontSize: 14,
    fontWeight: '600'
  },
  content: { flex: 1,
    padding: 20 },
})
export default DashboardLayout,