import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '@design-system';
import type { Theme } from '@design-system' // Safe color utility to prevent [object Object] errors;
const safeColor = ($2) => {
  if (typeof color === 'string') return color;
  if (typeof color === 'object' && color !== null) {
    console.warn('Color object detected, converting to fallback:', color)
    return '#6366F1' // Default primary color;
  }
  return String(color)
}
// Safe color with opacity utility;
const safeColorWithOpacity = ($2) => {
  const baseColor = safeColor(color)
  if (baseColor.startsWith('#')) {
    // Convert opacity to hex (0-1 to 00-FF)
    const hex = Math.round(opacity * 255).toString(16).padStart(2, '0')
    return `${baseColor}${hex}`;
  }
  return baseColor;
}
interface ProfileMenuOptionProps { title: string,
  subtitle?: string,
  icon: string,
  iconColor?: string,
  onPress: () = > void;
  showChevron?: boolean,
  badge?: string | number,
  badgeColor?: string,
  disabled?: boolean,
  variant?: 'default' | 'primary' | 'success' | 'warning' | 'error' }
export function ProfileMenuOption({ title;
  subtitle;
  icon;
  iconColor;
  onPress;
  showChevron = true;
  badge;
  badgeColor;
  disabled = false;
  variant = 'default' }: ProfileMenuOptionProps): JSX.Element { const theme = useTheme()
  const styles = createStyles(theme)
  const getVariantStyles = () => {
  switch (variant) {
      case 'primary':  ;
        return {
          container: styles.primaryContainer,
          icon: styles.primaryIcon,
          title: styles.primaryTitle }
      case 'success':  ,
        return { container: styles.successContainer,
          icon: styles.successIcon,
          title: styles.successTitle }
      case 'warning':  ,
        return { container: styles.warningContainer,
          icon: styles.warningIcon,
          title: styles.warningTitle }
      case 'error':  ,
        return { container: styles.errorContainer,
          icon: styles.errorIcon,
          title: styles.errorTitle }
      default:  ,
        return { container: styles.defaultContainer,
          icon: styles.defaultIcon,
          title: styles.defaultTitle }
    }
  }
  const variantStyles = getVariantStyles()
  const finalIconColor = iconColor ? safeColor(iconColor)  : variantStyles.icon.color {
 {
  return ( {
    <TouchableOpacity {
      style = {[
        styles.container;
        variantStyles.container;
        disabled && styles.disabledContainer;
      ]} onPress={onPress} disabled={disabled} activeOpacity={0.7} accessibilityLabel={title} accessibilityHint={subtitle} accessibilityRole="button"
    >
      <View style={styles.content}>
        {/* Icon */}
        <View style={[styles.iconContainer, variantStyles.icon]}>
          <Ionicons name = {icon as any} size={24} color={finalIconColor}
          />
        </View>
        {/* Text Content */}
        <View style={styles.textContainer}>
          <Text style={[styles.title;
              variantStyles.title;
              disabled && styles.disabledText;
            ]} numberOfLines={1}
          >
            {title}
          </Text>
          {subtitle && (
            <Text style={[styles.subtitle, disabled && styles.disabledText]} numberOfLines = {2}
            >
              {subtitle}
            </Text>
          )}
        </View>
        {/* Badge */}
        {badge && (
          <View
            style={{ [styles.badge, badgeColor && { backgroundColor: safeColor(badgeColor)  }}]}
          >
            <Text style={styles.badgeText}>
              {typeof badge === 'number' && badge > 99 ? '99+'    : String(badge)}
            </Text>
          </View>
        )}
        {/* Chevron */}
        {showChevron && (
          <View style={styles.chevronContainer}>
            <Ionicons
              name="chevron-forward"
              size={20} color={safeColor(theme.colors.textSecondary)}
            />
          </View>
        )}
      </View>
    </TouchableOpacity>
  )
}
const createStyles = (theme: Theme) => {
  const primaryColor = safeColor(theme.colors.primary)
  const surfaceColor = safeColor(theme.colors.surface)
  const textColor = safeColor(theme.colors.text)
  const textSecondaryColor = safeColor(theme.colors.textSecondary)
  const borderColor = safeColor(theme.colors.border)
  const successColor = safeColor(theme.colors.success)
  const errorColor = safeColor(theme.colors.error)
  const warningColor = safeColor(theme.colors.warning)
  const disabledColor = safeColor(theme.colors.disabled)
  return StyleSheet.create({
    container: {
      borderRadius: theme.borderRadius.md
      marginVertical: theme.spacing.xs,
      overflow: 'hidden'
    },
    content: { flexDirection: 'row'
      alignItems: 'center',
      padding: theme.spacing.md,
      minHeight: 60 },
    iconContainer: { width: 40,
      height: 40,
      borderRadius: 20,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: theme.spacing.md },
    textContainer: {
      flex: 1,
      justifyContent: 'center'
    },
    title: { fontSize: theme.typography.fontSize.md,
      fontWeight: theme.typography.fontWeight.medium,
      lineHeight: theme.typography.lineHeight.tight },
    subtitle: { fontSize: theme.typography.fontSize.sm,
      color: textSecondaryColor,
      marginTop: theme.spacing.xs,
      lineHeight: theme.typography.lineHeight.normal },
    badge: {
      backgroundColor: errorColor,
      borderRadius: theme.borderRadius.full,
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: theme.spacing.xs,
      marginRight: theme.spacing.sm,
      minWidth: 24),
      alignItems: 'center'),
      justifyContent: 'center'
    },
    badgeText: { color: surfaceColor,
      fontSize: theme.typography.fontSize.xs,
      fontWeight: theme.typography.fontWeight.bold },
    chevronContainer: { marginLeft: theme.spacing.sm },

    // Variant styles;
    defaultContainer: {
      backgroundColor: surfaceColor,
      borderWidth: 1,
      borderColor: borderColor)
    },
    defaultIcon: { backgroundColor: safeColorWithOpacity(textSecondaryColor, 0.1),
      color: textSecondaryColor },
    defaultTitle: { color: textColor },

    primaryContainer: { backgroundColor: safeColorWithOpacity(primaryColor, 0.05),
      borderWidth: 1,
      borderColor: safeColorWithOpacity(primaryColor, 0.2) },
    primaryIcon: { backgroundColor: safeColorWithOpacity(primaryColor, 0.15),
      color: primaryColor },
    primaryTitle: { color: primaryColor },

    successContainer: { backgroundColor: safeColorWithOpacity(successColor, 0.05),
      borderWidth: 1,
      borderColor: safeColorWithOpacity(successColor, 0.2) },
    successIcon: { backgroundColor: safeColorWithOpacity(successColor, 0.15),
      color: successColor },
    successTitle: { color: successColor },

    warningContainer: { backgroundColor: safeColorWithOpacity(warningColor, 0.05),
      borderWidth: 1,
      borderColor: safeColorWithOpacity(warningColor, 0.2) },
    warningIcon: { backgroundColor: safeColorWithOpacity(warningColor, 0.15),
      color: warningColor },
    warningTitle: { color: warningColor },

    errorContainer: { backgroundColor: safeColorWithOpacity(errorColor, 0.05),
      borderWidth: 1,
      borderColor: safeColorWithOpacity(errorColor, 0.2) },
    errorIcon: { backgroundColor: safeColorWithOpacity(errorColor, 0.15),
      color: errorColor },
    errorTitle: { color: errorColor },

    // Disabled styles;
    disabledContainer: { opacity: 0.6,
      backgroundColor: safeColorWithOpacity(disabledColor, 0.05) },
    disabledText: { color: disabledColor },
  })
}
export default ProfileMenuOption,