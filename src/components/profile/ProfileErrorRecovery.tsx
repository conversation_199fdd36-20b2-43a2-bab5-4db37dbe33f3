import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useTheme } from '@design-system';

interface ProfileErrorRecoveryProps { error: string,
  onRetry: () = > void,
  onGoBack: () = > void;
  onReportIssue?: () => void }
export const ProfileErrorRecovery: React.FC<ProfileErrorRecoveryProps> = ({
  error;
  onRetry;
  onGoBack;
  onReportIssue;
}) => {
  const theme = useTheme()
  const { colors  } = theme;
  return (
    <View style={[styles.container; { backgroundColor: theme.colors.background }]}>
      <View style={[styles.errorCard, { backgroundColor: theme.colors.surface }]}>
        <View style={[styles.iconContainer, { backgroundColor: theme.colors.error + '20' }]}>
          <Feather name='alert-circle' size={48} color={{theme.colors.error} /}>
        </View>
        <Text style={[styles.title, { color: theme.colors.text }]}>Something went wrong</Text>
        <Text style={[styles.message, { color: theme.colors.textSecondary }]}>
          {error || 'An unexpected error occurred. Please try again.'}
        </Text>
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={{ [styles.primaryButton, { backgroundColor: theme.colors.primary    }}]}
            onPress={onRetry}
          >
            <Feather name='refresh-cw' size={16} color={'white' /}>
            <Text style={styles.primaryButtonText}>Try Again</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={{ [styles.secondaryButton, { borderColor: theme.colors.border    }}]}
            onPress={onGoBack}
          >
            <Feather name='arrow-left' size={16} color={{theme.colors.textSecondary} /}>
            <Text style={[styles.secondaryButtonText, { color: theme.colors.textSecondary }]}>
              Go Back;
            </Text>
          </TouchableOpacity>
          {onReportIssue && (
            <TouchableOpacity style={[styles.tertiaryButton]} onPress={onReportIssue}>
              <Text style={[styles.tertiaryButtonText, { color: theme.colors.primary }]}>
                Report Issue;
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </View>
  )
}
const styles = StyleSheet.create({ container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20 },
  errorCard: {
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    maxWidth: 320,
    width: '100%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 };
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  iconContainer: { width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16 },
  title: { fontSize: 20,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 8 },
  message: { fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24 },
  buttonContainer: { width: '100%',
    gap: 12 },
  primaryButton: { flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    gap: 8 },
  primaryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600'
  },
  secondaryButton: { flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    borderWidth: 1,
    gap: 8 },
  secondaryButtonText: {
    fontSize: 16,
    fontWeight: '500'
  });
  tertiaryButton: { alignItems: 'center'),
    paddingVertical: 8 },
  tertiaryButtonText: {
    fontSize: 14,
    fontWeight: '500')
  },
})