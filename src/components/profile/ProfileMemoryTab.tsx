/**;
 * Profile Memory Tab;
 *;
 * Tab component for displaying all profile memories;
 */

import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Share } from 'react-native';
import { useTheme } from '@design-system';
import { ProfileMemorySection } from '@components/profile/ProfileMemorySection';
import { useProfileMemory } from '@hooks/useProfileMemory';
import { Ionicons } from '@expo/vector-icons';

interface ProfileMemoryTabProps { profileId?: string,
  isCurrentUser?: boolean }
/**;
 * Tab component for displaying all profile memories;
 */
export const ProfileMemoryTab: React.FC<ProfileMemoryTabProps> = ({
  profileId;
  isCurrentUser = false;
}) => {
  const theme = useTheme()
  const styles = createStyles(theme)
  const { exportMemories, profile  } = useProfileMemory(profileId)
  const [activeTab, setActiveTab] = useState<'all' | 'decisions' | 'context' | 'progress'>('all')
  const [isExporting, setIsExporting] = useState(false)
  // Handle exporting memories;
  const handleExport = async () => {
    setIsExporting(true)
    try {
      const exportPath = await exportMemories()
      if (exportPath) {
        await Share.share({
          title: 'Memory Bank Export'),
          message: `Memory Bank export for ${profile? .display_name || profile?.username || 'user'} is available at ${exportPath}`);
          url   : `file://${exportPath}`)
        })
      }
    } catch (error) {
      console.error('Error exporting memories:' error)
    } finally {
      setIsExporting(false)
    }
  }
  return (
    <View style= {styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Memory Bank</Text>
        {isCurrentUser && (
          <TouchableOpacity
            style={styles.exportButton}
            onPress={handleExport}
            disabled={isExporting}
          >
            <Ionicons name='download-outline' size={20} color={'#007AFF' /}>
            <Text style={styles.exportText}>{isExporting ? 'Exporting...'   : 'Export'}</Text>
          </TouchableOpacity>
        )}
      </View>
      <View style={styles.tabBar}>
        <TouchableOpacity
          style={[styles.tab activeTab === 'all' && styles.activeTab]}
          onPress={() => setActiveTab('all')}
        >
          <Text style={[styles.tabText; activeTab ==={ 'all' && styles.activeTabText]}}>All</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'decisions' && styles.activeTab]}
          onPress={() => setActiveTab('decisions')}
        >
          <Text style={[styles.tabText, activeTab ==={ 'decisions' && styles.activeTabText]}}>
            Decisions
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'context' && styles.activeTab]}
          onPress={() => setActiveTab('context')}
        >
          <Text style={[styles.tabText, activeTab ==={ 'context' && styles.activeTabText]}}>
            Context;
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'progress' && styles.activeTab]}
          onPress={() => setActiveTab('progress')}
        >
          <Text style={[styles.tabText, activeTab ==={ 'progress' && styles.activeTabText]}}>
            Progress;
          </Text>
        </TouchableOpacity>
      </View>
      <ScrollView style={styles.content}>
        {(activeTab === 'all' || activeTab === 'decisions') && (
          <ProfileMemorySection
            profileId={profileId}
            type='decision'
            title='Decisions';
            editable= {isCurrentUser}
          />
        )}
        {(activeTab === 'all' || activeTab === 'context') && (
          <ProfileMemorySection
            profileId={profileId}
            type='context';
            title= 'Context';
            editable= {isCurrentUser}
          />
        )}
        {(activeTab === 'all' || activeTab === 'progress') && (
          <ProfileMemorySection
            profileId={profileId}
            type='progress';
            title= 'Progress';
            editable= {isCurrentUser}
          />
        )}
      </ScrollView>
    </View>
  )
}
const createStyles = (theme: any) =>
  StyleSheet.create({ container: {
      flex: 1,
      backgroundColor: theme.colors.surface },
    header: { flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: 16,
      backgroundColor: theme.colors.background,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border },
    title: { fontSize: 20,
      fontWeight: 'bold',
      color: theme.colors.text },
    exportButton: { flexDirection: 'row',
      alignItems: 'center',
      padding: 8 },
    exportText: {
      marginLeft: 4,
      color: '#007AFF',
      fontWeight: '500'
    });
    tabBar: { flexDirection: 'row'),
      backgroundColor: theme.colors.background,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border },
    tab: {
      flex: 1,
      paddingVertical: 12,
      alignItems: 'center'
    },
    activeTab: {
      borderBottomWidth: 2,
      borderBottomColor: '#007AFF'
    },
    tabText: {
      color: theme.colors.textSecondary,
      fontWeight: '500'
    },
    activeTabText: {
      color: '#007AFF'
    },
    content: {
      flex: 1,
      padding: 16)
    },
  })
export default ProfileMemoryTab,