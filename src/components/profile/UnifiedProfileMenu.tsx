import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, StyleSheet, Alert, useColorScheme } from 'react-native';
import {
  User;
  Camera;
  Heart;
  Brain;
  Home;
  Settings;
  Shield;
  Bell;
  CreditCard;
  Star;
  BarChart3;
  Target;
  Globe;
  Mic;
  HelpCircle;
  Users;
  MapPin;
  Palette;
  Eye;
  Languages;
  Briefcase;
} from 'lucide-react-native';

import { ProfileMenuOption } from './ProfileMenuOption';
import { navigateToProfileRoute } from '@utils/unifiedProfileNavigation';
import { logger } from '@services/loggerService';

import { useTheme } from '@design-system';

interface UnifiedProfileMenuProps { userContext?: {
    isAuthenticated: boolean,
    completionPercentage: number }
}
export function UnifiedProfileMenu({ userContext }: UnifiedProfileMenuProps) {
  const theme = useTheme()
  const styles = createStyles(theme)
  const colorScheme = useColorScheme()
  const [completionPercentage, setCompletionPercentage] = useState(
    userContext? .completionPercentage || 0;
  )
  useEffect(() => {
    if (userContext?.completionPercentage !== undefined) {
      setCompletionPercentage(userContext.completionPercentage)
    }
  }, [userContext?.completionPercentage])
  const handleMenuPress = (route   : string title: string) => {
    try {
      const result = navigateToProfileRoute(
        route
        {
          requiresCompletion: true,
          trackAnalytics: true,
          source: 'profile_menu'
        },
        userContext;
      )
      if (!result.success && result.blocked) {
        const { reason, message, requiredCompletion, currentCompletion  } = result.blocked;
        if (reason = == 'completion') {
          Alert.alert('Profile Incomplete';
            `${message}\n\nCurrent: ${currentCompletion}%\nRequired: ${requiredCompletion}%`);
            [{ text: 'Cancel', style: 'cancel' }
              {
                text: 'Complete Profile')
                onPress: () => {
                  navigateToProfileRoute('edit', { source: 'completion_prompt' }, userContext)
                },
              }];
          )
        } else if (reason = == 'auth') {
          Alert.alert('Authentication Required', message, [{ text: 'OK' }])
        } else {
          Alert.alert('Navigation Error', message, [{ text: 'OK' }])
        }
      }
    } catch (error) {
      logger.error('Menu navigation error', 'UnifiedProfileMenu', {
        route;
        title;
        error: error instanceof Error ? error.message    : String(error)
      })
      Alert.alert('Navigation Error' 'Unable to navigate to the selected page.', [{ text: 'OK' }])
    }
  }
  const menuSections = [{
      title: 'Profile & Media'
      items: [;
        {
          icon: User,
          title: 'Edit Profile',
          subtitle: 'Update your basic information',
          route: 'edit'
        },
        {
          icon: Camera,
          title: 'Profile Photos',
          subtitle: 'Manage your photos',
          route: 'photos'
        },
        {
          icon: 'video',
          title: 'Video Introduction',
          subtitle: 'Record a personal introduction',
          route: 'video-intro'
        }],
    },
    {
      title: 'Preferences & Personality',
      items: [,
        {
          icon: Heart,
          title: 'Interests & Hobbies',
          subtitle: 'Share what you love',
          route: 'interests'
        },
        {
          icon: Brain,
          title: 'Personality Assessment',
          subtitle: 'Complete personality quiz',
          route: 'personality'
        },
        {
          icon: Home,
          title: 'Lifestyle Preferences',
          subtitle: 'Your living style',
          route: 'lifestyle'
        },
        {
          icon: Settings,
          title: 'General Preferences',
          subtitle: 'Basic preferences',
          route: 'preferences'
        },
        {
          icon: MapPin,
          title: 'Living Preferences',
          subtitle: 'Housing preferences',
          route: 'living-preferences'
        }],
    },
    {
      title: 'Verification & Security',
      items: [,
        {
          icon: Shield,
          title: 'Verification Dashboard',
          subtitle: 'Verify your identity',
          route: 'verification-dashboard'
        },
        {
          icon: 'shield-check',
          title: 'Background Checks',
          subtitle: 'Security verification',
          route: 'background-checks'
        }],
    },
    {
      title: 'Settings & Configuration',
      items: [,
        {
          icon: Bell,
          title: 'Notifications',
          subtitle: 'Manage notifications',
          route: 'notifications'
        },
        {
          icon: Settings,
          title: 'Unified Settings',
          subtitle: 'All settings in one place',
          route: 'unified-settings'
        },
        {
          icon: Palette,
          title: 'Cultural & Accessibility',
          subtitle: 'Language, cultural, regional & accessibility settings',
          route: 'unified-cultural'
        }],
    },
    {
      title: 'Payment & Subscription',
      items: [,
        {
          icon: CreditCard,
          title: 'Account Management',
          subtitle: 'Payment, subscription, history, legal & support',
          route: 'unified-account'
        }],
    },
    {
      title: 'Advanced Features',
      items: [,
        {
          icon: Users,
          title: 'Role Dashboard',
          subtitle: 'Manage your roles',
          route: 'role-dashboard'
        },
        {
          icon: BarChart3,
          title: 'Unified Dashboard',
          subtitle: 'All dashboards in one place',
          route: 'unified-dashboard'
        },
        {
          icon: Brain,
          title: 'AI Compatibility',
          subtitle: 'AI matching insights',
          route: 'ai-compatibility-dashboard'
        },
        {
          icon: Target,
          title: 'Smart Matching',
          subtitle: 'Advanced matching',
          route: 'smart-matching-dashboard'
        },
        {
          icon: BarChart3,
          title: 'Predictive Analytics',
          subtitle: 'Data insights',
          route: 'predictive-analytics-dashboard'
        },
        {
          icon: Heart,
          title: 'Compatibility Insights',
          subtitle: 'Relationship insights',
          route: 'compatibility-insights'
        },
        {
          icon: Settings,
          title: 'Unified Preferences',
          subtitle: 'Living, matching, lifestyle & general preferences',
          route: 'unified-preferences'
        },
        {
          icon: Briefcase,
          title: 'Service Provider Hub',
          subtitle: 'Provider profile, portfolio & analytics',
          route: 'unified-service-provider'
        }],
    },
  ];

  return (
    <ScrollView
      style= {{ [styles.container; { backgroundColor: theme.colors.background    }}]}
      showsVerticalScrollIndicator={false}
    >
      <View style={styles.content}>
        {/* Completion Progress */}
        <View style={[styles.progressCard, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.progressTitle, { color: theme.colors.text }]}>
            Profile Completion;
          </Text>
          <View style={[styles.progressBar, { backgroundColor: theme.colors.border }]}>
            <View
              style={{ [styles.progressFill, {
                  width: `${completionPercentage  }}%`;
                  backgroundColor: theme.colors.primary,
                }]}
            />
          </View>
          <Text style= {[styles.progressText, { color: theme.colors.textSecondary }]}>
            {completionPercentage}% Complete;
          </Text>
        </View>
        {/* Menu Sections */}
        {menuSections.map((section, sectionIndex) => (
          <View key={sectionIndex} style={styles.section}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>{section.title}</Text>
            {section.items.map((item, itemIndex) => (
              <ProfileMenuOption
                key={itemIndex}
                icon={item.icon}
                title={item.title}
                subtitle={item.subtitle}
                onPress={() => handleMenuPress(item.route, item.title)}
              />
            ))}
          </View>
        ))}
      </View>
    </ScrollView>
  )
}
const createStyles = (theme: any) =>
  StyleSheet.create({ container: {
      flex: 1 };
    content: { padding: 16 },
    progressCard: {
      padding: 16,
      borderRadius: 12,
      marginBottom: 24,
      shadowColor: theme.colors.text,
      shadowOffset: { width: 0, height: 2 };
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2,
    },
    progressTitle: { fontSize: 16,
      fontWeight: '600',
      marginBottom: 12 },
    progressBar: { height: 8,
      borderRadius: 4,
      overflow: 'hidden',
      marginBottom: 8 },
    progressFill: { height: '100%',
      borderRadius: 4 },
    progressText: {
      fontSize: 14,
      textAlign: 'center'
    },
    section: { marginBottom: 24 },
    sectionTitle: {
      fontSize: 18),
      fontWeight: '600'),
      marginBottom: 12,
      paddingHorizontal: 4)
    },
  })
export default UnifiedProfileMenu,