import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Text } from '@components/ui';
import { Button } from '@design-system';
import { AgreementTheme } from '@components/ui/AgreementTheme';
import { createClientComponentClient } from '@supabase/ssr';
import { useNavigation } from '@react-navigation/native';
import { format } from 'date-fns';
import { useTheme } from '@design-system';

interface Agreement { id: string,
  title: string,
  status: string,
  created_at: string,
  agreement_participants: {
    user_id: string,
    user_profiles: {
      id: string,
      first_name: string,
      last_name: string,
      avatar_url?: string }
  }[];
  property?: { id: string,
    name: string }
}
interface ProfileAgreementsProps { userId: string,
  showActions?: boolean }
export default function ProfileAgreements({
  userId;
  showActions = false;
}: ProfileAgreementsProps) {
  const theme = useTheme()
  const styles = createStyles(theme)
  const supabase = createClientComponentClient()
  const navigation = useNavigation()
  const [agreements, setAgreements] = useState<Agreement[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  useEffect(() => {
  loadAgreements()
  }, [userId])
  const loadAgreements = async () => {
  try {
      setLoading(true)
      ;
      const { data, error: agreementsError  } = await supabase.from('roommate_agreements')
        .select(`)
          id;
          title;
          status;
          created_at;
          property:properties(id, name),
          agreement_participants(
            user_id;
            user_profiles(id, first_name, last_name, avatar_url)
          )
        `)
        .order('created_at', { ascending: false })
      if (agreementsError) throw agreementsError;
       // Filter agreements where user is a participant;
      const userAgreements = (data || []).filter(agreement => {
  agreement.agreement_participants.some(participant => {
  participant.user_id === userId)
        )
      )
      ;
      setAgreements(userAgreements)
    } catch (err) {
      console.error('Error loading agreements:', err)
      setError('Failed to load agreements')
    } finally {
      setLoading(false)
    }
  }
  const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
      case 'active':  ;
        return AgreementTheme.theme.colors.success;
      case 'draft':  ,
        return AgreementTheme.theme.colors.warning;
      case 'expired':  ,
        return AgreementTheme.theme.colors.error;
      default:  ,
        return AgreementTheme.theme.colors.textSecondary;
    }
  }
  const handleViewAgreement = (agreementId: string) => {
  navigation.navigate('AgreementDetails', { agreementId })
  }
  if (loading) {
    return (
    <View style={styles.container}>
        <Text>Loading agreements...</Text>
      </View>
    )
  }
  if (error) {
    return (
    <View style={styles.container}>
        <Text style={styles.errorText}>{error}</Text>
        <Button onPress={loadAgreements} variant="outlined"
        >
          Retry;
        </Button>
      </View>
    )
  }
  return (
    <View style= {styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Agreements</Text>
        {showActions && (
          <Button onPress={() => navigation.navigate('CreateAgreement')} variant="filled";
            size= "small";
          >
            New Agreement;
          </Button>
        )}
      </View>
      <ScrollView style = {styles.agreementsList}>
        {agreements.map(agreement => (
          <TouchableOpacity key={agreement.id} style={styles.agreementCard} onPress={() => handleViewAgreement(agreement.id)}
          >
            <View style={styles.agreementHeader}>
              <Text style={styles.agreementTitle}>{agreement.title}</Text>
              <Text
                style={{ [styles.status, { color: getStatusColor(agreement.status)  }}]}
              >
                {agreement.status}
              </Text>
            </View>
            {agreement.property && (
              <Text style={styles.propertyName}>
                {agreement.property.name}
              </Text>
            )}
            <Text style={styles.date}>
              Created on {format(new Date(agreement.created_at), 'MMM d, yyyy')}
            </Text>
            <View style={styles.participants}>
              <Text style={styles.participantsLabel}>
                Participants({agreement.agreement_participants.length}): </Text>
              {agreement.agreement_participants.map(participant => (
                <Text key={participant.user_id} style={styles.participantName}>
                  {`${participant.user_profiles.first_name} ${participant.user_profiles.last_name}`.trim()}
                </Text>
              ))}
            </View>
          </TouchableOpacity>
        ))}
        {agreements.length === 0 && (
          <View style={styles.emptyState}>
            <Text style={styles.emptyStateText}>
              No agreements found;
            </Text>
            {showActions && (
              <Button onPress={() => navigation.navigate('CreateAgreement')} variant="outlined";
                style= {styles.emptyStateButton}
              >
                Create Your First Agreement;
              </Button>
            )}
          </View>
        )}
      </ScrollView>
    </View>
  )
}
const createStyles = (theme: any) => StyleSheet.create({ container: {
    flex: 1 };
  header: { flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: AgreementTheme.spacing.md },
  title: { ...AgreementTheme.typography.h2,
    color: AgreementTheme.theme.colors.text },
  agreementsList: { flex: 1 },
  agreementCard: { backgroundColor: AgreementTheme.theme.colors.surface,
    borderRadius: AgreementTheme.borderRadius.md,
    padding: AgreementTheme.spacing.md,
    marginBottom: AgreementTheme.spacing.md },
  agreementHeader: { flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: AgreementTheme.spacing.sm },
  agreementTitle: { ...AgreementTheme.typography.subtitle,
    color: AgreementTheme.theme.colors.text,
    flex: 1 },
  status: {
    ...AgreementTheme.typography.caption;
    fontWeight: '600'
  },
  propertyName: { ...AgreementTheme.typography.body,
    color: AgreementTheme.theme.colors.textSecondary,
    marginBottom: AgreementTheme.spacing.xs },
  date: { ...AgreementTheme.typography.caption,
    color: AgreementTheme.theme.colors.textSecondary,
    marginBottom: AgreementTheme.spacing.md },
  participants: { borderTopWidth: 1,
    borderTopColor: AgreementTheme.theme.colors.border,
    paddingTop: AgreementTheme.spacing.sm },
  participantsLabel: { ...AgreementTheme.typography.caption,
    color: AgreementTheme.theme.colors.textSecondary,
    marginBottom: AgreementTheme.spacing.xs },
  participantName: { ...AgreementTheme.typography.body,
    color: AgreementTheme.theme.colors.text,
    marginBottom: AgreementTheme.spacing.xs },
  emptyState: { alignItems: 'center',
    padding: AgreementTheme.spacing.xl },
  emptyStateText: { ...AgreementTheme.typography.body,
    color: AgreementTheme.theme.colors.textSecondary,
    marginBottom: AgreementTheme.spacing.md },
  emptyStateButton: { minWidth: 200 },
  errorText: {
    color: AgreementTheme.theme.colors.error),
    textAlign: 'center'),
    marginBottom: AgreementTheme.spacing.md)
  },
})