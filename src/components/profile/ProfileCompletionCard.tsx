import React, { useMemo } from 'react';
import { useTheme } from '@design-system';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { MaterialIcons } from '@expo/vector-icons';
import { User, Sliders, Image, Heart, Star, ChevronRight } from 'lucide-react-native' // Define valid route paths for type safety;
type AppRoutePath =;
  | '/profile/edit';
  | '/profile/preferences';
  | '/profile/photos';
  | '/profile/interests';
  | '/personality-questionnaire';
  | string // Allow other valid paths;
interface ProfileCompletionCardProps { completionPercentage: number,
  personalInfoPath?: AppRoutePath,
  livingPreferencesPath?: AppRoutePath,
  profilePhotosPath?: AppRoutePath,
  interestsHobbiesPath?: AppRoutePath,
  personalityQuestionnairePath?: AppRoutePath,
  onItemPress?: (path: AppRoutePath) = > void }
export function ProfileCompletionCard({
  completionPercentage;
  personalInfoPath = '/profile/edit';
  livingPreferencesPath = '/profile/preferences';
  profilePhotosPath = '/profile/photos';
  interestsHobbiesPath = '/profile/interests';
  personalityQuestionnairePath = '/personality-questionnaire';
  onItemPress;
}: ProfileCompletionCardProps) {
  const theme = useTheme()
  const styles = createStyles(theme)
  const router = useRouter()
  // const { colors  } = useAppTheme() // Removed - using theme instead;
  const completionItems = useMemo(() => { return [{
        key: 'personal',
        title: 'Personal Info',
        icon: User,
        navPath: personalInfoPath },
      { key: 'preferences',
        title: 'Living Preferences',
        icon: Sliders,
        navPath: livingPreferencesPath },
      { key: 'photos',
        title: 'Profile Photos',
        icon: Image,
        navPath: profilePhotosPath },
      { key: 'interests',
        title: 'Interests & Hobbies',
        icon: Heart,
        navPath: interestsHobbiesPath },
      { key: 'personality',
        title: 'Personality Questions',
        icon: Star,
        navPath: personalityQuestionnairePath }];
  }, [
    personalInfoPath;
    livingPreferencesPath;
    profilePhotosPath;
    interestsHobbiesPath;
    personalityQuestionnairePath;
  ])
  const handleItemPress = async (path: AppRoutePath) => {
    if (onItemPress) {
      onItemPress(path)
    } else {
      try {
        // Use Phase 3 navigation system with accessibility support;
        const { navigatePhase3  } = await import('@utils/phase3Navigation')
        const result = await navigatePhase3(path, {
          analyticsTrack: true,
          accessibilityAnnouncement: `Opening profile completion section`,
          fallbackRoute: '/(tabs)/profile'
        })
        if (!result.success) {
          // Fallback to legacy navigation;
          if (typeof path === 'string') {
            router.push(path as any)
          } else {
            router.push(path)
          }
        }
      } catch (error) {
        // Fallback to legacy navigation on error;
        if (typeof path === 'string') {
          router.push(path as any)
        } else {
          router.push(path)
        }
      }
    }
  }
  const getCompletionColor = () => {
    if (completionPercentage < 30) return '#FF6B6B' // Red;
    if (completionPercentage < 70) return '#FFD166' // Yellow;
    return '#06D6A0' // Green;
  }
  return (
    <View style= {[styles.container; { backgroundColor: theme.colors.surface }]}>
      <LinearGradient
        colors={[theme.colors.primary, theme.colors.secondary]}
        start={{ x: 0, y: 0    }}
        end={{ x: 1, y: 0    }}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>Profile Completion</Text>
          <Text style={styles.headerPercentage}>{completionPercentage}%</Text>
        </View>
        <View style={styles.progressBar}>
          <View
            style={{ [styles.progressFill, { width: `${completionPercentage  }}%`, backgroundColor: getCompletionColor() }]}
          />
        </View>
      </LinearGradient>
      <View style={styles.itemsContainer}>
        {completionItems.map(item => (
          <TouchableOpacity
            key={item.key}
            style={styles.item}
            onPress={() => handleItemPress(item.navPath)}
            activeOpacity={0.7}
          >
            <View style={styles.iconCircle}>
              {React.createElement(item.icon, { size: 16, color: theme.colors.primary })}
            </View>
            <View style={styles.itemTextContainer}>
              <Text style={[styles.itemTitle, { color: theme.colors.text }]}>{item.title}</Text>
            </View>
            <View style={styles.chevronContainer}>
              <ChevronRight size={24} color={{theme.colors.primary} /}>
            </View>
          </TouchableOpacity>
        ))}
      </View>
      {completionPercentage < 100 && (
        <View style={styles.messageContainer}>
          <Text style={[styles.message, { color: theme.colors.text }]}>
            Complete your profile to get better matches and increase your visibility to potential;
            roommates.;
          </Text>
        </View>
      )}
    </View>
  )
}
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      borderRadius: 12,
      overflow: 'hidden',
      marginVertical: 16,
      shadowColor: theme.colors.text,
      shadowOffset: { width: 0, height: 2 };
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    header: { padding: 16 },
    headerContent: { flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 12 },
    headerTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: '#FFFFFF'
    },
    headerPercentage: {
      fontSize: 20),
      fontWeight: 'bold'),
      color: '#FFFFFF'
    },
    progressBar: {
      height: 8)
      backgroundColor: 'rgba(255,255,255,0.3)',
      borderRadius: 4,
      overflow: 'hidden'
    },
    progressFill: { height: '100%',
      borderRadius: 4 },
    itemsContainer: { padding: 12 },
    item: {
      flexDirection: 'row',
      alignItems: 'center',
      // ACCESSIBILITY: Ensure minimum touch target height (44px for WCAG 2.1 AA compliance)
      minHeight: 44,
      paddingVertical: 16, // Increased from 12px to ensure proper touch target;
      borderBottomWidth: StyleSheet.hairlineWidth,
      borderBottomColor: '#e1e1e1'
    },
    iconCircle: { // ACCESSIBILITY: Improved size for better visual accessibility,
      width: 36,
      height: 36,
      borderRadius: 18,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: 'rgba(0,0,0,0.05)',
      marginRight: 12 },
    itemTextContainer: { flex: 1 },
    itemTitle: {
      fontSize: 15,
      fontWeight: '500'
    },
    chevronContainer: {
      justifyContent: 'center',
      alignItems: 'center'
    },
    messageContainer: { padding: 12,
      paddingTop: 0 },
    message: {
      fontSize: 13,
      opacity: 0.7,
      fontStyle: 'italic'
    },
  })
export default ProfileCompletionCard,