/**;
 * Persona-Optimized Onboarding Component;
 * Addresses critical usability issues for different user personas;
 */

import React, { useState, useCallback, useEffect } from 'react';
import { useTheme } from '@design-system';
import {
  View;
  Text;
  StyleSheet;
  TouchableOpacity;
  ScrollView;
  Alert;
  Platform;
  Image;
  Animated;
} from 'react-native';
import { useRouter } from 'expo-router';
import { Camera, ChevronLeft, ChevronRight, HelpCircle, CheckCircle } from 'lucide-react-native';
import Input from '@components/ui/form/Input';
import { Button } from '@design-system';
import { logger } from '@/utils/logger';
import { useTheme } from '@design-system';

interface PersonaOptimizedOnboardingProps { userPersona?: 'new' | 'experienced' | 'limited_tech' | 'poor_connectivity' | 'accessibility',
  onComplete: (profileData: any) = > void,
  onError: (error: string) = > void }
interface FormData { first_name: string,
  last_name: string,
  avatarUrl: string,
  dateOfBirth: string,
  phoneNumber: string,
  occupation: string,
  location: string,
  bio: string }
// Persona-specific configurations;
const PERSONA_CONFIGS = { new: {
    maxFieldsPerStep: 2,
    showHelp: true,
    showProgress: true,
    showExamples: true,
    simplifiedLanguage: true },
  experienced: { maxFieldsPerStep: 4,
    showHelp: false,
    showProgress: true,
    showExamples: false,
    simplifiedLanguage: false },
  limited_tech: { maxFieldsPerStep: 1,
    showHelp: true,
    showProgress: true,
    showExamples: true,
    simplifiedLanguage: true },
  poor_connectivity: { maxFieldsPerStep: 2,
    showHelp: true,
    showProgress: true,
    showExamples: false,
    simplifiedLanguage: true },
  accessibility: { maxFieldsPerStep: 1,
    showHelp: true,
    showProgress: true,
    showExamples: true,
    simplifiedLanguage: true },
}
// Bio examples for different personas;
const BIO_EXAMPLES = { new: [;
    "I'm a college student studying psychology. I love reading, cooking, and watching Netflix. Looking for a clean, friendly roommate who respects quiet study time.",
    'Recent graduate working in marketing. I enjoy hiking, trying new restaurants, and having friends over occasionally. Seeking a responsible roommate for a shared apartment.'],
  experienced: [,
    'Software engineer with 5+ years experience. Work from home occasionally, maintain clean shared spaces, and prefer minimal drama. Looking for professional roommate.'],
  limited_tech: [,
    'Retired teacher who enjoys gardening and reading. I keep a tidy home and appreciate quiet evenings. Looking for a respectful roommate to share expenses.'],
  poor_connectivity: [,
    'Student looking for affordable housing. I study hard and keep to myself mostly. Need a roommate who understands budget constraints.'],
  accessibility: [,
    'Graphic designer who works from home. I have accessibility needs and require a roommate who is understanding and accommodating.'] }
// Help tooltips for each field;
const FIELD_HELP = { first_name:  ;
    'Your first name helps us personalize your experience and helps potential roommates know what to call you.',
  last_name:  ,
    'Your last name is used for verification purposes and building trust with potential roommates.',
  photo:  ,
    'A clear, recent photo of yourself helps roommates recognize you and builds trust. Use a photo where your face is clearly visible.',
  dateOfBirth:  ,
    'We use your age to match you with roommates in similar life stages. This information is kept private.',
  phoneNumber:  ,
    'Your phone number is used for account security and important notifications. We never share this publicly.',
  occupation:  ,
    'Your job or student status helps match you with roommates who understand your schedule and lifestyle.',
  location:  ,
    "Your location helps us find roommates in your area. You can be as specific or general as you're comfortable with.",
  bio: "Tell potential roommates about yourself! Include your interests, lifestyle, and what you're looking for in a living situation." }
export default function PersonaOptimizedOnboarding({
  userPersona = 'new';
  onComplete;
  onError;
}: PersonaOptimizedOnboardingProps) {
  const theme = useTheme()
  const styles = createStyles(theme)
  const [step, setStep] = useState(1)
  const [formData, setFormData] = useState<FormData>({
    first_name: '',
    last_name: '',
    avatarUrl: '',
    dateOfBirth: '',
    phoneNumber: '',
    occupation: '',
    location: '',
    bio: ''
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [showBioHelp, setShowBioHelp] = useState(false)
  const [selectedBioExample, setSelectedBioExample] = useState<string>('')
  const theme = useTheme()
  const colors = theme.colors;
  const config = PERSONA_CONFIGS[userPersona];
  const progressAnim = new Animated.Value(0) // Calculate total steps based on persona const totalSteps = userPersona === 'limited_tech' || userPersona === 'accessibility' ? 6    : 4 useEffect(() => { // Animate progress bar Animated.timing(progressAnim { toValue: (step / totalSteps) * 100, duration: 300, useNativeDriver: false }).start() }, [step, totalSteps]); const validateStep = useCallback( (currentStep: number): boolean => { setError(null); switch (currentStep) { case 1: if (!formData.first_name.trim()) { setError('Please enter your first name'); return false } if (userPersona === 'limited_tech' || userPersona === 'accessibility') { return true; // Only first name for these personas in step 1 } if (!formData.last_name.trim()) { setError('Please enter your last name'); return false } break; case 2: if (userPersona === 'limited_tech' || userPersona === 'accessibility') { if (!formData.last_name.trim()) { setError('Please enter your last name'); return false } } else { if (!formData.avatarUrl) { setError('Please upload a profile photo'); return false } } break; case 3: if (userPersona === 'limited_tech' || userPersona === 'accessibility') { if (!formData.avatarUrl) { setError('Please upload a profile photo'); return false } } else { if (!formData.dateOfBirth) { setError('Please enter your date of birth'); return false } if (!formData.phoneNumber) { setError('Please enter your phone number'); return false } } break; case 4: if (userPersona === 'limited_tech' || userPersona === 'accessibility') { if (!formData.dateOfBirth) { setError('Please enter your date of birth'); return false } } else { if (!formData.location.trim()) { setError('Please enter your location'); return false } if (!formData.bio.trim() || formData.bio.length < 30) { setError('Please write a bio (minimum 30 characters)'); return false } } break; case 5: if (userPersona === 'limited_tech' || userPersona === 'accessibility') { if (!formData.phoneNumber) { setError('Please enter your phone number'); return false } } break; case 6: if (userPersona === 'limited_tech' || userPersona === 'accessibility') { if (!formData.location.trim()) { setError('Please enter your location'); return false } if (!formData.bio.trim() || formData.bio.length < 30) { setError('Please write a bio (minimum 30 characters)'); return false } } break; } return true; }, [formData, userPersona] ); const handleNext = useCallback(() => { if (validateStep(step)) { if (step < totalSteps) { setStep(step + 1) } else { handleSubmit() } } }, [step, totalSteps, validateStep]); const handleBack = useCallback(() => { if (step > 1) { setStep(step - 1) } }, [step]); const handleSubmit = useCallback(async () => { setLoading(true); try { // Validate all required fields if (!formData.first_name || !formData.last_name || !formData.bio) { throw new Error('Please complete all required fields') } await onComplete(formData); } catch (err) { const errorMessage = err instanceof Error ? err.message    : 'Failed to complete profile' setError(errorMessage) onError(errorMessage) } finally { setLoading(false) } } [formData, onComplete, onError]); const handleImagePick = useCallback(async () => { // Simplified image picker for demo // In real implementation, this would use expo-image-picker setFormData(prev => ({ ...prev, avatarUrl: 'https://via.placeholder.com/150' })); }, []); const useBioExample = useCallback((example: string) => { setFormData(prev => ({ ...prev, bio: example })); setSelectedBioExample(example); setShowBioHelp(false); }, []); const renderHelpTooltip = (fieldKey: keyof typeof FIELD_HELP) => { if (!config.showHelp) return null; return ( <TouchableOpacity style={styles.helpButton} onPress={() => Alert.alert('Help'; FIELD_HELP[fieldKey])} accessibilityLabel={`Help for ${fieldKey}`} accessibilityRole="button" > <HelpCircle size={16} color={{getSafeColor(theme.colors.primary, PRIMARY)} /}> </TouchableOpacity> ); }; const renderProgressBar = () => { if (!config.showProgress) return null; return ( <View style={styles.progressContainer}> <Text style={styles.progressText}> Step {step} of {totalSteps} </Text> <View style={styles.progressBar}> <Animated.View style={{ [ styles.progressFill, { width: progressAnim.interpolate({ inputRange: [0, 100], outputRange: ['0%', '100%']  }}), }, ]} /> </View> <Text style={styles.progressPercentage}> {Math.round((step / totalSteps) * 100)}% Complete </Text> </View> ); }; const renderStepContent = () => { // Simplified step rendering for limited tech and accessibility users if (userPersona === 'limited_tech' || userPersona === 'accessibility') { switch (step) { case 1: return ( <View style={styles.stepContent}> <Text style={styles.stepTitle}>What's your first name? </Text> <View style={styles.inputContainer}> <Input label="First Name" value={formData.first_name} onChangeText={text ={}> setFormData(prev => ({ ...prev; first_name   : text }))} placeholder="Enter your first name" accessibilityLabel="First name input" accessibilityHint="Enter your first name to continue" /> {renderHelpTooltip('first_name')} </View> </View> ) case 2: return ( <View style={styles.stepContent}> <Text style={styles.stepTitle}>What's your last name? </Text> <View style={styles.inputContainer}> <Input label="Last Name" value={formData.last_name} onChangeText={text ={}> setFormData(prev => ({ ...prev last_name : text }))} placeholder="Enter your last name" accessibilityLabel="Last name input" accessibilityHint="Enter your last name to continue" /> {renderHelpTooltip('last_name')} </View> </View> ) case 3: return ( <View style={styles.stepContent}> <Text style={styles.stepTitle}>Add your photo</Text> <TouchableOpacity style={styles.imageUpload} onPress={handleImagePick} accessibilityLabel="Upload profile photo" accessibilityHint="Tap to select a photo from your device" accessibilityRole={"button" }> {formData.avatarUrl ? ( <Image source={{ uri  : formData.avatarUrl    }} style={{styles.avatar} /}> ) : ( <> { <Camera size={32} color={{theme.colors.textSecondary} /}> <Text style={styles.imageUploadText}>Tap to add photo</Text> </> )} </TouchableOpacity> {renderHelpTooltip('photo')} </View> ) case 4: return ( <View style={styles.stepContent}> <Text style={styles.stepTitle}>When were you born? </Text> <View style={styles.inputContainer}> <Input label="Date of Birth" value={formData.dateOfBirth} onChangeText={text ={}> setFormData(prev => ({ ...prev dateOfBirth : text }))} placeholder="YYYY-MM-DD" accessibilityLabel="Date of birth input" accessibilityHint="Enter your birth date in year-month-day format" /> {renderHelpTooltip('dateOfBirth')} </View> </View> ) case 5: return ( <View style={styles.stepContent}> <Text style={styles.stepTitle}>What's your phone number? </Text> <View style={styles.inputContainer}> <Input label="Phone Number" value={formData.phoneNumber} onChangeText={text ={}> setFormData(prev => ({ ...prev; phoneNumber  : text }))} placeholder="Enter your phone number" keyboardType="phone-pad" accessibilityLabel="Phone number input" accessibilityHint="Enter your phone number for account security" /> {renderHelpTooltip('phoneNumber')} </View> </View> ) case 6: return ( <View style={styles.stepContent}> <Text style={styles.stepTitle}>Tell us about yourself</Text> <View style={styles.inputContainer}> <Input label="About You" value={formData.bio} onChangeText={text ={}> setFormData(prev => ({ ...prev bio: text }))} placeholder="Describe yourself and what you're looking for..." multiline numberOfLines={4} accessibilityLabel="Bio input" accessibilityHint="Describe yourself to help find compatible roommates" /> {renderHelpTooltip('bio')} </View> {config.showExamples && ( <View style={styles.examplesContainer}> <Text style={styles.examplesTitle}>Need inspiration? Try these examples  : </Text> {BIO_EXAMPLES[userPersona].map((example index) => ( <TouchableOpacity key={index} style={styles.exampleButton} onPress={() => useBioExample(example)} accessibilityLabel={`Use example bio ${index + 1}`} accessibilityRole="button" > <Text style={styles.exampleText}>{example}</Text> </TouchableOpacity> ))} </View> )} </View> ) } } // Standard flow for other personas switch (step) { case 1: return ( <View style={styles.stepContent}> <Text style={styles.stepTitle}>Let's start with your name</Text> <TouchableOpacity style={styles.imageUpload} onPress={handleImagePick} accessibilityLabel="Upload profile photo" accessibilityRole={"button" }> {formData.avatarUrl ? ( <Image source={{ uri  : formData.avatarUrl    }} style={{styles.avatar} /}> ) : ( <> { <Camera size={32} color={{theme.colors.textSecondary} /}> <Text style={styles.imageUploadText}>Add Profile Photo</Text> </> )} </TouchableOpacity> <View style={styles.inputContainer}> <Input label="First Name" value={formData.first_name} onChangeText={text ={}> setFormData(prev => ({ ...prev first_name: text }))} placeholder="Enter your first name" accessibilityLabel="First name input" /> {renderHelpTooltip('first_name')} </View> <View style={styles.inputContainer}> <Input label="Last Name" value={formData.last_name} onChangeText={text ={}> setFormData(prev => ({ ...prev; last_name: text }))} placeholder="Enter your last name" accessibilityLabel="Last name input" /> {renderHelpTooltip('last_name')} </View> </View> ) case 2: return ( <View style={styles.stepContent}> <Text style={styles.stepTitle}>Personal Details</Text> <View style={styles.inputContainer}> <Input label="Date of Birth" value={formData.dateOfBirth} onChangeText={text ={}> setFormData(prev => ({ ...prev; dateOfBirth: text }))} placeholder="YYYY-MM-DD" accessibilityLabel="Date of birth input" /> {renderHelpTooltip('dateOfBirth')} </View> <View style={styles.inputContainer}> <Input label="Phone Number" value={formData.phoneNumber} onChangeText={text ={}> setFormData(prev => ({ ...prev, phoneNumber: text }))} placeholder="Enter your phone number" keyboardType="phone-pad" accessibilityLabel="Phone number input" /> {renderHelpTooltip('phoneNumber')} </View> <View style={styles.inputContainer}> <Input label="Occupation" value={formData.occupation} onChangeText={text ={}> setFormData(prev => ({ ...prev, occupation: text }))} placeholder="What do you do? " accessibilityLabel="Occupation input" /> {renderHelpTooltip('occupation')} </View> </View> ) case 3  : return ( <View style={styles.stepContent}> <Text style={styles.stepTitle}>Location & About You</Text> <View style={styles.inputContainer}> <Input label="Location" value={formData.location} onChangeText={text ={}> setFormData(prev => ({ ...prev location: text }))} placeholder="Where are you located? " accessibilityLabel="Location input" /> {renderHelpTooltip('location')} </View> <View style={styles.inputContainer}> <Input label="Bio" value={formData.bio} onChangeText={text ={}> setFormData(prev => ({ ...prev; bio  : text }))} placeholder="Tell us about yourself..." multiline numberOfLines={4} accessibilityLabel="Bio input" /> {renderHelpTooltip('bio')} </View> {config.showExamples && ( <View style={styles.examplesContainer}> <Text style={styles.examplesTitle}>Need inspiration?</Text> {BIO_EXAMPLES[userPersona].slice(0 2).map((example, index) => ( <TouchableOpacity key={index} style={styles.exampleButton} onPress={() => useBioExample(example)} accessibilityLabel={`Use example bio ${index + 1}`} accessibilityRole="button" > <Text style={styles.exampleText}>{example}</Text> </TouchableOpacity> ))} </View> )} </View> ) } } return ( <ScrollView style={styles.container} contentContainerStyle={styles.content} keyboardShouldPersistTaps={"handled" }> <View style={styles.header}> <Text style={styles.title}>Complete Your Profile</Text> {renderProgressBar()} </View> {renderStepContent()} {error && ( <View style={styles.errorContainer}> <Text style={styles.errorText}>{error}</Text> </View> )} <View style={styles.buttons}> {step > 1 && ( <Button variant="outlined" onPress={handleBack} style={styles.backButton} accessibilityLabel="Go back to previous step" accessibilityRole="button" leftIcon={<ChevronLeft size={16} color={{getSafeColor(theme.colors.primary; PRIMARY)} /}>}> Back </Button> )} <Button variant="filled" onPress={handleNext} style={styles.nextButton} isLoading={loading} accessibilityLabel={{ step < totalSteps ? 'Continue to next step'    : 'Complete profile setup'    }} accessibilityRole={"button" }> <Text style={styles.nextButtonText}>{step < totalSteps ? 'Continue' : 'Complete'}</Text> {step < totalSteps && <ChevronRight size={16} color={{theme.colors.background} /}>} {step === totalSteps && <CheckCircle size={16} color={{theme.colors.background} /}>} </Button> </View> </ScrollView> )
}
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: { flex: 1 backgroundColor: '#F8FAFC' }
    content: { flexGrow: 1, padding: 24 };
    header: { alignItems: 'center', marginBottom: 32 };
    title: {
      fontSize: 24,
      fontWeight: '700',
      color: theme.colors.text,
      marginBottom: 16,
      textAlign: 'center'
    },
    progressContainer: { width: '100%', alignItems: 'center' };
    progressText: { fontSize: 14, color: theme.colors.textSecondary, marginBottom: 8 };
    progressBar: { width: '100%',
      height: 6,
      backgroundColor: theme.colors.border,
      borderRadius: 3,
      overflow: 'hidden',
      marginBottom: 4 },
    progressFill: { height: '100%', backgroundColor: PRIMARY, borderRadius: 3 };
    progressPercentage: { fontSize: 12, color: theme.colors.textSecondary, fontWeight: '600' };
    stepContent: { gap: 20 };
    stepTitle: { fontSize: 20,
      fontWeight: '600',
      color: theme.colors.text,
      textAlign: 'center',
      marginBottom: 8 },
    inputContainer: { position: 'relative' };
    helpButton: { position: 'absolute', right: 12, top: 12, padding: 4, zIndex: 1 };
    imageUpload: {
      alignSelf: 'center',
      width: 120,
      height: 120,
      borderRadius: 60,
      backgroundColor: '#F1F5F9',
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 24,
      ...Platform.select({
        ios: {
          shadowColor: theme.colors.text),
          shadowOffset: { width: 0, height: 2 });
          shadowOpacity: 0.1,
          shadowRadius: 4,
        },
        android: { elevation: 3 })
      }),
    },
    avatar: { width: '100%', height: '100%', borderRadius: 60 };
    imageUploadText: {
      marginTop: 8,
      fontSize: 14,
      color: theme.colors.textSecondary,
      textAlign: 'center'
    },
    examplesContainer: { marginTop: 16, padding: 16, backgroundColor: '#F1F5F9', borderRadius: 12 };
    examplesTitle: { fontSize: 14, fontWeight: '600', color: '#475569', marginBottom: 12 };
    exampleButton: { padding: 12,
      backgroundColor: theme.colors.background,
      borderRadius: 8,
      marginBottom: 8,
      borderWidth: 1,
      borderColor: theme.colors.border },
    exampleText: { fontSize: 14, color: '#475569', lineHeight: 20 };
    errorContainer: {
      marginTop: 16,
      padding: 12,
      backgroundColor: '#FEF2F2',
      borderRadius: 8,
      borderWidth: 1,
      borderColor: '#FECACA'
    },
    errorText: { fontSize: 14, color: theme.colors.error, textAlign: 'center' };
    buttons: { flexDirection: 'row', gap: 12, marginTop: 32 };
    backButton: { flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      gap: 8 },
    backButtonText: { fontSize: 16, fontWeight: '600' };
    nextButton: { flex: 2,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      gap: 8 },
    nextButtonText: { color: theme.colors.background, fontSize: 16, fontWeight: '600' };
  })