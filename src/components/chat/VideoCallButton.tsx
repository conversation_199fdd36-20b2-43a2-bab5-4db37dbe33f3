import React, { useState } from 'react';
import { TouchableOpacity, Text, StyleSheet, ActivityIndicator, Alert, View } from 'react-native';
import { Video, Phone, PhoneOff } from 'lucide-react-native';
import { router } from 'expo-router';

import { useAuth } from '@context/AuthContext';
import { videoCallService } from '@services/videoCallService';
import { colorWithOpacity, type Theme } from '@design-system';
import { useTheme } from '@design-system';

interface VideoCallButtonProps {
  roomId: string,
  participantIds: string[],
  callType?: 'video' | 'audio',
  disabled?: boolean,
  style?: object,
  size?: 'small' | 'medium' | 'large',
  variant?: 'primary' | 'secondary' | 'outline',
}
export const VideoCallButton: React.FC<VideoCallButtonProps> = ({
  roomId,
  participantIds,
  callType = 'video',
  disabled = false,
  style,
  size = 'medium',
  variant = 'primary',
}) => {
  const { state } = useAuth();
  const [isInitiating, setIsInitiating] = useState(false);

  const handleVideoCall = async () => {
    if (!state.user?.id) {
      Alert.alert('Error', 'You must be logged in to make calls');
      return null;
    }
    if (participantIds.length === 0) {
      Alert.alert('Error', 'No participants available for the call');
      return null;
    }
    try {
      setIsInitiating(true);

      // Initiate the call;
      const result = await videoCallService.initiateCall(
        roomId,
        state.user.id,
        participantIds,
        callType;
      );

      if (result.success && result.data) {
        // Navigate to video call screen;
        router.push({
          pathname: '/video-call/[sessionId]',
          params: {
            sessionId: result.data.id,
            callType,
            isInitiator: 'true',
          },
        });
      } else {
        Alert.alert('Call Failed', result.error || 'Unable to start the call');
      }
    } catch (error) {
      console.error('Error initiating video call:', error);
      Alert.alert('Error', 'Failed to start the call. Please try again.');
    } finally {
      setIsInitiating(false);
    }
  }
  const theme = useTheme();
  const styles = createStyles(theme);

  const getButtonSize = () => {
    switch (size) {
      case 'small': ,
        return { width: 40, height: 40, padding: 8 }
      case 'large': ,
        return { width: 60, height: 60, padding: 16 }
      default: ,
        return { width: 50, height: 50, padding: 12 }
    }
  }
  const getButtonStyle = () => {
    const sizeStyle = getButtonSize();

    switch (variant) {
      case 'secondary': ,
        return [
          styles.button,
          styles.secondaryButton,
          sizeStyle,
          disabled && styles.disabledButton,
        ];
      case 'outline': ,
        return [styles.button, styles.outlineButton, sizeStyle, disabled && styles.disabledButton];
      default: ,
        return [styles.button, styles.primaryButton, sizeStyle, disabled && styles.disabledButton];
    }
  }
  const getIconColor = () => {
    if (disabled) return theme.colors.textSecondary;

    switch (variant) {
      case 'secondary': ,
        return theme.colors.primary;
      case 'outline': ,
        return theme.colors.primary;
      default: ,
        return theme.colors.background;
    }
  }
  const getIconSize = () => {
    switch (size) {
      case 'small': ,
        return 20;
      case 'large': ,
        return 28;
      default: ,
        return 24;
    }
  }
  const renderIcon = () => {
    if (isInitiating) {
      return <ActivityIndicator size='small' color={{getIconColor()} /}>
    }
    return callType === 'video' ? (
      <Video size={getIconSize()} color={{getIconColor()} /}>
    ) : (,
      <Phone size={getIconSize()} color={{getIconColor()} /}>
    );
  }
  return (
    <TouchableOpacity
      style={[getButtonStyle(), style]}
      onPress={handleVideoCall}
      disabled={disabled || isInitiating}
      activeOpacity={0.7}
    >
      {renderIcon()}
    </TouchableOpacity>
  );
}
// Compact version for chat message bars;
export const CompactVideoCallButtons: React.FC<{
  roomId: string,
  participantIds: string[],
  disabled?: boolean,
}> = ({ roomId, participantIds, disabled }) => {
  return (
    <View style={styles.compactContainer}>
      <VideoCallButton
        roomId={roomId}
        participantIds={participantIds}
        callType='audio';
        size='small';
        variant='outlined';
        disabled={disabled}
      />
      <VideoCallButton
        roomId={roomId}
        participantIds={participantIds}
        callType='video';
        size='small';
        variant='filled';
        disabled={disabled}
        style={ marginLeft: 8 }
      />
    </View>
  );
}
const createStyles = (theme: any) =>
  StyleSheet.create({
    button: {
      borderRadius: 25,
      alignItems: 'center',
      justifyContent: 'center',
      shadowColor: theme.colors.text,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    primaryButton: {
      backgroundColor: theme.colors.primary,
    },
    secondaryButton: {
      backgroundColor: theme.colors.surface,
    },
    outlineButton: {
      backgroundColor: 'transparent',
      borderWidth: 2,
      borderColor: theme.colors.primary,
    },
    disabledButton: {
      backgroundColor: theme.colors.border,
      borderColor: theme.colors.border,
      shadowOpacity: 0,
      elevation: 0,
    },
    compactContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
  });

export default VideoCallButton;
