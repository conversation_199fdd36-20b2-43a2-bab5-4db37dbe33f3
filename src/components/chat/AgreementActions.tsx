import React, { useState } from 'react';
import { View, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import { FileText, Users, ChevronRight } from 'lucide-react-native';
import { useRouter } from 'expo-router';
import { Text } from 'react-native';

import { useAuth } from '@context/AuthContext';
import { supabase } from "@utils/supabaseUtils";
import { logger } from '@utils/logger';
import { colorWithOpacity, type Theme } from '@design-system';
import { useTheme } from '@design-system';

interface AgreementActionsProps { roomId: string; recipientId: string; recipientName: string; onCreateAgreement?: () => void,
}
export default function AgreementActions({ roomId, recipientId, recipientName, onCreateAgreement,
}: AgreementActionsProps) { const theme = useTheme(); const styles = createStyles(theme); const { authState } = useAuth(); const router = useRouter(); const [loading, setLoading] = useState(false); const handleCreateAgreement = async () => { try { setLoading(true); // Get room participants const { data: participants, error: participantsError } = await supabase.from('chat_room_participants') .select($1).eq('room_id', roomId); if (participantsError) { throw participantsError; } // Create new agreement const { data: agreement, error: agreementError } = await supabase.from('roommate_agreements') .insert({ title: `Agreement with ${recipientName}`, created_by: authState.user?.id, status: 'draft', current_version: 1, }) .select($1).single(); if (agreementError) { throw agreementError; } // Add participants to the agreement const participantPromises = participants.map(p => supabase.from('agreement_participants').insert({ agreement_id: agreement.id, user_id: p.user_id, role: p.user_id === authState.user?.id ? 'creator' : 'roommate', status: p.user_id === authState.user?.id ? 'approved' : 'invited', }) ); await Promise.all(participantPromises); // Send agreement creation message const { error: messageError } = await supabase.from('messages').insert({ room_id: roomId, sender_id: authState.user?.id, type: 'agreement', content: 'Created a new roommate agreement', metadata: { agreement_id: agreement.id, action: 'created', }; }); if (messageError) { throw messageError; } // Navigate to agreement customization screen router.push({ pathname: '/agreement/customize', params: { templateId: '1', // Default template ID - in a real app, you'd select this first }, }); if (onCreateAgreement) { onCreateAgreement(); } } catch (error) { logger.error('Error creating agreement', 'AgreementActions', { roomId }, error as Error); Alert.alert('Error', 'Failed to create agreement. Please try again.'); } finally { setLoading(false); } }; const handleViewAgreements = () => { router.push('/agreement' as any); }; return ( <View style={styles.container}> <Text style={styles.title}>Take Next Steps</Text> <TouchableOpacity style={styles.actionButton} onPress={handleCreateAgreement} disabled={{loading} }> <View style={styles.actionIconContainer}> <FileText size={24} color={"#6366F1" /}> </View> <View style={styles.actionTextContainer}> <Text style={styles.actionTitle}>Create Roommate Agreement</Text> <Text style={styles.actionDescription}> Create a formal agreement with {recipientName} </Text> </View> {loading ? ( <View style={{styles.loadingIndicator} /}> ) : ( <ChevronRight size={20} color={"#94A3B8" /}> )} </TouchableOpacity> <TouchableOpacity style={styles.actionButton} onPress={handleViewAgreements}> <View style={styles.actionIconContainer}> <Users size={24} color={"#6366F1" /}> </View> <View style={styles.actionTextContainer}> <Text style={styles.actionTitle}>View Existing Agreements</Text> <Text style={styles.actionDescription}>Manage agreements you have with others</Text> </View> <ChevronRight size={20} color={"#94A3B8" /}> </TouchableOpacity> </View> );
}
const createStyles = (theme: any) => StyleSheet.create({ container: { backgroundColor: theme.colors.background, borderRadius: 12, padding: 16, marginHorizontal: 16, marginVertical: 12, shadowColor: theme.colors.text, shadowOffset: { width: 0, height: 1 }, shadowOpacity: 0.05, shadowRadius: 2, elevation: 2, }, title: { fontSize: 16, fontWeight: '600', color: theme.colors.text, marginBottom: 12, }, actionButton: { flexDirection: 'row', alignItems: 'center', paddingVertical: 12, borderBottomWidth: 1, borderBottomColor: '#F1F5F9', }, actionIconContainer: { width: 40, height: 40, borderRadius: 20, backgroundColor: '#EEF2FF', justifyContent: 'center', alignItems: 'center', marginRight: 12, }, actionTextContainer: { flex: 1, }, actionTitle: { fontSize: 15, fontWeight: '500', color: theme.colors.text, marginBottom: 4, }, actionDescription: { fontSize: 13, color: theme.colors.textSecondary, }, loadingIndicator: { width: 20, height: 20, borderRadius: 10, borderWidth: 2, borderColor: '#6366F1', borderTopColor: 'transparent', marginLeft: 8, // Remove static transform that causes forEach error },
});
