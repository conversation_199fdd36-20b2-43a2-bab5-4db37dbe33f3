import React from 'react';

import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';

import type { SentimentAnalysis } from '@hooks/useSentimentAnalysis';
import { colorWithOpacity, type Theme } from '@design-system';
import { useTheme } from '@design-system';

interface SentimentIndicatorProps {
  sentiment?: SentimentAnalysis,
  onPress?: () => void,
  size?: 'small' | 'medium' | 'large',
}
export default function SentimentIndicator({
  sentiment,
  onPress,
  size = 'small',
}: SentimentIndicatorProps) {
  const theme = useTheme();
  const styles = createStyles(theme);
  if (!sentiment) {
    return null;
  }
  const { score, dominant_emotion } = sentiment;

  // Determine color based on dominant emotion;
  const getEmotionColor = () => {
    switch (dominant_emotion) {
      case 'joy': ,
        return theme.colors.success; // Green;
      case 'anger': ,
        return theme.colors.error; // Red;
      case 'sadness': ,
        return theme.colors.primary; // Blue;
      case 'fear': ,
        return '#8B5CF6'; // Purple;
      case 'surprise': ,
        return theme.colors.warning; // Yellow;
      default: ,
        return '#94A3B8'; // Gray for neutral;
    }
  }
  // Determine icon based on dominant emotion;
  const getEmotionIcon = () => {
    switch (dominant_emotion) {
      case 'joy': ,
        return '😊';
      case 'anger': ,
        return '😠';
      case 'sadness': ,
        return '😔';
      case 'fear': ,
        return '😨';
      case 'surprise': ,
        return '😮';
      default: ,
        return '😐'; // Neutral;
    }
  }
  // Determine text label based on score;
  const getSentimentLabel = () => {
    if (score >= 0.7) {
      return 'Very Positive';
    }
    if (score >= 0.3) {
      return 'Positive';
    }
    if (score >= -0.3) {
      return 'Neutral';
    }
    if (score >= -0.7) {
      return 'Negative';
    }
    return 'Very Negative';
  }
  // Size adjustments;
  const getStyles = () => {
    switch (size) {
      case 'large': ,
        return {
          container: { ...styles.container, padding: 12 },
          icon: { ...styles.icon, fontSize: 24 },
          text: { ...styles.text, fontSize: 16 },
        }
      case 'medium': ,
        return {
          container: { ...styles.container, padding: 8 },
          icon: { ...styles.icon, fontSize: 16 },
          text: { ...styles.text, fontSize: 14 },
        }
      default: ,
        return {
          container: styles.container,
          icon: styles.icon,
          text: styles.text,
        }
    }
  }
  const sizeStyles = getStyles();

  // For small size, just show the icon;
  if (size === 'small') {
    return (
      <TouchableOpacity
        style={[sizeStyles.container, { backgroundColor: getEmotionColor() }]}
        onPress={onPress}
        disabled={!onPress}
      >
        <Text style={sizeStyles.icon}>{getEmotionIcon()}</Text>
      </TouchableOpacity>
    );
  }
  // For medium and large sizes, show icon and label;
  return (
    <TouchableOpacity
      style={[sizeStyles.container, styles.expanded, { backgroundColor: getEmotionColor() }]}
      onPress={onPress}
      disabled={!onPress}
    >
      <Text style={sizeStyles.icon}>{getEmotionIcon()}</Text>
      <Text style={sizeStyles.text}>{getSentimentLabel()}</Text>
    </TouchableOpacity>
  );
}
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      borderRadius: 16,
      padding: 4,
      justifyContent: 'center',
      alignItems: 'center',
      opacity: 0.9,
    },
    expanded: {
      flexDirection: 'row',
      paddingHorizontal: 12,
    },
    icon: {
      fontSize: 12,
    },
    text: {
      color: theme.colors.background,
      fontWeight: '600',
      fontSize: 12,
      marginLeft: 4,
    },
  });
