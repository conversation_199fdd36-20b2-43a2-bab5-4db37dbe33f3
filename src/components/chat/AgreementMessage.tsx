import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { useTheme } from '@design-system';
import { AgreementTheme } from '@components/ui/AgreementTheme';
import { useNavigation } from '@react-navigation/native';
import { format } from 'date-fns';
import { Text } from '@components/ui';
import { Button } from '@design-system';
interface AgreementMessageProps {
  type: 'created' | 'updated' | 'signed' | 'review_requested' | 'reviewed',
  agreementId: string,
  agreementTitle: string,
  timestamp: string,
  senderName: string,
  onAction?: () => void,
}
export default function AgreementMessage({
  type,
  agreementId,
  agreementTitle,
  timestamp,
  senderName,
  onAction,
}: AgreementMessageProps) {
  const theme = useTheme();
  const styles = createStyles(theme);
  const navigation = useNavigation();

  const getMessageContent = () => {
    switch (type) {
      case 'created': ,
        return {
          title: 'New Agreement Created',
          message: `${senderName} created a new agreement: "${agreementTitle}"`,
          action: 'View Agreement',
        }
      case 'updated': ,
        return {
          title: 'Agreement Updated',
          message: `${senderName} made changes to "${agreementTitle}"`,
          action: 'View Changes',
        }
      case 'signed': ,
        return {
          title: 'Agreement Signed',
          message: `${senderName} signed the agreement: "${agreementTitle}"`,
          action: 'View Signatures',
        }
      case 'review_requested': ,
        return {
          title: 'Review Requested',
          message: `${senderName} requested your review on "${agreementTitle}"`,
          action: 'Review Now',
        }
      case 'reviewed': ,
        return {
          title: 'Agreement Reviewed',
          message: `${senderName} reviewed "${agreementTitle}"`,
          action: 'View Review',
        }
      default: ,
        return {
          title: 'Agreement Update',
          message: `Update to "${agreementTitle}"`,
          action: 'View Details',
        }
    }
  }
  const handleViewAgreement = () => {
    navigation.navigate('AgreementDetails', { agreementId });
  }
  const content = getMessageContent();

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>{content.title}</Text>
        <Text style={styles.timestamp}>
          {format(new Date(timestamp), "MMM d, yyyy 'at' h:mm a")}
        </Text>
      </View>
      <Text style={styles.message}>{content.message}</Text>
      <View style={styles.actions}>
        <Button
          onPress={handleViewAgreement}
          variant='outlined';
          size='small';
          style={styles.viewButton}
        >
          View Agreement;
        </Button>
        {onAction && (
          <Button onPress={onAction} variant='filled' size={'small'}>
            {content.action}
          </Button>
        )}
      </View>
    </View>
  );
}
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      backgroundColor: AgreementTheme?.theme?.colors?.background?.secondary || theme.colors.surface,
      borderRadius: AgreementTheme?.borderRadius?.md || theme.borderRadius?.md || 8,
      padding: AgreementTheme?.spacing?.md || theme.spacing?.md || 16,
      marginVertical: AgreementTheme?.spacing?.sm || theme.spacing?.sm || 8,
      borderLeftWidth: 4,
      borderLeftColor: AgreementTheme?.theme?.colors?.primary?.main || theme.colors.primary,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: AgreementTheme?.spacing?.sm || theme.spacing?.sm || 8,
    },
    title: {
      ...AgreementTheme?.typography?.subtitle,
      color: AgreementTheme?.theme?.colors?.text?.primary || theme.colors.text,
      fontWeight: '600',
      fontSize: 16,
    },
    timestamp: {
      ...AgreementTheme?.typography?.caption,
      color: AgreementTheme?.theme?.colors?.text?.secondary || theme.colors.textSecondary,
      fontSize: 12,
    },
    message: {
      ...AgreementTheme?.typography?.body,
      color: AgreementTheme?.theme?.colors?.text?.primary || theme.colors.text,
      marginBottom: AgreementTheme?.spacing?.md || theme.spacing?.md || 16,
      fontSize: 14,
    },
    actions: {
      flexDirection: 'row',
      justifyContent: 'flex-end',
      gap: AgreementTheme?.spacing?.sm || theme.spacing?.sm || 8,
    },
    viewButton: {
      backgroundColor: AgreementTheme?.theme?.colors?.background?.tertiary || theme.colors.surface,
    },
  });
