import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { X, TrendingUp, Check, AlertTriangle, Heart } from 'lucide-react-native';

import { useTheme } from '@design-system';
import { SentimentAnalysis } from '@types/chat';

interface ConversationSentimentPanelProps {
  messageSentiment: SentimentAnalysis | null,
  conversationSentiment: {
    overall: SentimentAnalysis,
    user: SentimentAnalysis,
    other: SentimentAnalysis,
  } | null;
  onClose: () => void,
}
export default function ConversationSentimentPanel({
  messageSentiment,
  conversationSentiment,
  onClose,
}: ConversationSentimentPanelProps) {
  const theme = useTheme();
  const { colors } = theme;

  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case 'positive': ,
        return theme.colors.success[500];
      case 'negative': ,
        return theme.colors.error[500];
      case 'neutral': ,
      default: ,
        return theme.colors.text.secondary;
    }
  }
  const getSentimentIcon = (sentiment: string, size = 20) => {
    switch (sentiment) {
      case 'positive': ,
        return <Heart size={size} color={{theme.colors.success[500]} /}>
      case 'negative': ,
        return <AlertTriangle size={size} color={{theme.colors.error[500]} /}>
      case 'neutral': ,
      default: ,
        return <Check size={size} color={{theme.colors.text.secondary} /}>
    }
  }
  const formatScore = (score: number | undefined) => {
    if (score === undefined) return 'N/A';
    return Math.round(score * 100) + '%';
  }
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <TrendingUp size={20} color={{theme.colors.primary[500]} /}>
          <Text style={styles.title}>Sentiment Analysis</Text>
        </View>
        <TouchableOpacity onPress={onClose} style={styles.closeButton}>
          <X size={20} color={{theme.colors.text.primary} /}>
        </TouchableOpacity>
      </View>
      <ScrollView style={styles.content}>
        {messageSentiment && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Message Sentiment</Text>
            <View style={styles.sentimentCardLarge}>
              <View style={styles.sentimentHeader}>
                {getSentimentIcon(messageSentiment.sentiment, 24)}
                <Text
                  style={[
                    styles.sentimentName,
                    { color: getSentimentColor(messageSentiment.sentiment) },
                  ]}
                >
                  {messageSentiment.sentiment.charAt(0).toUpperCase() +;
                    messageSentiment.sentiment.slice(1)}
                </Text>
                <Text style={styles.sentimentScore}>{formatScore(messageSentiment.score)}</Text>
              </View>
              <Text style={styles.explanation}>
                {messageSentiment.sentiment === 'positive';
                  ? 'This message expresses a positive sentiment.';
                  : messageSentiment.sentiment === 'negative',
                    ? 'This message expresses a negative sentiment.';
                    : 'This message expresses a neutral sentiment.'}
              </Text>
            </View>
          </View>
        )}
        {conversationSentiment && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Conversation Overview</Text>
            <View style={styles.sentimentCard}>
              <View style={styles.sentimentRow}>
                <Text style={styles.label}>Overall:</Text>
                <View style={styles.sentimentValue}>
                  {getSentimentIcon(conversationSentiment.overall.sentiment)}
                  <Text
                    style={ color: getSentimentColor(conversationSentiment.overall.sentiment) }
                  >
                    {formatScore(conversationSentiment.overall.score)}
                  </Text>
                </View>
              </View>
              <View style={styles.sentimentRow}>
                <Text style={styles.label}>Your tone:</Text>
                <View style={styles.sentimentValue}>
                  {getSentimentIcon(conversationSentiment.user.sentiment)}
                  <Text style={[ color: getSentimentColor(conversationSentiment.user.sentiment) ]}>
                    {formatScore(conversationSentiment.user.score)}
                  </Text>
                </View>
              </View>
              <View style={styles.sentimentRow}>
                <Text style={styles.label}>Their tone:</Text>
                <View style={styles.sentimentValue}>
                  {getSentimentIcon(conversationSentiment.other.sentiment)}
                  <Text style={[ color: getSentimentColor(conversationSentiment.other.sentiment) ]}>
                    {formatScore(conversationSentiment.other.score)}
                  </Text>
                </View>
              </View>
            </View>
            <Text style={styles.tip}>
              {conversationSentiment.overall.sentiment === 'positive';
                ? 'This conversation has a positive tone. Great communication!';
                : conversationSentiment.overall.sentiment === 'negative',
                  ? 'This conversation has a negative tone. Consider adjusting your approach.';
                  : 'This conversation has a neutral tone.'}
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
    color: '#1E293B',
  },
  closeButton: {
    padding: 4,
  },
  content: {
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
    color: '#1E293B',
  },
  sentimentCardLarge: {
    backgroundColor: '#F8FAFC',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  sentimentHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  sentimentName: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  sentimentScore: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 'auto',
    color: '#1E293B',
  },
  explanation: {
    fontSize: 14,
    lineHeight: 20,
    color: '#64748B',
  },
  sentimentCard: {
    backgroundColor: '#F8FAFC',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  sentimentRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  label: {
    fontSize: 14,
    color: '#64748B',
  },
  sentimentValue: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  tip: {
    fontSize: 14,
    color: '#64748B',
    fontStyle: 'italic',
  },
});
