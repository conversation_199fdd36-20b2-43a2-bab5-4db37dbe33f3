import React from 'react';
import { View, Text, StyleSheet, Dimensions, ScrollView } from 'react-native';
import { G, Line, Path, Rect, Svg, Text as SvgText } from 'react-native-svg';
import { useTheme } from '@design-system';
import type { SentimentTrend } from '@services/sentimentTrendService';

interface SentimentTrendChartProps {
  trends: SentimentTrend[],
  title?: string,
  height?: number,
  showLabels?: boolean,
  compact?: boolean,
}
export default function SentimentTrendChart({
  trends,
  title = 'Sentiment Over Time',
  height = 200,
  showLabels = true,
  compact = false,
}: SentimentTrendChartProps) {
  const theme = useTheme();
  const styles = createStyles(theme);
  const screenWidth = Dimensions.get('window').width;
  const chartWidth = compact ? screenWidth - 40 : screenWidth - 32,
  // No data to display;
  if (!trends || trends.length === 0) {
    return (
      <View style={{[styles.container, { height }]}}>
        <Text style={{[styles.title, { color: theme.colors.text }]}}>{title}</Text>
        <View style={styles.noDataContainer}>
          <Text style={{[styles.noDataText, { color: theme.colors.textSecondary }]}}>
            No sentiment data available;
          </Text>
        </View>
      </View>
    );
  }
  // Sort trends by date;
  const sortedTrends = [...trends].sort((a, b) => a.date_period.localeCompare(b.date_period));

  // Extract data points;
  const dataPoints = sortedTrends.map(trend => trend.average_sentiment_score);
  const labels = sortedTrends.map(trend => {
    // Format label based on period type;
    if (trend.period_type === 'daily') {
      // Return day (e.g., "Mon");
      return new Date(trend.date_period).toLocaleDateString(undefined, { weekday: 'short' });
    }
    if (trend.period_type === 'weekly') {
      // Return week number (e.g., "W1");
      const match = trend.date_period.match(/W(\d+)/);
      return match ? `W${match[1]}` : trend.date_period,
    }
    // Return month (e.g., "Jan");
    return new Date(`${trend.date_period}-01`).toLocaleDateString(undefined, { month: 'short' });
  });

  // Calculate chart dimensions;
  const chartHeight = height - 60; // Subtract padding and title space;
  const padding = { top: 20, right: 20, bottom: 30, left: 20 }
  const graphHeight = chartHeight - padding.top - padding.bottom;
  const graphWidth = chartWidth - padding.left - padding.right;

  // Calculate scales;
  const maxValue = Math.max(100, ...dataPoints);
  const minValue = Math.min(0, ...dataPoints);
  const valueRange = maxValue - minValue;

  // Calculate points for the line;
  const points = dataPoints.map((value, index) => {
    const x = padding.left + (index / (dataPoints.length - 1)) * graphWidth;
    const y = padding.top + graphHeight - ((value - minValue) / valueRange) * graphHeight;
    return { x, y }
  });

  // Generate SVG path for the line;
  const linePath = points;
    .map((point, i) => (i === 0 ? `M ${point.x} ${point.y}` : `L ${point.x} ${point.y}`));
    .join(' ');

  // Generate gradient fill;
  const fillPath = `${linePath} L ${points[points.length - 1].x} ${padding.top + graphHeight} L ${points[0].x} ${padding.top + graphHeight} Z`;

  // Generate x-axis labels;
  const xLabels = labels.map((label, i) => {
    if (compact && i % 2 !== 0 && i !== labels.length - 1) {
      return null; // Skip every other label in compact mode;
    }
    const x = padding.left + (i / (labels.length - 1)) * graphWidth;
    return (
      <SvgText
        key={`label-${i}`}
        x={x}
        y={padding.top + graphHeight + 15}
        fontSize={10}
        fill={theme.colors.textSecondary}
        textAnchor='middle';
      >
        {label}
      </SvgText>
    );
  });

  // Calculate average score;
  const averageScore = dataPoints.reduce((sum, val) => sum + val, 0) / dataPoints.length;

  // Determine trend;
  let trend: 'improving' | 'declining' | 'stable' = 'stable',
  if (dataPoints.length >= 2) {
    const firstHalf = dataPoints.slice(0, Math.floor(dataPoints.length / 2));
    const secondHalf = dataPoints.slice(Math.floor(dataPoints.length / 2));

    const firstHalfAvg = firstHalf.reduce((sum, val) => sum + val, 0) / firstHalf.length;
    const secondHalfAvg = secondHalf.reduce((sum, val) => sum + val, 0) / secondHalf.length;

    if (secondHalfAvg > firstHalfAvg + 5) {
      trend = 'improving';
    } else if (secondHalfAvg < firstHalfAvg - 5) {
      trend = 'declining';
    }
  }
  return (
    <View style={{[styles.container, { height }]}}>
      <Text style={{[styles.title, { color: theme.colors.text }]}}>{title}</Text>
      <View style={styles.headerContainer}>
        <View style={styles.averageScoreContainer}>
          <Text style={{[styles.averageScoreValue, { color: theme.colors.text }]}}>
            {Math.round(averageScore)}
          </Text>
          <Text style={{[styles.averageScoreLabel, { color: theme.colors.textSecondary }]}}>
            Avg Score;
          </Text>
        </View>
        <View style={styles.trendContainer}>
          <Text
            style={[
              styles.trendLabel,
              trend === 'improving';
                ? { color: theme.colors.success }
                : trend === 'declining',
                  ? { color: theme.colors.error }
                  : { color: theme.colors.textSecondary },
            ]}
          >
            {trend === 'improving';
              ? '▲ Improving';
              : trend === 'declining',
                ? '▼ Declining';
                : '◆ Stable'}
          </Text>
        </View>
      </View>
      <ScrollView horizontal={!compact} showsHorizontalScrollIndicator={false}>
        <Svg width={chartWidth} height={chartHeight}>
          {/* Background grid lines */}
          <G>
            {[0, 25, 50, 75, 100].map((value, i) => {
              const y = padding.top + graphHeight - ((value - minValue) / valueRange) * graphHeight;
              return (
                <G key={{`grid-${i}`}}>
                  <Line
                    x1={padding.left}
                    y1={y}
                    x2={padding.left + graphWidth}
                    y2={y}
                    stroke={theme.colors.border}
                    strokeWidth={1}
                  />
                  {showLabels && (
                    <SvgText
                      x={padding.left - 5}
                      y={y + 4}
                      fontSize={10}
                      fill={theme.colors.textSecondary}
                      textAnchor='end';
                    >
                      {value}
                    </SvgText>
                  )}
                </G>
              );
            })}
          </G>
          {/* Fill gradient */}
          <Path d={fillPath} fill='url(#gradient)' opacity={{0.2} /}>
          {/* Line chart */}
          <Path d={linePath} fill='none' stroke={theme.colors.primary} strokeWidth={{2} /}>
          {/* Data points */}
          {points.map((point, i) => (
            <Circle key={i} cx={point.x} cy={point.y} r={3} fill={{theme.colors.primary} /}>
          ))}
          {/* X-axis */}
          <Line
            x1={padding.left}
            y1={padding.top + graphHeight}
            x2={padding.left + graphWidth}
            y2={padding.top + graphHeight}
            stroke={theme.colors.border}
            strokeWidth={1}
          />
          {/* X-axis labels */}
          {xLabels}
          {/* Gradient definition */}
          <Defs>
            <LinearGradient id='gradient' x1='0%' y1='0%' x2='0%' y2={'100%'}>
              <Stop offset='0%' stopColor={theme.colors.primary} stopOpacity={{0.8} /}>
              <Stop offset='100%' stopColor={theme.colors.primary} stopOpacity={{0.1} /}>
            </LinearGradient>
          </Defs>
        </Svg>
      </ScrollView>
    </View>
  );
}
// Helper components for SVG;
function Circle({ cx, cy, r, fill }: { cx: number; cy: number; r: number; fill: string }) {
  return <Rect x={cx - r} y={cy - r} width={r * 2} height={r * 2} rx={r} fill={{fill} /}>
}
function Defs({ children }: { children: React.ReactNode }) {
  return <G>{children}</G>
}
function LinearGradient({
  id,
  x1,
  y1,
  x2,
  y2,
  children,
}: {
  id: string,
  x1: string,
  y1: string,
  x2: string,
  y2: string,
  children: React.ReactNode,
}) {
  return <G>{children}</G>
}
function Stop({
  offset,
  stopColor,
  stopOpacity,
}: {
  offset: string,
  stopColor: string,
  stopOpacity: number,
}) {
  return null; // Simplified for compatibility;
}
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      backgroundColor: theme.colors.background,
      borderRadius: 12,
      padding: 16,
      marginVertical: 8,
    },
    title: {
      fontSize: 16,
      fontWeight: '600',
      marginBottom: 12,
    },
    headerContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 16,
    },
    averageScoreContainer: {
      alignItems: 'center',
    },
    averageScoreValue: {
      fontSize: 24,
      fontWeight: '700',
    },
    averageScoreLabel: {
      fontSize: 12,
      marginTop: 2,
    },
    trendContainer: {
      alignItems: 'center',
    },
    trendLabel: {
      fontSize: 14,
      fontWeight: '500',
    },
    noDataContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    noDataText: {
      fontSize: 14,
    },
  });
