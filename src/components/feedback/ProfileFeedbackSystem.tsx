import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  TextInput,
  ScrollView,
  Alert,
  Animated,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useTheme } from '@design-system';
import { logger } from '@utils/logger';
import { profileAnalytics } from '@services/ProfileAnalytics';

const { width, height } = Dimensions.get('window');

interface FeedbackQuestion {
  id: string,
  type: 'rating' | 'choice' | 'text' | 'comparison',
  question: string,
  description?: string,
  required: boolean,
  options?: string[],
  maxRating?: number,
  comparisonContext?: {
    before: string,
    after: string,
  }
}
interface FeedbackResponse {
  questionId: string,
  value: string | number,
  timestamp: Date,
}
interface FeedbackSession {
  id: string,
  trigger: 'onboarding_complete' | 'feature_usage' | 'manual_request' | 'scheduled',
  responses: FeedbackResponse[],
  startTime: Date,
  endTime?: Date,
  completed: boolean,
}
const feedbackQuestions: FeedbackQuestion[] = [,
  {
    id: 'overall_satisfaction',
    type: 'rating',
    question: 'How satisfied are you with the new profile organization?',
    description: 'Rate your overall experience with the simplified 3-category structure',
    required: true,
    maxRating: 5,
  },
  {
    id: 'navigation_ease',
    type: 'rating',
    question: 'How easy is it to find what you need?',
    description: 'Compare to your previous experience navigating profile features',
    required: true,
    maxRating: 5,
  },
  {
    id: 'category_clarity',
    type: 'choice',
    question: 'Which category organization works best for you?',
    required: true,
    options: [,
      'The 3 main categories are perfect',
      'I need more categories for organization',
      'I prefer fewer categories',
      'I miss the old detailed structure',
    ],
  },
  {
    id: 'feature_discovery',
    type: 'choice',
    question: 'How did you discover advanced features?',
    required: false,
    options: [,
      'Through the onboarding process',
      'Found the toggle in profile',
      'Noticed it in settings',
      "Haven't discovered them yet",
      "Don't need advanced features",
    ],
  },
  {
    id: 'time_to_task',
    type: 'comparison',
    question: 'How long does it take to complete common tasks now?',
    comparisonContext: {
      before: 'Previous complex structure',
      after: 'New simplified structure',
    },
    required: true,
    options: ['Much faster', 'Slightly faster', 'About the same', 'Slightly slower', 'Much slower'],
  },
  {
    id: 'missing_features',
    type: 'text',
    question: 'What features or organization do you miss from the old structure?',
    description: "Help us understand if we've hidden something important",
    required: false,
  },
  {
    id: 'improvement_suggestions',
    type: 'text',
    question: 'How can we improve the profile organization further?',
    description: 'Any suggestions for making it even better?',
    required: false,
  },
];

interface ProfileFeedbackSystemProps {
  trigger: 'onboarding_complete' | 'feature_usage' | 'manual_request' | 'scheduled',
  visible: boolean,
  onClose: () => void,
  onComplete: (responses: FeedbackResponse[]) => void,
}
export function ProfileFeedbackSystem({
  trigger,
  visible,
  onClose,
  onComplete,
}: ProfileFeedbackSystemProps) {
  const theme = useTheme();
  const { colors, isDark } = theme;
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [responses, setResponses] = useState<FeedbackResponse[]>([]);
  const [currentResponse, setCurrentResponse] = useState<string | number>('');
  const [fadeAnim] = useState(new Animated.Value(0));
  const [slideAnim] = useState(new Animated.Value(50));

  useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible, fadeAnim, slideAnim]);

  const currentQuestion = feedbackQuestions[currentQuestionIndex];
  const isLastQuestion = currentQuestionIndex === feedbackQuestions.length - 1;
  const progress = ((currentQuestionIndex + 1) / feedbackQuestions.length) * 100;

  const handleNext = () => {
    if (currentQuestion.required && !currentResponse) {
      Alert.alert('Required Question', 'Please provide an answer before continuing.');
      return null;
    }
    // Save current response;
    if (currentResponse) {
      const response: FeedbackResponse = {
        questionId: currentQuestion.id,
        value: currentResponse,
        timestamp: new Date(),
      }
      setResponses(prev => [...prev, response]);
    }
    if (isLastQuestion) {
      handleComplete();
    } else {
      setCurrentQuestionIndex(prev => prev + 1);
      setCurrentResponse('');

      // Animate to next question;
      Animated.sequence([
        Animated.timing(slideAnim, {
          toValue: -50,
          duration: 150,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 50,
          duration: 0,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }
  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);

      // Restore previous response;
      const previousResponse = responses[currentQuestionIndex - 1];
      if (previousResponse) {
        setCurrentResponse(previousResponse.value);
      }
    }
  }
  const handleComplete = () => {
    const allResponses = [...responses];
    if (currentResponse) {
      allResponses.push({
        questionId: currentQuestion.id,
        value: currentResponse,
        timestamp: new Date(),
      });
    }
    // Track feedback completion;
    profileAnalytics.trackEvent('profile_feedback_completed', {
      trigger,
      question_count: allResponses.length,
      completion_time: new Date().toISOString(),
    });

    logger.info('Profile feedback completed', 'ProfileFeedbackSystem', {
      trigger,
      responseCount: allResponses.length,
    });

    onComplete(allResponses);
  }
  const handleSkip = () => {
    Alert.alert(
      'Skip Feedback',
      'Your feedback helps us improve the app. Are you sure you want to skip?',
      [
        { text: 'Continue Feedback', style: 'cancel' },
        {
          text: 'Skip',
          style: 'destructive',
          onPress: () => {
            profileAnalytics.trackEvent('profile_feedback_skipped', {
              trigger,
              questions_answered: responses.length,
            });
            onClose();
          },
        },
      ];
    );
  }
  const renderRatingQuestion = () => (
    <View style={styles.ratingContainer}>
      <View style={styles.starsContainer}>
        {Array.from({ length: currentQuestion.maxRating || 5 }, (_, index) => (
          <TouchableOpacity
            key={index}
            style={styles.starButton}
            onPress={() => setCurrentResponse(index + 1)}
          >
            <Ionicons
              name={index < Number(currentResponse) ? 'star' : 'star-outline'}
              size={40}
              color={index < Number(currentResponse) ? theme.colors.accent : theme.colors.border}
            />
          </TouchableOpacity>
        ))}
      </View>
      <Text style={{[styles.ratingLabel, { color: theme.colors.textSecondary }]}}>
        {currentResponse ? `${currentResponse} of ${currentQuestion.maxRating}` : 'Tap to rate'}
      </Text>
    </View>
  );

  const renderChoiceQuestion = () => (
    <View style={styles.choiceContainer}>
      {currentQuestion.options?.map((option, index) => (
        <TouchableOpacity
          key={index}
          style={[
            styles.choiceOption,
            {
              backgroundColor: ,
                currentResponse === option ? theme.colors.primary : theme.colors.surface,
              borderColor: currentResponse === option ? theme.colors.primary : theme.colors.border,
            },
          ]}
          onPress={() => setCurrentResponse(option)}
        >
          <Text
            style={[
              styles.choiceText,
              {
                color: ,
                  currentResponse === option ? theme.colors.primaryContrast : theme.colors.text,
              },
            ]}
          >
            {option}
          </Text>
          {currentResponse === option && (
            <Ionicons
              name='checkmark-circle';
              size={20}
              color={theme.colors.primaryContrast}
              style={styles.checkIcon}
            />
          )}
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderTextQuestion = () => (
    <View style={styles.textContainer}>
      <TextInput
        style={[
          styles.textInput,
          {
            backgroundColor: theme.colors.surface,
            borderColor: theme.colors.border,
            color: theme.colors.text,
          },
        ]}
        placeholder='Type your response here...';
        placeholderTextColor={theme.colors.textSecondary}
        value={currentResponse as string}
        onChangeText={setCurrentResponse}
        multiline;
        numberOfLines={4}
        textAlignVertical='top';
      />
    </View>
  );

  const renderComparisonQuestion = () => (
    <View style={styles.comparisonContainer}>
      <View style={styles.comparisonHeader}>
        <View style={styles.comparisonItem}>
          <Text style={{[styles.comparisonLabel, { color: theme.colors.textSecondary }]}}>
            {currentQuestion.comparisonContext?.before}
          </Text>
        </View>
        <Ionicons name='arrow-forward' size={20} color={{theme.colors.textSecondary} /}>
        <View style={styles.comparisonItem}>
          <Text style={{[styles.comparisonLabel, { color: theme.colors.textSecondary }]}}>
            {currentQuestion.comparisonContext?.after}
          </Text>
        </View>
      </View>
      {renderChoiceQuestion()}
    </View>
  );

  const renderCurrentQuestion = () => {
    switch (currentQuestion.type) {
      case 'rating': ,
        return renderRatingQuestion();
      case 'choice': ,
        return renderChoiceQuestion();
      case 'text': ,
        return renderTextQuestion();
      case 'comparison': ,
        return renderComparisonQuestion();
      default: ,
        return null;
    }
  }
  const styles = StyleSheet.create({
    overlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    container: {
      width: width * 0.9,
      maxHeight: height * 0.8,
      backgroundColor: theme.colors.background,
      borderRadius: 20,
      overflow: 'hidden',
    },
    header: {
      padding: 20,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    title: {
      fontSize: 24,
      fontWeight: '700',
      color: theme.colors.text,
      marginBottom: 8,
    },
    subtitle: {
      fontSize: 16,
      color: theme.colors.textSecondary,
      marginBottom: 12,
    },
    progressBar: {
      height: 4,
      backgroundColor: theme.colors.border,
      borderRadius: 2,
      overflow: 'hidden',
    },
    progressFill: {
      height: '100%',
      backgroundColor: theme.colors.primary,
      borderRadius: 2,
    },
    progressText: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      marginTop: 8,
    },
    content: {
      flex: 1,
      padding: 20,
    },
    questionContainer: {
      marginBottom: 20,
    },
    questionText: {
      fontSize: 20,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 8,
      lineHeight: 28,
    },
    questionDescription: {
      fontSize: 16,
      color: theme.colors.textSecondary,
      lineHeight: 22,
      marginBottom: 20,
    },
    ratingContainer: {
      alignItems: 'center',
    },
    starsContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      marginBottom: 16,
    },
    starButton: {
      marginHorizontal: 8,
      padding: 8,
    },
    ratingLabel: {
      fontSize: 14,
      textAlign: 'center',
    },
    choiceContainer: {
      gap: 12,
    },
    choiceOption: {
      padding: 16,
      borderRadius: 12,
      borderWidth: 2,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    choiceText: {
      fontSize: 16,
      flex: 1,
    },
    checkIcon: {
      marginLeft: 8,
    },
    textContainer: {
      marginBottom: 20,
    },
    textInput: {
      borderWidth: 1,
      borderRadius: 12,
      padding: 16,
      fontSize: 16,
      minHeight: 120,
    },
    comparisonContainer: {
      marginBottom: 20,
    },
    comparisonHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginBottom: 20,
      paddingHorizontal: 20,
    },
    comparisonItem: {
      flex: 1,
      alignItems: 'center',
    },
    comparisonLabel: {
      fontSize: 14,
      textAlign: 'center',
      fontWeight: '500',
    },
    footer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: 20,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
    },
    button: {
      paddingHorizontal: 20,
      paddingVertical: 12,
      borderRadius: 10,
      minWidth: 80,
      alignItems: 'center',
    },
    primaryButton: {
      backgroundColor: theme.colors.primary,
    },
    secondaryButton: {
      backgroundColor: theme.colors.surface,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    buttonText: {
      fontSize: 16,
      fontWeight: '600',
    },
    primaryButtonText: {
      color: theme.colors.primaryContrast,
    },
    secondaryButtonText: {
      color: theme.colors.text,
    },
    skipButton: {
      paddingHorizontal: 16,
      paddingVertical: 8,
    },
    skipButtonText: {
      fontSize: 14,
      color: theme.colors.textSecondary,
    },
  });

  if (!visible) return null;

  return (
    <Modal transparent visible={visible} animationType='none' onRequestClose={handleSkip}>
      <BlurView intensity={isDark ? 20 : 40} style={styles.overlay}>
        <Animated.View;
          style={[
            styles.container,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          <View style={styles.header}>
            <Text style={styles.title}>Profile Feedback</Text>
            <Text style={styles.subtitle}>Help us improve your profile experience</Text>
            <View style={styles.progressBar}>
              <View style={{[styles.progressFill, { width: `${progress}%` }]} /}>
            </View>
            <Text style={styles.progressText}>
              Question {currentQuestionIndex + 1} of {feedbackQuestions.length}
            </Text>
          </View>
          <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
            <Animated.View;
              style={[styles.questionContainer, { transform: [{ translateY: slideAnim }] }]}
            >
              <Text style={styles.questionText}>{currentQuestion.question}</Text>
              {currentQuestion.description && (
                <Text style={styles.questionDescription}>{currentQuestion.description}</Text>
              )}
              {renderCurrentQuestion()}
            </Animated.View>
          </ScrollView>
          <View style={styles.footer}>
            <View>
              {currentQuestionIndex > 0 && (
                <TouchableOpacity
                  style={[styles.button, styles.secondaryButton]}
                  onPress={handlePrevious}
                >
                  <Text style={[styles.buttonText, styles.secondaryButtonText]}>Previous</Text>
                </TouchableOpacity>
              )}
            </View>
            <TouchableOpacity style={styles.skipButton} onPress={handleSkip}>
              <Text style={styles.skipButtonText}>Skip</Text>
            </TouchableOpacity>
            <TouchableOpacity style={[styles.button, styles.primaryButton]} onPress={handleNext}>
              <Text style={[styles.buttonText, styles.primaryButtonText]}>
                {isLastQuestion ? 'Complete' : 'Next'}
              </Text>
            </TouchableOpacity>
          </View>
        </Animated.View>
      </BlurView>
    </Modal>
  );
}
// Hook for managing feedback system;
export function useProfileFeedback() {
  const [shouldShowFeedback, setShouldShowFeedback] = useState(false);
  const [feedbackTrigger, setFeedbackTrigger] = useState<;
    'onboarding_complete' | 'feature_usage' | 'manual_request' | 'scheduled';
  >('manual_request');
  const [sessions, setSessions] = useState<FeedbackSession[]>([]);

  useEffect(() => {
    checkFeedbackSchedule();
  }, []);

  const checkFeedbackSchedule = async () => {
    try {
      // Check if user should see scheduled feedback;
      const lastFeedback = await AsyncStorage.getItem('last_profile_feedback');
      const feedbackFrequency = 7 * 24 * 60 * 60 * 1000; // 7 days;

      if (!lastFeedback || Date.now() - parseInt(lastFeedback) > feedbackFrequency) {
        // Show feedback after random delay (1-3 days of usage);
        const randomDelay = Math.random() * 3 * 24 * 60 * 60 * 1000;
        setTimeout(() => {
          triggerFeedback('scheduled');
        }, randomDelay);
      }
    } catch (error) {
      logger.error('Failed to check feedback schedule', 'useProfileFeedback', error);
    }
  }
  const triggerFeedback = (trigger: typeof feedbackTrigger) => {
    setFeedbackTrigger(trigger);
    setShouldShowFeedback(true);

    profileAnalytics.trackEvent('profile_feedback_triggered', {
      trigger,
      timestamp: new Date().toISOString(),
    });
  }
  const handleFeedbackComplete = async (responses: FeedbackResponse[]) => {
    try {
      const session: FeedbackSession = {
        id: `feedback_${Date.now()}`,
        trigger: feedbackTrigger,
        responses,
        startTime: new Date(),
        endTime: new Date(),
        completed: true,
      }
      setSessions(prev => [...prev, session]);
      setShouldShowFeedback(false);

      // Save feedback to storage;
      await AsyncStorage.setItem('last_profile_feedback', Date.now().toString());

      // Send feedback to analytics;
      await submitFeedbackToAnalytics(session);

      logger.info('Profile feedback session completed', 'useProfileFeedback', {
        sessionId: session.id,
        responseCount: responses.length,
      });
    } catch (error) {
      logger.error('Failed to complete feedback session', 'useProfileFeedback', error);
    }
  }
  const submitFeedbackToAnalytics = async (session: FeedbackSession) => {
    try {
      // Process responses for analytics;
      const processedResponses = session.responses.reduce(
        (acc, response) => {
          acc[response.questionId] = response.value;
          return acc;
        },
        {} as Record<string, any>
      );

      await profileAnalytics.trackEvent('profile_feedback_responses', {
        session_id: session.id,
        trigger: session.trigger,
        responses: processedResponses,
        completion_time: session.endTime?.toISOString(),
      });
    } catch (error) {
      logger.error('Failed to submit feedback to analytics', 'useProfileFeedback', error);
    }
  }
  const closeFeedback = () => {
    setShouldShowFeedback(false);

    profileAnalytics.trackEvent('profile_feedback_closed', {
      trigger: feedbackTrigger,
      timestamp: new Date().toISOString(),
    });
  }
  return {
    shouldShowFeedback,
    feedbackTrigger,
    sessions,
    triggerFeedback,
    handleFeedbackComplete,
    closeFeedback,
  }
}