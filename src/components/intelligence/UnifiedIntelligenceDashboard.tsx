/**;
 * Unified Intelligence Dashboard - WeRoomies Platform;
 *;
 * Comprehensive dashboard component displaying real-time AI insights,
 * analytics, and predictive data from all platform systems.;
 */;

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  Dimensions,
  Alert,
  StyleSheet,
} from 'react-native';
import { useTheme } from '@design-system';
import { logger } from '@utils/logger';
import UnifiedAIIntelligenceHub from '@services/intelligence/UnifiedAIIntelligenceHub';
import RealTimeAnalyticsDashboard, {
  DashboardMetrics,
} from '@services/intelligence/RealTimeAnalyticsDashboard';
import PredictiveAnalyticsEngine from '@services/intelligence/PredictiveAnalyticsEngine';

interface DashboardState {
  metrics: DashboardMetrics | null,
  platformIntelligence: any | null,
  predictiveInsights: any | null,
  alerts: any[],
  isLoading: boolean,
  lastUpdate: Date | null,
  error: string | null,
}
interface MetricCardProps {
  title: string,
  value: string | number,
  subtitle?: string,
  trend?: 'up' | 'down' | 'stable',
  color?: string,
  onPress?: () => void,
}
const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  subtitle,
  trend,
  color,
  onPress,
}) => {
  const theme = useTheme();

  const getTrendIcon = () => {
    switch (trend) {
      case 'up': ,
        return '↗️';
      case 'down': ,
        return '↘️';
      case 'stable': ,
        return '→';
      default: ,
        return '';
    }
  }
  const getTrendColor = () => {
    switch (trend) {
      case 'up': ,
        return theme.colors.success;
      case 'down': ,
        return theme.colors.error;
      case 'stable': ,
        return theme.colors.textSecondary;
      default: ,
        return theme.colors.text;
    }
  }
  return (
    <TouchableOpacity
      style={[
        styles.metricCard,
        {
          backgroundColor: theme.colors.surface,
          borderColor: color || theme.colors.border,
        },
      ]}
      onPress={onPress}
      disabled={!onPress}
    >
      <Text style={{[styles.metricTitle, { color: theme.colors.textSecondary }]}}>{title}</Text>
      <Text style={{[styles.metricValue, { color: color || theme.colors.text }]}}>{value}</Text>
      {subtitle && (
        <View style={styles.metricSubtitle}>
          <Text style={{[styles.subtitleText, { color: getTrendColor() }]}}>
            {getTrendIcon()} {subtitle}
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );
}
interface AlertBannerProps {
  alerts: any[],
  onAlertPress: (alert: any) => void,
}
const AlertBanner: React.FC<AlertBannerProps> = ({ alerts, onAlertPress }) => {
  const theme = useTheme();

  if (alerts.length === 0) return null;

  const criticalAlerts = alerts.filter(alert => alert.severity === 'critical');
  const highPriorityAlerts = alerts.filter(alert => alert.severity === 'error');

  const getAlertColor = (severity: string) => {
    switch (severity) {
      case 'critical': ,
        return theme.colors.error;
      case 'error': ,
        return '#FF6B35';
      case 'warning': ,
        return '#FFB800';
      default: ,
        return theme.colors.primary;
    }
  }
  return (
    <View style={{[styles.alertBanner, { backgroundColor: theme.colors.surface }]}}>
      <Text style={{[styles.alertTitle, { color: theme.colors.text }]}}>
        Active Alerts ({alerts.length});
      </Text>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        {alerts.slice(0, 5).map((alert, index) => (
          <TouchableOpacity
            key={alert.id || index}
            style={[
              styles.alertItem,
              {
                backgroundColor: getAlertColor(alert.severity),
                marginRight: theme.spacing.sm,
              },
            ]}
            onPress={() => onAlertPress(alert)}
          >
            <Text style={{[styles.alertText, { color: theme.colors.background }]}}>
              {alert.title}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
}
interface ChartSectionProps {
  title: string,
  data: any,
  type: 'line' | 'bar' | 'pie',
}
const ChartSection: React.FC<ChartSectionProps> = ({ title, data, type }) => {
  const theme = useTheme();

  // Simplified chart representation for React Native;
  const renderSimpleChart = () => {
    if (!data || typeof data !== 'object') {
      return (
        <Text style={{[styles.chartPlaceholder, { color: theme.colors.textSecondary }]}}>
          No data available;
        </Text>
      );
    }
    if (Array.isArray(data)) {
      return (
        <View style={styles.chartData}>
          {data.slice(0, 5).map((item, index) => (
            <View key={index} style={styles.chartItem}>
              <Text style={{[styles.chartLabel, { color: theme.colors.text }]}}>
                {item.label || item.name || `Item ${index + 1}`}
              </Text>
              <Text style={{[styles.chartValue, { color: theme.colors.primary }]}}>
                {item.value || item.count || item.percentage || 'N/A'}
              </Text>
            </View>
          ))}
        </View>
      );
    }
    return (
      <View style={styles.chartData}>
        {Object.entries(data);
          .slice(0, 5);
          .map(([key, value]) => (
            <View key={key} style={styles.chartItem}>
              <Text style={{[styles.chartLabel, { color: theme.colors.text }]}}>
                {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </Text>
              <Text style={{[styles.chartValue, { color: theme.colors.primary }]}}>
                {typeof value === 'number' ? value.toFixed(2) : String(value)}
              </Text>
            </View>
          ))}
      </View>
    );
  }
  return (
    <View style={{[styles.chartSection, { backgroundColor: theme.colors.surface }]}}>
      <Text style={{[styles.chartTitle, { color: theme.colors.text }]}}>{title}</Text>
      {renderSimpleChart()}
    </View>
  );
}
const UnifiedIntelligenceDashboard: React.FC = () => {
  const theme = useTheme();
  const [state, setState] = useState<DashboardState>({
    metrics: null,
    platformIntelligence: null,
    predictiveInsights: null,
    alerts: [],
    isLoading: true,
    lastUpdate: null,
    error: null,
  });

  const intelligenceHub = UnifiedAIIntelligenceHub.getInstance();
  const analyticsDashboard = RealTimeAnalyticsDashboard.getInstance();
  const predictiveEngine = PredictiveAnalyticsEngine.getInstance();

  const loadDashboardData = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      // Load data from all intelligence systems in parallel;
      const [metrics, platformMetrics, alerts, modelMetrics] = await Promise.allSettled([
        analyticsDashboard.getDashboardMetrics(),
        intelligenceHub.getPlatformIntelligenceMetrics(),
        analyticsDashboard.getActiveAlerts(),
        predictiveEngine.getModelMetrics(),
      ]);

      // Extract successful results;
      const dashboardMetrics = metrics.status === 'fulfilled' ? metrics.value : null,
      const platformData = platformMetrics.status === 'fulfilled' ? platformMetrics.value : null,
      const activeAlerts = alerts.status === 'fulfilled' ? alerts.value : [],
      const predictiveData = modelMetrics.status === 'fulfilled' ? modelMetrics.value : null,
      setState(prev => ({
        ...prev,
        metrics: dashboardMetrics,
        platformIntelligence: platformData,
        predictiveInsights: predictiveData,
        alerts: activeAlerts,
        isLoading: false,
        lastUpdate: new Date(),
        error: null,
      }));

      logger.info('Dashboard data loaded successfully', 'UnifiedIntelligenceDashboard', {
        metricsLoaded: !!dashboardMetrics,
        alertsCount: activeAlerts.length,
        modelsCount: predictiveData?.length || 0,
      });
    } catch (error) {
      logger.error('Failed to load dashboard data', 'UnifiedIntelligenceDashboard', { error });
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Failed to load dashboard data. Please try again.',
      }));
    }
  }, [analyticsDashboard, intelligenceHub, predictiveEngine]);

  const handleRefresh = useCallback(() => {
    loadDashboardData();
  }, [loadDashboardData]);

  const handleAlertPress = useCallback(
    (alert: any) => {
      Alert.alert(alert.title, alert.message, [
        { text: 'Dismiss', style: 'cancel' },
        {
          text: 'Acknowledge',
          onPress: async () => {
            try {
              await analyticsDashboard.acknowledgeAlert(alert.id, 'dashboard_user');
              handleRefresh();
            } catch (error) {
              Alert.alert('Error', 'Failed to acknowledge alert');
            }
          },
        },
      ]);
    },
    [analyticsDashboard, handleRefresh];
  );

  const handleMetricPress = useCallback((metricType: string) => {
    // Navigate to detailed metric view;
    logger.info('Metric pressed', 'UnifiedIntelligenceDashboard', { metricType });
  }, []);

  useEffect(() => {
    loadDashboardData();

    // Set up real-time updates;
    const updateInterval = setInterval(
      () => {
        if (!state.isLoading) {
          loadDashboardData();
        }
      },
      2 * 60 * 1000;
    ); // Update every 2 minutes;

    return () => clearInterval(updateInterval);
  }, [loadDashboardData, state.isLoading]);

  const renderPlatformHealth = () => {
    if (!state.metrics?.platformHealth) return null;

    const health = state.metrics.platformHealth;
    const healthColor =;
      health.overallScore >= 90;
        ? theme.colors.success;
        : health.overallScore >= 70,
          ? '#FFB800';
          : theme.colors.error,
    return (
      <View style={styles.section}>
        <Text style={{[styles.sectionTitle, { color: theme.colors.text }]}}>Platform Health</Text>
        <View style={styles.metricsGrid}>
          <MetricCard
            title='Overall Score';
            value={`${health.overallScore}%`}
            subtitle={health.overallScore >= 90 ? 'Excellent' : 'Good'}
            trend={health.overallScore >= 90 ? 'up' : 'stable'}
            color={healthColor}
            onPress={() => handleMetricPress('platform_health')}
          />
          <MetricCard
            title='Systems Online';
            value={`${health.systemsOnline}/${health.totalSystems}`}
            subtitle='All systems operational';
            trend='stable';
            color={theme.colors.success}
            onPress={() => handleMetricPress('systems_status')}
          />
          <MetricCard
            title='Performance Index';
            value={`${health.performanceIndex}%`}
            subtitle='Response time optimal';
            trend='up';
            color={theme.colors.primary}
            onPress={() => handleMetricPress('performance')}
          />
          <MetricCard
            title='Critical Alerts';
            value={health.criticalAlerts}
            subtitle={health.criticalAlerts === 0 ? 'All clear' : 'Needs attention'}
            trend={health.criticalAlerts === 0 ? 'stable' : 'down'}
            color={health.criticalAlerts === 0 ? theme.colors.success : theme.colors.error}
            onPress={() => handleMetricPress('alerts')}
          />
        </View>
      </View>
    );
  }
  const renderUserEngagement = () => {
    if (!state.metrics?.userEngagement) return null;

    const engagement = state.metrics.userEngagement;

    return (
      <View style={styles.section}>
        <Text style={{[styles.sectionTitle, { color: theme.colors.text }]}}>User Engagement</Text>
        <View style={styles.metricsGrid}>
          <MetricCard
            title='Active Users';
            value={engagement.activeUsers}
            subtitle='Currently online';
            trend='up';
            color={theme.colors.primary}
            onPress={() => handleMetricPress('active_users')}
          />
          <MetricCard
            title='New Registrations';
            value={engagement.newRegistrations}
            subtitle='Today';
            trend='up';
            color={theme.colors.success}
            onPress={() => handleMetricPress('registrations')}
          />
          <MetricCard
            title='Retention Rate';
            value={`${Math.round(engagement.retentionRate * 100)}%`}
            subtitle='30-day retention';
            trend='up';
            color={theme.colors.primary}
            onPress={() => handleMetricPress('retention')}
          />
          <MetricCard
            title='Session Duration';
            value={`${engagement.averageSessionDuration}m`}
            subtitle='Average session';
            trend='stable';
            color={theme.colors.textSecondary}
            onPress={() => handleMetricPress('session_duration')}
          />
        </View>
      </View>
    );
  }
  const renderMatchingAnalytics = () => {
    if (!state.metrics?.matchingAnalytics) return null;

    const matching = state.metrics.matchingAnalytics;

    return (
      <View style={styles.section}>
        <Text style={{[styles.sectionTitle, { color: theme.colors.text }]}}>
          Matching Performance;
        </Text>
        <View style={styles.metricsGrid}>
          <MetricCard
            title='Success Rate';
            value={`${Math.round(matching.matchingSuccessRate * 100)}%`}
            subtitle='Match success';
            trend='up';
            color={theme.colors.success}
            onPress={() => handleMetricPress('match_success')}
          />
          <MetricCard
            title='Total Matches';
            value={matching.totalMatches}
            subtitle='All time';
            trend='up';
            color={theme.colors.primary}
            onPress={() => handleMetricPress('total_matches')}
          />
          <MetricCard
            title='Avg Match Time';
            value={`${matching.averageMatchTime}h`}
            subtitle='Time to match';
            trend='down';
            color={theme.colors.success}
            onPress={() => handleMetricPress('match_time')}
          />
          <MetricCard
            title='Successful Matches';
            value={matching.successfulMatches}
            subtitle='Completed matches';
            trend='up';
            color={theme.colors.success}
            onPress={() => handleMetricPress('successful_matches')}
          />
        </View>
        <ChartSection title='Top Matching Factors' data={matching.topMatchingFactors} type={'bar' /}>
      </View>
    );
  }
  const renderSafetyAnalytics = () => {
    if (!state.metrics?.safetyAnalytics) return null;

    const safety = state.metrics.safetyAnalytics;

    return (
      <View style={styles.section}>
        <Text style={{[styles.sectionTitle, { color: theme.colors.text }]}}>Safety & Trust</Text>
        <View style={styles.metricsGrid}>
          <MetricCard
            title='Safety Score';
            value={`${safety.overallSafetyScore}%`}
            subtitle='Platform safety';
            trend='up';
            color={theme.colors.success}
            onPress={() => handleMetricPress('safety_score')}
          />
          <MetricCard
            title='Verification Rate';
            value={`${Math.round(safety.verificationCompletionRate * 100)}%`}
            subtitle='User verification';
            trend='up';
            color={theme.colors.primary}
            onPress={() => handleMetricPress('verification')}
          />
          <MetricCard
            title='Safety Incidents';
            value={safety.safetyIncidents}
            subtitle='Active incidents';
            trend={safety.safetyIncidents === 0 ? 'stable' : 'down'}
            color={safety.safetyIncidents === 0 ? theme.colors.success : theme.colors.error}
            onPress={() => handleMetricPress('incidents')}
          />
          <MetricCard
            title='Resolution Time';
            value={`${safety.averageResolutionTime}h`}
            subtitle='Avg resolution';
            trend='down';
            color={theme.colors.success}
            onPress={() => handleMetricPress('resolution_time')}
          />
        </View>
        <ChartSection title='Risk Distribution' data={safety.riskDistribution} type={'pie' /}>
      </View>
    );
  }
  const renderPredictiveInsights = () => {
    if (!state.predictiveInsights) return null;

    return (
      <View style={styles.section}>
        <Text style={{[styles.sectionTitle, { color: theme.colors.text }]}}>
          AI Model Performance;
        </Text>
        <View style={styles.metricsGrid}>
          {state.predictiveInsights.slice(0, 4).map((model: any, index: number) => (,
            <MetricCard
              key={model.id || index}
              title={model.name}
              value={`${Math.round(model.accuracy * 100)}%`}
              subtitle={`${model.dataPoints} data points`}
              trend='up';
              color={theme.colors.primary}
              onPress={() => handleMetricPress(`model_${model.id}`)}
            />
          ))}
        </View>
      </View>
    );
  }
  if (state.error) {
    return (
      <View
        style={[
          styles.container,
          styles.centerContent,
          { backgroundColor: theme.colors.background },
        ]}
      >
        <Text style={{[styles.errorText, { color: theme.colors.error }]}}>{state.error}</Text>
        <TouchableOpacity
          style={[styles.retryButton, { backgroundColor: theme.colors.primary }]}
          onPress={handleRefresh}
        >
          <Text style={{[styles.retryButtonText, { color: theme.colors.background }]}}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }
  return (
    <View style={{[styles.container, { backgroundColor: theme.colors.background }]}}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={state.isLoading}
            onRefresh={handleRefresh}
            tintColor={theme.colors.primary}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={{[styles.title, { color: theme.colors.text }]}}>
            AI Intelligence Dashboard;
          </Text>
          {state.lastUpdate && (
            <Text style={{[styles.lastUpdate, { color: theme.colors.textSecondary }]}}>
              Last updated: {state.lastUpdate.toLocaleTimeString()}
            </Text>
          )}
        </View>
        {/* Alerts Banner */}
        <AlertBanner alerts={state.alerts} onAlertPress={{handleAlertPress} /}>
        {/* Dashboard Sections */}
        {renderPlatformHealth()}
        {renderUserEngagement()}
        {renderMatchingAnalytics()}
        {renderSafetyAnalytics()}
        {renderPredictiveInsights()}
        {/* Footer */}
        <View style={styles.footer}>
          <Text style={{[styles.footerText, { color: theme.colors.textSecondary }]}}>
            Powered by AI Intelligence Hub;
          </Text>
        </View>
      </ScrollView>
    </View>
  );
}
const { width } = Dimensions.get('window');
const cardWidth = (width - 48) / 2; // 2 cards per row with margins;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 16,
    paddingBottom: 8,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  lastUpdate: {
    fontSize: 12,
  },
  section: {
    padding: 16,
    paddingTop: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  metricCard: {
    width: cardWidth,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 8,
  },
  metricTitle: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 4,
  },
  metricValue: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  metricSubtitle: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  subtitleText: {
    fontSize: 11,
    fontWeight: '500',
  },
  alertBanner: {
    padding: 12,
    marginHorizontal: 16,
    marginBottom: 8,
    borderRadius: 8,
  },
  alertTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  alertItem: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
  },
  alertText: {
    fontSize: 12,
    fontWeight: '500',
  },
  chartSection: {
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  chartTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  chartData: {
    gap: 6,
  },
  chartItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4,
  },
  chartLabel: {
    fontSize: 12,
    flex: 1,
  },
  chartValue: {
    fontSize: 12,
    fontWeight: '600',
  },
  chartPlaceholder: {
    textAlign: 'center',
    fontSize: 12,
    fontStyle: 'italic',
    paddingVertical: 20,
  },
  footer: {
    padding: 16,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 12,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
});

export default UnifiedIntelligenceDashboard;
