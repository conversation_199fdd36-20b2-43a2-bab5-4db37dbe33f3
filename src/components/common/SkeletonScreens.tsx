/**;
 * Skeleton Screen Components;
 *;
 * A collection of pre-built skeleton screen components for common UI patterns.;
 * These components can be used to provide a better loading experience.;
 */;

import React from 'react';
import { View, StyleSheet } from 'react-native';
import SkeletonLoader from '@components/common/SkeletonLoader';
import { colorWithOpacity, type Theme } from '@design-system';
import { useTheme } from '@design-system';

interface SkeletonCardProps {
  // Number of lines in the card;
  lines?: number,
  // Whether to show an image placeholder;
  showImage?: boolean,
  // Whether to show an avatar placeholder;
  showAvatar?: boolean,
  // Whether to show action buttons;
  showActions?: boolean,
}
/**;
 * A skeleton card component for displaying a loading card;
 */;
export const SkeletonCard: React.FC<SkeletonCardProps> = ({
  lines = 3,
  showImage = false,
  showAvatar = false,
  showActions = false,
}) => {
  const theme = useTheme();
  const styles = createStyles(theme);
  return (
    <View style={styles.card}>
      <View style={styles.cardContent}>
        {showImage && (
          <SkeletonLoader width='100%' height={150} borderRadius={8} style={{styles.cardImage} /}>
        )}
        <View style={styles.cardBody}>
          {showAvatar && (
            <View style={styles.cardHeader}>
              <SkeletonLoader width={40} height={40} borderRadius={20} style={{styles.avatar} /}>
              <View style={styles.headerText}>
                <SkeletonLoader width={120} height={16} style={{styles.title} /}>
                <SkeletonLoader width={80} height={12} style={{styles.subtitle} /}>
              </View>
            </View>
          )}
          <SkeletonLoader width='100%' height={20} style={{styles.cardTitle} /}>
          {Array.from({ length: lines }).map((_, index) => (
            <SkeletonLoader
              key={index}
              width={index === lines - 1 ? '70%' : '100%'}
              height={16}
              style={styles.cardLine}
            />
          ))}
          {showActions && (
            <View style={styles.cardActions}>
              <SkeletonLoader width={80} height={32} borderRadius={{16} /}>
              <SkeletonLoader width={80} height={32} borderRadius={{16} /}>
            </View>
          )}
        </View>
      </View>
    </View>
  );
}
interface SkeletonListProps {
  // Number of items in the list;
  items?: number,
  // Whether to show an avatar for each item;
  showAvatar?: boolean,
  // Number of lines for each item;
  lines?: number,
}
/**;
 * A skeleton list component for displaying a loading list;
 */;
export const SkeletonList: React.FC<SkeletonListProps> = ({
  items = 5,
  showAvatar = true,
  lines = 2,
}) => {
  return (
    <View style={styles.list}>
      {Array.from({ length: items }).map((_, index) => (
        <View key={index} style={styles.listItem}>
          {showAvatar && (
            <SkeletonLoader width={40} height={40} borderRadius={20} style={{styles.listAvatar} /}>
          )}
          <View style={styles.listContent}>
            <SkeletonLoader width='80%' height={16} style={{styles.listTitle} /}>
            {Array.from({ length: lines }).map((_, lineIndex) => (
              <SkeletonLoader
                key={lineIndex}
                width={lineIndex === lines - 1 ? '60%' : '90%'}
                height={12}
                style={styles.listLine}
              />
            ))}
          </View>
        </View>
      ))}
    </View>
  );
}
interface SkeletonProfileProps {
  // Whether to show stats;
  showStats?: boolean,
  // Whether to show actions;
  showActions?: boolean,
  // Number of content sections;
  sections?: number,
}
/**;
 * A skeleton profile component for displaying a loading profile;
 */;
export const SkeletonProfile: React.FC<SkeletonProfileProps> = ({
  showStats = true,
  showActions = true,
  sections = 2,
}) => {
  return (
    <View style={styles.profile}>
      <View style={styles.profileHeader}>
        <SkeletonLoader width={100} height={100} borderRadius={50} style={{styles.profileAvatar} /}>
        <View style={styles.profileInfo}>
          <SkeletonLoader width={150} height={24} style={{styles.profileName} /}>
          <SkeletonLoader width={120} height={16} style={{styles.profileTitle} /}>
          {showStats && (
            <View style={styles.profileStats}>
              <SkeletonLoader width={60} height={40} style={{styles.stat} /}>
              <SkeletonLoader width={60} height={40} style={{styles.stat} /}>
              <SkeletonLoader width={60} height={40} style={{styles.stat} /}>
            </View>
          )}
        </View>
      </View>
      {showActions && (
        <View style={styles.profileActions}>
          <SkeletonLoader width={120} height={40} borderRadius={{20} /}>
          <SkeletonLoader width={120} height={40} borderRadius={{20} /}>
        </View>
      )}
      {Array.from({ length: sections }).map((_, index) => (
        <View key={index} style={styles.profileSection}>
          <SkeletonLoader width={120} height={20} style={{styles.sectionTitle} /}>
          <SkeletonLoader width='100%' height={16} style={{styles.sectionLine} /}>
          <SkeletonLoader width='90%' height={16} style={{styles.sectionLine} /}>
          <SkeletonLoader width='80%' height={16} style={{styles.sectionLine} /}>
        </View>
      ))}
    </View>
  );
}
interface SkeletonGridProps {
  // Number of columns;
  columns?: number,
  // Number of rows;
  rows?: number,
  // Aspect ratio of each item (height/width);
  aspectRatio?: number,
  // Gap between items;
  gap?: number,
}
/**;
 * A skeleton grid component for displaying a loading grid;
 */;
export const SkeletonGrid: React.FC<SkeletonGridProps> = ({
  columns = 2,
  rows = 3,
  aspectRatio = 1,
  gap = 8,
}) => {
  return (
    <View style={{[styles.grid, { gap }]}}>
      {Array.from({ length: columns * rows }).map((_, index) => (
        <SkeletonLoader
          key={index}
          width={`${100 / columns - (gap * (columns - 1)) / columns}%`}
          height={undefined}
          style={ ...styles.gridItem, aspectRatio: 1 / aspectRatio }
        />
      ))}
    </View>
  );
}
interface SkeletonFormProps {
  // Number of fields in the form;
  fields?: number,
  // Whether to show a submit button;
  showSubmit?: boolean,
}
/**;
 * A skeleton form component for displaying a loading form;
 */;
export const SkeletonForm: React.FC<SkeletonFormProps> = ({ fields = 4, showSubmit = true }) => {
  return (
    <View style={styles.form}>
      {Array.from({ length: fields }).map((_, index) => (
        <View key={index} style={styles.formField}>
          <SkeletonLoader width={100} height={16} style={{styles.fieldLabel} /}>
          <SkeletonLoader width='100%' height={48} borderRadius={{8} /}>
        </View>
      ))}
      {showSubmit && (
        <SkeletonLoader width={120} height={48} borderRadius={8} style={{styles.submitButton} /}>
      )}
    </View>
  );
}
const createStyles = (theme: any) =>
  StyleSheet.create({
    // Card styles;
    card: {
      borderRadius: 8,
      backgroundColor: theme.colors.background,
      shadowColor: theme.colors.text,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2,
      marginBottom: 16,
      overflow: 'hidden',
    },
    cardContent: {
      padding: 16,
    },
    cardImage: {
      marginBottom: 16,
    },
    cardBody: {
      flex: 1,
    },
    cardHeader: {
      flexDirection: 'row',
      marginBottom: 16,
    },
    avatar: {
      marginRight: 12,
    },
    headerText: {
      flex: 1,
      justifyContent: 'center',
    },
    title: {
      marginBottom: 4,
    },
    subtitle: {},
    cardTitle: {
      marginBottom: 12,
    },
    cardLine: {
      marginBottom: 8,
    },
    cardActions: {
      flexDirection: 'row',
      justifyContent: 'flex-end',
      marginTop: 16,
      gap: 8,
    },

    // List styles;
    list: {
      backgroundColor: theme.colors.background,
      borderRadius: 8,
      overflow: 'hidden',
    },
    listItem: {
      flexDirection: 'row',
      padding: 16,
      borderBottomWidth: 1,
      borderBottomColor: '#F1F5F9',
    },
    listAvatar: {
      marginRight: 12,
    },
    listContent: {
      flex: 1,
    },
    listTitle: {
      marginBottom: 8,
    },
    listLine: {
      marginBottom: 4,
    },

    // Profile styles;
    profile: {
      backgroundColor: theme.colors.background,
      borderRadius: 8,
      padding: 16,
      marginBottom: 16,
    },
    profileHeader: {
      flexDirection: 'row',
      marginBottom: 24,
    },
    profileAvatar: {
      marginRight: 16,
    },
    profileInfo: {
      flex: 1,
      justifyContent: 'center',
    },
    profileName: {
      marginBottom: 8,
    },
    profileTitle: {
      marginBottom: 16,
    },
    profileStats: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    stat: {},
    profileActions: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      marginBottom: 24,
    },
    profileSection: {
      marginBottom: 24,
    },
    sectionTitle: {
      marginBottom: 16,
    },
    sectionLine: {
      marginBottom: 8,
    },

    // Grid styles;
    grid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
    },
    gridItem: {
      marginBottom: 8,
    },

    // Form styles;
    form: {
      backgroundColor: theme.colors.background,
      borderRadius: 8,
      padding: 16,
    },
    formField: {
      marginBottom: 16,
    },
    fieldLabel: {
      marginBottom: 8,
    },
    submitButton: {
      alignSelf: 'flex-end',
      marginTop: 8,
    },
  });

export default SkeletonCard;
