import React, { useState } from 'react';
import type { GestureResponderEvent, LayoutChangeEvent } from 'react-native';
import { View, StyleSheet, Text, PanResponder, Animated } from 'react-native';
import { useTheme } from '@design-system';

interface SliderProps {
  minimumValue: number,
  maximumValue: number,
  step: number,
  values: number[],
  onValuesChange: (values: number[]) => void,
}
export default function Slider({
  minimumValue,
  maximumValue,
  step,
  values,
  onValuesChange,
}: SliderProps) {
  const theme = useTheme();
  const styles = createStyles(theme);
  const [sliderWidth, setSliderWidth] = useState(0);
  const [min, max] = values;

  // Calculate positions;
  const minPos = ((min - minimumValue) / (maximumValue - minimumValue)) * sliderWidth;
  const maxPos = ((max - minimumValue) / (maximumValue - minimumValue)) * sliderWidth;

  // Handle slider layout to get width;
  const onLayout = (event: LayoutChangeEvent) => {
    const { width } = event.nativeEvent.layout;
    setSliderWidth(width);
  }
  // Pan responder for min handle;
  const minPanResponder = PanResponder.create({
    onStartShouldSetPanResponder: () => true,
    onPanResponderMove: (event: GestureResponderEvent, gestureState: any) => {
      const newX = Math.max(0, Math.min(maxPos - 20, gestureState.moveX - 50));
      const newPosition =;
        Math.round(((newX / sliderWidth) * (maximumValue - minimumValue)) / step) * step +;
        minimumValue;
      onValuesChange([newPosition, max]);
    },
  });

  // Pan responder for max handle;
  const maxPanResponder = PanResponder.create({
    onStartShouldSetPanResponder: () => true,
    onPanResponderMove: (event: GestureResponderEvent, gestureState: any) => {
      const newX = Math.max(minPos + 20, Math.min(sliderWidth, gestureState.moveX - 50));
      const newPosition =;
        Math.round(((newX / sliderWidth) * (maximumValue - minimumValue)) / step) * step +;
        minimumValue;
      onValuesChange([min, newPosition]);
    },
  });

  return (
    <View style={styles.container}>
      <View style={styles.sliderContainer} onLayout={onLayout}>
        {/* Base track */}
        <View style={{styles.unselectedTrack} /}>
        {/* Selected track */}
        <View
          style={[
            styles.selectedTrack,
            {
              left: minPos,
              width: maxPos - minPos,
            },
          ]}
        />
        {/* Min handle */}
        <View style={{[styles.marker, { left: minPos - 10 }]} {...minPanResponder.panHandlers} /}>
        {/* Max handle */}
        <View style={{[styles.marker, { left: maxPos - 10 }]} {...maxPanResponder.panHandlers} /}>
      </View>
      <View style={styles.labelsContainer}>
        <Text style={styles.label}>${min}</Text>
        <Text style={styles.label}>${max}</Text>
      </View>
    </View>
  );
}
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      width: '100%',
      paddingVertical: 16,
    },
    sliderContainer: {
      height: 30,
      width: '100%',
      position: 'relative',
      justifyContent: 'center',
    },
    selectedTrack: {
      backgroundColor: theme.colors.primary,
      height: 4,
      position: 'absolute',
      borderRadius: 2,
    },
    unselectedTrack: {
      backgroundColor: theme.colors.border,
      height: 4,
      width: '100%',
      borderRadius: 2,
    },
    marker: {
      backgroundColor: theme.colors.background,
      borderColor: theme.colors.primary,
      borderWidth: 2,
      height: 20,
      width: 20,
      borderRadius: 10,
      position: 'absolute',
      top: 5,
      marginTop: -10,
      shadowColor: theme.colors.text,
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: 0.1,
      shadowRadius: 1,
      elevation: 2,
    },
    labelsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: 10,
    },
    label: {
      fontSize: 14,
      color: theme.colors.primary,
      fontWeight: '500',
    },
  });
