import React from 'react';
import { View, StyleSheet, Animated, Easing } from 'react-native';
import { useEffect, useRef } from 'react';
import { useTheme } from '@design-system';

interface SkeletonProps {
  width?: number | string,
  height?: number | string,
  borderRadius?: number,
  style?: any,
}
/**;
 * Skeleton component for loading states;
 * Creates a pulsing animation for better user experience during loading;
 */;
export const Skeleton: React.FC<SkeletonProps> = ({
  width = '100%',
  height = 20,
  borderRadius = 4,
  style,
}) => {
  const theme = useTheme();
  const styles = createStyles(theme);
  const pulseAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const pulse = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 800,
          easing: Easing.ease,
          useNativeDriver: false,
        }),
        Animated.timing(pulseAnim, {
          toValue: 0,
          duration: 800,
          easing: Easing.ease,
          useNativeDriver: false,
        }),
      ]);
    );

    pulse.start();

    return () => {
      pulse.stop();
    }
  }, [pulseAnim]);

  const backgroundColor = pulseAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [theme.colors.surface, theme.colors.border],
  });

  return (
    <Animated.View;
      style={[
        {
          width,
          height,
          borderRadius,
          backgroundColor,
        },
        styles.skeleton,
        style,
      ]}
    />
  );
}
/**;
 * ListingCardSkeleton component;
 * Displays a skeleton loader for listing cards;
 */;
export const ListingCardSkeleton: React.FC = () => {
  const theme = useTheme();
  const styles = createStyles(theme);

  return (
    <View style={styles.cardContainer}>
      <Skeleton height={180} borderRadius={8} style={{styles.imageContainer} /}>
      <View style={styles.contentContainer}>
        <Skeleton width='70%' height={24} style={{styles.titleSkeleton} /}>
        <Skeleton width='50%' height={16} style={{styles.subtitleSkeleton} /}>
        <View style={styles.detailsContainer}>
          <Skeleton width='40%' height={{16} /}>
          <Skeleton width='30%' height={{16} /}>
        </View>
        <View style={styles.actionsContainer}>
          <Skeleton width='45%' height={40} borderRadius={{8} /}>
          <Skeleton width='45%' height={40} borderRadius={{8} /}>
        </View>
      </View>
    </View>
  );
}
/**;
 * HousemateCardSkeleton component;
 * Displays a skeleton loader specifically for housemate cards;
 */;
export const HousemateCardSkeleton: React.FC = () => {
  const theme = useTheme();
  const styles = createStyles(theme);

  return (
    <View style={styles.cardContainer}>
      <View style={styles.headerContainer}>
        <Skeleton width={80} height={80} borderRadius={40} style={{styles.avatarSkeleton} /}>
        <View style={styles.headerContent}>
          <Skeleton width='60%' height={20} style={{styles.nameSkeleton} /}>
          <View style={styles.pillsContainer}>
            <Skeleton width={80} height={24} borderRadius={12} style={{styles.pillSkeleton} /}>
            <Skeleton width={100} height={24} borderRadius={12} style={{styles.pillSkeleton} /}>
          </View>
        </View>
      </View>
      <Skeleton height={80} borderRadius={8} style={{styles.bioSkeleton} /}>
      <View style={styles.tagsContainer}>
        <Skeleton width={60} height={28} borderRadius={14} style={{styles.tagSkeleton} /}>
        <Skeleton width={80} height={28} borderRadius={14} style={{styles.tagSkeleton} /}>
        <Skeleton width={70} height={28} borderRadius={14} style={{styles.tagSkeleton} /}>
      </View>
      <View style={styles.actionsContainer}>
        <Skeleton width='45%' height={44} borderRadius={{8} /}>
        <Skeleton width='45%' height={44} borderRadius={{8} /}>
      </View>
    </View>
  );
}
/**;
 * ProfileCompletionSkeleton component;
 * Displays a skeleton loader for the profile completion banner;
 */;
export const ProfileCompletionSkeleton: React.FC = () => {
  const theme = useTheme();
  const styles = createStyles(theme);

  return (
    <View style={styles.completionContainer}>
      <View style={styles.completionContent}>
        <Skeleton width='70%' height={20} style={{styles.completionTitleSkeleton} /}>
        <Skeleton width='90%' height={16} style={{styles.completionTextSkeleton} /}>
      </View>
      <Skeleton width='100%' height={8} borderRadius={4} style={{styles.progressBarSkeleton} /}>
    </View>
  );
}
const createStyles = (theme: any) =>
  StyleSheet.create({
    skeleton: {
      overflow: 'hidden',
    },
    cardContainer: {
      backgroundColor: theme.colors.background,
      borderRadius: 12,
      padding: 16,
      marginBottom: 16,
      shadowColor: theme.colors.text,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.05,
      shadowRadius: 4,
      elevation: 2,
    },
    imageContainer: {
      marginBottom: 12,
    },
    contentContainer: {
      gap: 8,
    },
    titleSkeleton: {
      marginBottom: 4,
    },
    subtitleSkeleton: {
      marginBottom: 8,
    },
    detailsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 12,
    },
    actionsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: 12,
    },
    headerContainer: {
      flexDirection: 'row',
      marginBottom: 16,
    },
    avatarSkeleton: {
      marginRight: 16,
    },
    headerContent: {
      flex: 1,
      justifyContent: 'center',
    },
    nameSkeleton: {
      marginBottom: 8,
    },
    pillsContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 8,
    },
    pillSkeleton: {
      marginRight: 8,
    },
    bioSkeleton: {
      marginBottom: 16,
    },
    tagsContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginBottom: 16,
      gap: 8,
    },
    tagSkeleton: {
      marginRight: 8,
    },
    completionContainer: {
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 16,
      marginHorizontal: 16,
      marginBottom: 16,
    },
    completionContent: {
      marginBottom: 12,
    },
    completionTitleSkeleton: {
      marginBottom: 8,
    },
    completionTextSkeleton: {
      marginBottom: 12,
    },
    progressBarSkeleton: {
      marginTop: 8,
    },
  });

export default Skeleton;
