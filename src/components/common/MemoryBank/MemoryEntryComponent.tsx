import React, { useState } from 'react';
import { useTheme } from '@design-system';
import { View, Text, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { MemoryEntry } from '@services/memoryBankService';
import { useColorFix } from '@hooks/useColorFix';
interface MemoryEntryComponentProps {
  entry: MemoryEntry,
  onDelete?: (entry: MemoryEntry) => void,
}
export const MemoryEntryComponent: React.FC<MemoryEntryComponentProps> = ({ entry, onDelete }) => {
  const router = useRouter();
  const [isExpanded, setIsExpanded] = useState(false);

  // Check if entry is related to a room;
  const isRoomEntry = entry.tags?.includes('room') || entry.tags?.includes('property');
  const roomId = entry.metadata?.roomId;

  // Format date for display;
  const formatDate = (dateString: string) => {
    const theme = useTheme();
    const styles = createStyles(theme);

    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  }
  // Get icon based on entry type;
  const getIcon = () => {
    if (isRoomEntry) return 'apartment';

    switch (entry.type) {
      case 'decision': ,
        return 'lightbulb';
      case 'context': ,
        return 'info';
      case 'progress': ,
        return 'check-circle';
      case 'pattern': ,
        return 'category';
      default: ,
        return 'note';
    }
  }
  // Get color based on entry type;
  const getColor = () => {
    if (isRoomEntry) return theme.colors.primary;

    switch (entry.type) {
      case 'decision': ,
        return theme.colors.secondary;
      case 'context': ,
        return theme.colors.tertiary;
      case 'progress': ,
        return theme.colors.quaternary;
      case 'pattern': ,
        return theme.colors.quinary;
      default: ,
        return theme.colors.senary;
    }
  }
  const getBadgeColor = (type: MemoryEntry['type']) => {
    if (isRoomEntry) return theme.colors.primary;
    return theme.colors.secondary;
  }
  // Handle edit entry;
  const handleEdit = () => {
    router.push({
      pathname: '/memory-bank/edit-entry',
      params: {
        id: entry.id || '',
        type: entry.type,
        title: entry.title,
        content: entry.content,
        tags: entry.tags ? entry.tags.join(',') : '',
      },
    });
  }
  // Handle view room;
  const handleViewRoom = () => {
    if (roomId) {
      router.push(`/room/${roomId}`);
    }
  }
  // Handle delete entry;
  const handleDelete = () => {
    Alert.alert(
      'Delete Memory',
      'Are you sure you want to delete this memory? This cannot be undone.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          onPress: () => {
            if (onDelete) {
              onDelete(entry);
            }
          },
          style: 'destructive',
        },
      ];
    );
  }
  // Get a truncated version of the content;
  const getTruncatedContent = () => {
    if (isExpanded) return entry.content;

    const maxLength = 150;
    if (entry.content.length <= maxLength) return entry.content;

    return entry.content.substring(0, maxLength) + '...';
  }
  return (
    <View style={{[styles.container, { borderLeftColor: getColor() }]}}>
      <View style={styles.header}>
        <View style={styles.titleRow}>
          <MaterialIcons name={getIcon()} size={18} color={{getColor()} /}>
          <Text style={styles.title}>{entry.title}</Text>
        </View>
        <Text style={styles.date}>{formatDate(entry.timestamp)}</Text>
      </View>
      <TouchableOpacity style={styles.contentContainer} onPress={() => setIsExpanded(!isExpanded)}>
        <Text style={styles.content}>{getTruncatedContent()}</Text>
        {entry.content.length > 150 && (
          <Text style={styles.expandText}>{isExpanded ? 'Show less' : 'Show more'}</Text>
        )}
      </TouchableOpacity>
      {entry.tags && entry.tags.length > 0 && (
        <View style={styles.tagsContainer}>
          {entry.tags.map((tag, index) => (
            <View key={index} style={styles.tag}>
              <Text style={styles.tagText}>#{tag}</Text>
            </View>
          ))}
        </View>
      )}
      <View style={styles.actionsContainer}>
        <TouchableOpacity style={[styles.actionButton, styles.editButton]} onPress={handleEdit}>
          <MaterialIcons name='edit' size={16} color={'#0066cc' /}>
          <Text style={styles.editButtonText}>Edit</Text>
        </TouchableOpacity>
        {isRoomEntry && roomId && (
          <TouchableOpacity
            style={[styles.actionButton, styles.viewRoomButton]}
            onPress={handleViewRoom}
          >
            <MaterialIcons name='apartment' size={16} color={{theme.colors.primary} /}>
            <Text style={styles.viewRoomButtonText}>View Room</Text>
          </TouchableOpacity>
        )}
        <TouchableOpacity style={[styles.actionButton, styles.deleteButton]} onPress={handleDelete}>
          <MaterialIcons name='delete' size={16} color={'#cc0000' /}>
          <Text style={styles.deleteButtonText}>Delete</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      backgroundColor: theme.colors.background,
      borderRadius: 8,
      padding: 12,
      marginBottom: 12,
      borderLeftWidth: 4,
      shadowColor: theme.colors.text,
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 2,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 8,
    },
    titleRow: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    title: {
      fontSize: 16,
      fontWeight: '600',
      marginLeft: 6,
    },
    date: {
      fontSize: 12,
      color: '#8E8E93',
    },
    contentContainer: {
      marginVertical: 8,
    },
    content: {
      fontSize: 14,
      lineHeight: 20,
      color: '#3C3C43',
    },
    expandText: {
      color: '#0066cc',
      fontSize: 14,
      marginTop: 4,
    },
    tagsContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginVertical: 8,
    },
    tag: {
      backgroundColor: '#F2F2F7',
      borderRadius: 12,
      paddingHorizontal: 8,
      paddingVertical: 4,
      marginRight: 8,
      marginBottom: 4,
    },
    tagText: {
      fontSize: 12,
      color: '#636366',
    },
    actionsContainer: {
      flexDirection: 'row',
      justifyContent: 'flex-end',
      marginTop: 8,
    },
    actionButton: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 4,
      marginLeft: 8,
    },
    editButton: {
      backgroundColor: '#E5F2FF',
    },
    editButtonText: {
      color: '#0066cc',
      fontSize: 12,
      fontWeight: '500',
      marginLeft: 4,
    },
    deleteButton: {
      backgroundColor: '#FFEEEE',
    },
    deleteButtonText: {
      color: '#cc0000',
      fontSize: 12,
      fontWeight: '500',
      marginLeft: 4,
    },
    viewRoomButton: {
      backgroundColor: '#E5F2FF',
    },
    viewRoomButtonText: {
      color: theme.colors.primary,
      fontSize: 12,
      fontWeight: '500',
      marginLeft: 4,
    },
  });

export default MemoryEntryComponent;
