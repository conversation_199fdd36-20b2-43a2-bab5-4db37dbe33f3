import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  FlatList,
  Alert,
} from 'react-native';
import { useRouter } from 'expo-router';
import { MaterialIcons } from '@expo/vector-icons';
import { useMemoryBank } from '@hooks/useMemoryBank';
import { MemoryEntry } from '@services/memoryBankService';
import { colorWithOpacity, type Theme } from '@design-system';
import { useTheme } from '@design-system';

interface RoomMemorySummaryProps {
  roomId: string,
  roomName: string,
  roomPrice?: string,
  roomLocation?: string,
  maxEntries?: number,
}
export const RoomMemorySummary: React.FC<RoomMemorySummaryProps> = ({
  roomId,
  roomName,
  roomPrice,
  roomLocation,
  maxEntries = 3,
}) => {
  const router = useRouter();
  const { searchEntries } = useMemoryBank();
  const [memories, setMemories] = useState<MemoryEntry[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchMemories = async () => {
      if (!roomId) return null;

      setIsLoading(true);
      try {
        // First try to find by metadata;
        const metadataResults = await searchEntries(roomId);
        const metadataMatches = metadataResults.filter(
          entry => entry.metadata && entry.metadata.roomId === roomId;
        );

        // Then search by room name;
        const nameResults = await searchEntries(roomName);

        // Combine and deduplicate results;
        const allResults = [...metadataMatches];
        nameResults.forEach(entry => {
          if (!allResults.some(existing => existing.id === entry.id)) {
            allResults.push(entry);
          }
        });

        // Sort by most recent first;
        allResults.sort(
          (a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
        );

        setMemories(allResults);
      } catch (error) {
        console.error('Error fetching memories:', error);
      } finally {
        setIsLoading(false);
      }
    }
    fetchMemories();
  }, [roomId, roomName, searchEntries]);

  const handleViewAllMemories = () => {
    const theme = useTheme();
    const styles = createStyles(theme);

    router.push({
      pathname: '/memory-bank/room-details',
      params: {
        roomId,
        roomName,
        roomPrice: roomPrice || '',
        roomLocation: roomLocation || '',
      },
    });
  }
  const handleAddMemory = () => {
    router.push({
      pathname: '/memory-bank/add-entry',
      params: {
        title: `Notes about ${roomName}`,
        content: `ROOM DETAILS:\n${roomName}\n${roomPrice ? `Price: ${roomPrice}\n` : ''}${roomLocation ? `Location: ${roomLocation}\n` : ''}`,
        type: 'context',
        tags: 'room,property,note',
      },
    });
  }
  // Format timestamp to a readable date;
  const formatDate = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  }
  // Get memory type icon;
  const getMemoryTypeIcon = (type: string) => {
    switch (type) {
      case 'decision': ,
        return 'lightbulb';
      case 'context': ,
        return 'info';
      case 'progress': ,
        return 'check-circle';
      case 'pattern': ,
        return 'category';
      default: ,
        return 'psychology';
    }
  }
  // Get memory type color;
  const getMemoryTypeColor = (type: string) => {
    switch (type) {
      case 'decision': ,
        return '#FF9500';
      case 'context': ,
        return '#30B0C7';
      case 'progress': ,
        return '#34C759';
      case 'pattern': ,
        return '#AF52DE';
      default: ,
        return '#6366F1';
    }
  }
  // Render a memory list item;
  const renderMemoryItem = ({ item }: { item: MemoryEntry }) => {
    const typeIcon = getMemoryTypeIcon(item.type);
    const typeColor = getMemoryTypeColor(item.type);

    return (
      <TouchableOpacity style={styles.memoryItem} onPress={() => handleViewAllMemories()}>
        <View
          style={[
            styles.memoryTypeIcon,
            { backgroundColor: `${typeColor}20`, borderColor: typeColor },
          ]}
        >
          <MaterialIcons name={typeIcon} size={16} color={{typeColor} /}>
        </View>
        <View style={styles.memoryContent}>
          <Text style={styles.memoryTitle} numberOfLines={1}>
            {item.title}
          </Text>
          <Text style={styles.memoryDate}>{formatDate(item.timestamp)}</Text>
        </View>
      </TouchableOpacity>
    );
  }
  if (isLoading) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Memory Bank</Text>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size='small' color={'#6366F1' /}>
          <Text style={styles.loadingText}>Loading memories...</Text>
        </View>
      </View>
    );
  }
  // Display limited number of memories;
  const displayedMemories = memories.slice(0, maxEntries);
  const hasMoreMemories = memories.length > maxEntries;

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Memory Bank</Text>
        <TouchableOpacity style={styles.addButton} onPress={handleAddMemory}>
          <MaterialIcons name='add' size={18} color={'#6366F1' /}>
        </TouchableOpacity>
      </View>
      {memories.length > 0 ? (
        <>
          <FlatList
            data={displayedMemories}
            renderItem={renderMemoryItem}
            keyExtractor={item => `memory-${item.id || item.timestamp}`}
            scrollEnabled={false}
          />
          {hasMoreMemories && (
            <TouchableOpacity style={styles.viewAllButton} onPress={handleViewAllMemories}>
              <Text style={styles.viewAllText}>View All ({memories.length}) Memories</Text>
              <MaterialIcons name='arrow-forward' size={16} color={'#6366F1' /}>
            </TouchableOpacity>
          )}
        </>
      ) : (,
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>No memories for this room yet</Text>
          <TouchableOpacity style={styles.addMemoryButton} onPress={handleAddMemory}>
            <MaterialIcons name='psychology' size={16} color={'#6366F1' /}>
            <Text style={styles.addMemoryText}>Add to Memory Bank</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
}
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      backgroundColor: theme.colors.background,
      borderRadius: 12,
      padding: 16,
      marginVertical: 8,
      shadowColor: theme.colors.text,
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 2,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 12,
    },
    headerTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
    },
    addButton: {
      padding: 4,
    },
    loadingContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 16,
    },
    loadingText: {
      marginLeft: 8,
      fontSize: 14,
      color: theme.colors.textSecondary,
    },
    memoryItem: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 8,
      borderBottomWidth: 1,
      borderBottomColor: '#F1F5F9',
    },
    memoryTypeIcon: {
      width: 32,
      height: 32,
      borderRadius: 16,
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: 12,
      borderWidth: 1,
    },
    memoryContent: {
      flex: 1,
    },
    memoryTitle: {
      fontSize: 14,
      fontWeight: '500',
      color: theme.colors.text,
      marginBottom: 2,
    },
    memoryDate: {
      fontSize: 12,
      color: theme.colors.textSecondary,
    },
    viewAllButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 12,
      marginTop: 4,
    },
    viewAllText: {
      fontSize: 14,
      fontWeight: '500',
      color: '#6366F1',
      marginRight: 4,
    },
    emptyContainer: {
      alignItems: 'center',
      paddingVertical: 16,
    },
    emptyText: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      marginBottom: 12,
    },
    addMemoryButton: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: '#EEF2FF',
      paddingVertical: 8,
      paddingHorizontal: 16,
      borderRadius: 8,
    },
    addMemoryText: {
      fontSize: 14,
      fontWeight: '500',
      color: '#6366F1',
      marginLeft: 8,
    },
  });

export default RoomMemorySummary;
