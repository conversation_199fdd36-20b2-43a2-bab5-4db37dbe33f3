import React, { useState, useEffect, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  Dimensions,
  Alert,
} from 'react-native';
import { useMemoryBank } from '@hooks/useMemoryBank';
import { MemoryEntry } from '@services/memoryBankService';
import { MemoryEntryComponent } from '@components/common/MemoryBank/MemoryEntryComponent';
import { useRouter } from 'expo-router';
import { MaterialIcons } from '@expo/vector-icons';
import { colorWithOpacity, type Theme } from '@design-system';
import { useTheme } from '@design-system';

interface RoomMemoryDetailsProps {
  roomId: string,
  roomName: string,
  roomPrice?: string,
  roomLocation?: string,
}
type SortOption = 'newest' | 'oldest' | 'type';

export const RoomMemoryDetails: React.FC<RoomMemoryDetailsProps> = ({
  roomId,
  roomName,
  roomPrice,
  roomLocation,
}) => {
  const router = useRouter();
  const { searchEntries, deleteEntry, addEntry } = useMemoryBank();
  const [memories, setMemories] = useState<MemoryEntry[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [sortOption, setSortOption] = useState<SortOption>('newest');
  const [selectedTypes, setSelectedTypes] = useState<Record<string, boolean>>({
    decision: true,
    context: true,
    progress: true,
    pattern: true,
  });

  // Fetch memories related to this room;
  useEffect(() => {
    async function fetchMemories() {
      setIsLoading(true);
      try {
        // First try to find by metadata;
        const metadataResults = await searchEntries(roomId);
        const metadataMatches = metadataResults.filter(
          entry => entry.metadata && entry.metadata.roomId === roomId;
        );

        // Then search by room name;
        const nameResults = await searchEntries(roomName);

        // Combine and deduplicate results;
        const allResults = [...metadataMatches];
        nameResults.forEach(entry => {
          if (!allResults.some(existing => existing.id === entry.id)) {
            allResults.push(entry);
          }
        });

        setMemories(allResults);
      } catch (error) {
        console.error('Error fetching room memories:', error);
        Alert.alert('Error', 'Failed to load room memories');
      } finally {
        setIsLoading(false);
      }
    }
    fetchMemories();
  }, [roomId, roomName, searchEntries]);

  // Handle memory deletion;
  const handleDelete = async (entry: MemoryEntry) => {
    if (!entry.id) return null;

    try {
      const success = await deleteEntry(entry.id);
      if (success) {
        setMemories(prev => prev.filter(e => e.id !== entry.id));
        Alert.alert('Success', 'Memory deleted successfully');
      }
    } catch (error) {
      console.error('Error deleting memory:', error);
      Alert.alert('Error', 'Failed to delete memory');
    }
  }
  // Add new memory;
  const handleAddMemory = () => {
    const theme = useTheme();
    const styles = createStyles(theme);

    router.push({
      pathname: '/memory-bank/add-entry',
      params: {
        title: `Notes about ${roomName}`,
        content: `ROOM DETAILS:\n${roomName}\n${roomPrice ? `Price: ${roomPrice}\n` : ''}${roomLocation ? `Location: ${roomLocation}\n` : ''}`,
        type: 'context',
        tags: 'room,property,note',
      },
    });
  }
  // Toggle type filter;
  const toggleTypeFilter = (type: string) => {
    setSelectedTypes(prev => ({
      ...prev,
      [type]: !prev[type],
    }));
  }
  // Apply filters and sorting;
  const filteredAndSortedMemories = useMemo(() => {
    // First filter by selected types;
    const filtered = memories.filter(memory => selectedTypes[memory.type]);

    // Then sort;
    return [...filtered].sort((a, b) => {
      if (sortOption === 'newest') {
        return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
      } else if (sortOption === 'oldest') {
        return new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime();
      } else if (sortOption === 'type') {
        return a.type.localeCompare(b.type);
      }
      return 0;
    });
  }, [memories, selectedTypes, sortOption]);

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size='large' color={'#6366F1' /}>
        <Text style={styles.loadingText}>Loading memories...</Text>
      </View>
    );
  }
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Memories for {roomName}</Text>
        <TouchableOpacity style={styles.addButton} onPress={handleAddMemory}>
          <MaterialIcons name='add' size={18} color={{theme.colors.background} /}>
          <Text style={styles.addButtonText}>Add Memory</Text>
        </TouchableOpacity>
      </View>
      <View style={styles.filterContainer}>
        <View style={styles.typeFilters}>
          <Text style={styles.filterLabel}>Types:</Text>
          <View style={styles.typeBadges}>
            <TouchableOpacity
              style={[styles.typeBadge, selectedTypes.decision && styles.activeBadgeDecision]}
              onPress={() => toggleTypeFilter('decision')}
            >
              <MaterialIcons
                name='lightbulb-outline';
                size={14}
                color={selectedTypes.decision ? theme.colors.background : '#FF9500'}
              />
              <Text style={[styles.typeBadgeText, selectedTypes.decision && styles.activeTypeText]}>
                Decisions;
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.typeBadge, selectedTypes.context && styles.activeBadgeContext]}
              onPress={() => toggleTypeFilter('context')}
            >
              <MaterialIcons
                name='info';
                size={14}
                color={selectedTypes.context ? theme.colors.background : '#30B0C7'}
              />
              <Text style={[styles.typeBadgeText, selectedTypes.context && styles.activeTypeText]}>
                Context;
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.typeBadge, selectedTypes.progress && styles.activeBadgeProgress]}
              onPress={() => toggleTypeFilter('progress')}
            >
              <MaterialIcons
                name='check-circle';
                size={14}
                color={selectedTypes.progress ? theme.colors.background : '#34C759'}
              />
              <Text style={[styles.typeBadgeText, selectedTypes.progress && styles.activeTypeText]}>
                Progress;
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.typeBadge, selectedTypes.pattern && styles.activeBadgePattern]}
              onPress={() => toggleTypeFilter('pattern')}
            >
              <MaterialIcons
                name='category';
                size={14}
                color={selectedTypes.pattern ? theme.colors.background : '#AF52DE'}
              />
              <Text style={[styles.typeBadgeText, selectedTypes.pattern && styles.activeTypeText]}>
                Patterns;
              </Text>
            </TouchableOpacity>
          </View>
        </View>
        <View style={styles.sortOptions}>
          <Text style={styles.filterLabel}>Sort by:</Text>
          <View style={styles.sortButtons}>
            <TouchableOpacity
              style={[styles.sortButton, sortOption === 'newest' && styles.activeSortButton]}
              onPress={() => setSortOption('newest')}
            >
              <Text
                style={[
                  styles.sortButtonText,
                  sortOption === 'newest' && styles.activeSortButtonText,
                ]}
              >
                Newest;
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.sortButton, sortOption === 'oldest' && styles.activeSortButton]}
              onPress={() => setSortOption('oldest')}
            >
              <Text
                style={[
                  styles.sortButtonText,
                  sortOption === 'oldest' && styles.activeSortButtonText,
                ]}
              >
                Oldest;
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.sortButton, sortOption === 'type' && styles.activeSortButton]}
              onPress={() => setSortOption('type')}
            >
              <Text
                style={[
                  styles.sortButtonText,
                  sortOption === 'type' && styles.activeSortButtonText,
                ]}
              >
                Type;
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
      {filteredAndSortedMemories.length > 0 ? (
        <FlatList
          data={filteredAndSortedMemories}
          renderItem={({ item }) => <MemoryEntryComponent entry={item} onDelete={{handleDelete} /}>
          keyExtractor={item => `memory-${item.id || item.timestamp}`}
          contentContainerStyle={styles.listContainer}
        />
      ) : (,
        <View style={styles.emptyContainer}>
          <MaterialIcons name='psychology' size={48} color={{theme.colors.border} /}>
          <Text style={styles.emptyText}>No memories found for this room</Text>
          <TouchableOpacity style={styles.emptyAddButton} onPress={handleAddMemory}>
            <Text style={styles.emptyAddButtonText}>Create Your First Memory</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
}
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    headerTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
    },
    addButton: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: '#6366F1',
      paddingVertical: 8,
      paddingHorizontal: 12,
      borderRadius: 6,
    },
    addButtonText: {
      color: theme.colors.background,
      fontSize: 14,
      fontWeight: '500',
      marginLeft: 4,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    loadingText: {
      marginTop: 12,
      fontSize: 16,
      color: theme.colors.textSecondary,
    },
    filterContainer: {
      paddingHorizontal: 16,
      paddingVertical: 12,
      backgroundColor: '#F8FAFC',
    },
    filterLabel: {
      fontSize: 14,
      fontWeight: '500',
      color: theme.colors.textSecondary,
      marginBottom: 8,
    },
    typeFilters: {
      marginBottom: 12,
    },
    typeBadges: {
      flexDirection: 'row',
      flexWrap: 'wrap',
    },
    typeBadge: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
      marginRight: 8,
      marginBottom: 8,
      borderWidth: 1,
      borderColor: theme.colors.border,
      backgroundColor: theme.colors.background,
    },
    typeBadgeText: {
      fontSize: 12,
      marginLeft: 4,
    },
    activeTypeText: {
      color: theme.colors.background,
    },
    activeBadgeDecision: {
      backgroundColor: '#FF9500',
      borderColor: '#FF9500',
    },
    activeBadgeContext: {
      backgroundColor: '#30B0C7',
      borderColor: '#30B0C7',
    },
    activeBadgeProgress: {
      backgroundColor: '#34C759',
      borderColor: '#34C759',
    },
    activeBadgePattern: {
      backgroundColor: '#AF52DE',
      borderColor: '#AF52DE',
    },
    sortOptions: {
      marginBottom: 8,
    },
    sortButtons: {
      flexDirection: 'row',
    },
    sortButton: {
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 6,
      marginRight: 8,
      borderWidth: 1,
      borderColor: theme.colors.border,
      backgroundColor: theme.colors.background,
    },
    sortButtonText: {
      fontSize: 12,
      color: theme.colors.textSecondary,
    },
    activeSortButton: {
      backgroundColor: '#6366F1',
      borderColor: '#6366F1',
    },
    activeSortButtonText: {
      color: theme.colors.background,
      fontWeight: '500',
    },
    listContainer: {
      paddingHorizontal: 16,
      paddingVertical: 8,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 24,
    },
    emptyText: {
      fontSize: 16,
      color: theme.colors.textSecondary,
      marginTop: 16,
      marginBottom: 24,
      textAlign: 'center',
    },
    emptyAddButton: {
      backgroundColor: '#6366F1',
      paddingVertical: 12,
      paddingHorizontal: 24,
      borderRadius: 8,
    },
    emptyAddButtonText: {
      color: theme.colors.background,
      fontSize: 16,
      fontWeight: '500',
    },
  });

export default RoomMemoryDetails;
