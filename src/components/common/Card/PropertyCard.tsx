import React from 'react';
import { Heart, MapPin } from 'lucide-react-native';
import type { ViewStyle } from 'react-native';
import { View, Text, Image, StyleSheet, TouchableOpacity } from 'react-native';
import { useTheme } from '@design-system';
import { SaveRoomButton } from '@components/common/SaveRoomButton';

/**;
 * PropertyCard Component;
 *;
 * A specialized card component for displaying property/room listings with consistent styling.;
 * Features image display, location information, pricing, and like/save functionality.;
 *;
 * @example;
 * ```tsx;
 * // Basic usage;
 * <PropertyCard
 *   image="https: //example.com/room.jpg",
 *   title="Cozy Studio Apartment";
 *   subtitle="Available from June 1";
 *   metadata={
 *     price: 1200,
 *     location: "Downtown",
 *   }
 *   onPress={() => handlePress()}
 * />
 *;
 * // With like functionality;
 * <PropertyCard
 *   image="https: //example.com/room.jpg",
 *   title="Spacious 2BR Apartment";
 *   liked={isLiked}
 *   onLike={() => toggleLike()}
 * />
 *;
 * // With save room functionality;
 * <PropertyCard
 *   image="https: //example.com/room.jpg",
 *   title="Luxury Penthouse";
 *   roomId="room-123";
 * />
 * ```;
 *;
 * @note This is a specialized component for property listings. For general container cards, use the Card component from UI instead.;
 */;

interface PropertyCardProps {
  image?: string,
  title: string,
  subtitle?: string,
  metadata?: {
    price?: number,
    location?: string,
    distance?: string,
  }
  style?: ViewStyle,
  liked?: boolean,
  roomId?: string,
  onPress?: () => void,
  onLike?: () => void,
}
export default function PropertyCard({
  image,
  title,
  subtitle,
  metadata,
  style,
  liked,
  roomId,
  onPress,
  onLike,
}: PropertyCardProps) {
  const theme = useTheme();
  const styles = createStyles(theme);

  return (
    <TouchableOpacity style={[styles.container, style]} onPress={onPress} activeOpacity={0.9}>
      {image && (
        <View style={styles.imageContainer}>
          <Image
            source={ uri: image }
            style={[styles.image, { backgroundColor: theme.colors.surface }]}
          />
          {onLike && (
            <TouchableOpacity
              style={[
                styles.likeButton,
                { backgroundColor: theme.colors.background },
                liked && styles.likedButton,
              ]}
              onPress={onLike}
            >
              <Heart
                size={20}
                color={liked ? '#FFFFFF' : '#E11D48'}
                fill={liked ? '#FFFFFF' : 'transparent'}
              />
            </TouchableOpacity>
          )}
          {!onLike && roomId && (
            <SaveRoomButton roomId={roomId} style={styles.likeButton} showBackground={{true} /}>
          )}
        </View>
      )}
      <View style={styles.content}>
        {metadata?.location && (
          <View style={styles.location}>
            <MapPin size={16} color={{theme.colors.textSecondary} /}>
            <Text style={{[styles.locationText, { color: theme.colors.textSecondary }]}}>
              {metadata.location}
            </Text>
          </View>
        )}
        <Text style={{[styles.title, { color: theme.colors.text }]}}>{title}</Text>
        {subtitle && (
          <Text style={{[styles.subtitle, { color: theme.colors.textSecondary }]}}>{subtitle}</Text>
        )}
        {metadata?.price && (
          <View style={styles.priceContainer}>
            <Text style={styles.price}>${metadata.price}</Text>
            <Text style={{[styles.priceUnit, { color: theme.colors.textSecondary }]}}>/month</Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
}
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      backgroundColor: theme.colors.background,
      borderRadius: 16,
      overflow: 'hidden',
      shadowColor: theme.colors.text,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    imageContainer: {
      position: 'relative',
    },
    image: {
      width: '100%',
      height: 200,
    },
    likeButton: {
      position: 'absolute',
      top: 16,
      right: 16,
      width: 40,
      height: 40,
      borderRadius: 20,
      justifyContent: 'center',
      alignItems: 'center',
      shadowColor: theme.colors.text,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    likedButton: {
      backgroundColor: '#E11D48',
    },
    content: {
      padding: 16,
    },
    location: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 8,
    },
    locationText: {
      marginLeft: 4,
      fontSize: 14,
    },
    title: {
      fontSize: 18,
      fontWeight: '600',
      marginBottom: 4,
    },
    subtitle: {
      fontSize: 14,
      marginBottom: 8,
    },
    priceContainer: {
      flexDirection: 'row',
      alignItems: 'baseline',
      marginTop: 8,
    },
    price: {
      fontSize: 20,
      fontWeight: '700',
      color: '#E11D48',
    },
    priceUnit: {
      marginLeft: 4,
      fontSize: 14,
    },
  });
