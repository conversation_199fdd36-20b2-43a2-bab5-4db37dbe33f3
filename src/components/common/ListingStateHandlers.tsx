import React from 'react';
import {
  View,
  Text,
  ActivityIndicator,
  TouchableOpacity,
  StyleSheet,
  AccessibilityRole,
} from 'react-native';
import { RefreshCw } from 'lucide-react-native';
import { colorWithOpacity, type Theme } from '@design-system';
import { useTheme } from '@design-system';

/**;
 * Loading state component for listing screens;
 */;
export const LoadingState: React.FC<{
  message?: string,
}> = ({ message = 'Loading listings...' }) => {
  const theme = useTheme();
  const styles = createStyles(theme);
  return (
    <View style={styles.loadingContainer}>
      <ActivityIndicator size='large' color={'#4F46E5' /}>
      <Text style={styles.loadingText}>{message}</Text>
    </View>
  );
}
/**;
 * Error state component for listing screens;
 */;
export const ErrorState: React.FC<{
  error: string | Error,
  onRetry: () => void,
  accessible?: boolean,
  accessibilityLabel?: string,
  accessibilityHint?: string,
}> = ({ error, onRetry, accessible = true, accessibilityLabel, accessibilityHint }) => {
  const errorMessage = error instanceof Error ? error.message : error,
  return (
    <View
      style={styles.errorContainer}
      accessible={accessible}
      accessibilityLabel={accessibilityLabel || `Error: ${errorMessage}`}
      accessibilityHint={accessibilityHint}
    >
      <Text style={styles.errorTitle}>Oops! Something went wrong</Text>
      <Text style={styles.errorText}>{errorMessage}</Text>
      <TouchableOpacity
        style={styles.retryButton}
        onPress={onRetry}
        accessible={true}
        accessibilityLabel='Try again';
        accessibilityRole='button';
        accessibilityHint='Attempts to reload the content';
      >
        <Text style={styles.retryText}>Try Again</Text>
      </TouchableOpacity>
    </View>
  );
}
/**;
 * Empty state component for listing screens;
 */;
export const EmptyState: React.FC<{
  title?: string,
  message?: string,
  onRefresh?: () => void,
  accessible?: boolean,
  accessibilityLabel?: string,
  accessibilityHint?: string,
  accessibilityRole?: AccessibilityRole,
}> = ({
  title = 'No listings found',
  message = "We couldn't find any listings matching your criteria. Try adjusting your filters or check back later.",
  onRefresh,
  accessible = true,
  accessibilityLabel,
  accessibilityHint,
  accessibilityRole = 'text' as AccessibilityRole,
}) => {
  return (
    <View
      style={styles.emptyContainer}
      accessible={accessible}
      accessibilityLabel={accessibilityLabel || `${title}. ${message}`}
      accessibilityRole={accessibilityRole}
      accessibilityHint={accessibilityHint}
    >
      <Text style={styles.emptyTitle}>{title}</Text>
      <Text style={styles.emptyText}>{message}</Text>
      {onRefresh && (
        <TouchableOpacity
          style={styles.refreshButton}
          onPress={onRefresh}
          accessible={true}
          accessibilityLabel='Refresh the listings';
          accessibilityRole='button';
          accessibilityHint='Tries to load the listings again';
        >
          <RefreshCw size={18} color={'#4F46E5' /}>
          <Text style={styles.refreshText}>Refresh</Text>
        </TouchableOpacity>
      )}
    </View>
  );
}
/**;
 * Loading footer for infinite scroll lists;
 */;
export const LoadingFooter: React.FC = () => {
  return (
    <View style={styles.loadingFooter}>
      <ActivityIndicator size='small' color={'#4F46E5' /}>
      <Text style={styles.loadingFooterText}>Loading more...</Text>
    </View>
  );
}
const createStyles = (theme: any) =>
  StyleSheet.create({
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20,
    },
    loadingText: {
      marginTop: 12,
      fontSize: 16,
      color: '#6B7280',
    },
    errorContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20,
    },
    errorTitle: {
      fontSize: 20,
      fontWeight: '600',
      color: '#1F2937',
      marginBottom: 8,
      textAlign: 'center',
    },
    errorText: {
      fontSize: 16,
      color: theme.colors.error,
      textAlign: 'center',
      marginBottom: 24,
    },
    retryButton: {
      backgroundColor: theme.colors.primary,
      paddingHorizontal: 24,
      paddingVertical: 12,
      borderRadius: 8,
    },
    retryText: {
      color: theme.colors.background,
      fontWeight: '600',
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20,
    },
    emptyTitle: {
      fontSize: 20,
      fontWeight: '600',
      color: '#1F2937',
      marginBottom: 8,
      textAlign: 'center',
    },
    emptyText: {
      fontSize: 16,
      color: '#6B7280',
      textAlign: 'center',
      marginBottom: 24,
      lineHeight: 22,
    },
    refreshButton: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: '#F3F4F6',
      paddingHorizontal: 16,
      paddingVertical: 10,
      borderRadius: 8,
    },
    refreshText: {
      marginLeft: 8,
      fontSize: 16,
      fontWeight: '500',
      color: '#4F46E5',
    },
    loadingFooter: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      paddingVertical: 16,
    },
    loadingFooterText: {
      marginLeft: 8,
      fontSize: 14,
      color: '#6B7280',
    },
  });

export default LoadingState;
