import React from 'react';
import { BaseErrorBoundary } from './BaseErrorBoundary';
import { ErrorBoundaryProps, ErrorInfo } from './types';
import { logger } from '@utils/logger';
import { ProfileErrorFallback } from './ProfileErrorFallback';

interface ProfileErrorBoundaryProps extends Omit<ErrorBoundaryProps, 'errorBoundaryName'>
  profileSection?: string,
  userId?: string,
}
export const ProfileErrorBoundary: React.FC<ProfileErrorBoundaryProps> = ({
  children,
  profileSection = 'unknown',
  userId,
  onError,
  ...props;
}) => {
  const handleProfileError = (error: Error, errorInfo: ErrorInfo) => {
  // Enhanced logging for profile-specific errors;
    const profileContext = {
      profileSection,
      userId,
      userAgent: navigator?.userAgent,
      timestamp: new Date().toISOString(),
      errorType: error.name,
      errorMessage: error.message,
      componentStack: errorInfo.componentStack,
    }
    logger.error(
      `Profile error in ${profileSection}`,
      'ProfileErrorBoundary.handleProfileError',
      profileContext;
    );

    // Report to analytics/monitoring service;
    if (typeof window !== 'undefined' && window.analytics) {
      window.analytics.track('Profile Error', {
        section: profileSection,
        error: error.message,
        userId,
        errorId: errorInfo.errorBoundary,
      });
    }
    // Call the original onError callback if provided;
    if (onError) {
      onError(error, errorInfo);
    }
  }
  return (
    <BaseErrorBoundary
      {...props}
      errorBoundaryName={`ProfileErrorBoundary-${profileSection}`}
      fallback={ProfileErrorFallback} onError={handleProfileError} maxRetries={2} // Profile errors get fewer retries;
      showErrorDetails={process.env.NODE_ENV === 'development'} // Show details in development;
    >
      {children}
    </BaseErrorBoundary>
  );
}