/**;
 * Global Error Boundary Component;
 *;
 * Provides the highest level of error protection for the entire application.;
 * Catches all unhandled React component errors and provides recovery mechanisms.;
 */;

import React, { Component, ReactNode } from 'react';
import { useTheme } from '@design-system';
import { View, Text, TouchableOpacity, ScrollView, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { errorBoundaryManager } from '@core/services/ErrorBoundaryManager';
import { ErrorBoundaryState, ErrorInfo, EnhancedErrorDetails, ErrorBoundaryProps } from '@core/interfaces/IErrorBoundary';
import { logger } from '@services/loggerService';
;

/**;
 * Global Error Boundary Props;
 */;
interface GlobalErrorBoundaryProps extends ErrorBoundaryProps {
  children: ReactNode,
  enableDeveloperMode?: boolean,
  enableErrorReporting?: boolean,
  enableAutoRecovery?: boolean,
  maxRetries?: number,
  theme?: any; // Add theme as optional prop,
}
/**;
 * Global Error Boundary State;
 */;
interface GlobalErrorBoundaryState extends ErrorBoundaryState {
  showDetails: boolean,
  autoRecoveryAttempted: boolean,
  userDismissed: boolean,
}
/**;
 * Global Error Boundary Component;
 */;
export class GlobalErrorBoundary extends Component<;
  GlobalErrorBoundaryProps,
  GlobalErrorBoundaryState;
>
  private retryTimeoutId?: NodeJS.Timeout,
  private autoRecoveryTimeoutId?: NodeJS.Timeout,
  constructor(props: GlobalErrorBoundaryProps) {
    super(props);

    this.state = {
      hasError: false,
      error: undefined,
      errorInfo: undefined,
      errorDetails: undefined,
      retryCount: 0,
      lastErrorTime: 0,
      isRecovering: false,
      showDetails: false,
      autoRecoveryAttempted: false,
      userDismissed: false,
    }
    // Register this boundary with the error manager;
    errorBoundaryManager.registerBoundary('global', {
      name: 'Global Error Boundary',
      level: 'global',
      recovery: {
        enableRetry: true,
        maxRetries: props.maxRetries || 3,
        retryDelay: 3000,
        showErrorDetails: props.enableDeveloperMode || process.env.NODE_ENV === 'development',
        enableReporting: props.enableErrorReporting !== false,
        autoRecover: props.enableAutoRecovery !== false,
        autoRecoverDelay: 5000,
      },
      onError: this.handleErrorReport.bind(this),
      onRecover: this.handleRecovery.bind(this),
      onRetry: this.handleRetry.bind(this),
    });
  }
  componentWillUnmount() {
    // Clean up timeouts;
    if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId);
    }
    if (this.autoRecoveryTimeoutId) {
      clearTimeout(this.autoRecoveryTimeoutId);
    }
    // Unregister boundary;
    errorBoundaryManager.unregisterBoundary('global');
  }
  /**;
   * React Error Boundary lifecycle method;
   */;
  static getDerivedStateFromError(error: Error): Partial<GlobalErrorBoundaryState>
    return {
      hasError: true,
      error,
      lastErrorTime: Date.now(),
      userDismissed: false,
    }
  }
  /**;
   * React Error Boundary lifecycle method;
   */;
  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const { enableErrorReporting = true } = this.props;

    // Enhanced error details;
    const errorDetails: EnhancedErrorDetails = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      errorBoundary: 'global',
      timestamp: new Date().toISOString(),
      errorId: this.generateErrorId(),
      severity: 'critical',
      category: 'ui',
      recoverable: true,
      retryCount: this.state.retryCount,
      route: this.getCurrentRoute(),
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'React Native',
      buildVersion: process.env.EXPO_PUBLIC_APP_VERSION || 'unknown',
    }
    // Update state with error details;
    this.setState({
      errorInfo,
      errorDetails,
    });

    // Report error to manager;
    if (enableErrorReporting) {
      errorBoundaryManager.reportError('global', error, errorInfo, {
        route: this.getCurrentRoute(),
        retryCount: this.state.retryCount,
      });
    }
    // Log error locally;
    logger.error('Global Error Boundary caught error', 'GlobalErrorBoundary', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      errorId: errorDetails.errorId,
    });

    // Attempt auto-recovery if enabled;
    if (this.props.enableAutoRecovery !== false && !this.state.autoRecoveryAttempted) {
      this.attemptAutoRecovery();
    }
  }
  /**;
   * Handle error reporting callback;
   */;
  private handleErrorReport(
    error: Error,
    errorInfo: ErrorInfo,
    errorDetails: EnhancedErrorDetails,
  ) {
    logger.info('Error reported to global boundary', 'GlobalErrorBoundary', {
      errorId: errorDetails.errorId,
      category: errorDetails.category,
      severity: errorDetails.severity,
    });
  }
  /**;
   * Handle recovery callback;
   */;
  private handleRecovery(errorDetails: EnhancedErrorDetails) {
    logger.info('Global boundary recovery successful', 'GlobalErrorBoundary', {
      errorId: errorDetails.errorId,
    });

    this.setState({
      hasError: false,
      error: undefined,
      errorInfo: undefined,
      errorDetails: undefined,
      isRecovering: false,
      showDetails: false,
      autoRecoveryAttempted: false,
      userDismissed: false,
    });
  }
  /**;
   * Handle retry callback;
   */;
  private handleRetry(retryCount: number, errorDetails: EnhancedErrorDetails) {
    logger.info('Global boundary retry attempt', 'GlobalErrorBoundary', {
      errorId: errorDetails.errorId,
      retryCount,
    });
  }
  /**;
   * Attempt automatic recovery;
   */;
  private attemptAutoRecovery() {
    this.setState({ isRecovering: true, autoRecoveryAttempted: true });

    this.autoRecoveryTimeoutId = setTimeout(() => {
  const recoveryService = errorBoundaryManager.getRecoveryService();

      if (this.state.error && this.state.errorInfo) {
        recoveryService.recover(this.state.error, this.state.errorInfo);
          .then(recovered => {
  if (recovered) {
              this.handleRecovery(this.state.errorDetails!);
            } else {
              this.setState({ isRecovering: false });
            }
          });
          .catch(recoveryError => {
  logger.error('Auto-recovery failed', 'GlobalErrorBoundary', { recoveryError });
            this.setState({ isRecovering: false });
          });
      }
    }, 5000);
  }
  /**;
   * Manual retry handler;
   */;
  private handleRetryClick = () => {
  const { maxRetries = 3 } = this.props;

    if (this.state.retryCount >= maxRetries) {
      Alert.alert(
        'Maximum Retries Reached',
        'The app has reached the maximum number of retry attempts. Please restart the app.',
        [
          { text: 'Restart App', onPress: this.handleRestartApp },
          { text: 'Go Home', onPress: this.handleGoHome },
        ];
      );
      return null;
    }
    this.setState({
      retryCount: this.state.retryCount + 1,
      isRecovering: true,
    });

    // Report retry attempt;
    if (this.state.errorDetails) {
      errorBoundaryManager.getReporter();
        .reportRetry(this.state.errorDetails, this.state.retryCount + 1);
    }
    // Attempt recovery;
    this.retryTimeoutId = setTimeout(() => {
  this.setState({
        hasError: false,
        error: undefined,
        errorInfo: undefined,
        errorDetails: undefined,
        isRecovering: false,
        showDetails: false,
        userDismissed: false,
      });
    }, 2000);
  }
  /**;
   * Dismiss error handler;
   */;
  private handleDismiss = () => {
  this.setState({ userDismissed: true });
  }
  /**;
   * Show/hide error details;
   */;
  private toggleDetails = () => {
  this.setState({ showDetails: !this.state.showDetails });
  }
  /**;
   * Report issue handler;
   */;
  private handleReportIssue = () => {
  if (this.state.errorDetails) {
      // Track user action;
      errorBoundaryManager.getAnalytics().trackUserAction('report_issue', this.state.errorDetails);

      Alert.alert(
        'Report Issue',
        'Thank you for reporting this issue. Our team will investigate and work on a fix.',
        [{ text: 'OK' }];
      );
    }
  }
  /**;
   * Restart app handler;
   */;
  private handleRestartApp = () => {
  // In a real app, you might use a library like react-native-restart;
    if (typeof window !== 'undefined') {
      window.location.reload();
    } else {
      // React Native - restart not available, navigate to home instead;
      this.handleGoHome();
    }
  }
  /**;
   * Go to home handler;
   */;
  private handleGoHome = () => {
  try {
      router.replace('/');
      this.setState({
        hasError: false,
        error: undefined,
        errorInfo: undefined,
        errorDetails: undefined,
        isRecovering: false,
        showDetails: false,
        userDismissed: false,
      });
    } catch (navigationError) {
      logger.error('Failed to navigate home from error boundary', 'GlobalErrorBoundary', {
        navigationError,
      });
      this.handleRestartApp();
    }
  }
  /**;
   * Get current route;
   */;
  private getCurrentRoute(): string {
    try {
      if (typeof window !== 'undefined') {
        return window.location.pathname || '/';
      } else {
        // React Native - return a default route;
        return '/';
      }
    } catch {
      return '/';
    }
  }
  /**;
   * Generate error ID;
   */;
  private generateErrorId(): string {
    return `global_error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  /**;
   * Render error UI;
   */;
  private renderErrorUI() {
    const { error, errorDetails, isRecovering, showDetails, retryCount, userDismissed } =;
      this.state;
    const { maxRetries = 3, enableDeveloperMode = process.env.NODE_ENV === 'development', theme = {
      colors: {
        surface: '#ffffff',
        error: '#ef4444',
        primary: '#0ea5e9',
        success: '#10b981',
        warning: '#f59e0b',
        background: '#ffffff',
        border: '#e5e7eb',
      }
    } } =;
      this.props;

    if (userDismissed) {
      return null;
    }
    const recoveryService = errorBoundaryManager.getRecoveryService();
    const classifier = errorBoundaryManager.getClassifier();

    const suggestions = error ? classifier.getSuggestions(error) : [],
    const recoveryMessage = error ? recoveryService.getRecoveryMessage(error) : '',
    const canRetry = retryCount < maxRetries;

    return (
    <SafeAreaView style={[ flex: 1, backgroundColor: theme.colors.surface ]}>
        <ScrollView
          style={ flex: 1 }
          contentContainerStyle={
            padding: 20,
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: '100%',
          }
      >
          {/* Error Icon */}
          <View
            style={[
              width: 80,
              height: 80,
              borderRadius: 40,
              backgroundColor: '#fee2e2',
              alignItems: 'center',
              justifyContent: 'center',
              marginBottom: 24,
            ]}
      >
            <Text style={[ fontSize: 40, color: theme.colors.error ]}>⚠️</Text>
          </View>
          {/* Error Title */}
          <Text
            style={[
              fontSize: 24,
              fontWeight: 'bold',
              color: '#1f2937',
              textAlign: 'center',
              marginBottom: 12,
            ]}
      >
            Something went wrong;
          </Text>
          {/* Error Message */}
          <Text
            style={[
              fontSize: 16,
              color: '#6b7280',
              textAlign: 'center',
              marginBottom: 24,
              lineHeight: 24,
            ]}
      >
            {isRecovering;
              ? recoveryMessage;
              : "We're sorry, but something unexpected happened. Don't worry, we're working to fix it."}
          </Text>
          {/* Recovery Status */}
          {isRecovering && (
            <View
              style={[
                backgroundColor: '#fef3c7',
                padding: 16,
                borderRadius: 8,
                marginBottom: 24,
                width: '100%',
              ]}
      >
              <Text style={[ color: '#92400e', textAlign: 'center', fontWeight: '500' ]}>
                🔄 Attempting to recover...;
              </Text>
            </View>
          )}
          {/* Suggestions */}
          {suggestions.length > 0 && (
            <View style={[ width: '100%', marginBottom: 24 ]}>
              <Text
                style={[
                  fontSize: 16,
                  fontWeight: '600',
                  color: '#374151',
                  marginBottom: 12,
                ]}
      >
                What you can try: ,
              </Text>
              {suggestions.map((suggestion, index) => (
                <Text key={index} style={[
                    fontSize: 14,
                    color: '#6b7280',
                    marginBottom: 8,
                    paddingLeft: 16,
                  ]}
      >
                  • {suggestion}
                </Text>
              ))}
            </View>
          )}
          {/* Action Buttons */}
          <View style={[ width: '100%', gap: 12 ]}>
            {canRetry && !isRecovering && (
              <TouchableOpacity onPress={this.handleRetryClick} style={[
                  backgroundColor: theme.colors.primary,
                  padding: 16,
                  borderRadius: 8,
                  alignItems: 'center',
                ]}
      >
                <Text style={[ color: theme.colors.background, fontSize: 16, fontWeight: '600' ]}>
                  Try Again ({maxRetries - retryCount} attempts left);
                </Text>
              </TouchableOpacity>
            )}
            <TouchableOpacity onPress={this.handleGoHome} style={[
                backgroundColor: theme.colors.success,
                padding: 16,
                borderRadius: 8,
                alignItems: 'center',
              ]}
      >
              <Text style={[ color: theme.colors.background, fontSize: 16, fontWeight: '600' ]}>Go to Home</Text>
            </TouchableOpacity>
            <TouchableOpacity onPress={this.handleReportIssue} style={[
                backgroundColor: theme.colors.warning,
                padding: 16,
                borderRadius: 8,
                alignItems: 'center',
              ]}
      >
              <Text style={[ color: theme.colors.background, fontSize: 16, fontWeight: '600' ]}>Report Issue</Text>
            </TouchableOpacity>
            {enableDeveloperMode && (
              <TouchableOpacity onPress={this.toggleDetails} style={[
                  backgroundColor: '#6b7280',
                  padding: 12,
                  borderRadius: 8,
                  alignItems: 'center',
                ]}
      >
                <Text style={[ color: theme.colors.background, fontSize: 14 ]}>
                  {showDetails ? 'Hide' : 'Show'} Technical Details;
                </Text>
              </TouchableOpacity>
            )}
            <TouchableOpacity onPress={this.handleDismiss} style={[
                backgroundColor: 'transparent',
                padding: 12,
                borderRadius: 8,
                alignItems: 'center',
                borderWidth: 1,
                borderColor: theme.colors.border,
              ]}
      >
              <Text style={[ color: '#6b7280', fontSize: 14 ]}>Dismiss</Text>
            </TouchableOpacity>
          </View>
          {/* Technical Details */}
          {showDetails && enableDeveloperMode && errorDetails && (
            <View
              style={[
                width: '100%',
                marginTop: 24,
                padding: 16,
                backgroundColor: '#f3f4f6',
                borderRadius: 8,
              ]}
      >
              <Text style={[ fontSize: 14, fontWeight: '600', marginBottom: 8 ]}>
                Technical Details: ,
              </Text>
              <Text style={[ fontSize: 12, color: '#374151', fontFamily: 'monospace' ]}>
                Error ID: {errorDetails.errorId}
                {'\n'}
                Message: {errorDetails.message}
                {'\n'}
                Category: {errorDetails.category}
                {'\n'}
                Severity: {errorDetails.severity}
                {'\n'}
                Timestamp: {errorDetails.timestamp}
                {'\n'}
                Route: {errorDetails.route}
                {errorDetails.stack && `\n\nStack Trace:\n${errorDetails.stack}`}
              </Text>
            </View>
          )}
        </ScrollView>
      </SafeAreaView>
    );
  }
  render() {
    if (this.state.hasError) {
      // Custom fallback UI;
      if (this.props.fallback) {
        if (typeof this.props.fallback === 'function') {
          return this.props.fallback(this.state.error!, this.handleRetryClick);
        }
        return this.props.fallback;
      }
      // Default error UI;
      return this.renderErrorUI();
    }
    return this.props.children;
  }
}
// Wrapper component that provides theme to the class component;
const GlobalErrorBoundaryWithTheme: React.FC<Omit<GlobalErrorBoundaryProps, 'theme'>> = (props) => {
  const theme = useTheme();
  return <GlobalErrorBoundary {...props} theme={{theme} /}>
}
export default GlobalErrorBoundaryWithTheme;
