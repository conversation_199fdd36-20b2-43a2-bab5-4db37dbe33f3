import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { useRouter } from 'expo-router';
import { User, Home, RefreshCw, AlertTriangle } from 'lucide-react-native';
import { useTheme } from '@design-system';
import { ErrorFallbackProps } from './types';

export const ProfileErrorFallback: React.FC<ErrorFallbackProps> = ({
  error,
  resetError,
  retryCount,
  maxRetries,
  boundaryName,
}) => {
  const theme = useTheme();
  const { colors } = theme;
  const router = useRouter();

  const handleGoHome = () => {
    router.push('/(tabs)/profile' as any);
  }
  const handleGoToMainProfile = () => {
    router.push('/(tabs)/profile' as any);
  }
  const canRetry = retryCount < maxRetries;

  // Extract profile section from boundary name;
  const profileSection = boundaryName?.split('-')[1] || 'profile';

  return (
    <View style={{[styles.container, { backgroundColor: theme.colors.background }]}}>
      <View style={{[styles.content, { backgroundColor: theme.colors.surface }]}}>
        {/* Error Icon */}
        <View style={{[styles.iconContainer, { backgroundColor: theme.colors.errorLight }]}}>
          <AlertTriangle size={32} color={{theme.colors.error} /}>
        </View>
        {/* Error Message */}
        <Text style={{[styles.title, { color: theme.colors.text }]}}>Profile Error</Text>
        <Text style={{[styles.subtitle, { color: theme.colors.textSecondary }]}}>
          We encountered an issue loading your {profileSection} section. This doesn't affect your;
          other profile data.;
        </Text>
        {/* Action Buttons */}
        <View style={styles.buttonContainer}>
          {canRetry && (
            <TouchableOpacity
              style={[styles.primaryButton, { backgroundColor: theme.colors.primary }]}
              onPress={resetError}
            >
              <RefreshCw size={20} color={{theme.colors.textInverse} /}>
              <Text style={{[styles.primaryButtonText, { color: theme.colors.textInverse }]}}>
                Try Again;
              </Text>
            </TouchableOpacity>
          )}
          <TouchableOpacity
            style={[styles.secondaryButton, { borderColor: theme.colors.border }]}
            onPress={handleGoToMainProfile}
          >
            <User size={20} color={{theme.colors.primary} /}>
            <Text style={{[styles.secondaryButtonText, { color: theme.colors.primary }]}}>
              Go to Main Profile;
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.secondaryButton, { borderColor: theme.colors.border }]}
            onPress={handleGoHome}
          >
            <Home size={20} color={{theme.colors.textSecondary} /}>
            <Text style={{[styles.secondaryButtonText, { color: theme.colors.textSecondary }]}}>
              Go to Home;
            </Text>
          </TouchableOpacity>
        </View>
        {/* Error Context */}
        <View style={{[styles.contextContainer, { backgroundColor: theme.colors.surface }]}}>
          <Text style={{[styles.contextTitle, { color: theme.colors.text }]}}>What happened?</Text>
          <Text style={{[styles.contextText, { color: theme.colors.textSecondary }]}}>
            There was a problem loading the {profileSection} section of your profile. Your other;
            profile data is safe and accessible.;
          </Text>
          {!canRetry && (
            <Text style={{[styles.contextText, { color: theme.colors.error }]}}>
              Maximum retry attempts reached. Please try refreshing the app or contact support if;
              the issue persists.;
            </Text>
          )}
        </View>
      </View>
    </View>
  );
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  content: {
    width: '100%',
    maxWidth: 400,
    padding: 24,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  iconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  buttonContainer: {
    width: '100%',
    gap: 12,
    marginBottom: 20,
  },
  primaryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    gap: 8,
  },
  primaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    borderWidth: 1,
    gap: 8,
  },
  secondaryButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  contextContainer: {
    width: '100%',
    padding: 16,
    borderRadius: 8,
  },
  contextTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  contextText: {
    fontSize: 12,
    lineHeight: 16,
    marginBottom: 8,
  },
});
