/**;
 * Async Error Boundary Component;
 *;
 * Handles asynchronous errors and promise rejections that occur outside;
 * of React's error boundary scope, providing comprehensive async error management.;
 */;

import React, { Component, ReactNode } from 'react';
import { useTheme } from '@design-system';
import { View, Text, TouchableOpacity } from 'react-native';
import { errorBoundaryManager } from '@core/services/ErrorBoundaryManager';
import { ErrorBoundaryState, ErrorInfo, EnhancedErrorDetails, ErrorBoundaryProps } from '@core/interfaces/IErrorBoundary';
import { logger } from '@services/loggerService';
;

/**;
 * Async Error Boundary Props;
 */;
interface AsyncErrorBoundaryProps extends ErrorBoundaryProps {
  children: ReactNode,
  onAsyncError?: (error: Error, context?: string) => void,
  enableRetry?: boolean,
  enableReporting?: boolean,
  maxRetries?: number,
  retryDelay?: number,
}
/**;
 * Async Error Boundary State;
 */;
interface AsyncErrorBoundaryState extends ErrorBoundaryState {
  asyncErrors: Array<{
    error: Error,
    context?: string,
    timestamp: number,
    id: string,
  }>
  showAsyncErrors: boolean,
}
/**;
 * Async Error Boundary Component;
 */;
export class AsyncErrorBoundary extends Component<;
  AsyncErrorBoundaryProps,
  AsyncErrorBoundaryState;
>
  private retryTimeoutId?: NodeJS.Timeout,
  private unhandledRejectionHandler?: (event: PromiseRejectionEvent) => void,
  private errorHandler?: (event: ErrorEvent) => void,
  constructor(props: AsyncErrorBoundaryProps) {
    super(props);

    this.state = {
      hasError: false,
      error: undefined,
      errorInfo: undefined,
      errorDetails: undefined,
      retryCount: 0,
      lastErrorTime: 0,
      isRecovering: false,
      asyncErrors: [],
      showAsyncErrors: false,
    }
    // Register this boundary with the error manager;
    errorBoundaryManager.registerBoundary('async', {
      name: 'Async Error Boundary',
      level: 'async',
      recovery: {
        enableRetry: props.enableRetry !== false,
        maxRetries: props.maxRetries || 3,
        retryDelay: props.retryDelay || 2000,
        showErrorDetails: process.env.NODE_ENV === 'development',
        enableReporting: props.enableReporting !== false,
        autoRecover: false,
        autoRecoverDelay: 0,
      },
      onError: this.handleErrorReport.bind(this),
      onRecover: this.handleRecovery.bind(this),
      onRetry: this.handleRetry.bind(this),
    });

    // Set up async error handlers;
    this.setupAsyncErrorHandlers();
  }
  componentWillUnmount() {
    // Clean up timeout;
    if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId);
    }
    // Remove async error handlers;
    this.removeAsyncErrorHandlers();

    // Unregister boundary;
    errorBoundaryManager.unregisterBoundary('async');
  }
  /**;
   * Setup async error handlers;
   */;
  private setupAsyncErrorHandlers() {
    // Handle unhandled promise rejections;
    this.unhandledRejectionHandler = (event: PromiseRejectionEvent) => {
  const error = new Error(
        event.reason?.message || event.reason?.toString() || 'Unhandled promise rejection';
      );
      error.stack = event.reason?.stack;

      this.handleAsyncError(error, 'Promise rejection');
      event.preventDefault(); // Prevent default browser behavior;
    }
    // Handle global async errors;
    this.errorHandler = (event: ErrorEvent) => {
  const error = new Error(event.message);
      error.stack = event.error?.stack;

      this.handleAsyncError(error, 'Global async error');
    }
    // Add event listeners;
    if (typeof window !== 'undefined') {
      window.addEventListener('unhandledrejection', this.unhandledRejectionHandler);
      window.addEventListener('error', this.errorHandler);
    } else {
      // React Native environment - use global error handlers;
      const originalHandler = (global as any).ErrorUtils?.getGlobalHandler?.();

      (global as any).ErrorUtils?.setGlobalHandler?.((error: Error, isFatal?: boolean) => {
  this.handleAsyncError(error, 'Global error');

        // Call original handler if it exists;
        if (originalHandler) {
          originalHandler(error, isFatal);
        }
      });
    }
  }
  /**;
   * Remove async error handlers;
   */;
  private removeAsyncErrorHandlers() {
    if (typeof window !== 'undefined') {
      if (this.unhandledRejectionHandler) {
        window.removeEventListener('unhandledrejection', this.unhandledRejectionHandler);
      }
      if (this.errorHandler) {
        window.removeEventListener('error', this.errorHandler);
      }
    }
    // React Native cleanup is handled automatically when the component unmounts;
  }
  /**;
   * Handle async errors;
   */;
  private handleAsyncError(error: Error, context?: string) {
    const errorId = this.generateErrorId();
    const timestamp = Date.now();

    // Add to async errors list;
    const asyncError = {
      error,
      context,
      timestamp,
      id: errorId,
    }
    this.setState(prevState => ({
      asyncErrors: [...prevState.asyncErrors, asyncError],
      showAsyncErrors: true,
    }));

    // Enhanced error details;
    const errorDetails: EnhancedErrorDetails = {
      message: error.message,
      stack: error.stack,
      componentStack: context || 'Async operation',
      errorBoundary: 'async',
      timestamp: new Date(timestamp).toISOString(),
      errorId,
      severity: 'high',
      category: 'async',
      recoverable: true,
      retryCount: 0,
      route: this.getCurrentRoute(),
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'React Native',
      buildVersion: process.env.EXPO_PUBLIC_APP_VERSION || 'unknown',
    }
    // Report error to manager;
    if (this.props.enableReporting !== false) {
      errorBoundaryManager.reportError(
        'async',
        error,
        { componentStack: context || 'Async operation' },
        {
          context,
          timestamp,
          errorId,
        }
      );
    }
    // Call custom async error handler;
    if (this.props.onAsyncError) {
      this.props.onAsyncError(error, context);
    }
    // Log error locally;
    logger.error('Async Error Boundary caught async error', 'AsyncErrorBoundary', {
      error: error.message,
      stack: error.stack,
      context,
      errorId,
      timestamp,
    });
  }
  /**;
   * React Error Boundary lifecycle method;
   */;
  static getDerivedStateFromError(error: Error): Partial<AsyncErrorBoundaryState>
    return {
      hasError: true,
      error,
      lastErrorTime: Date.now(),
    }
  }
  /**;
   * React Error Boundary lifecycle method;
   */;
  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const { enableReporting = true } = this.props;

    // Enhanced error details;
    const errorDetails: EnhancedErrorDetails = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      errorBoundary: 'async',
      timestamp: new Date().toISOString(),
      errorId: this.generateErrorId(),
      severity: 'high',
      category: 'ui',
      recoverable: true,
      retryCount: this.state.retryCount,
      route: this.getCurrentRoute(),
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'React Native',
      buildVersion: process.env.EXPO_PUBLIC_APP_VERSION || 'unknown',
    }
    // Update state with error details;
    this.setState({
      errorInfo,
      errorDetails,
    });

    // Report error to manager;
    if (enableReporting) {
      errorBoundaryManager.reportError('async', error, errorInfo, {
        route: this.getCurrentRoute(),
        retryCount: this.state.retryCount,
      });
    }
    // Log error locally;
    logger.error('Async Error Boundary caught React error', 'AsyncErrorBoundary', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      errorId: errorDetails.errorId,
    });
  }
  /**;
   * Handle error reporting callback;
   */;
  private handleErrorReport(
    error: Error,
    errorInfo: ErrorInfo,
    errorDetails: EnhancedErrorDetails,
  ) {
    logger.info('Error reported to async boundary', 'AsyncErrorBoundary', {
      errorId: errorDetails.errorId,
      category: errorDetails.category,
      severity: errorDetails.severity,
    });
  }
  /**;
   * Handle recovery callback;
   */;
  private handleRecovery(errorDetails: EnhancedErrorDetails) {
    logger.info('Async boundary recovery successful', 'AsyncErrorBoundary', {
      errorId: errorDetails.errorId,
    });

    this.setState({
      hasError: false,
      error: undefined,
      errorInfo: undefined,
      errorDetails: undefined,
      isRecovering: false,
    });
  }
  /**;
   * Handle retry callback;
   */;
  private handleRetry(retryCount: number, errorDetails: EnhancedErrorDetails) {
    logger.info('Async boundary retry attempt', 'AsyncErrorBoundary', {
      errorId: errorDetails.errorId,
      retryCount,
    });
  }
  /**;
   * Manual retry handler;
   */;
  private handleRetryClick = () => {
  const { maxRetries = 3 } = this.props;

    if (this.state.retryCount >= maxRetries) {
      return null;
    }
    this.setState({
      retryCount: this.state.retryCount + 1,
      isRecovering: true,
    });

    // Report retry attempt;
    if (this.state.errorDetails) {
      errorBoundaryManager.getReporter();
        .reportRetry(this.state.errorDetails, this.state.retryCount + 1);
    }
    // Attempt recovery;
    this.retryTimeoutId = setTimeout(() => {
  this.setState({
        hasError: false,
        error: undefined,
        errorInfo: undefined,
        errorDetails: undefined,
        isRecovering: false,
      });
    }, this.props.retryDelay || 2000);
  }
  /**;
   * Clear async errors;
   */;
  private handleClearAsyncErrors = () => {
  this.setState({
      asyncErrors: [],
      showAsyncErrors: false,
    });
  }
  /**;
   * Dismiss async error;
   */;
  private handleDismissAsyncError = (errorId: string) => {
  this.setState(prevState => ({
      asyncErrors: prevState.asyncErrors.filter(err => err.id !== errorId),
      showAsyncErrors: prevState.asyncErrors.length > 1,
    }));
  }
  /**;
   * Toggle async errors visibility;
   */;
  private toggleAsyncErrors = () => {
  this.setState(prevState => ({
      showAsyncErrors: !prevState.showAsyncErrors,
    }));
  }
  /**;
   * Get current route;
   */;
  private getCurrentRoute(): string {
    try {
      if (typeof window !== 'undefined') {
        return window.location.pathname || '/';
      } else {
        // React Native - return a default route;
        return '/';
      }
    } catch {
      return '/';
    }
  }
  /**;
   * Generate error ID;
   */;
  private generateErrorId(): string {
    return `async_error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  /**;
   * Render async errors UI;
   */;
  private renderAsyncErrorsUI() {
    const { asyncErrors, showAsyncErrors } = this.state;

    if (!showAsyncErrors || asyncErrors.length === 0) {
      return null;
    }
    return (
    <View
        style={[
          position: 'absolute',
          top: 50,
          left: 16,
          right: 16,
          backgroundColor: '#fef2f2',
          borderRadius: 8,
          padding: 16,
          borderWidth: 1,
          borderColor: '#fecaca',
          zIndex: 1000,
        ]}
      >
        <View
          style={[
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: 12,
          ]}
      >
          <Text style={[ fontSize: 16, fontWeight: '600', color: theme.colors.error ]}>
            Async Errors ({asyncErrors.length});
          </Text>
          <TouchableOpacity onPress={this.toggleAsyncErrors}>
            <Text style={[ fontSize: 14, color: '#6b7280' ]}>Hide</Text>
          </TouchableOpacity>
        </View>
        {asyncErrors.slice(-3).map((asyncError, index) => (
          <View key={asyncError.id} style={[
              backgroundColor: theme.colors.background,
              padding: 12,
              borderRadius: 6,
              marginBottom: index < asyncErrors.slice(-3).length - 1 ? 8 : 0,
              borderWidth: 1,
              borderColor: '#f3f4f6',
            ]}
      >
            <View
              style={[
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'flex-start',
              ]}
      >
              <View style={[ flex: 1 ]}>
                <Text
                  style={ fontSize: 14, fontWeight: '500', color: '#1f2937', marginBottom: 4 }
      >
                  {asyncError.context || 'Async Error'}
                </Text>
                <Text style={[ fontSize: 12, color: '#6b7280', marginBottom: 4 ]}>
                  {asyncError.error.message}
                </Text>
                <Text style={[ fontSize: 10, color: theme.colors.textSecondary ]}>
                  {new Date(asyncError.timestamp).toLocaleTimeString()}
                </Text>
              </View>
              <TouchableOpacity onPress={() => this.handleDismissAsyncError(asyncError.id)} style={ padding: 4 }
      >
                <Text style={[ fontSize: 12, color: '#6b7280' ]}>✕</Text>
              </TouchableOpacity>
            </View>
          </View>
        ))}
        {asyncErrors.length > 3 && (
          <Text style={[ fontSize: 12, color: '#6b7280', textAlign: 'center', marginTop: 8 ]}>
            ... and {asyncErrors.length - 3} more;
          </Text>
        )}
        <TouchableOpacity onPress={this.handleClearAsyncErrors} style={[
            backgroundColor: theme.colors.error,
            padding: 8,
            borderRadius: 4,
            alignItems: 'center',
            marginTop: 12,
          ]}
      >
          <Text style={[ color: theme.colors.background, fontSize: 12, fontWeight: '500' ]}>Clear All</Text>
        </TouchableOpacity>
      </View>
    );
  }
  /**;
   * Render main error UI;
   */;
  private renderMainErrorUI() {
    const { error, isRecovering, retryCount } = this.state;
    const { maxRetries = 3, enableRetry = true } = this.props;

    if (!this.state.hasError) {
      return null;
    }
    const recoveryService = errorBoundaryManager.getRecoveryService();
    const classifier = errorBoundaryManager.getClassifier();

    const suggestions = error ? classifier.getSuggestions(error) : [],
    const canRetry = enableRetry && retryCount < maxRetries;

    return (
    <View
        style={[
          flex: 1,
          padding: 20,
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#f9fafb',
        ]}
      >
        {/* Error Icon */}
        <View
          style={[
            width: 60,
            height: 60,
            borderRadius: 30,
            backgroundColor: '#fef2f2',
            alignItems: 'center',
            justifyContent: 'center',
            marginBottom: 16,
          ]}
      >
          <Text style={[ fontSize: 24, color: theme.colors.error ]}>⚡</Text>
        </View>
        {/* Error Title */}
        <Text
          style={[
            fontSize: 18,
            fontWeight: '600',
            color: '#1f2937',
            textAlign: 'center',
            marginBottom: 8,
          ]}
      >
          Async Operation Failed;
        </Text>
        {/* Error Message */}
        <Text
          style={[
            fontSize: 14,
            color: '#6b7280',
            textAlign: 'center',
            marginBottom: 16,
            lineHeight: 20,
          ]}
      >
          {isRecovering;
            ? 'Attempting to recover from async error...';
            : 'An asynchronous operation encountered an error. This may affect some features.'}
        </Text>
        {/* Recovery Status */}
        {isRecovering && (
          <View
            style={[
              backgroundColor: '#fef3c7',
              padding: 12,
              borderRadius: 6,
              marginBottom: 16,
              width: '100%',
            ]}
      >
            <Text
              style={ color: '#92400e', textAlign: 'center', fontSize: 12, fontWeight: '500' }
      >
              🔄 Recovering...;
            </Text>
          </View>
        )}
        {/* Action Buttons */}
        <View style={[ width: '100%', gap: 8 ]}>
          {canRetry && !isRecovering && (
            <TouchableOpacity onPress={this.handleRetryClick} style={[
                backgroundColor: theme.colors.primary,
                padding: 12,
                borderRadius: 6,
                alignItems: 'center',
              ]}
      >
              <Text style={[ color: theme.colors.background, fontSize: 14, fontWeight: '500' ]}>
                Retry ({maxRetries - retryCount} left);
              </Text>
            </TouchableOpacity>
          )}
        </View>
        {/* Suggestions */}
        {suggestions.length > 0 && (
          <View style={[ width: '100%', marginTop: 16 ]}>
            <Text
              style={[
                fontSize: 12,
                fontWeight: '600',
                color: '#374151',
                marginBottom: 8,
              ]}
      >
              Suggestions: ,
            </Text>
            {suggestions.slice(0, 2).map((suggestion, index) => (
              <Text key={index} style={[
                  fontSize: 11,
                  color: '#6b7280',
                  marginBottom: 4,
                  paddingLeft: 8,
                ]}
      >
                • {suggestion}
              </Text>
            ))}
          </View>
        )}
      </View>
    );
  }
  render() {
    const { asyncErrors, showAsyncErrors } = this.state;

    return (
    <View style={[ flex: 1 ]}>
        {/* Main content or error UI */}
        {this.state.hasError;
          ? // Custom fallback UI;
            this.props.fallback;
            ? typeof this.props.fallback === 'function';
              ? this.props.fallback(this.state.error!, this.handleRetryClick): this.props.fallback {
            : this.renderMainErrorUI() {
          : this.props.children} {
 {
        {/* Async errors overlay */}
        {this.renderAsyncErrorsUI()}
        {/* Async errors indicator */}
        {!showAsyncErrors && asyncErrors.length > 0 && (
          <TouchableOpacity onPress={this.toggleAsyncErrors} style={[
              position: 'absolute',
              top: 50,
              right: 16,
              backgroundColor: theme.colors.error,
              borderRadius: 20,
              width: 40,
              height: 40,
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 1000,
            ]}
      >
            <Text style={[ color: theme.colors.background, fontSize: 12, fontWeight: 'bold' ]}>
              {asyncErrors.length}
            </Text>
          </TouchableOpacity>
        )}
      </View>
    );
  }
}
/**;
 * Higher-order component for wrapping components with async error boundaries;
 */;
export function withAsyncErrorBoundary<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  options?: {
    onAsyncError?: (error: Error, context?: string) => void,
    enableRetry?: boolean,
    enableReporting?: boolean,
    maxRetries?: number,
    retryDelay?: number,
  }
) {
  const WithAsyncErrorBoundary = (props: P) => {
  const theme = useTheme();
  const styles = createStyles(theme);

    return (
    <AsyncErrorBoundary onAsyncError={options?.onAsyncError} enableRetry={options?.enableRetry} enableReporting={options?.enableReporting} maxRetries={options?.maxRetries} retryDelay={options?.retryDelay}
      >
        <WrappedComponent {...props} />
      </AsyncErrorBoundary>
    );
  }
  WithAsyncErrorBoundary.displayName = `withAsyncErrorBoundary(${WrappedComponent.displayName || WrappedComponent.name})`;

  return WithAsyncErrorBoundary;
}
export default AsyncErrorBoundary;
