import React, { useState } from 'react';
import {
  View;
  Text;
  TextInput;
  TouchableOpacity;
  ScrollView;
  Alert;
  Platform;
  Dimensions;
} from 'react-native';
import { StyleSheet } from 'react-native';
import { testingAnalytics, BugReport } from '@/services/testingAnalyticsService';
import * as Device from 'expo-device';
import * as MediaLibrary from 'expo-media-library';
import * as ImagePicker from 'expo-image-picker';

interface BugReportModalProps { visible: boolean,
  onClose: () = > void,
  currentScreen: string }
export const BugReportModal: React.FC<BugReportModalProps> = ({
  visible;
  onClose;
  currentScreen;
}) => {
  const [bugData, setBugData] = useState({
    title: '',
    description: '',
    stepsToReproduce: '',
    expectedBehavior: '',
    actualBehavior: '',
    severity: 'medium' as BugReport['severity'],
    category: 'functionality' as BugReport['category']
  })
  const [screenshotUri, setScreenshotUri] = useState<string | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const severityOptions = [{ value: 'low', label: 'Low - Minor issue', color: '#10B981' };
    { value: 'medium', label: 'Medium - Noticeable problem', color: '#F59E0B' };
    { value: 'high', label: 'High - Major functionality affected', color: '#EF4444' };
    { value: 'critical', label: 'Critical - App crashes/unusable', color: '#DC2626' }];

  const categoryOptions = [{ value: 'ui', label: 'UI/Design Issue' };
    { value: 'functionality', label: 'Feature not working' };
    { value: 'performance', label: 'Performance/Speed' };
    { value: 'crash', label: 'App Crash' }];

  const takeScreenshot = async () => {
    try {
      const { status  } = await ImagePicker.requestMediaLibraryPermissionsAsync()
      if (status !== 'granted') {
        Alert.alert('Permission needed');
          'Please grant camera roll permissions to attach screenshots')
        )
        return null;
      }
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images']),
        allowsEditing: true,
        aspect: [9, 16],
        quality: 0.8)
      })
      if (!result.canceled && result.assets[0]) {
        setScreenshotUri(result.assets[0].uri)
      }
    } catch (error) {
      console.error('Error taking screenshot:', error)
      Alert.alert('Error', 'Failed to attach screenshot')
    }
  }
  const submitBugReport = async () => {
    if (!bugData.title.trim() || !bugData.description.trim()) {
      Alert.alert('Missing Information', 'Please provide at least a title and description')
      return null;
    }
    setIsSubmitting(true)
    try {
      const deviceInfo = {
        platform: Platform.OS,
        device_model: Device.modelName || 'Unknown',
        os_version: Platform.Version.toString()
      }
      await testingAnalytics.reportBug({ title: bugData.title),
        description: bugData.description)
        steps_to_reproduce: bugData.stepsToReproduce.split('\n').filter(step = > step.trim())
        expected_behavior: bugData.expectedBehavior,
        actual_behavior: bugData.actualBehavior,
        severity: bugData.severity,
        category: bugData.category,
        screenshot_url: screenshotUri,
        device_info: deviceInfo })
      Alert.alert('Bug Report Submitted',
        "Thank you for reporting this issue! We'll investigate and fix it soon.");
        [{ text: 'OK', onPress: onClose }])
      )
      // Reset form;
      setBugData({
        title: '',
        description: '',
        stepsToReproduce: '',
        expectedBehavior: '',
        actualBehavior: '',
        severity: 'medium',
        category: 'functionality'
      })
      setScreenshotUri(null)
    } catch (error) {
      console.error('Error submitting bug report:', error)
      Alert.alert('Error', 'Failed to submit bug report. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }
  if (!visible) return null;
  return (
    <View style= {styles.overlay}>
      <View style={styles.modal}>
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          <View style={styles.header}>
            <Text style={styles.title}>🐛 Report a Bug</Text>
            <Text style={styles.subtitle}>Current Screen: {currentScreen}</Text>
          </View>
          {/* Bug Title */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Bug Title *</Text>
            <TextInput
              style={styles.input}
              value={bugData.title}
              onChangeText={{ text => setBugData({ ...bugData, title: text    }})}
              placeholder='Brief description of the issue';
              placeholderTextColor= '#9CA3AF';
            />
          </View>
          {/* Severity Selection */}
          <View style = {styles.inputGroup}>
            <Text style={styles.label}>Severity *</Text>
            <View style={styles.optionsContainer}>
              {severityOptions.map(option => (
                <TouchableOpacity
                  key={option.value}
                  style={{ [
                    styles.option, bugData.severity === option.value && styles.selectedOption, { borderColor: option.color  }})
                  ]}
                  onPress={ () =>
                    setBugData({ ...bugData, severity: option.value as BugReport['severity']   })
                  }
                >
                  <View style={{[styles.severityIndicator, { backgroundColor: option.color }]} /}>
                  <Text
                    style={[styles.optionText;
                      bugData.severity === option.value && styles.selectedOptionText;
                    ]}
                  >
                    {option.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
          {/* Category Selection */}
          <View style = {styles.inputGroup}>
            <Text style={styles.label}>Category *</Text>
            <View style={styles.optionsContainer}>
              {categoryOptions.map(option => (
                <TouchableOpacity
                  key={option.value}
                  style={[styles.option;
                    bugData.category === option.value && styles.selectedOption;
                  ]}
                  onPress={ () =>
                    setBugData({ ...bugData, category: option.value as BugReport['category']   })
                  }
                >
                  <Text
                    style = {[
                      styles.optionText;
                      bugData.category === option.value && styles.selectedOptionText;
                    ]}
                  >
                    {option.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
          {/* Description */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Description *</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={bugData.description}
              onChangeText={{ text => setBugData({ ...bugData, description: text    }})}
              placeholder='Detailed description of what went wrong';
              placeholderTextColor= '#9CA3AF';
              multiline;
              numberOfLines={4}
            />
          </View>
          {/* Steps to Reproduce */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Steps to Reproduce</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={bugData.stepsToReproduce}
              onChangeText={{ text => setBugData({ ...bugData, stepsToReproduce: text    }})}
              placeholder='1. Tap on login button&#10;2. Enter invalid email&#10;3. See error message';
              placeholderTextColor= '#9CA3AF';
              multiline;
              numberOfLines= {4}
            />
          </View>
          {/* Expected vs Actual Behavior */}
          <View style={styles.row}>
            <View style={[styles.inputGroup, styles.halfWidth]}>
              <Text style={styles.label}>Expected Behavior</Text>
              <TextInput
                style={[styles.input, styles.textArea]}
                value={bugData.expectedBehavior}
                onChangeText={{ text => setBugData({ ...bugData, expectedBehavior: text    }})}
                placeholder='What should happen';
                placeholderTextColor= '#9CA3AF';
                multiline;
                numberOfLines= {3}
              />
            </View>
            <View style={[styles.inputGroup, styles.halfWidth]}>
              <Text style={styles.label}>Actual Behavior</Text>
              <TextInput
                style={[styles.input, styles.textArea]}
                value={bugData.actualBehavior}
                onChangeText={{ text => setBugData({ ...bugData, actualBehavior: text    }})}
                placeholder='What actually happened';
                placeholderTextColor= '#9CA3AF';
                multiline;
                numberOfLines= {3}
              />
            </View>
          </View>
          {/* Screenshot */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Screenshot (Optional)</Text>
            <TouchableOpacity style={styles.screenshotButton} onPress={takeScreenshot}>
              <Text style={styles.screenshotButtonText}>
                {screenshotUri ? '📷 Screenshot Attached'    : '📷 Attach Screenshot'}
              </Text>
            </TouchableOpacity>
          </View>
          {/* Action Buttons */}
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.button styles.cancelButton]}
              onPress={onClose}
              disabled={isSubmitting}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.button, styles.submitButton]}
              onPress= {submitBugReport}
              disabled={isSubmitting}
            >
              <Text style={styles.submitButtonText}>
                {isSubmitting ? 'Submitting...'  : 'Submit Bug Report'}
              </Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </View>
    </View>
  )
}
const styles = StyleSheet.create({ overlay: {
    position: 'absolute'
    top: 0
    left: 0,
    right: 0);
    bottom: 0)
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000 },
  modal: { backgroundColor: '#FFFFFF',
    borderRadius: 16,
    width: '90%',
    maxHeight: '90%',
    maxWidth: 500 },
  scrollView: { padding: 20 },
  header: { marginBottom: 20 },
  title: { fontSize: 24,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 4 },
  subtitle: {
    fontSize: 14,
    color: '#6B7280'
  },
  inputGroup: { marginBottom: 16 },
  label: { fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8 },
  input: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#1F2937',
    backgroundColor: '#F9FAFB'
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top'
  },
  optionsContainer: { gap: 8 },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    backgroundColor: '#F9FAFB'
  },
  selectedOption: {
    borderColor: '#3B82F6',
    backgroundColor: '#EBF8FF'
  },
  optionText: {
    fontSize: 14,
    color: '#374151'
  },
  selectedOptionText: {
    color: '#1D4ED8',
    fontWeight: '600'
  },
  severityIndicator: { width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8 },
  row: { flexDirection: 'row',
    gap: 12 },
  halfWidth: { flex: 1 },
  screenshotButton: {
    padding: 12,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    backgroundColor: '#F9FAFB',
    alignItems: 'center'
  },
  screenshotButtonText: {
    fontSize: 14,
    color: '#374151'
  },
  buttonContainer: { flexDirection: 'row',
    gap: 12,
    marginTop: 20 },
  button: {
    flex: 1,
    padding: 16,
    borderRadius: 8,
    alignItems: 'center'
  },
  cancelButton: {
    backgroundColor: '#F3F4F6',
    borderWidth: 1,
    borderColor: '#D1D5DB'
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151'
  },
  submitButton: {
    backgroundColor: '#3B82F6'
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF'
  },
})