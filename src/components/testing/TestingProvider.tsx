import React, { createContext, useContext, useState, useEffect } from 'react';
import { View, TouchableOpacity, Text, StyleSheet, Alert } from 'react-native';
import { BugReportModal } from './BugReportModal';
import { FeedbackForm } from './FeedbackForm';
import { testingAnalytics } from '@/services/testingAnalyticsService';
import * as Device from 'expo-device';
import { Platform } from 'react-native';

interface TestingContextType { isTestingMode: boolean,
  currentScreen: string,
  setCurrentScreen: (screen: string) = > void,
  showBugReport: () = > void;
  showFeedback: () = > void,
  trackAction: (action: string, details?: any) => void }
const TestingContext = createContext<TestingContextType | null>(null)
export const useTestingContext = () => {
  const context = useContext(TestingContext)
  if (!context) {
    throw new Error('useTestingContext must be used within TestingProvider')
  }
  return context;
}
interface TestingProviderProps { children: React.ReactNode }
export const TestingProvider: React.FC<TestingProviderProps> = ({ children }) => {
  const [isTestingMode] = useState(true) // Always true for tunneling;
  const [currentScreen, setCurrentScreen] = useState('Unknown')
  const [showBugReportModal, setShowBugReportModal] = useState(false)
  const [showFeedbackModal, setShowFeedbackModal] = useState(false)
  const [sessionStarted, setSessionStarted] = useState(false)
  useEffect(() => {
    if (isTestingMode && !sessionStarted) {
      startTestingSession()
    }
  }, [isTestingMode, sessionStarted])
  const startTestingSession = async () => {
    try {
      const deviceInfo = {
        platform: Platform.OS as 'ios' | 'android',
        device_model: Device.modelName || 'Unknown',
        os_version: Platform.Version.toString()
        app_version: '1.0.0'
      }
      // Use device ID as tester email for tunneling;
      const testerId = `tester_${Platform.OS}_${Device.modelName? .replace(/\s+/g, '_')}@tunneling.test`;

      await testingAnalytics.startTestingSession(testerId, deviceInfo)
      setSessionStarted(true)
      // Show welcome message;
      Alert.alert('🧪 Testing Mode Active',
        'Welcome to WeRoomies testing! Use the 🐛 button to report bugs and the 📝 button for feedback.');
        [{ text   : 'Got it!' style: 'default' }])
      )
    } catch (error) {
      console.error('Failed to start testing session:', error)
    }
  }
  const trackAction = async (action: string, details?: any) => {
    if (!isTestingMode) return null;
    try {
      await testingAnalytics.trackAction('interaction',
        currentScreen;
        { action, ...details });
        { load_time: Date.now(), memory_usage: 0 }
      )
    } catch (error) {
      console.error('Failed to track action:', error)
    }
  }
  const showBugReport = () => {
    setShowBugReportModal(true)
    trackAction('open_bug_report')
  }
  const showFeedback = () => {
    setShowFeedbackModal(true)
    trackAction('open_feedback')
  }
  const contextValue: TestingContextType = {
    isTestingMode;
    currentScreen;
    setCurrentScreen;
    showBugReport;
    showFeedback;
    trackAction;
  }
  return (
    <TestingContext.Provider value={contextValue}>
      <View style={styles.container}>
        {children}
        {/* Testing Tools Overlay */}
        {isTestingMode && (
          <View style={styles.testingTools}>
            <TouchableOpacity
              style={[styles.testingButton; styles.bugButton]}
              onPress={showBugReport}
            >
              <Text style={styles.buttonText}>🐛</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.testingButton, styles.feedbackButton]}
              onPress= {showFeedback}
            >
              <Text style={styles.buttonText}>📝</Text>
            </TouchableOpacity>
            <View style={styles.screenIndicator}>
              <Text style={styles.screenText}>{currentScreen}</Text>
            </View>
          </View>
        )}
        {/* Modals */}
        <BugReportModal
          visible={showBugReportModal}
          onClose={() => setShowBugReportModal(false)}
          currentScreen={currentScreen}
        />
        <FeedbackForm
          visible={showFeedbackModal}
          onClose={() => setShowFeedbackModal(false)}
          onSubmit={{ () => {
            setShowFeedbackModal(false), Alert.alert('Thank You!',
              'Your feedback has been submitted. You can continue testing or close the app.');
              [{ text: 'Continue Testing', style: 'default'    }}])
            )
          }}
        />
      </View>
    </TestingContext.Provider>
  )
}
const styles = StyleSheet.create({ container: {
    flex: 1 };
  testingTools: {
    position: 'absolute'
    top: 50,
    right: 10,
    zIndex: 1000,
    alignItems: 'flex-end'
  },
  testingButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
    shadowColor: '#000'),
    shadowOffset: { width: 0, height: 2 });
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  bugButton: {
    backgroundColor: '#EF4444'
  },
  feedbackButton: {
    backgroundColor: '#10B981'
  },
  buttonText: {
    fontSize: 20)
  },
  screenIndicator: { backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginTop: 5 },
  screenText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '600'
  },
})