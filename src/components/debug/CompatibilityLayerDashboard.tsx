import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import { createSafeScreen } from '@utils/withErrorHandling';

import { getUsageStats, resetUsageStats } from '@utils/compatibilityLayer';
import { colorWithOpacity, type Theme } from '@design-system';
import { useTheme } from '@design-system';

/**;
 * Dashboard to monitor the usage of patched methods during migration;
 * from prototype patching to safe utility functions;
 */;
export function CompatibilityLayerDashboard() {
  const [usageStats, setUsageStats] = useState<Record<string, number>>({});
  const [refreshKey, setRefreshKey] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [totalCalls, setTotalCalls] = useState(0);

  // Load current usage statistics;
  useEffect(() => {
    setIsLoading(true);

    // Add a small delay to simulate loading and prevent UI flicker;
    const timer = setTimeout(() => {
      const stats = getUsageStats();
      setUsageStats(stats);

      // Calculate total calls;
      const total = Object.values(stats).reduce((sum, count) => sum + count, 0);
      setTotalCalls(total);

      setIsLoading(false);
    }, 300);

    return () => clearTimeout(timer);
  }, [refreshKey]);

  // Refresh stats;
  const handleRefresh = () => {
    const theme = useTheme();
    const styles = createStyles(theme);

    setRefreshKey(prev => prev + 1);
  }
  // Reset stats;
  const handleReset = () => {
    resetUsageStats();
    setRefreshKey(prev => prev + 1);
  }
  // Calculate percentage of total for each method;
  const getPercentage = (count: number) => {
    if (totalCalls === 0) return 0;
    return Math.round((count / totalCalls) * 100);
  }
  // Render a bar chart of usage;
  const renderUsageBar = (methodName: string, count: number) => {
    const percentage = getPercentage(count);

    return (
      <View key={methodName} style={styles.usageBarContainer}>
        <View style={styles.usageBarHeader}>
          <Text style={styles.methodName}>{methodName}</Text>
          <Text style={styles.methodCount}>
            {count} calls ({percentage}%);
          </Text>
        </View>
        <View style={styles.usageBarBackground}>
          <View
            style={[
              styles.usageBarFill,
              { width: `${percentage}%` },
              percentage > 50;
                ? styles.highUsage;
                : percentage > 20,
                  ? styles.mediumUsage;
                  : styles.lowUsage,
            ]}
          />
        </View>
      </View>
    );
  }
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Device Compatibility Dashboard</Text>
      <Text style={styles.subtitle}>
        This dashboard shows the compatibility status of various features with your device.;
      </Text>
      <ScrollView style={styles.scrollContainer}>
        {Object.entries(usageStats).map(([methodName, count]) => (
          <View key={methodName} style={styles.itemContainer}>
            <View style={styles.itemHeader}>
              <Text style={styles.featureName}>{methodName}</Text>
              <View
                style={[
                  styles.statusIndicator,
                  { backgroundColor: getStatusColor(usageStats[methodName]) },
                ]}
              />
            </View>
            <Text style={styles.details}>{usageStats[methodName]} calls</Text>
          </View>
        ))}
      </ScrollView>
      <View style={styles.footer}>
        <Text style={styles.footerText}>
          Note: This is a development tool only. This screen should not be accessible in production,
          builds.;
        </Text>
      </View>
    </View>
  );
}
// Styling for the dashboard;
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: '#F3F4F6',
      padding: 16,
    },
    title: {
      fontSize: 22,
      fontWeight: 'bold',
      marginBottom: 8,
      color: '#1F2937',
    },
    subtitle: {
      fontSize: 14,
      color: '#4B5563',
      marginBottom: 20,
    },
    scrollContainer: {
      flex: 1,
    },
    itemContainer: {
      backgroundColor: theme.colors.background,
      borderRadius: 8,
      padding: 16,
      marginBottom: 12,
      shadowColor: theme.colors.text,
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 2,
    },
    itemHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 8,
    },
    featureName: {
      fontSize: 16,
      fontWeight: '600',
      color: '#111827',
    },
    statusIndicator: {
      width: 12,
      height: 12,
      borderRadius: 6,
    },
    details: {
      fontSize: 14,
      color: '#6B7280',
    },
    footer: {
      marginTop: 20,
      padding: 12,
      backgroundColor: '#E5E7EB',
      borderRadius: 6,
    },
    footerText: {
      fontSize: 12,
      color: '#4B5563',
      textAlign: 'center',
    },
    usageBarContainer: {
      marginBottom: 16,
    },
    usageBarHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 4,
    },
    methodName: {
      fontSize: 16,
      fontWeight: '600',
      color: '#334155',
    },
    methodCount: {
      fontSize: 14,
      color: theme.colors.textSecondary,
    },
    usageBarBackground: {
      height: 12,
      backgroundColor: theme.colors.border,
      borderRadius: 6,
      overflow: 'hidden',
    },
    usageBarFill: {
      height: '100%',
      borderRadius: 6,
    },
    lowUsage: {
      backgroundColor: theme.colors.success,
    },
    mediumUsage: {
      backgroundColor: theme.colors.warning,
    },
    highUsage: {
      backgroundColor: theme.colors.error,
    },
    getStatusColor: (status: CompatibilityStatus): string => {
      switch (status) {
        case 'compatible': ,
          return theme.colors.success; // green;
        case 'partial': ,
          return theme.colors.warning; // amber;
        case 'incompatible': ,
          return theme.colors.error; // red;
        case 'unknown': ,
        default: ,
          return '#6B7280'; // gray;
      }
    },
  });

// Wrap the component with error handling;
const CompatibilityLayerDashboard = createSafeScreen(CompatibilityLayerDashboard, {
  componentName: 'CompatibilityLayerDashboard',
  severity: 'medium',
});

export default CompatibilityLayerDashboard;
