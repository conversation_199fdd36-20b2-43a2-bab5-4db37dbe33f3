import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Alert } from 'react-native';
import { useRouter } from 'expo-router';
import {
  CheckCircle,
  XCircle,
  Play,
  FileText,
  MessageSquare,
  Users,
  Home,
} from 'lucide-react-native';
import { NavigationFlowValidator } from '@utils/navigationFlowValidator';

interface TestResult {
  name: string,
  success: boolean,
  error?: string,
  timestamp: string,
}
export default function FlowIntegrityTest() {
  const router = useRouter();
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const addTestResult = (name: string, success: boolean, error?: string) => {
    const result: TestResult = {
      name,
      success,
      error,
      timestamp: new Date().toLocaleTimeString(),
    }
    setTestResults(prev => [...prev, result]);
  }
  const runCriticalFixTests = async () => {
    setIsRunning(true);
    setTestResults([]);

    try {
      addTestResult('Starting Flow Integrity Tests', true);

      // Test 1: Room Browse to Details Navigation,
      try {
        router.push('/rooms/1' as any);
        addTestResult('Room Browse → Details Navigation', true);
      } catch (error) {
        addTestResult('Room Browse → Details Navigation', false, 'Navigation failed');
      }
      // Test 2: Agreement Creation Path,
      try {
        router.push('/agreement/create?chatRoomId=test-123' as any);
        addTestResult('Chat → Agreement Creation Path', true);
      } catch (error) {
        addTestResult('Chat → Agreement Creation Path', false, 'Navigation failed');
      }
      // Test 3: Full Agreement Flow,
      const agreementSteps = [
        { route: '/agreement/create', name: 'Agreement Create' },
        { route: '/agreement/review?id=test', name: 'Agreement Review' },
        { route: '/agreement/share?id=test', name: 'Agreement Share' },
        { route: '/agreement/signature-flow?id=test', name: 'Agreement Signature' },
      ];

      for (const step of agreementSteps) {
        try {
          // Test route accessibility (simplified check);
          addTestResult(`${step.name} Route`, true);
        } catch (error) {
          addTestResult(`${step.name} Route`, false, 'Route inaccessible');
        }
      }
      // Test 4: Roommate Flow,
      try {
        router.push('/(tabs)/search/housemate?id=test-user' as any);
        addTestResult('Roommate Search → Profile', true);
      } catch (error) {
        addTestResult('Roommate Search → Profile', false, 'Navigation failed');
      }
      // Test 5: Chat Integration,
      try {
        router.push('/chat?roomId=test&recipientId=test-user' as any);
        addTestResult('Chat Integration', true);
      } catch (error) {
        addTestResult('Chat Integration', false, 'Navigation failed');
      }
      addTestResult('All Tests Completed', true);
    } catch (error) {
      addTestResult(
        'Test Suite Failed',
        false,
        error instanceof Error ? error.message : 'Unknown error',
      );
    } finally {
      setIsRunning(false);
    }
  }
  const testSpecificFlow = (flowType: 'room' | 'roommate') => {
    Alert.alert(
      `Test ${flowType === 'room' ? 'Room' : 'Roommate'} Flow`,
      `This will navigate through the complete ${flowType} flow. Continue?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Test Flow',
          onPress: () => {
            if (flowType === 'room') {
              router.push('/(tabs)/room' as any);
            } else {
              router.push('/(tabs)/search/housemate' as any);
            }
          },
        },
      ];
    );
  }
  const testAgreementCreation = () => {
    Alert.alert(
      'Test Agreement Creation',
      'This will test the agreement creation flow from chat. Continue?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Test Agreement',
          onPress: () => {
            router.push('/agreement/create?chatRoomId=test-123&participants=user1,user2' as any);
          },
        },
      ];
    );
  }
  const clearResults = () => {
    setTestResults([]);
  }
  const successCount = testResults.filter(r => r.success).length;
  const totalCount = testResults.length;

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <FileText size={24} color={'#0066CC' /}>
        <Text style={styles.title}>Flow Integrity Test</Text>
        <Text style={styles.subtitle}>Validate Room & Roommate → Agreement flows</Text>
      </View>
      {/* Quick Actions */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Quick Tests</Text>
        <TouchableOpacity
          style={[styles.actionButton, isRunning && styles.disabledButton]}
          onPress={runCriticalFixTests}
          disabled={isRunning}
        >
          <Play size={20} color={'#FFFFFF' /}>
          <Text style={styles.actionButtonText}>
            {isRunning ? 'Running Tests...' : 'Run Critical Fix Tests'}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.secondaryButton} onPress={() => testSpecificFlow('room')}>
          <Home size={20} color={'#0066CC' /}>
          <Text style={styles.secondaryButtonText}>Test Room Flow</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.secondaryButton}
          onPress={() => testSpecificFlow('roommate')}
        >
          <Users size={20} color={'#0066CC' /}>
          <Text style={styles.secondaryButtonText}>Test Roommate Flow</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.secondaryButton} onPress={testAgreementCreation}>
          <FileText size={20} color={'#0066CC' /}>
          <Text style={styles.secondaryButtonText}>Test Agreement Creation</Text>
        </TouchableOpacity>
      </View>
      {/* Test Results */}
      {testResults.length > 0 && (
        <View style={styles.section}>
          <View style={styles.resultsHeader}>
            <Text style={styles.sectionTitle}>Test Results</Text>
            <View style={styles.scoreContainer}>
              <Text style={styles.score}>
                {successCount}/{totalCount} passed;
              </Text>
              <TouchableOpacity onPress={clearResults} style={styles.clearButton}>
                <Text style={styles.clearButtonText}>Clear</Text>
              </TouchableOpacity>
            </View>
          </View>
          <View style={styles.resultsList}>
            {testResults.map((result, index) => (
              <View key={index} style={styles.resultItem}>
                <View style={styles.resultStatus}>
                  {result.success ? (
                    <CheckCircle size={20} color={'#10B981' /}>
                  ) : (,
                    <XCircle size={20} color={'#EF4444' /}>
                  )}
                </View>
                <View style={styles.resultContent}>
                  <Text style={styles.resultName}>{result.name}</Text>
                  <Text style={styles.resultTime}>{result.timestamp}</Text>
                  {result.error && <Text style={styles.resultError}>{result.error}</Text>
                </View>
              </View>
            ))}
          </View>
        </View>
      )}
      {/* Navigation Quick Links */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Direct Navigation Tests</Text>
        <View style={styles.linkGrid}>
          <TouchableOpacity
            style={styles.linkButton}
            onPress={() => router.push('/(tabs)/room' as any)}
          >
            <Text style={styles.linkText}>Room Browse</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.linkButton}
            onPress={() => router.push('/rooms/1' as any)}
          >
            <Text style={styles.linkText}>Room Details</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.linkButton}
            onPress={() => router.push('/chat?roomId=test' as any)}
          >
            <Text style={styles.linkText}>Chat Screen</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.linkButton}
            onPress={() => router.push('/agreement/create' as any)}
          >
            <Text style={styles.linkText}>Create Agreement</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.linkButton}
            onPress={() => router.push('/agreement/review?id=test' as any)}
          >
            <Text style={styles.linkText}>Review Agreement</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.linkButton}
            onPress={() => router.push('/agreement/share?id=test' as any)}
          >
            <Text style={styles.linkText}>Share Agreement</Text>
          </TouchableOpacity>
        </View>
      </View>
      {/* Status Summary */}
      {testResults.length > 0 && (
        <View style={styles.section}>
          <View
            style={[
              styles.statusSummary,
              { backgroundColor: successCount === totalCount ? '#10B981' : '#EF4444' },
            ]}
          >
            <Text style={styles.statusText}>
              {successCount === totalCount;
                ? '✅ All Navigation Flows Working';
                : `❌ ${totalCount - successCount} Issues Detected`}
            </Text>
          </View>
        </View>
      )}
    </ScrollView>
  );
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    padding: 24,
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1E293B',
    marginTop: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#64748B',
    marginTop: 4,
  },
  section: {
    margin: 16,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1E293B',
    marginBottom: 16,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#0066CC',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
  },
  disabledButton: {
    backgroundColor: '#94A3B8',
  },
  actionButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  secondaryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F1F5F9',
    borderWidth: 1,
    borderColor: '#0066CC',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  secondaryButtonText: {
    color: '#0066CC',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
  },
  resultsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  scoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  score: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1E293B',
    marginRight: 12,
  },
  clearButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#EF4444',
    borderRadius: 6,
  },
  clearButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '500',
  },
  resultsList: {
    gap: 8,
  },
  resultItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 12,
    backgroundColor: '#F8FAFC',
    borderRadius: 8,
  },
  resultStatus: {
    marginRight: 12,
    marginTop: 2,
  },
  resultContent: {
    flex: 1,
  },
  resultName: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1E293B',
  },
  resultTime: {
    fontSize: 12,
    color: '#64748B',
    marginTop: 2,
  },
  resultError: {
    fontSize: 12,
    color: '#EF4444',
    marginTop: 4,
  },
  linkGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  linkButton: {
    backgroundColor: '#EEF2FF',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#C7D2FE',
  },
  linkText: {
    fontSize: 12,
    color: '#3730A3',
    fontWeight: '500',
  },
  statusSummary: {
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  statusText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});
