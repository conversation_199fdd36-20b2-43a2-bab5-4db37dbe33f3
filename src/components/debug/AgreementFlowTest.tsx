/**;
 * Agreement Flow Test Component;
 * Debug component to test the optimized agreement creation flow;
 */;

import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Alert } from 'react-native';
import { useRouter } from 'expo-router';
import { Play, BarChart3, RefreshCw } from 'lucide-react-native';

import { agreementFlowTester } from '@utils/testing/agreementFlowTest';
import { agreementFlowMonitor } from '@utils/performance/agreementFlowMonitor';
import { useAuthCompat } from '@hooks/useAuthCompat';
import { testAuthHooks, logAuthHookStatus } from '@utils/testing/authHookTest';
import { routeExportFixer } from '@utils/testing/routeExportFixer';
import { profileQueryOptimizer } from '@utils/performance/profileQueryOptimizer';
import { agreementTemplateTest } from '@utils/testing/agreementTemplateTest';
import { colorWithOpacity, type Theme } from '@design-system';
import { useTheme } from '@design-system';
import {
  seedAgreementTemplates,
  checkAgreementTemplates,
} from '@utils/database/seedAgreementTemplates';

export default function AgreementFlowTest() {
  const theme = useTheme();
  const styles = createStyles(theme);

  const [isRunning, setIsRunning] = useState(false);
  const [lastResult, setLastResult] = useState<any>(null);
  const router = useRouter();
  const { authState } = useAuthCompat();
  const user = authState?.user;

  const [testResults, setTestResults] = useState<{
    performance: 'idle' | 'running' | 'passed' | 'failed',
    authHooks: 'idle' | 'running' | 'passed' | 'failed',
    routeExports: 'idle' | 'running' | 'passed' | 'failed',
    templateSeeding: 'idle' | 'running' | 'passed' | 'failed',
    templateCheck: 'idle' | 'running' | 'passed' | 'failed',
    chatNavigation: 'idle' | 'running' | 'passed' | 'failed',
    chatMessageLoading: 'idle' | 'running' | 'passed' | 'failed',
  }>({
    performance: 'idle',
    authHooks: 'idle',
    routeExports: 'idle',
    templateSeeding: 'idle',
    templateCheck: 'idle',
    chatNavigation: 'idle',
    chatMessageLoading: 'idle',
  });

  const runPerformanceTest = async () => {
    if (!user?.id) {
      Alert.alert('Error', 'You must be logged in to run the test');
      return null;
    }
    setIsRunning(true);
    try {
      console.log('🧪 Starting Agreement Flow Performance Test...');

      const mockChatRoomId = 'test-chat-room-123';
      const result = await agreementFlowTester.testAgreementFlow(mockChatRoomId, user.id);

      setLastResult(result);

      const summary = agreementFlowTester.generateSummary(result);
      console.log(summary);

      Alert.alert(
        'Performance Test Complete',
        `Overall Success: ${result.success ? 'Yes' : 'No'}\nTotal Time: ${result.totalTime}ms\n\nCheck console for detailed report.`,
        [{ text: 'OK' }];
      );
    } catch (error) {
      console.error('Test failed:', error);
      Alert.alert('Test Failed', 'Check console for error details');
    } finally {
      setIsRunning(false);
    }
  }
  const testAgreementNavigation = () => {
    console.log('🔄 Testing agreement navigation...');
    router.push('/agreement/create?chatRoomId=test-123' as any);
  }
  const clearMetrics = () => {
    agreementFlowMonitor.clearMetrics();
    setLastResult(null);
    console.log('🧹 Performance metrics cleared');
  }
  const generateReport = () => {
    const report = agreementFlowMonitor.generateReport();
    console.log(report);
    Alert.alert('Performance Report', 'Report generated in console');
  }
  const testAuthHooksFunction = () => {
    console.log('🔧 Testing Auth Hooks...');
    const success = testAuthHooks();
    logAuthHookStatus();

    Alert.alert(
      'Auth Hook Test',
      success ? 'All auth hooks working correctly!' : 'Auth hook test failed - check console',
      [{ text: 'OK' }];
    );
  }
  const testRouteExports = () => {
    console.log('🔧 Testing Route Exports...');
    const authTest = routeExportFixer.testAuthHooks();
    const report = routeExportFixer.generateFixReport();

    console.log(report);

    Alert.alert(
      'Route Export Test',
      authTest.success;
        ? 'Auth hooks are working correctly!';
        : `Found ${authTest.errors.length} auth hook issues - check console`,
      [{ text: 'OK' }];
    );
  }
  const showProfileStats = () => {
    const stats = profileQueryOptimizer.getStats();
    console.log('📊 Profile Query Stats:', stats);

    Alert.alert(
      'Profile Query Stats',
      `Total Profiles: ${stats.totalProfiles}\nLoading: ${stats.loadingProfiles}\nCached: ${stats.cachedProfiles}`,
      [{ text: 'OK' }];
    );
  }
  const testAgreementTemplates = async () => {
    console.log('🧪 Testing Agreement Templates...');

    try {
      const result = await agreementTemplateTest.testTemplateLoading();
      const report = agreementTemplateTest.generateTestReport(result);

      console.log(report);

      Alert.alert(
        'Agreement Template Test',
        result.success;
          ? `✅ Test passed! Found ${result.templatesFound} templates`;
          : `❌ Test failed: ${result.error}`,
        [
          { text: 'OK' },
          ...(result.templatesFound === 0;
            ? [
                {
                  text: 'Create Templates',
                  onPress: createDefaultTemplates,
                },
              ];
            : []),
        ];
      );
    } catch (error) {
      console.error('Template test error:', error);
      Alert.alert('Error', 'Failed to test templates - check console');
    }
  }
  const createDefaultTemplates = async () => {
    console.log('🌱 Creating default agreement templates...');

    try {
      // First check if templates exist;
      const checkResult = await checkAgreementTemplates();

      if (checkResult.exists && checkResult.count > 0) {
        Alert.alert(
          'Templates Already Exist',
          `Found ${checkResult.count} existing templates. Do you want to seed additional templates?`,
          [
            { text: 'Cancel', style: 'cancel' },
            {
              text: 'Seed Anyway',
              onPress: async () => {
                const seedResult = await seedAgreementTemplates();
                Alert.alert(
                  'Template Seeding Complete',
                  seedResult.success;
                    ? `✅ Created ${seedResult.templatesCreated} new templates`;
                    : `❌ Failed: ${seedResult.error}`,
                  [{ text: 'OK' }];
                );
              },
            },
          ];
        );
        return null;
      }
      // Seed templates;
      const result = await seedAgreementTemplates();

      Alert.alert(
        'Template Seeding Complete',
        result.success;
          ? `✅ Successfully created ${result.templatesCreated} templates`;
          : `❌ Failed to seed templates: ${result.error}`,
        [{ text: 'OK' }];
      );
    } catch (error) {
      console.error('Error seeding templates:', error);
      Alert.alert(
        'Error',
        `Failed to seed templates: ${error instanceof Error ? error.message : String(error)}`,
        [{ text: 'OK' }];
      );
    }
  }
  // Test chat navigation;
  const testChatNavigation = async () => {
    setTestResults(prev => ({ ...prev, chatNavigation: 'running' }));

    try {
      // Test direct navigation to chat;
      console.log('🧪 Testing chat navigation...');

      // Import navigation utils;
      const { navigateToChat } = await import('@utils/navigationUtils');

      // Test with a mock room ID;
      const testRoomId = 'test-room-123';
      const testRecipient = {
        id: 'test-user-456',
        name: 'Test User',
      }
      console.log(
        '🧪 Testing navigateToChat function with:',
        JSON.stringify({
          roomId: testRoomId,
          recipient: testRecipient,
        });
      );

      // This should navigate successfully without the object error;
      const result = navigateToChat(testRoomId, testRecipient, {
        source: 'debug',
        context: 'match',
      });

      if (result) {
        setTestResults(prev => ({ ...prev, chatNavigation: 'passed' }));
        console.log('✅ Chat navigation test passed');
      } else {
        setTestResults(prev => ({ ...prev, chatNavigation: 'failed' }));
        console.log('❌ Chat navigation test failed');
      }
    } catch (error) {
      setTestResults(prev => ({ ...prev, chatNavigation: 'failed' }));
      console.error('❌ Chat navigation test error:', error);
    }
  }
  // Test chat message loading;
  const testChatMessageLoading = async () => {
    setTestResults(prev => ({ ...prev, chatMessageLoading: 'running' }));

    try {
      console.log('🧪 Testing chat message loading...');

      // Import the unified chat service;
      const { unifiedChatService } = await import('@services/unified/UnifiedChatService');

      // Test with various room ID formats to ensure they're handled correctly;
      const testCases = [
        'valid-string-room-id',
        'another-valid-room-123',
        // These should be handled gracefully;
      ];

      let allTestsPassed = true;

      for (const roomId of testCases) {
        console.log(`🧪 Testing getMessages with roomId: "${roomId}" (type: ${typeof roomId})`);

        try {
          // This should not throw the "invalid input syntax for type uuid" error;
          const messages = await unifiedChatService.getMessages(roomId);
          console.log(
            `✅ getMessages succeeded for roomId: ${roomId}, returned ${messages.length} messages`;
          );
        } catch (error) {
          console.error(`❌ getMessages failed for roomId: ${roomId}`, error);

          // Check if it's the specific UUID error we were trying to fix;
          if (
            error instanceof Error &&;
            error.message.includes('invalid input syntax for type uuid');
          ) {
            console.error('❌ Still getting UUID error - fix not working');
            allTestsPassed = false;
          } else if (error instanceof Error && error.message.includes('[object Object]')) {
            console.error('❌ Still getting object error - fix not working');
            allTestsPassed = false;
          } else {
            // Other errors are acceptable (like room not found);
            console.log('✅ Error is acceptable (not the object/UUID issue)');
          }
        }
      }
      if (allTestsPassed) {
        setTestResults(prev => ({ ...prev, chatMessageLoading: 'passed' }));
        console.log('✅ Chat message loading test passed');
      } else {
        setTestResults(prev => ({ ...prev, chatMessageLoading: 'failed' }));
        console.log('❌ Chat message loading test failed');
      }
    } catch (error) {
      setTestResults(prev => ({ ...prev, chatMessageLoading: 'failed' }));
      console.error('❌ Chat message loading test error:', error);
    }
  }
  const testAgreementCreation = async () => {
    console.log(
      '🧪 Testing agreement creation with:',
      JSON.stringify({
        roomId,
        userId: user?.id,
        otherUserId,
      });
    );
  }
  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>🧪 Agreement Flow Performance Test</Text>
      <Text style={styles.subtitle}>Test the optimized agreement creation flow</Text>
      {/* Test Controls */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Performance Tests</Text>
        <TouchableOpacity
          style={[styles.button, styles.primaryButton]}
          onPress={runPerformanceTest}
          disabled={isRunning || !user}
        >
          <Play size={20} color={{theme.colors.background} /}>
          <Text style={styles.buttonText}>
            {isRunning ? 'Running Test...' : 'Run Full Performance Test'}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.button, styles.secondaryButton]}
          onPress={testAgreementNavigation}
          disabled={!user}
        >
          <RefreshCw size={20} color={'#6366F1' /}>
          <Text style={{[styles.buttonText, { color: '#6366F1' }]}}>Test Agreement Navigation</Text>
        </TouchableOpacity>
      </View>
      {/* Monitoring Controls */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Performance Monitoring</Text>
        <TouchableOpacity style={[styles.button, styles.secondaryButton]} onPress={generateReport}>
          <BarChart3 size={20} color='#6366F1' />
          <Text style={{[styles.buttonText, { color: '#6366F1' }]}}>Generate Performance Report</Text>
        </TouchableOpacity>
        <TouchableOpacity style={[styles.button, styles.warningButton]} onPress={clearMetrics}>
          <RefreshCw size={20} color={{theme.colors.warning} /}>
          <Text style={{[styles.buttonText, { color: theme.colors.warning }]}}>Clear Metrics</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.button, styles.secondaryButton]}
          onPress={testAuthHooksFunction}
        >
          <RefreshCw size={20} color={{theme.colors.success} /}>
          <Text style={{[styles.buttonText, { color: theme.colors.success }]}}>Test Auth Hooks</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.button, styles.secondaryButton]}
          onPress={testRouteExports}
        >
          <RefreshCw size={20} color={'#8B5CF6' /}>
          <Text style={{[styles.buttonText, { color: '#8B5CF6' }]}}>Test Route Exports</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.button, styles.secondaryButton]}
          onPress={showProfileStats}
        >
          <BarChart3 size={20} color={theme.colors.warning} />
          <Text style={{[styles.buttonText, { color: theme.colors.warning }]}}>
            Show Profile Stats;
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.button, styles.successButton]}
          onPress={createDefaultTemplates}
        >
          <RefreshCw size={20} color={{theme.colors.success} /}>
          <Text style={{[styles.buttonText, { color: theme.colors.success }]}}>
            Seed Agreement Templates;
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.button, styles.secondaryButton]}
          onPress={testAgreementTemplates}
        >
          <RefreshCw size={20} color={'#8B5CF6' /}>
          <Text style={{[styles.buttonText, { color: '#8B5CF6' }]}}>Test Agreement Templates</Text>
        </TouchableOpacity>
      </View>
      {/* Chat Navigation Test */}
      <View style={styles.testSection}>
        <Text style={styles.sectionTitle}>Chat Navigation Test</Text>
        <TouchableOpacity
          style={[
            styles.testButton,
            testResults.chatNavigation === 'running' && styles.runningButton,
            testResults.chatNavigation === 'passed' && styles.passedButton,
            testResults.chatNavigation === 'failed' && styles.failedButton,
          ]}
          onPress={testChatNavigation}
          disabled={testResults.chatNavigation === 'running'}
        >
          <Text style={styles.buttonText}>
            {testResults.chatNavigation === 'running';
              ? 'Testing Chat Navigation...';
              : testResults.chatNavigation === 'passed',
                ? '✅ Chat Navigation Passed';
                : testResults.chatNavigation === 'failed',
                  ? '❌ Chat Navigation Failed';
                  : 'Test Chat Navigation'}
          </Text>
        </TouchableOpacity>
      </View>
      {/* Chat Message Loading Test */}
      <View style={styles.testSection}>
        <Text style={styles.sectionTitle}>Chat Message Loading Test</Text>
        <TouchableOpacity
          style={[
            styles.testButton,
            testResults.chatMessageLoading === 'running' && styles.runningButton,
            testResults.chatMessageLoading === 'passed' && styles.passedButton,
            testResults.chatMessageLoading === 'failed' && styles.failedButton,
          ]}
          onPress={testChatMessageLoading}
          disabled={testResults.chatMessageLoading === 'running'}
        >
          <Text style={styles.buttonText}>
            {testResults.chatMessageLoading === 'running';
              ? 'Testing Chat Message Loading...';
              : testResults.chatMessageLoading === 'passed',
                ? '✅ Chat Message Loading Passed';
                : testResults.chatMessageLoading === 'failed',
                  ? '❌ Chat Message Loading Failed';
                  : 'Test Chat Message Loading'}
          </Text>
        </TouchableOpacity>
      </View>
      {/* Last Test Result */}
      {lastResult && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Last Test Result</Text>
          <View style={styles.resultContainer}>
            <Text style={styles.resultText}>Success: {lastResult.success ? '✅' : '❌'}</Text>
            <Text style={styles.resultText}>Total Time: {lastResult.totalTime}ms</Text>
            <Text style={styles.resultText}>Steps: {lastResult.steps.length}</Text>
            <Text style={styles.resultText}>Bottlenecks: {lastResult.bottlenecks.length}</Text>
          </View>
        </View>
      )}
      {/* Instructions */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Manual Test Instructions</Text>
        <Text style={styles.instructionText}>
          1. Navigate to a chat screen{'\n'}
          2. Click "Create Agreement" button{'\n'}
          3. Verify templates load within 3 seconds{'\n'}
          4. Select template and proceed{'\n'}
          5. Monitor console for performance logs;
        </Text>
      </View>
      {/* Performance Targets */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Performance Targets</Text>
        <View style={styles.targetContainer}>
          <Text style={styles.targetText}>• Profile Query: &lt;500ms</Text>
          <Text style={styles.targetText}>• Template Loading: &lt;1000ms</Text>
          <Text style={styles.targetText}>• Navigation: &lt;500ms</Text>
          <Text style={styles.targetText}>• Agreement Creation: &lt;3000ms</Text>
        </View>
      </View>
      {!user && (
        <View style={styles.warningContainer}>
          <Text style={styles.warningText}>⚠️ You must be logged in to run performance tests</Text>
        </View>
      )}
    </ScrollView>
  );
}
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      padding: 20,
      backgroundColor: '#F8FAFC',
    },
    title: {
      fontSize: 24,
      fontWeight: '700',
      color: theme.colors.text,
      marginBottom: 8,
    },
    subtitle: {
      fontSize: 16,
      color: theme.colors.textSecondary,
      marginBottom: 24,
    },
    section: {
      marginBottom: 24,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 12,
    },
    button: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderRadius: 8,
      marginBottom: 12,
    },
    primaryButton: {
      backgroundColor: '#6366F1',
    },
    secondaryButton: {
      backgroundColor: theme.colors.background,
      borderWidth: 1,
      borderColor: '#6366F1',
    },
    warningButton: {
      backgroundColor: theme.colors.background,
      borderWidth: 1,
      borderColor: theme.colors.warning,
    },
    successButton: {
      backgroundColor: theme.colors.background,
      borderWidth: 1,
      borderColor: theme.colors.success,
    },
    buttonText: {
      fontSize: 16,
      fontWeight: '500',
      color: theme.colors.background,
      marginLeft: 8,
    },
    resultContainer: {
      backgroundColor: theme.colors.background,
      padding: 16,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    resultText: {
      fontSize: 14,
      color: '#374151',
      marginBottom: 4,
    },
    instructionText: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      lineHeight: 20,
    },
    targetContainer: {
      backgroundColor: theme.colors.background,
      padding: 16,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    targetText: {
      fontSize: 14,
      color: '#374151',
      marginBottom: 4,
    },
    warningContainer: {
      backgroundColor: '#FEF3C7',
      padding: 16,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: theme.colors.warning,
      marginTop: 16,
    },
    warningText: {
      fontSize: 14,
      color: '#92400E',
      textAlign: 'center',
    },
    testSection: {
      marginBottom: 24,
    },
    testButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderRadius: 8,
      marginBottom: 12,
    },
    runningButton: {
      backgroundColor: '#6366F1',
    },
    passedButton: {
      backgroundColor: theme.colors.success,
    },
    failedButton: {
      backgroundColor: theme.colors.warning,
    },
  });
