/**;
 * AI Profile Enhancement Debugger;
 *;
 * Comprehensive testing interface for all AI-powered profile enhancement features;
 * including analytics, optimization, insights dashboard, and smart completion.;
 */;

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Switch,
} from 'react-native';
import { useTheme } from '@design-system';
import { StyleSheet } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { advancedProfileAnalytics } from '@services/profile/AdvancedProfileAnalytics';
import { aiProfileOptimizer } from '@services/profile/AIProfileOptimizer';
import { smartProfileCompletion } from '@services/profile/SmartProfileCompletion';
import { EnhancedProfileInsights } from '@components/profile/EnhancedProfileInsights';
import { logger } from '@services/loggerService';
import { useAuth } from '@context/AuthContext';

// ==================== TYPES ====================;

interface TestResult {
  testName: string,
  status: 'pending' | 'running' | 'success' | 'error',
  duration?: number,
  data?: any,
  error?: string,
  timestamp?: string,
}
interface DebugSection {
  id: string,
  title: string,
  icon: string,
  tests: TestConfig[],
}
interface TestConfig {
  id: string,
  name: string,
  description: string,
  testFunction: () => Promise<any>
  expectedResult?: string,
}
// ==================== MAIN COMPONENT ====================;

export const AIProfileEnhancementDebugger: React.FC = () => {
  const theme = useTheme();
  const styles = createStyles(theme);
  const { user } = useAuth();

  // ==================== STATE ====================;

  const [testResults, setTestResults] = useState<{ [key: string]: TestResult }>({});
  const [isRunningAll, setIsRunningAll] = useState(false);
  const [selectedSection, setSelectedSection] = useState<string>('analytics');
  const [showInsightsDemo, setShowInsightsDemo] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(false);

  // ==================== TEST CONFIGURATIONS ====================;

  const debugSections: DebugSection[] = [,
    {
      id: 'analytics',
      title: 'Advanced Analytics',
      icon: 'analytics',
      tests: [,
        {
          id: 'analytics_basic',
          name: 'Basic Analytics Test',
          description: 'Test profile analytics data retrieval',
          testFunction: async () => {
            const result = await advancedProfileAnalytics.getProfileAnalytics(
              user?.id || 'test-user';
            );
            if (!result.success) throw new Error(result.error || 'Analytics failed');
            return result.data;
          },
          expectedResult: 'Analytics data with view metrics, match success, and performance scores',
        },
        {
          id: 'analytics_performance',
          name: 'Performance Metrics Test',
          description: 'Test performance analytics calculation',
          testFunction: async () => {
            const result = await advancedProfileAnalytics.getProfileAnalytics(
              user?.id || 'test-user';
            );
            if (!result.success || !result.data?.performance) {
              throw new Error('Performance metrics not available');
            }
            return {
              visibility: result.data.performance.visibility,
              attractiveness: result.data.performance.attractiveness,
              compatibility: result.data.performance.compatibility,
              trustworthiness: result.data.performance.trustworthiness,
              completeness: result.data.performance.completeness,
            }
          },
          expectedResult: 'Performance scores for all categories (0-100)',
        },
        {
          id: 'analytics_trends',
          name: 'Trend Analysis Test',
          description: 'Test trend analysis and predictions',
          testFunction: async () => {
            const result = await advancedProfileAnalytics.getProfileAnalytics(
              user?.id || 'test-user';
            );
            if (!result.success || !result.data?.trends) {
              throw new Error('Trend data not available');
            }
            return result.data.trends;
          },
          expectedResult: 'Trend data with performance history and predictions',
        },
      ],
    },
    {
      id: 'optimization',
      title: 'AI Optimization',
      icon: 'psychology',
      tests: [,
        {
          id: 'optimization_basic',
          name: 'Basic Optimization Test',
          description: 'Test AI profile optimization engine',
          testFunction: async () => {
            const result = await aiProfileOptimizer.optimizeProfile(user?.id || 'test-user');
            if (!result.success) throw new Error(result.error || 'Optimization failed');
            return {
              overallScore: result.data?.optimizationScore?.overall,
              recommendationsCount: result.data?.overallRecommendations?.length,
              aiInsightsCount: result.data?.aiInsights?.length,
            }
          },
          expectedResult: 'Optimization data with scores and recommendations',
        },
        {
          id: 'optimization_content',
          name: 'Content Optimization Test',
          description: 'Test content analysis and suggestions',
          testFunction: async () => {
            const result = await aiProfileOptimizer.optimizeProfile(user?.id || 'test-user');
            if (!result.success || !result.data?.contentOptimization) {
              throw new Error('Content optimization not available');
            }
            return {
              contentScore: result.data.contentOptimization.contentScore,
              bioSuggestions: result.data.contentOptimization.bioSuggestions.length,
              keywordOptimization: ,
                result.data.contentOptimization.keywordOptimization.missingKeywords.length,
            }
          },
          expectedResult: 'Content analysis with bio suggestions and keyword optimization',
        },
        {
          id: 'optimization_photos',
          name: 'Photo Optimization Test',
          description: 'Test photo analysis and recommendations',
          testFunction: async () => {
            const result = await aiProfileOptimizer.optimizeProfile(user?.id || 'test-user');
            if (!result.success || !result.data?.photoOptimization) {
              throw new Error('Photo optimization not available');
            }
            return {
              photoQualityScore: result.data.photoOptimization.photoQualityScore,
              diversityScore: result.data.photoOptimization.diversityScore,
              missingPhotoTypes: result.data.photoOptimization.missingPhotoTypes.length,
              photoSuggestions: result.data.photoOptimization.photoSuggestions.length,
            }
          },
          expectedResult: 'Photo analysis with quality scores and suggestions',
        },
        {
          id: 'optimization_personality',
          name: 'Personality Optimization Test',
          description: 'Test personality analysis and compatibility',
          testFunction: async () => {
            const result = await aiProfileOptimizer.optimizeProfile(user?.id || 'test-user');
            if (!result.success || !result.data?.personalityOptimization) {
              throw new Error('Personality optimization not available');
            }
            return {
              personalityScore: result.data.personalityOptimization.personalityScore,
              compatibilityScore: ,
                result.data.personalityOptimization.compatibilityOptimization.compatibilityScore,
              personalityGaps: result.data.personalityOptimization.personalityGaps.length,
              suggestions: result.data.personalityOptimization.personalitySuggestions.length,
            }
          },
          expectedResult: 'Personality analysis with compatibility scores and suggestions',
        },
      ],
    },
    {
      id: 'completion',
      title: 'Smart Completion',
      icon: 'checklist',
      tests: [,
        {
          id: 'completion_basic',
          name: 'Basic Completion Test',
          description: 'Test smart completion analysis',
          testFunction: async () => {
            const result = await smartProfileCompletion.getSmartCompletion(user?.id || 'test-user');
            if (!result.success) throw new Error(result.error || 'Smart completion failed');
            return {
              overallCompletion: result.data?.currentCompletion?.overall,
              suggestionsCount: result.data?.intelligentSuggestions?.length,
              priorityActionsCount: result.data?.priorityActions?.length,
            }
          },
          expectedResult: 'Completion analysis with suggestions and priority actions',
        },
        {
          id: 'completion_roadmap',
          name: 'Completion Roadmap Test',
          description: 'Test completion roadmap generation',
          testFunction: async () => {
            const result = await smartProfileCompletion.getSmartCompletion(user?.id || 'test-user');
            if (!result.success || !result.data?.completionRoadmap) {
              throw new Error('Completion roadmap not available');
            }
            return {
              currentPhase: result.data.completionRoadmap.currentPhase,
              nextMilestones: result.data.completionRoadmap.nextMilestones.length,
              quickWins: result.data.completionRoadmap.quickWins.length,
              estimatedTime: result.data.completionRoadmap.estimatedTimeToCompletion,
            }
          },
          expectedResult: 'Roadmap with phases, milestones, and time estimates',
        },
        {
          id: 'completion_behavior',
          name: 'Behavior Analysis Test',
          description: 'Test user behavior analysis',
          testFunction: async () => {
            const result = await smartProfileCompletion.getSmartCompletion(user?.id || 'test-user');
            if (!result.success || !result.data?.behaviorAnalysis) {
              throw new Error('Behavior analysis not available');
            }
            return {
              completionPattern: result.data.behaviorAnalysis.completionPattern,
              preferredTime: result.data.behaviorAnalysis.preferredCompletionTime,
              velocity: result.data.behaviorAnalysis.completionVelocity,
              motivationFactors: result.data.behaviorAnalysis.motivationFactors.length,
            }
          },
          expectedResult: 'Behavior analysis with patterns and motivation factors',
        },
      ],
    },
    {
      id: 'integration',
      title: 'System Integration',
      icon: 'integration_instructions',
      tests: [,
        {
          id: 'integration_cache',
          name: 'Cache Performance Test',
          description: 'Test caching system performance',
          testFunction: async () => {
            const startTime = performance.now();

            // First call (should cache);
            await advancedProfileAnalytics.getProfileAnalytics(user?.id || 'test-user');
            const firstCallTime = performance.now() - startTime;

            // Second call (should use cache);
            const cacheStartTime = performance.now();
            await advancedProfileAnalytics.getProfileAnalytics(user?.id || 'test-user');
            const cacheCallTime = performance.now() - cacheStartTime;

            return {
              firstCallTime: Math.round(firstCallTime),
              cacheCallTime: Math.round(cacheCallTime),
              cacheSpeedup: Math.round(firstCallTime / cacheCallTime),
            }
          },
          expectedResult: 'Cache should provide significant speedup (>5x faster)',
        },
        {
          id: 'integration_parallel',
          name: 'Parallel Processing Test',
          description: 'Test parallel data processing',
          testFunction: async () => {
            const startTime = performance.now();

            // Run all services in parallel;
            const [analyticsResult, optimizationResult, completionResult] = await Promise.all([
              advancedProfileAnalytics.getProfileAnalytics(user?.id || 'test-user'),
              aiProfileOptimizer.optimizeProfile(user?.id || 'test-user'),
              smartProfileCompletion.getSmartCompletion(user?.id || 'test-user'),
            ]);

            const totalTime = performance.now() - startTime;

            return {
              totalTime: Math.round(totalTime),
              analyticsSuccess: analyticsResult.success,
              optimizationSuccess: optimizationResult.success,
              completionSuccess: completionResult.success,
              allSuccessful: ,
                analyticsResult.success && optimizationResult.success && completionResult.success,
            }
          },
          expectedResult: 'All services should complete successfully in parallel',
        },
        {
          id: 'integration_error_handling',
          name: 'Error Handling Test',
          description: 'Test error handling and recovery',
          testFunction: async () => {
            try {
              // Test with invalid user ID;
              const result = await advancedProfileAnalytics.getProfileAnalytics('invalid-user-id');

              return {
                handlesInvalidUser: !result.success,
                errorMessage: result.error,
                gracefulFailure: !!result.error && result.data === null,
              }
            } catch (error) {
              return {
                handlesInvalidUser: true,
                errorMessage: error instanceof Error ? error.message : 'Unknown error',
                gracefulFailure: true,
              }
            }
          },
          expectedResult: 'Should handle errors gracefully without crashing',
        },
      ],
    },
  ];

  // ==================== TEST EXECUTION ====================;

  const runTest = async (testConfig: TestConfig) => {
    const testId = testConfig.id;

    setTestResults(prev => ({
      ...prev,
      [testId]: {
        testName: testConfig.name,
        status: 'running',
      },
    }));

    try {
      const startTime = performance.now();
      const data = await testConfig.testFunction();
      const duration = performance.now() - startTime;

      setTestResults(prev => ({
        ...prev,
        [testId]: {
          testName: testConfig.name,
          status: 'success',
          duration: Math.round(duration),
          data,
          timestamp: new Date().toISOString(),
        },
      }));

      logger.info(
        `Test ${testConfig.name} completed successfully`,
        'AIProfileEnhancementDebugger',
        {
          testId,
          duration: Math.round(duration),
          dataKeys: Object.keys(data || {}),
        }
      );
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error),
      setTestResults(prev => ({
        ...prev,
        [testId]: {
          testName: testConfig.name,
          status: 'error',
          error: errorMessage,
          timestamp: new Date().toISOString(),
        },
      }));

      logger.error(`Test ${testConfig.name} failed`, 'AIProfileEnhancementDebugger', {
        testId,
        error: errorMessage,
      });
    }
  }
  const runAllTests = async () => {
    setIsRunningAll(true);

    try {
      for (const section of debugSections) {
        for (const test of section.tests) {
          await runTest(test);
          // Small delay between tests;
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }
      Alert.alert('Tests Complete', 'All AI Profile Enhancement tests have been executed.');
    } catch (error) {
      Alert.alert('Test Error', 'An error occurred while running tests.');
    } finally {
      setIsRunningAll(false);
    }
  }
  const clearResults = () => {
    setTestResults({});
    Alert.alert('Results Cleared', 'All test results have been cleared.');
  }
  // ==================== RENDER METHODS ====================;

  const renderTestResult = (testConfig: TestConfig) => {
    const result = testResults[testConfig.id];
    const status = result?.status || 'pending';

    return (
      <View key={testConfig.id} style={styles.testItem}>
        <View style={styles.testHeader}>
          <View style={styles.testInfo}>
            <Text style={styles.testName}>{testConfig.name}</Text>
            <Text style={styles.testDescription}>{testConfig.description}</Text>
          </View>
          <View style={styles.testStatus}>
            {status === 'running' && (
              <ActivityIndicator size='small' color={{theme.colors.primary} /}>
            )}
            {status === 'success' && (
              <MaterialIcons name='check-circle' size={24} color={{theme.colors.success} /}>
            )}
            {status === 'error' && (
              <MaterialIcons name='error' size={24} color={{theme.colors.error} /}>
            )}
            {status === 'pending' && (
              <MaterialIcons name='schedule' size={24} color={{theme.colors.textSecondary} /}>
            )}
          </View>
        </View>
        {result && (
          <View style={styles.testDetails}>
            {result.duration && (
              <Text style={styles.testMetric}>Duration: {result.duration}ms</Text>
            )}
            {result.timestamp && (
              <Text style={styles.testMetric}>
                Time: {new Date(result.timestamp).toLocaleTimeString()}
              </Text>
            )}
            {result.error && <Text style={styles.errorText}>Error: {result.error}</Text>
            {result.data && (
              <View style={styles.dataContainer}>
                <Text style={styles.dataTitle}>Result Data:</Text>
                <Text style={styles.dataText}>{JSON.stringify(result.data, null, 2)}</Text>
              </View>
            )}
          </View>
        )}
        <View style={styles.testActions}>
          <TouchableOpacity
            style={styles.runButton}
            onPress={() => runTest(testConfig)}
            disabled={status === 'running'}
          >
            <Text style={styles.runButtonText}>
              {status === 'running' ? 'Running...' : 'Run Test'}
            </Text>
          </TouchableOpacity>
          {testConfig.expectedResult && (
            <TouchableOpacity
              style={styles.infoButton}
              onPress={() => Alert.alert('Expected Result', testConfig.expectedResult)}
            >
              <MaterialIcons name='info' size={16} color={{theme.colors.primary} /}>
            </TouchableOpacity>
          )}
        </View>
      </View>
    );
  }
  const renderSection = (section: DebugSection) => (,
    <View key={section.id} style={styles.section}>
      <View style={styles.sectionHeader}>
        <MaterialIcons name={section.icon as any} size={24} color={{theme.colors.primary} /}>
        <Text style={styles.sectionTitle}>{section.title}</Text>
        <Text style={styles.testCount}>({section.tests.length} tests)</Text>
      </View>
      {section.tests.map(renderTestResult)}
    </View>
  );

  const renderSectionTabs = () => (
    <View style={styles.tabContainer}>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        {debugSections.map(section => (
          <TouchableOpacity
            key={section.id}
            style={[styles.tab, selectedSection === section.id && styles.activeTab]}
            onPress={() => setSelectedSection(section.id)}
          >
            <MaterialIcons
              name={section.icon as any}
              size={20}
              color={
                selectedSection === section.id ? theme.colors.primary : theme.colors.textSecondary,
              }
            />
            <Text style={[styles.tabText, selectedSection ==={ section.id && styles.activeTabText]}}>
              {section.title}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  const renderControls = () => (
    <View style={styles.controls}>
      <View style={styles.controlRow}>
        <TouchableOpacity
          style={styles.primaryButton}
          onPress={runAllTests}
          disabled={isRunningAll}
        >
          <MaterialIcons name='play-arrow' size={20} color={{theme.colors.background} /}>
          <Text style={styles.primaryButtonText}>
            {isRunningAll ? 'Running All Tests...' : 'Run All Tests'}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.secondaryButton} onPress={clearResults}>
          <MaterialIcons name='clear' size={20} color={{theme.colors.primary} /}>
          <Text style={styles.secondaryButtonText}>Clear Results</Text>
        </TouchableOpacity>
      </View>
      <View style={styles.controlRow}>
        <View style={styles.switchContainer}>
          <Text style={styles.switchLabel}>Show Insights Demo</Text>
          <Switch
            value={showInsightsDemo}
            onValueChange={setShowInsightsDemo}
            trackColor={ false: theme.colors.border, true: theme.colors.primaryLight }
            thumbColor={showInsightsDemo ? theme.colors.primary : theme.colors.textSecondary}
          />
        </View>
        <View style={styles.switchContainer}>
          <Text style={styles.switchLabel}>Auto Refresh</Text>
          <Switch
            value={autoRefresh}
            onValueChange={setAutoRefresh}
            trackColor={ false: theme.colors.border, true: theme.colors.primaryLight }
            thumbColor={autoRefresh ? theme.colors.primary : theme.colors.textSecondary}
          />
        </View>
      </View>
    </View>
  );

  const renderStats = () => {
    const totalTests = debugSections.reduce((sum, section) => sum + section.tests.length, 0);
    const completedTests = Object.keys(testResults).length;
    const successfulTests = Object.values(testResults).filter(r => r.status === 'success').length;
    const failedTests = Object.values(testResults).filter(r => r.status === 'error').length;

    return (
      <View style={styles.stats}>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{totalTests}</Text>
          <Text style={styles.statLabel}>Total Tests</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{completedTests}</Text>
          <Text style={styles.statLabel}>Completed</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={{[styles.statValue, { color: theme.colors.success }]}}>{successfulTests}</Text>
          <Text style={styles.statLabel}>Successful</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={{[styles.statValue, { color: theme.colors.error }]}}>{failedTests}</Text>
          <Text style={styles.statLabel}>Failed</Text>
        </View>
      </View>
    );
  }
  // ==================== AUTO REFRESH EFFECT ====================;

  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(() => {
        const currentSection = debugSections.find(s => s.id === selectedSection);
        if (currentSection) {
          currentSection.tests.forEach(test => {
            if (testResults[test.id]?.status !== 'running') {
              runTest(test);
            }
          });
        }
      }, 30000); // Refresh every 30 seconds;

      return () => clearInterval(interval);
    }
  }, [autoRefresh, selectedSection, testResults]);

  // ==================== MAIN RENDER ====================;

  if (!user) {
    return (
      <View style={styles.container}>
        <View style={styles.errorContainer}>
          <MaterialIcons name='person-off' size={48} color={{theme.colors.error} /}>
          <Text style={styles.errorText}>User authentication required for testing</Text>
        </View>
      </View>
    );
  }
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>AI Profile Enhancement Debugger</Text>
        <Text style={styles.subtitle}>Test all AI-powered profile features</Text>
      </View>
      {renderStats()}
      {renderControls()}
      {renderSectionTabs()}
      <ScrollView style={styles.content}>
        {showInsightsDemo && (
          <View style={styles.demoContainer}>
            <Text style={styles.demoTitle}>Enhanced Profile Insights Demo</Text>
            <EnhancedProfileInsights />
          </View>
        )}
        {selectedSection === 'all';
          ? debugSections.map(renderSection);
          : debugSections.filter(section => section.id === selectedSection).map(renderSection)}
      </ScrollView>
    </View>
  );
}
// ==================== STYLES ====================;

const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      padding: theme.spacing.lg,
      backgroundColor: theme.colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    title: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: theme.spacing.xs,
    },
    subtitle: {
      fontSize: 16,
      color: theme.colors.textSecondary,
    },
    stats: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      padding: theme.spacing.md,
      backgroundColor: theme.colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    statItem: {
      alignItems: 'center',
    },
    statValue: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.text,
    },
    statLabel: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      marginTop: theme.spacing.xs,
    },
    controls: {
      padding: theme.spacing.md,
      backgroundColor: theme.colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    controlRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: theme.spacing.sm,
    },
    primaryButton: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.primary,
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      borderRadius: theme.borderRadius.md,
      gap: theme.spacing.xs,
    },
    primaryButtonText: {
      color: theme.colors.background,
      fontSize: 16,
      fontWeight: 'bold',
    },
    secondaryButton: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: 'transparent',
      borderWidth: 1,
      borderColor: theme.colors.primary,
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      borderRadius: theme.borderRadius.md,
      gap: theme.spacing.xs,
    },
    secondaryButtonText: {
      color: theme.colors.primary,
      fontSize: 16,
      fontWeight: 'bold',
    },
    switchContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: theme.spacing.sm,
    },
    switchLabel: {
      fontSize: 14,
      color: theme.colors.text,
    },
    tabContainer: {
      backgroundColor: theme.colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    tab: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      gap: theme.spacing.xs,
    },
    activeTab: {
      borderBottomWidth: 2,
      borderBottomColor: theme.colors.primary,
    },
    tabText: {
      fontSize: 14,
      color: theme.colors.textSecondary,
    },
    activeTabText: {
      color: theme.colors.primary,
      fontWeight: 'bold',
    },
    content: {
      flex: 1,
    },
    demoContainer: {
      margin: theme.spacing.md,
      padding: theme.spacing.md,
      backgroundColor: theme.colors.surface,
      borderRadius: theme.borderRadius.lg,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    demoTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: theme.spacing.md,
    },
    section: {
      margin: theme.spacing.md,
      backgroundColor: theme.colors.surface,
      borderRadius: theme.borderRadius.lg,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    sectionHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: theme.spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
      gap: theme.spacing.sm,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.text,
      flex: 1,
    },
    testCount: {
      fontSize: 12,
      color: theme.colors.textSecondary,
    },
    testItem: {
      padding: theme.spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    testHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginBottom: theme.spacing.sm,
    },
    testInfo: {
      flex: 1,
      marginRight: theme.spacing.md,
    },
    testName: {
      fontSize: 16,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: theme.spacing.xs,
    },
    testDescription: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      lineHeight: 20,
    },
    testStatus: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    testDetails: {
      marginBottom: theme.spacing.sm,
    },
    testMetric: {
      fontSize: 12,
      color: theme.colors.textSecondary,
      marginBottom: theme.spacing.xs,
    },
    dataContainer: {
      marginTop: theme.spacing.sm,
      padding: theme.spacing.sm,
      backgroundColor: theme.colors.background,
      borderRadius: theme.borderRadius.sm,
    },
    dataTitle: {
      fontSize: 12,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: theme.spacing.xs,
    },
    dataText: {
      fontSize: 10,
      color: theme.colors.textSecondary,
      fontFamily: 'monospace',
    },
    testActions: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    runButton: {
      backgroundColor: theme.colors.primary,
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      borderRadius: theme.borderRadius.md,
    },
    runButtonText: {
      color: theme.colors.background,
      fontSize: 14,
      fontWeight: 'bold',
    },
    infoButton: {
      padding: theme.spacing.sm,
    },
    errorContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      gap: theme.spacing.md,
    },
    errorText: {
      fontSize: 16,
      color: theme.colors.error,
      textAlign: 'center',
    },
  });

export default AIProfileEnhancementDebugger;
