import React from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { Text } from '@components/ui';
import { AgreementTheme } from '@components/ui/AgreementTheme';
import { format } from 'date-fns';
import { colorWithOpacity, type Theme } from '@design-system';
import { useTheme } from '@design-system';

interface Change {
  type: 'added' | 'removed' | 'modified',
  field: string,
  oldValue?: any,
  newValue?: any,
}
interface VersionDiffProps {
  changes: Change[],
  oldVersion: number,
  newVersion: number,
  createdAt: string,
  createdByName: string,
}
export default function VersionDiff({
  changes,
  oldVersion,
  newVersion,
  createdAt,
  createdByName,
}: VersionDiffProps) {
  const theme = useTheme();
  const styles = createStyles(theme);
  const renderChangeValue = (value: any) => {
    if (typeof value === 'object') {
      return JSON.stringify(value, null, 2);
    }
    return String(value);
  }
  const renderChange = (change: Change) => {
    switch (change.type) {
      case 'added': ,
        return (
          <View style={styles.changeContainer} key={change.field}>
            <Text style={styles.fieldName}>{change.field}</Text>
            <View style={[styles.changeContent, styles.addedContent]}>
              <Text style={styles.addedText}>+ {renderChangeValue(change.newValue)}</Text>
            </View>
          </View>
        );

      case 'removed': ,
        return (
          <View style={styles.changeContainer} key={change.field}>
            <Text style={styles.fieldName}>{change.field}</Text>
            <View style={[styles.changeContent, styles.removedContent]}>
              <Text style={styles.removedText}>- {renderChangeValue(change.oldValue)}</Text>
            </View>
          </View>
        );

      case 'modified': ,
        return (
          <View style={styles.changeContainer} key={change.field}>
            <Text style={styles.fieldName}>{change.field}</Text>
            <View style={[styles.changeContent, styles.removedContent]}>
              <Text style={styles.removedText}>- {renderChangeValue(change.oldValue)}</Text>
            </View>
            <View style={[styles.changeContent, styles.addedContent]}>
              <Text style={styles.addedText}>+ {renderChangeValue(change.newValue)}</Text>
            </View>
          </View>
        );
    }
  }
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.versionInfo}>
          Version {oldVersion} → {newVersion}
        </Text>
        <Text style={styles.timestamp}>
          {format(new Date(createdAt), "MMM d, yyyy 'at' h:mm a")}
        </Text>
        <Text style={styles.author}>by {createdByName}</Text>
      </View>
      <ScrollView style={styles.changesContainer}>
        {changes.map(change => renderChange(change))}
      </ScrollView>
    </View>
  );
}
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      backgroundColor: AgreementTheme.theme.colors.surface,
      borderRadius: AgreementTheme.borderRadius.md,
      padding: AgreementTheme.spacing.md,
      marginBottom: AgreementTheme.spacing.md,
    },
    header: {
      marginBottom: AgreementTheme.spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: AgreementTheme.theme.colors.border,
      paddingBottom: AgreementTheme.spacing.sm,
    },
    versionInfo: {
      ...AgreementTheme.typography.subtitle,
      color: AgreementTheme.theme.colors.text,
      fontWeight: '600',
    },
    timestamp: {
      ...AgreementTheme.typography.caption,
      color: AgreementTheme.theme.colors.textSecondary,
      marginTop: AgreementTheme.spacing.xs,
    },
    author: {
      ...AgreementTheme.typography.caption,
      color: AgreementTheme.theme.colors.textSecondary,
      fontStyle: 'italic',
    },
    changesContainer: {
      maxHeight: 300,
    },
    changeContainer: {
      marginBottom: AgreementTheme.spacing.md,
    },
    fieldName: {
      ...AgreementTheme.typography.caption,
      color: AgreementTheme.theme.colors.textSecondary,
      marginBottom: AgreementTheme.spacing.xs,
    },
    changeContent: {
      padding: AgreementTheme.spacing.sm,
      borderRadius: AgreementTheme.borderRadius.sm,
      marginBottom: AgreementTheme.spacing.xs,
    },
    addedContent: {
      backgroundColor: AgreementTheme.theme.colors.success + '20',
    },
    removedContent: {
      backgroundColor: AgreementTheme.theme.colors.error + '20',
    },
    addedText: {
      ...AgreementTheme.typography.code,
      color: AgreementTheme.theme.colors.success,
    },
    removedText: {
      ...AgreementTheme.typography.code,
      color: AgreementTheme.theme.colors.error,
    },
  });
