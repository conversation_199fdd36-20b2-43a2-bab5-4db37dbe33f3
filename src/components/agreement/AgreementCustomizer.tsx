import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TextInput, TouchableOpacity, Switch, Alert, ActivityIndicator } from 'react-native';
import { ChevronDown, ChevronUp, Edit2, Trash2, PlusCircle, Info } from 'lucide-react-native';
import { useAuth } from '@context/AuthContext';
import { useToast } from '@core/errors';
import { useColorFix } from '@hooks/useColorFix';
import { colorWithOpacity, type Theme } from '@design-system';
import { useTheme } from '@design-system';

interface Field {
  name: string,
  type: string,
  required: boolean,
  options?: string[],
  value?: any,
}
interface SectionContent {
  description: string,
  fields: Field[],
}
interface Section {
  key: string,
  title: string,
  order: number,
  is_required: boolean,
  content: SectionContent,
  isExpanded?: boolean,
}
interface AgreementTemplateProps {
  id: string,
  name: string,
  description: string,
  sections: {
    sections: Section[],
  }
}
interface AgreementCustomizerProps {
  template: AgreementTemplateProps,
  onComplete: (sections: Section[], title: string) => void,
  onBack: () => void,
}
export default function AgreementCustomizer({
  template,
  onComplete,
  onBack,
}: AgreementCustomizerProps) {
  const theme = useTheme();
  const styles = createStyles(theme);
  const { fix } = useColorFix();
  const [title, setTitle] = useState(`${template.name} - Roommate Agreement`);
  const [sections, setSections] = useState<Section[]>([]);
  const [loading, setLoading] = useState(false);
  const { state, actions } = useAuth();
  const { showToast } = useToast();

  // Initialize sections from template with proper field defaults;
  useEffect(() => {
  const initializeFieldValue = (field: Field) => {
  if (field.value !== undefined) return field.value;
      ;
      switch (field.type) {
        case 'text': ,
        case 'textarea': ,
        case 'currency': ,
        case 'select': ,
        case 'date': ,
          return '';
        case 'boolean': ,
          return false;
        case 'multiselect': ,
          return [];
        case 'period': ,
          return { duration: '', unit: '' }
        case 'day_of_month': ,
          return '';
        case 'time_range': ,
          return { start: '', end: '' }
        default: ,
          return '';
      }
    }
    const initialSections = template.sections.sections.map((section, index) => ({
      ...section,
      order: index,
      isExpanded: index === 0, // Expand first section by default;
      content: {
        ...section.content,
        fields: section.content.fields.map(field => ({
          ...field,
          value: initializeFieldValue(field),
        }));
      }
    }));
    setSections(initialSections);
  }, [template]);

  const toggleSection = (index: number) => {
  setSections(prevSections => {
  prevSections.map((section, i) => {
  i === index ? { ...section, isExpanded: !section.isExpanded } : section,
      );
    );
  }
  const moveSection = (index: number, direction: 'up' | 'down') => {
  if (
      (direction === 'up' && index === 0) ||;
      (direction === 'down' && index === sections.length - 1);
    ) {
      return null;
    }
    const newSections = [...sections];
    const newIndex = direction === 'up' ? index - 1 : index + 1,
    const temp = newSections[index];
    newSections[index] = newSections[newIndex];
    newSections[newIndex] = temp;

    // Update order property;
    newSections.forEach((section, i) => {
  section.order = i;
    });

    setSections(newSections);
  }
  const updateSectionTitle = (index: number, newTitle: string) => {
  setSections(prevSections => {
  prevSections.map((section, i) => (i === index ? { ...section, title: newTitle } : section)),
    );
  }
  const updateFieldValue = (sectionIndex: number, fieldIndex: number, value: any) => {
  setSections(prevSections => {
  const newSections = [...prevSections];
      const section = { ...newSections[sectionIndex] }
      const content = { ...section.content }
      const fields = [...content.fields];

      fields[fieldIndex] = {
        ...fields[fieldIndex],
        value,
      }
      content.fields = fields;
      section.content = content;
      newSections[sectionIndex] = section;

      return newSections;
    });
  }
  const addCustomSection = () => {
  const newSection: Section = {
      key: `custom-${Date.now()}`,
      title: 'New Custom Section',
      order: sections.length,
      is_required: false,
      content: {
        description: 'Custom section description',
        fields: [,
          {
            name: 'customText',
            type: 'text',
            required: false,
            value: '',
          },
        ],
      },
      isExpanded: true,
    }
    setSections([...sections, newSection]);
  }
  const removeSection = (index: number) => {
  const section = sections[index];

    if (section.is_required) {
      showToast('error', 'Cannot remove required sections');
      return null;
    }
          Alert.alert('Remove Section', `Are you sure you want to remove "${section.title}"?`, [
      {
        text: 'Cancel',
        style: 'cancel',
      },
      {
        text: 'Remove',
        style: 'destructive',
        onPress: () => {
  const newSections = sections.filter((_, i) => i !== index);
          // Reorder sections;
          newSections.forEach((section, i) => {
  section.order = i;
          });
          setSections(newSections);
        },
      },
    ]);
  }
  const validateForm = () => {
  let isValid = true;
    let errorMessage = '';

    if (!title.trim()) {
      errorMessage = 'Agreement title is required';
      isValid = false;
      showToast('error', errorMessage);
      return isValid;
    }
    // Check required fields in each section;
    for (const section of sections) {
      for (const field of section.content.fields) {
        if (field.required) {
          let isEmpty = false;
          ;
          switch (field.type) {
            case 'text': ,
            case 'textarea': ,
              isEmpty = !field.value || (typeof field.value === 'string' && field.value.trim() === '');
              break;
              ;
            case 'currency': ,
              // Allow 0 as a valid currency value, just not empty string;
              isEmpty = field.value === undefined || field.value === null || field.value === '';
              break;
              ;
            case 'select': ,
              isEmpty = !field.value || field.value === '';
              break;
              ;
            case 'boolean': ,
              // Boolean fields are never empty since false is a valid value;
              isEmpty = false;
              break;
              ;
            case 'multiselect': ,
              isEmpty = !field.value || !Array.isArray(field.value) || field.value.length === 0;
              break;
              ;
            case 'period': ,
              isEmpty = !field.value || ;
                       typeof field.value !== 'object' ||;
                       !field.value.duration || ;
                       !field.value.unit || ;
                       field.value.duration === '' || ;
                       field.value.unit === '';
              break;
              ;
            case 'day_of_month': ,
              isEmpty = field.value === undefined || ;
                       field.value === null || ;
                       field.value === '' || ;
                       field.value < 1 || ;
                       field.value > 31;
              break;
              ;
            case 'time_range': ,
              isEmpty = !field.value || ;
                       typeof field.value !== 'object' ||;
                       !field.value.start || ;
                       !field.value.end || ;
                       field.value.start === '' || ;
                       field.value.end === '';
              break;
              ;
            case 'date': ,
              isEmpty = !field.value || field.value === '';
              break;
              ;
            default: ,
              isEmpty = !field.value || (typeof field.value === 'string' && field.value.trim() === '');
          }
          if (isEmpty) {
            errorMessage = `Please fill out all required fields in "${section.title}" - Missing: ${field.name}`;
            isValid = false;
            break;
          }
        }
      }
      if (!isValid) break;
    }
    if (!isValid) {
      showToast('error', errorMessage);
    }
    return isValid;
  }
  const handleComplete = () => {
  if (!validateForm()) {
      return null;
    }
    setLoading(true);
    ;
    // Call onComplete directly instead of setTimeout simulation;
    try {
      onComplete(sections, title);
    } catch (error) {
      console.error('Error in onComplete:', error);
      showToast('error', 'Failed to proceed to review');
    } finally {
      setLoading(false);
    }
  }
  const renderField = (field: Field, sectionIndex: number, fieldIndex: number) => {
  switch (field.type) {
      case 'text': ,
        return (
    <TextInput style={styles.textInput} value={field.value || ''} onChangeText={text ={}> updateFieldValue(sectionIndex, fieldIndex, text)} placeholder={`Enter ${field.name.replace(/_/g, ' ')}`}
            multiline={false}
          />
        );
      case 'textarea': ,
        return (
    <TextInput style={[styles.textInput, styles.textArea]} value={field.value || ''} onChangeText={text ={}> updateFieldValue(sectionIndex, fieldIndex, text)} placeholder={`Enter ${field.name.replace(/_/g, ' ')}`}
            multiline={true} numberOfLines={4}
          />
        );
      case 'currency': ,
        return (
    <View style={styles.currencyContainer}>
            <Text style={styles.currencySymbol}>$</Text>
            <TextInput style={[styles.textInput, styles.currencyInput]} value={field.value || ''} onChangeText={text ={}> updateFieldValue(sectionIndex, fieldIndex, text)} placeholder="0.00";
              keyboardType="numeric";
            />
          </View>
        );
      case 'period': ,
        const periodValue = field.value || { duration: '', unit: '' }
        return (
    <View style={styles.periodContainer}>
            <TextInput style={[styles.textInput, styles.periodInput]} value={periodValue.duration || ''} onChangeText={duration ={}> updateFieldValue(sectionIndex, fieldIndex, {
                ...periodValue,
                duration: duration ,
              })}
              placeholder="12";
              keyboardType="numeric";
            />
            <View style={styles.selectContainer}>
              {['months', 'weeks', 'days'].map((unit, unitIndex) => (
                <TouchableOpacity key={unitIndex} style={[
                    styles.periodUnit,
                    periodValue.unit === unit && styles.selectedPeriodUnit;
                  ]} onPress={() => updateFieldValue(sectionIndex, fieldIndex, {
                    ...periodValue,
                    unit: unit ,
                  })}
                >
                  <Text
                    style={[
                      styles.periodUnitText,
                      periodValue.unit === unit && styles.selectedPeriodUnitText,
                    ]}
                  >
                    {unit}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        );
      case 'day_of_month': ,
        return (
    <View style={styles.dayPickerContainer}>
            <View style={styles.dayPickerGrid}>
              {Array.from({ length: 31 }, (_, i) => i + 1).map(day => (
                <TouchableOpacity key={day} style={[
                    styles.dayOption,
                    field.value === day && styles.selectedDayOption;
                  ]} onPress={() => updateFieldValue(sectionIndex, fieldIndex, day)}
                >
                  <Text
                    style={[
                      styles.dayOptionText,
                      field.value === day && styles.selectedDayOptionText,
                    ]}
                  >
                    {day}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        );
      case 'time_range': ,
        const timeValue = field.value || { start: '', end: '' }
        return (
    <View style={styles.timeRangeContainer}>
            <View style={styles.timeInputRow}>
              <Text style={styles.timeLabel}>From:</Text>
              <TextInput style={styles.timeInput} value={timeValue.start || ''} onChangeText={start ={}> updateFieldValue(sectionIndex, fieldIndex, {
                  ...timeValue,
                  start: start ,
                })}
                placeholder="10: 00 PM",
              />
            </View>
            <View style={styles.timeInputRow}>
              <Text style={styles.timeLabel}>To:</Text>
              <TextInput style={styles.timeInput} value={timeValue.end || ''} onChangeText={end ={}> updateFieldValue(sectionIndex, fieldIndex, {
                  ...timeValue,
                  end: end ,
                })}
                placeholder="7: 00 AM",
              />
            </View>
          </View>
        );
      case 'multiselect': ,
        if (!field.options || field.options.length === 0) {
          return <Text style={styles.errorText}>No options available</Text>
        }
        const selectedValues = field.value || [];
        return (
    <View style={styles.multiselectContainer}>
            {field.options.map((option, optionIndex) => {
  const isSelected = selectedValues.includes(option);
              return (
    <TouchableOpacity key={optionIndex} style={[styles.multiselectOption, isSelected && styles.selectedMultiselectOption]} onPress={() => {
  let newSelected;
                    if (isSelected) {
                      newSelected = selectedValues.filter((val: string) => val !== option),
                    } else {
                      newSelected = [...selectedValues, option];
                    }
                    updateFieldValue(sectionIndex, fieldIndex, newSelected);
                  }}
      >
                  <Text
                    style={[
                      styles.multiselectOptionText,
                      isSelected && styles.selectedMultiselectOptionText,
                    ]}
                  >
                    {option}
                  </Text>
                </TouchableOpacity>
              );
            })}
          </View>
        );
      case 'boolean': ,
        return (
    <Switch value={field.value || false} onValueChange={value ={}> updateFieldValue(sectionIndex, fieldIndex, value)} trackColor={ false: '#CBD5E1', true: '#8B5CF6' }
            thumbColor={field.value ? theme.colors.background : theme.colors.background}
          />
        );
      case 'select': ,
        if (!field.options || field.options.length === 0) {
          return <Text style={styles.errorText}>No options available</Text>
        }
        return (
    <View style={styles.selectContainer}>
            {field.options.map((option, optionIndex) => (
              <TouchableOpacity key={optionIndex} style={[styles.selectOption, field.value === option && styles.selectedOption]} onPress={() => updateFieldValue(sectionIndex, fieldIndex, option)}
              >
                <Text
                  style={[
                    styles.selectOptionText,
                    field.value === option && styles.selectedOptionText,
                  ]}
                >
                  {option}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        );
      case 'date': ,
        // In a real implementation, use DateTimePicker;
        return (
    <TextInput style={styles.textInput} value={field.value || ''} onChangeText={text ={}> updateFieldValue(sectionIndex, fieldIndex, text)} placeholder="YYYY-MM-DD";
          />
        );
      default: ,
        return <Text style={styles.errorText}>Unsupported field type: {field.type}</Text>
    }
  }
  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        <View style={styles.titleContainer}>
          <Text style={styles.label}>Agreement Title</Text>
          <TextInput style={styles.titleInput} value={title} onChangeText={setTitle} placeholder="Enter agreement title";
          />
        </View>
        <View style={styles.sectionsContainer}>
          <Text style={styles.sectionHeader}>Agreement Sections</Text>
          {sections.map((section, sectionIndex) => (
            <View key={section.key} style={styles.sectionItem}>
              <TouchableOpacity style={styles.sectionHeader} onPress={() => toggleSection(sectionIndex)}
              >
                <View style={styles.sectionTitleRow}>
                  <Text style={styles.sectionTitle}>{section.title}</Text>
                  {section.is_required && (
                    <View style={styles.requiredBadge}>
                      <Text style={styles.requiredBadgeText}>Required</Text>
                    </View>
                  )}
                </View>
                <View style={styles.sectionActions}>
                  {!section.is_required && (
                    <TouchableOpacity style={styles.actionButton} onPress={() => removeSection(sectionIndex)}
                    >
                      <Trash2 size={18} color={theme.colors.error} />
                    </TouchableOpacity>
                  )}
                  <TouchableOpacity style={styles.actionButton} onPress={() => toggleSection(sectionIndex)}
                  >
                    {section.isExpanded ? (
                      <ChevronUp size={20} color={{theme.colors.textSecondary} /}>
                    ) : (,
                      <ChevronDown size={20} color={{theme.colors.textSecondary} /}>
                    )}
                  </TouchableOpacity>
                </View>
              </TouchableOpacity>
              {section.isExpanded && (
                <View style={styles.sectionContent}>
                  <View style={styles.sectionTitleEdit}>
                    <Text style={styles.label}>Section Title</Text>
                    <TextInput style={styles.textInput} value={section.title} onChangeText={text ={}> updateSectionTitle(sectionIndex, text)}
                    />
                  </View>
                  <Text style={styles.sectionDescription}>{section.content.description}</Text>
                  {section.content.fields.map((field, fieldIndex) => (
                    <View key={`${section.key}-${field.name}`} style={styles.fieldContainer}>
                      <View style={styles.fieldLabelRow}>
                        <Text style={styles.fieldLabel}>
                          {field.name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                          {field.required && <Text style={styles.requiredAsterisk}> *</Text>
                        </Text>
                      </View>
                      {renderField(field, sectionIndex, fieldIndex)}
                    </View>
                  ))}
                  <View style={styles.sectionOrderControls}>
                    <TouchableOpacity style={[styles.orderButton, sectionIndex === 0 && styles.disabledButton]} disabled={sectionIndex === 0} onPress={() => moveSection(sectionIndex, 'up')}
                    >
                      <Text style={styles.orderButtonText}>Move Up</Text>
                    </TouchableOpacity>
                    <TouchableOpacity style={[
                        styles.orderButton,
                        sectionIndex === sections.length - 1 && styles.disabledButton,
                      ]} disabled={sectionIndex === sections.length - 1} onPress={() => moveSection(sectionIndex, 'down')}
                    >
                      <Text style={styles.orderButtonText}>Move Down</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              )}
            </View>
          ))}
          <TouchableOpacity style={styles.addSectionButton} onPress={addCustomSection}>
            <PlusCircle size={20} color={"#6366F1" /}>
            <Text style={styles.addSectionButtonText}>Add Custom Section</Text>
          </TouchableOpacity>
        </View>
        <View style={styles.infoBox}>
          <Info size={16} color={"#6366F1" /}>
          <Text style={styles.infoText}>
            All parties will be able to review this agreement before signing;
          </Text>
        </View>
      </ScrollView>
      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.backButton} onPress={onBack}>
          <Text style={styles.backButtonText}>Back</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.continueButton} onPress={handleComplete} disabled={loading}>
          {loading ? (
            <ActivityIndicator size="small" color={{theme.colors.background} /}>
          ) : (,
            <Text style={styles.continueButtonText}>Continue to Review</Text>
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
}
const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  scrollContainer: {
    flex: 1,
    padding: 20,
  },
  titleContainer: {
    marginBottom: 24,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: 8,
  },
  titleInput: {
    backgroundColor: theme.colors.background,
    borderWidth: 1,
    borderColor: '#CBD5E1',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: theme.colors.text,
  },
  sectionsContainer: {
    marginBottom: 24,
  },
  sectionHeader: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 16,
  },
  sectionItem: {
    backgroundColor: theme.colors.background,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: theme.colors.border,
    marginBottom: 16,
    overflow: 'hidden',
  },
  sectionTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
  },
  requiredBadge: {
    backgroundColor: '#EEF2FF',
    borderRadius: 4,
    paddingHorizontal: 8,
    paddingVertical: 2,
    marginLeft: 8,
  },
  requiredBadgeText: {
    fontSize: 12,
    color: '#6366F1',
    fontWeight: '500',
  },
  sectionActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    padding: 8,
    marginLeft: 4,
  },
  sectionContent: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  sectionTitleEdit: {
    marginBottom: 16,
  },
  sectionDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 16,
    lineHeight: 20,
  },
  fieldContainer: {
    marginBottom: 16,
  },
  fieldLabelRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  fieldLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
  },
  requiredAsterisk: {
    color: theme.colors.error,
  },
  textInput: {
    backgroundColor: theme.colors.background,
    borderWidth: 1,
    borderColor: '#CBD5E1',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: theme.colors.text,
  },
  textArea: {
    height: 120,
    textAlignVertical: 'top',
  },
  selectContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  selectOption: {
    backgroundColor: '#F1F5F9',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 8,
    marginBottom: 8,
  },
  selectedOption: {
    backgroundColor: '#EEF2FF',
    borderWidth: 1,
    borderColor: '#6366F1',
  },
  selectOptionText: {
    fontSize: 14,
    color: '#475569',
  },
  selectedOptionText: {
    color: '#6366F1',
    fontWeight: '500',
  },
  sectionOrderControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  orderButton: {
    backgroundColor: '#F1F5F9',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 8,
    alignItems: 'center',
    flex: 0.48,
  },
  orderButtonText: {
    fontSize: 14,
    color: '#475569',
    fontWeight: '500',
  },
  disabledButton: {
    opacity: 0.5,
  },
  addSectionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#EEF2FF',
    borderWidth: 1,
    borderColor: '#C7D2FE',
    borderRadius: 12,
    paddingVertical: 12,
    marginBottom: 24,
  },
  addSectionButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#6366F1',
    marginLeft: 8,
  },
  infoBox: {
    flexDirection: 'row',
    backgroundColor: '#F8FAFC',
    borderWidth: 1,
    borderColor: '#CBD5E1',
    borderRadius: 8,
    padding: 12,
    marginBottom: 24,
    alignItems: 'center',
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    color: '#475569',
    marginLeft: 8,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
    backgroundColor: theme.colors.background,
  },
  backButton: {
    backgroundColor: '#F1F5F9',
    borderRadius: 8,
    paddingHorizontal: 20,
    paddingVertical: 12,
    flex: 0.48,
    alignItems: 'center',
  },
  backButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#475569',
  },
  continueButton: {
    backgroundColor: '#6366F1',
    borderRadius: 8,
    paddingHorizontal: 20,
    paddingVertical: 12,
    flex: 0.48,
    alignItems: 'center',
  },
  continueButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.background,
  },
  errorText: {
    color: theme.colors.error,
    fontSize: 14,
    marginTop: 4,
  },
  // Currency field styles;
  currencyContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
    borderWidth: 1,
    borderColor: '#CBD5E1',
    borderRadius: 8,
    paddingLeft: 12,
  },
  currencySymbol: {
    fontSize: 16,
    color: theme.colors.text,
    marginRight: 8,
  },
  currencyInput: {
    flex: 1,
    borderWidth: 0,
    paddingLeft: 0,
  },
  // Period field styles;
  periodContainer: {
    backgroundColor: theme.colors.background,
    borderWidth: 1,
    borderColor: '#CBD5E1',
    borderRadius: 8,
    padding: 12,
  },
  periodInput: {
    borderWidth: 0,
    padding: 0,
    marginBottom: 12,
  },
  periodUnit: {
    backgroundColor: '#F1F5F9',
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
    marginBottom: 8,
  },
  selectedPeriodUnit: {
    backgroundColor: '#EEF2FF',
    borderWidth: 1,
    borderColor: '#6366F1',
  },
  periodUnitText: {
    fontSize: 14,
    color: '#475569',
  },
  selectedPeriodUnitText: {
    color: '#6366F1',
    fontWeight: '500',
  },
  // Day of month picker styles;
  dayPickerContainer: {
    backgroundColor: theme.colors.background,
    borderWidth: 1,
    borderColor: '#CBD5E1',
    borderRadius: 8,
    padding: 12,
  },
  dayPickerGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  dayOption: {
    width: 40,
    height: 40,
    backgroundColor: '#F1F5F9',
    borderRadius: 6,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  selectedDayOption: {
    backgroundColor: '#EEF2FF',
    borderWidth: 1,
    borderColor: '#6366F1',
  },
  dayOptionText: {
    fontSize: 14,
    color: '#475569',
  },
  selectedDayOptionText: {
    color: '#6366F1',
    fontWeight: '500',
  },
  // Time range styles;
  timeRangeContainer: {
    backgroundColor: theme.colors.background,
    borderWidth: 1,
    borderColor: '#CBD5E1',
    borderRadius: 8,
    padding: 12,
  },
  timeInputRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  timeLabel: {
    fontSize: 14,
    color: theme.colors.text,
    width: 50,
    marginRight: 12,
  },
  timeInput: {
    flex: 1,
    backgroundColor: '#F8F9FA',
    borderWidth: 1,
    borderColor: '#E2E8F0',
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 16,
    color: theme.colors.text,
  },
  // Multiselect styles;
  multiselectContainer: {
    backgroundColor: theme.colors.background,
    borderWidth: 1,
    borderColor: '#CBD5E1',
    borderRadius: 8,
    padding: 8,
  },
  multiselectOption: {
    backgroundColor: '#F1F5F9',
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 8,
    marginBottom: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectedMultiselectOption: {
    backgroundColor: '#EEF2FF',
    borderWidth: 1,
    borderColor: '#6366F1',
  },
  multiselectOptionText: {
    fontSize: 14,
    color: '#475569',
  },
  selectedMultiselectOptionText: {
    color: '#6366F1',
    fontWeight: '500',
  },
  // Multiselect styles;
  multiselectContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  multiselectOption: {
    backgroundColor: '#F1F5F9',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  selectedMultiselectOption: {
    backgroundColor: '#EEF2FF',
    borderColor: '#6366F1',
  },
  multiselectOptionText: {
    fontSize: 14,
    color: '#475569',
  },
  selectedMultiselectOptionText: {
    color: '#6366F1',
    fontWeight: '500',
  },
});

