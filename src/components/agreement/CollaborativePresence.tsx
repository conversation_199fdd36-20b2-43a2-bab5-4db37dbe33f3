import React, { useEffect, useState } from 'react';,
  import {
   View, StyleSheet ,
  } from 'react-native';
import {,
  createClientComponentClient 
} from '@supabase/ssr';,
  import {
   useAuth ,
  } from '@context/AuthContext';
;,
  ;
import {,
  AgreementTheme 
} from '@components/ui/AgreementTheme';,
  import {
   colorWithOpacity, type Theme ,
  } from '@design-system';
import {,
  useTheme 
} from '@design-system';,
  import {
   Avatar, Text ,
  } from '@components/ui';
interface CollaborativePresenceProps { agreementId: string },
  interface Presence { userId: string,
  username: string,
  avatarUrl?: string,
  lastSeen: string,
  isTyping: boolean },
  export default function CollaborativePresence({
  agreementId }: CollaborativePresenceProps) {,
  const theme = useTheme()
  const styles = createStyles(theme),
  const supabase = createClientComponentClient()
  const { state, actions  } = useAuth(),
  const [presences, setPresences] = useState<Presence[]>([]),
  useEffect(() => {;
  let presenceTimeout: NodeJS.Timeout;,
  const channel = supabase.channel(`presence:${agreementId}`, {,
  config: {,
    presence: {,
  key: authState.user? .id)
        },
      },;,
  });
    // Handle presence updates;,
  channel.on('presence', { event    : 'sync' } () = > { const state = channel.presenceState(),
  const presenceList = Object.entries(state).map(([userId, userStates]) => {, ,
  const userState = userStates[0] as any,
  return {
          userId;,
  username: userState.username,
          avatarUrl: userState.avatarUrl,
          lastSeen: userState.lastSeen,
          isTyping: userState.isTyping },
  })
      setPresences(presenceList),
  })
    // Join the channel with user info;,
  channel.subscribe(async (status) = > {
  if (status === 'SUBSCRIBED') {,
  // Get user profile info;
        const { data: profile } = await supabase.from('user_profiles'),
  .select('username, avatar_url'),
  .eq('id', authState.user? .id),
  .single()
        await channel.track({ username  : profile?.username,
  avatarUrl: profile? .avatar_url)
          lastSeen : new Date().toISOString(),
  isTyping: false })
        // Start sending presence updates;,
  presenceTimeout = setInterval(async () => { await channel.track({
            username: profile? .username),
            avatarUrl   : profile?.avatar_url),
  lastSeen: new Date().toISOString(),
    isTyping: false }),
  } 30000) // Update presence every 30 seconds
      },
  })
    return () => {,
  clearInterval(presenceTimeout)
      channel.unsubscribe(),
  }
  }; [agreementId]),
  return (
    <View style= {styles.container}>,
  {presences.map((presence) => (
        <View key={presence.userId} style={styles.presenceItem}>,
  <Avatar
            size="small", ,
  source= {{  presence.avatarUrl ? { uri    : presence.avatarUrl     }} : undefined}
            fallback={presence.username?.[0]?.toUpperCase() || '?'},
  />
          <Text style={styles.username}>{presence.username}</Text>,
  {presence.isTyping && (
            <Text style={styles.typingIndicator}>typing...</Text>,
  )}
        </View>,
  ))}
    </View>,
  )
},
  const createStyles = (theme: any) => StyleSheet.create({ container: {,
    flexDirection: 'row',
  alignItems: 'center',
    gap: AgreementTheme.spacing.sm },
  presenceItem: { flexDirection: 'row'),
    alignItems: 'center'),
    gap: AgreementTheme.spacing.xs },
  username: { ...AgreementTheme.typography.caption,
    color: AgreementTheme.theme.colors.textSecondary },
  typingIndicator: {,
  ...AgreementTheme.typography.caption;
    color: AgreementTheme.theme.colors.primary.main,
    fontStyle: 'italic'),
  },
})