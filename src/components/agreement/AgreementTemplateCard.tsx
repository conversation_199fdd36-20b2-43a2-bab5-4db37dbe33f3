import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Feather } from '@expo/vector-icons';
import { AgreementTemplate } from '@/data/agreementTemplates';
import { colorWithOpacity, type Theme } from '@design-system';
import { useTheme } from '@design-system';

interface AgreementTemplateCardProps {
  template: AgreementTemplate,
  isSelected: boolean,
  onSelect: () => void,
}
export default function AgreementTemplateCard({
  template,
  isSelected,
  onSelect,
}: AgreementTemplateCardProps) {
  const theme = useTheme();
  const styles = createStyles(theme);
  return (
    <TouchableOpacity
      style={[styles.container, isSelected && styles.selectedContainer]}
      onPress={onSelect}
      activeOpacity={0.8}
    >
      <View style={styles.header}>
        <View style={styles.iconContainer}>
          <Feather name='file-text' size={24} color={{isSelected ? '#6366F1' : '#4B5563'} /}>
        </View>
        <View style={styles.titleContainer}>
          <Text style={[styles.title, isSelected && styles.selectedText]}>{template.name}</Text>
          <Text style={styles.description}>{template.description}</Text>
          {template.tags && template.tags.length > 0 && (
            <View style={styles.tagsContainer}>
              {template.tags.slice(0, 3).map((tag, index) => (
                <View key={index} style={styles.tagPill}>
                  <Text style={styles.tagText}>{tag}</Text>
                </View>
              ))}
            </View>
          )}
        </View>
        <View style={styles.checkContainer}>
          {isSelected ? (
            <View style={styles.checkCircle}>
              <Feather name='check' size={16} color={'#FFFFFF' /}>
            </View>
          ) : (,
            <View style={{styles.emptyCircle} /}>
          )}
        </View>
      </View>
      <View style={{styles.divider} /}>
      <View style={styles.sectionsContainer}>
        <Text style={styles.sectionsTitle}>Includes sections for:</Text>
        {template.sections.slice(0, 4).map((section, index) => (
          <View key={index} style={styles.sectionItem}>
            <Feather
              name='check-circle';
              size={16}
              color={isSelected ? '#6366F1' : '#6B7280'}
              style={styles.sectionIcon}
            />
            <Text style={[styles.sectionText, isSelected && styles.selectedSectionText]}>
              {section.section_title || section.title}
            </Text>
          </View>
        ))}
        {template.sections.length > 4 && (
          <Text style={styles.moreSections}>+{template.sections.length - 4} more sections</Text>
        )}
      </View>
    </TouchableOpacity>
  );
}
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      backgroundColor: theme.colors.background,
      borderRadius: 12,
      padding: 16,
      marginBottom: 16,
      borderWidth: 1,
      borderColor: theme.colors.border,
      shadowColor: theme.colors.text,
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 2,
      elevation: 2,
    },
    selectedContainer: {
      borderColor: '#6366F1',
      backgroundColor: '#F5F3FF',
    },
    header: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      marginBottom: 12,
    },
    iconContainer: {
      width: 40,
      height: 40,
      borderRadius: 8,
      backgroundColor: '#F3F4F6',
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 12,
    },
    titleContainer: {
      flex: 1,
    },
    title: {
      fontSize: 18,
      fontWeight: '600',
      color: '#1F2937',
      marginBottom: 4,
    },
    selectedText: {
      color: '#4F46E5',
    },
    description: {
      fontSize: 14,
      color: '#4B5563',
      marginBottom: 8,
    },
    tagsContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
    },
    tagPill: {
      backgroundColor: '#E5E7EB',
      paddingHorizontal: 8,
      paddingVertical: 2,
      borderRadius: 12,
      marginRight: 6,
      marginBottom: 4,
    },
    tagText: {
      fontSize: 12,
      color: '#4B5563',
    },
    checkContainer: {
      marginLeft: 8,
      alignItems: 'center',
      justifyContent: 'center',
      width: 24,
    },
    checkCircle: {
      width: 24,
      height: 24,
      borderRadius: 12,
      backgroundColor: '#6366F1',
      justifyContent: 'center',
      alignItems: 'center',
    },
    emptyCircle: {
      width: 24,
      height: 24,
      borderRadius: 12,
      borderWidth: 2,
      borderColor: theme.colors.border,
    },
    divider: {
      height: 1,
      backgroundColor: '#E5E7EB',
      marginVertical: 12,
    },
    sectionsContainer: {
      paddingTop: 4,
    },
    sectionsTitle: {
      fontSize: 14,
      fontWeight: '500',
      color: '#4B5563',
      marginBottom: 8,
    },
    sectionItem: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 6,
    },
    sectionIcon: {
      marginRight: 8,
    },
    sectionText: {
      fontSize: 14,
      color: '#1F2937',
    },
    selectedSectionText: {
      color: '#4F46E5',
    },
    moreSections: {
      fontSize: 14,
      color: '#6B7280',
      fontStyle: 'italic',
      marginTop: 4,
    },
  });
