import React, { useState } from 'react';
import { View, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import { Text } from '@components/ui';
import { Button } from '@design-system';
import { Bell, CheckCircle, Clock, Send } from 'lucide-react-native';
import { supabase } from '@utils/supabaseUtils';
import { useColorFix } from '@hooks/useColorFix';
import { colorWithOpacity, type Theme } from '@design-system';
import { useTheme } from '@design-system';

interface AgreementRemindersProps {
  agreementId: string,
  participants: any[],
  onSuccess: () => void,
}
export default function AgreementReminders({
  agreementId,
  participants,
  onSuccess,
}: AgreementRemindersProps) {
  const theme = useTheme();
  const styles = createStyles(theme);
  const { fix } = useColorFix();
  const [isReminding, setIsReminding] = useState(false); // Filter participants who haven't signed const pendingParticipants = participants.filter( (p) => p.status !== 'signed' && p.status !== 'declined' ); const handleSendReminders = async () => { if (pendingParticipants.length === 0) { Alert.alert("No Reminders Needed", "All participants have already signed or declined."); return null; } try { setIsReminding(true); // Create a reminder record for each pending participant for (const participant of pendingParticipants) { await supabase.from('agreement_reminders').insert({ agreement_id: agreementId, user_id: participant.user_id, reminder_type: 'signature_needed', created_at: new Date().toISOString(), status: 'sent' }); } // In a real app, this would trigger actual notifications // For now, we'll just show an alert Alert.alert( "Reminders Sent", `Reminders sent to ${pendingParticipants.length} participant${pendingParticipants.length !== 1 ? 's' : ''}`, [{ text: "OK" }] ); onSuccess(); } catch (error) { console.error("Error sending reminders:", error); Alert.alert("Error", "Failed to send reminders. Please try again."); } finally { setIsReminding(false); } }; return ( <View style={styles.container}> <View style={styles.header}> <Bell size={20} color={"#6366F1" /}> <Text style={styles.title}>Signature Reminders</Text> </View> <View style={styles.content}> {pendingParticipants.length === 0 ? ( <View style={styles.completedContainer}> <CheckCircle size={24} color={{theme.colors.success} /}> <Text style={styles.completedText}> All participants have responded to this agreement! </Text> </View> ) : ( <> { <Text style={styles.description}> {pendingParticipants.length} participant{pendingParticipants.length !== 1 ? 's' : ''} still need{pendingParticipants.length === 1 ? 's' : ''} to sign this agreement. </Text> <View style={styles.participantsList}> {pendingParticipants.map((participant) => ( <View key={participant.user_id} style={styles.participantItem}> <View style={styles.participantStatus}> <Clock size={16} color={{fix(theme.colors.warning, theme.colors.warning)} /}> </View> <Text style={styles.participantName}> {participant.profiles?.full_name || 'Unknown User'} </Text> <Text style={styles.participantEmail} numberOfLines={1}> {participant.profiles?.email || ''} </Text> </View> ))} </View> <Button title="Send Reminders" onPress={handleSendReminders} style={styles.reminderButton} loading={isReminding} icon={<Send size={16} color={{theme.colors.background} /}>} /> </> )} </View> </View> );
}
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      backgroundColor: theme.colors.background,
      borderRadius: 12,
      overflow: 'hidden',
      borderWidth: 1,
      borderColor: theme.colors.border,
      marginBottom: 16,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 16,
      backgroundColor: '#EEF2FF',
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    title: { fontSize: 18, fontWeight: '600', color: theme.colors.text, marginLeft: 8 },
    content: { padding: 16 },
    completedContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: '#D1FAE5',
      padding: 12,
      borderRadius: 8,
    },
    completedText: { marginLeft: 8, fontSize: 14, color: '#059669', flex: 1 },
    description: { fontSize: 14, color: theme.colors.textSecondary, marginBottom: 16 },
    participantsList: { marginBottom: 16 },
    participantItem: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 8,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    participantStatus: {
      width: 28,
      height: 28,
      borderRadius: 14,
      backgroundColor: '#FEF3C7',
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 12,
    },
    participantName: { fontSize: 14, fontWeight: '500', color: theme.colors.text, width: 120 },
    participantEmail: { fontSize: 12, color: theme.colors.textSecondary, flex: 1 },
    reminderButton: { backgroundColor: '#6366F1' },
  });
