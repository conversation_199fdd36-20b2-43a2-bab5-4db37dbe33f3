/**;
 * App Layout With Notifications;
 *;
 * A layout wrapper that includes the MatchNotificationManager to handle;
 * match notifications and celebrations across the app.;
 */;

import React from 'react';
import { View, StyleSheet } from 'react-native';
import MatchNotificationManager from '@components/matching/MatchNotificationManager';

interface AppLayoutWithNotificationsProps {
  children: React.ReactNode,
}
/**;
 * A layout wrapper that includes the MatchNotificationManager to handle;
 * match notifications and celebrations across the app.;
 */;
export function AppLayoutWithNotifications({ children }: AppLayoutWithNotificationsProps) {
  return (
    <View style={styles.container}>
      <MatchNotificationManager>{children}</MatchNotificationManager>
    </View>
  );
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default AppLayoutWithNotifications;
