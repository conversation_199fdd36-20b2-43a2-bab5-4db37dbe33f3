import React, { useState } from 'react';
import {
  View;
  Text;
  StyleSheet;
  TouchableOpacity;
  Animated;
  LayoutAnimation;
  Platform;
  UIManager;
} from 'react-native';
import { useTheme } from '@design-system';
import { ChevronDown, ChevronUp } from 'lucide-react-native';
import { Icons } from '@components/common/Icon' // Enable LayoutAnimation for Android;
if (Platform.OS = == 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true)
}
// Use validated icons;
const ChevronDownIcon = Icons.ChevronDown;
const ChevronUpIcon = Icons.ChevronUp;
export interface FAQItem { question: string,
  answer: string }
interface ProviderFAQProps { faqs: FAQItem[],
  title?: string }
export default function ProviderFAQ({ faqs;
  title = 'Frequently Asked Questions' }: ProviderFAQProps) {
  const theme = useTheme()
  const styles = createStyles(theme)
  const [expandedIndex, setExpandedIndex] = useState<number | null>(null)
  const toggleItem = (index: number) => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
    setExpandedIndex(expandedIndex === index ? null   : index)
  }
  if (!faqs || faqs.length === 0) {
    return null;
  }
  return (
    <View style={styles.container}>
      <Text style={styles.title}>{title}</Text>
      {faqs.map((faq; index) => (
        <View key={index} style={[styles.faqItem, expandedIndex = =={ index && styles.expandedItem]}}>
          <TouchableOpacity
            style={styles.questionContainer}
            onPress={() => toggleItem(index)}
            activeOpacity={0.7}
          >
            <Text style={styles.question}>{faq.question}</Text>
            {expandedIndex === index ? (
              <ChevronUpIcon size={20} color={{theme.colors.primary} /}>
            )   : (<ChevronDownIcon size={20} color={{theme.colors.primary} /}>
            )}
          </TouchableOpacity>
          {expandedIndex === index && <Text style={styles.answer}>{faq.answer}</Text>
        </View>
      ))}
    </View>
  )
}
const createStyles = (theme: any) =>
  StyleSheet.create({ container: {
      marginBottom: theme.spacing.md }
    title: { fontSize: 18,
      fontWeight: '700'
      color: theme.colors.text,
      marginBottom: theme.spacing.sm },
    faqItem: {
      backgroundColor: theme.colors.surface,
      borderRadius: theme.borderRadius.md,
      marginBottom: theme.spacing.sm,
      borderWidth: 1,
      borderColor: theme.colors.border,
      overflow: 'hidden'
    },
    expandedItem: { backgroundColor: theme.colors.primarySurface },
    questionContainer: { flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: theme.spacing.md },
    question: { fontSize: 15),
      fontWeight: '600'),
      color: theme.colors.text,
      flex: 1,
      marginRight: theme.spacing.sm },
    answer: {
      fontSize: 14,
      lineHeight: 20,
      color: theme.colors.textMuted,
      paddingHorizontal: theme.spacing.md,
      paddingBottom: theme.spacing.md)
    },
  })