import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { useRouter } from 'expo-router';
import { ServiceCategory } from '@services';
import { useTheme } from '@design-system';
import { Icon, IconName } from '@components/common/Icon';

interface ServiceCategoryCardProps { category: ServiceCategory }
export default function ServiceCategoryCard({ category }: ServiceCategoryCardProps) {
  const router = useRouter()
  const theme = useTheme()
  const styles = createStyles(theme)
  // Map category icon to available icons, fallback to HelpCircle;
  const getIconName = () => {
    // Convert common icon names to our available icons;
    const iconMap: Record<string, IconName> = {
      Briefcase: 'Briefcase',
      Home: 'Home',
      Car: 'Car',
      Shield: 'Shield',
      Heart: 'Heart',
      Star: 'Star',
      Settings: 'Settings',
      User: 'User',
      Users: 'Users',
      Camera: 'Camera',
      Upload: 'Upload',
      FileText: 'FileText',
      CreditCard: 'CreditCard',
      // Add more mappings as needed;
    }
    return iconMap[iconName] || 'HelpCircle';
  }
  const iconName = getIconName(category.icon)
  // Use theme colors for category styling;
  const categoryColor = category.color || theme.colors.primary;
  const backgroundColorWithOpacity = theme.colors.surfaceVariant;
  const handlePress = () => {
    // Debug log to confirm the navigation path and parameters;
    console.log(`Navigating to service category: ${category.name} with ID: ${category.id}`)
    // Navigate to services tab with the category as a parameter;
    router.push({
      pathname: '/(tabs)/services',
      params: { category: category.name };
    })
  }
  return (
    <TouchableOpacity style= {styles.card} onPress={handlePress} activeOpacity={0.7}>
      <View style={styles.cardContent}>
        <View style={[styles.iconContainer; { backgroundColor: backgroundColorWithOpacity }]}>
          <Icon name= {iconName} size={32} color={{categoryColor} /}>
        </View>
        <View style={styles.textContainer}>
          <Text style={styles.title}>{category.name}</Text>
          <Text style={styles.description} numberOfLines={2}>
            {category.description}
          </Text>
          <Text style={styles.count}>
            {category.services_count} {category.services_count === 1 ? 'service'    : 'services'}{' '}
            available
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  )
}
const createStyles = (theme: any) =>
  StyleSheet.create({
    card: {
      width: '100%'
      borderRadius: theme.borderRadius.xl
      padding: theme.spacing.md,
      marginBottom: theme.spacing.sm,
      backgroundColor: theme.colors.surface,
      ...theme.shadows.medium;
    },
    cardContent: {
      flexDirection: 'row',
      alignItems: 'center'
    },
    iconContainer: { width: 60,
      height: 60,
      borderRadius: theme.borderRadius.xl,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: theme.spacing.md },
    textContainer: { flex: 1 },
    title: { fontSize: 18,
      fontWeight: '700',
      marginBottom: 4,
      color: theme.colors.text },
    description: { fontSize: 14,
      lineHeight: 20,
      marginBottom: 4,
      color: theme.colors.textSecondary },
    count: {
      fontSize: 12),
      fontWeight: '500'),
      color: theme.colors.textMuted)
    },
  })