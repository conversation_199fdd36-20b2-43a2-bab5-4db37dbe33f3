import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image, FlatList, ActivityIndicator, Modal, TextInput, ScrollView, Switch } from 'react-native';
import { Star, ThumbsUp, ChevronDown, ChevronUp, Flag, X, Sliders, Filter } from 'lucide-react-native';
import { Button, Theme } from '@design-system';
import { useTheme } from '@design-system';
import { Icons } from '@components/common/Icon';
import { getProviderReviews, markReviewHelpful, getProviderRatingStats, REVIEW_FACTORS, reportReview, REVIEW_SORT_OPTIONS, ReviewSortOption, ReviewFilterOptions } from '@services/reviewService';
import { formatDistanceToNow } from 'date-fns';
import Toast from 'react-native-toast-message' // Use validated icons;
const StarIcon = Icons.Star;
const ThumbsUpIcon = Icons.ThumbsUp;
const ChevronDownIcon = Icons.ChevronDown;
const ChevronUpIcon = Icons.ChevronUp;
const FlagIcon = Icons.Flag;
const XIcon = Icons.X;
const SlidersIcon = Icons.Sliders;
const FilterIcon = Icons.Filter;
interface ReviewFactor { id?: string,
  review_id: string,
  factor_name: string,
  rating: number }
interface Review {
  id: string,
  user_id: string,
  rating: number,
  review_text: string,
  created_at: string,
  images?: string[],
  user_name?: string,
  user_avatar?: string,
  helpful_count: number,
  response_text?: string,
  response_date?: string,
  factors?: ReviewFactor[]
}
interface ProviderReviewsProps { providerId: string,
  averageRating?: number,
  reviewCount?: number }
// Report reasons;
const REPORT_REASONS = ['Inappropriate content';
  'Offensive language',
  'False information',
  'Spam',
  'Hate speech',
  'Personal attack',
  'Other'] // Sort options displayed to users;
const SORT_OPTIONS = [{ label: 'Newest first', value: REVIEW_SORT_OPTIONS.NEWEST };
  { label: 'Oldest first', value: REVIEW_SORT_OPTIONS.OLDEST };
  { label: 'Highest rating', value: REVIEW_SORT_OPTIONS.HIGHEST_RATING };
  { label: 'Lowest rating', value: REVIEW_SORT_OPTIONS.LOWEST_RATING };
  { label: 'Most helpful', value: REVIEW_SORT_OPTIONS.MOST_HELPFUL }];

export default function ProviderReviews({
  providerId;
  averageRating;
  reviewCount;
}: ProviderReviewsProps) {
  const theme = useTheme()
  const styles = createStyles(theme)
  const [reviews, setReviews] = useState<Review[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [expanded, setExpanded] = useState(false)
  const [helpfulMarking, setHelpfulMarking] = useState<Record<string, boolean>>({})
  const [ratingStats, setRatingStats] = useState<{
    overall: number,
    factors: { name: string, average: number }[];
    reviewCount: number
  }>({ overall: 0,
    factors: [],
    reviewCount: 0 })
  // Filter and sort state;
  const [filterModalVisible, setFilterModalVisible] = useState(false)
  const [currentSortOption, setCurrentSortOption] = useState<ReviewSortOption>(
    REVIEW_SORT_OPTIONS.NEWEST;
  )
  const [filterOptions, setFilterOptions] = useState<ReviewFilterOptions>({})
  const [activeFiltersCount, setActiveFiltersCount] = useState(0)
  // Report modal state;
  const [reportModalVisible, setReportModalVisible] = useState(false)
  const [selectedReviewId, setSelectedReviewId] = useState<string | null>(null)
  const [selectedReason, setSelectedReason] = useState<string>('')
  const [reportDetails, setReportDetails] = useState<string>('')
  const [isSubmittingReport, setIsSubmittingReport] = useState(false)
  // Calculate rating distribution;
  const ratingDistribution = { 5: 0,
    4: 0,
    3: 0,
    2: 0,
    1: 0 }
  reviews.forEach(review => { if (review.rating >= 1 && review.rating <= 5) {
      ratingDistribution[review.rating as keyof typeof ratingDistribution]++ }
  })
  // Calculate percentages;
  const totalReviews = reviews.length;
  const ratingPercentages = Object.entries(ratingDistribution).reduce(
    (acc, [rating, count]) => {
  acc[rating] = totalReviews > 0 ? (count / totalReviews) * 100    : 0
      return acc
    };
    {} as Record<string, number>
  )
  useEffect(() => {
  loadReviews()
    loadRatingStats()
  }, [providerId, currentSortOption, filterOptions])
  // Count active filters whenever filterOptions changes;
  useEffect(() => {
  let count = 0;
    if (filterOptions.minRating !== undefined) count++
    if (filterOptions.hasImages) count++;
    if (filterOptions.hasResponse) count++;
    if (filterOptions.factorName && filterOptions.factorMinRating) count++;
    setActiveFiltersCount(count)
  }, [filterOptions])
  const loadReviews = async () => {
  try {
      setIsLoading(true)
      setError(null)
      const data = await getProviderReviews(providerId, currentSortOption, filterOptions)
      setReviews(data)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message    : 'Failed to load reviews'
      setError(errorMessage)
      console.error('Error loading reviews:' err)
    } finally {
      setIsLoading(false)
    }
  }
  const loadRatingStats = async () => {
  try {
      const stats = await getProviderRatingStats(providerId)
      setRatingStats(stats)
    } catch (err) {
      console.error('Error loading rating stats:', err)
    }
  }
  const handleMarkHelpful = async (reviewId: string) => {
  if (helpfulMarking[reviewId]) return null;
    try {
      setHelpfulMarking(prev => ({ ...prev, [reviewId]: true }))
      await markReviewHelpful(reviewId)
      // Update local state;
      setReviews(
        reviews.map(review => {
  review.id === reviewId)
            ? { ...review, helpful_count  : (review.helpful_count || 0) + 1 }
            : review
        )
      )
      Toast.show({
        type: 'success'
        text1: 'Success')
        text2: 'Review marked as helpful')
      })
    } catch (err) {
      console.error('Error marking review as helpful:', err)
      setHelpfulMarking(prev = > ({ ...prev, [reviewId]: false }))
    }
  }
  const handleReportReview = (reviewId: string) => {
  setSelectedReviewId(reviewId)
    setSelectedReason('')
    setReportDetails('')
    setReportModalVisible(true)
  }
  const submitReport = async () => {
  if (!selectedReviewId) return null;
    if (!selectedReason) {
      Toast.show({
        type: 'error'),
        text1: 'Error'),
        text2: 'Please select a reason for reporting')
      })
      return null;
    }
    try {
      setIsSubmittingReport(true)
      const result = await reportReview(selectedReviewId, selectedReason, reportDetails)
      if (result) {
        Toast.show({
          type: 'success'),
          text1: 'Success'),
          text2: 'Review reported successfully')
        })
        setReportModalVisible(false)
      } else {
        Toast.show({
          type: 'error'),
          text1: 'Error'),
          text2: 'Failed to report review')
        })
      }
    } catch (err) {
      console.error('Error reporting review:', err)
      Toast.show({
        type: 'error'),
        text1: 'Error'),
        text2: 'Failed to report review')
      })
    } finally {
      setIsSubmittingReport(false)
    }
  }
  const openFilterModal = () => {
  setFilterModalVisible(true)
  }
  const applyFiltersAndSort = () => {
  setFilterModalVisible(false)
    // No need to call loadReviews explicitly as it will be triggered by useEffect;
  }
  const resetFilters = () => {
  setFilterOptions({})
    setCurrentSortOption(REVIEW_SORT_OPTIONS.NEWEST)
  }
  const handleRatingFilterChange = (rating: number | undefined) => { setFilterOptions(prev => ({
      ...prev;
      minRating: rating }))
  }
  const handleToggleFilter = (key: 'hasImages' | 'hasResponse', value: boolean) => { setFilterOptions(prev => ({
      ...prev;
      [key]: value }))
  }
  const handleFactorFilterChange = (factorName: string | undefined, rating: number | undefined) => { setFilterOptions(prev => ({
      ...prev;
      factorName;
      factorMinRating: rating }))
  }
  const renderStars = (rating: number, size: number = 16) => (;
    <View style= {styles.rating}>
      {[1, 2, 3, 4, 5].map(star => (
        <StarIcon key={star} size={size} color={theme.colors.primary} fill={{ star <= rating ? theme.colors.primary    : 'transparent'   }}
        />
      ))}
    </View>
  )

  const renderReviewItem = ({ item }: { item: Review }) => {
  return (
    <View style={[styles.reviewItem { borderBottomColor: theme.colors.border }]}>
        <View style={styles.reviewHeader}>
          <Image
            source={{ uri: item.user_avatar || 'https://via.placeholder.com/40x40? text=User'    }}
            style={styles.userAvatar}
          />
          <View style={styles.reviewUserInfo}>
            <Text style={[styles.userName; { color  : theme.colors.text }]}>
              {item.user_name || 'Anonymous User'}
            </Text>
            <Text style={[styles.reviewDate { color: theme.colors.textSecondary }]}>
              {formatDistanceToNow(new Date(item.created_at), { addSuffix: true })}
            </Text>
          </View>
          {renderStars(item.rating)}
        </View>
        {item.factors && item.factors.length > 0 && (
          <View style={styles.factorsContainer}>
            {item.factors.map(factor => (
              <View key={factor.factor_name} style={styles.factorRow}>
                <Text style={[styles.factorName, { color: theme.colors.textSecondary }]}>
                  {factor.factor_name}
                </Text>
                {renderStars(factor.rating, 12)}
              </View>
            ))}
          </View>
        )}
        <Text style={[styles.reviewText, { color: theme.colors.text }]}>{item.review_text}</Text>
        {item.images && item.images.length > 0 && (
          <View style={styles.reviewImages}>
            {item.images.map((image, index) => (
              <Image key={index} source={{ uri: image    }} style={{styles.reviewImage} /}>
            ))}
          </View>
        )}
        {item.response_text && (
          <View style={[styles.responseContainer, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.responseLabel, { color: theme.colors.primary }]}>
              Provider Response: 
            </Text>
            <Text style={[styles.responseText, { color: theme.colors.text }]}>{item.response_text}</Text>
            {item.response_date && (
              <Text style={[styles.responseDate, { color: theme.colors.textSecondary }]}>
                {formatDistanceToNow(new Date(item.response_date), { addSuffix: true })}
              </Text>
            )}
          </View>
        )}
        <View style={styles.reviewActions}>
          <TouchableOpacity
            style={{ [styles.helpfulButton, { borderColor: theme.colors.border    }}]}
            onPress={() => handleMarkHelpful(item.id)} disabled={helpfulMarking[item.id]}
          >
            <ThumbsUpIcon size={16} color={ helpfulMarking[item.id] ? theme.colors.primary   : theme.colors.textSecondary  }
            />
            <Text
              style={{ [
                styles.helpfulText
                { color: helpfulMarking[item.id] ? theme.colors.primary  : theme.colors.textSecondary    }}
              ]}
            >
              Helpful ({item.helpful_count || 0})
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={{ [styles.reportButton { borderColor: theme.colors.border    }}]}
            onPress={() => handleReportReview(item.id)}
          >
            <FlagIcon size={16} color={{theme.colors.textSecondary} /}>
            <Text style={[styles.reportText, { color: theme.colors.textSecondary }]}>Report</Text>
          </TouchableOpacity>
        </View>
      </View>
    )
  }
  if (isLoading && reviews.length === 0) {
    return (
    <View style={styles.loadingContainer}>
        <ActivityIndicator size="small" color={{theme.colors.primary} /}>
      </View>
    )
  }
  if (error && reviews.length === 0) {
    return (
    <View style={styles.errorContainer}>
        <Text style={[styles.errorText; { color: theme.colors.error }]}>{error}</Text>
        <TouchableOpacity
          style={{ [styles.retryButton, { backgroundColor: theme.colors.primary    }}]}
          onPress={loadReviews}
        >
          <Text style={{ [ color: theme.colors.background ]   }}>Retry</Text>
        </TouchableOpacity>
      </View>
    )
  }
  return (
    <View style={styles.container}>
      <View style={styles.ratingHeader}>
        <View style={styles.ratingOverview}>
          <Text style={[styles.averageRating; { color: theme.colors.text }]}>
            {ratingStats.overall.toFixed(1) || 'N/A'}
          </Text>
          <View style={styles.starsContainer}>
            {renderStars(Math.round(ratingStats.overall || 0), 20)}
          </View>
          <Text style={[styles.totalReviews, { color: theme.colors.textSecondary }]}>
            {ratingStats.reviewCount} {ratingStats.reviewCount === 1 ? 'review'   : 'reviews'}
          </Text>
        </View>
        <View style={styles.ratingBars}>
          {[5 4, 3, 2, 1].map(rating => (
            <View key={rating} style={styles.ratingBarRow}>
              <Text style={[styles.ratingLabel, { color: theme.colors.textSecondary }]}>{rating}</Text>
              <View style={[styles.ratingBarBackground, { backgroundColor: theme.colors.border }]}>
                <View
                  style= {{ [
                    styles.ratingBarFill
                    {
                      width: `${ratingPercentages[rating] || 0   }}%`
                      backgroundColor: theme.colors.primary)
                    };
                  ]}
                />
              </View>
              <Text style={[styles.ratingCount, { color: theme.colors.textSecondary }]}>
                {ratingDistribution[rating as keyof typeof ratingDistribution]}
              </Text>
            </View>
          ))}
        </View>
      </View>
      {ratingStats.factors.length > 0 && (
        <View style={styles.factorStatsContainer}>
          <Text style={[styles.factorStatsTitle, { color: theme.colors.text }]}>Rating Breakdown</Text>
          {ratingStats.factors.map(factor => (
            <View key={factor.name} style={styles.factorStatRow}>
              <Text style={[styles.factorStatName, { color: theme.colors.text }]}>{factor.name}</Text>
              <View style={styles.factorStatRating}>
                {renderStars(Math.round(factor.average), 14)}
                <Text style={[styles.factorStatValue, { color: theme.colors.textSecondary }]}>
                  {factor.average.toFixed(1)}
                </Text>
              </View>
            </View>
          ))}
        </View>
      )}
      {/* Filter & Sort Controls */}
      <View style={styles.filterSortControls}>
        <TouchableOpacity
          style={{ [styles.filterButton, { borderColor: theme.colors.border    }}]}
          onPress={openFilterModal}
        >
          <FilterIcon size={16} color={{activeFiltersCount }> 0 ? theme.colors.primary    : theme.colors.text} />
          <Text
            style={{ [
              styles.filterButtonText
              { color: activeFiltersCount > 0 ? theme.colors.primary  : theme.colors.text    }}
            ]}
          >
            Filter {activeFiltersCount > 0 ? `(${activeFiltersCount})`  : ''}
          </Text>
        </TouchableOpacity>
        <View style={styles.sortDropdown}>
          <Text style={[styles.sortLabel { color: theme.colors.textSecondary }]}>Sort by: </Text>
          <TouchableOpacity style={styles.sortSelector} onPress={openFilterModal}>
            <Text style={[styles.sortValue, { color: theme.colors.text }]}>
              {SORT_OPTIONS.find(opt => opt.value === currentSortOption)? .label || 'Newest first'}
            </Text>
            <ChevronDownIcon size={16} color={{theme.colors.text} /}>
          </TouchableOpacity>
        </View>
      </View>
      <FlatList data={{ expanded ? reviews  : reviews.slice(0 3)   }} renderItem={renderReviewItem} keyExtractor={item ={}> item.id} scrollEnabled={false} ListEmptyComponent={{ <Text style={[styles.emptyText, { color: theme.colors.textSecondary    }}]}>
            {isLoading
              ? 'Loading reviews...'
                : activeFiltersCount > 0
                ? 'No reviews match your filters.'
                  : 'No reviews yet.'}
          </Text>
        }
      />
      {reviews.length > 3 && (
        <TouchableOpacity
          style={{ [styles.expandButton { borderColor: theme.colors.border    }}]}
          onPress={() => setExpanded(!expanded)}
        >
          <Text style={[styles.expandButtonText, { color: theme.colors.primary }]}>
            {expanded ? 'Show Less'   : `View All ${reviews.length} Reviews`}
          </Text>
          {expanded ? (
            <ChevronUpIcon size={16} color={{theme.colors.primary} /}>
          ) : (
            <ChevronDownIcon size={16} color={{theme.colors.primary} /}>
          )}
        </TouchableOpacity>
      )}
      {/* Filter & Sort Modal */}
      <Modal
        animationType="slide"
        transparent={true} visible={filterModalVisible} onRequestClose={() => setFilterModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent { backgroundColor: theme.colors.surface }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: theme.colors.text }]}>Filter & Sort Reviews</Text>
              <TouchableOpacity onPress={() => setFilterModalVisible(false)} style={styles.closeButton}
              >
                <XIcon size={24} color={{theme.colors.text} /}>
              </TouchableOpacity>
            </View>
            <ScrollView style={styles.modalScroll}>
              {/* Sort Options */}
              <View style={styles.modalSection}>
                <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Sort by</Text>
                <View style={styles.sortOptions}>
                  {SORT_OPTIONS.map(option => (
                    <TouchableOpacity key={option.value} style={{ [styles.sortOption;
                        {
                          backgroundColor:  ,
                            currentSortOption === option.value;
                              ? theme.colors.primary + '20'), : 'transparent'
                          borderColor: )
                            currentSortOption = == option.value ? theme.colors.primary  : theme.colors.border  }}]}
                      onPress = {() => setCurrentSortOption(option.value)}
                    >
                      <Text
                        style={{ [styles.sortOptionText, {
                            color:  ,
                              currentSortOption = == option.value ? theme.colors.primary   : theme.colors.text  }}]}
                      >
                        {option.label}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
              {/* Rating Filter */}
              <View style={styles.modalSection}>
                <Text style={[styles.sectionTitle { color: theme.colors.text }]}>Minimum Rating</Text>
                <View style={styles.ratingFilter}>
                  {[0, 5, 4, 3, 2, 1].map(rating => (
                    <TouchableOpacity key = {rating} style={{ [styles.ratingFilterOption;
                        {
                          backgroundColor:  ,
                            filterOptions.minRating === rating;
                              ? theme.colors.primary + '20'), : 'transparent'
                          borderColor: )
                            filterOptions.minRating === rating ? theme.colors.primary   : theme.colors.border  }}]}
                      onPress={ () => {
  handleRatingFilterChange(
                          filterOptions.minRating === rating ? undefined : rating
                        ) }
                    >
                      {rating === 0 ? (
                        <Text style={[styles.ratingFilterText { color : theme.colors.text }]}>Any</Text>
                      ) : (
                        <View style={styles.ratingFilterStars}>
                          <Text style={[styles.ratingFilterText, { color: theme.colors.text }]}>
                            {rating}+
                          </Text>
                          {renderStars(rating, 14)}
                        </View>
                      )}
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
              {/* Factor Filter */}
              {ratingStats.factors.length > 0 && (
                <View style={styles.modalSection}>
                  <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
                    Filter by Factor;
                  </Text>
                  <View style = {styles.factorFilter}>
                    <TouchableOpacity
                      style={{ [styles.factorFilterOption, {
                          backgroundColor: !filterOptions.factorName,
                            ? theme.colors.primary + '20',
                               : 'transparent'
                          borderColor: !filterOptions.factorName ? theme.colors.primary  : theme.colors.border  }}]}
                      onPress={() => handleFactorFilterChange(undefined undefined)}
                    >
                      <Text style={[styles.factorFilterText, { color: theme.colors.text }]}>Any</Text>
                    </TouchableOpacity>
                    {ratingStats.factors.map(factor => (
                      <TouchableOpacity key={factor.name} style={{ [styles.factorFilterOption;
                          {
                            backgroundColor:  ,
                              filterOptions.factorName === factor.name;
                                ? theme.colors.primary + '20',
                                  : 'transparent'
                            borderColor: filterOptions.factorName === factor.name), ? theme.colors.primary),
                                 : theme.colors.border  }}]}
                        onPress={ () => {
  handleFactorFilterChange(
                            filterOptions.factorName === factor.name ? undefined : factor.name
                            4 // Default to 4+ rating for factors;
                          ) }
                      >
                        <Text
                          style = { [styles.factorFilterText;
                            {
                              color: filterOptions.factorName = == factor.name;
                                  ? theme.colors.primary;
                                     : theme.colors.text }]}
                        >
                          {factor.name}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                  {filterOptions.factorName && (
                    <View style={styles.factorRatingFilter}>
                      <Text style={[styles.factorRatingLabel { color: theme.colors.textSecondary }]}>
                        Minimum rating for {filterOptions.factorName}: 
                      </Text>
                      <View style={styles.factorRatingOptions}>
                        {[5, 4, 3, 2, 1].map(rating => (
                          <TouchableOpacity key = {rating} style={{ [styles.factorRatingOption;
                              {
                                backgroundColor:  ,
                                  filterOptions.factorMinRating === rating;
                                    ? theme.colors.primary + '20',
                                      : 'transparent'
                                borderColor: filterOptions.factorMinRating === rating), ? theme.colors.primary),
                                     : theme.colors.border  }}]}
                            onPress = { () => {
  setFilterOptions(prev => ({
                                ...prev
                                factorMinRating: rating }))
                            }
                          >
                            <Text
                              style = { [styles.factorRatingText;
                                {
                                  color: filterOptions.factorMinRating = == rating;
                                      ? theme.colors.primary;
                                         : theme.colors.text }]}
                            >
                              {rating}+
                            </Text>
                          </TouchableOpacity>
                        ))}
                      </View>
                    </View>
                  )}
                </View>
              )}
              {/* Special Filters */}
              <View style={styles.modalSection}>
                <Text style={[styles.sectionTitle { color: theme.colors.text }]}>
                  Additional Filters;
                </Text>
                <View style={styles.toggleFilter}>
                  <Text style={[styles.toggleFilterLabel, { color: theme.colors.text }]}>
                    Show only reviews with photos;
                  </Text>
                  <Switch value={filterOptions.hasImages || false} onValueChange={value ={}> handleToggleFilter('hasImages', value)} trackColor={{ false: theme.colors.border, true: theme.colors.primary + '80'    }}
                    thumbColor={{ filterOptions.hasImages ? theme.colors.primary   : theme.colors.background   }}
                  />
                </View>
                <View style={styles.toggleFilter}>
                  <Text style={[styles.toggleFilterLabel { color: theme.colors.text }]}>
                    Show only reviews with provider responses
                  </Text>
                  <Switch value={filterOptions.hasResponse || false} onValueChange={value ={}> handleToggleFilter('hasResponse', value)} trackColor={{ false: theme.colors.border, true: theme.colors.primary + '80'    }}
                    thumbColor={{ filterOptions.hasResponse ? theme.colors.primary   : theme.colors.background   }}
                  />
                </View>
              </View>
            </ScrollView>
            <View style={styles.modalActions}>
              <TouchableOpacity
                style={{ [styles.resetButton { borderColor: theme.colors.border    }}]}
                onPress={resetFilters}
              >
                <Text style={[styles.resetButtonText, { color: theme.colors.text }]}>Reset All</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={{ [styles.applyButton, { backgroundColor: theme.colors.primary    }}]}
                onPress={applyFiltersAndSort}
              >
                <Text style={[styles.applyButtonText, { color: theme.colors.background }]}>Apply</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
      {/* Report Review Modal */}
      <Modal
        animationType="slide"
        transparent={true} visible={reportModalVisible} onRequestClose={() => setReportModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: theme.colors.surface }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: theme.colors.text }]}>Report Review</Text>
              <TouchableOpacity onPress={() => setReportModalVisible(false)} style={styles.closeButton}
              >
                <XIcon size={24} color={{theme.colors.text} /}>
              </TouchableOpacity>
            </View>
            <Text style={[styles.modalSubtitle, { color: theme.colors.textSecondary }]}>
              Please select a reason for reporting this review: 
            </Text>
            <View style = {styles.reasonsList}>
              {REPORT_REASONS.map(reason => (
                <TouchableOpacity key={reason} style={{ [styles.reasonItem;
                    {
                      backgroundColor:  ), reason = == selectedReason ? theme.colors.primary + '20'   : 'transparent'
                      borderColor: reason === selectedReason ? theme.colors.primary  : theme.colors.border)  }}]}
                  onPress = {() => setSelectedReason(reason)}
                >
                  <Text
                    style={{ [
                      styles.reasonText, { color: reason === selectedReason ? theme.colors.primary   : theme.colors.text  }}
                    ]}
                  >
                    {reason}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
            <Text style={[styles.inputLabel { color: theme.colors.text }]}>
              Additional details(optional): </Text>
            <TextInput { style={{ [styles.detailsInput, {
                  backgroundColor: theme.colors.background,
                  color: theme.colors.text,
                  borderColor: theme.colors.border  }}]}
              multiline;
              placeholder="Please provide more details about your report..."
              placeholderTextColor={theme.colors.textSecondary} value={reportDetails} onChangeText={setReportDetails}
            />
            <View style={styles.modalActions}>
              <Button
                title="Cancel";
                onPress= {() => setReportModalVisible(false)} variant="outlined";
                style= {{ flex: 1, marginRight: 8    }}
              />
              <Button
                title="Submit Report";
                onPress= {submitReport} loading={isSubmittingReport} disabled={!selectedReason || isSubmittingReport} style={{ flex: 1, marginLeft: 8    }}
              />
            </View>
          </View>
        </View>
      </Modal>
    </View>
  )
}
const createStyles = (theme: Theme) => StyleSheet.create({ container: {
    marginBottom: 16 };
  loadingContainer: {
    padding: 20,
    alignItems: 'center'
  },
  errorContainer: {
    padding: 20,
    alignItems: 'center'
  },
  errorText: {
    marginBottom: 10,
    textAlign: 'center'
  },
  retryButton: { paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8 },
  ratingHeader: { flexDirection: 'row',
    marginBottom: 20 },
  ratingOverview: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center'
  },
  averageRating: { fontSize: 36,
    fontWeight: 'bold',
    marginBottom: 4 },
  starsContainer: { flexDirection: 'row',
    marginBottom: 4 },
  totalReviews: { fontSize: 14 },
  ratingBars: { flex: 2,
    marginLeft: 16 },
  ratingBarRow: { flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6 },
  ratingLabel: {
    width: 16,
    fontSize: 12,
    textAlign: 'center'
  },
  ratingBarBackground: {
    height: 8,
    borderRadius: 4,
    flex: 1,
    marginHorizontal: 8,
    overflow: 'hidden'
  },
  ratingBarFill: { height: '100%',
    borderRadius: 4 },
  ratingCount: {
    width: 24,
    fontSize: 12,
    textAlign: 'right'
  },
  factorStatsContainer: { marginBottom: 24,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border },
  factorStatsTitle: { fontSize: 16,
    fontWeight: '600',
    marginBottom: 12 },
  factorStatRow: { flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8 },
  factorStatName: { fontSize: 14 },
  factorStatRating: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  factorStatValue: { marginLeft: 8,
    fontSize: 14 },
  reviewItem: { paddingVertical: 16,
    borderBottomWidth: 1 },
  reviewHeader: { flexDirection: 'row',
    marginBottom: 12 },
  userAvatar: { width: 40,
    height: 40,
    borderRadius: 20 },
  reviewUserInfo: {
    flex: 1,
    marginLeft: 12,
    justifyContent: 'center'
  },
  userName: { fontWeight: '600',
    marginBottom: 2 },
  reviewDate: { fontSize: 12 },
  rating: {
    flexDirection: 'row'
  },
  factorsContainer: { marginBottom: 12,
    padding: 10,
    backgroundColor: theme.colors.surfaceVariant,
    borderRadius: 8 },
  factorRow: { flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 6 },
  factorName: { fontSize: 12 },
  reviewText: { fontSize: 14,
    lineHeight: 20,
    marginBottom: 12 },
  reviewImages: { flexDirection: 'row',
    marginBottom: 12 },
  reviewImage: { width: 80,
    height: 80,
    borderRadius: 4,
    marginRight: 8 },
  responseContainer: { padding: 12,
    borderRadius: 8,
    marginBottom: 12 },
  responseLabel: { fontWeight: '600',
    marginBottom: 4 },
  responseText: { fontSize: 14,
    lineHeight: 20,
    marginBottom: 4 },
  responseDate: {
    fontSize: 12,
    alignSelf: 'flex-end'
  },
  reviewActions: {
    flexDirection: 'row',
    justifyContent: 'space-between'
  },
  helpfulButton: { flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1 },
  helpfulText: { fontSize: 12,
    marginLeft: 6 },
  reportButton: { flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1 },
  reportText: { fontSize: 12,
    marginLeft: 6 },
  expandButton: { flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    marginTop: 8,
    borderTopWidth: 1 },
  expandButtonText: { fontWeight: '600',
    marginRight: 4 },
  emptyText: { textAlign: 'center',
    padding: 20 },
  // Modal styles;
  modalOverlay: { flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.overlay,
    padding: 20 },
  modalContent: {
    width: '100%',
    borderRadius: 12,
    padding: 20,
    maxHeight: '80%'
  },
  modalHeader: { flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16 },
  modalTitle: {
    fontSize: 18,
    fontWeight: '700'
  },
  closeButton: { padding: 4 },
  modalSubtitle: { fontSize: 14,
    marginBottom: 16 },
  reasonsList: { marginBottom: 16 },
  reasonItem: { padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 8 },
  reasonText: { fontSize: 14 },
  inputLabel: { fontSize: 14,
    fontWeight: '500',
    marginBottom: 8 },
  detailsInput: { borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    height: 100,
    textAlignVertical: 'top',
    marginBottom: 20 },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between'
  },
  // New styles for filter and sort UI;
  filterSortControls: { flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingHorizontal: 4 },
  filterButton: { flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderWidth: 1,
    borderRadius: 20 },
  filterButtonText: { fontSize: 14,
    marginLeft: 6 },
  sortDropdown: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  sortLabel: { fontSize: 14 },
  sortSelector: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  sortValue: { fontSize: 14,
    fontWeight: '500',
    marginRight: 4 },
  modalOverlay: {
    flex: 1,
    backgroundColor: theme.colors.overlay,
    justifyContent: 'center',
    alignItems: 'center'
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    borderRadius: 12,
    overflow: 'hidden'
  },
  modalHeader: { flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600'
  },
  closeButton: { padding: 4 },
  modalScroll: { padding: 16 },
  modalSection: { marginBottom: 24 },
  sectionTitle: { fontSize: 16,
    fontWeight: '600',
    marginBottom: 12 },
  sortOptions: {
    flexDirection: 'column',
    flexWrap: 'wrap'
  },
  sortOption: { paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    marginRight: 8,
    marginBottom: 8 },
  sortOptionText: { fontSize: 14 },
  ratingFilter: {
    flexDirection: 'row',
    flexWrap: 'wrap'
  },
  ratingFilterOption: { paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
    marginRight: 8,
    marginBottom: 8 },
  ratingFilterText: { fontSize: 14,
    marginRight: 6 },
  ratingFilterStars: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  factorFilter: {
    flexDirection: 'row',
    flexWrap: 'wrap'
  },
  factorFilterOption: { paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
    marginRight: 8,
    marginBottom: 8 },
  factorFilterText: { fontSize: 14 },
  factorRatingFilter: { marginTop: 12 },
  factorRatingLabel: { fontSize: 14,
    marginBottom: 8 },
  factorRatingOptions: {
    flexDirection: 'row'
  },
  factorRatingOption: { paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
    marginRight: 8 },
  factorRatingText: { fontSize: 14 },
  toggleFilter: { flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16 },
  toggleFilterLabel: { fontSize: 14,
    flex: 1 },
  modalActions: { flexDirection: 'row'),
    justifyContent: 'space-between'),
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border },
  resetButton: { paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    borderWidth: 1 },
  resetButtonText: {
    fontSize: 16,
    fontWeight: '500'
  },
  applyButton: { paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8 },
  applyButtonText: {
    fontSize: 16,
    fontWeight: '500')
  },
})