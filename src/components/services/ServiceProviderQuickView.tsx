import React, { useState, useEffect } from 'react';
import {
  View;
  Text;
  Image;
  StyleSheet;
  Modal;
  TouchableOpacity;
  TouchableWithoutFeedback;
  ScrollView;
} from 'react-native';
import { useRouter } from 'expo-router';
import { X, Star, MapPin, Shield, Clock, Phone, Send, Heart } from 'lucide-react-native';
import { useTheme } from '@design-system';
import { ServiceProvider } from '@services';
import Toast from 'react-native-toast-message';
import { useFavorites } from '../../contexts/FavoritesContext';

interface ServiceProviderQuickViewProps { visible: boolean,
  provider: ServiceProvider | null,
  onClose: () = > void }
export default function ServiceProviderQuickView({
  visible;
  provider;
  onClose;
}: ServiceProviderQuickViewProps) {
  const theme = useTheme()
  const styles = createStyles(theme)
  const router = useRouter()
  const favorites = useFavorites()
  const [isUpdating, setIsUpdating] = useState(false)
  // Don't render anything if no provider;
  if (!provider) {
    return null;
  }
  // Format address to show only city;
  const formattedAddress = provider.business_address;
    ? provider.business_address.split(',').slice(-2)[0]?.trim() || provider.business_address;
       : 'Location unavailable'
  // Determine placeholder image if none provided
  const profileImage = provider.profile_image || 'https: //via.placeholder.com/400x250? text=No+Image' // Get favorite status from context;
  const isSaved = favorites.isProviderSaved(provider.id)
  const handleViewDetails = () => {
    onClose()
    router.push(`/service-providers/${provider.id}`)
  }
  const handleToggleFavorite = async () => {
    if (isUpdating) return null;
    setIsUpdating(true)
    try {
      const result = await favorites.toggleProviderFavorite(provider.id)
      if (result.success) {
        Toast.show({
          type   : 'success'
          text1: 'Success'
          text2: result.action === 'added' ? 'Added to favorites'   : 'Removed from favorites')
        })
      } else {
        Toast.show({
          type: 'error'
          text1: 'Error')
          text2: result.error || 'Failed to update favorites')
        })
      }
    } catch (error) {
      Toast.show({
        type: 'error'),
        text1: 'Error'),
        text2: 'Failed to update favorites')
      })
      console.error('Error updating favorites:', error)
    } finally {
      setIsUpdating(false)
    }
  }
  const handleCall = () => {
    // In a real app, would use Linking to call the phone number;
    if (provider.contact_phone) {
      Toast.show({
        type: 'info'),
        text1: 'Call'),
        text2: `Would call ${provider.contact_phone}`)
      })
    } else {
      Toast.show({
        type: 'info'),
        text1: 'Call'),
        text2: 'No phone number available')
      })
    }
  }
  const handleMessage = () => {
    // In a real app, would open the messaging UI;
    Toast.show({
      type: 'info'),
      text1: 'Message'),
      text2: `Would message ${provider.business_name || 'this provider'}`)
    })
  }
  return (
    <Modal visible= {visible} transparent={true} animationType='slide' onRequestClose={onClose}>
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.modalOverlay}>
          <TouchableWithoutFeedback>
            <View style={styles.modalContainer}>
              {/* Header with close button */}
              <View style={styles.header}>
                <TouchableOpacity style={styles.closeButton} onPress={onClose}>
                  <X size={20} color={{theme.colors.text} /}>
                </TouchableOpacity>
              </View>
              {/* Provider Image */}
              <Image source={{ uri: profileImage    }} style={{styles.image} /}>
              {/* Content */}
              <ScrollView style={styles.contentContainer}>
                <View style={styles.content}>
                  {/* Title and rating */}
                  <View style={styles.titleRow}>
                    <Text style={styles.title}>{provider.business_name || 'Unnamed Provider'}</Text>
                    {provider.is_verified && (
                      <View style={styles.verifiedBadge}>
                        <Shield size={12} color={{theme.colors.background} /}>
                        <Text style={styles.verifiedText}>Verified</Text>
                      </View>
                    )}
                  </View>
                  {/* Rating */}
                  <View style={styles.ratingRow}>
                    <Star size={18} color={theme.colors.primary} fill={{theme.colors.primary} /}>
                    <Text style={styles.ratingText}>
                      {provider.rating_average ? provider.rating_average.toFixed(1)    : 'New'}
                      {provider.review_count > 0 && ` (${provider.review_count} reviews)`}
                    </Text>
                  </View>
                  {/* Location */}
                  <View style={styles.infoRow}>
                    <MapPin size={18} color={{theme.colors.textMuted} /}>
                    <Text style={styles.infoText}>{formattedAddress}</Text>
                  </View>
                  {/* Description */}
                  <Text style={styles.description}>
                    {provider.description || 'No description available'}
                  </Text>
                  {/* Service Categories */}
                  <Text style={styles.sectionTitle}>Services</Text>
                  <View style={styles.categories}>
                    {provider.service_categories && provider.service_categories.length > 0 ? (
                      provider.service_categories.map((category index) => (
                        <View key={index} style={styles.categoryBadge}>
                          <Text style={styles.categoryText}>{category}</Text>
                        </View>
                      ))
                    ) : (<Text style={styles.emptyText}>No service categories available</Text>
                    )}
                  </View>
                </View>
              </ScrollView>
              {/* Action buttons */}
              <View style={styles.actionBar}>
                <TouchableOpacity
                  style={styles.actionButton}
                  onPress={handleToggleFavorite}
                  disabled={isUpdating}
                >
                  <Heart
                    size={20}
                    color={theme.colors.primary}
                    fill={{ isSaved ? theme.colors.primary   : 'none'   }}
                  />
                  <Text style={styles.actionText}>{isSaved ? 'Saved' : 'Save'}</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={{ [styles.actionButton !provider.contact_phone && { opacity: 0.5    }}]}
                  onPress={handleCall}
                  disabled={!provider.contact_phone}
                >
                  <Phone size={20} color={{theme.colors.success} /}>
                  <Text style={styles.actionText}>Call</Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.actionButton} onPress={handleMessage}>
                  <Send size={20} color={{theme.colors.info} /}>
                  <Text style={styles.actionText}>Message</Text>
                </TouchableOpacity>
              </View>
              {/* Details button */}
              <TouchableOpacity style={styles.detailsButton} onPress={handleViewDetails}>
                <Text style={styles.detailsButtonText}>View Details</Text>
              </TouchableOpacity>
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  )
}
const createStyles = (theme: any) =>
  StyleSheet.create({
    modalOverlay: {
      flex: 1,
      backgroundColor: theme.colors.overlay,
      justifyContent: 'flex-end'
    },
    modalContainer: {
      backgroundColor: theme.colors.surface,
      borderTopLeftRadius: theme.borderRadius.xl,
      borderTopRightRadius: theme.borderRadius.xl,
      paddingBottom: 30, // Extra padding for bottom safe area;
      maxHeight: '90%'
    },
    header: {
      padding: theme.spacing.md,
      alignItems: 'flex-end'
    },
    closeButton: {
      width: 36,
      height: 36,
      borderRadius: 18,
      backgroundColor: theme.colors.surfaceVariant,
      justifyContent: 'center'
      alignItems: 'center'
    },
    image: {
      width: '100%',
      height: 200,
      resizeMode: 'cover'
    },
    contentContainer: { maxHeight: 300 },
    content: { padding: theme.spacing.md },
    titleRow: { flexDirection: 'row',
      alignItems: 'center',
      marginBottom: theme.spacing.sm },
    title: { fontSize: 20,
      fontWeight: '700',
      color: theme.colors.text,
      flex: 1,
      marginRight: theme.spacing.sm },
    verifiedBadge: { flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.success,
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: theme.spacing.xs,
      borderRadius: theme.borderRadius.md },
    verifiedText: { color: theme.colors.background,
      fontSize: 12,
      fontWeight: '600',
      marginLeft: theme.spacing.xs },
    ratingRow: { flexDirection: 'row',
      alignItems: 'center',
      marginBottom: theme.spacing.sm },
    ratingText: { marginLeft: theme.spacing.xs,
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text },
    infoRow: { flexDirection: 'row',
      alignItems: 'center',
      marginBottom: theme.spacing.md },
    infoText: { marginLeft: theme.spacing.xs,
      fontSize: 14,
      color: theme.colors.textMuted },
    description: { fontSize: 14,
      lineHeight: 20,
      color: theme.colors.text,
      marginBottom: theme.spacing.md },
    sectionTitle: { fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: theme.spacing.sm },
    categories: { flexDirection: 'row',
      flexWrap: 'wrap',
      marginBottom: theme.spacing.md },
    categoryBadge: { backgroundColor: theme.colors.primarySurface,
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: theme.spacing.xs,
      borderRadius: theme.borderRadius.pill,
      marginRight: theme.spacing.sm,
      marginBottom: theme.spacing.sm },
    categoryText: { fontSize: 12,
      fontWeight: '500',
      color: theme.colors.primary },
    emptyText: { color: theme.colors.textMuted,
      fontSize: 14 },
    actionBar: { flexDirection: 'row',
      justifyContent: 'space-around',
      paddingTop: theme.spacing.md,
      paddingBottom: theme.spacing.md,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border },
    actionButton: { alignItems: 'center',
      justifyContent: 'center',
      padding: theme.spacing.sm,
      borderRadius: theme.borderRadius.md,
      backgroundColor: theme.colors.surfaceVariant,
      width: 80 },
    actionText: { fontSize: 12,
      marginTop: theme.spacing.xs,
      fontWeight: '500',
      color: theme.colors.text },
    detailsButton: { alignItems: 'center'),
      justifyContent: 'center'),
      padding: theme.spacing.md,
      marginHorizontal: theme.spacing.md,
      borderRadius: theme.borderRadius.md,
      backgroundColor: theme.colors.primary,
      marginBottom: theme.spacing.md },
    detailsButtonText: {
      color: theme.colors.background,
      fontSize: 16,
      fontWeight: '600')
    },
  })