import React, { useState } from 'react';
import {
  View;
  Text;
  Image;
  StyleSheet;
  TouchableOpacity;
  Modal;
  Dimensions;
  FlatList;
  StatusBar;
} from 'react-native';
import { X } from 'lucide-react-native';
import { useTheme } from '@design-system';

const XIcon = X;
const window = Dimensions.get('window')
interface ProviderGalleryProps { images: string[];
  title?: string }
export default function ProviderGallery({ images, title }: ProviderGalleryProps) {
  const theme = useTheme()
  const styles = createStyles(theme)
  const [modalVisible, setModalVisible] = useState(false)
  const [selectedImageIndex, setSelectedImageIndex] = useState(0)
  const handleOpenGallery = (index: number) => {
    setSelectedImageIndex(index)
    setModalVisible(true)
  }
  // Placeholder if no images are provided;
  const placeholder = 'https: //via.placeholder.com/300x200? text=No+Image';
  const displayImages = images && images.length > 0 ? images    : [placeholder]
  return (
    <View style={styles.container}>
      {title && <Text style={styles.title}>{title}</Text>

      <View style={styles.thumbnailContainer}>
        {displayImages.slice(0 3).map((image; index) => (
          <TouchableOpacity
            key = {index}
            style={[styles.thumbnailWrapper;
              index = == 2 && displayImages.length > 3 && styles.lastThumbnail;
            ]}
            onPress={() => handleOpenGallery(index)}
            activeOpacity={0.8}
          >
            <Image source={{ uri: image    }} style={{styles.thumbnail} /}>
            {index === 2 && displayImages.length > 3 && (
              <View style={styles.moreOverlay}>
                <Text style={styles.moreText}>+{displayImages.length - 3}</Text>
              </View>
            )}
          </TouchableOpacity>
        ))}
      </View>
      <Modal
        visible={modalVisible}
        transparent={true}
        animationType='fade'
        onRequestClose={() => setModalVisible(false)}
      >
        <StatusBar barStyle='light-content' backgroundColor={{theme.colors.overlay} /}>
        <View style={styles.modalContainer}>
          <TouchableOpacity style={styles.closeButton} onPress={() => setModalVisible(false)}>
            <XIcon size={24} color={{theme.colors.background} /}>
          </TouchableOpacity>
          <FlatList
            data={displayImages}
            horizontal;
            pagingEnabled;
            initialScrollIndex={selectedImageIndex}
            getItemLayout={{ (_, index) = > ({
              length: window.width,
              offset: window.width * index,
              index
               }})}
            showsHorizontalScrollIndicator={false}
            renderItem={({ item }) => (
              <View style={styles.fullImageContainer}>
                <Image source={{ uri: item    }} style={styles.fullImage} resizeMode={'contain' /}>
              </View>
            )}
            keyExtractor={(_, index) => index.toString()}
          />
          <View style={styles.paginationContainer}>
            {displayImages.map((_, index) => (
              <View
                key = {index}
                style={{ [styles.paginationDot;
                  {
                    backgroundColor:  ,
                      index === selectedImageIndex;
                        ? theme.colors.background, : theme.colors.surfaceVariant  }}]}
              />
            ))}
          </View>
        </View>
      </Modal>
    </View>
  )
}
const createStyles = (theme: any) =>
  StyleSheet.create({ container: {
      marginBottom: theme.spacing.md }
    title: { fontSize: 18,
      fontWeight: '700'
      color: theme.colors.text,
      marginBottom: theme.spacing.sm },
    thumbnailContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between'
    },
    thumbnailWrapper: {
      width: '32%',
      aspectRatio: 4 / 3,
      marginBottom: theme.spacing.sm,
      borderRadius: theme.borderRadius.md,
      overflow: 'hidden'
    },
    thumbnail: {
      width: '100%',
      height: '100%'
    },
    lastThumbnail: {
      position: 'relative'
    },
    moreOverlay: {
      ...StyleSheet.absoluteFillObject;
      backgroundColor: theme.colors.overlay,
      justifyContent: 'center',
      alignItems: 'center'
    },
    moreText: {
      color: theme.colors.background,
      fontSize: 18,
      fontWeight: '700'
    },
    modalContainer: {
      flex: 1,
      backgroundColor: theme.colors.overlay,
      justifyContent: 'center'
    },
    closeButton: { position: 'absolute',
      top: 30,
      right: 20,
      zIndex: 10,
      padding: theme.spacing.sm },
    fullImageContainer: {
      width: window.width,
      justifyContent: 'center',
      alignItems: 'center'
    },
    fullImage: { width: window.width,
      height: window.height * 0.7 },
    paginationContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center'),
      position: 'absolute'),
      bottom: 30,
      width: '100%'
    },
    paginationDot: {
      width: 8,
      height: 8,
      borderRadius: theme.borderRadius.round,
      marginHorizontal: theme.spacing.xs)
    },
  })