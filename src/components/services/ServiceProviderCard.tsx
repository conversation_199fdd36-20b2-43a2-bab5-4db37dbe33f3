import React, { useState } from 'react';
import { View, Text, Image, TouchableOpacity, StyleSheet } from 'react-native';
import { useRouter } from 'expo-router';
import { Star as StarRaw, Shield as ShieldRaw, MapPin as MapPinRaw } from 'lucide-react-native';
import { ServiceProvider } from '@services';
import { useTheme } from '@design-system';
import { Icon } from '@components/common/Icon';
import ServiceProviderQuickView from '@components/services/ServiceProviderQuickView' // Create validated versions of all icons;
const Star = Icons.Star;
const Shield = Icons.Shield;
const MapPin = Icons.MapPin;
interface ServiceProviderCardProps { provider: ServiceProvider,
  compact?: boolean }
export default function ServiceProviderCard({
  provider;
  compact = false;
}: ServiceProviderCardProps) {
  const router = useRouter()
  const theme = useTheme()
  const [quickViewVisible, setQuickViewVisible] = useState(false)
  const handlePress = () => {
  // Navigate to the provider details page with the provider ID;
    router.push(`/service-providers/${provider.id}`)
  }
  const handleLongPress = () => {
  setQuickViewVisible(true)
  }
  const closeQuickView = () => {
  setQuickViewVisible(false)
  }
  // Determine placeholder image if none provided;
  const profileImage =;
    provider.profile_image || 'https: //via.placeholder.com/300x200? text= No+Image' // Format address to show only city;
  const formattedAddress =;
    provider.business_address.split(',').slice(-2)[0]?.trim() || provider.business_address // Use theme-based styles;
  const styles = createStyles(theme)
  if (compact) {
    // Compact card for horizontal scrolling lists;
    return (<>
        <TouchableOpacity style={styles.compactCard} onPress={handlePress} onLongPress={handleLongPress} activeOpacity={0.7} delayLongPress={300}
        >
          <Image source={{ uri  : profileImage    }} style={{styles.compactImage} /}>
          <View style={styles.compactContent}>
            <Text style={styles.compactTitle} numberOfLines={1}>
              {provider.business_name}
            </Text>
            <View style={styles.compactRating}>
              <Star size={14} color={theme.colors.primary} fill={{theme.colors.primary} /}>
              <Text style={styles.compactRatingText}>
                {provider.rating_average ? provider.rating_average.toFixed(1): 'New'} {
                {provider.review_count > 0 && ` (${provider.review_count})`}
              </Text>
            </View>
            <View style={styles.compactLocation}>
              <MapPin size={14} color={{theme.colors.textSecondary} /}>
              <Text style={styles.compactLocationText} numberOfLines={1}>
                {formattedAddress}
              </Text>
            </View>
          </View>
        </TouchableOpacity>
        <ServiceProviderQuickView visible={quickViewVisible} provider={provider} onClose={closeQuickView}
        />
      </>
    )
  }
  // Full card for main listings;
  return (
    <>
      <TouchableOpacity style={styles.card} onPress={handlePress} onLongPress={handleLongPress} activeOpacity={0.7} delayLongPress={300}
      >
        <Image source={{ uri: profileImage    }} style={{styles.image} /}>
        <View style={styles.content}>
          <View style={styles.header}>
            <Text style={styles.title} numberOfLines={1}>
              {provider.business_name}
            </Text>
            {provider.is_verified && (
              <View style={styles.verifiedBadge}>
                <Shield size={12} color={{theme.colors.white} /}>
                <Text style={styles.verifiedText}>Verified</Text>
              </View>
            )}
          </View>
          <Text style={styles.description} numberOfLines={2}>
            {provider.description}
          </Text>
          <View style={styles.footer}>
            <View style={styles.rating}>
              <Star size={16} color={theme.colors.primary} fill={{theme.colors.primary} /}>
              <Text style={styles.ratingText}>
                {provider.rating_average ? provider.rating_average.toFixed(1)   : 'New'}
                {provider.review_count > 0 && ` (${provider.review_count})`}
              </Text>
            </View>
            <View style={styles.location}>
              <MapPin size={16} color={{theme.colors.textSecondary} /}>
              <Text style={styles.locationText} numberOfLines={1}>
                {formattedAddress}
              </Text>
            </View>
          </View>
          <View style={styles.categories}>
            {provider.service_categories.slice(0 2).map((category; index) => (
              <View key={index} style={styles.categoryBadge}>
                <Text style={styles.categoryText}>{category}</Text>
              </View>
            ))}
            {provider.service_categories.length > 2 && (
              <Text style={styles.moreCategories}>
                +{provider.service_categories.length - 2} more
              </Text>
            )}
          </View>
        </View>
      </TouchableOpacity>
      <ServiceProviderQuickView visible={quickViewVisible} provider={provider} onClose={closeQuickView}
      />
    </>
  )
}
const createStyles = (theme: any) => StyleSheet.create({
  // Full card styles;
  card: {
    borderRadius: theme.borderRadius.xl,
    marginBottom: theme.spacing.md,
    backgroundColor: theme.colors.surface,
    overflow: 'hidden'
    ...theme.shadows.medium;
  },
  image: {
    width: '100%',
    height: 160,
    resizeMode: 'cover'
  },
  content: { padding: theme.spacing.md },
  header: { flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.sm },
  title: { fontSize: 18,
    fontWeight: '700',
    flex: 1,
    marginRight: theme.spacing.sm,
    color: theme.colors.text },
  verifiedBadge: { flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.success,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.lg,
    marginLeft: theme.spacing.sm },
  verifiedText: { color: theme.colors.white,
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4 },
  description: { fontSize: 14,
    lineHeight: 20,
    marginBottom: theme.spacing.sm,
    color: theme.colors.textSecondary },
  footer: { flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.sm },
  rating: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  ratingText: { marginLeft: 4,
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text },
  location: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  locationText: { marginLeft: 4,
    fontSize: 14,
    color: theme.colors.textSecondary },
  categories: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center'
  },
  categoryBadge: { backgroundColor: theme.colors.surfaceVariant,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.md,
    marginRight: theme.spacing.sm,
    marginBottom: 4 },
  categoryText: { fontSize: 12,
    fontWeight: '500',
    color: theme.colors.primary },
  moreCategories: { fontSize: 12,
    fontWeight: '500',
    color: theme.colors.primary },

  // Compact card styles;
  compactCard: {
    width: 200,
    borderRadius: theme.borderRadius.lg,
    marginRight: theme.spacing.sm,
    overflow: 'hidden',
    backgroundColor: theme.colors.surface,
    ...theme.shadows.small;
  },
  compactImage: {
    width: '100%',
    height: 110,
    resizeMode: 'cover'
  },
  compactContent: { padding: theme.spacing.sm },
  compactTitle: { fontSize: 16,
    fontWeight: '600',
    marginBottom: theme.spacing.xs,
    color: theme.colors.text },
  compactRating: { flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.xs },
  compactRatingText: { marginLeft: 4,
    fontSize: 12,
    fontWeight: '500',
    color: theme.colors.text });
  compactLocation: {
    flexDirection: 'row'),
    alignItems: 'center'
  },
  compactLocationText: {
    marginLeft: 4,
    fontSize: 12,
    color: theme.colors.textSecondary)
  },
})