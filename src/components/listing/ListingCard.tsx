import React from 'react';
import { View, StyleSheet, Image, TouchableOpacity, Dimensions } from 'react-native';
import { MapPin, DollarSign, BedDouble, Bath, Calendar } from 'lucide-react-native';
import { Text } from '@design-system';
import type { RoomWithDetails } from '../../types/models';
import { formatCurrency } from '@utils/format';
import { formatDateForDisplay } from '@utils/date';
import { colorWithOpacity, type Theme } from '@design-system';
import { useTheme } from '@design-system';
import { Badge } from '@components/ui';

interface ListingCardProps {
  listing: RoomWithDetails,
  onPress?: () => void,
  compact?: boolean,
}
export const ListingCard: React.FC<ListingCardProps> = ({ listing, onPress, compact = false }) => {
  const theme = useTheme();
  const styles = createStyles(theme);
  // Default image if none provided;
  const imageUrl =;
    listing.images && listing.images.length > 0;
      ? listing.images[0];
      : 'https://via.placeholder.com/300x200?text=No+Image',
  // Format room type for display;
  const formatRoomType = (type: string) => {
    switch (type) {
      case 'private': ,
        return 'Private Room';
      case 'shared': ,
        return 'Shared Room';
      case 'entire': ,
        return 'Entire Place';
      default: ,
        return type;
    }
  }
  return (
    <TouchableOpacity
      style={[styles.container, compact && styles.compactContainer]}
      onPress={onPress}
      activeOpacity={0.9}
    >
      <Image
        source={ uri: imageUrl }
        style={[styles.image, compact && styles.compactImage]}
        resizeMode='cover';
      />
      <View style={[styles.content, compact && styles.compactContent]}>
        <Text style={styles.title} numberOfLines={1}>
          {listing.title}
        </Text>
        <View style={styles.priceRow}>
          <DollarSign size={16} color={{theme.colors.primary} /}>
          <Text style={styles.price}>{formatCurrency(listing.price)}/month</Text>
        </View>
        <View style={styles.locationRow}>
          <MapPin size={14} color={{theme.colors.textSecondary} /}>
          <Text style={styles.location} numberOfLines={1}>
            {listing.location}
          </Text>
        </View>
        {!compact && (
          <View style={styles.detailsRow}>
            <View style={styles.detailItem}>
              <BedDouble size={14} color={{theme.colors.textSecondary} /}>
              <Text style={styles.detailText}>
                {listing.bedrooms} {listing.bedrooms === 1 ? 'Bedroom' : 'Bedrooms'}
              </Text>
            </View>
            <View style={styles.detailItem}>
              <Bath size={14} color={{theme.colors.textSecondary} /}>
              <Text style={styles.detailText}>
                {listing.bathrooms} {listing.bathrooms === 1 ? 'Bathroom' : 'Bathrooms'}
              </Text>
            </View>
          </View>
        )}
        {!compact && listing.updated_at && (
          <View style={styles.moveInRow}>
            <Calendar size={14} color={{theme.colors.textSecondary} /}>
            <Text style={styles.moveInText}>
              Updated{' '}
              {formatDateForDisplay(
                new Date(listing.updated_at || listing.created_at || new Date().toISOString());
              )}
            </Text>
          </View>
        )}
        <View style={styles.typeContainer}>
          <Text style={styles.typeText}>{formatRoomType(listing.room_type || 'unknown')}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
}
const { width } = Dimensions.get('window');

const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      backgroundColor: theme.colors.surface,
      borderRadius: theme.borderRadius?.xl || 16,
      overflow: 'hidden',
      shadowColor: theme.colors.text,
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.08,
      shadowRadius: 12,
      elevation: 5,
      borderWidth: 1,
      borderColor: theme.colors.border,
      marginBottom: theme.spacing?.md || 12,
    },
    compactContainer: {
      flexDirection: 'row',
      height: 120,
      borderRadius: theme.borderRadius?.lg || 12,
    },
    image: {
      width: '100%',
      height: 200,
      backgroundColor: theme.colors.surfaceVariant,
    },
    compactImage: {
      width: 120,
      height: '100%',
      borderTopRightRadius: 0,
      borderBottomRightRadius: 0,
    },
    content: {
      padding: theme.spacing?.lg || 16,
    },
    compactContent: {
      flex: 1,
      padding: theme.spacing?.md || 12,
    },
    title: {
      fontSize: theme.typography?.fontSize?.lg || 18,
      fontWeight: '700',
      color: theme.colors.text,
      marginBottom: theme.spacing?.sm || 8,
      lineHeight: 24,
    },
    priceRow: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colorWithOpacity(theme.colors.primary, 0.1),
      paddingHorizontal: theme.spacing?.sm || 8,
      paddingVertical: theme.spacing?.xs || 4,
      borderRadius: theme.borderRadius?.md || 8,
      marginBottom: theme.spacing?.sm || 8,
      alignSelf: 'flex-start',
    },
    price: {
      fontSize: theme.typography?.fontSize?.md || 16,
      fontWeight: '800',
      color: theme.colors.primary,
      marginLeft: theme.spacing?.xs || 4,
    },
    locationRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: theme.spacing?.md || 12,
      backgroundColor: theme.colors.surfaceVariant,
      paddingHorizontal: theme.spacing?.sm || 8,
      paddingVertical: theme.spacing?.xs || 4,
      borderRadius: theme.borderRadius?.md || 8,
    },
    location: {
      fontSize: theme.typography?.fontSize?.sm || 14,
      color: theme.colors.textSecondary,
      marginLeft: theme.spacing?.xs || 4,
      flex: 1,
      fontWeight: '500',
    },
    detailsRow: {
      flexDirection: 'row',
      marginBottom: theme.spacing?.md || 12,
      backgroundColor: colorWithOpacity(theme.colors.success, 0.05),
      padding: theme.spacing?.sm || 8,
      borderRadius: theme.borderRadius?.md || 8,
    },
    detailItem: {
      flexDirection: 'row',
      alignItems: 'center',
      marginRight: theme.spacing?.lg || 20,
      flex: 1,
    },
    detailText: {
      fontSize: theme.typography?.fontSize?.sm || 13,
      color: theme.colors.textSecondary,
      marginLeft: theme.spacing?.xs || 4,
      fontWeight: '600',
    },
    moveInRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: theme.spacing?.md || 12,
      backgroundColor: colorWithOpacity(theme.colors.warning, 0.08),
      paddingHorizontal: theme.spacing?.sm || 8,
      paddingVertical: theme.spacing?.xs || 4,
      borderRadius: theme.borderRadius?.md || 8,
    },
    moveInText: {
      fontSize: theme.typography?.fontSize?.xs || 12,
      color: theme.colors.textMuted,
      marginLeft: theme.spacing?.xs || 4,
      fontWeight: '500',
      fontStyle: 'italic',
    },
    typeContainer: {
      alignSelf: 'flex-start',
      backgroundColor: theme.colors.primary,
      paddingHorizontal: theme.spacing?.md || 12,
      paddingVertical: theme.spacing?.sm || 6,
      borderRadius: theme.borderRadius?.full || 20,
      shadowColor: theme.colors.primary,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.2,
      shadowRadius: 4,
      elevation: 3,
    },
    typeText: {
      fontSize: theme.typography?.fontSize?.xs || 12,
      color: theme.colors.textInverse,
      fontWeight: '700',
      textTransform: 'uppercase',
      letterSpacing: 0.5,
    },
  });

export default ListingCard;
