import React from 'react';
import { useTheme } from '@design-system';
import { View, StyleSheet, ScrollView, Image, Dimensions } from 'react-native';
import {
  MapPin,
  DollarSign,
  Calendar,
  BedDouble,
  Bath,
  Check,
  Tag,
  Users,
} from 'lucide-react-native';
import { Text } from '@design-system';
import { RoomFormData } from '@types/models';
import { formatDateForDisplay } from '@utils/date';
import { calculateListingCompletion } from '@utils/listingValidation';

interface ListingPreviewProps {
  data: Partial<RoomFormData>
}
/**;
 * A component that shows a preview of how the listing will appear to users;
 */;
export const ListingPreview: React.FC<ListingPreviewProps> = ({ data }) => {
  const theme = useTheme();
  const styles = createStyles(theme);
  const completionPercentage = calculateListingCompletion(data);
  const windowWidth = Dimensions.get('window').width;

  // Helper function to render amenities and preferences;
  const renderTags = (items: string[] = [], title: string) => {
    if (!items.length) return null;

    return (
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>{title}</Text>
        <View style={styles.tagsContainer}>
          {items.map((item, index) => (
            <View key={index} style={styles.tag}>
              <Check size={14} color={{theme.colors.primary} /}>
              <Text style={styles.tagText}>{item}</Text>
            </View>
          ))}
        </View>
      </View>
    );
  }
  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.completionContainer}>
        <Text style={styles.completionText}>Listing Completion: {completionPercentage}%</Text>
        <View style={styles.completionBarContainer}>
          <View
            style={[
              styles.completionBar,
              {
                width: `${completionPercentage}%`,
                backgroundColor: ,
                  completionPercentage < 70 ? theme.colors.warning : theme.colors.success,
              },
            ]}
          />
        </View>
      </View>
      {/* Image Gallery */}
      {data.images && data.images.length > 0 ? (
        <View style={styles.imageContainer}>
          <Image source={ uri: data.images[0] } style={styles.mainImage} resizeMode={'cover' /}>
          {data.images.length > 1 && (
            <View style={styles.thumbnailContainer}>
              {data.images.slice(1, 4).map((image, index) => (
                <Image
                  key={index}
                  source={ uri: image }
                  style={[styles.thumbnail, { width: (windowWidth - 48) / 3 - 4 }]}
                  resizeMode='cover';
                />
              ))}
              {data.images.length > 4 && (
                <View style={{[styles.moreImagesOverlay, { width: (windowWidth - 48) / 3 - 4 }]}}>
                  <Text style={styles.moreImagesText}>+{data.images.length - 4}</Text>
                </View>
              )}
            </View>
          )}
        </View>
      ) : (,
        <View style={styles.noImageContainer}>
          <Text style={styles.noImageText}>No images added</Text>
        </View>
      )}
      {/* Title and Price */}
      <View style={styles.titleContainer}>
        <Text style={styles.title}>{data.title || 'No Title'}</Text>
        <View style={styles.priceContainer}>
          <DollarSign size={16} color={{theme.colors.primary} /}>
          <Text style={styles.price}>{data.price || 0}/month</Text>
        </View>
      </View>
      {/* Location */}
      <View style={styles.infoRow}>
        <MapPin size={16} color={{theme.colors.textSecondary} /}>
        <Text style={styles.infoText}>{data.location || 'No location specified'}</Text>
      </View>
      {/* Room Details */}
      <View style={styles.detailsContainer}>
        <View style={styles.detailItem}>
          <BedDouble size={20} color={{theme.colors.textSecondary} /}>
          <Text style={styles.detailText}>
            {data.bedrooms || 0} Bedroom{data.bedrooms !== 1 ? 's' : ''}
          </Text>
        </View>
        <View style={styles.detailItem}>
          <Bath size={20} color={{theme.colors.textSecondary} /}>
          <Text style={styles.detailText}>
            {data.bathrooms || 0} Bathroom{data.bathrooms !== 1 ? 's' : ''}
          </Text>
        </View>
        <View style={styles.detailItem}>
          <Calendar size={20} color={{theme.colors.textSecondary} /}>
          <Text style={styles.detailText}>
            {data.move_in_date ? formatDateForDisplay(new Date(data.move_in_date)) : 'No date'}
          </Text>
        </View>
        <View style={styles.detailItem}>
          <Tag size={20} color={{theme.colors.textSecondary} /}>
          <Text style={styles.detailText}>
            {data.room_type;
              ? data.room_type.charAt(0).toUpperCase() + data.room_type.slice(1) + ' Room';
              : 'No room type'}
          </Text>
        </View>
      </View>
      {/* Description */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Description</Text>
        <Text style={styles.description}>{data.description || 'No description provided'}</Text>
      </View>
      {/* Amenities */}
      {renderTags(data.amenities, 'Amenities')}
      {/* Preferences */}
      {renderTags(data.preferences, 'Roommate Preferences')}
      {/* Additional Info */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Additional Information</Text>
        <View style={styles.additionalInfo}>
          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>Furnished:</Text>
            <Text style={styles.infoValue}>{data.furnished ? 'Yes' : 'No'}</Text>
          </View>
          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>Pets Allowed:</Text>
            <Text style={styles.infoValue}>{data.pets_allowed ? 'Yes' : 'No'}</Text>
          </View>
        </View>
      </View>
      <View style={styles.disclaimer}>
        <Text style={styles.disclaimerText}>
          This is a preview of how your listing will appear to other users.;
        </Text>
      </View>
    </ScrollView>
  );
}
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    completionContainer: {
      marginBottom: 16,
      backgroundColor: theme.colors.surface,
      padding: 12,
      borderRadius: 8,
    },
    completionText: {
      fontSize: 14,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 8,
    },
    completionBarContainer: {
      height: 8,
      backgroundColor: theme.colors.border,
      borderRadius: 4,
      overflow: 'hidden',
    },
    completionBar: {
      height: '100%',
      borderRadius: 4,
    },
    imageContainer: {
      marginBottom: 16,
      borderRadius: 12,
      overflow: 'hidden',
    },
    mainImage: {
      width: '100%',
      height: 240,
      borderRadius: 12,
    },
    thumbnailContainer: {
      flexDirection: 'row',
      marginTop: 8,
      justifyContent: 'space-between',
    },
    thumbnail: {
      height: 80,
      borderRadius: 8,
    },
    moreImagesOverlay: {
      height: 80,
      borderRadius: 8,
      backgroundColor: 'rgba(0,0,0,0.7)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    moreImagesText: {
      color: theme.colors.white,
      fontSize: 16,
      fontWeight: '600',
    },
    noImageContainer: {
      height: 240,
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 16,
    },
    noImageText: {
      color: theme.colors.textMuted,
      fontSize: 16,
    },
    titleContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginBottom: 8,
    },
    title: {
      fontSize: 22,
      fontWeight: '700',
      color: theme.colors.text,
      flex: 1,
      marginRight: 16,
    },
    priceContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.surfaceVariant,
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
    },
    price: {
      fontSize: 16,
      fontWeight: '700',
      color: theme.colors.primary,
      marginLeft: 4,
    },
    infoRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 16,
    },
    infoText: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      marginLeft: 8,
    },
    detailsContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginBottom: 16,
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 16,
    },
    detailItem: {
      flexDirection: 'row',
      alignItems: 'center',
      width: '50%',
      marginBottom: 12,
    },
    detailText: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      marginLeft: 8,
    },
    section: {
      marginBottom: 20,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 12,
    },
    description: {
      fontSize: 16,
      lineHeight: 24,
      color: theme.colors.textSecondary,
    },
    tagsContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
    },
    tag: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: theme.colors.surfaceVariant,
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
      marginRight: 8,
      marginBottom: 8,
    },
    tagText: {
      fontSize: 14,
      color: theme.colors.text,
      marginLeft: 6,
    },
    additionalInfo: {
      backgroundColor: theme.colors.surface,
      borderRadius: 8,
      padding: 12,
    },
    infoItem: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 8,
    },
    infoLabel: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      fontWeight: '500',
    },
    infoValue: {
      fontSize: 14,
      color: theme.colors.text,
      fontWeight: '600',
    },
    disclaimer: {
      marginTop: 8,
      marginBottom: 24,
      padding: 12,
      backgroundColor: theme.colors.surfaceVariant,
      borderRadius: 8,
    },
    disclaimerText: {
      fontSize: 14,
      color: theme.colors.textMuted,
      fontStyle: 'italic',
      textAlign: 'center',
    },
  });

export default ListingPreview;
