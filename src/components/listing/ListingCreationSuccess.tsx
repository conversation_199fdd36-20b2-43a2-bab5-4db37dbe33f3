import React from 'react';
import { useTheme } from '@design-system';
import { View, StyleSheet, Image, TouchableOpacity } from 'react-native';
import { useRouter } from 'expo-router';
import { Check, Eye, Plus, Share2 } from 'lucide-react-native';
import { Text } from '@design-system';
import { Button } from '@design-system';
import { RoomWithDetails } from '@types/models';

interface ListingCreationSuccessProps {
  listing: RoomWithDetails,
  onCreateAnother: () => void,
}
/**;
 * A component that shows a success message after a listing is created;
 */;
export const ListingCreationSuccess: React.FC<ListingCreationSuccessProps> = ({
  listing,
  onCreateAnother,
}) => {
  const router = useRouter();
  const theme = useTheme();
  const styles = createStyles(theme);

  const handleViewListing = () => {
    router.push(`/room/${listing.id}`);
  }
  const handleShareListing = () => {
    // This would be implemented with the Share API;
    alert('Sharing functionality will be implemented in a future update');
  }
  return (
    <View style={styles.container}>
      <View style={styles.iconContainer}>
        <Check size={40} color={{theme.colors.white} /}>
      </View>
      <Text style={styles.title}>Listing Created Successfully!</Text>
      <Text style={styles.subtitle}>
        Your listing "{listing.title}" has been published and is now visible to potential roommates.;
      </Text>
      {listing.images && listing.images.length > 0 && (
        <Image source={ uri: listing.images[0] } style={styles.previewImage} resizeMode={'cover' /}>
      )}
      <View style={styles.statsContainer}>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>${listing.price}</Text>
          <Text style={styles.statLabel}>per month</Text>
        </View>
        <View style={{styles.divider} /}>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{listing.room_type}</Text>
          <Text style={styles.statLabel}>room type</Text>
        </View>
        <View style={{styles.divider} /}>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{listing.location}</Text>
          <Text style={styles.statLabel}>location</Text>
        </View>
      </View>
      <View style={styles.actionsContainer}>
        <Button onPress={handleViewListing} variant='filled' leftIcon={Eye} style={styles.button}>
          View Listing;
        </Button>
        <Button onPress={onCreateAnother} variant='outlined' leftIcon={Plus} style={styles.button}>
          Create Another;
        </Button>
        <TouchableOpacity style={styles.shareButton} onPress={handleShareListing}>
          <Share2 size={20} color={theme.colors.primary} />
          <Text style={styles.shareText}>Share Listing</Text>
        </TouchableOpacity>
      </View>
      <View style={styles.tipContainer}>
        <Text style={styles.tipTitle}>Tips for Success:</Text>
        <Text style={styles.tipText}>• Keep your listing up to date with accurate information</Text>
        <Text style={styles.tipText}>• Respond promptly to inquiries from potential roommates</Text>
        <Text style={styles.tipText}>• Add more photos to increase interest in your listing</Text>
      </View>
    </View>
  );
}
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      backgroundColor: theme.colors.background,
      borderRadius: 16,
      padding: 24,
      alignItems: 'center',
    },
    iconContainer: {
      width: 80,
      height: 80,
      borderRadius: 40,
      backgroundColor: theme.colors.success,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 20,
      shadowColor: theme.colors.success,
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 8,
      elevation: 5,
    },
    title: {
      fontSize: 24,
      fontWeight: '700',
      color: theme.colors.text,
      marginBottom: 12,
      textAlign: 'center',
    },
    subtitle: {
      fontSize: 16,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      marginBottom: 24,
      lineHeight: 24,
    },
    previewImage: {
      width: '100%',
      height: 180,
      borderRadius: 12,
      marginBottom: 24,
    },
    statsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      width: '100%',
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 16,
      marginBottom: 24,
    },
    statItem: {
      flex: 1,
      alignItems: 'center',
    },
    statValue: {
      fontSize: 16,
      fontWeight: '700',
      color: theme.colors.text,
      marginBottom: 4,
    },
    statLabel: {
      fontSize: 12,
      color: theme.colors.textSecondary,
    },
    divider: {
      width: 1,
      backgroundColor: theme.colors.border,
      marginHorizontal: 8,
    },
    actionsContainer: {
      width: '100%',
      marginBottom: 24,
    },
    button: {
      marginBottom: 12,
    },
    shareButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      padding: 12,
    },
    shareText: {
      color: theme.colors.primary,
      fontSize: 16,
      fontWeight: '500',
      marginLeft: 8,
    },
    tipContainer: {
      width: '100%',
      backgroundColor: theme.colors.surfaceVariant,
      borderRadius: 12,
      padding: 16,
    },
    tipTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 8,
    },
    tipText: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      marginBottom: 6,
      lineHeight: 20,
    },
  });

export default ListingCreationSuccess;
