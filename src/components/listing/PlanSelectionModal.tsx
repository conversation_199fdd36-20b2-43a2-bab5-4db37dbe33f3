import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Modal,
  Dimensions,
  Alert,
} from 'react-native';
import { Check, Star, X } from 'lucide-react-native';
import { Text } from '@design-system';
import { Button } from '@design-system';
import { LISTING_PLANS, type ListingPlan } from '@config/listingConfig';
import { colorWithOpacity, type Theme } from '@design-system';
import { useTheme } from '@design-system';

interface PlanSelectionModalProps {
  visible: boolean,
  onClose: () => void,
  onSelectPlan: (planId: string) => void,
  selectedPlanId?: string,
}
const { width: screenWidth } = Dimensions.get('window');

// Defensive theme validation;
const validateTheme = (theme: any) => {
  if (!theme) {
    console.warn('PlanSelectionModal: Theme is undefined, using fallbacks');
    return false;
  }
  if (!theme.colors) {
    console.warn('PlanSelectionModal: Theme colors are undefined, using fallbacks');
    return false;
  }
  return true;
}
export function PlanSelectionModal({
  visible,
  onClose,
  onSelectPlan,
  selectedPlanId,
}: PlanSelectionModalProps) {
  // Theme validation;
  const theme = useTheme();

  // Check if theme is valid;
  const isThemeValid =;
    theme && theme.colors && theme.spacing && theme.typography && theme.borderRadius;

  if (!isThemeValid) {
    console.error('PlanSelectionModal: Invalid theme detected, component may not render correctly');
  }
  const styles = createStyles(theme);

  const [localSelectedPlan, setLocalSelectedPlan] = useState<string>(selectedPlanId || 'basic');
  const [isProcessing, setIsProcessing] = useState(false);

  const handleSelectPlan = (planId: string) => {
    setLocalSelectedPlan(planId);
  }
  const handleConfirm = async () => {
    console.log('📋 Plan confirmed:', localSelectedPlan);
    setIsProcessing(true);

    try {
      await new Promise(resolve => setTimeout(resolve, 500)); // Brief delay for UX;
      onSelectPlan(localSelectedPlan);
    } finally {
      setIsProcessing(false);
    }
    // Don't call onClose() here - let the parent handle modal state;
  }
  // Move PlanCard definition here to ensure access to theme and styles;
  const renderPlanCard = ({ plan, isSelected }: { plan: ListingPlan; isSelected: boolean }) => (
    <TouchableOpacity
      style={[
        styles.planCard,
        isSelected && styles.selectedPlanCard,
        plan.isPopular && styles.popularPlanCard,
      ]}
      onPress={() => handleSelectPlan(plan.id)}
    >
      {plan.isPopular && (
        <View style={styles.popularBadge}>
          <Star size={12} color={{theme.colors.textInverse} /}>
          <Text style={styles.popularText}>Most Popular</Text>
        </View>
      )}
      <View style={styles.planHeader}>
        <Text style={[styles.planName, isSelected && styles.selectedText]}>{plan.name}</Text>
        <View style={styles.priceContainer}>
          <Text style={[styles.price, isSelected && styles.selectedText]}>${plan.price}</Text>
          <Text style={[styles.duration, isSelected && styles.selectedText]}>
            for {plan.duration} days;
          </Text>
        </View>
      </View>
      <Text style={[styles.planDescription, isSelected && styles.selectedText]}>
        {plan.description}
      </Text>
      <View style={styles.featuresContainer}>
        {plan.features.map((feature, index) => (
          <View key={index} style={styles.featureItem}>
            <Check size={16} color={{isSelected ? theme.colors.textInverse : theme.colors.success} /}>
            <Text style={[styles.featureText, isSelected && styles.selectedText]}>{feature}</Text>
          </View>
        ))}
      </View>
      {isSelected && (
        <View style={styles.selectedIndicator}>
          <Check size={20} color={{theme.colors.textInverse} /}>
        </View>
      )}
    </TouchableOpacity>
  );

  return (
    <Modal
      visible={visible}
      animationType='slide';
      presentationStyle='pageSheet';
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Choose Your Listing Plan</Text>
          <TouchableOpacity
            onPress={() => {
              console.log('❌ User tapped close on plan selection');
              // Add confirmation before closing;
              Alert.alert(
                'Exit Plan Selection?',
                'Are you sure you want to exit without selecting a plan? Your listing draft will be saved.',
                [
                  {
                    text: 'Stay',
                    style: 'cancel',
                  },
                  {
                    text: 'Exit',
                    style: 'destructive',
                    onPress: onClose,
                  },
                ];
              );
            }}
            style={styles.closeButton}
          >
            <X size={24} color={{theme.colors.textSecondary} /}>
          </TouchableOpacity>
        </View>
        <Text style={styles.subtitle}>
          Select a plan to get the most visibility for your listing;
        </Text>
        <ScrollView style={styles.plansContainer} showsVerticalScrollIndicator={false}>
          {LISTING_PLANS.map(plan => (
            <View key={plan.id}>
              {renderPlanCard({ plan, isSelected: localSelectedPlan === plan.id })}
            </View>
          ))}
        </ScrollView>
        <View style={styles.footer}>
          <Button
            onPress={handleConfirm}
            style={styles.continueButton}
            variant='filled';
            loading={isProcessing}
          >
            Continue with {LISTING_PLANS.find(p => p.id === localSelectedPlan)?.name}
          </Button>
        </View>
      </View>
    </Modal>
  );
}
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      backgroundColor: theme.colors.primary,
      paddingTop: theme.spacing?.xl || 24,
      paddingHorizontal: theme.spacing?.lg || 20,
      paddingBottom: theme.spacing?.lg || 20,
      shadowColor: theme.colors.primary,
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.15,
      shadowRadius: 8,
      elevation: 6,
    },
    title: {
      fontSize: theme.typography?.fontSize?.xl || 22,
      fontWeight: '700',
      color: theme.colors.textInverse,
      flex: 1,
      textAlign: 'center',
    },
    closeButton: {
      backgroundColor: colorWithOpacity(theme.colors.textInverse, 0.15),
      borderRadius: theme.borderRadius?.full || 20,
      padding: theme.spacing?.sm || 8,
      alignItems: 'center',
      justifyContent: 'center',
    },
    subtitle: {
      fontSize: theme.typography?.fontSize?.md || 16,
      color: theme.colors.textMuted,
      textAlign: 'center',
      paddingHorizontal: theme.spacing?.lg || 20,
      paddingVertical: theme.spacing?.lg || 20,
      backgroundColor: theme.colors.surfaceVariant,
      fontWeight: '500',
    },
    plansContainer: {
      flex: 1,
      paddingHorizontal: theme.spacing?.lg || 20,
      paddingTop: theme.spacing?.lg || 20,
    },
    planCard: {
      backgroundColor: theme.colors.surface,
      borderRadius: theme.borderRadius?.xl || 16,
      padding: theme.spacing?.xl || 24,
      marginBottom: theme.spacing?.lg || 20,
      borderWidth: 1.5,
      borderColor: theme.colors.border,
      position: 'relative',
      shadowColor: theme.colors.text,
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.08,
      shadowRadius: 12,
      elevation: 5,
    },
    selectedPlanCard: {
      backgroundColor: theme.colors.primary,
      borderColor: theme.colors.primary,
      shadowColor: theme.colors.primary,
      shadowOffset: { width: 0, height: 6 },
      shadowOpacity: 0.25,
      shadowRadius: 16,
      elevation: 10,
      transform: [{ scale: 1.02 }],
    },
    popularPlanCard: {
      borderWidth: 2,
      borderColor: theme.colors.warning,
      shadowColor: theme.colors.warning,
      shadowOpacity: 0.15,
      shadowRadius: 8,
      elevation: 6,
    },
    popularBadge: {
      position: 'absolute',
      top: -10,
      alignSelf: 'center',
      backgroundColor: theme.colors.warning,
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: theme.spacing?.md || 12,
      paddingVertical: theme.spacing?.sm || 6,
      borderRadius: theme.borderRadius?.full || 20,
      shadowColor: theme.colors.warning,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.3,
      shadowRadius: 4,
      elevation: 4,
    },
    popularText: {
      color: theme.colors.textInverse,
      fontSize: theme.typography?.fontSize?.xs || 12,
      fontWeight: '700',
      marginLeft: theme.spacing?.xs || 4,
      textTransform: 'uppercase',
      letterSpacing: 0.5,
    },
    planHeader: {
      marginBottom: theme.spacing?.lg || 16,
      alignItems: 'center',
    },
    planName: {
      fontSize: theme.typography?.fontSize?.xl || 20,
      fontWeight: '700',
      color: theme.colors.text,
      marginBottom: theme.spacing?.sm || 8,
      textAlign: 'center',
    },
    priceContainer: {
      flexDirection: 'row',
      alignItems: 'baseline',
      justifyContent: 'center',
      backgroundColor: colorWithOpacity(theme.colors.primary, 0.08),
      paddingHorizontal: theme.spacing?.lg || 16,
      paddingVertical: theme.spacing?.sm || 8,
      borderRadius: theme.borderRadius?.lg || 12,
    },
    price: {
      fontSize: theme.typography?.fontSize?.xxl || 28,
      fontWeight: '800',
      color: theme.colors.primary,
      marginRight: theme.spacing?.xs || 4,
    },
    duration: {
      fontSize: theme.typography?.fontSize?.sm || 14,
      color: theme.colors.textMuted,
      fontWeight: '600',
    },
    planDescription: {
      fontSize: theme.typography?.fontSize?.md || 15,
      color: theme.colors.textSecondary,
      marginBottom: theme.spacing?.lg || 18,
      lineHeight: 22,
      textAlign: 'center',
      fontWeight: '500',
    },
    featuresContainer: {
      marginBottom: theme.spacing?.lg || 16,
      backgroundColor: colorWithOpacity(theme.colors.success, 0.05),
      borderRadius: theme.borderRadius?.lg || 12,
      padding: theme.spacing?.md || 12,
    },
    featureItem: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: theme.spacing?.sm || 10,
      paddingVertical: theme.spacing?.xs || 2,
    },
    featureText: {
      fontSize: theme.typography?.fontSize?.md || 15,
      color: theme.colors.text,
      marginLeft: theme.spacing?.sm || 10,
      flex: 1,
      fontWeight: '500',
    },
    selectedText: {
      color: theme.colors.textInverse,
    },
    selectedIndicator: {
      position: 'absolute',
      top: theme.spacing?.lg || 16,
      right: theme.spacing?.lg || 16,
      width: 36,
      height: 36,
      borderRadius: 18,
      backgroundColor: theme.colors.success,
      alignItems: 'center',
      justifyContent: 'center',
      shadowColor: theme.colors.success,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.4,
      shadowRadius: 6,
      elevation: 6,
    },
    footer: {
      backgroundColor: theme.colors.surface,
      paddingHorizontal: theme.spacing?.lg || 20,
      paddingVertical: theme.spacing?.xl || 24,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
      shadowColor: theme.colors.text,
      shadowOffset: { width: 0, height: -2 },
      shadowOpacity: 0.1,
      shadowRadius: 8,
      elevation: 8,
    },
    continueButton: {
      width: '100%',
      paddingVertical: theme.spacing?.md || 16,
      borderRadius: theme.borderRadius?.xl || 16,
      shadowColor: theme.colors.primary,
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 8,
      elevation: 6,
    },
  });
