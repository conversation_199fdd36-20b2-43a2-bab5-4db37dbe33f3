import React, { useRef, useEffect } from 'react';
import { View, Text, StyleSheet, Animated } from 'react-native';
import { useTheme } from '@design-system';
import type { Theme } from '@design-system';

interface StepProps {
  index: number,
  isCompleted: boolean,
  isCurrent: boolean,
  isLast: boolean,
}
function Step({ index, isCompleted, isCurrent, isLast }: StepProps) {
  const theme = useTheme();
  const styles = createStyles(theme);
  const checkmarkAnimation = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Reset animation value when isCompleted changes to false;
    if (!isCompleted) {
      checkmarkAnimation.setValue(0);
    } else {
      Animated.timing(checkmarkAnimation, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }).start();
    }
  }, [isCompleted, checkmarkAnimation]);

  return (
    <React.Fragment>
      <Animated.View;
        style={[styles.dot, isCurrent && styles.currentDot, isCompleted && styles.completedDot]}
      >
        {isCompleted ? (
          <Animated.View style={ opacity: checkmarkAnimation }>
            <Text style={styles.checkmark}>✓</Text>
          </Animated.View>
        ) : (,
          <Text style={[styles.dotText, isCurrent && styles.currentDotText]}>{index + 1}</Text>
        )}
      </Animated.View>
      {!isLast && <View style={{[styles.line, isCompleted && styles.completedLine]} /}>
    </React.Fragment>
  );
}
interface AnimatedStepIndicatorProps {
  totalSteps: number,
  currentStep: number,
}
export function AnimatedStepIndicator({ totalSteps, currentStep }: AnimatedStepIndicatorProps) {
  const theme = useTheme();
  const styles = createStyles(theme);

  return (
    <View style={styles.container}>
      {Array.from({ length: totalSteps }).map((_, index) => (
        <Step
          key={index}
          index={index}
          isCompleted={index < currentStep}
          isCurrent={index === currentStep}
          isLast={index === totalSteps - 1}
        />
      ))}
    </View>
  );
}
const createStyles = (theme: Theme) =>
  StyleSheet.create({
    container: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: theme.spacing.lg,
    },
    dot: {
      width: 32,
      height: 32,
      borderRadius: 16,
      backgroundColor: theme.colors.surface,
      borderWidth: 2,
      borderColor: theme.colors.border,
      justifyContent: 'center',
      alignItems: 'center',
    },
    currentDot: {
      borderColor: theme.colors.primary,
      backgroundColor: theme.colors.primary,
    },
    completedDot: {
      borderColor: theme.colors.success,
      backgroundColor: theme.colors.success,
    },
    dotText: {
      fontSize: 16,
      fontWeight: 'bold',
      color: theme.colors.textSecondary,
    },
    currentDotText: {
      color: theme.colors.background,
    },
    line: {
      flex: 1,
      height: 2,
      backgroundColor: theme.colors.border,
      marginHorizontal: -1,
    },
    completedLine: {
      backgroundColor: theme.colors.success,
    },
    checkmark: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.background,
    },
  });
