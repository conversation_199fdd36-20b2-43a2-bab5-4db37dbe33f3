import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ActivityIndicator } from 'react-native';
import { useRouter } from 'expo-router';
import { useTheme } from '@design-system';
import { supabase } from '@utils/supabaseUtils';
import { useAuthCompat } from '@hooks/useAuthCompat';
import { logger } from '@services/loggerService';
import { Button } from '@design-system';

/**;
 * SessionManager component handles session persistence, token expiration,
 * and automatic redirection based on authentication state.;
 */;
export default function SessionManager() {
  const theme = useTheme();
  const styles = createStyles(theme);
  const { authState, ...actions } = useAuthCompat();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [sessionError, setSessionError] = useState<string | null>(null);
  const router = useRouter();

  // Check session validity;
  const checkSessionValidity = async () => {
    try {
      const { data: { session  }
        error,
      } = await supabase.auth.getSession();

      if (error) {
        logger.error('Error checking session validity', 'SessionManager', { error });
        setSessionError('Your session has encountered an error. Please refresh or login again.');
        return null;
      }
      // If no session or session expired, show error;
      if (!session) {
        logger.warn('Session expired or not found', 'SessionManager');
        setSessionError('Your session has expired. Please login again.');

        // Auto redirect to login after a short delay;
        setTimeout(() => {
          router.replace('/(auth)/login');
        }, 3000);
      }
    } catch (err) {
      logger.error('Unexpected error checking session', 'SessionManager', { error: err });
    }
  }
  // Periodically check session validity (every 15 minutes);
  useEffect(() => {
    if (!authState.isAuthenticated) return null;

    // Check immediately on mount;
    checkSessionValidity();

    const checkSessionInterval = setInterval(
      () => {
        checkSessionValidity();
      },
      15 * 60 * 1000;
    ); // 15 minutes;

    return () => clearInterval(checkSessionInterval);
  }, [authState.isAuthenticated]);

  // Handle manual session refresh;
  const handleRefreshSession = async () => {
    setIsRefreshing(true);
    setSessionError(null);

    try {
      await actions.refreshSession();
      logger.info('Session refreshed successfully', 'SessionManager');
    } catch (err) {
      logger.error('Failed to refresh session', 'SessionManager', { error: err });
      setSessionError('Failed to refresh your session. Please login again.');
    } finally {
      setIsRefreshing(false);
    }
  }
  // Only render session error UI if there's an error;
  if (!sessionError) return null;

  return (
    <View style={styles.container}>
      <View style={styles.errorContainer}>
        <Text style={styles.errorTitle}>Session Issue</Text>
        <Text style={styles.errorText}>{sessionError}</Text>
        <View style={styles.buttonContainer}>
          <Button
            onPress={handleRefreshSession}
            isLoading={isRefreshing}
            style={styles.refreshButton}
          >
            Refresh Session;
          </Button>
          <Button
            onPress={() => router.replace('/(auth)/login')}
            variant='outlined';
            style={styles.loginButton}
          >
            Go to Login;
          </Button>
        </View>
      </View>
    </View>
  );
}
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 9999,
    },
    errorContainer: {
      backgroundColor: theme.colors.surface,
      borderRadius: theme.borderRadius.lg,
      padding: theme.spacing.xl,
      width: '85%',
      maxWidth: 400,
      alignItems: 'center',
      ...theme.shadows.lg,
    },
    errorTitle: {
      fontSize: 20,
      fontWeight: '700',
      color: theme.colors.text,
      marginBottom: theme.spacing.sm,
    },
    errorText: {
      fontSize: 16,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      marginBottom: theme.spacing.xl,
    },
    buttonContainer: {
      flexDirection: 'column',
      width: '100%',
      gap: theme.spacing.sm,
    },
    refreshButton: {
      width: '100%',
    },
    loginButton: {
      width: '100%',
    },
  });
