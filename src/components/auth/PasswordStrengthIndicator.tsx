import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useTheme } from '@design-system';
import { Ionicons } from '@expo/vector-icons';

interface PasswordRequirement {
  id: string,
  label: string,
  isMet: boolean,
}
interface PasswordStrengthIndicatorProps {
  password?: string,
}
export function PasswordStrengthIndicator({ password = '' }: PasswordStrengthIndicatorProps) {
  const theme = useTheme();
  const styles = createStyles(theme);

  const requirements: PasswordRequirement[] = [,
    { id: 'length', label: 'At least 8 characters', isMet: password.length >= 8 },
    { id: 'uppercase', label: 'An uppercase letter', isMet: /[A-Z]/.test(password) },
    { id: 'lowercase', label: 'A lowercase letter', isMet: /[a-z]/.test(password) },
    { id: 'number', label: 'A number', isMet: /\d/.test(password) },
    { id: 'special', label: 'A special character', isMet: /[^A-Za-z0-9]/.test(password) },
  ];

  return (
    <View style={styles.container}>
      {requirements.map(req => (
        <View key={req.id} style={styles.requirementRow}>
          <Ionicons
            name={req.isMet ? 'checkmark-circle' : 'close-circle'}
            size={16}
            color={req.isMet ? theme.colors.success : theme.colors.textSecondary}
          />
          <Text
            style={[
              styles.requirementText,
              { color: req.isMet ? theme.colors.success : theme.colors.textSecondary },
            ]}
          >
            {req.label}
          </Text>
        </View>
      ))}
    </View>
  );
}
const createStyles = theme =>
  StyleSheet.create({
    container: {
      marginTop: theme.spacing.sm,
      padding: theme.spacing.md,
      backgroundColor: theme.colors.surface,
      borderRadius: theme.borderRadius.md,
    },
    requirementRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: theme.spacing.sm,
    },
    requirementText: {
      marginLeft: theme.spacing.sm,
      fontSize: 14,
    },
  });
