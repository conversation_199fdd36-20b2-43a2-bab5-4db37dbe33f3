import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Linking } from 'react-native';
import { useRouter } from 'expo-router';
import { Mail, ArrowRight, Shield, ExternalLink } from 'lucide-react-native';
import { Button } from '@design-system';
import { useTheme } from '@design-system';

interface ResetPasswordConfirmationProps {
  email?: string,
  onDismiss?: () => void,
  onResend?: () => void,
}
/**;
 * A component that shows a confirmation screen after a password reset email has been sent;
 */;
const ResetPasswordConfirmation = ({
  email,
  onDismiss,
  onResend,
}: ResetPasswordConfirmationProps) => {
  const theme = useTheme();
  const styles = createStyles(theme);
  const router = useRouter();

  const handleOpenEmail = async () => {
    try {
      // Try to open the default email app;
      await Linking.openURL('mailto: '),
    } catch (error) {
      console.error('Could not open email app', error);
    }
  }
  const handleBackToLogin = () => {
    router.replace('/(auth)/login');
  }
  return (
    <View style={styles.container}>
      <View style={styles.iconContainer}>
        <Mail size={24} color={'#FFFFFF' /}>
      </View>
      <View style={styles.contentWrapper}>
        <View style={styles.mailContainer}>
          <Mail size={40} color={{theme.colors.primary} /}>
        </View>
        <Text style={styles.title}>Check Your Email</Text>
        <Text style={styles.description}>
          We've sent password reset instructions to{' '}
          {email ? <Text style={styles.emailText}>{email}</Text> : 'your email address'}.;
        </Text>
        <View style={styles.instructionsContainer}>
          <Text style={styles.instructionsTitle}>Next steps:</Text>
          <View style={styles.instructionItem}>
            <Text style={styles.instructionNumber}>1.</Text>
            <Text style={styles.instructionText}>Check your email inbox (and spam folder)</Text>
          </View>
          <View style={styles.instructionItem}>
            <Text style={styles.instructionNumber}>2.</Text>
            <Text style={styles.instructionText}>Click the reset link in the email</Text>
          </View>
          <View style={styles.instructionItem}>
            <Text style={styles.instructionNumber}>3.</Text>
            <Text style={styles.instructionText}>Create a new secure password</Text>
          </View>
        </View>
        <View style={{styles.divider} /}>
        <Text style={styles.timeNote}>
          The reset link will expire in 30 minutes for security reasons.;
        </Text>
        <Button
          onPress={handleOpenEmail}
          variant='filled';
          color='primary';
          style={styles.emailButton}
          rightIcon={ExternalLink}
        >
          Open Email App;
        </Button>
        <Button
          onPress={handleBackToLogin}
          variant='outlined';
          color='primary';
          style={styles.loginButton}
        >
          Back to Login;
        </Button>
        {onResend && (
          <TouchableOpacity style={styles.resendButton} onPress={onResend}>
            <Text style={styles.resendText}>Didn't receive an email? Resend</Text>
          </TouchableOpacity>
        )}
        {onDismiss && (
          <TouchableOpacity style={styles.dismissButton} onPress={onDismiss}>
            <Text style={styles.dismissText}>Dismiss</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
}
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      backgroundColor: theme.colors.surface,
      borderRadius: theme.borderRadius.lg,
      overflow: 'hidden',
      width: '100%',
      maxWidth: 500,
      alignSelf: 'center',
      marginVertical: 20,
      ...theme.shadows.md,
    },
    iconContainer: {
      backgroundColor: theme.colors.primary,
      paddingVertical: theme.spacing.sm,
      alignItems: 'center',
    },
    contentWrapper: {
      padding: theme.spacing.xl,
      alignItems: 'center',
    },
    mailContainer: {
      width: 80,
      height: 80,
      borderRadius: 40,
      backgroundColor: theme.colors.primaryLight,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: theme.spacing.md,
    },
    title: {
      fontSize: 24,
      fontWeight: '700',
      color: theme.colors.text,
      marginBottom: theme.spacing.xs,
      textAlign: 'center',
    },
    description: {
      fontSize: 16,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      marginBottom: 20,
      lineHeight: 24,
    },
    emailText: {
      fontWeight: '600',
      color: theme.colors.text,
    },
    instructionsContainer: {
      width: '100%',
      backgroundColor: theme.colors.surfaceVariant,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.md,
      marginBottom: theme.spacing.md,
    },
    instructionsTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: theme.spacing.sm,
    },
    instructionItem: {
      flexDirection: 'row',
      marginBottom: theme.spacing.xs,
      alignItems: 'flex-start',
    },
    instructionNumber: {
      fontSize: 14,
      fontWeight: '700',
      color: theme.colors.primary,
      width: 20,
    },
    instructionText: {
      fontSize: 14,
      color: theme.colors.textSecondary,
      flex: 1,
      lineHeight: 20,
    },
    divider: {
      height: 1,
      backgroundColor: theme.colors.border,
      width: '100%',
      marginVertical: theme.spacing.md,
    },
    timeNote: {
      fontSize: 14,
      color: theme.colors.warning,
      textAlign: 'center',
      marginBottom: theme.spacing.xl,
      fontStyle: 'italic',
    },
    emailButton: {
      width: '100%',
      marginBottom: theme.spacing.sm,
    },
    loginButton: {
      width: '100%',
    },
    resendButton: {
      marginTop: theme.spacing.md,
      padding: theme.spacing.xs,
    },
    resendText: {
      fontSize: 14,
      color: theme.colors.primary,
      fontWeight: '500',
    },
    dismissButton: {
      marginTop: theme.spacing.xs,
      padding: theme.spacing.xs,
    },
    dismissText: {
      fontSize: 14,
      color: theme.colors.textMuted,
      fontWeight: '500',
    },
  });

export default ResetPasswordConfirmation;
