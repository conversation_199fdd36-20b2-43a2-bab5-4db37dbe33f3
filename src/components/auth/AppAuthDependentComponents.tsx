/**;
 * AppAuthDependentComponents;
 *;
 * A component that safely initializes and renders components that depend on auth context.;
 * This component uses <PERSON>act's useEffect to ensure the auth context is fully available;
 * before attempting to render auth-dependent components.;
 */;

import React, { useEffect, useState } from 'react';
import { View } from 'react-native';
// Fix the import to use the direct path instead of the alias;
import { useAuthCompat } from '@hooks/useAuthCompat';
import { RouteCheck } from '@core/middleware/auth/routeCheck';
import SessionManager from '@components/auth/SessionManager';

export function AppAuthDependentComponents() {
  const [isAuthReady, setIsAuthReady] = useState(false);
  const auth = useAuthCompat();

  // Use an effect to ensure auth is fully initialized;
  useEffect(() => {
    // Only set auth as ready once we've confirmed the context is loaded;
    // Check for auth.authState which comes from our compatibility hook;
    if (auth && auth.authState) {
      // Small delay to ensure context propagation is complete;
      const timer = setTimeout(() => {
        console.log('[AppAuthDependentComponents] Auth provider is ready');
        setIsAuthReady(true);
      }, 50);

      return () => clearTimeout(timer);
    }
  }, [auth]);

  // Only render dependent components when auth is confirmed ready;
  if (!isAuthReady) {
    return null;
  }
  return (
    <View>
      <RouteCheck />
      <SessionManager />
    </View>
  );
}
export default AppAuthDependentComponents;
