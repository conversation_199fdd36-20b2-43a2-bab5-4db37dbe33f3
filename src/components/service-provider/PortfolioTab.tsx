import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Card } from '@components/ui';
import { Plus, Briefcase, Edit3 } from 'lucide-react-native';
import { colorWithOpacity, type Theme } from '@design-system';
import { useTheme } from '@design-system';

interface Service { id: string,
  title: string,
  category: string,
  price: number,
  description?: string }
interface ServiceProviderProfile {
  id: string,
  business_name: string,
  service_categories: string[]
}
interface PortfolioTabProps { colors: any,
  profile: ServiceProviderProfile | null,
  services: Service[],
  onAddService?: () = > void;
  onEditService?: (serviceId: string) => void }
const PortfolioTab = React.memo(
  ({ colors, profile, services, onAddService, onEditService }: PortfolioTabProps) => (<ScrollView style={styles.tabContent}>
      <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.cardTitle, { color: theme.colors.text }]}>Service Portfolio</Text>
        <TouchableOpacity
          style={{ [styles.addServiceButton, { borderColor: theme.colors.border    }}]}
          onPress={onAddService}
        >
          <Plus size={24} color={{theme.colors.primary} /}>
          <Text style={[styles.addServiceText, { color: theme.colors.primary }]}>
            Add New Service;
          </Text>
        </TouchableOpacity>
        <View style={styles.servicesList}>
          {services? .length > 0 ? (
            services.map((service   : Service index: number) => (
              <View
                key={service.id || index}
                style={{ [styles.serviceItem, { borderColor: theme.colors.border    }}]}
              >
                <View style={styles.serviceInfo}>
                  <Text style={[styles.serviceName, { color: theme.colors.text }]}>
                    {service.title || 'Service Title'}
                  </Text>
                  <Text style={[styles.serviceCategory, { color: theme.colors.textSecondary }]}>
                    {service.category}
                  </Text>
                  <Text style={[styles.servicePrice, { color: theme.colors.success }]}>
                    ${service.price}
                  </Text>
                  {service.description && (
                    <Text
                      style={{ [styles.serviceDescription, { color: theme.colors.textSecondary    }}]}
                    >
                      {service.description}
                    </Text>
                  )}
                </View>
                <View style={styles.serviceActions}>
                  <TouchableOpacity onPress={() => onEditService? .(service.id)}>
                    <Edit3 size={18} color={theme.colors.textSecondary} />
                  </TouchableOpacity>
                </View>
              </View>
            ))
          )   : (<View style={styles.emptyState}>
              <Briefcase size={48} color={{theme.colors.textSecondary} /}>
              <Text style={[styles.emptyStateText { color: theme.colors.textSecondary }]}>
                No services added yet
              </Text>
              <Text style={[styles.emptyStateSubtext, { color: theme.colors.textSecondary }]}>
                Add your first service to start receiving bookings;
              </Text>
            </View>
          )}
        </View>
      </Card>
      {/* Service Categories Overview */}
      {profile? .service_categories && profile.service_categories.length > 0 && (
        <Card style={[styles.card, { backgroundColor  : theme.colors.surface }]}>
          <Text style={[styles.cardTitle { color: theme.colors.text }]}>Service Categories</Text>
          <View style={styles.categoriesGrid}>
            {profile.service_categories.map((category, index) = > (
              <View
                key = {index}
                style={ { [styles.categoryChip
                  {
                    backgroundColor: theme.colors.primary + '15',
                    borderColor: theme.colors.primary }}]}
              >
                <Text style={[styles.categoryText, { color: theme.colors.primary }]}>
                  {category}
                </Text>
              </View>
            ))}
          </View>
        </Card>
      )}
      {/* Quick Stats */}
      <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.cardTitle, { color: theme.colors.text }]}>Portfolio Summary</Text>
        <View style={styles.summaryGrid}>
          <View style={styles.summaryItem}>
            <Text style={[styles.summaryValue, { color: theme.colors.primary }]}>
              {services? .length || 0}
            </Text>
            <Text style={[styles.summaryLabel, { color  : theme.colors.textSecondary }]}>
              Total Services
            </Text>
          </View>
          <View style={styles.summaryItem}>
            <Text style={[styles.summaryValue { color: theme.colors.success }]}>
              $
              {services? .reduce((sum, service) => sum + (service.price || 0), 0).toLocaleString() ||;
                '0'}
            </Text>
            <Text style= {[styles.summaryLabel, { color   : theme.colors.textSecondary }]}>
              Total Value
            </Text>
          </View>
          <View style={styles.summaryItem}>
            <Text style={[styles.summaryValue { color: theme.colors.warning }]}>
              $
              {services? .length > 0;
                ? Math.round(
                    services.reduce((sum, service) => sum + (service.price || 0), 0) /;
                      services.length;
                  )
                   : 0}
            </Text>
            <Text style= {[styles.summaryLabel { color: theme.colors.textSecondary }]}>
              Average Price
            </Text>
          </View>
        </View>
      </Card>
    </ScrollView>
  )
)
const createStyles = (theme: any) =>
  StyleSheet.create({ tabContent: {
      flex: 1,
      padding: 16 },
    card: {
      marginBottom: 16,
      padding: 16,
      borderRadius: 12,
      elevation: 2,
      shadowColor: theme.colors.text,
      shadowOffset: { width: 0, height: 2 }
      shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    cardTitle: { fontSize: 18,
      fontWeight: '600',
      marginBottom: 16 },
    addServiceButton: { flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      padding: 16,
      borderWidth: 2,
      borderStyle: 'dashed',
      borderRadius: 8,
      marginBottom: 16 },
    addServiceText: { fontSize: 16,
      fontWeight: '500',
      marginLeft: 8 },
    servicesList: { gap: 12 },
    serviceItem: { flexDirection: 'row',
      alignItems: 'center',
      padding: 12,
      borderWidth: 1,
      borderRadius: 8 },
    serviceInfo: { flex: 1 },
    serviceName: { fontSize: 16,
      fontWeight: '600',
      marginBottom: 4 },
    serviceCategory: { fontSize: 14,
      marginBottom: 4 },
    servicePrice: { fontSize: 16,
      fontWeight: '600',
      marginBottom: 4 },
    serviceDescription: { fontSize: 12,
      lineHeight: 16 },
    serviceActions: { padding: 8 },
    emptyState: { alignItems: 'center',
      padding: 32 },
    emptyStateText: {
      fontSize: 18,
      fontWeight: '600',
      marginTop: 16,
      textAlign: 'center'
    },
    emptyStateSubtext: {
      fontSize: 14,
      marginTop: 8,
      textAlign: 'center'
    },
    categoriesGrid: { flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 8 },
    categoryChip: { paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
      borderWidth: 1 },
    categoryText: {
      fontSize: 12,
      fontWeight: '500'
    },
    summaryGrid: { flexDirection: 'row',
      gap: 16 },
    summaryItem: { flex: 1,
      alignItems: 'center',
      padding: 12 },
    summaryValue: {
      fontSize: 18,
      fontWeight: '700'
    },
    summaryLabel: {
      fontSize: 12),
      textAlign: 'center'),
      marginTop: 4)
    },
  })
export default PortfolioTab,