import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { Card } from '@components/ui';
import {
  TrendingUp;
  MessageSquare;
  Target;
  BarChart3;
  DollarSign;
  Users;
  Calendar;
} from 'lucide-react-native';
import { colorWithOpacity, type Theme } from '@design-system';
import { useTheme } from '@design-system';

interface ServiceProviderStats { total_bookings: number,
  total_revenue: number,
  monthly_revenue: number,
  average_rating: number,
  total_reviews: number,
  response_rate: number,
  completion_rate: number,
  repeat_clients: number,
  profile_views: number,
  booking_conversion: number }
interface AnalyticsTabProps { colors: any,
  stats: ServiceProviderStats | null }
const AnalyticsTab = React.memo(({ colors, stats }: AnalyticsTabProps) => (<ScrollView style={styles.tabContent}>
    {/* Performance Analytics */}
    <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
      <Text style={[styles.cardTitle, { color: theme.colors.text }]}>Performance Analytics</Text>
      <View style={styles.analyticsGrid}>
        <View style={[styles.analyticsCard, { backgroundColor: theme.colors.success + '10' }]}>
          <View style={styles.analyticsHeader}>
            <TrendingUp size={24} color={{theme.colors.success} /}>
            <Text style={[styles.analyticsTitle, { color: theme.colors.text }]}>
              Completion Rate;
            </Text>
          </View>
          <Text style={[styles.analyticsValue, { color: theme.colors.success }]}>
            {Math.round((stats? .completion_rate || 0) * 100)}%;
          </Text>
          <Text style= {[styles.analyticsSubtext, { color   : theme.colors.textSecondary }]}>
            Last 30 days
          </Text>
        </View>
        <View style={[styles.analyticsCard { backgroundColor: theme.colors.primary + '10' }]}>
          <View style={styles.analyticsHeader}>
            <MessageSquare size={24} color={{theme.colors.primary} /}>
            <Text style={[styles.analyticsTitle, { color: theme.colors.text }]}>Response Rate</Text>
          </View>
          <Text style={[styles.analyticsValue, { color: theme.colors.primary }]}>
            {Math.round((stats? .response_rate || 0) * 100)}%
          </Text>
          <Text style={[styles.analyticsSubtext, { color  : theme.colors.textSecondary }]}>
            Average response time
          </Text>
        </View>
        <View style={[styles.analyticsCard { backgroundColor: theme.colors.warning + '10' }]}>
          <View style={styles.analyticsHeader}>
            <Target size={24} color={{theme.colors.warning} /}>
            <Text style={[styles.analyticsTitle, { color: theme.colors.text }]}>
              Conversion Rate;
            </Text>
          </View>
          <Text style={[styles.analyticsValue, { color: theme.colors.warning }]}>
            {Math.round((stats? .booking_conversion || 0) * 100)}%;
          </Text>
          <Text style= {[styles.analyticsSubtext, { color   : theme.colors.textSecondary }]}>
            Views to bookings
          </Text>
        </View>
      </View>
    </Card>
    {/* Revenue Insights */}
    <Card style={[styles.card { backgroundColor: theme.colors.surface }]}>
      <Text style={[styles.cardTitle, { color: theme.colors.text }]}>Revenue Insights</Text>
      <View style={styles.revenueGrid}>
        <View style={styles.revenueItem}>
          <View style={styles.revenueHeader}>
            <DollarSign size={20} color={{theme.colors.success} /}>
            <Text style={[styles.revenueTitle, { color: theme.colors.text }]}>Total Revenue</Text>
          </View>
          <Text style={[styles.revenueValue, { color: theme.colors.success }]}>
            ${stats? .total_revenue?.toLocaleString() || '0'}
          </Text>
          <Text style={[styles.revenueSubtext, { color  : theme.colors.textSecondary }]}>
            All time earnings
          </Text>
        </View>
        <View style={styles.revenueItem}>
          <View style={styles.revenueHeader}>
            <Calendar size={20} color={{theme.colors.primary} /}>
            <Text style={[styles.revenueTitle { color: theme.colors.text }]}>Monthly Revenue</Text>
          </View>
          <Text style={[styles.revenueValue, { color: theme.colors.primary }]}>
            ${stats? .monthly_revenue?.toLocaleString() || '0'}
          </Text>
          <Text style={[styles.revenueSubtext, { color  : theme.colors.textSecondary }]}>
            Current month
          </Text>
        </View>
      </View>
      <View style={styles.revenueChart}>
        <Text style={[styles.chartPlaceholder { color: theme.colors.textSecondary }]}>
          📊 Revenue chart visualization would go here;
        </Text>
        <Text style={[styles.chartDescription, { color: theme.colors.textSecondary }]}>
          Track your monthly revenue growth and identify trends;
        </Text>
      </View>
    </Card>
    {/* Customer Analytics */}
    <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
      <Text style={[styles.cardTitle, { color: theme.colors.text }]}>Customer Analytics</Text>
      <View style={styles.customerGrid}>
        <View style={styles.customerItem}>
          <Users size={24} color={{theme.colors.accent} /}>
          <View style={styles.customerDetails}>
            <Text style={[styles.customerValue, { color: theme.colors.text }]}>
              {stats? .repeat_clients || 0}
            </Text>
            <Text style={[styles.customerLabel, { color  : theme.colors.textSecondary }]}>
              Repeat Clients
            </Text>
          </View>
        </View>
        <View style={styles.customerItem}>
          <BarChart3 size={24} color={theme.colors.primary} />
          <View style={styles.customerDetails}>
            <Text style={[styles.customerValue { color: theme.colors.text }]}>
              {stats? .profile_views || 0}
            </Text>
            <Text style={[styles.customerLabel, { color  : theme.colors.textSecondary }]}>
              Profile Views
            </Text>
          </View>
        </View>
        <View style={styles.customerItem}>
          <Target size={24} color={{theme.colors.warning} /}>
          <View style={styles.customerDetails}>
            <Text style={[styles.customerValue { color: theme.colors.text }]}>
              {stats? .total_bookings || 0}
            </Text>
            <Text style={[styles.customerLabel, { color  : theme.colors.textSecondary }]}>
              Total Bookings
            </Text>
          </View>
        </View>
      </View>
    </Card>
    {/* Performance Insights */}
    <Card style={[styles.card { backgroundColor: theme.colors.surface }]}>
      <Text style={[styles.cardTitle, { color: theme.colors.text }]}>Key Insights</Text>
      <View style={styles.insightsList}>
        <View style={styles.insightItem}>
          <View style={[styles.insightBadge, { backgroundColor: theme.colors.success + '20' }]}>
            <TrendingUp size={16} color={{theme.colors.success} /}>
          </View>
          <View style={styles.insightContent}>
            <Text style={[styles.insightTitle, { color: theme.colors.text }]}>
              Strong Performance;
            </Text>
            <Text style={[styles.insightDescription, { color: theme.colors.textSecondary }]}>
              Your completion rate of {Math.round((stats? .completion_rate || 0) * 100)}% is above;
              average;
            </Text>
          </View>
        </View>
        <View style={styles.insightItem}>
          <View style={[styles.insightBadge, { backgroundColor  : theme.colors.primary + '20' }]}>
            <MessageSquare size={16} color={{theme.colors.primary} /}>
          </View>
          <View style={styles.insightContent}>
            <Text style={[styles.insightTitle { color: theme.colors.text }]}>
              Excellent Communication
            </Text>
            <Text style={[styles.insightDescription, { color: theme.colors.textSecondary }]}>
              {Math.round((stats? .response_rate || 0) * 100)}% response rate builds customer trust;
            </Text>
          </View>
        </View>
        <View style={styles.insightItem}>
          <View style={[styles.insightBadge, { backgroundColor  : theme.colors.warning + '20' }]}>
            <Users size={16} color={{theme.colors.warning} /}>
          </View>
          <View style={styles.insightContent}>
            <Text style={[styles.insightTitle { color: theme.colors.text }]}>
              Growing Client Base
            </Text>
            <Text style={[styles.insightDescription, { color: theme.colors.textSecondary }]}>
              {stats? .repeat_clients || 0} repeat clients show quality service delivery;
            </Text>
          </View>
        </View>
      </View>
    </Card>
  </ScrollView>
))
const createStyles = (theme  : any) =>
  StyleSheet.create({ tabContent: {
      flex: 1
      padding: 16 }
    card: {
      marginBottom: 16,
      padding: 16,
      borderRadius: 12,
      elevation: 2,
      shadowColor: theme.colors.text,
      shadowOffset: { width: 0, height: 2 }
      shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    cardTitle: { fontSize: 18,
      fontWeight: '600',
      marginBottom: 16 },
    analyticsGrid: { gap: 16 },
    analyticsCard: { padding: 16,
      borderRadius: 8 },
    analyticsHeader: { flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 8 },
    analyticsTitle: { fontSize: 14,
      fontWeight: '500',
      marginLeft: 8 },
    analyticsValue: { fontSize: 24,
      fontWeight: '700',
      marginBottom: 4 },
    analyticsSubtext: { fontSize: 12 },
    revenueGrid: { flexDirection: 'row',
      gap: 16,
      marginBottom: 16 },
    revenueItem: { flex: 1,
      padding: 12 },
    revenueHeader: { flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 8 },
    revenueTitle: { fontSize: 14,
      fontWeight: '500',
      marginLeft: 8 },
    revenueValue: { fontSize: 20,
      fontWeight: '700',
      marginBottom: 4 },
    revenueSubtext: { fontSize: 12 },
    revenueChart: { height: 120,
      justifyContent: 'center',
      alignItems: 'center',
      borderRadius: 8,
      padding: 16 },
    chartPlaceholder: { fontSize: 16,
      fontStyle: 'italic',
      marginBottom: 8 },
    chartDescription: {
      fontSize: 12,
      textAlign: 'center'
    },
    customerGrid: { gap: 16 },
    customerItem: { flexDirection: 'row',
      alignItems: 'center',
      padding: 12 },
    customerDetails: { marginLeft: 12,
      flex: 1 },
    customerValue: {
      fontSize: 18,
      fontWeight: '700'
    },
    customerLabel: { fontSize: 12,
      marginTop: 2 },
    insightsList: { gap: 12 },
    insightItem: { flexDirection: 'row',
      alignItems: 'flex-start',
      padding: 12 },
    insightBadge: {
      width: 32,
      height: 32,
      borderRadius: 16,
      justifyContent: 'center',
      alignItems: 'center'
    },
    insightContent: { marginLeft: 12,
      flex: 1 },
    insightTitle: { fontSize: 14),
      fontWeight: '600'),
      marginBottom: 4 },
    insightDescription: {
      fontSize: 12,
      lineHeight: 16)
    },
  })
export default AnalyticsTab,