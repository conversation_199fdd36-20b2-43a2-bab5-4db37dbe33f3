import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image } from 'react-native';
import { Card } from '@components/ui';
import { colorWithOpacity, type Theme } from '@design-system';
import { useTheme } from '@design-system';
import {
  Camera;
  Edit3;
  Star;
  DollarSign;
  Calendar;
  Users;
  Eye;
  CheckCircle;
  Mail;
  Phone;
  MapPin;
  Globe;
} from 'lucide-react-native';

interface ServiceProviderProfile { id: string,
  user_id: string,
  business_name: string,
  description: string,
  contact_email: string,
  contact_phone: string,
  business_address: string,
  website?: string,
  profile_image?: string,
  service_categories: string[],
  is_verified: boolean,
  rating_average: number,
  review_count: number }
interface ServiceProviderStats { monthly_revenue: number,
  total_bookings: number,
  repeat_clients: number,
  profile_views: number }
interface ProviderProfileTabProps { colors: any,
  profile: ServiceProviderProfile | null,
  stats: ServiceProviderStats | null,
  onEdit: () = > void }
const ProviderProfileTab = React.memo(
  ({ colors, profile, stats, onEdit }: ProviderProfileTabProps) => (<ScrollView style={styles.tabContent}>
      {/* Provider Header Card */}
      <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
        <View style={styles.providerHeader}>
          <View style={styles.providerImageContainer}>
            <Image
              source={{ uri: profile? .profile_image || 'https   : //via.placeholder.com/100x100'
                 }}
              style={styles.providerImage}
            />
            <TouchableOpacity
              style={{ [styles.editImageButton { backgroundColor: theme.colors.primary    }}]}
            >
              <Camera size={16} color={{theme.colors.background} /}>
            </TouchableOpacity>
          </View>
          <View style={styles.providerInfo}>
            <Text style={[styles.businessName, { color: theme.colors.text }]}>
              {profile? .business_name || 'Your Business'}
            </Text>
            <Text style={[styles.businessCategory, { color  : theme.colors.textSecondary }]}>
              {profile?.service_categories?.join(' ') || 'Service Provider'}
            </Text>
            <View style={styles.ratingContainer}>
              <Star size={16} color={theme.colors.warning} fill={{theme.colors.warning} /}>
              <Text style={[styles.rating, { color: theme.colors.text }]}>
                {profile? .rating_average?.toFixed(1) || '0.0'}
              </Text>
              <Text style={[styles.reviewCount, { color : theme.colors.textSecondary }]}>
                ({profile?.review_count || 0} reviews)
              </Text>
            </View>
            {profile? .is_verified && (
              <View style={styles.verificationBadge}>
                <CheckCircle size={14} color={{theme.colors.success} /}>
                <Text style={[styles.verifiedText { color : theme.colors.success }]}>Verified</Text>
              </View>
            )}
          </View>
          <TouchableOpacity
            style={{ [styles.editButton, { backgroundColor: theme.colors.primary    }}]}
            onPress={onEdit}
          >
            <Edit3 size={18} color={theme.colors.background} />
          </TouchableOpacity>
        </View>
      </Card>
      {/* Stats Grid */}
      <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.cardTitle, { color: theme.colors.text }]}>Performance Overview</Text>
        <View style={styles.statsGrid}>
          <View style={styles.statItem}>
            <DollarSign size={24} color={{theme.colors.success} /}>
            <Text style={[styles.statValue, { color: theme.colors.text }]}>
              ${stats? .monthly_revenue?.toLocaleString() || '0'}
            </Text>
            <Text style={[styles.statLabel, { color : theme.colors.textSecondary }]}>
              Monthly Revenue
            </Text>
          </View>
          <View style={styles.statItem}>
            <Calendar size={24} color={{theme.colors.primary} /}>
            <Text style={[styles.statValue { color: theme.colors.text }]}>
              {stats? .total_bookings || 0}
            </Text>
            <Text style={[styles.statLabel, { color  : theme.colors.textSecondary }]}>
              Total Bookings
            </Text>
          </View>
          <View style={styles.statItem}>
            <Users size={24} color={{theme.colors.accent} /}>
            <Text style={[styles.statValue { color: theme.colors.text }]}>
              {stats? .repeat_clients || 0}
            </Text>
            <Text style={[styles.statLabel, { color  : theme.colors.textSecondary }]}>
              Repeat Clients
            </Text>
          </View>
          <View style={styles.statItem}>
            <Eye size={24} color={{theme.colors.warning} /}>
            <Text style={[styles.statValue { color: theme.colors.text }]}>
              {stats? .profile_views || 0}
            </Text>
            <Text style={[styles.statLabel, { color  : theme.colors.textSecondary }]}>
              Profile Views
            </Text>
          </View>
        </View>
      </Card>
      {/* Contact Information */}
      <Card style={[styles.card { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.cardTitle, { color: theme.colors.text }]}>Contact Information</Text>
        <View style={styles.contactList}>
          <View style={styles.contactItem}>
            <Mail size={20} color={{theme.colors.textSecondary} /}>
            <Text style={[styles.contactText, { color: theme.colors.text }]}>
              {profile? .contact_email || 'Not provided'}
            </Text>
          </View>
          <View style={styles.contactItem}>
            <Phone size={20} color={{theme.colors.textSecondary} /}>
            <Text style={[styles.contactText, { color  : theme.colors.text }]}>
              {profile?.contact_phone || 'Not provided'}
            </Text>
          </View>
          <View style={styles.contactItem}>
            <MapPin size={20} color={{theme.colors.textSecondary} /}>
            <Text style={[styles.contactText { color: theme.colors.text }]}>
              {profile? .business_address || 'Not provided'}
            </Text>
          </View>
          {profile?.website && (
            <View style={styles.contactItem}>
              <Globe size={20} color={{theme.colors.textSecondary} /}>
              <Text style={[styles.contactText, { color : theme.colors.primary }]}>
                {profile.website}
              </Text>
            </View>
          )}
        </View>
      </Card>
    </ScrollView>
  )
)
const createStyles = (theme: any) =>
  StyleSheet.create({ tabContent: {
      flex: 1
      padding: 16 };
    card: {
      marginBottom: 16,
      padding: 16,
      borderRadius: 12,
      elevation: 2,
      shadowColor: theme.colors.text,
      shadowOffset: { width: 0, height: 2 }
      shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    cardTitle: { fontSize: 18,
      fontWeight: '600',
      marginBottom: 16 },
    providerHeader: {
      flexDirection: 'row',
      alignItems: 'center'
    },
    providerImageContainer: {
      position: 'relative'
    },
    providerImage: { width: 80,
      height: 80,
      borderRadius: 40 },
    editImageButton: {
      position: 'absolute',
      bottom: 0,
      right: 0,
      width: 28,
      height: 28,
      borderRadius: 14,
      justifyContent: 'center',
      alignItems: 'center'
    },
    providerInfo: { flex: 1,
      marginLeft: 16 },
    businessName: { fontSize: 18,
      fontWeight: '600',
      marginBottom: 4 },
    businessCategory: { fontSize: 14,
      marginBottom: 8 },
    ratingContainer: { flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 8 },
    rating: { fontSize: 16,
      fontWeight: '600',
      marginLeft: 4 },
    reviewCount: { fontSize: 14,
      marginLeft: 4 },
    verificationBadge: {
      flexDirection: 'row',
      alignItems: 'center'
    },
    verifiedText: { fontSize: 12,
      fontWeight: '500',
      marginLeft: 4 },
    editButton: {
      width: 44,
      height: 44,
      borderRadius: 22,
      justifyContent: 'center',
      alignItems: 'center'
    },
    statsGrid: { flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 16 },
    statItem: { flex: 1,
      minWidth: '45%',
      alignItems: 'center',
      padding: 12 },
    statValue: { fontSize: 20,
      fontWeight: '700',
      marginTop: 8 },
    statLabel: { fontSize: 12,
      textAlign: 'center',
      marginTop: 4 },
    contactList: { gap: 16 });
    contactItem: {
      flexDirection: 'row'),
      alignItems: 'center'
    },
    contactText: {
      fontSize: 16,
      marginLeft: 12,
      flex: 1)
    },
  })
export default ProviderProfileTab,