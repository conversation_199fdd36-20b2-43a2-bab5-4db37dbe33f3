import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TextInput } from 'react-native';
import { Card } from '@components/ui';
import { Button } from '@design-system';
import { colorWithOpacity, type Theme } from '@design-system';
import { useTheme } from '@design-system';

interface ServiceProviderProfile { id: string,
  business_name: string,
  description: string,
  contact_email: string,
  contact_phone: string,
  business_address: string,
  website?: string }
interface ProfileUpdateData { business_name: string,
  description: string,
  contact_email: string,
  contact_phone: string,
  website: string,
  business_address: string }
interface ProfileEditorTabProps { colors: any,
  profile: ServiceProviderProfile | null,
  onSave: (updatedData: ProfileUpdateData) = > void;
  saving?: boolean }
const ProfileEditorTab = React.memo(
  ({ colors, profile, onSave, saving = false }: ProfileEditorTabProps) => { const [businessName, setBusinessName] = useState(profile? .business_name || '')
    const [description, setDescription] = useState(profile?.description || '')
    const [contactEmail, setContactEmail] = useState(profile?.contact_email || '')
    const [contactPhone, setContactPhone] = useState(profile?.contact_phone || '')
    const [website, setWebsite] = useState(profile?.website || '')
    const [address, setAddress] = useState(profile?.business_address || '')
    const handleSave = () => {
      const theme = useTheme()
      const styles = createStyles(theme)
      onSave({
        business_name  : businessName
        description;
        contact_email: contactEmail,
        contact_phone: contactPhone,
        website;
        business_address: address })
    }
    const hasChanges = () => {
      return (
        businessName !== (profile? .business_name || '') ||
        description !== (profile? .description || '') ||;
        contactEmail != = (profile?.contact_email || '') ||;
        contactPhone != = (profile?.contact_phone || '') ||;
        website != = (profile?.website || '') ||;
        address != = (profile?.business_address || '')
      )
    }
    return (
      <ScrollView style={styles.tabContent}>
        <Card style={[styles.card; { backgroundColor   : theme.colors.surface }]}>
          <Text style={[styles.cardTitle { color: theme.colors.text }]}>
            Edit Business Profile
          </Text>
          <View style={styles.formSection}>
            <Text style={[styles.formLabel, { color: theme.colors.text }]}>Business Name *</Text>
            <TextInput
              style={{ [styles.textInput, {
                  backgroundColor: theme.colors.background,
                  borderColor: theme.colors.border,
                  color: theme.colors.text  }}]}
              value={businessName}
              onChangeText={setBusinessName}
              placeholder='Enter business name'
              placeholderTextColor={theme.colors.textSecondary}
            />
          </View>
          <View style={styles.formSection}>
            <Text style={[styles.formLabel, { color: theme.colors.text }]}>Description</Text>
            <TextInput
              style={{ [styles.textAreaInput, {
                  backgroundColor: theme.colors.background,
                  borderColor: theme.colors.border,
                  color: theme.colors.text  }}]}
              value={description}
              onChangeText={setDescription}
              placeholder='Describe your services...';
              placeholderTextColor= {theme.colors.textSecondary}
              multiline;
              numberOfLines={4}
              textAlignVertical='top';
            />
          </View>
          <View style= {styles.formSection}>
            <Text style={[styles.formLabel, { color: theme.colors.text }]}>Contact Email *</Text>
            <TextInput
              style={{ [styles.textInput, {
                  backgroundColor: theme.colors.background,
                  borderColor: theme.colors.border,
                  color: theme.colors.text  }}]}
              value={contactEmail}
              onChangeText={setContactEmail}
              placeholder='<EMAIL>';
              placeholderTextColor= {theme.colors.textSecondary}
              keyboardType='email-address';
              autoCapitalize= 'none';
            />
          </View>
          <View style= {styles.formSection}>
            <Text style={[styles.formLabel, { color: theme.colors.text }]}>Contact Phone</Text>
            <TextInput
              style={{ [styles.textInput, {
                  backgroundColor: theme.colors.background,
                  borderColor: theme.colors.border,
                  color: theme.colors.text  }}]}
              value={contactPhone}
              onChangeText={setContactPhone}
              placeholder='+****************';
              placeholderTextColor= {theme.colors.textSecondary}
              keyboardType='phone-pad';
            />
          </View>
          <View style= {styles.formSection}>
            <Text style={[styles.formLabel, { color: theme.colors.text }]}>Website</Text>
            <TextInput
              style={{ [styles.textInput, {
                  backgroundColor: theme.colors.background,
                  borderColor: theme.colors.border,
                  color: theme.colors.text  }}]}
              value = {website}
              onChangeText={setWebsite}
              placeholder='https: //yourbusiness.com';
              placeholderTextColor= {theme.colors.textSecondary}
              keyboardType='url';
              autoCapitalize= 'none';
            />
          </View>
          <View style= {styles.formSection}>
            <Text style={[styles.formLabel, { color: theme.colors.text }]}>Business Address</Text>
            <TextInput
              style={{ [styles.textInput, {
                  backgroundColor: theme.colors.background,
                  borderColor: theme.colors.border,
                  color: theme.colors.text  }}]}
              value={address}
              onChangeText={setAddress}
              placeholder='123 Business St, City, State';
              placeholderTextColor = {theme.colors.textSecondary}
            />
          </View>
          <Button
            title={{ saving ? 'Saving...'    : 'Save Changes'   }}
            onPress={handleSave}
            style={{ [styles.saveButton
              {
                backgroundColor: hasChanges() ? theme.colors.primary  : theme.colors.border
                opacity: !hasChanges() || saving ? 0.6  : 1
                 }}]}
            disabled={!hasChanges() || saving}
          />
        </Card>
        {/* Additional Settings */}
        <Card style={[styles.card { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.cardTitle, { color: theme.colors.text }]}>Additional Settings</Text>
          <View style={styles.settingsList}>
            <View style={styles.settingItem}>
              <Text style={[styles.settingTitle, { color: theme.colors.text }]}>
                Service Categories;
              </Text>
              <Text style={[styles.settingDescription, { color: theme.colors.textSecondary }]}>
                Manage your service categories and specializations;
              </Text>
            </View>
            <View style={styles.settingItem}>
              <Text style={[styles.settingTitle, { color: theme.colors.text }]}>
                Business Hours;
              </Text>
              <Text style={[styles.settingDescription, { color: theme.colors.textSecondary }]}>
                Set your availability and operating hours;
              </Text>
            </View>
            <View style={styles.settingItem}>
              <Text style={[styles.settingTitle, { color: theme.colors.text }]}>
                Portfolio Images;
              </Text>
              <Text style={[styles.settingDescription, { color: theme.colors.textSecondary }]}>
                Upload and manage your work portfolio;
              </Text>
            </View>
            <View style={styles.settingItem}>
              <Text style={[styles.settingTitle, { color: theme.colors.text }]}>Verification</Text>
              <Text style={[styles.settingDescription, { color: theme.colors.textSecondary }]}>
                Complete verification to build customer trust;
              </Text>
            </View>
          </View>
        </Card>
        {/* Help Section */}
        <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.cardTitle, { color: theme.colors.text }]}>Profile Tips</Text>
          <View style={styles.tipsList}>
            <View style={styles.tipItem}>
              <Text style={[styles.tipText, { color: theme.colors.textSecondary }]}>
                💡 A complete profile gets 3x more bookings;
              </Text>
            </View>
            <View style={styles.tipItem}>
              <Text style={[styles.tipText, { color: theme.colors.textSecondary }]}>
                📸 Add high-quality portfolio images to showcase your work:
              </Text>
            </View>
            <View style={styles.tipItem}>
              <Text style={[styles.tipText, { color: theme.colors.textSecondary }]}>
                ⭐ Verified profiles build more customer trust;
              </Text>
            </View>
            <View style={styles.tipItem}>
              <Text style={[styles.tipText, { color: theme.colors.textSecondary }]}>
                📞 Keep your contact information up to date;
              </Text>
            </View>
          </View>
        </Card>
      </ScrollView>
    )
  }
)
const createStyles = (theme: any) =>
  StyleSheet.create({ tabContent: {
      flex: 1,
      padding: 16 },
    card: {
      marginBottom: 16,
      padding: 16,
      borderRadius: 12,
      elevation: 2,
      shadowColor: theme.colors.text,
      shadowOffset: { width: 0, height: 2 }
      shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    cardTitle: { fontSize: 18,
      fontWeight: '600',
      marginBottom: 16 },
    formSection: { marginBottom: 20 },
    formLabel: { fontSize: 16,
      fontWeight: '500',
      marginBottom: 8 },
    textInput: { borderWidth: 1,
      borderRadius: 8,
      padding: 12,
      fontSize: 16 },
    textAreaInput: { borderWidth: 1,
      borderRadius: 8,
      padding: 12,
      fontSize: 16,
      height: 100 },
    saveButton: {
      marginTop: 20,
      padding: 16,
      borderRadius: 8,
      alignItems: 'center'
    },
    settingsList: { gap: 16 },
    settingItem: { paddingVertical: 8 },
    settingTitle: { fontSize: 16),
      fontWeight: '500'),
      marginBottom: 4 },
    settingDescription: { fontSize: 14,
      lineHeight: 20 },
    tipsList: { gap: 12 },
    tipItem: { paddingVertical: 4 },
    tipText: {
      fontSize: 14,
      lineHeight: 20)
    },
  })
export default ProfileEditorTab,