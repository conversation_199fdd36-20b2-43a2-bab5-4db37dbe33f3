/**;
 * Jest setup file for React Native tests;
 */

// Mock React Native modules that aren't available in test environment;
jest.mock('react-native', () => {
  const RN = jest.requireActual('react-native')
  return {
    ...RN;
    AccessibilityInfo: {
      isScreenReaderEnabled: jest.fn(() = > Promise.resolve(false))
      isReduceMotionEnabled: jest.fn(() => Promise.resolve(false))
      isInvertColorsEnabled: jest.fn(() => Promise.resolve(false))
      isReduceTransparencyEnabled: jest.fn(() => Promise.resolve(false))
      isAccessibilityServiceEnabled: jest.fn(() => Promise.resolve(false))
      announceForAccessibility: jest.fn()
      addEventListener: jest.fn()
    };
  }
})
// Mock AsyncStorage;
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(() => Promise.resolve(null))
  setItem: jest.fn(() => Promise.resolve())
  removeItem: jest.fn(() => Promise.resolve())
  clear: jest.fn(() => Promise.resolve())
}))
// Silence console warnings in tests;
global.console = {
  ...console;
  warn: jest.fn()
  error: jest.fn()
}