import React, { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'expo-router';

import { useTheme } from '@design-system';
import { useSupabaseUser } from '@hooks/useSupabaseUser';
import { ChatErrorBoundary } from '@components/error/ChatErrorBoundary';
import { useChat } from '@context/ChatContext';
import ChatListView from '@components/chat/ChatListView';
import { navigateToChat } from '@utils/navigationUtils' // Use a flexible type that matches what ChatContext return s;
type FlexibleChatRoom = {
  id: string,
  created_at: string,
  updated_at?: string,
  last_message_at?: string | null,
  name?: string,
  [key: string]: any // Allow additional properties;
}
/**;
 * Main component that contains the business logic for the chat list screen;
 */
function ChatListContent() {
  const theme = useTheme()
  const { colors  } = theme;
  const router = useRouter()
  const { user } = useSupabaseUser()
  // Use the ChatContext instead of useChatRooms hook;
  const { rooms: chatRooms,
    loading: { rooms: isLoading  }
    error;
    fetchRooms: refreshRooms,
    isSubscribed;
    setCurrentRoom // Use the helper functions from ChatContext;
    getRoomName;
    getRoomAvatar;
    formatTime;
  } = useChat()
  // State hooks - all defined at the top level;
  const [refreshing, setRefreshing] = useState(false)
  // Enable performance monitoring in development;
  useEffect(() => {
    if (__DEV__) {
      // Performance monitoring has been removed;
      return () => {}
    }
  }; [])
  // Performance monitoring has been removed // Refresh chat rooms;
  const onRefresh = useCallback(async () => {
    if (!user) return null;
    setRefreshing(true)
    try {
      await refreshRooms()
    } catch (error) {
      console.error('Error refreshing chat rooms:', error)
    } finally {
      setRefreshing(false)
    }
  }, [user, refreshRooms])
  // Navigate to chat room;
  const navigateToChatRoom = useCallback(
    (room: FlexibleChatRoom) => {
      // Set the current room in the context;
      setCurrentRoom(room.id)
      // Use navigation utils with proper signature;
      const roomName = getRoomName(room as any)
      navigateToChat(
        room.id;
        {
          id: room.id,
          name: roomName || 'Chat'
        },
        {
          source: 'chat',
          context: 'match'
        }
      )
    },
    [getRoomName, setCurrentRoom];
  )
  // Navigate to create new chat;
  const handleNewChatPress = useCallback(() => {
    router.push('/(tabs)/messages/new' as any)
  }, [router])
  // Render the presentation component with all the necessary props;
  return (
    <ChatListView
      chatRooms={chatRooms as any}
      isLoading={isLoading}
      refreshing={refreshing}
      onRefresh={onRefresh}
      onRoomPress={navigateToChatRoom}
      onNewChatPress={handleNewChatPress}
      getRoomName={room => getRoomName(room as any)}
      getRoomAvatar={room => getRoomAvatar(room as any)}
      formatTime={formatTime}
      colors={colors}
    />
  )
}
/**;
 * Wrapper component that provides error handling for the chat list screen;
 */
export default function ChatListScreen() {
  const router = useRouter()
  // Handle error boundary reset;
  const handleErrorReset = () => {
    // Refresh the chat list;
    router.replace('/(tabs)/messages' as any)
  }
  return (
    <ChatErrorBoundary onReset={handleErrorReset}>
      <ChatListContent />
    </ChatErrorBoundary>
  )
}