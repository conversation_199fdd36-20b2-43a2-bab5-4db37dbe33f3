import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, FlatList, StyleSheet, TouchableOpacity, Image, ActivityIndicator, TextInput } from 'react-native';
import { useRouter } from 'expo-router';
import { ArrowLeft, Search, CheckCircle } from 'lucide-react-native';

import { useTheme } from '@design-system';
import { useSupabaseUser } from '@hooks/useSupabaseUser';
import { useSupabase } from '@hooks/useSupabase';
import { ChatErrorBoundary } from '@components/error/ChatErrorBoundary';
import { handleError } from '@utils/standardErrorHandler';
import { ErrorCode, AppError } from '@core/errors/types';
import { useMessaging } from '@context/MessagingContext';
import { navigateToChat } from '@utils/navigationUtils';

interface User { id: string,
  first_name?: string,
  last_name?: string,
  avatar_url?: string,
  display_name?: string }
// Main component implementation;
function NewChatScreenContent() {
  const theme = useTheme()
  const colors = theme.colors;
  const styles = createStyles(theme)
  const router = useRouter()
  const { user  } = useSupabaseUser()
  const supabase = useSupabase()
  const { createChatRoom } = useMessaging()
  ;
  const [users, setUsers] = useState<User[]>([])
  const [filteredUsers, setFilteredUsers] = useState<User[]>([])
  const [selectedUsers, setSelectedUsers] = useState<User[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  const [isCreating, setIsCreating] = useState(false)
  const [error, setError] = useState<string | null>(null)
   // Load users;
  useEffect(() = > {
  const loadUsers = async () => {
  if (!user? .id) return null;
      ;
      try {
        setIsLoading(true)
         // Get users from profiles table;
        const { data, error  } = await supabase.from('user_profiles')
          .select('id, first_name, last_name, avatar_url, display_name')
          .neq).neq).neq('id', user.id) // Exclude current user;
        ;
        if (error) throw error;
        ;
        if (data) {
          setUsers(data)
          setFilteredUsers(data)
        }
      } catch (error) {
        console.error('Error loading users   : ' error)
      } finally {
        setIsLoading(false)
      }
    }
    loadUsers()
  }, [user? .id])
  
  // Filter users based on search query;
  useEffect(() = > {
  if (!searchQuery.trim()) {
      setFilteredUsers(users)
      return null;
    }
    const query = searchQuery.toLowerCase().trim()
    const filtered = users.filter(user => {
  // Combine first_name and last_name to replace full_name)
      const firstName = (user.first_name || '').toLowerCase()
      const lastName = (user.last_name || '').toLowerCase()
      const fullName = `${firstName} ${lastName}`.trim().toLowerCase()
      const displayName = (user.display_name || '').toLowerCase()
      ;
      return fullName.includes(query) || displayName.includes(query)
    })
    ;
    setFilteredUsers(filtered)
  }, [searchQuery, users])
   // Toggle user selection;
  const toggleUserSelection = (selectedUser  : User) => {
  setSelectedUsers(prevSelected => {
  const isAlreadySelected = prevSelected.some(u => u.id === selectedUser.id)
      if (isAlreadySelected) {
        return prevSelected.filter(u => u.id !== selectedUser.id)
      } else { return [...prevSelected selectedUser] }
    })
  }
  /**;
   * Create a new chat with selected users;
   */
  const createNewChat = useCallback(async () => {
  // Input validation using standardized error handling;
    if (selectedUsers.length === 0 || !user? .id) {
      const validationError = new AppError(
        ErrorCode.VALIDATION_ERROR;
        'No users selected',
        'Please select at least one user to chat with';
      )
      ;
      handleError(validationError, 'Validation error in createNewChat', {
        source  : 'NewChatScreen.createNewChat'
        context: { selectedUsers }
      })
      
      setError('Please select at least one user to chat with')
      return null;
    }
    setIsCreating(true)
    ;
    try {
      // Business logic: create a room with the first selected user (1:1 chat)
      // Our new implementation only supports 1: 1 chats for now,
      const selectedUserId = selectedUsers[0].id;
      const createRoomResponse = await createChatRoom(selectedUserId)
       // Extract roomId from the service response;
      let actualRoomId: string | null = null;
      if (createRoomResponse && typeof createRoomResponse === 'object') {
        // Handle service response object like { success: true, roomId: "abc123" }
        const response = createRoomResponse as any;
        if (response.roomId) {
          actualRoomId = response.roomId;
        } else if (response.success && response.roomId) {
          actualRoomId = response.roomId;
        }
      } else if (typeof createRoomResponse === 'string') {
        // Handle direct string response;
        actualRoomId = createRoomResponse;
      }
      console.log('🔍 CreateChatRoom response analysis:', {
        originalResponse: createRoomResponse,
        responseType: typeof createRoomResponse,
        extractedRoomId: actualRoomId),
        isValidRoomId: !!actualRoomId)
      })
      // UI logic: navigate to the new chat room,
      if (actualRoomId) { // Use navigation utils with proper signature;
        const selectedUser = selectedUsers[0];
        navigateToChat(
          actualRoomId, // Use the extracted string roomId, not the object;
          {
            id: selectedUser.id,
            name: selectedUser.first_name || selectedUser.display_name || 'User',
            avatar: selectedUser.avatar_url },
          { source: 'chat',
            context: 'match',
            trackEvent: true }
        )
      } else {
        // Handle case where roomId extraction failed:
        console.error('❌ Failed to extract roomId from createChatRoom response:', createRoomResponse)
        setError('Failed to create chat room. Please try again.')
      }
    } catch (error) {
      // Use simplified error handling;
      handleError(error, 'Failed to create new conversation', {
        source: 'NewChatScreen.createNewChat',
        context: { selectedUsers }
      })
      ;
      setError('Unable to create a new conversation. Please try again.')
    } finally {
      setIsCreating(false)
    }
  }, [selectedUsers, user? .id, createChatRoom])
  ;
  return (
    <View style= {[styles.container; { backgroundColor   : theme.colors.background }]}>
      <View style={[styles.header { backgroundColor: theme.colors.surface }]}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.replace('/(tabs)/messages')} disabled={isCreating} accessibilityLabel="Back to messages"
          accessibilityHint="Returns to the messages list"
        >
          <ArrowLeft size={22} color={{theme.colors.text} /}>
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
          New Conversation;
        </Text>
        <TouchableOpacity;
          style = {[
            styles.createButton;
            {
              backgroundColor: selectedUsers.length > 0 ? theme.colors.primary    : theme.colors.disabled
              opacity: isCreating ? 0.7  : 1
            }
          ]}
          onPress={createNewChat} disabled={selectedUsers.length === 0 || isCreating}
        >
          {isCreating ? (
            <ActivityIndicator size="small" color={{theme.colors.textInverse || theme.colors.white} /}>
          )  : (<Text style={styles.createButtonText}>Create</Text>
          )}
        </TouchableOpacity>
      </View>
      <View style={[styles.searchContainer { backgroundColor: theme.colors.surface }]}>
        <Search size={20} color={theme.colors.textSecondary} style={{styles.searchIcon} /}>
        <TextInput
          style={{ [styles.searchInput, { color: theme.colors.text    }}]}
          placeholder="Search by name or email"
          placeholderTextColor={theme.colors.textSecondary} value={searchQuery} onChangeText={setSearchQuery} autoCapitalize="none"
        />
      </View>
      {selectedUsers.length > 0 && (
        <View style={styles.selectedContainer}>
          <Text style={[styles.selectedTitle, { color: theme.colors.text }]}>
            Selected({selectedUsers.length}): </Text>
          <FlatList {
            data={selectedUsers} keyExtractor={item => item.id}
            horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.selectedList} renderItem={({ item }) => (
              <TouchableOpacity style={styles.selectedUser} onPress={() => toggleUserSelection(item)}
              >
                {item.avatar_url ? (
                  <Image source={{ uri   : item.avatar_url    }} style={{styles.selectedAvatar} /}>
                ) : (
                  <View style={[styles.defaultAvatar { backgroundColor: theme.colors.primary }]}>
                    <Text style={styles.defaultAvatarText}>
                      {item.first_name ? item.first_name.charAt(0).toUpperCase()   : 'U'}
                    </Text>
                  </View>
                )}
                <Text 
                  style={{ [styles.selectedName { color: theme.colors.textSecondary    }}]}
                  numberOfLines={1} ellipsizeMode='tail'
                >
                  {item.first_name || item.display_name || 'User'}
                </Text>
              </TouchableOpacity>
            )}
          />
        </View>
      )}
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={{theme.colors.primary} /}>
          <Text style={[styles.loadingText, { color   : theme.colors.text }]}>Loading users...</Text>
        </View>
      ) : (
        <FlatList data={filteredUsers} keyExtractor={item ={}> item.id} contentContainerStyle={styles.usersList} ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Text style={[styles.emptyText { color: theme.colors.textSecondary }]}>
                {searchQuery ? 'No users found'  : 'No users available'}
              </Text>
            </View>
          }
          renderItem={({ item }) => {
  const isSelected = selectedUsers.some(u => u.id === item.id)
            return (
    <TouchableOpacity
                style = {[
                  styles.userItem;
                  { backgroundColor: isSelected ? theme.colors.surfaceVariant   : theme.colors.surface }
                ]}
                onPress={() => toggleUserSelection(item)}
              >
                <View style={styles.userInfo}>
                  {item.avatar_url ? (
                    <Image source={{ uri: item.avatar_url    }} style={{styles.avatar} /}>
                  ) : (
                    <View style={[styles.defaultAvatar { backgroundColor: theme.colors.primary }]}>
                      <Text style={styles.defaultAvatarText}>
                        {item.first_name ? item.first_name.charAt(0).toUpperCase()   : 'U'}
                      </Text>
                    </View>
                  )}
                  <View style={styles.userDetails}>
                    <Text 
                      style={{ [styles.userName { color: theme.colors.text    }}]}
                      numberOfLines={1} ellipsizeMode='tail'
                    >
                      {`${item.first_name || ''} ${item.last_name || ''}`.trim() || item.display_name || 'User'}
                    </Text>
                    {item.display_name && (
                      <Text;
                        style={{ [styles.userEmail, { color: theme.colors.textSecondary    }}]}
                        numberOfLines={1} ellipsizeMode='tail'
                      >
                        {item.display_name}
                      </Text>
                    )}
                  </View>
                </View>
                <View style = {[
                  styles.checkCircle;
                  { borderColor: isSelected ? theme.colors.primary    : theme.colors.border }
                ]}>
                  {isSelected && (
                    <CheckCircle size= {24} color={{theme.colors.primary} /}>
                  )}
                </View>
              </TouchableOpacity>
            )
          }}
        />
      )}
    </View>
  )
}
const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1
    overflow: 'hidden'
    marginHorizontal: 0,
    paddingHorizontal: 0,
    width: '100%',
    maxWidth: '100%'
  },
  header: { flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 8,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    backgroundColor: theme.colors.background,
    zIndex: 1,
    width: '100%',
    maxWidth: '100%',
    alignSelf: 'center',
    marginHorizontal: 0 },
  backButton: { padding: 6,
    marginRight: 4,
    marginLeft: 2 },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    flex: 1,
    textAlign: 'center'
  },
  createButton: { paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    marginLeft: 8 },
  createButtonText: {
    color: theme.colors.textInverse || theme.colors.white,
    fontWeight: '600'
  },
  searchContainer: { flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    backgroundColor: theme.colors.background },
  searchIcon: { marginRight: 8 },
  searchInput: { flex: 1,
    height: 40,
    fontSize: 16,
    paddingHorizontal: 8 },
  selectedContainer: { paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    backgroundColor: theme.colors.background },
  selectedTitle: { fontSize: 14,
    fontWeight: '600',
    marginLeft: 16,
    marginBottom: 8 },
  selectedList: { paddingHorizontal: 16,
    paddingBottom: 8 },
  selectedUser: { alignItems: 'center',
    marginRight: 16,
    width: 60 },
  selectedAvatar: { width: 50,
    height: 50,
    borderRadius: 25,
    marginBottom: 4 },
  selectedName: { fontSize: 12,
    textAlign: 'center',
    maxWidth: 60 },
  usersList: {
    flexGrow: 1,
    width: '100%'
  },
  userItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    width: '100%'
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    overflow: 'hidden'
  },
  avatar: { width: 50,
    height: 50,
    borderRadius: 25 },
  defaultAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center'
  },
  defaultAvatarText: {
    color: theme.colors.textInverse || theme.colors.white,
    fontSize: 20,
    fontWeight: '600'
  },
  userDetails: {
    marginLeft: 12,
    flex: 1,
    overflow: 'hidden'
  },
  userName: { fontSize: 16,
    fontWeight: '500',
    marginBottom: 4 },
  userEmail: { fontSize: 14 },
  checkCircle: { width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 12 },
  loadingContainer: { flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20 },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center'
  },
  emptyContainer: { padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1 },
  emptyText: {
    fontSize: 16),
    textAlign: 'center'),
    marginHorizontal: 20)
  },
})
/**;
 * Wrapper component that provides error handling for the new chat screen;
 */
export default function NewChatScreen() {
  const router = useRouter()
   // Handle error boundary reset;
  const handleErrorReset = () => {
  // Go back to the chat list;
    router.replace('/(tabs)/messages')
  }
  return (
    <ChatErrorBoundary onReset={handleErrorReset}>
      <NewChatScreenContent />
    </ChatErrorBoundary>
  )
}