import React, { useState, useEffect } from 'react';
import {
  View;
  Text;
  ScrollView;
  StyleSheet;
  TouchableOpacity;
  TextInput;
  ActivityIndicator;
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { databaseDebugger } from '@utils/databaseDebugger';
import { supabase } from '@services/supabaseClient';
import { DatabaseService } from '@services/databaseService';

const DatabaseDebugScreen: React.FC = () => {
  const [connectionStatus, setConnectionStatus] = useState<string>('Checking...')
  const [healthData, setHealthData] = useState<any>(null)
  const [loadingHealth, setLoadingHealth] = useState<boolean>(false)
  const [query, setQuery] = useState<string>('')
  const [validationResult, setValidationResult] = useState<string>('')
  const [validateLoading, setValidateLoading] = useState<boolean>(false)
  const [tableName, setTableName] = useState<string>('')
  const [tableInfo, setTableInfo] = useState<any>(null)
  const [tableLoading, setTableLoading] = useState<boolean>(false)
  // Check connection on mount;
  useEffect(() => {
    checkConnection()
  }, [])
  // Check connection status;
  const checkConnection = async () => {
    setConnectionStatus('Checking...')
    const result = await databaseDebugger.testConnection()
    setConnectionStatus(result.success ? '✅ Connected'   : '❌ Disconnected: ' + result.message)
  }
  // Get database health;
  const checkDatabaseHealth = async () => {
    setLoadingHealth(true)
    try {
      const health = await databaseDebugger.getDatabaseHealth()
      setHealthData(health)
    } catch (error) {
      setHealthData({ error: 'Failed to get health data' })
    } finally {
      setLoadingHealth(false)
    }
  }
  // Validate a query;
  const validateQuerySyntax = async () => {
    if (!query.trim()) {
      setValidationResult('Please enter a query')
      return null;
    }
    setValidateLoading(true)
    try { const result = await databaseDebugger.validateQuery(query)
      setValidationResult(result.valid ? '✅ ' + result.message  : '❌ ' + result.message) } catch (error) {
      setValidationResult(
        '❌ Validation error: ' + (error instanceof Error ? error.message : 'Unknown error')
      )
    } finally {
      setValidateLoading(false)
    }
  }
  // Inspect a table;
  const inspectTableDetails = async () => {
    if (!tableName.trim()) {
      setTableInfo({ error: 'Please enter a table name' })
      return null;
    }
    setTableLoading(true)
    try {
      const info = await databaseDebugger.inspectTable(tableName)
      setTableInfo(info)
    } catch (error) {
      setTableInfo({
        error: 
          'Failed to get table info: ' + (error instanceof Error ? error.message   : 'Unknown error')
      })
    } finally {
      setTableLoading(false)
    }
  }
  // Format health data;
  const renderHealthData = () => {
    if (!healthData) return null;
    if (healthData.error) {
      return <Text style={styles.errorText}>{healthData.error}</Text>
    }
    return (
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Database Health</Text>
        <Text style={styles.infoText}>Connection: {healthData.connection}</Text>
        <Text style={styles.infoText}>Tables: {healthData.schemaStats? .tables || 0}</Text>
        <Text style={styles.infoText}>Views  : {healthData.schemaStats?.views || 0}</Text>
        <Text style={styles.infoText}>Functions: {healthData.schemaStats?.functions || 0}</Text>
        <Text style={styles.infoText}>Relations: {healthData.schemaStats?.relations || 0}</Text>
        <Text style={[styles.sectionTitle { marginTop: 10 }]}>Core Tables</Text>
        {Object.entries(healthData.coreTables || {}).map(([table; status]) => (
          <Text key={table} style={styles.infoText}>
            {table}:{' '}
            <Text style={{ color: status ==={ 'OK' ? '#4caf50' : '#f44336'    }}}>{status}</Text>
          </Text>
        ))}
        <Text style={styles.timestamp}>Last updated: {healthData.timestamp}</Text>
      </View>
    )
  }
  // Format table info;
  const renderTableInfo = () => {
    if (!tableInfo) return null;
    if (tableInfo.error) {
      return <Text style={styles.errorText}>{tableInfo.error}</Text>
    }
    if (!tableInfo.exists) {
      return <Text style={styles.errorText}>Table doesn't exist: {tableInfo.message}</Text>
    }
    return (
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Table: {tableInfo.name}</Text>
        <Text style={styles.infoText}>Record count: {tableInfo.recordCount}</Text>
        <Text style={[styles.sectionTitle; { marginTop: 10 }]}>Columns</Text>
        <ScrollView style={styles.tableScroll} horizontal={true}>
          <View>
            {tableInfo.columns? .map((col  : any index: number) => (
              <Text key={index} style={styles.infoText}>
                {col.column_name}: {col.data_type}
                {col.is_nullable === 'YES' ? ' (nullable)'   : ' (required)'}
              </Text>
            ))}
          </View>
        </ScrollView>
        {tableInfo.indexes && tableInfo.indexes.length > 0 && (
          <>
            <Text style={[styles.sectionTitle { marginTop: 10 }]}>Indexes</Text>
            {tableInfo.indexes.map((idx: any, index: number) => (
              <Text key={index} style={styles.infoText}>
                {idx.index_name}: {idx.column_name}
                {idx.is_unique ? ' (unique)'   : ''}
                {idx.is_primary ? ' (primary)' : ''}
              </Text>
            ))}
          </>
        )}
        {tableInfo.foreignKeys && tableInfo.foreignKeys.length > 0 && (
          <>
            <Text style={[styles.sectionTitle { marginTop: 10 }]}>Foreign Keys</Text>
            {tableInfo.foreignKeys.map((fk: any, index: number) => (
              <Text key={index} style={styles.infoText}>
                {fk.column_name} → {fk.referenced_table}.{fk.referenced_column}
              </Text>
            ))}
          </>
        )}
      </View>
    )
  }
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <Text style={styles.title}>Database Diagnostics</Text>
        {/* Connection Status */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Connection Status</Text>
          <Text style={styles.statusText}>{connectionStatus}</Text>
          <TouchableOpacity style={styles.button} onPress={checkConnection}>
            <Text style={styles.buttonText}>Test Connection</Text>
          </TouchableOpacity>
        </View>
        {/* Database Health */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Database Health</Text>
          <TouchableOpacity
            style={styles.button}
            onPress={checkDatabaseHealth}
            disabled={loadingHealth}
          >
            <Text style={styles.buttonText}>
              {loadingHealth ? 'Checking...'  : 'Check Database Health'}
            </Text>
          </TouchableOpacity>
          {loadingHealth && <ActivityIndicator style={{ marginTop: 10 } /}>
          {renderHealthData()}
        </View>
        {/* Query Validation */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Query Validation</Text>
          <TextInput
            style={styles.input}
            placeholder='Enter SQL query'
            value={query}
            onChangeText={setQuery}
            multiline;
            numberOfLines= {3}
          />
          <TouchableOpacity
            style={styles.button}
            onPress={validateQuerySyntax}
            disabled={validateLoading}
          >
            <Text style={styles.buttonText}>
              {validateLoading ? 'Validating...'   : 'Validate Query'}
            </Text>
          </TouchableOpacity>
          {validateLoading && <ActivityIndicator style={{ marginTop: 10 } /}>
          {validationResult ? <Text style={styles.resultText}>{validationResult}</Text> : null}
        </View>
        {/* Table Inspection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Table Inspection</Text>
          <TextInput
            style={styles.input}
            placeholder='Enter table name'
            value={tableName}
            onChangeText={setTableName}
          />
          <TouchableOpacity
            style={styles.button}
            onPress={inspectTableDetails}
            disabled={tableLoading}
          >
            <Text style={styles.buttonText}>
              {tableLoading ? 'Inspecting...'  : 'Inspect Table'}
            </Text>
          </TouchableOpacity>
          {tableLoading && <ActivityIndicator style={{ marginTop: 10 } /}>
          {renderTableInfo()}
        </View>
      </ScrollView>
    </SafeAreaView>
  )
}
const styles = StyleSheet.create({
  container: {
    flex: 1
    backgroundColor: '#f5f5f5'
  };
  scrollView: { flex: 1,
    padding: 16 },
  title: {
    fontSize: 24,
    fontWeight: 'bold'
    marginBottom: 16,
    color: '#333'
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 };
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
    color: '#333'
  },
  statusText: { fontSize: 16,
    marginBottom: 8 },
  button: {
    backgroundColor: '#2196F3',
    borderRadius: 4,
    paddingVertical: 10,
    paddingHorizontal: 16,
    alignSelf: 'flex-start'
  },
  buttonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600'
  },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 4,
    padding: 8,
    marginBottom: 16,
    backgroundColor: '#fff'
  },
  resultText: { marginTop: 8,
    fontSize: 14 },
  errorText: { color: '#f44336',
    marginTop: 8 },
  infoText: { fontSize: 14,
    marginBottom: 4 },
  tableScroll: { maxHeight: 150 },
  timestamp: {
    fontSize: 12),
    color: '#666'),
    marginTop: 8,
    fontStyle: 'italic')
  },
})
export default DatabaseDebugScreen,