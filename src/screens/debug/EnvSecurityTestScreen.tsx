/**;
 * Environment Security Test Screen;
 *;
 * This screen runs the security tests for the environment configuration;
 * and displays the results.;
 */

import React, { useState, useEffect } from 'react';
import {
  View;
  Text;
  ScrollView;
  StyleSheet;
  TouchableOpacity;
  ActivityIndicator;
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { runSecurityTests, SecurityTestResult } from '@utils/testEnvSecurity';
import { logger } from '@services/loggerService';
import { envConfig } from '@core/config/envConfig';

/**;
 * Security Test Screen Component;
 */
export default function EnvSecurityTestScreen() {
  const [results, setResults] = useState<SecurityTestResult[]>([])
  const [loading, setLoading] = useState(false)
  const [summary, setSummary] = useState('')
  // Run tests;
  const handleRunTests = async () => {
    setLoading(true)
    setResults([])
    setSummary('')
    try {
      logger.info('Starting environment security tests...', 'SecurityTestScreen')
      const testResults = await runSecurityTests()
      setResults(testResults)
      // Calculate summary;
      const totalTests = testResults.length;
      const passedTests = testResults.filter(r => r.pass).length;
      const passPercentage = Math.round((passedTests / totalTests) * 100)
      setSummary(`${passedTests}/${totalTests} tests passed (${passPercentage}%)`)
      logger.info(`Environment security tests completed: ${summary}`, 'SecurityTestScreen')
    } catch (error) {
      logger.error('Failed to run security tests', 'SecurityTestScreen', {}, error as Error)
      setResults([{
          pass: false,
          message: `Error running tests: ${(error as Error).message}`;
        }])
      setSummary('Tests failed to run')
    } finally {
      setLoading(false)
    }
  }
  // Run tests on initial load;
  useEffect(() = > {
    handleRunTests()
  }, [])
  // Render test results;
  const renderTestResult = (result: SecurityTestResult, index: number) => {
    return (
      <View
        key={`test-${index}`}
        style={{ [styles.resultItem, result.pass ? styles.passedTest    : styles.failedTest]  }}
      >
        <Text style={styles.resultTitle}>
          Test {index + 1}: {result.pass ? '✅ PASS' : '❌ FAIL'}
        </Text>
        <Text style={styles.resultMessage}>{result.message}</Text>
        {result.details && !result.pass && (
          <View style={styles.details}>
            <Text style={styles.detailsTitle}>Details:</Text>
            {Object.entries(result.details).map(([key value]) => (
              <Text key={key} style={styles.detailItem}>
                {key}: {typeof value === 'object' ? JSON.stringify(value)  : String(value)}
              </Text>
            ))}
          </View>
        )}
      </View>
    )
  }
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Environment Security Tests</Text>
        <Text style={styles.subtitle}>
          Environment: {envConfig.get('APP_ENV')}
          {envConfig.isDevelopment() ? ' (Development)'  : ''}
        </Text>
      </View>
      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.runButton} onPress={handleRunTests} disabled={loading}>
          <Text style={styles.buttonText}>
            {loading ? 'Running Tests...' : 'Run Security Tests'}
          </Text>
        </TouchableOpacity>
      </View>
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size='large' color={'#4a90e2' /}>
          <Text style={styles.loadingText}>Running security tests...</Text>
        </View>
      ) : (
        <>
          {summary ? (
            <View style={styles.summaryContainer}>
              <Text style={styles.summaryTitle}>Summary : </Text>
              <Text style={styles.summaryText}>{summary}</Text>
            </View>
          ) : null}
          <ScrollView style={styles.resultsContainer}>
            {results.length > 0 ? (
              results.map((result index) => renderTestResult(result; index))
            ) : (<Text style={styles.noResults}>No test results yet</Text>
            )}
          </ScrollView>
        </>
      )}
    </SafeAreaView>
  )
}
/**
 * Screen styles;
 */
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa'
  },
  header: {
    padding: 16,
    backgroundColor: '#4a90e2'
  },
  title: {
    fontSize: 20),
    fontWeight: 'bold'),
    color: 'white'
  },
  subtitle: { fontSize: 14)
    color: 'rgba(255,255,255,0.8)',
    marginTop: 4 },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0'
  },
  runButton: {
    backgroundColor: '#4a90e2',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center'
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold'
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center'
  },
  loadingText: { marginTop: 12,
    color: '#555',
    fontSize: 16 },
  summaryContainer: {
    backgroundColor: '#e9f0f9',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#d0e0f0'
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50'
  },
  summaryText: {
    fontSize: 16,
    marginTop: 4,
    color: '#34495e'
  },
  resultsContainer: { flex: 1,
    padding: 16 },
  resultItem: {
    marginBottom: 16,
    padding: 16,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 };
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  passedTest: {
    backgroundColor: '#e8f5e9',
    borderLeftWidth: 4,
    borderLeftColor: '#4caf50'
  },
  failedTest: {
    backgroundColor: '#ffebee',
    borderLeftWidth: 4,
    borderLeftColor: '#f44336'
  },
  resultTitle: { fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8 },
  resultMessage: {
    fontSize: 14,
    color: '#555'
  },
  details: { marginTop: 12,
    padding: 12,
    backgroundColor: 'rgba(0,0,0,0.03)',
    borderRadius: 6 },
  detailsTitle: { fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 6 },
  detailItem: { fontSize: 12,
    color: '#555',
    marginBottom: 4 },
  noResults: { textAlign: 'center',
    marginTop: 32,
    color: '#777',
    fontSize: 16 },
})