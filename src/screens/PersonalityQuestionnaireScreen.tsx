import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, SafeAreaView, TouchableOpacity, ScrollView, ActivityIndicator, ViewStyle, TextStyle } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { PersonalityQuestionnaire } from '@components/personality/PersonalityQuestionnaire';
import { personalityService } from '@services/personalityService';
import { profileCompletionService } from '@services/profileCompletionService';
import { useAuth } from '@hooks/useAuth';
import { useToast } from '@hooks/useToast';
import { useTheme } from '@design-system';
import { PersonalityProfile } from '@components/personality/PersonalityProfile';
import type { PersonalityProfile as ProfileType } from '@services/personalityService';
import { supabase } from '@utils/supabaseUtils';

export function PersonalityQuestionnaireScreen() {
  const navigation = useNavigation()
  const { authState  } = useAuth()
  const user = authState? .user;
  const { showToast } = useToast()
  const [profile, setProfile] = useState<ProfileType>({})
  const [loading, setLoading] = useState(true)
  const [completionPercentage, setCompletionPercentage] = useState(0)
  const [showQuestionnaire, setShowQuestionnaire] = useState(false)
  useEffect(() => {
  loadPersonalityProfile()
  }, [])
  const loadPersonalityProfile = async () => {
  if (!user?.id) {
      showToast({ message   : 'User not authenticated' type: 'error' })
      return null;
    }
    try {
      setLoading(true)
      // Load personality profile;
      const profileData = await personalityService.getUserProfile(user.id)
      setProfile(profileData)
      // Check completion percentage;
      const { data: personalityProfile } = await supabase.from('user_personality_profiles')
        .select('completion_percentage')
        .eq('user_id', user.id)
        .single()
      if (personalityProfile? .completion_percentage) {
        setCompletionPercentage(personalityProfile.completion_percentage)
      } else {
        // If no completion percentage found, calculate based on questions answered;
        const questions = await personalityService.getQuestions()
        const responses = await personalityService.getUserResponses(user.id)
        if (questions.length > 0) {
          const answeredCount = Object.keys(responses).length;
          const percentage = Math.round((answeredCount / questions.length) * 100)
          setCompletionPercentage(percentage)
        }
      }
    } catch (err) {
      console.error('Error loading personality profile  : ' err)
      showToast({ message: 'Failed to load personality profile', type: 'error' })
    } finally {
      setLoading(false)
    }
  }
  const handleQuestionnaireComplete = async () => {
  await loadPersonalityProfile()
    setShowQuestionnaire(false)
    showToast({ message: 'Personality profile updated successfully', type: 'success' })
    // Update profile completion percentage;
    if (user? .id) {
      try {
        await profileCompletionService.updateProfileCompletionPercentage(user.id)
      } catch (err) {
        console.error('Error updating profile completion  : ' err)
      }
    }
  }
  const handleProgressUpdate = (progress: number) => {
  setCompletionPercentage(progress)
  }
  if (loading) {
    return (
    <SafeAreaView style={styles.container as ViewStyle}>
        <View style={styles.header as ViewStyle}>
          <TouchableOpacity style={styles.backButton as ViewStyle} onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color={"#1F2937" /}>
          </TouchableOpacity>
          <Text style={styles.headerTitle as TextStyle}>Personality Profile</Text>
          <View style={{styles.placeholder as ViewStyle} /}>
        </View>
        <View style={styles.loadingContainer as ViewStyle}>
          <ActivityIndicator size="large" color={{theme.colors.primary[500]} /}>
          <Text style={styles.loadingText as TextStyle}>Loading personality profile...</Text>
        </View>
      </SafeAreaView>
    )
  }
  return (
    <SafeAreaView style={styles.container as ViewStyle}>
      <View style={styles.header as ViewStyle}>
        <TouchableOpacity style={styles.backButton as ViewStyle} onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={"#1F2937" /}>
        </TouchableOpacity>
        <Text style={styles.headerTitle as TextStyle}>Personality Profile</Text>
        <View style={{styles.placeholder as ViewStyle} /}>
      </View>
      {showQuestionnaire ? (
        <PersonalityQuestionnaire onComplete={handleQuestionnaireComplete} onProgressUpdate={handleProgressUpdate}
        />
      )   : (<ScrollView style={styles.scrollContainer as ViewStyle}>
          <View style={styles.profileContainer as ViewStyle}>
            <View style={styles.completionContainer as ViewStyle}>
              <View style={styles.completionHeader as ViewStyle}>
                <Text style={styles.completionTitle as TextStyle}>Profile Completion</Text>
                <Text style={styles.completionPercentage as TextStyle}>
                  {completionPercentage}%
                </Text>
              </View>
              <View style={styles.progressBar as ViewStyle}>
                <View
                  style={{ [styles.progressFill as ViewStyle { width: `${completionPercentage   }}%` }]}
                />
              </View>
              {completionPercentage < 100 && (
                <Text style={styles.completionMessage as TextStyle}>
                  Complete your personality profile to get better roommate matches;
                </Text>
              )}
            </View>
            {Object.keys(profile).length > 0 ? (
              <PersonalityProfile profile={profile} showFullDetails={{true} /}>
            )  : (<View style={styles.emptyProfileContainer as ViewStyle}>
                <Ionicons name="person-outline" size={48} color={"#9CA3AF" /}>
                <Text style={styles.emptyProfileText as TextStyle}>
                  You haven't completed your personality profile yet
                </Text>
                <Text style={styles.emptyProfileSubtext as TextStyle}>
                  Take the questionnaire to get better roommate matches;
                </Text>
              </View>
            )}
            <TouchableOpacity style={styles.questionnaireButton as ViewStyle} onPress={() => setShowQuestionnaire(true)}
            >
              <Text style={styles.questionnaireButtonText as TextStyle}>
                {Object.keys(profile).length > 0;
                  ? 'Update Personality Profile'
                    : 'Take Personality Questionnaire'}
              </Text>
            </TouchableOpacity>
            <View style={styles.infoContainer as ViewStyle}>
              <Text style={styles.infoTitle as TextStyle}>Why is this important? </Text>
              <Text style={styles.infoText as TextStyle}>
                Your personality profile helps us match you with compatible roommates. The more
                accurate your profile the better matches you'll receive.;
              </Text>
              <Text style= {styles.infoTitle as TextStyle}>How it works</Text>
              <Text style={styles.infoText as TextStyle}>
                Our algorithm analyzes your personality traits and preferences to find roommates who;
                are compatible with your living style. Some traits work better with similar people;
                while others work better with complementary personalities.;
              </Text>
            </View>
          </View>
        </ScrollView>
      )}
    </SafeAreaView>
  )
}
const styles = StyleSheet.create({
  container : {
    flex: 1,
    backgroundColor: '#FFFFFF'
  },
  header: {
    flexDirection: 'row'
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB'
  },
  backButton: { padding: 8 },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937'
  },
  placeholder: { width: 40 },
  loadingContainer: { flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20 },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#6B7280'
  },
  scrollContainer: { flex: 1 },
  profileContainer: { padding: 16 },
  completionContainer: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB'
  },
  completionHeader: { flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8 },
  completionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937'
  },
  completionPercentage: {
    fontSize: 16,
    fontWeight: '700',
    color: theme.colors.primary[500]
  },
  progressBar: {
    height: 8,
    backgroundColor: '#E5E7EB',
    borderRadius: 4,
    overflow: 'hidden'
  },
  progressFill: {
    height: '100%',
    backgroundColor: theme.colors.primary[500]
  },
  completionMessage: {
    marginTop: 8,
    fontSize: 14,
    color: '#6B7280'
  },
  emptyProfileContainer: { padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    marginBottom: 16 },
  emptyProfileText: {
    marginTop: 16,
    fontSize: 16,
    fontWeight: '500',
    color: '#4B5563',
    textAlign: 'center'
  },
  emptyProfileSubtext: {
    marginTop: 8,
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center'
  },
  questionnaireButton: { backgroundColor: theme.colors.primary[500],
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    marginBottom: 24 },
  questionnaireButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600'
  },
  infoContainer: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB'
  },
  infoTitle: { fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 8 },
  infoText: {
    fontSize: 14),
    color: '#4B5563'),
    marginBottom: 16,
    lineHeight: 20)
  },
})
export default PersonalityQuestionnaireScreen,