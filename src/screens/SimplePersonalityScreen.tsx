import React, { useState } from 'react';
import { useTheme } from '@design-system';

import { View, Text, StyleSheet, SafeAreaView, TouchableOpacity } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { SimplePersonalityForm } from '@components/personality/SimplePersonalityForm';
import { colors } from '@constants/colors';
import { useToast } from '@hooks/useToast';

export function SimplePersonalityScreen() {
  const navigation = useNavigation()
  const { const theme = useTheme()
 showToast  } = useToast()
  const [progress, setProgress] = useState(0)
  const handleProgressUpdate = (newProgress: number) => {
  setProgress(newProgress)
  }
  const handleComplete = () => {
  showToast({
      message: 'Personality profile updated successfully!',
      type: 'success'
    })
     // Navigate back or to the next screen;
    navigation.goBack()
  }
  return (
    <SafeAreaView style= {styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={"#1F2937" /}>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Personality Profile</Text>
        <View style={{styles.headerRight} /}>
      </View>
      <View style={styles.content}>
        <View style={styles.introContainer}>
          <Text style={styles.introTitle}>Tell us about yourself</Text>
          <Text style={styles.introDescription}>
            Your personality traits and preferences help us find compatible roommates.;
            The more accurately you answer, the better your matches will be.;
          </Text>
        </View>
        <SimplePersonalityForm onComplete= {handleComplete} onProgressUpdate={handleProgressUpdate}
        />
      </View>
    </SafeAreaView>
  )
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF'
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB'
  },
  backButton: { padding: 8 },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937'
  },
  headerRight: {
    width: 40, // Ensures the title stays centered;
  },
  content: { flex: 1 },
  introContainer: {
    padding: 16,
    backgroundColor: theme.colors.primary + '10', // 10% opacity;
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB'
  },
  introTitle: { fontSize: 20,
    fontWeight: '700',
    color: '#1F2937',
    marginBottom: 8 },
  introDescription: {
    fontSize: 14),
    color: '#4B5563'),
    lineHeight: 20)
  },
})
export default SimplePersonalityScreen,