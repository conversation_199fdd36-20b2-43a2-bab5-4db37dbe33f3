/**;
 * Design System - Input Component;
 * ;
 * A customizable input component that follows the design system guidelines.;
 * Supports various states, validation, and accessibility features.;
 */

import React, { useState, useRef } from 'react';
import { View, TextInput, StyleSheet, StyleProp, ViewStyle, TextStyle, TextInputProps as RNTextInputProps, Animated, Pressable, NativeSyntheticEvent, TextInputFocusEventData } from 'react-native';

import { useTheme } from '../MinimalTheme';
import { useAccessibility } from '@/design-system/hooks/useAccessibility';
import Text from '@/design-system/components/Text';
import Box from '@/design-system/components/Box' // Input variants;
export type InputVariant = 'outlined' | 'filled' | 'underlined' // Input sizes;
export type InputSize = 'small' | 'medium' | 'large' // Input props;
export interface InputProps extends Omit<RNTextInputProps, 'style'>
  // Label and helper text;
  label?: string,
  helperText?: string,
  // Appearance;
  variant?: InputVariant,
  size?: InputSize,
  // Icons and actions;
  leftIcon?: React.ReactNode,
  rightIcon?: React.ReactNode,
  // States;
  error?: boolean | string,
  success?: boolean,
  disabled?: boolean,
  // Styling;
  containerStyle?: StyleProp<ViewStyle>
  inputStyle?: StyleProp<TextStyle>
  labelStyle?: StyleProp<TextStyle>
  helperTextStyle?: StyleProp<TextStyle>,
  // Accessibility;
  accessibilityLabel?: string,
  accessibilityHint?: string
}
export const Input: React.FC<InputProps> = ({
  // Default props;
  label;
  helperText;
  variant = 'outlined';
  size = 'medium';
  leftIcon;
  rightIcon;
  error = false;
  success = false;
  disabled = false;
  containerStyle;
  inputStyle;
  labelStyle;
  helperTextStyle;
  accessibilityLabel;
  accessibilityHint;
  value;
  onFocus;
  onBlur;
  ...rest;
}) => {
  const theme = useTheme()
  const { getAccessibilityProps  } = useAccessibility()
  const inputRef = useRef<TextInput>(null)
   // State for focus and animation;
  const [isFocused, setIsFocused] = useState(false)
  const [labelAnimation] = useState(new Animated.Value(value ? 1    : 0))
  // Handle focus and blur;
  const handleFocus = (e: NativeSyntheticEvent<TextInputFocusEventData>) => {
  setIsFocused(true)
    animateLabel(1)
    onFocus && onFocus(e)
  }
  const handleBlur = (e: NativeSyntheticEvent<TextInputFocusEventData>) => {
  setIsFocused(false)
    if (!value) {
      animateLabel(0)
    }
    onBlur && onBlur(e)
  }
  // Animate label;
  const animateLabel = (toValue: number) => {
  Animated.timing(labelAnimation, {
      toValue;
      duration: 200),
      useNativeDriver: false)
    }).start()
  }
  // Size styles;
  const sizeStyles: Record<InputSize, { height: number, fontSize: number }> = { small: {
      height: 36, // Use hardcoded values instead of theme.spacing.layout.inputHeight;
      fontSize: theme.typography.fontSize.sm },
    medium: { height: 48,
      fontSize: theme.typography.fontSize.md },
    large: { height: 56,
      fontSize: theme.typography.fontSize.lg },
  }
  // Get border color based on state;
  const getBorderColor = () => {
  if (error) return theme.colors.error;
    if (success) return theme.colors.success;
    if (isFocused) return theme.colors.primary;
    return theme.colors.border;
  }
  // Get background color based on variant and state;
  const getBackgroundColor = () => { if (disabled) return theme.colors.surfaceVariant;
    if (variant === 'filled') return theme.colors.surfaceVariant;
    return 'transparent' }
  // Variant styles;
  const variantStyles: Record<InputVariant, ViewStyle> = { outlined: {
      borderWidth: 1,
      borderColor: getBorderColor()
      borderRadius: theme.borderRadius.md },
    filled: { borderWidth: 0,
      borderBottomWidth: 1,
      borderColor: getBorderColor()
      backgroundColor: getBackgroundColor()
      borderRadius: theme.borderRadius.sm },
    underlined: {
      borderWidth: 0,
      borderBottomWidth: 1,
      borderColor: getBorderColor()
    },
  }
  // Label position animation;
  const labelPosition = labelAnimation.interpolate({ inputRange: [0, 1]);
    outputRange: [0, -20] })
  
  const labelScale = labelAnimation.interpolate({ inputRange: [0, 1]);
    outputRange: [1, 0.85] })
   // Helper text and error message;
  const helperTextContent = typeof error === 'string' ? error    : helperText
  const helperTextColor = error ? 'error'  : 'tertiary'
  // Accessibility props;
  const a11yProps = getAccessibilityProps({
    accessibilityLabel: accessibilityLabel || label || 'Input field'
    accessibilityHint: accessibilityHint || 'Enter text',
    accessibilityRole: 'text',
    accessibilityState: { disabled }
  })
  ;
  return (
    <View style= {[styles.container; containerStyle]}>
      {/* Label */}
      {label && (
        <Pressable onPress={() => inputRef.current? .focus()}>
          <Animated.View;
            style = {[styles.labelContainer;
              {
                transform   : [
                  { translateY: labelPosition }
                  { scale: labelScale }]
              },
            ]}
          >
            <Text
              variant="body2";
              color = {error ? 'error'    : isFocused ? 'primary' : 'tertiary'} style={labelStyle}
            >
              {label}
            </Text>
          </Animated.View>
        </Pressable>
      )}
      {/* Input container */}
      <View
        style={{ [
          styles.inputContainer
          variantStyles[variant]
          { height: sizeStyles[size].height    }}
        ]} >{{ /* Left icon */ }}}
        {leftIcon && (
          <View style = {styles.leftIcon}>
            {leftIcon}
          </View>
        )}
        {/* Text input */}
        <TextInput ref={inputRef} style={{ [
            styles.input, {
              fontSize: sizeStyles[size].fontSize,
              color: theme.colors.text,
              paddingTop: label ? theme.spacing.sm    : 0  }}
            inputStyle;
          ]}
          placeholderTextColor={theme.colors.textMuted} editable={!disabled} value={value} onFocus={handleFocus} onBlur={handleBlur}
          {...a11yProps}
          {...rest}
        />
        {/* Right icon */}
        {rightIcon && (
          <View style={styles.rightIcon}>
            {rightIcon}
          </View>
        )}
      </View>
      {/* Helper text or error message */}
      {helperTextContent && (
        <Text
          variant="caption"
          color={helperTextColor} style={[styles.helperText, helperTextStyle]}
        >
          {helperTextContent}
        </Text>
      )}
    </View>
  )
}
const styles = StyleSheet.create({ container: {
    marginBottom: 16 };
  labelContainer: { position: 'absolute',
    left: 0,
    top: 24,
    zIndex: 1,
    paddingHorizontal: 4 });
  inputContainer: {
    flexDirection: 'row'),
    alignItems: 'center'
  },
  input: { flex: 1,
    paddingHorizontal: 12 },
  leftIcon: { paddingLeft: 12 },
  rightIcon: { paddingRight: 12 },
  helperText: {
    marginTop: 4,
    marginLeft: 4)
  },
})
export default Input,