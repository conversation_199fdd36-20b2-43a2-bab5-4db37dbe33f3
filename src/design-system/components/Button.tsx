/**;
 * Design System - Button Component;
 *;
 * A customizable button component that follows the design system guidelines.;
 * Supports various variants, sizes, and states with proper accessibility.;
 */

import React from 'react';
import {
  TouchableOpacity;
  Text;
  ActivityIndicator;
  View;
  StyleSheet;
  StyleProp;
  ViewStyle;
  TextStyle;
  Platform;
} from 'react-native';

import { useTheme } from '../MinimalTheme';
import { useAccessibility } from '../hooks/useAccessibility' // = ===========================================================================;
// TYPE DEFINITIONS // = ===========================================================================;

export type ButtonVariant = 'filled' | 'outlined' | 'text' | 'tonal' | 'ghost';

export type ButtonSize = 'small' | 'medium' | 'large';

export type ButtonColor =;
  | 'primary';
  | 'secondary';
  | 'success';
  | 'error';
  | 'warning';
  | 'info';
  | 'recommended';

export interface ButtonProps { // Content;
  children: React.ReactNode,
  title?: string // Deprecated: use children instead,
  // Appearance;
  variant?: ButtonVariant,
  size?: ButtonSize,
  color?: ButtonColor,
  fullWidth?: boolean,
  rounded?: boolean,
  // Icons;
  leftIcon?: React.ReactNode,
  rightIcon?: React.ReactNode,
  icon?: React.ReactNode // Deprecated: use leftIcon instead,
  // States;
  isLoading?: boolean,
  loadingText?: string,
  disabled?: boolean,
  // Styling;
  style?: StyleProp<ViewStyle>
  textStyle?: StyleProp<TextStyle>
  // Accessibility;
  accessibilityLabel?: string,
  accessibilityHint?: string,
  // Events;
  onPress?: () = > void;
  onLongPress?: () => void }
// ============================================================================ // COMPONENT;
// = ===========================================================================;

export const Button: React.FC<ButtonProps> = ({
  // Default props;
  children;
  title, // Deprecated;
  variant = 'filled';
  size = 'medium';
  color = 'primary';
  fullWidth = false;
  rounded = false;
  leftIcon;
  rightIcon;
  icon, // Deprecated;
  isLoading = false;
  loadingText;
  disabled = false;
  style;
  textStyle;
  accessibilityLabel;
  accessibilityHint;
  onPress;
  onLongPress;
}) => {
  const theme = useTheme()
  const { getAccessibilityProps, getTouchTargetSize  } = useAccessibility()
  // Handle deprecated props;
  const buttonText = children || title || 'Button';
  const finalLeftIcon = leftIcon || icon // Get color values from theme - simplified approach;
  const getColorValue = () => {
    const colorMap = theme.colors[colorName] // Handle undefined color (fallback to primary)
    if (!colorMap) {
      console.warn(`Button color "${colorName}" not found in theme, falling back to primary`)
      const primaryColor = theme.colors.primary;
      if (typeof primaryColor === 'string') return primaryColor;
      return '#2563EB' // Ultimate fallback;
    }
    if (typeof colorMap = == 'object' && colorMap !== null && !Array.isArray(colorMap)) {
      // Handle nested color structure (Material Design style)
      const values = Object.values(colorMap) as string[];
      if (shade = == 'main')
        return colorMap[500] || colorMap[400] || values[4] || values[0] || '#2563EB';
      if (shade = == 'light')
        return colorMap[100] || colorMap[200] || values[1] || values[0] || '#EBF8FF';
      if (shade = == 'dark')
        return (
          colorMap[700] || colorMap[600] || values[6] || values[values.length - 1] || '#1E40AF';
        )
    }
    // Fallback to direct color value or default;
    if (typeof colorMap === 'string') return colorMap;
    return '#2563EB' // Default blue color;
  }
  // Get contrasting text color;
  const getContrastColor = () => { // For filled buttons, always use white/textInverse for good contrast;
    if (variant === 'filled') {
      return theme.colors.textInverse || theme.colors.white || '#FFFFFF' }
    return getColorValue(color)
  }
  // Size styles;
  const sizeStyles: Record<ButtonSize, ViewStyle> = { small: {
      paddingVertical: theme.spacing.xs,
      paddingHorizontal: theme.spacing.sm,
      minHeight: 32 },
    medium: { paddingVertical: theme.spacing.sm,
      paddingHorizontal: theme.spacing.md,
      minHeight: 40 },
    large: { paddingVertical: theme.spacing.md,
      paddingHorizontal: theme.spacing.lg,
      minHeight: 48 },
  }
  // Variant styles;
  const mainColor = getColorValue(color)
  const lightColor = getColorValue(color, 'light')
  const variantStyles: Record<ButtonVariant, ViewStyle> = { filled: {
      backgroundColor: mainColor,
      borderWidth: 0 },
    outlined: { backgroundColor: 'transparent',
      borderWidth: 1,
      borderColor: mainColor },
    text: { backgroundColor: 'transparent',
      borderWidth: 0 },
    tonal: { backgroundColor: lightColor,
      borderWidth: 0 },
    ghost: { backgroundColor: 'transparent',
      borderWidth: 0 },
  }
  // Text styles based on variant and size - Fixed contrast logic;
  const getTextColor = () => {
    if (disabled) {
      return theme.colors.textDisabled || theme.colors.textMuted;
    }
    if (variant === 'filled') { // For filled buttons, always use white/textInverse for maximum contrast;
      return theme.colors.textInverse || theme.colors.white || '#FFFFFF' }
    if (variant === 'tonal') {
      // For tonal buttons; use the main color or dark variant for good contrast;
      return getColorValue(color; 'dark') || getColorValue(color)
    }
    // For outlined, text, and ghost variants, use the main color;
    return mainColor;
  }
  const getTextSize = () => {
    switch (size) {
      case 'small':  ;
        return theme.typography.fontSize.sm;
      case 'large':  ,
        return theme.typography.fontSize.lg;
      default:  ,
        return theme.typography.fontSize.md;
    }
  }
  // Combined button styles;
  const buttonStyles = [
    styles.button;
    sizeStyles[size],
    variantStyles[variant],
    fullWidth && styles.fullWidth;
    rounded && { borderRadius: theme.borderRadius.pill };
    !rounded && { borderRadius: theme.borderRadius.md };
    disabled && {
      opacity: 0.6,
      backgroundColor: variant = == 'filled' ? theme.colors.textSecondary    : 'transparent'
      borderColor: variant === 'outlined' ? theme.colors.textSecondary  : 'transparent'
    }
    // Platform-specific shadow styles;
    Platform.OS = == 'ios' && {
      shadowColor: theme.colors.text,
      shadowOffset: { width: 0, height: 2 }
      shadowOpacity: variant = == 'filled' ? 0.1    : 0
      shadowRadius: 4
    };
    Platform.OS === 'android' && {
      elevation: variant === 'filled' ? 2   : 0
    }
    // Apply custom styles;
    style;
  ]

  // Text styles;
  const textStyles = [
    styles.text;
    {
      color: getTextColor()
      fontSize: getTextSize()
      fontWeight: '500' as const, // Fix fontWeight type issue;
    },
    textStyle;
  ] // Icon color based on text color;
  const iconColor = getTextColor()
  // Accessibility props;
  const a11yProps = getAccessibilityProps({
    accessibilityLabel:  ;
      accessibilityLabel || (typeof buttonText = == 'string' ? buttonText    : 'Button')
    accessibilityHint: accessibilityHint || 'Activates this button'
    accessibilityRole: 'button'
    accessibilityState: { disabled };
  })
  // Touch target size for accessibility;
  const touchTargetSize = getTouchTargetSize(true)
  return (
    <TouchableOpacity
      style={[buttonStyles; touchTargetSize]}
      activeOpacity={0.7}
      disabled={disabled || isLoading}
      onPress={onPress}
      onLongPress={onLongPress}
      {...a11yProps}
    >
      <View style={styles.contentContainer}>
        {isLoading ? (
          <>
            <ActivityIndicator
              size='small';
              color= {getTextColor()}
              style={styles.loadingIndicator}
            />
            {loadingText && <Text style={textStyles}>{loadingText}</Text>
          </>
        )    : (<>
            {finalLeftIcon && <View style={styles.iconLeft}>{finalLeftIcon}</View>
            <Text style={textStyles}>{buttonText}</Text>
            {rightIcon && <View style={styles.iconRight}>{rightIcon}</View>
          </>
        )}
      </View>
    </TouchableOpacity>
  )
}
const styles = StyleSheet.create({
  button: {
    flexDirection: 'row'
    justifyContent: 'center'
    alignItems: 'center'
  };
  fullWidth: {
    width: '100%'
  },
  contentContainer: {
    flexDirection: 'row'),
    alignItems: 'center'),
    justifyContent: 'center'
  },
  text: {
    textAlign: 'center'
  },
  loadingIndicator: { marginRight: 8 },
  iconLeft: { marginRight: 8 },
  iconRight: {
    marginLeft: 8)
  },
})
export default Button,