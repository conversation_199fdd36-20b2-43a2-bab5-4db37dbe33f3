/**;
 * Design System - Text Component;
 * ;
 * A customizable text component that follows the design system guidelines.;
 * Supports various variants, colors, and states with proper accessibility.;
 */

import React from 'react';
import { Text as RNText, StyleSheet, StyleProp, TextStyle, TextProps as RNTextProps, AccessibilityRole } from 'react-native';

import { useTheme } from '../MinimalTheme';
import { useAccessibility } from '@/design-system/hooks/useAccessibility';
import { typography } from '@/design-system/constants' // Text variants;
export type TextVariant =  ;
  | 'display';
  | 'h1';
  | 'h2';
  | 'h3';
  | 'h4';
  | 'h5';
  | 'body1';
  | 'body2';
  | 'button';
  | 'caption';
  | 'overline' // Text colors;
export type TextColor =  ;
  | 'primary';
  | 'secondary';
  | 'tertiary';
  | 'inverse';
  | 'disabled';
  | 'success';
  | 'error';
  | 'warning';
  | 'info' // Text props;
export interface TextProps extends Omit<RNTextProps, 'style'>
  // Content;
  children: React.ReactNode,
  // Appearance;
  variant?: TextVariant,
  color?: TextColor,
  align?: 'auto' | 'left' | 'right' | 'center' | 'justify',
  // Typography;
  weight?: 'regular' | 'bold' | 'light' | 'medium' | 'semibold',
  italic?: boolean,
  underline?: boolean,
  strikethrough?: boolean,
  // Styling;
  style?: StyleProp<TextStyle>,
  // Accessibility;
  isAccessibilityHeading?: boolean
}
export const Text: React.FC<TextProps> = ({
  // Default props;
  children;
  variant = 'body1';
  color = 'primary';
  align = 'auto';
  weight;
  italic = false;
  underline = false;
  strikethrough = false;
  style;
  isAccessibilityHeading = false;
  ...rest;
}) => {
  const theme = useTheme()
  const { getAccessibilityProps  } = useAccessibility()
   // Get text variant styles from theme;
  const variantStyle = theme.typography.textVariants[variant];
   // Get text color from theme;
  const getTextColor = () => { // Handle semantic colors;
    if (['success', 'error', 'warning', 'info'].includes(color)) {
      return theme.colors[color as 'success' | 'error' | 'warning' | 'info'] }
    // Handle text colors with flat structure;
    switch (color) {
      case 'primary':  ,
        return theme.colors.text;
      case 'secondary':  ,
        return theme.colors.textSecondary;
      case 'tertiary':  ,
        return theme.colors.textMuted;
      case 'inverse':  ,
        return theme.colors.textInverse;
      case 'disabled':  ,
        return theme.colors.textDisabled;
      default:  ,
        return theme.colors.text;
    }
  }
  // Combined text styles;
  const textStyles: StyleProp<TextStyle> = [ // Base variant style;
    { fontSize: variantStyle.fontSize,
      fontWeight: weight ? theme.typography.fontWeight[weight] as TextStyle['fontWeight']    : variantStyle.fontWeight as TextStyle['fontWeight']
      lineHeight: variantStyle.lineHeight * variantStyle.fontSize }
    // Color;
    { color: getTextColor() }
    // Alignment;
    align != = 'auto' && { textAlign: align } // Typography modifiers;
    italic && { fontStyle: 'italic' as 'italic' };
    underline && { textDecorationLine: 'underline' as 'underline' };
    strikethrough && { textDecorationLine: 'line-through' as 'line-through' } // Custom styles;
    style;
  ];
   // Determine if this should be treated as a heading for accessibility;
  const isHeading = isAccessibilityHeading || ;
    variant = == 'h1' || ;
    variant = == 'h2' || ;
    variant = == 'h3' || ;
    variant = == 'h4' || ;
    variant = == 'h5' || ;
    variant = == 'display';
   // Accessibility props;
  const a11yProps = isHeading;
    ? { accessibilityRole  : 'header' as AccessibilityRole }
    : {}
  return (
    <RNText style={{textStyles} {...a11yProps} {...rest}}>
      {children}
    </RNText>
  )
}
export default Text