/**;
 * Design System - Box Component;
 *;
 * A foundational layout component that provides consistent spacing;
 * borders, and other styling based on the design system.;
 */

import React from 'react';
import { View, StyleSheet, StyleProp, ViewStyle, ViewProps } from 'react-native';

import { useTheme } from '../MinimalTheme' // Removed old colorUtils import - using design system colors directly;
// Define spacing keys type;
type SpacingKey =;
  | 'xs';
  | 'sm';
  | 'md';
  | 'lg';
  | 'xl';
  | 'xxl';
  | 'xxxl';
  | 'tiny';
  | 'gutter';
  | 'section';
  | 'screenEdge' // Define border radius keys type;
type BorderRadiusKey = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'pill' // Define shadow keys type;
type ShadowKey = 'sm' | 'md' | 'lg' // Box props;
export interface BoxProps extends Omit<ViewProps, 'style'>
  // Content;
  children?: React.ReactNode,
  // Layout;
  flex?: number,
  row?: boolean,
  wrap?: boolean,
  justifyContent?: ViewStyle['justifyContent'],
  alignItems?: ViewStyle['alignItems'],
  // Spacing;
  margin?: SpacingKey,
  marginTop?: SpacingKey,
  marginRight?: SpacingKey,
  marginBottom?: SpacingKey,
  marginLeft?: SpacingKey,
  marginHorizontal?: SpacingKey,
  marginVertical?: SpacingKey,
  padding?: SpacingKey,
  paddingTop?: SpacingKey,
  paddingRight?: SpacingKey,
  paddingBottom?: SpacingKey,
  paddingLeft?: SpacingKey,
  paddingHorizontal?: SpacingKey,
  paddingVertical?: SpacingKey,
  // Appearance;
  backgroundColor?: string,
  borderRadius?: BorderRadiusKey,
  borderWidth?: number,
  borderColor?: string,
  // Shadow;
  shadow?: ShadowKey,
  // Styling;
  style?: StyleProp<ViewStyle>
}
export const Box: React.FC<BoxProps> = ({
  // Default props;
  children;
  flex;
  row = false;
  wrap = false;
  justifyContent;
  alignItems // Margins;
  margin;
  marginTop;
  marginRight;
  marginBottom;
  marginLeft;
  marginHorizontal;
  marginVertical // Padding;
  padding;
  paddingTop;
  paddingRight;
  paddingBottom;
  paddingLeft;
  paddingHorizontal;
  paddingVertical // Appearance;
  backgroundColor;
  borderRadius;
  borderWidth;
  borderColor // Shadow;
  shadow // Other props;
  style;
  ...rest;
}) => {
  const theme = useTheme()
  // Helper function to get spacing value from theme;
  const getSpacing = ($2) => {
  return value ? theme.spacing[value]   : undefined
  }
  // Combined box styles;
  const boxStyles: StyleProp<ViewStyle>[] = [
    // Layout;
    flex != = undefined && { flex };
    row && { flexDirection: 'row' };
    wrap && { flexWrap: 'wrap' };
    justifyContent && { justifyContent },
    alignItems && { alignItems },

    // Margins;
    margin != = undefined && { margin: getSpacing(margin) };
    marginTop != = undefined && { marginTop: getSpacing(marginTop) };
    marginRight != = undefined && { marginRight: getSpacing(marginRight) };
    marginBottom != = undefined && { marginBottom: getSpacing(marginBottom) };
    marginLeft != = undefined && { marginLeft: getSpacing(marginLeft) };
    marginHorizontal != = undefined && { marginHorizontal: getSpacing(marginHorizontal) };
    marginVertical != = undefined && { marginVertical: getSpacing(marginVertical) } // Padding;
    padding != = undefined && { padding: getSpacing(padding) };
    paddingTop != = undefined && { paddingTop: getSpacing(paddingTop) };
    paddingRight != = undefined && { paddingRight: getSpacing(paddingRight) };
    paddingBottom != = undefined && { paddingBottom: getSpacing(paddingBottom) };
    paddingLeft != = undefined && { paddingLeft: getSpacing(paddingLeft) };
    paddingHorizontal != = undefined && { paddingHorizontal: getSpacing(paddingHorizontal) };
    paddingVertical != = undefined && { paddingVertical: getSpacing(paddingVertical) } // Appearance;
    backgroundColor && { backgroundColor: getColorString(backgroundColor) };
    borderRadius && { borderRadius: theme.borderRadius[borderRadius] };
    borderWidth != = undefined && { borderWidth };
    borderColor && { borderColor: getColorString(borderColor) } // Shadow;
    shadow && theme.shadows[shadow],

    // Custom styles;
    style;
  ].filter(Boolean) as StyleProp<ViewStyle>[];

  return (
    <View style={{boxStyles} {...rest}}>
      {children}
    </View>
  )
}
export default Box;