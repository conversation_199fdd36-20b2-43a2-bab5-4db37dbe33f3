import React, { createContext, useContext, ReactNode } from 'react';
import { useColorScheme } from 'react-native' // Simple hardcoded constants to avoid import issues;
const spacing = { xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
  xxxl: 64,
  tiny: 2,
  gutter: 16,
  section: 40,
  screenEdge: 16 }
const typography = { fontSize: {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
    xxl: 24,
    xxxl: 32 },
  fontWeight: {
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700'
  },
  lineHeight: { tight: 1.2,
    normal: 1.5,
    relaxed: 1.8 },
}
const shadows = {
  small: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 };
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
  },
  medium: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 };
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
}
const responsive = { breakpoints: {
    sm: 640,
    md: 768,
    lg: 1024,
    xl: 1280 },
}
const borderRadius = { none: 0,
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  xxl: 24,
  full: 9999 }
const accessibility = { minTouchTarget: 44,
  focusRingWidth: 2,
  touchTargets: {
    minSize: 44,
    recommended: 48 },
  focus: {
    outlineWidth: 2,
    outlineColor: '#6366F1',
    outlineStyle: 'solid'
  },
  animation: { duration: {
      fast: 150,
      normal: 250,
      slow: 350 },
    reducedDuration: { fast: 50,
      normal: 100,
      slow: 150 },
  },
}
// Simple, hardcoded theme that will definitely work;
const LIGHT_COLORS = {
  // Brand colors - Payment Methods colors;
  primary: '#6366F1', // Changed from #0284c7 to match Payment Methods;
  primaryVariant: '#4F46E5',
  secondary: '#8B5CF6', // Purple for secondary actions;
  accent: '#9333ea', // Purple accent color // Background colors;
  background: '#FFFFFF',
  backgroundSecondary: '#f8fafc', // Secondary background for cards, inputs, etc.;
  surface: '#f8fafc',
  surfaceVariant: '#f1f5f9',
  // Text colors;
  text: '#1e293b',
  textSecondary: '#475569',
  textMuted: '#64748b',
  textInverse: '#FFFFFF',
  textDisabled: '#cbd5e1',
  // Border colors;
  border: '#e2e8f0',
  borderLight: '#f1f5f9',
  // Interactive states - Payment Methods colors;
  interactive: '#6366F1',
  interactiveHover: '#4F46E5',
  interactivePressed: '#3730A3',
  // Status colors - Quick Actions colors;
  success: '#10B981', // Green for positive actions;
  error: '#ef4444',
  warning: '#F59E0B', // Orange for warnings/calendar;
  warningBorder: '#F59E0B', // Border color for warning elements;
  warningText: '#92400E', // Text color for warning elements;
  info: '#3b82f6',
  recommended: '#10B981', // Use success green for recommended actions // Special colors;
  overlay: 'rgba(0, 0, 0, 0.5)',
  disabled: '#cbd5e1',
  premium: '#FFD700',
  shadow: '#000000', // Shadow color for elevation // Legacy compatibility;
  white: '#FFFFFF',
  black: '#000000',
  transparent: 'transparent',
  // Additional colors that components expect;
  gray: '#64748b',
  // Basic color properties that components expect;
  blue: '#2563eb',
  red: '#dc2626',
  green: '#059669',
  yellow: '#f59e0b',
  purple: '#9333ea',
  orange: '#ea580c'
}
const DARK_COLORS = {
  // Brand colors - Payment Methods dark mode colors;
  primary: '#6366F1', // Keep same primary for consistency;
  primaryVariant: '#4F46E5',
  secondary: '#8B5CF6', // Purple for secondary actions;
  accent: '#9333ea', // Purple accent color // Background colors;
  background: '#0f172a',
  backgroundSecondary: '#1e293b', // Secondary background for cards, inputs, etc.;
  surface: '#1e293b',
  surfaceVariant: '#334155',
  // Text colors;
  text: '#f1f5f9',
  textSecondary: '#cbd5e1',
  textMuted: '#94a3b8',
  textInverse: '#0f172a',
  textDisabled: '#475569',
  // Border colors;
  border: '#334155',
  borderLight: '#475569',
  // Interactive states - Payment Methods colors;
  interactive: '#6366F1',
  interactiveHover: '#4F46E5',
  interactivePressed: '#3730A3',
  // Status colors - Quick Actions colors;
  success: '#10B981', // Keep same green for consistency;
  error: '#f87171',
  warning: '#F59E0B', // Keep same orange for consistency;
  warningBorder: '#F59E0B', // Border color for warning elements;
  warningText: '#92400E', // Text color for warning elements;
  info: '#60a5fa',
  recommended: '#10B981', // Use success green for recommended actions // Special colors;
  overlay: 'rgba(0, 0, 0, 0.7)',
  disabled: '#475569',
  premium: '#FFD700',
  shadow: '#000000', // Shadow color for elevation // Legacy compatibility;
  white: '#FFFFFF',
  black: '#000000',
  transparent: 'transparent',
  // Additional colors that components expect;
  gray: '#94a3b8',
  // Basic color properties that components expect;
  blue: '#2563eb',
  red: '#dc2626',
  green: '#059669',
  yellow: '#f59e0b',
  purple: '#9333ea',
  orange: '#ea580c'
}
export interface MinimalTheme { mode: 'light' | 'dark',
  isDark: boolean,
  colors: typeof LIGHT_COLORS,
  spacing: typeof spacing,
  typography: typeof typography,
  borderRadius: typeof borderRadius,
  shadows: typeof shadows,
  responsive: typeof responsive,
  accessibility: typeof accessibility }
interface MinimalThemeContextType { theme: MinimalTheme,
  toggleTheme: () = > void,
  setTheme: (mode: 'light' | 'dark') = > void,
  isDark: boolean }
const createMinimalTheme = (mode: 'light' | 'dark') => {
  const theme = {
    mode;
    isDark: mode = == 'dark',
    colors: mode = == 'dark' ? DARK_COLORS   : LIGHT_COLORS
    spacing;
    typography;
    borderRadius;
    shadows;
    responsive;
    accessibility;
  }
  return theme;
}
const lightTheme = createMinimalTheme('light')
const darkTheme = createMinimalTheme('dark')
const MinimalThemeContext = createContext<MinimalThemeContextType>({
  theme: lightTheme,
  toggleTheme: () => {}
  setTheme: () => {};
  isDark: false
})
interface MinimalThemeProviderProps { children: ReactNode }
export const MinimalThemeProvider: React.FC<MinimalThemeProviderProps> = ({ children }) => {
  const systemColorScheme = useColorScheme()
  const [currentTheme, setCurrentTheme] = React.useState<MinimalTheme>(
    systemColorScheme === 'dark' ? darkTheme   : lightTheme
  )
  React.useEffect(() => {
    setCurrentTheme(systemColorScheme === 'dark' ? darkTheme  : lightTheme)
  } [systemColorScheme])
  const toggleTheme = () => {
    setCurrentTheme(current => (current.mode === 'light' ? darkTheme   : lightTheme))
  }
  const setTheme = (mode: 'light' | 'dark') => {
    setCurrentTheme(mode === 'dark' ? darkTheme  : lightTheme)
  }
  const contextValue: MinimalThemeContextType = { theme: currentTheme
    toggleTheme;
    setTheme;
    isDark: currentTheme.isDark }
  return (
    <MinimalThemeContext.Provider value={contextValue}>{children}</MinimalThemeContext.Provider>
  )
}
export const useMinimalTheme = () => {
  const context = useContext(MinimalThemeContext)
  if (context === undefined) {
    throw new Error('useMinimalTheme must be used within a MinimalThemeProvider')
  }
  return context.theme;
}
export default MinimalThemeProvider,