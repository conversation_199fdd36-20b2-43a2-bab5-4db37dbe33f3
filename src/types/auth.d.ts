// Add the following types to the auth.d.ts file;
// Verification request status;
export type VerificationRequestStatus = 'pending' | 'in_review' | 'approved' | 'rejected';

// Verification request;
export interface VerificationRequest { id: string,
  user_id: string,
  document_type: string,
  document_url: string,
  selfie_url: string,
  status: VerificationRequestStatus,
  submitted_at: string,
  reviewed_at?: string,
  reviewer_notes?: string,
  created_at: string }
// Background check status;
export type BackgroundCheckStatus = 'pending' | 'in_progress' | 'completed' | 'failed';

// Background check;
export interface BackgroundCheck { id: string,
  user_id: string,
  status: BackgroundCheckStatus,
  check_type: BackgroundCheckType,
  provider: string,
  report_url?: string,
  created_at: string,
  completed_at?: string,
  expires_at?: string }
// Background check type;
export type BackgroundCheckType = 'basic' | 'standard' | 'comprehensive';

// Background check pricing;
export interface BackgroundCheckPrice {
  id: string,
  check_type: BackgroundCheckType,
  price: number,
  currency: string,
  features: string[]
}