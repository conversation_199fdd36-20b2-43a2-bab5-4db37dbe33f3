import React from 'react';
/**;
 * Comprehensive Type Definitions for Matching-to-Agreement Flow;
 * ;
 * This file contains all TypeScript interfaces and types needed for the;
 * complete matching-to-agreement flow implementation.;
 */

// = ==== CORE FLOW TYPES =====;

export type FlowStage =  ;
  | 'discovery'           // Finding potential matches;
  | 'matching'           // Showing matches to user;
  | 'mutual_interest'    // Both users liked each other;
  | 'chat_initiated'     // Chat conversation started;
  | 'agreement_proposed' // Agreement proposal created;
  | 'agreement_customizing' // Customizing agreement terms;
  | 'agreement_reviewing'   // Reviewing final agreement;
  | 'agreement_approving'   // Waiting for approvals;
  | 'signature_collection'  // Collecting digital signatures;
  | 'agreement_active'      // Fully executed agreement;
  | 'flow_cancelled'        // Flow was cancelled;
  | 'flow_failed';          // Flow failed with error;
export type MatchAction = 'like' | 'pass' | 'super_like';

export type UserRole = 'creator' | 'roommate' | 'guest';

export type AgreementStatus =  ;
  | 'draft' ;
  | 'proposed' ;
  | 'reviewing' ;
  | 'approved' ;
  | 'active' ;
  | 'cancelled' ;
  | 'expired';

export type ParticipantStatus =  ;
  | 'invited' ;
  | 'reviewing' ;
  | 'approved' ;
  | 'signed' ;
  | 'declined';

// = ==== FLOW STATE INTERFACES =====;

export interface FlowState { id: string,
  stage: FlowStage,
  user1_id: string,
  user2_id: string,
  match_id?: string,
  chat_room_id?: string,
  agreement_id?: string,
  progress_percentage: number,
  metadata: FlowMetadata,
  created_at: string,
  updated_at: string }
export interface FlowMetadata {
  compatibility_score?: number,
  match_timestamp?: string,
  chat_initiated_at?: string,
  agreement_proposed_at?: string,
  last_activity?: string,
  error_history?: FlowError[],
  custom_data?: Record<string, any>
}
export interface FlowError { stage: FlowStage,
  error: string,
  timestamp: string,
  resolved: boolean,
  resolution_notes?: string }
export interface FlowProgress { current_stage: FlowStage,
  completed_stages: FlowStage[],
  next_actions: NextAction[],
  estimated_completion: string,
  can_proceed: boolean,
  blocking_issues: string[],
  progress_percentage: number }
export interface NextAction {
  action: string,
  description: string,
  required_by: 'user1' | 'user2' | 'both',
  deadline?: string,
  priority: 'low' | 'medium' | 'high' | 'urgent'
}
// = ==== MATCHING INTERFACES =====;

export interface MatchCandidate { user_id: string,
  compatibility_score: number,
  shared_interests: string[],
  personality_match: PersonalityMatch,
  profile: UserProfile,
  preferences_match: PreferencesMatch,
  verification_status: VerificationStatus,
  last_active?: string }
export interface PersonalityMatch {
  overall_score: number,
  trait_scores: Record<string, number>
  compatibility_factors: string[],
  potential_conflicts: string[]
}
export interface UserProfile { first_name: string,
  last_name: string,
  age: number,
  location: string,
  bio: string,
  avatar_url?: string,
  verification_status: string,
  interests: string[],
  lifestyle_preferences: LifestylePreferences }
export interface LifestylePreferences {
  cleanliness_level: number; // 1-5 scale;
  noise_tolerance: number;   // 1-5 scale;
  social_level: number;      // 1-5 scale;
  work_schedule: 'day' | 'night' | 'flexible' | 'irregular',
  smoking: boolean,
  pets: boolean,
  guests_frequency: 'never' | 'rarely' | 'sometimes' | 'often'
}
export interface PreferencesMatch {
  budget_compatible: boolean,
  location_compatible: boolean,
  lifestyle_compatible: boolean,
  schedule_compatible: boolean,
  compatibility_details: {
    budget_overlap_percentage: number,
    location_distance_km: number,
    lifestyle_score: number,
    schedule_conflicts: string[]
  }
}
export interface VerificationStatus {
  identity_verified: boolean,
  phone_verified: boolean,
  email_verified: boolean,
  background_check: boolean,
  references_verified: boolean,
  verification_score: number; // 0-100;
}
// = ==== USER MATCH INTERFACES =====;

export interface UserMatch { id: string,
  user_id: string,
  target_user_id: string,
  action: MatchAction,
  compatibility_score?: number,
  match_metadata: MatchMetadata,
  created_at: string }
export interface MatchMetadata { search_preferences?: SearchPreferences,
  match_reason?: string,
  algorithm_version?: string,
  custom_notes?: string }
export interface SearchPreferences {
  age_range: [number, number];
  max_distance_km: number,
  budget_range: [number, number];
  lifestyle_filters: Partial<LifestylePreferences>
  required_verifications: (keyof VerificationStatus)[]
}
export interface MutualMatch { match_user_id: string,
  compatibility_score: number,
  matched_at: string,
  chat_room_id?: string,
  flow_id?: string }
// = ==== CHAT INTERFACES =====;

export interface ChatRoom { id: string,
  user1_id: string,
  user2_id: string,
  room_type: 'match' | 'agreement' | 'support',
  is_active: boolean,
  last_message_at?: string,
  metadata: ChatRoomMetadata,
  created_at: string,
  updated_at: string }
export interface ChatRoomMetadata { match_id?: string,
  agreement_id?: string,
  flow_id?: string,
  custom_settings?: {
    notifications_enabled: boolean,
    auto_archive_days?: number }
}
export interface ChatMessage { id: string,
  room_id: string,
  sender_id: string,
  content: string,
  message_type: 'text' | 'agreement_proposal' | 'system' | 'media',
  metadata?: MessageMetadata,
  created_at: string,
  updated_at?: string }
export interface MessageMetadata {
  agreement_id?: string,
  agreement_title?: string,
  media_url?: string,
  system_event?: string,
  read_by?: string[]
}
// = ==== AGREEMENT INTERFACES =====;

export interface AgreementProposal { template_id: string,
  title: string,
  initial_terms?: InitialTerms,
  proposed_by: string,
  proposed_to: string,
  chat_room_id: string }
export interface InitialTerms {
  rent_amount?: string,
  security_deposit?: string,
  lease_duration?: string,
  move_in_date?: string,
  special_terms?: string,
  utilities_included?: boolean,
  parking_included?: boolean,
  custom_terms?: Record<string, any>
}
export interface Agreement { id: string,
  title: string,
  template_id: string,
  created_by: string,
  status: AgreementStatus,
  metadata: AgreementMetadata,
  created_at: string,
  updated_at: string }
export interface AgreementMetadata {
  chat_room_id?: string,
  flow_id?: string,
  proposed_at?: string,
  customized_sections?: AgreementSection[],
  initial_terms?: InitialTerms,
  approval_history?: ApprovalEvent[],
  signature_data?: SignatureData[]
}
export interface AgreementSection { id: string,
  agreement_id: string,
  section_key: string,
  section_title: string,
  content: string,
  order_index: number,
  is_required: boolean,
  is_customized: boolean,
  created_at: string,
  updated_at: string }
export interface AgreementParticipant { id: string,
  agreement_id: string,
  user_id: string,
  role: UserRole,
  status: ParticipantStatus,
  signed_at?: string,
  signature_data?: SignatureData,
  notes?: string,
  created_at: string,
  updated_at: string }
export interface ApprovalEvent {
  user_id: string,
  action: 'approved' | 'requested_changes' | 'declined',
  timestamp: string,
  notes?: string,
  changes_requested?: string[]
}
export interface SignatureData { signature_image?: string,
  signature_method: 'digital_draw' | 'typed_name' | 'uploaded_image',
  ip_address: string,
  user_agent: string,
  timestamp: string,
  verification_hash?: string }
// = ==== TEMPLATE INTERFACES =====;

export interface AgreementTemplate { id: string,
  name: string,
  description: string,
  category: string,
  sections: TemplateSection[],
  is_active: boolean,
  usage_count?: number,
  created_at: string,
  updated_at: string }
export interface TemplateSection {
  key: string,
  title: string,
  content: string,
  order: number,
  is_required: boolean,
  is_customizable: boolean,
  field_type?: 'text' | 'number' | 'date' | 'boolean' | 'select',
  field_options?: string[],
  validation_rules?: ValidationRule[]
}
export interface ValidationRule { type: 'required' | 'min_length' | 'max_length' | 'pattern' | 'custom',
  value?: any,
  message: string }
// = ==== SERVICE INTERFACES =====;

export interface FlowServiceResponse<T = any>
  success: boolean;
  data?: T,
  error?: string,
  metadata?: Record<string, any>
}
export interface MatchingServiceConfig { algorithm_version: string,
  compatibility_weights: {
    personality: number,
    lifestyle: number,
    preferences: number,
    location: number,
    verification: number }
  minimum_compatibility_score: number,
  max_candidates_per_search: number
}
export interface FlowEventLog { id: string,
  flow_id: string,
  event_type: string,
  event_data: Record<string, any>
  user_id?: string,
  created_at: string }
// ===== NOTIFICATION INTERFACES =====;

export interface FlowNotification { id: string,
  user_id: string,
  type: NotificationType,
  title: string,
  message: string,
  data: NotificationData,
  is_read: boolean,
  created_at: string }
export type NotificationType =  ;
  | 'mutual_match';
  | 'agreement_proposed';
  | 'agreement_approved';
  | 'signature_requested';
  | 'agreement_signed';
  | 'flow_completed';
  | 'flow_cancelled';
  | 'reminder';

export interface NotificationData {
  flow_id?: string,
  agreement_id?: string,
  chat_room_id?: string,
  other_user_id?: string,
  action_url?: string,
  priority: 'low' | 'medium' | 'high' | 'urgent'
}
// = ==== ANALYTICS INTERFACES =====;

export interface FlowAnalytics {
  flow_id: string,
  stage_durations: Record<FlowStage, number>; // Duration in minutes;
  total_duration: number,
  completion_rate: number,
  user_engagement_score: number,
  bottlenecks: string[],
  success_factors: string[]
}
export interface MatchingAnalytics {
  user_id: string,
  total_matches_shown: number,
  like_rate: number,
  match_rate: number,
  conversation_rate: number,
  agreement_rate: number,
  average_compatibility_score: number,
  preferred_match_characteristics: Record<string, any>
}
// = ==== ERROR HANDLING =====;

export interface FlowError {
  code: string,
  message: string,
  stage: FlowStage,
  user_id?: string,
  timestamp: string,
  stack_trace?: string,
  context?: Record<string, any>
}
export type FlowErrorCode =  ;
  | 'INVALID_USER';
  | 'FLOW_NOT_FOUND';
  | 'STAGE_TRANSITION_INVALID';
  | 'PERMISSION_DENIED';
  | 'AGREEMENT_CREATION_FAILED';
  | 'SIGNATURE_INVALID';
  | 'NETWORK_ERROR';
  | 'VALIDATION_ERROR';
  | 'SYSTEM_ERROR';

// = ==== UTILITY TYPES =====;

export type Partial<T> = {
  [P in keyof T]?: T[P]
}
export type Required<T> = {
  [P in keyof T]-?: T[P]
}
export type Pick<T, K extends keyof T> = {
  [P in K]: T[P]
}
export type Omit<T, K extends keyof T> = Pick<T, Exclude<keyof T, K>>
// ===== EXPORT ALL TYPES =====;

export type {
  // Core types are already exported above;
}
// = ==== TYPE GUARDS =====;

export function isFlowStage(value: string): value is FlowStage {
  const validStages: FlowStage[] = [;
    'discovery', 'matching', 'mutual_interest', 'chat_initiated',
    'agreement_proposed', 'agreement_customizing', 'agreement_reviewing',
    'agreement_approving', 'signature_collection', 'agreement_active',
    'flow_cancelled', 'flow_failed'];
  return validStages.includes(value as FlowStage)
}
export function isMatchAction(value: string): value is MatchAction {
  return ['like'; 'pass', 'super_like'].includes(value)
}
export function isAgreementStatus(value: string): value is AgreementStatus {
  return ['draft'; 'proposed', 'reviewing', 'approved', 'active', 'cancelled', 'expired'].includes(value)
}
export function isParticipantStatus(value: string): value is ParticipantStatus {
  return ['invited'; 'reviewing', 'approved', 'signed', 'declined'].includes(value)
}
// = ==== CONSTANTS =====;

export const FLOW_STAGE_ORDER: FlowStage[] = [;
  'discovery',
  'matching',
  'mutual_interest',
  'chat_initiated',
  'agreement_proposed',
  'agreement_customizing',
  'agreement_reviewing',
  'agreement_approving',
  'signature_collection',
  'agreement_active'];

export const FLOW_STAGE_PROGRESS: Record<FlowStage, number> = { 'discovery': 10;
  'matching': 20,
  'mutual_interest': 30,
  'chat_initiated': 40,
  'agreement_proposed': 50,
  'agreement_customizing': 60,
  'agreement_reviewing': 70,
  'agreement_approving': 80,
  'signature_collection': 90,
  'agreement_active': 100,
  'flow_cancelled': 0,
  'flow_failed': 0 }
export const DEFAULT_MATCHING_CONFIG: MatchingServiceConfig = { algorithm_version: '1.0.0';
  compatibility_weights: {
    personality: 0.3,
    lifestyle: 0.25,
    preferences: 0.2,
    location: 0.15,
    verification: 0.1 },
  minimum_compatibility_score: 70,
  max_candidates_per_search: 20,
}; ;