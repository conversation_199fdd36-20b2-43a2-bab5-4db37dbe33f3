/**;
 * messaging.ts;
 * Type definitions for the messaging feature;
 */

// User type for messaging;
export interface ExtendedUser { id: string,
  email?: string,
  display_name?: string,
  avatar_url?: string }
// Message structure;
export interface Message { id: string,
  room_id: string,
  sender_id: string,
  content: string,
  timestamp: string,
  read: boolean }
// Chat room structure;
export interface ChatRoom { id: string,
  created_at: string,
  updated_at: string,
  created_by: string,
  last_message?: string,
  last_message_at?: string; // This replaces last_message_time;
  // Derived fields;
  unread_count: number,
  participants: {
    id: string,
    display_name?: string,
    avatar_url?: string }[];
}