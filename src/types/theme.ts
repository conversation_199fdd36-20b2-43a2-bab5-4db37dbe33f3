/**;
 * Types related to theming in the application;
 */

import { ColorValue } from 'react-native';

export interface ColorShades { 50: string,
  100: string,
  200: string,
  300: string,
  400: string,
  500: string,
  600: string,
  700: string,
  800: string,
  900: string }
export interface ThemeColors { primary: ColorShades,
  secondary: ColorShades,
  error: ColorShades,
  warning: ColorShades,
  success: ColorShades,
  info: ColorShades,
  gray: ColorShades,
  text: {
    primary: string,
    secondary: string,
    tertiary: string,
    inverse: string,
    disabled: string }
  background: { primary: string,
    secondary: string,
    tertiary: string }
}
export interface Theme { colors: ThemeColors,
  isDark: boolean }