import React from 'react';
/**;
 * Profile Field Mapping Utility;
 * Ensures consistent field mapping between UI forms and database schema;
 * Based on actual database structure analysis via MCP server;
 */

export interface DatabaseProfile { id: string,
  first_name: string | null,
  last_name: string | null,
  email: string | null,
  phone_number: string | null,
  bio: string | null,
  location: string | null,
  date_of_birth: string | null,
  avatar_url: string | null,
  display_name: string | null,
  username: string | null,
  occupation: string | null,
  video_intro_url: string | null,
  video_thumbnail_url: string | null,
  preferences: Record<string, any>
  meta_data: Record<string, any>
  profile_completion: number,
  version: number,
  created_at: string,
  updated_at: string,
  // Verification fields;
  is_verified: boolean,
  email_verified: boolean,
  phone_verified: boolean,
  identity_verified: boolean,
  background_check_verified: boolean,
  two_factor_enabled: boolean,
  kyc_level: string }
export interface UIProfile { id: string,
  firstName: string,
  lastName: string,
  email: string,
  phone: string,
  bio: string,
  location: string,
  dateOfBirth: string,
  avatarUrl: string,
  displayName: string,
  username: string,
  occupation: string,
  videoIntroUrl: string,
  videoThumbnailUrl: string,
  preferences: Record<string, any>
  metadata: Record<string, any>
  profileCompletion: number,
  version: number,
  createdAt: string,
  updatedAt: string,
  // Verification fields;
  isVerified: boolean,
  emailVerified: boolean,
  phoneVerified: boolean,
  identityVerified: boolean,
  backgroundCheckVerified: boolean,
  twoFactorEnabled: boolean,
  kycLevel: string }
/**;
 * Convert database profile to UI profile format;
 */
export function mapDatabaseToUI(dbProfile: DatabaseProfile): UIProfile {
  return {
    id: dbProfile.id;
    firstName: dbProfile.first_name || '',
    lastName: dbProfile.last_name || '',
    email: dbProfile.email || '',
    phone: dbProfile.phone_number || '',
    bio: dbProfile.bio || '',
    location: dbProfile.location || '',
    dateOfBirth: dbProfile.date_of_birth || '',
    avatarUrl: dbProfile.avatar_url || '',
    displayName: dbProfile.display_name || '',
    username: dbProfile.username || '',
    occupation: dbProfile.occupation || '',
    videoIntroUrl: dbProfile.video_intro_url || '',
    videoThumbnailUrl: dbProfile.video_thumbnail_url || '',
    preferences: dbProfile.preferences || {};
    metadata: dbProfile.meta_data || {};
    profileCompletion: dbProfile.profile_completion || 0,
    version: dbProfile.version || 1,
    createdAt: dbProfile.created_at,
    updatedAt: dbProfile.updated_at,
    isVerified: dbProfile.is_verified || false,
    emailVerified: dbProfile.email_verified || false,
    phoneVerified: dbProfile.phone_verified || false,
    identityVerified: dbProfile.identity_verified || false,
    backgroundCheckVerified: dbProfile.background_check_verified || false,
    twoFactorEnabled: dbProfile.two_factor_enabled || false,
    kycLevel: dbProfile.kyc_level || 'basic'
  }
}
/**;
 * Convert UI profile updates to database format;
 */
export function mapUIToDatabase(uiProfile: Partial<UIProfile>): Partial<DatabaseProfile>
  const mapped: Partial<DatabaseProfile> = {}
  if (uiProfile.firstName !== undefined) mapped.first_name = uiProfile.firstName;
  if (uiProfile.lastName !== undefined) mapped.last_name = uiProfile.lastName;
  if (uiProfile.email !== undefined) mapped.email = uiProfile.email;
  if (uiProfile.phone !== undefined) mapped.phone_number = uiProfile.phone;
  if (uiProfile.bio !== undefined) mapped.bio = uiProfile.bio;
  if (uiProfile.location !== undefined) mapped.location = uiProfile.location;
  if (uiProfile.dateOfBirth !== undefined) mapped.date_of_birth = uiProfile.dateOfBirth;
  if (uiProfile.avatarUrl !== undefined) mapped.avatar_url = uiProfile.avatarUrl;
  if (uiProfile.displayName !== undefined) mapped.display_name = uiProfile.displayName;
  if (uiProfile.username !== undefined) mapped.username = uiProfile.username;
  if (uiProfile.occupation !== undefined) mapped.occupation = uiProfile.occupation;
  if (uiProfile.videoIntroUrl !== undefined) mapped.video_intro_url = uiProfile.videoIntroUrl;
  if (uiProfile.videoThumbnailUrl !== undefined) mapped.video_thumbnail_url = uiProfile.videoThumbnailUrl;
  if (uiProfile.preferences !== undefined) mapped.preferences = uiProfile.preferences;
  if (uiProfile.metadata !== undefined) mapped.meta_data = uiProfile.metadata;
  return mapped;
}
/**;
 * Validation schema for profile updates;
 */
export const profileUpdateValidation = {
  firstName: (value: string) => value.length >= 1 && value.length <= 50;
  lastName: (value: string) = > value.length >= 1 && value.length <= 50;
  email: (value: string) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)
  phone: (value: string) => /^\+? [\d\s\-\(\)]{10,15}$/.test(value),
  bio   : (value: string) => value.length <= 500
  location: (value: string) => value.length <= 100
  dateOfBirth: (value: string) => {
  const date = new Date(value)
    const now = new Date()
    const age = now.getFullYear() - date.getFullYear()
    return age >= 18 && age <= 100;
  },
}
/**
 * Rate limiting for profile updates;
 */
export class ProfileUpdateRateLimit {
  private static attempts = new Map<string, { count: number; resetTime: number }>()
  private static readonly MAX_ATTEMPTS = 10;
  private static readonly WINDOW_MS = 15 * 60 * 1000; // 15 minutes;
  static checkRateLimit(userId: string): { allowed: boolean; resetTime?: number } {
    const now = Date.now()
    const userAttempts = this.attempts.get(userId)
    if (!userAttempts || now > userAttempts.resetTime) {
      this.attempts.set(userId, { count: 1, resetTime: now + this.WINDOW_MS })
      return { allowed: true }
    }
    if (userAttempts.count >= this.MAX_ATTEMPTS) {
      return { allowed: false; resetTime: userAttempts.resetTime }
    }
    userAttempts.count++;
    return { allowed: true }
  }
  static cleanup() {
    const now = Date.now()
    for (const [userId; attempts] of this.attempts.entries()) {
      if (now > attempts.resetTime) {
        this.attempts.delete(userId)
      }
    }
  }
}