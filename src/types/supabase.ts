export type Json = string | number | boolean | null | { [key: string]: Json | undefined } | Json[];

// Define enum types that match our PostgreSQL enums;
export type BackgroundCheckStatusType = 'pending' | 'in_progress' | 'completed' | 'failed';
export type BackgroundCheckType = 'basic' | 'standard' | 'comprehensive';
export type PersonalityTraitCategoryType =;
  | 'openness';
  | 'conscientiousness';
  | 'extraversion';
  | 'agreeableness';
  | 'neuroticism';
  | 'lifestyle';
  | 'habits';
  | 'communication';
  | 'conflict_resolution';
export type UserRoleType = 'roommate_seeker' | 'property_owner' | 'service_provider' | 'admin';
export type VerificationStatusType = 'pending' | 'in_review' | 'verified' | 'rejected';
export type SubscriptionStatusType = 'active' | 'cancelled' | 'expired' | 'pending';
export type PaymentStatusType = 'pending' | 'completed' | 'failed' | 'refunded';
export type SplitPaymentStatusType = 'pending' | 'partially_paid' | 'completed' | 'cancelled';
export type SplitPaymentShareStatusType = 'pending' | 'completed' | 'failed' | 'disputed';
export type RecurringType = 'one_time' | 'weekly' | 'monthly' | 'custom' | null;
export interface Database { public: {
    Tables: {
      profiles: {
        Row: {
          id: string,
          username: string | null,
          display_name: string | null,
          avatar_url: string | null,
          bio: string | null,
          location: string | null,
          website: string | null,
          created_at: string | null,
          updated_at: string | null,
          followers_count: number | null,
          following_count: number | null,
          articles_count: number | null,
          comments_count: number | null,
          shares_count: number | null }
        Insert: { id: string,
          username?: string | null,
          display_name?: string | null,
          avatar_url?: string | null,
          bio?: string | null,
          location?: string | null,
          website?: string | null,
          created_at?: string | null,
          updated_at?: string | null,
          followers_count?: number | null,
          following_count?: number | null,
          articles_count?: number | null,
          comments_count?: number | null,
          shares_count?: number | null }
        Update: { id?: string,
          username?: string | null,
          display_name?: string | null,
          avatar_url?: string | null,
          bio?: string | null,
          location?: string | null,
          website?: string | null,
          created_at?: string | null,
          updated_at?: string | null,
          followers_count?: number | null,
          following_count?: number | null,
          articles_count?: number | null,
          comments_count?: number | null,
          shares_count?: number | null }
      }
      chat_rooms: { Row: {
          id: string,
          created_at: string,
          updated_at: string | null,
          name: string | null,
          last_message: string | null,
          last_message_time: string | null }
        Insert: { id?: string,
          created_at?: string,
          updated_at?: string | null,
          name?: string | null,
          last_message?: string | null,
          last_message_time?: string | null }
        Update: { id?: string,
          created_at?: string,
          updated_at?: string | null,
          name?: string | null,
          last_message?: string | null,
          last_message_time?: string | null }
      }
      chat_room_participants: { Row: {
          id: string,
          chat_room_id: string,
          profile_id: string,
          created_at: string }
        Insert: { id?: string,
          chat_room_id: string,
          profile_id: string,
          created_at?: string }
        Update: { id?: string,
          chat_room_id?: string,
          profile_id?: string,
          created_at?: string }
      }
      messages: { Row: {
          id: string,
          chat_room_id: string,
          sender_id: string,
          content: string,
          created_at: string,
          read: boolean }
        Insert: { id?: string,
          chat_room_id: string,
          sender_id: string,
          content: string,
          created_at?: string,
          read?: boolean }
        Update: { id?: string,
          chat_room_id?: string,
          sender_id?: string,
          content?: string,
          created_at?: string,
          read?: boolean }
      }
      rooms: { Row: {
          id: string,
          title: string,
          description: string | null,
          price: number,
          location: string,
          landlord_id: string,
          created_at: string,
          updated_at: string | null,
          is_available: boolean,
          room_type: string,
          bedrooms: number | null,
          bathrooms: number | null,
          furnished: boolean | null,
          pets_allowed: boolean | null,
          images: string[] | null }
        Insert: { id?: string,
          title: string,
          description?: string | null,
          price: number,
          location: string,
          landlord_id: string,
          created_at?: string,
          updated_at?: string | null,
          is_available?: boolean,
          room_type?: string | null,
          bedrooms?: number | null,
          bathrooms?: number | null,
          furnished?: boolean | null,
          pets_allowed?: boolean | null,
          images?: string[] | null }
        Update: { id?: string,
          title?: string,
          description?: string | null,
          price?: number,
          location?: string,
          landlord_id?: string,
          created_at?: string,
          updated_at?: string | null,
          is_available?: boolean,
          room_type?: string | null,
          bedrooms?: number | null,
          bathrooms?: number | null,
          furnished?: boolean | null,
          pets_allowed?: boolean | null,
          images?: string[] | null }
      }
      saved_rooms: { Row: {
          id: string,
          room_id: string,
          profile_id: string,
          created_at: string }
        Insert: { id?: string,
          room_id: string,
          profile_id: string,
          created_at?: string }
        Update: { id?: string,
          room_id?: string,
          profile_id?: string,
          created_at?: string }
      }
      housemate_profiles: { Row: {
          id: string,
          profile_id: string,
          age: number | null,
          gender: string | null,
          occupation: string | null,
          lifestyle: string[] | null,
          interests: string[] | null,
          budget: number | null,
          move_in_date: string | null,
          preferred_locations: string[] | null,
          about_me: string | null,
          created_at: string,
          updated_at: string | null }
        Insert: { id?: string,
          profile_id: string,
          age?: number | null,
          gender?: string | null,
          occupation?: string | null,
          lifestyle?: string[] | null,
          interests?: string[] | null,
          budget?: number | null,
          move_in_date?: string | null,
          preferred_locations?: string[] | null,
          about_me?: string | null,
          created_at?: string,
          updated_at?: string | null }
        Update: { id?: string,
          profile_id?: string,
          age?: number | null,
          gender?: string | null,
          occupation?: string | null,
          lifestyle?: string[] | null,
          interests?: string[] | null,
          budget?: number | null,
          move_in_date?: string | null,
          preferred_locations?: string[] | null,
          about_me?: string | null,
          created_at?: string,
          updated_at?: string | null }
      }
      services: { Row: {
          id: string,
          name: string,
          description: string,
          provider_id: string,
          category: string,
          price: number | null,
          contact_info: string,
          created_at: string,
          updated_at: string | null,
          images: string[] | null,
          is_available: boolean }
        Insert: { id?: string,
          name: string,
          description: string,
          provider_id: string,
          category: string,
          price?: number | null,
          contact_info: string,
          created_at?: string,
          updated_at?: string | null,
          images?: string[] | null,
          is_available?: boolean }
        Update: { id?: string,
          name?: string,
          description?: string,
          provider_id?: string,
          category?: string,
          price?: number | null,
          contact_info?: string,
          created_at?: string,
          updated_at?: string | null,
          images?: string[] | null,
          is_available?: boolean }
      }
      subscription_plans: { Row: {
          id: string,
          name: string,
          description: string | null,
          price: number,
          duration_days: number,
          features: Json | null,
          is_active: boolean,
          created_at: string,
          updated_at: string }
        Insert: { id?: string,
          name: string,
          description?: string | null,
          price: number,
          duration_days: number,
          features?: Json | null,
          is_active?: boolean,
          created_at?: string,
          updated_at?: string }
        Update: { id?: string,
          name?: string,
          description?: string | null,
          price?: number,
          duration_days?: number,
          features?: Json | null,
          is_active?: boolean,
          created_at?: string,
          updated_at?: string }
      }
      subscriptions: { Row: {
          id: string,
          user_id: string,
          plan_id: string,
          status: SubscriptionStatusType,
          start_date: string,
          end_date: string,
          is_auto_renew: boolean,
          external_subscription_id: string | null,
          metadata: Json | null,
          created_at: string,
          updated_at: string }
        Insert: { id?: string,
          user_id: string,
          plan_id: string,
          status?: SubscriptionStatusType,
          start_date?: string,
          end_date: string,
          is_auto_renew?: boolean,
          external_subscription_id?: string | null,
          metadata?: Json | null,
          created_at?: string,
          updated_at?: string }
        Update: { id?: string,
          user_id?: string,
          plan_id?: string,
          status?: SubscriptionStatusType,
          start_date?: string,
          end_date?: string,
          is_auto_renew?: boolean,
          external_subscription_id?: string | null,
          metadata?: Json | null,
          created_at?: string,
          updated_at?: string }
      }
      payments: { Row: {
          id: string,
          user_id: string,
          subscription_id: string | null,
          amount: number,
          currency: string,
          status: PaymentStatusType,
          payment_method: string,
          external_payment_id: string | null,
          receipt_url: string | null,
          metadata: Json | null,
          created_at: string,
          updated_at: string }
        Insert: { id?: string,
          user_id: string,
          subscription_id?: string | null,
          amount: number,
          currency?: string,
          status: PaymentStatusType,
          payment_method: string,
          external_payment_id?: string | null,
          receipt_url?: string | null,
          metadata?: Json | null,
          created_at?: string,
          updated_at?: string }
        Update: { id?: string,
          user_id?: string,
          subscription_id?: string | null,
          amount?: number,
          currency?: string,
          status?: PaymentStatusType,
          payment_method?: string,
          external_payment_id?: string | null,
          receipt_url?: string | null,
          metadata?: Json | null,
          created_at?: string,
          updated_at?: string }
      }
      split_payments: { Row: {
          id: string,
          total_amount: number,
          currency: string,
          title: string,
          description: string | null,
          status: SplitPaymentStatusType,
          due_date: string | null,
          recurring_type: RecurringType,
          recurring_interval: number | null,
          next_payment_date: string | null,
          creator_id: string,
          metadata: Json | null,
          created_at: string,
          updated_at: string }
        Insert: { id?: string,
          total_amount: number,
          currency: string,
          title: string,
          description?: string | null,
          status?: SplitPaymentStatusType,
          due_date?: string | null,
          recurring_type?: RecurringType,
          recurring_interval?: number | null,
          next_payment_date?: string | null,
          creator_id: string,
          metadata?: Json | null,
          created_at?: string,
          updated_at?: string }
        Update: { id?: string,
          total_amount?: number,
          currency?: string,
          title?: string,
          description?: string | null,
          status?: SplitPaymentStatusType,
          due_date?: string | null,
          recurring_type?: RecurringType,
          recurring_interval?: number | null,
          next_payment_date?: string | null,
          creator_id?: string,
          metadata?: Json | null,
          created_at?: string,
          updated_at?: string }
      }
      split_payment_shares: { Row: {
          id: string,
          split_payment_id: string,
          user_id: string,
          amount: number,
          payment_id: string | null,
          status: SplitPaymentShareStatusType,
          notes: string | null,
          reminder_sent: boolean,
          created_at: string,
          updated_at: string }
        Insert: { id?: string,
          split_payment_id: string,
          user_id: string,
          amount: number,
          payment_id?: string | null,
          status?: SplitPaymentShareStatusType,
          notes?: string | null,
          reminder_sent?: boolean,
          created_at?: string,
          updated_at?: string }
        Update: { id?: string,
          split_payment_id?: string,
          user_id?: string,
          amount?: number,
          payment_id?: string | null,
          status?: SplitPaymentShareStatusType,
          notes?: string | null,
          reminder_sent?: boolean,
          created_at?: string,
          updated_at?: string }
      }
      payment_methods: { Row: {
          id: string,
          user_id: string,
          type: string,
          provider: string,
          account_details: Json,
          is_default: boolean,
          created_at: string,
          updated_at: string }
        Insert: { id?: string,
          user_id: string,
          type: string,
          provider: string,
          account_details: Json,
          is_default?: boolean,
          created_at?: string,
          updated_at?: string }
        Update: { id?: string,
          user_id?: string,
          type?: string,
          provider?: string,
          account_details?: Json,
          is_default?: boolean,
          created_at?: string,
          updated_at?: string }
      }
      payment_refunds: { Row: {
          id: string,
          payment_id: string,
          amount: number,
          reason: string | null,
          status: string,
          refunded_by: string,
          created_at: string,
          updated_at: string }
        Insert: { id?: string,
          payment_id: string,
          amount: number,
          reason?: string | null,
          status: string,
          refunded_by: string,
          created_at?: string,
          updated_at?: string }
        Update: { id?: string,
          payment_id?: string,
          amount?: number,
          reason?: string | null,
          status?: string,
          refunded_by?: string,
          created_at?: string,
          updated_at?: string }
      }
    }
    Views: { [_ in never]: never }
    Functions: { [_ in never]: never }
    Enums: { background_check_status: BackgroundCheckStatusType,
      background_check_type: BackgroundCheckType,
      personality_trait_category: PersonalityTraitCategoryType,
      user_role: UserRoleType,
      verification_status: VerificationStatusType }
  }
}
// Export specific types to be used throughout the application;
export type Tables = Database['public']['Tables'];
export type SubscriptionPlan = Tables['subscription_plans']['Row'];
export type Subscription = Tables['subscriptions']['Row'];
export type Payment = Tables['payments']['Row'];
export type SplitPayment = Tables['split_payments']['Row'];
export type SplitPaymentShare = Tables['split_payment_shares']['Row'];
export type PaymentMethod = Tables['payment_methods']['Row'];
export type PaymentRefund = Tables['payment_refunds']['Row'];

// Export type for database enums;
export type Enums = Database['public']['Enums'];
