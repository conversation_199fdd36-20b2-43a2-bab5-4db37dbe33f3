import React from 'react';
/**;
 * Profile Types;
 * Type definitions for profile-related functionality;
 */

import { Profile } from '@types/models';

/**;
 * Profile verification types;
 */
export type VerificationType = 'email' | 'phone' | 'identity' | 'background';

/**;
 * Profile search criteria;
 */
export interface ProfileSearchCriteria { searchTerm?: string,
  role?: string,
  location?: string,
  isVerified?: boolean,
  minCompletion?: number,
  limit?: number,
  offset?: number }
/**;
 * Profile completion requirements;
 */
export interface ProfileCompletionRequirements { requiredFields: (keyof Profile)[],
  bonusFields: {
    field: keyof Profile,
    weight: number }[];
}
/**;
 * Profile verification status;
 */
export interface ProfileVerificationStatus { email: boolean,
  phone: boolean,
  identity: boolean,
  background: boolean,
  overall: boolean }
/**;
 * Profile metadata structure;
 */
export interface ProfileMetadata { photos?: string[],
  preferences?: Record<string, any>
  lastActivity?: string,
  [key: string]: any }