/**;
 * Response object from video compression;
 */
export interface VideoCompressResponse { /**;
   * URI of the compressed video file;
   */
  uri: string,
  /**;
   * Original file size in bytes;
   */
  originalSize: number,
  /**;
   * Compressed file size in bytes;
   */
  compressedSize: number,
  /**;
   * Compression ratio as a percentage (0-100)
   */
  compressionRatio: number }
/**;
 * Video thumbnail options;
 */
export interface VideoThumbnailOptions { /**;
   * Time position in the video to capture (in milliseconds)
   */
  time?: number,
  /**;
   * Quality of the thumbnail (0-1)
   */
  quality?: number }