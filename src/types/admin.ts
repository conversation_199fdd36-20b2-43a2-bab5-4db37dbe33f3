import React from 'react';
/**;
 * Admin Types and Interfaces for WeRoomies Admin System;
 * ;
 * Comprehensive type definitions for admin functionality including:  ,
 * - User management;
 * - Content moderation;
 * - Analytics and reporting;
 * - Emergency response;
 * - Financial management;
 */

import type { Profile } from './models';

// = ====================================================;
// CORE ADMIN TYPES;
// = ====================================================;

export interface AdminUser extends Profile { admin_permissions: AdminPermission[],
  admin_role: AdminRole,
  last_admin_action: string | null,
  admin_notes: string | null,
  created_by_admin: string | null,
  admin_access_granted_at: string | null }
export type AdminRole =  ;
  | 'super_admin' ;
  | 'admin' ;
  | 'moderator' ;
  | 'support_agent' ;
  | 'analyst';

export type AdminPermission =  ;
  | 'manage_users';
  | 'manage_content';
  | 'manage_finances';
  | 'manage_providers';
  | 'view_analytics';
  | 'emergency_response';
  | 'system_settings';
  | 'audit_logs';

// = ====================================================;
// USER MANAGEMENT TYPES;
// = ====================================================;

export interface AdminUserListItem { id: string,
  first_name: string | null,
  last_name: string | null,
  email: string,
  role: string,
  is_verified: boolean,
  is_active: boolean,
  profile_completion: number,
  created_at: string,
  updated_at: string,
  last_login: string | null,
  suspension_status: UserSuspensionStatus | null,
  verification_badges: VerificationBadge[],
  total_rooms: number,
  total_matches: number,
  total_bookings: number }
export interface UserSuspensionStatus {
  is_suspended: boolean,
  suspended_at: string | null,
  suspended_by: string | null,
  suspension_reason: string | null,
  suspension_expires_at: string | null,
  suspension_type: 'temporary' | 'permanent' | 'warning'
}
export interface VerificationBadge { type: 'email' | 'phone' | 'id' | 'social_media' | 'background_check',
  verified: boolean,
  verified_at: string | null,
  verified_by: string | null }
export interface UserActivityTimeline { id: string,
  user_id: string,
  activity_type: UserActivityType,
  activity_description: string,
  metadata: Record<string, any>
  created_at: string,
  ip_address: string | null,
  user_agent: string | null }
export type UserActivityType =  ;
  | 'login';
  | 'logout';
  | 'profile_update';
  | 'room_created';
  | 'room_updated';
  | 'match_created';
  | 'message_sent';
  | 'booking_created';
  | 'payment_made';
  | 'verification_submitted';
  | 'report_submitted';
  | 'suspension_applied';
  | 'account_deleted';

export interface UserSearchFilters { search_query?: string,
  role?: string,
  verification_status?: 'verified' | 'unverified' | 'pending',
  account_status?: 'active' | 'suspended' | 'deleted',
  registration_date_from?: string,
  registration_date_to?: string,
  last_login_from?: string,
  last_login_to?: string,
  profile_completion_min?: number,
  profile_completion_max?: number,
  has_rooms?: boolean,
  has_matches?: boolean,
  has_bookings?: boolean }
export interface BulkUserAction { action_type: 'suspend' | 'activate' | 'verify' | 'delete' | 'send_notification',
  user_ids: string[],
  reason?: string,
  duration?: number; // for temporary suspensions;
  notification_message?: string,
  performed_by: string }
// = ====================================================;
// CONTENT MODERATION TYPES;
// = ====================================================;

export interface ModerationItem { id: string,
  content_type: 'room_listing' | 'user_profile' | 'message' | 'review' | 'image',
  content_id: string,
  reported_by: string | null,
  report_reason: string | null,
  moderation_status: ModerationStatus,
  priority: ModerationPriority,
  flagged_content: any,
  moderator_notes: string | null,
  moderated_by: string | null,
  moderated_at: string | null,
  created_at: string,
  auto_flagged: boolean,
  ai_confidence_score: number | null }
export type ModerationStatus =  ;
  | 'pending';
  | 'approved';
  | 'rejected';
  | 'requires_review';
  | 'escalated';

export type ModerationPriority =  ;
  | 'low';
  | 'medium';
  | 'high';
  | 'critical';

export interface ModerationAction { action_type: 'approve' | 'reject' | 'escalate' | 'request_changes',
  reason: string,
  notes?: string,
  notify_user: boolean }
// = ====================================================;
// ROOM MANAGEMENT TYPES;
// = ====================================================;

export interface AdminRoomListItem {
  id: string,
  title: string,
  description: string,
  price: number,
  location: string,
  owner_id: string,
  owner_name: string,
  status: RoomStatus,
  created_at: string,
  updated_at: string,
  views: number,
  inquiries: number,
  bookings: number,
  images_count: number,
  moderation_status: ModerationStatus,
  flagged_count: number,
  verification_status: 'verified' | 'unverified' | 'pending'
}
export type RoomStatus =  ;
  | 'available';
  | 'occupied';
  | 'pending';
  | 'suspended';
  | 'deleted';

export interface RoomModerationDetails { room_id: string,
  moderation_history: ModerationItem[],
  flag_reasons: string[],
  verification_documents: any[],
  admin_notes: string,
  performance_metrics: {
    view_rate: number,
    inquiry_rate: number,
    booking_rate: number,
    user_rating: number }
}
// = ====================================================;
// ANALYTICS & REPORTING TYPES;
// = ====================================================;

export interface AdminDashboardMetrics { users: {
    total: number,
    active_today: number,
    new_registrations_today: number,
    new_registrations_week: number,
    verification_pending: number,
    suspended: number }
  rooms: { total: number,
    available: number,
    pending_approval: number,
    flagged: number,
    new_today: number }
  matches: { total_today: number,
    total_week: number,
    success_rate: number,
    pending_review: number }
  financial: { revenue_today: number,
    revenue_week: number,
    revenue_month: number,
    pending_payouts: number,
    disputed_transactions: number }
  moderation: { pending_reviews: number,
    flagged_content: number,
    escalated_issues: number,
    auto_flagged_today: number }
  system: { active_sessions: number,
    server_health: 'healthy' | 'warning' | 'critical',
    database_performance: number,
    api_response_time: number }
}
export interface AnalyticsTimeRange {
  start_date: string,
  end_date: string,
  granularity: 'hour' | 'day' | 'week' | 'month'
}
export interface ReportConfig { report_type: ReportType,
  time_range: AnalyticsTimeRange,
  filters: Record<string, any>
  format: 'pdf' | 'csv' | 'json',
  recipients: string[],
  schedule?: ReportSchedule }
export type ReportType =  ;
  | 'user_activity';
  | 'financial_summary';
  | 'moderation_summary';
  | 'platform_health';
  | 'compliance_report';
  | 'custom_analytics';

export interface ReportSchedule { frequency: 'daily' | 'weekly' | 'monthly',
  day_of_week?: number,
  day_of_month?: number,
  time: string,
  timezone: string }
// = ====================================================;
// EMERGENCY RESPONSE TYPES;
// = ====================================================;

export interface EmergencyIncident { id: string,
  incident_type: IncidentType,
  severity: IncidentSeverity,
  title: string,
  description: string,
  affected_users: string[],
  affected_systems: string[],
  status: IncidentStatus,
  reported_by: string,
  assigned_to: string | null,
  created_at: string,
  resolved_at: string | null,
  resolution_notes: string | null,
  escalation_level: number }
export type IncidentType =  ;
  | 'security_breach';
  | 'data_leak';
  | 'system_outage';
  | 'payment_fraud';
  | 'user_safety';
  | 'content_violation';
  | 'legal_issue';

export type IncidentSeverity =  ;
  | 'low';
  | 'medium';
  | 'high';
  | 'critical';

export type IncidentStatus =  ;
  | 'open';
  | 'investigating';
  | 'mitigating';
  | 'resolved';
  | 'closed';

export interface EmergencyAction { action_type: EmergencyActionType,
  target_type: 'user' | 'room' | 'system' | 'platform',
  target_ids: string[],
  reason: string,
  duration?: number,
  notify_users: boolean,
  escalate_to_legal: boolean }
export type EmergencyActionType =  ;
  | 'emergency_suspend';
  | 'platform_lockdown';
  | 'content_takedown';
  | 'payment_freeze';
  | 'data_quarantine';
  | 'system_maintenance';
  | 'mass_notification';

// = ====================================================;
// FINANCIAL MANAGEMENT TYPES;
// = ====================================================;

export interface TransactionOverview { id: string,
  user_id: string,
  user_name: string,
  transaction_type: TransactionType,
  amount: number,
  currency: string,
  status: TransactionStatus,
  payment_method: string,
  created_at: string,
  processed_at: string | null,
  dispute_status: DisputeStatus | null,
  admin_notes: string | null }
export type TransactionType =  ;
  | 'room_booking';
  | 'service_payment';
  | 'subscription';
  | 'refund';
  | 'payout';
  | 'fee';

export type TransactionStatus =  ;
  | 'pending';
  | 'completed';
  | 'failed';
  | 'cancelled';
  | 'disputed';
  | 'refunded';

export type DisputeStatus =  ;
  | 'none';
  | 'pending';
  | 'investigating';
  | 'resolved';
  | 'escalated';

export interface PayoutRequest { id: string,
  provider_id: string,
  provider_name: string,
  amount: number,
  currency: string,
  status: PayoutStatus,
  requested_at: string,
  processed_at: string | null,
  payment_method: string,
  admin_notes: string | null }
export type PayoutStatus =  ;
  | 'pending';
  | 'approved';
  | 'rejected';
  | 'processed';
  | 'failed';

// = ====================================================;
// AUDIT & LOGGING TYPES;
// = ====================================================;

export interface AdminAuditLog { id: string,
  admin_user_id: string,
  admin_user_name: string,
  action_type: AdminActionType,
  target_type: string,
  target_id: string | null,
  action_description: string,
  metadata: Record<string, any>
  ip_address: string,
  user_agent: string,
  created_at: string,
  session_id: string }
export type AdminActionType =  ;
  | 'user_suspend';
  | 'user_activate';
  | 'user_delete';
  | 'user_verify';
  | 'content_approve';
  | 'content_reject';
  | 'room_suspend';
  | 'room_approve';
  | 'transaction_refund';
  | 'emergency_action';
  | 'system_setting_change';
  | 'report_generate';
  | 'bulk_action';

// = ====================================================;
// API RESPONSE TYPES;
// = ====================================================;

export interface AdminApiResponse<T>
  data: T | null,
  error: string | null,
  status: number,
  pagination?: { page: number,
    limit: number,
    total: number,
    total_pages: number }
  metadata?: Record<string, any>
}
export interface PaginationParams { page?: number,
  limit?: number,
  sort_by?: string,
  sort_order?: 'asc' | 'desc' }
// = ====================================================;
// NOTIFICATION TYPES;
// = ====================================================;

export interface AdminNotification { id: string,
  type: AdminNotificationType,
  title: string,
  message: string,
  priority: NotificationPriority,
  target_admin_roles: AdminRole[],
  metadata: Record<string, any>
  created_at: string,
  expires_at: string | null,
  is_read: boolean,
  read_at: string | null }
export type AdminNotificationType =  ;
  | 'user_report';
  | 'content_flagged';
  | 'system_alert';
  | 'security_incident';
  | 'financial_anomaly';
  | 'performance_warning';
  | 'compliance_issue';

export type NotificationPriority =  ;
  | 'low';
  | 'medium';
  | 'high';
  | 'urgent';

// = ====================================================;
// SETTINGS & CONFIGURATION TYPES;
// = ====================================================;

export interface AdminSettings { platform_settings: {
    maintenance_mode: boolean,
    registration_enabled: boolean,
    matching_enabled: boolean,
    payments_enabled: boolean,
    max_users_per_room: number,
    verification_required: boolean }
  moderation_settings: { auto_moderation_enabled: boolean,
    ai_confidence_threshold: number,
    escalation_threshold: number,
    review_timeout_hours: number }
  notification_settings: {
    admin_email_notifications: boolean,
    push_notifications: boolean,
    sms_notifications: boolean,
    notification_frequency: 'immediate' | 'hourly' | 'daily'
  }
  security_settings: {
    session_timeout_minutes: number,
    max_login_attempts: number,
    require_2fa: boolean,
    ip_whitelist: string[]
  }
}
export interface SystemHealth { status: 'healthy' | 'warning' | 'critical',
  components: {
    database: ComponentHealth,
    api: ComponentHealth,
    storage: ComponentHealth,
    cache: ComponentHealth,
    queue: ComponentHealth }
  last_updated: string
}
export interface ComponentHealth { status: 'healthy' | 'warning' | 'critical',
  response_time: number,
  error_rate: number,
  uptime: number,
  last_check: string }