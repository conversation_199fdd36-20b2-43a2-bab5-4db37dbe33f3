import React from 'react';
export type UserRole = 'roommate_seeker' | 'property_owner' | 'service_provider' | 'admin';

export interface UserProfile { id: string,
  role: UserR<PERSON>,
  first_name: string | null,
  last_name: string | null,
  date_of_birth: string | null,
  bio: string | null,
  occupation: string | null,
  phone_number: string | null,
  avatar_url: string | null,
  is_verified: boolean,
  background_check_verified?: boolean,
  profile_completion: number,
  preferences: Record<string, any>
  created_at: string,
  updated_at: string }
export interface VerificationRequest { id: string,
  user_id: string,
  status: 'pending' | 'in_review' | 'verified' | 'rejected',
  document_type: string,
  document_url: string,
  selfie_url: string,
  submitted_at: string,
  reviewed_at: string | null,
  reviewer_id: string | null,
  notes: string | null,
  created_at: string,
  updated_at: string }