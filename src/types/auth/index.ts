import React from 'react';
// Authentication domain type definitions;
export interface SignUpData { email: string,
  password: string,
  username: string,
  displayName?: string }
export interface SignInData { email: string,
  password: string }
// Possible auth statuses for detailed state tracking;
export type AuthStatus =;
  | 'initializing';
  | 'checking-session';
  | 'signing-in';
  | 'signing-up';
  | 'signing-out';
  | 'authenticated';
  | 'unauthenticated';
  | 'pending-verification';
  | 'resetting-password';
  | 'updating-password';
  | 'error';

export interface AuthState { isAuthenticated: boolean,
  user: any | null,
  isLoading: boolean,
  error: string | null,
  pendingVerification?: boolean,
  authStatus: AuthStatus }
export interface AuthContextType {
  authState: AuthState,
  signIn: (data: SignInData) => Promise<string | null>
  signUp: (data: SignUpData) => Promise<string | null>
  signOut: () => Promise<string | null>
  resetPassword: (email: string) => Promise<string | null>
  updatePassword: (newPassword: string) => Promise<string | null>
}