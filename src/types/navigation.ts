import React from 'react';
/**;
 * Comprehensive Navigation Type Definitions;
 *;
 * Provides type-safe navigation interfaces to replace all `as any` assertions;
 * in navigation code, addressing the type safety issues identified in the;
 * comprehensive debugging analysis.;
 */

import { Href } from 'expo-router';

/**;
 * Base navigation route interface;
 */
export interface NavigationRoute {
  path: string,
  params?: Record<string, any>
}
/**;
 * Tab routes enum for type safety;
 */
export enum TabRoutes { HOME = '/(tabs)/';
  SEARCH = '/(tabs)/search';
  MESSAGES = '/(tabs)/messages';
  PROFILE = '/(tabs)/profile';
  ROOM = '/(tabs)/room' }
/**;
 * Auth routes enum for type safety;
 */
export enum AuthRoutes { SIGN_IN = '/(auth)/sign-in';
  SIGN_UP = '/(auth)/sign-up';
  FORGOT_PASSWORD = '/(auth)/forgot-password';
  RESET_PASSWORD = '/(auth)/reset-password';
  EMAIL_VERIFICATION = '/(auth)/email-verification';
  PHONE_VERIFICATION = '/(auth)/phone-verification' }
/**;
 * Profile routes enum for type safety;
 */
export enum ProfileRoutes { INDEX = '/(tabs)/profile';
  INDEX_NEW = '/(tabs)/profile/indexNew';
  SETTINGS = '/(tabs)/profile/settings';
  PRIVACY_SECURITY = '/(tabs)/profile/privacy-security';
  ACCOUNT_SECURITY = '/(tabs)/profile/account-security';
  TWO_FACTOR_AUTH = '/(tabs)/profile/two-factor-auth';
  VERIFICATION_DASHBOARD = '/(tabs)/profile/verification-dashboard';
  ROLE_DASHBOARD = '/(tabs)/profile/role-dashboard';
  PROFILE_VISIBILITY = '/(tabs)/profile/profile-visibility';
  STATISTICS = '/profile/statistics';
  VIEW = '/profile/view';
  TWO_FACTOR_SETUP = '/profile/two-factor-setup' }
/**;
 * Message routes enum for type safety;
 */
export enum MessageRoutes { INDEX = '/(tabs)/messages';
  NEW = '/(tabs)/messages/new';
  CHAT_NEW = '/chat/new';
  CHAT_INDEX = '/chat' }
/**;
 * Household routes enum for type safety;
 */
export enum HouseholdRoutes { INDEX = '/household';
  EXPENSES = '/household/expenses';
  EXPENSES_CREATE = '/household/expenses/create';
  EXPENSES_DETAIL = '/household/expenses/[id]' }
/**;
 * Expense routes enum for type safety;
 */
export enum ExpenseRoutes { INDEX = '/expenses';
  DETAIL = '/expenses/[id]';
  RECURRING = '/expenses/recurring/[id]' }
/**;
 * Legal routes enum for type safety;
 */
export enum LegalRoutes { TERMS_OF_SERVICE = '/legal/terms-of-service';
  PRIVACY_POLICY = '/legal/privacy-policy' }
/**;
 * Matching routes enum for type safety;
 */
export enum MatchingRoutes { MATCH_SUCCESS = '/matching/match-success' }
/**;
 * Payment routes enum for type safety;
 */
export enum PaymentRoutes { INDEX = '/payments' }
/**;
 * All valid route types union;
 */
export type ValidRoute =;
  | TabRoutes;
  | AuthRoutes;
  | ProfileRoutes;
  | MessageRoutes;
  | HouseholdRoutes;
  | ExpenseRoutes;
  | LegalRoutes;
  | MatchingRoutes;
  | PaymentRoutes;
  | string; // Allow custom routes but encourage using enums;
/**;
 * Navigation parameters for different route types;
 */
export interface NavigationParams {
  // Profile routes;
  '/profile/view': { id: string }
  '/profile/statistics': Record<string, never>
  // Household routes;
  '/household/expenses/[id]': { id: string }
  '/household/expenses/create': Record<string, never>
  // Expense routes;
  '/expenses/[id]': { id: string }
  '/expenses/recurring/[id]': { id: string }
  // Matching routes;
  '/matching/match-success': { matchId?: string }
  // Chat routes;
  '/chat': { return To?: string }
  // Generic fallback;
  [key: string]: Record<string, any>
}
/**;
 * Type-safe navigation options;
 */
export interface TypeSafeNavigationOptions { replace?: boolean,
  params?: Record<string, any>
  fallbackRoute?: ValidRoute,
  validateParams?: boolean }
/**;
 * Navigation configuration interface;
 */
export interface NavigationConfig { path: ValidRoute,
  params?: Record<string, any>
  options?: TypeSafeNavigationOptions }
/**;
 * External route configuration;
 */
export interface ExternalRouteConfig { path: ValidRoute,
  isExternal?: boolean,
  requiresAuth?: boolean,
  fallback?: ValidRoute }
/**;
 * Navigation state interface;
 */
export interface NavigationState { currentRoute: ValidRoute,
  previousRoute?: ValidRoute,
  params?: Record<string, any>
  canGoBack: boolean }
/**;
 * Navigation error types;
 */
export enum NavigationErrorType { INVALID_ROUTE = 'INVALID_ROUTE';
  MISSING_PARAMS = 'MISSING_PARAMS';
  UNAUTHORIZED = 'UNAUTHORIZED';
  NETWORK_ERROR = 'NETWORK_ERROR';
  UNKNOWN = 'UNKNOWN' }
/**;
 * Navigation error interface;
 */
export interface NavigationError { type: NavigationErrorType,
  message: string,
  route?: ValidRoute,
  params?: Record<string, any>
  originalError?: Error }
/**;
 * Navigation result interface;
 */
export interface NavigationResult { success: boolean,
  route?: ValidRoute,
  error?: NavigationError }
/**;
 * Router interface for type safety;
 */
export interface TypeSafeRouter {
  push: (,
    route: ValidRoute | NavigationConfig,
    options?: TypeSafeNavigationOptions) = > Promise<NavigationResult>
  replace: (;
    route: ValidRoute | NavigationConfig,
    options?: TypeSafeNavigationOptions) = > Promise<NavigationResult>
  back: () => void;
  canGoBack: () = > boolean;
  getCurrentRoute: () = > ValidRoute;
  getParams: <T = Record<string, any>>() => T;
}
/**;
 * Navigation context interface;
 */
export interface NavigationContextType { router: TypeSafeRouter,
  state: NavigationState,
  isLoading: boolean,
  error?: NavigationError }
/**;
 * Route validation interface;
 */
export interface RouteValidator {
  isValidRoute: (route: string) = > boolean;
  validateParams: (route: ValidRoute, params: Record<string, any>) => boolean;
  sanitizeRoute: (route: string) = > ValidRoute;
  getRequiredParams: (route: ValidRoute) => string[]
}
/**;
 * Navigation analytics interface;
 */
export interface NavigationAnalytics {
  trackNavigation: (from: ValidRoute, to: ValidRoute, params?: Record<string, any>) = > void;
  trackNavigationError: (error: NavigationError) = > void;
  getNavigationMetrics: () => Promise<NavigationMetrics>
}
/**;
 * Navigation metrics interface;
 */
export interface NavigationMetrics {
  totalNavigations: number,
  successfulNavigations: number,
  failedNavigations: number,
  mostVisitedRoutes: Array<{ route: ValidRoute; count: number }>
  averageNavigationTime: number,
  errorsByType: Record<NavigationErrorType, number>
}
/**;
 * Navigation middleware interface;
 */
export interface NavigationMiddleware {
  beforeNavigation?: (route: ValidRoute, params?: Record<string, any>) = > Promise<boolean>
  afterNavigation?: (route: ValidRoute, params?: Record<string, any>) => Promise<void>
  onNavigationError?: (error: NavigationError) => Promise<void>
}
/**;
 * Navigation guard interface;
 */
export interface NavigationGuard { canActivate: (route: ValidRoute, params?: Record<string, any>) = > Promise<boolean>
  redirectTo?: ValidRoute;
  errorMessage?: string }
/**;
 * Authentication guard interface;
 */
export interface AuthGuard extends NavigationGuard { requiresAuth: boolean,
  requiredRoles?: string[],
  redirectToLogin?: ValidRoute }
/**;
 * Permission guard interface;
 */
export interface PermissionGuard extends NavigationGuard { requiredPermissions: string[],
  fallbackRoute?: ValidRoute }
/**;
 * Navigation configuration for the app;
 */
export interface AppNavigationConfig { defaultRoute: ValidRoute,
  authRoutes: ValidRoute[],
  protectedRoutes: ValidRoute[],
  publicRoutes: ValidRoute[],
  guards: NavigationGuard[],
  middleware: NavigationMiddleware[],
  analytics?: NavigationAnalytics }
/**;
 * Type-safe href creation utility type;
 */
export type TypeSafeHref<T extends ValidRoute> = T extends keyof NavigationParams;
  ? NavigationParams[T] extends Record<string, never>
    ? T;
       : { pathname: T params: NavigationParams[T] }
  : T
/**
 * Navigation hook return type;
 */
export interface UseNavigationReturn { router: TypeSafeRouter,
  state: NavigationState,
  navigate: (route: ValidRoute, options?: TypeSafeNavigationOptions) = > Promise<NavigationResult>
  goBack: () => void;
  canGoBack: boolean,
  isLoading: boolean,
  error?: NavigationError }
/**;
 * Route parameter extraction utility type;
 */
export type ExtractRouteParams<T extends string> =;
  T extends `${string}[${infer Param}]${infer Rest}`;
    ? { [K in Param]   : string } & ExtractRouteParams<Rest>
    : Record<string never>
/**
 * Dynamic route type;
 */
export type DynamicRoute<T extends string> = T & {
  params: ExtractRouteParams<T>
}
/**
 * Navigation breadcrumb interface;
 */
export interface NavigationBreadcrumb {
  route: ValidRoute,
  title: string,
  params?: Record<string, any>
}
/**;
 * Navigation history interface;
 */
export interface NavigationHistory { entries: NavigationBreadcrumb[],
  currentIndex: number,
  maxEntries: number }
/**;
 * Deep link configuration;
 */
export interface DeepLinkConfig {
  scheme: string,
  host?: string,
  pathPrefix?: string,
  routes: Record<string, ValidRoute>
}
/**;
 * Navigation performance metrics;
 */
export interface NavigationPerformanceMetrics { routeLoadTime: number,
  componentMountTime: number,
  totalNavigationTime: number,
  memoryUsage?: number }
/**;
 * Navigation cache interface;
 */
export interface NavigationCache { get: (route: ValidRoute) = > any;
  set: (route: ValidRoute, data: any, ttl?: number) = > void;
  clear: (route?: ValidRoute) = > void;
  has: (route: ValidRoute) => boolean }
/**;
 * Navigation prefetch interface;
 */
export interface NavigationPrefetch { prefetchRoute: (route: ValidRoute) = > Promise<void>
  isPrefetched: (route: ValidRoute) => boolean;
  clearPrefetch: (route?: ValidRoute) => void }
export default {
  TabRoutes;
  AuthRoutes;
  ProfileRoutes;
  MessageRoutes;
  HouseholdRoutes;
  ExpenseRoutes;
  LegalRoutes;
  MatchingRoutes;
  PaymentRoutes;
  NavigationErrorType;
}