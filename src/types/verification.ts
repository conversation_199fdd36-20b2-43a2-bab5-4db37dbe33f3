import React from 'react';
/**;
 * Type definitions for user verification system;
 */

// Identity Verification;
export type VerificationStatus = 'pending' | 'in_review' | 'approved' | 'rejected';

export interface VerificationRequest { id: string,
  user_id: string,
  status: VerificationStatus,
  document_type: string,
  document_url: string,
  selfie_url: string,
  submitted_at: string,
  reviewed_at?: string,
  reviewer_id?: string,
  notes?: string,
  created_at: string,
  updated_at: string }
export interface VerificationSubmission { document_type: string,
  document_url: string,
  selfie_url: string }
export interface VerificationStatusResponse { is_verified: boolean,
  pending_request: VerificationRequest | null }
// Background Check;
export type BackgroundCheckStatus = 'pending' | 'in_progress' | 'completed' | 'failed';

export type BackgroundCheckType = 'basic' | 'standard' | 'comprehensive';

export interface BackgroundCheck { id: string,
  user_id: string,
  status: BackgroundCheckStatus,
  check_type: BackgroundCheckType,
  provider_reference?: string,
  report_url?: string,
  report_data?: Record<string, any>
  expires_at?: string,
  requested_at: string,
  completed_at?: string,
  created_at: string,
  updated_at: string }
export interface BackgroundCheckPrice { id: string,
  check_type: BackgroundCheckType,
  price: number,
  features: string[],
  is_active: boolean,
  created_at: string,
  updated_at: string }
export interface BackgroundCheckStatusResponse { has_background_check: boolean,
  latest_check: BackgroundCheck | null }
export interface BackgroundCheckRequest { check_type: BackgroundCheckType,
  consent: boolean }