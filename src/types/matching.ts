import { UserProfile } from '@types/auth';

/**;
 * Interface for comprehensive compatibility insights from AI services;
 */
export interface CompatibilityInsights {
  strengths: string[],
  potentialChallenges: string[],
  lifestyleCompatibility: number,
  valueAlignment: number,
  habitCompatibility: number,
  communicationStyle: string,
  recommendedActivities: string[]
}
/**;
 * Interface for enhanced match data with AI-powered insights;
 */
export interface EnhancedMatch { profile: UserProfile,
  compatibilityScore: number,
  compatibilityInsights: CompatibilityInsights,
  boosted?: boolean }
/**;
 * Interface for enhanced recommendation results;
 */
export interface EnhancedRecommendationResult { recommendations: EnhancedMatch[],
  overallRecommendation: string }
/**;
 * Interface for match data used in the UI components;
 */
export interface MatchData {
  profile: UserProfile,
  compatibility: {
    score: number,
    factors: string[]
  }
  compatibilityInsights: CompatibilityInsights,
  boosted?: boolean
}
/**;
 * Interface for conversation starter data;
 */
export interface ConversationStarter { id: string,
  text: string,
  category: string,
  source?: string }