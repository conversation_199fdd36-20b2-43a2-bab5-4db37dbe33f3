import React from 'react';
import type { Database, UserRoleType, Json } from './supabase';

// Simplified types for often-used database types;
// Note: Using user_profiles instead of profiles view for the definitive type,
export interface Profile { id: string,
  role?: UserRoleType,
  first_name?: string | null,
  last_name?: string | null,
  date_of_birth?: string | null,
  bio?: string | null,
  occupation?: string | null,
  phone_number?: string | null,
  avatar_url?: string | null,
  is_verified?: boolean | null,
  profile_completion?: number | null,
  preferences?: Json | null,
  created_at?: string | null,
  updated_at?: string | null,
  background_check_verified?: boolean | null,
  video_intro_url?: string | null,
  display_name?: string | null,
  meta_data?: Record<string, any> | null;
  email_verified?: boolean | null,
  phone_verified?: boolean | null,
  identity_verified?: boolean | null,
  two_factor_enabled?: boolean | null,
  kyc_level?: string | null,
  video_thumbnail_url?: string | null,
  video_compression_stats?: Record<string, any> | null;
  email?: string | null,
  username?: string | null,
  location?: string | null,
  gallery?: string[],
  version?: number }
export type Room = Database['public']['Tables']['rooms']['Row'];
export type Service = Database['public']['Tables']['services']['Row'];
// Define custom ServiceProvider type since it's not in the Database type yet;
export interface ServiceProvider { id: string,
  user_id: string,
  service_category: string,
  description: string,
  hourly_rate: number | null,
  availability: string | null,
  rating: number | null,
  created_at: string,
  updated_at: string | null }
// Define custom ServiceBooking type since it's not in the Database type yet;
export interface ServiceBooking { id: string,
  service_id: string,
  user_id: string,
  provider_id: string,
  booking_date: string,
  start_time: string,
  end_time: string,
  status: string,
  notes: string | null,
  created_at: string,
  updated_at: string | null }
export type HousemateProfile = Database['public']['Tables']['housemate_profiles']['Row'];
export type Message = Database['public']['Tables']['messages']['Row'];
export type ChatRoom = Database['public']['Tables']['chat_rooms']['Row'];
export type ChatParticipant = Database['public']['Tables']['chat_room_participants']['Row'];
export type SavedRoom = Database['public']['Tables']['saved_rooms']['Row'];

// Domain-specific interfaces that extend the database models with additional properties;
export interface ProfileWithRelations extends Profile {
  housemate_profile?: HousemateProfile,
  saved_rooms?: SavedRoom[],
  living_preferences?: Record<string, any>
  personality_traits?: Array<Record<string, any>>
  verification_status?: Record<string, any>
}
export interface RoomWithDetails extends Room { landlord?: Profile,
  landlord_id?: string,
  is_saved?: boolean,
  is_available?: boolean,
  bedrooms?: number,
  bathrooms?: number }
export interface ChatRoomWithDetails extends ChatRoom { participants?: ProfileWithRelations[],
  messages?: Message[],
  unread_count?: number }
export interface ServiceWithProvider extends Service { provider?: Profile }
// For form handling;
export interface RoomFormData { title: string,
  description: string,
  price: number,
  location: string,
  room_type: string,
  bedrooms: number,
  bathrooms: number,
  furnished: boolean,
  pets_allowed: boolean,
  images: string[],
  move_in_date: string,
  amenities: string[],
  preferences: string[],
  status?: 'available' | 'unavailable' | 'pending' | 'rented',
  // UI-specific fields that don't map directly to database;
  imagePaths?: string[],
  locationData?: any,
  locationText?: string,
  location_id?: string }
export interface HousemateFormData { age: number,
  gender: string,
  occupation: string,
  lifestyle: string[],
  interests: string[],
  budget: number,
  move_in_date: string,
  preferred_locations: string[],
  about_me: string }
export interface ServiceFormData {
  name: string,
  description: string,
  category: string,
  price: number | null,
  contact_info: string,
  images: string[]
}
// Application state interfaces;
export interface AuthState { isAuthenticated: boolean,
  user: Profile | null,
  isLoading: boolean,
  error: string | null }
export interface ChatState { rooms: ChatRoomWithDetails[],
  currentRoom: ChatRoomWithDetails | null,
  messages: Message[],
  isLoading: boolean,
  error: string | null }
export interface RoomState { rooms: RoomWithDetails[],
  savedRooms: RoomWithDetails[],
  currentRoom: RoomWithDetails | null,
  isLoading: boolean,
  error: string | null }
export interface HousemateState { profiles: ProfileWithRelations[],
  currentProfile: ProfileWithRelations | null,
  isLoading: boolean,
  error: string | null }
export interface ServiceState { services: ServiceWithProvider[],
  currentService: ServiceWithProvider | null,
  isLoading: boolean,
  error: string | null }
export interface FilterState { priceRange: [number, number];
  locations: string[],
  roomTypes: string[],
  bedrooms: number[],
  bathrooms: number[],
  furnished: boolean | null,
  petsAllowed: boolean | null }
export interface NotificationPayload {
  type:  ,
    | 'message';
    | 'match';
    | 'roomUpdate';
    | 'system';
    | 'suspicious_profile';
    | 'booking_confirmation';
    | 'booking_reminder';
  title: string,
  body: string,
  data?: Record<string, any>
}
// Updated ServiceWithProvider interface to use the correct provider type;
export interface BookingWithDetails extends ServiceBooking { service?: Service,
  provider?: ServiceProvider,
  is_reviewed?: boolean }
/**;
 * Memory Bank Entry;
 * Represents an entry in the memory bank table;
 */
export interface MemoryBankEntry { id: string,
  user_id: string,
  type: 'decision' | 'context' | 'progress' | 'pattern',
  title: string,
  content: string,
  timestamp: string,
  tags?: string[],
  metadata?: Record<string, any>
  created_at?: string,
  updated_at?: string }