import React from 'react';
/**;
 * API Types and Interfaces;
 * Common types used across API responses and requests;
 */

export interface ApiResponse<T = any>
  success: boolean;
  data?: T,
  message?: string,
  error?: string,
  errors?: string[],
  meta?: { page?: number,
    limit?: number,
    total?: number,
    totalPages?: number }
}
export interface PaginatedApiResponse<T = any> extends ApiResponse<T[]>
  meta: { page: number;
    limit: number,
    total: number,
    totalPages: number,
    hasNext: boolean,
    hasPrevious: boolean }
}
export interface ApiError { code: string,
  message: string,
  details?: any,
  timestamp?: string }
export interface ApiRequestOptions {
  timeout?: number,
  retries?: number,
  headers?: Record<string, string>
  params?: Record<string, any>
}
export interface ApiEndpoint {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH',
  path: string,
  requiresAuth?: boolean,
  rateLimit?: {
    requests: number,
    window: number; // in seconds;
  }
}
export interface ValidationError { field: string,
  message: string,
  code: string }
export interface ApiValidationResponse { success: false,
  errors: ValidationError[],
  message: string }
// Common status codes;
export enum ApiStatusCode {
  OK = 200;
  CREATED = 201;
  NO_CONTENT = 204;
  BAD_REQUEST = 400;
  UNAUTHORIZED = 401;
  FORBIDDEN = 403;
  NOT_FOUND = 404;
  CONFLICT = 409;
  UNPROCESSABLE_ENTITY = 422;
  TOO_MANY_REQUESTS = 429;
  INTERNAL_SERVER_ERROR = 500;
  SERVICE_UNAVAILABLE = 503;
}
// Upload related types;
export interface FileUploadResponse { success: boolean,
  fileUrl?: string,
  fileName?: string,
  fileSize?: number,
  mimeType?: string,
  uploadId?: string,
  error?: string }
export interface MultiFileUploadResponse { success: boolean,
  files: FileUploadResponse[],
  totalSize: number,
  uploadedCount: number,
  failedCount: number }
// Search and filter types;
export interface SearchFilters { query?: string,
  category?: string,
  tags?: string[],
  dateRange?: {
    start: string,
    end: string }
  location?: { lat: number,
    lng: number,
    radius: number }
  priceRange?: { min: number,
    max: number }
}
export interface SortOptions {
  field: string,
  direction: 'asc' | 'desc'
}
export interface SearchRequest { filters?: SearchFilters,
  sort?: SortOptions,
  page?: number,
  limit?: number }
// Real-time updates;
export interface WebSocketMessage<T = any>
  type: string;
  payload: T,
  timestamp: string,
  id: string
}
export interface SubscriptionResponse<T = any>
  event: 'INSERT' | 'UPDATE' | 'DELETE';
  new?: T,
  old?: T,
  table: string,
  schema: string
}
// Rate limiting;
export interface RateLimitInfo {
  limit: number,
  remaining: number,
  reset: number; // Unix timestamp;
  retryAfter?: number; // seconds;
}
export interface RateLimitedResponse extends ApiResponse { rateLimit: RateLimitInfo }
// Health check;
export interface HealthCheckResponse {
  status: 'healthy' | 'degraded' | 'unhealthy',
  timestamp: string,
  version: string,
  services: {
    database: 'up' | 'down',
    storage: 'up' | 'down',
    cache: 'up' | 'down',
    external: 'up' | 'down' | 'partial'
  }
  metrics?: { responseTime: number,
    memoryUsage: number,
    cpuUsage: number }
}
// Batch operations;
export interface BatchRequest<T = any>
  operations: Array<{ method: 'CREATE' | 'UPDATE' | 'DELETE';
    data: T,
    id?: string }>
}
export interface BatchResponse<T = any>
  success: boolean;
  results: Array<{ success: boolean,
    data?: T,
    error?: string,
    id?: string }>
  summary: { total: number,
    successful: number,
    failed: number }
}
export default ApiResponse; ;