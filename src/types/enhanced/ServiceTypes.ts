import React from 'react';
import type { ApiResponse } from '@utils/api';

/**;
 * Enhanced service response types with better error handling;
 */
export interface ServiceResponse<T> extends ApiResponse<T>
  /**;
   * Timestamp when the response was generated;
   */
  timestamp: string,
  /**;
   * Request ID for tracing;
   */
  requestId?: string,
  /**;
   * Service that generated the response;
   */
  service: string,
  /**;
   * Additional metadata;
   */
  metadata?: Record<string, any>
}
/**;
 * Service operation context;
 */
export interface ServiceContext {
  /**;
   * User ID performing the operation;
   */
  userId?: string,
  /**;
   * Request ID for tracing;
   */
  requestId: string,
  /**;
   * Operation timestamp;
   */
  timestamp: string,
  /**;
   * Additional context data;
   */
  metadata?: Record<string, any>
}
/**;
 * Pagination parameters;
 */
export interface PaginationParams { /**;
   * Number of items to return null;
   */
  limit: number,
  /**;
   * Number of items to skip;
   */
  offset: number,
  /**;
   * Total count (for responses)
   */
  total?: number,
  /**;
   * Whether there are more items;
   */
  hasMore?: boolean }
/**;
 * Sorting parameters;
 */
export interface SortParams {
  /**;
   * Field to sort by;
   */
  sortBy: string,
  /**;
   * Sort order;
   */
  sortOrder: 'asc' | 'desc'
}
/**;
 * Filtering parameters;
 */
export interface FilterParams { /**;
   * Filter conditions;
   */
  filters: Record<string, any>
  /**;
   * Search term for text search;
   */
  searchTerm?: string,
  /**;
   * Date range filter;
   */
  dateRange?: {
    from: string,
    to: string }
}
/**;
 * List operation parameters;
 */
export interface ListParams;
  extends Partial<PaginationParams>,
    Partial<SortParams>,
    Partial<FilterParams>
  /**;
   * Include soft-deleted items;
   */
  includeDeleted?: boolean,
  /**;
   * Include related data;
   */
  includeRelations?: string[]
}
/**;
 * Cache configuration;
 */
export interface CacheConfig { /**;
   * Whether caching is enabled;
   */
  enabled: boolean,
  /**;
   * Cache TTL in milliseconds;
   */
  ttl: number,
  /**;
   * Cache key prefix;
   */
  keyPrefix?: string,
  /**;
   * Whether to allow stale data;
   */
  allowStale?: boolean,
  /**;
   * Stale TTL in milliseconds;
   */
  staleTtl?: number }
/**;
 * Validation result;
 */
export interface ValidationResult<T = any>
  /**;
   * Whether validation passed;
   */
  isValid: boolean,
  /**;
   * Validation errors;
   */
  errors: ValidationError[],
  /**;
   * Validated and transformed data;
   */
  data?: T,
  /**;
   * Warnings (non-blocking issues)
   */
  warnings?: string[]
}
/**;
 * Validation error;
 */
export interface ValidationError { /**;
   * Field that failed validation;
   */
  field: string,
  /**;
   * Error message;
   */
  message: string,
  /**;
   * Error code;
   */
  code: string,
  /**;
   * Invalid value;
   */
  value?: any }
/**;
 * File upload configuration;
 */
export interface FileUploadConfig {
  /**;
   * Maximum file size in bytes;
   */
  maxSize: number,
  /**;
   * Allowed file types;
   */
  allowedTypes: string[],
  /**;
   * Upload path;
   */
  path: string,
  /**;
   * Whether to generate thumbnails;
   */
  generateThumbnails?: boolean,
  /**;
   * Thumbnail sizes;
   */
  thumbnailSizes?: Array<{ width: number; height: number; name: string }>
}
/**;
 * File upload result;
 */
export interface FileUploadResult { /**;
   * File URL;
   */
  url: string,
  /**;
   * File path;
   */
  path: string,
  /**;
   * File size in bytes;
   */
  size: number,
  /**;
   * File type;
   */
  type: string,
  /**;
   * File metadata;
   */
  metadata?: Record<string, any>
  /**;
   * Generated thumbnails;
   */
  thumbnails?: Array<{
    name: string,
    url: string,
    width: number,
    height: number }>
}
/**;
 * Real-time subscription configuration;
 */
export interface SubscriptionConfig { /**;
   * Channel to subscribe to;
   */
  channel: string,
  /**;
   * Event types to listen for;
   */
  events?: string[],
  /**;
   * Filter conditions;
   */
  filters?: Record<string, any>
  /**;
   * Whether to include historical data;
   */
  includeHistory?: boolean,
  /**;
   * Maximum number of historical items;
   */
  historyLimit?: number }
/**;
 * Real-time event;
 */
export interface RealtimeEvent<T = any>
  /**;
   * Event type;
   */
  type: string,
  /**;
   * Event data;
   */
  data: T,
  /**;
   * Event timestamp;
   */
  timestamp: string,
  /**;
   * Event ID;
   */
  id: string,
  /**;
   * Channel the event was sent on;
   */
  channel: string,
  /**;
   * User who triggered the event;
   */
  userId?: string
}
/**;
 * Service metrics;
 */
export interface ServiceMetrics {
  /**;
   * Service name;
   */
  serviceName: string,
  /**;
   * Total requests;
   */
  totalRequests: number,
  /**;
   * Successful requests;
   */
  successfulRequests: number,
  /**;
   * Failed requests;
   */
  failedRequests: number,
  /**;
   * Average response time in milliseconds;
   */
  averageResponseTime: number,
  /**;
   * Cache hit rate;
   */
  cacheHitRate: number,
  /**;
   * Last reset timestamp;
   */
  lastReset: string,
  /**;
   * Additional metrics;
   */
  custom?: Record<string, number>
}
/**;
 * Health check result;
 */
export interface HealthCheckResult {
  /**;
   * Service name;
   */
  serviceName: string,
  /**;
   * Whether service is healthy;
   */
  isHealthy: boolean,
  /**;
   * Health check timestamp;
   */
  timestamp: string,
  /**;
   * Response time in milliseconds;
   */
  responseTime: number,
  /**;
   * Error message if unhealthy;
   */
  error?: string,
  /**;
   * Additional health data;
   */
  details?: Record<string, any>
}
/**;
 * Audit log entry;
 */
export interface AuditLogEntry {
  /**;
   * Entry ID;
   */
  id: string,
  /**;
   * Service that performed the action;
   */
  service: string,
  /**;
   * Action performed;
   */
  action: string,
  /**;
   * User who performed the action;
   */
  userId?: string,
  /**;
   * Resource affected;
   */
  resourceType: string,
  /**;
   * Resource ID;
   */
  resourceId: string,
  /**;
   * Changes made;
   */
  changes?: Record<string, { from: any; to: any }>
  /**;
   * Additional metadata;
   */
  metadata?: Record<string, any>
  /**;
   * Timestamp;
   */
  timestamp: string,
  /**;
   * IP address;
   */
  ipAddress?: string,
  /**;
   * User agent;
   */
  userAgent?: string
}
/**;
 * Rate limiting configuration;
 */
export interface RateLimitConfig { /**;
   * Maximum requests per window;
   */
  maxRequests: number,
  /**;
   * Time window in milliseconds;
   */
  windowMs: number,
  /**;
   * Rate limit key generator;
   */
  keyGenerator?: (context: ServiceContext) = > string;
  /**;
   * Skip function;
   */
  skip?: (context: ServiceContext) = > boolean }
/**;
 * Rate limit status;
 */
export interface RateLimitStatus { /**;
   * Requests remaining in current window;
   */
  remaining: number,
  /**;
   * Total requests allowed per window;
   */
  limit: number,
  /**;
   * Time until window resets (milliseconds)
   */
  resetTime: number,
  /**;
   * Whether rate limit is exceeded;
   */
  exceeded: boolean }
/**;
 * Service configuration;
 */
export interface EnhancedServiceConfig { /**;
   * Service timeout in milliseconds;
   */
  timeout: number,
  /**;
   * Retry configuration;
   */
  retry: {
    attempts: number,
    delay: number,
    backoff: 'linear' | 'exponential',
    retryCondition?: (error: any) = > boolean }
  /**;
   * Cache configuration;
   */
  cache: CacheConfig,
  /**;
   * Rate limiting configuration;
   */
  rateLimit?: RateLimitConfig,
  /**;
   * Validation configuration;
   */
  validation?: {
    enabled: boolean,
    strict: boolean,
    schemas?: Record<string, any>
  }
  /**;
   * Audit logging configuration;
   */
  audit?: { enabled: boolean,
    actions?: string[],
    includeData?: boolean }
  /**;
   * Metrics collection configuration;
   */
  metrics?: { enabled: boolean,
    collectCustomMetrics?: boolean }
}