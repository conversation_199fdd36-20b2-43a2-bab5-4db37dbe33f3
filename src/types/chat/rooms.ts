/**;
 * Chat Room Types;
 * Shared type definitions for chat rooms and related entities;
 */

/**;
 * Base Chat Room interface;
 */
export interface ChatRoom { id: string,
  created_at: string,
  updated_at?: string,
  last_message_at?: string,
  name?: string,
  type?: string,
  metadata?: any }
/**;
 * Chat participant interface;
 */
export interface ChatParticipant { user_id: string,
  room_id: string,
  joined_at?: string,
  last_read_at?: string,
  profile?: {
    id: string,
    full_name?: string,
    avatar_url?: string }
}
/**;
 * Enhanced Chat Room with participant details;
 */
export interface EnhancedChatRoom extends ChatRoom { participants: ChatParticipant[],
  unread_count: number }