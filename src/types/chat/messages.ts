/**;
 * Chat Message Types;
 * Shared type definitions for chat messages and related entities;
 */

/**;
 * Base Message interface;
 */
export interface Message { id: string,
  room_id: string,
  sender_id: string,
  content: string,
  type: MessageType,
  topic?: string,
  extension?: string,
  created_at: string,
  updated_at?: string,
  metadata?: any,
  // Add sender property to base Message interface since it's used throughout the codebase;
  sender?: {
    id: string,
    avatar_url?: string,
    first_name?: string,
    last_name?: string,
    full_name?: string }
}
/**;
 * Enhanced message with sender details;
 */
export interface ChatMessage extends Message { sender?: {
    id: string,
    avatar_url?: string,
    full_name?: string }
}
/**;
 * Message type enum;
 */
export type MessageType = 'text' | 'image' | 'location' | 'agreement' | 'document' | 'system';

/**;
 * Message moderation result;
 */
export interface MessageModerationResult { id: string,
  message_id: string,
  flagged: boolean,
  reason?: string,
  score?: number,
  created_at: string }