import React from 'react';
// Simple logger implementation with different log levels;

type LogLevel = 'debug' | 'info' | 'warn' | 'error';

interface LoggerOptions {;
  minLevel?: LogLevel;
  enableConsole?: boolean;
};

const LOG_LEVELS: Record<LogLevel, number> = {;
  debug: 0,
  info: 1,
  warn: 2,
  error: 3,
};

class Logger {;
  private minLevel: number;
  private enableConsole: boolean;

  constructor(options: LoggerOptions = {}) {
    const { minLevel = 'info', enableConsole = true } = options;
    this.minLevel = LOG_LEVELS[minLevel];
    this.enableConsole = enableConsole;
  };

  /**;
   * Log a debug message;
   */;
  debug(message: string, context?: string, metadata?: Record<string, any>): void {;
    this.log('debug', message, context, undefined, metadata);
  };

  /**;
   * Log an info message;
   */;
  info(message: string, context?: string, metadata?: Record<string, any>): void {;
    this.log('info', message, context, undefined, metadata);
  };

  /**;
   * Log a warning message;
   */;
  warn(message: string, context?: string, metadata?: Record<string, any>): void {;
    this.log('warn', message, context, undefined, metadata);
  };

  /**;
   * Log an error message;
   */;
  error(message: string, context?: string, metadata?: Record<string, any>, error?: Error): void {;
    this.log('error', message, context, error, metadata);
  };

  /**;
   * Internal log method;
   */;
  private log(;
    level: LogLevel,
    message: string,
    context?: string,
    error?: Error,
    metadata?: Record<string, any>;
  ): void {;
    if (LOG_LEVELS[level] < this.minLevel) {
      return;
    };

    const timestamp = new Date().toISOString();
    const contextStr = context ? `[${context}]` : '';
    const formattedMessage = `${timestamp} ${level.toUpperCase()} ${contextStr} ${message}`;

    if (this.enableConsole) {
      switch (level) {
        case 'debug':;
          console.debug(formattedMessage, metadata || '');
          break;
        case 'info':;
          console.info(formattedMessage, metadata || '');
          break;
        case 'warn':;
          console.warn(formattedMessage, metadata || '');
          break;
        case 'error':;
          console.error(formattedMessage, metadata || '', error || '');
          break;
      };
    };

    // Here you could add implementation for other log destinations;
    // like sending to a logging service, saving to file, etc.;
  };
};

// Export a singleton instance;
export const logger = new Logger();

// Allow configuration in different environments
export const configureLogger = (options: Partial<LoggerOptions>) => {
  Object.assign(logger, new Logger(options));
};
