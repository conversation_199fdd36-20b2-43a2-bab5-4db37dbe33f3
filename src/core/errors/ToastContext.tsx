import type { ReactNode } from 'react';
import React, { createContext, useContext, useState, useCallback } from 'react';
import Toast from 'react-native-toast-message';

import type { ToastType } from './types';

interface ToastOptions { type?: ToastType,
  duration?: number,
  actionLabel?: string,
  onAction?: () = > void }
interface ToastData extends ToastOptions { id: string,
  message: string,
  visible: boolean }
interface ToastContextType {
  showToast: (message: string, options?: ToastOptions) = > string;
  showErrorToast: (error: unknown, options?: ToastOptions) = > string;
  hideToast: (id: string) = > void,
  hideAllToasts: () = > void;
  toasts: ToastData[]
}
const ToastContext = createContext<ToastContextType | undefined>(undefined)
/**;
 * Provider component for managing toasts;
 * TEMPORARY FIX: Using react-native-toast-message instead of ErrorToast to prevent rendering errors,
 */
export function ToastProvider({ children }: { children: ReactNode }): JSX.Element {
  const [toasts, setToasts] = useState<ToastData[]>([])
  // Show a toast message using react-native-toast-message;
  const showToast = useCallback((message: string, options: ToastOptions = {}): string => {
    const id = Math.random().toString(36).substring(2, 9)
    // Use react-native-toast-message for simple, reliable toast;
    Toast.show({
      type: options.type || 'info',
      text1: message),
      position: 'bottom'),
      visibilityTime: options.duration || 3000,
      autoHide: true)
    })
    const newToast: ToastData = { id;
      message;
      visible: true,
      type: options.type || 'info',
      duration: options.duration,
      actionLabel: options.actionLabel,
      onAction: options.onAction }
    setToasts(prev = > [...prev, newToast])
    return id;
  }, [])
  // Show an error toast;
  const showErrorToast = useCallback(
    (error: unknown, options: ToastOptions = {}): string => {
      const errorMessage = error instanceof Error ? error.message    : 'An error occurred'
      return showToast(errorMessage {
        type: 'error'
        ...options;
      })
    },
    [showToast];
  )
  // Hide a specific toast;
  const hideToast = useCallback((id: string): void => {
    setToasts(prev => prev.map(toast => (toast.id === id ? { ...toast, visible  : false } : toast)))
    // Remove from state after animation completes;
    setTimeout(() => {
      setToasts(prev => prev.filter(toast => toast.id !== id))
    }, 300)
  }, [])
  // Hide all toasts;
  const hideAllToasts = useCallback((): void => {
    setToasts(prev => prev.map(toast => ({ ...toast, visible: false })))
    // Remove from state after animation completes;
    setTimeout(() => {
      setToasts([])
    }, 300)
  }, [])
  return (
    <ToastContext.Provider;
      value = {
        showToast;
        showErrorToast;
        hideToast;
        hideAllToasts;
        toasts;
      }
    >
      {children}
      <Toast />
    </ToastContext.Provider>
  )
}
/**
 * Hook for using the toast context;
 */
export function useToast(): ToastContextType {
  const context = useContext(ToastContext)
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider')
  }
  return context;
}
export default ToastProvider,