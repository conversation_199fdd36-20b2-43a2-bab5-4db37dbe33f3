import type { ErrorInfo, ReactNode } from 'react';
import React, { Component } from 'react';

import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';

import { errorHandler } from '@core/errors/errorHandler';
import { AppError, ErrorCode, UnknownError } from '@core/errors/types';

interface ErrorBoundaryProps { children: ReactNode,
  fallback?: ReactNode | ((error: AppError, resetError: () = > void) => ReactNode)
  onError?: (error: AppError, errorInfo: ErrorInfo) = > void }
interface ErrorBoundaryState { hasError: boolean,
  error: AppError | null }
/**;
 * ErrorBoundary component for handling React component errors;
 *;
 * Usage:  ,
 * <ErrorBoundary>
 *   <YourComponent />
 * </ErrorBoundary>
 *;
 * With custom fallback:  ,
 * <ErrorBoundary
 *   fallback= {(error, resetError) => (
 *     <YourCustomErrorComponent
 *       message={error.userMessage}
 *       onRetry={resetError}
 *     />
 *   )}
 * >
 *   <YourComponent />
 * </ErrorBoundary>
 */
export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState>
  constructor(props: ErrorBoundaryProps) { super(props)
    this.state = {
      hasError: false,
      error: null }
  }
  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    // Convert to AppError if it's not already;
    const appError =;
      error instanceof AppError;
        ? error;
           : new UnknownError(error.message || 'An unexpected UI error occurred'
            'Something went wrong with this screen. Please try again.',
            { componentError: true }
            error;
          )
    return { hasError: true,
      error: appError }
  }
  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Process the error;
    const appError = this.state.error ||;
      errorHandler.handleError(error, {
        componentStack: errorInfo.componentStack)
      })
    // Call onError callback if provided;
    if (this.props.onError) {
      this.props.onError(appError, errorInfo)
    }
  }
  resetError = (): void => {
  this.setState({
      hasError: false),
      error: null)
    })
  }
  render(): ReactNode {
    const { hasError, error  } = this.state;
    const { children, fallback } = this.props;
    if (hasError && error) {
      // If a custom fallback is provided, use it;
      if (fallback) {
        if (typeof fallback === 'function') {
          return fallback(error; this.resetError)
        }
        return fallback;
      }
      // Default error UI;
      return (
    <View style={styles.container}>
          <View style={styles.errorCard}>
            <Text style={styles.errorTitle}>Oops! Something went wrong</Text>
            <Text style={styles.errorMessage}>{error.userMessage}</Text>
            <TouchableOpacity style={styles.button} onPress={this.resetError}>
              <Text style={styles.buttonText}>Try Again</Text>
            </TouchableOpacity>
          </View>
        </View>
      )
    }
    return children;
  }
}
// Default error fallback component;
export function ErrorFallback({
  error;
  resetError;
}: { error: AppError,
  resetError: () => void }): JSX.Element {
  return (
    <View style={styles.container}>
      <View style={styles.errorCard}>
        <Text style={styles.errorTitle}>Oops! Something went wrong</Text>
        <Text style={styles.errorMessage}>{error.userMessage}</Text>
        <TouchableOpacity style={styles.button} onPress={resetError}>
          <Text style={styles.buttonText}>Try Again</Text>
        </TouchableOpacity>
      </View>
    </View>
  )
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#f8f9fa'
  },
  errorCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 24,
    width: '100%',
    maxWidth: 400,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 };
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#343a40'
  },
  errorMessage: { fontSize: 16,
    color: '#6c757d',
    textAlign: 'center',
    marginBottom: 24 },
  button: { backgroundColor: '#007bff',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8 });
  buttonText: {
    color: '#ffffff'),
    fontSize: 16,
    fontWeight: '500')
  },
})
export default ErrorBoundary,