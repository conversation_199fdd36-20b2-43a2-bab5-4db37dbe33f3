import React from 'react';
/**;
 * Session Hook;
 * Provides session management and state tracking;
 */;

import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@lib/supabase';
import { Session, User } from '@supabase/supabase-js';
import { logger } from '@services/loggerService';

export interface SessionState {
  session: Session | null,
  user: User | null,
  isLoading: boolean,
  isAuthenticated: boolean,
  error: string | null,
}
export interface SessionHookReturn extends SessionState {
  refreshSession: () => Promise<void>
  signOut: () => Promise<void>
  clearError: () => void,
}
/**;
 * Hook for managing user session state;
 */;
export function useSession(): SessionHookReturn {
  const [sessionState, setSessionState] = useState<SessionState>({
    session: null,
    user: null,
    isLoading: true,
    isAuthenticated: false,
    error: null,
  });

  // Initialize session state;
  useEffect(() => {
    initializeSession();

    // Set up auth state change listener;
    const { data: { subscription  }
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      logger.info('Auth state changed', 'useSession', { event, userId: session?.user?.id });

      setSessionState(prev => ({
        ...prev,
        session,
        user: session?.user || null,
        isAuthenticated: !!session,
        isLoading: false,
        error: null,
      }));

      // Handle specific auth events;
      switch (event) {
        case 'SIGNED_IN': ,
          logger.info('User signed in', 'useSession', { userId: session?.user?.id });
          break;
        case 'SIGNED_OUT': ,
          logger.info('User signed out', 'useSession');
          break;
        case 'TOKEN_REFRESHED': ,
          logger.info('Session token refreshed', 'useSession', { userId: session?.user?.id });
          break;
        case 'USER_UPDATED': ,
          logger.info('User updated', 'useSession', { userId: session?.user?.id });
          break;
      }
    });

    return () => {
      subscription.unsubscribe();
    }
  }, []);

  const initializeSession = async () => {
    try {
      logger.info('Initializing session', 'useSession');

      const { data: { session  }
        error,
      } = await supabase.auth.getSession();

      if (error) {
        throw error;
      }
      setSessionState({
        session,
        user: session?.user || null,
        isLoading: false,
        isAuthenticated: !!session,
        error: null,
      });

      logger.info('Session initialized', 'useSession', {
        isAuthenticated: !!session,
        userId: session?.user?.id,
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to initialize session',
      logger.error('Failed to initialize session', 'useSession', {}, error as Error);

      setSessionState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
    }
  }
  const refreshSession = useCallback(async () => {
    try {
      logger.info('Refreshing session', 'useSession');

      setSessionState(prev => ({ ...prev, isLoading: true, error: null }));

      const { data: { session  }
        error,
      } = await supabase.auth.refreshSession();

      if (error) {
        throw error;
      }
      setSessionState(prev => ({
        ...prev,
        session,
        user: session?.user || null,
        isAuthenticated: !!session,
        isLoading: false,
        error: null,
      }));

      logger.info('Session refreshed successfully', 'useSession', { userId: session?.user?.id });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to refresh session',
      logger.error('Failed to refresh session', 'useSession', {}, error as Error);

      setSessionState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
    }
  }, []);

  const signOut = useCallback(async () => {
    try {
      logger.info('Signing out user', 'useSession', { userId: sessionState.user?.id });

      setSessionState(prev => ({ ...prev, isLoading: true, error: null }));

      const { error } = await supabase.auth.signOut();

      if (error) {
        throw error;
      }
      // Session state will be updated by the auth state change listener;
      logger.info('User signed out successfully', 'useSession');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to sign out',
      logger.error('Failed to sign out', 'useSession', {}, error as Error);

      setSessionState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
    }
  }, [sessionState.user?.id]);

  const clearError = useCallback(() => {
    setSessionState(prev => ({ ...prev, error: null }));
  }, []);

  return {
    ...sessionState,
    refreshSession,
    signOut,
    clearError,
  }
}
export default useSession;
