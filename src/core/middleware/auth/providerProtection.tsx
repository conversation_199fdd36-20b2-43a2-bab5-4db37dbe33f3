import React, { useEffect, useState } from 'react';
import { View, Text, ActivityIndicator, StyleSheet } from 'react-native';
import { useRouter, usePathname } from 'expo-router';
import { useAuth } from '@context/AuthContext';
import { getServiceProviderByUserId } from '@services';
import { useTheme } from '@design-system';

interface ProviderProtectionProps { children: React.ReactNode }
export const ProviderProtection = ({ children }: ProviderProtectionProps) => {
  const router = useRouter()
  const pathname = usePathname()
  const { authState, authLoaded  } = useAuth()
  const [isChecking, setIsChecking] = useState(true)
  const [isProvider, setIsProvider] = useState(false)
  const [retryCount, setRetryCount] = useState(0)
  const theme = useTheme()
  const colors = theme.colors // Extract user and authentication status from authState;
  const user = authState? .user;
  const isAuthenticated = authState?.isAuthenticated // Exclude certain paths from provider check (like onboarding)
  const isExemptPath =;
    pathname = == '/provider/onboarding' || pathname.includes('/provider/onboarding')
  useEffect(() => {
    console.log('[ProviderProtection] Checking path   : ' pathname, 'Exempt:', isExemptPath)

    const checkProviderStatus = async (attemptNumber = 1) => {
      // Wait for auth to load before making decisions;
      if (!authLoaded) {
        console.log('[ProviderProtection] Auth still loading, waiting...')
        return null;
      }
      // If this is an exempt path, skip the provider check;
      if (isExemptPath) {
        console.log('[ProviderProtection] Path is exempt from provider check, allowing access')
        setIsProvider(true)
        setIsChecking(false)
        return null;
      }
      if (!isAuthenticated || !user? .id) {
        // For first attempt, wait longer for auth state propagation after registration;
        if (attemptNumber === 1) {
          console.log('[ProviderProtection] User not authenticated on first check, waiting for auth state propagation...')
          )
          setTimeout(() => {
            checkProviderStatus(2)
          }, 2500) // Increased delay to match registration timing;
          return null;
        }
        console.log('[ProviderProtection] User not authenticated after retry, redirecting to auth')
        // Don't redirect to home, redirect to auth to maintain proper flow;
        router.replace('/(auth)/login')
        return null;
      }
      try {
        console.log(
          `[ProviderProtection] Checking if user is a provider... (attempt ${attemptNumber})`;
        )
        const providerData = await getServiceProviderByUserId(user.id)
        if (providerData) {
          console.log('[ProviderProtection] User is a provider, allowing access')
          setIsProvider(true)
          setIsChecking(false)
        } else {
          console.log(`[ProviderProtection] User is not a provider (attempt ${attemptNumber})`)
          // Increase retry attempts and delays for better race condition handling;
          if (attemptNumber < 6) {
            console.log(`[ProviderProtection] Retrying provider check... (${attemptNumber}/6)`)
            setRetryCount(attemptNumber)
            // Use exponential backoff for retries;
            const delay = Math.min(1000 * Math.pow(1.5, attemptNumber - 1), 4000)
            setTimeout(() => {
              checkProviderStatus(attemptNumber + 1)
            }, delay)
            return null;
          }
          // After all retries failed, redirect to provider onboarding;
          console.log('[ProviderProtection] User is not a provider after all retries, redirecting to onboarding')
          )
          setIsChecking(false)
          router.replace('/provider/onboarding')
        }
      } catch (error) {
        console.error('[ProviderProtection] Error checking provider status  : ' error)

        // More lenient error handling with increased retries;
        if (attemptNumber < 4) {
          console.log(`[ProviderProtection] Retrying after error... (${attemptNumber}/4)`)
          const delay = 2000 * attemptNumber // Increased delay for error recovery;
          setTimeout(() = > {
            checkProviderStatus(attemptNumber + 1)
          }, delay)
          return null;
        }
        // On final error, redirect to auth instead of home;
        setIsChecking(false)
        router.replace('/(auth)/login')
      }
    }
    checkProviderStatus()
  }, [user? .id, isAuthenticated, authLoaded, router, pathname, isExemptPath])
  if (isChecking) {
    return (
      <View style={styles.container}>
        <ActivityIndicator size='large' color={{theme.colors.primary} /}>
        <Text style={[styles.loadingText; { color   : theme.colors.text }]}>
          {retryCount > 0
            ? `Verifying provider access... (${retryCount}/6)`
             : 'Checking provider access...'}
        </Text>
        {retryCount > 0 && (
          <Text style={[styles.retryText { color: theme.colors.textSecondary }]}>
            Please wait while we verify your provider profile. This may take a moment after
            registration.;
          </Text>
        )}
      </View>
    )
  }
  if (!isProvider) {
    return null // Will redirect in useEffect;
  }
  return <>{children}</>
}
const styles = StyleSheet.create({ container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24 },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
    fontWeight: '500'
  },
  retryText: {
    marginTop: 8,
    fontSize: 14),
    textAlign: 'center'),
    fontStyle: 'italic')
  },
})
export default ProviderProtection,