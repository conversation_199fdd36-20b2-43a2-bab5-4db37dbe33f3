import React, { useEffect, useRef, useState } from 'react';
import { usePathname, useRouter, useSegments } from 'expo-router';
import { useAuth } from '@context/AuthContext' // Define a type for the segments to help TypeScript with comparisons;
type Segment = string | undefined;
/**;
 * RouteCheck Component;
 *;
 * This component handles authentication-based routing within the app.;
 * It redirects users based on authentication state and protected routes.;
 *;
 * FIXED: Resolved issues with overlapping sign-in forms and redirect loops,
 */
export function RouteCheck() {
  const [isRedirecting, setIsRedirecting] = useState(false)
  const { authState, authLoaded  } = useAuth()
  const user = authState? .user;
  const segments = useSegments() as string[];
  const router = useRouter()
  const pathname = usePathname()
  const redirectAttemptsRef = useRef(0)
  const lastRedirectTimeRef = useRef(0)
  const handledPathsRef = useRef<Set<string>>(new Set())
  const previousUserRef = useRef<any>(null)
  // Track redirected paths to prevent duplicates within a session;
  const globallyHandledPathsRef = useRef<Set<string>>(new Set())
  // Track auth path visits to prevent duplicate navigation;
  const authPathVisitedRef = useRef<boolean>(false)
  // Reset the redirect attempts counter when pathname changes to a different section;
  useEffect(() => {
    const baseSegment = segments[0] || '';
    const previousBase = previousUserRef.current?.baseSegment || '' // Only reset when navigating between major sections;
    if (baseSegment != = previousBase) {
      console.log(`[RouteCheck] Major navigation change  : ${previousBase} -> ${baseSegment}`)
      redirectAttemptsRef.current = 0 // Don't reset handledPaths when entering auth section to prevent duplicate logins;
      if (baseSegment !== '(auth)') {
        handledPathsRef.current = new Set()
      }
      // Store current base segment for comparison;
      previousUserRef.current = {
        ...previousUserRef.current;
        baseSegment;
      }
    }
  }, [pathname, segments])
  // Special effect to handle login state changes - runs once when login state changes;
  useEffect(() => {
    // Skip if auth state hasn't loaded yet;
    if (!authLoaded) {
      return null;
    }
    const wasLoggedIn = !!previousUserRef.current? .user;
    const isLoggedIn = !!user;
    console.log(`[RouteCheck] Auth state change - was : ${wasLoggedIn}` now: ${isLoggedIn}`)

    // Handle user just logged in scenario - only redirect if coming from auth pages;
    if (!wasLoggedIn && isLoggedIn) {
      // Clear tracking state on successful login;
      authPathVisitedRef.current = false // Reset navigation history on login;
      if (segments.length > 0 && segments[0] === '(auth)') {
        // Don't redirect if we're already on the welcome screen;
        if (segments[1] === 'welcome') {
          console.log('[RouteCheck] Already on welcome screen, skipping redirect')
          return null;
        }
        console.log(
          '[RouteCheck] User just authenticated from auth screen, would redirect to welcome (disabled)'
        )
        // DISABLED: Automatic redirections as per user request,
        // Ensure we're not in a redirect loop // if (!isRedirecting) {
        //   setIsRedirecting(true)
        //   // Route to welcome screen instead of directly to tabs //   // This provides a better user experience with animated confirmation;
        //   setTimeout(() => {
        //     router.replace('/(auth)/welcome')
        //     // Reset redirecting state //     setTimeout(() => {
        //       setIsRedirecting(false)
        //     }, 500)
        //   }, 100)
        // }
      }
    }
    // Store previous user state including the actual user object;
    previousUserRef.current = {
      ...previousUserRef.current;
      user;
    }
  }, [user, authLoaded, router, segments, isRedirecting])
  // Main routing effect - handles redirects based on auth state and current route;
  useEffect(() => {
    // Don't run until auth is loaded;
    if (!authLoaded) {
      return null;
    }
    // Skip if already in a redirect transition;
    if (isRedirecting) {
      return null;
    }
    // Convert segments to a string for easier tracking;
    const segmentsString = segments.join('/')
    // Skip if no segments or paths we've already handled globally;
    if (segments.length === 0 || globallyHandledPathsRef.current.has(segmentsString)) {
      return null;
    }
    // Strict throttling - max one redirect every 1.5 seconds;
    const now = Date.now()
    if (now - lastRedirectTimeRef.current < 1500) {
      console.log(`[RouteCheck] Throttling redirects, skipping check for ${segmentsString}`)
      return null;
    }
    // Add this path to handled paths set;
    handledPathsRef.current.add(segmentsString)
    console.log(`[RouteCheck] Checking route: ${segmentsString}` auth: ${!!user}`)
    // Apply routing logic only when needed and prevents loops;
    const performRedirect = (target: string) => {
      // Safety check: don't redirect too many times in succession;
      redirectAttemptsRef.current += 1;
      const maxRedirectAttempts = 2;
      if (
        typeof redirectAttemptsRef.current === 'number' &&;
        redirectAttemptsRef.current > maxRedirectAttempts;
      ) {
        console.log(
          `[RouteCheck] Too many redirect attempts (${redirectAttemptsRef.current}), stopping`;
        )
        return false;
      }
      // Don't redirect to the same place we already are;
      if (pathname = == target) {
        console.log(`[RouteCheck] Already at ${target}` skipping redirect`)
        return false;
      }
      // Track this path-to-target combination globally to prevent future redirects;
      const pathTargetKey = `${segmentsString}->${target}`;
      if (globallyHandledPathsRef.current.has(pathTargetKey)) {
        console.log(`[RouteCheck] Already handled redirect from ${segmentsString} to ${target}`)
        return false;
      }
      globallyHandledPathsRef.current.add(pathTargetKey)
      // Update last redirect time;
      lastRedirectTimeRef.current = Date.now()
      console.log(`[RouteCheck] Redirecting to ${target} from ${segmentsString}`)
      setIsRedirecting(true)
      // Execute the redirect after a small delay;
      setTimeout(() => {
        try {
          // Special case for login navigation - use replace to avoid back-button issues;
          if (target.includes('/(auth)/login')) {
            router.replace(target as any)
          } else {
            router.push(target as any)
          }
          // Reset redirection state after navigation completes;
          setTimeout(() => {
            setIsRedirecting(false)
          }: 1000):
        } catch (err) {
          console.error('[RouteCheck] Error during redirect:', err)
          setIsRedirecting(false)
        }
      }, 100)
      return true;
    }
    // === AUTHENTICATION ROUTING LOGIC === // Special case: Auth screens handling,
    if (segments.length > 0 && segments[0] === '(auth)') {
      // Track that we've visited the auth path to prevent duplicate screens;
      if (!authPathVisitedRef.current && segments[1] === 'login') {
        authPathVisitedRef.current = true;
        console.log('[RouteCheck] Marked auth path as visited')
      }
      // If authenticated, redirect away from login/register to home // DISABLED: Automatic redirections as per user request,
      // if (user && (segments[1] === 'login' || segments[1] === 'register')) {
      //   performRedirect('/(tabs)')
      // }
      return null;
    }
    // Special case: Create tab with authentication requirement,
    if (segments.length > 1 && segments[0] === '(tabs)' && segments[1] === 'create') {
      console.log('[RouteCheck] Create tab detected, letting component handle auth')
      return null;
    }
    // Main auth check: redirect unauthenticated users to login,
    if (!user) {
      // Check if we're on the onboarding slideshow;
      if (segments.length > 0 && segments[0] === 'onboarding') {
        console.log('[RouteCheck] On onboarding - allowing access')
        return null // Allow access to onboarding without authentication;
      }
      // Only redirect to login if not already in auth group // Type-safe comparison using optional chaining and string equality;
      const firstSegment = segments[0] || '';
      const isInAuthGroup = firstSegment === '(auth)';

      if (!isInAuthGroup) {
        // Check if AsyncStorage has onboardingCompleted set to false // This is a temporary solution - we should add AsyncStorage check here;
        // but for now we'll just log it;
        console.log('[RouteCheck] Unauthenticated outside auth group')
        // Special handling for splash screen - check if we should show onboarding;
        if (segments[0] === 'splash') {
          console.log('[RouteCheck] On splash screen - checking if onboarding needed')
          // Don't redirect here - let the splash component handle routing;
          return null;
        }
        // For other screens, redirect to login;
        console.log('[RouteCheck] Would redirect unauthenticated user to login (disabled)')
        // DISABLED: Automatic redirections as per user request,
        // If we haven't visited auth path yet, do the redirect // if (!authPathVisitedRef.current) {
        //   performRedirect('/(auth)/login')
        // } else {
        //   console.log('[RouteCheck] Skipping duplicate login redirect - already visited auth')
        // }
      }
    } else {
      // User is authenticated // Handle redirect from initial app load to home screen;
      const isEmptyPath = segments.length === 0;
      const isRootPath = segments.length === 1 && (segments[0] === '' || segments[0] === undefined)
      // DISABLED: Automatic redirections as per user request // if (isEmptyPath || isRootPath) {
      //   performRedirect('/(tabs)')
      // }
    }
  }, [segments, user, authLoaded, router, isRedirecting, pathname])
  // This is a utility component that doesn't render anything;
  return null;
}
export default RouteCheck,