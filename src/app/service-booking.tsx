import React, { useState, useEffect } from 'react';,
  import {
   View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator, TextInput, Alert ,
  } from 'react-native';
import {,
  MaterialIcons 
} from '@expo/vector-icons';,
  import DateTimePicker from '@react-native-community/datetimepicker';
import {,
  Stack, useLocalSearchParams, useRouter ,
  } from 'expo-router';
import {,
  format, addMinutes ,
  } from 'date-fns' // Import from correct relative paths to match project structure;
import {,
  useAuthCompat 
} from '../hooks/useAuthCompat';,
  import {
   useToast ,
  } from '../hooks/useToast';
import {,
  useTheme 
} from '@design-system';,
  import {
   useStandardizedBookings ,
  } from '../hooks/useStandardizedBookings';
import {,
  getServiceRepository 
} from '../core/repositories/RepositoryFactory';,
  import {
   logError ,
  } from '../utils/errorUtils';
import {,
  BookingStatus, PaymentStatus ,
  } from '../services/standardized/BookingService' // Define Booking interface to match the BookingService requirements;
interface Booking { id?: string,
  service_id: string,
  user_id: string,
  booking_date: string,
  end_date: string,
  status: BookingStatus,
  address: string,
  special_instructions?: string,
  price: number,
  payment_status: PaymentStatus,
  shared_with?: string[],
  roommate_shared: boolean },
  // Extended theme colors to match ThemeColors type;
interface ExtendedThemeColors { primary: string,
  secondary: string,
  background: string,
  card: string,
  text: string,
  border: string,
  notification: string,
  success: string,
  warning: string,
  error: string,
  info: string,
  textLight: string,
  cardLight: string,
  white: string },
  // Type for Profile with business properties;
interface Profile {,
  id: string,
  user_id: string,
  created_at: string,
  updated_at: string,
  name?: string,
  business_name?: string,
  profile_image?: string,
  contact_phone?: string,
  contact_email?: string,
  duration?: number // Added for compatibility;,
  [key: string]: any // Allow additional properties,
  }
// Define service provider interface, ,
  interface ServiceProvider {
  id: string,
  name: string,
  business_name: string,
  profile_image: string,
  contact_phone: string,
  contact_email: string,
  [key: string]: any // Allow additional properties,
  }
interface ServiceInterface { id: string,
  name: string,
  description: string,
  provider_id: string,
  category: string,
  price: number,
  contact_info?: string,
  created_at: string,
  updated_at: string,
  images: string[],
  is_available: boolean,
  duration?: number // Make optional since it might not always be present;,
  business_name?: string,
  profile_image?: string,
  contact_phone?: string,
  contact_email?: string,
  provider?: ServiceProvider },
  // Define step type as a string union to ensure type safety in comparisons;
type Step = 'booking-details' | 'confirmation' // Define button variant type;,
  type ButtonVariant = 'primary' | 'default' | 'outlined' | 'text' // Define the service repository interface for proper typing;
interface ServiceRepository {,
  findServiceWithProvider: (id: string) = > Promise<ServiceInterface>
},
  // Define ServiceWithProvider as a type that extends ServiceInterface and ensures it has all the required properties;
interface ServiceWithProvider extends Omit<ServiceInterface, 'duration'>,
  // Make sure duration is always a number (not optional)
  duration: number,
  // Make sure price is always a number;,
  price: number,
  // Provider is required with this interface;,
  provider?: ServiceProvider
},
  // Define roommate interface;
interface Roommate { id: string,
  name: string,
  image?: string,
  selected?: boolean },
  // Define form input interface;
interface FormInput { label: string,
  value: string,
  onChangeText: (text: string) = > void;,
  placeholder?: string,
  keyboardType?: 'default' | 'email-address' | 'numeric' | 'phone-pad',
  secureTextEntry?: boolean,
  multiline?: boolean,
  numberOfLines?: number,
  error?: string },
  // Define input props interface;
interface CustomInputProps { placeholder: string,
  value: string,
  onChangeText: (text: string) = > void;,
  multiline?: boolean,
  numberOfLines?: number,
  label?: string },
  // Define button props interface;
interface ButtonProps { onPress: () = > void,
  children: React.ReactNode,
  variant?: ButtonVariant,
  style?: any,
  disabled?: boolean,
  title?: string,
  leftIcon?: React.ReactNode },
  // Custom Input component with improved styling;
const CustomInput: React.FC<CustomInputProps> = (props) => {,
  return (
    <View style={styles.inputContainer}>,
  {props.label && <Text style={styles.inputLabel}>{props.label}</Text>
      <TextInput,
  style={{  [styles.input, props.multiline && { height: props.numberOfLines ? props.numberOfLines * 24     : 100 textAlignVertical: 'top' }}]},
  placeholder={props.placeholder} value={props.value} onChangeText={props.onChangeText} multiline={props.multiline} numberOfLines={props.numberOfLines}
      />,
  </View>
  ),
  }
// Form Input component with error handling;,
  const FormInput: React.FC<FormInput> = ({;
  label;,
  value;
  onChangeText;,
  placeholder;
  keyboardType = 'default',
  secureTextEntry = false;
  multiline = false;,
  numberOfLines;
  error, ,
  }) = > {
  return (,
  <View style={styles.inputContainer}>
      <Text style={styles.inputLabel}>{label}</Text>,
  <TextInput
        style={{  [styles.input, error && styles.inputError, multiline && { height: numberOfLines ? numberOfLines * 24     : 100 textAlignVertical: 'top' }}]},
  placeholder={placeholder} value={value} onChangeText={onChangeText} keyboardType={keyboardType} secureTextEntry={secureTextEntry} multiline={multiline} numberOfLines={numberOfLines}
      />,
  {error && <Text style={styles.errorText}>{error}</Text>
    </View>,
  )
},
  // Define styles for the component, ,
  const ServiceBookingScreen = () => {
  const router = useRouter(),
  const { id: serviceId  } = useLocalSearchParams<{ id: string }>()
  const [service, setService] = useState<ServiceWithProvider | null>(null),
  const [isLoading, setIsLoading] = useState(true),
  const [error, setError] = useState<string | null>(null),
  const [currentStep, setCurrentStep] = useState<Step>('booking-details'),
  const [isSubmitting, setIsSubmitting] = useState(false),
  const [bookingId, setBookingId] = useState<string | null>(null),
  // Booking form state;
  const [selectedDate, setSelectedDate] = useState<Date | null>(null),
  const [selectedTime, setSelectedTime] = useState<string>(''),
  const [showDatePicker, setShowDatePicker] = useState(false),
  const [address, setAddress] = useState<string>(''),
  const [specialInstructions, setSpecialInstructions] = useState<string>(''),
  const [isSharedWithRoommates, setIsSharedWithRoommates] = useState<boolean>(false),
  const [selectedRoommates, setSelectedRoommates] = useState<string[]>([]),
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<'card' | 'cash'>('card'),
  const [roommates, setRoommates] = useState<Roommate[]>([]),
  const [availableTimes, setAvailableTimes] = useState<string[]>(['08:00', '09:00', '10:00', '11:00', '12: 00',
    '13:00', '14:00', '15:00', '16:00', '17:00', '18: 00']);,
  const { user } = useAuthCompat().authState;
  const bookingService = useStandardizedBookings(),
  const toast = useToast()
  const theme = useTheme();,
  const { colors } = theme;
  const themeColors = colors as any // Type assertion for theme colors // Button component;,
  const Button: React.FC<ButtonProps> = ({ onPress, children, variant = 'primary', style, disabled, title, leftIcon }) => {,
  const buttonStyle = [;
      styles.button;,
  variant = == 'primary' && styles.primaryButton;
      variant = == 'outlined' && styles.outlinedButton;,
  disabled && styles.disabledButton;
      style;,
   ];,
  const textStyle = [;
      styles.buttonText;,
  variant = == 'outlined' && styles.outlinedButtonText;
      disabled && styles.disabledButtonText;,
   ];,
  return (
    <TouchableOpacity style= {buttonStyle} onPress={onPress} disabled={disabled}>,
  {leftIcon && leftIcon}
        <Text style={textStyle}>{title || children}</Text>,
  </TouchableOpacity>
    ),
  }
  useEffect(() => {,
  if (serviceId) {
      loadServiceDetails(),
  loadRoommates()
    } else {,
  toast.showToast({ message: 'Service ID is required', type: 'error' }),
  router.back()
    },
  }, [serviceId]),
  const loadServiceDetails = async () => {
  console.log('🔄 Loading service details for serviceId:', serviceId),
  setIsLoading(true)
    try {,
  const serviceRepository = getServiceRepository()
      console.log('📦 Service repository obtained'),
  const serviceData = await serviceRepository.findServiceWithProvider(serviceId)
      console.log('📋 Service data fetched:', serviceData ? 'SUCCESS'     : 'FAILED'),
  if (!serviceData) {
        toast.showToast({ message: 'Service not found', type: 'error' }),
  router.back()
        return null;,
  }
      // Create a standardized service object with all required properties;,
  const serviceWithProvider: ServiceWithProvider = {;
        ...serviceData;,
  price: serviceData.price || 0,
        contact_info: serviceData.contact_info || '',
    created_at: serviceData.created_at || '',
        updated_at: serviceData.updated_at || '',
        images: serviceData.images || [],
        is_available: serviceData.is_available || false,
        duration: +(serviceData.duration || 60), // Convert to number and ensure duration is set;,
  provider: serviceData.provider ? {
          id   : serviceData.provider.id || 'unknown',
  // Use optional chaining and provide fallbacks for properties that might not exist on Profile
          name: (serviceData.provider as ServiceProvider)? .name || 'Unknown Provider',
  business_name   : (serviceData.provider as ServiceProvider)? .business_name || 'Unknown Provider'
          profile_image : (serviceData.provider as ServiceProvider)? .profile_image || '',
  contact_phone : (serviceData.provider as ServiceProvider)? .contact_phone || ''
          contact_email : (serviceData.provider as ServiceProvider)? .contact_email || '',
  }  : {
          id: 'unknown',
    name: 'Unknown Provider',
  business_name: 'Unknown Provider',
    profile_image: '',
          contact_phone: '',
          contact_email: '',
  }
      },
  setService(serviceWithProvider)
      if (serviceWithProvider.provider && serviceWithProvider.provider.business_name) {,
  // setProviderName(serviceWithProvider.provider.business_name)
      },
  if (serviceWithProvider.price) {
        // setTotalPrice(serviceWithProvider.price),
  }
      setAvailableTimes(['09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16: 00']),
      console.log('✅ Service details loaded successfully'),
  } catch (error) {
      console.error('❌ Error fetching service details:', error),
  toast.showToast({ message: 'Failed to load service details', type: 'error' }),
  router.back()
    } finally {,
  console.log('🏁 Loading completed, setting isLoading to false'),
  setIsLoading(false)
    },
  }
  const loadRoommates = async () => {,
  setRoommates([{ id: 'roommate1', name: 'Alex Johnson' };,
  { id: 'roommate2', name: 'Jamie Smith' }, ,
  { id: 'roommate3', name: 'Taylor Lee' }]),
  }
  const loadAvailableTimes = () => { setAvailableTimes(['09:00', '10:00', '11:00', '13:00', '14:00', '15:00', '16: 00']) },
  useEffect(() => {
  if (currentStep === 'booking-details') {,
  loadAvailableTimes()
    },
  }, [currentStep, selectedDate]),
  const handleDateChange = (event: any, date?: Date) => {,
  setShowDatePicker(false)
    if (date) {,
  setSelectedDate(date)
      setSelectedTime(''),
  }
  },
  const handleTimeSelection = (time: string) => {
  setSelectedTime(time),
  }
  const handleRoommateToggle = (roommateId: string) => {,
  if (selectedRoommates.includes(roommateId)) {
      setSelectedRoommates(prev => prev.filter(id => id !== roommateId)),
  } else {
      setSelectedRoommates(prev => [...prev, roommateId]),
  }
  },
  const validateBookingForm = ($2) => {
  if (!service || !user || !selectedDate || !selectedTime || !address.trim()) {,
  if (showToast) {
        toast.showToast({ message: 'Please fill in all required fields', type: 'info' }),
  };
      return false;,
  }
    return true;,
  }
  const createServiceBooking = async () => {,
  if (!validateBookingForm()) {;
      return null;,
  }
    try {,
  setIsSubmitting(true)
      console.log('🚀 Starting booking creation process'),
  console.log('📋 Service:', { id: service.id, name: service.name, price: service.price }),
  console.log('👤 User:', { id: user.id, email: user.email }),
  console.log('📅 Date/Time:', { selectedDate, selectedTime }),
  console.log('📍 Address:', address),
  // Format date and time;
      const bookingDate = new Date(selectedDate);,
  const [hours, minutes] = selectedTime.split(': ').map(Number);,
  bookingDate.setHours(hours, minutes, 0, 0),
  // Calculate end time based on service duration;
      const endDate = addMinutes(bookingDate, service.duration || 60),
  console.log('⏰ Formatted times:', {,
  bookingDate: bookingDate.toISOString(),
    endDate: endDate.toISOString(),
  });
      // Prepare booking data // Filter out placeholder roommate IDs that aren't valid UUIDs;,
  const validRoommateIds = isSharedWithRoommates;
        ? selectedRoommates.filter(id = > {,
  // Check if the ID is a valid UUID format (basic check)
            const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i, ,
  return uuidPattern.test(id)
          }),
  : [],
  const bookingData: Booking = { service_id: service.id,
    user_id: user.id,
        booking_date: bookingDate.toISOString(),
    end_date: endDate.toISOString(),
  status: BookingStatus.PENDING,
        address: address,
        special_instructions: specialInstructions,
        price: service.price,
        payment_status: PaymentStatus.PENDING,
        shared_with: validRoommateIds.length > 0 ? validRoommateIds   : undefined,
    roommate_shared: isSharedWithRoommates },
  console.log('📦 Booking data prepared:' JSON.stringify(bookingData, null, 2)),
  // Call booking service;
      console.log('🔄 Calling booking service...'),
  const newBooking = await bookingService.createBooking(bookingData)
      console.log('✅ Booking service response:', newBooking),
  if (newBooking) {
        setCurrentStep('confirmation'),
  toast.showToast({ message: 'Your service has been booked successfully', type: 'success' }),
  if (newBooking.id) {
          setBookingId(newBooking.id),
  }
      } else {,
  throw new Error('Failed to create booking')
      },
  } catch (error) {
      console.error('❌ Error creating booking:', error),
  console.error('❌ Error details:', {,
  message: error instanceof Error ? error.message    : 'Unknown error',
    stack: error instanceof Error ? error.stack  : undefined),
  })
      ,
  logError('Error creating booking' error as Error)
       // Show more specific error message if available;,
  const errorMessage = error instanceof Error ? error.message     : 'Failed to create booking. Please try again.'
              toast.showToast({ message: errorMessage type: 'error' }),
  } finally {
      setIsSubmitting(false),
  console.log('🏁 Booking creation process completed')
    },
  }
  const calculateTotalPrice = ($2) => {,
  if (!service) return 0;
    if (isSharedWithRoommates && selectedRoommates.length > 0) {,
  const totalParticipants = selectedRoommates.length + 1;
      return service.price / totalParticipants;,
  }
    return service.price;,
  }
  // Render the comprehensive booking details form;,
  const renderBookingDetailsForm = () => {;
  if (!service) return null;,
  return (
    <ScrollView style= {styles.scrollContainer} showsVerticalScrollIndicator={false}>,
  {/* Service Summary Card */}
        <View style={styles.serviceCard}>,
  <Text style={styles.serviceName}>{service.name}</Text>
          <Text style={styles.serviceProvider}>{service.provider? .business_name}</Text>,
  <View style={styles.serviceDetails}>
            <Text style={styles.servicePrice}>${service.price.toFixed(2)}</Text>,
  <Text style={styles.serviceDuration}>{service.duration} minutes</Text>
          </View>,
  </View>
        {/* Date & Time Selection */},
  <View style={styles.sectionCard}>
          <Text style={styles.sectionTitle}>📅 Date & Time</Text>,
  <TouchableOpacity style={styles.dateButton} onPress={() => setShowDatePicker(true)} activeOpacity={0.7}
          >,
  <Text style={styles.dateButtonText}>
              {selectedDate ? format(selectedDate,  'MMM d, yyyy')   : 'Select Date'},
  </Text>
            <MaterialIcons name="calendar-today" size={24} color={"#3B82F6" /}>,
  </TouchableOpacity>
          {showDatePicker && (,
  <DateTimePicker value={selectedDate || new Date()} mode="date"
              display="default",
  onChange = {handleDateChange} minimumDate={new Date()}
            />,
  )}
          {selectedDate && (,
  <View style={styles.timeContainer}>
              <Text style={styles.timeTitle}>Available Times</Text>,
  <View style={styles.timeGrid}>
                {availableTimes.map((time) => (,
  <TouchableOpacity key={time} style={[styles.timeSlot
                      selectedTime === time && styles.selectedTimeSlot, ,
   ]} onPress = {() => handleTimeSelection(time)},
  >
                    <Text,
  style={[styles.timeSlotText;
                        selectedTime = == time && styles.selectedTimeSlotText;,
   ]},
  >
                      {time},
  </Text>
                  </TouchableOpacity>,
  ))}
              </View>,
  </View>
          )},
  </View>
        {/* Service Location */},
  <View style= {styles.sectionCard}>
          <Text style={styles.sectionTitle}>📍 Service Location</Text>,
  <FormInput
            label="Address";,
  value= {address} onChangeText={setAddress} placeholder="Enter your full address";
            multiline numberOfLines= {2} error={{  address.trim() === '' ? 'Address is required'    : undefined    }},
  />
        </View>,
  {/* Special Instructions */}
        <View style={styles.sectionCard}>,
  <Text style={styles.sectionTitle}>📝 Special Instructions</Text>
          <FormInput,
  label="Special Instructions (Optional)"
            value={specialInstructions} onChangeText={setSpecialInstructions} placeholder="Any special requirements or instructions",
  multiline
            numberOfLines = {3},
  />
        </View>,
  {/* Roommate Sharing */}
        <View style={styles.sectionCard}>,
  <Text style={styles.sectionTitle}>👥 Share with Roommates</Text>
          <View style={styles.shareToggle}>,
  <Text style={styles.shareToggleText}>Split cost with roommates</Text>
            <TouchableOpacity,
  style={{  [styles.toggleButton, {,
  backgroundColor: isSharedWithRoommates ? themeColors.primary    : '#E5E7EB',
    justifyContent: isSharedWithRoommates ? 'flex-end'  : 'flex-start' }}]},
  onPress={() => setIsSharedWithRoommates(!isSharedWithRoommates)}
            >,
  <View style={{styles.toggleCircle} /}>
            </TouchableOpacity>,
  </View>
          {isSharedWithRoommates && (,
  <View style={styles.roommateList}>
              <Text style={styles.roommateListTitle}>Select Roommates:</Text>,
  {roommates.map((roommate) => (
                <TouchableOpacity key={roommate.id} style={[styles.roommateItem,
  selectedRoommates.includes(roommate.id) && styles.selectedRoommateItem;
                  ]} onPress = {() => handleRoommateToggle(roommate.id)},
  >
                  <Text style={styles.roommateName}>{roommate.name}</Text>,
  {selectedRoommates.includes(roommate.id) && (
                    <MaterialIcons name="check" size={20} color={{themeColors.primary} /}>,
  )}
                </TouchableOpacity>,
  ))}
            </View>,
  )}
        </View>,
  {/* Payment Method */}
        <View style={styles.sectionCard}>,
  <Text style={styles.sectionTitle}>💳 Payment Method</Text>
          <View style={styles.paymentMethods}>,
  <TouchableOpacity style={[styles.paymentMethod;
                selectedPaymentMethod = == 'card' && styles.selectedPaymentMethod;,
   ]} onPress= {() => setSelectedPaymentMethod('card')},
  >
              <MaterialIcons,
  name="credit-card"
                size = {24} color={ selectedPaymentMethod === 'card' ? themeColors.primary    : '#666666'  },
  />
              <Text style={styles.paymentMethodText}>Credit/Debit Card</Text>,
  {selectedPaymentMethod === 'card' && (
                <MaterialIcons name="check-circle" size={20} color={{themeColors.primary} /}>,
  )}
            </TouchableOpacity>,
  <TouchableOpacity style={[styles.paymentMethod
                selectedPaymentMethod === 'cash' && styles.selectedPaymentMethod, ,
   ]} onPress={() => setSelectedPaymentMethod('cash')},
  >
              <MaterialIcons,
  name="attach-money"
                size={24} color={ selectedPaymentMethod === 'cash' ? themeColors.primary   : '#666666'  },
  />
              <Text style={styles.paymentMethodText}>Cash on Delivery</Text>,
  {selectedPaymentMethod === 'cash' && (
                <MaterialIcons name="check-circle" size={20} color={{themeColors.primary} /}>,
  )}
            </TouchableOpacity>,
  </View>
        </View>,
  {/* Price Summary */}
        <View style={styles.priceSummaryCard}>,
  <Text style={styles.priceSummaryTitle}>💰 Price Summary</Text>
          <View style={styles.priceRow}>,
  <Text style={styles.priceLabel}>Service Price</Text>
            <Text style={styles.priceValue}>${service.price.toFixed(2)}</Text>,
  </View>
          {isSharedWithRoommates && selectedRoommates.length > 0 && (,
  <View style={styles.priceRow}>
              <Text style={styles.priceLabel}>Split with {selectedRoommates.length} roommates</Text>,
  <Text style={styles.priceValue}>
                -${((service.price * selectedRoommates.length) / (selectedRoommates.length + 1)).toFixed(2)},
  </Text>
            </View>,
  )}
          <View style={styles.totalRow}>,
  <Text style={styles.totalLabel}>Your Total</Text>
            <Text style={styles.totalValue}>${calculateTotalPrice().toFixed(2)}</Text>,
  </View>
        </View>,
  {/* Bottom spacing for scroll */}
        <View style={{ height: 100} /}>,
  </ScrollView>
    ),
  }
  // Render confirmation,
  const renderConfirmation = () => {
  return (,
  <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        <View style={styles.confirmationContainer}>,
  <View style={styles.confirmationIcon}>
            <MaterialIcons name="check" size={40} color={"#FFFFFF" /}>,
  </View>
          <Text style={styles.confirmationTitle}>Booking Confirmed!</Text>,
  <Text style={styles.confirmationText}>
            Your booking has been successfully confirmed. You will receive a confirmation email shortly.,
  </Text>
          <View style={styles.serviceCard}>,
  <Text style={styles.priceSummaryTitle}>Booking Details</Text>
            <View style={styles.priceRow}>,
  <Text style={styles.priceLabel}>Booking ID:</Text>
              <Text style={styles.priceValue}>{bookingId || 'N/A'}</Text>,
  </View>
            <View style={styles.priceRow}>,
  <Text style={styles.priceLabel}>Service:</Text>
              <Text style={styles.priceValue}>{service? .name}</Text>,
  </View>
            <View style={styles.priceRow}>,
  <Text style={styles.priceLabel}>Date & Time    : </Text>
              <Text style={styles.priceValue}>,
  {selectedDate && format(selectedDate 'MMM d,  yyyy')} at {selectedTime},
  </Text>
            </View>,
  <View style={styles.priceRow}>
              <Text style={styles.priceLabel}>Total Amount:</Text>,
  <Text style={styles.priceValue}>${calculateTotalPrice().toFixed(2)}</Text>
            </View>,
  </View>
          <View style={styles.confirmationActions}>,
  <Button onPress={() => router.push('/bookings' as any)} variant="outlined"
              style={{  marginBottom: 12 }},
  >
              View My Bookings;,
  </Button>
            <Button onPress= {() => router.push('/(tabs)' as any)} variant="outlined",
  >
              Back to Home;,
  </Button>
          </View>,
  </View>
      </ScrollView>,
  )
  },
  if (isLoading) {
    return (,
  <View style= {styles.loadingContainer}>
        <Text>Loading service details...</Text>,
  </View>
    ),
  }
  if (error || !service) {,
  return (
    <View style={styles.errorContainer}>,
  <Text style={styles.errorText}>{error || 'Service not found'}</Text>
        <Button onPress={() => router.back()}>Go Back</Button>,
  </View>
    ),
  }
  return (,
  <View style={styles.container}>
      {/* Header */},
  <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>,
  <MaterialIcons name="arrow-back" size={24} color={"#000" /}>
        </TouchableOpacity>,
  <Text style={styles.headerTitle}>
          {currentStep === 'booking-details' ? 'Book Service'    : 'Booking Confirmed'},
  </Text>
      </View>,
  {/* Content */}
      {currentStep === 'booking-details' ? renderBookingDetailsForm(): renderConfirmation()} {,
  {
      {/* Bottom Action Button */},
  {currentStep === 'booking-details' && (
        <View style={styles.bottomContainer}>,
  <Button onPress={createServiceBooking} disabled={isSubmitting} style={styles.bookButton}
          >,
  {isSubmitting ? 'Creating Booking...' : `Book Now - $${calculateTotalPrice().toFixed(2)}`}
          </Button>,
  </View>
      )},
  </View>
  ),
  }
export default ServiceBookingScreen,
  const styles = StyleSheet.create({
  container: {,
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {,
    flexDirection: 'row',
  alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: { padding: 8 },
  headerTitle: { fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 16 },
  scrollContainer: { flex: 1 },
  serviceCard: { backgroundColor: '#FFFFFF',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    padding: 16,
    marginBottom: 16 },
  serviceName: { fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8 },
  serviceProvider: { fontSize: 14,
    color: '#6B7280',
    marginBottom: 8 },
  serviceDetails: {,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  servicePrice: { fontSize: 18,
    fontWeight: '600',
    color: '#10B981',
    marginBottom: 8 },
  serviceDuration: { fontSize: 14,
    color: '#6B7280',
    marginBottom: 16 },
  sectionCard: { padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    marginBottom: 16 },
  sectionTitle: { fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8 },
  dateButton: { borderWidth: 2,;,
  borderColor: '#3B82F6', // Blue border for better visibility;,
  borderRadius: 8,
    padding: 16,
    backgroundColor: '#FFFFFF',
    marginBottom: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {,
    width: 0,
      height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  dateButtonText: {,
    fontSize: 16,
    color: '#374151',
    fontWeight: '500',
  },
  timeContainer: { marginTop: 16 },
  timeTitle: {,
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
    color: '#374151',
  },
  timeGrid: {,
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  timeSlot: {,
    width: '30%',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    marginBottom: 8,
    backgroundColor: '#FFFFFF',
  },
  selectedTimeSlot: {,
    backgroundColor: '#3B82F6',
    borderColor: '#3B82F6',
  },
  timeSlotText: {,
    fontSize: 14,
    textAlign: 'center',
    color: '#374151',
  },
  selectedTimeSlotText: {,
    color: '#FFFFFF',
    fontWeight: '600',
  },
  shareToggle: { flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16 },
  shareToggleText: { fontSize: 16 },
  toggleButton: {,
    width: 50,
    height: 30,
    borderRadius: 15,
    padding: 2,
    backgroundColor: '#E5E7EB',
    justifyContent: 'flex-start',
  },
  toggleCircle: {,
    width: 26,
    height: 26,
    borderRadius: 13,
    backgroundColor: '#FFFFFF',
  },
  roommateList: { marginTop: 16 },
  roommateListTitle: { fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8 },
  roommateItem: {,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  selectedRoommateItem: {,
    backgroundColor: '#F3F4F6',
  },
  roommateName: { fontSize: 16,
    marginLeft: 12 },
  paymentMethods: { marginBottom: 16 },
  paymentMethod: { flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8 },
  selectedPaymentMethod: {,
    borderColor: '#3B82F6',
  },
  paymentMethodText: { fontSize: 14,
    marginLeft: 12 },
  priceSummaryCard: { backgroundColor: '#FFFFFF',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    padding: 16,
    marginBottom: 16 },
  priceSummaryTitle: { fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8 },
  priceRow: { flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8 },
  priceLabel: {,
    fontSize: 14,
    color: '#666666',
  },
  priceValue: {,
    fontSize: 14,
    fontWeight: '500',
  },
  totalRow: {,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  totalLabel: {,
    fontSize: 16,
    fontWeight: 'bold',
  },
  totalValue: {,
    fontSize: 16,
    fontWeight: 'bold',
    color: '#10B981',
  },
  bottomContainer: {,
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    backgroundColor: '#FFFFFF',
  },
  bookButton: { width: '100%',
    marginBottom: 12 },
  loadingContainer: {,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: { flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16 },
  errorText: { color: '#EF4444',
    fontSize: 12,
    marginBottom: 16 },
  // Button styles;,
  button: {,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  primaryButton: {,
    backgroundColor: '#3B82F6',
  },
  outlinedButton: {,
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#3B82F6',
  },
  disabledButton: {,
    backgroundColor: '#E5E7EB',
  },
  buttonText: {,
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  outlinedButtonText: {,
    color: '#3B82F6',
  },
  disabledButtonText: {,
    color: '#9CA3AF',
  },
  confirmationContainer: { alignItems: 'center',
    padding: 32 },
  confirmationIcon: { width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#10B981',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24 },
  confirmationTitle: {,
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  confirmationText: { fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 24 },
  confirmationActions: { width: '100%',
    marginTop: 24 },
  // Input styles;,
  inputContainer: { marginBottom: 16 },
  inputLabel: { fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8 },
  input: {,
    borderWidth: 2,
    borderColor: '#3B82F6', // Blue border for better visibility, ,
  borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16),
    backgroundColor: '#FFFFFF'),
    color: '#374151',
  },
  inputError: {,
    borderColor: '#EF4444'),
  }
})