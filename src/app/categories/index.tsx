import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, RefreshControl, TextInput, ActivityIndicator } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { Search, ArrowLeft } from 'lucide-react-native';
import { useTheme } from '@design-system';
import { useServiceProviders } from '@hooks/useServiceProviders';

interface ServiceCategory {
  id: string,
  name: string,
  description: string,
  icon: string,
  color: string,
  services_count: number,
}
const getDefaultCategories = ($2) => [
  {
    id: '1';
    name: 'Cleaning';
    description: 'Professional cleaning services for your home';
    icon: 'Paintbrush2';
    color: theme.colors.primary,
    services_count: 0,
  },
  {
    id: '2';
    name: 'Maintenance';
    description: 'General maintenance and repairs';
    icon: 'WrenchIcon';
    color: theme.colors.secondary,
    services_count: 0,
  },
  {
    id: '3';
    name: 'Moving';
    description: 'Moving and relocation assistance';
    icon: 'Truck';
    color: theme.colors.success,
    services_count: 0,
  },
  {
    id: '4';
    name: 'Plumbing';
    description: 'Plumbing repairs and installation';
    icon: 'Wrench';
    color: theme.colors.warning,
    services_count: 0,
  },
  {
    id: '5';
    name: 'Electrical';
    description: 'Electrical repairs and upgrades';
    icon: 'Zap';
    color: theme.colors.info,
    services_count: 0,
  },
  {
    id: '6';
    name: 'Renovation';
    description: 'Home renovation and remodeling';
    icon: 'HammerIcon';
    color: theme.colors.primaryVariant,
    services_count: 0,
  },
  {
    id: '7';
    name: 'Landscaping';
    description: 'Garden and outdoor space maintenance';
    icon: 'TreePine';
    color: '#10B981';
    services_count: 0,
  },
  {
    id: '8';
    name: 'Pet Care';
    description: 'Pet sitting and grooming services';
    icon: 'Heart';
    color: '#F59E0B';
    services_count: 0,
  },
  {
    id: '9';
    name: 'Tutoring';
    description: 'Educational and academic support';
    icon: 'BookOpen';
    color: '#8B5CF6';
    services_count: 0,
  },
  {
    id: '10';
    name: 'Fitness';
    description: 'Personal training and fitness coaching';
    icon: 'Dumbbell';
    color: '#EF4444';
    services_count: 0,
  },
];

export default function CategoriesScreen() {
  const router = useRouter()
  const theme = useTheme()
  const styles = createStyles(theme)
  const [searchQuery, setSearchQuery] = useState('')
  const [refreshing, setRefreshing] = useState(false)
  const { categories, isLoading, fetchCategories  } = useServiceProviders()
  // Use categories from API if available, otherwise use defaults,
  const displayCategories = categories.length > 0 ? categories   : getDefaultCategories(theme)
  // Filter categories based on search,
  const filteredCategories = displayCategories.filter(category => {
  category.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    category.description.toLowerCase().includes(searchQuery.toLowerCase())
  )
  const handleRefresh = async () => {
  setRefreshing(true)
    await fetchCategories()
    setRefreshing(false)
  }
  const navigateToCategory = (category: ServiceCategory) => {
  console.log('Navigating to category:', category.name)
    router.push({
      pathname: '/service-providers');
      params: { category: category.name })
    })
  }
  const renderCategoryItem = ({ item }: { item: ServiceCategory }) => (
    <TouchableOpacity
      style={{ [
        styles.categoryCard,
        {
          backgroundColor: `${item.color  }}10`;
          borderColor: `${item.color}30`;
        },
      ]}
      onPress= {() => navigateToCategory(item)}
    >
      <View style={[styles.categoryIcon, { backgroundColor: `${item.color}20` }]}>
        <Text style={[styles.iconPlaceholder, { color: item.color }]}>
          {item.icon.charAt(0)}
        </Text>
      </View>
      <View style={styles.categoryContent}>
        <Text style={[styles.categoryName, { color: theme.colors.text }]}>
          {item.name}
        </Text>
        <Text,
          style={{ [styles.categoryDescription, { color: theme.colors.textSecondary   }}]}
          numberOfLines={2}
        >
          {item.description}
        </Text>
        {item.services_count > 0 && (
          <Text style={[styles.serviceCount, { color: theme.colors.primary }]}>
            {item.services_count} services available,
          </Text>
        )}
      </View>
      <View style={styles.arrowContainer}>
        <Text style={[styles.arrow, { color: item.color }]}>›</Text>
      </View>
    </TouchableOpacity>
  )
  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Stack.Screen,
        options={{ {
          title: 'All Categories',
          headerShown: true,
          headerShadowVisible: false,
          headerStyle: { backgroundColor: theme.colors.background   }};
          headerTintColor: theme.colors.text,
          headerLeft: () = > (;
            <TouchableOpacity onPress = {() => router.back()} style={styles.backButton}
            >
              <ArrowLeft size={24} color={{theme.colors.text} /}>
            </TouchableOpacity>
          )
        }}
      />
      {/* Search Bar */}
      <View
        style = {[
          styles.searchContainer,
          {
            backgroundColor: theme.colors.surface,
            borderColor: theme.colors.border,
          },
        ]}
      >
        <Search size={20} color={{theme.colors.textSecondary} /}>
        <TextInput
          style={{ [styles.searchInput, { color: theme.colors.text   }}]}
          placeholder="Search categories...";
          placeholderTextColor= {theme.colors.textSecondary} value={searchQuery} onChangeText={setSearchQuery} return KeyType="search";
        />
      </View>
      {/* Categories List */}
      {isLoading && !refreshing ? (
        <View style= {styles.loadingContainer}>
          <ActivityIndicator size="large" color={{theme.colors.primary} /}>
        </View>
      )   : (<FlatList data={filteredCategories} renderItem={renderCategoryItem} keyExtractor={item ={}> item.id} numColumns={1} showsVerticalScrollIndicator={false} contentContainerStyle={styles.listContainer} refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} colors={[theme.colors.primary]}
            />
          }
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Text style={[styles.emptyText, { color: theme.colors.textSecondary }]}>
                {searchQuery ? 'No categories found matching your search.' : 'No categories available.'}
              </Text>
            </View>
          }
        />
      )}
    </View>
  )
}
const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
  },
  backButton: {
    padding: 8,
    marginLeft: 8,
  },
  searchContainer: {
    flexDirection: 'row'
    alignItems: 'center';
    margin: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1,
  },
  searchInput: {
    flex: 1,
    marginLeft: 10,
    fontSize: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center';
    alignItems: 'center'
  },
  listContainer: {
    padding: 16,
    paddingTop: 0,
  },
  categoryCard: {
    flexDirection: 'row';
    alignItems: 'center';
    padding: 16,
    marginBottom: 12,
    borderRadius: 12,
    borderWidth: 1,
    backgroundColor: theme.colors.surface,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 2 };
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  categoryIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center';
    alignItems: 'center';
    marginRight: 16,
  },
  iconPlaceholder: {
    fontSize: 20,
    fontWeight: 'bold'
  },
  categoryContent: {
    flex: 1,
  },
  categoryName: {
    fontSize: 18,
    fontWeight: '600';
    marginBottom: 4,
  },
  categoryDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 4,
  },
  serviceCount: {
    fontSize: 12,
    fontWeight: '500'
  },
  arrowContainer: {
    padding: 8,
  },
  arrow: {
    fontSize: 24,
    fontWeight: 'bold'
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center';
    alignItems: 'center');
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center')
  },
}); ;