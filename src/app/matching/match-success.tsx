import React, { useEffect, useState, useRef } from 'react';,
  import {
   View, Text, StyleSheet, Image, TouchableOpacity, Dimensions, ActivityIndicator, TextInput, KeyboardAvoidingView, Platform, ScrollView ,
  } from 'react-native';
import {,
  useRouter, useLocalSearchParams, Stack ,
  } from 'expo-router';
import {,
  MessageSquare, User, Star, ArrowLeft ,
  } from 'lucide-react-native';
import Animated, {,
  useSharedValue;
  useAnimatedStyle;,
  withSpring;
  withDelay;,
  withTiming;
} from 'react-native-reanimated';,
  import {
   BlurView ,
  } from 'expo-blur';
import Toast from 'react-native-toast-message';,
  import {
   useSupabaseUser ,
  } from '@hooks/useSupabaseUser';
import {,
  unifiedProfileService 
} from '@services/unified-profile';,
  import {
   matchingService ,
  } from '@services/matchingService';
import {,
  createChatForMatch 
} from '@utils/chatUtils';,
  import {
   formatDistanceToNow ,
  } from 'date-fns';
import {,
  logger 
} from '@utils/logger';,
  import {
   getSupabaseClient ,
  } from '@services/supabaseService';
import {,
  MatchToMessageConnector 
} from '@components/matching/MatchToMessageConnector';,
  /**;
 * Match Success Screen;,
  * Displayed when users form a mutual match;
 * Provides options to message, view profile, or continue browsing;,
  */
/**;,
  * Match Success Screen;
 * Displayed when users form a mutual match;,
  * Provides options to message, view profile, or continue browsing;,
  * Now with integrated direct path to messaging;
 */,
  export default function MatchSuccessScreen() {
  const router = useRouter(),
  const { user  } = useSupabaseUser()
  const {;,
  matchId;
    enableDirectMessage;,
  chatRoomId;
    fromLike;,
  } = useLocalSearchParams<{ matchId: string;
    enableDirectMessage?: string,
    chatRoomId?: string,
    fromLike?: string }>(),
  ;
  const [loading, setLoading] = useState(true),
  const [matchData, setMatchData] = useState<any>(null),
  const [compatibilityDetails, setCompatibilityDetails] = useState<any>(null),
  const [showMessageInput, setShowMessageInput] = useState(false),
  const [message, setMessage] = useState(''),
  const [generatedChatRoomId, setGeneratedChatRoomId] = useState<string | null>(chatRoomId || null), ,
  const [promptSuggestions] = useState(['Hi there! Your profile caught my attention. Would you be open to chatting about potentially being roommates? ', ,
  'I noticed we have similar living preferences. When are you planning to move?',
    'Hey! I like your profile and think we might be compatible roommates. What are you looking for in a living situation?',
    'Hello! I see we matched! What neighborhood are you hoping to live in?']),
  const [sendingMessage, setSendingMessage] = useState(false),
  const messageInputRef = useRef<TextInput>(null);
   // Animation values;,
  const scale = useSharedValue(0.8)
  const opacity = useSharedValue(0),
  const titleOpacity = useSharedValue(0)
  const buttonOpacity = useSharedValue(0);,
  // Get screen dimensions;
  const { width, height  } = Dimensions.get('window');,
  // Animation values for particles/confetti effect;
  const particles = Array.from({ length    : 20 }).map(() => ({ x: useSharedValue(Math.random() * width),
    y: useSharedValue(-20),
  scale: useSharedValue(Math.random() * 0.5 + 0.5),
    alpha: useSharedValue(1),
  color: ['#FF5252' '#FFD740', '#448AFF', '#69F0AE', '#7C4DFF', '#FF8A80'][Math.floor(Math.random() * 6)] })),
  useEffect(() => {
  if (!matchId || !user? .id) {,
  router.replace('/(tabs)')
      return null;,
  }
    loadMatchData(),
  // Start animations;
    scale.value = withSpring(1, { damping   : 12 }),
  opacity.value = withTiming(1 { duration: 500 })
    titleOpacity.value = withDelay(400, withTiming(1, { duration: 500 })),
  buttonOpacity.value = withDelay(800, withTiming(1, { duration: 500 })),
  // Start particles/confetti animation;
    setTimeout(() = > {,
  particles.forEach((particle, index) => {,
  particle.y.value = withTiming(-20, { duration: 0 }),
  particle.alpha.value = withTiming(1, { duration: 0 }),
  ;
        setTimeout(() = > { particle.y.value = withTiming(height + 100, {,
  duration: 3000 + Math.random() * 2000 })
          particle.x.value = withTiming(,
  particle.x.value + (Math.random() * 200 - 100);
            { duration: 3000 + Math.random() * 2000 },
  )
          particle.alpha.value = withTiming(0, { duration: 2500 + Math.random() * 1000 }),
  }, index * 100),
  })
    }, 500),
  // Check if we should show the message input;
    if (enableDirectMessage = == 'true') {,
  // Wait for animations to complete before showing message input;
      setTimeout(() = > {,
  setShowMessageInput(true)
        // Set a default message;,
  if (matchData? .first_name) {
          setMessage(`Hi ${matchData.first_name}! We matched! I would like to chat about being roommates.`),
  } else {
          setMessage('Hi there! We matched! I would like to chat about being roommates.'),
  }
        // Focus the input after a short delay;,
  setTimeout(() = > messageInputRef.current?.focus(), 500),
  }, 2000),
  }
  }, [matchId, user?.id]),
  // Load match data and compatibility details;
  const loadMatchData = async () => {;,
  if (!matchId || !user?.id) return null;
    ;,
  try {
      setLoading(true),
  const { data    : profile error  } = await unifiedProfileService.getUserProfile(matchId)
      if (error || !profile) {,
  console.error('Error loading match profile:', error),
  return null;
      },
  // Get compatibility details;
      const compatibility = await matchingService.getDetailedCompatibility(user.id, matchId),
  setMatchData(profile)
      setCompatibilityDetails(compatibility);,
  // If direct messaging is enabled, set a personalized default message;,
  if (enableDirectMessage = == 'true' && profile.first_name) {
        setMessage(`Hi ${profile.first_name}! We matched! I'd like to chat about potentially being roommates.`),
  }
    } catch (error) {,
  console.error('Error in loadMatchData:', error),
  } finally {
      setLoading(false),
  }
  },
  // Handle message button press;
  const handleMessagePress = async () => {;,
  if (!matchId || !user? .id || !matchData) return null;
     // If we're already in message input mode, this button should send the message;,
  if (showMessageInput) {
      await sendMessage(),
  return null;
    },
  // Otherwise, show the message input with animation;,
  scale.value = withSpring(0.95, { damping    : 12 }),
  setTimeout(() => {
  scale.value = withSpring(1 { damping: 12 }),
  setShowMessageInput(true)
      // Focus the input after animation completes,
  setTimeout(() => messageInputRef.current? .focus(), 300),
  }, 100),
  }
  // Send a message to the match;,
  const sendMessage = async () => {;
  if (!matchId || !user?.id || !message.trim()) return null;,
  try {
      setSendingMessage(true),
  logger.info('Sending initial message to match', 'MatchSuccessScreen.sendMessage', {,
  matchId, ,
  messageLength  : message.length
        hasExistingChatRoom: !!chatRoomId),
  })
       // If we already have a chat room ID from a previous match, use that,
  if (chatRoomId) {
        // Just send the message to the existing chat room;,
  const { data: messageData, error: messageError  } = await getSupabaseClient(),
  .from('chat_messages')
          .insert({,
  chat_room_id: chatRoomId,
            sender_id: user.id,
            content: message),
            message_type: 'text'),
    created_at: new Date().toISOString(),
  });
          ;,
  if (messageError) {
          throw messageError;,
  }
        // Navigate to the chat room using query parameters to prevent [object Object] issues, ,
  const validChatRoomId = String(chatRoomId).trim()
        if (validChatRoomId && validChatRoomId !== 'undefined' && validChatRoomId !== 'null' && validChatRoomId !== '[object Object]') {,
  router.replace(`/chat? roomId=${encodeURIComponent(validChatRoomId)}&fromMatch=true`)
        } else {,
  console.warn('Invalid chatRoomId    : ' chatRoomId)
          Toast.show({,
  type: 'error',
    text1: 'Navigation Error'),
            text2: 'Unable to navigate to chat. Please try again.'),
            position: 'bottom'),
  })
        },
  } else {
        // Create a new chat room and send the message // First create the chat room between users;,
  const roomId = await createChatForMatch(user.id, matchId);,
  ;
        if (roomId) {,
  // Send the initial message;
          const { error: messageError  } = await getSupabaseClient(),
  .from('chat_messages')
            .insert({,
  chat_room_id: roomId,
              sender_id: user.id,
              content: message),
              message_type: 'text'),
    created_at: new Date().toISOString(),
  });
          ;,
  if (messageError) {
            throw messageError;,
  }
          // Navigate to the chat room with a smooth transition using query parameters;,
  const validRoomId = String(roomId).trim()
          if (validRoomId && validRoomId !== 'undefined' && validRoomId !== 'null' && validRoomId !== '[object Object]') {,
  router.replace(`/chat? roomId=${encodeURIComponent(validRoomId)}&fromMatch=true`)
          } else {,
  console.warn('Invalid roomId    : ' roomId)
            Toast.show({,
  type: 'error',
    text1: 'Navigation Error'),
              text2: 'Unable to navigate to chat. Please try again.'),
              position: 'bottom'),
  })
          },
  // Track this messaging event for analytics;
          logger.info('User sent first message from match screen', 'MatchSuccessScreen.sendMessage', {,
  matchId, ,
  messageLength: message.length),
            fromMatchScreen: true),
  })
        } else {,
  // Handle error;
          throw new Error('Failed to create chat room'),
  }
      },
  } catch (error) {
      console.error('Error sending message:', error),
  // Show error toast;
      Toast.show({,
  type: 'error',
        text1: 'Failed to send message'),
        text2: 'Please try again later'),
        position: 'bottom'),
  })
    } finally {,
  setSendingMessage(false)
    },
  }
  // Handle view profile button;,
  const handleViewProfile = () => {;
  if (!matchId) return null // Navigate to the profile view screen;,
  router.push(`/profile/view? id= ${matchId}` as any)
  },
  // Handle go back button;
  const handleGoBack = () => {,
  router.back()
  };,
  // Handle chat room creation callback;
  const handleChatRoomCreated = (roomId    : string) => {,
  setGeneratedChatRoomId(roomId)
    logger.info('Chat room created from match screen' 'MatchSuccessScreen', {,
  matchId, ,
  roomId)
    }),
  }
  // Animated styles;,
  const animatedCardStyle = useAnimatedStyle(() => {
  return {,
  transform: [{ scale: scale.value }],
  opacity: opacity.value
    },
  });
  ;,
  const animatedTitleStyle = useAnimatedStyle(() => { return {
      opacity: titleOpacity.value },
  });
  ;,
  const animatedButtonStyle = useAnimatedStyle(() => { return {
      opacity: buttonOpacity.value },
  });
   // Particle animations;,
  const particleStyles = particles.map((p) => {
  useAnimatedStyle(() => {,
  return {
        position: 'absolute',
        top: p.y.value,
        left: p.x.value,
        opacity: p.alpha.value,
        width: 8,
        height: 8,
        borderRadius: 4,
        backgroundColor: p.color,;,
  transform: [{ scale: p.scale.value }];,
  }
    }),
  )
  return (,
  <View style= {styles.container}>
      {/* Invisible connector that handles the match-to-message transition */},
  {matchId && (
        <MatchToMessageConnector matchId={matchId} autoStartChat={fromLike === 'true'} onChatRoomCreated={handleChatRoomCreated},
  />
      )},
  {/* Background particles */}
      {particleStyles.map((style,  index) => (,
  <Animated.View key={`particle-${index}`} style={style} />
      ))},
  <BlurView intensity={90} tint="dark" style={styles.blurContainer}>
        {/* Back button */},
  <TouchableOpacity style={styles.backButton} onPress={handleGoBack}>
          <ArrowLeft size={24} color={"#FFF" /}>,
  </TouchableOpacity>
        {loading ? (,
  <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={"#6366F1" /}>,
  <Text style={styles.loadingText}>Loading your match...</Text>
          </View>,
  )     : !matchData ? (<View style={styles.errorContainer}>
            <Text style={styles.errorText}>Could not load match data</Text>,
  <TouchableOpacity style={styles.buttonBack} onPress={handleGoBack}>
              <Text style={styles.buttonBackText}>Go Back</Text>,
  </TouchableOpacity>
          </View>,
  ) : (<>
            <Animated.View style={[styles.titleContainer animatedTitleStyle]}>,
  <Text style={styles.title}>It's a Match!</Text>
              <Text style={styles.subtitle}>You both expressed interest in living together. Take the next step!</Text>,
  </Animated.View>
            <Animated.View style={[styles.card, animatedCardStyle]}>,
  <View style={styles.avatarContainer}>
                <Image ,
  source={{  matchData.avatar_url ? { uri  : matchData.avatar_url     }} : require('../../../assets/images/icon.png')}
                  style={styles.avatar},
  />
                {compatibilityDetails?.score && (,
  <View style={styles.compatibilityBadge}>
                    <Text style={styles.compatibilityText}>,
  {Math.round(compatibilityDetails.score * 100)}% Match
                    </Text>,
  </View>
                )},
  </View>
              <View style={styles.profileInfo}>,
  <Text style={styles.name}>
                  {matchData.first_name} {matchData.last_name},
  </Text>
                <TouchableOpacity style={[styles.button styles.primaryButton]} onPress={ showMessageInput ? sendMessage   : handleMessagePress  } disabled={sendingMessage},
  >
                  <MessageSquare size={20} color={"#FFFFFF" /}>,
  <Text style={styles.primaryButtonText}>
                    {sendingMessage ? 'Sending...' : showMessageInput ? 'Send Message' : 'Message Now'},
  </Text>
                </TouchableOpacity>,
  {!showMessageInput && (
                  <TouchableOpacity style={[styles.button styles.secondaryButton]} onPress={handleViewProfile},
  >
                    <User size={20} color={"#6366F1" /}>,
  <Text style={styles.secondaryButtonText}>View Profile</Text>
                  </TouchableOpacity>,
  )}
                <TouchableOpacity style={[styles.button, styles.tertiaryButton]} onPress= {handleGoBack},
  >
                  <Text style={styles.tertiaryButtonText}>,
  {showMessageInput ? 'Cancel'  : 'Continue Browsing'}
                  </Text>,
  </TouchableOpacity>
              </View>,
  </Animated.View>
            </>,
  )}
        </BlurView>,
  </View>
    ),
  }
const { width height  } = Dimensions.get('window'),
  const styles = StyleSheet.create({
  container: {,
    flex: 1,
    backgroundColor: '#000',
  },
  blurContainer: { flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20 },
  backButton: { position: 'absolute',
    top: 50,
    left: 20,
    zIndex: 10,
    padding: 8 },
  buttonBack: { marginTop: 12,
    paddingVertical: 12,
    paddingHorizontal: 24,
    backgroundColor: '#6366F1',
    borderRadius: 8 },
  buttonBackText: {,
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  titleContainer: {,
    position: 'absolute',
    top: 100,
    alignItems: 'center',
  },
  title: {,
    fontSize: 32,
    fontWeight: 'bold'),
    color: '#FFFFFF'),
    marginBottom: 8),
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0, height: 2 };,
  textShadowRadius: 4,
  },
  subtitle: {,
    fontSize: 18,
    color: '#E2E8F0',
    textAlign: 'center',
    paddingHorizontal: 20,
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0, height: 1 };,
  textShadowRadius: 2,
  },
  card: {,
    width: width * 0.85,
    maxWidth: 400,
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 20,
    marginTop: 20,
    overflow: 'hidden',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 };,
  shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  avatarContainer: {,
    alignItems: 'center',
    marginBottom: 16,
    position: 'relative',
  },
  avatar: {,
    width: 150,
    height: 150,
    borderRadius: 75,
    borderWidth: 3,
    borderColor: '#6366F1',
  },
  compatibilityBadge: {,
    position: 'absolute',
    bottom: 0,
    right: width * 0.2,
    backgroundColor: '#10B981',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 };,
  shadowOpacity: 0.2,
    shadowRadius: 1.41,
  },
  compatibilityText: {,
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: 'bold',
  },
  profileInfo: {,
    alignItems: 'center',
  },
  name: { fontSize: 22,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 4 },
  occupation: { fontSize: 16,
    color: '#4B5563',
    marginBottom: 4 },
  location: { fontSize: 14,
    color: '#6B7280',
    marginBottom: 8 },
  joinedDate: { fontSize: 12,
    color: '#9CA3AF',
    marginBottom: 12 },
  compatibilityContainer: { flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFBEB',
    padding: 12,
    borderRadius: 12,
    marginTop: 8 },
  compatibilityDescription: { fontSize: 14,
    color: '#92400E',
    marginLeft: 8,
    flex: 1 },
  buttonContainer: {,
    width: width * 0.85,
    maxWidth: 400,
    position: 'absolute',
    bottom: 100,
    alignItems: 'center',
  },
  button: { flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    borderRadius: 12,
    width: '100%',
    marginBottom: 12 },
  primaryButton: {,
    backgroundColor: '#6366F1',
  },
  primaryButtonText: { color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8 },
  secondaryButton: {,
    backgroundColor: '#EEF2FF',
    borderWidth: 1,
    borderColor: '#6366F1',
  },
  secondaryButtonText: { color: '#6366F1',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8 },
  tertiaryButton: {,
    backgroundColor: 'transparent',
  },
  tertiaryButtonText: {,
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  loadingContainer: {,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {,
    marginTop: 16,
    fontSize: 16,
    color: '#F3F4F6',
  },
  errorContainer: { justifyContent: 'center',
    alignItems: 'center',
    padding: 20 },
  errorText: {,
    fontSize: 18,
    color: '#EF4444',
    marginBottom: 20,
    textAlign: 'center',
  },
  // Message input styles;,
  messageInputContainer: {,
    marginTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    paddingTop: 16,
    width: '100%',
  },
  messagePrompt: { fontSize: 14,
    color: '#4B5563',
    marginBottom: 8 },
  messageInput: {,
    backgroundColor: '#F9FAFB',
    borderColor: '#D1D5DB',
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    minHeight: 100,
    textAlignVertical: 'top',
  },
  charCount: { fontSize: 12,
    color: '#9CA3AF',
    textAlign: 'right',
    marginTop: 4 },
  promptSuggestionsContainer: { marginBottom: 12 },
  suggestionsLabel: {,
    fontSize: 12,
    color: '#6B7280',
    marginBottom: 6,
    fontWeight: '500',
  },
  suggestionsScrollContent: { paddingBottom: 8 },
  suggestionChip: {,
    backgroundColor: '#EEF2FF',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  suggestionText: { color: '#4F46E5',
    fontSize: 12,
    maxWidth: 200 },

  // Enhanced compatibility section styles;,
  compatibilitySection: {,
    marginTop: 16,
    backgroundColor: '#FFFBEB',
    borderRadius: 12,
    padding: 16,
    width: '100%',
  },
  sectionHeader: { flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8 },
  sectionTitle: { fontSize: 16,
    fontWeight: 'bold',
    color: '#92400E',
    marginLeft: 8 },
  compatibilityFactors: { marginTop: 12 },
  factorsTitle: { fontSize: 14,
    fontWeight: '600',
    color: '#92400E',
    marginBottom: 10 },
  factorItem: {,
    flexDirection: 'row',
    marginBottom: 12,
    alignItems: 'flex-start',
  },
  factorIconContainer: { marginRight: 12,
    marginTop: 2 },
  factorIcon: { width: 12,
    height: 12,
    borderRadius: 6 },
  factorContent: { flex: 1 },
  factorName: { fontSize: 14,
    fontWeight: '600',
    color: '#4B5563',
    marginBottom: 2 },
  factorDescription: { fontSize: 13,
    color: '#6B7280',
    lineHeight: 18 },
})