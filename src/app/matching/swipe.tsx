import React, { useState, useEffect } from 'react';
import { useTheme } from '@design-system';

import { View, Text, StyleSheet, SafeAreaView, TouchableOpacity } from 'react-native';
import { useRouter } from 'expo-router';
import { ArrowLeft, Sliders, Filter } from 'lucide-react-native';

import { colors } from '@constants/colors';
import { useSupabaseUser } from '@hooks/useSupabaseUser';
import SwipeMatchDeck from '@components/matching/SwipeMatchDeck';
import MatchFilter from '@components/matching/MatchFilter';

export default function SwipeMatchScreen() {
  const router = useRouter();
  const {
  const theme = useTheme();
 user } = useSupabaseUser();
  // We're using the MatchFilter component which manages its own state through the store;

  // Handle navigation to browse screen;
  const handleGoToBrowse = () => {
  router.push('/(tabs)/browse' as any);
  }
  // Handle when no more matches are available;
  const handleNoMoreMatches = () => {
  // Could show a modal suggesting to adjust preferences;
    console.log('No more matches available');
  }
  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <ArrowLeft size={24} color={{theme.colors.gray[700]} /}>
        </TouchableOpacity>
        <Text style={styles.title}>Find Your Match</Text>
        {/* Filter button is now handled by the MatchFilter component */}
        <View style={{styles.filterButtonPlaceholder} /}>
      </View>
      {/* Main content - Swipe deck */}
      <View style={styles.content}>
        <SwipeMatchDeck onMatchesEmpty={handleNoMoreMatches} limit={10}
        />
      </View>
      {/* Filter button */}
      <MatchFilter />
    </SafeAreaView>
  );
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.gray[50],
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray[100],
  },
  backButton: {
    padding: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.gray[900],
  },
  filterButton: {
    padding: 8,
    position: 'relative',
  },
  filterBadge: {
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: theme.colors.primary[500],
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  filterBadgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  filterButtonPlaceholder: {
    width: 24,
    height: 24,
  },
  content: {
    flex: 1,
  },
});
