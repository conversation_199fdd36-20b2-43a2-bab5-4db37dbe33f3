/**;,
  * Root Layout - Complete Setup with Auth Provider and <PERSON>rror Boundaries;
 * This layout provides the AuthProvider, Error Boundaries, and configures all necessary screens;,
  */

import React, { useEffect } from 'react';,
  import {
   Stack, useRouter, useSegments ,
  } from 'expo-router';
import {,
  StatusBar 
} from 'expo-status-bar';,
  import {
   SafeAreaProvider ,
  } from 'react-native-safe-area-context';
import {,
  GestureHandlerRootView 
} from 'react-native-gesture-handler';,
  import {
   QueryClientProvider ,
  } from '@tanstack/react-query';
import {,
  AuthContextAdapterProvider, useAuthAdapter ,
  } from '@context/AuthContextAdapter';
import {,
  AuthProvider 
} from '@context/AuthContext';,
  import {
   ThemeProvider, useTheme ,
  } from '@design-system';
import {,
  ToastProvider 
} from '@context/ToastContext';,
  import {
   FavoritesProvider ,
  } from '../contexts/FavoritesContext';
import {,
  TestingProvider 
} from '../components/testing/TestingProvider';,
  import {
   View, Text ,
  } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';,
  import {
   queryClient ,
  } from '@utils/queryClient';
import {,
  logEnvironmentValidation 
} from '@utils/validateEnvironment';,
  import {
   useSession ,
  } from '@hooks/useSession';
import {,
  applyGlobalColorPatch 
} from '@utils/globalColorPatch';,
  import {
   applyStylePatches ,
  } from '@utils/stylePatches' // Error Boundary for React errors including hooks violations;
class AuthErrorBoundary extends React.Component<;,
  { children: React.ReactNode };
  { hasError: boolean; error?: Error },
  > {
  constructor(props: { children: React.ReactNode }) {,
  super(props)
    this.state = { hasError: false },
  }
  static getDerivedStateFromError(error: Error) {,
  console.error('🔴 [AuthErrorBoundary] Caught error:', error),
  return { hasError: true,  error },
  }
  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {,
  console.error('🔴 [AuthErrorBoundary] Error details:', error, errorInfo),
  }
  render() {,
  if (this.state.hasError) {
      return (,
  <View style={{  [ flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 ] }}>,
  <Text style={{  [ fontSize: 18, fontWeight: 'bold', marginBottom: 10 ] }}>,
  Authentication Error, ,
  </Text>
          <Text style={{  [ textAlign: 'center', color: '#666' ] }}>,
  There was an issue with the authentication system. Please restart the app., ,
  </Text>
          <Text style={{  [ marginTop: 10, fontSize: 12, color: '#999' ] }}>,
  Error: {this.state.error? .message}
          </Text>,
  </View>
      ),
  }
    return this.props.children;,
  }
},
  // Session management component;
function SessionManager() {,
  useSession() // Initialize session management;
  return null;,
  }
// Navigation handler to manage auth state changes with verification gates;,
  function NavigationHandler() {
  const { authState, authLoaded, refreshAuthState  } = useAuthAdapter(),
  const router = useRouter()
  const segments = useSegments();,
  useEffect(() = > { if (!authLoaded) return null;
    const inAuthGroup = segments[0] === '(auth)', ,
  const inTabsGroup = segments[0] === '(tabs)';,
  const inOnboarding = segments[0] === 'unified-onboarding', ,
  const inSplash = segments[0] === 'splash';,
  const inProviderFlow = segments[0] === 'provider', ,
  const inVerificationFlow = segments[0] === 'verification';,
  console.log('🔄 [NavigationHandler] Auth state changed    : ' {,
  isAuthenticated: authState.isAuthenticated,
    authStatus: authState.authStatus),
  currentSegments: segments.join('/')
      inAuthGroup,
  inTabsGroup;
      inOnboarding;,
  inSplash;
      inProviderFlow;,
  inVerificationFlow;
      userProfileCompletion: authState.user? .profile_completion || 0 }),
  // Don't interfere if user is in splash, onboarding, verification, or provider registration flow;,
  if (inSplash || inOnboarding || inProviderFlow || inVerificationFlow) {
      console.log('🔄 [NavigationHandler] User in protected flow, not interfering'),
  return null, ,
  }
    // Handle logout   : if user is not authenticated and not in auth group redirect to login,
  if (!authState.isAuthenticated && authState.authStatus = == 'unauthenticated' && !inAuthGroup) {
      console.log('🔄 [NavigationHandler] User logged out, redirecting to login'),
  router.replace('/(auth)/login')
      return null,
  }
    // Handle authenticated users with verification gates;,
  if (authState.isAuthenticated && authState.authStatus = == 'authenticated') {
      const profileCompletion = authState.user? .profile_completion || 0;,
  const isEmailVerified = authState.user?.email_verified;
      const isIdentityVerified = authState.user?.identity_verified;,
  const isFullyVerified = isEmailVerified && isIdentityVerified // If user is in auth group but authenticated, check where they should go;,
  if (inAuthGroup) {
        // New users (low completion) must complete onboarding;,
  if (profileCompletion < 60) {
          console.log('🔄 [NavigationHandler] New user needs onboarding, redirecting to unified-onboarding'),
  )
          router.replace('/unified-onboarding'),
  return null;
        },
  // Users with high completion (70%+) can access main app even without full verification;
        if (profileCompletion >= 70) {,
  // Check verification status before allowing main app access;
          if (!isFullyVerified) {,
  console.log('🔄 [NavigationHandler] High completion user needs verification before main app access'),
  )
            router.replace('/verification/simple-flow'),
  return null, ,
  }
          console.log('🔄 [NavigationHandler] High completion user with verification, allowing main app access'),
  )
          router.replace('/(tabs)'),
  return null;
        },
  // Medium completion users (60-69%) need verification to proceed;
        if (!isFullyVerified) {,
  console.log('🔄 [NavigationHandler] Medium completion user needs verification'),
  router.replace('/verification')
          return null;,
  }
        // Fully verified users can access main app;,
  console.log('🔄 [NavigationHandler] Verified user logging in, redirecting to main app'),
  router.replace('/(tabs)')
        return null;,
  }
      // If user is trying to access main app, enforce verification gates;,
  if (inTabsGroup) {
        // Check if we need to refresh auth state (profile completion might be stale),
  const checkForStaleData = async () => {
          try {;,
  // Check AsyncStorage for onboarding completion;
            const onboardingCompleted = await AsyncStorage.getItem('onboarding_completed'),
  if (onboardingCompleted === 'true' && profileCompletion < 70) {
              console.log('🔄 [NavigationHandler] Detected stale profile completion, refreshing auth state'),
  )
              if (refreshAuthState) {,
  await refreshAuthState()
              };,
  return null;
            },
  } catch (error) {
            console.warn('Failed to check onboarding status   : ' error),
  }
        },
  // Run stale data check
        checkForStaleData(),
  // Block access if profile completion is too low;
        if (profileCompletion < 60) {,
  console.log('🔄 [NavigationHandler] Profile completion too low for main app, redirecting to onboarding'),
  )
          router.replace('/unified-onboarding'),
  return null;
        },
  // Users with 70%+ completion can access main app regardless of verification status // (verification prompts will be shown in-app)
        if (profileCompletion >= 70) {,
  // Check verification status before allowing continued main app access;
          if (!isFullyVerified) {,
  console.log('🔄 [NavigationHandler] High completion user in main app needs verification'),
  )
            router.replace('/verification/simple-flow'),
  return null, ,
  }
          console.log('🔄 [NavigationHandler] High completion user allowed in main app'),
  return null;
        },
  // Medium completion users (60-69%) need verification to access main app;
        if (!isFullyVerified) {,
  console.log('🔄 [NavigationHandler] Medium completion user needs verification for main app access'),
  )
          router.replace('/verification'),
  return null;
        },
  // User meets all requirements, allow access;,
  console.log('🔄 [NavigationHandler] User meets all requirements, allowing main app access'),
  return null;
      },
  }
  }, [
    authState.isAuthenticated;,
  authState.authStatus;
    authState.user;,
  segments;
    authLoaded;,
  router;
    refreshAuthState;,
   ]),
  return null;
},
  // Themed Stack Navigator Component;
function ThemedStackNavigator() { const theme = useTheme(),
  return (
    <Stack,
  screenOptions={{  {
        headerStyle: {,
    backgroundColor: theme.colors.primary   }};,
  headerTintColor: theme.colors.background,
        headerTitleStyle: { fontWeight: 'bold',
    color: theme.colors.background },
      }},
  >
      {/* Splash Screen */},
  <Stack.Screen;
        name= 'splash';,
  options= {{  headerShown: false,
          gestureEnabled: false  }},
  />
      {/* Auth Screens */},
  <Stack.Screen, ,
  name='(auth)';
        options= {{  headerShown: false,
          gestureEnabled: false  }},
  />
      {/* Main App */},
  <Stack.Screen;
        name= '(tabs)';,
  options= {{  headerShown: false  }}
      />,
  {/* Index (fallback) */}
      <Stack.Screen;,
  name= 'index';
        options= {{  title: 'RoomieMatch AI',
          headerShown: true  }},
  />
    </Stack>,
  )
},
  export default function RootLayout() {
  const router = useRouter(),
  const segments = useSegments();
  // Add navigation debugging;,
  useEffect(() = > {
    if (__DEV__) {,
  console.log('🔄 Navigation changed:', segments.join('/')),
  }
  }, [segments]),
  // Validate environment variables on app startup, ,
  React.useEffect(() => {
    logEnvironmentValidation(),
  // Apply global color patches to prevent [object Object] issues;,
  try {
      applyGlobalColorPatch(),
  applyStylePatches()
      console.log('✅ Global color patches applied successfully'),
  } catch (error) {
      console.warn('⚠️ Failed to apply global color patches:', error),
  }
  }, []),
  return (
    <GestureHandlerRootView style={{  [ flex: 1 ] }}>,
  <SafeAreaProvider>
        <ThemeProvider>,
  <QueryClientProvider client={queryClient}>
            <AuthErrorBoundary>,
  <AuthContextAdapterProvider>
                <AuthProvider>,
  <FavoritesProvider>
                    <TestingProvider>,
  <SessionManager />
                      <NavigationHandler />,
  <ToastProvider>
                        <ThemedStackNavigator />,
  <StatusBar style={'light' /}>
                      </ToastProvider>,
  </TestingProvider>
                  </FavoritesProvider>,
  </AuthProvider>
              </AuthContextAdapterProvider>,
  </AuthErrorBoundary>
          </QueryClientProvider>,
  </ThemeProvider>
      </SafeAreaProvider>,
  </GestureHandlerRootView>
  ),
  }