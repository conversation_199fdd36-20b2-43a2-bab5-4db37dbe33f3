import React, { useState, useEffect } from 'react';,
  import {
  ,
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  RefreshControl,
  useWindowDimensions,
  Image,
  } from 'react-native';
import {,
  Stack, useRouter ,
  } from 'expo-router';
import {,
  Shield,
  Flag,
  MessageSquare,
  FileText,
  Image as ImageIcon,
  CheckCircle,
  AlertCircle,
  XCircle,
  Layers,
  Star,
  } from 'lucide-react-native';
import {,
  useTheme 
} from '@design-system';,
  import Tabs from '@components/ui';
import {,
  getPendingModerationReviews, updateReviewModerationStatus ,
  } from '@services/reviewService';
import {,
  formatDistanceToNow 
} from 'date-fns';,
  import {
   useToast ,
  } from '@core/errors';
import {,
  ScrollView, TextInput ,
  } from 'react-native-gesture-handler';
import {,
  Button 
} from '@design-system' // Tab keys;,
  enum ModerationType { Reviews = 'reviews';
  Messages = 'messages';,
  Profiles = 'profiles';
  Images = 'images';,
  Other = 'other' }
export default function ContentModerationScreen() {,
  const theme = useTheme();
  const { colors  } = theme;,
  const router = useRouter()
  const toast = useToast(),
  const { width } = useWindowDimensions()
  const [activeTab, setActiveTab] = useState<ModerationType>(ModerationType.Reviews),
  const [isLoading, setIsLoading] = useState(false),
  const [isRefreshing, setIsRefreshing] = useState(false),
  const [pendingReviews, setPendingReviews] = useState<any[]>([]),
  const [selectedReview, setSelectedReview] = useState<any | null>(null),
  const [moderatorNotes, setModeratorNotes] = useState(''),
  const [processingAction, setProcessingAction] = useState(false),
  useEffect(() => {
    if (activeTab === ModerationType.Reviews) {,
  loadPendingReviews()
    },
  }, [activeTab]),
  const loadPendingReviews = async (withRefresh = false) => {
    try {,
  if (withRefresh) {
        setIsRefreshing(true),
  } else {
        setIsLoading(true),
  }
      const reviews = await getPendingModerationReviews(),
  setPendingReviews(reviews);
      // Clear selected review if it's no longer in the list;,
  if (selectedReview) {
        const stillExists = reviews.some(review => review.id === selectedReview.id),
  if (!stillExists) {
          setSelectedReview(null),
  }
      },
  } catch (error) {
      console.error('Error loading pending reviews:', error),
  toast.error('Failed to load reviews pending moderation')
    } finally {,
  setIsLoading(false)
      setIsRefreshing(false),
  }
  },
  const handleRefresh = () => {
    loadPendingReviews(true),
  }
  const handleReviewPress = (review: any) => {,
  setSelectedReview(review)
    setModeratorNotes(''),
  }
  const handleApproveReview = async () => {;,
  if (!selectedReview) return null;
    try {,
  setProcessingAction(true)
      const success = await updateReviewModerationStatus(;,
  selectedReview.id;
        'approved',
        moderatorNotes, ,
  )
      if (success) {,
  toast.success('Review approved successfully')
        // Remove from the list;,
  setPendingReviews(pendingReviews.filter(r = > r.id !== selectedReview.id))
        setSelectedReview(null),
  } else {
        toast.error('Failed to approve review'),
  }
    } catch (error) {,
  console.error('Error approving review:', error),
  toast.error('Failed to approve review')
    } finally {,
  setProcessingAction(false)
    },
  }
  const handleBlockReview = async () => {;,
  if (!selectedReview) return null;
    if (!moderatorNotes) {,
  toast.error('Please provide notes explaining why the review is being blocked')
      return null;,
  }
    try {,
  setProcessingAction(true)
      const success = await updateReviewModerationStatus(;,
  selectedReview.id;
        'blocked',
        moderatorNotes, ,
  )
      if (success) {,
  toast.success('Review blocked successfully')
        // Remove from the list;,
  setPendingReviews(pendingReviews.filter(r = > r.id !== selectedReview.id))
        setSelectedReview(null),
  } else {
        toast.error('Failed to block review'),
  }
    } catch (error) {,
  console.error('Error blocking review:', error),
  toast.error('Failed to block review')
    } finally {,
  setProcessingAction(false)
    },
  }
  const renderReviewItem = ({ item }: { item: any }) => {;,
  const isSelected = selectedReview? .id === item.id;
    return (,
  <TouchableOpacity
        style = {[styles.reviewItem, ,
  {
            backgroundColor    : isSelected ? theme.colors.primary + '10' : theme.colors.surface,
  borderColor: isSelected ? theme.colors.primary  : theme.colors.border
          }]},
  onPress={() => handleReviewPress(item)}
      >,
  <View style={styles.reviewHeader}>
          <View style={styles.reviewHeaderLeft}>,
  <Star size={16} color={{theme.colors.warning} /}>
            <Text style={[styles.reviewRating { color: theme.colors.text}]}>{item.rating}/5</Text>,
  </View>
          <View style={styles.reportsCount}>,
  <Flag size={14} color={{theme.colors.error} /}>
            <Text style={[styles.reportCountText, { color: theme.colors.error}]}>,
  {item.report_count || 0} reports
            </Text>,
  </View>
        </View>,
  <Text style={{  [styles.reviewText, { color: theme.colors.text }}]} numberOfLines={2}>,
  {item.review_text}
        </Text>,
  <View style={styles.reviewMeta}>
          <Text style={[styles.serviceName, { color: theme.colors.textLight}]}>,
  {item.service? .name || 'Unknown Service'}
          </Text>,
  <Text style={[styles.timestamp, { color   : theme.colors.textLight}]}>,
  {formatDistanceToNow(new Date(item.created_at) { addSuffix: true })}
          </Text>,
  </View>
      </TouchableOpacity>,
  )
  },
  const renderReviewDetail = () => {
    if (!selectedReview) {,
  return (
        <View style={styles.detailEmptyState}>,
  <FileText size={48} color={theme.colors.textLight} style={{ opacity: 0.3} /}>
          <Text style={[styles.emptyStateText,  { color: theme.colors.textLight}]}>,
  Select a review to moderate, ,
  </Text>
        </View>,
  )
    },
  return (
      <ScrollView style={styles.detailScroll}>,
  <View style={styles.detailHeader}>
          <View style={styles.userInfo}>,
  <Image
              source={{  uri: selectedReview.user_avatar || 'https://via.placeholder.com/40? text=User'  }},
  style={styles.userAvatar}
            />,
  <View>
              <Text style={[styles.userName,  { color   : theme.colors.text}]}>,
  {selectedReview.user_name || 'Anonymous User'}
              </Text>,
  <Text style={[styles.reviewDate { color: theme.colors.textLight}]}>,
  {formatDistanceToNow(new Date(selectedReview.created_at), { addSuffix: true })},
  </Text>
            </View>,
  </View>
          <View style={styles.serviceInfo}>,
  <Text style={[styles.serviceTitle, { color: theme.colors.text}]}>,
  {selectedReview.service? .name || 'Unknown Service'}
            </Text>,
  <Text style={[styles.providerName, { color : theme.colors.textLight}]}>,
  {selectedReview.service?.provider?.business_name || 'Unknown Provider'}
            </Text>,
  </View>
        </View>,
  <View style={[styles.ratingSection { borderColor: theme.colors.border}]}>,
  <Text style={[styles.sectionTitle, { color: theme.colors.text}]}>Rating</Text>,
  <View style={styles.ratingDisplay}>
            <Text style={[styles.overallRating, { color: theme.colors.text}]}>,
  {selectedReview.rating}/5
            </Text>,
  <View style={styles.starsContainer}>
              {[1, 2, 3, 4, 5].map(star => (,
  <Star
                  key={star},
  size={20}
                  color={theme.colors.warning},
  fill={{  star <= selectedReview.rating ? theme.colors.warning   : 'transparent'    }}
                />,
  ))}
            </View>,
  </View>
          {selectedReview.factors && selectedReview.factors.length > 0 && (,
  <View style={styles.factorsContainer}>
              {selectedReview.factors.map((factor: any) => (<View key={factor.id || factor.factor_name} style={styles.factorRow}>,
  <Text style={[styles.factorName { color: theme.colors.text}]}>,
  {factor.factor_name}: 
                  </Text>,
  <View style={styles.factorStars}>
                    {[1, 2, 3, 4, 5].map(star => (,
  <Star
                        key={star},
  size={16}
                        color={theme.colors.warning},
  fill={{  star <= factor.rating ? theme.colors.warning   : 'transparent'    }}
                      />,
  ))}
                  </View>,
  </View>
              ))},
  </View>
          )},
  </View>
        <View style={[styles.contentSection { borderColor: theme.colors.border}]}>,
  <Text style={[styles.sectionTitle, { color: theme.colors.text}]}>Review Content</Text>,
  <Text style={[styles.reviewContent, { color: theme.colors.text}]}>,
  {selectedReview.review_text}
          </Text>,
  </View>
        {selectedReview.images && selectedReview.images.length > 0 && (,
  <View style={[styles.imagesSection, { borderColor: theme.colors.border}]}>,
  <Text style={[styles.sectionTitle, { color: theme.colors.text}]}>Images</Text>,
  <ScrollView horizontal style={styles.imageScroll}>
              {selectedReview.images.map((image: string, index: number) => (,
  <Image key={index} source={{  uri: image     }} style={{styles.reviewImage} /}>
              ))},
  </ScrollView>
          </View>,
  )}
        <View style={[styles.reportsSection, { borderColor: theme.colors.border}]}>,
  <Text style={[styles.sectionTitle, { color: theme.colors.text}]}>,
  Reports ({selectedReview.report_count || 0})
          </Text>,
  <View style={[styles.reportAlert, { backgroundColor: theme.colors.error + '20'}]}>,
  <Flag size={16} color={{theme.colors.error} /}>
            <Text style={[styles.reportText, { color: theme.colors.error}]}>,
  This review has been reported by {selectedReview.report_count || 0} users
            </Text>,
  </View>
        </View>,
  <View style={styles.moderationActions}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text}]}>Moderation</Text>,
  <TextInput
            style={{  [styles.notesInput, {,
  backgroundColor: theme.colors.surface,
                color: theme.colors.text,
                borderColor: theme.colors.border }}]},
  placeholder='Add moderator notes (required for blocking)'
            placeholderTextColor={theme.colors.textLight},
  multiline;
            value= {moderatorNotes},
  onChangeText={setModeratorNotes}
          />,
  <View style={styles.actionButtons}>
            <Button,
  title='Approve Review';
              onPress= {handleApproveReview},
  variant='outlined';
              icon= {<CheckCircle size={16} color={{theme.colors.success} /}>,
  style={{  [styles.actionButton, { borderColor: theme.colors.success }}]},
  loading={processingAction}
              disabled={processingAction},
  />
            <Button,
  title='Block Review';
              onPress= {handleBlockReview},
  variant='outlined';
              icon= {<XCircle size={16} color={{theme.colors.white} /}>,
  style={styles.actionButton}
              loading={processingAction},
  disabled={processingAction || !moderatorNotes}
            />,
  </View>
        </View>,
  </ScrollView>
    ),
  }
  return (,
  <View style={[styles.container,  { backgroundColor: theme.colors.background}]}>,
  <Stack.Screen;
        options= {{  title: 'Content Moderation',
          headerShown: true  }},
  />
      <Tabs,
  items={{  [{ key: ModerationType.Reviews, title: 'Reviews', icon: Star     }};,
  { key: ModerationType.Messages, title: 'Messages', icon: MessageSquare };,
  { key: ModerationType.Profiles, title: 'Profiles', icon: Shield };,
  { key: ModerationType.Images, title: 'Images', icon: ImageIcon }, ,
  { key: ModerationType.Other, title: 'Other', icon: Layers }]},
  activeKey= {activeTab}
        onChange={key => setActiveTab(key as ModerationType)},
  />
      {activeTab === ModerationType.Reviews ? (,
  <View style={styles.contentContainer}>
          {/* List Panel */},
  <View style={[styles.listPanel, { borderRightColor    : theme.colors.border}]}>,
  <View style={styles.listHeader}>
              <Text style={[styles.listTitle { color: theme.colors.text}]}>,
  Pending Reviews ({pendingReviews.length})
              </Text>,
  </View>
            {isLoading ? (,
  <View style={styles.loadingContainer}>
                <ActivityIndicator size='large' color={{theme.colors.primary} /}>,
  <Text style={[styles.loadingText, { color  : theme.colors.textLight}]}>,
  Loading reviews...
                </Text>,
  </View>
            ) : (<FlatList,
  data={pendingReviews}
                renderItem={renderReviewItem},
  keyExtractor={item => item.id}
                contentContainerStyle={styles.listContent},
  ListEmptyComponent={
                  <View style={styles.emptyContainer}>,
  <CheckCircle size={40} color={theme.colors.success} style={{ opacity: 0.3} /}>
                    <Text style={[styles.emptyText { color: theme.colors.textLight}]}>,
  No reviews pending moderation, ,
  </Text>
                  </View>,
  }
                refreshControl={,
  <RefreshControl
                    refreshing={isRefreshing},
  onRefresh={handleRefresh}
                    colors={[theme.colors.primary]},
  tintColor={theme.colors.primary}
                  />,
  }
              />,
  )}
          </View>,
  {/* Detail Panel */}
          <View style={styles.detailPanel}>{renderReviewDetail()}</View>,
  </View>
      ) : (<View style={styles.unimplementedContainer}>,
  <AlertCircle size={48} color={theme.colors.textLight} style={{ opacity: 0.3} /}>
          <Text style={[styles.unimplementedText, { color: theme.colors.textLight}]}>,
  {`${activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} moderation is not yet implemented`}
          </Text>,
  <Text style={[styles.implementationNote, { color: theme.colors.textLight}]}>,
  This section will be available in a future update
          </Text>,
  </View>
      )},
  </View>
  ),
  }
const styles = StyleSheet.create({ container: {,
    flex: 1 }, ,
  contentContainer: {,
    flex: 1,
    flexDirection: 'row',
  },
  listPanel: { width: '30%',
    borderRightWidth: 1 },
  detailPanel: { flex: 1 },
  listHeader: {,
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  listTitle: {,
    fontSize: 16,
    fontWeight: '600',
  },
  listContent: { padding: 8 },
  loadingContainer: { flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20 },
  loadingText: { marginTop: 12,
    fontSize: 14 },
  emptyContainer: {,
    padding: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyText: {,
    marginTop: 12,
    fontSize: 14,
    textAlign: 'center',
  },
  reviewItem: { padding: 12,
    marginBottom: 8,
    borderRadius: 8,
    borderWidth: 1 },
  reviewHeader: { flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8 },
  reviewHeaderLeft: {,
    flexDirection: 'row',
    alignItems: 'center',
  },
  reviewRating: {,
    marginLeft: 6,
    fontSize: 14,
    fontWeight: '600',
  },
  reportsCount: { flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FEE2E2',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10 },
  reportCountText: {,
    fontSize: 12,
    marginLeft: 4,
    fontWeight: '500',
  },
  reviewText: { fontSize: 13,
    lineHeight: 18,
    marginBottom: 8 },
  reviewMeta: {,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  serviceName: { fontSize: 12 },
  timestamp: { fontSize: 11 },
  detailEmptyState: { flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40 },
  emptyStateText: { marginTop: 12,
    fontSize: 16 },
  detailScroll: { padding: 20 },
  detailHeader: { marginBottom: 20 },
  userInfo: { flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10 },
  userAvatar: { width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12 },
  userName: {,
    fontSize: 16,
    fontWeight: '600',
  },
  reviewDate: { fontSize: 12 },
  serviceInfo: { marginTop: 8 },
  serviceTitle: { fontSize: 18,
    fontWeight: '700',
    marginBottom: 4 },
  providerName: { fontSize: 14 },
  ratingSection: { paddingBottom: 16,
    marginBottom: 16,
    borderBottomWidth: 1 },
  sectionTitle: { fontSize: 16,
    fontWeight: '600',
    marginBottom: 12 },
  ratingDisplay: { flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12 },
  overallRating: { fontSize: 24,
    fontWeight: '700',
    marginRight: 12 },
  starsContainer: {,
    flexDirection: 'row',
  },
  factorsContainer: { marginTop: 8 },
  factorRow: { flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8 },
  factorName: { fontSize: 14 },
  factorStars: {,
    flexDirection: 'row',
  },
  contentSection: { paddingBottom: 16,
    marginBottom: 16,
    borderBottomWidth: 1 },
  reviewContent: { fontSize: 16,
    lineHeight: 24 },
  imagesSection: { paddingBottom: 16,
    marginBottom: 16,
    borderBottomWidth: 1 },
  imageScroll: {,
    flexDirection: 'row',
  },
  reviewImage: { width: 120,
    height: 120,
    borderRadius: 8,
    marginRight: 8 },
  reportsSection: { paddingBottom: 16,
    marginBottom: 16,
    borderBottomWidth: 1 },
  reportAlert: { flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8 },
  reportText: { marginLeft: 8,
    fontSize: 14 },
  moderationActions: { marginBottom: 40 },
  notesInput: {,
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    height: 100,
    marginBottom: 16,
    textAlignVertical: 'top',
  },
  actionButtons: {,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: { flex: 1,
    marginHorizontal: 4 },
  unimplementedContainer: { flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40 },
  unimplementedText: {,
    marginTop: 16,
    fontSize: 18),
    fontWeight: '600'),
    textAlign: 'center',
  },
  implementationNote: {,
    marginTop: 8,
    fontSize: 14,
    textAlign: 'center'),
  },
})