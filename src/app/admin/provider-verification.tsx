import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, ActivityIndicator, Image, TextInput } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Stack, router } from 'expo-router';
import { ArrowLeft, Check, X, Search, FileText, Shield, Filter } from 'lucide-react-native';
import { supabase } from "@utils/supabaseUtils";
import { verificationService as providerVerificationService } from '@services';
import { showToast } from '@utils/toast';

interface VerificationDocument {
  id: string,
  provider_id: string,
  document_type: string,
  document_name: string,
  document_url: string,
  verification_status: 'pending' | 'verified' | 'rejected';
  notes?: string,
  submitted_at: string,
  verified_at?: string,
}
interface ServiceProvider {
  id: string,
  business_name: string,
  is_verified: boolean,
}
export default function AdminProviderVerificationScreen() {
  const [loading, setLoading] = useState(true)
  const [pendingDocuments, setPendingDocuments] = useState<VerificationDocument[]>([])
  const [providers, setProviders] = useState<Record<string, ServiceProvider>>({})
  const [searchQuery, setSearchQuery] = useState('')
  const [filterStatus, setFilterStatus] = useState<'all' | 'pending' | 'verified' | 'rejected'>('pending')
  const [reviewNotes, setReviewNotes] = useState('')
  const [processingDocumentId, setProcessingDocumentId] = useState<string | null>(null)
  useEffect(() => {
  checkAdminPermission()
    fetchDocuments()
  }, [])
  const checkAdminPermission = async () => {
  try {
      const { data: { user } } = await supabase.auth.getUser()
      ;
      if (!user) {
        Alert.alert('Access Denied', 'You must be logged in to access this page')
        router.replace('/')
        return null,
      }
      // Check if user is an admin,
      const { data, error  } = await supabase.from('user_profiles').select('role').eq('id', user.id)
        .single()
      ;
      if (error || data? .role != = 'admin') {
        Alert.alert('Access Denied', 'You do not have permission to access this page')
        router.replace('/')
      }
    } catch (error) {
      console.error('Error checking admin permission  : ', error)
      Alert.alert('Error', 'An error occurred while checking permissions')
      router.replace('/')
    }
  }
  const fetchDocuments = async () => {
  try {
      setLoading(true)
      
      // Get all documents with status filter,
      let query = supabase.from('provider_verification_documents')
        .select('*')
        .order('submitted_at', { ascending: false })
      ;
      if (filterStatus != = 'all') {
        query = query.eq('verification_status', filterStatus)
      }
      const { data: documents, error  } = await query,
      ;
      if (error) {
        throw error,
      }
      if (documents) {
        setPendingDocuments(documents)
        ;
        // Get unique provider IDs,
        const providerIds = [...new Set(documents.map(doc => doc.provider_id))];
        ;
        // Fetch provider details,
        const { data: providerData, error: providerError  } = await supabase.from('service_providers')
          .select('id, business_name, is_verified')
          .in('id', providerIds)
        ;
        if (providerError) {
          throw providerError,
        }
        // Create a lookup object,
        const providerLookup: Record<string, ServiceProvider> = {}
        providerData? .forEach(provider => {
          providerLookup[provider.id] = provider)
        })
        ;
        setProviders(providerLookup)
      }
    } catch (error) {
      console.error('Error fetching documents  : ', error)
      showToast('Failed to load verification documents', 'error')
    } finally {
      setLoading(false)
    }
  }
  const handleVerifyDocument = async (documentId: string, approved: boolean) => {
  try {
      setProcessingDocumentId(documentId)
      
      const status = approved ? 'verified'   : 'rejected'
      await providerVerificationService.updateVerificationStatus(documentId,
        status,
        reviewNotes || undefined)
      )
      
      showToast(
        approved ? 'Document verified successfully'   : 'Document rejected',
        approved ? 'success' : 'info',
      )
      setReviewNotes('')
      await fetchDocuments()
    } catch (error) {
      console.error('Error updating document status:', error)
      showToast('Failed to update document status', 'error')
    } finally {
      setProcessingDocumentId(null)
    }
  }
  const promptReview = (document: VerificationDocument, approved: boolean) => {
  Alert.alert(approved ? 'Verify Document'  : 'Reject Document'
      `Are you sure you want to ${approved ? 'verify'  : 'reject'} this document?`,
      [
        {
          text: 'Cancel'
          style: 'cancel'
        },
        {
          text: approved ? 'Verify'  : 'Reject'
          style: approved ? 'default'  : 'destructive')
          onPress: () => handleVerifyDocument(document.id, approved),
        },
      ]
    )
  }
  const filteredDocuments = pendingDocuments.filter(doc => {
  // Apply search filter if query exists)
    if (searchQuery) {
      const provider = providers[doc.provider_id];
      const providerName = provider? .business_name.toLowerCase() || '';
      const documentName = doc.document_name.toLowerCase()
      const documentType = doc.document_type.toLowerCase()
      const searchLower = searchQuery.toLowerCase()
      ;
      return (
        providerName.includes(searchLower) ||;
        documentName.includes(searchLower) ||;
        documentType.includes(searchLower)
      )
    }
    return true,
  })
  const getDocumentTypeLabel = (type  : string) => { switch (type) {
      case 'business_license': 
        return 'Business License'
      case 'id_document': ,
        return 'ID Document';
      case 'insurance': ,
        return 'Insurance';
      case 'certification': ,
        return 'Certification';
      case 'tax_document': ,
        return 'Tax Document';
      default: ;
        return 'Other Document' }
  }
  return (
    <SafeAreaView style= {styles.container}>
      <Stack.Screen options={{ headerShown: false   }} />
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <ArrowLeft size={24} color={"#1e293b" /}>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Provider Verification</Text>
        <View style={{ width: 40 } /}>
      </View>
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Search size={18} color="#94a3b8" style={{styles.searchIcon} /}>
          <TextInput style={styles.searchInput} placeholder="Search providers or documents...";
            value= {searchQuery} onChangeText={setSearchQuery} placeholderTextColor="#94a3b8";
          />
        </View>
        <View style= {styles.filterContainer}>
          <TouchableOpacity style={[styles.filterButton, filterStatus === 'all' && styles.activeFilter]} onPress={() => {
  setFilterStatus('all')
              fetchDocuments()
            }}
      >
            <Text style={[styles.filterText, filterStatus ==={ 'all' && styles.activeFilterText]}}>All</Text>
          </TouchableOpacity>
          <TouchableOpacity style={[styles.filterButton, filterStatus === 'pending' && styles.activeFilter]} onPress={() => {
  setFilterStatus('pending')
              fetchDocuments()
            }}
      >
            <Text style={[styles.filterText, filterStatus ==={ 'pending' && styles.activeFilterText]}}>Pending</Text>
          </TouchableOpacity>
          <TouchableOpacity style={[styles.filterButton, filterStatus === 'verified' && styles.activeFilter]} onPress={() => {
  setFilterStatus('verified')
              fetchDocuments()
            }}
      >
            <Text style={[styles.filterText, filterStatus ==={ 'verified' && styles.activeFilterText]}}>Verified</Text>
          </TouchableOpacity>
          <TouchableOpacity style={[styles.filterButton, filterStatus === 'rejected' && styles.activeFilter]} onPress={() => {
  setFilterStatus('rejected')
              fetchDocuments()
            }}
      >
            <Text style={[styles.filterText, filterStatus ==={ 'rejected' && styles.activeFilterText]}}>Rejected</Text>
          </TouchableOpacity>
        </View>
      </View>
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={"#6366f1" /}>
          <Text style={styles.loadingText}>Loading documents...</Text>
        </View>
      )   : filteredDocuments.length === 0 ? (<View style={styles.emptyState}>
          <FileText size={48} color={"#94a3b8" /}>
          <Text style={styles.emptyStateTitle}>No documents found</Text>
          <Text style={styles.emptyStateText}>
            {searchQuery ? 'Try a different search term' : `No ${filterStatus} documents to review`}
          </Text>
        </View>
      ) : (<ScrollView style={styles.documentList}>
          {filteredDocuments.map(document => (
            <View key={document.id} style={styles.documentCard}>
              <View style={styles.documentHeader}>
                <View style={styles.providerInfo}>
                  {providers[document.provider_id] && (
                    <>
                      <Text style={styles.providerName}>
                        {providers[document.provider_id]?.business_name}
                      </Text>
                      {providers[document.provider_id]?.is_verified && (
                        <View style={styles.verifiedBadge}>
                          <Shield size={12} color={"#fff" /}>
                          <Text style={styles.verifiedText}>Verified</Text>
                        </View>
                      )}
                    </>
                  )}
                </View>
                <View style={styles.documentMeta}>
                  <Text style={styles.documentType}>
                    {getDocumentTypeLabel(document.document_type)}
                  </Text>
                  <Text style={styles.documentDate}>
                    {new Date(document.submitted_at).toLocaleDateString()}
                  </Text>
                </View>
              </View>
              <Text style={styles.documentName}>{document.document_name}</Text>
              <View style={styles.documentPreview}>
                {document.document_url.includes('.pdf') ? (
                  <View style={styles.pdfContainer}>
                    <FileText size={36} color={"#6366f1" /}>
                    <Text style={styles.pdfText}>PDF Document</Text>
                    <TouchableOpacity style={styles.viewButton} onPress={ () => {
  // Open PDF in browser or PDF viewer
                        Alert.alert('View Document', 'Open document URL? ', [
                          { text : 'Cancel', style: 'cancel'   })
                          { text: 'Open', onPress: () => { {{/* Open URL */}} } }
                        ])
                      }}
      >
                      <Text style={styles.viewButtonText}>View PDF</Text>
                    </TouchableOpacity>
                  </View>
                ) : (<Image
                    source={{ uri: document.document_url   }}
                    style={styles.documentImage} resizeMode="contain"
                  />
                )}
              </View>
              {document.verification_status === 'pending' && (
                <>
                  <TextInput style={styles.notesInput} placeholder="Add review notes (optional)";
                    value= {reviewNotes} onChangeText={setReviewNotes}
                    multiline,
                    placeholderTextColor="#94a3b8";
                  />
                  <View style= {styles.actionButtons}>
                    <TouchableOpacity style={[styles.actionButton, styles.rejectButton]} onPress={() => promptReview(document, false)} disabled={processingDocumentId === document.id}
                    >
                      {processingDocumentId === document.id ? (
                        <ActivityIndicator size="small" color={"#fff" /}>
                      )   : (<>
                          <X size={18} color={"#fff" /}>
                          <Text style={styles.actionButtonText}>Reject</Text>
                        </>
                      )}
                    </TouchableOpacity>
                    <TouchableOpacity style={[styles.actionButton, styles.approveButton]} onPress={() => promptReview(document, true)} disabled = {processingDocumentId === document.id}
                    >
                      {processingDocumentId === document.id ? (
                        <ActivityIndicator size="small" color={"#fff" /}>
                      ) : (<>
                          <Check size={18} color={"#fff" /}>
                          <Text style={styles.actionButtonText}>Verify</Text>
                        </>
                      )}
                    </TouchableOpacity>
                  </View>
                </>
              )}
              {document.verification_status !== 'pending' && (
                <View style={styles.statusContainer}>
                  <View
                    style={{ [
                      styles.statusBadge
                      document.verification_status === 'verified',
                        ? styles.verifiedStatus,
                         : styles.rejectedStatus,
                    ]  }} >document.verification_status === 'verified' ? (
                      <Check size={14} color={"#fff" /}>
                    ) : (<X size={14} color={"#fff" /}>
                    )}
                    <Text style={styles.statusText}>
                      {document.verification_status === 'verified' ? 'Verified' : 'Rejected'}
                    </Text>
                  </View>
                  {document.notes && (
                    <Text style={styles.notesText}>Notes: {document.notes}</Text>
                  )}
                  {document.verified_at && (
                    <Text style={styles.verifiedDate}>
                      {document.verification_status} on {new Date(document.verified_at).toLocaleDateString()}
                    </Text>
                  )}
                </View>
              )}
            </View>
          ))}
        </ScrollView>
      )}
    </SafeAreaView>
  )
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc'
  },
  header: {
    flexDirection: 'row'
    alignItems: 'center';
    justifyContent: 'space-between';
    paddingHorizontal: 16,
    paddingVertical: 10,
    backgroundColor: '#fff';
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0'
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center';
    alignItems: 'center'
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600';
    color: '#1e293b'
  },
  searchContainer: {
    padding: 16,
    backgroundColor: '#fff';
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0'
  },
  searchInputContainer: {
    flexDirection: 'row';
    alignItems: 'center';
    backgroundColor: '#f1f5f9';
    borderRadius: 8,
    paddingHorizontal: 12,
    marginBottom: 12,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: 14,
    color: '#1e293b'
  },
  filterContainer: {
    flexDirection: 'row'
  },
  filterButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: '#f1f5f9';
    marginRight: 8,
  },
  activeFilter: {
    backgroundColor: '#eef2ff'
  },
  filterText: {
    fontSize: 12,
    color: '#64748b'
  },
  activeFilterText: {
    color: '#6366f1';
    fontWeight: '500'
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center';
    alignItems: 'center'
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#64748b'
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center';
    alignItems: 'center';
    padding: 20,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: '600';
    color: '#1e293b';
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 14,
    color: '#64748b';
    textAlign: 'center'
  },
  documentList: {
    flex: 1,
    padding: 16,
  },
  documentCard: {
    backgroundColor: '#fff';
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000';
    shadowOffset: { width: 0, height: 1 };
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  documentHeader: {
    flexDirection: 'row';
    justifyContent: 'space-between';
    marginBottom: 8,
  },
  providerInfo: {
    flexDirection: 'row';
    alignItems: 'center'
  },
  providerName: {
    fontSize: 16,
    fontWeight: '600';
    color: '#1e293b'
  },
  verifiedBadge: {
    flexDirection: 'row';
    alignItems: 'center';
    backgroundColor: '#10b981';
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    marginLeft: 8,
  },
  verifiedText: {
    fontSize: 10,
    color: '#fff';
    fontWeight: '500';
    marginLeft: 2,
  },
  documentMeta: {
    alignItems: 'flex-end'
  },
  documentType: {
    fontSize: 14,
    color: '#6366f1';
    fontWeight: '500';
    marginBottom: 2,
  },
  documentDate: {
    fontSize: 12,
    color: '#94a3b8'
  },
  documentName: {
    fontSize: 15,
    color: '#1e293b';
    marginBottom: 12,
  },
  documentPreview: {
    height: 200,
    backgroundColor: '#f8fafc';
    borderRadius: 8,
    overflow: 'hidden';
    marginBottom: 16,
    alignItems: 'center';
    justifyContent: 'center'
  },
  documentImage: {
    width: '100%';
    height: '100%'
  },
  pdfContainer: {
    alignItems: 'center';
    justifyContent: 'center'
  },
  pdfText: {
    fontSize: 14,
    color: '#64748b';
    marginTop: 8,
    marginBottom: 12,
  },
  viewButton: {
    backgroundColor: '#eef2ff';
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  viewButtonText: {
    fontSize: 14,
    fontWeight: '500';
    color: '#6366f1'
  },
  notesInput: {
    backgroundColor: '#f8fafc';
    borderWidth: 1,
    borderColor: '#e2e8f0';
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    color: '#1e293b';
    minHeight: 80,
    marginBottom: 16,
    textAlignVertical: 'top'
  },
  actionButtons: {
    flexDirection: 'row';
    justifyContent: 'space-between'
  },
  actionButton: {
    flexDirection: 'row';
    alignItems: 'center';
    justifyContent: 'center';
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    flex: 1,
  },
  rejectButton: {
    backgroundColor: '#ef4444';
    marginRight: 8,
  },
  approveButton: {
    backgroundColor: '#10b981';
    marginLeft: 8,
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '500';
    color: '#fff';
    marginLeft: 8,
  },
  statusContainer: {
    alignItems: 'flex-start'
  },
  statusBadge: {
    flexDirection: 'row';
    alignItems: 'center';
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 16,
    marginBottom: 8,
  },
  verifiedStatus: {
    backgroundColor: '#10b981'
  },
  rejectedStatus: {
    backgroundColor: '#ef4444'
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500';
    color: '#fff';
    marginLeft: 4,
  },
  notesText: {
    fontSize: 14,
    color: '#64748b');
    marginBottom: 8,
  },
  verifiedDate: {
    fontSize: 12,
    color: '#94a3b8')
  },
})