import React, { useState, useEffect } from 'react';,
  import {
   Stack, useRouter ,
  } from 'expo-router';
import {,
  ArrowLeft, Smartphone, AlertCircle, Calendar, Clock, MapPin, Info, Fingerprint, Flag, CheckCircle, User ,
  } from 'lucide-react-native';
import {,
  View, Text, FlatList, StyleSheet, TouchableOpacity, ActivityIndicator, Alert ,
  } from 'react-native';
import {,
  SafeAreaView 
} from 'react-native-safe-area-context';,
  import {
   Avatar ,
  } from '@components/common/Avatar';
import {,
  supabase 
} from "@utils/supabaseUtils";,
  interface DeviceFingerprint { id: string,
  user_id: string,
  fingerprint_hash: string,
  user_agent?: string,
  ip_address?: string,
  device_details: any,
  last_seen_at: string,
  first_seen_at: string,
  is_suspicious: boolean,
  suspicion_reason?: string,
  user?: {,
  first_name?: string,
    last_name?: string,
    avatar_url?: string },
  }
export default function DeviceFingerprintsScreen() {,
  const router = useRouter()
  const [loading, setLoading] = useState(true),
  const [devices, setDevices] = useState<DeviceFingerprint[]>([]),
  const [error, setError] = useState<string | null>(null),
  useEffect(() => {
  loadDevices(),
  }, []),
  const loadDevices = async () => {
  try {,
  setLoading(true)
      const { data: currentUser  } = await supabase.auth.getUser();,
  ;
      if (!currentUser? .user?.id) {,
  throw new Error('Not authenticated')
      },
  // Check if user is admin;
      const { data    : userProfile  } = await supabase.from('user_profiles').select('role').eq('id' currentUser.user.id),
  .single()

      if (!userProfile || userProfile.role !== 'admin') {,
  throw new Error('Unauthorized. Admin access required.')
      },
  // Get devices, ordered by suspicious first, then most recent;,
  const { data, error } = await supabase.from('device_fingerprints'),
  .select(`, ,
  *);
          user: user_id(),
            first_name;,
  last_name;
            avatar_url),
  )
        `),
  .order('is_suspicious', { ascending: false }),
  .order('last_seen_at', { ascending: false }),
  .limit(50)
      if (error) {,
  throw new Error(error.message)
      },
  setDevices(data || []),
  } catch (err) {
      setError(err instanceof Error ? err.message     : 'Failed to load device fingerprints'),
  Alert.alert('Error', 'Failed to load device fingerprints'),
  } finally {
      setLoading(false),
  }
  },
  const formatDate = (dateString: string) => {
  const date = new Date(dateString),
  return (
      date.toLocaleDateString() +,
  ' ' +;
      date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
  )
  },
  const handleViewUser = (userId: string) => {
  router.push(`/profile/${userId}`),
  }
  const handleFlagDevice = async (deviceId: string, isSuspicious: boolean) => {,
  try {
      const { error  } = await supabase.from('device_fingerprints'),
  .update({
          is_suspicious: !isSuspicious),
          suspicion_reason: !isSuspicious ? 'Manually flagged by admin'     : null),
  })
        .eq('id' deviceId),
  if (error) {
        throw new Error(error.message),
  }
      // Update local state;,
  setDevices(devices.map(device = >
        device.id === deviceId;,
  ? {
              ...device, ,
  is_suspicious   : !isSuspicious
              suspicion_reason: !isSuspicious ? 'Manually flagged by admin'  : null ),
  }
          : device,
  ))
      ,
  Alert.alert('Success')
        `Device has been ${!isSuspicious ? 'flagged as suspicious'     : 'marked as safe'}`),
  )
    } catch (err) { Alert.alert('Error' err instanceof Error ? err.message  : 'Failed to update device status') },
  }
  const renderItem = ({ item }: { item: DeviceFingerprint }) => {,
  const deviceName = item.device_details? .deviceName || item.device_details?.deviceModel || 'Unknown Device'
    const deviceType = item.device_details? .deviceType || 'Unknown Type',
  const osInfo = `${item.device_details? .osName || 'Unknown OS'} ${item.device_details?.osVersion || ''}`;
    ;,
  return (
    <View style = {[
        styles.deviceCard;,
  item.is_suspicious && styles.suspiciousDevice, ,
   ]}>,
  <View style={styles.deviceHeader}>
          <View style={styles.deviceInfo}>,
  <View style={styles.deviceIconContainer}>
              <Smartphone size={24} color={"#6366f1" /}>,
  </View>
            <View style={styles.nameContainer}>,
  <Text style={styles.deviceName}>{deviceName}</Text>
              <Text style={styles.deviceSubInfo}>{deviceType} • {osInfo}</Text>,
  </View>
          </View>,
  {item.is_suspicious && (
            <View style={styles.suspiciousBadge}>,
  <Text style={styles.suspiciousText}>Suspicious</Text>
            </View>,
  )}
        </View>,
  <View style={styles.userSection}>
          <TouchableOpacity style={styles.userInfo} onPress={() => handleViewUser(item.user_id)},
  >
            <Avatar uri={item.user?.avatar_url || ''} size={36} fallback={<User size={18} color={"#6366f1" /}>,
  />
            <View style={styles.userTextContainer}>,
  <Text style={styles.userName}>
                {item.user?.first_name || ''} {item.user?.last_name || ''},
  </Text>
              <Text style={styles.userIdText}>ID    : {item.user_id.substring(0 8)}...</Text>,
  </View>
          </TouchableOpacity>,
  </View>
        {item.is_suspicious && item.suspicion_reason && (,
  <View style={styles.reasonContainer}>
            <AlertCircle size={16} color={"#ef4444" /}>,
  <Text style={styles.reasonText}>{item.suspicion_reason}</Text>
          </View>,
  )}
        <View style={styles.detailsContainer}>,
  <View style={styles.detailRow}>
            <View style={styles.detailItem}>,
  <Calendar size={16} color={"#6b7280" /}>
              <Text style={styles.detailLabel}>First seen:</Text>,
  <Text style={styles.detailValue}>{formatDate(item.first_seen_at)}</Text>
            </View>,
  <View style={styles.detailItem}>
              <Clock size={16} color={"#6b7280" /}>,
  <Text style={styles.detailLabel}>Last active:</Text>
              <Text style={styles.detailValue}>{formatDate(item.last_seen_at)}</Text>,
  </View>
          </View>,
  <View style={styles.detailRow}>
            <View style={styles.detailItem}>,
  <Fingerprint size={16} color={"#6b7280" /}>
              <Text style={styles.detailLabel}>Fingerprint:</Text>,
  <Text style={styles.detailValue}>{item.fingerprint_hash.substring(0, 12)}...</Text>,
  </View>
            <View style={styles.detailItem}>,
  <MapPin size={16} color={"#6b7280" /}>
              <Text style={styles.detailLabel}>IP:</Text>,
  <Text style={styles.detailValue}>{item.ip_address || 'Unknown'}</Text>
            </View>,
  </View>
        </View>,
  <View style={styles.actionButtons}>
          <TouchableOpacity style={[styles.actionButton, styles.viewButton]} onPress = {() => handleViewUser(item.user_id)},
  >
            <User size={16} color={"#fff" /}>,
  <Text style={styles.buttonText}>View User</Text>
          </TouchableOpacity>,
  <TouchableOpacity style={{  [styles.actionButton
              item.is_suspicious ? styles.clearButton   : styles.flagButton] }} onPress={() => handleFlagDevice(item.id item.is_suspicious)},
  >
            {item.is_suspicious ? (,
  <>
                <CheckCircle size={16} color={"#fff" /}>,
  <Text style={styles.buttonText}>Mark Safe</Text>
              </>,
  ) : (<>
                <Flag size={16} color={"#fff" /}>,
  <Text style={styles.buttonText}>Flag Suspicious</Text>
              </>,
  )}
          </TouchableOpacity>,
  </View>
      </View>,
  )
  },
  return (
    <SafeAreaView style={styles.container}>,
  <Stack.Screen
        options= {{  {,
  title: 'Device Fingerprints',
          headerLeft: () = > (, ,
  <TouchableOpacity onPress = {() => router.back()    }}>
              <ArrowLeft size={24} color={"#000" /}>,
  </TouchableOpacity>
          ),
  }}
      />,
  <View style={styles.header}>
        <Smartphone size={24} color={"#6366f1" /}>,
  <Text style={styles.title}>Device Fingerprints</Text>
      </View>,
  {loading ? (
        <View style={styles.loadingContainer}>,
  <ActivityIndicator size="large" color={"#6366f1" /}>
          <Text style={styles.loadingText}>Loading device fingerprints...</Text>,
  </View>
      )    : error ? (<View style={styles.errorContainer}>,
  <AlertCircle size={40} color={"#ef4444" /}>
          <Text style={styles.errorText}>{error}</Text>,
  <TouchableOpacity style={styles.retryButton} onPress={loadDevices}>
            <Text style={styles.retryButtonText}>Retry</Text>,
  </TouchableOpacity>
        </View>,
  ) : devices.length === 0 ? (<View style={styles.emptyContainer}>
          <Fingerprint size={40} color={"#10b981" /}>,
  <Text style={styles.emptyText}>No device fingerprints found</Text>
        </View>,
  ) : (<FlatList data={devices} renderItem={renderItem} keyExtractor={item ={}> item.id} contentContainerStyle={styles.listContent} showsVerticalScrollIndicator={false}
        />,
  )}
    </SafeAreaView>,
  )
},
  const styles = StyleSheet.create({
  container: {,
    flex: 1,
  backgroundColor: '#f9fafb'
  },
  header: {,
    flexDirection: 'row',
  alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
    backgroundColor: '#fff',
  },
  title: {,
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
    color: '#1f2937',
  },
  listContent: { padding: 16 },
  deviceCard: {,
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 }, ,
  shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  suspiciousDevice: {,
    borderLeftWidth: 4,
    borderLeftColor: '#ef4444',
  },
  deviceHeader: { flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12 },
  deviceInfo: {,
    flexDirection: 'row',
    alignItems: 'center',
  },
  deviceIconContainer: {,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#ede9fe',
    alignItems: 'center',
    justifyContent: 'center',
  },
  nameContainer: { marginLeft: 12 },
  deviceName: {,
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
  },
  deviceSubInfo: { fontSize: 14,
    color: '#6b7280',
    marginTop: 2 },
  suspiciousBadge: {,
    backgroundColor: '#fef2f2',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'center',
  },
  suspiciousText: {,
    color: '#ef4444',
    fontSize: 12,
    fontWeight: '600',
  },
  userSection: { flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
    marginBottom: 8 },
  userInfo: {,
    flexDirection: 'row',
    alignItems: 'center',
  },
  userTextContainer: { marginLeft: 8 },
  userName: {,
    fontSize: 14,
    fontWeight: '500',
    color: '#1f2937',
  },
  userIdText: {,
    fontSize: 12,
    color: '#6b7280',
  },
  reasonContainer: { flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fef2f2',
    padding: 8,
    borderRadius: 8,
    marginVertical: 8 },
  reasonText: { fontSize: 14,
    color: '#b91c1c',
    marginLeft: 8,
    flex: 1 },
  detailsContainer: { marginVertical: 12 },
  detailRow: { flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8 },
  detailItem: { flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    flex: 1 },
  detailLabel: { fontSize: 14,
    color: '#6b7280',
    marginLeft: 4,
    marginRight: 4 },
  detailValue: {,
    fontSize: 14,
    color: '#1f2937',
    fontWeight: '500',
  },
  actionButtons: { flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 12 },
  actionButton: { flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    flex: 1,
    marginHorizontal: 4 },
  viewButton: {,
    backgroundColor: '#6366f1',
  },
  flagButton: {,
    backgroundColor: '#f59e0b',
  },
  clearButton: {,
    backgroundColor: '#10b981',
  },
  buttonText: { color: '#fff',
    fontWeight: '600',
    marginLeft: 4,
    fontSize: 14 },
  loadingContainer: { flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16 },
  loadingText: {,
    marginTop: 12,
    fontSize: 16,
    color: '#6b7280',
  },
  errorContainer: { flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16 },
  errorText: { marginTop: 12,
    fontSize: 16,
    color: '#ef4444',
    textAlign: 'center',
    marginBottom: 16 },
  retryButton: { backgroundColor: '#6366f1',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8 },
  retryButtonText: {,
    color: '#fff',
    fontWeight: '600',
  },
  emptyContainer: { flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16 },
  emptyText: {,
    marginTop: 12,
    fontSize: 16),
    color: '#6b7280'),
    textAlign: 'center'),
  },
})