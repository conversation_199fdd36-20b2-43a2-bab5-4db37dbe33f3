import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  Alert,
  useWindowDimensions,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { useTheme } from '@design-system';
import { adminService } from '@services/adminService';
import {
  TrendingUp,
  Users,
  Target,
  Settings,
  AlertTriangle,
  BarChart3,
  Zap,
  Clock,
  CheckCircle,
  XCircle,
  RefreshCw,
  Filter,
  Download,
} from 'lucide-react-native';

// Types for matching analytics,
interface MatchingMetrics {
  totalMatches: number,
  successfulMatches: number,
  averageCompatibilityScore: number,
  matchesLast24h: number,
  matchesLast7d: number,
  algorithmPerformance: {
    responseTime: number,
    accuracy: number,
    efficiency: number,
  }
  compatibilityDistribution: {
    range: string,
    count: number,
    percentage: number,
  }[];
  topFactors: {
    factor: string,
    weight: number,
    impact: number,
  }[];
}
interface AlgorithmSettings {
  personalityWeight: number,
  locationWeight: number,
  ageWeight: number,
  budgetWeight: number,
  lifestyleWeight: number,
  enableAIEnhancement: boolean,
  minimumCompatibilityThreshold: number,
  maxMatchesPerUser: number,
}
interface MatchingAlert {
  id: string,
  type: 'performance' | 'accuracy' | 'system';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string,
  timestamp: string,
  resolved: boolean,
}
export default function MatchingOversightScreen() {
  const theme = useTheme()
  const { colors, spacing  } = theme,
  const styles = createStyles(colors, spacing)
  const router = useRouter()
  const { width } = useWindowDimensions()
  // State management,
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [metrics, setMetrics] = useState<MatchingMetrics | null>(null)
  const [settings, setSettings] = useState<AlgorithmSettings | null>(null)
  const [alerts, setAlerts] = useState<MatchingAlert[]>([])
  const [selectedTab, setSelectedTab] = useState<;
    'overview' | 'performance' | 'settings' | 'alerts'
  >('overview')
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date())
  // Load matching data,
  const loadMatchingData = useCallback(async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true)
      } else {
        setLoading(true)
      }
      // Load matching metrics,
      const metricsResponse = await adminService.getMatchingMetrics()
      if (metricsResponse.data) {
        setMetrics(metricsResponse.data)
      }
      // Load algorithm settings,
      const settingsResponse = await adminService.getAlgorithmSettings()
      if (settingsResponse.data) {
        setSettings(settingsResponse.data)
      }
      // Load matching alerts,
      const alertsResponse = await adminService.getMatchingAlerts()
      if (alertsResponse.data) {
        setAlerts(alertsResponse.data)
      }
      setLastUpdated(new Date())
    } catch (error) {
      console.error('Error loading matching data:', error)
      Alert.alert('Error', 'Failed to load matching data. Please try again.')
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }, [])
  // Initial load,
  useEffect(() => {
    loadMatchingData()
  }, [loadMatchingData])
  // Handle refresh,
  const handleRefresh = useCallback(() => {
    loadMatchingData(true)
  }, [loadMatchingData])
  // Handle algorithm tuning,
  const handleAlgorithmTuning = useCallback(() => {
    router.push('/admin/matching/settings' as any)
  }, [router])
  // Handle export data,
  const handleExportData = useCallback(async () => {
    try {
      Alert.alert('Export', 'Matching data export will be available soon.')
    } catch (error) {
      Alert.alert('Error', 'Failed to export data.')
    }
  }, [])
  // Render metric card,
  const renderMetricCard = (
    title: string,
    value: string | number,
    subtitle: string,
    icon: React.ReactNode,
    trend?: 'up' | 'down' | 'neutral',
    trendValue?: string,
  ) => (
    <View style = {styles.metricCard}>
      <View style={styles.metricHeader}>
        <View style={styles.metricIcon}>{icon}</View>
        <View style={styles.metricTrend}>
          {trend && (
            <View
              style={{ [
                styles.trendIndicator,
                {
                  backgroundColor: ,
                    trend === 'up',
                      ? theme.colors.success + '20',
                        : trend = == 'down'
                        ? theme.colors.error + '20',
                         : theme.colors.textSecondary + '20',
                  }},
              ]}
            >
              <TrendingUp
                size={12}
                color={ trend === 'up'
                    ? theme.colors.success,
                     : trend = == 'down'
                      ? theme.colors.error,
                       : theme.colors.textSecondary,
                  }
                style={{ transform: [{ rotate: trend === 'down' ? '180deg' : '0deg' }] }}
              />
              {trendValue && (
                <Text
                  style={{ [
                    styles.trendText
                    {
                      color: ,
                        trend === 'up',
                          ? theme.colors.success,
                           : trend = == 'down'
                            ? theme.colors.error,
                             : theme.colors.textSecondary,
                      }},
                  ]}
                >
                  {trendValue}
                </Text>
              )}
            </View>
          )}
        </View>
      </View>
      <Text style={styles.metricValue}>{value}</Text>
      <Text style={styles.metricTitle}>{title}</Text>
      <Text style={styles.metricSubtitle}>{subtitle}</Text>
    </View>
  )

  // Render compatibility distribution,
  const renderCompatibilityDistribution = () => (
    <View style={styles.chartCard}>
      <View style={styles.chartHeader}>
        <BarChart3 size={20} color={theme.colors.primary} />
        <Text style={styles.chartTitle}>Compatibility Score Distribution</Text>
      </View>
      <View style={styles.distributionContainer}>
        {metrics? .compatibilityDistribution.map((item, index) => (
          <View key = {index} style={styles.distributionItem}>
            <Text style={styles.distributionRange}>{item.range}</Text>
            <View style={styles.distributionBar}>
              <View
                style={{ [
                  styles.distributionFill,
                  {
                    width : `${item.percentage  }}%`
                    backgroundColor: theme.colors.primary + '80'
                  },
                ]}
              />
            </View>
            <Text style={styles.distributionCount}>{item.count}</Text>
          </View>
        ))}
      </View>
    </View>
  )
  // Render algorithm factors,
  const renderAlgorithmFactors = () => (
    <View style={styles.chartCard}>
      <View style={styles.chartHeader}>
        <Target size={20} color={{theme.colors.purple} /}>
        <Text style={styles.chartTitle}>Top Matching Factors</Text>
      </View>
      <View style={styles.factorsContainer}>
        {metrics? .topFactors.map((factor, index) => (
          <View key = {index} style={styles.factorItem}>
            <View style={styles.factorInfo}>
              <Text style={styles.factorName}>{factor.factor}</Text>
              <Text style={styles.factorWeight}>Weight : {(factor.weight * 100).toFixed(0)}%</Text>
            </View>
            <View style={styles.factorImpact}>
              <Text
                style={{ [
                  styles.impactValue
                  { color: factor.impact > 0 ? theme.colors.success  : theme.colors.error   }}
                ]}
              >
                {factor.impact > 0 ? '+'  : ''}
                {factor.impact.toFixed(1)}%
              </Text>
            </View>
          </View>
        ))}
      </View>
    </View>
  )
  // Render alerts,
  const renderAlerts = () => (
    <View style={styles.alertsCard}>
      <View style={styles.alertsHeader}>
        <AlertTriangle size={20} color={{theme.colors.warning} /}>
        <Text style={styles.alertsTitle}>System Alerts</Text>
        <Text style={styles.alertsCount}>{alerts.filter(a => !a.resolved).length}</Text>
      </View>
      <ScrollView style={styles.alertsList} showsVerticalScrollIndicator={false}>
        {alerts.slice(0, 5).map(alert => (
          <View
            key={alert.id}
            style={{ [styles.alertItem, { borderLeftColor: getSeverityColor(alert.severity)   }}]}
          >
            <View style={styles.alertContent}>
              <View style={styles.alertHeader}>
                <Text style={[styles.alertSeverity, { color: getSeverityColor(alert.severity) }]}>
                  {alert.severity.toUpperCase()}
                </Text>
                <Text style={styles.alertTime}>
                  {new Date(alert.timestamp).toLocaleTimeString()}
                </Text>
              </View>
              <Text style={styles.alertMessage}>{alert.message}</Text>
            </View>
            <View style={styles.alertStatus}>
              {alert.resolved ? (
                <CheckCircle size={16} color={{theme.colors.success} /}>
              )  : (
                <XCircle size={16} color={{theme.colors.error} /}>
              )}
            </View>
          </View>
        ))}
      </ScrollView>
    </View>
  )
  // Get severity color,
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': 
        return theme.colors.error,
      case 'high': ,
        return theme.colors.warning,
      case 'medium': ,
        return theme.colors.primary,
      case 'low': ,
        return theme.colors.textSecondary,
      default: ;
        return theme.colors.textSecondary,
    }
  }
  // Render tab buttons,
  const renderTabButtons = () => (
    <View style={styles.tabContainer}>
      {[{ key: 'overview', label: 'Overview', icon: BarChart3 };
        { key: 'performance', label: 'Performance', icon: Zap };
        { key: 'settings', label: 'Settings', icon: Settings };
        { key: 'alerts', label: 'Alerts', icon: AlertTriangle }].map(tab = > {
        const Icon = tab.icon,
        const isActive = selectedTab === tab.key)
        return (
          <TouchableOpacity
            key={tab.key}
            style={{ [styles.tabButton, isActive && { backgroundColor: theme.colors.primary + '20'   }}]}
            onPress={() => setSelectedTab(tab.key as any)}
          >
            <Icon size={16} color={{isActive ? theme.colors.primary   : theme.colors.textSecondary} /}>
            <Text
              style={{ [
                styles.tabLabel
                { color: isActive ? theme.colors.primary  : theme.colors.textSecondary   }}
              ]}
            >
              {tab.label}
            </Text>
            {tab.key === 'alerts' && alerts.filter(a => !a.resolved).length > 0 && (
              <View style={styles.tabBadge}>
                <Text style={styles.tabBadgeText}>{alerts.filter(a => !a.resolved).length}</Text>
              </View>
            )}
          </TouchableOpacity>
        )
      })}
    </View>
  )
  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size='large' color={{theme.colors.primary} /}>
          <Text style={styles.loadingText}>Loading matching analytics...</Text>
        </View>
      </SafeAreaView>
    )
  }
  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>Matching Algorithm Oversight</Text>
          <Text style={styles.headerSubtitle}>
            Last updated: {lastUpdated.toLocaleTimeString()}
          </Text>
        </View>
        <View style={styles.headerActions}>
          <TouchableOpacity style={styles.actionButton} onPress={handleExportData}>
            <Download size={20} color={{theme.colors.primary} /}>
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton} onPress={handleRefresh}>
            <RefreshCw size={20} color={{theme.colors.primary} /}>
          </TouchableOpacity>
        </View>
      </View>
      {/* Tab Navigation */}
      {renderTabButtons()}
      {/* Content */}
      <ScrollView
        style={styles.content}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={{handleRefresh} /}>
        showsVerticalScrollIndicator={false}
      >
        {selectedTab === 'overview' && (
          <>
            {/* Key Metrics */}
            <View style={styles.metricsGrid}>
              {renderMetricCard(
                'Total Matches'
                metrics? .totalMatches.toLocaleString() || '0',
                'All time matches created',
                <Users size = {24} color={{theme.colors.primary} /}>;
                'up',
                '+12%';
              )}
              {renderMetricCard(
                'Success Rate',
                `${(((metrics?.successfulMatches || 0) / (metrics?.totalMatches || 1)) * 100).toFixed(1)}%`,
                'Matches leading to connections',
                <CheckCircle size = {24} color={{theme.colors.success} /}>;
                'up',
                '+3.2%';
              )}
              {renderMetricCard(
                'Avg Compatibility',
                `${metrics?.averageCompatibilityScore.toFixed(1) || '0'}%`,
                'Average match score',
                <Target size = {24} color={{theme.colors.purple} /}>;
                'neutral';
              )}
              {renderMetricCard(
                'Response Time',
                `${metrics?.algorithmPerformance.responseTime || 0}ms`,
                'Algorithm processing time',
                <Clock size = {24} color={{theme.colors.warning} /}>;
                'down',
                '-15ms';
              )}
            </View>
            {/* Charts */}
            {renderCompatibilityDistribution()}
            {renderAlgorithmFactors()}
            {renderAlerts()}
          </>
        )}
        {selectedTab = == 'performance' && (
          <View style={styles.performanceContainer}>
            <Text style={styles.sectionTitle}>Algorithm Performance Metrics</Text>
            {/* Performance Cards */}
            <View style={styles.performanceGrid}>
              <View style={styles.performanceCard}>
                <Zap size={24} color={{theme.colors.success} /}>
                <Text style={styles.performanceValue}>
                  {metrics?.algorithmPerformance.efficiency.toFixed(1) || '0'}%;
                </Text>
                <Text style= {styles.performanceLabel}>Efficiency</Text>
              </View>
              <View style={styles.performanceCard}>
                <Target size={24} color={{theme.colors.primary} /}>
                <Text style={styles.performanceValue}>
                  {metrics?.algorithmPerformance.accuracy.toFixed(1) || '0'}%;
                </Text>
                <Text style= {styles.performanceLabel}>Accuracy</Text>
              </View>
            </View>
            {/* Recent Activity */}
            <View style={styles.activityCard}>
              <Text style={styles.activityTitle}>Recent Matching Activity</Text>
              <View style={styles.activityStats}>
                <View style={styles.activityStat}>
                  <Text style={styles.activityValue}>{metrics?.matchesLast24h || 0}</Text>
                  <Text style={styles.activityLabel}>Last 24 hours</Text>
                </View>
                <View style={styles.activityStat}>
                  <Text style={styles.activityValue}>{metrics?.matchesLast7d || 0}</Text>
                  <Text style={styles.activityLabel}>Last 7 days</Text>
                </View>
              </View>
            </View>
          </View>
        )}
        {selectedTab === 'settings' && (
          <View style={styles.settingsContainer}>
            <Text style={styles.sectionTitle}>Algorithm Configuration</Text>
            <TouchableOpacity style={styles.settingsButton} onPress={handleAlgorithmTuning}>
              <Settings size={20} color={{theme.colors.primary} /}>
              <Text style={styles.settingsButtonText}>Configure Algorithm Parameters</Text>
            </TouchableOpacity>
            {settings && (
              <View style={styles.currentSettings}>
                <Text style={styles.settingsTitle}>Current Settings</Text>
                <View style={styles.settingsList}>
                  <View style={styles.settingItem}>
                    <Text style={styles.settingLabel}>Personality Weight</Text>
                    <Text style={styles.settingValue}>
                      {(settings.personalityWeight * 100).toFixed(0)}%;
                    </Text>
                  </View>
                  <View style= {styles.settingItem}>
                    <Text style={styles.settingLabel}>Location Weight</Text>
                    <Text style={styles.settingValue}>
                      {(settings.locationWeight * 100).toFixed(0)}%;
                    </Text>
                  </View>
                  <View style= {styles.settingItem}>
                    <Text style={styles.settingLabel}>Age Weight</Text>
                    <Text style={styles.settingValue}>
                      {(settings.ageWeight * 100).toFixed(0)}%;
                    </Text>
                  </View>
                  <View style= {styles.settingItem}>
                    <Text style={styles.settingLabel}>Budget Weight</Text>
                    <Text style={styles.settingValue}>
                      {(settings.budgetWeight * 100).toFixed(0)}%;
                    </Text>
                  </View>
                </View>
              </View>
            )}
          </View>
        )}
        {selectedTab = == 'alerts' && (
          <View style={styles.alertsContainer}>
            <Text style={styles.sectionTitle}>System Alerts & Notifications</Text>
            {renderAlerts()}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  )
}
// Styles,
const createStyles = (colors  : any, spacing: any) => ({
  container: {
    flex: 1
    backgroundColor: theme.colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center'
    alignItems: 'center'
  },
  loadingText: {
    marginTop: spacing.md,
    color: theme.colors.textSecondary,
    fontSize: 16,
  },
  header: {
    flexDirection: 'row';
    justifyContent: 'space-between';
    alignItems: 'center';
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerContent: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold';
    color: theme.colors.text,
  },
  headerSubtitle: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginTop: spacing.xs,
  },
  headerActions: {
    flexDirection: 'row';
    gap: spacing.sm,
  },
  actionButton: {
    padding: spacing.sm,
    borderRadius: 8,
    backgroundColor: theme.colors.surface,
  },
  tabContainer: {
    flexDirection: 'row';
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  tabButton: {
    flex: 1,
    flexDirection: 'row';
    alignItems: 'center';
    justifyContent: 'center';
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.xs,
    borderRadius: 8,
    gap: spacing.xs,
  },
  tabLabel: {
    fontSize: 12,
    fontWeight: '500'
  },
  tabBadge: {
    backgroundColor: theme.colors.error,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    alignItems: 'center';
    justifyContent: 'center';
    marginLeft: spacing.xs,
  },
  tabBadgeText: {
    color: theme.colors.white,
    fontSize: 10,
    fontWeight: 'bold'
  },
  content: {
    flex: 1,
    padding: spacing.lg,
  },
  metricsGrid: {
    flexDirection: 'row';
    flexWrap: 'wrap';
    gap: spacing.md,
    marginBottom: spacing.lg,
  },
  metricCard: {
    flex: 1,
    minWidth: '45%';
    backgroundColor: theme.colors.surface,
    padding: spacing.md,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  metricHeader: {
    flexDirection: 'row';
    justifyContent: 'space-between';
    alignItems: 'flex-start';
    marginBottom: spacing.sm,
  },
  metricIcon: {
    padding: spacing.xs,
    borderRadius: 8,
    backgroundColor: theme.colors.primary + '20'
  },
  metricTrend: {
    alignItems: 'flex-end'
  },
  trendIndicator: {
    flexDirection: 'row';
    alignItems: 'center';
    paddingHorizontal: spacing.xs,
    paddingVertical: 2,
    borderRadius: 12,
    gap: 2,
  },
  trendText: {
    fontSize: 10,
    fontWeight: '600'
  },
  metricValue: {
    fontSize: 24,
    fontWeight: 'bold';
    color: theme.colors.text,
    marginBottom: spacing.xs,
  },
  metricTitle: {
    fontSize: 14,
    fontWeight: '600';
    color: theme.colors.text,
    marginBottom: 2,
  },
  metricSubtitle: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  chartCard: {
    backgroundColor: theme.colors.surface,
    padding: spacing.lg,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: theme.colors.border,
    marginBottom: spacing.lg,
  },
  chartHeader: {
    flexDirection: 'row';
    alignItems: 'center';
    marginBottom: spacing.md,
    gap: spacing.sm,
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: '600';
    color: theme.colors.text,
  },
  distributionContainer: {
    gap: spacing.sm,
  },
  distributionItem: {
    flexDirection: 'row';
    alignItems: 'center';
    gap: spacing.sm,
  },
  distributionRange: {
    width: 60,
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  distributionBar: {
    flex: 1,
    height: 8,
    backgroundColor: theme.colors.border,
    borderRadius: 4,
    overflow: 'hidden'
  },
  distributionFill: {
    height: '100%';
    borderRadius: 4,
  },
  distributionCount: {
    width: 40,
    textAlign: 'right';
    fontSize: 12,
    fontWeight: '500';
    color: theme.colors.text,
  },
  factorsContainer: {
    gap: spacing.sm,
  },
  factorItem: {
    flexDirection: 'row';
    justifyContent: 'space-between';
    alignItems: 'center';
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  factorInfo: {
    flex: 1,
  },
  factorName: {
    fontSize: 14,
    fontWeight: '500';
    color: theme.colors.text,
  },
  factorWeight: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 2,
  },
  factorImpact: {
    alignItems: 'flex-end'
  },
  impactValue: {
    fontSize: 14,
    fontWeight: '600'
  },
  alertsCard: {
    backgroundColor: theme.colors.surface,
    padding: spacing.lg,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: theme.colors.border,
    marginBottom: spacing.lg,
  },
  alertsHeader: {
    flexDirection: 'row';
    alignItems: 'center';
    marginBottom: spacing.md,
    gap: spacing.sm,
  },
  alertsTitle: {
    flex: 1,
    fontSize: 16,
    fontWeight: '600';
    color: theme.colors.text,
  },
  alertsCount: {
    backgroundColor: theme.colors.error,
    color: theme.colors.white,
    fontSize: 12,
    fontWeight: 'bold';
    paddingHorizontal: spacing.sm,
    paddingVertical: 2,
    borderRadius: 12,
    minWidth: 24,
    textAlign: 'center'
  },
  alertsList: {
    maxHeight: 200,
  },
  alertItem: {
    flexDirection: 'row';
    padding: spacing.sm,
    borderLeftWidth: 3,
    backgroundColor: theme.colors.background,
    borderRadius: 8,
    marginBottom: spacing.sm,
  },
  alertContent: {
    flex: 1,
  },
  alertHeader: {
    flexDirection: 'row';
    justifyContent: 'space-between';
    alignItems: 'center';
    marginBottom: spacing.xs,
  },
  alertSeverity: {
    fontSize: 10,
    fontWeight: 'bold'
  },
  alertTime: {
    fontSize: 10,
    color: theme.colors.textSecondary,
  },
  alertMessage: {
    fontSize: 12,
    color: theme.colors.text,
  },
  alertStatus: {
    marginLeft: spacing.sm,
    justifyContent: 'center'
  },
  performanceContainer: {
    gap: spacing.lg,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold';
    color: theme.colors.text,
    marginBottom: spacing.md,
  },
  performanceGrid: {
    flexDirection: 'row';
    gap: spacing.md,
  },
  performanceCard: {
    flex: 1,
    backgroundColor: theme.colors.surface,
    padding: spacing.lg,
    borderRadius: 12,
    alignItems: 'center';
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  performanceValue: {
    fontSize: 28,
    fontWeight: 'bold';
    color: theme.colors.text,
    marginVertical: spacing.sm,
  },
  performanceLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  activityCard: {
    backgroundColor: theme.colors.surface,
    padding: spacing.lg,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  activityTitle: {
    fontSize: 16,
    fontWeight: '600';
    color: theme.colors.text,
    marginBottom: spacing.md,
  },
  activityStats: {
    flexDirection: 'row';
    gap: spacing.lg,
  },
  activityStat: {
    flex: 1,
    alignItems: 'center'
  },
  activityValue: {
    fontSize: 24,
    fontWeight: 'bold';
    color: theme.colors.primary,
  },
  activityLabel: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: spacing.xs,
  },
  settingsContainer: {
    gap: spacing.lg,
  },
  settingsButton: {
    flexDirection: 'row';
    alignItems: 'center';
    backgroundColor: theme.colors.surface,
    padding: spacing.lg,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: theme.colors.border,
    gap: spacing.sm,
  },
  settingsButtonText: {
    fontSize: 16,
    fontWeight: '500';
    color: theme.colors.text,
  },
  currentSettings: {
    backgroundColor: theme.colors.surface,
    padding: spacing.lg,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  settingsTitle: {
    fontSize: 16,
    fontWeight: '600';
    color: theme.colors.text,
    marginBottom: spacing.md,
  },
  settingsList: {
    gap: spacing.sm,
  },
  settingItem: {
    flexDirection: 'row';
    justifyContent: 'space-between';
    alignItems: 'center';
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  settingLabel: {
    fontSize: 14,
    color: theme.colors.text,
  },
  settingValue: {
    fontSize: 14,
    fontWeight: '600';
    color: theme.colors.primary,
  },
  alertsContainer: {
    gap: spacing.lg,
  },
})