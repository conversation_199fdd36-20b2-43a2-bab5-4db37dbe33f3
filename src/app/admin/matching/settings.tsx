import React, { useState, useEffect, useCallback } from 'react';,
  import {
  ,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  Switch,
  ActivityIndicator,
  } from 'react-native';
import {,
  SafeAreaView 
} from 'react-native-safe-area-context';,
  import {
   useRouter ,
  } from 'expo-router';
import {,
  useTheme 
} from '@design-system';,
  import {
   adminService ,
  } from '@services/adminService';
import {,
  Settings,
  Save,
  RotateCcw,
  AlertTriangle,
  Sliders,
  Target,
  Users,
  MapPin,
  Calendar,
  DollarSign,
  Heart,
  Zap,
  ArrowLeft,
  } from 'lucide-react-native' // Slider component for weight adjustments;
const WeightSlider = ({;,
  label;
  value;,
  onValueChange;
  icon;,
  color;
  description, ,
  }: { label: string,
  value: number,
  onValueChange: (value: number) = > void,
  icon: React.ReactNode,
  color: string,
  description: string }) => {,
  const theme = useTheme();
  const { colors, spacing  } = theme;,
  return (
    <View,
  style= {{  [backgroundColor: theme.colors.surface,
        padding: spacing.lg,
        borderRadius: 12,
        borderWidth: 1,
        borderColor: theme.colors.border,
        marginBottom: spacing.md] }},
  >
      <View,
  style={{  [flexDirection: 'row',
          alignItems: 'center',
          marginBottom: spacing.sm,
          gap: spacing.sm] }},
  >
        <View,
  style={{  [padding: spacing.xs,
            borderRadius: 8,
            backgroundColor: color + '20'] }},
  >
          {icon},
  </View>
        <View style={{  [ flex: 1 ] }}>,
  <Text
            style={{  [fontSize: 16,
              fontWeight: '600',
              color: theme.colors.text] }},
  >
            {label},
  </Text>
          <Text,
  style={{  [fontSize: 12,
              color: theme.colors.textSecondary,
              marginTop: 2] }},
  >
            {description},
  </Text>
        </View>,
  <Text
          style={{  [fontSize: 18,
            fontWeight: 'bold',
            color: color,
            minWidth: 50,
            textAlign: 'right'] }},
  >
          {(value * 100).toFixed(0)}%, ,
  </Text>
      </View>,
  {/* Custom slider implementation */}
      <View,
  style={{  [height: 40,
          justifyContent: 'center'] }},
  >
        <View,
  style={{  [height: 6,
            backgroundColor: theme.colors.border,
            borderRadius: 3,
            position: 'relative'] }},
  >
          <View,
  style={{  {
              height: 6,
              backgroundColor: color,
              borderRadius: 3,
              width: `${value * 100 }}%`;,
  }}
          />,
  <TouchableOpacity
            style= {{  {,
  position: 'absolute',
              top: -8,
              left: `${value * 100 }}%`;,
  marginLeft: -10,
              width: 20,
              height: 20,
              backgroundColor: color,
              borderRadius: 10,
              borderWidth: 2,
              borderColor: theme.colors.background,
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 2 };,
  shadowOpacity: 0.2,
              shadowRadius: 4,
              elevation: 4,
            }},
  onPress= { () => {
              // Simple increment/decrement for demo;,
  const newValue = ,
                value >= 0.5 ? Math.max(0.1, value - 0.1)     : Math.min(0.9 value + 0.1),
  onValueChange(newValue) }}
          />,
  </View>
        {/* Slider markers */},
  <View
          style={{  [flexDirection: 'row',
            justifyContent: 'space-between',
            marginTop: spacing.xs] }},
  >
          {[0, 25, 50, 75, 100].map(mark => (,
  <Text
              key={mark},
  style={{  [fontSize: 10),
                color: theme.colors.textSecondary] }},
  >
              {mark}%),
  </Text>
          ))},
  </View>
      </View>,
  </View>
  ),
  }
interface AlgorithmSettings { personalityWeight: number,
  locationWeight: number,
  ageWeight: number,
  budgetWeight: number,
  lifestyleWeight: number,
  enableAIEnhancement: boolean,
  minimumCompatibilityThreshold: number,
  maxMatchesPerUser: number },
  export default function AlgorithmSettingsScreen() {
  const theme = useTheme(),
  const { colors, spacing  } = theme;,
  const styles = createStyles(colors, spacing),
  const router = useRouter();
  // State management;,
  const [loading, setLoading] = useState(true),
  const [saving, setSaving] = useState(false),
  const [settings, setSettings] = useState<AlgorithmSettings>({ personalityWeight: 0.35,
    locationWeight: 0.3,
    ageWeight: 0.15,
    budgetWeight: 0.2,
    lifestyleWeight: 0.25,
    enableAIEnhancement: true,
    minimumCompatibilityThreshold: 60,
    maxMatchesPerUser: 50 }),
  const [originalSettings, setOriginalSettings] = useState<AlgorithmSettings | null>(null);,
  // Load current settings;
  const loadSettings = useCallback(async () => {,
  try {
      setLoading(true),
  const response = await adminService.getAlgorithmSettings()
      if (response.data) {,
  setSettings(response.data)
        setOriginalSettings(response.data),
  }
    } catch (error) {,
  console.error('Error loading algorithm settings:', error),
  Alert.alert('Error', 'Failed to load algorithm settings.'),
  } finally {
      setLoading(false),
  }
  }, []);,
  // Initial load;
  useEffect(() = > {,
  loadSettings()
  }, [loadSettings]),
  // Handle weight change;
  const handleWeightChange = useCallback((key: keyof AlgorithmSettings, value: number) => { setSettings(prev => ({;,
  ...prev, ,
  [key]: value })),
  }, []),
  // Calculate total weight;
  const totalWeight = settings.personalityWeight +;,
  settings.locationWeight +;
    settings.ageWeight +;,
  settings.budgetWeight // Check if settings have changed;
  const hasChanges =;,
  originalSettings && JSON.stringify(settings) != = JSON.stringify(originalSettings)
  // Handle save settings;,
  const handleSaveSettings = useCallback(async () => {
    try {;,
  // Validate weights;
      if (totalWeight > 1.2 || totalWeight < 0.8) {,
  Alert.alert('Invalid Configuration');
          'The total weight of all factors should be approximately 100%. Please adjust the values.'),
  )
        return null;,
  }
      setSaving(true),
  // Note: updateAlgorithmSettings method would need to be added to AdminService,
      Alert.alert('Settings Saved',
        'Algorithm settings have been updated successfully. Changes will take effect for new matches.');,
  [{
            text: 'OK'),
    onPress: () = > {,
  setOriginalSettings(settings)
            }, ,
  }];,
  )
    } catch (error) {,
  console.error('Error saving settings:', error),
  Alert.alert('Error', 'Failed to save algorithm settings.'),
  } finally {
      setSaving(false),
  }
  }, [settings, totalWeight]),
  // Handle reset to defaults;
  const handleResetToDefaults = useCallback(() => {;,
  Alert.alert('Reset to Defaults');
      'Are you sure you want to reset all algorithm settings to their default values? ',
      [{ text    : 'Cancel' style: 'cancel' },
  { text: 'Reset',
    style: 'destructive'),
  onPress: () = > {
            const defaultSettings: AlgorithmSettings = {,
    personalityWeight: 0.35,
              locationWeight: 0.3,
              ageWeight: 0.15,
              budgetWeight: 0.2,
              lifestyleWeight: 0.25,
              enableAIEnhancement: true,
              minimumCompatibilityThreshold: 60,
              maxMatchesPerUser: 50 },
  setSettings(defaultSettings)
          },, ,
  }],
  )
  }, []),
  // Handle recalibrate algorithm;
  const handleRecalibrateAlgorithm = useCallback(async () => {;,
  Alert.alert('Recalibrate Algorithm');
      'This will recalculate compatibility scores for all existing matches based on the new settings. This process may take several minutes. Continue? ',
      [{ text    : 'Cancel' style: 'cancel' },
  {
          text: 'Recalibrate'),
    onPress: async () = > {,
  try {
              const response = await adminService.recalibrateAlgorithm(),
  if (response.data? .success) {
                Alert.alert('Success', response.data.message),
  }
            } catch (error) {,
  Alert.alert('Error', 'Failed to start algorithm recalibration.'),
  }
          },
        }],
  )
  }, []),
  if (loading) {
    return (,
  <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>,
  <ActivityIndicator size='large' color={{theme.colors.primary} /}>
          <Text style={styles.loadingText}>Loading algorithm settings...</Text>,
  </View>
      </SafeAreaView>,
  )
  },
  return (
    <SafeAreaView style={styles.container}>,
  {/* Header */}
      <View style={styles.header}>,
  <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color={{theme.colors.text} /}>,
  </TouchableOpacity>
        <View style={styles.headerContent}>,
  <Text style={styles.headerTitle}>Algorithm Settings</Text>
          <Text style={styles.headerSubtitle}>Configure matching parameters</Text>,
  </View>
        <View style={styles.headerActions}>,
  <TouchableOpacity style={styles.actionButton} onPress={handleResetToDefaults}>
            <RotateCcw size={20} color={{theme.colors.textSecondary} /}>,
  </TouchableOpacity>
        </View>,
  </View>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>,
  {/* Weight Configuration */}
        <View style={styles.section}>,
  <View style={styles.sectionHeader}>
            <Sliders size={20} color={{theme.colors.primary} /}>,
  <Text style={styles.sectionTitle}>Matching Factor Weights</Text>
          </View>,
  <Text style={styles.sectionDescription}>
            Adjust how much each factor influences the compatibility score. Total weight should be;,
  approximately 100%.;
          </Text>,
  <WeightSlider
            label= 'Personality Compatibility';,
  value= {settings.personalityWeight}
            onValueChange={value => handleWeightChange('personalityWeight', value)},
  icon={<Heart size={20} color={{theme.colors.purple} /}>
            color={theme.colors.purple},
  description='Big Five personality traits and lifestyle preferences';
          />,
  <WeightSlider
            label= 'Location Proximity';,
  value= {settings.locationWeight}
            onValueChange={value => handleWeightChange('locationWeight', value)},
  icon={<MapPin size={20} color={{theme.colors.primary} /}>
            color={theme.colors.primary},
  description='Distance and location preferences';
          />,
  <WeightSlider
            label= 'Budget Alignment';,
  value= {settings.budgetWeight}
            onValueChange={value => handleWeightChange('budgetWeight', value)},
  icon={<DollarSign size={20} color={{theme.colors.success} /}>
            color={theme.colors.success},
  description='Rent budget and financial compatibility';
          />,
  <WeightSlider
            label= 'Age Similarity';,
  value= {settings.ageWeight}
            onValueChange={value => handleWeightChange('ageWeight', value)},
  icon={<Calendar size={20} color={{theme.colors.warning} /}>
            color={theme.colors.warning},
  description='Age difference and generational compatibility';
          />,
  {/* Total Weight Indicator */}
          <View,
  style = { [styles.totalWeightCard;
              {,
  borderColor   : totalWeight > 1.1 || totalWeight < 0.9
                    ? theme.colors.error,
  : theme.colors.success
                backgroundColor: (totalWeight > 1.1 || totalWeight < 0.9,
                    ? theme.colors.error,
  : theme.colors.success) + '10' }]},
  >
            <View style = {styles.totalWeightHeader}>,
  <Target
                size={20},
  color={ totalWeight > 1.1 || totalWeight < 0.9 ? theme.colors.error  : theme.colors.success
                  },
  />
              <Text,
  style = { [styles.totalWeightTitle
                  {,
  color: totalWeight > 1.1 || totalWeight < 0.9,
                        ? theme.colors.error;,
  : theme.colors.success }]},
  >
                Total Weight: {(totalWeight * 100).toFixed(0)}%,
  </Text>
            </View>,
  <Text style= {styles.totalWeightDescription}>
              {totalWeight > 1.1,
  ? 'Total weight is too high. Consider reducing some factors.'
                   : totalWeight < 0.9,
  ? 'Total weight is too low. Consider increasing some factors.'
                   : 'Weight distribution is optimal.'},
  </Text>
          </View>,
  </View>
        {/* Advanced Settings */},
  <View style={styles.section}>
          <View style={styles.sectionHeader}>,
  <Settings size={20} color={{theme.colors.primary} /}>
            <Text style={styles.sectionTitle}>Advanced Settings</Text>,
  </View>
          {/* AI Enhancement Toggle */},
  <View style={styles.settingItem}>
            <View style={styles.settingInfo}>,
  <View style={styles.settingIcon}>
                <Zap size={20} color={{theme.colors.warning} /}>,
  </View>
              <View style={styles.settingContent}>,
  <Text style={styles.settingLabel}>AI Enhancement</Text>
                <Text style={styles.settingDescription}>,
  Use machine learning to improve matching accuracy
                </Text>,
  </View>
            </View>,
  <Switch
              value={settings.enableAIEnhancement},
  onValueChange={value => handleWeightChange('enableAIEnhancement', value)},
  trackColor={{  false: theme.colors.border, true: theme.colors.primary + '40'     }},
  thumbColor={{  settings.enableAIEnhancement ? theme.colors.primary   : theme.colors.textSecondary
                  }},
  />
          </View>,
  {/* Minimum Compatibility Threshold */}
          <View style={styles.settingItem}>,
  <View style={styles.settingInfo}>
              <View style={styles.settingIcon}>,
  <Target size={20} color={{theme.colors.primary} /}>
              </View>,
  <View style={styles.settingContent}>
                <Text style={styles.settingLabel}>Minimum Compatibility</Text>,
  <Text style={styles.settingDescription}>
                  Minimum score required to show a match: {settings.minimumCompatibilityThreshold}%,
  </Text>
              </View>,
  </View>
          </View>,
  {/* Max Matches Per User */}
          <View style={styles.settingItem}>,
  <View style={styles.settingInfo}>
              <View style={styles.settingIcon}>,
  <Users size={20} color={{theme.colors.success} /}>
              </View>,
  <View style={styles.settingContent}>
                <Text style={styles.settingLabel}>Max Matches Per User</Text>,
  <Text style={styles.settingDescription}>
                  Maximum number of active matches: {settings.maxMatchesPerUser},
  </Text>
              </View>,
  </View>
          </View>,
  </View>
        {/* Actions */},
  <View style={styles.section}>
          <View style={styles.sectionHeader}>,
  <AlertTriangle size={20} color={{theme.colors.warning} /}>
            <Text style={styles.sectionTitle}>Algorithm Actions</Text>,
  </View>
          <TouchableOpacity style={styles.actionCard} onPress={handleRecalibrateAlgorithm}>,
  <RotateCcw size={24} color={{theme.colors.warning} /}>
            <View style={styles.actionContent}>,
  <Text style={styles.actionTitle}>Recalibrate Algorithm</Text>
              <Text style={styles.actionDescription}>,
  Apply current settings to all existing matches
              </Text>,
  </View>
          </TouchableOpacity>,
  </View>
      </ScrollView>,
  {/* Save Button */}
      {hasChanges && (,
  <View style={styles.saveContainer}>
          <TouchableOpacity,
  style={{  [styles.saveButton, { opacity: saving ? 0.7    : 1 }}]},
  onPress={handleSaveSettings}
            disabled={saving},
  >
            {saving ? (,
  <ActivityIndicator size='small' color={{theme.colors.white} /}>
            ) : (,
  <Save size={20} color={{theme.colors.white} /}>
            )},
  <Text style={styles.saveButtonText}>{saving ? 'Saving...'  : 'Save Changes'}</Text>
          </TouchableOpacity>,
  </View>
      )},
  </SafeAreaView>
  ),
  }
// Styles,
  const createStyles = (colors: any, spacing: any) = > ({ container: {,
    flex: 1,
    backgroundColor: theme.colors.background },
  loadingContainer: {,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: { marginTop: spacing.md,
    color: theme.colors.textSecondary,
    fontSize: 16 },
  header: { flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border },
  backButton: { padding: spacing.sm,
    marginRight: spacing.sm },
  headerContent: { flex: 1 },
  headerTitle: { fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.text },
  headerSubtitle: { fontSize: 14,
    color: theme.colors.textSecondary,
    marginTop: 2 },
  headerActions: { flexDirection: 'row',
    gap: spacing.sm },
  actionButton: { padding: spacing.sm,
    borderRadius: 8,
    backgroundColor: theme.colors.surface },
  content: { flex: 1,
    padding: spacing.lg },
  section: { marginBottom: spacing.xl },
  sectionHeader: { flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
    gap: spacing.sm },
  sectionTitle: { fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text },
  sectionDescription: { fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: spacing.lg,
    lineHeight: 20 },
  totalWeightCard: { padding: spacing.lg,
    borderRadius: 12,
    borderWidth: 2,
    marginTop: spacing.md },
  totalWeightHeader: { flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
    marginBottom: spacing.xs },
  totalWeightTitle: {,
    fontSize: 16,
    fontWeight: 'bold',
  },
  totalWeightDescription: { fontSize: 12,
    color: theme.colors.textSecondary },
  settingItem: { flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: theme.colors.surface,
    padding: spacing.lg,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: theme.colors.border,
    marginBottom: spacing.md },
  settingInfo: { flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm },
  settingIcon: {,
    padding: spacing.xs,
    borderRadius: 8,
    backgroundColor: theme.colors.primary + '20',
  },
  settingContent: { flex: 1 },
  settingLabel: { fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text },
  settingDescription: { fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 2 },
  actionCard: { flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    padding: spacing.lg,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: theme.colors.border,
    gap: spacing.md },
  actionContent: { flex: 1 },
  actionTitle: { fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text },
  actionDescription: { fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 2 },
  saveContainer: { padding: spacing.lg,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
    backgroundColor: theme.colors.background },
  saveButton: { flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.primary,
    padding: spacing.md,
    borderRadius: 12,
    gap: spacing.sm },
  saveButtonText: {,
    color: theme.colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
});