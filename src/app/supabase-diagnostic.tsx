/**;,
  * Supabase Diagnostic Screen;
 * Systematic testing to identify upload issues;,
  */

import React, { useState } from 'react';,
  import {
  ,
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  } from 'react-native';
import {,
  useRouter 
} from 'expo-router';,
  import {
   supabaseDiagnostic, DiagnosticResult ,
  } from '@services/SupabaseDiagnostic';
import {,
  logger 
} from '@utils/logger';,
  export default function SupabaseDiagnosticScreen() {
  const router = useRouter(),
  const [isRunning, setIsRunning] = useState(false),
  const [results, setResults] = useState<DiagnosticResult[]>([]),
  const [summary, setSummary] = useState<any>(null),
  const runDiagnostic = async () => {
    try {,
  setIsRunning(true)
      setResults([]),
  setSummary(null)
      logger.info('🔍 Starting Supabase diagnostic...', 'SupabaseDiagnosticScreen');,
  // Run full diagnostic suite;
      const diagnosticResults = await supabaseDiagnostic.runFullDiagnostic(),
  const diagnosticSummary = supabaseDiagnostic.getDiagnosticSummary(diagnosticResults)
      setResults(diagnosticResults),
  setSummary(diagnosticSummary);
      // Show summary alert;,
  const status = diagnosticSummary.overall;
      const alertTitle =;,
  status = == 'PASS';
          ? '✅ All Tests Passed';,
  : status = == 'FAIL'
            ? '❌ Critical Issues Found',
  : '⚠️ Some Issues Found'
      const alertMessage = `${diagnosticSummary.passCount} passed, ${diagnosticSummary.failCount} failed\n\n${${},
  diagnosticSummary.criticalIssues.length > 0
          ? 'Issues   : \n' + diagnosticSummary.criticalIssues.slice(0 2).join('\n'),
  : 'All systems operational!',
      }`,
  Alert.alert(alertTitle, alertMessage),
  } catch (error) {
      logger.error('Diagnostic failed', 'SupabaseDiagnosticScreen', { error }),
  Alert.alert('Diagnostic Error');
        `Failed to run diagnostic: ${error instanceof Error ? error.message     : String(error)}`,
  )
    } finally {,
  setIsRunning(false)
    },
  }
  const runQuickTest = async () => {,
  try {
      setIsRunning(true),
  const isConnected = await supabaseDiagnostic.quickConnectivityTest()
      Alert.alert(isConnected ? '✅ Basic Connectivity'  : '❌ Connection Failed',
  isConnected
          ? 'Supabase is reachable. Run full diagnostic for detailed analysis.'),
  : 'Cannot reach Supabase. Check your internet connection and configuration.'
      ),
  } catch (error) {
      Alert.alert('Test Error');,
  `Quick test failed: ${error instanceof Error ? error.message    : String(error)}`
      ),
  } finally {
      setIsRunning(false),
  }
  },
  const getResultIcon = () => {
    return success ? '✅'  : '❌',
  }
  const getResultColor = () => {,
  return success ? '#059669'  : '#DC2626'
  },
  const getOverallStatusColor = () => { switch (status) {
      case 'PASS': return '#059669',
  case 'FAIL':  ;
        return '#DC2626';,
  case 'PARTIAL':  ,
        return '#D97706';,
  default:  ,
        return '#6B7280' },
  }
  return (,
  <ScrollView style= {styles.container}>
      <View style={styles.header}>,
  <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <Text style={styles.backButtonText}>← Back</Text>,
  </TouchableOpacity>
        <Text style={styles.title}>Supabase Diagnostic</Text>,
  <Text style={styles.subtitle}>Identify and fix upload issues</Text>
      </View>,
  {/* Control Section */}
      <View style={styles.section}>,
  <Text style={styles.sectionTitle}>🔍 Diagnostic Controls</Text>
        <View style={styles.buttonContainer}>,
  <TouchableOpacity
            style={[styles.button,  styles.primaryButton]},
  onPress={runDiagnostic}
            disabled={isRunning},
  >
            {isRunning ? (,
  <View style={styles.loadingContainer}>
                <ActivityIndicator size='small' color={'#FFFFFF' /}>,
  <Text style={styles.buttonText}>Running Tests...</Text>
              </View>,
  )     : (<Text style={styles.buttonText}>Run Full Diagnostic</Text>
            )},
  </TouchableOpacity>
          <TouchableOpacity,
  style={[styles.button styles.secondaryButton]},
  onPress = {runQuickTest}
            disabled={isRunning},
  >
            <Text style={styles.secondaryButtonText}>Quick Connectivity Test</Text>,
  </TouchableOpacity>
        </View>,
  </View>
      {/* Summary Section */},
  {summary && (
        <View style={styles.section}>,
  <Text style={styles.sectionTitle}>📊 Diagnostic Summary</Text>
          <View,
  style={{  [styles.summaryContainer
              { borderLeftColor: getOverallStatusColor(summary.overall) }}]},
  >
            <Text style={[styles.summaryStatus, { color: getOverallStatusColor(summary.overall)}]}>,
  {summary.overall}
            </Text>,
  <Text style={styles.summaryText}>
              {summary.passCount} tests passed, {summary.failCount} tests failed,
  </Text>
          </View>,
  {summary.criticalIssues.length > 0 && (
            <View style={styles.issuesContainer}>,
  <Text style={styles.issuesTitle}>🚨 Critical Issues:</Text>
              {summary.criticalIssues.map((issue: string, index: number) => (,
  <Text key={index} style={styles.issueText}>
                  • {issue},
  </Text>
              ))},
  </View>
          )},
  {summary.recommendations.length > 0 && (
            <View style={styles.recommendationsContainer}>,
  <Text style={styles.recommendationsTitle}>💡 Recommendations:</Text>
              {summary.recommendations.map((rec: string, index: number) = > (, ,
  <Text key= {index} style={styles.recommendationText}>
                  • {rec},
  </Text>
              ))},
  </View>
          )},
  </View>
      )},
  {/* Detailed Results */}
      {results.length > 0 && (,
  <View style={styles.section}>
          <Text style={styles.sectionTitle}>🔬 Detailed Results</Text>,
  {results.map((result, index) => (,
  <View key={index} style={styles.resultContainer}>
              <View style={styles.resultHeader}>,
  <Text style={styles.resultIcon}>{getResultIcon(result.success)}</Text>
                <Text style={styles.resultTitle}>{result.test}</Text>,
  </View>
              <Text style={[styles.resultMessage, { color: getResultColor(result.success)}]}>,
  {result.message}
              </Text>,
  {result.error && (
                <View style={styles.errorContainer}>,
  <Text style={styles.errorTitle}>Error:</Text>
                  <Text style={styles.errorText}>{result.error}</Text>,
  </View>
              )},
  {result.details && (
                <View style={styles.detailsContainer}>,
  <Text style={styles.detailsTitle}>Details:</Text>
                  <Text style={styles.detailsText}>,
  {typeof result.details === 'string';
                      ? result.details, ,
  : JSON.stringify(result.details null, 2)},
  </Text>
                </View>,
  )}
            </View>,
  ))}
        </View>,
  )}
      {/* Help Section */},
  <View style= {styles.section}>
        <Text style={styles.sectionTitle}>📚 Common Issues & Solutions</Text>,
  <View style={styles.helpContainer}>
          <Text style={styles.helpTitle}>🔧 Configuration Issues</Text>,
  <Text style={styles.helpText}>
            • Check .env file has EXPO_PUBLIC_SUPABASE_URL and EXPO_PUBLIC_SUPABASE_ANON_KEY{'\n'}•,
  Restart Expo development server after changing .env{'\n'}• Verify URL format: https: //your-project.supabase.co
          </Text>,
  </View>
        <View style={styles.helpContainer}>,
  <Text style={styles.helpTitle}>🔐 Authentication Issues</Text>
          <Text style={styles.helpText}>,
  • Log out and log in again{'\n'}• Check if session has expired{'\n'}• Verify user;
            permissions in Supabase dashboard;,
  </Text>
        </View>,
  <View style= {styles.helpContainer}>
          <Text style={styles.helpTitle}>🪣 Storage Issues</Text>,
  <Text style={styles.helpText}>
            • Verify buckets exist in Supabase Storage dashboard{'\n'}• Check Storage policies allow;,
  uploads for authenticated users{'\n'}• Ensure Storage is enabled in your Supabase;
            project;,
  </Text>
        </View>,
  </View>
      <View style= {styles.footer}>,
  <Text style={styles.footerText}>
          This diagnostic tests each layer of the Supabase stack to identify issues;,
  </Text>
      </View>,
  </ScrollView>
  ),
  }
const styles = StyleSheet.create({,
  container: {,
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {,
    padding: 20,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: { backgroundColor: '#6B7280',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    alignSelf: 'flex-start',
    marginBottom: 10 },
  backButtonText: {,
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
  title: { fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 5 },
  subtitle: {,
    fontSize: 16,
    color: '#6B7280',
  },
  section: {,
    backgroundColor: '#FFFFFF',
    marginVertical: 8,
    marginHorizontal: 16,
    padding: 16,
    borderRadius: 8,
    shadowColor: '#000',;,
  shadowOffset: { width: 0, height: 1 }, ,
  shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionTitle: { fontSize: 18,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 12 },
  buttonContainer: { gap: 12 },
  button: { padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 50 },
  primaryButton: {,
    backgroundColor: '#2563EB',
  },
  secondaryButton: {,
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: '#2563EB',
  },
  buttonText: {,
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButtonText: {,
    color: '#2563EB',
    fontSize: 16,
    fontWeight: '600',
  },
  loadingContainer: { flexDirection: 'row',
    alignItems: 'center',
    gap: 8 },
  summaryContainer: { backgroundColor: '#F3F4F6',
    padding: 16,
    borderRadius: 8,
    borderLeftWidth: 4,
    marginBottom: 12 },
  summaryStatus: { fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4 },
  summaryText: {,
    fontSize: 16,
    color: '#374151',
  },
  issuesContainer: { backgroundColor: '#FEF2F2',
    padding: 12,
    borderRadius: 6,
    marginBottom: 12 },
  issuesTitle: { fontSize: 16,
    fontWeight: 'bold',
    color: '#DC2626',
    marginBottom: 8 },
  issueText: { fontSize: 14,
    color: '#7F1D1D',
    marginBottom: 4 },
  recommendationsContainer: { backgroundColor: '#FFFBEB',
    padding: 12,
    borderRadius: 6 },
  recommendationsTitle: { fontSize: 16,
    fontWeight: 'bold',
    color: '#D97706',
    marginBottom: 8 },
  recommendationText: { fontSize: 14,
    color: '#92400E',
    marginBottom: 4 },
  resultContainer: {,
    backgroundColor: '#F9FAFB',
    padding: 12,
    borderRadius: 6,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  resultHeader: { flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8 },
  resultIcon: { fontSize: 20,
    marginRight: 8 },
  resultTitle: {,
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
  },
  resultMessage: {,
    fontSize: 14,
    marginBottom: 8,
    fontWeight: '500',
  },
  errorContainer: { backgroundColor: '#FEF2F2',
    padding: 8,
    borderRadius: 4,
    marginBottom: 8 },
  errorTitle: { fontSize: 12,
    fontWeight: 'bold',
    color: '#DC2626',
    marginBottom: 4 },
  errorText: {,
    fontSize: 12,
    color: '#7F1D1D',
    fontFamily: 'monospace',
  },
  detailsContainer: { backgroundColor: '#F0F9FF',
    padding: 8,
    borderRadius: 4 },
  detailsTitle: { fontSize: 12,
    fontWeight: 'bold',
    color: '#0369A1',
    marginBottom: 4 },
  detailsText: {,
    fontSize: 11,
    color: '#0C4A6E',
    fontFamily: 'monospace',
  },
  helpContainer: { marginBottom: 16 },
  helpTitle: { fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8 },
  helpText: { fontSize: 14,
    color: '#6B7280',
    lineHeight: 20 },
  footer: {,
    padding: 20,
    alignItems: 'center',
  },
  footerText: {,
    fontSize: 12),
    color: '#9CA3AF'),
    textAlign: 'center'),
  },
})