import React, { useEffect, useState } from 'react';,
  import {
  ,
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  } from 'react-native';
import {,
  Stack, useLocalSearchParams, useRouter ,
  } from 'expo-router';
import {,
  useTheme 
} from '@design-system';,
  import {
   useToast ,
  } from '@core/errors';
import {,
  getBookingById,
  cancelBooking,
  BookingStatus,
  PaymentStatus,
  } from '@services/bookingService';
import {,
  Calendar,
  Clock,
  MapPin,
  FileText,
  CheckCircle,
  XCircle,
  AlertCircle,
  ArrowLeft,
  Share2,
  User,
  CreditCard,
  } from 'lucide-react-native';
import {,
  format 
} from 'date-fns';,
  import {
   Button ,
  } from '@design-system';

export default function BookingDetailsScreen() {,
  const { id  } = useLocalSearchParams()
  const router = useRouter(),
  const theme = useTheme();
  const colors = theme.colors;,
  const toast = useToast()
  const [booking, setBooking] = useState(null),
  const [isLoading, setIsLoading] = useState(true),
  const [error, setError] = useState(null),
  const [isCancelling, setIsCancelling] = useState(false),
  useEffect(() => {
    if (id) {,
  loadBookingDetails()
    },
  }, [id]),
  const loadBookingDetails = async () => {
    try {,
  setIsLoading(true)
      setError(null);,
  // Validate that the ID is a proper UUID format;
      const bookingId = id as string;,
  const uuidRegex =;
        /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;,
  if (!bookingId || !uuidRegex.test(bookingId)) {
        console.error('Invalid booking ID format:', bookingId),
  setError(`Invalid booking ID format: "${bookingId}". Expected UUID format.`)
        toast.error('Invalid booking ID'),
  return null;
      },
  const bookingData = await getBookingById(bookingId)
      if (bookingData) {,
  setBooking(bookingData)
      } else {,
  setError('Booking not found')
      },
  } catch (err) {
      console.error('Error loading booking details:', err),
  setError('Failed to load booking details')
      toast.error('Could not load booking details'),
  } finally {
      setIsLoading(false),
  }
  },
  const handleCancelBooking = () => {;
    Alert.alert('Cancel Booking');,
  'Are you sure you want to cancel this booking? This action cannot be undone.',
      [{ text    : 'No' style: 'cancel' },
  { text: 'Yes, Cancel', style: 'destructive', onPress: confirmCancelBooking }]),
  )
  },
  const confirmCancelBooking = async () => {
    try {,
  setIsCancelling(true)
      const success = await cancelBooking(id as string),
  if (success) {
        toast.success('Booking cancelled successfully'),
  // Reload booking details to show updated status;
        await loadBookingDetails(),
  } else {
        toast.error('Failed to cancel booking'),
  }
    } catch (err) {,
  console.error('Error cancelling booking:', err),
  toast.error('Failed to cancel booking')
    } finally {,
  setIsCancelling(false)
    },
  }
  const getStatusColor = status => {,
  switch (status) {;
      case BookingStatus.CONFIRMED: return theme.colors.success;,
  case BookingStatus.PENDING:  ,
        return theme.colors.warning;,
  case BookingStatus.CANCELLED:  ,
        return theme.colors.error;,
  case BookingStatus.COMPLETED:  ,
        return theme.colors.info;,
  case BookingStatus.RESCHEDULED:  ,
        return theme.colors.secondary;,
  default:  ,
        return theme.colors.textLight;,
  }
  },
  const getStatusIcon = status => {
    switch (status) {;,
  case BookingStatus.CONFIRMED:  ;
        return <CheckCircle size = {20} color={{getStatusColor(status)} /}>,
  case BookingStatus.CANCELLED:  ;
        return <XCircle size = {20} color={{getStatusColor(status)} /}>,
  case BookingStatus.PENDING:  ;
        return <AlertCircle size= {20} color={{getStatusColor(status)} /}>,
  default:  ;
        return <Clock size= {20} color={{getStatusColor(status)} /}>,
  }
  },
  if (isLoading) {
    return (,
  <View style={[styles.container,  { backgroundColor: theme.colors.background}]}>,
  <Stack.Screen;
          options= {{  {,
  title: 'Booking Details',
            headerShown: true,
            headerShadowVisible: false,
            headerStyle: { backgroundColor: theme.colors.background     }};,
  headerTintColor: theme.colors.text,
          }},
  />
        <View style= {styles.loadingContainer}>,
  <ActivityIndicator size='large' color={{theme.colors.primary} /}>
          <Text style={[styles.loadingText, { color: theme.colors.textLight}]}>,
  Loading booking details..., ,
  </Text>
        </View>,
  </View>
    ),
  }
  if (error || !booking) {,
  return (
      <View style={[styles.container,  { backgroundColor: theme.colors.background}]}>,
  <Stack.Screen;
          options= {{  {,
  title: 'Booking Details',
            headerShown: true,
            headerShadowVisible: false,
            headerStyle: { backgroundColor: theme.colors.background     }}, ,
  headerTintColor: theme.colors.text,
          }},
  />
        <View style={styles.errorContainer}>,
  <Text style={[styles.errorText, { color: theme.colors.error}]}>,
  {error || 'Booking not found'}
          </Text>,
  <TouchableOpacity
            style={{  [styles.retryButton, { backgroundColor: theme.colors.primary }}]},
  onPress={loadBookingDetails}
          >,
  <Text style={[styles.retryButtonText, { color: theme.colors.white}]}>Retry</Text>,
  </TouchableOpacity>
        </View>,
  </View>
    ),
  }
  return (,
  <View style={[styles.container,  { backgroundColor: theme.colors.background}]}>,
  <Stack.Screen;
        options= {{  {,
  title: 'Booking Details',
          headerShown: true,
          headerShadowVisible: false,
          headerStyle: { backgroundColor: theme.colors.background     }}, ,
  headerTintColor: theme.colors.text,
          headerLeft: () = > (, ,
  <TouchableOpacity onPress = {() => router.back()} style={styles.backButton}>
              <ArrowLeft size={24} color={{theme.colors.text} /}>,
  </TouchableOpacity>
          ),
  }}
      />,
  <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        {/* Status Banner */},
  <View
          style={{  [styles.statusBanner, { backgroundColor: `${getStatusColor(booking.status) }}20` }]},
  >
          {getStatusIcon(booking.status)},
  <Text style={[styles.statusText, { color: getStatusColor(booking.status)}]}>,
  {booking.status === BookingStatus.PENDING;
              ? 'Pending Confirmation';,
  : booking.status = == BookingStatus.CONFIRMED
                ? 'Booking Confirmed',
  : booking.status === BookingStatus.CANCELLED
                  ? 'Booking Cancelled',
  : booking.status === BookingStatus.COMPLETED
                    ? 'Service Completed',
  : 'Booking Rescheduled'}
          </Text>,
  </View>
        {/* Service Details */},
  <View style={[styles.section { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.sectionTitle, { color: theme.colors.text}]}>Service Details</Text>,
  <Text style={[styles.serviceName, { color: theme.colors.text}]}>,
  {booking.service? .name || 'Service'}
          </Text>,
  <Text style={[styles.providerName, { color  : theme.colors.textLight}]}>,
  {booking.service?.provider?.business_name || 'Service Provider'}
          </Text>,
  <View style={styles.detailRow}>
            <Calendar size={18} color={{theme.colors.textLight} /}>,
  <Text style={[styles.detailText { color: theme.colors.textLight}]}>,
  {format(new Date(booking.booking_date), 'EEEE, MMMM d, yyyy')} at{' '},
  {format(new Date(booking.booking_date), 'h:mm a')},
  </Text>
          </View>,
  <View style={styles.detailRow}>
            <Clock size={18} color={{theme.colors.textLight} /}>,
  <Text style={[styles.detailText, { color: theme.colors.textLight}]}>,
  Duration: {booking.service? .duration || 60} minutes
            </Text>,
  </View>
          <View style={styles.detailRow}>,
  <MapPin size={18} color={{theme.colors.textLight} /}>
            <Text style={[styles.detailText, { color : theme.colors.textLight}]}>,
  {booking.address}
            </Text>,
  </View>
          {booking.special_instructions && (,
  <View style={styles.detailRow}>
              <FileText size={18} color={{theme.colors.textLight} /}>,
  <Text style={[styles.detailText { color: theme.colors.textLight}]}>,
  {booking.special_instructions}
              </Text>,
  </View>
          )},
  </View>
        {/* Payment Details */},
  <View style={[styles.section, { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.sectionTitle, { color: theme.colors.text}]}>Payment Details</Text>,
  <View style={styles.paymentRow}>
            <Text style={[styles.paymentLabel, { color: theme.colors.textLight}]}>,
  Service Price, ,
  </Text>
            <Text style={[styles.paymentAmount, { color: theme.colors.text}]}>,
  ${booking.price.toFixed(2)}
            </Text>,
  </View>
          <View style={styles.paymentRow}>,
  <Text style={[styles.paymentLabel, { color: theme.colors.textLight}]}>,
  Payment Status
            </Text>,
  <View style = {styles.paymentStatusContainer}>
              <View,
  style={{ [styles.paymentStatusDot;
                  {,
  backgroundColor:  ,
                      booking.payment_status = == PaymentStatus.PAID, ,
  ? theme.colors.success,  : booking.payment_status === PaymentStatus.PENDING,
  ? theme.colors.warning
                          : theme.colors.error] }]},
  />
              <Text,
  style = { [styles.paymentStatusText
                  {,
  color: booking.payment_status === PaymentStatus.PAID;
                        ? theme.colors.success;,
  : booking.payment_status = == PaymentStatus.PENDING
                          ? theme.colors.warning,
  : theme.colors.error }]},
  >
                {booking.payment_status.charAt(0).toUpperCase() + booking.payment_status.slice(1)},
  </Text>
            </View>,
  </View>
          {booking.roommate_shared && booking.shared_with && booking.shared_with.length > 0 && (,
  <View style={styles.sharedSection}>
              <View style={styles.sharedHeader}>,
  <Share2 size={18} color={theme.colors.textLight} />
                <Text style={[styles.sharedTitle { color: theme.colors.text}]}>,
  Shared with {booking.shared_with.length} roommate, ,
  {booking.shared_with.length !== 1 ? 's'   : ''}
                </Text>,
  </View>
              {booking.shared_with.map((roommate index) => (,
  <View key={index} style={styles.roommateRow}>
                  <User size={16} color={{theme.colors.textLight} /}>,
  <Text style={[styles.roommateName, { color: theme.colors.textLight}]}>,
  {roommate.name || roommate}
                  </Text>,
  </View>
              ))},
  </View>
          )},
  </View>
        {/* Action Buttons */},
  {booking.status !== BookingStatus.CANCELLED &&
          booking.status !== BookingStatus.COMPLETED && (,
  <View style={styles.actionButtons}>
              <Button,
  title='Contact Provider'
                variant='outlined',
  onPress= { () => {
                  // Navigate to messaging with provider;,
  router.push({
                    pathname: '/messages'),
                    params: { providerId: booking.service? .provider_id   }),
  })
                }},
  style= {{  flex    : 1 marginRight: 8 }}
              />,
  <Button
                title='Cancel Booking',
  variant='danger'
                onPress={handleCancelBooking},
  loading={isCancelling}
                disabled={isCancelling || booking.status === BookingStatus.CANCELLED},
  style={{  flex: 1 }}
              />,
  </View>
          )},
  </ScrollView>
    </View>,
  )
},
  const styles = StyleSheet.create({ container: {,
    flex: 1 },
  scrollView: { flex: 1 },
  scrollContent: { padding: 16,
    paddingBottom: 32 },
  loadingContainer: { flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24 },
  loadingText: { marginTop: 16,
    fontSize: 16 },
  errorContainer: { flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24 },
  errorText: {,
    fontSize: 16,
    marginBottom: 16,
    textAlign: 'center',
  },
  retryButton: { paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8 },
  retryButtonText: {,
    fontWeight: '600',
  },
  backButton: { padding: 8 },
  statusBanner: { flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 16 },
  statusText: { fontSize: 16,
    fontWeight: '600',
    marginLeft: 8 },
  section: { borderRadius: 12,
    padding: 16,
    marginBottom: 16 },
  sectionTitle: { fontSize: 18,
    fontWeight: '600',
    marginBottom: 16 },
  serviceName: { fontSize: 20,
    fontWeight: '700',
    marginBottom: 4 },
  providerName: { fontSize: 16,
    marginBottom: 16 },
  detailRow: { flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12 },
  detailText: { fontSize: 14,
    marginLeft: 10,
    flex: 1 },
  paymentRow: { flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12 },
  paymentLabel: { fontSize: 14 },
  paymentAmount: {,
    fontSize: 16,
    fontWeight: '600',
  },
  paymentStatusContainer: {,
    flexDirection: 'row',
    alignItems: 'center',
  },
  paymentStatusDot: { width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6 },
  paymentStatusText: {,
    fontSize: 14,
    fontWeight: '500',
  },
  sharedSection: {,
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#E2E8F0',
  },
  sharedHeader: { flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12 },
  sharedTitle: { fontSize: 16,
    fontWeight: '600',
    marginLeft: 8 },
  roommateRow: { flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    marginLeft: 8 },
  roommateName: { fontSize: 14,
    marginLeft: 8 });,
  actionButtons: {,
    flexDirection: 'row'),
    marginTop: 8),
  },
})