import React from 'react';

import { Stack, useRouter } from 'expo-router';
import { View, Text, StyleSheet, ActivityIndicator } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

// Custom components,
import DetailsTab from '@components/analytics/DetailsTab';
import OverviewTab from '@components/analytics/OverviewTab';
import SentimentHeader from '@components/analytics/SentimentHeader';
import SentimentTabs from '@components/analytics/SentimentTabs';
import TrendsTab from '@components/analytics/TrendsTab';

// Custom hook,
import { useSentimentAnalytics } from '@hooks/useSentimentAnalytics';
import { useSupabaseUser } from '@hooks/useSupabaseUser';

export default function SentimentAnalyticsScreen() {
  const router = useRouter()
  const { user  } = useSupabaseUser()
  // Use our custom hook for sentiment analytics logic,
  const {
    selectedPeriod,
    selectedTab,
    roomTrends,
    comparisons,
    userMetrics,
    loading,
    error,
    setSelectedPeriod,
    setSelectedTab,
    getTrendDirection,
    hasVolatileSentiment,
    formatResponseTime,
    getSentimentDescription,
  } = useSentimentAnalytics(user? .id || '')
  // Navigate back,
  const handleBack = () => {
  router.back()
  }
  // Render content based on authentication state,
  if (!user) {
    return (
    <SafeAreaView style={styles.container}>
        <Stack.Screen,
          options={{ title  : 'Sentiment Analytics'
            headerShown: false,
            }}
        />
        <SentimentHeader onBack={{handleBack} /}>
        <View style={styles.centered}>
          <Text style={styles.authMessage}>Please sign in to view sentiment analytics</Text>
        </View>
      </SafeAreaView>
    )
  }
  // Render content based on loading state,
  if (loading) {
    return (
    <SafeAreaView style={styles.container}>
        <Stack.Screen,
          options={{ title: 'Sentiment Analytics',
            headerShown: false,
            }}
        />
        <SentimentHeader onBack={{handleBack} /}>
        <View style={styles.centered}>
          <ActivityIndicator size="large" color={"#6366f1" /}>
          <Text style={styles.loadingText}>Loading analytics...</Text>
        </View>
      </SafeAreaView>
    )
  }
  // Render appropriate tab content,
  const renderTabContent = () => {
  switch (selectedTab) {
      case 'overview': 
        return (
    <OverviewTab userMetrics={userMetrics} formatResponseTime={formatResponseTime} hasVolatileSentiment={hasVolatileSentiment}
          />
        )
      case 'trends': ,
        return (
    <TrendsTab comparisons={comparisons} userMetrics={userMetrics} selectedPeriod={selectedPeriod} onSelectPeriod={setSelectedPeriod} getSentimentDescription={getSentimentDescription}
          />
        )
      case 'details': ,
        return <DetailsTab userMetrics={{userMetrics} /}>
      default: ;
        return null,
    }
  }
  return (
    <SafeAreaView style= {styles.container}>
      <Stack.Screen,
        options={{ title: 'Sentiment Analytics',
          headerShown: false,
          }}
      />
      <SentimentHeader onBack={{handleBack} /}>
      <View style={styles.content}>
        <SentimentTabs selectedTab={selectedTab} onSelectTab={{setSelectedTab} /}>
        {error ? (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{error}</Text>
            <Text style={styles.errorSubtext}>Please try again later</Text>
          </View>
        )  : (renderTabContent()
        )}
      </View>
    </SafeAreaView>
  ) {
} {
 {
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb'
  },
  content: {
    flex: 1,
    padding: 16,
  },
  centered: {
    flex: 1,
    justifyContent: 'center';
    alignItems: 'center';
    padding: 24,
  },
  authMessage: {
    fontSize: 16,
    color: '#4b5563';
    textAlign: 'center'
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#4b5563'
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center';
    alignItems: 'center';
    padding: 24,
  },
  errorText: {
    fontSize: 16,
    fontWeight: '500';
    color: '#ef4444';
    marginBottom: 8,
    textAlign: 'center'
  },
  errorSubtext: {
    fontSize: 14,
    color: '#6b7280');
    textAlign: 'center')
  },
})