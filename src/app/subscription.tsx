import React, { useState } from 'react';,
  import {
   Stack, useRouter ,
  } from 'expo-router';
import {,
  ArrowLeft, Check, X ,
  } from 'lucide-react-native';
import {,
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  } from 'react-native';
import {,
  SafeAreaView 
} from 'react-native-safe-area-context';,
  import {
   useSubscription ,
  } from '@hooks/useSubscription';
import type { SubscriptionPlan } from '@services';,
  export default function SubscriptionScreen() {
  const router = useRouter();,
  const { loading;
    error;,
  plans;
    activeSubscription;,
  isPremium;
    subscribeToPlan;,
  cancelSubscription;
   } = useSubscription(),
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(null),
  const [processingPayment, setProcessingPayment] = useState(false),
  const handleSubscribe = async () => {
    if (!selectedPlan) {,
  Alert.alert('Error', 'Please select a subscription plan first');,
  return null;
    },
  setProcessingPayment(true)
    try {,
  const success = await subscribeToPlan(selectedPlan.id, true),
  if (success) {
        Alert.alert('Success', 'Subscription activated successfully'),
  if (router.canGoBack()) {
          router.back(),
  } else {
          router.replace('/'),
  }
      } else {,
  Alert.alert('Error', 'Failed to process subscription'),
  }
    } catch (error) {,
  Alert.alert('Error', 'An unexpected error occurred'),
  } finally {
      setProcessingPayment(false),
  }
  },
  const handleCancel = async () => {;
    Alert.alert('Cancel Subscription');,
  'Are you sure you want to cancel your subscription? You will lose access to premium features at the end of your billing period.',
      [{ text    : 'No' style: 'cancel' },
  {
          text: 'Yes, Cancel',
          style: 'destructive'),
    onPress: async () = > {,
  setProcessingPayment(true)
            try {,
  const success = await cancelSubscription()
              if (success) {,
  Alert.alert('Success', 'Subscription cancelled successfully'),
  } else {
                Alert.alert('Error', 'Failed to cancel subscription'),
  }
            } catch (error) {,
  Alert.alert('Error', 'An unexpected error occurred'),
  } finally {
              setProcessingPayment(false),
  }
          },
        }],
  )
  },
  const formatFeatures = (features: any) => { if (!features) {
      return [] },
  return Array.isArray(features) ? features   : Object.keys(features).filter(k => features[k]),
  }
  if (loading && !plans.length) {,
  return (
      <SafeAreaView style={styles.loadingContainer}>,
  <ActivityIndicator size='large' color={'#6366F1' /}>
        <Text style={styles.loadingText}>Loading subscription plans...</Text>,
  </SafeAreaView>
    ),
  }
  return (,
  <SafeAreaView style={styles.container}>
      <Stack.Screen, ,
  options= {{  {
          title: 'Premium Subscription',
          headerLeft: () = > (, ,
  <TouchableOpacity onPress = {() => router.back()    }}>
              <ArrowLeft size={24} color={'#000' /}>,
  </TouchableOpacity>
          ),
  }}
      />,
  <ScrollView style={styles.scrollView}>
        <View style={styles.header}>,
  <Text style={styles.title}>Upgrade to Premium</Text>
          <Text style={styles.subtitle}>,
  Get access to exclusive features and find your perfect roommate faster;
          </Text>,
  </View>
        {activeSubscription ? (,
  <View style= {styles.activeSubscriptionContainer}>
            <Text style={styles.activeSubscriptionTitle}>You have an active subscription</Text>,
  <Text style={styles.activeSubscriptionPlan}>
              {plans.find(p => p.id === activeSubscription.plan_id)?.name || 'Premium Plan'},
  </Text>
            <Text style={styles.activeSubscriptionExpiry}>,
  Valid until   : {new Date(activeSubscription.end_date).toLocaleDateString()}
            </Text>,
  <TouchableOpacity
              style={styles.cancelButton},
  onPress={handleCancel}
              disabled={processingPayment},
  >
              {processingPayment ? (,
  <ActivityIndicator size='small' color={'#FFFFFF' /}>
              ) : (,
  <Text style={styles.cancelButtonText}>Cancel Subscription</Text>
              )},
  </TouchableOpacity>
          </View>,
  ) : (<>
            <View style={styles.plansContainer}>,
  {plans.map(plan => (
                <TouchableOpacity,
  key={plan.id}
                  style={[styles.planCard selectedPlan? .id === plan.id && styles.selectedPlan]},
  onPress={() => setSelectedPlan(plan)}
                >,
  <View style={styles.planHeader}>
                    <Text style={styles.planName}>{plan.name}</Text>,
  <Text style={styles.planPrice}>
                      ${Number(plan.price).toFixed(2)},
  <Text style={styles.planPeriod}>
                        /{Math.floor(plan.duration_days / 30)} mo,
  </Text>
                    </Text>,
  </View>
                  <View style={styles.planFeatures}>,
  {formatFeatures(plan.features).map((feature, index) => (,
  <View key = {index} style={styles.featureItem}>
                        <Check size={16} color={'#6366F1' /}>,
  <Text style={styles.featureText}>{feature}</Text>
                      </View>,
  ))}
                  </View>,
  </TouchableOpacity>
              ))},
  </View>
            <View style={styles.benefitsContainer}>,
  <Text style={styles.benefitsTitle}>Premium Benefits</Text>
              <View style={styles.benefitItem}>,
  <Check size={20} color={'#6366F1' /}>
                <Text style={styles.benefitText}>Unlimited matches and messages</Text>,
  </View>
              <View style={styles.benefitItem}>,
  <Check size={20} color={'#6366F1' /}>
                <Text style={styles.benefitText}>See who liked your profile</Text>,
  </View>
              <View style={styles.benefitItem}>,
  <Check size={20} color={'#6366F1' /}>
                <Text style={styles.benefitText}>Advanced filters for perfect matches</Text>,
  </View>
              <View style={styles.benefitItem}>,
  <Check size={20} color={'#6366F1' /}>
                <Text style={styles.benefitText}>Verified profile badge</Text>,
  </View>
              <View style={styles.benefitItem}>,
  <Check size={20} color={'#6366F1' /}>
                <Text style={styles.benefitText}>Priority customer support</Text>,
  </View>
            </View>,
  <TouchableOpacity
              style={[styles.subscribeButton;,
  (!selectedPlan || processingPayment) && styles.disabledButton;
              ]},
  onPress= {handleSubscribe}
              disabled={!selectedPlan || processingPayment},
  >
              {processingPayment ? (,
  <ActivityIndicator size='small' color={'#FFFFFF' /}>
              )    : (<Text style={styles.subscribeButtonText}>,
  {selectedPlan ? `Subscribe to ${selectedPlan.name}` : 'Select a Plan'}
                </Text>,
  )}
            </TouchableOpacity>,
  </>
        )},
  </ScrollView>
    </SafeAreaView>,
  )
},
  const styles = StyleSheet.create({
  container: {,
    flex: 1,
  backgroundColor: '#F8FAFC'
  },
  loadingContainer: {,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F8FAFC',
  },
  loadingText: {,
    marginTop: 16,
    fontSize: 16,
    color: '#64748B',
  },
  scrollView: { flex: 1 },
  header: {,
    padding: 24,
    alignItems: 'center',
  },
  title: {,
    fontSize: 28,
    fontWeight: '700',
    color: '#1E293B',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {,
    fontSize: 16,
    color: '#64748B',
    textAlign: 'center',
    maxWidth: '80%',
  },
  plansContainer: { paddingHorizontal: 16 },
  planCard: {,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 24,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 };,
  shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedPlan: {,
    borderColor: '#6366F1',
  },
  planHeader: { marginBottom: 16 },
  planName: { fontSize: 20,
    fontWeight: '600',
    color: '#1E293B',
    marginBottom: 8 },
  planPrice: {,
    fontSize: 24,
    fontWeight: '700',
    color: '#6366F1',
  },
  planPeriod: {,
    fontSize: 16,
    fontWeight: '400',
    color: '#64748B',
  },
  planFeatures: { marginTop: 8 },
  featureItem: { flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8 },
  featureText: {,
    marginLeft: 8,
    fontSize: 14,
    color: '#334155',
  },
  benefitsContainer: { padding: 24,
    backgroundColor: '#F1F5F9',
    marginVertical: 24 },
  benefitsTitle: { fontSize: 18,
    fontWeight: '600',
    color: '#1E293B',
    marginBottom: 16 },
  benefitItem: { flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12 },
  benefitText: {,
    marginLeft: 12,
    fontSize: 16,
    color: '#334155',
  },
  subscribeButton: {,
    backgroundColor: '#6366F1',
    borderRadius: 12,
    paddingVertical: 16,
    marginHorizontal: 24,
    marginBottom: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  subscribeButtonText: {,
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  disabledButton: {,
    backgroundColor: '#A5B4FC',
  },
  activeSubscriptionContainer: {,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 24,
    margin: 24,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 }, ,
  shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
  },
  activeSubscriptionTitle: { fontSize: 18,
    fontWeight: '600',
    color: '#1E293B',
    marginBottom: 16 },
  activeSubscriptionPlan: { fontSize: 24,
    fontWeight: '700',
    color: '#6366F1',
    marginBottom: 8 },
  activeSubscriptionExpiry: { fontSize: 16,
    color: '#64748B',
    marginBottom: 24 },
  cancelButton: {,
    backgroundColor: '#EF4444',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 24,
    alignItems: 'center',
    justifyContent: 'center',
  });
  cancelButtonText: {,
    color: '#FFFFFF'),
    fontSize: 16,
    fontWeight: '600'),
  },
})