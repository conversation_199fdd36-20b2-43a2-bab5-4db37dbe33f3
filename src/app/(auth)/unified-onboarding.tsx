import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  ActivityIndicator,
} from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { useTheme } from '@design-system';
import { useAuth } from '@context/AuthContext';
import { ArrowRight, CheckCircle, User, Home, Shield } from 'lucide-react-native';

interface OnboardingStep {
  id: string,
  title: string,
  description: string,
  icon: any,
  route: string,
  completed: boolean,
  required: boolean,
}
export default function UnifiedOnboardingScreen() {
  const theme = useTheme();
  const styles = createStyles(theme);
  const router = useRouter();
  const { authState } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [steps, setSteps] = useState<OnboardingStep[]>([]);

  useEffect(() => {
    initializeOnboarding();
  }, []);

  const initializeOnboarding = async () => {
    try {
      setIsLoading(true);

      // Define onboarding steps based on user role and completion status;
      const onboardingSteps: OnboardingStep[] = [,
        {
          id: 'profile-setup',
          title: 'Complete Your Profile',
          description: 'Add your personal information and preferences',
          icon: User,
          route: '/(auth)/profile-setup',
          completed: false,
          required: true,
        },
        {
          id: 'verification',
          title: 'Verify Your Identity',
          description: 'Complete identity verification for safety',
          icon: Shield,
          route: '/verification',
          completed: false,
          required: true,
        },
        {
          id: 'preferences',
          title: 'Set Your Preferences',
          description: "Tell us what you're looking for in a roommate",
          icon: Home,
          route: '/(tabs)/browse',
          completed: false,
          required: false,
        },
      ];

      // TODO: Check completion status from user profile,
      // For now, all steps are incomplete;
      setSteps(onboardingSteps);
    } catch (error) {
      console.error('Error initializing onboarding:', error);
    } finally {
      setIsLoading(false);
    }
  }
  const handleStepPress = (step: OnboardingStep) => {
    if (step.completed) {
      return null; // Already completed;
    }
    router.push(step.route as any);
  }
  const handleSkipOnboarding = () => {
    // Navigate to main app;
    router.replace('/browse');
  }
  const handleCompleteOnboarding = () => {
    // Mark onboarding as complete and navigate to main app;
    router.replace('/browse');
  }
  const completedSteps = steps.filter(step => step.completed).length;
  const totalSteps = steps.length;
  const progress = totalSteps > 0 ? (completedSteps / totalSteps) * 100 : 0,
  if (isLoading) {
    return (
      <SafeAreaView style={{[styles.container, { backgroundColor: theme.colors.background }]}}>
        <Stack.Screen;
          options={
            title: 'Getting Started',
            headerShown: false,
          }
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size='large' color={{theme.colors.primary} /}>
          <Text style={{[styles.loadingText, { color: theme.colors.text }]}}>
            Setting up your onboarding...;
          </Text>
        </View>
      </SafeAreaView>
    );
  }
  return (
    <SafeAreaView style={{[styles.container, { backgroundColor: theme.colors.background }]}}>
      <Stack.Screen;
        options={{
          title: 'Getting Started',
          headerShown: true,
          headerStyle: { backgroundColor: theme.colors.background },
          headerTintColor: theme.colors.text,
          headerShadowVisible: false,
        }}
      />
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Welcome Header */}
        <View style={styles.header}>
          <Text style={{[styles.title, { color: theme.colors.text }]}}>Welcome to WeRoomies! 👋</Text>
          <Text style={{[styles.subtitle, { color: theme.colors.textSecondary }]}}>
            Let's get you set up to find your perfect roommate match;
          </Text>
        </View>
        {/* Progress Bar */}
        <View style={styles.progressSection}>
          <View style={styles.progressHeader}>
            <Text style={{[styles.progressTitle, { color: theme.colors.text }]}}>Setup Progress</Text>
            <Text style={{[styles.progressText, { color: theme.colors.textSecondary }]}}>
              {completedSteps} of {totalSteps} completed;
            </Text>
          </View>
          <View style={{[styles.progressBar, { backgroundColor: theme.colors.border }]}}>
            <View
              style={[
                styles.progressFill,
                {
                  backgroundColor: theme.colors.primary,
                  width: `${progress}%`,
                },
              ]}
            />
          </View>
        </View>
        {/* Onboarding Steps */}
        <View style={styles.stepsSection}>
          {steps.map((step, index) => {
            const IconComponent = step.icon;

            return (
              <TouchableOpacity
                key={step.id}
                style={[
                  styles.stepCard,
                  {
                    backgroundColor: theme.colors.surface,
                    borderColor: step.completed ? theme.colors.success : theme.colors.border,
                    borderWidth: step.completed ? 2 : 1,
                  },
                ]}
                onPress={() => handleStepPress(step)}
                disabled={step.completed}
              >
                <View style={styles.stepContent}>
                  <View
                    style={[
                      styles.stepIcon,
                      {
                        backgroundColor: step.completed,
                          ? theme.colors.success;
                          : theme.colors.primary + '20',
                      },
                    ]}
                  >
                    {step.completed ? (
                      <CheckCircle size={24} color={{theme.colors.white} /}>
                    ) : (,
                      <IconComponent size={24} color={{theme.colors.primary} /}>
                    )}
                  </View>
                  <View style={styles.stepText}>
                    <Text style={{[styles.stepTitle, { color: theme.colors.text }]}}>
                      {step.title}
                      {step.required && <Text style={[ color: theme.colors.error ]}> *</Text>
                    </Text>
                    <Text style={{[styles.stepDescription, { color: theme.colors.textSecondary }]}}>
                      {step.description}
                    </Text>
                  </View>
                  {!step.completed && <ArrowRight size={20} color={{theme.colors.textSecondary} /}>
                </View>
              </TouchableOpacity>
            );
          })}
        </View>
        {/* Action Buttons */}
        <View style={styles.actionSection}>
          {completedSteps === totalSteps ? (
            <TouchableOpacity
              style={[styles.primaryButton, { backgroundColor: theme.colors.primary }]}
              onPress={handleCompleteOnboarding}
            >
              <Text style={{[styles.buttonText, { color: theme.colors.white }]}}>Get Started!</Text>
            </TouchableOpacity>
          ) : (,
            <>
              <TouchableOpacity
                style={[styles.primaryButton, { backgroundColor: theme.colors.primary }]}
                onPress={() => {
                  const nextIncompleteStep = steps.find(step => !step.completed);
                  if (nextIncompleteStep) {
                    handleStepPress(nextIncompleteStep);
                  }
                }}
              >
                <Text style={{[styles.buttonText, { color: theme.colors.white }]}}>
                  Continue Setup;
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.secondaryButton, { borderColor: theme.colors.border }]}
                onPress={handleSkipOnboarding}
              >
                <Text style={{[styles.secondaryButtonText, { color: theme.colors.textSecondary }]}}>
                  Skip for Now;
                </Text>
              </TouchableOpacity>
            </>
          )}
        </View>
        {/* Required Steps Note */}
        <Text style={{[styles.requiredNote, { color: theme.colors.textSecondary }]}}>
          * Required steps must be completed for the best experience;
        </Text>
      </ScrollView>
    </SafeAreaView>
  );
}
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    loadingText: {
      marginTop: 16,
      fontSize: 16,
    },
    content: {
      flex: 1,
      padding: 20,
    },
    header: {
      marginBottom: 32,
      alignItems: 'center',
    },
    title: {
      fontSize: 28,
      fontWeight: '700',
      textAlign: 'center',
      marginBottom: 8,
    },
    subtitle: {
      fontSize: 16,
      textAlign: 'center',
      lineHeight: 22,
    },
    progressSection: {
      marginBottom: 32,
    },
    progressHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 12,
    },
    progressTitle: {
      fontSize: 18,
      fontWeight: '600',
    },
    progressText: {
      fontSize: 14,
    },
    progressBar: {
      height: 8,
      borderRadius: 4,
      overflow: 'hidden',
    },
    progressFill: {
      height: '100%',
      borderRadius: 4,
    },
    stepsSection: {
      marginBottom: 32,
    },
    stepCard: {
      borderRadius: 12,
      padding: 16,
      marginBottom: 12,
    },
    stepContent: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    stepIcon: {
      width: 48,
      height: 48,
      borderRadius: 24,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 16,
    },
    stepText: {
      flex: 1,
    },
    stepTitle: {
      fontSize: 16,
      fontWeight: '600',
      marginBottom: 4,
    },
    stepDescription: {
      fontSize: 14,
      lineHeight: 18,
    },
    actionSection: {
      marginBottom: 16,
    },
    primaryButton: {
      borderRadius: 12,
      padding: 16,
      alignItems: 'center',
      marginBottom: 12,
    },
    secondaryButton: {
      borderRadius: 12,
      padding: 16,
      alignItems: 'center',
      borderWidth: 1,
    },
    buttonText: {
      fontSize: 16,
      fontWeight: '600',
    },
    secondaryButtonText: {
      fontSize: 16,
      fontWeight: '500',
    },
    requiredNote: {
      fontSize: 12,
      textAlign: 'center',
      fontStyle: 'italic',
    },
  });
