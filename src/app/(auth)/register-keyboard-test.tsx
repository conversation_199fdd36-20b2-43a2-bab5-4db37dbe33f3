import React, { useState, useRef, useEffect } from 'react';
import {;
  View,
  Text,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  TouchableOpacity,
  Keyboard,
  Dimensions,
} from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { FormInput as Input } from '@components/ui';
import { useTheme } from '@design-system';
import { Mail, Lock, User, Phone } from 'lucide-react-native';

const { height: screenHeight } = Dimensions.get('window');

export default function RegisterKeyboardTestScreen() {
  const router = useRouter();
  const theme = useTheme();
  const verticalScrollRef = useRef<ScrollView>(null);

  // Keyboard state tracking;
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const [focusedField, setFocusedField] = useState<string | null>(null);

  // Form state;
  const [email, setEmail] = useState('');
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [phone, setPhone] = useState('');

  // Debug info;
  const [debugInfo, setDebugInfo] = useState<any>({});

  // Keyboard listeners with debug tracking;
  useEffect(() => {;
    const showKeyboard = (event: any) => {;
      const { height, duration } = event.endCoordinates;
      setKeyboardVisible(true);
      setKeyboardHeight(height);
      setDebugInfo(prev => ({;
        ...prev,
        keyboardShow: {;
          height,
          duration,
          timestamp: new Date().toISOString(),
        },
      }));

      // Auto-scroll to focused field when keyboard appears;
      if (focusedField) {
        setTimeout(() => {;
          verticalScrollRef.current?.scrollTo({;
            y: 200, // Scroll to show the focused input;
            animated: true,
          });
        }, 100);
      };
    };

    const hideKeyboard = () => {;
      setKeyboardVisible(false);
      setKeyboardHeight(0);
      setFocusedField(null);
      setDebugInfo(prev => ({;
        ...prev,
        keyboardHide: {;
          timestamp: new Date().toISOString(),
        },
      }));
    };

    const keyboardWillShow = Keyboard.addListener(;
      Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
      showKeyboard;
    );
    const keyboardWillHide = Keyboard.addListener(;
      Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
      hideKeyboard;
    );

    return () => {;
      keyboardWillShow.remove();
      keyboardWillHide.remove();
    };
  }, [focusedField]);

  const handleFieldFocus = (fieldName: string) => {;
    setFocusedField(fieldName);
    setDebugInfo(prev => ({;
      ...prev,
      focusedField: fieldName,
      focusTimestamp: new Date().toISOString(),
    }));
  };

  const handleFieldBlur = () => {;
    setFocusedField(null);
    setDebugInfo(prev => ({;
      ...prev,
      focusedField: null,
      blurTimestamp: new Date().toISOString(),
    }));
  };

  const safeTheme = {;
    colors: {;
      primary: theme.colors?.primary || '#3B82F6',
      background: theme.colors?.background || '#FFFFFF',
      text: theme.colors?.text || '#1E293B',
      textSecondary: theme.colors?.textSecondary || '#64748B',
      surface: theme.colors?.surface || '#F8FAFC',
      border: theme.colors?.border || '#E2E8F0',
      error: theme.colors?.error || '#EF4444',
      success: theme.colors?.success || '#10B981',
    },
  };

  return (
    <SafeAreaView style={{[styles.container, { backgroundColor: safeTheme.theme.colors.surface }]}}>;
      <Stack.Screen;
        options={{;
          title: 'Keyboard Test',
          headerStyle: {;
            backgroundColor: safeTheme.theme.colors.surface,
          },
          headerTitleStyle: {;
            color: safeTheme.theme.colors.text,
            fontWeight: '600',
          },
          headerTintColor: safeTheme.theme.colors.textSecondary,
        }};
      />;

      <KeyboardAvoidingView
        style={styles.keyboardAvoidingView};
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'};
        keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0};
      >;
        {/* Debug Info Panel */};
        <View style={{[styles.debugPanel, { backgroundColor: safeTheme.theme.colors.background }]}}>;
          <Text style={{[styles.debugTitle, { color: safeTheme.theme.colors.text }]}}>;
            Keyboard Debug Info;
          </Text>;
          <Text style={{[styles.debugText, { color: safeTheme.theme.colors.textSecondary }]}}>;
            Platform: {Platform.OS} | Screen Height: {screenHeight}px;
          </Text>;
          <Text style={{[styles.debugText, { color: safeTheme.theme.colors.textSecondary }]}}>;
            Keyboard: {keyboardVisible ? `Visible (${keyboardHeight}px)` : 'Hidden'};
          </Text>;
          <Text style={{[styles.debugText, { color: safeTheme.theme.colors.textSecondary }]}}>;
            Focused: {focusedField || 'None'};
          </Text>;
          {keyboardVisible && (;
            <Text style={{[styles.debugText, { color: safeTheme.theme.colors.success }]}}>;
              Available Space: {screenHeight - keyboardHeight}px;
            </Text>;
          )};
        </View>;

        <ScrollView
          ref={verticalScrollRef};
          style={styles.scrollView};
          contentContainerStyle={[;
            styles.scrollContent,
            {;
              paddingBottom: keyboardVisible ? keyboardHeight + 50 : 50,
            },
          ]};
          keyboardShouldPersistTaps='handled';
          showsVerticalScrollIndicator={true};
        >;
          {/* Test Form */};
          <View
            style={[styles.formContainer, { backgroundColor: safeTheme.theme.colors.background }]};
          >;
            <Text style={{[styles.formTitle, { color: safeTheme.theme.colors.text }]}}>;
              Registration Form Test;
            </Text>;

            <Input
              label='Email';
              value={email};
              onChangeText={setEmail};
              onFocus={() => handleFieldFocus('email')};
              onBlur={handleFieldBlur};
              placeholder='Enter your email';
              keyboardType='email-address';
              autoCapitalize='none';
              leftIcon={Mail};
              containerStyle={focusedField === 'email' ? styles.focusedInput : {}};
            />;

            <Input
              label='Username';
              value={username};
              onChangeText={setUsername};
              onFocus={() => handleFieldFocus('username')};
              onBlur={handleFieldBlur};
              placeholder='Choose a username';
              autoCapitalize='none';
              leftIcon={User};
              containerStyle={focusedField === 'username' ? styles.focusedInput : {}};
            />;

            <Input
              label='Password';
              value={password};
              onChangeText={setPassword};
              onFocus={() => handleFieldFocus('password')};
              onBlur={handleFieldBlur};
              placeholder='Create a password';
              secureTextEntry;
              leftIcon={Lock};
              containerStyle={focusedField === 'password' ? styles.focusedInput : {}};
            />;

            <Input
              label='Confirm Password';
              value={confirmPassword};
              onChangeText={setConfirmPassword};
              onFocus={() => handleFieldFocus('confirmPassword')};
              onBlur={handleFieldBlur};
              placeholder='Confirm your password';
              secureTextEntry;
              leftIcon={Lock};
              containerStyle={focusedField === 'confirmPassword' ? styles.focusedInput : {}};
            />;

            <Input
              label='Phone Number';
              value={phone};
              onChangeText={setPhone};
              onFocus={() => handleFieldFocus('phone')};
              onBlur={handleFieldBlur};
              placeholder='Enter your phone number';
              keyboardType='phone-pad';
              leftIcon={Phone};
              containerStyle={focusedField === 'phone' ? styles.focusedInput : {}};
            />;

            {/* Spacer content to test scrolling */};
            <View style={styles.spacer}>;
              <Text style={{[styles.spacerText, { color: safeTheme.theme.colors.textSecondary }]}}>;
                This content should be visible when keyboard is open.;
              </Text>;
              <Text style={{[styles.spacerText, { color: safeTheme.theme.colors.textSecondary }]}}>;
                Test scrolling by focusing on inputs above.;
              </Text>;
            </View>;
          </View>;
        </ScrollView>;

        {/* Fixed bottom buttons */};
        <View style={{[styles.buttonContainer, { backgroundColor: safeTheme.theme.colors.surface }]}}>;
          <TouchableOpacity
            style={[styles.testButton, { backgroundColor: safeTheme.theme.colors.primary }]};
            onPress={() => Keyboard.dismiss()};
          >;
            <Text style={{[styles.buttonText, { color: safeTheme.theme.colors.background }]}}>;
              Dismiss Keyboard;
            </Text>;
          </TouchableOpacity>;

          <TouchableOpacity
            style={[styles.testButton, { backgroundColor: safeTheme.theme.colors.success }]};
            onPress={() => router.back()};
          >;
            <Text style={{[styles.buttonText, { color: safeTheme.theme.colors.background }]}}>;
              Back to Register;
            </Text>;
          </TouchableOpacity>;
        </View>;
      </KeyboardAvoidingView>;
    </SafeAreaView>;
  );
};

const styles = StyleSheet.create({;
  container: {;
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  keyboardAvoidingView: {;
    flex: 1,
  },
  debugPanel: {;
    padding: 16,
    borderBottomWidth: Platform.select({;
      ios: StyleSheet.hairlineWidth,
      android: 1,
      default: 1,
    }),
    borderBottomColor: '#E2E8F0',
  },
  debugTitle: {;
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  debugText: {;
    fontSize: 12,
    marginBottom: 4,
    fontFamily: Platform.OS === 'ios' ? 'Menlo' : 'monospace',
  },
  scrollView: {;
    flex: 1,
  },
  scrollContent: {;
    flexGrow: 1,
    padding: 16,
  },
  formContainer: {;
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.05,
    shadowRadius: 12,
    elevation: 2,
    marginBottom: 20,
  },
  formTitle: {;
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 24,
    textAlign: 'center',
  },
  focusedInput: {;
    borderColor: '#3B82F6',
    borderWidth: 2,
    shadowColor: '#3B82F6',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  spacer: {;
    marginTop: 32,
    padding: 20,
    backgroundColor: '#F1F5F9',
    borderRadius: 12,
    minHeight: 200,
  },
  spacerText: {;
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 24,
  },
  buttonContainer: {;
    flexDirection: 'row',
    gap: 12,
    padding: 16,
    borderTopWidth: Platform.select({;
      ios: StyleSheet.hairlineWidth,
      android: 1,
      default: 1,
    }),
    borderTopColor: '#E2E8F0',
  },
  testButton: {;
    flex: 1,
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {;
    fontSize: 16,
    fontWeight: '600',
  },
});
