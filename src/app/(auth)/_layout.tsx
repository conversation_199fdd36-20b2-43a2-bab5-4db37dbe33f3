import React from 'react';
import { Stack } from 'expo-router';
import { SimpleAuthProvider } from '@context/SimpleAuthContext';

export default function AuthLayout() {
  return (
    <SimpleAuthProvider>
      <Stack screenOptions={{ headerShown: false }}>
        {/* Legacy auth screens (still available) */}
        <Stack.Screen name='index' options={{ headerShown: false }} />
        <Stack.Screen name='login' options={{ headerShown: false }} />
        <Stack.Screen name='register' options={{ headerShown: false }} />
        <Stack.Screen name='forgot-password' options={{ headerShown: false }} />

        {/* NEW: Simplified 3-Step Authentication Flow */}
        <Stack.Screen
          name='quick-register'
          options={{
            headerShown: false,
            title: 'Quick Registration',
            presentation: 'modal',
          }}
        />
        <Stack.Screen
          name='profile-setup'
          options={{
            headerShown: false,
            title: 'Profile Setup',
            presentation: 'card',
          }}
        />
        <Stack.Screen
          name='id-verification'
          options={{
            headerShown: false,
            title: 'ID Verification',
            presentation: 'card',
          }}
        />

        {/* Legacy complex onboarding (DEPRECATED - will be removed) */}
        <Stack.Screen
          name='unified-onboarding'
          options={{
            headerShown: false,
            // Hide from navigation - deprecated
          }}
        />
      </Stack>
    </SimpleAuthProvider>
  );
}
