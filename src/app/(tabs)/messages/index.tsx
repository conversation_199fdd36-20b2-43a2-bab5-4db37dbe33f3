import React, { useEffect, useState } from 'react';
import { Stack, useRouter } from 'expo-router';
import {
  SafeAreaView,
  StyleSheet,
  View,
  FlatList,
  Text,
  ActivityIndicator,
  TouchableOpacity,
  Image,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useMessaging } from '@context/MessagingContext';
import { MessageListItem } from '@components/messaging/MessageListItem';
import { ChatRoom } from '@services/unified/types';
import { unifiedChatService } from '@services/unified/UnifiedChatService';
import { useAuth } from '@context/AuthContext';
import { matchService } from '@services/MatchService';
import { ChevronRight, MessageCircle, Search } from 'lucide-react-native';
import {
  navigateToChat,
  navigateToProfile,
  createChatWithMatchAndNavigate,
} from '@utils/navigationUtils';

/**;
 * Messages tab screen that displays the chat rooms list;
 * Uses our new simplified messaging implementation;
 */;
export default function MessagesScreen() {
  const { rooms, fetchRooms, loading } = useMessaging();
  const router = useRouter();
  const { authState } = useAuth();
  const user = authState?.user;
  const [chatRooms, setChatRooms] = useState<ChatRoom[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [recentMatches, setRecentMatches] = useState<any[]>([]);
  const [loadingMatches, setLoadingMatches] = useState(false);

  // Navigate to new conversation screen;
  const goToNewChat = () => {
    router.push('/chat/new' as any);
  }
  // Navigate to match profile;
  const goToMatchProfile = (matchId: string) => {
    navigateToProfile(matchId, { source: 'message' });
  }
  // Start chat with a recent match;
  const startChatWithRecentMatch = async (matchId: string, name: string) => {
    if (!user?.id) return null;

    try {
      await createChatWithMatchAndNavigate(
        user.id,
        matchId,
        name,
        `Hi ${name}! We matched recently. I'd like to chat about potentially being roommates.`;
      );

      // Refresh the chat rooms list;
      await fetchChatRooms();
    } catch (error) {
      console.error('Error starting chat with match:', error);
    }
  }
  // Fetch rooms when component mounts;
  useEffect(() => {
    fetchRooms();
  }, [fetchRooms]);

  useEffect(() => {
    if (user?.id) {
      fetchChatRooms();
      fetchRecentMatches();
    }
  }, [user?.id]);

  // Fetch recent matches;
  const fetchRecentMatches = async () => {
    if (!user?.id) return null;

    try {
      setLoadingMatches(true);
      // Get recent matches (last 7 days);
      const matches = await matchService.getRecentMatches(user.id, 7);
      setRecentMatches(matches);
    } catch (err) {
      console.error('Error fetching recent matches:', err);
    } finally {
      setLoadingMatches(false);
    }
  }
  const fetchChatRooms = async () => {
    if (!user?.id) return null;

    try {
      setIsLoading(true);
      // Get real chat rooms from the messaging service;
      const rooms = await unifiedChatService.getChatRooms(user.id);

      // Try to import the ChatService to get mock rooms;
      try {
        const { ChatService } = await import('../../../services/standardized/ChatService');
        if (ChatService && typeof ChatService.getMockRoomsForUser === 'function') {
          // Get mock rooms and combine with real rooms;
          const mockRooms = ChatService.getMockRoomsForUser(user.id);
          // Ensure we have compatible types with explicit typing;
          setChatRooms([...rooms, ...mockRooms] as ChatRoom[]);
        } else {
          setChatRooms(rooms);
        }
      } catch (importError) {
        // If we can't import ChatService, just use the real rooms;
        console.warn('Could not import ChatService:', importError);
        setChatRooms(rooms);
      }
    } catch (err) {
      console.error('Error fetching chat rooms:', err);
      setError('Failed to load messages. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }
  const navigateToChatRoom = (roomId: string) => {
    const room = chatRooms.find(r => r.id === roomId);

    // Get recipient info if available;
    const recipientInfo = room?.participants?.[0];
      ? {
          id: room.participants[0].id,
          name: room.participants[0].display_name || 'User',
        }
      : undefined,
    // Use standardized navigation;
    navigateToChat(roomId, recipientInfo);
  }
  // Render empty state when no rooms;
  const renderEmpty = () => (
    <View style={styles.emptyContainer}>
      {loading.rooms ? (
        <ActivityIndicator size='large' color={'#6200ee' /}>
      ) : (,
        <Text style={styles.emptyText}>
          No conversations yet. Start chatting with potential roommates!;
        </Text>
      )}
    </View>
  );

  const renderChatRoom = ({ item }: { item: ChatRoom }) => {
    // Get the other participant's name;
    const otherParticipant = item.participants?.[0];
    const name = otherParticipant?.display_name || 'Unknown User';
    const avatarUrl = otherParticipant?.avatar_url;

    // Check if this is a mock room by ID pattern or custom property;
    const isMockRoom =;
      (typeof item.id === 'string' && item.id.startsWith('mock_')) ||;
      (item as any).is_mock === true;

    // Format last message time;
    let lastMessageTime = '';
    if (item.last_message_at) {
      const messageDate = new Date(item.last_message_at);
      const now = new Date();

      if (messageDate.toDateString() === now.toDateString()) {
        // Today - show time;
        lastMessageTime = messageDate.toLocaleTimeString([], {
          hour: '2-digit',
          minute: '2-digit',
        });
      } else {
        // Not today - show date;
        lastMessageTime = messageDate.toLocaleDateString([], { month: 'short', day: 'numeric' });
      }
    }
    return (
      <TouchableOpacity style={styles.chatRoomItem} onPress={() => navigateToChatRoom(item.id)}>
        <View style={styles.avatarContainer}>
          {avatarUrl ? (
            <Image source={ uri: avatarUrl } style={{styles.avatar} /}>
          ) : (,
            <View style={[styles.avatarPlaceholder, isMockRoom && styles.mockAvatarPlaceholder]}>
              <Text style={styles.avatarText}>{name[0]}</Text>
            </View>
          )}
        </View>
        <View style={styles.chatInfo}>
          <View style={styles.chatHeader}>
            <Text style={styles.chatName}>
              {name}
              {isMockRoom && ' (Temporary)'}
            </Text>
            {lastMessageTime && <Text style={styles.timeText}>{lastMessageTime}</Text>
          </View>
          <View style={styles.messagePreview}>
            <Text
              style={[styles.previewText, isMockRoom && styles.mockPreviewText]}
              numberOfLines={1}
              ellipsizeMode='tail';
            >
              {item.last_message || 'No messages yet'}
            </Text>
            {item.unread_count > 0 && (
              <View style={styles.unreadBadge}>
                <Text style={styles.unreadText}>{item.unread_count}</Text>
              </View>
            )}
          </View>
        </View>
        <ChevronRight size={20} color={'#94A3B8' /}>
      </TouchableOpacity>
    );
  }
  return (
    <>
      <Stack.Screen;
        options={{
          title: 'Messages',
          headerShown: true,
          headerRight: () => (,
            <TouchableOpacity onPress={goToNewChat} style={styles.headerButton}>
              <Ionicons name='chatbubble-ellipses-outline' size={24} color={'#6200ee' /}>
            </TouchableOpacity>
          ),
        }}
      />
      <SafeAreaView style={styles.container}>
        <View style={styles.content}>
          {/* Recent Matches Section */}
          {recentMatches.length > 0 && (
            <View style={styles.matchesSection}>
              <View style={styles.sectionHeader}>
                <Text style={styles.sectionTitle}>Recent Matches</Text>
              </View>
              <ScrollView
                horizontal;
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={styles.matchesScrollContent}
              >
                {recentMatches.map(match => (
                  <TouchableOpacity
                    key={match.id}
                    style={styles.matchItem}
                    onPress={() => goToMatchProfile(match.id)}
                  >
                    {/* Match Avatar */}
                    <View style={styles.matchAvatarContainer}>
                      <Image
                        source={ uri: match.avatar_url || 'https://via.placeholder.com/150' }
                        style={styles.matchAvatar}
                      />
                      <View style={styles.matchBadge}>
                        <Text style={styles.matchPercentage}>{match.compatibility || 85}%</Text>
                      </View>
                    </View>
                    {/* Match Name */}
                    <Text style={styles.matchName} numberOfLines={1}>
                      {match.first_name || 'User'}
                    </Text>
                    {/* Start Chat Button */}
                    <TouchableOpacity
                      style={styles.matchMessageButton}
                      onPress={() => startChatWithRecentMatch(match.id, match.first_name || 'User')}
                    >
                      <MessageCircle size={14} color={'#FFFFFF' /}>
                      <Text style={styles.matchMessageText}>Message</Text>
                    </TouchableOpacity>
                  </TouchableOpacity>
                ))}
                {/* View All Button */}
                <TouchableOpacity
                  style={styles.viewAllMatches}
                  onPress={() => router.push('/saved-matches' as any)}
                >
                  <Text style={styles.viewAllText}>View All</Text>
                  <ChevronRight size={16} color={'#6366F1' /}>
                </TouchableOpacity>
              </ScrollView>
            </View>
          )}
          {isLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size='large' color={'#6366F1' /}>
              <Text style={styles.loadingText}>Loading messages...</Text>
            </View>
          ) : error ? (,
            <View style={styles.errorContainer}>
              <Text style={styles.errorText}>{error}</Text>
              <TouchableOpacity style={styles.retryButton} onPress={fetchChatRooms}>
                <Text style={styles.retryText}>Retry</Text>
              </TouchableOpacity>
            </View>
          ) : chatRooms.length === 0 ? (,
            <View style={styles.emptyContainer}>
              <MessageCircle size={48} color={'#94A3B8' /}>
              <Text style={styles.emptyTitle}>No messages yet</Text>
              <Text style={styles.emptyText}>
                When you match with someone, you'll be able to chat with them here.;
              </Text>
            </View>
          ) : (,
            <FlatList
              data={chatRooms}
              keyExtractor={item => item.id}
              renderItem={renderChatRoom}
              contentContainerStyle={styles.listContent}
              showsVerticalScrollIndicator={false}
              onRefresh={fetchChatRooms}
              refreshing={isLoading}
            />
          )}
        </View>
      </SafeAreaView>
    </>
  );
}
const styles = StyleSheet.create({
  // New styles for matches section;
  matchesSection: {
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  matchesScrollContent: {
    paddingLeft: 16,
    paddingRight: 8,
  },
  matchItem: {
    width: 110,
    marginRight: 12,
    alignItems: 'center',
  },
  matchAvatarContainer: {
    position: 'relative',
    marginBottom: 8,
  },
  matchAvatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 2,
    borderColor: '#6366F1',
  },
  matchBadge: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: '#10B981',
    borderRadius: 10,
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  matchPercentage: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
  },
  matchName: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1F2937',
    marginBottom: 6,
  },
  matchMessageButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#6366F1',
    borderRadius: 16,
    paddingVertical: 6,
    paddingHorizontal: 12,
    marginTop: 4,
  },
  matchMessageText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  viewAllMatches: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: 90,
    alignSelf: 'center',
    padding: 8,
    marginTop: 24,
  },
  viewAllText: {
    color: '#6366F1',
    fontSize: 14,
    fontWeight: '500',
    marginRight: 4,
  },
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  content: {
    flex: 1,
    width: '100%',
    overflow: 'hidden',
  },
  listContent: {
    flexGrow: 1,
    paddingBottom: 20,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
    marginTop: 40,
  },
  emptyText: {
    fontSize: 16,
    color: '#757575',
    textAlign: 'center',
    marginTop: 12,
  },
  headerButton: {
    padding: 8,
    marginRight: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#64748B',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#EF4444',
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    backgroundColor: '#6366F1',
    borderRadius: 8,
  },
  retryText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1E293B',
    marginTop: 16,
    marginBottom: 8,
  },
  chatRoomItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  },
  avatarContainer: {
    marginRight: 12,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  avatarPlaceholder: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#E2E8F0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  mockAvatarPlaceholder: {
    backgroundColor: '#FEF3C7',
    borderWidth: 1,
    borderColor: '#F59E0B',
    borderStyle: 'dashed',
  },
  avatarText: {
    fontSize: 20,
    fontWeight: '600',
    color: '#64748B',
  },
  chatInfo: {
    flex: 1,
    marginRight: 8,
  },
  chatHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  chatName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1E293B',
  },
  timeText: {
    fontSize: 12,
    color: '#94A3B8',
  },
  messagePreview: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  previewText: {
    fontSize: 14,
    color: '#64748B',
    flex: 1,
  },
  mockPreviewText: {
    color: '#B45309',
    fontStyle: 'italic',
  },
  unreadBadge: {
    backgroundColor: '#6366F1',
    width: 22,
    height: 22,
    borderRadius: 11,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  unreadText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});
