/**;
 * New chat creation screen;
 * This screen allows users to start a new conversation;
 */;

import React from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, Image, TextInput } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { ArrowLeft, Search, UserPlus } from 'lucide-react-native';

export default function NewChatRoute() {
  const router = useRouter();

  // Mock data for potential contacts;
  const potentialContacts = [;
    {;
      id: 'contact-1',
      name: '<PERSON>',
      avatarUrl: 'https://i.pravatar.cc/150?img=1',
      matchScore: 95,
    },
    {;
      id: 'contact-2',
      name: '<PERSON>',
      avatarUrl: 'https://i.pravatar.cc/150?img=5',
      matchScore: 92,
    },
    {;
      id: 'contact-3',
      name: '<PERSON>',
      avatarUrl: 'https://i.pravatar.cc/150?img=8',
      matchScore: 88,
    },
    {;
      id: 'contact-4',
      name: '<PERSON>',
      avatarUrl: 'https://i.pravatar.cc/150?img=9',
      matchScore: 85,
    },
    {;
      id: 'contact-5',
      name: 'Alex Brown',
      avatarUrl: 'https://i.pravatar.cc/150?img=11',
      matchScore: 82,
    },
  ];

  const handleStartChat = (contactId: string, contactName: string) => {;
    try {
      // Mock creating a chat room;
      const roomId = `room-${contactId}-${Date.now()}`;

      // Navigate to the chat room using direct navigation to prevent [object Object] issues;
      const queryParams = new URLSearchParams();
      queryParams.set('roomId', roomId);
      queryParams.set('recipientName', contactName);

      router.push(`/chat?${queryParams.toString()}`);
    } catch (error) {
      console.error('Error starting chat:', error);
    };
  };

  return (
    <SafeAreaView style={styles.container} edges={{['top']}}>;
      <Stack.Screen;
        options={{;
          headerShown: false,
        }};
      />;

      <View style={styles.header}>;
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>;
          <ArrowLeft size={24} color={'#1E293B' /}>;
        </TouchableOpacity>;
        <Text style={styles.headerTitle}>New Conversation</Text>;
        <View style={{ width: 40 } /}>;
      </View>;

      <View style={styles.searchContainer}>;
        <View style={styles.searchBar}>;
          <Search size={20} color={'#64748B' /}>;
          <TextInput
            style={styles.searchInput};
            placeholder='Search connections...';
            placeholderTextColor='#64748B';
          />;
        </View>;
      </View>;

      <FlatList
        data={potentialContacts};
        keyExtractor={item => item.id};
        contentContainerStyle={styles.listContent};
        renderItem={({ item }) => (;
          <TouchableOpacity
            style={styles.contactItem};
            onPress={() => handleStartChat(item.id, item.name)};
          >;
            <View style={styles.avatarContainer}>;
              {item.avatarUrl ? (;
                <Image source={{ uri: item.avatarUrl }} style={{styles.avatar} /}>;
              ) : (;
                <View style={[styles.avatar, styles.placeholderAvatar]}>;
                  <UserPlus size={24} color={'#94A3B8' /}>;
                </View>;
              )};
            </View>;

            <View style={styles.contactInfo}>;
              <Text style={styles.contactName}>{item.name}</Text>;
              <Text style={styles.matchScore}>{item.matchScore}% match</Text>;
            </View>;

            <TouchableOpacity
              style={styles.chatButton};
              onPress={() => handleStartChat(item.id, item.name)};
            >;
              <Text style={styles.chatButtonText}>Chat</Text>;
            </TouchableOpacity>;
          </TouchableOpacity>;
        )};
      />;
    </SafeAreaView>;
  );
};

const styles = StyleSheet.create({;
  container: {;
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {;
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
    backgroundColor: '#FFFFFF',
  },
  backButton: {;
    padding: 8,
  },
  headerTitle: {;
    fontSize: 18,
    fontWeight: '600',
    color: '#1E293B',
  },
  searchContainer: {;
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  },
  searchBar: {;
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F1F5F9',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
  },
  searchInput: {;
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
    color: '#1E293B',
    padding: 0,
  },
  listContent: {;
    padding: 16,
  },
  contactItem: {;
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  avatarContainer: {;
    marginRight: 12,
  },
  avatar: {;
    width: 48,
    height: 48,
    borderRadius: 24,
  },
  placeholderAvatar: {;
    backgroundColor: '#F1F5F9',
    alignItems: 'center',
    justifyContent: 'center',
  },
  contactInfo: {;
    flex: 1,
  },
  contactName: {;
    fontSize: 16,
    fontWeight: '600',
    color: '#1E293B',
    marginBottom: 4,
  },
  matchScore: {;
    fontSize: 14,
    color: '#10B981',
    fontWeight: '500',
  },
  chatButton: {;
    backgroundColor: '#6366F1',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  chatButtonText: {;
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
});
