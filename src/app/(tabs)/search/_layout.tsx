import React from 'react';,
  import {
   Stack ,
  } from 'expo-router';

export default function SearchLayout() {,
  return (
    <Stack screenOptions= {{  headerShown: false    }}>,
  <Stack.Screen name='index' />
      <Stack.Screen;,
  name='profile', ,
  options= {{  presentation: 'card',
          headerShown: true,
          title: 'Profile Details',
          animation: 'slide_from_right'  }},
  />
    </Stack>,
  )
}