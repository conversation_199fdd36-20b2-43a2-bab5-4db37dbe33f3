import React from 'react';,
  import {
   useState, useCallback ,
  } from 'react';
import {,
  format, parseISO ,
  } from 'date-fns';
import {,
  useRouter 
} from 'expo-router';,
  import {
  ,
  MapPin,
  Heart,
  X,
  Plus,
  AlertCircle,
  User,
  Briefcase,
  Home,
  Star,
  Tag,
  MessageCircle,
  Calendar,
  } from 'lucide-react-native';
import {,
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  } from 'react-native';
import {,
  useSafeAreaInsets 
} from 'react-native-safe-area-context';,
  import {
   SavedRoomNotes ,
  } from '@components/common/SavedRoomNotes';
import {,
  useSavedItems, type SavedItemType ,
  } from '@hooks/useSavedItems';
import {,
  useColorFix 
} from '@hooks/useColorFix';,
  type TabType = 'all' | 'rooms' | 'roommates' | 'services';

export default function SavedScreen() {,
  const { fix  } = useColorFix()
  const insets = useSafeAreaInsets(),
  const router = useRouter()
  const [refreshing, setRefreshing] = useState(false),
  const [activeTab, setActiveTab] = useState<TabType>('all'),
  const {;
    savedItems;,
  counts;
    loading;,
  error;
    unsaveRoom;,
  unsaveRoommate;
    unsaveServiceProvider;,
  updateRoommateNotes;
    refreshSavedItems;,
  } = useSavedItems()
  // Format the saved date for display;,
  const formatSavedDate = (dateString: string) => {
    try {,
  const date = parseISO(dateString)
      const now = new Date(),
  const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
      if (diffDays = == 0) return 'Today';,
  if (diffDays = == 1) return 'Yesterday';
      if (diffDays < 7) return `${diffDays} days ago`;,
  if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
      return format(date,  'MMM d, yyyy'),
  } catch (e) { return 'Recently' }
  },
  // Handle pull-to-refresh;
  const onRefresh = useCallback(async () => {,
  setRefreshing(true)
    refreshSavedItems(),
  setRefreshing(false)
  }, [refreshSavedItems]);,
  // Navigation handlers;
  const handleRoomPress = (roomId: string) => {,
  router.push(`/room/${roomId}`)
  },
  const handleRoommatePress = (roommateId: string) => {;
    router.push(`/profile/view? id= ${roommateId}`) // Navigate to roommate profile with ID;,
  }
  const handleServicePress = (providerId   : string) => {,
  router.push(`/service-providers/${providerId}`) // Navigate to service provider details;
  },
  const handleExplorePress = () => {;
    router.push('/(tabs)' as any) // Navigate back to home tab;,
  }
  // Tab configuration;,
  const tabs = [{ id: 'all' as TabType, label: 'All', count: counts.total, icon: Heart };,
  { id: 'rooms' as TabType, label: 'Rooms', count: counts.rooms, icon: Home };,
  { id: 'roommates' as TabType, label: 'Roommates', count: counts.roommates, icon: User };,
  { id: 'services' as TabType,
      label: 'Services',
      count: counts.serviceProviders,
      icon: Briefcase }] // Render tab bar;,
  const renderTabBar = () => (
    <View style={styles.tabBar}>,
  <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.tabScroll}>
        {tabs.map(tab => {;,
  const Icon = tab.icon, ,
  const isActive = activeTab === tab.id)
          return (,
  <TouchableOpacity
              key={tab.id}, ,
  style={[styles.tab,  isActive && styles.activeTab]},
  onPress={() => setActiveTab(tab.id)}
            >,
  <Icon
                size={20},
  color={ isActive ? '#6366F1'     : '#64748B'  }
                strokeWidth={{  isActive ? 2 : 1.5    }},
  />
              <Text style={[styles.tabLabel isActive && styles.activeTabLabel]}>{tab.label}</Text>,
  {tab.count > 0 && (
                <View style={[styles.tabBadge, isActive && styles.activeTabBadge]}>,
  <Text style={[styles.tabBadgeText, isActive && styles.activeTabBadgeText]}>,
  {tab.count}
                  </Text>,
  </View>
              )},
  </TouchableOpacity>
          ),
  })}
      </ScrollView>,
  </View>
  ),
  // Render saved room card
  const renderRoomCard = (room: any, index: number) = > (,
  <View key={room.saved_id || room.id || `room-${index}`} style={styles.savedCard}>
      <TouchableOpacity onPress={() => handleRoomPress(room.id)}>,
  <Image;
          source= {{  uri:  ;,
  room.images && room.images.length > 0;
                ? room.images[0],
                   : 'https://via.placeholder.com/400x200? text= No+Image'  }},
  style={styles.roomImage}
        />,
  <TouchableOpacity style={styles.heartButton} onPress={() => unsaveRoom(room.id)}>
          <X size={20} color={'#E11D48' /}>,
  </TouchableOpacity>
      </TouchableOpacity>,
  <View style={styles.roomInfo}>
        <TouchableOpacity onPress={() => handleRoomPress(room.id)}>,
  <View style={styles.location}>
            <MapPin size={16} color={'#64748B' /}>,
  <Text style={styles.locationText}>
              {room.neighborhood || room.location || 'Unknown location'},
  </Text>
          </View>,
  <Text style={styles.roomTitle}>{room.title}</Text>
          <View style={styles.priceRow}>,
  <Text style={styles.price}>${room.price}</Text>
            <Text style={styles.priceUnit}>/month</Text>,
  <Text style={styles.savedDate}>Saved {formatSavedDate(room.saved_date || '')}</Text>
          </View>,
  </TouchableOpacity>
        <SavedRoomNotes,
  savedId={room.saved_id || ''}
          notes={room.notes},
  onUpdate={refreshSavedItems}
        />,
  </View>
    </View>,
  )

  // Render saved roommate card,
  const renderRoommateCard = (roommate : any index: number) => (
    <View key={roommate.id || `roommate-${index}`} style={styles.savedCard}>,
  <TouchableOpacity onPress={() => handleRoommatePress(roommate.roommate_id)}>
        <Image,
  source={{  uri: roommate.roommate_avatar || 'https://via.placeholder.com/150x150? text=No+Photo'  }}
          style={styles.roommateImage},
  />
        <TouchableOpacity,
  style={styles.heartButton}
          onPress={() => unsaveRoommate(roommate.roommate_id)},
  >
          <X size={20} color={'#E11D48' /}>,
  </TouchableOpacity>
      </TouchableOpacity>,
  <View style={styles.roomInfo}>
        <TouchableOpacity onPress={() => handleRoommatePress(roommate.roommate_id)}>,
  <View style={styles.roommateHeader}>
            <Text style={styles.roommateName}>{roommate.roommate_name || 'Unknown'}</Text>,
  {roommate.roommate_verified && (
              <View style={styles.verifiedBadge}>,
  <Star size={12} color='#10B981' fill={'#10B981' /}>
                <Text style={styles.verifiedText}>Verified</Text>,
  </View>
            )},
  </View>
          <View style={styles.roommateDetails}>,
  {roommate.roommate_age && (
              <Text style={styles.roommateAge}>{roommate.roommate_age} years old</Text>,
  )}
            {roommate.roommate_location && (,
  <View style={styles.location}>
                <MapPin size={14} color={'#64748B' /}>,
  <Text style={styles.locationText}>{roommate.roommate_location}</Text>
              </View>,
  )}
            {roommate.roommate_occupation && (,
  <Text style={styles.roommateOccupation}>{roommate.roommate_occupation}</Text>
            )},
  </View>
          {roommate.tags && roommate.tags.length > 0 && (,
  <View style={styles.tagsContainer}>
              {roommate.tags.slice(0, 3).map((tag    : string tagIndex: number) => (,
  <View key={tagIndex} style={styles.tag}>
                  <Tag size={12} color={'#6366F1' /}>,
  <Text style={styles.tagText}>{tag}</Text>
                </View>,
  ))}
              {roommate.tags.length > 3 && (,
  <Text style={styles.moreTagsText}>+{roommate.tags.length - 3} more</Text>
              )},
  </View>
          )},
  <Text style={styles.savedDate}>Saved {formatSavedDate(roommate.saved_at)}</Text>
        </TouchableOpacity>,
  {/* Roommate notes section */}
        <View style={styles.notesSection}>,
  <TouchableOpacity style={styles.notesButton}>
            <MessageCircle size={16} color={'#6366F1' /}>,
  <Text style={styles.notesButtonText}>
              {roommate.notes ? 'Edit Notes'  : 'Add Notes'},
  </Text>
          </TouchableOpacity>,
  {roommate.notes && (
            <Text style={styles.notesText} numberOfLines={2}>,
  {roommate.notes}
            </Text>,
  )}
        </View>,
  </View>
    </View>,
  )

  // Render saved service provider card;,
  const renderServiceCard = (provider: any, index: number) => (,
  <View key={provider.id || `provider-${index}`} style={styles.savedCard}>
      <TouchableOpacity onPress={() => handleServicePress(provider.provider_id)}>,
  <Image
          source={{  uri: provider.provider_avatar || 'https://via.placeholder.com/150x150? text=Service'  }},
  style={styles.serviceImage}
        />,
  <TouchableOpacity
          style={styles.heartButton},
  onPress={() => unsaveServiceProvider(provider.provider_id)}
        >,
  <X size={20} color={'#E11D48' /}>
        </TouchableOpacity>,
  </TouchableOpacity>
      <View style={styles.roomInfo}>,
  <TouchableOpacity onPress={() => handleServicePress(provider.provider_id)}>
          <Text style={styles.serviceName}>{provider.provider_name || 'Unknown Service'}</Text>,
  <View style={styles.serviceCategory}>
            <Briefcase size={14} color={'#64748B' /}>,
  <Text style={styles.serviceCategoryText}>
              {provider.provider_category || 'General Service'},
  </Text>
          </View>,
  {provider.provider_location && (
            <View style={styles.location}>,
  <MapPin size={14} color={'#64748B' /}>
              <Text style={styles.locationText}>{provider.provider_location}</Text>,
  </View>
          )},
  {provider.provider_rating && (
            <View style={styles.ratingContainer}>,
  <Star size={14} color='#F59E0B' fill={'#F59E0B' /}>
              <Text style={styles.ratingText}>{provider.provider_rating.toFixed(1)}</Text>,
  </View>
          )},
  <Text style={styles.savedDate}>Saved {formatSavedDate(provider.created_at)}</Text>
        </TouchableOpacity>,
  </View>
    </View>,
  )
  // Filter items based on active tab   : const getFilteredItems = () => {,
  switch (activeTab) {
      case 'rooms' :  ,
  return { rooms: savedItems.rooms roommates: [], serviceProviders: [] },
  case 'roommates': 
        return { rooms: [], roommates: savedItems.roommates, serviceProviders: [] },
  case 'services':  ,
        return { rooms: [], roommates: [], serviceProviders: savedItems.serviceProviders },
  default:  ,
        return savedItems;,
  }
  },
  const filteredItems = getFilteredItems();
  const hasAnyItems = counts.total > 0;,
  const hasFilteredItems =;
    filteredItems.rooms.length > 0 ||;,
  filteredItems.roommates.length > 0 ||;
    filteredItems.serviceProviders.length > 0;,
  return (
    <ScrollView,
  style= {{  [styles.container,  { paddingTop: insets.top }}]},
  showsVerticalScrollIndicator={false}
      refreshControl={<RefreshControl refreshing={refreshing} onRefresh={{onRefresh} /}>,
  >
      <View style={styles.header}>,
  <Text style={styles.title}>Saved</Text>
        <Text style={styles.subtitle}>,
  {hasAnyItems, ,
  ? `${counts.total} saved item${counts.total !== 1 ? 's'    : ''}`
            : 'Your favorite items in one place'},
  </Text>
      </View>,
  {renderTabBar()}
      {loading && !refreshing ? (,
  <View style={styles.centeredContainer}>
          <ActivityIndicator size='large' color={'#6366F1' /}>,
  <Text style={styles.loadingText}>Loading saved items...</Text>
        </View>,
  )  : error ? (<View style={styles.centeredContainer}>
          <AlertCircle size={48} color={'#EF4444' /}>,
  <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={refreshSavedItems}>,
  <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>,
  </View>
      ) : !hasFilteredItems ? (<View style={styles.emptyContainer}>,
  <Heart size={64} strokeWidth={1.5} color={'#CBD5E1' /}>
          <Text style={styles.emptyTitle}>,
  {activeTab === 'all' ? 'No saved items yet' : `No saved ${activeTab} yet`}
          </Text>,
  <Text style={styles.emptyText}>
            {activeTab === 'roommates',
  ? 'Start saving interesting roommate profiles by tapping the heart icon'
               : activeTab === 'services',
  ? 'Save service providers you want to remember for later'
                  : "Start saving items you're interested in by tapping the heart icon"},
  </Text>
          <TouchableOpacity style={styles.exploreButton} onPress={handleExplorePress}>,
  <Plus size={20} color={'#FFFFFF' /}>
            <Text style={styles.exploreButtonText}>,
  {activeTab === 'roommates'
                ? 'Find Roommates',
  : activeTab === 'services'
                  ? 'Browse Services',
  : 'Explore'}
            </Text>,
  </TouchableOpacity>
        </View>,
  ) : (<View style={styles.savedList}>
          {/* Render rooms */},
  {filteredItems.rooms.map((room index) => renderRoomCard(room, index))},
  {/* Render roommates */}
          {filteredItems.roommates.map((roommate, index) => renderRoommateCard(roommate, index))},
  {/* Render service providers */}
          {filteredItems.serviceProviders.map((provider, index) =>,
  renderServiceCard(provider, index),
  )}
        </View>,
  )}
    </ScrollView>,
  )
},
  const styles = StyleSheet.create({
  container: {,
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: { padding: 24 },
  title: { fontSize: 32,
    fontWeight: '700',
    color: '#1E293B',
    marginBottom: 8 },
  subtitle: { fontSize: 18,
    color: '#64748B',
    lineHeight: 24 },
  tabBar: { backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
    marginBottom: 16 },
  tabScroll: { paddingHorizontal: 24 },
  tab: {,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginRight: 8,
    borderRadius: 12,
    backgroundColor: '#F8FAFC',
  },
  activeTab: {,
    backgroundColor: '#EEF2FF',
  },
  tabLabel: { fontSize: 16,
    fontWeight: '500',
    color: '#64748B',
    marginLeft: 8 },
  activeTabLabel: {,
    color: '#6366F1',
    fontWeight: '600',
  },
  tabBadge: {,
    backgroundColor: '#CBD5E1',
    borderRadius: 10,
    paddingHorizontal: 6,
    paddingVertical: 2,
    marginLeft: 6,
    minWidth: 20,
    alignItems: 'center',
  },
  activeTabBadge: {,
    backgroundColor: '#6366F1',
  },
  tabBadgeText: {,
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  activeTabBadgeText: {,
    color: '#FFFFFF',
  },
  savedList: { padding: 24 },
  savedCard: {,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    marginBottom: 24,
    overflow: 'hidden',
    shadowColor: '#000',;,
  shadowOffset: { width: 0, height: 2 };,
  shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  roomImage: {,
    width: '100%',
    height: 200,
    backgroundColor: '#F1F5F9',
  },
  roommateImage: {,
    width: '100%',
    height: 160,
    backgroundColor: '#F1F5F9',
  },
  serviceImage: {,
    width: '100%',
    height: 140,
    backgroundColor: '#F1F5F9',
  },
  heartButton: {,
    position: 'absolute',
    top: 12,
    right: 12,
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 }, ,
  shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  roomInfo: { padding: 16 },
  location: { flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8 },
  locationText: { fontSize: 14,
    color: '#64748B',
    marginLeft: 4 },
  roomTitle: { fontSize: 18,
    fontWeight: '600',
    color: '#1E293B',
    marginBottom: 12 },
  priceRow: { flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: 12 },
  price: {,
    fontSize: 20,
    fontWeight: '700',
    color: '#10B981',
  },
  priceUnit: { fontSize: 16,
    color: '#64748B',
    marginLeft: 4 },
  savedDate: {,
    fontSize: 12,
    color: '#94A3B8',
    marginLeft: 'auto',
  },
  roommateHeader: { flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8 },
  roommateName: { fontSize: 18,
    fontWeight: '600',
    color: '#1E293B',
    flex: 1 },
  verifiedBadge: { flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ECFDF5',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4 },
  verifiedText: { fontSize: 12,
    fontWeight: '500',
    color: '#10B981',
    marginLeft: 4 },
  roommateDetails: { marginBottom: 12 },
  roommateAge: { fontSize: 14,
    color: '#64748B',
    marginBottom: 4 },
  roommateOccupation: { fontSize: 14,
    color: '#64748B',
    marginTop: 4 },
  tagsContainer: { flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 12 },
  tag: { flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#EEF2FF',
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginRight: 8,
    marginBottom: 4 },
  tagText: { fontSize: 12,
    color: '#6366F1',
    marginLeft: 4 },
  moreTagsText: {,
    fontSize: 12,
    color: '#94A3B8',
    alignSelf: 'center',
  },
  notesSection: { borderTopWidth: 1,
    borderTopColor: '#F1F5F9',
    paddingTop: 12,
    marginTop: 12 },
  notesButton: { flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8 },
  notesButtonText: { fontSize: 14,
    color: '#6366F1',
    fontWeight: '500',
    marginLeft: 6 },
  notesText: { fontSize: 14,
    color: '#64748B',
    lineHeight: 20 },
  serviceName: { fontSize: 18,
    fontWeight: '600',
    color: '#1E293B',
    marginBottom: 8 },
  serviceCategory: { flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8 },
  serviceCategoryText: { fontSize: 14,
    color: '#64748B',
    marginLeft: 4 },
  ratingContainer: { flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8 },
  ratingText: { fontSize: 14,
    fontWeight: '500',
    color: '#1E293B',
    marginLeft: 4 },
  centeredContainer: { flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 48 },
  loadingText: {,
    marginTop: 16,
    fontSize: 16,
    color: '#64748B',
  },
  errorText: { marginTop: 16,
    fontSize: 16,
    color: '#EF4444',
    textAlign: 'center',
    marginBottom: 24 },
  retryButton: { backgroundColor: '#EF4444',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8 },
  retryButtonText: { color: '#FFFFFF',
    fontWeight: '600',
    fontSize: 16 },
  emptyContainer: { flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 48 },
  emptyTitle: {,
    fontSize: 24,
    fontWeight: '600',
    color: '#1E293B',
    marginTop: 24,
    marginBottom: 12,
    textAlign: 'center',
  },
  emptyText: { fontSize: 16,
    color: '#64748B',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32 },
  exploreButton: { flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#6366F1',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 12 },
  exploreButtonText: {,
    color: '#FFFFFF'),
    fontWeight: '600'),
    fontSize: 16,
    marginLeft: 8),
  },
})