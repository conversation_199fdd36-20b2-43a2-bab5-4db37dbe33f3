import { Paintbrush as Paint<PERSON>2Raw, Wrench as Wrench<PERSON>conRaw, Truck as TruckRaw, Wrench as <PERSON>ch<PERSON>aw, Zap as ZapRaw, Hammer as HammerIconRaw, Bell as BellRaw, Search as SearchRaw, Filter as FilterRaw, Bookmark as BookmarkRaw, Heart, CheckCircle as CheckCircleIconRaw } from 'lucide-react-native';
import { Icons } from '@components/common/Icon';
import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator, TextInput, RefreshControl, Image, FlatList } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { Stack } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { ArrowRight, ArrowUpRight, ShieldCheck, Briefcase } from 'lucide-react-native';

// Custom hooks;
import { useServiceProviders } from '@hooks/useServiceProviders';
import { useTheme, colorWithOpacity, type Theme } from '@design-system';
import { useAuth } from '@context/AuthContext';

// Custom components;
import ServiceCategoryCard from '@components/services/ServiceCategoryCard';
import ServiceProviderCard from '@components/services/ServiceProviderCard';
import { ServiceCategory } from '@services';

// Create validated versions of all used icons;
const Paintbrush2 = Icons.Paintbrush;
const WrenchIcon = Icons.Wrench;
const Truck = Icons.Truck;
const Wrench = Icons.Wrench;
const Zap = Icons.Zap;
const HammerIcon = Icons.Hammer;
const Bell = Icons.Bell;
const Search = Icons.Search;
const Filter = Icons.Filter;
const Bookmark = Icons.Bookmark;
const SavedIcon = Icons.Heart;
const BriefcaseIcon = Icons.Briefcase;
const ArrowRightIcon = Icons.ArrowRight;
const CheckCircleIcon = Icons.CheckCircle;

// Default categories to use when database isn't available yet;
const getDefaultCategories = ($2) => [
  {
    id: '1',
    name: 'Cleaning',
    description: 'Professional cleaning services for your home',
    icon: 'Paintbrush2',
    color: theme.colors.primary,
    services_count: 0,
  },
  {
    id: '2',
    name: 'Maintenance',
    description: 'General maintenance and repairs',
    icon: 'WrenchIcon',
    color: theme.colors.secondary,
    services_count: 0,
  },
  {
    id: '3',
    name: 'Moving',
    description: 'Moving and relocation assistance',
    icon: 'Truck',
    color: theme.colors.success,
    services_count: 0,
  },
  {
    id: '4',
    name: 'Plumbing',
    description: 'Plumbing repairs and installation',
    icon: 'Wrench',
    color: theme.colors.warning,
    services_count: 0,
  },
  {
    id: '5',
    name: 'Electrical',
    description: 'Electrical repairs and upgrades',
    icon: 'Zap',
    color: theme.colors.info,
    services_count: 0,
  },
  {
    id: '6',
    name: 'Renovation',
    description: 'Home renovation and remodeling',
    icon: 'HammerIcon',
    color: theme.colors.primaryVariant,
    services_count: 0,
  },
];

// Safe color utility to prevent [object Object] issues;
const safeColor = ($2) => {
  if (typeof color === 'string') return color;
  if (typeof color === 'object' && color !== null) {
    console.warn('Color object detected in services screen, converting to fallback:', color);
    return '#000000'; // Fallback to black;
  }
  return String(color);
}
// Safe colorWithOpacity wrapper;
const safeColorWithOpacity = ($2) => {
  const safeColorValue = safeColor(color);
  try {
    return colorWithOpacity(safeColorValue, opacity);
  } catch (error) {
    console.warn('colorWithOpacity failed in services screen, using fallback:', error);
    return `rgba(0, 0, 0, ${opacity})`; // Fallback;
  }
}
export default function ServicesScreen() {
  const insets = useSafeAreaInsets();
  const router = useRouter();
  const theme = useTheme();
  const { authState } = useAuth();
  const styles = createStyles(theme);
  const [searchQuery, setSearchQuery] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [hasProviderProfile, setHasProviderProfile] = useState<boolean | null>(null);
  const [checkingProviderStatus, setCheckingProviderStatus] = useState(true);

  const { providers, categories, isLoading, error, fetchProviders, fetchCategories } =;
    useServiceProviders();

  // Check if current user has a provider profile;
  useEffect(() => {
  checkProviderStatus();
  }, []);

  const checkProviderStatus = async () => {
  try {
      setCheckingProviderStatus(true);
      console.log('🔍 Checking user provider status...');
      ;
      // Get current user from auth context;
      const user = authState?.user;
      ;
      if (!user?.id) {
        console.log('❌ No authenticated user found');
        setHasProviderProfile(false);
        return null;
      }
      // Check if user has a provider profile;
      const { getServiceProviderByUserId } = require('@services');
      const providerData = await getServiceProviderByUserId(user.id);
      ;
      const hasProfile = !!providerData;
      console.log('📋 Provider status check result:', {
        userId: user.id,
        hasProfile,
        providerId: providerData?.id,
      });
      ;
      setHasProviderProfile(hasProfile);
    } catch (error) {
      console.error('❌ Error checking provider status:', error);
      setHasProviderProfile(false);
    } finally {
      setCheckingProviderStatus(false);
    }
  }
  // Top 5 featured service providers;
  const featuredProviders = providers.filter(p => p.is_verified && (p.rating_average || 0) >= 4);
    .sort((a, b) => (b.rating_average || 0) - (a.rating_average || 0));
    .slice(0, 5);

  // Use default categories if none are available from the database;
  const displayCategories = categories.length > 0 ? categories : getDefaultCategories(theme),
  const handleSearch = () => {
  if (searchQuery.trim()) {
      fetchProviders({ keyword: searchQuery.trim() });
    }
  }
  const goToAdvancedSearch = () => {
  router.push(`/unified-search?searchType=service&timestamp=${Date.now()}`);
  }
  const goToServiceFilter = () => {
  router.push('/service-filter' as any);
  }
  const handleRefresh = async () => {
  setRefreshing(true);
    await Promise.all([fetchProviders(), fetchCategories(), checkProviderStatus()]);
    setRefreshing(false);
  }
  const goToSavedProviders = () => {
  router.push('/(tabs)/saved' as any);
  }
  const navigateToCategory = (category) => {
    console.log('Navigating to category:', category.name);
    router.push({
      pathname: '/service-providers',
      params: { category: category.name },
    });
  }
  const navigateToProviderDashboard = () => {
  if (hasProviderProfile) {
      console.log('✅ User has provider profile, navigating to dashboard...');
      router.push('/provider/dashboard' as any);
    } else {
      console.log('❌ User has no provider profile, redirecting to onboarding...');
      navigateToProviderProfile();
    }
  }
  const navigateToProviderProfile = () => {
  console.log('🚀 Navigating to provider onboarding...');
    router.push('/provider/onboarding' as any);
  }
  const renderProviderPortalSection = () => {
  if (checkingProviderStatus) {
      return (
    <View style={styles.section}>
          <Text style={{[styles.sectionTitle, { color: safeColor(theme.colors.text) }]}}>Provider Portal</Text>
          <View style={{[styles.providerBanner, { backgroundColor: safeColor(theme.colors.surface) }]}}>
            <View style={styles.bannerContent}>
              <ActivityIndicator size="small" color={{safeColor(theme.colors.primary)} /}>
              <Text style={{[styles.bannerDescription, { color: safeColor(theme.colors.textSecondary), marginLeft: 12 }]}}>
                Checking your provider status...;
              </Text>
            </View>
          </View>
        </View>
      );
    }
    if (hasProviderProfile) {
      // User has a provider profile - show dashboard access;
      return (
    <View style={styles.section}>
          <Text style={{[styles.sectionTitle, { color: safeColor(theme.colors.text) }]}}>Provider Portal</Text>
          <View style={{[styles.providerBanner, { backgroundColor: safeColorWithOpacity(safeColor(theme.colors.success), 0.08) }]}}>
            <View style={styles.bannerContent}>
              <View style={{[styles.bannerIcon, { backgroundColor: safeColor(theme.colors.success) }]}}>
                <CheckCircleIcon size={24} color={{safeColor(theme.colors.white)} /}>
              </View>
              <View style={styles.bannerText}>
                <Text style={{[styles.bannerTitle, { color: safeColor(theme.colors.text) }]}}>
                  Provider Dashboard;
                </Text>
                <Text
                  style={[styles.bannerDescription, { color: safeColor(theme.colors.textSecondary) }]}
                  numberOfLines={2}
                >
                  Manage your services, bookings, and business performance;
                </Text>
              </View>
            </View>
          </View>
          <TouchableOpacity
            style={[styles.dashboardButton, { backgroundColor: safeColor(theme.colors.success) }]}
            onPress={navigateToProviderDashboard}
          >
            <Text style={{[styles.dashboardButtonText, { color: safeColor(theme.colors.white) }]}}>
              Open Provider Dashboard;
            </Text>
            <ArrowRightIcon size={18} color={{safeColor(theme.colors.white)} /}>
          </TouchableOpacity>
        </View>
      );
    } else {
      // User doesn't have a provider profile - show onboarding;
      return (
    <View style={styles.section}>
          <Text style={{[styles.sectionTitle, { color: safeColor(theme.colors.text) }]}}>Provider Portal</Text>
          <View style={{[styles.providerBanner, { backgroundColor: safeColor(theme.colors.surface) }]}}>
            <View style={styles.bannerContent}>
              <View style={{[styles.bannerIcon, { backgroundColor: safeColor(theme.colors.primary) }]}}>
                <BriefcaseIcon size={24} color={{safeColor(theme.colors.white)} /}>
              </View>
              <View style={styles.bannerText}>
                <Text style={{[styles.bannerTitle, { color: safeColor(theme.colors.text) }]}}>
                  Become a Service Provider;
                </Text>
                <Text
                  style={[styles.bannerDescription, { color: safeColor(theme.colors.textSecondary) }]}
                  numberOfLines={2}
                >
                  Offer your services to thousands of potential clients in your area;
                </Text>
              </View>
            </View>
            <TouchableOpacity
              style={[styles.bannerButton, { borderColor: safeColor(theme.colors.primary) }]}
              onPress={navigateToProviderProfile}
            >
              <Text style={[ color: safeColor(theme.colors.primary) ]}>Get Started</Text>
            </TouchableOpacity>
          </View>
          <View style={styles.helpText}>
            <Text style={{[styles.helpDescription, { color: safeColor(theme.colors.textSecondary) }]}}>
              💡 New to providing services? Click "Get Started" to create your provider profile;
            </Text>
          </View>
        </View>
      );
    }
  }
  const renderCategoryItem = ({ item }) => (
    <TouchableOpacity
      style={[
        styles.categoryCard,
        {
          backgroundColor: safeColorWithOpacity(safeColor(item.color), 0.06),
          borderColor: safeColorWithOpacity(safeColor(item.color), 0.2) ;
        },
      ]}
      onPress={() => navigateToCategory(item)}
    >
      <View style={{[styles.categoryIcon, { backgroundColor: safeColorWithOpacity(safeColor(item.color), 0.15) }]}}>
        {/* Use a dynamic icon based on the category */}
        <Text style={{[styles.iconPlaceholder, { color: safeColor(item.color) }]}}>{item.icon.charAt(0)}</Text>
      </View>
      <Text style={{[styles.categoryName, { color: safeColor(theme.colors.text) }]}}>{item.name}</Text>
      <Text style={[styles.categoryDescription, { color: safeColor(theme.colors.textSecondary) }]} numberOfLines={2}>
        {item.description}
      </Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={{[styles.container, { backgroundColor: safeColor(theme.colors.background) }]}}>
      <Stack.Screen;
        options={{
          title: 'Services',
          headerShadowVisible: false,
          headerStyle: { backgroundColor: safeColor(theme.colors.background) },
          headerTintColor: safeColor(theme.colors.text),
          headerRight: () => (,
            <TouchableOpacity onPress={goToSavedProviders} style={styles.headerButton}>
              <SavedIcon size={24} color={{safeColor(theme.colors.primary)} /}>
            </TouchableOpacity>
          ),
        }}
      />
      {/* Search Bar */}
      <View
        style={[
          styles.searchContainer,
          { backgroundColor: safeColor(theme.colors.surface), borderColor: safeColor(theme.colors.border) },
        ]}
      >
        <Search size={20} color={{safeColor(theme.colors.textSecondary)} /}>
        <TextInput
          style={[styles.searchInput, { color: safeColor(theme.colors.text) }]}
          placeholder="Search services or providers...";
          placeholderTextColor={safeColor(theme.colors.textSecondary)} value={searchQuery} onChangeText={setSearchQuery} onSubmitEditing={handleSearch} returnKeyType="search";
        />
        <TouchableOpacity style={styles.filterButton} onPress={goToServiceFilter}>
          <Filter size={20} color={{safeColor(theme.colors.primary)} /}>
        </TouchableOpacity>
      </View>
      {isLoading && !refreshing ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={{safeColor(theme.colors.primary)} /}>
        </View>
      ) : error ? (,
        <View style={styles.errorContainer}>
          <Text style={{[styles.errorText, { color: safeColor(theme.colors.error) }]}}>{error}</Text>
          <TouchableOpacity
            style={[styles.retryButton, { backgroundColor: safeColor(theme.colors.primary) }]}
            onPress={handleRefresh}
          >
            <Text style={[ color: safeColor(theme.colors.white), fontWeight: '600' ]}>Retry</Text>
          </TouchableOpacity>
        </View>
      ) : (,
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false} refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} colors={[safeColor(theme.colors.primary)]}
            />
          }
        >
          {renderProviderPortalSection()}
          {/* Popular Categories Section */}
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={{[styles.sectionTitle, { color: safeColor(theme.colors.text) }]}}>Popular Categories</Text>
              <TouchableOpacity onPress={() => router.push('/categories' as any)}>
                <Text style={{[styles.viewAllText, { color: safeColor(theme.colors.primary) }]}}>View All</Text>
              </TouchableOpacity>
            </View>
            <FlatList data={displayCategories.slice(0, 4)} renderItem={renderCategoryItem} keyExtractor={item ={}> item.id}
              horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.categoriesContainer}
            />
          </View>
          {/* Featured Providers Section */}
          {featuredProviders.length > 0 && (
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <Text style={{[styles.sectionTitle, { color: safeColor(theme.colors.text) }]}}>
                  Featured Providers;
                </Text>
                <TouchableOpacity onPress={() => router.push('/service-providers?featured=true' as any)}>
                  <Text style={{[styles.viewAllText, { color: safeColor(theme.colors.primary) }]}}>View All</Text>
                </TouchableOpacity>
              </View>
              <ScrollView horizontal={true} showsHorizontalScrollIndicator={false} contentContainerStyle={styles.horizontalList}>
                {featuredProviders.map(provider => (
                  <ServiceProviderCard key={provider.id} provider={{provider} compact /}>
                ))}
              </ScrollView>
            </View>
          )}
          {/* All Service Providers Section */}
          <View style={styles.section}>
            <Text style={{[styles.sectionTitle, { color: safeColor(theme.colors.text) }]}}>All Service Providers</Text>
            <View style={styles.providersGrid}>
              {providers.slice(0, 4).map(provider => (
                <ServiceProviderCard key={provider.id} provider={{provider} /}>
              ))}
            </View>
            {providers.length > 4 && (
              <TouchableOpacity
                style={[styles.viewAllButton, { borderColor: safeColor(theme.colors.primary) }]}
                onPress={() => router.push('/service-providers' as any)}
              >
                <Text style={[ color: safeColor(theme.colors.primary), fontWeight: '600' ]}>View All Providers</Text>
              </TouchableOpacity>
            )}
          </View>
        </ScrollView>
      )}
    </SafeAreaView>
  );
}
const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
  },
  headerButton: {
    padding: 8,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    margin: 16,
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 12,
    borderWidth: 1,
  },
  searchInput: {
    flex: 1,
    marginLeft: 10,
    fontSize: 16,
  },
  filterButton: {
    padding: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  content: {
    flex: 1,
  },
  section: {
    marginBottom: 24,
    paddingHorizontal: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 16,
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: '600',
  },
  horizontalList: {
    paddingBottom: 8,
  },
  providersGrid: {
    marginTop: 8,
  },
  viewAllButton: {
    marginTop: 16,
    padding: 12,
    borderWidth: 1,
    borderRadius: 8,
    alignItems: 'center',
  },
  providerBanner: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: theme.colors.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  bannerContent: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  bannerIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  bannerText: {
    flex: 1,
  },
  bannerTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 4,
  },
  bannerDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  bannerButton: {
    padding: 12,
    borderWidth: 1,
    borderRadius: 8,
    alignItems: 'center',
  },
  dashboardButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  dashboardButtonText: {
    fontWeight: '600',
    marginRight: 8,
  },
  categoriesContainer: {
    paddingBottom: 8,
  },
  categoryCard: {
    width: 180,
    borderRadius: 12,
    padding: 16,
    marginRight: 12,
    borderWidth: 1,
  },
  categoryIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  iconPlaceholder: {
    fontSize: 20,
    fontWeight: '700',
  },
  categoryName: {
    fontSize: 16,
    fontWeight: '700',
    marginBottom: 4,
  },
  categoryDescription: {
    fontSize: 12,
    lineHeight: 16,
  },
  helpText: {
    marginTop: 16,
    padding: 16,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 8,
  },
  helpDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
});
