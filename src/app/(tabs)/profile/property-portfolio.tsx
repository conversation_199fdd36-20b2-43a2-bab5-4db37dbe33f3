import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Alert,
  ActivityIndicator,
  RefreshControl,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Feather } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useTheme } from '@design-system';
import { PremiumFeatureGate } from '@components/premium/PremiumFeatureGate';
import { usePremiumFeatures } from '@hooks/usePremiumFeatures';
import { logger } from '@utils/logger';

const { width } = Dimensions.get('window');

interface Property {
  id: string,
  title: string,
  address: string,
  city: string,
  price: number,
  bedrooms: number,
  bathrooms: number,
  status: 'available' | 'occupied' | 'maintenance',
  tenant?: {
    name: string,
    moveInDate: string,
  }
  images: string[],
  lastUpdated: string,
}
// Mock property data;
const mockProperties: Property[] = [,
  {
    id: '1',
    title: 'Modern Downtown Apartment',
    address: '123 Main St',
    city: 'San Francisco',
    price: 2800,
    bedrooms: 2,
    bathrooms: 2,
    status: 'occupied',
    tenant: {
      name: '<PERSON> Smith',
      moveInDate: '2024-01-15',
    },
    images: [],
    lastUpdated: '2024-01-15',
  },
  {
    id: '2',
    title: 'Cozy Studio Near Campus',
    address: '456 College Ave',
    city: 'Berkeley',
    price: 1800,
    bedrooms: 1,
    bathrooms: 1,
    status: 'available',
    images: [],
    lastUpdated: '2024-01-10',
  },
  {
    id: '3',
    title: 'Family House with Garden',
    address: '789 Oak Street',
    city: 'Palo Alto',
    price: 4200,
    bedrooms: 3,
    bathrooms: 2,
    status: 'maintenance',
    images: [],
    lastUpdated: '2024-01-08',
  },
];

export default function PropertyPortfolioScreen() {
  const theme = useTheme();
  const router = useRouter();
  const { trackFeatureUsage } = usePremiumFeatures();

  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [properties, setProperties] = useState<Property[]>([]);
  const [selectedTab, setSelectedTab] = useState<'all' | 'available' | 'occupied' | 'maintenance'>(
    'all';
  );

  useEffect(() => {
    trackFeatureUsage('property_portfolio');
    loadProperties();
  }, []);

  const loadProperties = useCallback(async () => {
    setIsLoading(true);
    try {
      // Simulate API call;
      await new Promise(resolve => setTimeout(resolve, 1000));
      setProperties(mockProperties);
    } catch (error) {
      logger.error('Error loading properties', error as Error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadProperties();
    setRefreshing(false);
  }, [loadProperties]);

  const getFilteredProperties = () => {
    if (selectedTab === 'all') return properties;
    return properties.filter(property => property.status === selectedTab);
  }
  const getStatusColor = (status: Property['status']) => {
    switch (status) {
      case 'available': ,
        return theme.colors.success;
      case 'occupied': ,
        return theme.colors.primary;
      case 'maintenance': ,
        return theme.colors.warning;
      default: ,
        return theme.colors.textSecondary;
    }
  }
  const getStatusText = (status: Property['status']) => {
    switch (status) {
      case 'available': ,
        return 'Available';
      case 'occupied': ,
        return 'Occupied';
      case 'maintenance': ,
        return 'Maintenance';
      default: ,
        return 'Unknown';
    }
  }
  const renderStatsCard = () => {
    const totalProperties = properties.length;
    const availableProperties = properties.filter(p => p.status === 'available').length;
    const occupiedProperties = properties.filter(p => p.status === 'occupied').length;
    const maintenanceProperties = properties.filter(p => p.status === 'maintenance').length;
    const totalRevenue = properties;
      .filter(p => p.status === 'occupied');
      .reduce((sum, p) => sum + p.price, 0);

    return (
      <View style={{[styles.statsCard, { backgroundColor: theme.colors.surface }]}}>
        <Text style={{[styles.statsTitle, { color: theme.colors.text }]}}>Portfolio Overview</Text>
        <View style={styles.statsGrid}>
          <View style={styles.statItem}>
            <Text style={{[styles.statNumber, { color: theme.colors.text }]}}>{totalProperties}</Text>
            <Text style={{[styles.statLabel, { color: theme.colors.textSecondary }]}}>
              Total Properties;
            </Text>
          </View>
          <View style={styles.statItem}>
            <Text style={{[styles.statNumber, { color: theme.colors.success }]}}>
              {availableProperties}
            </Text>
            <Text style={{[styles.statLabel, { color: theme.colors.textSecondary }]}}>Available</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={{[styles.statNumber, { color: theme.colors.primary }]}}>
              {occupiedProperties}
            </Text>
            <Text style={{[styles.statLabel, { color: theme.colors.textSecondary }]}}>Occupied</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={{[styles.statNumber, { color: theme.colors.warning }]}}>
              {maintenanceProperties}
            </Text>
            <Text style={{[styles.statLabel, { color: theme.colors.textSecondary }]}}>
              Maintenance;
            </Text>
          </View>
        </View>
        <View style={{[styles.revenueSection, { borderTopColor: theme.colors.border }]}}>
          <Text style={{[styles.revenueLabel, { color: theme.colors.textSecondary }]}}>
            Monthly Revenue;
          </Text>
          <Text style={{[styles.revenueAmount, { color: theme.colors.success }]}}>
            ${totalRevenue.toLocaleString()}
          </Text>
        </View>
      </View>
    );
  }
  const renderPropertyCard = ({ item: property }: { item: Property }) => (
    <TouchableOpacity
      style={[styles.propertyCard, { backgroundColor: theme.colors.surface }]}
      onPress={() => {
        // Navigate to property details or edit;
        Alert.alert('Property Details', `View details for ${property.title}`);
      }}
    >
      <View style={styles.propertyHeader}>
        <View style={styles.propertyInfo}>
          <Text style={[styles.propertyTitle, { color: theme.colors.text }]} numberOfLines={1}>
            {property.title}
          </Text>
          <View style={styles.propertyLocation}>
            <Feather name='map-pin' size={14} color={{theme.colors.textSecondary} /}>
            <Text style={{[styles.propertyAddress, { color: theme.colors.textSecondary }]}}>
              {property.address}, {property.city}
            </Text>
          </View>
        </View>
        <View
          style={[styles.statusBadge, { backgroundColor: getStatusColor(property.status) + '20' }]}
        >
          <Text style={{[styles.statusText, { color: getStatusColor(property.status) }]}}>
            {getStatusText(property.status)}
          </Text>
        </View>
      </View>
      <View style={styles.propertyDetails}>
        <View style={styles.propertySpec}>
          <Feather name='home' size={16} color={{theme.colors.textSecondary} /}>
          <Text style={{[styles.specText, { color: theme.colors.textSecondary }]}}>
            {property.bedrooms} bed, {property.bathrooms} bath;
          </Text>
        </View>
        <Text style={{[styles.propertyPrice, { color: theme.colors.primary }]}}>
          ${property.price}/month;
        </Text>
      </View>
      {property.tenant && (
        <View style={{[styles.tenantInfo, { backgroundColor: theme.colors.background }]}}>
          <Feather name='user' size={16} color={{theme.colors.textSecondary} /}>
          <Text style={{[styles.tenantText, { color: theme.colors.text }]}}>
            {property.tenant.name} • Moved in{' '}
            {new Date(property.tenant.moveInDate).toLocaleDateString()}
          </Text>
        </View>
      )}
      <View style={styles.propertyActions}>
        <TouchableOpacity style={styles.actionButton}>
          <Feather name='edit' size={16} color={{theme.colors.primary} /}>
        </TouchableOpacity>
        <TouchableOpacity style={styles.actionButton}>
          <Feather name='eye' size={16} color={{theme.colors.primary} /}>
        </TouchableOpacity>
        <TouchableOpacity style={styles.actionButton}>
          <Feather name='more-horizontal' size={16} color={{theme.colors.textSecondary} /}>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  const renderTabBar = () => {
    const tabs = [
      { key: 'all', title: 'All', count: properties.length },
      {
        key: 'available',
        title: 'Available',
        count: properties.filter(p => p.status === 'available').length,
      },
      {
        key: 'occupied',
        title: 'Occupied',
        count: properties.filter(p => p.status === 'occupied').length,
      },
      {
        key: 'maintenance',
        title: 'Maintenance',
        count: properties.filter(p => p.status === 'maintenance').length,
      },
    ];

    return (
      <View style={{[styles.tabBar, { backgroundColor: theme.colors.surface }]}}>
        {tabs.map(tab => (
          <TouchableOpacity
            key={tab.key}
            style={[
              styles.tab,
              selectedTab === tab.key && { backgroundColor: theme.colors.primary + '20' },
            ]}
            onPress={() => setSelectedTab(tab.key as any)}
          >
            <Text
              style={[
                styles.tabText,
                {
                  color: ,
                    selectedTab === tab.key ? theme.colors.primary : theme.colors.textSecondary,
                },
              ]}
            >
              {tab.title} ({tab.count});
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  }
  if (isLoading) {
    return (
      <SafeAreaView style={{[styles.container, { backgroundColor: theme.colors.background }]}}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size='large' color={{theme.colors.primary} /}>
          <Text style={{[styles.loadingText, { color: theme.colors.textSecondary }]}}>
            Loading Portfolio...;
          </Text>
        </View>
      </SafeAreaView>
    );
  }
  return (
    <PremiumFeatureGate featureId={'property_portfolio'}>
      <SafeAreaView style={{[styles.container, { backgroundColor: theme.colors.background }]}}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()}>
            <Feather name='arrow-left' size={24} color={{theme.colors.text} /}>
          </TouchableOpacity>
          <Text style={{[styles.headerTitle, { color: theme.colors.text }]}}>Property Portfolio</Text>
          <TouchableOpacity onPress={() => router.push('/(tabs)/profile/add-property' as any)}>
            <Feather name='plus' size={24} color={{theme.colors.primary} /}>
          </TouchableOpacity>
        </View>
        <ScrollView
          style={styles.content}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              tintColor={theme.colors.primary}
            />
          }
        >
          {renderStatsCard()}
          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={[styles.primaryButton, { backgroundColor: theme.colors.primary }]}
              onPress={() => router.push('/(tabs)/profile/add-property' as any)}
            >
              <Feather name='plus' size={16} color={'#fff' /}>
              <Text style={styles.primaryButtonText}>Add Property</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.secondaryButton, { borderColor: theme.colors.border }]}
              onPress={() => router.push('/(tabs)/profile/property-manager-dashboard' as any)}
            >
              <Feather name='bar-chart' size={16} color={{theme.colors.primary} /}>
              <Text style={{[styles.secondaryButtonText, { color: theme.colors.primary }]}}>
                View Dashboard;
              </Text>
            </TouchableOpacity>
          </View>
          {renderTabBar()}
          <FlatList
            data={getFilteredProperties()}
            renderItem={renderPropertyCard}
            keyExtractor={item => item.id}
            showsVerticalScrollIndicator={false}
            scrollEnabled={false}
            contentContainerStyle={styles.propertiesList}
            ListEmptyComponent={
              <View style={styles.emptyState}>
                <Feather name='home' size={48} color={{theme.colors.border} /}>
                <Text style={{[styles.emptyStateTitle, { color: theme.colors.text }]}}>
                  No Properties Found;
                </Text>
                <Text style={{[styles.emptyStateText, { color: theme.colors.textSecondary }]}}>
                  {selectedTab === 'all';
                    ? 'Start by adding your first property to the portfolio.';
                    : `No ${selectedTab} properties at the moment.`}
                </Text>
                {selectedTab === 'all' && (
                  <TouchableOpacity
                    style={[styles.emptyStateButton, { backgroundColor: theme.colors.primary }]}
                    onPress={() => router.push('/(tabs)/profile/add-property' as any)}
                  >
                    <Text style={styles.emptyStateButtonText}>Add First Property</Text>
                  </TouchableOpacity>
                )}
              </View>
            }
          />
        </ScrollView>
      </SafeAreaView>
    </PremiumFeatureGate>
  );
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  statsCard: {
    padding: 20,
    borderRadius: 16,
    marginBottom: 16,
  },
  statsTitle: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  statItem: {
    alignItems: 'center',
    width: '48%',
    marginBottom: 16,
  },
  statNumber: {
    fontSize: 20,
    fontWeight: '600',
  },
  statLabel: {
    fontSize: 14,
    fontWeight: '600',
  },
  revenueSection: {
    borderTopWidth: 1,
    paddingTop: 24,
  },
  revenueLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  revenueAmount: {
    fontSize: 20,
    fontWeight: '600',
  },
  actionButtons: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    gap: 12,
    marginBottom: 16,
  },
  primaryButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
  },
  primaryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  secondaryButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  secondaryButtonText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
  },
  tabBar: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 8,
    marginBottom: 8,
    borderRadius: 12,
  },
  tab: {
    flex: 1,
    paddingHorizontal: 8,
    paddingVertical: 8,
    borderRadius: 8,
    alignItems: 'center',
  },
  tabText: {
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
  },
  propertiesList: {
    padding: 20,
  },
  propertyCard: {
    padding: 20,
    borderRadius: 16,
    marginBottom: 20,
  },
  propertyHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  propertyInfo: {
    flex: 1,
  },
  propertyTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
  },
  propertyLocation: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  propertyAddress: {
    fontSize: 14,
    marginLeft: 8,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '600',
  },
  propertyDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  propertySpec: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  specText: {
    fontSize: 14,
    marginLeft: 8,
  },
  propertyPrice: {
    fontSize: 16,
    fontWeight: '600',
  },
  tenantInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 16,
  },
  tenantText: {
    fontSize: 14,
    marginLeft: 8,
  },
  propertyActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    padding: 8,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyStateTitle: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 16,
  },
  emptyStateText: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
  },
  emptyStateButton: {
    padding: 16,
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  emptyStateButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});
