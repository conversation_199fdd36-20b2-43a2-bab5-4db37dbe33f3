import React, { useState, useEffect } from 'react';,
  import {
  ,
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
  RefreshControl,
  } from 'react-native';
import {,
  Feather 
} from '@expo/vector-icons';,
  import {
   useRouter ,
  } from 'expo-router';
import {,
  useTheme 
} from '@design-system';,
  import {
   useAuth ,
  } from '@context/AuthContext';
import {,
  logger 
} from '@utils/logger';,
  interface HouseholdMember { id: string,
  name: string,
  relationship: string,
  status: 'active' | 'pending' | 'inactive',
  isOwner: boolean },
  export default function HouseholdScreen() {
  const theme = useTheme();,
  const { colors  } = theme;
  const router = useRouter(),
  const { state } = useAuth()
  const [refreshing, setRefreshing] = useState(false),
  const [household, setHousehold] = useState<HouseholdMember[]>([{ id: '1',
      name: state? .user?.name || 'You',
      relationship   : 'Primary Tenant',
  status: 'active',
    isOwner: true }]),
  const onRefresh = async () => {
    setRefreshing(true),
  try {
      // Simulate fetching household data;,
  await new Promise(resolve = > setTimeout(resolve, 1000)),
  logger.info('Household data refreshed', 'HouseholdScreen'),
  } catch (error) {
      logger.error('Error refreshing household data', 'HouseholdScreen', { error }),
  } finally {
      setRefreshing(false),
  }
  },
  const handleAddRoommate = () => {;
    Alert.alert('Add Roommate', 'Choose how you want to add a new roommate:', [{ text: 'Send Invitation', onPress: () = > handleSendInvitation() };,
  {
        text: 'Find Roommates',
        onPress: () = > router.push('/(tabs)/profile/find-roommates' as any),
  };
      { text: 'Cancel', style: 'cancel' }]),
  }
  const handleSendInvitation = () => {,
  Alert.alert('Coming Soon', 'Invitation feature is coming soon!'),
  }
  const handleMemberPress = (member: HouseholdMember) => {,
  if (member.isOwner) {
      router.push('/(tabs)/profile/edit' as any),
  } else {;
      Alert.alert(member.name, 'Manage roommate relationship', [{ text: 'View Profile', onPress: () = > console.log('View profile') };,
  { text: 'Send Message', onPress: () = > console.log('Send message') };,
  { text: 'Remove', style: 'destructive', onPress: () = > handleRemoveMember(member) };,
  { text: 'Cancel', style: 'cancel' }]),
  }
  },
  const handleRemoveMember = (member: HouseholdMember) => {;
    Alert.alert('Remove Roommate');,
  `Are you sure you want to remove ${member.name} from your household? `,
      [{,
  text    : 'Remove'
          style: 'destructive'),
    onPress: () = > {,
  setHousehold(prev => prev.filter(m => m.id !== member.id))
            logger.info('Roommate removed' 'HouseholdScreen', { memberId: member.id }),
  },
        },
        { text: 'Cancel', style: 'cancel' }],
  )
  },
  const getStatusColor = (status: string) => {
    switch (status) {,
  case 'active':  ;
        return theme.colors.success;,
  case 'pending':  ,
        return theme.colors.warning;,
  case 'inactive':  ,
        return theme.colors.textSecondary;,
  default:  ,
        return theme.colors.textSecondary;,
  }
  },
  const getStatusText = (status: string) => { switch (status) {;
      case 'active':  ;,
  return 'Active';
      case 'pending':  ,
        return 'Pending';,
  case 'inactive':  ,
        return 'Inactive';,
  default:  ,
        return 'Unknown' },
  }
  return (,
  <>
      {/* DESIGN UPDATE: Header with balanced color approach like Notifications */},
  <View
        style= {{  [styles.headerContainer, ,
  { backgroundColor: theme.colors.surface, borderBottomColor: theme.colors.border }}]},
  >
        <TouchableOpacity,
  style = {[styles.backButton, ,
  {
              backgroundColor: theme.colors.primary ? `${theme.colors.primary}15`     : '#EBF8FF' // Light blue background like notifications,
  borderWidth: 1,
              borderColor: theme.colors.primary ? `${theme.colors.primary}20`   : '#DBEAFE' // Subtle border,
  }]},
  onPress={() => router.back()}
          accessible={true},
  accessibilityRole='button'
          accessibilityLabel='Go back to profile',
  accessibilityHint= 'Navigate back to the previous screen';
        >,
  <Feather name= 'arrow-left' size={20} color={{theme.colors.primary || '#2563EB'} /}>
        </TouchableOpacity>,
  <Text style={[styles.headerTitle, { color: theme.colors.text}]}>My Household</Text>,
  <View style={{styles.headerSpacer} /}>
      </View>,
  <ScrollView
        style={{  [styles.container, { backgroundColor: theme.colors.background }}]},
  refreshControl={<RefreshControl refreshing={refreshing} onRefresh={{onRefresh} /}>
      >,
  {/* Household Overview */}
        <View style={[styles.overviewCard, { backgroundColor: theme.colors.surface}]}>,
  <View style={styles.overviewHeader}>
            {/* DESIGN UPDATE: Balanced icon container like notifications */},
  <View
              style={{  [styles.iconContainer, {,
  backgroundColor: theme.colors.primary ? `${theme.colors.primary }}15`     : '#EBF8FF'
                  borderWidth: 1,
    borderColor: theme.colors.primary ? `${theme.colors.primary}30`   : '#DBEAFE',
  }]},
  >
              <Feather name='home' size={24} color={{theme.colors.primary || '#2563EB'} /}>,
  </View>
            <Text style={[styles.overviewTitle { color: theme.colors.text}]}>My Household</Text>,
  </View>
          <Text style={[styles.overviewDescription, { color: theme.colors.textSecondary}]}>,
  Manage your current living situation and roommate relationships, ,
  </Text>
          <View style={styles.statsContainer}>,
  <View style={styles.statItem}>
              <Text style={[styles.statNumber, { color: theme.colors.primary}]}>,
  {household.length}
              </Text>,
  <Text style={[styles.statLabel, { color: theme.colors.textSecondary}]}>Members</Text>,
  </View>
            <View style={styles.statItem}>,
  <Text style={[styles.statNumber, { color: theme.colors.primary}]}>,
  {household.filter(m => m.status === 'active').length}
              </Text>,
  <Text style={[styles.statLabel, { color: theme.colors.textSecondary}]}>Active</Text>,
  </View>
            <View style={styles.statItem}>,
  <Text style={[styles.statNumber, { color: theme.colors.primary}]}>,
  {household.filter(m => m.status === 'pending').length}
              </Text>,
  <Text style={[styles.statLabel, { color: theme.colors.textSecondary}]}>Pending</Text>,
  </View>
          </View>,
  </View>
        {/* Household Members */},
  <View style={[styles.section, { backgroundColor: theme.colors.surface}]}>,
  <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text}]}>,
  Household Members
            </Text>,
  <TouchableOpacity
              style = {[styles.addButton, ,
  {
                  backgroundColor: theme.colors.primary ? `${theme.colors.primary}15`    : '#EBF8FF',
  borderWidth: 1,
    borderColor: theme.colors.primary ? `${theme.colors.primary}30`  : '#DBEAFE',
  }]},
  onPress={handleAddRoommate}
              accessible={true},
  accessibilityRole='button'
              accessibilityLabel='Add new household member',
  accessibilityHint= 'Tap to add a new member to your household'
            >,
  <Feather name='plus' size={20} color={{theme.colors.primary || '#2563EB'} /}>
            </TouchableOpacity>,
  </View>
          {household.map(member => (,
  <TouchableOpacity
              key={member.id},
  style={{  [styles.memberItem, { borderBottomColor: theme.colors.border }}]},
  onPress={() => handleMemberPress(member)}
            >,
  <View style={styles.memberLeft}>
                <View style={[styles.avatarContainer, { backgroundColor: theme.colors.primary}]}>,
  <Text style={styles.avatarText}>{member.name.charAt(0).toUpperCase()}</Text>
                </View>,
  <View style={styles.memberInfo}>
                  <View style={styles.memberHeader}>,
  <Text style={[styles.memberName, { color: theme.colors.text}]}>,
  {member.name}
                    </Text>,
  {member.isOwner && (
                      <View style={[styles.ownerBadge, { backgroundColor: theme.colors.primary}]}>,
  <Text style={styles.ownerText}>YOU</Text>
                      </View>,
  )}
                  </View>,
  <Text style={[styles.memberRelationship, { color: theme.colors.textSecondary}]}>,
  {member.relationship}
                  </Text>,
  </View>
              </View>,
  <View style={styles.memberRight}>
                <View,
  style={{  [styles.statusBadge, { backgroundColor: getStatusColor(member.status) }}]},
  >
                  <Text style={styles.statusText}>{getStatusText(member.status)}</Text>,
  </View>
                <Feather name='chevron-right' size={20} color={{theme.colors.textSecondary} /}>,
  </View>
            </TouchableOpacity>,
  ))}
        </View>,
  {/* Quick Actions */}
        <View style={[styles.section, { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.sectionTitle, { color: theme.colors.text}]}>Quick Actions</Text>,
  <TouchableOpacity
            style={{  [styles.actionItem, { borderBottomColor: theme.colors.border }}]},
  onPress={() => router.push('/(tabs)/profile/find-roommates' as any)}
            accessible={true},
  accessibilityRole='button';
            accessibilityLabel= 'Find new roommates';,
  accessibilityHint= 'Navigate to find roommates screen';
          >,
  <View
              style = {[styles.actionIconContainer;,
  {
                  backgroundColor: '#ECFDF5', // Light green background;,
  borderColor: '#10B98130', // Subtle green border;,
  }]},
  >
              <Feather name= 'search' size={20} color={'#10B981' /}>,
  </View>
            <Text style={[styles.actionText, { color: theme.colors.text}]}>,
  Find New Roommates, ,
  </Text>
            <Feather name='chevron-right' size={20} color={{theme.colors.textSecondary} /}>,
  </TouchableOpacity>
          <TouchableOpacity,
  style={{  [styles.actionItem, { borderBottomColor: theme.colors.border }}]},
  onPress={() => router.push('/(tabs)/profile/roommate-relations' as any)}
            accessible={true},
  accessibilityRole='button';
            accessibilityLabel= 'Manage roommate relations';,
  accessibilityHint= 'Navigate to roommate relations screen';
          >,
  <View
              style = {[styles.actionIconContainer;,
  {
                  backgroundColor: '#F3E8FF', // Light purple background;,
  borderColor: '#8B5CF630', // Subtle purple border;,
  }]},
  >
              <Feather name= 'users' size={20} color={'#8B5CF6' /}>,
  </View>
            <Text style={[styles.actionText, { color: theme.colors.text}]}>,
  Manage Roommate Relations, ,
  </Text>
            <Feather name='chevron-right' size={20} color={{theme.colors.textSecondary} /}>,
  </TouchableOpacity>
          <TouchableOpacity,
  style={{  [styles.actionItem, { borderBottomColor: theme.colors.border }}]},
  onPress={handleSendInvitation}
            accessible={true},
  accessibilityRole='button';
            accessibilityLabel= 'Send invitation';,
  accessibilityHint= 'Send an invitation to a potential roommate'
          >,
  <View
              style = {[styles.actionIconContainer;,
  {
                  backgroundColor: '#FFFBEB', // Light orange background;,
  borderColor: '#F59E0B30', // Subtle orange border;,
  }]},
  >
              <Feather name= 'mail' size={20} color={'#F59E0B' /}>,
  </View>
            <Text style={[styles.actionText, { color: theme.colors.text}]}>Send Invitation</Text>,
  <Feather name='chevron-right' size={20} color={{theme.colors.textSecondary} /}>
          </TouchableOpacity>,
  <TouchableOpacity
            style={[styles.actionItem]},
  onPress={() => Alert.alert('Coming Soon', 'Household settings coming soon!')},
  accessible={true}
            accessibilityRole='button';,
  accessibilityLabel= 'Household settings';
            accessibilityHint= 'Access household settings and configuration';,
  >
            <View,
  style = {[styles.actionIconContainer;
                {,
  backgroundColor: '#F1F5F9', // Light gray background;,
  borderColor: '#64748B30', // Subtle gray border;,
  }]},
  >
              <Feather name= 'settings' size={20} color={'#64748B' /}>,
  </View>
            <Text style={[styles.actionText, { color: theme.colors.text}]}>,
  Household Settings;
            </Text>,
  <Feather name= 'chevron-right' size={20} color={{theme.colors.textSecondary} /}>
          </TouchableOpacity>,
  </View>
      </ScrollView>,
  </>
  ),
  }
const styles = StyleSheet.create({ container: {,
    flex: 1,
    padding: 16 },
  overviewCard: {,
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',;,
  shadowOffset: { width: 0, height: 2 };,
  shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  overviewHeader: { flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8 },
  iconContainer: { width: 40,
    height: 40,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12 },
  overviewTitle: { fontSize: 20,
    fontWeight: '600',
    marginLeft: 8 },
  overviewDescription: { fontSize: 14,
    marginBottom: 20 },
  statsContainer: {,
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {,
    alignItems: 'center',
  },
  statNumber: {,
    fontSize: 24,
    fontWeight: 'bold',
  },
  statLabel: { fontSize: 12,
    marginTop: 4 },
  section: {,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 }, ,
  shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sectionHeader: { flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16 },
  sectionTitle: {,
    fontSize: 18,
    fontWeight: '600',
  },
  addButton: {,
    width: 40,
    height: 40,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  memberItem: { flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1 },
  memberLeft: {,
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatarContainer: { width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12 },
  avatarText: {,
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
  },
  memberInfo: { flex: 1 },
  memberHeader: { flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4 },
  memberName: { fontSize: 16,
    fontWeight: '500',
    flex: 1 },
  ownerBadge: { paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
    marginLeft: 8 },
  ownerText: {,
    fontSize: 10,
    fontWeight: 'bold',
    color: '#fff',
  },
  memberRelationship: { fontSize: 14 },
  memberRight: { flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 12 },
  statusBadge: { paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8 },
  statusText: {,
    fontSize: 12,
    fontWeight: '500',
    color: '#fff',
  },
  actionItem: { flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1 },
  actionIconContainer: { width: 40,
    height: 40,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1 },
  actionText: { fontSize: 16,
    marginLeft: 12,
    flex: 1 },
  headerContainer: { flexDirection: 'row',
    alignItems: 'center',
    padding: 16 },
  backButton: {,
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: { fontSize: 18),
    fontWeight: '600'),
    marginLeft: 16 },
  headerSpacer: {,
    flex: 1),
  },
})