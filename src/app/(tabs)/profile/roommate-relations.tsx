import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Modal,
  TextInput,
  Switch,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Feather } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useTheme } from '@design-system';
import { useAuth } from '@context/AuthContext';
import { logger } from '@utils/logger';

interface Roommate {
  id: string,
  name: string,
  email: string,
  phone?: string,
  profileImage?: string,
  moveInDate: string,
  status: 'current' | 'moving_out' | 'past';
  relationship: 'good' | 'neutral' | 'poor';
  isVerified: boolean,
  preferences: {
    allowDirectMessages: boolean,
    shareExpenses: boolean,
    shareChores: boolean,
    allowEmergencyContact: boolean,
  }
  notes?: string,
}
interface ConflictReport {
  id: string,
  roommateId: string,
  roommateName: string,
  type: 'noise' | 'cleanliness' | 'guests' | 'bills' | 'other';
  severity: 'low' | 'medium' | 'high';
  description: string,
  status: 'open' | 'resolved' | 'escalated';
  createdAt: string,
  resolvedAt?: string,
}
export default function RoommateRelationsScreen() {
  const theme = useTheme()
  const { colors  } = theme,
  const router = useRouter()
  const { state } = useAuth()
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState<'current' | 'conflicts' | 'history'>('current')
  const [roommates, setRoommates] = useState<Roommate[]>([])
  const [conflicts, setConflicts] = useState<ConflictReport[]>([])
  const [showConflictModal, setShowConflictModal] = useState(false)
  const [selectedRoommate, setSelectedRoommate] = useState<Roommate | null>(null)
  const [showPreferencesModal, setShowPreferencesModal] = useState(false)
  // Conflict form state,
  const [conflictForm, setConflictForm] = useState({
    roommateId: '';
    type: 'other' as ConflictReport['type'];
    severity: 'medium' as ConflictReport['severity'];
    description: ''
  })
  useEffect(() = > {
    loadRoommateData()
  }, [])
  const loadRoommateData = async () => {
    setLoading(true)
    try {
      // Mock data - replace with actual API calls,
      const mockRoommates: Roommate[] = [;
        {
          id: '1';
          name: 'Sarah Chen';
          email: '<EMAIL>';
          phone: '+1234567890';
          moveInDate: '2023-09-01';
          status: 'current';
          relationship: 'good';
          isVerified: true,
          preferences: {
            allowDirectMessages: true,
            shareExpenses: true,
            shareChores: true,
            allowEmergencyContact: true,
          },
          notes: 'Great roommate, very clean and respectful.',
        },
        {
          id: '2';
          name: 'Alex Rodriguez';
          email: '<EMAIL>';
          moveInDate: '2023-11-15';
          status: 'current';
          relationship: 'neutral';
          isVerified: true,
          preferences: {
            allowDirectMessages: true,
            shareExpenses: false,
            shareChores: true,
            allowEmergencyContact: false,
          },
          notes: 'Keeps to themselves mostly.'
        },
        {
          id: '3';
          name: 'Jordan Kim';
          email: '<EMAIL>';
          moveInDate: '2023-01-01';
          status: 'past';
          relationship: 'good';
          isVerified: false,
          preferences: {
            allowDirectMessages: false,
            shareExpenses: false,
            shareChores: false,
            allowEmergencyContact: false,
          },
          notes: 'Moved out for job relocation. Was a good roommate.'
        },
      ];

      const mockConflicts: ConflictReport[] = [;
        {
          id: '1';
          roommateId: '2';
          roommateName: 'Alex Rodriguez';
          type: 'noise';
          severity: 'medium';
          description: 'Late night music/TV volume is too loud on weekdays.';
          status: 'open';
          createdAt: '2024-01-10'
        },
      ];

      setRoommates(mockRoommates)
      setConflicts(mockConflicts)
      logger.info('Roommate data loaded', 'RoommateRelationsScreen')
    } catch (error) {
      logger.error('Failed to load roommate data', 'RoommateRelationsScreen', { error })
      Alert.alert('Error', 'Failed to load roommate information')
    } finally {
      setLoading(false)
    }
  }
  const handleSendMessage = (roommate: Roommate) => {
    if (!roommate.preferences.allowDirectMessages) {
      Alert.alert('Messages Not Allowed');
        `${roommate.name} has disabled direct messages. Try talking to them in person or through your household chat.`)
      )
      return null,
    }
    // Use query parameters to prevent [object Object] issues,
    const roommateId = String(roommate.id).trim()
    if (
      roommateId &&;
      roommateId != = 'undefined' &&;
      roommateId != = 'null' &&;
      roommateId != = '[object Object]';
    ) {
      router.push(
        `/chat? recipientId= ${encodeURIComponent(roommateId)}&recipientName=${encodeURIComponent(roommate.name)}&context=roommate`;
      )
    } else {
      console.warn('Invalid roommate ID for chat  : ', roommate.id)
      Alert.alert('Error', 'Unable to start conversation. Please try again.')
    }
  }
  const handleReportConflict = (roommate: Roommate) => {
    setConflictForm(prev => ({
      ...prev,
      roommateId: roommate.id,
    }))
    setShowConflictModal(true)
  }
  const handleSubmitConflict = async () => {
    if (!conflictForm.description.trim()) {
      Alert.alert('Error', 'Please describe the conflict')
      return null,
    }
    try {
      const roommate = roommates.find(r => r.id === conflictForm.roommateId)
      const newConflict: ConflictReport = {
        id: Date.now().toString()
        roommateId: conflictForm.roommateId,
        roommateName: roommate? .name || 'Unknown'
        type : conflictForm.type
        severity: conflictForm.severity,
        description: conflictForm.description,
        status: 'open';
        createdAt: new Date().toISOString().split('T')[0]
      }
      setConflicts(prev = > [newConflict, ...prev])
      setShowConflictModal(false)
      setConflictForm({
        roommateId: '';
        type: 'other';
        severity: 'medium';
        description: ''
      })
      Alert.alert('Conflict Reported',
        "Your conflict report has been submitted. We'll help mediate a resolution.")
      )
      logger.info('Conflict reported', 'RoommateRelationsScreen', { conflictId: newConflict.id })
    } catch (error) {
      logger.error('Failed to submit conflict', 'RoommateRelationsScreen', { error })
      Alert.alert('Error', 'Failed to submit conflict report')
    }
  }
  const handleUpdatePreferences = async (
    roommate: Roommate,
    preferences: Roommate['preferences'];
  ) = > {
    try {
      setRoommates(prev => prev.map(r => (r.id === roommate.id ? { ...r, preferences }   : r))),
      // Save to backend
      // await roommateService.updatePreferences(roommate.id, preferences)
      Alert.alert('Success', 'Roommate preferences updated')
      logger.info('Roommate preferences updated', 'RoommateRelationsScreen', {
        roommateId: roommate.id)
      })
    } catch (error) {
      logger.error('Failed to update preferences', 'RoommateRelationsScreen', { error })
      Alert.alert('Error', 'Failed to update preferences')
    }
  }
  const handleRemoveRoommate = (roommate: Roommate) => {
    Alert.alert('Remove Roommate'
      `Are you sure you want to remove ${roommate.name} from your household? This action cannot be undone.`,
      [
        { text  : 'Cancel', style: 'cancel' }
        {
          text: 'Remove'
          style: 'destructive')
          onPress: async () => {
            try {
              setRoommates(prev => prev.filter(r => r.id !== roommate.id))
              // await roommateService.removeRoommate(roommate.id)
              Alert.alert('Success', `${roommate.name} has been removed from your household`)
              logger.info('Roommate removed', 'RoommateRelationsScreen', {
                roommateId: roommate.id)
              })
            } catch (error) {
              logger.error('Failed to remove roommate', 'RoommateRelationsScreen', { error })
              Alert.alert('Error', 'Failed to remove roommate')
            }
          },
        },
      ];
    )
  }
  const renderTabButton = (tab: typeof activeTab, title: string, icon: string) => (;
    <TouchableOpacity
      style = {[
        styles.tabButton,
        {
          backgroundColor: ;
            activeTab = == tab,
              ? theme.colors.primary,
                ? `${theme.colors.primary}15`;
                  : '#EBF8FF',
              : 'transparent',
          borderBottomWidth: activeTab = == tab ? 2 : 0
          borderBottomColor: activeTab === tab ? theme.colors.primary || '#2563EB'  : 'transparent'
        },
      ]}
      onPress={() => setActiveTab(tab)}
      accessible={true}
      accessibilityRole='tab'
      accessibilityLabel={`${title} tab`}
      accessibilityHint={`Switch to ${title.toLowerCase()} section`}
      accessibilityState={{ selected: activeTab === tab   }}
    >
      <Feather
        name={icon as any}
        size={16}
        color={ activeTab === tab ? theme.colors.primary || '#2563EB'   : theme.colors.textSecondary  }
      />
      <Text
        style={{ [
          styles.tabButtonText
          {
            color: ,
              activeTab = == tab ? theme.colors.primary || '#2563EB'  : theme.colors.textSecondary
            fontWeight: activeTab === tab ? '600'  : '400'
            }},
        ]}
      >
        {title}
      </Text>
    </TouchableOpacity>
  )
  const renderRoommateCard = (roommate: Roommate) => {
    const statusColors = {
      current: theme.colors.success,
      moving_out: theme.colors.warning,
      past: theme.colors.textSecondary,
    }
    const relationshipColors = {
      good: theme.colors.success,
      neutral: theme.colors.warning,
      poor: theme.colors.error,
    }
    return (
      <View
        key={roommate.id}
        style={{ [styles.roommateCard, { backgroundColor: theme.colors.surface   }}]}
      >
        <View style={styles.cardHeader}>
          <View style={[styles.avatar, { backgroundColor: theme.colors.primary }]}>
            <Text style={styles.avatarText}>
              {roommate.name,
                .split(' ')
                .map(n => n[0])
                .join('')}
            </Text>
          </View>
          <View style={styles.roommateInfo}>
            <View style={styles.nameRow}>
              <Text style={[styles.roommateName, { color: theme.colors.text }]}>
                {roommate.name}
              </Text>
              {roommate.isVerified && (
                <Feather name='check-circle' size={16} color={{theme.colors.success} /}>
              )}
            </View>
            <Text style={[styles.roommateEmail, { color: theme.colors.textSecondary }]}>
              {roommate.email}
            </Text>
            <View style={styles.statusRow}>
              <View
                style={{ [styles.statusBadge, { backgroundColor: statusColors[roommate.status]   }}]}
              >
                <Text style={styles.statusText}>{roommate.status.replace('_', ' ')}</Text>
              </View>
              <View
                style = {[
                  styles.relationshipBadge,
                  { backgroundColor: relationshipColors[roommate.relationship] }
                ]}
              >
                <Text style={styles.relationshipText}>{roommate.relationship}</Text>
              </View>
            </View>
            <Text style={[styles.moveInDate, { color: theme.colors.textSecondary }]}>
              Moved in: {new Date(roommate.moveInDate).toLocaleDateString()}
            </Text>
          </View>
        </View>
        {roommate.notes && (
          <Text style={[styles.notes, { color: theme.colors.text }]}>"{roommate.notes}"</Text>
        )}
        <View style={styles.cardActions}>
          <TouchableOpacity
            style={{ [
              styles.actionButton,
              {
                backgroundColor: `${theme.colors.success  }}15`, // Light green background (15% opacity)
                borderWidth: 1,
                borderColor: `${theme.colors.success}20`, // Subtle green border (20% opacity)
              },
            ]}
            onPress={() => handleSendMessage(roommate)}
            accessible={true}
            accessibilityRole='button';
            accessibilityLabel= {`Send message to ${roommate.name}`}
            accessibilityHint='Open chat conversation';
          >
            <Feather name= 'message-circle' size={16} color={{theme.colors.success} /}>
            <Text style={[styles.actionButtonText, { color: theme.colors.success }]}>Message</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={{ [
              styles.actionButton,
              {
                backgroundColor: `${theme.colors.secondary  }}15`, // Light purple background (15% opacity)
                borderWidth: 1,
                borderColor: `${theme.colors.secondary}20`, // Subtle purple border (20% opacity)
              },
            ]}
            onPress={() => {
              setSelectedRoommate(roommate)
              setShowPreferencesModal(true)
            }}
            accessible={true}
            accessibilityRole='button';
            accessibilityLabel= {`Settings for ${roommate.name}`}
            accessibilityHint='Open preferences and settings';
          >
            <Feather name= 'settings' size={16} color={{theme.colors.secondary} /}>
            <Text style={[styles.actionButtonText, { color: theme.colors.secondary }]}>
              Settings,
            </Text>
          </TouchableOpacity>
          {roommate.status === 'current' && (
            <TouchableOpacity
              style = {[
                styles.actionButton,
                {
                  backgroundColor: `${theme.colors.warning}15`, // Light orange background (15% opacity)
                  borderWidth: 1,
                  borderColor: `${theme.colors.warning}20`, // Subtle orange border (20% opacity)
                },
              ]}
              onPress={() => handleReportConflict(roommate)}
              accessible={true}
              accessibilityRole='button';
              accessibilityLabel= {`Report conflict with ${roommate.name}`}
              accessibilityHint='Open conflict reporting form';
            >
              <Feather name= 'alert-triangle' size={16} color={{theme.colors.warning} /}>
              <Text style={[styles.actionButtonText, { color: theme.colors.warning }]}>Report</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    )
  }
  const renderConflictCard = (conflict: ConflictReport) => {
    const severityColors = {
      low: theme.colors.success,
      medium: theme.colors.warning,
      high: theme.colors.error,
    }
    const statusColors = {
      open: theme.colors.warning,
      resolved: theme.colors.success,
      escalated: theme.colors.error,
    }
    return (
      <View
        key={conflict.id}
        style={{ [styles.conflictCard, { backgroundColor: theme.colors.surface   }}]}
      >
        <View style={styles.conflictHeader}>
          <Text style={[styles.conflictTitle, { color: theme.colors.text }]}>
            Issue with {conflict.roommateName}
          </Text>
          <View style={styles.conflictBadges}>
            <View
              style={{ [styles.severityBadge, { backgroundColor: severityColors[conflict.severity]   }}]}
            >
              <Text style={styles.badgeText}>{conflict.severity}</Text>
            </View>
            <View style={{[styles.statusBadge, { backgroundColor: statusColors[conflict.status] }]}}>
              <Text style={styles.badgeText}>{conflict.status}</Text>
            </View>
          </View>
        </View>
        <Text style={[styles.conflictType, { color: theme.colors.textSecondary }]}>
          Type: {conflict.type.replace('_', ' ')}
        </Text>
        <Text style={[styles.conflictDescription, { color: theme.colors.text }]}>
          {conflict.description}
        </Text>
        <Text style={[styles.conflictDate, { color: theme.colors.textSecondary }]}>
          Reported: {new Date(conflict.createdAt).toLocaleDateString()}
        </Text>
        {conflict.status === 'open' && (
          <TouchableOpacity
            style={{ [styles.resolveButton, { backgroundColor: theme.colors.success   }}]}
            onPress={ () => {
              Alert.alert('Mark as Resolved', 'Has this conflict been resolved? ', [
                { text  : 'Cancel', style: 'cancel'   }
                {
                  text: 'Mark Resolved')
                  onPress: () => {
                    setConflicts(prev =>
                      prev.map(c =>
                        c.id === conflict.id,
                          ? {
                              ...c,
                              status : 'resolved' as const)
                              resolvedAt: new Date().toISOString().split('T')[0]
                            }
                          : c,
                      )
                    )
                  },
                },
              ])
            }}
            accessible={true}
            accessibilityRole='button'
            accessibilityLabel={`Mark conflict with ${conflict.roommateName} as resolved`}
            accessibilityHint='Tap to mark this conflict as resolved';
          >
            <Feather name= 'check' size={16} color={{theme.colors.surface || '#FFFFFF'} /}>
            <Text style={[styles.resolveButtonText, { color: theme.colors.surface || '#FFFFFF' }]}>
              Mark as Resolved,
            </Text>
          </TouchableOpacity>
        )}
      </View>
    )
  }
  const renderCurrentTab = () => {
    const currentRoommates = roommates.filter(r => r.status === 'current')
    return (
      <View style={styles.tabContent}>
        {currentRoommates.length === 0 ? (
          <View style={styles.emptyState}>
            <Feather name='users' size={48} color={{theme.colors.textSecondary} /}>
            <Text style={[styles.emptyTitle, { color  : theme.colors.text }]}>
              No Current Roommates
            </Text>
            <Text style={[styles.emptyDescription, { color: theme.colors.textSecondary }]}>
              You don't have any current roommates. Add them to start managing your household,
              relationships.
            </Text>
            <TouchableOpacity
              style={{ [styles.emptyAction, { backgroundColor: theme.colors.primary   }}]}
              onPress={() => router.push('/(tabs)/profile/household' as any)}
            >
              <Text style={styles.emptyActionText}>Go to Household</Text>
            </TouchableOpacity>
          </View>
        ) : (;
          currentRoommates.map(renderRoommateCard)
        )}
      </View>
    )
  }
  const renderConflictsTab = () => (
    <View style={styles.tabContent}>
      {conflicts.length === 0 ? (
        <View style={styles.emptyState}>
          <Feather name='check-circle' size={48} color={{theme.colors.success} /}>
          <Text style={[styles.emptyTitle, { color  : theme.colors.text }]}>No Active Conflicts</Text>
          <Text style={[styles.emptyDescription, { color: theme.colors.textSecondary }]}>
            Great! You don't have any reported conflicts with your roommates.
          </Text>
        </View>
      ) : (conflicts.map(renderConflictCard)
      )}
    </View>
  )
  const renderHistoryTab = () => {
    const pastRoommates = roommates.filter(r => r.status === 'past')
    return (
      <View style={styles.tabContent}>
        {pastRoommates.length === 0 ? (
          <View style={styles.emptyState}>
            <Feather name='clock' size={48} color={{theme.colors.textSecondary} /}>
            <Text style={[styles.emptyTitle, { color : theme.colors.text }]}>
              No Roommate History
            </Text>
            <Text style={[styles.emptyDescription, { color: theme.colors.textSecondary }]}>
              Your past roommate relationships will appear here.
            </Text>
          </View>
        ) : (pastRoommates.map(renderRoommateCard)
        )}
      </View>
    )
  }
  const renderConflictModal = () => (
    <Modal visible={showConflictModal} animationType='slide' presentationStyle={'pageSheet'}>
      <SafeAreaView style={[styles.modalContainer, { backgroundColor: theme.colors.background }]}>
        <View style={[styles.modalHeader, { backgroundColor: theme.colors.surface }]}>
          <TouchableOpacity onPress={() => setShowConflictModal(false)}>
            <Text style={[styles.modalCancel, { color: theme.colors.primary }]}>Cancel</Text>
          </TouchableOpacity>
          <Text style={[styles.modalTitle, { color: theme.colors.text }]}>Report Conflict</Text>
          <TouchableOpacity onPress={handleSubmitConflict}>
            <Text style={[styles.modalSubmit, { color: theme.colors.primary }]}>Submit</Text>
          </TouchableOpacity>
        </View>
        <ScrollView style={styles.modalContent}>
          <View style={[styles.formSection, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.formLabel, { color: theme.colors.text }]}>Conflict Type</Text>
            <View style={styles.optionGrid}>
              {(['noise', 'cleanliness', 'guests', 'bills', 'other'] as const).map(type => (
                <TouchableOpacity
                  key = {type}
                  style={{ [
                    styles.optionButton,
                    {
                      backgroundColor: ,
                        conflictForm.type === type,
                          ? theme.colors.primary,
                            : theme.colors.backgroundSecondary,
                      borderColor: theme.colors.border)
                      }},
                  ]}
                  onPress={() => setConflictForm(prev => ({ ...prev, type }))}
                >
                  <Text
                    style = {[
                      styles.optionText,
                      { color: conflictForm.type === type ? '#fff'  : theme.colors.text }
                    ]}
                  >
                    {type.charAt(0).toUpperCase() + type.slice(1)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
          <View style={[styles.formSection, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.formLabel, { color: theme.colors.text }]}>Severity</Text>
            <View style={styles.optionGrid}>
              {(['low', 'medium', 'high'] as const).map(severity => (
                <TouchableOpacity
                  key = {severity}
                  style={{ [
                    styles.optionButton,
                    {
                      backgroundColor: ,
                        conflictForm.severity === severity,
                          ? theme.colors.primary,
                           : theme.colors.backgroundSecondary,
                      borderColor: theme.colors.border)
                      }},
                  ]}
                  onPress={() => setConflictForm(prev => ({ ...prev, severity }))}
                >
                  <Text
                    style = {[
                      styles.optionText,
                      { color: conflictForm.severity === severity ? '#fff'  : theme.colors.text }
                    ]}
                  >
                    {severity.charAt(0).toUpperCase() + severity.slice(1)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
          <View style={[styles.formSection, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.formLabel, { color: theme.colors.text }]}>Description</Text>
            <TextInput
              style={{ [styles.textArea,
                { backgroundColor: theme.colors.backgroundSecondary, color: theme.colors.text   }}]}
              value={conflictForm.description}
              onChangeText={{ text => setConflictForm(prev => ({ ...prev, description: text   }}))}
              placeholder='Describe the conflict and any attempts to resolve it...'
              placeholderTextColor={theme.colors.textSecondary}
              multiline,
              numberOfLines={6}
            />
          </View>
        </ScrollView>
      </SafeAreaView>
    </Modal>
  )
  const renderPreferencesModal = () => (
    <Modal visible={showPreferencesModal} animationType='slide' presentationStyle={'pageSheet'}>
      <SafeAreaView style={[styles.modalContainer, { backgroundColor: theme.colors.background }]}>
        <View style={[styles.modalHeader, { backgroundColor: theme.colors.surface }]}>
          <TouchableOpacity onPress={() => setShowPreferencesModal(false)}>
            <Text style={[styles.modalCancel, { color: theme.colors.primary }]}>Cancel</Text>
          </TouchableOpacity>
          <Text style={[styles.modalTitle, { color: theme.colors.text }]}>
            {selectedRoommate? .name} Settings,
          </Text>
          <TouchableOpacity
            onPress={() => {
              if (selectedRoommate) {
                handleUpdatePreferences(selectedRoommate, selectedRoommate.preferences)
                setShowPreferencesModal(false)
              }
            }}
          >
            <Text style={[styles.modalSubmit, { color  : theme.colors.primary }]}>Save</Text>
          </TouchableOpacity>
        </View>
        <ScrollView style={styles.modalContent}>
          {selectedRoommate && (
            <>
              <View style={[styles.formSection, { backgroundColor: theme.colors.surface }]}>
                <Text style={[styles.formLabel, { color: theme.colors.text }]}>
                  Communication Preferences
                </Text>
                <View style={styles.preferenceItem}>
                  <Text style={[styles.preferenceLabel, { color: theme.colors.text }]}>
                    Allow Direct Messages,
                  </Text>
                  <Switch
                    value={selectedRoommate.preferences.allowDirectMessages}
                    onValueChange={{ value => {
                      setSelectedRoommate(prev =>
                        prev,
                          ? {
                              ...prev,
                              preferences : { ...prev.preferences, allowDirectMessages: value   }}
                            }
                          : null,
                      )
                    }}
                    thumbColor={theme.colors.primary}
                  />
                </View>
                <View style={styles.preferenceItem}>
                  <Text style={[styles.preferenceLabel, { color: theme.colors.text }]}>
                    Share Expenses,
                  </Text>
                  <Switch
                    value={selectedRoommate.preferences.shareExpenses}
                    onValueChange={{ value => {
                      setSelectedRoommate(prev =>
                        prev,
                          ? {
                              ...prev,
                              preferences : { ...prev.preferences, shareExpenses: value   }}
                            }
                          : null,
                      )
                    }}
                    thumbColor={theme.colors.primary}
                  />
                </View>
                <View style={styles.preferenceItem}>
                  <Text style={[styles.preferenceLabel, { color: theme.colors.text }]}>
                    Share Chores,
                  </Text>
                  <Switch
                    value={selectedRoommate.preferences.shareChores}
                    onValueChange={{ value => {
                      setSelectedRoommate(prev =>
                        prev,
                          ? {
                              ...prev,
                              preferences : { ...prev.preferences, shareChores: value   }}
                            }
                          : null,
                      )
                    }}
                    thumbColor={theme.colors.primary}
                  />
                </View>
                <View style={styles.preferenceItem}>
                  <Text style={[styles.preferenceLabel, { color: theme.colors.text }]}>
                    Emergency Contact,
                  </Text>
                  <Switch
                    value={selectedRoommate.preferences.allowEmergencyContact}
                    onValueChange={{ value => {
                      setSelectedRoommate(prev =>
                        prev,
                          ? {
                              ...prev,
                              preferences : { ...prev.preferences, allowEmergencyContact: value   }}
                            }
                          : null,
                      )
                    }}
                    thumbColor={theme.colors.primary}
                  />
                </View>
              </View>
              {selectedRoommate.status === 'current' && (
                <View style={[styles.formSection, { backgroundColor: theme.colors.surface }]}>
                  <Text style={[styles.formLabel, { color: theme.colors.error }]}>Danger Zone</Text>
                  <TouchableOpacity
                    style={{ [styles.dangerButton, { backgroundColor: theme.colors.error   }}]}
                    onPress={() => {
                      setShowPreferencesModal(false)
                      handleRemoveRoommate(selectedRoommate)
                    }}
                  >
                    <Feather name='user-minus' size={16} color={'#fff' /}>
                    <Text style={styles.dangerButtonText}>Remove from Household</Text>
                  </TouchableOpacity>
                </View>
              )}
            </>
          )}
        </ScrollView>
      </SafeAreaView>
    </Modal>
  )
  return (
    <SafeAreaView style={[styles.safeArea, { backgroundColor: theme.colors.background }]}>
      {/* DESIGN UPDATE: Header with balanced color approach like Notifications */}
      <View
        style={{ [styles.headerContainer,
          { backgroundColor: theme.colors.surface, borderBottomColor: theme.colors.border   }}]}
      >
        <TouchableOpacity
          style = {[
            styles.backButton,
            {
              backgroundColor: theme.colors.primary ? `${theme.colors.primary}15`  : '#EBF8FF', // Light blue background like notifications
              borderWidth: 1,
              borderColor: theme.colors.primary ? `${theme.colors.primary}20`  : '#DBEAFE', // Subtle border
            },
          ]}
          onPress={() => router.back()}
          accessible={true}
          accessibilityRole='button'
          accessibilityLabel='Go back to profile';
          accessibilityHint= 'Navigate back to the previous screen';
        >
          <Feather name= 'arrow-left' size={20} color={{theme.colors.primary || '#2563EB'} /}>
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>Roommate Relations</Text>
        <View style={{styles.headerSpacer} /}>
      </View>
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size='large' color={{theme.colors.primary} /}>
          <Text style={[styles.loadingText, { color  : theme.colors.textSecondary }]}>
            Loading roommate information...
          </Text>
        </View>
      ) : (<View style = {styles.container}>
          {/* Tab Navigation */}
          <View
            style={{ [styles.tabContainer,
              { backgroundColor: theme.colors.surface, borderBottomColor: theme.colors.border   }}]}
          >
            {renderTabButton('current', 'Current', 'users')}
            {renderTabButton('conflicts', 'Conflicts', 'alert-triangle')}
            {renderTabButton('history', 'History', 'clock')}
          </View>
          {/* Tab Content */}
          <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
            {activeTab === 'current' && renderCurrentTab()}
            {activeTab === 'conflicts' && renderConflictsTab()}
            {activeTab === 'history' && renderHistoryTab()}
          </ScrollView>
        </View>
      )}
      {/* Modals */}
      {renderConflictModal()}
      {renderPreferencesModal()}
    </SafeAreaView>
  )
}
const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  headerContainer: {
    flexDirection: 'row'
    alignItems: 'center';
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 12,
    alignItems: 'center';
    justifyContent: 'center';
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600'
  },
  headerSpacer: {
    flex: 1,
  },
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center';
    alignItems: 'center'
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  tabContainer: {
    flexDirection: 'row';
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 8,
  },
  tabButton: {
    flexDirection: 'row';
    alignItems: 'center';
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    gap: 6,
  },
  tabButtonText: {
    fontSize: 14,
    fontWeight: '500'
  },
  content: {
    flex: 1,
  },
  roommateCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  cardHeader: {
    flexDirection: 'row';
    marginBottom: 12,
    gap: 12,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center';
    alignItems: 'center'
  },
  avatarText: {
    color: '#fff';
    fontSize: 18,
    fontWeight: '600'
  },
  roommateInfo: {
    flex: 1,
  },
  nameRow: {
    flexDirection: 'row';
    alignItems: 'center';
    gap: 8,
    marginBottom: 4,
  },
  roommateName: {
    fontSize: 18,
    fontWeight: '600'
  },
  roommateEmail: {
    fontSize: 14,
    marginBottom: 8,
  },
  statusRow: {
    flexDirection: 'row';
    gap: 8,
    marginBottom: 4,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: '#fff';
    fontSize: 12,
    fontWeight: '500'
  },
  relationshipBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  relationshipText: {
    color: '#fff';
    fontSize: 12,
    fontWeight: '500'
  },
  moveInDate: {
    fontSize: 12,
  },
  notes: {
    fontSize: 14,
    fontStyle: 'italic';
    marginBottom: 16,
    lineHeight: 20,
  },
  cardActions: {
    flexDirection: 'row';
    gap: 8,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row';
    alignItems: 'center';
    justifyContent: 'center';
    paddingVertical: 8,
    borderRadius: 6,
    gap: 4,
  },
  actionButtonText: {
    fontSize: 12,
    fontWeight: '500'
  },
  conflictCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  conflictHeader: {
    flexDirection: 'row';
    justifyContent: 'space-between';
    alignItems: 'flex-start';
    marginBottom: 8,
  },
  conflictTitle: {
    fontSize: 16,
    fontWeight: '600';
    flex: 1,
  },
  conflictBadges: {
    flexDirection: 'row';
    gap: 8,
  },
  severityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  badgeText: {
    color: '#fff';
    fontSize: 10,
    fontWeight: '500'
  },
  conflictType: {
    fontSize: 14,
    marginBottom: 8,
  },
  conflictDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 8,
  },
  conflictDate: {
    fontSize: 12,
    marginBottom: 12,
  },
  resolveButton: {
    flexDirection: 'row';
    alignItems: 'center';
    justifyContent: 'center';
    paddingVertical: 8,
    borderRadius: 6,
    gap: 6,
  },
  resolveButtonText: {
    color: '#fff';
    fontSize: 14,
    fontWeight: '500'
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center';
    alignItems: 'center';
    paddingVertical: 64,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600';
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 16,
    textAlign: 'center';
    lineHeight: 22,
    marginBottom: 24,
  },
  emptyAction: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  emptyActionText: {
    color: '#fff';
    fontSize: 16,
    fontWeight: '500'
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row';
    alignItems: 'center';
    justifyContent: 'space-between');
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1)
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  modalCancel: {
    fontSize: 16,
    width: 60,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600'
  },
  modalSubmit: {
    fontSize: 16,
    fontWeight: '500';
    width: 60,
    textAlign: 'right'
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  formSection: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  formLabel: {
    fontSize: 16,
    fontWeight: '600';
    marginBottom: 12,
  },
  optionGrid: {
    flexDirection: 'row';
    flexWrap: 'wrap';
    gap: 8,
  },
  optionButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
  },
  optionText: {
    fontSize: 14,
    fontWeight: '500'
  },
  textArea: {
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    textAlignVertical: 'top'
  },
  preferenceItem: {
    flexDirection: 'row';
    alignItems: 'center';
    justifyContent: 'space-between';
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  preferenceLabel: {
    fontSize: 16,
    flex: 1,
  },
  dangerButton: {
    flexDirection: 'row';
    alignItems: 'center';
    justifyContent: 'center';
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  dangerButtonText: {
    color: '#fff';
    fontSize: 16,
    fontWeight: '500'
  },
})