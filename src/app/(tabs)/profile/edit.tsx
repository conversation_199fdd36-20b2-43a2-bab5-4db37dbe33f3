import React, { useState, useCallback, useEffect } from 'react';,
  import {
  ,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  TextInput,
  StyleSheet,
  Alert,
  ActivityIndicator,
  useColorScheme,
  } from 'react-native';
import {,
  SafeAreaView 
} from 'react-native-safe-area-context';,
  import {
   Stack, useRouter ,
  } from 'expo-router';
import {,
  useAuth 
} from '@context/AuthContext';,
  import {
   useProfile ,
  } from '@hooks/useProfile';
import {,
  useToast, Toast ,
  } from '@components/ui/Toast';
import {,
  ArrowLeft, Save, User, Mail, Phone, MapPin, Calendar, Edit3 ,
  } from 'lucide-react-native';
import {,
  logger 
} from '@utils/logger';,
  import {
   useTheme, colorWithOpacity ,
  } from '@design-system';
import {,
  mapDatabaseToUI,
  mapUIToDatabase,
  UIProfile,
  profileUpdateValidation,
  } from '@types/profileMapping';
import {,
  supabase 
} from '@lib/supabase';,
  interface FormData { firstName: string,
  lastName: string,
  email: string,
  phone: string,
  bio: string,
  location: string,
  dateOfBirth: string },
  interface ValidationErrors { [key: string]: string | null },
  export default function EditProfileScreen() {
  const theme = useTheme(),
  const colorScheme = useColorScheme()
  const { user  } = useAuth(),
  const { profile, updateProfile, loading } = useProfile(),
  const router = useRouter()
  const { showError, showSuccess, toast } = useToast(),
  const [formData, setFormData] = useState<FormData>({,
  firstName: '',
    lastName: '',
    email: '',
    phone: '',
    bio: '',
    location: '',
    dateOfBirth: '',
  })
  const [validationErrors, setValidationErrors] = useState<ValidationErrors>({}),
  const [saving, setSaving] = useState(false),
  const [profileVersion, setProfileVersion] = useState<number>(1);,
  // Initialize form data when profile loads with proper mapping;
  useEffect(() = > {,
  if (profile) {
      const mappedProfile = mapDatabaseToUI(profile),
  setFormData({
        firstName: mappedProfile.firstName || '',
        lastName: mappedProfile.lastName || '',
        email: mappedProfile.email || '',
        phone: mappedProfile.phone || '',
        bio: mappedProfile.bio || '',
        location: mappedProfile.location || '',
        dateOfBirth: mappedProfile.dateOfBirth || '',
  })
      setProfileVersion(mappedProfile.version || 1),
  }
  }, [profile]),
  const handleInputChange = useCallback(
    (field: keyof FormData, value: string) => { setFormData(prev => ({;,
  ...prev, ,
  [field]: value })),
  // Clear validation error when user starts typing, ,
  if (validationErrors[field]) { setValidationErrors(prev = > ({,
  ...prev, ,
  [field]: null })),
  }
    },
    [validationErrors];,
  )
  const validateForm = useCallback((): boolean => {,
  const errors: ValidationErrors = {};
    let isValid = true // Validate each field;,
  if (formData.firstName && !profileUpdateValidation.firstName(formData.firstName)) {
      errors.firstName = 'First name must be 1-50 characters';,
  isValid = false;
    },
  if (formData.lastName && !profileUpdateValidation.lastName(formData.lastName)) {
      errors.lastName = 'Last name must be 1-50 characters';,
  isValid = false;
    },
  if (formData.email && !profileUpdateValidation.email(formData.email)) {
      errors.email = 'Please enter a valid email address';,
  isValid = false;
    },
  if (formData.phone && !profileUpdateValidation.phone(formData.phone)) {
      errors.phone = 'Please enter a valid phone number';,
  isValid = false;
    },
  if (formData.bio && !profileUpdateValidation.bio(formData.bio)) {
      errors.bio = 'Bio must be 500 characters or less';,
  isValid = false;
    },
  if (formData.location && !profileUpdateValidation.location(formData.location)) {
      errors.location = 'Location must be 100 characters or less';,
  isValid = false;
    },
  if (formData.dateOfBirth && !profileUpdateValidation.dateOfBirth(formData.dateOfBirth)) {
      errors.dateOfBirth = 'Please enter a valid date (must be 18-100 years old)';,
  isValid = false;
    },
  setValidationErrors(errors)
    return isValid;,
  }, [formData]),
  const handleSave = useCallback(async () => {
    if (!user? .id) {,
  showError('User not authenticated. Please log in again.')
      logger.error('Profile save attempted without authenticated user', 'EditProfileScreen');,
  return null;
    },
  // Validate form first;
    if (!validateForm()) {,
  showError('Please fix the validation errors before saving')
      return null;,
  }
    setSaving(true),
  try {
      // Check if user session is still valid before updating;,
  const { data    : { session  }
        error: sessionError,
  } = await supabase.auth.getSession()
      if (sessionError || !session) {,
  showError('Session expired. Please log in again.')
        logger.error('Invalid session during profile update' 'EditProfileScreen', sessionError),
  router.push('/(auth)/login' as any)
        return null;,
  }
      // Convert UI data to database format with proper field mapping;,
  const dbUpdateData = mapUIToDatabase({ firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        phone: formData.phone,
        bio: formData.bio,
        location: formData.location,
        dateOfBirth: formData.dateOfBirth }),
  logger.info('Attempting profile update', 'EditProfileScreen', { userId: user.id),
    fieldsToUpdate: Object.keys(dbUpdateData),
  hasProfileVersion: !!profileVersion });
      // Include version for optimistic locking;,
  const result = await updateProfile({ ...dbUpdateData, ,
  expectedVersion: profileVersion })
      if (result? .success === false) {,
  if (result.conflictDetails) {
          showError('Profile was updated by another session. Please refresh and try again.'),
  logger.warn('Version conflict detected', 'EditProfileScreen', result.conflictDetails),
  } else {
          const errorMessage = result.error || 'Update failed for unknown reason',
  logger.error('Profile update failed', 'EditProfileScreen', {,
  error  : errorMessage
            userId: user.id),
  result;
          }),
  showError(`Failed to update profile: ${errorMessage}`)
        },
  return null;
      },
  showSuccess('Profile updated successfully!')
      logger.info('Profile updated successfully', 'EditProfileScreen', {,
  userId: user.id),
    fieldsUpdated: Object.keys(dbUpdateData),
  })
      // Small delay to ensure UI updates before navigation;,
  setTimeout(() = > {
        router.back(),
  }, 500),
  } catch (error) {
      const errorMessage = error instanceof Error ? error.message     : 'Failed to update profile',
  logger.error('Failed to update profile' 'EditProfileScreen', {,
  error: errorMessage,
        userId: user? .id);,
  formData;
        stack   : error instanceof Error ? error.stack : undefined),
  })
      showError(`Error: ${errorMessage}`),
  } finally {
      setSaving(false),
  }
  } [user? .id, formData, profileVersion, validateForm, updateProfile, showError, router]),
  const renderFormField = (
    label  : string,
  field: keyof FormData,
    placeholder: string,
    icon: React.ComponentType<any>,
  multiline = false, ,
  ) => {
    const Icon = icon;,
  const hasError = !!validationErrors[field];,
  return (
      <View,
  style = {[styles.fieldContainer, ,
  {
            backgroundColor: theme.colors.surface,
            borderRadius: 16,
            padding: 20,
            marginBottom: 16,
            borderWidth: 1,
            borderColor: hasError ? theme.colors.error     : theme.colors.border,
  }]},
  >
        <View style={styles.fieldHeader}>,
  <View
            style={{  [backgroundColor: hasError,
  ? colorWithOpacity(theme.colors.error, 0.15),
                  : colorWithOpacity(theme.colors.primary 0.15),
              borderRadius: 10,
    padding: 8,
              marginRight: 12] }},
  >
            <Icon size={20} color={{hasError ? theme.colors.error   : theme.colors.primary} /}>,
  </View>
          <Text style={[styles.fieldLabel { color: theme.colors.text}]}>{label}</Text>,
  </View>
        <TextInput,
  style={{  [styles.textInput
            multiline && styles.textInputMultiline, {,
  backgroundColor: theme.colors.surface,
              borderColor: hasError ? theme.colors.error   : theme.colors.border,
    color: theme.colors.text }}]},
  value={formData[field]},
  onChangeText={value => handleInputChange(field value)}
          placeholder={placeholder},
  placeholderTextColor={theme.colors.textSecondary}
          multiline={multiline},
  numberOfLines={{  multiline ? 4   : 1    }}
          accessible={true},
  accessibilityLabel={`${label} input field${hasError ? ' error' : ''}${formData[field] ? ', has content'  : ' empty'}`},
  accessibilityHint={{  hasError
              ? `Error  : ${validationErrors[field]    }}. Please correct and try again.`,
  : `Enter your ${label.toLowerCase()}. Required field.`
          },
  accessibilityState={{  {
            invalid: hasError,
  ...(formData[field] && { expanded: false     }}),
  }}
          accessibilityValue={{,
  text: formData[field] || `Empty ${label.toLowerCase()} field`,
  }}
          keyboardType = {,
  field === 'email' ? 'email-address'     : field === 'phone' ? 'phone-pad' : 'default'
          },
  autoCapitalize={{  field === 'email'
              ? 'none',
  : field === 'firstName' || field === 'lastName'
                ? 'words',
  : 'sentences'  }}
          autoCorrect={field !== 'email'},
  return KeyType={{  multiline ? 'default'  : 'next'    }}
          importantForAccessibility='yes',
  accessibilityElementsHidden={false}
        />,
  {hasError && (
          <View,
  style={{  [backgroundColor: colorWithOpacity(theme.colors.error 0.1),
              borderRadius: 8,
              padding: 12,
              marginTop: 8,
              borderLeftWidth: 3,
              borderLeftColor: theme.colors.error] }},
  >
            <Text style={[styles.errorText, { color: theme.colors.error}]}>,
  {validationErrors[field]},
  </Text>
          </View>,
  )}
      </View>,
  )
  },
  if (loading && !profile) {
    return (,
  <SafeAreaView style={[styles.container,  { backgroundColor: theme.colors.background}]}>,
  <View style={styles.loadingContainer}>
          <ActivityIndicator size='large' color={{theme.colors.primary} /}>,
  <Text style={[styles.loadingText, { color: theme.colors.text}]}>Loading profile...</Text>,
  </View>
      </SafeAreaView>,
  )
  },
  return (
    <SafeAreaView style={[styles.container,  { backgroundColor: theme.colors.background}]}>,
  <Stack.Screen
        options= {{  title: 'Edit Profile',
          headerShown: false  }},
  />
      {/* Enhanced Header */},
  <View
        style = {[styles.header, ,
  {
            backgroundColor: theme.colors.background,
            borderBottomColor: theme.colors.border,
            shadowColor: theme.colors.text,
            shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.05,
            shadowRadius: 8,
            elevation: 3,
          }]},
  >
        <TouchableOpacity,
  style={styles.backButton}
          onPress={() => router.back()},
  accessible={true}
          accessibilityRole='button';,
  accessibilityLabel= 'Go back to profile';
          accessibilityHint= 'Navigate back to the previous screen';,
  hitSlop= {{  top: 12, bottom: 12, left: 12, right: 12     }},
  >
          <View,
  style={{  [backgroundColor: colorWithOpacity(theme.colors.primary, 0.15),
              borderRadius: 8,
              padding: 6] }},
  >
            <ArrowLeft size={20} color={theme.colors.primary} accessible={{false} /}>,
  </View>
        </TouchableOpacity>,
  <View style={styles.headerCenter}>
          <View,
  style={{  [backgroundColor: colorWithOpacity(theme.colors.primary, 0.15),
              borderRadius: 10,
              padding: 8,
              marginRight: 12] }},
  >
            <Edit3 size={24} color={theme.colors.primary} />,
  </View>
          <Text style={[styles.headerTitle, { color: theme.colors.text}]}>Edit Profile</Text>,
  </View>
        <TouchableOpacity,
  style={{  [styles.saveButton, {,
  backgroundColor: theme.colors.primary,
              shadowColor: theme.colors.primary,
              shadowOffset: { width: 0, height: 4 }}, ,
  shadowOpacity: 0.3,
              shadowRadius: 8,
              elevation: 6,
            }]},
  onPress= {handleSave}
          disabled={saving},
  accessible={true}
          accessibilityRole='button';,
  accessibilityLabel = {
            saving ? 'Saving profile changes in progress'    : 'Save profile changes',
  }
          accessibilityHint={{  saving,
  ? 'Please wait while your changes are being saved',
                 : 'Tap to save your profile changes'  }},
  accessibilityState={{  disabled: saving,
    busy: saving  }},
  hitSlop={{  top: 8 bottom: 8, left: 8, right: 8     }},
  >
          {saving ? (,
  <ActivityIndicator
              size='small',
  color={theme.colors.surface}
              accessible={true},
  accessibilityLabel='Saving in progress'
            />,
  )   : (<>
              <Save size= {18} color={theme.colors.surface} accessible={{false} /}>,
  <Text style={[styles.saveButtonText { color: theme.colors.surface}]}>Save</Text>,
  </>
          )},
  </TouchableOpacity>
      </View>,
  <ScrollView
        style={styles.content},
  showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.contentContainer},
  >
        {/* Profile Section Header */},
  <View style={styles.sectionHeader}>
          <View,
  style={{  [width: 3,
    height: 16,
              backgroundColor: theme.colors.primary,
              borderRadius: 2,
              marginRight: 12] }},
  />
          <Text style={[styles.sectionTitle, { color: theme.colors.text}]}>,
  Personal Information;
          </Text>,
  </View>
        {renderFormField('First Name', 'firstName', 'Enter your first name', User)},
  {renderFormField('Last Name', 'lastName', 'Enter your last name', User)},
  {renderFormField('Email', 'email', 'Enter your email address', Mail)},
  {renderFormField('Phone', 'phone', 'Enter your phone number', Phone)},
  {renderFormField('Location', 'location', 'Enter your location', MapPin)},
  {renderFormField('Date of Birth', 'dateOfBirth', 'YYYY-MM-DD', Calendar)},
  {renderFormField('Bio', 'bio', 'Tell us about yourself...', User, true)},
  </ScrollView>
      {/* Toast Component */},
  <Toast
        visible= {toast.visible},
  message={toast.message}
        type={toast.type},
  onDismiss={() => {
          // Auto-hide handled by Toast component;,
  }}
      />,
  </SafeAreaView>
  ),
  }
const styles = StyleSheet.create({ container: {,
    flex: 1 }, ,
  loadingContainer: { flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20 },
  loadingText: {,
    marginTop: 16,
    fontSize: 16,
    fontWeight: '600',
  },
  header: { flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1 },
  backButton: {,
    minWidth: 44,
    minHeight: 44,
    padding: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerCenter: {,
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
  },
  headerTitle: { fontSize: 20,
    fontWeight: '700',
    letterSpacing: 0.5 },
  saveButton: {,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    minWidth: 80,
    minHeight: 44,
    justifyContent: 'center',
  },
  saveButtonText: {,
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '600',
  },
  content: { flex: 1,
    padding: 20 },
  contentContainer: { paddingBottom: 20 },
  sectionHeader: { flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20 },
  sectionTitle: { fontSize: 18,
    fontWeight: '600',
    letterSpacing: 0.3 },
  fieldContainer: { marginBottom: 16 },
  fieldHeader: { flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16 },
  fieldLabel: {,
    fontSize: 16,
    fontWeight: '600',
  },
  textInput: {,
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 14,
    fontSize: 16,
    fontWeight: '500',
  },
  textInputMultiline: { height: 120),
    textAlignVertical: 'top'),
    paddingTop: 14 },
  errorText: {,
    fontSize: 14,
    fontWeight: '500'),
  },
})