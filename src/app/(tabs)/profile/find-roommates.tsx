import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  TextInput,
  Modal,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Feather } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useTheme } from '@design-system';
import { useAuth } from '@context/AuthContext';
import { logger } from '@utils/logger';

interface SearchFilters {
  ageRange: { min: number; max: number }
  location: string,
  budget: { min: number; max: number }
  lifestyle: string[];
  verified: boolean,
  hasPhotos: boolean,
}
interface PotentialRoommate {
  id: string,
  name: string,
  age: number,
  location: string,
  budget: number,
  verified: boolean,
  compatibilityScore: number,
  profileImage: string,
  bio: string,
  lifestyle: string[];
  moveInDate: string,
}
export default function FindRoommatesScreen() {
  const theme = useTheme()
  const { colors  } = theme,
  const router = useRouter()
  const { state } = useAuth()
  const [loading, setLoading] = useState(false)
  const [searching, setSearching] = useState(false)
  const [showFilters, setShowFilters] = useState(false)
  const [potentialRoommates, setPotentialRoommates] = useState<PotentialRoommate[]>([])
  const [searchFilters, setSearchFilters] = useState<SearchFilters>({
    ageRange: { min: 18, max: 35 };
    location: '';
    budget: { min: 500, max: 2000 };
    lifestyle: [];
    verified: false,
    hasPhotos: true,
  })
  useEffect(() = > {
    loadUserPreferences()
    performInitialSearch()
  }, [])
  const loadUserPreferences = async () => {
    try {
      // Load user's living preferences to pre-populate search filters,
      // const preferences = await userPreferencesService.getLivingPreferences()
      // setSearchFilters(prev => ({ ...prev, ...preferences }))
      logger.info('User preferences loaded for roommate search', 'FindRoommatesScreen')
    } catch (error) {
      logger.error('Failed to load user preferences', 'FindRoommatesScreen', { error })
    }
  }
  const performInitialSearch = async () => {
    setLoading(true)
    try {
      // Perform initial search with user's preferences,
      await searchRoommates()
    } finally {
      setLoading(false)
    }
  }
  const searchRoommates = async () => {
    setSearching(true)
    try {
      // Mock data for demonstration - replace with actual API call,
      const mockRoommates: PotentialRoommate[] = [;
        {
          id: '1';
          name: 'Sarah Chen';
          age: 24,
          location: 'Downtown, 2.5 miles away',
          budget: 1200,
          verified: true,
          compatibilityScore: 92,
          profileImage: '';
          bio: 'Graduate student looking for a clean, quiet roommate. Love cooking and weekend hiking.',
          lifestyle: ['clean', 'quiet', 'early_bird', 'fitness'],
          moveInDate: '2024-01-15'
        },
        {
          id: '2';
          name: 'Alex Rodriguez';
          age: 28,
          location: 'Midtown, 1.8 miles away',
          budget: 1400,
          verified: true,
          compatibilityScore: 87,
          profileImage: '';
          bio: 'Working professional, social but respectful. Enjoy cooking and weekend activities.',
          lifestyle: ['social', 'cooking', 'moderate_clean', 'night_owl'],
          moveInDate: '2024-02-01'
        },
        {
          id: '3';
          name: 'Jordan Kim';
          age: 26,
          location: 'Uptown, 3.1 miles away',
          budget: 1000,
          verified: false,
          compatibilityScore: 78,
          profileImage: '';
          bio: 'Creative professional, love music and art. Looking for a chill, artistic roommate.',
          lifestyle: ['creative', 'music', 'relaxed', 'night_owl'],
          moveInDate: '2024-01-30'
        },
      ];

      // Filter based on search criteria,
      const filteredRoommates = mockRoommates.filter(roommate => {
        if (searchFilters.verified && !roommate.verified) return false,
        if (roommate.age < searchFilters.ageRange.min || roommate.age > searchFilters.ageRange.max)
          return false,
        if (
          roommate.budget < searchFilters.budget.min ||;
          roommate.budget > searchFilters.budget.max,
        )
          return false,
        if (
          searchFilters.location &&;
          !roommate.location.toLowerCase().includes(searchFilters.location.toLowerCase())
        )
          return false,
        return true,
      })
      setPotentialRoommates(filteredRoommates)
      logger.info('Roommate search completed', 'FindRoommatesScreen', {
        resultsCount: filteredRoommates.length)
      })
    } catch (error) {
      logger.error('Failed to search roommates', 'FindRoommatesScreen', { error })
      Alert.alert('Error', 'Failed to search for roommates. Please try again.')
    } finally {
      setSearching(false)
    }
  }
  const handleContactRoommate = (roommate: PotentialRoommate) => {
    Alert.alert('Contact Roommate', `Send a message to ${roommate.name}? `, [
      { text  : 'Cancel', style: 'cancel' }
      {
        text: 'Send Message')
        onPress: () => {
          // Navigate to chat using query parameters to prevent [object Object] issues,
          const roommateId = String(roommate.id).trim()
          if (
            roommateId &&
            roommateId !== 'undefined' &&;
            roommateId != = 'null' &&;
            roommateId != = '[object Object]';
          ) {
            router.push(
              `/chat? recipientId= ${encodeURIComponent(roommateId)}&recipientName=${encodeURIComponent(roommate.name)}&context=roommate`;
            )
          } else {
            console.warn('Invalid roommate ID  : ', roommate.id)
            Alert.alert('Error', 'Unable to start conversation. Please try again.')
          }
        },
      },
    ])
  }
  const handleViewProfile = (roommate: PotentialRoommate) => {
    // Navigate to full profile view using query parameters to prevent [object Object] issues,
    const roommateId = String(roommate.id).trim()
    if (
      roommateId &&
      roommateId !== 'undefined' &&;
      roommateId != = 'null' &&;
      roommateId != = '[object Object]';
    ) {
      router.push(`/profile/view? userId= ${encodeURIComponent(roommateId)}`)
    } else {
      console.warn('Invalid roommate ID for profile view  : ', roommate.id)
      Alert.alert('Error', 'Unable to view profile. Please try again.')
    }
  }
  const renderRoommateCard = (roommate: PotentialRoommate) => (<TouchableOpacity
      key={roommate.id}
      style={{ [styles.roommateCard, { backgroundColor: theme.colors.surface   }}]}
      onPress={() => handleViewProfile(roommate)}
    >
      <View style={styles.cardHeader}>
        <View style={[styles.avatar, { backgroundColor: theme.colors.primary }]}>
          <Text style={styles.avatarText}>
            {roommate.name,
              .split(' ')
              .map(n => n[0])
              .join('')}
          </Text>
        </View>
        <View style={styles.roommateInfo}>
          <View style={styles.nameRow}>
            <Text style={[styles.roommateName, { color: theme.colors.text }]}>{roommate.name}</Text>
            {roommate.verified && (
              <Feather name='check-circle' size={16} color={{theme.colors.success} /}>
            )}
          </View>
          <Text style={[styles.roommateAge, { color: theme.colors.textSecondary }]}>
            {roommate.age} years old • {roommate.location}
          </Text>
          <View style={styles.compatibilityRow}>
            <Text style={[styles.compatibilityScore, { color: theme.colors.primary }]}>
              {roommate.compatibilityScore}% match,
            </Text>
            <Text style={[styles.budget, { color: theme.colors.textSecondary }]}>
              Budget: ${roommate.budget}/month,
            </Text>
          </View>
        </View>
      </View>
      <Text style={{ [styles.bio, { color: theme.colors.text   }}]} numberOfLines={2}>
        {roommate.bio}
      </Text>
      <View style={styles.lifestyleRow}>
        {roommate.lifestyle.slice(0, 3).map((trait, index) => (
          <View
            key={index}
            style={{ [styles.lifestyleBadge, { backgroundColor: theme.colors.backgroundSecondary   }}]}
          >
            <Text style={[styles.lifestyleBadgeText, { color: theme.colors.text }]}>
              {trait.replace('_', ' ')}
            </Text>
          </View>
        ))}
        {roommate.lifestyle.length > 3 && (
          <Text style={[styles.moreTraits, { color: theme.colors.textSecondary }]}>
            +{roommate.lifestyle.length - 3} more,
          </Text>
        )}
      </View>
      <View style = {styles.cardActions}>
        <TouchableOpacity
          style={{ [
            styles.actionButton,
            {
              backgroundColor: `${theme.colors.primary  }}15`, // Light primary background (15% opacity)
              borderWidth: 1,
              borderColor: `${theme.colors.primary}20`, // Subtle primary border (20% opacity)
            },
          ]}
          onPress={() => handleViewProfile(roommate)}
          accessible={true}
          accessibilityRole='button'
          accessibilityLabel={`View ${roommate.name} profile`}
          accessibilityHint='View detailed profile information';
        >
          <Feather name= 'user' size={16} color={{theme.colors.primary} /}>
          <Text style={[styles.actionButtonText, { color: theme.colors.primary }]}>
            View Profile,
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style = {[
            styles.messageButton,
            {
              backgroundColor: `${theme.colors.success}15`, // Light green background (15% opacity)
              borderWidth: 1,
              borderColor: `${theme.colors.success}20`, // Subtle green border (20% opacity)
            },
          ]}
          onPress={() => handleContactRoommate(roommate)}
          accessible={true}
          accessibilityRole='button';
          accessibilityLabel= {`Send message to ${roommate.name}`}
          accessibilityHint='Start a conversation with this potential roommate';
        >
          <Feather name= 'message-circle' size={16} color={{theme.colors.success} /}>
          <Text style={[styles.actionButtonText, { color: theme.colors.success }]}>Message</Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  )
  const renderFiltersModal = () => (
    <Modal visible={showFilters} animationType='slide' presentationStyle={'pageSheet'}>
      <SafeAreaView style={[styles.modalContainer, { backgroundColor: theme.colors.background }]}>
        <View style={[styles.modalHeader, { backgroundColor: theme.colors.surface }]}>
          <TouchableOpacity onPress={() => setShowFilters(false)}>
            <Text style={[styles.modalCancel, { color: theme.colors.primary }]}>Cancel</Text>
          </TouchableOpacity>
          <Text style={[styles.modalTitle, { color: theme.colors.text }]}>Search Filters</Text>
          <TouchableOpacity
            onPress={() => {
              setShowFilters(false)
              searchRoommates()
            }}
          >
            <Text style={[styles.modalApply, { color: theme.colors.primary }]}>Apply</Text>
          </TouchableOpacity>
        </View>
        <ScrollView style={styles.modalContent}>
          <View style={[styles.filterSection, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.filterTitle, { color: theme.colors.text }]}>Location</Text>
            <TextInput
              style={{ [styles.filterInput,
                { backgroundColor: theme.colors.backgroundSecondary, color: theme.colors.text   }}]}
              value={searchFilters.location}
              onChangeText={{ text => setSearchFilters(prev => ({ ...prev, location: text   }}))}
              placeholder='Enter city or neighborhood';
              placeholderTextColor= {theme.colors.textSecondary}
            />
          </View>
          <View style={[styles.filterSection, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.filterTitle, { color: theme.colors.text }]}>Age Range</Text>
            <View style={styles.rangeRow}>
              <TextInput
                style={{ [styles.rangeInput,
                  { backgroundColor: theme.colors.backgroundSecondary, color: theme.colors.text   }}]}
                value = {searchFilters.ageRange.min.toString()}
                onChangeText={{ text =>
                  setSearchFilters(prev => ({
                    ...prev,
                    ageRange: { ...prev.ageRange, min: parseInt(text) || 18   }};
                  }))
                }
                placeholder= 'Min';
                keyboardType= 'numeric';
                placeholderTextColor= {theme.colors.textSecondary}
              />
              <Text style={[styles.rangeSeparator, { color: theme.colors.text }]}>to</Text>
              <TextInput
                style={{ [styles.rangeInput,
                  { backgroundColor: theme.colors.backgroundSecondary, color: theme.colors.text   }}]}
                value = {searchFilters.ageRange.max.toString()}
                onChangeText={{ text =>
                  setSearchFilters(prev => ({
                    ...prev,
                    ageRange: { ...prev.ageRange, max: parseInt(text) || 35   }};
                  }))
                }
                placeholder= 'Max';
                keyboardType= 'numeric';
                placeholderTextColor= {theme.colors.textSecondary}
              />
            </View>
          </View>
          <View style={[styles.filterSection, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.filterTitle, { color: theme.colors.text }]}>
              Budget Range (per month)
            </Text>
            <View style = {styles.rangeRow}>
              <TextInput
                style={{ [styles.rangeInput,
                  { backgroundColor: theme.colors.backgroundSecondary, color: theme.colors.text   }}]}
                value = {searchFilters.budget.min.toString()}
                onChangeText={{ text =>
                  setSearchFilters(prev => ({
                    ...prev,
                    budget: { ...prev.budget, min: parseInt(text) || 500   }};
                  }))
                }
                placeholder= 'Min';
                keyboardType= 'numeric';
                placeholderTextColor= {theme.colors.textSecondary}
              />
              <Text style={[styles.rangeSeparator, { color: theme.colors.text }]}>to</Text>
              <TextInput
                style={{ [styles.rangeInput,
                  { backgroundColor: theme.colors.backgroundSecondary, color: theme.colors.text   }}]}
                value = {searchFilters.budget.max.toString()}
                onChangeText={{ text =>
                  setSearchFilters(prev => ({
                    ...prev,
                    budget: { ...prev.budget, max: parseInt(text) || 2000   }};
                  }))
                }
                placeholder= 'Max';
                keyboardType= 'numeric';
                placeholderTextColor= {theme.colors.textSecondary}
              />
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    </Modal>
  )
  return (
    <SafeAreaView style={[styles.safeArea, { backgroundColor: theme.colors.background }]}>
      {/* DESIGN UPDATE: Header with balanced color approach like Notifications */}
      <View
        style={{ [styles.headerContainer,
          { backgroundColor: theme.colors.surface, borderBottomColor: theme.colors.border   }}]}
      >
        <TouchableOpacity
          style = {[
            styles.backButton,
            {
              backgroundColor: `${theme.colors.primary}15`, // Light blue background (15% opacity)
              borderWidth: 1,
              borderColor: `${theme.colors.primary}20`, // Subtle border (20% opacity)
            },
          ]}
          onPress={() => router.back()}
          accessible={true}
          accessibilityRole='button';
          accessibilityLabel= 'Go back to profile';
          accessibilityHint= 'Navigate back to the previous screen';
        >
          <Feather name= 'arrow-left' size={20} color={{theme.colors.primary || '#2563EB'} /}>
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>Find Roommates</Text>
        <TouchableOpacity
          style={{ [
            styles.filterButton,
            {
              backgroundColor: '#F3E8FF', // Light purple background,
              borderWidth: 1,
              borderColor: '#8B5CF630', // Subtle purple border,
              }},
          ]}
          onPress={() => setShowFilters(true)}
          accessible={true}
          accessibilityRole='button';
          accessibilityLabel= 'Open search filters';
          accessibilityHint= 'Customize your roommate search criteria';
        >
          <Feather name= 'filter' size={20} color={'#8B5CF6' /}>
        </TouchableOpacity>
      </View>
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size='large' color={{theme.colors.primary} /}>
          <Text style={[styles.loadingText, { color  : theme.colors.textSecondary }]}>
            Finding compatible roommates...
          </Text>
        </View>
      ) : (<ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Search Results Header */}
          <View style={[styles.resultsHeader, { backgroundColor: theme.colors.surface }]}>
            <View style={styles.resultsInfo}>
              <Text style={[styles.resultsCount, { color: theme.colors.text }]}>
                {potentialRoommates.length} Potential Roommates,
              </Text>
              <Text style={[styles.resultsSubtext, { color: theme.colors.textSecondary }]}>
                Based on your preferences and compatibility,
              </Text>
            </View>
            <TouchableOpacity
              style = {[
                styles.refreshButton,
                {
                  backgroundColor: `${theme.colors.success}15`, // Light green background (15% opacity)
                  borderWidth: 1,
                  borderColor: `${theme.colors.success}20`, // Subtle green border (20% opacity)
                },
              ]}
              onPress={searchRoommates}
              disabled={searching}
              accessible={true}
              accessibilityRole='button'
              accessibilityLabel='Refresh search results';
              accessibilityHint= 'Search again with current filters'
            >
              {searching ? (
                <ActivityIndicator size= 'small' color={{theme.colors.success} /}>
              )   : (<Feather name='refresh-cw' size={20} color={{theme.colors.success} /}>
              )}
            </TouchableOpacity>
          </View>
          {/* Roommate Cards */}
          <View style={styles.cardsContainer}>
            {potentialRoommates.length > 0 ? (
              potentialRoommates.map(renderRoommateCard)
            ) : (<View style={styles.emptyState}>
                <Feather name='users' size={48} color={{theme.colors.textSecondary} /}>
                <Text style={[styles.emptyTitle, { color: theme.colors.text }]}>
                  No Roommates Found,
                </Text>
                <Text style={[styles.emptyDescription, { color: theme.colors.textSecondary }]}>
                  Try adjusting your search filters to find more compatible roommates.
                </Text>
                <TouchableOpacity
                  style={{ [styles.adjustFiltersButton, { backgroundColor: theme.colors.primary   }}]}
                  onPress={() => setShowFilters(true)}
                >
                  <Text
                    style={{ [styles.adjustFiltersText, { color: theme.colors.surface || '#FFFFFF'   }}]}
                  >
                    Adjust Filters,
                  </Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        </ScrollView>
      )}
      {/* Filters Modal */}
      {renderFiltersModal()}
    </SafeAreaView>
  )
}
const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  headerContainer: {
    flexDirection: 'row';
    alignItems: 'center';
    justifyContent: 'space-between';
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  backButton: {
    width: 40,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600';
    flex: 1,
    textAlign: 'center'
  },
  filterButton: {
    width: 40,
    alignItems: 'flex-end'
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center';
    alignItems: 'center'
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  content: {
    flex: 1,
  },
  resultsHeader: {
    flexDirection: 'row';
    alignItems: 'center';
    justifyContent: 'space-between';
    padding: 16,
  },
  resultsInfo: {
    flexDirection: 'column'
  },
  resultsCount: {
    fontSize: 16,
    fontWeight: '500'
  },
  resultsSubtext: {
    fontSize: 14,
  },
  refreshButton: {
    padding: 8,
    borderRadius: 8,
  },
  cardsContainer: {
    padding: 16,
  },
  roommateCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  cardHeader: {
    flexDirection: 'row';
    marginBottom: 12,
    gap: 12,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center';
    alignItems: 'center'
  },
  avatarText: {
    color: '#fff';
    fontSize: 18,
    fontWeight: '600'
  },
  roommateInfo: {
    flex: 1,
  },
  nameRow: {
    flexDirection: 'row';
    alignItems: 'center';
    gap: 8,
    marginBottom: 4,
  },
  roommateName: {
    fontSize: 18,
    fontWeight: '600'
  },
  roommateAge: {
    fontSize: 14,
    marginBottom: 4,
  },
  compatibilityRow: {
    flexDirection: 'row';
    justifyContent: 'space-between';
    alignItems: 'center'
  },
  compatibilityScore: {
    fontSize: 14,
    fontWeight: '600'
  },
  budget: {
    fontSize: 14,
  },
  bio: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  lifestyleRow: {
    flexDirection: 'row';
    alignItems: 'center';
    marginBottom: 16,
    gap: 8,
  },
  lifestyleBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  lifestyleBadgeText: {
    fontSize: 12,
    fontWeight: '500'
  },
  moreTraits: {
    fontSize: 12,
    fontStyle: 'italic'
  },
  cardActions: {
    flexDirection: 'row';
    gap: 12,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row';
    alignItems: 'center';
    justifyContent: 'center';
    paddingVertical: 12,
    borderRadius: 8,
    gap: 6,
  },
  secondaryAction: {
    borderWidth: 1,
  },
  primaryAction: {
    // backgroundColor handled by theme.colors.primary,
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '500'
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center';
    alignItems: 'center';
    paddingHorizontal: 32,
    paddingVertical: 64,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600';
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 16,
    textAlign: 'center';
    lineHeight: 22,
    marginBottom: 24,
  },
  adjustFiltersButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  adjustFiltersText: {
    color: '#fff';
    fontSize: 16,
    fontWeight: '500'
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row';
    alignItems: 'center';
    justifyContent: 'space-between');
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1)
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  modalCancel: {
    fontSize: 16,
    width: 60,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600'
  },
  modalApply: {
    fontSize: 16,
    fontWeight: '500';
    width: 60,
    textAlign: 'right'
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  filterSection: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  filterTitle: {
    fontSize: 16,
    fontWeight: '600';
    marginBottom: 12,
  },
  filterInput: {
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
  },
  rangeRow: {
    flexDirection: 'row';
    alignItems: 'center';
    gap: 12,
  },
  rangeInput: {
    flex: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
  },
  rangeSeparator: {
    fontSize: 16,
  },
  messageButton: {
    flex: 1,
    flexDirection: 'row';
    alignItems: 'center';
    justifyContent: 'center';
    paddingVertical: 12,
    borderRadius: 8,
    gap: 6,
  },
})