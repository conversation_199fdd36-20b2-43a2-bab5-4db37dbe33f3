import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, StyleSheet, RefreshControl } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { TrendingDown, Calculator, DollarSign, Users, Calendar, Award } from 'lucide-react-native';
import { useTheme } from '@design-system';
import { Card } from '@components/ui';
import { Button } from '@design-system';

import { CostSavingsCard } from '@components/verification';
import { SimpleAuthContext } from '@context/SimpleAuthContext';
import { supabase } from '@lib/supabase';
import { costSavingsBreakdown } from '@config/simplifiedAuthConfig';

interface CostSavingsAnalytics {
  user_savings: {
    total_saved: number,
    monthly_savings: number,
    verification_cycle_savings: number,
    services_used: string[],
  }
  platform_savings: {
    total_users_saved: number,
    total_platform_savings: number,
    monthly_platform_savings: number,
    average_savings_per_user: number,
  }
  comparison_data: {
    traditional_cost: number,
    our_cost: number,
    savings_percentage: number,
    services_replaced: number,
  }
  verification_history: {
    date: string,
    service: string,
    traditional_cost: number,
    our_cost: number,
    savings: number,
  }[];
}
export default function CostSavingsAnalyticsScreen() {
  const theme = useTheme();
  const { user, profile } = React.useContext(SimpleAuthContext);
  const [analytics, setAnalytics] = useState<CostSavingsAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const loadAnalytics = async () => {
    if (!user?.id) return null;

    try {
      // Fetch user's cost savings data;
      const { data: userSavingsData, error: userError } = await supabase;
        .from('cost_savings_analytics')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (userError) throw userError;

      // Fetch platform-wide savings data;
      const { data: platformData, error: platformError } = await supabase;
        .from('cost_savings_analytics')
        .select('total_saved, user_id')
        .not('user_id', 'is', null);

      if (platformError) throw platformError;

      // Calculate analytics;
      const userTotalSaved =;
        userSavingsData?.reduce((sum, item) => sum + (item.total_saved || 0), 0) || 0;
      const platformTotalSaved =;
        platformData?.reduce((sum, item) => sum + (item.total_saved || 0), 0) || 0;
      const uniqueUsers = new Set(platformData?.map(item => item.user_id)).size;

      const analyticsData: CostSavingsAnalytics = {
        user_savings: {
          total_saved: userTotalSaved,
          monthly_savings: userTotalSaved, // Assuming monthly cycle for now;
          verification_cycle_savings: 57.05, // From our config;
          services_used: [,
            'Identity Verification',
            'Background Check',
            'Reference Verification',
            'Phone Verification',
          ],
        },
        platform_savings: {
          total_users_saved: uniqueUsers,
          total_platform_savings: platformTotalSaved,
          monthly_platform_savings: platformTotalSaved, // Simplified for demo;
          average_savings_per_user: uniqueUsers > 0 ? platformTotalSaved / uniqueUsers : 57.05,
        },
        comparison_data: {
          traditional_cost: 57.05,
          our_cost: 0,
          savings_percentage: 100,
          services_replaced: costSavingsBreakdown.length,
        },
        verification_history: ,
          userSavingsData?.map(item => ({
            date: new Date(item.created_at).toLocaleDateString(),
            service: item.verification_method || 'Manual Review',
            traditional_cost: ,
              item.identity_verification_cost_saved +;
              item.background_check_cost_saved +;
              item.reference_verification_cost_saved,
            our_cost: 0,
            savings: item.total_saved,
          })) || [],
      }
      setAnalytics(analyticsData);
    } catch (error) {
      console.error('Error loading cost savings analytics:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }
  useEffect(() => {
    loadAnalytics();
  }, [user?.id]);

  const onRefresh = () => {
    setRefreshing(true);
    loadAnalytics();
  }
  const renderUserSavingsCard = () => (
    <Card style={{[styles.savingsCard, { backgroundColor: theme.colors.surface }]}}>
      <View style={styles.cardHeader}>
        <View style={{[styles.iconContainer, { backgroundColor: theme.colors.success + '15' }]}}>
          <DollarSign size={24} color={{theme.colors.success} /}>
        </View>
        <View style={styles.headerText}>
          <Text style={{[styles.cardTitle, { color: theme.colors.text }]}}>Your Savings</Text>
          <Text style={{[styles.cardSubtitle, { color: theme.colors.textSecondary }]}}>
            Personal verification cost savings;
          </Text>
        </View>
        <Text style={{[styles.totalAmount, { color: theme.colors.success }]}}>
          ${analytics?.user_savings.total_saved.toFixed(2) || '57.05'}
        </Text>
      </View>
      <View style={styles.savingsBreakdown}>
        <View style={styles.savingsItem}>
          <Text style={{[styles.savingsLabel, { color: theme.colors.textSecondary }]}}>
            Per Verification Cycle;
          </Text>
          <Text style={{[styles.savingsValue, { color: theme.colors.success }]}}>$57.05 saved</Text>
        </View>
        <View style={styles.savingsItem}>
          <Text style={{[styles.savingsLabel, { color: theme.colors.textSecondary }]}}>
            Services Included;
          </Text>
          <Text style={{[styles.savingsValue, { color: theme.colors.text }]}}>
            {analytics?.user_savings.services_used.length || 4} services;
          </Text>
        </View>
        <View style={styles.savingsItem}>
          <Text style={{[styles.savingsLabel, { color: theme.colors.textSecondary }]}}>
            Savings Method;
          </Text>
          <Text style={{[styles.savingsValue, { color: theme.colors.text }]}}>
            Manual + Public APIs;
          </Text>
        </View>
      </View>
    </Card>
  );

  const renderPlatformImpactCard = () => (
    <Card style={{[styles.impactCard, { backgroundColor: theme.colors.surface }]}}>
      <View style={styles.cardHeader}>
        <View style={{[styles.iconContainer, { backgroundColor: theme.colors.primary + '15' }]}}>
          <Users size={24} color={{theme.colors.primary} /}>
        </View>
        <View style={styles.headerText}>
          <Text style={{[styles.cardTitle, { color: theme.colors.text }]}}>Platform Impact</Text>
          <Text style={{[styles.cardSubtitle, { color: theme.colors.textSecondary }]}}>
            Community cost savings;
          </Text>
        </View>
      </View>
      <View style={styles.impactStats}>
        <View style={styles.statCard}>
          <Text style={{[styles.statValue, { color: theme.colors.primary }]}}>
            {analytics?.platform_savings.total_users_saved || '0'}
          </Text>
          <Text style={{[styles.statLabel, { color: theme.colors.textSecondary }]}}>
            Users Helped;
          </Text>
        </View>
        <View style={styles.statCard}>
          <Text style={{[styles.statValue, { color: theme.colors.success }]}}>
            ${analytics?.platform_savings.total_platform_savings.toLocaleString() || '0'}
          </Text>
          <Text style={{[styles.statLabel, { color: theme.colors.textSecondary }]}}>Total Saved</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={{[styles.statValue, { color: theme.colors.warning }]}}>
            ${analytics?.platform_savings.average_savings_per_user.toFixed(0) || '57'}
          </Text>
          <Text style={{[styles.statLabel, { color: theme.colors.textSecondary }]}}>
            Avg Per User;
          </Text>
        </View>
      </View>
      <View style={{[styles.highlight, { backgroundColor: theme.colors.primary + '10' }]}}>
        <Text style={{[styles.highlightText, { color: theme.colors.primary }]}}>
          🌟 Together, we're making verification accessible for everyone!;
        </Text>
      </View>
    </Card>
  );

  const renderComparisonCard = () => (
    <Card style={{[styles.comparisonCard, { backgroundColor: theme.colors.surface }]}}>
      <View style={styles.cardHeader}>
        <View style={{[styles.iconContainer, { backgroundColor: theme.colors.warning + '15' }]}}>
          <Calculator size={24} color={{theme.colors.warning} /}>
        </View>
        <View style={styles.headerText}>
          <Text style={{[styles.cardTitle, { color: theme.colors.text }]}}>Cost Comparison</Text>
          <Text style={{[styles.cardSubtitle, { color: theme.colors.textSecondary }]}}>
            Traditional vs Our Method;
          </Text>
        </View>
      </View>
      <View style={styles.comparisonGrid}>
        <View style={styles.comparisonItem}>
          <Text style={{[styles.comparisonLabel, { color: theme.colors.textSecondary }]}}>
            Traditional Services;
          </Text>
          <Text style={{[styles.traditionalCost, { color: theme.colors.error }]}}>
            $57.05 per user;
          </Text>
        </View>
        <View style={styles.comparisonArrow}>
          <Text style={{[styles.arrow, { color: theme.colors.textSecondary }]}}>VS</Text>
        </View>
        <View style={styles.comparisonItem}>
          <Text style={{[styles.comparisonLabel, { color: theme.colors.textSecondary }]}}>
            Our Method;
          </Text>
          <Text style={{[styles.ourCost, { color: theme.colors.success }]}}>$0.00 per user</Text>
        </View>
      </View>
      <View style={{[styles.savingsHighlight, { backgroundColor: theme.colors.success + '15' }]}}>
        <Text style={{[styles.savingsPercentage, { color: theme.colors.success }]}}>
          100% Cost Reduction;
        </Text>
        <Text style={{[styles.savingsDescription, { color: theme.colors.textSecondary }]}}>
          Same security, zero cost through manual verification + public APIs;
        </Text>
      </View>
    </Card>
  );

  const renderHistoryCard = () => (
    <Card style={{[styles.historyCard, { backgroundColor: theme.colors.surface }]}}>
      <View style={styles.cardHeader}>
        <View style={{[styles.iconContainer, { backgroundColor: theme.colors.accent + '15' }]}}>
          <Calendar size={24} color={{theme.colors.accent} /}>
        </View>
        <View style={styles.headerText}>
          <Text style={{[styles.cardTitle, { color: theme.colors.text }]}}>Verification History</Text>
          <Text style={{[styles.cardSubtitle, { color: theme.colors.textSecondary }]}}>
            Your cost savings timeline;
          </Text>
        </View>
      </View>
      {analytics?.verification_history.length ? (
        <View style={styles.historyList}>
          {analytics.verification_history.map((item, index) => (
            <View
              key={index}
              style={[styles.historyItem, { borderBottomColor: theme.colors.border }]}
            >
              <View style={styles.historyDate}>
                <Text style={{[styles.dateText, { color: theme.colors.textSecondary }]}}>
                  {item.date}
                </Text>
              </View>
              <View style={styles.historyDetails}>
                <Text style={{[styles.serviceText, { color: theme.colors.text }]}}>
                  {item.service}
                </Text>
                <View style={styles.costDetails}>
                  <Text style={{[styles.traditionalPrice, { color: theme.colors.textSecondary }]}}>
                    ${item.traditional_cost} →;
                  </Text>
                  <Text style={{[styles.finalPrice, { color: theme.colors.success }]}}>
                    FREE (${item.savings} saved);
                  </Text>
                </View>
              </View>
            </View>
          ))}
        </View>
      ) : (,
        <View style={styles.emptyHistory}>
          <Text style={{[styles.emptyText, { color: theme.colors.textSecondary }]}}>
            Complete your verification to see savings history;
          </Text>
        </View>
      )}
    </Card>
  );

  return (
    <SafeAreaView style={{[styles.container, { backgroundColor: theme.colors.background }]}}>
      <View style={styles.header}>
        <Text style={{[styles.headerTitle, { color: theme.colors.text }]}}>
          Cost Savings Analytics;
        </Text>
        <Text style={{[styles.headerSubtitle, { color: theme.colors.textSecondary }]}}>
          Your personal verification savings;
        </Text>
      </View>
      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={{onRefresh} /}>
      >
        {/* Main Cost Savings Display */}
        <CostSavingsCard showBreakdown={{true} /}>
        {/* User Savings */}
        {renderUserSavingsCard()}
        {/* Platform Impact */}
        {renderPlatformImpactCard()}
        {/* Cost Comparison */}
        {renderComparisonCard()}
        {/* Verification History */}
        {renderHistoryCard()}
        {/* Achievement Badge */}
        <Card style={{[styles.achievementCard, { backgroundColor: theme.colors.surface }]}}>
          <View style={styles.achievementContent}>
            <Award size={32} color={{theme.colors.warning} /}>
            <View style={styles.achievementText}>
              <Text style={{[styles.achievementTitle, { color: theme.colors.text }]}}>
                Cost Saver Badge;
              </Text>
              <Text style={{[styles.achievementDescription, { color: theme.colors.textSecondary }]}}>
                You're helping make verification affordable for everyone!;
              </Text>
            </View>
          </View>
        </Card>
        {/* Action Button */}
        <Button
          title='Share Your Savings';
          onPress={() => {
            {{/* Handle sharing */}}
          }}
          style={styles.shareButton}
          leftIcon={<TrendingDown size={20} color={{theme.colors.white} /}>
        />
      </ScrollView>
    </SafeAreaView>
  );
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
  },
  headerSubtitle: {
    fontSize: 14,
    marginTop: 4,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  savingsCard: {
    marginBottom: 16,
    padding: 20,
  },
  impactCard: {
    marginBottom: 16,
    padding: 20,
  },
  comparisonCard: {
    marginBottom: 16,
    padding: 20,
  },
  historyCard: {
    marginBottom: 16,
    padding: 20,
  },
  achievementCard: {
    marginBottom: 16,
    padding: 20,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  headerText: {
    flex: 1,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '700',
  },
  cardSubtitle: {
    fontSize: 14,
    marginTop: 2,
  },
  totalAmount: {
    fontSize: 24,
    fontWeight: '700',
  },
  savingsBreakdown: {
    gap: 12,
  },
  savingsItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  savingsLabel: {
    fontSize: 14,
  },
  savingsValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  impactStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  statCard: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    fontSize: 20,
    fontWeight: '700',
  },
  statLabel: {
    fontSize: 12,
    marginTop: 4,
    textAlign: 'center',
  },
  highlight: {
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  highlightText: {
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
  },
  comparisonGrid: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  comparisonItem: {
    flex: 1,
    alignItems: 'center',
  },
  comparisonArrow: {
    marginHorizontal: 16,
  },
  comparisonLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  traditionalCost: {
    fontSize: 16,
    fontWeight: '600',
  },
  ourCost: {
    fontSize: 16,
    fontWeight: '600',
  },
  arrow: {
    fontSize: 16,
    fontWeight: '600',
  },
  savingsHighlight: {
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  savingsPercentage: {
    fontSize: 18,
    fontWeight: '700',
  },
  savingsDescription: {
    fontSize: 12,
    marginTop: 4,
    textAlign: 'center',
  },
  historyList: {
    gap: 12,
  },
  historyItem: {
    flexDirection: 'row',
    paddingBottom: 12,
    borderBottomWidth: 1,
  },
  historyDate: {
    width: 80,
  },
  dateText: {
    fontSize: 12,
  },
  historyDetails: {
    flex: 1,
  },
  serviceText: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 2,
  },
  costDetails: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  traditionalPrice: {
    fontSize: 12,
    textDecorationLine: 'line-through',
    marginRight: 4,
  },
  finalPrice: {
    fontSize: 12,
    fontWeight: '600',
  },
  emptyHistory: {
    padding: 20,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 14,
    textAlign: 'center',
  },
  achievementContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  achievementText: {
    marginLeft: 16,
    flex: 1,
  },
  achievementTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  achievementDescription: {
    fontSize: 12,
    marginTop: 2,
  },
  shareButton: {
    marginTop: 8,
    marginBottom: 20,
  },
});
