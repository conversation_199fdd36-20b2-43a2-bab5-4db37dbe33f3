import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  TextInput,
  TouchableOpacity,
  Modal,
  Dimensions,
  useColorScheme,
} from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from '@context/AuthContext';
import { supabase } from '@utils/supabaseUtils';
import { Button } from '@design-system';
import { useToast } from '@components/ui/Toast';
import { logger } from '@utils/logger';
import { useTheme } from '@design-system';
import { colorWithOpacity } from '@design-system';
import { Big5TestCard, MBTITestCard, PersonalityOverview } from '@components/personality';
import {
  Brain,
  Users,
  MessageCircle,
  Shield,
  Heart,
  Zap,
  Info,
  ChevronDown,
  ChevronUp,
  Check,
  Star,
  Target,
  Lightbulb,
  Coffee,
  Moon,
  Sun,
  Volume2,
  VolumeX,
  Home,
  User<PERSON><PERSON><PERSON>,
  ArrowL<PERSON>t,
  Save,
  User,
} from 'lucide-react-native';

const { width  } = Dimensions.get('window')
// React Native compatible color system using centralized utility,
// Enhanced personality data structure,
interface PersonalityProfile {
  // Big 5 Personality Traits (0-100 scale) big5: { openness: number; conscientiousness: number; extraversion: number; agreeableness: number; neuroticism: number }; // MBTI Type mbti: { type: string; // e.g., "ENFP" dimensions: { ei: 'E' | 'I'; // Extraversion/Introversion sn: 'S' | 'N'; // Sensing/Intuition tf: 'T' | 'F'; // Thinking/Feeling jp: 'J' | 'P'; // Judging/Perceiving }; }; // Communication Style communication: { style: 'direct' | 'diplomatic' | 'supportive' | 'analytical'; preferences: string[] }; // Conflict Resolution Style conflict_resolution: { style: 'collaborative' | 'competitive' | 'accommodating' | 'avoiding' | 'compromising'; approach: string }; // Lifestyle Compatibility lifestyle: { energy_level: 'low' | 'moderate' | 'high'; social_battery: 'introvert' | 'ambivert' | 'extrovert'; routine_preference: 'structured' | 'flexible' | 'spontaneous'; decision_making: 'quick' | 'deliberate' | 'collaborative' }; // Free-form responses (existing questions) responses: Record<string, string>
}
// Big 5 Personality Test Questions,
const BIG5_QUESTIONS = [// Openness { trait: 'openness', question: 'I enjoy trying new experiences and activities', reverse: false }, { trait: 'openness', question: 'I prefer familiar routines over new experiences', reverse: true }, { trait: 'openness', question: 'I am curious about many different things', reverse: false }, // Conscientiousness { trait: 'conscientiousness', question: 'I am always prepared and organized', reverse: false }, { trait: 'conscientiousness', question: 'I often leave tasks until the last minute', reverse: true, }, { trait: 'conscientiousness', question: 'I pay attention to details', reverse: false }, // Extraversion { trait: 'extraversion', question: 'I feel energized when around other people', reverse: false }, { trait: 'extraversion', question: 'I prefer quiet, solitary activities', reverse: true }, { trait: 'extraversion', question: 'I enjoy being the center of attention', reverse: false }, // Agreeableness { trait: 'agreeableness', question: "I try to be considerate of others' feelings", reverse: false, }, { trait: 'agreeableness', question: 'I tend to be critical of others', reverse: true }, { trait: 'agreeableness', question: 'I am generally trusting of people', reverse: false }, // Neuroticism { trait: 'neuroticism', question: 'I often feel stressed or anxious', reverse: false }, { trait: 'neuroticism', question: 'I remain calm under pressure', reverse: true }, { trait: 'neuroticism', question: 'I worry about things frequently', reverse: false }];

// MBTI Questions,
const MBTI_QUESTIONS = [{
    dimension: 'ei';
    question: 'How do you prefer to recharge your energy? ';
    options  : [
      {
        value: 'E'
        label: 'Spending time with others';
        description: 'Social activities energize me'
      },
      { value: 'I', label: 'Spending time alone', description: 'Solitude helps me recharge' }],
  },
  {
    dimension: 'sn';
    question: 'How do you prefer to take in information? ';
    options  : [
      {
        value: 'S'
        label: 'Focus on facts and details';
        description: 'I trust concrete information'
      },
      {
        value: 'N';
        label: 'Focus on patterns and possibilities';
        description: 'I see the big picture'
      },
    ],
  },
  {
    dimension: 'tf';
    question: 'How do you prefer to make decisions? ';
    options  : [
      {
        value: 'T'
        label: 'Based on logic and analysis';
        description: 'Objective reasoning guides me'
      },
      {
        value: 'F';
        label: 'Based on values and feelings';
        description: 'Personal values are important'
      },
    ],
  },
  {
    dimension: 'jp';
    question: 'How do you prefer to organize your life? ';
    options  : [{ value: 'J', label: 'Planned and organized', description: 'I like structure and closure' }
      { value: 'P', label: 'Flexible and adaptable', description: 'I prefer to keep options open' }],
  },
];

// Communication Styles,
const COMMUNICATION_STYLES = [
  {
    value: 'direct';
    label: 'Direct';
    description: 'I say what I mean clearly and concisely';
    icon: Target,
  },
  {
    value: 'diplomatic';
    label: 'Diplomatic';
    description: "I consider others' feelings when communicating";
    icon: Heart,
  },
  {
    value: 'supportive';
    label: 'Supportive';
    description: 'I focus on encouraging and helping others';
    icon: Users,
  },
  {
    value: 'analytical';
    label: 'Analytical';
    description: 'I prefer detailed, fact-based discussions',
    icon: Brain,
  },
];

// Conflict Resolution Styles,
const CONFLICT_STYLES = [
  {
    value: 'collaborative';
    label: 'Collaborative';
    description: 'Work together to find win-win solutions';
    icon: Users,
  },
  {
    value: 'competitive';
    label: 'Assertive';
    description: 'Stand firm on important issues';
    icon: Zap,
  },
  {
    value: 'accommodating';
    label: 'Accommodating';
    description: "Prioritize harmony and others' needs";
    icon: Heart,
  },
  {
    value: 'avoiding';
    label: 'Avoiding';
    description: 'Prefer to step back and cool down first';
    icon: Shield,
  },
  {
    value: 'compromising';
    label: 'Compromising';
    description: 'Find middle ground that works for everyone';
    icon: Target,
  },
];

// Lifestyle Preferences,
const LIFESTYLE_OPTIONS = {
  energy_level: [;
    {
      value: 'low';
      label: 'Low Energy';
      description: 'I prefer calm, relaxed activities',
      icon: Moon,
    },
    {
      value: 'moderate';
      label: 'Moderate Energy';
      description: 'I balance active and quiet time';
      icon: Coffee,
    },
    {
      value: 'high';
      label: 'High Energy';
      description: 'I love active, dynamic activities',
      icon: Sun,
    },
  ],
  social_battery: [;
    {
      value: 'introvert';
      label: 'Introvert';
      description: 'I need regular alone time to recharge';
      icon: Home,
    },
    {
      value: 'ambivert';
      label: 'Ambivert';
      description: 'I balance social and alone time';
      icon: UserCheck,
    },
    {
      value: 'extrovert';
      label: 'Extrovert';
      description: 'I gain energy from social interactions';
      icon: Users,
    },
  ],
  routine_preference: [;
    {
      value: 'structured';
      label: 'Structured';
      description: 'I thrive with consistent routines';
      icon: Target,
    },
    {
      value: 'flexible';
      label: 'Flexible';
      description: 'I adapt easily to changes';
      icon: Lightbulb,
    },
    {
      value: 'spontaneous';
      label: 'Spontaneous';
      description: 'I love unexpected adventures';
      icon: Star,
    },
  ],
  decision_making: [;
    { value: 'quick', label: 'Quick Decider', description: 'I make decisions rapidly', icon: Zap };
    {
      value: 'deliberate';
      label: 'Deliberate';
      description: 'I take time to consider options';
      icon: Brain,
    },
    {
      value: 'collaborative';
      label: 'Collaborative';
      description: 'I prefer group decision-making';
      icon: Users,
    },
  ],
}
// Original personality questions (for backward compatibility)
const PERSONALITY_QUESTIONS = [
  {
    id: 'morning_person';
    question: 'Are you a morning person or a night owl? ';
    placeholder  : 'E.g., "I\'m definitely a night owl. I\'m most productive after 8pm."',
  },
  {
    id: 'clean_habits'
    question: 'How would you describe your cleaning habits? '
    placeholder : 'E.g., "I like to keep common areas tidy and clean my space weekly."',
  },
  {
    id: 'social_style'
    question: 'Describe your social style and preferences.';
    placeholder: 'E.g., "I\'m an ambivert - I enjoy socializing but also need alone time."',
  },
  {
    id: 'cooking';
    question: 'Do you enjoy cooking? What are your favorite meals to make?';
    placeholder  : 'E.g., "I love cooking Italian food and baking on weekends."',
  },
  {
    id: 'guests'
    question: 'How often do you have guests over? '
    placeholder : 'E.g., "I occasionally have friends over for dinner, maybe once a month."',
  },
  {
    id: 'schedule'
    question: 'Describe your typical weekly schedule.';
    placeholder: ;
      'E.g., "I work 9-5 on weekdays, go to the gym 3 times a week, and like to relax on weekends."',
  },
];

export default function EnhancedPersonalityScreen() {
  const { user  } = useAuth()
  const colorScheme = useColorScheme()
  const theme = useTheme()
  const router = useRouter()
  const { toast } = useToast()
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [currentSection, setCurrentSection] = useState<;
    'overview' | 'big5' | 'mbti' | 'communication' | 'conflict' | 'lifestyle' | 'questions'
  >('overview'); // Personality profile state const [personalityProfile, setPersonalityProfile] = useState<PersonalityProfile>({ big5: { openness: 50, conscientiousness: 50, extraversion: 50, agreeableness: 50, neuroticism: 50, }, mbti: { type: '', dimensions: { ei: 'E', sn: 'S', tf: 'T', jp: 'J', }, }, communication: { style: 'direct', preferences: [], }, conflict_resolution: { style: 'collaborative', approach: '', }, lifestyle: { energy_level: 'moderate', social_battery: 'ambivert', routine_preference: 'flexible', decision_making: 'deliberate', }, responses: {}, }); // Big 5 test state const [big5Responses, setBig5Responses] = useState<Record<number, number>>({}); useEffect(() => { if (user? .id) { fetchPersonalityData() } }, [user?.id]); const fetchPersonalityData = useCallback(async () => { if (!user?.id) return null; try { setLoading(true); // Fetch personality data from user_personality_profiles table const { data  : personalityData, error: personalityError  } = await supabase.from('user_personality_profiles') .select('profile_data, completion_percentage') .eq('user_id', user.id).maybeSingle() if (personalityError && personalityError.code !== 'PGRST116') { throw personalityError } // Fetch user responses from user_personality_responses table const { data: responsesData, error: responsesError } = await supabase.from('user_personality_responses') .select($1).eq('user_id', user.id) if (responsesError && responsesError.code !== 'PGRST116') { throw responsesError } // Merge data let profile = { ...personalityProfile }; if (personalityData? .profile_data) { profile = { ...profile, ...personalityData.profile_data }; } if (responsesData && responsesData.length > 0) { const responseMap = responsesData.reduce((acc, item) => { acc[item.question_id] = item.response_value; return acc }, {}); profile.responses = responseMap; } setPersonalityProfile(profile); // Update MBTI type based on dimensions updateMBTIType(profile.mbti.dimensions); } catch (error) { logger.error( 'Error fetching personality data', 'EnhancedPersonalityScreen.fetchPersonalityData', error as Error ); toast?.show('Failed to load personality data', 'error') } finally { setLoading(false) } }, [user?.id, toast]); const updateMBTIType = (dimensions  : PersonalityProfile['mbti']['dimensions']) => { const type = `${dimensions.ei}${dimensions.sn}${dimensions.tf}${dimensions.jp}` setPersonalityProfile(prev => ({ ...prev, mbti: { ...prev.mbti, type, dimensions, }, })) }; const calculateBig5FromResponses = () => { const traitScores = { openness: [], conscientiousness: [], extraversion: [], agreeableness: [], neuroticism: [], }; BIG5_QUESTIONS.forEach((question, index) => { const response = big5Responses[index]; if (response !== undefined) { const score = question.reverse ? 6 - response   : response traitScores[question.trait].push(score) } }) const big5 = {}; Object.keys(traitScores).forEach(trait => { const scores = traitScores[trait]; if (scores.length > 0) { const average = scores.reduce((sum, score) => sum + score, 0) / scores.length; big5[trait] = Math.round((average / 5) * 100) } else { big5[trait] = 50; // Default to neutral } }); setPersonalityProfile(prev => ({ ...prev, big5: big5 as PersonalityProfile['big5'], })); }; const handleSave = useCallback(async () => { if (!user? .id) return null; try { setSaving(true); // Calculate completion percentage const completionScore = [ personalityProfile.mbti.type.length === 4 ? 25   : 0, personalityProfile.communication.style ? 25 : 0, personalityProfile.conflict_resolution.style ? 25 : 0, Object.values(personalityProfile.responses).filter(r => r.trim()).length >= 3 ? 25 : 0, ].reduce((sum, score) => sum + score, 0) // Save personality data to user_personality_profiles table const { error: upsertError  } = await supabase.from('user_personality_profiles') .upsert({ user_id: user.id, profile_data: personalityProfile, completion_percentage: completionScore, updated_at: new Date().toISOString(), }, { onConflict: '$1' }) if (upsertError) { throw upsertError } // Save individual responses to user_personality_responses table if (Object.keys(personalityProfile.responses).length > 0) { // Delete existing responses await supabase.from('user_personality_responses').delete().eq('user_id', user.id); // Insert new responses const responsesToInsert = Object.entries(personalityProfile.responses) .filter(([_, response]) => response.trim() !== '') .map(([questionId, response]) => ({ user_id: user.id, question_id: questionId, response_value: response.trim(), created_at: new Date().toISOString(), updated_at: new Date().toISOString(), })); if (responsesToInsert.length > 0) { const { error: responsesError  } = await supabase.from('user_personality_responses').insert(responsesToInsert); if (responsesError) { throw responsesError } } } toast? .show('Personality profile saved successfully!', 'success'); logger.info( 'Enhanced personality profile saved successfully', 'EnhancedPersonalityScreen.handleSave' ); } catch (error) { logger.error( 'Error saving personality profile', 'EnhancedPersonalityScreen.handleSave', error as Error ); toast?.show('Failed to save personality profile', 'error') } finally { setSaving(false) } }, [user?.id, personalityProfile, toast]); const getPersonalityInsights = () => { const { big5, mbti, communication, conflict_resolution, lifestyle  } = personalityProfile; const completionScore = [ mbti.type.length === 4 ? 25   : 0, communication.style ? 25 : 0, conflict_resolution.style ? 25 : 0, Object.values(personalityProfile.responses).filter(r => r.trim()).length >= 3 ? 25 : 0, ].reduce((sum, score) => sum + score, 0) const dominantTrait = Object.entries(big5).reduce( (max, [trait, score]) => (score > max.score ? { trait, score }  : max), { trait: '', score: 0 } ) return { completionScore, dominantTrait: dominantTrait.trait, mbtiType: mbti.type, communicationStyle: communication.style, conflictStyle: conflict_resolution.style, compatibilityFactors: [ lifestyle.social_battery, lifestyle.routine_preference, communication.style, conflict_resolution.style, ].filter(Boolean).length, } }; const insights = getPersonalityInsights(); if (loading) { return ( <SafeAreaView style={{ [styles.container, { backgroundColor: theme.colors.background   }}]} edges={{ ['top'] }}> <Stack.Screen options={{ title: 'Personality Assessment', headerShadowVisible: false, headerStyle: { backgroundColor: theme.colors.background }, }} /> <View style={styles.loadingContainer}> <ActivityIndicator size="large" color={{theme.colors.primary} /}> <Text style={[styles.loadingText, { color: theme.colors.text }]}> Loading personality data... </Text> </View> </SafeAreaView> ); } const renderOverview = () => ( <View style={styles.sectionContent}> {/* Header */} <View style={styles.header}> <Text style={[styles.title, { color: theme.colors.text }]}>Enhanced Personality Assessment</Text> <Text style={[styles.description, { color: theme.colors.textSecondary }]}> Complete your personality profile to find the most compatible roommates. </Text> </View> {/* Insights Card */} <View style={[styles.insightsCard, { backgroundColor: theme.colors.surface }]}> <View style={styles.insightsHeader}> <Brain size={20} color={{theme.colors.primary} /}> <Text style={[styles.insightsTitle, { color: theme.colors.text }]}>Personality Insights</Text> </View> <View style={styles.insightsContent}> <View style={styles.insightItem}> <Text style={[styles.insightValue, { color: theme.colors.primary }]}> {insights.completionScore}% </Text> <Text style={[styles.insightLabel, { color: theme.colors.textSecondary }]}> Profile Complete </Text> </View> <View style={styles.insightItem}> <Text style={[styles.insightValue, { color: theme.colors.primary }]}> {insights.mbtiType || 'TBD'} </Text> <Text style={[styles.insightLabel, { color: theme.colors.textSecondary }]}>MBTI Type</Text> </View> <View style={styles.insightItem}> <Text style={[styles.insightValue, { color: theme.colors.primary }]}> {insights.compatibilityFactors}/4 </Text> <Text style={[styles.insightLabel, { color: theme.colors.textSecondary }]}> Match Factors </Text> </View> </View> </View> {/* Assessment Sections */} <View style={styles.sectionsContainer}> <TouchableOpacity style={{ [styles.sectionCard, { backgroundColor: theme.colors.surface   }}]} onPress={() => setCurrentSection('big5')} accessibilityRole="button" accessibilityLabel="Take Big 5 personality test" > <Brain size={24} color={{theme.colors.primary} /}> <View style={styles.sectionInfo}> <Text style={[styles.sectionTitle, { color: theme.colors.text }]}> Big 5 Personality Test </Text> <Text style={[styles.sectionDescription, { color: theme.colors.textSecondary }]}> Discover your core personality traits </Text> </View> <ChevronDown size={20} color={{theme.colors.textSecondary} /}> </TouchableOpacity> <TouchableOpacity style={{ [styles.sectionCard, { backgroundColor: theme.colors.surface   }}]} onPress={() => setCurrentSection('mbti')} accessibilityRole="button" accessibilityLabel="Take MBTI assessment" > <Star size={24} color={{theme.colors.primary} /}> <View style={styles.sectionInfo}> <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>MBTI Assessment</Text> <Text style={[styles.sectionDescription, { color: theme.colors.textSecondary }]}> Find your personality type </Text> </View> <ChevronDown size={20} color={{theme.colors.textSecondary} /}> </TouchableOpacity> <TouchableOpacity style={{ [styles.sectionCard, { backgroundColor: theme.colors.surface   }}]} onPress={() => setCurrentSection('communication')} accessibilityRole="button" accessibilityLabel="Set communication preferences" > <MessageCircle size={24} color={{theme.colors.primary} /}> <View style={styles.sectionInfo}> <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Communication Style</Text> <Text style={[styles.sectionDescription, { color: theme.colors.textSecondary }]}> How you prefer to communicate </Text> </View> <ChevronDown size={20} color={{theme.colors.textSecondary} /}> </TouchableOpacity> <TouchableOpacity style={{ [styles.sectionCard, { backgroundColor: theme.colors.surface   }}]} onPress={() => setCurrentSection('conflict')} accessibilityRole="button" accessibilityLabel="Set conflict resolution style" > <Shield size={24} color={{theme.colors.primary} /}> <View style={styles.sectionInfo}> <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Conflict Resolution</Text> <Text style={[styles.sectionDescription, { color: theme.colors.textSecondary }]}> How you handle disagreements </Text> </View> <ChevronDown size={20} color={{theme.colors.textSecondary} /}> </TouchableOpacity> <TouchableOpacity style={{ [styles.sectionCard, { backgroundColor: theme.colors.surface   }}]} onPress={() => setCurrentSection('lifestyle')} accessibilityRole="button" accessibilityLabel="Set lifestyle preferences" > <Home size={24} color={{theme.colors.primary} /}> <View style={styles.sectionInfo}> <Text style={[styles.sectionTitle, { color: theme.colors.text }]}> Lifestyle Compatibility </Text> <Text style={[styles.sectionDescription, { color: theme.colors.textSecondary }]}> Your daily life preferences </Text> </View> <ChevronDown size={20} color={{theme.colors.textSecondary} /}> </TouchableOpacity> <TouchableOpacity style={{ [styles.sectionCard, { backgroundColor: theme.colors.surface   }}]} onPress={() => setCurrentSection('questions')} accessibilityRole="button" accessibilityLabel="Answer personality questions" > <Lightbulb size={24} color={{theme.colors.primary} /}> <View style={styles.sectionInfo}> <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Personal Questions</Text> <Text style={[styles.sectionDescription, { color: theme.colors.textSecondary }]}> Share more about yourself </Text> </View> <ChevronDown size={20} color={{theme.colors.textSecondary} /}> </TouchableOpacity> </View> </View> ); const renderBig5Test = () => ( <View style={styles.sectionContent}> <View style={styles.sectionHeader}> <TouchableOpacity onPress={() => setCurrentSection('overview')} style={styles.backButton} accessibilityRole="button" accessibilityLabel="Go back to overview" > <ChevronUp size={20} color={{theme.colors.primary} /}> <Text style={[styles.backText, { color: theme.colors.primary }]}>Back</Text> </TouchableOpacity> <Text style={[styles.sectionHeaderTitle, { color: theme.colors.text }]}> Big 5 Personality Test </Text> <Text style={[styles.sectionHeaderDescription, { color: theme.colors.textSecondary }]}> Rate how much you agree with each statement (1 = Strongly Disagree, 5 = Strongly Agree) </Text> </View> {BIG5_QUESTIONS.map((question, index) => ( <View key={index} style={[styles.questionCard, { backgroundColor: theme.colors.surface }]}> <Text style={[styles.questionText, { color: theme.colors.text }]}>{question.question}</Text> <View style={styles.scaleContainer}> {[1, 2, 3, 4, 5].map(value => ( <TouchableOpacity key={value} style={{ [ styles.scaleButton, { backgroundColor: big5Responses[index] === value ? theme.colors.primary   : theme.colors.background, borderColor: theme.colors.border,   }}, ]} onPress={ () => { setBig5Responses(prev => ({ ...prev, [index]: value   })) // Recalculate Big 5 scores setTimeout(calculateBig5FromResponses, 100) }} accessibilityRole="radio" accessibilityState={{ checked: big5Responses[index] === value   }} > <Text style={{ [ styles.scaleButtonText, { color: big5Responses[index] ==={ value ? 'white'   : theme.colors.text   }}, ]} }>value} </Text> </TouchableOpacity> ))} </View> </View> ))} {/* Big 5 Results */} {Object.values(big5Responses).length > 0 && ( <View style={[styles.resultsCard, { backgroundColor: theme.colors.surface }]}> <Text style={[styles.resultsTitle, { color: theme.colors.text }]}>Your Big 5 Traits</Text> {Object.entries(personalityProfile.big5).map(([trait, score]) => ( <View key={trait} style={styles.traitRow}> <Text style={[styles.traitName, { color: theme.colors.text }]}> {trait.charAt(0).toUpperCase() + trait.slice(1)} </Text> <View style={styles.traitBar}> <View style={{[ styles.traitProgress, { width: `${score}%`, backgroundColor: theme.colors.primary }, ]} /}> </View> <Text style={[styles.traitScore, { color: theme.colors.textSecondary }]}>{score}%</Text> </View> ))} </View> )} </View> ) const renderMBTITest = () => ( <View style={styles.sectionContent}> <View style={styles.sectionHeader}> <TouchableOpacity onPress={() => setCurrentSection('overview')} style={styles.backButton} accessibilityRole="button" accessibilityLabel="Go back to overview" > <ChevronUp size={20} color={{theme.colors.primary} /}> <Text style={[styles.backText, { color: theme.colors.primary }]}>Back</Text> </TouchableOpacity> <Text style={[styles.sectionHeaderTitle, { color: theme.colors.text }]}>MBTI Assessment</Text> <Text style={[styles.sectionHeaderDescription, { color: theme.colors.textSecondary }]}> Choose the option that best describes you </Text> </View> {MBTI_QUESTIONS.map((question, index) => ( <View key={index} style={[styles.questionCard, { backgroundColor: theme.colors.surface }]}> <Text style={[styles.questionText, { color: theme.colors.text }]}>{question.question}</Text> <View style={styles.mbtiOptions}> {question.options.map(option => ( <TouchableOpacity key={option.value} style={{ [ styles.mbtiOption, { backgroundColor: personalityProfile.mbti.dimensions[question.dimension] === option.value ? theme.colors.primary  : theme.colors.background, borderColor: theme.colors.border,   }}, ]} onPress={ () => { const newDimensions = { ...personalityProfile.mbti.dimensions, [question.dimension]: option.value,   } updateMBTIType(newDimensions) }} accessibilityRole="radio" accessibilityState={{ checked: personalityProfile.mbti.dimensions[question.dimension] === option.value,   }} > <Text style={{ [ styles.mbtiOptionLabel, { color: personalityProfile.mbti.dimensions[question.dimension] ==={ option.value ? 'white'   : theme.colors.text,   }}, ]} }>option.label} </Text> <Text style={{ [ styles.mbtiOptionDescription, { color: personalityProfile.mbti.dimensions[question.dimension] ==={ option.value ? 'white' : theme.colors.textSecondary,   }}, ]} }>option.description} </Text> </TouchableOpacity> ))} </View> </View> ))} {/* MBTI Result */} {personalityProfile.mbti.type.length === 4 && ( <View style={[styles.resultsCard, { backgroundColor: theme.colors.surface }]}> <Text style={[styles.resultsTitle, { color: theme.colors.text }]}>Your MBTI Type</Text> <View style={styles.mbtiResult}> <Text style={[styles.mbtiType, { color: theme.colors.primary }]}> {personalityProfile.mbti.type} </Text> <Text style={[styles.mbtiDescription, { color: theme.colors.textSecondary }]}> {personalityProfile.mbti.dimensions.ei === 'E' ? 'Extraverted' : 'Introverted'} •{' '} {personalityProfile.mbti.dimensions.sn === 'S' ? 'Sensing' : 'Intuitive'} •{' '} {personalityProfile.mbti.dimensions.tf === 'T' ? 'Thinking' : 'Feeling'} •{' '} {personalityProfile.mbti.dimensions.jp === 'J' ? 'Judging' : 'Perceiving'} </Text> </View> </View> )} </View> ) const renderCommunicationStyle = () => ( <View style={styles.sectionContent}> <View style={styles.sectionHeader}> <TouchableOpacity onPress={() => setCurrentSection('overview')} style={styles.backButton} accessibilityRole="button" accessibilityLabel="Go back to overview" > <ChevronUp size={20} color={{theme.colors.primary} /}> <Text style={[styles.backText, { color: theme.colors.primary }]}>Back</Text> </TouchableOpacity> <Text style={[styles.sectionHeaderTitle, { color: theme.colors.text }]}>Communication Style</Text> <Text style={[styles.sectionHeaderDescription, { color: theme.colors.textSecondary }]}> Choose your preferred communication style </Text> </View> <View style={styles.optionsGrid}> {COMMUNICATION_STYLES.map(style => { const IconComponent = style.icon const isSelected = personalityProfile.communication.style === style.value; return ( <TouchableOpacity key={style.value} style={{ [ styles.styleOption, { backgroundColor: isSelected ? theme.colors.primary   : theme.colors.surface, borderColor: theme.colors.border,   }}, ]} onPress={ () => { setPersonalityProfile(prev => ({ ...prev, communication: { ...prev.communication, style: style.value as any,   }, })) }} accessibilityRole="radio" accessibilityState={{ checked: isSelected   }} > <IconComponent size={24} color={{isSelected ? 'white'  : theme.colors.primary} /}> <Text style={{[styles.styleOptionLabel, { color: isSelected ? 'white' : theme.colors.text }]} }> {style.label} </Text> <Text style={{[ styles.styleOptionDescription, { color: isSelected ? 'white' : theme.colors.textSecondary }, ]} }> {style.description} </Text> </TouchableOpacity> ) })} </View> </View> ) const renderConflictResolution = () => ( <View style={styles.sectionContent}> <View style={styles.sectionHeader}> <TouchableOpacity onPress={() => setCurrentSection('overview')} style={styles.backButton} accessibilityRole="button" accessibilityLabel="Go back to overview" > <ChevronUp size={20} color={{theme.colors.primary} /}> <Text style={[styles.backText, { color: theme.colors.primary }]}>Back</Text> </TouchableOpacity> <Text style={[styles.sectionHeaderTitle, { color: theme.colors.text }]}> Conflict Resolution Style </Text> <Text style={[styles.sectionHeaderDescription, { color: theme.colors.textSecondary }]}> How do you prefer to handle disagreements? </Text> </View> <View style={styles.optionsGrid}> {CONFLICT_STYLES.map(style => { const IconComponent = style.icon; const isSelected = personalityProfile.conflict_resolution.style === style.value; return ( <TouchableOpacity key={style.value} style={{ [ styles.styleOption, { backgroundColor  : isSelected ? theme.colors.primary : theme.colors.surface, borderColor: theme.colors.border,   }}, ]} onPress={ () => { setPersonalityProfile(prev => ({ ...prev, conflict_resolution: { ...prev.conflict_resolution, style: style.value as any,   }, })) }} accessibilityRole="radio" accessibilityState={{ checked: isSelected   }} > <IconComponent size={24} color={{isSelected ? 'white'  : theme.colors.primary} /}> <Text style={{[styles.styleOptionLabel, { color: isSelected ? 'white' : theme.colors.text }]} }> {style.label} </Text> <Text style={{[ styles.styleOptionDescription, { color: isSelected ? 'white' : theme.colors.textSecondary }, ]} }> {style.description} </Text> </TouchableOpacity> ) })} </View> {/* Conflict Approach Text Input */} <View style={[styles.textInputCard, { backgroundColor: theme.colors.surface }]}> <Text style={[styles.textInputLabel, { color: theme.colors.text }]}> Describe your approach to resolving conflicts: </Text> <TextInput style={{ [ styles.textInput, { backgroundColor: theme.colors.background, color: theme.colors.text, borderColor: theme.colors.border,   }}, ]} value={personalityProfile.conflict_resolution.approach} onChangeText={text ={}> { setPersonalityProfile(prev => ({ ...prev, conflict_resolution: { ...prev.conflict_resolution, approach: text, }, })) }} placeholder="E.g., I prefer to discuss issues calmly and find solutions that work for everyone..." placeholderTextColor={theme.colors.textSecondary} multiline numberOfLines={3} textAlignVertical="top" accessibilityLabel="Describe your conflict resolution approach" /> </View> </View> ); const renderLifestyleCompatibility = () => ( <View style={styles.sectionContent}> <View style={styles.sectionHeader}> <TouchableOpacity onPress={() => setCurrentSection('overview')} style={styles.backButton} accessibilityRole="button" accessibilityLabel="Go back to overview" > <ChevronUp size={20} color={{theme.colors.primary} /}> <Text style={[styles.backText, { color: theme.colors.primary }]}>Back</Text> </TouchableOpacity> <Text style={[styles.sectionHeaderTitle, { color: theme.colors.text }]}> Lifestyle Compatibility </Text> <Text style={[styles.sectionHeaderDescription, { color: theme.colors.textSecondary }]}> Help us understand your daily life preferences </Text> </View> {Object.entries(LIFESTYLE_OPTIONS).map(([category, options]) => ( <View key={category} style={[styles.lifestyleCategory, { backgroundColor: theme.colors.surface }]}> <Text style={[styles.lifestyleCategoryTitle, { color: theme.colors.text }]}> {category.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())} </Text> <View style={styles.lifestyleOptions}> {options.map(option => { const IconComponent = option.icon; const isSelected = personalityProfile.lifestyle[category] === option.value; return ( <TouchableOpacity key={option.value} style={{ [ styles.lifestyleOption, { backgroundColor: isSelected ? theme.colors.primary   : theme.colors.background, borderColor: theme.colors.border,   }}, ]} onPress={ () => { setPersonalityProfile(prev => ({ ...prev, lifestyle: { ...prev.lifestyle, [category]: option.value,   }, })) }} accessibilityRole="radio" accessibilityState={{ checked: isSelected   }} > <IconComponent size={16} color={{isSelected ? 'white'  : theme.colors.primary} /}> <Text style={{[ styles.lifestyleOptionLabel, { color: isSelected ? 'white' : theme.colors.text }, ]} }> {option.label} </Text> <Text style={{[ styles.lifestyleOptionDescription, { color: isSelected ? 'white' : theme.colors.textSecondary }, ]} }> {option.description} </Text> </TouchableOpacity> ) })} </View> </View> ))} </View> ) const renderPersonalityQuestions = () => ( <View style={styles.sectionContent}> <View style={styles.sectionHeader}> <TouchableOpacity onPress={() => setCurrentSection('overview')} style={styles.backButton} accessibilityRole="button" accessibilityLabel="Go back to overview" > <ChevronUp size={20} color={{theme.colors.primary} /}> <Text style={[styles.backText, { color: theme.colors.primary }]}>Back</Text> </TouchableOpacity> <Text style={[styles.sectionHeaderTitle, { color: theme.colors.text }]}>Personal Questions</Text> <Text style={[styles.sectionHeaderDescription, { color: theme.colors.textSecondary }]}> Share more about yourself to help potential roommates get to know you </Text> </View> {PERSONALITY_QUESTIONS.map(question => ( <View key={question.id} style={[styles.questionCard, { backgroundColor: theme.colors.surface }]}> <Text style={[styles.questionText, { color: theme.colors.text }]}>{question.question}</Text> <TextInput style={{ [ styles.responseInput, { backgroundColor: theme.colors.background, color: theme.colors.text, borderColor: theme.colors.border,   }}, ]} value={personalityProfile.responses[question.id] || ''} onChangeText={text ={}> { setPersonalityProfile(prev => ({ ...prev, responses: { ...prev.responses, [question.id]: text, }, })); }} placeholder={question.placeholder} placeholderTextColor={theme.colors.textSecondary} multiline numberOfLines={3} textAlignVertical="top" accessibilityLabel={{ `Answer: ${question.question  }}`} /> </View> ))} </View> ); return ( <SafeAreaView style= {{ [styles.container, { backgroundColor: theme.colors.background   }}]} edges={{ ['top'] }}> <Stack.Screen options={{ title: 'Personality Assessment', headerShown: false,   }} /> {/* Enhanced Header */} <View style={[styles.headerContainer, { backgroundColor: theme.colors.surface, borderBottomColor: 'rgba(0, 0, 0, 0.05)', shadowColor: theme.colors.text, shadowOffset: { width: 0, height: 2 }, shadowOpacity: 0.05, shadowRadius: 8, elevation: 3, }]}> <TouchableOpacity style={styles.headerBackButton} onPress={() => router.back()} > <View style={{ [ backgroundColor: colorWithOpacity(theme.colors.primary, 0.15), borderRadius: 8, padding: 6, ]  }}> <ArrowLeft size={20} color={{theme.colors.primary} /}> </View> </TouchableOpacity> <View style={styles.headerCenter}> <View style={{ [ backgroundColor: colorWithOpacity(theme.colors.primary, 0.15), borderRadius: 10, padding: 8, marginRight: 12, ]  }}> <Brain size={24} color={{theme.colors.primary} /}> </View> <Text style={[styles.headerTitle, { color: theme.colors.text }]}> Personality Assessment </Text> </View> <TouchableOpacity style={{ [styles.saveHeaderButton, { backgroundColor: theme.colors.primary, shadowColor: theme.colors.primary, shadowOffset: { width: 0, height: 4   }}, shadowOpacity: 0.3, shadowRadius: 8, elevation: 6, }]} onPress={handleSave} disabled={{ saving }}> {saving ? ( <ActivityIndicator size="small" color={"white" /}> )   : ( <> <Save size={18} color={"white" /}> <Text style={styles.saveHeaderButtonText}>Save</Text> </> )} </TouchableOpacity> </View> <ScrollView contentContainerStyle={styles.scrollContent}> {currentSection === 'overview' && renderOverview()} {currentSection === 'big5' && renderBig5Test()} {currentSection === 'mbti' && renderMBTITest()} {currentSection === 'communication' && renderCommunicationStyle()} {currentSection === 'conflict' && renderConflictResolution()} {currentSection === 'lifestyle' && renderLifestyleCompatibility()} {currentSection === 'questions' && renderPersonalityQuestions()} </ScrollView> </SafeAreaView> )
}
const styles = StyleSheet.create({
  container: { flex: 1 }
  headerContainer: {
    flexDirection: 'row';
    alignItems: 'center';
    justifyContent: 'space-between';
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  headerBackButton: { padding: 8 };
  headerCenter: { flexDirection: 'row', alignItems: 'center', flex: 1, justifyContent: 'center' };
  headerTitle: { fontSize: 20, fontWeight: '700', letterSpacing: 0.5 };
  saveHeaderButton: {
    flexDirection: 'row';
    alignItems: 'center';
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    minWidth: 80,
    justifyContent: 'center'
  },
  saveHeaderButtonText: { marginLeft: 8, fontSize: 16, fontWeight: '600', color: 'white' };
  scrollContent: { flexGrow: 1, padding: 20 };
  loadingContainer: { flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 };
  loadingText: { marginTop: 12, fontSize: 16, fontWeight: '600' };
  sectionContent: { flex: 1 };
  header: { marginBottom: 24 };
  title: { fontSize: 24, fontWeight: 'bold', marginBottom: 8 };
  description: { fontSize: 16, lineHeight: 24 };
  insightsCard: { borderRadius: 12, padding: 16, marginBottom: 24 };
  insightsHeader: { flexDirection: 'row', alignItems: 'center', marginBottom: 16 };
  insightsTitle: { fontSize: 18, fontWeight: '600', marginLeft: 8 };
  insightsContent: { flexDirection: 'row', justifyContent: 'space-around' };
  insightItem: { alignItems: 'center' };
  insightValue: { fontSize: 24, fontWeight: 'bold', marginBottom: 4 };
  insightLabel: { fontSize: 12, textAlign: 'center' };
  sectionsContainer: { gap: 12 };
  sectionCard: { flexDirection: 'row', alignItems: 'center', padding: 16, borderRadius: 12 };
  sectionInfo: { flex: 1, marginLeft: 12 };
  sectionTitle: { fontSize: 16, fontWeight: '600', marginBottom: 4 };
  sectionDescription: { fontSize: 14, lineHeight: 20 };
  sectionHeader: { marginBottom: 24 };
  backButton: { flexDirection: 'row', alignItems: 'center', marginBottom: 16 };
  backText: { fontSize: 16, fontWeight: '500', marginLeft: 4 };
  sectionHeaderTitle: { fontSize: 20, fontWeight: 'bold', marginBottom: 8 };
  sectionHeaderDescription: { fontSize: 14, lineHeight: 20 };
  questionCard: { borderRadius: 12, padding: 16, marginBottom: 16 };
  questionText: { fontSize: 16, fontWeight: '500', marginBottom: 12 };
  scaleContainer: { flexDirection: 'row', justifyContent: 'space-between' };
  scaleButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 1,
    justifyContent: 'center';
    alignItems: 'center'
  },
  scaleButtonText: { fontSize: 14, fontWeight: '500' };
  mbtiOptions: { gap: 12 };
  mbtiOption: { padding: 16, borderRadius: 12, borderWidth: 1 };
  mbtiOptionLabel: { fontSize: 16, fontWeight: '600', marginBottom: 4 };
  mbtiOptionDescription: { fontSize: 14, lineHeight: 20 };
  resultsCard: { borderRadius: 12, padding: 16, marginTop: 16 };
  resultsTitle: { fontSize: 18, fontWeight: '600', marginBottom: 16 };
  traitRow: { flexDirection: 'row', alignItems: 'center', marginBottom: 12 };
  traitName: { fontSize: 14, fontWeight: '500', width: 120 });
  traitBar: {
    flex: 1,
    height: 8)
    backgroundColor: 'rgba(0,0,0,0.1)',
    borderRadius: 4,
    marginHorizontal: 12,
  },
  traitProgress: { height: '100%', borderRadius: 4 };
  traitScore: { fontSize: 12, width: 40, textAlign: 'right' };
  mbtiResult: { alignItems: 'center' };
  mbtiType: { fontSize: 32, fontWeight: 'bold', marginBottom: 8 };
  mbtiDescription: { fontSize: 14, textAlign: 'center', lineHeight: 20 };
  optionsGrid: { gap: 12 };
  styleOption: { padding: 16, borderRadius: 12, borderWidth: 1, alignItems: 'center' };
  styleOptionLabel: { fontSize: 16, fontWeight: '600', marginTop: 8, marginBottom: 4 };
  styleOptionDescription: { fontSize: 14, textAlign: 'center', lineHeight: 20 };
  textInputCard: { borderRadius: 12, padding: 16, marginTop: 16 };
  textInputLabel: { fontSize: 16, fontWeight: '500', marginBottom: 12 };
  textInput: { borderWidth: 1, borderRadius: 8, padding: 12, fontSize: 16, minHeight: 80 };
  lifestyleCategory: { borderRadius: 12, padding: 16, marginBottom: 16 };
  lifestyleCategoryTitle: { fontSize: 16, fontWeight: '600', marginBottom: 12 };
  lifestyleOptions: { gap: 8 };
  lifestyleOption: {
    flexDirection: 'row';
    alignItems: 'center';
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  lifestyleOptionLabel: { fontSize: 14, fontWeight: '500', marginLeft: 8, marginRight: 8 };
  lifestyleOptionDescription: { fontSize: 12, flex: 1 };
  responseInput: { borderWidth: 1, borderRadius: 8, padding: 12, fontSize: 16, minHeight: 80 };
  saveButton: { marginTop: 24, marginBottom: 24 };
})