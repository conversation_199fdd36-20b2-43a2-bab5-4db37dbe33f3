/**;
 * Unified Profile Dashboard - PHASE 2 CONSOLIDATION,
 * ;
 * This component consolidates 8 dashboard routes into a single interface: ;
 * - dashboard.tsx (Profile Dashboard)
 * - role-dashboard.tsx (Role Management)
 * - smart-matching-dashboard.tsx (Matching Analytics)
 * - ai-compatibility-dashboard.tsx (AI Insights)
 * - compatibility-insights.tsx (Compatibility Analysis)
 * - verification-dashboard.tsx (Verification Status)
 * - predictive-analytics-dashboard.tsx (Advanced Analytics)
 * - property-manager-dashboard.tsx (Property Management)
 * ;
 * Result: 8 routes → 1 route (87% reduction)
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { View, Text, StyleSheet, ScrollView, ActivityIndicator, TouchableOpacity, useColorScheme, Alert, RefreshControl } from 'react-native';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from '@context/AuthContext';
import { Card, Progress } from '@components/ui';
import { Button } from '@design-system';
import { User, BarChart3, Users, Brain, Zap, Shield, TrendingUp, Home, Settings, Eye, ChevronLeft, Refresh } from 'lucide-react-native';

// Import services for data fetching,
import { unifiedProfileService } from '@services/unified-profile';
import { profileCompletionService, verificationService, backgroundCheckService } from '@services';
import type { ProfileWithRelations } from '@types/models';
import { useTheme } from '@design-system';
import { colorWithOpacity } from '@design-system';

// Tab configuration for the unified dashboard,
interface DashboardTab {
  id: string,
  title: string,
  icon: React.ComponentType<any>
  description: string,
  component: React.ComponentType<any>
}
// React Native compatible color system using centralized utility,
// Individual dashboard components,
const ProfileOverviewTab = ({ profileData, theme }: any) => (<ScrollView style={styles.tabContent}>
    <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
      {/* Modern card header with icon */}
      <View style={{ [
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 20,
      ]  }}>
        <View style={{ [
          backgroundColor: colorWithOpacity(theme.colors.primary, 0.1),
          borderRadius: 12,
          padding: 8,
          marginRight: 12,
        ]  }}>
          <User size={24} color={{theme.colors.primary} /}>
        </View>
        <Text style={[styles.cardTitle, { color: theme.colors.text }]}>Profile Overview</Text>
      </View>
      <View style={styles.statsGrid}>
        <View style={{ [styles.statItem, {
          backgroundColor: colorWithOpacity(theme.colors.primary, 0.1),
          borderColor: colorWithOpacity(theme.colors.primary, 0.15),
          }}]}>
          <Text style={[styles.statValue, { color: theme.colors.primary }]}>
            {profileData? .profile_completion || 0}%;
          </Text>
          <Text style= {styles.statLabel}>Complete</Text>
        </View>
        <View style={{ [styles.statItem, {
          backgroundColor  : colorWithOpacity(theme.colors.success, 0.1),
          borderColor: colorWithOpacity(theme.colors.success, 0.15),
          }}]}>
          <Text style={[styles.statValue, { color: theme.colors.success }]}>
            {profileData?.verification_score || 0}
          </Text>
          <Text style={styles.statLabel}>Trust Score</Text>
        </View>
        <View style={{ [styles.statItem, {
          backgroundColor: colorWithOpacity(theme.colors.warning, 0.1),
          borderColor: colorWithOpacity(theme.colors.warning, 0.15),
          }}]}>
          <Text style={[styles.statValue, { color: theme.colors.warning }]}>
            {profileData?.match_count || 0}
          </Text>
          <Text style={styles.statLabel}>Matches</Text>
        </View>
      </View>
    </Card>
  </ScrollView>
)

const RoleManagementTab = ({ profileData, theme }: any) => (<ScrollView style={styles.tabContent}>
    <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
      {/* Modern card header with icon */}
      <View style={{ [
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 20,
      ]  }}>
        <View style={{ [
          backgroundColor: colorWithOpacity(theme.colors.success, 0.15),
          borderRadius: 12,
          padding: 8,
          marginRight: 12,
        ]  }}>
          <Users size={24} color={{theme.colors.success} /}>
        </View>
        <Text style={[styles.cardTitle, { color: theme.colors.text }]}>Role Management</Text>
      </View>
      <View style={styles.roleList}>
        {profileData? .roles?.map((role : string, index: number) => (
          <View key={index} style={{ [styles.roleItem, {
            borderColor: theme.colors.border,
            backgroundColor: theme.colors.surface,
            }}]}>
            <View style={{ [
              flexDirection: 'row',
              alignItems: 'center',
              flex: 1,
            ]  }}>
              <View style={{ [
                backgroundColor: colorWithOpacity(theme.colors.primary, 0.15),
                borderRadius: 8,
                padding: 6,
                marginRight: 12,
              ]  }}>
                <Users size={16} color={{theme.colors.primary} /}>
              </View>
              <Text style={[styles.roleText, { color: theme.colors.text }]}>{role}</Text>
            </View>
            <View style={[styles.roleBadge, { backgroundColor: colorWithOpacity(theme.colors.success, 0.2) }]}>
              <Text style={[styles.roleBadgeText, { color: theme.colors.success }]}>Active</Text>
            </View>
          </View>
        ))}
      </View>
    </Card>
  </ScrollView>
)
const MatchingAnalyticsTab = ({ profileData, theme }: any) => (<ScrollView style={styles.tabContent}>
    <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
      {/* Modern card header with icon */}
      <View style={{ [
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 20,
      ]  }}>
        <View style={{ [
          backgroundColor: colorWithOpacity(theme.colors.warning, 0.15),
          borderRadius: 12,
          padding: 8,
          marginRight: 12,
        ]  }}>
          <BarChart3 size={24} color={theme.colors.warning} />
        </View>
        <Text style={[styles.cardTitle, { color: theme.colors.text }]}>Matching Analytics</Text>
      </View>
      <View style={styles.analyticsGrid}>
        <View style={{ [styles.analyticsItem, {
          backgroundColor: colorWithOpacity(theme.colors.primary, 0.08),
          borderColor: colorWithOpacity(theme.colors.primary, 0.15),
          }}]}>
          <Text style={[styles.analyticsValue, { color: theme.colors.primary }]}>
            {profileData? .matching_stats?.views || 0}
          </Text>
          <Text style={[styles.analyticsLabel, { color : theme.colors.text }]}>Profile Views</Text>
        </View>
        <View style={{ [styles.analyticsItem, {
          backgroundColor: colorWithOpacity(theme.colors.success, 0.08),
          borderColor: colorWithOpacity(theme.colors.success, 0.15),
          }}]}>
          <Text style={[styles.analyticsValue, { color: theme.colors.success }]}>
            {profileData?.matching_stats?.likes || 0}
          </Text>
          <Text style={[styles.analyticsLabel, { color: theme.colors.text }]}>Likes Received</Text>
        </View>
      </View>
    </Card>
  </ScrollView>
)

const AIInsightsTab = ({ profileData, theme }: any) => (<ScrollView style={styles.tabContent}>
    <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
      {/* Modern card header with icon */}
      <View style={{ [
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 20,
      ]  }}>
        <View style={{ [
          backgroundColor: colorWithOpacity(theme.colors.primary, 0.15),
          borderRadius: 12,
          padding: 8,
          marginRight: 12,
        ]  }}>
          <Brain size={24} color={{theme.colors.primary} /}>
        </View>
        <Text style={[styles.cardTitle, { color: theme.colors.text }]}>AI Insights</Text>
      </View>
      <View style={styles.insightsList}>
        <Text style={[styles.insightText, { color: theme.colors.textSecondary }]}>
          AI-powered compatibility insights and recommendations will appear here.
        </Text>
      </View>
    </Card>
  </ScrollView>
)
const CompatibilityTab = ({ profileData, theme }: any) => (<ScrollView style={styles.tabContent}>
    <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
      {/* Modern card header with icon */}
      <View style={{ [
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 20,
      ]  }}>
        <View style={{ [
          backgroundColor: colorWithOpacity(theme.colors.success, 0.15),
          borderRadius: 12,
          padding: 8,
          marginRight: 12,
        ]  }}>
          <Zap size={24} color={{theme.colors.success} /}>
        </View>
        <Text style={[styles.cardTitle, { color: theme.colors.text }]}>Compatibility Analysis</Text>
      </View>
      <View style={styles.compatibilityScores}>
        <Progress value={profileData? .compatibility_score || 0} label="Overall Compatibility";
          color= {theme.colors.primary}
        />
      </View>
    </Card>
  </ScrollView>
)
const VerificationTab = ({ profileData, theme }  : any) => (<ScrollView style={styles.tabContent}>
    <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
      {/* Modern card header with icon */}
      <View style={{ [
        flexDirection: 'row'
        alignItems: 'center',
        marginBottom: 20,
      ]  }}>
        <View style={{ [
          backgroundColor: colorWithOpacity(theme.colors.warning, 0.15),
          borderRadius: 12,
          padding: 8,
          marginRight: 12,
        ]  }}>
          <Shield size={24} color={{theme.colors.warning} /}>
        </View>
        <Text style={[styles.cardTitle, { color: theme.colors.text }]}>Verification Status</Text>
      </View>
      <View style={styles.verificationList}>
        <View style={{ [styles.verificationItem, {
          borderColor: theme.colors.border,
          backgroundColor: 'rgba(248, 250, 252, 0.4)',
          }}]}>
          <View style={{ [
            flexDirection: 'row',
            alignItems: 'center',
            flex: 1,
          ]  }}>
            <View style={{ [
              backgroundColor: colorWithOpacity(theme.colors.success, 0.15),
              borderRadius: 8,
              padding: 6,
              marginRight: 12,
            ]  }}>
              <Shield size={16} color={{theme.colors.success} /}>
            </View>
            <Text style={[styles.verificationText, { color: theme.colors.text }]}>Identity Verified</Text>
          </View>
          <Text style={[styles.verificationStatus, { color: theme.colors.success }]}>
            {profileData? .is_verified ? 'Verified'  : 'Pending'}
          </Text>
        </View>
        <View style={{ [styles.verificationItem, {
          borderColor: theme.colors.border
          backgroundColor: 'rgba(248, 250, 252, 0.4)',
          }}]}>
          <View style={{ [
            flexDirection: 'row',
            alignItems: 'center',
            flex: 1,
          ]  }}>
            <View style={{ [
              backgroundColor: colorWithOpacity(theme.colors.warning, 0.15),
              borderRadius: 8,
              padding: 6,
              marginRight: 12,
            ]  }}>
              <Eye size={16} color={{theme.colors.warning} /}>
            </View>
            <Text style={[styles.verificationText, { color: theme.colors.text }]}>Background Check</Text>
          </View>
          <Text style={[styles.verificationStatus, { color: theme.colors.warning }]}>
            {profileData? .background_check_status || 'Not Started'}
          </Text>
        </View>
      </View>
    </Card>
  </ScrollView>
)
const AdvancedAnalyticsTab = ({ profileData, theme } : any) => (<ScrollView style={styles.tabContent}>
    <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
      <Text style={[styles.cardTitle, { color: theme.colors.text }]}>Advanced Analytics</Text>
      <View style={styles.chartPlaceholder}>
        <TrendingUp size={48} color={{theme.colors.textSecondary} /}>
        <Text style={[styles.placeholderText, { color: theme.colors.textSecondary }]}>
          Advanced analytics dashboard coming soon
        </Text>
      </View>
    </Card>
  </ScrollView>
)
const PropertyManagementTab = ({ profileData, theme }: any) => (<ScrollView style={styles.tabContent}>
    <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
      <Text style={[styles.cardTitle, { color: theme.colors.text }]}>Property Management</Text>
      <View style={styles.propertyStats}>
        <Text style={[styles.propertyText, { color: theme.colors.textSecondary }]}>
          {profileData? .role === 'property_owner' 
            ? 'Property management dashboard will appear here.';
              : 'Available for property owners only.'}
        </Text>
      </View>
    </Card>
  </ScrollView>
)

export default function UnifiedDashboardScreen() {
  const theme = useTheme()
  const colorScheme = useColorScheme()
  const router = useRouter()
  const { authState  } = useAuth()
  const params = useLocalSearchParams()
  // State management,
  const [activeTab, setActiveTab] = useState('overview')
  const [profileData, setProfileData] = useState<ProfileWithRelations | null>(null)
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  // Tab configuration,
  const dashboardTabs: DashboardTab[] = useMemo(() => [
    {
      id: 'overview';
      title: 'Overview';
      icon: User,
      description: 'Profile summary and key metrics';
      component: ProfileOverviewTab,
    },
    {
      id: 'roles';
      title: 'Roles';
      icon: Users,
      description: 'Manage your user roles';
      component: RoleManagementTab,
    },
    {
      id: 'matching';
      title: 'Matching';
      icon: BarChart3,
      description: 'Matching analytics and insights';
      component: MatchingAnalyticsTab,
    },
    {
      id: 'ai-insights';
      title: 'AI Insights';
      icon: Brain,
      description: 'AI-powered recommendations';
      component: AIInsightsTab,
    },
    {
      id: 'compatibility';
      title: 'Compatibility';
      icon: Zap,
      description: 'Compatibility analysis';
      component: CompatibilityTab,
    },
    {
      id: 'verification';
      title: 'Verification';
      icon: Shield,
      description: 'Identity and background verification';
      component: VerificationTab,
    },
    {
      id: 'analytics';
      title: 'Analytics';
      icon: TrendingUp,
      description: 'Advanced analytics dashboard';
      component: AdvancedAnalyticsTab,
    },
    {
      id: 'property';
      title: 'Property';
      icon: Home,
      description: 'Property management dashboard';
      component: PropertyManagementTab,
    },
  ], [])
  // Data fetching,
  const fetchProfileData = useCallback(async () => {
  if (!authState.user) return null,
    try {
      setLoading(true)
      const data = await unifiedProfileService.getUserProfile(authState.user.id)
      setProfileData(data)
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
      Alert.alert('Error', 'Failed to load dashboard data')
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }, [authState.user])
  // Initial data load,
  useEffect(() => {
  fetchProfileData()
  }, [fetchProfileData])
  // Set initial tab from params,
  useEffect(() => {
  if (params.tab && typeof params.tab === 'string') {
      setActiveTab(params.tab)
    }
  }, [params.tab])
  const onRefresh = useCallback(() => {
  setRefreshing(true)
    fetchProfileData()
  }, [fetchProfileData])
  // Get active tab component,
  const ActiveTabComponent = useMemo(() => {
  const tab = dashboardTabs.find(t => t.id === activeTab)
    return tab? .component || ProfileOverviewTab,
  }, [activeTab, dashboardTabs])
  if (loading && !profileData) {
    return (
    <SafeAreaView style={[styles.container, { backgroundColor  : theme.colors.background }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={{theme.colors.primary} /}>
          <Text style={[styles.loadingText, { color: theme.colors.textSecondary }]}>
            Loading dashboard...
          </Text>
        </View>
      </SafeAreaView>
    )
  }
  return (
    <SafeAreaView style={{ [styles.container, { backgroundColor: theme.colors.background   }}]} edges={['top']}>
      <Stack.Screen,
        options={{ {
          title: 'Dashboard',
          headerStyle: { backgroundColor: theme.colors.primary   }}
          headerTintColor: '#FFFFFF';
          headerLeft: () = > (;
            <TouchableOpacity onPress = {() => router.back()} style={styles.headerButton}>
                              <ChevronLeft size={24} color={"#FFFFFF" /}>
            </TouchableOpacity>
          )
          headerRight: () => (;
            <TouchableOpacity onPress = {onRefresh} style={styles.headerButton}>
                              <Refresh size={20} color={"#FFFFFF" /}>
            </TouchableOpacity>
          )
        }}
      />
      {/* Tab Navigation */}
      <ScrollView,
        horizontal showsHorizontalScrollIndicator={false} style={{ [styles.tabBar, { backgroundColor: theme.colors.surface, borderBottomColor: theme.colors.border   }}]}
        contentContainerStyle={styles.tabBarContent}
      >
        {dashboardTabs.map((tab) => {
  const IconComponent = tab.icon,
          const isActive = activeTab === tab.id,
          ;
          return (
            <TouchableOpacity,
              key = {tab.id}
              style={{ [
                styles.tab,
                {
                  backgroundColor: isActive ,
                    ? colorWithOpacity(theme.colors.primary, 0.15) // Modern active background,
                      : 'transparent',
                  borderWidth: isActive ? 1 : 0
                  borderColor: isActive ? colorWithOpacity(theme.colors.primary, 0.3)  : 'transparent',
                  elevation: isActive ? 2 : 0
                  shadowColor: isActive ? theme.colors.primary  : 'transparent'
                  shadowOffset: { width: 0, height: 2   }}
                  shadowOpacity: isActive ? 0.15   : 0
                  shadowRadius: isActive ? 6  : 0
                }
              ]}
              onPress={() => setActiveTab(tab.id)}
              activeOpacity={0.8}
            >
              {/* Modern icon container */}
              <View style={{ [
                backgroundColor: isActive ? colorWithOpacity(theme.colors.primary, 0.2)  : 'transparent',
                borderRadius: 8
                padding: isActive ? 4  : 0
              ]  }}>
                <IconComponent,
                  size={22} // Increased from 20,
                  color = {isActive ? theme.colors.primary  : theme.colors.textSecondary}
                />
              </View>
              <Text
                style={{ [
                  styles.tabText
                  {
                    color: isActive ? theme.colors.primary  : theme.colors.textSecondary
                    fontWeight: isActive ? '700'  : '500', // Dynamic font weight
                    }}
                ]}
              >
                {tab.title}
              </Text>
              {/* Modern active indicator */}
              {isActive && (
                <View style={{ [
                  position: 'absolute',
                  bottom: 0,
                  left: '20%',
                  right: '20%',
                  height: 3,
                  backgroundColor: theme.colors.primary,
                  borderRadius: 2,
                ]  }} />
              )}
            </TouchableOpacity>
          )
        })}
      </ScrollView>
      {/* Tab Content */}
      <ScrollView style={styles.content} refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} colors={{[theme.colors.primary]} /}>
        }
      >
                    <ActiveTabComponent profileData={profileData} theme={{theme} /}>
      </ScrollView>
    </SafeAreaView>
  )
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC', // Modern light background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center'
    alignItems: 'center';
    backgroundColor: '#F8FAFC'
  },
  loadingText: {
    marginTop: 20, // Increased from 16,
    fontSize: 16,
    fontWeight: '500');
    color: '#64748B'
  },
  headerButton: {
    padding: 12, // Increased from 8,
    borderRadius: 8)
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
  },
  tabBar: {
    borderBottomWidth: 0, // Removed border for modern look,
    backgroundColor: '#FFFFFF';
    elevation: 4,
    shadowColor: '#000000';
    shadowOffset: { width: 0, height: 2 };
    shadowOpacity: 0.1,
    shadowRadius: 8,
    borderRadius: 16,
    margin: 16,
    marginBottom: 8,
  },
  tabBarContent: {
    paddingHorizontal: 20, // Increased from 16,
    paddingVertical: 8, // Added vertical padding,
  },
  tab: {
    flexDirection: 'row';
    alignItems: 'center';
    paddingHorizontal: 20, // Increased from 16,
    paddingVertical: 16, // Increased from 12,
    marginRight: 8,
    borderRadius: 12, // Added border radius for modern tabs,
    backgroundColor: 'transparent';
    borderBottomWidth: 0, // Removed border bottom,
    minHeight: 52, // Increased touch target,
    transition: 'all 0.2s ease', // Modern transition (CSS-like)
  },
  tabText: {
    marginLeft: 10, // Increased from 8,
    fontSize: 15, // Increased from 14,
    fontWeight: '600', // Increased from 500,
    letterSpacing: 0.3,
  },
  content: {
    flex: 1,
    backgroundColor: 'transparent'
  },
  tabContent: {
    padding: 20, // Increased from 16,
  },
  card: {
    marginBottom: 20, // Increased from 16,
    padding: 24, // Increased from 16,
    borderRadius: 20, // Increased from 12 for modern look,
    elevation: 3, // Increased from 2,
    shadowColor: '#000000';
    shadowOffset: { width: 0, height: 4 }, // Enhanced shadow,
    shadowOpacity: 0.08, // Reduced from 0.1 for subtlety,
    shadowRadius: 12, // Increased from 4,
    backgroundColor: '#FFFFFF';
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.04)', // Subtle border,
  },
  cardTitle: {
    fontSize: 22, // Increased from 18,
    fontWeight: '700', // Increased from 600,
    marginBottom: 20, // Increased from 16,
    letterSpacing: 0.3,
    color: '#1E293B'
  },
  statsGrid: {
    flexDirection: 'row';
    justifyContent: 'space-around';
    gap: 16, // Added modern spacing,
  },
  statItem: {
    alignItems: 'center';
    flex: 1,
    backgroundColor: 'rgba(248, 250, 252, 0.6)',
    padding: 16,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.03)',
  },
  statValue: {
    fontSize: 28, // Increased from 24,
    fontWeight: '800', // Increased from bold,
    marginBottom: 6, // Increased from 4,
    letterSpacing: 0.5,
  },
  statLabel: {
    fontSize: 13, // Increased from 12,
    textAlign: 'center';
    fontWeight: '500';
    letterSpacing: 0.3,
    color: '#64748B'
  },
  roleList: {
    gap: 16, // Increased from 12,
  },
  roleItem: {
    flexDirection: 'row';
    justifyContent: 'space-between';
    alignItems: 'center';
    paddingVertical: 16, // Increased from 12,
    paddingHorizontal: 4,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(226, 232, 240, 0.6)',
    borderRadius: 8,
    backgroundColor: 'rgba(248, 250, 252, 0.3)',
  },
  roleText: {
    fontSize: 17, // Increased from 16,
    fontWeight: '600', // Increased from 500,
    letterSpacing: 0.2,
  },
  roleBadge: {
    paddingHorizontal: 16, // Increased from 12,
    paddingVertical: 6, // Increased from 4,
    borderRadius: 16, // Increased from 12,
    elevation: 1,
    shadowColor: '#000000';
    shadowOffset: { width: 0, height: 2 };
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  roleBadgeText: {
    fontSize: 13, // Increased from 12,
    fontWeight: '700', // Increased from 600,
    letterSpacing: 0.4,
  },
  analyticsGrid: {
    gap: 20, // Increased from 16,
  },
  analyticsItem: {
    padding: 20, // Increased from 16,
    borderRadius: 16, // Increased from 8,
    alignItems: 'center';
    elevation: 2,
    shadowColor: '#000000';
    shadowOffset: { width: 0, height: 2 };
    shadowOpacity: 0.05,
    shadowRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.8)',
  },
  analyticsValue: {
    fontSize: 36, // Increased from 32,
    fontWeight: '800', // Increased from bold,
    marginBottom: 10, // Increased from 8,
    letterSpacing: 0.5,
  },
  analyticsLabel: {
    fontSize: 15, // Increased from 14,
    fontWeight: '600', // Increased from 500,
    letterSpacing: 0.3,
  },
  insightsList: {
    padding: 20, // Increased from 16,
    backgroundColor: 'rgba(248, 250, 252, 0.5)',
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.03)',
  },
  insightText: {
    fontSize: 15, // Increased from 14,
    lineHeight: 24, // Increased from 20,
    textAlign: 'center';
    fontWeight: '500';
    letterSpacing: 0.2,
  },
  compatibilityScores: {
    gap: 20, // Increased from 16,
  },
  verificationList: {
    gap: 16, // Increased from 12,
  },
  verificationItem: {
    flexDirection: 'row';
    justifyContent: 'space-between';
    alignItems: 'center';
    paddingVertical: 16, // Increased from 12,
    paddingHorizontal: 8,
    borderBottomWidth: 1,
    backgroundColor: 'rgba(248, 250, 252, 0.4)',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.03)',
  },
  verificationText: {
    fontSize: 17, // Increased from 16,
    fontWeight: '600', // Increased from 500,
    letterSpacing: 0.2,
  },
  verificationStatus: {
    fontSize: 15, // Increased from 14,
    fontWeight: '700', // Increased from 600,
    letterSpacing: 0.3,
  },
  chartPlaceholder: {
    alignItems: 'center';
    justifyContent: 'center';
    padding: 40, // Increased from 32,
    backgroundColor: 'rgba(248, 250, 252, 0.6)',
    borderRadius: 20,
    borderWidth: 2,
    borderColor: 'rgba(0, 0, 0, 0.03)',
    borderStyle: 'dashed'
  },
  placeholderText: {
    marginTop: 20, // Increased from 16,
    fontSize: 15, // Increased from 14,
    textAlign: 'center';
    fontWeight: '500';
    letterSpacing: 0.3,
  },
  propertyStats: {
    padding: 20, // Increased from 16,
    backgroundColor: 'rgba(248, 250, 252, 0.5)',
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.03)',
  },
  propertyText: {
    fontSize: 15, // Increased from 14,
    lineHeight: 24, // Increased from 20,
    textAlign: 'center';
    fontWeight: '500';
    letterSpacing: 0.2,
  },
}); ;