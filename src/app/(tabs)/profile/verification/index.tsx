import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '@design-system/ThemeProvider';

export default function VerificationScreen() {
  const theme = useTheme();
  const styles = createStyles(theme);

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.content}>
        <View style={styles.header}>
          <Text style={styles.title}>Verification Center</Text>
          <Text style={styles.subtitle}>
            Verify your identity and build trust with the community;
          </Text>
        </View>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🆔 Identity Verification</Text>
          <Text style={styles.description}>
            Upload government-issued ID for manual review by our team.;
          </Text>
          <Text style={styles.status}>Status: Coming Soon</Text>
        </View>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>📞 Reference Verification</Text>
          <Text style={styles.description}>
            Add references from previous landlords, employers, or roommates.;
          </Text>
          <Text style={styles.status}>Status: Coming Soon</Text>
        </View>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🏠 Background Check</Text>
          <Text style={styles.description}>
            Complete background verification using public records.;
          </Text>
          <Text style={styles.status}>Status: Coming Soon</Text>
        </View>
        <View style={styles.costSavings}>
          <Text style={styles.costTitle}>💰 Zero-Cost Verification</Text>
          <Text style={styles.costDescription}>
            Save $57+ per verification cycle with our manual review system;
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  content: {
    padding: 20,
  },
  header: {
    marginBottom: 30,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
  section: {
    backgroundColor: theme.colors.surface,
    padding: 20,
    borderRadius: 12,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 8,
  },
  description: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 8,
    lineHeight: 20,
  },
  status: {
    fontSize: 14,
    color: theme.colors.warning,
    fontWeight: '500',
  },
  costSavings: {
    backgroundColor: theme.colors.success + '20',
    padding: 20,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: theme.colors.success,
    marginTop: 20,
  },
  costTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.success,
    marginBottom: 8,
  },
  costDescription: {
    fontSize: 14,
    color: theme.colors.text,
    lineHeight: 20,
  },
}); ;