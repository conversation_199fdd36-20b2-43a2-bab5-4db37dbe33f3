/**;
 * Unified Service Provider Management - PHASE 2 OPTIMIZATION;
 *;
 * This component consolidates 4 service provider routes into a single interface:;
 * - service-provider-profile.tsx (Provider Profile & Analytics);
 * - match-details.tsx (Match Analysis);
 * - match-statistics.tsx (Match Statistics);
 * - enhanced-edit.tsx (Enhanced Profile Editing);
 *;
 * Result: 4 routes → 1 route (75% reduction);
 * OPTIMIZED: Extracted 4 tab components for better organization;
 */;

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {;
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
  TouchableOpacity,
  useColorScheme,
  ScrollView,
  RefreshControl,
} from 'react-native';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from '@context/AuthContext';
import { useToast } from '@components/ui/Toast';
import { Briefcase, Star, BarChart3, Edit3, ChevronLeft } from 'lucide-react-native';

import { serviceProviderService } from '@services/serviceProviderService';
import { logger } from '@utils/logger';
import { useTheme, colorWithOpacity } from '@design-system';

// Import extracted tab components;
import {;
  ProviderProfileTab,
  PortfolioTab,
  AnalyticsTab,
  ProfileEditorTab,
} from '@components/service-provider';

// Data interfaces;
interface ServiceProviderProfile {;
  id: string;
  user_id: string;
  business_name: string;
  description: string;
  contact_email: string;
  contact_phone: string;
  business_address: string;
  website?: string;
  profile_image?: string;
  service_categories: string[];
  is_verified: boolean;
  rating_average: number;
  review_count: number;
  created_at: string;
  updated_at: string;
};

interface ServiceProviderStats {;
  total_bookings: number;
  total_revenue: number;
  monthly_revenue: number;
  average_rating: number;
  total_reviews: number;
  response_rate: number;
  completion_rate: number;
  repeat_clients: number;
  profile_views: number;
  booking_conversion: number;
};

// Service Provider tab interface;
interface ServiceProviderTab {;
  id: string;
  title: string;
  icon: React.ComponentType<any>;
  description: string;
  component: React.ComponentType<any>;
};

export default function UnifiedServiceProviderScreen() {
  const theme = useTheme();
  const { colors } = theme;
  const router = useRouter();
  const { user } = useAuth();
  const { toast } = useToast();
  const params = useLocalSearchParams();

  // State management;
  const [activeTab, setActiveTab] = useState('profile');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [providerProfile, setProviderProfile] = useState<ServiceProviderProfile | null>(null);
  const [providerStats, setProviderStats] = useState<ServiceProviderStats | null>(null);
  const [services, setServices] = useState<any[]>([]);

  // Service Provider tabs configuration with enhanced descriptions;
  const serviceProviderTabs: ServiceProviderTab[] = useMemo(;
    () => [;
      {;
        id: 'profile',
        title: 'Business Profile',
        icon: Briefcase,
        description: 'View your business profile and key statistics',
        component: ProviderProfileTab,
      },
      {;
        id: 'portfolio',
        title: 'Service Portfolio',
        icon: Star,
        description: 'Manage your services and showcase your work',
        component: PortfolioTab,
      },
      {;
        id: 'analytics',
        title: 'Performance Analytics',
        icon: BarChart3,
        description: 'Track your business performance and metrics',
        component: AnalyticsTab,
      },
      {;
        id: 'editor',
        title: 'Profile Editor',
        icon: Edit3,
        description: 'Edit your business information and settings',
        component: ProfileEditorTab,
      },
    ],
    [];
  );

  // Load provider data;
  const loadProviderData = useCallback(async () => {;
    if (!user) return;

    try {
      setLoading(true);

      // Get service provider profile;
      const providerResponse = await serviceProviderService.getServiceProviderByUserId(user.id);

      // Handle cases where no provider profile exists;
      if (providerResponse.error || !providerResponse.data) {
        logger.info('No service provider profile found for user, showing setup screen', {;
          userId: user.id,
          error: providerResponse.error,
          status: providerResponse.status,
        });

        // Set profile to null to show setup screen;
        setProviderProfile(null);
        setLoading(false);
        return;
      };

      const provider = providerResponse.data;

      // Transform data;
      setProviderProfile(provider);

      // Mock stats data (in real app, this would come from API);
      setProviderStats({;
        total_bookings: 45,
        total_revenue: 8500,
        monthly_revenue: 2200,
        average_rating: provider.rating_average || 4.8,
        total_reviews: provider.review_count || 23,
        response_rate: 0.95,
        completion_rate: 0.98,
        repeat_clients: 18,
        profile_views: 156,
        booking_conversion: 0.32,
      });

      // Get services;
      const servicesResponse = await serviceProviderService.getServicesByProviderId(provider.id);
      setServices(servicesResponse.data || []);
    } catch (error) {
      logger.warn('Error loading provider data, showing setup screen', error as Error);

      // On any error, show the setup screen instead of crashing;
      setProviderProfile(null);
    } finally {;
      setLoading(false);
    };
  }, [user, toast, router]);

  // Save profile data;
  const saveProfileData = useCallback(;
    async (updatedData: any) => {;
      if (!user || !providerProfile) return;

      try {
        setSaving(true);

        // Update provider profile;
        const updateResponse = await serviceProviderService.updateServiceProvider(;
          providerProfile.id,
          updatedData;
        );

        if (updateResponse.error) {
          throw new Error(updateResponse.error);
        };

        setProviderProfile(prev => (prev ? { ...prev, ...updatedData } : null));
        toast?.show('Profile updated successfully!', 'success');
      } catch (error) {
        logger.error('Error saving provider data:', error);
        toast?.show('Failed to save profile', 'error');
      } finally {;
        setSaving(false);
      };
    },
    [user, providerProfile, toast];
  );

  // Refresh handler;
  const onRefresh = useCallback(async () => {;
    setRefreshing(true);
    await loadProviderData();
    setRefreshing(false);
  }, [loadProviderData]);

  // Load data on mount;
  useEffect(() => {;
    loadProviderData();
  }, [loadProviderData]);

  // Set initial tab from params;
  useEffect(() => {;
    if (params.tab && typeof params.tab === 'string') {
      setActiveTab(params.tab);
    };
  }, [params.tab]);

  // Get active tab component and data;
  const getCurrentTabConfig = () => serviceProviderTabs.find(tab => tab.id === activeTab);
  const ActiveTabComponent = useMemo(() => {;
    const tab = serviceProviderTabs.find(t => t.id === activeTab);
    return tab?.component || ProviderProfileTab;
  }, [activeTab, serviceProviderTabs]);

  if (loading) {
    return (
      <SafeAreaView style={{[styles.container, { backgroundColor: theme.colors.background }]}}>;
        <View style={styles.loadingContainer}>;
          <ActivityIndicator size='large' color={{theme.colors.primary} /}>;
          <Text style={{[styles.loadingText, { color: theme.colors.text }]}}>;
            Loading service provider data...;
          </Text>;
        </View>;
      </SafeAreaView>;
    );
  };

  if (!providerProfile) {
    return (
      <SafeAreaView style={{[styles.container, { backgroundColor: theme.colors.background }]}}>;
        <Stack.Screen;
          options={{;
            title: 'Service Provider',
            headerStyle: { backgroundColor: theme.colors.surface },
            headerTintColor: theme.colors.text,
            headerTitleStyle: { fontWeight: '600' },
            headerLeft: () => (;
              <TouchableOpacity onPress={() => router.back()} style={styles.headerButton}>;
                <ChevronLeft size={24} color={{theme.colors.text} /}>;
              </TouchableOpacity>;
            ),
          }};
        />;
        <View style={styles.setupContainer}>;
          <View style={{[styles.setupCard, { backgroundColor: theme.colors.surface }]}}>;
            <Briefcase size={64} color={{theme.colors.primary} /}>;
            <Text style={{[styles.setupTitle, { color: theme.colors.text }]}}>;
              Welcome to Service Provider Hub;
            </Text>;
            <Text style={{[styles.setupDescription, { color: theme.colors.textSecondary }]}}>;
              Start offering your services to the community. Create your business profile and manage;
              your service portfolio.;
            </Text>;

            <View style={styles.setupBenefits}>;
              <View style={styles.benefitItem}>;
                <Star size={20} color={{theme.colors.success} /}>;
                <Text style={{[styles.benefitText, { color: theme.colors.text }]}}>;
                  Build your reputation with reviews;
                </Text>;
              </View>;
              <View style={styles.benefitItem}>;
                <BarChart3 size={20} color={theme.colors.success} />;
                <Text style={{[styles.benefitText, { color: theme.colors.text }]}}>;
                  Track your business performance;
                </Text>;
              </View>;
              <View style={styles.benefitItem}>;
                <Edit3 size={20} color={theme.colors.success} />;
                <Text style={{[styles.benefitText, { color: theme.colors.text }]}}>;
                  Manage services and pricing;
                </Text>;
              </View>;
            </View>;

            <TouchableOpacity
              style={[styles.setupButton, { backgroundColor: theme.colors.primary }]};
              onPress={() => router.push('/provider/setup' as any)};
            >;
              <Text style={styles.setupButtonText}>Create Service Provider Profile</Text>;
            </TouchableOpacity>;

            <TouchableOpacity
              style={[styles.secondaryButton, { borderColor: theme.colors.border }]};
              onPress={() => router.back()};
            >;
              <Text style={{[styles.secondaryButtonText, { color: theme.colors.text }]}}>;
                Maybe Later;
              </Text>;
            </TouchableOpacity>;
          </View>;
        </View>;
      </SafeAreaView>;
    );
  };

  return (
    <SafeAreaView style={{[styles.container, { backgroundColor: theme.colors.background }]}}>;
      <Stack.Screen;
        options={{;
          title: 'Service Provider',
          headerStyle: { backgroundColor: theme.colors.surface },
          headerTintColor: theme.colors.text,
          headerTitleStyle: { fontWeight: '600' },
          headerLeft: () => (;
            <TouchableOpacity onPress={() => router.back()} style={styles.headerButton}>;
              <ChevronLeft size={24} color={{theme.colors.text} /}>;
            </TouchableOpacity>;
          ),
        }};
      />;

      {/* Enhanced Tab Header */};
      <View
        style={[;
          styles.tabHeader,
          {;
            backgroundColor: theme.colors.surface,
            shadowColor: theme.colors.text,
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.05,
            shadowRadius: 8,
            elevation: 3,
          },
        ]};
      >;
        <ScrollView
          horizontal;
          showsHorizontalScrollIndicator={false};
          contentContainerStyle={styles.tabScrollContent};
        >;
          {serviceProviderTabs.map(tab => {;
            const isActive = activeTab === tab.id;
            const IconComponent = tab.icon;

            return (
              <TouchableOpacity
                key={tab.id};
                style={[;
                  styles.tab,
                  {;
                    backgroundColor: isActive ? theme.colors.primary : 'transparent',
                    borderColor: isActive ? theme.colors.primary : 'transparent',
                    shadowColor: isActive ? theme.colors.primary : 'transparent',
                    shadowOffset: { width: 0, height: 4 },
                    shadowOpacity: isActive ? 0.3 : 0,
                    shadowRadius: 8,
                    elevation: isActive ? 6 : 0,
                  },
                ]};
                onPress={() => setActiveTab(tab.id)};
              >;
                <View
                  style={[;
                    styles.tabIconContainer,
                    {;
                      backgroundColor: isActive;
                        ? 'rgba(255, 255, 255, 0.2)';
                        : colorWithOpacity(theme.colors.primary, 0.15),
                    },
                  ]};
                >;
                  <IconComponent size={20} color={{isActive ? 'white' : theme.colors.primary} /}>;
                </View>;
                <Text
                  style={[;
                    styles.tabText,
                    {;
                      color: isActive ? 'white' : theme.colors.text,
                      fontWeight: isActive ? '700' : '500',
                    },
                  ]};
                >;
                  {tab.title};
                </Text>;
                {isActive && (;
                  <Text style={{[styles.tabDescription, { color: 'rgba(255, 255, 255, 0.8)' }]}}>;
                    {tab.description};
                  </Text>;
                )};
              </TouchableOpacity>;
            );
          })};
        </ScrollView>;
      </View>;

      {/* Tab Content */};
      <View style={styles.tabContainer}>;
        <ActiveTabComponent
          colors={colors};
          providerProfile={providerProfile};
          providerStats={providerStats};
          services={services};
          onSave={saveProfileData};
          saving={saving};
          refreshControl={;
            <RefreshControl
              refreshing={refreshing};
              onRefresh={onRefresh};
              colors={[theme.colors.primary]};
              tintColor={theme.colors.primary};
            />;
          };
        />;
      </View>;
    </SafeAreaView>;
  );
};

const styles = StyleSheet.create({;
  container: {;
    flex: 1,
  },
  loadingContainer: {;
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {;
    marginTop: 16,
    fontSize: 16,
    fontWeight: '500',
  },
  errorText: {;
    marginTop: 16,
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  headerButton: {;
    padding: 8,
  },
  tabHeader: {;
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.05)',
    paddingVertical: 16,
  },
  tabScrollContent: {;
    paddingHorizontal: 16,
    gap: 12,
  },
  tab: {;
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    minHeight: 56,
    borderWidth: 1,
  },
  tabIconContainer: {;
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  tabText: {;
    fontSize: 16,
    fontWeight: '500',
  },
  tabDescription: {;
    fontSize: 12,
    fontWeight: '400',
    marginTop: 4,
  },
  tabContainer: {;
    flex: 1,
  },
  setupContainer: {;
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  setupCard: {;
    padding: 32,
    borderRadius: 16,
    maxWidth: 400,
    width: '100%',
    alignItems: 'center',
  },
  setupTitle: {;
    fontSize: 24,
    fontWeight: '700',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  setupDescription: {;
    fontSize: 16,
    lineHeight: 22,
    marginBottom: 24,
    textAlign: 'center',
  },
  setupBenefits: {;
    width: '100%',
    marginBottom: 24,
  },
  benefitItem: {;
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  benefitText: {;
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },
  setupButton: {;
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderRadius: 12,
    marginBottom: 16,
    width: '100%',
    alignItems: 'center',
  },
  setupButtonText: {;
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },
  secondaryButton: {;
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderRadius: 12,
    borderWidth: 1,
    width: '100%',
    alignItems: 'center',
  },
  secondaryButtonText: {;
    fontSize: 16,
    fontWeight: '500',
  },
});
