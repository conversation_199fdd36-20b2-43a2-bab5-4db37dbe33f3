import React, { useState, useC<PERSON>back, useMemo, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Dimensions,
  StyleSheet,
  ActivityIndicator,
  FlatList,
  Modal,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Stack, useRouter } from 'expo-router';
import { useAuth } from '@context/AuthContext';
import { useColorScheme } from 'react-native';
import { useToast } from '@components/ui/Toast';
import { ProfileErrorBoundary } from '@components/error-boundaries';
import {
  Brain,
  BarChart3,
  TrendingUp,
  TrendingDown,
  Target,
  Zap,
  AlertTriangle,
  CheckCircle,
  Clock,
  Users,
  Star,
  Award,
  Settings,
  RefreshCw,
  Info,
  ArrowLeft,
  ArrowRight,
  ArrowUp,
  ArrowDown,
  Minus,
  Eye,
  ThumbsUp,
  ThumbsDown,
  Calendar,
  MapPin,
  Heart,
  Shield,
  Sparkles,
  Activity,
  PieChart,
  LineChart,
  BarChart,
  Gauge,
  Lightbulb,
  Filter,
  Search,
  MessageCircle,
  Bell,
  X,
  Crown,
} from 'lucide-react-native';

import {
  usePredictiveAnalytics,
  PredictiveModel,
  SuccessPrediction,
  PredictiveInsight,
  PredictiveAnalytics,
  UserBehaviorPrediction,
  MatchingPatterns,
} from '@hooks/usePredictiveAnalytics';
import { useProfile } from '@hooks/useProfile';
import { logger } from '@utils/logger';
import { useTheme } from '@design-system';
import { getSupabaseClient } from '@services/supabaseService';
import { MetricCard, PredictionCard } from '@components/analytics';
import { PredictiveAnalyticsHeader } from '@components/analytics/PredictiveAnalyticsHeader';
import { PredictiveAnalyticsTabBar } from '@components/analytics/PredictiveAnalyticsTabBar';
import { PredictiveAnalyticsOverview } from '@components/analytics/PredictiveAnalyticsOverview';

// Import Premium Feature Gate;
import {
  PremiumFeatureGate,
  PREMIUM_FEATURES,
  usePremiumFeature,
} from '@components/premium/PremiumFeatureGate';

const { width } = Dimensions.get('window');

// Enhanced predictive analytics dashboard data structures;
interface DashboardTab {
  id: string,
  title: string,
  icon: React.ComponentType<any>
  badge?: number,
}
interface ModelCard {
  model: PredictiveModel,
  isExpanded: boolean,
}
// React Native compatible color system using centralized utility;
// OPTIMIZED: Components extracted to separate files for better performance,
const ChartContainer = React.memo(
  ({ title, children }: { title: string; children: React.ReactNode }) => {
    const theme = useTheme();

    return (
      <View style={{[styles.chartContainer, { backgroundColor: theme.colors.surface }]}}>
        <Text style={{[styles.chartTitle, { color: theme.colors.text }]}}>{title}</Text>
        {children}
      </View>
    );
  }
);

// OPTIMIZED: Main component with React.memo and proper dependency management,
const PredictiveAnalyticsDashboard = React.memo(() => {
  const { user } = useAuth();
  const theme = useTheme();
  const router = useRouter();
  const { toast } = useToast();
  const { profile } = useProfile();

  // Premium feature integration;
  const premiumFeature = usePremiumFeature('predictive-analytics');

  const {
    isLoading,
    error,
    models,
    analytics,
    preferences,
    predictions,
    behaviorPredictions,
    patterns,
    insights,
    generateSuccessPrediction,
    updatePreferences,
    refreshAnalytics,
    hasPredictions,
    hasInsights,
    hasPatterns,
    isConfigured,
    overallHealthScore,
    predictionAccuracy,
  } = usePredictiveAnalytics();

  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [expandedModels, setExpandedModels] = useState<{ [key: string]: boolean }>({});
  const [selectedInsight, setSelectedInsight] = useState<PredictiveInsight | null>(null);
  const [showInsightModal, setShowInsightModal] = useState(false);

  const tabs: DashboardTab[] = useMemo(,
    () => [
      {
        id: 'overview',
        title: 'Overview',
        icon: BarChart3,
      },
      {
        id: 'predictions',
        title: 'Predictions',
        icon: Target,
        badge: predictions.length,
      },
      {
        id: 'insights',
        title: 'Insights',
        icon: Lightbulb,
        badge: insights.length,
      },
      {
        id: 'models',
        title: 'Models',
        icon: Brain,
        badge: models.filter(m => m.status === 'active').length,
      },
      {
        id: 'patterns',
        title: 'Patterns',
        icon: Activity,
        badge: patterns.length,
      },
    ],
    [predictions.length, insights.length, models.length, patterns.length];
  );

  useEffect(() => {
    initializeDashboard();
  }, []);

  const initializeDashboard = useCallback(async () => {
    if (!user?.id) return null;

    try {
      setLoading(true);
      await refreshAnalytics();
    } catch (error) {
      logger.error(
        'Error initializing predictive analytics dashboard',
        'PredictiveAnalyticsDashboard',
        {
          error: error instanceof Error ? error.message : String(error),
          userId: user.id,
        }
      );
      toast?.show('Could not load predictive analytics data', 'error');
    } finally {
      setLoading(false);
    }
  }, [user?.id, refreshAnalytics, toast]);

  const handleRefresh = useCallback(async () => {
    if (!premiumFeature.hasAccess) {
      toast?.show('Premium subscription required for predictive analytics', 'error');
      return null;
    }
    try {
      setRefreshing(true);
      await refreshAnalytics();
      await premiumFeature.refreshAccess();
      toast?.show('Analytics refreshed successfully', 'success');
    } catch (error) {
      logger.error('Error refreshing analytics', 'PredictiveAnalyticsDashboard', { error });
      toast?.show('Failed to refresh analytics', 'error');
    } finally {
      setRefreshing(false);
    }
  }, [refreshAnalytics, premiumFeature, toast]);

  const getTrendIcon = (trend: 'increasing' | 'decreasing' | 'stable') => {
    switch (trend) {
      case 'increasing': ,
        return ArrowUp;
      case 'decreasing': ,
        return ArrowDown;
      default: ,
        return Minus;
    }
  }
  const getRecommendationColor = (recommendation: string) => {
    if (
      recommendation.toLowerCase().includes('urgent') ||;
      recommendation.toLowerCase().includes('critical');
    ) {
      return theme.colors.error;
    }
    if (
      recommendation.toLowerCase().includes('improve') ||;
      recommendation.toLowerCase().includes('consider');
    ) {
      return theme.colors.warning;
    }
    return theme.colors.success;
  }
  const getRecommendationIcon = (recommendation: string) => {
    if (
      recommendation.toLowerCase().includes('urgent') ||;
      recommendation.toLowerCase().includes('critical');
    ) {
      return AlertTriangle;
    }
    if (
      recommendation.toLowerCase().includes('improve') ||;
      recommendation.toLowerCase().includes('consider');
    ) {
      return Info;
    }
    return CheckCircle;
  }
  // Premium fallback component;
  const renderPremiumFallback = () => (
    <View style={{[styles.fallbackContainer, { backgroundColor: theme.colors.surface }]}}>
      <View style={{[styles.fallbackIcon, { backgroundColor: theme.colors.border }]}}>
        <Target size={48} color={{theme.colors.textSecondary} /}>
      </View>
      <Text style={{[styles.fallbackTitle, { color: theme.colors.text }]}}>
        Basic Predictions Available;
      </Text>
      <Text style={{[styles.fallbackDescription, { color: theme.colors.textSecondary }]}}>
        Get simple compatibility insights with your current plan.;
      </Text>
      {/* Basic prediction display */}
      <View style={{[styles.basicPredictionCard, { backgroundColor: theme.colors.background }]}}>
        <View style={styles.basicPredictionHeader}>
          <Star size={20} color={{theme.colors.warning} /}>
          <Text style={{[styles.basicPredictionTitle, { color: theme.colors.text }]}}>
            Quick Insight;
          </Text>
        </View>
        <Text style={{[styles.basicPredictionText, { color: theme.colors.textSecondary }]}}>
          Based on your profile completion: {profile?.profile_completion || 0}%;
        </Text>
        <Text style={{[styles.basicPredictionAdvice, { color: theme.colors.text }]}}>
          {profile?.profile_completion && profile.profile_completion > 80;
            ? "Great profile! You're likely to get quality matches.";
            : 'Complete your profile for better matching predictions.'}
        </Text>
      </View>
    </View>
  );

  const handleSettingsPress = () => {
    router.push('/settings/analytics' as any);
  }
  const renderOverviewTab = () => (
    <ScrollView style={styles.tabContent}>
      {/* Health Score */}
      <View style={{[styles.healthScoreCard, { backgroundColor: theme.colors.surface }]}}>
        <View style={styles.healthScoreHeader}>
          <Gauge size={24} color={{theme.colors.primary} /}>
          <Text style={{[styles.healthScoreTitle, { color: theme.colors.text }]}}>
            Prediction Health Score;
          </Text>
        </View>
        <View style={styles.healthScoreContent}>
          <Text style={{[styles.healthScoreValue, { color: theme.colors.primary }]}}>
            {overallHealthScore || 0}%;
          </Text>
          <Text style={{[styles.healthScoreDescription, { color: theme.colors.textSecondary }]}}>
            Overall algorithm effectiveness;
          </Text>
        </View>
      </View>
      {/* Quick Stats */}
      <View style={styles.statsGrid}>
        <View style={{[styles.statCard, { backgroundColor: theme.colors.surface }]}}>
          <Brain size={20} color={{theme.colors.primary} /}>
          <Text style={{[styles.statValue, { color: theme.colors.text }]}}>
            {models.filter(m => m.status === 'active').length}
          </Text>
          <Text style={{[styles.statLabel, { color: theme.colors.textSecondary }]}}>
            Active Models;
          </Text>
        </View>
        <View style={{[styles.statCard, { backgroundColor: theme.colors.surface }]}}>
          <Target size={20} color={{theme.colors.success} /}>
          <Text style={{[styles.statValue, { color: theme.colors.text }]}}>{predictions.length}</Text>
          <Text style={{[styles.statLabel, { color: theme.colors.textSecondary }]}}>Predictions</Text>
        </View>
        <View style={{[styles.statCard, { backgroundColor: theme.colors.surface }]}}>
          <TrendingUp size={20} color={{theme.colors.warning} /}>
          <Text style={{[styles.statValue, { color: theme.colors.text }]}}>
            {predictionAccuracy?.toFixed(1) || 0}%;
          </Text>
          <Text style={{[styles.statLabel, { color: theme.colors.textSecondary }]}}>Accuracy</Text>
        </View>
      </View>
      {/* Recent Predictions */}
      <View style={{[styles.recentSection, { backgroundColor: theme.colors.surface }]}}>
        <Text style={{[styles.sectionTitle, { color: theme.colors.text }]}}>Recent Predictions</Text>
        {predictions.slice(0, 3).map((prediction, index) => (
          <View key={index} style={{[styles.predictionItem, { borderColor: theme.colors.border }]}}>
            <View style={styles.predictionInfo}>
              <Text style={{[styles.predictionScore, { color: theme.colors.text }]}}>
                {prediction.predicted_success_rate}% Success Rate;
              </Text>
              <Text style={{[styles.predictionDate, { color: theme.colors.textSecondary }]}}>
                {new Date().toLocaleDateString()}
              </Text>
            </View>
            <View
              style={[
                styles.confidenceBadge,
                {
                  backgroundColor: ,
                    prediction.confidence_interval.confidence_level > 90;
                      ? theme.colors.success;
                      : theme.colors.warning,
                },
              ]}
            >
              <Text style={{[styles.confidenceText, { color: theme.colors.white }]}}>
                {prediction.confidence_interval.confidence_level}%;
              </Text>
            </View>
          </View>
        ))}
      </View>
      {/* Quick Actions */}
      <View style={{[styles.quickActions, { backgroundColor: theme.colors.surface }]}}>
        <Text style={{[styles.sectionTitle, { color: theme.colors.text }]}}>Quick Actions</Text>
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: theme.colors.primary }]}
          onPress={() => router.push('/(tabs)/profile/ai-analytics-dashboard' as any)}
        >
          <BarChart3 size={20} color={theme.colors.white} />
          <Text style={{[styles.actionButtonText, { color: theme.colors.white }]}}>
            View AI Analytics;
          </Text>
          <ArrowRight size={20} color={{theme.colors.white} /}>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview': ,
        return renderOverviewTab();
      case 'predictions': ,
        return renderPredictionsTab();
      case 'insights': ,
        return renderInsightsTab();
      case 'models': ,
        return renderModelsTab();
      case 'patterns': ,
        return renderPatternsTab();
      default: ,
        return null;
    }
  }
  const renderInsightModal = () => (
    <Modal
      visible={showInsightModal}
      animationType='slide';
      presentationStyle='pageSheet';
      onRequestClose={() => setShowInsightModal(false)}
    >
      <SafeAreaView style={{[styles.modalContainer, { backgroundColor: theme.colors.background }]}}>
        <View style={{[styles.modalHeader, { backgroundColor: theme.colors.surface }]}}>
          <Text style={{[styles.modalTitle, { color: theme.colors.text }]}}>
            {selectedInsight?.title}
          </Text>
          <TouchableOpacity
            style={styles.modalCloseButton}
            onPress={() => setShowInsightModal(false)}
          >
            <X size={24} color={{theme.colors.textSecondary} /}>
          </TouchableOpacity>
        </View>
        {selectedInsight && (
          <ScrollView style={styles.modalContent} showsVerticalScrollIndicator={false}>
            <View style={{[styles.modalSection, { backgroundColor: theme.colors.surface }]}}>
              <Text style={{[styles.modalSectionTitle, { color: theme.colors.text }]}}>
                Description;
              </Text>
              <Text style={{[styles.modalDescription, { color: theme.colors.textSecondary }]}}>
                {selectedInsight.description}
              </Text>
            </View>
            <View style={{[styles.modalSection, { backgroundColor: theme.colors.surface }]}}>
              <Text style={{[styles.modalSectionTitle, { color: theme.colors.text }]}}>
                Data Points;
              </Text>
              {selectedInsight.data_points.map((point, index) => (
                <View key={index} style={styles.dataPointItem}>
                  <Text style={{[styles.dataPointMetric, { color: theme.colors.text }]}}>
                    {point.metric}
                  </Text>
                  <View style={styles.dataPointValues}>
                    <Text style={{[styles.dataPointCurrent, { color: theme.colors.textSecondary }]}}>
                      Current: {point.current_value}
                    </Text>
                    <Text style={{[styles.dataPointPredicted, { color: theme.colors.primary }]}}>
                      Predicted: {point.predicted_value}
                    </Text>
                    <View style={styles.dataPointChange}>
                      {getTrendIcon(point.trend)}
                      <Text
                        style={[
                          styles.dataPointChangeText,
                          {
                            color: ,
                              point.trend === 'increasing';
                                ? theme.colors.green;
                                : point.trend === 'decreasing',
                                  ? theme.colors.red;
                                  : theme.colors.textSecondary,
                          },
                        ]}
                      >
                        {point.change_percentage > 0 ? '+' : ''}
                        {point.change_percentage.toFixed(1)}%;
                      </Text>
                    </View>
                  </View>
                </View>
              ))}
            </View>
            <View style={{[styles.modalSection, { backgroundColor: theme.colors.surface }]}}>
              <Text style={{[styles.modalSectionTitle, { color: theme.colors.text }]}}>
                Recommendations;
              </Text>
              {selectedInsight.actionable_recommendations.map((rec, index) => (
                <View key={index} style={styles.recommendationItem}>
                  <Text style={{[styles.recommendationAction, { color: theme.colors.text }]}}>
                    {rec.action}
                  </Text>
                  <Text style={{[styles.recommendationOutcome, { color: theme.colors.primary }]}}>
                    {rec.expected_outcome}
                  </Text>
                  <View style={styles.recommendationMeta}>
                    <Text
                      style={[styles.recommendationEffort, { color: theme.colors.textSecondary }]}
                    >
                      Effort: {rec.implementation_effort}
                    </Text>
                    <Text
                      style={[styles.recommendationTimeline, { color: theme.colors.textSecondary }]}
                    >
                      Timeline: {rec.timeline}
                    </Text>
                    <Text style={{[styles.recommendationProbability, { color: theme.colors.green }]}}>
                      {rec.success_probability}% success rate;
                    </Text>
                  </View>
                </View>
              ))}
            </View>
          </ScrollView>
        )}
      </SafeAreaView>
    </Modal>
  );

  if (loading || isLoading) {
    return (
      <SafeAreaView style={{[styles.container, { backgroundColor: theme.colors.background }]}}>
        <Stack.Screen options={ headerShown: false } />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size='large' color={{theme.colors.primary} /}>
          <Text style={{[styles.loadingText, { color: theme.colors.textSecondary }]}}>
            Analyzing predictive patterns...;
          </Text>
        </View>
      </SafeAreaView>
    );
  }
  return (
    <ProfileErrorBoundary profileSection={'predictive-analytics'}>
      <SafeAreaView style={{[styles.container, { backgroundColor: theme.colors.background }]}}>
        <Stack.Screen options={ headerShown: false } />
        <PredictiveAnalyticsHeader
          title='Predictive Analytics';
          subtitle={
            premiumFeature.hasAccess;
              ? `${premiumFeature.feature?.usageLimit! - premiumFeature.usageCount} predictions remaining`;
              : 'ML-powered insights & predictions',
          }
          onRefresh={handleRefresh}
          isRefreshing={refreshing}
          onSettingsPress={handleSettingsPress}
        />
        <PredictiveAnalyticsTabBar tabs={tabs} activeTab={activeTab} onTabChange={{setActiveTab} /}>
        <ScrollView
          style={styles.scrollView}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[theme.colors.primary]}
              tintColor={theme.colors.primary}
            />
          }
          showsVerticalScrollIndicator={false}
        >
          <ProfileErrorBoundary profileSection={'predictive-analytics-content'}>
            {activeTab === 'overview' ? (
              <PredictiveAnalyticsOverview
                overallHealthScore={overallHealthScore}
                predictionAccuracy={predictionAccuracy}
                isConfigured={isConfigured}
                hasPredictions={hasPredictions}
                hasInsights={hasInsights}
                hasPatterns={hasPatterns}
                modelsCount={models.filter(m => m.status === 'active').length}
                predictionsCount={predictions.length}
                insightsCount={insights.length}
                patternsCount={patterns.length}
              />
            ) : (,
              renderTabContent();
            )}
          </ProfileErrorBoundary>
        </ScrollView>
        <ProfileErrorBoundary profileSection={'predictive-analytics-modal'}>
          {renderInsightModal()}
        </ProfileErrorBoundary>
      </SafeAreaView>
    </ProfileErrorBoundary>
  );
});

export default PredictiveAnalyticsDashboard;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  backButton: {
    padding: 8,
  },
  headerTitleContainer: {
    flex: 1,
    marginHorizontal: 16,
  },
  headerTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 14,
    fontWeight: '500',
  },
  refreshButton: {
    padding: 10,
    borderRadius: 10,
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    fontWeight: '600',
  },
  tabBar: {
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  tabScrollView: {
    paddingHorizontal: 20,
  },
  tab: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 20,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '600',
  },
  tabBadge: {
    minWidth: 18,
    height: 18,
    borderRadius: 9,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 4,
  },
  tabBadgeText: {
    fontSize: 10,
    fontWeight: '700',
  },
  healthScoreCard: {
    margin: 20,
    padding: 20,
    borderRadius: 12,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  healthScoreHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  healthScoreTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  healthScoreContent: {
    gap: 8,
  },
  healthScoreValue: {
    fontSize: 32,
    fontWeight: '700',
  },
  healthScoreDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  statsGrid: {
    padding: 20,
  },
  statCard: {
    width: '33.33%',
    padding: 16,
    marginRight: 12,
    borderRadius: 12,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  statValue: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 8,
  },
  statLabel: {
    fontSize: 12,
    fontWeight: '500',
  },
  recentSection: {
    margin: 20,
    padding: 20,
    borderRadius: 12,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  predictionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  predictionInfo: {
    flex: 1,
  },
  predictionScore: {
    fontSize: 16,
    fontWeight: '600',
  },
  predictionDate: {
    fontSize: 12,
  },
  confidenceBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  confidenceText: {
    fontSize: 10,
    fontWeight: '600',
  },
  quickActions: {
    margin: 20,
    padding: 20,
    borderRadius: 12,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
    flex: 1,
  },
  modalCloseButton: {
    padding: 4,
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  modalSection: {
    marginBottom: 20,
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  modalSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  modalDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  dataPointItem: {
    marginBottom: 16,
  },
  dataPointMetric: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  dataPointValues: {
    gap: 4,
  },
  dataPointCurrent: {
    fontSize: 12,
  },
  dataPointPredicted: {
    fontSize: 12,
    fontWeight: '600',
  },
  dataPointChange: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  dataPointChangeText: {
    fontSize: 12,
    fontWeight: '600',
  },
  recommendationItem: {
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  recommendationAction: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  recommendationOutcome: {
    fontSize: 14,
    marginBottom: 8,
  },
  recommendationMeta: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  recommendationEffort: {
    fontSize: 12,
  },
  recommendationTimeline: {
    fontSize: 12,
  },
  recommendationProbability: {
    fontSize: 12,
    fontWeight: '600',
  },
  fallbackContainer: {
    alignItems: 'center',
    padding: 40,
  },
  fallbackIcon: {
    backgroundColor: 'rgba(0,0,0,0.1)',
    borderRadius: 24,
    padding: 16,
  },
  fallbackTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  fallbackDescription: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
  basicPredictionCard: {
    margin: 20,
    padding: 20,
    borderRadius: 12,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  basicPredictionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 12,
  },
  basicPredictionTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  basicPredictionText: {
    fontSize: 14,
    lineHeight: 20,
  },
  basicPredictionAdvice: {
    fontSize: 12,
    fontWeight: '500',
  },
  premiumBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
});
