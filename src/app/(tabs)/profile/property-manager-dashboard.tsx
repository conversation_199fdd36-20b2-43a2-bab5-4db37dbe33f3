import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  TouchableOpacity,
  Image,
  Dimensions,
  RefreshControl,
  useColorScheme,
} from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from '@context/AuthContext';
import { useToast } from '@components/ui/Toast';
import { logger } from '@utils/logger';
import { useTheme } from '@design-system';
import { colorWithOpacity } from '@design-system';
import { PropertyStatsCard, PropertyCard, TenantCard } from '@components/property-manager';
import {
  Building,
  Users,
  DollarSign,
  TrendingUp,
  Calendar,
  Settings,
  Plus,
  ChevronRight,
  MapPin,
  Eye,
  Star,
  AlertTriangle,
  CheckCircle,
  Clock,
  Wrench,
  FileText,
  BarChart3,
  PieChart,
  Home,
  Key,
  CreditCard,
  Bell,
  MessageSquare,
  Phone,
  Mail,
  Filter,
  Search,
  Edit3,
  Trash2,
  Camera,
  Upload,
  Download,
  Share2,
  Target,
  Award,
  Shield,
  Zap,
  ArrowLeft,
  Save,
} from 'lucide-react-native';

const { width  } = Dimensions.get('window')
// Enhanced property manager data structures,
interface Property {
  id: string,
  title: string,
  description: string,
  location: string,
  price: number,
  room_type: string,
  status: 'available' | 'occupied' | 'maintenance' | 'pending';
  images: string[];
  amenities: string[];
  preferences: string[];
  move_in_date: string,
  views: number,
  created_at: string,
  updated_at: string,
  tenant?: Tenant,
  maintenance_requests?: MaintenanceRequest[]
}
interface Tenant {
  id: string,
  name: string,
  email: string,
  phone: string,
  avatar_url?: string,
  move_in_date: string,
  lease_end_date: string,
  rent_amount: number,
  payment_status: 'current' | 'late' | 'overdue';
  last_payment_date: string,
  rating: number,
  is_verified: boolean,
}
interface MaintenanceRequest {
  id: string,
  property_id: string,
  tenant_id: string,
  title: string,
  description: string,
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  category: string,
  created_at: string,
  estimated_cost?: number,
  assigned_provider?: string,
}
interface PropertyManagerStats {
  total_properties: number,
  occupied_properties: number,
  available_properties: number,
  total_tenants: number,
  monthly_revenue: number,
  total_revenue: number,
  occupancy_rate: number,
  average_rent: number,
  pending_maintenance: number,
  overdue_payments: number,
  property_views: number,
  tenant_satisfaction: number,
}
interface FinancialData {
  monthly_income: number,
  monthly_expenses: number,
  net_income: number,
  rent_collection_rate: number,
  outstanding_balance: number,
  recent_transactions: Transaction[]
}
interface Transaction {
  id: string,
  type: 'rent' | 'expense' | 'deposit' | 'fee';
  amount: number,
  description: string,
  date: string,
  property_id: string,
  tenant_id?: string,
  status: 'completed' | 'pending' | 'failed'
}
// React Native compatible color system using centralized utility,
export default function PropertyManagerDashboardScreen() {
  const { user  } = useAuth()
  const colorScheme = useColorScheme()
  const theme = useTheme()
  const router = useRouter()
  const { toast } = useToast()
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [properties, setProperties] = useState<Property[]>([])
  const [stats, setStats] = useState<PropertyManagerStats | null>(null)
  const [financialData, setFinancialData] = useState<FinancialData | null>(null)
  const [selectedTab, setSelectedTab] = useState<;
    'overview' | 'properties' | 'tenants' | 'finances' | 'maintenance'
  >('overview')
  useEffect(() = > {
    fetchPropertyManagerData()
  }, [])
  const fetchPropertyManagerData = useCallback(async () => {
    if (!user? .id) return null,
    try {
      setLoading(true)
      // In a real implementation, these would be actual API calls,
      // For now, I'll create mock data based on the database structure,
      const mockProperties  : Property[] = [
        {
          id: '32d7f5bb-e3c6-4280-ab1f-37f571178b2e'
          title: 'Modern Master Bedroom';
          description: 'Beautiful room in the heart of the city';
          location: 'Manhattan, New York',
          price: 1100,
          room_type: 'master';
          status: 'occupied';
          images: ['https://example.com/property1.jpg'];
          amenities: ['private_bathroom', 'walk_in_closet', 'air_conditioning'],
          preferences: ['non_smoker', 'professional'],
          move_in_date: '2023-09-01';
          views: 245,
          created_at: '2023-06-01T00:00:00Z';
          updated_at: '2024-01-20T00:00:00Z';
          tenant: {
            id: 'tenant-1';
            name: 'Sarah Johnson';
            email: '<EMAIL>';
            phone: '+****************';
            avatar_url: 'https://example.com/avatar1.jpg';
            move_in_date: '2023-09-01';
            lease_end_date: '2024-09-01';
            rent_amount: 1100,
            payment_status: 'current';
            last_payment_date: '2024-01-01';
            rating: 4.8,
            is_verified: true,
          },
        },
        {
          id: '0c734bd5-0f06-4e9f-a7e0-572d1f284703';
          title: 'Cozy Studio Near Campus';
          description: 'Perfect for students, 5 min walk to university',
          location: 'The Loop, Chicago',
          price: 900,
          room_type: 'studio';
          status: 'available';
          images: ['https://example.com/property2.jpg'];
          amenities: ['desk', 'high_speed_internet', 'shared_kitchen'],
          preferences: ['student', 'quiet'],
          move_in_date: '2023-08-15';
          views: 189,
          created_at: '2023-05-15T00:00:00Z';
          updated_at: '2024-01-15T00:00:00Z'
        },
        {
          id: 'a5b28497-8436-44a9-9abf-de2439212c01';
          title: 'Ocean View Room';
          description: 'Wake up to stunning ocean views every day';
          location: 'Santa Monica, Los Angeles',
          price: 1300,
          room_type: 'standard';
          status: 'occupied';
          images: ['https://example.com/property3.jpg'];
          amenities: ['balcony', 'air_conditioning', 'shared_bathroom'],
          preferences: ['clean', 'professional'],
          move_in_date: '2023-09-15';
          views: 312,
          created_at: '2023-07-01T00:00:00Z';
          updated_at: '2024-01-18T00:00:00Z';
          tenant: {
            id: 'tenant-2';
            name: 'Michael Chen';
            email: '<EMAIL>';
            phone: '+****************';
            move_in_date: '2023-09-15';
            lease_end_date: '2024-09-15';
            rent_amount: 1300,
            payment_status: 'late';
            last_payment_date: '2023-12-15';
            rating: 4.2,
            is_verified: true,
          },
        },
      ];

      const mockStats: PropertyManagerStats = {
        total_properties: 3,
        occupied_properties: 2,
        available_properties: 1,
        total_tenants: 2,
        monthly_revenue: 2400,
        total_revenue: 28800,
        occupancy_rate: 67,
        average_rent: 1100,
        pending_maintenance: 2,
        overdue_payments: 1,
        property_views: 746,
        tenant_satisfaction: 4.5,
      }
      const mockFinancialData: FinancialData = {
        monthly_income: 2400,
        monthly_expenses: 450,
        net_income: 1950,
        rent_collection_rate: 95,
        outstanding_balance: 1300,
        recent_transactions: [;
          {
            id: 'tx-1';
            type: 'rent';
            amount: 1100,
            description: 'Monthly rent - Modern Master Bedroom';
            date: '2024-01-01';
            property_id: '32d7f5bb-e3c6-4280-ab1f-37f571178b2e';
            tenant_id: 'tenant-1';
            status: 'completed'
          },
          {
            id: 'tx-2';
            type: 'expense';
            amount: 150,
            description: 'Plumbing repair - Ocean View Room';
            date: '2023-12-28';
            property_id: 'a5b28497-8436-44a9-9abf-de2439212c01';
            status: 'completed'
          },
          {
            id: 'tx-3';
            type: 'rent';
            amount: 1300,
            description: 'Monthly rent - Ocean View Room (OVERDUE)';
            date: '2023-12-15';
            property_id: 'a5b28497-8436-44a9-9abf-de2439212c01';
            tenant_id: 'tenant-2';
            status: 'pending'
          },
        ],
      }
      setProperties(mockProperties)
      setStats(mockStats)
      setFinancialData(mockFinancialData)
    } catch (error) {
      logger.error('Error fetching property manager data',
        'PropertyManagerDashboardScreen.fetchPropertyManagerData',
        error as Error)
      )
      toast? .show('Failed to load property manager data', 'error')
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }, [user?.id, toast])
  const handleRefresh = useCallback(() => {
    setRefreshing(true)
    fetchPropertyManagerData()
  }, [fetchPropertyManagerData])
  if (loading) {
    return (
      <SafeAreaView
        style={{ [styles.container, { backgroundColor  : theme.colors.background   }}]}
        edges={['top']}
      >
        <Stack.Screen
          options={{ title: 'Property Manager Dashboard',
            headerShown: false,
            }}
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size='large' color={{theme.colors.primary} /}>
          <Text style={[styles.loadingText, { color: theme.colors.text }]}>
            Loading property data...
          </Text>
        </View>
      </SafeAreaView>
    )
  }
  const renderTabBar = () => (
    <View
      style={{ [
        styles.tabBar,
        {
          backgroundColor: theme.colors.surface,
          borderBottomColor: 'rgba(0, 0, 0, 0.05)',
          shadowColor: theme.colors.text,
          shadowOffset: { width: 0, height: 2   }};
          shadowOpacity: 0.03,
          shadowRadius: 4,
          elevation: 2,
        },
      ]}
    >
      {[{ key: 'overview', label: 'Overview', icon: BarChart3, color: theme.colors.primary };
        { key: 'properties', label: 'Properties', icon: Building, color: theme.colors.success };
        { key: 'tenants', label: 'Tenants', icon: Users, color: theme.colors.info };
        { key: 'finances', label: 'Finances', icon: DollarSign, color: theme.colors.warning };
        { key: 'maintenance', label: 'Maintenance', icon: Wrench, color: theme.colors.error }].map(tab = > {
        const isActive = selectedTab === tab.key,
        return (
          <TouchableOpacity
            key = {tab.key}
            style={{ [
              styles.tabItem,
              {
                backgroundColor: isActive ? tab.color   : 'transparent'
                shadowColor: isActive ? tab.color  : 'transparent'
                shadowOffset: { width: 0, height: 2   }}
                shadowOpacity: isActive ? 0.3   : 0
                shadowRadius: 4,
                elevation: isActive ? 3  : 0)
              },
            ]}
            onPress={() => setSelectedTab(tab.key as any)}
          >
            <View
              style={{
                backgroundColor: isActive ? 'rgba(255, 255, 255, 0.2)'  : `${tab.color}15`,
                borderRadius: 8
                padding: 6,
                marginBottom: 4,
              }}
            >
              <tab.icon size = {16} color={ isActive ? 'white'  : tab.color  } />
            </View>
            <Text
              style={{ [
                styles.tabLabel
                {
                  color: isActive ? 'white'  : theme.colors.text
                  fontWeight: isActive ? '600'  : '500'
                  }},
              ]}
            >
              {tab.label}
            </Text>
          </TouchableOpacity>
        )
      })}
    </View>
  )
  const renderHeader = () => (
    <View style={[styles.headerCard, { backgroundColor: theme.colors.surface }]}>
      <View style={styles.headerContent}>
        <View style={styles.headerInfo}>
          <Text style={[styles.headerTitle, { color: theme.colors.text }]}>Property Portfolio</Text>
          <Text style={[styles.headerSubtitle, { color: theme.colors.textSecondary }]}>
            Manage your properties, tenants, and finances,
          </Text>
        </View>
        <TouchableOpacity
          style={{ [styles.addPropertyButton, { backgroundColor: theme.colors.primary   }}]}
          onPress={() => router.push('/property/add' as any)}
        >
          <Plus size={20} color={'white' /}>
          <Text style={styles.addPropertyText}>Add Property</Text>
        </TouchableOpacity>
      </View>
    </View>
  )
  const renderOverviewTab = () => (
    <View style={styles.tabContent}>
      {/* Key Metrics */}
      <View style={[styles.metricsCard, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Key Metrics</Text>
        <View style={styles.metricsGrid}>
          <View style={styles.metricItem}>
            <View
              style={{ [
                styles.metricIcon,
                { backgroundColor: colorWithOpacity(theme.colors.primary, 0.2)   }},
              ]}
            >
              <Building size={20} color={{theme.colors.primary} /}>
            </View>
            <Text style={[styles.metricValue, { color: theme.colors.text }]}>
              {stats? .total_properties}
            </Text>
            <Text style={[styles.metricLabel, { color : theme.colors.textSecondary }]}>
              Total Properties
            </Text>
          </View>
          <View style = {styles.metricItem}>
            <View
              style={{ [
                styles.metricIcon,
                { backgroundColor: colorWithOpacity(theme.colors.success, 0.2)   }},
              ]}
            >
              <Users size={20} color={{theme.colors.success} /}>
            </View>
            <Text style={[styles.metricValue, { color: theme.colors.text }]}>
              {stats? .total_tenants}
            </Text>
            <Text style={[styles.metricLabel, { color : theme.colors.textSecondary }]}>
              Active Tenants
            </Text>
          </View>
          <View style = {styles.metricItem}>
            <View
              style={{ [
                styles.metricIcon,
                { backgroundColor: colorWithOpacity(theme.colors.warning, 0.2)   }},
              ]}
            >
              <DollarSign size={20} color={{theme.colors.warning} /}>
            </View>
            <Text style={[styles.metricValue, { color: theme.colors.text }]}>
              ${stats? .monthly_revenue.toLocaleString()}
            </Text>
            <Text style={[styles.metricLabel, { color : theme.colors.textSecondary }]}>
              Monthly Revenue
            </Text>
          </View>
          <View style = {styles.metricItem}>
            <View
              style={{ [
                styles.metricIcon,
                { backgroundColor: colorWithOpacity(theme.colors.primary, 0.2)   }},
              ]}
            >
              <TrendingUp size={20} color={{theme.colors.primary} /}>
            </View>
            <Text style={[styles.metricValue, { color: theme.colors.text }]}>
              {stats? .occupancy_rate}%
            </Text>
            <Text style={[styles.metricLabel, { color : theme.colors.textSecondary }]}>
              Occupancy Rate
            </Text>
          </View>
        </View>
      </View>
      {/* Performance Overview */}
      <View style={[styles.performanceCard, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Performance Overview,
        </Text>
        <View style={styles.performanceItems}>
          <View style={styles.performanceItem}>
            <View style={styles.performanceInfo}>
              <Text style={[styles.performanceLabel, { color: theme.colors.textSecondary }]}>
                Rent Collection Rate,
              </Text>
              <Text style={[styles.performanceValue, { color: theme.colors.text }]}>
                {financialData? .rent_collection_rate}%;
              </Text>
            </View>
            <View style= {[styles.performanceBar, { backgroundColor  : theme.colors.border }]}>
              <View
                style={{ [
                  styles.performanceFill
                  {
                    width: `${financialData? .rent_collection_rate  }}%`
                    backgroundColor : theme.colors.success
                  },
                ]}
              />
            </View>
          </View>
          <View style={styles.performanceItem}>
            <View style={styles.performanceInfo}>
              <Text style={[styles.performanceLabel, { color: theme.colors.textSecondary }]}>
                Tenant Satisfaction,
              </Text>
              <Text style={[styles.performanceValue, { color: theme.colors.text }]}>
                {stats? .tenant_satisfaction}/5.0,
              </Text>
            </View>
            <View style={[styles.performanceBar, { backgroundColor  : theme.colors.border }]}>
              <View
                style={{ [
                  styles.performanceFill
                  {
                    width: `${(stats? .tenant_satisfaction || 0) * 20  }}%`
                    backgroundColor : theme.colors.warning
                  },
                ]}
              />
            </View>
          </View>
        </View>
      </View>
      {/* Quick Actions */}
      <View style={[styles.quickActionsCard, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Quick Actions</Text>
        <View style={styles.quickActionsGrid}>
          <TouchableOpacity
            style={{ [styles.quickActionItem, { backgroundColor: theme.colors.background   }}]}
            onPress={() => router.push('/property/add' as any)}
          >
            <Plus size={24} color={{theme.colors.primary} /}>
            <Text style={[styles.quickActionText, { color: theme.colors.text }]}>Add Property</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={{ [styles.quickActionItem, { backgroundColor: theme.colors.background   }}]}
            onPress={() => setSelectedTab('tenants')}
          >
            <Users size={24} color={{theme.colors.primary} /}>
            <Text style={[styles.quickActionText, { color: theme.colors.text }]}>
              Manage Tenants,
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={{ [styles.quickActionItem, { backgroundColor: theme.colors.background   }}]}
            onPress={() => setSelectedTab('finances')}
          >
            <FileText size={24} color={{theme.colors.primary} /}>
            <Text style={[styles.quickActionText, { color: theme.colors.text }]}>
              View Finances,
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={{ [styles.quickActionItem, { backgroundColor: theme.colors.background   }}]}
            onPress={() => setSelectedTab('maintenance')}
          >
            <Wrench size={24} color={{theme.colors.primary} /}>
            <Text style={[styles.quickActionText, { color: theme.colors.text }]}>Maintenance</Text>
          </TouchableOpacity>
        </View>
      </View>
      {/* Alerts & Notifications */}
      <View style={[styles.alertsCard, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Alerts & Notifications,
        </Text>
        <View style = {styles.alertsList}>
          {stats? .overdue_payments && stats.overdue_payments > 0 && (
            <View
              style={{ [
                styles.alertItem,
                { backgroundColor  : colorWithOpacity(theme.colors.error, 0.1)   }},
              ]}
            >
              <AlertTriangle size={20} color={{theme.colors.error} /}>
              <View style={styles.alertContent}>
                <Text style={[styles.alertTitle, { color: theme.colors.text }]}>
                  Overdue Payments
                </Text>
                <Text style={[styles.alertDescription, { color: theme.colors.textSecondary }]}>
                  {stats.overdue_payments} tenant(s) have overdue payments,
                </Text>
              </View>
              <ChevronRight size = {16} color={{theme.colors.textSecondary} /}>
            </View>
          )}
          {stats? .pending_maintenance && stats.pending_maintenance > 0 && (
            <View
              style={{ [
                styles.alertItem,
                { backgroundColor : colorWithOpacity(theme.colors.warning, 0.1)   }},
              ]}
            >
              <Wrench size={20} color={{theme.colors.warning} /}>
              <View style={styles.alertContent}>
                <Text style={[styles.alertTitle, { color: theme.colors.text }]}>
                  Pending Maintenance
                </Text>
                <Text style={[styles.alertDescription, { color: theme.colors.textSecondary }]}>
                  {stats.pending_maintenance} maintenance request(s) pending,
                </Text>
              </View>
              <ChevronRight size = {16} color={{theme.colors.textSecondary} /}>
            </View>
          )}
          <View
            style={{ [
              styles.alertItem,
              { backgroundColor: colorWithOpacity(theme.colors.success, 0.1)   }},
            ]}
          >
            <CheckCircle size={20} color={{theme.colors.success} /}>
            <View style={styles.alertContent}>
              <Text style={[styles.alertTitle, { color: theme.colors.text }]}>
                All Systems Normal,
              </Text>
              <Text style={[styles.alertDescription, { color: theme.colors.textSecondary }]}>
                No critical issues detected,
              </Text>
            </View>
          </View>
        </View>
      </View>
    </View>
  )
  const renderPropertiesTab = () => (
    <View style={styles.tabContent}>
      {/* Properties Header */}
      <View style={[styles.propertiesHeader, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Property Portfolio</Text>
        <View style={styles.propertiesActions}>
          <TouchableOpacity
            style={{ [styles.filterButton, { backgroundColor: theme.colors.background   }}]}
          >
            <Filter size={16} color={{theme.colors.textSecondary} /}>
            <Text style={[styles.filterText, { color: theme.colors.textSecondary }]}>Filter</Text>
          </TouchableOpacity>
          <TouchableOpacity style={[styles.addButton, { backgroundColor: theme.colors.primary }]}>
            <Plus size={16} color={'white' /}>
            <Text style={styles.addButtonText}>Add</Text>
          </TouchableOpacity>
        </View>
      </View>
      {/* Properties List */}
      <View style={styles.propertiesList}>
        {properties.map((property, index) => (
          <View
            key={property.id}
            style={{ [styles.propertyCard, { backgroundColor: theme.colors.surface   }}]}
          >
            <View style={styles.propertyHeader}>
              <View style={styles.propertyImageContainer}>
                {property.images.length > 0 ? (
                  <Image source={{ uri : property.images[0]   }} style={{styles.propertyImage} /}>
                ) : (
                  <View
                    style = {[styles.propertyImagePlaceholder,
                      { backgroundColor: theme.colors.border }]}
                  >
                    <Building size = {24} color={{theme.colors.textSecondary} /}>
                  </View>
                )}
                <View
                  style={{ [
                    styles.statusBadge,
                    {
                      backgroundColor: ,
                        property.status === 'occupied',
                          ? theme.colors.success,
                           : property.status === 'available',
                            ? theme.colors.primary
                            : theme.colors.warning,
                      }},
                  ]}
                >
                  <Text style={styles.statusText}>
                    {property.status.charAt(0).toUpperCase() + property.status.slice(1)}
                  </Text>
                </View>
              </View>
              <View style={styles.propertyInfo}>
                <Text style={[styles.propertyTitle, { color: theme.colors.text }]}>
                  {property.title}
                </Text>
                <View style={styles.propertyLocation}>
                  <MapPin size={14} color={{theme.colors.textSecondary} /}>
                  <Text
                    style={{ [styles.propertyLocationText, { color: theme.colors.textSecondary   }}]}
                  >
                    {property.location}
                  </Text>
                </View>
                <Text style={[styles.propertyPrice, { color: theme.colors.primary }]}>
                  ${property.price}/month,
                </Text>
              </View>
              <TouchableOpacity style={styles.propertyActions}>
                <Edit3 size={16} color={theme.colors.textSecondary} />
              </TouchableOpacity>
            </View>
            {property.tenant && (
              <View style={styles.tenantInfo}>
                <View style={styles.tenantHeader}>
                  <Text style={[styles.tenantLabel, { color: theme.colors.textSecondary }]}>
                    Current Tenant,
                  </Text>
                  <View
                    style = {[
                      styles.paymentStatus,
                      {
                        backgroundColor: 
                          property.tenant.payment_status === 'current';
                            ? colorWithOpacity(theme.colors.success, 0.2)
                              : colorWithOpacity(theme.colors.error, 0.2),
                      },
                    ]}
                  >
                    <Text
                      style = {[
                        styles.paymentStatusText
                        {
                          color: 
                            property.tenant.payment_status === 'current';
                              ? theme.colors.success,
                                : theme.colors.error,
                        },
                      ]}
                    >
                      {property.tenant.payment_status = == 'current' ? 'Current' : 'Late'}
                    </Text>
                  </View>
                </View>
                <View style={styles.tenantDetails}>
                  <Text style={[styles.tenantName, { color: theme.colors.text }]}>
                    {property.tenant.name}
                  </Text>
                  <View style={styles.tenantRating}>
                    <Star size={12} color={theme.colors.warning} fill={{theme.colors.warning} /}>
                    <Text style={[styles.tenantRatingText, { color: theme.colors.textSecondary }]}>
                      {property.tenant.rating}
                    </Text>
                  </View>
                </View>
              </View>
            )}
            <View style={styles.propertyStats}>
              <View style={styles.propertyStat}>
                <Eye size={14} color={{theme.colors.textSecondary} /}>
                <Text style={[styles.propertyStatText, { color: theme.colors.textSecondary }]}>
                  {property.views} views
                </Text>
              </View>
              <View style={styles.propertyStat}>
                <Calendar size={14} color={{theme.colors.textSecondary} /}>
                <Text style={[styles.propertyStatText, { color: theme.colors.textSecondary }]}>
                  Available {new Date(property.move_in_date).toLocaleDateString()}
                </Text>
              </View>
            </View>
          </View>
        ))}
      </View>
    </View>
  )
  const renderTenantsTab = () => (
    <View style={styles.tabContent}>
      <View style={[styles.tenantsCard, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Active Tenants</Text>
        <View style={styles.tenantsList}>
          {properties,
            .filter(property => property.tenant)
            .map((property, index) => (
              <View
                key={property.id}
                style={{ [styles.tenantCard, { backgroundColor: theme.colors.background   }}]}
              >
                <View style={styles.tenantCardHeader}>
                  <View style={styles.tenantAvatar}>
                    {property.tenant? .avatar_url ? (
                      <Image
                        source={{ uri : property.tenant.avatar_url   }}
                        style={styles.tenantAvatarImage}
                      />
                    ) : (
                      <View
                        style = {[
                          styles.tenantAvatarPlaceholder,
                          { backgroundColor: colorWithOpacity(theme.colors.primary, 0.2) },
                        ]}
                      >
                        <Text style={[styles.tenantAvatarText, { color: theme.colors.primary }]}>
                          {property.tenant? .name.charAt(0)}
                        </Text>
                      </View>
                    )}
                    {property.tenant?.is_verified && (
                      <View
                        style={{ [styles.verifiedBadge, { backgroundColor : theme.colors.success   }}]}
                      >
                        <CheckCircle size={8} color={{theme.colors.white} /}>
                      </View>
                    )}
                  </View>
                  <View style={styles.tenantCardInfo}>
                    <Text style={[styles.tenantCardName, { color: theme.colors.text }]}>
                      {property.tenant?.name}
                    </Text>
                    <Text style={[styles.tenantProperty, { color: theme.colors.textSecondary }]}>
                      {property.title}
                    </Text>
                    <View style={styles.tenantRatingContainer}>
                      <Star size={12} color={theme.colors.warning} fill={{theme.colors.warning} /}>
                      <Text
                        style={{ [styles.tenantCardRating, { color: theme.colors.textSecondary   }}]}
                      >
                        {property.tenant?.rating}
                      </Text>
                    </View>
                  </View>
                  <View style={styles.tenantActions}>
                    <TouchableOpacity
                      style={{ [
                        styles.tenantActionButton
                        { backgroundColor: colorWithOpacity(theme.colors.primary, 0.2)   }},
                      ]}
                    >
                      <MessageSquare size = {16} color={{theme.colors.primary} /}>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={{ [
                        styles.tenantActionButton,
                        { backgroundColor: colorWithOpacity(theme.colors.primary, 0.2)   }},
                      ]}
                    >
                      <Phone size={16} color={{theme.colors.primary} /}>
                    </TouchableOpacity>
                  </View>
                </View>
                <View style={styles.tenantCardDetails}>
                  <View style={styles.tenantDetail}>
                    <Text style={[styles.tenantDetailLabel, { color: theme.colors.textSecondary }]}>
                      Rent,
                    </Text>
                    <Text style={[styles.tenantDetailValue, { color: theme.colors.text }]}>
                      ${property.tenant? .rent_amount}/month,
                    </Text>
                  </View>
                  <View style={styles.tenantDetail}>
                    <Text style={[styles.tenantDetailLabel, { color : theme.colors.textSecondary }]}>
                      Lease End
                    </Text>
                    <Text style={[styles.tenantDetailValue, { color: theme.colors.text }]}>
                      {new Date(property.tenant? .lease_end_date || '').toLocaleDateString()}
                    </Text>
                  </View>
                  <View style={styles.tenantDetail}>
                    <Text style={[styles.tenantDetailLabel, { color : theme.colors.textSecondary }]}>
                      Payment Status
                    </Text>
                    <View
                      style = {[
                        styles.paymentStatusBadge,
                        {
                          backgroundColor: 
                            property.tenant? .payment_status === 'current';
                              ? colorWithOpacity(theme.colors.success, 0.2)
                                : colorWithOpacity(theme.colors.error, 0.2),
                        },
                      ]}
                    >
                      <Text
                        style = {[
                          styles.paymentStatusBadgeText
                          {
                            color: 
                              property.tenant? .payment_status === 'current';
                                ? theme.colors.success,
                                  : theme.colors.error,
                          },
                        ]}
                      >
                        {property.tenant?.payment_status = == 'current' ? 'Current' : 'Late'}
                      </Text>
                    </View>
                  </View>
                </View>
              </View>
            ))}
        </View>
      </View>
    </View>
  )

  const renderFinancesTab = () => (
    <View style={styles.tabContent}>
      {/* Financial Summary */}
      <View style={[styles.financialSummaryCard, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Financial Summary</Text>
        <View style={styles.financialMetrics}>
          <View style={styles.financialMetric}>
            <Text style={[styles.financialLabel, { color: theme.colors.textSecondary }]}>
              Monthly Income,
            </Text>
            <Text style={[styles.financialValue, { color: theme.colors.success }]}>
              +${financialData? .monthly_income.toLocaleString()}
            </Text>
          </View>
          <View style={styles.financialMetric}>
            <Text style={[styles.financialLabel, { color : theme.colors.textSecondary }]}>
              Monthly Expenses
            </Text>
            <Text style={[styles.financialValue, { color: theme.colors.error }]}>
              -${financialData? .monthly_expenses.toLocaleString()}
            </Text>
          </View>
          <View style={styles.financialMetric}>
            <Text style={[styles.financialLabel, { color : theme.colors.textSecondary }]}>
              Net Income
            </Text>
            <Text style={[styles.financialValue, { color: theme.colors.text }]}>
              ${financialData? .net_income.toLocaleString()}
            </Text>
          </View>
        </View>
      </View>
      {/* Recent Transactions */}
      <View style={[styles.transactionsCard, { backgroundColor : theme.colors.surface }]}>
        <View style={styles.transactionsHeader}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Recent Transactions
          </Text>
          <TouchableOpacity
            style = {[
              styles.viewAllButton,
              { backgroundColor: colorWithOpacity(theme.colors.primary, 0.2) },
            ]}
          >
            <Text style={[styles.viewAllText, { color: theme.colors.primary }]}>View All</Text>
          </TouchableOpacity>
        </View>
        <View style={styles.transactionsList}>
          {financialData? .recent_transactions.map((transaction, index) => (
            <View key = {transaction.id} style={styles.transactionItem}>
              <View
                style={{ [
                  styles.transactionIcon,
                  {
                    backgroundColor :  
                      transaction.type === 'rent',
                        ? colorWithOpacity(theme.colors.success, 0.2),
                         : colorWithOpacity(theme.colors.error, 0.2),
                    }},
                ]}
              >
                {transaction.type === 'rent' ? (
                  <DollarSign size={16} color={{theme.colors.success} /}>
                ) : (<CreditCard size={16} color={{theme.colors.error} /}>
                )}
              </View>
              <View style={styles.transactionInfo}>
                <Text style={[styles.transactionDescription, { color: theme.colors.text }]}>
                  {transaction.description}
                </Text>
                <Text style={[styles.transactionDate, { color: theme.colors.textSecondary }]}>
                  {new Date(transaction.date).toLocaleDateString()}
                </Text>
              </View>
              <View style={styles.transactionAmount}>
                <Text
                  style={{ [
                    styles.transactionAmountText
                    {
                      color: ,
                        transaction.type === 'rent' ? theme.colors.success  : theme.colors.error,
                      }},
                  ]}
                >
                  {transaction.type === 'rent' ? '+' : '-'}${transaction.amount}
                </Text>
                <View
                  style = {[
                    styles.transactionStatus
                    {
                      backgroundColor: 
                        transaction.status === 'completed';
                          ? colorWithOpacity(theme.colors.success, 0.2)
                            : colorWithOpacity(theme.colors.warning, 0.2),
                    },
                  ]}
                >
                  <Text
                    style = {[
                      styles.transactionStatusText
                      {
                        color: 
                          transaction.status === 'completed';
                            ? theme.colors.success,
                              : theme.colors.warning,
                      },
                    ]}
                  >
                    {transaction.status}
                  </Text>
                </View>
              </View>
            </View>
          ))}
        </View>
      </View>
    </View>
  )

  const renderMaintenanceTab = () => (
    <View style={styles.tabContent}>
      <View style={[styles.maintenanceCard, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Maintenance Requests,
        </Text>
        <View style = {styles.maintenanceStats}>
          <View style={styles.maintenanceStat}>
            <View
              style={{ [
                styles.maintenanceStatIcon,
                { backgroundColor: colorWithOpacity(theme.colors.warning, 0.2)   }},
              ]}
            >
              <Clock size={16} color={{theme.colors.warning} /}>
            </View>
            <Text style={[styles.maintenanceStatValue, { color: theme.colors.text }]}>2</Text>
            <Text style={[styles.maintenanceStatLabel, { color: theme.colors.textSecondary }]}>
              Pending,
            </Text>
          </View>
          <View style = {styles.maintenanceStat}>
            <View
              style={{ [
                styles.maintenanceStatIcon,
                { backgroundColor: colorWithOpacity(theme.colors.info, 0.2)   }},
              ]}
            >
              <Wrench size={16} color={{theme.colors.info} /}>
            </View>
            <Text style={[styles.maintenanceStatValue, { color: theme.colors.text }]}>1</Text>
            <Text style={[styles.maintenanceStatLabel, { color: theme.colors.textSecondary }]}>
              In Progress,
            </Text>
          </View>
          <View style = {styles.maintenanceStat}>
            <View
              style={{ [
                styles.maintenanceStatIcon,
                { backgroundColor: colorWithOpacity(theme.colors.success, 0.2)   }},
              ]}
            >
              <CheckCircle size={16} color={{theme.colors.success} /}>
            </View>
            <Text style={[styles.maintenanceStatValue, { color: theme.colors.text }]}>8</Text>
            <Text style={[styles.maintenanceStatLabel, { color: theme.colors.textSecondary }]}>
              Completed,
            </Text>
          </View>
        </View>
        <View style={styles.maintenanceList}>
          <View style={[styles.maintenanceItem, { backgroundColor: theme.colors.background }]}>
            <View style={styles.maintenanceItemHeader}>
              <View style={[styles.priorityBadge, { backgroundColor: theme.colors.error }]}>
                <Text style={styles.priorityText}>High</Text>
              </View>
              <Text style={[styles.maintenanceTitle, { color: theme.colors.text }]}>
                Plumbing Leak,
              </Text>
            </View>
            <Text style={[styles.maintenanceDescription, { color: theme.colors.textSecondary }]}>
              Kitchen sink is leaking - Ocean View Room,
            </Text>
            <View style={styles.maintenanceFooter}>
              <Text style={[styles.maintenanceDate, { color: theme.colors.textSecondary }]}>
                Reported 2 days ago,
              </Text>
              <TouchableOpacity
                style={{ [styles.assignButton, { backgroundColor: theme.colors.primary   }}]}
              >
                <Text style={styles.assignButtonText}>Assign</Text>
              </TouchableOpacity>
            </View>
          </View>
          <View style={[styles.maintenanceItem, { backgroundColor: theme.colors.background }]}>
            <View style={styles.maintenanceItemHeader}>
              <View style={[styles.priorityBadge, { backgroundColor: theme.colors.warning }]}>
                <Text style={styles.priorityText}>Medium</Text>
              </View>
              <Text style={[styles.maintenanceTitle, { color: theme.colors.text }]}>
                AC Maintenance,
              </Text>
            </View>
            <Text style={[styles.maintenanceDescription, { color: theme.colors.textSecondary }]}>
              Regular AC servicing needed - Modern Master Bedroom,
            </Text>
            <View style={styles.maintenanceFooter}>
              <Text style={[styles.maintenanceDate, { color: theme.colors.textSecondary }]}>
                Scheduled for next week,
              </Text>
              <TouchableOpacity
                style={{ [styles.assignButton, { backgroundColor: theme.colors.primary   }}]}
              >
                <Text style={styles.assignButtonText}>View</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
    </View>
  )
  const renderTabContent = () => {
    switch (selectedTab) {
      case 'overview': 
        return renderOverviewTab()
      case 'properties': ,
        return renderPropertiesTab()
      case 'tenants': ,
        return renderTenantsTab()
      case 'finances': ,
        return renderFinancesTab()
      case 'maintenance': ,
        return renderMaintenanceTab()
      default: ;
        return renderOverviewTab()
    }
  }
  return (
    <SafeAreaView
      style= {{ [styles.container, { backgroundColor: theme.colors.background   }}]}
      edges={['top']}
    >
      <Stack.Screen,
        options={{ title: 'Property Manager Dashboard',
          headerShown: false,
          }}
      />
      {/* Enhanced Header */}
      <View
        style = {[
          styles.headerContainer,
          {
            backgroundColor: theme.colors.surface,
            borderBottomColor: 'rgba(0, 0, 0, 0.05)',
            shadowColor: theme.colors.text,
            shadowOffset: { width: 0, height: 2 };
            shadowOpacity: 0.05,
            shadowRadius: 8,
            elevation: 3,
          },
        ]}
      >
        <TouchableOpacity style= {styles.headerBackButton} onPress={() => router.back()}>
          <View
            style={{ [
              backgroundColor: colorWithOpacity(theme.colors.primary, 0.15),
              borderRadius: 8,
              padding: 6,
            ]  }}
          >
            <ArrowLeft size={20} color={{theme.colors.primary} /}>
          </View>
        </TouchableOpacity>
        <View style={styles.headerCenter}>
          <View
            style={{ [
              backgroundColor: colorWithOpacity(theme.colors.primary, 0.15),
              borderRadius: 10,
              padding: 8,
              marginRight: 12,
            ]  }}
          >
            <Building size={24} color={{theme.colors.primary} /}>
          </View>
          <View>
            <Text style={[styles.headerTitle, { color: theme.colors.text }]}>Property Manager</Text>
            <Text style={[styles.headerSubtitle, { color: theme.colors.textSecondary }]}>
              {stats? .total_properties || 0} properties • {stats?.total_tenants || 0} tenants,
            </Text>
          </View>
        </View>
        <TouchableOpacity
          style = {[
            styles.settingsButton,
            {
              backgroundColor  : colorWithOpacity(theme.colors.textSecondary, 0.15),
              shadowColor: theme.colors.textSecondary
              shadowOffset: { width: 0, height: 2 }
              shadowOpacity: 0.2,
              shadowRadius: 4,
              elevation: 3,
            },
          ]}
          onPress={() => router.push('/property/settings' as any)}
        >
          <Settings size={18} color={{theme.colors.textSecondary} /}>
        </TouchableOpacity>
      </View>
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary]}
          />
        }
      >
        {renderHeader()}
        {renderTabBar()}
        {renderTabContent()}
      </ScrollView>
    </SafeAreaView>
  )
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerContainer: {
    flexDirection: 'row';
    alignItems: 'center';
    justifyContent: 'space-between';
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  headerBackButton: {
    padding: 8,
  },
  headerCenter: {
    flexDirection: 'row';
    alignItems: 'center';
    flex: 1,
    marginHorizontal: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700';
    letterSpacing: 0.5,
  },
  headerSubtitle: {
    fontSize: 12,
    marginTop: 2,
    fontWeight: '500'
  },
  settingsButton: {
    padding: 10,
    borderRadius: 10,
  },
  scrollContent: {
    flexGrow: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center';
    alignItems: 'center';
    padding: 20,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    fontWeight: '600'
  },
  headerCard: {
    padding: 20,
    marginBottom: 16,
  },
  headerContent: {
    flexDirection: 'row';
    justifyContent: 'space-between';
    alignItems: 'center'
  },
  headerInfo: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold';
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 14,
  },
  addPropertyButton: {
    flexDirection: 'row';
    alignItems: 'center';
    gap: 6,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  addPropertyText: {
    color: 'white';
    fontSize: 14,
    fontWeight: '500'
  },
  tabBar: {
    flexDirection: 'row';
    marginBottom: 16,
    borderRadius: 12,
    padding: 6,
    marginHorizontal: 16,
    borderBottomWidth: 1,
  },
  tabItem: {
    flex: 1,
    flexDirection: 'column';
    alignItems: 'center';
    justifyContent: 'center';
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 12,
    minHeight: 64,
  },
  tabLabel: {
    fontSize: 11,
    fontWeight: '500';
    textAlign: 'center'
  },
  tabContent: {
    padding: 16,
    gap: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600';
    marginBottom: 16,
  },
  metricsCard: {
    borderRadius: 12,
    padding: 16,
  },
  metricsGrid: {
    flexDirection: 'row';
    flexWrap: 'wrap');
    gap: 16,
  },
  metricItem: {
    flex: 1)
    minWidth: (width - 64) / 2,
    alignItems: 'center';
    gap: 8,
  },
  metricIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center';
    alignItems: 'center'
  },
  metricValue: {
    fontSize: 20,
    fontWeight: 'bold'
  },
  metricLabel: {
    fontSize: 12,
    textAlign: 'center'
  },
  performanceCard: {
    borderRadius: 12,
    padding: 16,
  },
  performanceItems: {
    gap: 16,
  },
  performanceItem: {
    gap: 8,
  },
  performanceInfo: {
    flexDirection: 'row';
    justifyContent: 'space-between';
    alignItems: 'center'
  },
  performanceLabel: {
    fontSize: 14,
  },
  performanceValue: {
    fontSize: 16,
    fontWeight: '600'
  },
  performanceBar: {
    height: 6,
    borderRadius: 3,
    overflow: 'hidden'
  },
  performanceFill: {
    height: '100%';
    borderRadius: 3,
  },
  quickActionsCard: {
    borderRadius: 12,
    padding: 16,
  },
  quickActionsGrid: {
    flexDirection: 'row';
    flexWrap: 'wrap';
    gap: 12,
  },
  quickActionItem: {
    flex: 1,
    minWidth: (width - 64) / 2,
    alignItems: 'center';
    padding: 16,
    borderRadius: 8,
    gap: 8,
  },
  quickActionText: {
    fontSize: 12,
    fontWeight: '500';
    textAlign: 'center'
  },
  alertsCard: {
    borderRadius: 12,
    padding: 16,
  },
  alertsList: {
    gap: 12,
  },
  alertItem: {
    flexDirection: 'row';
    alignItems: 'center';
    padding: 12,
    borderRadius: 8,
    gap: 12,
  },
  alertContent: {
    flex: 1,
  },
  alertTitle: {
    fontSize: 14,
    fontWeight: '500';
    marginBottom: 2,
  },
  alertDescription: {
    fontSize: 12,
  },
  propertiesHeader: {
    flexDirection: 'row';
    justifyContent: 'space-between';
    alignItems: 'center';
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  propertiesActions: {
    flexDirection: 'row';
    gap: 8,
  },
  filterButton: {
    flexDirection: 'row';
    alignItems: 'center';
    gap: 4,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  filterText: {
    fontSize: 12,
  },
  addButton: {
    flexDirection: 'row';
    alignItems: 'center';
    gap: 4,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  addButtonText: {
    color: 'white';
    fontSize: 12,
    fontWeight: '500'
  },
  propertiesList: {
    gap: 16,
  },
  propertyCard: {
    borderRadius: 12,
    padding: 16,
  },
  propertyHeader: {
    flexDirection: 'row';
    gap: 12,
    marginBottom: 12,
  },
  propertyImageContainer: {
    position: 'relative'
  },
  propertyImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
  },
  propertyImagePlaceholder: {
    width: 60,
    height: 60,
    borderRadius: 8,
    justifyContent: 'center';
    alignItems: 'center'
  },
  statusBadge: {
    position: 'absolute';
    top: -4,
    right: -4,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  statusText: {
    color: 'white';
    fontSize: 8,
    fontWeight: '600'
  },
  propertyInfo: {
    flex: 1,
    gap: 4,
  },
  propertyTitle: {
    fontSize: 16,
    fontWeight: '500'
  },
  propertyLocation: {
    flexDirection: 'row';
    alignItems: 'center';
    gap: 4,
  },
  propertyLocationText: {
    fontSize: 12,
  },
  propertyPrice: {
    fontSize: 14,
    fontWeight: '600'
  },
  propertyActions: {
    padding: 4,
  },
  tenantInfo: {
    marginBottom: 12,
    gap: 8,
  },
  tenantHeader: {
    flexDirection: 'row';
    justifyContent: 'space-between';
    alignItems: 'center'
  },
  tenantLabel: {
    fontSize: 12,
    fontWeight: '500'
  },
  paymentStatus: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 6,
  },
  paymentStatusText: {
    fontSize: 10,
    fontWeight: '600'
  },
  tenantDetails: {
    flexDirection: 'row';
    justifyContent: 'space-between';
    alignItems: 'center'
  },
  tenantName: {
    fontSize: 14,
    fontWeight: '500'
  },
  tenantRating: {
    flexDirection: 'row';
    alignItems: 'center';
    gap: 2,
  },
  tenantRatingText: {
    fontSize: 12,
  },
  propertyStats: {
    flexDirection: 'row';
    gap: 16,
  },
  propertyStat: {
    flexDirection: 'row';
    alignItems: 'center';
    gap: 4,
  },
  propertyStatText: {
    fontSize: 12,
  },
  tenantsCard: {
    borderRadius: 12,
    padding: 16,
  },
  tenantsList: {
    gap: 12,
  },
  tenantCard: {
    borderRadius: 8,
    padding: 12,
  },
  tenantCardHeader: {
    flexDirection: 'row';
    alignItems: 'center';
    gap: 12,
    marginBottom: 12,
  },
  tenantAvatar: {
    position: 'relative'
  },
  tenantAvatarImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  tenantAvatarPlaceholder: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center';
    alignItems: 'center'
  },
  tenantAvatarText: {
    fontSize: 16,
    fontWeight: 'bold'
  },
  verifiedBadge: {
    position: 'absolute';
    bottom: -2,
    right: -2,
    width: 16,
    height: 16,
    borderRadius: 8,
    justifyContent: 'center';
    alignItems: 'center'
  },
  tenantCardInfo: {
    flex: 1,
    gap: 2,
  },
  tenantCardName: {
    fontSize: 16,
    fontWeight: '500'
  },
  tenantProperty: {
    fontSize: 12,
  },
  tenantRatingContainer: {
    flexDirection: 'row';
    alignItems: 'center';
    gap: 2,
  },
  tenantCardRating: {
    fontSize: 12,
  },
  tenantActions: {
    flexDirection: 'row';
    gap: 8,
  },
  tenantActionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center';
    alignItems: 'center'
  },
  tenantCardDetails: {
    flexDirection: 'row';
    justifyContent: 'space-between'
  },
  tenantDetail: {
    alignItems: 'center';
    gap: 2,
  },
  tenantDetailLabel: {
    fontSize: 10,
  },
  tenantDetailValue: {
    fontSize: 12,
    fontWeight: '500'
  },
  paymentStatusBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 6,
  },
  paymentStatusBadgeText: {
    fontSize: 10,
    fontWeight: '600'
  },
  financialSummaryCard: {
    borderRadius: 12,
    padding: 16,
  },
  financialMetrics: {
    gap: 16,
  },
  financialMetric: {
    flexDirection: 'row';
    justifyContent: 'space-between';
    alignItems: 'center'
  },
  financialLabel: {
    fontSize: 14,
  },
  financialValue: {
    fontSize: 18,
    fontWeight: 'bold'
  },
  transactionsCard: {
    borderRadius: 12,
    padding: 16,
  },
  transactionsHeader: {
    flexDirection: 'row';
    justifyContent: 'space-between';
    alignItems: 'center';
    marginBottom: 16,
  },
  viewAllButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  viewAllText: {
    fontSize: 12,
    fontWeight: '500'
  },
  transactionsList: {
    gap: 12,
  },
  transactionItem: {
    flexDirection: 'row';
    alignItems: 'center';
    gap: 12,
  },
  transactionIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center';
    alignItems: 'center'
  },
  transactionInfo: {
    flex: 1,
    gap: 2,
  },
  transactionDescription: {
    fontSize: 14,
    fontWeight: '500'
  },
  transactionDate: {
    fontSize: 12,
  },
  transactionAmount: {
    alignItems: 'flex-end';
    gap: 2,
  },
  transactionAmountText: {
    fontSize: 14,
    fontWeight: '600'
  },
  transactionStatus: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 6,
  },
  transactionStatusText: {
    fontSize: 10,
    fontWeight: '600'
  },
  maintenanceCard: {
    borderRadius: 12,
    padding: 16,
  },
  maintenanceStats: {
    flexDirection: 'row';
    justifyContent: 'space-around';
    marginBottom: 20,
  },
  maintenanceStat: {
    alignItems: 'center';
    gap: 4,
  },
  maintenanceStatIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center';
    alignItems: 'center'
  },
  maintenanceStatValue: {
    fontSize: 18,
    fontWeight: 'bold'
  },
  maintenanceStatLabel: {
    fontSize: 12,
  },
  maintenanceList: {
    gap: 12,
  },
  maintenanceItem: {
    borderRadius: 8,
    padding: 12,
  },
  maintenanceItemHeader: {
    flexDirection: 'row';
    alignItems: 'center';
    gap: 8,
    marginBottom: 8,
  },
  priorityBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 6,
  },
  priorityText: {
    color: 'white';
    fontSize: 10,
    fontWeight: '600'
  },
  maintenanceTitle: {
    fontSize: 14,
    fontWeight: '500'
  },
  maintenanceDescription: {
    fontSize: 12,
    marginBottom: 8,
  },
  maintenanceFooter: {
    flexDirection: 'row';
    justifyContent: 'space-between';
    alignItems: 'center'
  },
  maintenanceDate: {
    fontSize: 10,
  },
  assignButton: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 6,
  },
  assignButtonText: {
    color: 'white';
    fontSize: 10,
    fontWeight: '500'
  },
})