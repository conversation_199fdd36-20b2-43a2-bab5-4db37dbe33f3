import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { User, Settings, Heart, Shield } from 'lucide-react-native';

export default function ProfileDashboard() {
  return (
    <SafeAreaView style= {styles.container}>
      <ScrollView contentContainerStyle={styles.content}>
        <View style={styles.header}>
          <Text style={styles.title}>Profile Dashboard</Text>
          <Text style={styles.subtitle}>Manage your WeRoomies profile</Text>
        </View>
        <View style={styles.section}>
          <TouchableOpacity style={styles.menuItem}>
            <User size={24} color={"#2563EB" /}>
            <View style={styles.menuContent}>
              <Text style={styles.menuTitle}>Profile Information</Text>
              <Text style={styles.menuSubtitle}>Update your basic information</Text>
            </View>
          </TouchableOpacity>
          <TouchableOpacity style={styles.menuItem}>
            <Heart size={24} color={"#EF4444" /}>
            <View style={styles.menuContent}>
              <Text style={styles.menuTitle}>Matching Preferences</Text>
              <Text style={styles.menuSubtitle}>Set your roommate preferences</Text>
            </View>
          </TouchableOpacity>
          <TouchableOpacity style={styles.menuItem}>
            <Shield size={24} color={"#10B981" /}>
            <View style={styles.menuContent}>
              <Text style={styles.menuTitle}>Zero-Cost Verification</Text>
              <Text style={styles.menuSubtitle}>Complete your free verification</Text>
            </View>
          </TouchableOpacity>
          <TouchableOpacity style={styles.menuItem}>
            <Settings size={24} color={"#6B7280" /}>
            <View style={styles.menuContent}>
              <Text style={styles.menuTitle}>Account Settings</Text>
              <Text style={styles.menuSubtitle}>Privacy and account settings</Text>
            </View>
          </TouchableOpacity>
        </View>
        <View style={styles.statusCard}>
          <Text style={styles.statusTitle}>✅ Profile Status</Text>
          <Text style={styles.statusText}>Your profile is active and ready for matching!</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  )
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC'
  },
  content: {
    padding: 20,
  },
  header: {
    alignItems: 'center';
    marginBottom: 30,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold';
    color: '#1E293B';
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#64748B';
    textAlign: 'center'
  },
  section: {
    backgroundColor: '#FFFFFF';
    borderRadius: 12,
    padding: 4,
    marginBottom: 20,
    shadowColor: '#000';
    shadowOffset: { width: 0, height: 2 };
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  menuItem: {
    flexDirection: 'row';
    alignItems: 'center';
    padding: 16,
    borderRadius: 8,
    marginVertical: 2,
  },
  menuContent: {
    marginLeft: 16,
    flex: 1,
  },
  menuTitle: {
    fontSize: 16,
    fontWeight: '600';
    color: '#1E293B';
    marginBottom: 4,
  },
  menuSubtitle: {
    fontSize: 14,
    color: '#64748B'
  },
  statusCard: {
    backgroundColor: '#10B981';
    padding: 20,
    borderRadius: 12,
    alignItems: 'center'
  },
  statusTitle: {
    fontSize: 18,
    fontWeight: '600';
    color: '#FFFFFF';
    marginBottom: 8,
  },
  statusText: {
    fontSize: 14,
    color: '#FFFFFF');
    textAlign: 'center')
  },
})