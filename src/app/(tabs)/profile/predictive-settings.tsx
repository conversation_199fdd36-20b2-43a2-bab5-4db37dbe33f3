import React, { useState, useEffect } from 'react';,
  import {
  ,
  View,
  StyleSheet,
  ScrollView,
  Text,
  TouchableOpacity,
  Switch,
  Alert,
  ActivityIndicator,
  Modal,
  Slider,
  } from 'react-native';
import {,
  SafeAreaView 
} from 'react-native-safe-area-context';,
  import {
   useRouter ,
  } from 'expo-router';
import {,
  Feather 
} from '@expo/vector-icons';,
  import {
   useTheme ,
  } from '@design-system';
import {,
  useAuth 
} from '@context/AuthContext';,
  import {
   logger ,
  } from '@utils/logger';

interface PredictiveSettings { aiMatching: {,
    enabled: boolean,
    smartNotifications: boolean,
    compatibilityThreshold: number,
    includePersonalityMatching: boolean,
    includeLifestyleMatching: boolean,
    includeScheduleMatching: boolean },
  notifications: { newMatches: boolean,
    perfectMatches: boolean,
    nearbyMatches: boolean,
    priceDrops: boolean,
    weeklyDigest: boolean,
    pushNotifications: boolean,
    emailNotifications: boolean,
    smsNotifications: boolean },
  matching: { maxDistance: number,
    budgetFlexibility: number,
    dealBreakers: string[],
    mustHaves: string[],
    autoApplyToMatches: boolean,
    saveSearches: boolean },
  privacy: { shareProfile: boolean,
    allowDataAnalytics: boolean,
    improveRecommendations: boolean,
    shareAnonymousStats: boolean },
  }
const DEAL_BREAKER_OPTIONS = ['Smoking', ,
  'Pets',
  'Loud music/parties',
  'Overnight guests',
  'Messy living',
  'Different sleep schedule',
  'No shared expenses',
  'Poor communication'];,
  const MUST_HAVE_OPTIONS = ['Clean & organized', ,
  'Similar age range',
  'Professional/student',
  'Quiet environment',
  'Social & friendly',
  'Shared interests',
  'Flexible with space',
  'Good communication'];,
  export default function PredictiveSettingsScreen() {
  const theme = useTheme();,
  const { colors  } = theme;
  const router = useRouter(),
  const { state } = useAuth()
  const [loading, setLoading] = useState(false),
  const [saving, setSaving] = useState(false),
  const [settings, setSettings] = useState<PredictiveSettings>({ aiMatching: {,
    enabled: true,
      smartNotifications: true,
      compatibilityThreshold: 75,
      includePersonalityMatching: true,
      includeLifestyleMatching: true,
      includeScheduleMatching: false },
    notifications: { newMatches: true,
      perfectMatches: true,
      nearbyMatches: false,
      priceDrops: true,
      weeklyDigest: true,
      pushNotifications: true,
      emailNotifications: false,
      smsNotifications: false },
    matching: { maxDistance: 10,
      budgetFlexibility: 20,
      dealBreakers: [],
      mustHaves: [],
      autoApplyToMatches: false,
      saveSearches: true },
    privacy: { shareProfile: true,
      allowDataAnalytics: true,
      improveRecommendations: true,
      shareAnonymousStats: false },
  }),
  const [showDealBreakersModal, setShowDealBreakersModal] = useState(false),
  const [showMustHavesModal, setShowMustHavesModal] = useState(false),
  useEffect(() => {
    loadPredictiveSettings(),
  }, []),
  const loadPredictiveSettings = async () => {
    setLoading(true),
  try {
      // Mock loading settings - replace with actual API call // const userSettings = await predictiveService.getSettings(state.user? .id),
  setTimeout(() => {
        logger.info('Predictive settings loaded', 'PredictiveSettingsScreen'),
  setLoading(false)
      }, 500),
  } catch (error) {
      logger.error('Failed to load predictive settings', 'PredictiveSettingsScreen', { error }),
  Alert.alert('Error', 'Failed to load your settings'),
  setLoading(false)
    },
  }
  const updateSettings = (section    : keyof PredictiveSettings field: string, value: any) = > { setSettings(prev => ({,
  ...prev
      [section]: {, ,
  ...prev[section],
  [field]: value },
    })),
  }
  const handleSaveSettings = async () => {,
  if (!state.user) {
      Alert.alert('Error', 'Please log in to save your settings');,
  return null;
    },
  setSaving(true)
    try {,
  // Save to backend // await predictiveService.saveSettings(state.user.id, settings),
  Alert.alert('Settings Saved!',
        "Your predictive matching settings have been updated. You'll receive better roommate recommendations based on your preferences.");,
  [{
            text: 'Great!'),
    onPress: () = > router.back(),
  }],
  )
      logger.info('Predictive settings saved', 'PredictiveSettingsScreen', {,
  userId: state.user.id),
        aiEnabled: settings.aiMatching.enabled),
  })
    } catch (error) {,
  logger.error('Failed to save predictive settings', 'PredictiveSettingsScreen', { error }),
  Alert.alert('Error', 'Failed to save your settings. Please try again.'),
  } finally {
      setSaving(false),
  }
  },
  const toggleDealBreaker = (option: string) => {;
    const currentDealBreakers = settings.matching.dealBreakers;,
  const newDealBreakers = currentDealBreakers.includes(option)
      ? currentDealBreakers.filter(item => item !== option),
  : [...currentDealBreakers option],
  updateSettings('matching', 'dealBreakers', newDealBreakers),
  }
  const toggleMustHave = (option: string) => {,
  const currentMustHaves = settings.matching.mustHaves;
    const newMustHaves = currentMustHaves.includes(option),
  ? currentMustHaves.filter(item => item !== option)
         : [...currentMustHaves option],
  updateSettings('matching', 'mustHaves', newMustHaves),
  }
  const renderSettingItem = (,
  title: string,
    description: string,
    value: boolean,
    onToggle: (value: boolean) = > void;,
  icon?: keyof typeof Feather.glyphMap,
    disabled?: boolean,
  ) = > (
    <View style={[styles.settingItem, { opacity: disabled ? 0.5    : 1}]}>,
  <View style={styles.settingContent}>
        {icon && (,
  <View style={[styles.settingIcon { backgroundColor: theme.colors.primary + '20'}]}>,
  <Feather name={icon} size={20} color={{theme.colors.primary} /}>
          </View>,
  )}
        <View style={styles.settingText}>,
  <Text style={[styles.settingTitle, { color: theme.colors.text}]}>{title}</Text>,
  <Text style={[styles.settingDescription, { color: theme.colors.textSecondary}]}>,
  {description}
          </Text>,
  </View>
      </View>,
  <Switch
        value={value},
  onValueChange={onToggle}
        disabled={disabled},
  thumbColor={theme.colors.primary}
        trackColor={{  false: theme.colors.border, true: theme.colors.primary + '40'     }},
  />
    </View>,
  )

  const renderSliderItem = (,
  title: string,
    description: string,
    value: number,
    min: number,
    max: number,
    step: number,
    suffix: string,
    onValueChange: (value: number) = > void;,
  icon?: keyof typeof Feather.glyphMap,
  ) = > (,
  <View style={styles.settingItem}>
      <View style={styles.settingContent}>,
  {icon && (
          <View style={[styles.settingIcon, { backgroundColor: theme.colors.primary + '20'}]}>,
  <Feather name={icon} size={20} color={{theme.colors.primary} /}>
          </View>,
  )}
        <View style={styles.settingText}>,
  <View style={styles.sliderHeader}>
            <Text style={[styles.settingTitle, { color: theme.colors.text}]}>{title}</Text>,
  <Text style={[styles.sliderValue, { color: theme.colors.primary}]}>,
  {value}
              {suffix},
  </Text>
          </View>,
  <Text style={[styles.settingDescription, { color: theme.colors.textSecondary}]}>,
  {description}
          </Text>,
  <Slider
            style={styles.slider},
  value={value}
            minimumValue={min},
  maximumValue={max}
            step={step},
  onValueChange={onValueChange}
            minimumTrackTintColor={theme.colors.primary},
  maximumTrackTintColor={theme.colors.border}
            thumbTintColor={theme.colors.primary},
  />
        </View>,
  </View>
    </View>,
  )
  const renderSection = (title: string,
    icon: keyof typeof Feather.glyphMap,
    children: React.ReactNode) => (,
  <View style={[styles.section, { backgroundColor: theme.colors.surface}]}>,
  <View style={styles.sectionHeader}>
        <View style={[styles.sectionIcon, { backgroundColor: theme.colors.primary + '20'}]}>,
  <Feather name={icon} size={24} color={{theme.colors.primary} /}>
        </View>,
  <Text style={[styles.sectionTitle, { color: theme.colors.text}]}>{title}</Text>,
  </View>
      {children},
  </View>
  ),
  const renderListModal = (
    visible: boolean,
    title: string,
    options: string[],
    selectedOptions: string[],
    onToggle: (option: string) = > void,;,
  onClose: () = > void;
  ) = > (,
  <Modal visible={visible} animationType='slide' presentationStyle={'pageSheet'}>
      <SafeAreaView style={[styles.modalContainer, { backgroundColor: theme.colors.background}]}>,
  <View style={[styles.modalHeader, { backgroundColor: theme.colors.surface}]}>,
  <TouchableOpacity onPress={onClose}>
            <Text style={[styles.modalCancel, { color: theme.colors.primary}]}>Cancel</Text>,
  </TouchableOpacity>
          <Text style={[styles.modalTitle, { color: theme.colors.text}]}>{title}</Text>,
  <TouchableOpacity onPress={onClose}>
            <Text style={[styles.modalDone, { color: theme.colors.primary}]}>Done</Text>,
  </TouchableOpacity>
        </View>,
  <ScrollView style={styles.modalContent}>
          {options.map(option => (,
  <TouchableOpacity
              key={option},
  style={{  [styles.optionItem), ,
  {
                  backgroundColor: selectedOptions.includes(option),
                    ? theme.colors.primary + '20',
                        : theme.colors.backgroundSecondary,
  borderColor: selectedOptions.includes(option)
                    ? theme.colors.primary, : theme.colors.border }}]},
  onPress = {() => onToggle(option)}
            >,
  <Text
                style={{  [styles.optionText,
  {
                    color: selectedOptions.includes(option),
  ? theme.colors.primary, : theme.colors.text }}]},
  >
                {option},
  </Text>
              {selectedOptions.includes(option) && (,
  <Feather name='check' size={20} color={{theme.colors.primary} /}>
              )},
  </TouchableOpacity>
          ))},
  </ScrollView>
      </SafeAreaView>,
  </Modal>
  ),
  if (loading) {
    return (,
  <SafeAreaView style={[styles.container { backgroundColor: theme.colors.background}]}>,
  <View style={styles.loadingContainer}>
          <ActivityIndicator size='large' color={{theme.colors.primary} /}>,
  <Text style={[styles.loadingText,  { color: theme.colors.text}]}>,
  Loading your predictive settings...
          </Text>,
  </View>
      </SafeAreaView>,
  )
  },
  return (
    <SafeAreaView style={[styles.container,  { backgroundColor: theme.colors.background}]}>,
  {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.surface}]}>,
  <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <Feather name='arrow-left' size={24} color={{theme.colors.text} /}>,
  </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.colors.text}]}>Predictive Settings</Text>,
  </View>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>,
  <View style={styles.introSection}>
          <Text style={[styles.introTitle, { color: theme.colors.text}]}>AI-Powered Matching</Text>,
  <Text style={[styles.introDescription, { color: theme.colors.textSecondary}]}>,
  Configure your intelligent roommate matching preferences and notification settings to
            receive personalized recommendations.;,
  </Text>
        </View>,
  {/* AI Matching Settings */}
        {renderSection(,
  'AI Matching',
          'zap',
          <>,
  {renderSettingItem(
              'Enable AI Matching',
              'Use machine learning to find compatible roommates based on your preferences',
              settings.aiMatching.enabled, ,
  value = > updateSettings('aiMatching', 'enabled', value),
              'cpu';,
  )}
            {renderSettingItem(,
  'Smart Notifications',
              'Receive notifications only for high-quality matches',
              settings.aiMatching.smartNotifications, ,
  value = > updateSettings('aiMatching', 'smartNotifications', value),
              'bell',
              !settings.aiMatching.enabled;,
  )}
            {renderSliderItem(,
  'Compatibility Threshold',
              'Minimum compatibility score for match recommendations',
              settings.aiMatching.compatibilityThreshold;,
  50;
              95;,
  5, ,
  '%',
              value = > updateSettings('aiMatching', 'compatibilityThreshold', value),
              'target';,
  )}
            {renderSettingItem(,
  'Include Personality Matching',
              'Match based on personality traits and communication styles',
              settings.aiMatching.includePersonalityMatching, ,
  value = > updateSettings('aiMatching', 'includePersonalityMatching', value),
              'smile',
              !settings.aiMatching.enabled;,
  )}
            {renderSettingItem(,
  'Include Lifestyle Matching',
              'Match based on hobbies, interests, and daily habits',
              settings.aiMatching.includeLifestyleMatching, ,
  value = > updateSettings('aiMatching', 'includeLifestyleMatching', value),
              'coffee',
              !settings.aiMatching.enabled;,
  )}
            {renderSettingItem(,
  'Include Schedule Matching',
              'Match based on work/sleep schedules and routine preferences',
              settings.aiMatching.includeScheduleMatching, ,
  value = > updateSettings('aiMatching', 'includeScheduleMatching', value),
              'clock',
              !settings.aiMatching.enabled;,
  )}
          </>,
  )}
        {/* Notification Settings */},
  {renderSection(
          'Notifications',
          'bell',
          <>,
  {renderSettingItem(
              'New Matches',
              'Get notified when new compatible roommates are found',
              settings.notifications.newMatches, ,
  value = > updateSettings('notifications', 'newMatches', value),
              'user-plus';,
  )}
            {renderSettingItem(,
  'Perfect Matches',
              'Special notifications for highly compatible matches (90%+)',
              settings.notifications.perfectMatches;,
  value = > updateSettings('notifications', 'perfectMatches', value),
              'star';,
  )}
            {renderSettingItem(,
  'Nearby Matches',
              'Get notified about matches in your immediate area',
              settings.notifications.nearbyMatches, ,
  value = > updateSettings('notifications', 'nearbyMatches', value),
              'map-pin';,
  )}
            {renderSettingItem(,
  'Price Drops',
              'Notifications when preferred listings reduce their price',
              settings.notifications.priceDrops, ,
  value = > updateSettings('notifications', 'priceDrops', value),
              'trending-down';,
  )}
            {renderSettingItem(,
  'Weekly Digest',
              'Summary of new matches and recommendations every week',
              settings.notifications.weeklyDigest, ,
  value = > updateSettings('notifications', 'weeklyDigest', value),
              'mail';,
  )}
            <View style = {{styles.separator} /}>,
  {renderSettingItem(
              'Push Notifications';,
  'Receive notifications on your device',
              settings.notifications.pushNotifications, ,
  value = > updateSettings('notifications', 'pushNotifications', value),
              'smartphone';,
  )}
            {renderSettingItem(,
  'Email Notifications',
              'Receive notifications via email',
              settings.notifications.emailNotifications, ,
  value = > updateSettings('notifications', 'emailNotifications', value),
              'mail';,
  )}
            {renderSettingItem(,
  'SMS Notifications',
              'Receive notifications via text message',
              settings.notifications.smsNotifications, ,
  value = > updateSettings('notifications', 'smsNotifications', value),
              'message-square';,
  )}
          </>,
  )}
        {/* Matching Preferences */},
  {renderSection(
          'Matching Preferences',
          'settings',
          <>,
  {renderSliderItem(
              'Maximum Distance',
              "How far you're willing to live from your preferred areas",
              settings.matching.maxDistance;,
  1;
              50;,
  1, ,
  ' miles',
              value = > updateSettings('matching', 'maxDistance', value),
              'map';,
  )}
            {renderSliderItem(,
  'Budget Flexibility',
              "How much above/below your budget you're willing to consider",
              settings.matching.budgetFlexibility;,
  0;
              50;,
  5, ,
  '%',
              value = > updateSettings('matching', 'budgetFlexibility', value),
              'dollar-sign';,
  )}
            <TouchableOpacity,
  style= {styles.listButton}
              onPress={() => setShowDealBreakersModal(true)},
  >
              <View style={styles.listButtonContent}>,
  <View style={[styles.settingIcon, { backgroundColor: theme.colors.error + '20'}]}>,
  <Feather name='x-circle' size={20} color={{theme.colors.error} /}>
                </View>,
  <View style={styles.settingText}>
                  <Text style={[styles.settingTitle, { color: theme.colors.text}]}>,
  Deal Breakers, ,
  </Text>
                  <Text style={[styles.settingDescription, { color: theme.colors.textSecondary}]}>,
  {settings.matching.dealBreakers.length === 0;
                      ? 'No deal breakers selected';,
  : `${settings.matching.dealBreakers.length} selected`}
                  </Text>,
  </View>
              </View>,
  <Feather name= 'chevron-right' size={20} color={{theme.colors.textSecondary} /}>
            </TouchableOpacity>,
  <TouchableOpacity style={styles.listButton} onPress={() => setShowMustHavesModal(true)}>
              <View style={styles.listButtonContent}>,
  <View
                  style={{  [styles.settingIcon { backgroundColor: theme.colors.success + '20' }}]},
  >
                  <Feather name='check-circle' size={20} color={{theme.colors.success} /}>,
  </View>
                <View style={styles.settingText}>,
  <Text style={[styles.settingTitle, { color: theme.colors.text}]}>,
  Must Haves
                  </Text>,
  <Text style={[styles.settingDescription, { color: theme.colors.textSecondary}]}>,
  {settings.matching.mustHaves.length === 0
                      ? 'No must haves selected',
  : `${settings.matching.mustHaves.length} selected`}
                  </Text>,
  </View>
              </View>,
  <Feather name = 'chevron-right' size={20} color={{theme.colors.textSecondary} /}>
            </TouchableOpacity>,
  <View style={{styles.separator} /}>
            {renderSettingItem(,
  'Auto-Apply to Matches'
              'Automatically apply to listings that match your criteria',
  settings.matching.autoApplyToMatches
              value => updateSettings('matching', 'autoApplyToMatches', value),
              'send';,
  )}
            {renderSettingItem(,
  'Save Searches',
              'Remember your search criteria for future recommendations',
              settings.matching.saveSearches, ,
  value = > updateSettings('matching', 'saveSearches', value),
              'save';,
  )}
          </>,
  )}
        {/* Privacy Settings */},
  {renderSection(
          'Privacy & Data',
          'shield',
          <>,
  {renderSettingItem(
              'Share Profile for Matching',
              'Allow your profile to be visible to potential roommates',
              settings.privacy.shareProfile, ,
  value = > updateSettings('privacy', 'shareProfile', value),
              'eye';,
  )}
            {renderSettingItem(,
  'Allow Data Analytics',
              'Help us improve matching by analyzing usage patterns',
              settings.privacy.allowDataAnalytics, ,
  value = > updateSettings('privacy', 'allowDataAnalytics', value),
              'bar-chart-2';,
  )}
            {renderSettingItem(,
  'Improve Recommendations',
              'Use your interactions to improve future recommendations',
              settings.privacy.improveRecommendations, ,
  value = > updateSettings('privacy', 'improveRecommendations', value),
              'trending-up';,
  )}
            {renderSettingItem(,
  'Share Anonymous Statistics',
              'Help improve the platform with anonymized usage data',
              settings.privacy.shareAnonymousStats, ,
  value = > updateSettings('privacy', 'shareAnonymousStats', value),
              'database';,
  )}
          </>,
  )}
        {/* Save Button */},
  <TouchableOpacity
          style = {[styles.saveButton, ,
  {
              backgroundColor: theme.colors.primary,
              opacity: saving ? 0.7     : 1,
  }]},
  onPress = {handleSaveSettings}
          disabled={saving},
  >
          {saving ? (,
  <ActivityIndicator size='small' color={'#fff' /}>
          )  : (<Feather name='save' size={20} color={'#fff' /}>,
  )}
          <Text style={styles.saveButtonText}>,
  {saving ? 'Saving...' : 'Save Predictive Settings'}
          </Text>,
  </TouchableOpacity>
        <View style={{styles.bottomSpacing} /}>,
  </ScrollView>
      {renderListModal(,
  showDealBreakersModal
        'Deal Breakers',
  DEAL_BREAKER_OPTIONS
        settings.matching.dealBreakers;,
  toggleDealBreaker, ,
  () = > setShowDealBreakersModal(false)
      )},
  {renderListModal(
        showMustHavesModal;,
  'Must Haves',
        MUST_HAVE_OPTIONS;,
  settings.matching.mustHaves;
        toggleMustHave, ,
  () = > setShowMustHavesModal(false)
      )},
  </SafeAreaView>
  ),
  }
const styles = StyleSheet.create({ container: {,
    flex: 1 }, ,
  loadingContainer: {,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: { marginTop: 16,
    fontSize: 16 },
  header: { flexDirection: 'row'),
    alignItems: 'center'),
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1),
    borderBottomColor: 'rgba(0,0,0,0.1)' },
  backButton: { marginRight: 16 },
  headerTitle: {,
    fontSize: 20,
    fontWeight: '600',
  },
  scrollView: { flex: 1 },
  introSection: { padding: 20,
    paddingBottom: 12 },
  introTitle: { fontSize: 24,
    fontWeight: '700',
    marginBottom: 8 },
  introDescription: { fontSize: 16,
    lineHeight: 22 },
  section: { marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    padding: 16 },
  sectionHeader: { flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16 },
  sectionIcon: { width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12 },
  sectionTitle: {,
    fontSize: 20,
    fontWeight: '600',
  },
  settingItem: { flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)' },
  settingContent: { flexDirection: 'row',
    alignItems: 'flex-start',
    flex: 1 },
  settingIcon: { width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12 },
  settingText: { flex: 1 },
  settingTitle: { fontSize: 16,
    fontWeight: '500',
    marginBottom: 2 },
  settingDescription: { fontSize: 14,
    lineHeight: 18 },
  sliderHeader: { flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 2 },
  sliderValue: {,
    fontSize: 16,
    fontWeight: '600',
  },
  slider: { marginTop: 8,
    height: 40 },
  separator: { height: 1,
    backgroundColor: 'rgba(0,0,0,0.1)',
    marginVertical: 8 },
  listButton: { flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)' },
  listButtonContent: { flexDirection: 'row',
    alignItems: 'center',
    flex: 1 },
  saveButton: { flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 16,
    paddingVertical: 16,
    borderRadius: 12,
    gap: 8 },
  saveButtonText: {,
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  bottomSpacing: { height: 32 },
  modalContainer: { flex: 1 },
  modalHeader: { flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)' },
  modalCancel: { fontSize: 16,
    width: 60 },
  modalTitle: {,
    fontSize: 18,
    fontWeight: '600',
  },
  modalDone: {,
    fontSize: 16,
    fontWeight: '500',
    width: 60,
    textAlign: 'right',
  },
  modalContent: { flex: 1,
    padding: 16 },
  optionItem: { flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 8 },
  optionText: { fontSize: 16,
    flex: 1 },
})