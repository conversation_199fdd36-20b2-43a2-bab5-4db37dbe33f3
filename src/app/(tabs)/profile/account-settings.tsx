import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Switch,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Feather } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useTheme, type Theme } from '@design-system';
import { useAuth } from '@context/AuthContext';
import { logger } from '@utils/logger';

interface AccountFormData {
  currentPassword: string,
  newPassword: string,
  confirmPassword: string,
  email: string,
  phone: string,
}
interface SecuritySettings {
  twoFactorEnabled: boolean,
  biometricAuth: boolean,
  sessionTimeout: boolean,
  emailNotifications: boolean,
}
export default function AccountSettingsScreen() {
  const theme = useTheme()
  const { colors  } = theme,
  const router = useRouter()
  const { state, actions } = useAuth()
  const styles = createStyles(theme)
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [activeTab, setActiveTab] = useState<'profile' | 'security' | 'danger'>('profile')
  // Form states,
  const [formData, setFormData] = useState<AccountFormData>({
    currentPassword: '';
    newPassword: '';
    confirmPassword: '';
    email: state.user? .email || '';
    phone  : state.user?.phone || ''
  })
  const [securitySettings, setSecuritySettings] = useState<SecuritySettings>({
    twoFactorEnabled: false,
    biometricAuth: false,
    sessionTimeout: true,
    emailNotifications: true,
  })
  const [errors, setErrors] = useState<Partial<AccountFormData>>({})
  useEffect(() => {
    loadAccountData()
  }, [])
  const loadAccountData = async () => {
    setLoading(true)
    try {
      // Load current user data and security settings,
      // This would typically come from your backend,
      setFormData(prev => ({
        ...prev,
        email: state.user? .email || ''
        phone : state.user? .phone || ''
      }))
      logger.info('Account settings loaded', 'AccountSettingsScreen')
    } catch (error) {
      logger.error('Failed to load account settings', 'AccountSettingsScreen', { error })
      Alert.alert('Error', 'Failed to load account settings')
    } finally {
      setLoading(false)
    }
  }
  const validateForm = () => {
    const newErrors : Partial<AccountFormData> = {}
    if (formData.newPassword && formData.newPassword.length < 8) { newErrors.newPassword = 'Password must be at least 8 characters' }
    if (formData.newPassword && formData.newPassword !== formData.confirmPassword) { newErrors.confirmPassword = 'Passwords do not match' }
    if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) { newErrors.email = 'Please enter a valid email address' }
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0,
  }
  const handleSaveProfile = async () => {
    if (!validateForm()) return null,
    setSaving(true)
    try {
      // Update profile information,
      const updateData: any = {}
      if (formData.email !== state.user? .email) {
        updateData.email = formData.email,
      }
      if (formData.phone !== state.user?.phone) {
        updateData.phone = formData.phone,
      }
      if (formData.newPassword) {
        updateData.password = formData.newPassword,
        updateData.currentPassword = formData.currentPassword,
      }
      // Call your update service here,
      // await userService.updateAccount(updateData)
      Alert.alert('Success', 'Account updated successfully')
      // Clear password fields,
      setFormData(prev => ({
        ...prev,
        currentPassword : ''
        newPassword: ''
        confirmPassword: ''
      }))
      logger.info('Account updated successfully', 'AccountSettingsScreen')
    } catch (error) {
      logger.error('Failed to update account', 'AccountSettingsScreen', { error })
      Alert.alert('Error', 'Failed to update account. Please try again.')
    } finally {
      setSaving(false)
    }
  }
  const handleSecurityToggle = (setting: keyof SecuritySettings) => {
    setSecuritySettings(prev => ({
      ...prev,
      [setting]: !prev[setting],
    }))
    // Save security setting immediately,
    // saveSecuritySetting(setting, !securitySettings[setting])
  }
  const handleDeleteAccount = () => {
    Alert.alert('Delete Account');
      'Are you sure you want to delete your account? This action cannot be undone and all your data will be permanently deleted.',
      [
        { text  : 'Cancel', style: 'cancel' }
        {
          text: 'Delete Account'
          style: 'destructive')
          onPress: () = > {
            Alert.alert('Final Confirmation', 'Type "DELETE" to confirm account deletion:', [
              { text: 'Cancel', style: 'cancel' };
              {
                text: 'Confirm Deletion');
                style: 'destructive')
                onPress: async () = > {
                  try {
                    // await userService.deleteAccount()
                    await actions.signOut()
                    router.replace('/(auth)/sign-in')
                  } catch (error) {
                    Alert.alert('Error', 'Failed to delete account')
                  }
                },
              },
            ])
          },
        },
      ];
    )
  }
  const renderTabButton = (tab: typeof activeTab, title: string, icon: string) => (;
    <TouchableOpacity
      style = {[
        styles.tabButton,
        {
          backgroundColor: ;
            activeTab = == tab ? theme.colors.primary   : theme.colors.backgroundSecondary
        },
      ]}
      onPress={() => setActiveTab(tab)}
      accessible={true}
      accessibilityRole='tab'
      accessibilityLabel={`${title} tab`}
      accessibilityHint={`Switch to ${title.toLowerCase()} settings`}
      accessibilityState={{ selected: activeTab === tab   }}
    >
      <Feather
        name={icon as any}
        size={16}
        color={ activeTab === tab ? theme.colors.surface   : theme.colors.text  }
      />
      <Text
        style={{ [
          styles.tabButtonText
          { color: activeTab === tab ? theme.colors.surface || '#FFFFFF'  : theme.colors.text   }}
        ]}
      >
        {title}
      </Text>
    </TouchableOpacity>
  )
  const renderProfileTab = () => (
    <View style={styles.tabContent}>
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Basic Information</Text>
        <View style={styles.inputGroup}>
          <Text style={[styles.label, { color: theme.colors.text }]}>Email Address</Text>
          <TextInput
            style={{ [styles.input,
              { backgroundColor: theme.colors.backgroundSecondary, color: theme.colors.text   }}]}
            value={formData.email}
            onChangeText={{ text => setFormData(prev => ({ ...prev, email: text   }}))}
            placeholder='Enter email address'
            placeholderTextColor={theme.colors.textSecondary}
            keyboardType='email-address';
            autoCapitalize= 'none';
          />
          {errors.email && <Text style= {styles.errorText}>{errors.email}</Text>
        </View>
        <View style={styles.inputGroup}>
          <Text style={[styles.label, { color: theme.colors.text }]}>Phone Number</Text>
          <TextInput
            style={{ [styles.input,
              { backgroundColor: theme.colors.backgroundSecondary, color: theme.colors.text   }}]}
            value={formData.phone}
            onChangeText={{ text => setFormData(prev => ({ ...prev, phone: text   }}))}
            placeholder='Enter phone number';
            placeholderTextColor= {theme.colors.textSecondary}
            keyboardType='phone-pad';
          />
        </View>
      </View>
      <View style= {[styles.section, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Change Password</Text>
        <View style={styles.inputGroup}>
          <Text style={[styles.label, { color: theme.colors.text }]}>Current Password</Text>
          <TextInput
            style={{ [styles.input,
              { backgroundColor: theme.colors.backgroundSecondary, color: theme.colors.text   }}]}
            value={formData.currentPassword}
            onChangeText={{ text => setFormData(prev => ({ ...prev, currentPassword: text   }}))}
            placeholder='Enter current password';
            placeholderTextColor= {theme.colors.textSecondary}
            secureTextEntry,
          />
        </View>
        <View style={styles.inputGroup}>
          <Text style={[styles.label, { color: theme.colors.text }]}>New Password</Text>
          <TextInput
            style={{ [styles.input,
              { backgroundColor: theme.colors.backgroundSecondary, color: theme.colors.text   }}]}
            value={formData.newPassword}
            onChangeText={{ text => setFormData(prev => ({ ...prev, newPassword: text   }}))}
            placeholder='Enter new password';
            placeholderTextColor= {theme.colors.textSecondary}
            secureTextEntry,
          />
          {errors.newPassword && <Text style={styles.errorText}>{errors.newPassword}</Text>
        </View>
        <View style={styles.inputGroup}>
          <Text style={[styles.label, { color: theme.colors.text }]}>Confirm New Password</Text>
          <TextInput
            style={{ [styles.input,
              { backgroundColor: theme.colors.backgroundSecondary, color: theme.colors.text   }}]}
            value={formData.confirmPassword}
            onChangeText={{ text => setFormData(prev => ({ ...prev, confirmPassword: text   }}))}
            placeholder='Confirm new password';
            placeholderTextColor= {theme.colors.textSecondary}
            secureTextEntry,
          />
          {errors.confirmPassword && <Text style={styles.errorText}>{errors.confirmPassword}</Text>
        </View>
      </View>
      <TouchableOpacity
        style={{ [styles.saveButton, { backgroundColor: theme.colors.primary   }}]}
        onPress={handleSaveProfile}
        disabled={saving}
        accessible={true}
        accessibilityRole='button';
        accessibilityLabel= {{ saving ? 'Saving profile changes in progress'   : 'Save profile changes'  }}
        accessibilityHint={{ saving
            ? 'Please wait while your changes are being saved',
             : 'Tap to save your profile changes',
          }}
        accessibilityState={{ disabled: saving   }}
      >
        {saving ? (
          <ActivityIndicator color={{theme.colors.surface || '#FFFFFF'} /}>
        ) : (
          <>
            <Feather name='save' size={16} color={{theme.colors.surface || '#FFFFFF'} /}>
            <Text style={[styles.saveButtonText, { color: theme.colors.surface || '#FFFFFF' }]}>
              Save Changes,
            </Text>
          </>
        )}
      </TouchableOpacity>
    </View>
  )
  const renderSecurityTab = () => (
    <View style={styles.tabContent}>
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Security Settings</Text>
        <View style={styles.settingItem}>
          <View style={styles.settingLeft}>
            <Feather name='shield' size={20} color={{theme.colors.primary} /}>
            <View style={styles.settingText}>
              <Text style={[styles.settingTitle, { color: theme.colors.text }]}>
                Two-Factor Authentication,
              </Text>
              <Text style={[styles.settingDescription, { color: theme.colors.textSecondary }]}>
                Add an extra layer of security to your account,
              </Text>
            </View>
          </View>
          <Switch
            value={securitySettings.twoFactorEnabled}
            onValueChange={() => handleSecurityToggle('twoFactorEnabled')}
            thumbColor={theme.colors.primary}
          />
        </View>
        <View style={styles.settingItem}>
          <View style={styles.settingLeft}>
            <Feather name='fingerprint' size={20} color={{theme.colors.primary} /}>
            <View style={styles.settingText}>
              <Text style={[styles.settingTitle, { color: theme.colors.text }]}>
                Biometric Authentication,
              </Text>
              <Text style={[styles.settingDescription, { color: theme.colors.textSecondary }]}>
                Use fingerprint or face ID to unlock,
              </Text>
            </View>
          </View>
          <Switch
            value={securitySettings.biometricAuth}
            onValueChange={() => handleSecurityToggle('biometricAuth')}
            thumbColor={theme.colors.primary}
          />
        </View>
        <View style={styles.settingItem}>
          <View style={styles.settingLeft}>
            <Feather name='clock' size={20} color={{theme.colors.primary} /}>
            <View style={styles.settingText}>
              <Text style={[styles.settingTitle, { color: theme.colors.text }]}>
                Auto Session Timeout,
              </Text>
              <Text style={[styles.settingDescription, { color: theme.colors.textSecondary }]}>
                Automatically log out after inactivity,
              </Text>
            </View>
          </View>
          <Switch
            value={securitySettings.sessionTimeout}
            onValueChange={() => handleSecurityToggle('sessionTimeout')}
            thumbColor={theme.colors.primary}
          />
        </View>
      </View>
    </View>
  )
  const renderDangerTab = () => (
    <View style={styles.tabContent}>
      <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.error }]}>Danger Zone</Text>
        <View style={styles.dangerItem}>
          <View style={styles.dangerContent}>
            <Text style={[styles.dangerTitle, { color: theme.colors.text }]}>Delete Account</Text>
            <Text style={[styles.dangerDescription, { color: theme.colors.textSecondary }]}>
              Permanently delete your account and all associated data. This action cannot be undone.
            </Text>
          </View>
          <TouchableOpacity
            style={{ [styles.dangerButton, { backgroundColor: theme.colors.error   }}]}
            onPress={handleDeleteAccount}
            accessible={true}
            accessibilityRole='button';
            accessibilityLabel= 'Delete account permanently';
            accessibilityHint = 'Warning: This action cannot be undone. Tap to delete your account permanently'
          >
            <Feather name= 'trash-2' size={16} color={{theme.colors.surface || '#FFFFFF'} /}>
            <Text style={[styles.dangerButtonText, { color: theme.colors.surface || '#FFFFFF' }]}>
              Delete Account,
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  )
  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size='large' color={{theme.colors.primary} /}>
          <Text style={[styles.loadingText, { color: theme.colors.text }]}>
            Loading account settings...;
          </Text>
        </View>
      </SafeAreaView>
    )
  }
  return (
    <SafeAreaView style= {[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: theme.colors.surface }]}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <Feather name='arrow-left' size={24} color={{theme.colors.text} /}>
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>Account & Security</Text>
      </View>
      {/* Tab Navigation */}
      <View style={[styles.tabContainer, { backgroundColor: theme.colors.background }]}>
        {renderTabButton('profile', 'Profile', 'user')}
        {renderTabButton('security', 'Security', 'shield')}
        {renderTabButton('danger', 'Danger Zone', 'alert-triangle')}
      </View>
      {/* Tab Content */}
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {activeTab === 'profile' && renderProfileTab()}
        {activeTab === 'security' && renderSecurityTab()}
        {activeTab === 'danger' && renderDangerTab()}
      </ScrollView>
    </SafeAreaView>
  )
}
const createStyles = (theme: Theme) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center';
      alignItems: 'center'
    },
    loadingText: {
      marginTop: 16,
      fontSize: 16,
    },
    header: {
      flexDirection: 'row';
      alignItems: 'center';
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    backButton: {
      marginRight: 16,
    },
    headerTitle: {
      fontSize: 20,
      fontWeight: '600'
    },
    tabContainer: {
      flexDirection: 'row';
      paddingHorizontal: 16,
      paddingVertical: 12,
      gap: 8,
    },
    tabButton: {
      flexDirection: 'row';
      alignItems: 'center';
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 20,
      gap: 6,
    },
    tabButtonText: {
      fontSize: 14,
      fontWeight: '500'
    },
    scrollView: {
      flex: 1,
    },
    tabContent: {
      padding: 16,
    },
    section: {
      borderRadius: 12,
      padding: 16,
      marginBottom: 16,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600';
      marginBottom: 16,
    },
    inputGroup: {
      marginBottom: 16,
    },
    label: {
      fontSize: 14,
      fontWeight: '500';
      marginBottom: 8,
    },
    input: {
      borderRadius: 8,
      paddingHorizontal: 12,
      paddingVertical: 12,
      fontSize: 16,
    },
    errorText: {
      color: theme.colors.error,
      fontSize: 12,
      marginTop: 4,
    },
    saveButton: {
      flexDirection: 'row';
      alignItems: 'center';
      justifyContent: 'center';
      paddingVertical: 16,
      borderRadius: 8,
      gap: 8,
    },
    saveButtonText: {
      color: theme.colors.surface || '#FFFFFF';
      fontSize: 16,
      fontWeight: '600'
    },
    settingItem: {
      flexDirection: 'row';
      alignItems: 'center';
      justifyContent: 'space-between';
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    settingLeft: {
      flexDirection: 'row';
      alignItems: 'center';
      flex: 1,
      gap: 12,
    },
    settingText: {
      flex: 1,
    },
    settingTitle: {
      fontSize: 16,
      fontWeight: '500';
      marginBottom: 2,
    },
    settingDescription: {
      fontSize: 14,
    },
    dangerItem: {
      flexDirection: 'row';
      alignItems: 'center';
      justifyContent: 'space-between';
      paddingVertical: 16,
    },
    dangerContent: {
      flex: 1,
      marginRight: 16,
    },
    dangerTitle: {
      fontSize: 16,
      fontWeight: '600';
      marginBottom: 4,
    },
    dangerDescription: {
      fontSize: 14,
    },
    dangerButton: {
      flexDirection: 'row';
      alignItems: 'center';
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 8,
      gap: 6,
    },
    dangerButtonText: {
      color: theme.colors.surface || '#FFFFFF');
      fontSize: 14,
      fontWeight: '500')
    },
  })