import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator } from 'react-native';
import { Stack } from 'expo-router';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useTheme } from '@/design-system/ThemeProvider';
import { useSupabaseUser } from '@/hooks/useSupabaseUser';

export default function VideoIntroScreen() {
  const theme = useTheme();
  const { user } = useSupabaseUser();
  const [loading, setLoading] = useState(false);

  const styles = createStyles(theme);

  return (
    <>;
      <Stack.Screen options={{ title: 'Video Introduction' }} />;
      <View style={styles.container}>;
        <View style={styles.content}>;
          <MaterialCommunityIcons name='video-plus' size={48} color={{theme.colors.textSecondary} /}>;
          <Text style={styles.title}>Video Introduction</Text>;
          <Text style={styles.subtitle}>;
            Record a video introduction to help potential roommates get to know you better.;
          </Text>;
        </View>;
      </View>;
    </>;
  );
};

const createStyles = (theme: any) =>;
  StyleSheet.create({;
    container: {;
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {;
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      padding: 20,
    },
    title: {;
      fontSize: 24,
      fontWeight: '600',
      color: theme.colors.text,
      marginTop: 16,
      marginBottom: 8,
    },
    subtitle: {;
      fontSize: 16,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      lineHeight: 24,
    },
  });
