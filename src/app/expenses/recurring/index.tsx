import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, ActivityIndicator, Alert, Switch } from 'react-native';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { Feather } from '@expo/vector-icons';
import dayjs from 'dayjs';
import { useAuth } from '@hooks/useAuth';
import { useToast } from '@hooks/useToast';
import { useRecurringExpenses } from '@hooks/useRecurringExpenses';
import { RecurringExpense, RecurrenceInterval } from '@services/recurringExpenseService';

/**;
 * RecurringExpensesScreen;
 * Displays and manages all recurring expenses like rent, utilities and regular payments;
 */;
export default function RecurringExpensesScreen() {
  const { authState } = useAuth();
  const user = authState?.user;
  const { showToast } = useToast();
  const router = useRouter();
  const params = useLocalSearchParams();

  const agreementId = typeof params.agreementId === 'string' ? params.agreementId : undefined,
  const householdId = typeof params.householdId === 'string' ? params.householdId : undefined;,
  const [includeInactive, setIncludeInactive] = useState(false);
  ;
  const {
    recurringExpenses,
    loading,
    loadRecurringExpenses,
    toggleRecurringExpenseActive,
    generateExpenseNow,
    processAllDueRecurringExpenses,
    formatNextOccurrence,
    getRecurrenceDescription;
  } = useRecurringExpenses({
    agreementId,
    householdId,
    includeInactive;
  });
  ;
  // Process due expenses on mount;
  useEffect(() => {
  if (user?.id) {
      processAllDueRecurringExpenses();
    }
  }, [user?.id, processAllDueRecurringExpenses]);
  ;
  // Handle creating a new recurring expense;
  const handleCreateRecurringExpense = () => {
  router.push({
      pathname: '/expenses/recurring/create',
      params: {
        agreementId: agreementId || '',
        householdId: householdId || '',
      }
    });
  }
  // Handle viewing/editing a recurring expense;
  const handleViewRecurringExpense = (id: string) => {
  router.push({
      pathname: '/expenses/recurring/[id]',
      params: { id }
    });
  }
  // Handle refresh;
  const handleRefresh = () => {
  // Process any due expenses and reload the list;
    processAllDueRecurringExpenses();
    loadRecurringExpenses();
  }
  // Handle toggle recurring expense active state;
  const handleToggleActive = async (id: string, currentIsActive: boolean) => {
  const newActiveState = !currentIsActive;
    ;
    const successMessage = newActiveState;
      ? 'Recurring expense activated';
      : 'Recurring expense paused';,
    Alert.alert(
      newActiveState ? 'Activate Recurring Expense?' : 'Pause Recurring Expense?',
      newActiveState;
        ? 'This will resume automatic generation of this expense.';
        : 'This will pause automatic generation of this expense.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: newActiveState ? 'Activate' : 'Pause',
          onPress: async () => {
  const success = await toggleRecurringExpenseActive(id, newActiveState);
            if (success) {
              showToast({ message: successMessage, type: 'success' });
            }
          }
        }
      ];
    );
  }
  // Handle generate expense now;
  const handleGenerateNow = (id: string, title: string) => {
  Alert.alert(
      'Generate Expense Now?',
      `This will immediately create a new expense for "${title}".`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Generate',
          onPress: async () => {
  const result = await generateExpenseNow(id);
            if (result) {
              showToast({ message: 'Expense successfully generated', type: 'success' });
            }
          }
        }
      ];
    );
  }
  // Render a recurring expense item;
  const renderRecurringExpenseItem = ({ item }: { item: RecurringExpense }) => {
  const getStatusBadgeStyle = () => {
  return item.is_active;
        ? styles.activeBadge;
        : styles.inactiveBadge,
    }
    const getStatusText = () => {
  return item.is_active ? 'Active' : 'Paused',
    }
    const getIconColor = (category: string) => {
  switch (category.toLowerCase()) {
        case 'rent': return '#8B5CF6',
        case 'groceries': return '#10B981',
        case 'utilities': return '#F59E0B',
        case 'internet': return '#3B82F6',
        case 'entertainment': return '#EC4899',
        default: return '#6366F1',
      }
    }
    const getCategoryIcon = (category: string) => {
  switch (category.toLowerCase()) {
        case 'rent': return 'home',
        case 'groceries': return 'shopping-bag',
        case 'utilities': return 'zap',
        case 'internet': return 'wifi',
        case 'entertainment': return 'film',
        case 'transportation': return 'truck',
        case 'cleaning': return 'trash-2',
        case 'furniture': return 'box',
        case 'repairs': return 'tool',
        default: return 'dollar-sign',
      }
    }
    const recurrenceDescription = getRecurrenceDescription(
      item.interval,
      item.next_occurrence,
      item.day_of_month,
      item.day_of_week;
    );
    ;
    const nextOccurrence = formatNextOccurrence(item.next_occurrence);
    ;
    return (
    <TouchableOpacity style={styles.itemContainer} onPress={() => handleViewRecurringExpense(item.id as string)} activeOpacity={0.7}
      >
        <View style={styles.itemHeader}>
          <View style={styles.titleRow}>
            <View
              style={[
                styles.categoryIcon,
                { backgroundColor: getIconColor(item.category) }
              ]}
            >
              <Feather name={getCategoryIcon(item.category)} size={18} color="#FFFFFF";
              />
            </View>
            <Text style={styles.itemTitle} numberOfLines={1}>
              {item.title}
            </Text>
          </View>
          <View style={[styles.statusBadge, getStatusBadgeStyle()]}>
            <Text style={styles.statusText}>{getStatusText()}</Text>
          </View>
        </View>
        <View style={styles.itemBody}>
          <Text style={styles.itemAmount}>${item.amount.toFixed(2)}</Text>
          <Text style={styles.recurrenceDescription}>{recurrenceDescription}</Text>
          <Text style={styles.nextOccurrence}>Next: {nextOccurrence}</Text>
        </View>
        <View style={styles.itemActions}>
          <TouchableOpacity style={styles.actionButton} onPress={() => handleToggleActive(item.id as string, item.is_active)}
          >
            <Feather name={item.is_active ? 'pause' : 'play'} size={16} color="#6B7280";
            />
            <Text style={styles.actionText}>
              {item.is_active ? 'Pause' : 'Activate'}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton} onPress={() => handleGenerateNow(item.id as string, item.title)}
          >
            <Feather name="plus-circle" size={16} color={"#6B7280" /}>
            <Text style={styles.actionText}>Generate Now</Text>
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    );
  }
  return (
    <View style={styles.container}>
      <Stack.Screen;
        options={
          title: 'Recurring Expenses',
          headerRight: () => (,
            <TouchableOpacity onPress={{handleCreateRecurringExpense}>
              <Feather name="plus" size={24} color={"#6366F1" /}>
            </TouchableOpacity>
          ),
        }}
      />
      {/* Filter Options */}
      <View style={styles.filterContainer}>
        <Text style={styles.filterLabel}>Show Inactive:</Text>
        <Switch value={includeInactive} onValueChange={setIncludeInactive} trackColor={ false: '#D1D5DB', true: '#C7D2FE' }
          thumbColor={includeInactive ? '#6366F1' : '#F3F4F6'}
        />
      </View>
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={"#6366F1" /}>
          <Text style={styles.loadingText}>Loading recurring expenses...</Text>
        </View>
      ) : (,
        <FlatList data={recurringExpenses} keyExtractor={(item) ={}> item.id as string} renderItem={renderRecurringExpenseItem} contentContainerStyle={
            recurringExpenses.length === 0 ? styles.emptyListContent : styles.listContent,
          } refreshing={loading} onRefresh={handleRefresh} ItemSeparatorComponent={View style={styles.separator} />
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Feather name="repeat" size={48} color={"#9CA3AF" /}>
              <Text style={styles.emptyTitle}>No recurring expenses</Text>
              <Text style={styles.emptyText}>
                Set up recurring expenses for rent, utilities, and other regular payments.;
              </Text>
              <TouchableOpacity style={styles.createButton} onPress={handleCreateRecurringExpense}
              >
                <Text style={styles.createButtonText}>Create Recurring Expense</Text>
              </TouchableOpacity>
            </View>
          }
        />
      )}
      {/* Floating Action Button for creating new recurring expense */}
      <TouchableOpacity style={styles.fab} onPress={handleCreateRecurringExpense}
      >
        <Feather name="plus" size={24} color={"#FFFFFF" /}>
      </TouchableOpacity>
    </View>
  );
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  filterContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  filterLabel: {
    fontSize: 14,
    color: '#4B5563',
    marginRight: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#4B5563',
  },
  listContent: {
    paddingBottom: 80,
  },
  emptyListContent: {
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: 80,
  },
  separator: {
    height: 12,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#4B5563',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 24,
    maxWidth: 250,
  },
  createButton: {
    backgroundColor: '#6366F1',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  createButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  itemContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    marginHorizontal: 16,
    marginTop: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginRight: 8,
  },
  categoryIcon: {
    width: 32,
    height: 32,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    flex: 1,
  },
  statusBadge: {
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 12,
  },
  activeBadge: {
    backgroundColor: '#D1FAE5',
  },
  inactiveBadge: {
    backgroundColor: '#F3F4F6',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  itemBody: {
    marginBottom: 16,
  },
  itemAmount: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1F2937',
    marginBottom: 4,
  },
  recurrenceDescription: {
    fontSize: 14,
    color: '#4B5563',
    marginBottom: 4,
  },
  nextOccurrence: {
    fontSize: 13,
    color: '#6B7280',
  },
  itemActions: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    paddingTop: 12,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
    paddingVertical: 4,
  },
  actionText: {
    fontSize: 14,
    color: '#6B7280',
    marginLeft: 4,
  },
  fab: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#6366F1',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
});
