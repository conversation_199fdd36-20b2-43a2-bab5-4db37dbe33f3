import React, { useEffect } from 'react';
import { Text, View, StyleSheet, ActivityIndicator } from 'react-native';
import { Link, Stack, useRouter } from 'expo-router';
import { useAuthAdapter } from '@context/AuthContextAdapter';
import { useTheme } from '@design-system';

export default function NotFoundScreen() {
  const theme = useTheme();
  const styles = createStyles(theme);
  const router = useRouter(); // Safe auth adapter usage with error handling let authState, authLoaded; let authError = false; try { const auth = useAuthAdapter(); authState = auth.authState; authLoaded = auth.authLoaded; } catch (error) { console.warn('🔴 [NotFound] Auth adapter not available:', error); authError = true; authState = { isAuthenticated: false, authStatus: 'unauthenticated' }; authLoaded = true; // Assume loaded to prevent infinite loading } useEffect(() => { // Auto-redirect after 3 seconds if auth is loaded if (authLoaded && !authError) { const timer = setTimeout(() => { if (authState.isAuthenticated && authState.authStatus === 'authenticated') { console.log('🔄 [NotFound] Redirecting authenticated user to main app'); router.replace('/(tabs)'); } else { console.log('🔄 [NotFound] Redirecting unauthenticated user to login'); router.replace('/(auth)/login'); } }, 3000); return () => clearTimeout(timer); } else if (authError) { // If auth is not available, redirect to login after a short delay const timer = setTimeout(() => { console.log('🔄 [NotFound] Auth unavailable, redirecting to login'); router.replace('/(auth)/login'); }, 2000); return () => clearTimeout(timer); } }, [authLoaded, authState.isAuthenticated, authState.authStatus, router, authError]); return(<> <Stack.Screen options={ title: 'Oops!' } /> <View style={styles.container}> <Text style={styles.title}>This screen doesn't exist.</Text> {!authLoaded ? ( <View style={styles.loadingContainer}> <ActivityIndicator size="small" color={{theme.colors.primary} /}> <Text style={styles.loadingText}>Loading...</Text> </View> ) : ( <> { <Text style={styles.subtitle}> {authError ? "We'll redirect you to login in a moment..." : "We'll redirect you to the right place in a moment..." } </Text> <View style={styles.linksContainer}> {authState.isAuthenticated && !authError ? ( <Link href="/(tabs)" style={styles.link}> <Text style={styles.linkText}>Go to Home</Text> </Link> ) : ( <Link href="/(auth)/login" style={styles.link}> <Text style={styles.linkText}>Go to Login</Text> </Link> )} </View> </> )} </View> </> );
}
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      padding: theme.spacing.lg,
      backgroundColor: theme.colors.background,
    },
    title: {
      fontSize: theme.typography.fontSize.xl,
      fontWeight: 'bold',
      marginBottom: theme.spacing.sm,
      color: theme.colors.text,
    },
    subtitle: {
      fontSize: theme.typography.fontSize.md,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      marginBottom: theme.spacing.lg,
    },
    loadingContainer: { alignItems: 'center', marginTop: theme.spacing.lg },
    loadingText: {
      marginTop: theme.spacing.sm,
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.textSecondary,
    },
    linksContainer: { marginTop: theme.spacing.lg },
    link: {
      marginTop: theme.spacing.md,
      paddingVertical: theme.spacing.md,
      paddingHorizontal: theme.spacing.lg,
      backgroundColor: theme.colors.primary,
      borderRadius: theme.borderRadius.md,
    },
    linkText: {
      fontSize: theme.typography.fontSize.md,
      color: theme.colors.background,
      fontWeight: '600',
      textAlign: 'center',
    },
  });
