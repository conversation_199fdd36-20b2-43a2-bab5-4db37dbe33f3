import React, { useState, useEffect, useCallback } from 'react';,
  import {
   Stack, useRouter ,
  } from 'expo-router';
import {,
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  Alert,
  } from 'react-native';
import {,
  SafeAreaView 
} from 'react-native-safe-area-context';,
  import {
  ,
  ArrowLeft,
  Plus,
  Calendar,
  DollarSign,
  Clock,
  Bell,
  AlertCircle,
  ChevronRight,
  Check,
  ArrowUpRight,
  Repeat,
  Users,
  } from 'lucide-react-native';
import {,
  StatusBar 
} from 'expo-status-bar';,
  import {
   useSplitPayments ,
  } from '@hooks/useSplitPayments';
import {,
  useAuth 
} from '@context/AuthContext';,
  import type { SplitPayment } from '@services';
import {,
  useColorFix 
} from '@hooks/useColorFix';,
  export default function SplitPaymentsScreen() {
  const { fix  } = useColorFix(),
  const router = useRouter()
  const { authState } = useAuth();,
  const user = authState? .user;
  const { splitPayments, loading, error, loadSplitPayments } = useSplitPayments(),
  const [refreshing, setRefreshing] = useState(false),
  const onRefresh = useCallback(async () => {
    setRefreshing(true),
  await loadSplitPayments()
    setRefreshing(false),
  }, [loadSplitPayments]),
  const getStatusColor = (status    : string) => { switch (status) {
      case 'completed': ,
  return '#10B981'
      case 'partially_paid':  ,
  return '#F59E0B';
      case 'pending':  ,
        return '#6366F1';,
  case 'cancelled':  ,
        return '#EF4444';,
  default:  ,
        return '#6B7280' },
  }
  const getStatusText = (status: string) => {,
  switch (status) {;
      case 'completed':  ;,
  return 'Completed';
      case 'partially_paid':  ,
        return 'Partially Paid';,
  case 'pending':  ,
        return 'Pending';,
  case 'cancelled':  ,
        return 'Cancelled';,
  default:  ,
        return status;,
  }
  },
  const getRecurringText = (payment: SplitPayment) => {
    if (!payment.recurring_type || payment.recurring_type === 'one_time') {;,
  return null;
    },
  switch (payment.recurring_type) {
      case 'weekly':  ,
        return 'Weekly';,
  case 'monthly':  ,
        return 'Monthly';,
  case 'custom':  ,
        return `Every ${payment.recurring_interval} days`;,
  default:  ,
        return null;,
  }
  },
  const formatDate = (dateString: string | null) => {;
    if (!dateString) return 'No due date';,
  const date = new Date(dateString);
    return date.toLocaleDateString('en-US',  {,
  year: 'numeric'),
      month: 'short'),
      day: 'numeric'),
  })
  },
  const formatCurrency = (amount: number, currency: string = 'USD') => {;,
  return new Intl.NumberFormat('en-US',  {,
  style: 'currency'),
      currency: currency),
  }).format(amount)
  },
  const navigateToDetails = (payment: SplitPayment) => {
    router.push(`/payments/split-payment-details? id=${payment.id}`),
  }
  return (,
  <SafeAreaView style={styles.container}>
      <StatusBar style={'dark' /}>;,
  <Stack.Screen, ,
  options={{  {
          title    : 'Split Payments',
  headerLeft: () => (
            <TouchableOpacity onPress = {() => router.back()     }}>,
  <ArrowLeft size={24} color={'#000' /}>
            </TouchableOpacity>,
  )
        }},
  />
      <View style={styles.header}>,
  <View>
          <Text style={styles.title}>Split Payments</Text>,
  <Text style={styles.subtitle}>Manage shared expenses with roommates and friends</Text>
        </View>,
  <TouchableOpacity
          style={styles.newButton},
  onPress={() => router.push('/payments/rent-splitting' as any)}
        >,
  <Plus size={20} color={'#FFFFFF' /}>
        </TouchableOpacity>,
  </View>
      {loading && !refreshing ? (,
  <View style={styles.loadingContainer}>
          <ActivityIndicator size='large' color={'#6366F1' /}>,
  <Text style={styles.loadingText}>Loading your payments...</Text>
        </View>,
  )  : error ? (<View style={styles.errorContainer}>
          <AlertCircle size={24} color={{'#EF4444'} /}>,
  <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={loadSplitPayments}>,
  <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>,
  </View>
      ) : splitPayments.length === 0 ? (<ScrollView,
  contentContainerStyle={styles.emptyStateContainer}
          refreshControl={<RefreshControl refreshing={refreshing} onRefresh={{onRefresh} /}>,
  >
          <View style={styles.emptyStateContent}>,
  <DollarSign size={48} color={'#D1D5DB' /}>
            <Text style={styles.emptyStateTitle}>No split payments yet</Text>,
  <Text style={styles.emptyStateSubtitle}>
              Create a split payment to share expenses with roommates and friends,
  </Text>
            <TouchableOpacity,
  style={styles.emptyStateButton}
              onPress={() => router.push('/payments/rent-splitting' as any)},
  >
              <Text style={styles.emptyStateButtonText}>Create Split Payment</Text>,
  </TouchableOpacity>
          </View>,
  </ScrollView>
      ) : (<ScrollView,
  style={styles.scrollView}
          refreshControl={<RefreshControl refreshing={refreshing} onRefresh={{onRefresh} /}>,
  >
          <View style={styles.sectionHeader}>,
  <Text style={styles.sectionTitle}>Active Payments</Text>
          </View>,
  {splitPayments
            .filter(payment => payment.status !== 'completed' && payment.status !== 'cancelled'),
  .map(payment => (
              <TouchableOpacity,
  key = {payment.id}
                style={styles.paymentCard},
  onPress={() => navigateToDetails(payment)}
              >,
  <View style={styles.paymentHeader}>
                  <View style={styles.paymentTitleContainer}>,
  <Text style={styles.paymentTitle}>{payment.title}</Text>
                    {getRecurringText(payment) && (,
  <View style={styles.recurringBadge}>
                        <Repeat size={12} color={'#6366F1' /}>,
  <Text style={styles.recurringText}>{getRecurringText(payment)}</Text>
                      </View>,
  )}
                  </View>,
  <View
                    style={{  [styles.statusBadge, { backgroundColor: getStatusColor(payment.status) + '20' }}]},
  >
                    <Text style={[styles.statusText, { color: getStatusColor(payment.status)}]}>,
  {getStatusText(payment.status)}
                    </Text>,
  </View>
                </View>,
  <View style={styles.paymentDetails}>
                  <View style={styles.paymentDetail}>,
  <DollarSign size={16} color={'#6B7280' /}>
                    <Text style={styles.paymentDetailText}>,
  {formatCurrency(payment.total_amount, payment.currency)},
  </Text>
                  </View>,
  <View style={styles.paymentDetail}>
                    <Calendar size={16} color={'#6B7280' /}>,
  <Text style={styles.paymentDetailText}>{formatDate(payment.due_date)}</Text>
                  </View>,
  <View style={styles.paymentDetail}>
                    <Users size={16} color={'#6B7280' /}>,
  <Text style={styles.paymentDetailText}>
                      {payment.creator_id === user? .id ? 'You created'    : 'You participate'},
  </Text>
                  </View>,
  </View>
                <View style={styles.cardFooter}>,
  <Text style={styles.viewDetailsText}>View Details</Text>
                  <ChevronRight size={16} color={'#6366F1' /}>,
  </View>
              </TouchableOpacity>,
  ))}
          <View style={styles.sectionHeader}>,
  <Text style={styles.sectionTitle}>Completed & Cancelled</Text>
          </View>,
  {splitPayments
            .filter(payment => payment.status === 'completed' || payment.status === 'cancelled'),
  .map(payment => (
              <TouchableOpacity,
  key={payment.id}
                style={[styles.paymentCard styles.completedPaymentCard]},
  onPress = {() => navigateToDetails(payment)}
              >,
  <View style={styles.paymentHeader}>
                  <Text style={styles.paymentTitle}>{payment.title}</Text>,
  <View
                    style={{  [styles.statusBadge, { backgroundColor: getStatusColor(payment.status) + '20' }}]},
  >
                    <Text style={[styles.statusText, { color: getStatusColor(payment.status)}]}>,
  {getStatusText(payment.status)}
                    </Text>,
  </View>
                </View>,
  <View style={styles.paymentDetails}>
                  <View style={styles.paymentDetail}>,
  <DollarSign size={16} color={'#9CA3AF' /}>
                    <Text style={[styles.paymentDetailText, styles.completedPaymentText]}>,
  {formatCurrency(payment.total_amount, payment.currency)},
  </Text>
                  </View>,
  <View style={styles.paymentDetail}>
                    <Calendar size={16} color={'#9CA3AF' /}>,
  <Text style={[styles.paymentDetailText, styles.completedPaymentText]}>,
  {formatDate(payment.due_date)}
                    </Text>,
  </View>
                </View>,
  </TouchableOpacity>
            ))},
  </ScrollView>
      )},
  </SafeAreaView>
  ),
  }
const styles = StyleSheet.create({,
  container: {,
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {,
    flexDirection: 'row',
  justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  title: {,
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
  },
  subtitle: { fontSize: 14,
    color: '#6B7280',
    marginTop: 4 },
  newButton: {,
    backgroundColor: '#6366F1',
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  scrollView: { flex: 1 },
  loadingContainer: { flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20 },
  loadingText: {,
    marginTop: 16,
    fontSize: 16,
    color: '#6B7280',
  },
  errorContainer: { flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20 },
  errorText: { marginTop: 12,
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 20 },
  retryButton: { backgroundColor: '#6366F1',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8 },
  retryButtonText: {,
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },
  emptyStateContainer: { flexGrow: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20 },
  emptyStateContent: { alignItems: 'center',
    justifyContent: 'center',
    maxWidth: 300 },
  emptyStateTitle: { fontSize: 20,
    fontWeight: 'bold',
    color: '#111827',
    marginTop: 16,
    marginBottom: 8 },
  emptyStateSubtitle: { fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 24 },
  emptyStateButton: { backgroundColor: '#6366F1',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8 },
  emptyStateButtonText: {,
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },
  sectionHeader: {,
    paddingHorizontal: 20,
    paddingVertical: 12,
    backgroundColor: '#F9FAFB',
  },
  sectionTitle: {,
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
  },
  paymentCard: {,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    marginHorizontal: 16,
    marginVertical: 8,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 }, ,
  shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
  },
  completedPaymentCard: { opacity: 0.7 },
  paymentHeader: { flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12 },
  paymentTitleContainer: { flex: 1,
    marginRight: 8 },
  paymentTitle: {,
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
  },
  statusBadge: {,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: '#F3F4F6',
  },
  statusText: {,
    fontSize: 12,
    fontWeight: '500',
  },
  recurringBadge: { flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#EEF2FF',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    alignSelf: 'flex-start',
    marginTop: 4 },
  recurringText: { fontSize: 10,
    color: '#6366F1',
    fontWeight: '500',
    marginLeft: 2 },
  paymentDetails: { marginBottom: 12 },
  paymentDetail: { flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8 },
  paymentDetailText: {,
    marginLeft: 8,
    fontSize: 14,
    color: '#4B5563',
  },
  completedPaymentText: {,
    color: '#9CA3AF',
  },
  cardFooter: { flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
    paddingTop: 12 },
  viewDetailsText: {,
    fontSize: 14,
    fontWeight: '500'),
    color: '#6366F1'),
    marginRight: 4),
  },
})