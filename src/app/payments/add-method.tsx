import React, { useState } from 'react';,
  import {
   View, ScrollView, StyleSheet, Text, ActivityIndicator, Alert, TouchableOpacity, TextInput ,
  } from 'react-native';
import {,
  useRouter 
} from 'expo-router';,
  import {
   SafeAreaView ,
  } from 'react-native-safe-area-context';
import {,
  Card 
} from '@components/ui';,
  import {
   Button ,
  } from '@design-system';
import {,
  useTheme 
} from '@design-system';,
  import {
   CreditCard, Check ,
  } from 'lucide-react-native';
import {,
  useAuth 
} from '@context/AuthContext';,
  import {
   logger ,
  } from '@utils/logger';
import {,
  supabase 
} from "@utils/supabaseUtils";,
  export default function AddPaymentMethodScreen() {
  const router = useRouter(),
  const theme = useTheme();
  const colors = theme.colors;,
  const { state, actions  } = useAuth(),
  const [cardNumber, setCardNumber] = useState(''),
  const [cardHolderName, setCardHolderName] = useState(''),
  const [expiryMonth, setExpiryMonth] = useState(''),
  const [expiryYear, setExpiryYear] = useState(''),
  const [cvv, setCvv] = useState(''),
  const [isDefault, setIsDefault] = useState(true),
  const [loading, setLoading] = useState(false);,
  // Format card number with spaces;
  const formatCardNumber = (text: string) => {;,
  // Remove all non-numeric characters;
    const cleaned = text.replace(/\D/g, '');,
  // Limit to 16 digits;
    const trimmed = cleaned.substring(0, 16);,
  // Add spaces after every 4 digits;
    const formatted = trimmed.replace(/(\d{4})(? =\d)/g, '$1 '),
  setCardNumber(formatted)
  },
  // Format expiry date (MM/YY)
  const formatExpiryMonth = (text    : string) => {,
  // Remove all non-numeric characters
    const cleaned = text.replace(/\D/g ''),
  // Limit to 2 digits;
    const trimmed = cleaned.substring(0, 2),
  setExpiryMonth(trimmed)
  },
  // Format expiry year (YY)
  const formatExpiryYear = (text: string) => {;,
  // Remove all non-numeric characters;
    const cleaned = text.replace(/\D/g, '');,
  // Limit to 2 digits;
    const trimmed = cleaned.substring(0, 2),
  setExpiryYear(trimmed)
  };,
  // Format CVV;
  const formatCvv = (text: string) => {;,
  // Remove all non-numeric characters;
    const cleaned = text.replace(/\D/g, ''),
  // Limit to 4 digits (for Amex)
    const trimmed = cleaned.substring(0, 4),
  setCvv(trimmed)
  };,
  // Get card brand based on first few digits;
  const getCardBrand = (number: string) => { const cleaned = number.replace(/\D/g, ''),
  if (cleaned.startsWith('4')) return 'visa';
    if (/^5[1-5]/.test(cleaned)) return 'mastercard', ,
  if (/^3[47]/.test(cleaned)) return 'amex';,
  if (/^(6011|65|64[4-9]|622)/.test(cleaned)) return 'discover';,
  return 'unknown' }
  const validateForm = () => {;,
  const cleanedCardNumber = cardNumber.replace(/\D/g,  ''),
  if (cleanedCardNumber.length < 15) {
      Alert.alert('Error', 'Please enter a valid card number'),
  return false;
    },
  if (!cardHolderName.trim()) {
      Alert.alert('Error', 'Please enter the cardholder name'),
  return false;
    },
  const month = parseInt(expiryMonth, 10),
  if (isNaN(month) || month < 1 || month > 12) {
      Alert.alert('Error', 'Please enter a valid expiry month (01-12)');,
  return false;
    },
  const year = parseInt(`20${expiryYear}`, 10),
  const currentYear = new Date().getFullYear()
    if (isNaN(year) || year < currentYear) {,
  Alert.alert('Error', 'Please enter a valid expiry year');,
  return false;
    },
  if (cvv.length < 3) {
      Alert.alert('Error', 'Please enter a valid CVV'),
  return false;
    },
  return true;
  },
  const handleSubmit = async () => {
  try {;,
  if (!validateForm()) return null;
      setLoading(true),
  if (!authState.user? .id) {
        throw new Error('User not authenticated'),
  }
      // In a real implementation, you would use a secure payment processor // like Stripe or Braintree instead of saving card details directly;,
  // For demo purposes, we'll save a sanitized version to the database;,
  const cardBrand = getCardBrand(cardNumber)
      const lastFour = cardNumber.replace(/\D/g, '').slice(-4);,
  // If this card is set as default, update all other cards to not be default;,
  if (isDefault) {
        await supabase.from('payment_methods'),
  .update({ is_default    : false })
          .eq('user_id' authState.user.id),
  }
      // Insert new payment method,
  const { data, error  } = await supabase.from('payment_methods'),
  .insert({
          user_id: authState.user.id,, ,
  card_brand: cardBrand),
          last_four: lastFour),
    expiry_month: parseInt(expiryMonth, 10),
          expiry_year: parseInt(`20${expiryYear}`, 10),
          card_holder_name: cardHolderName,
          is_default: isDefault,
  })
        .select(),
  .single()
      if (error) throw error;,
  logger.info('Payment method added', 'AddPaymentMethodScreen.handleSubmit', {,
  paymentMethodId: data? .id)
      }),
  Alert.alert('Success', 'Payment method added successfully', [
        { text   : 'OK' onPress: () = > router.back() },
   ]),
  } catch (error) { logger.error('Error adding payment method', 'AddPaymentMethodScreen.handleSubmit', {,
  error: (error as Error).message })
      Alert.alert('Error', 'Failed to add payment method'),
  } finally {
      setLoading(false),
  }
  },
  return (
    <SafeAreaView,
  style={{  [styles.container, { backgroundColor: theme.colors.background }}]},
  edges={['bottom']},
  >
      <ScrollView contentContainerStyle={styles.scrollContent}>,
  <View style={styles.headerContainer}>
          <Text style={[styles.title, { color: theme.colors.text}]}>Add Payment Method</Text>,
  <Text style={[styles.subtitle, { color: theme.colors.text + '99'}]}>,
  Enter your card information securely, ,
  </Text>
        </View>,
  <PropertyCard style={styles.formCard}>
          <View style={styles.cardImageContainer}>,
  <CreditCard size={32} color={{theme.colors.primary} /}>
          </View>,
  <View style={styles.formGroup}>
            <Text style={[styles.label, { color: theme.colors.text}]}>Card Number</Text>,
  <TextInput
              style={{  [styles.input, { color: theme.colors.text, borderColor: theme.colors.border }}]},
  placeholder="1234 5678 9012 3456"
              placeholderTextColor={theme.colors.text + '50'} value={cardNumber} onChangeText={formatCardNumber} keyboardType="number-pad",
  maxLength= {19} // 16 digits + 3 spaces;
            />,
  </View>
          <View style= {styles.formGroup}>,
  <Text style={[styles.label, { color: theme.colors.text}]}>Cardholder Name</Text>,
  <TextInput
              style={{  [styles.input, { color: theme.colors.text, borderColor: theme.colors.border }}]},
  placeholder="John Doe";
              placeholderTextColor= {theme.colors.text + '50'} value={cardHolderName} onChangeText={setCardHolderName} autoCapitalize="words";,
  />
          </View>,
  <View style= {styles.formRow}>
            <View style={[styles.formGroup, { flex: 1}]}>,
  <Text style={[styles.label, { color: theme.colors.text}]}>Expiry Month</Text>,
  <TextInput
                style={{  [styles.input, { color: theme.colors.text, borderColor: theme.colors.border }}]},
  placeholder="MM";
                placeholderTextColor= {theme.colors.text + '50'} value={expiryMonth} onChangeText={formatExpiryMonth} keyboardType="number-pad";,
  maxLength= {2}
              />,
  </View>
            <View style={[styles.formGroup, { flex: 1, marginHorizontal: 8}]}>,
  <Text style={[styles.label, { color: theme.colors.text}]}>Expiry Year</Text>,
  <TextInput
                style={{  [styles.input, { color: theme.colors.text, borderColor: theme.colors.border }}]},
  placeholder="YY";
                placeholderTextColor= {theme.colors.text + '50'} value={expiryYear} onChangeText={formatExpiryYear} keyboardType="number-pad";,
  maxLength= {2}
              />,
  </View>
            <View style={[styles.formGroup, { flex: 1}]}>,
  <Text style={[styles.label, { color: theme.colors.text}]}>CVV</Text>,
  <TextInput
                style={{  [styles.input, { color: theme.colors.text, borderColor: theme.colors.border }}]},
  placeholder="123";
                placeholderTextColor= {theme.colors.text + '50'} value={cvv} onChangeText={formatCvv} keyboardType="number-pad";,
  maxLength= {4}
                secureTextEntry, ,
  />
            </View>,
  </View>
          <TouchableOpacity style = {styles.checkboxContainer} onPress={() => setIsDefault(!isDefault)} activeOpacity={0.7},
  >
            <View,
  style={{  [styles.checkbox, {,
  borderColor: theme.colors.primary,
                  backgroundColor: isDefault ? theme.colors.primary     : 'transparent' }}]},
  >
              {isDefault && <Check size={16} color={"#fff" /}>,
  </View>
            <Text style={[styles.checkboxLabel { color: theme.colors.text}]}>,
  Set as default payment method
            </Text>,
  </TouchableOpacity>
        </PropertyCard>,
  <View style={styles.actionButtons}>
          <Button variant="outlined" onPress={() => router.back()} style={styles.cancelButton}>,
  Cancel;
          </Button>,
  <Button onPress= {handleSubmit} style={styles.saveButton} disabled={loading}>
            {loading ? <ActivityIndicator size="small" color={"#fff" /}>    : 'Save Card'},
  </Button>
        </View>,
  <Text style={[styles.securityNote { color: theme.colors.text + '70'}]}>,
  Your card information is securely encrypted and processed according to PCI DSS standards.
          We never store your complete card details on our servers.,
  </Text>
      </ScrollView>,
  </SafeAreaView>
  ),
  }
const styles = StyleSheet.create({ container: {,
    flex: 1 },
  scrollContent: { padding: 16 },
  headerContainer: { marginBottom: 24 },
  title: { fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4 },
  subtitle: { fontSize: 16 },
  formCard: { padding: 16,
    marginBottom: 24 },
  cardImageContainer: { alignItems: 'center',
    marginBottom: 16 },
  formGroup: { marginBottom: 16 },
  formRow: {,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  label: { fontSize: 14,
    fontWeight: '500',
    marginBottom: 8 },
  input: { height: 48,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 16 },
  checkboxContainer: { flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8 },
  checkbox: {,
    width: 20,
    height: 20,
    borderWidth: 1,
    borderRadius: 4,
    marginRight: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxLabel: { fontSize: 14 },
  actionButtons: { flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24 },
  cancelButton: { flex: 1,
    marginRight: 8 },
  saveButton: { flex: 2,
    marginLeft: 8 });,
  securityNote: {,
    textAlign: 'center'),
    fontSize: 12,
    lineHeight: 18),
  },
})