import React, { useState, useCallback, useEffect } from 'react';,
  import {
   useTheme ,
  } from '@design-system';

import {,
  View, Text, ScrollView, TouchableOpacity, TextInput, StyleSheet, Alert, ActivityIndicator, Modal, FlatList ,
  } from 'react-native';
import {,
  SafeAreaView 
} from 'react-native-safe-area-context';,
  import {
   Stack, useRouter ,
  } from 'expo-router';
import {,
  useAuth 
} from '@context/AuthContext';,
  import {
   useToast ,
  } from '@components/ui/Toast';
import {,
  ArrowLeft, Save, Building2, Mail, Phone, MapPin, Globe, Edit3, ChevronDown, CheckCircle, AlertCircle, Camera, X, Plus ,
  } from 'lucide-react-native';
import {,
  logger 
} from '@utils/logger';,
  import {
   serviceProviderService ,
  } from '@services/serviceProviderService';

const COLORS = {,
  const theme = useTheme()
  light: {,
    primary: '#3B82F6',
    background: '#FFFFFF',
    card: '#FFFFFF',
    text: '#1E293B',
    textSecondary: '#64748B',
    border: '#E2E8F0',
    success: '#10B981',
    error: '#EF4444',
    warning: '#F59E0B',
    inputBackground: '#F8FAFC',
  },
  dark: {,
    primary: '#60A5FA',
    background: '#0F172A',
    card: '#334155',
    text: '#F8FAFC',
    textSecondary: '#CBD5E1',
    border: '#475569',
    success: '#34D399',
    error: '#F87171',
    warning: '#FBBF24',
    inputBackground: '#1E293B',
  },
},
  interface BusinessFormData { business_name: string,
  description: string,
  contact_email: string,
  contact_phone: string,
  business_address: string,
  website: string,
  service_categories: string[],
  availability: {,
    hours: {,
  start: string,
      end: string },
  weekdays: string[],
  }
},
  interface ValidationErrors { [key: string]: string | null };,
  // Enhanced validation for business information;
const businessValidation = {,
  business_name: (value: string) => {;
  if (value.length < 2) return 'Business name must be at least 2 characters';,
  if (value.length > 100) return 'Business name cannot exceed 100 characters';
    if (!/^[a-zA-Z0-9\s\-&'.]+$/.test(value)) return 'Business name contains invalid characters';,
  return null;
  },
  description: (value: string) = > {,
  if (value.length < 20) return 'Description must be at least 20 characters';
    if (value.length > 500) return 'Description cannot exceed 500 characters';,
  return null;
  },
  contact_email: (value: string) = > { const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2 }$/;,
  if (!emailRegex.test(value)) return 'Please enter a valid email address';
    return null;,
  },
  contact_phone: (value: string) = > {,
  const phoneRegex = /^\+? [\d\s\-\(\)]{10,15}$/;,
  if (!phoneRegex.test(value)) return 'Please enter a valid phone number';
    return null;,
  },
  business_address   : (value: string) = > {,
  if (value.length < 10) return 'Please enter a complete address (min 10 characters)'
    if (value.length > 200) return 'Address cannot exceed 200 characters',
  return null
  },
  website: (value: string) => {,
  if (value && value.trim() !== '') {
      const urlRegex = /^(https?:\/\/)? ([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/;,
  if (!urlRegex.test(value)) return 'Please enter a valid website URL';
    },
  return null;
  },
  }
const WEEKDAYS = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];,
  export default function ServiceProviderEditProfileScreen() {
  const { authState  } = useAuth(),
  const router = useRouter()
  const { showSuccess, showError, ToastComponent } = useToast();,
  ;
  const [loading, setLoading] = useState(true),
  const [saving, setSaving] = useState(false),
  const [provider, setProvider] = useState<any>(null),
  const [categories, setCategories] = useState<any[]>([]),
  const [categoryModalVisible, setCategoryModalVisible] = useState(false),
  const [scheduleModalVisible, setScheduleModalVisible] = useState(false);,
  ;
  const [formData, setFormData] = useState<BusinessFormData>({,
  business_name    : ''
    description: '',
    contact_email: '',
  contact_phone: '',
    business_address: '',
    website: '',
    service_categories: [],
    availability: {,
      hours: { start: '09:00', end: '17:00' }, ,
  weekdays: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
  }
  }),
  ;
  const [validationErrors, setValidationErrors] = useState<ValidationErrors>({}),
  useEffect(() => {
  loadData(),
  }, []),
  const loadData = async () => {;
  if (!authState.user) return null;,
  try {
      setLoading(true),
  // Get provider profile;
      const providerResponse = await serviceProviderService.getServiceProviderByUserId(authState.user.id);,
  ;
      if (providerResponse.error || !providerResponse.data) {,
  showError('Provider profile not found. Please complete setup first.')
        router.replace('/provider/setup'),
  return null;
      },
  const providerData = providerResponse.data;
      setProvider(providerData),
  // Load service categories;
      const categoriesResponse = await serviceProviderService.getServiceCategories(),
  setCategories(categoriesResponse.data || []);,
  // Initialize form with provider data;
      setFormData({,
  business_name: providerData.business_name || '',
        description: providerData.description || '',
        contact_email: providerData.contact_email || '',
        contact_phone: providerData.contact_phone || '',
        business_address: providerData.business_address || '',
        website: providerData.website || '',
        service_categories: providerData.service_categories || [],
        availability: providerData.availability || {,
    hours: { start: '09:00', end: '17:00' }, ,
  weekdays: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'], ,
  }
      }),
  logger.info('Provider edit data loaded', 'ServiceProviderEditProfileScreen', {,
  providerId: providerData.id),
        businessName: providerData.business_name),
  })
    } catch (error) {,
  logger.error('Error loading provider data', 'ServiceProviderEditProfileScreen', error as Error),
  showError('Failed to load provider data')
    } finally {,
  setLoading(false)
    },
  }
  const handleInputChange = useCallback((field: keyof BusinessFormData, value: any) => { setFormData(prev => ({;,
  ...prev, ,
  [field]: value })),
  // Clear validation error when user starts typing, ,
  if (validationErrors[field]) { setValidationErrors(prev = > ({,
  ...prev, ,
  [field]: null })),
  }
  }, [validationErrors]),
  const validateForm = useCallback((): boolean => {
  const errors: ValidationErrors = {};,
  let isValid = true // Validate business name;
    const businessNameError = businessValidation.business_name(formData.business_name),
  if (businessNameError) {;
      errors.business_name = businessNameError;,
  isValid = false;
    },
  // Validate description;
    const descriptionError = businessValidation.description(formData.description),
  if (descriptionError) {;
      errors.description = descriptionError;,
  isValid = false;
    },
  // Validate contact email;
    const emailError = businessValidation.contact_email(formData.contact_email),
  if (emailError) {;
      errors.contact_email = emailError;,
  isValid = false;
    },
  // Validate contact phone;
    const phoneError = businessValidation.contact_phone(formData.contact_phone),
  if (phoneError) {;
      errors.contact_phone = phoneError;,
  isValid = false;
    },
  // Validate business address;
    const addressError = businessValidation.business_address(formData.business_address),
  if (addressError) {;
      errors.business_address = addressError;,
  isValid = false;
    },
  // Validate website (optional)
    if (formData.website) {,
  const websiteError = businessValidation.website(formData.website)
      if (websiteError) {;,
  errors.website = websiteError;
        isValid = false;,
  }
    },
  // Validate service categories;
    if (formData.service_categories.length = == 0) {,
  errors.service_categories = 'Please select at least one service category';
      isValid = false;,
  }
    // Validate availability;,
  if (formData.availability.weekdays.length = == 0) {
      errors.availability = 'Please select at least one available day';,
  isValid = false;
    },
  setValidationErrors(errors)
    return isValid;,
  }, [formData]),
  const handleSave = useCallback(async () => {;
  if (!provider) return null // Validate form first;,
  if (!validateForm()) {
      showError('Please fix the validation errors before saving'),
  return null;
    },
  setSaving(true)
    try { const updateData = {,
  business_name: formData.business_name,
        description: formData.description,
        contact_email: formData.contact_email,
        contact_phone: formData.contact_phone,
        business_address: formData.business_address,
        website: formData.website || null,
        service_categories: formData.service_categories,
        availability: formData.availability },
  const response = await serviceProviderService.updateServiceProvider(provider.id, updateData);,
  ;
      if (response.error) {,
  throw new Error(response.error)
      },
  showSuccess('Provider profile updated successfully')
      logger.info('Provider profile updated', 'ServiceProviderEditProfileScreen', {,
  providerId: provider.id),
    fieldsUpdated: Object.keys(updateData),
  })
      ;,
  router.back()
    } catch (error) {,
  const errorMessage = error instanceof Error ? error.message     : 'Failed to update profile'
      logger.error('Failed to update provider profile' 'ServiceProviderEditProfileScreen', {,
  error: errorMessage),
        providerId: provider? .id),
  })
      showError(errorMessage),
  } finally {
      setSaving(false),
  }
  }, [provider, formData, validateForm, showSuccess, showError, router]),
  const handleCategoryToggle = (categoryName  : string) => {
  const currentCategories = formData.service_categories,
  const isSelected = currentCategories.includes(categoryName)
    ,
  if (isSelected) {
      handleInputChange('service_categories' currentCategories.filter(cat => cat !== categoryName)),
  } else {
      handleInputChange('service_categories', [...currentCategories, categoryName]),
  }
  },
  const handleWeekdayToggle = (weekday: string) => {
  const currentWeekdays = formData.availability.weekdays;,
  const isSelected = currentWeekdays.includes(weekday);
    ;,
  if (isSelected) {
      handleInputChange('availability', {,
  ...formData.availability, ,
  weekdays: currentWeekdays.filter(day = > day !== weekday)
      }),
  } else { handleInputChange('availability', {,
  ...formData.availability, ,
  weekdays: [...currentWeekdays, weekday] }),
  }
  },
  const colors = COLORS.light // You can implement dark mode logic here;
  const renderFormField = (label: string,
    field: keyof BusinessFormData,
    placeholder: string,
    icon: React.ComponentType<any>,;,
  multiline = false, ,
  keyboardType: 'default' | 'email-address' | 'phone-pad' | 'url' = 'default') = > {
  const Icon = icon;,
  const hasError = !!validationErrors[field];,
  ;
    return (,
  <View style= {styles.fieldContainer}>
        <View style={styles.fieldHeader}>,
  <Icon size={20} color={{hasError ? theme.colors.error     : theme.colors.primary} /}>
          <Text style={[styles.fieldLabel { color: theme.colors.text}]}>{label}</Text>,
  {['business_name',  'description', 'contact_email', 'contact_phone', 'business_address'].includes(field) && (,
  <Text style={[styles.requiredIndicator, { color: theme.colors.error}]}>*</Text>,
  )}
        </View>,
  <TextInput
          style={{  [styles.textInput,
  multiline && styles.textInputMultiline, {,
  backgroundColor: theme.colors.inputBackground,
              borderColor: hasError ? theme.colors.error   : theme.colors.border,
    color: theme.colors.text }}]},
  value={formData[field] as string} onChangeText={value => handleInputChange(field value)} placeholder={placeholder} placeholderTextColor={theme.colors.textSecondary} multiline={multiline} numberOfLines={{  multiline ? 4   : 1    }} keyboardType={keyboardType},
  />
        {hasError && (,
  <Text style={[styles.errorText { color: theme.colors.error}]}>,
  {validationErrors[field]},
  </Text>
        )},
  </View>
    ),
  }
  const renderCategoryModal = () => (,
  <Modal visible={categoryModalVisible} animationType="slide"
      presentationStyle="pageSheet",
  >
      <SafeAreaView style={[styles.modalContainer, { backgroundColor: theme.colors.background}]}>,
  <View style={styles.modalHeader}>
          <Text style={[styles.modalTitle, { color: theme.colors.text}]}>Select Service Categories</Text>,
  <TouchableOpacity onPress={() => setCategoryModalVisible(false)}>
            <X size={24} color={{theme.colors.text} /}>,
  </TouchableOpacity>
        </View>,
  <FlatList data={categories} keyExtractor={(item) ={}> item.id} renderItem={({ item }) => {
  const isSelected = formData.service_categories.includes(item.name),
  return (
    <TouchableOpacity,
  style = {[
                  styles.categoryItem, ,
  {
                    backgroundColor: isSelected ? theme.colors.primary + '20'     : theme.colors.surface,
    borderColor: isSelected ? theme.colors.primary  : theme.colors.border,
  }
                ]},
  onPress={() => handleCategoryToggle(item.name)}
              >,
  <View style={styles.categoryContent}>
                  <Text style={[styles.categoryName { color: theme.colors.text}]}>{item.name}</Text>,
  <Text style={[styles.categoryDescription, { color: theme.colors.textSecondary}]}>,
  {item.description}
                  </Text>,
  </View>
                {isSelected && <CheckCircle size={20} color={{theme.colors.primary} /}>,
  </TouchableOpacity>
            ),
  }}
          style={styles.categoryList},
  />
        <TouchableOpacity,
  style={{  [styles.modalSaveButton, { backgroundColor: theme.colors.primary }}]},
  onPress={() => setCategoryModalVisible(false)}
        >,
  <Text style={styles.modalSaveButtonText}>Done ({formData.service_categories.length} selected)</Text>
        </TouchableOpacity>,
  </SafeAreaView>
    </Modal>,
  )
  const renderScheduleModal = () => (,
  <Modal visible={scheduleModalVisible} animationType="slide"
      presentationStyle="pageSheet",
  >
      <SafeAreaView style={[styles.modalContainer, { backgroundColor: theme.colors.background}]}>,
  <View style={styles.modalHeader}>
          <Text style={[styles.modalTitle, { color: theme.colors.text}]}>Set Availability</Text>,
  <TouchableOpacity onPress={() => setScheduleModalVisible(false)}>
            <X size={24} color={{theme.colors.text} /}>,
  </TouchableOpacity>
        </View>,
  <ScrollView style={styles.scheduleContent}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text}]}>Working Days</Text>,
  <View style={styles.weekdaysContainer}>
            {WEEKDAYS.map((day) => {,
  const isSelected = formData.availability.weekdays.includes(day)
              return (,
  <TouchableOpacity key = {day} style={{  [
                    styles.weekdayChip, {,
  backgroundColor: isSelected ? theme.colors.primary   : theme.colors.surface,
    borderColor: isSelected ? theme.colors.primary  : theme.colors.border }},
   ]},
  onPress = {() => handleWeekdayToggle(day)}
                >,
  <Text style={{  [
                    styles.weekdayText, { color: isSelected ? 'white'   : theme.colors.text }},
   ]}>,
  {day.substring(0 3)}
                  </Text>,
  </TouchableOpacity>
              ),
  })}
          </View>,
  <Text style={[styles.sectionTitle, { color: theme.colors.text, marginTop: 24}]}>Working Hours</Text>,
  <View style={styles.hoursContainer}>
            <View style={styles.timeInputContainer}>,
  <Text style={[styles.timeLabel, { color: theme.colors.textSecondary}]}>Start Time</Text>,
  <TextInput
                style={{  [styles.timeInput, { backgroundColor: theme.colors.inputBackground, borderColor: theme.colors.border, color: theme.colors.text }}]},
  value={formData.availability.hours.start} onChangeText={{  (value) => handleInputChange('availability', {,
  ...formData.availability, hours: { ...formData.availability.hours, start: value     }},
  })}
                placeholder="09: 00",
  placeholderTextColor={theme.colors.textSecondary}
              />,
  </View>
            <View style={styles.timeInputContainer}>,
  <Text style={[styles.timeLabel, { color: theme.colors.textSecondary}]}>End Time</Text>,
  <TextInput
                style={{  [styles.timeInput, { backgroundColor: theme.colors.inputBackground, borderColor: theme.colors.border, color: theme.colors.text }}]},
  value={formData.availability.hours.end} onChangeText={{  (value) => handleInputChange('availability', {,
  ...formData.availability, hours: { ...formData.availability.hours, end: value     }},
  })}
                placeholder= "17: 00";,
  placeholderTextColor= {theme.colors.textSecondary}
              />,
  </View>
          </View>,
  </ScrollView>
        <TouchableOpacity,
  style={{  [styles.modalSaveButton, { backgroundColor: theme.colors.primary }}]},
  onPress={() => setScheduleModalVisible(false)}
        >,
  <Text style={styles.modalSaveButtonText}>Save Schedule</Text>
        </TouchableOpacity>,
  </SafeAreaView>
    </Modal>,
  )
  if (loading) {,
  return (
    <SafeAreaView style={[styles.container,  { backgroundColor: theme.colors.background}]}>,
  <Stack.Screen options={{  title: 'Edit Provider Profile'      }} />
        <View style={styles.loadingContainer}>,
  <ActivityIndicator size="large" color={{theme.colors.primary} /}>
          <Text style={[styles.loadingText, { color: theme.colors.text}]}>Loading profile...</Text>,
  </View>
      </SafeAreaView>,
  )
  },
  return (
    <SafeAreaView style={[styles.container,  { backgroundColor: theme.colors.background}]}>,
  <Stack.Screen options= {{  {
          title: 'Edit Provider Profile',
          headerRight: () = > (, ,
  <TouchableOpacity onPress = {handleSave    }} disabled={saving}>
              {saving ? (,
  <ActivityIndicator size="small" color={{theme.colors.primary} /}>
              )     : (<Save size={20} color={{theme.colors.primary} /}>,
  )}
            </TouchableOpacity>,
  )
        }},
  />
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>,
  {/* Business Information */}
        <View style={[styles.section { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.sectionHeader, { color: theme.colors.text}]}>Business Information</Text>,
  {renderFormField('Business Name', 'business_name', 'Enter your business name', Building2)},
  {renderFormField('Business Description', 'description', 'Describe your services and expertise', Edit3, true)},
  {renderFormField('Contact Email', 'contact_email', '<EMAIL>', Mail, false, 'email-address')},
  {renderFormField('Contact Phone', 'contact_phone', '+****************', Phone, false, 'phone-pad')},
  {renderFormField('Business Address', 'business_address', 'Enter your business address', MapPin)},
  {renderFormField('Website (Optional)', 'website', 'https://yourbusiness.com', Globe, false, 'url')},
  </View>
        {/* Service Categories */},
  <View style={[styles.section, { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.sectionHeader, { color: theme.colors.text}]}>Service Categories *</Text>,
  <TouchableOpacity
            style={{  [
              styles.selectButton, {,
  backgroundColor: theme.colors.inputBackground,
                borderColor: validationErrors.service_categories ? theme.colors.error  : theme.colors.border }},
   ]},
  onPress = {() => setCategoryModalVisible(true)}
          >,
  <View style={styles.selectButtonContent}>
              <Text style={{  [
                styles.selectButtonText, { color: formData.service_categories.length > 0 ? theme.colors.text   : theme.colors.textSecondary }},
   ]}>,
  {formData.service_categories.length > 0 
                  ? `${formData.service_categories.length} categories selected`,
  : 'Select service categories'
                },
  </Text>
              <ChevronDown size={20} color={{theme.colors.textSecondary} /}>,
  </View>
          </TouchableOpacity>,
  {formData.service_categories.length > 0 && (
            <View style={styles.selectedCategoriesContainer}>,
  {formData.service_categories.map((category, index) => (,
  <View key={index} style={[styles.categoryChip, { backgroundColor: theme.colors.primary + '20'}]}>,
  <Text style={[styles.categoryChipText, { color: theme.colors.primary}]}>{category}</Text>,
  </View>
              ))},
  </View>
          )},
  {validationErrors.service_categories && (
            <Text style={[styles.errorText, { color: theme.colors.error}]}>,
  {validationErrors.service_categories}
            </Text>,
  )}
        </View>,
  {/* Availability */}
        <View style={[styles.section, { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.sectionHeader, { color: theme.colors.text}]}>Availability *</Text>,
  <TouchableOpacity
            style={{  [
              styles.selectButton,
  {
                backgroundColor: theme.colors.inputBackground,
                borderColor: validationErrors.availability ? theme.colors.error    : theme.colors.border }},
   ]},
  onPress={() => setScheduleModalVisible(true)}
          >,
  <View style={styles.selectButtonContent}>
              <Text style={[styles.selectButtonText { color: theme.colors.text}]}>,
  {formData.availability.weekdays.length > 0, ,
  ? `${formData.availability.weekdays.length} days • ${formData.availability.hours.start} - ${formData.availability.hours.end}`
                    : 'Set your availability',
  }
              </Text>,
  <ChevronDown size={20} color={{theme.colors.textSecondary} /}>
            </View>,
  </TouchableOpacity>
          {validationErrors.availability && (,
  <Text style={[styles.errorText, { color: theme.colors.error}]}>,
  {validationErrors.availability}
            </Text>,
  )}
        </View>,
  {/* Save Button */}
        <TouchableOpacity,
  style={{  [styles.saveButton, { backgroundColor: theme.colors.primary }}]},
  onPress= {handleSave} disabled={saving}
        >,
  {saving ? (
            <ActivityIndicator color={"white" /}>,
  )   : (
            <>,
  <Save size={20} color={"white" /}>
              <Text style={styles.saveButtonText}>Save Changes</Text>,
  </>
          )},
  </TouchableOpacity>
      </ScrollView>,
  {renderCategoryModal()}
      {renderScheduleModal()},
  <ToastComponent />
    </SafeAreaView>,
  )
},
  const styles = StyleSheet.create({ container: {,
    flex: 1 },
  loadingContainer: { flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20 },
  loadingText: { marginTop: 12,
    fontSize: 16 },
  scrollView: { flex: 1 },
  scrollContent: { padding: 16 },
  section: {,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 };,
  shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionHeader: { fontSize: 18,
    fontWeight: '600',
    marginBottom: 16 },
  fieldContainer: { marginBottom: 20 },
  fieldHeader: { flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8 },
  fieldLabel: { fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
    flex: 1 },
  requiredIndicator: {,
    fontSize: 16,
    fontWeight: 'bold',
  },
  textInput: { borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    minHeight: 48 },
  textInputMultiline: {,
    minHeight: 100,
    textAlignVertical: 'top',
  },
  selectButton: {,
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    minHeight: 48,
    justifyContent: 'center',
  },
  selectButtonContent: {,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  selectButtonText: { fontSize: 16 },
  selectedCategoriesContainer: { flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 12,
    gap: 8 },
  categoryChip: { paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16 },
  categoryChipText: {,
    fontSize: 14,
    fontWeight: '500',
  },
  errorText: { fontSize: 14,
    marginTop: 4 },
  saveButton: { flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 8,
    marginTop: 8,
    marginBottom: 32,
    gap: 8 },
  saveButtonText: {,
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  // Modal styles;,
  modalContainer: { flex: 1 },
  modalHeader: {,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  },
  modalTitle: {,
    fontSize: 18,
    fontWeight: '600',
  },
  categoryList: { flex: 1,
    padding: 16 },
  categoryItem: { flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 8 },
  categoryContent: { flex: 1 },
  categoryName: { fontSize: 16,
    fontWeight: '500',
    marginBottom: 4 },
  categoryDescription: { fontSize: 14 },
  modalSaveButton: {,
    margin: 16,
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  modalSaveButtonText: {,
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  // Schedule modal styles, ,
  scheduleContent: { flex: 1,
    padding: 16 },
  sectionTitle: { fontSize: 16,
    fontWeight: '600',
    marginBottom: 12 },
  weekdaysContainer: { flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8 },
  weekdayChip: { paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1 },
  weekdayText: {,
    fontSize: 14,
    fontWeight: '500',
  });
  hoursContainer: { flexDirection: 'row'),
    gap: 16 },
  timeInputContainer: { flex: 1 },
  timeLabel: { fontSize: 14,
    marginBottom: 8 },
  timeInput: {,
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    textAlign: 'center'),
  },
}); ;