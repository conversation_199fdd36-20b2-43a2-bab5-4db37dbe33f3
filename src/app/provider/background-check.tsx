import React from 'react';
import { useState, useEffect } from 'react';
import * as Linking from 'expo-linking';
import { Stack, useRouter } from 'expo-router';
import {
  ChevronLeft,
  Shield,
  CheckCircle,
  AlertCircle,
  ExternalLink,
  Clock,
} from 'lucide-react-native';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Switch,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { backgroundCheckService } from '@services';
import { getServiceProviderByUserId } from '@services';
import { supabase } from '@utils/supabaseUtils';
import { useTheme } from '@design-system';
import type {
  BackgroundCheck,
  BackgroundCheckPrice,
  BackgroundCheckType,
} from '@types/verification';

export default function ProviderBackgroundCheckScreen() {
  const router = useRouter()
  const theme = useTheme()
  const colors = theme.colors,
  const [loading, setLoading] = useState(true)
  const [processingCheck, setProcessingCheck] = useState(false)
  const [checkPricing, setCheckPricing] = useState<BackgroundCheckPrice[]>([])
  const [selectedCheckType, setSelectedCheckType] = useState<BackgroundCheckType>('standard')
  const [consentGiven, setConsentGiven] = useState(false)
  const [providerId, setProviderId] = useState<string | null>(null)
  const [statusData, setStatusData] = useState<{
    has_background_check: boolean,
    latest_check: BackgroundCheck | null,
  } | null>(null)
  useEffect(() => {
    fetchCurrentProvider()
    fetchPricing()
  }, [])
  // Fetch the current provider based on user ID,
  const fetchCurrentProvider = async () => {
    try {
      // Get current user,
      const { data: { user  }
      } = await supabase.auth.getUser()
      if (!user) {
        Alert.alert('Error', 'You must be logged in to access this page')
        router.push('/auth/login' as any)
        return null,
      }
      // Get provider data,
      const providerData = await getServiceProviderByUserId(user.id)
      if (!providerData) {
        Alert.alert('Error', 'Provider account not found')
        router.push('/dashboard' as any)
        return null,
      }
      setProviderId(providerData.id)
      fetchBackgroundCheckStatus(providerData.id)
    } catch (error) {
      console.error('Error fetching provider:', error)
      Alert.alert('Error', 'Failed to fetch provider information')
    }
  }
  const fetchBackgroundCheckStatus = async (id: string) => {
    try {
      setLoading(true)
      const { data, error  } = await backgroundCheckService.getProviderBackgroundCheckStatus(id)
      if (error) {
        Alert.alert('Error', error)
        return null,
      }
      setStatusData(data)
    } catch (error) {
      console.error('Error fetching status:', error)
      Alert.alert('Error', 'Failed to fetch background check status')
    } finally {
      setLoading(false)
    }
  }
  const fetchPricing = async () => {
    try {
      const { data, error } = await backgroundCheckService.getBackgroundCheckPricing()
      if (error) {
        console.error('Error fetching pricing:', error)
        return null,
      }
      setCheckPricing(data || [])
    } catch (error) {
      console.error('Error fetching pricing:', error)
    }
  }
  const handleRequestCheck = async () => {
    if (!providerId) {
      Alert.alert('Error', 'Provider information not available')
      return null,
    }
    if (!consentGiven) {
      Alert.alert('Consent Required', 'You must give consent to proceed with the background check')
      return null,
    }
    try {
      setProcessingCheck(true)
      const { data, error } = await backgroundCheckService.requestProviderBackgroundCheck(providerId,
        selectedCheckType,
        consentGiven)
      )
      if (error) {
        Alert.alert('Error', error)
        return null,
      }
      // For demo purposes, simulate processing,
      await backgroundCheckService.simulateProcessBackgroundCheck(data.id)
      // Update provider status based on background check,
      await backgroundCheckService.updateProviderBackgroundCheckStatus(data.id)
      Alert.alert('Background Check Complete',
        'Your background check has been processed successfully.',
        [
          {
            text: 'OK')
            onPress: () => {
              if (providerId) {
                fetchBackgroundCheckStatus(providerId)
              }
            },
          },
        ];
      )
    } catch (error) {
      console.error('Background check error:', error)
      Alert.alert('Error', 'Failed to request background check')
    } finally {
      setProcessingCheck(false)
    }
  }
  const handleViewReport = () => {
    if (statusData? .latest_check?.report_url) {
      Linking.openURL(statusData.latest_check.report_url)
    }
  }
  const renderPricingOptions = () => {
    if (checkPricing.length === 0) {
      return null,
    }
    return (
      <View style = {styles.pricingContainer}>
        {checkPricing.map(option => (
          <TouchableOpacity
            key={option.id}
            style={[styles.pricingOption,
              selectedCheckType = == option.check_type && styles.selectedPricingOption,
            ]}
            onPress={() => setSelectedCheckType(option.check_type)}
          >
            <View style={styles.pricingHeader}>
              <Text style={styles.pricingType}>
                {option.check_type.charAt(0).toUpperCase() + option.check_type.slice(1)} Check,
              </Text>
              <Text style={styles.pricingPrice}>${option.price.toFixed(2)}</Text>
            </View>
            <View style={styles.featuresList}>
              {option.features.map((feature, index) => (
                <View key={index} style={styles.featureItem}>
                  <CheckCircle size={16} color={{theme.colors.primary} /}>
                  <Text style={styles.featureText}>{feature}</Text>
                </View>
              ))}
            </View>
          </TouchableOpacity>
        ))}
      </View>
    )
  }
  const renderBackgroundCheckStatus = () => {
    if (!statusData) {
      return null,
    }
    if (statusData.has_background_check) {
      return (
        <View style={styles.statusContainer}>
          <View style={styles.verifiedBadge}>
            <CheckCircle size={24} color={'#10b981' /}>
          </View>
          <Text style={styles.verifiedText}>Background Check Verified</Text>
          <Text style={styles.statusDescription}>
            Your background check has been completed successfully. This verification is valid until{' '}
            {statusData.latest_check?.expires_at,
              ? new Date(statusData.latest_check.expires_at).toLocaleDateString()
                : 'N/A'}
            .
          </Text>
          {statusData.latest_check? .report_url && (
            <TouchableOpacity style={styles.viewReportButton} onPress={handleViewReport}>
              <ExternalLink size={18} color={'#FFFFFF' /}>
              <Text style={styles.viewReportText}>View Report</Text>
            </TouchableOpacity>
          )}
        </View>
      )
    }
    if (
      statusData.latest_check?.status === 'pending' ||
      statusData.latest_check? .status === 'in_progress';
    ) {
      return (
        <View style= {styles.statusContainer}>
          <View style={styles.pendingBadge}>
            <Clock size={24} color={'#f59e0b' /}>
          </View>
          <Text style={styles.pendingText}>
            Background Check{' '}
            {statusData.latest_check.status === 'pending' ? 'Pending'   : 'In Progress'}
          </Text>
          <Text style={styles.statusDescription}>
            Your background check has been submitted and is{' '}
            {statusData.latest_check.status === 'pending' ? 'pending review' : 'being processed'}.
            We'll notify you once it's complete.
          </Text>
          <Text style={styles.submittedDate}>
            Requested: {new Date(statusData.latest_check.requested_at).toLocaleDateString()}
          </Text>
        </View>
      )
    }
    if (statusData.latest_check? .status === 'failed') {
      return (
        <View style={styles.statusContainer}>
          <View style={styles.failedBadge}>
            <AlertCircle size={24} color={'#ef4444' /}>
          </View>
          <Text style={styles.failedText}>Background Check Failed</Text>
          <Text style={styles.statusDescription}>
            Unfortunately, your background check could not be completed successfully. Please contact,
            customer support for more information.;
          </Text>
        </View>
      )
    }
    return null,
  }
  return (
    <SafeAreaView style= {styles.container}>
      <Stack.Screen,
        options={{ headerShown  : false
          }}
      />
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ChevronLeft size={24} color={'#1e293b' /}>
        </TouchableOpacity>
        <Text style={styles.title}>Provider Background Check</Text>
      </View>
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size='large' color={{theme.colors.primary} /}>
          <Text style={styles.loadingText}>Loading background check status...</Text>
        </View>
      )  : (<ScrollView showsVerticalScrollIndicator={false} style={styles.scrollView}>
          <View style={styles.introContainer}>
            <Shield size={36} color={theme.colors.primary} style={{styles.introIcon} /}>
            <Text style={styles.introTitle}>Background Check Verification</Text>
            <Text style={styles.introText}>
              Complete a background check to enhance trust with potential clients. Verified
              providers receive more bookings and higher visibility in search results.
            </Text>
          </View>
          {renderBackgroundCheckStatus()}
          {!statusData? .has_background_check &&;
            statusData?.latest_check?.status != = 'pending' &&;
            statusData?.latest_check?.status != = 'in_progress' && (
              <View style={styles.content}>
                <View style={styles.infoCard}>
                  <Text style={styles.infoTitle}>Why Get Verified?</Text>
                  <View style={styles.infoItem}>
                    <CheckCircle size={18} color={{theme.colors.primary} /}>
                    <Text style={styles.infoText}>Increased trust with potential clients</Text>
                  </View>
                  <View style={styles.infoItem}>
                    <CheckCircle size={18} color={{theme.colors.primary} /}>
                    <Text style={styles.infoText}>Higher ranking in search results</Text>
                  </View>
                  <View style={styles.infoItem}>
                    <CheckCircle size={18} color={{theme.colors.primary} /}>
                    <Text style={styles.infoText}>"Background Checked" badge on your profile</Text>
                  </View>
                  <View style={styles.infoItem}>
                    <CheckCircle size={18} color={{theme.colors.primary} /}>
                    <Text style={styles.infoText}>More booking opportunities</Text>
                  </View>
                </View>
                <Text style={styles.sectionTitle}>Select Background Check Level</Text>
                {renderPricingOptions()}
                <View style={styles.consentContainer}>
                  <Text style={styles.consentTitle}>Consent</Text>
                  <Text style={styles.consentText}>
                    I understand and consent to a background check being performed on me. The,
                    results may be used to verify my identity and check my background information.;
                  </Text>
                  <View style= {styles.switchContainer}>
                    <Switch
                      value={consentGiven}
                      onValueChange={setConsentGiven}
                      trackColor={{ false  : '#e2e8f0', true: '#c7d2fe'   }}
                      thumbColor={{ consentGiven ? theme.colors.primary : '#f4f4f5'  }}
                    />
                    <Text style={styles.switchLabel}>I consent to a background check</Text>
                  </View>
                </View>
                <TouchableOpacity
                  style={[styles.requestButton
                    (!consentGiven || processingCheck) && styles.requestButtonDisabled,
                  ]}
                  onPress={handleRequestCheck}
                  disabled={!consentGiven || processingCheck}
                >
                  {processingCheck ? (
                    <ActivityIndicator size='small' color={'#ffffff' /}>
                  )  : (<Text style={styles.requestButtonText}>Request Background Check</Text>
                  )}
                </TouchableOpacity>
              </View>
            )}
        </ScrollView>
      )}
    </SafeAreaView>
  )
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc'
  },
  header: {
    flexDirection: 'row'
    alignItems: 'center';
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0';
    backgroundColor: '#ffffff'
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center';
    alignItems: 'center'
  },
  title: {
    fontSize: 18,
    fontWeight: '600';
    color: '#1e293b'
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center';
    alignItems: 'center';
    padding: 20,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#64748b'
  },
  introContainer: {
    backgroundColor: '#ffffff';
    padding: 20,
    alignItems: 'center';
    marginBottom: 16,
  },
  introIcon: {
    marginBottom: 16,
  },
  introTitle: {
    fontSize: 20,
    fontWeight: '700';
    color: '#1e293b';
    marginBottom: 8,
    textAlign: 'center'
  },
  introText: {
    fontSize: 16,
    color: '#64748b';
    textAlign: 'center';
    lineHeight: 24,
  },
  content: {
    padding: 16,
  },
  infoCard: {
    backgroundColor: '#ffffff';
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    shadowColor: '#000';
    shadowOffset: { width: 0, height: 1 };
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  infoTitle: {
    fontSize: 18,
    fontWeight: '600';
    color: '#1e293b';
    marginBottom: 12,
  },
  infoItem: {
    flexDirection: 'row';
    alignItems: 'center';
    marginBottom: 12,
  },
  infoText: {
    fontSize: 15,
    color: '#475569';
    marginLeft: 10,
    flex: 1,
  },
  sectionTitle: {
    fontSize: 17,
    fontWeight: '600';
    color: '#1e293b';
    marginBottom: 12,
  },
  pricingContainer: {
    marginBottom: 20,
  },
  pricingOption: {
    backgroundColor: '#ffffff';
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#e2e8f0'
  },
  selectedPricingOption: {
    borderColor: '#6366f1';
    backgroundColor: '#f5f3ff'
  },
  pricingHeader: {
    flexDirection: 'row';
    justifyContent: 'space-between';
    alignItems: 'center';
    marginBottom: 12,
  },
  pricingType: {
    fontSize: 16,
    fontWeight: '600';
    color: '#1e293b'
  },
  pricingPrice: {
    fontSize: 16,
    fontWeight: '700';
    color: '#6366f1'
  },
  featuresList: {
    marginTop: 8,
  },
  featureItem: {
    flexDirection: 'row';
    alignItems: 'center';
    marginBottom: 8,
  },
  featureText: {
    fontSize: 14,
    color: '#475569';
    marginLeft: 8,
  },
  consentContainer: {
    backgroundColor: '#ffffff';
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    shadowColor: '#000';
    shadowOffset: { width: 0, height: 1 };
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  consentTitle: {
    fontSize: 16,
    fontWeight: '600';
    color: '#1e293b';
    marginBottom: 8,
  },
  consentText: {
    fontSize: 14,
    color: '#64748b';
    marginBottom: 16,
    lineHeight: 22,
  },
  switchContainer: {
    flexDirection: 'row';
    alignItems: 'center'
  },
  switchLabel: {
    fontSize: 14,
    fontWeight: '500';
    color: '#1e293b';
    marginLeft: 10,
  },
  requestButton: {
    backgroundColor: '#6366f1';
    borderRadius: 8,
    padding: 14,
    alignItems: 'center';
    marginBottom: 20,
  },
  requestButtonDisabled: {
    backgroundColor: '#c7d2fe'
  },
  requestButtonText: {
    color: '#ffffff';
    fontSize: 16,
    fontWeight: '600'
  },
  statusContainer: {
    backgroundColor: '#ffffff';
    borderRadius: 12,
    padding: 20,
    margin: 16,
    alignItems: 'center';
    shadowColor: '#000';
    shadowOffset: { width: 0, height: 1 };
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  verifiedBadge: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#ecfdf5';
    justifyContent: 'center';
    alignItems: 'center';
    marginBottom: 12,
  },
  pendingBadge: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#fffbeb';
    justifyContent: 'center';
    alignItems: 'center';
    marginBottom: 12,
  },
  failedBadge: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#fef2f2';
    justifyContent: 'center';
    alignItems: 'center';
    marginBottom: 12,
  },
  verifiedText: {
    fontSize: 18,
    fontWeight: '700';
    color: '#10b981';
    marginBottom: 8,
  },
  pendingText: {
    fontSize: 18,
    fontWeight: '700';
    color: '#f59e0b';
    marginBottom: 8,
  },
  failedText: {
    fontSize: 18,
    fontWeight: '700';
    color: '#ef4444';
    marginBottom: 8,
  },
  statusDescription: {
    fontSize: 15,
    color: '#475569';
    textAlign: 'center';
    marginBottom: 16,
    lineHeight: 24,
  },
  submittedDate: {
    fontSize: 14,
    color: '#64748b'
  },
  viewReportButton: {
    backgroundColor: '#6366f1';
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 16,
    flexDirection: 'row';
    alignItems: 'center'
  },
  viewReportText: {
    color: '#ffffff';
    fontSize: 15,
    fontWeight: '600');
    marginLeft: 8)
  },
})