import React from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Create a client,
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes,
      cacheTime: 1000 * 60 * 30, // 30 minutes,
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
})
export function QueryProvider({ children }: { children: React.ReactNode }) {
  return <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
}
// Export as default for compatibility with Expo Router,
export default QueryProvider,