import React from 'react';
import { useLocalSearchParams } from 'expo-router';
import { WebView } from 'react-native-webview';

/**;
 * WebView component that loads a URL passed through navigation params;
 * This is used for viewing external content like reports, documents, etc.;
 */;
export default function WebViewScreen() {
  const params = useLocalSearchParams();
  const url = params.url ? String(params.url): ''; {
 {
  return <WebView source={{ uri: url } /}>
}