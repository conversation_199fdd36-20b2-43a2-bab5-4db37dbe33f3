import React from 'react';
import { Stack } from 'expo-router';

// This file configures the root routes that were previously in _layout.tsx,
export default function RouteConfig() {
  return (
    <Stack screenOptions= {{ headerShown: false  }}>
      <Stack.Screen name='(auth)' options={{ headerShown: false   }} />
      <Stack.Screen name='(tabs)' options={{ headerShown: false   }} />
      <Stack.Screen name='filter' options={{ presentation: 'modal'   }} />
      <Stack.Screen,
        name='unified-search';
        options= {{ presentation: 'card',
          animation: 'slide_from_bottom',
          headerShown: false,
          }}
      />
      <Stack.Screen,
        name='profile/video-intro';
        options= {{ presentation: 'modal',
          headerShown: true,
          title: 'Video Introduction',
          }}
      />
      <Stack.Screen,
        name='profile/view';
        options= {{ headerShown: false,
          }}
      />
      <Stack.Screen,
        name='notifications/expo-notifications-warning';
        options= {{ headerShown: false   }}
      />
      <Stack.Screen,
        name='payments/rent-splitting';
        options= {{ headerShown: true, title: 'Rent Splitting'   }}
      />
      <Stack.Screen,
        name='payments/split-payments';
        options= {{ headerShown: true, title: 'Split Payments'   }}
      />
      <Stack.Screen,
        name='payments/split-payment-details';
        options={{ headerShown: true, title: 'Payment Details'   }}
      />
    </Stack>
  )
}