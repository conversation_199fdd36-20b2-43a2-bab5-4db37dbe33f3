import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { useAuth } from '@context/AuthContext';
import { useTheme } from '@design-system';
import { useToast } from '@core/errors';
import { getUserBookings, BookingStatus } from '@services/bookingService';
import { Calendar, Clock, MapPin, FileText, CheckCircle, XCircle } from 'lucide-react-native';
import { format } from 'date-fns';

export default function BookingsScreen() {
  const router = useRouter();
  const { authState } = useAuth();
  const user = authState?.user;
  const theme = useTheme();
  const colors = theme.colors;
  const toast = useToast();
  const [bookings, setBookings] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (user) {
      loadBookings();
    }
  }, [user]);

  const loadBookings = async () => {
    try {
      setIsLoading(true);
      setError(null);

      if (!user?.id) {
        setError('User not authenticated');
        return null;
      }
      const userBookings = await getUserBookings(user.id);
      setBookings(userBookings);
    } catch (err) {
      console.error('Error loading bookings:', err);
      setError('Failed to load bookings');
      toast.error('Could not load your bookings');
    } finally {
      setIsLoading(false);
    }
  }
  const getStatusColor = status => {
    switch (status) {
      case BookingStatus.CONFIRMED: ,
        return theme.colors.success;
      case BookingStatus.PENDING: ,
        return theme.colors.warning;
      case BookingStatus.CANCELLED: ,
        return theme.colors.error;
      case BookingStatus.COMPLETED: ,
        return theme.colors.info;
      case BookingStatus.RESCHEDULED: ,
        return theme.colors.secondary;
      default: ,
        return theme.colors.textLight;
    }
  }
  const getStatusIcon = status => {
    switch (status) {
      case BookingStatus.CONFIRMED: ,
        return <CheckCircle size={16} color={{getStatusColor(status)} /}>
      case BookingStatus.CANCELLED: ,
        return <XCircle size={16} color={{getStatusColor(status)} /}>
      default: ,
        return <Clock size={16} color={{getStatusColor(status)} /}>
    }
  }
  const renderBookingItem = ({ item }) => (
    <TouchableOpacity
      style={[
        styles.bookingCard,
        { backgroundColor: theme.colors.surface, borderColor: theme.colors.border },
      ]}
      onPress={() => router.push(`/booking-details/${item.id}`)}
    >
      <View style={styles.bookingHeader}>
        <Text style={[styles.serviceName, { color: theme.colors.text }]} numberOfLines={1}>
          {item.service?.name || 'Service'}
        </Text>
        <View style={styles.statusContainer}>
          {getStatusIcon(item.status)}
          <Text style={{[styles.statusText, { color: getStatusColor(item.status) }]}}>
            {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
          </Text>
        </View>
      </View>
      <View style={styles.bookingDetail}>
        <Calendar size={16} color={{theme.colors.textLight} /}>
        <Text style={{[styles.detailText, { color: theme.colors.textLight }]}}>
          {format(new Date(item.booking_date), 'EEE, MMM d, yyyy')} at{' '}
          {format(new Date(item.booking_date), 'h:mm a')}
        </Text>
      </View>
      <View style={styles.bookingDetail}>
        <MapPin size={16} color={{theme.colors.textLight} /}>
        <Text style={[styles.detailText, { color: theme.colors.textLight }]} numberOfLines={1}>
          {item.address}
        </Text>
      </View>
      <View style={styles.bookingFooter}>
        <Text style={{[styles.priceText, { color: theme.colors.text }]}}>
          ${item.price.toFixed(2)}
        </Text>
        <TouchableOpacity
          style={[styles.detailsButton, { borderColor: theme.colors.primary }]}
          onPress={() => router.push(`/booking-details/${item.id}`)}
        >
          <Text style={{[styles.detailsButtonText, { color: theme.colors.primary }]}}>
            View Details;
          </Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={{[styles.container, { backgroundColor: theme.colors.background }]}}>
      <Stack.Screen;
        options={{
          title: 'My Bookings',
          headerShown: true,
          headerShadowVisible: false,
          headerStyle: { backgroundColor: theme.colors.background },
          headerTintColor: theme.colors.text,
        }}
      />
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size='large' color={{theme.colors.primary} /}>
          <Text style={{[styles.loadingText, { color: theme.colors.textLight }]}}>
            Loading your bookings...;
          </Text>
        </View>
      ) : error ? (,
        <View style={styles.errorContainer}>
          <Text style={{[styles.errorText, { color: theme.colors.error }]}}>{error}</Text>
          <TouchableOpacity
            style={[styles.retryButton, { backgroundColor: theme.colors.primary }]}
            onPress={loadBookings}
          >
            <Text style={{[styles.retryButtonText, { color: theme.colors.white }]}}>Retry</Text>
          </TouchableOpacity>
        </View>
      ) : bookings.length === 0 ? (,
        <View style={styles.emptyContainer}>
          <FileText size={48} color={{theme.colors.textLight} /}>
          <Text style={{[styles.emptyTitle, { color: theme.colors.text }]}}>No Bookings Yet</Text>
          <Text style={{[styles.emptyText, { color: theme.colors.textLight }]}}>
            You haven't made any service bookings yet. Browse services to book your first;
            appointment.;
          </Text>
          <TouchableOpacity
            style={[styles.browseButton, { backgroundColor: theme.colors.primary }]}
            onPress={() => router.push('/(tabs)/services' as any)}
          >
            <Text style={{[styles.browseButtonText, { color: theme.colors.white }]}}>
              Browse Services;
            </Text>
          </TouchableOpacity>
        </View>
      ) : (,
        <FlatList
          data={bookings}
          renderItem={renderBookingItem}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
        />
      )}
    </View>
  );
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  errorText: {
    fontSize: 16,
    marginBottom: 16,
    textAlign: 'center',
  },
  retryButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    fontWeight: '600',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '700',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 24,
  },
  browseButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  browseButtonText: {
    fontWeight: '600',
  },
  listContent: {
    padding: 16,
  },
  bookingCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
  },
  bookingHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  serviceName: {
    fontSize: 18,
    fontWeight: '600',
    flex: 1,
    marginRight: 8,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 4,
  },
  bookingDetail: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  detailText: {
    fontSize: 14,
    marginLeft: 8,
    flex: 1,
  },
  bookingFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#E2E8F0',
  },
  priceText: {
    fontSize: 18,
    fontWeight: '700',
  },
  detailsButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 6,
    borderWidth: 1,
  },
  detailsButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
});
