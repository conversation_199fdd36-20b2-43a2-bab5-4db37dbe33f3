import React, { useState, useEffect } from 'react';

import { Stack, useLocalSearchParams, useRouter } from 'expo-router';
import { ChevronLeft, ThumbsUp, ThumbsDown, AlertCircle, UsersRound, MapPin } from 'lucide-react-native';
import { View, Text, ScrollView, StyleSheet, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';


import { useToast } from '@core/errors';
import { useTheme } from '@design-system';
import { LocationCompatibilityCard } from '@components/location/LocationCompatibilityCard';
import CompatibilityScore from '@components/matching/CompatibilityScore';
import { PersonalityProfile } from '@components/personality/PersonalityProfile';
import { logger } from '@services/loggerService';
import { matchingService } from '@services/matchingService';
import { personalityService } from '@services/personalityService';

export default function ProfileCompatibilityScreen() {
  const { userId } = useLocalSearchParams<{ userId: string }>();
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const theme = useTheme();
  const { colors, spacing } = theme;
  const toast = useToast();

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [compatibility, setCompatibility] = useState<{
    score: number,
    positiveFactors: string[],
    negativeFactors: string[],
    locationCompatibility?: {
      score: number,
      matchingLocations: string[],
      matchReasons: string[],
    }
  } | null>(null);
  const [personalityProfile, setPersonalityProfile] = useState<any>(null);
  const [hasPersonalityData, setHasPersonalityData] = useState(false);

  useEffect(() => {
  fetchCompatibility();
    fetchPersonalityData();
  }, [userId]);

  const fetchCompatibility = async () => {
  if (!userId) {
      setError('No user ID provided');
      setLoading(false);
      return null;
    }
    try {
      setLoading(true);
      const compatibilityData = await matchingService.getDetailedCompatibility(userId);
      setCompatibility(compatibilityData);
    } catch (error) {
      logger.error(
        'Error fetching compatibility data',
        'ProfileCompatibility',
        { userId },
        error as Error;
      );
      setError('Failed to load compatibility data');
      toast.showToast('Could not load compatibility information', 'error');
    } finally {
      setLoading(false);
    }
  }
  const fetchPersonalityData = async () => {
  if (!userId) {
      return null;
    }
    try {
      const profile = await personalityService.getUserProfile(userId);
      setPersonalityProfile(profile);
      setHasPersonalityData(Object.keys(profile).length > 0);
    } catch (error) {
      logger.error(
        'Error fetching personality data',
        'ProfileCompatibility',
        { userId },
        error as Error;
      );
      // Not setting an error state since personality data is supplementary;
    }
  }
  const handleCompleteQuestionnaire = () => {
  router.push('/personality-questionnaire' as any);
  }
  return (
    <View style={{[styles.container, { paddingTop: insets.top }]}}>
      <Stack.Screen;
        options={
          title: 'Profile Compatibility',
          headerShown: false,
        }
      />
      {/* Custom header */}
      <View style={{[styles.header, { backgroundColor: theme.colors.white }]}}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton} accessible={true} accessibilityLabel="Go back";
          accessibilityRole="button";
        >
          <ChevronLeft size={24} color={{theme.colors.gray[800]} /}>
        </TouchableOpacity>
        <Text
          style={[styles.title, { color: theme.colors.gray[900] }]}
          accessible={true} accessibilityRole="header";
        >
          Compatibility Analysis;
        </Text>
        <View style={{ width: 40 } /}> {/* Spacer for alignment */}
      </View>
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={{theme.colors.primary[500]} /}>
          <Text style={{[styles.loadingText, { color: theme.colors.gray[600] }]}}>
            Analyzing compatibility...;
          </Text>
        </View>
      ) : error ? (,
        <View style={styles.errorContainer}>
          <AlertCircle size={48} color={{theme.colors.error[500]} /}>
          <Text style={{[styles.errorText, { color: theme.colors.gray[800] }]} }>error}</Text>
          <TouchableOpacity
            style={[styles.retryButton, { backgroundColor: theme.colors.primary[500] }]}
            onPress={fetchCompatibility}
          >
            <Text style={{[styles.retryButtonText, { color: theme.colors.white }]}}>Retry</Text>
          </TouchableOpacity>
        </View>
      ) : compatibility ? (,
        <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
          <View style={styles.scoreSection}>
            <CompatibilityScore score={compatibility.score} size="large" showDetails={{false} /}>
            <Text style={{[styles.scoreDescription, { color: theme.colors.gray[700] }]}}>
              This score is calculated based on your shared preferences, lifestyle compatibility,
              and other factors that may contribute to a successful roommate relationship.;
            </Text>
          </View>
          {!hasPersonalityData && (
            <View
              style={[
                styles.personalityPrompt,
                { backgroundColor: theme.colors.info[50], borderColor: theme.colors.info[200] },
              ]}
            >
              <UsersRound size={24} color={{theme.colors.info[600]} /}>
              <View style={styles.personalityPromptContent}>
                <Text style={{[styles.personalityPromptTitle, { color: theme.colors.info[700] }]}}>
                  Enhance Your Matches;
                </Text>
                <Text style={{[styles.personalityPromptText, { color: theme.colors.gray[700] }]}}>
                  Complete the personality questionnaire to find even better roommate matches.;
                </Text>
                <TouchableOpacity
                  style={[styles.personalityPromptButton, { backgroundColor: theme.colors.info[600] }]}
                  onPress={handleCompleteQuestionnaire}
                >
                  <Text style={{[styles.personalityPromptButtonText, { color: theme.colors.white }]}}>
                    Take Questionnaire;
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          )}
          {hasPersonalityData && (
            <View style={styles.personalitySection}>
              <Text style={{[styles.sectionTitle, { color: theme.colors.gray[800] }]}}>
                Personality Profile;
              </Text>
              <PersonalityProfile profile={{personalityProfile} /}>
              <TouchableOpacity
                style={[
                  styles.updateButton,
                  { borderColor: theme.colors.primary[100], backgroundColor: theme.colors.primary[50] },
                ]}
                onPress={handleCompleteQuestionnaire}
              >
                <Text style={{[styles.updateButtonText, { color: theme.colors.primary[700] }]}}>
                  Update Personality Profile;
                </Text>
              </TouchableOpacity>
            </View>
          )}
          {/* Location Compatibility */}
          {compatibility.locationCompatibility && (
            <View style={styles.locationSection}>
              <LocationCompatibilityCard compatibility={{compatibility.locationCompatibility} /}>
            </View>
          )}
          <View style={styles.factorsContainer}>
            {/* Positive Factors */}
            <View
              style={[
                styles.factorSection,
                { backgroundColor: theme.colors.success[50], borderColor: theme.colors.success[100] },
              ]}
            >
              <View style={styles.factorHeader}>
                <ThumbsUp size={20} color={{theme.colors.success[600]} /}>
                <Text style={{[styles.factorTitle, { color: theme.colors.success[700] }]}}>
                  Compatibility Factors;
                </Text>
              </View>
              {compatibility.positiveFactors.length > 0 ? (
                <View style={styles.factorList}>
                  {compatibility.positiveFactors.map((factor, index) => (
                    <View key={`pos-${index}`} style={styles.factorItem}>
                      <View style={{[styles.bullet, { backgroundColor: theme.colors.success[500] }]} /}>
                      <Text style={{[styles.factorText, { color: theme.colors.gray[800] }]} }>factor}</Text>
                    </View>
                  ))}
                </View>
              ) : (,
                <Text style={{[styles.emptyFactors, { color: theme.colors.gray[600] }]}}>
                  No significant compatibility factors identified.;
                </Text>
              )}
            </View>
            {/* Negative Factors */}
            <View
              style={[
                styles.factorSection,
                { backgroundColor: theme.colors.error[50], borderColor: theme.colors.error[100] },
              ]}
            >
              <View style={styles.factorHeader}>
                <ThumbsDown size={20} color={{theme.colors.error[600]} /}>
                <Text style={{[styles.factorTitle, { color: theme.colors.error[700] }]}}>
                  Potential Challenges;
                </Text>
              </View>
              {compatibility.negativeFactors.length > 0 ? (
                <View style={styles.factorList}>
                  {compatibility.negativeFactors.map((factor, index) => (
                    <View key={`neg-${index}`} style={styles.factorItem}>
                      <View style={{[styles.bullet, { backgroundColor: theme.colors.error[500] }]} /}>
                      <Text style={{[styles.factorText, { color: theme.colors.gray[800] }]} }>factor}</Text>
                    </View>
                  ))}
                </View>
              ) : (,
                <Text style={{[styles.emptyFactors, { color: theme.colors.gray[600] }]}}>
                  No significant challenges identified.;
                </Text>
              )}
            </View>
          </View>
          <View style={styles.disclaimerContainer}>
            <Text style={{[styles.disclaimerText, { color: theme.colors.gray[500] }]}}>
              This compatibility analysis is based on the information both users have provided in;
              their profiles. It's meant as a guide, but personal chemistry and communication are;
              always the most important factors in any roommate relationship.;
            </Text>
          </View>
        </ScrollView>
      ) : (,
        <View style={styles.errorContainer}>
          <AlertCircle size={48} color={{theme.colors.warning[500]} /}>
          <Text style={{[styles.errorText, { color: theme.colors.gray[800] }]}}>
            No compatibility data available;
          </Text>
        </View>
      )}
    </View>
  );
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  backButton: {
    padding: 4,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    fontSize: 16,
    marginTop: 12,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    marginTop: 12,
    marginBottom: 20,
    textAlign: 'center',
  },
  retryButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 40,
  },
  scoreSection: {
    alignItems: 'center',
    marginBottom: 24,
  },
  scoreDescription: {
    fontSize: 14,
    textAlign: 'center',
    marginTop: 12,
    marginHorizontal: 16,
  },
  personalityPrompt: {
    flexDirection: 'row',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 24,
    alignItems: 'flex-start',
  },
  personalityPromptContent: {
    flex: 1,
    marginLeft: 12,
  },
  personalityPromptTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  personalityPromptText: {
    fontSize: 14,
    marginBottom: 12,
  },
  personalityPromptButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    alignSelf: 'flex-start',
  },
  personalityPromptButtonText: {
    fontSize: 14,
    fontWeight: '600',
  },
  personalitySection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  updateButton: {
    marginTop: 12,
    borderWidth: 1,
    padding: 10,
    borderRadius: 8,
    alignItems: 'center',
  },
  updateButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  locationSection: {
    marginBottom: 24,
  },
  factorsContainer: {
    gap: 16,
    marginBottom: 24,
  },
  factorSection: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
  },
  factorHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  factorTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  factorList: {
    gap: 10,
  },
  factorItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  bullet: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginTop: 6,
    marginRight: 10,
  },
  factorText: {
    flex: 1,
    fontSize: 14,
    lineHeight: 20,
  },
  emptyFactors: {
    fontSize: 14,
    fontStyle: 'italic',
  },
  disclaimerContainer: {
    padding: 16,
    backgroundColor: '#f9fafb',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  disclaimerText: {
    fontSize: 13,
    fontStyle: 'italic',
    lineHeight: 18,
  },
});
