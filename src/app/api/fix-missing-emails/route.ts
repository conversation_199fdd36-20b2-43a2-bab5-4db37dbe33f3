import {,
  supabase 
} from '@utils/supabaseUtils';,
  export async function POST(request: Request) {
  try {,
  // Get profiles with missing emails;
    const { data: profilesWithoutEmail, error: selectError  } = await supabase;,
  .from('user_profiles')
      .select('id, first_name, email'),
  .is('email', null),
  if (selectError) {
      console.error('Error fetching profiles without email:', selectError),
  return Response.json({
          error: 'Failed to fetch profiles'),
          details: selectError.message),
  },
        { status: 500 },
  )
    },
  if (!profilesWithoutEmail || profilesWithoutEmail.length = == 0) {
      return Response.json({,
  message: 'No profiles found with missing emails'),
        updated: 0),
  })
    },
  const results = [];,
  let successCount = 0;
    let errorCount = 0;,
  // Process each profile individually to avoid transaction issues;
    for (const profile of profilesWithoutEmail) {,
  try {
        // Get the auth user's email;,
  const { data: authUser, error: authError  } = await supabase.auth.admin.getUserById(profile.id),
  )
        if (authError || !authUser || !authUser.user? .email) {,
  console.warn(`No auth user found for profile ${profile.id}`)
          results.push({,
  profileId   : profile.id
            status: 'skipped',
    reason: 'No auth user or email found'),
  })
          continue;,
  }
        // Update the profile with the email from auth;,
  const { error: updateError } = await supabase;
          .from('user_profiles'),
  .update({
            email: authUser.user.email),
    updated_at: new Date().toISOString(),
  })
          .eq('id', profile.id),
  if (updateError) {
          console.error(`Error updating profile ${profile.id}:`, updateError),
  results.push({
            profileId: profile.id),
            status: 'error'),
            error: updateError.message),
  })
          errorCount++;,
  } else {
          console.log(`Successfully updated email for profile ${profile.id}`),
  results.push({
            profileId: profile.id),
            status: 'updated'),
            email: authUser.user.email),
  })
          successCount++;,
  }
      } catch (err) {,
  console.error(`Unexpected error processing profile ${profile.id}:`, err),
  results.push({
          profileId: profile.id),
          status: 'error'),
          error: err instanceof Error ? err.message     : 'Unknown error'),
  })
        errorCount++,
  }
    },
  return Response.json({
      message: 'Email fix process completed'),
    totalProfiles: profilesWithoutEmail.length),
  successCount
      errorCount;,
  results;
    }),
  } catch (error) {
    console.error('Unexpected error in fix-missing-emails:', error),
  return Response.json({
        error: 'Internal server error'),
        details: error instanceof Error ? error.message    : 'Unknown error'),
  }
      { status: 500 },
  )
  },
  }
// Default export for Expo Router,
  export default POST,