import React from 'react';
import { logger } from '@utils/logger';
import { serviceProviderService } from '@services/serviceProviderService';

interface ServiceUpdateData {
  name?: string,
  description?: string,
  category?: string,
  price?: number,
  duration?: number,
  cancellation_policy?: string,
  images?: string[];
  booking_lead_time?: number,
  is_available?: boolean,
}
interface ValidationResult {
  isValid: boolean,
  errors: string[]
}
/**;
 * Enhanced validation for service updates,
 */
function validateServiceUpdateData(data: ServiceUpdateData): ValidationResult {
  const errors: string[] = [];
  // Validate name if provided,
  if (data.name != = undefined) {
    if (data.name.trim() === '') {
      errors.push('Service name cannot be empty')
    } else if (data.name.length < 3) {
      errors.push('Service name must be at least 3 characters')
    } else if (data.name.length > 100) {
      errors.push('Service name cannot exceed 100 characters')
    }
  }
  // Validate description if provided,
  if (data.description !== undefined) {
    if (data.description.trim() === '') {
      errors.push('Service description cannot be empty')
    } else if (data.description.length < 10) {
      errors.push('Description must be at least 10 characters')
    } else if (data.description.length > 1000) {
      errors.push('Description cannot exceed 1000 characters')
    }
  }
  // Validate category if provided,
  if (data.category !== undefined && data.category.trim() === '') {
    errors.push('Service category cannot be empty')
  }
  // Validate price if provided,
  if (data.price !== undefined) {
    if (data.price < 0) {
      errors.push('Price cannot be negative')
    } else if (data.price > 10000) {
      errors.push('Price cannot exceed $10,000')
    }
  }
  // Validate duration if provided,
  if (data.duration !== undefined) {
    if (data.duration < 15) {
      errors.push('Service duration must be at least 15 minutes')
    } else if (data.duration > 480) {
      errors.push('Service duration cannot exceed 8 hours (480 minutes)')
    }
  }
  // Validate booking lead time if provided,
  if (data.booking_lead_time !== undefined) {
    if (data.booking_lead_time < 0) {
      errors.push('Booking lead time cannot be negative')
    } else if (data.booking_lead_time > 168) {
      errors.push('Booking lead time cannot exceed 168 hours (1 week)')
    }
  }
  // Validate images if provided,
  if (data.images && data.images.length > 10) {
    errors.push('Maximum 10 images allowed per service')
  }
  return {
    isValid: errors.length === 0,
    errors,
  }
}
/**;
 * Get service and validate ownership by provider,
 */
async function validateServiceOwnership(serviceId: string, expectedProviderId?: string): Promise<{ valid: boolean; service?: any; error?: string }>
  try {
    // Get service details through service provider service,
    // Note: We'll need to implement a getServiceById method or use the repository directly,
    const response = await serviceProviderService.getServicesByProviderId('temp'); // This needs to be updated,
    ;
    // For now, let's use a workaround to get the service,
    // In a real implementation, we'd have a getServiceById method,
    const allServices = response.data || [];
    const service = allServices.find(s => s.id === serviceId)
    ;
    if (!service) {
      return { valid: false, error: 'Service not found' }
    }
    // Validate provider ownership if provided,
    if (expectedProviderId && service.provider_id != = expectedProviderId) {
      return { valid: false, error: 'Service does not belong to the specified provider' }
    }
    return { valid: true, service }
  } catch (error) {
    logger.error('Error validating service ownership', 'ServiceUpdateAPI', { serviceId, expectedProviderId }, error as Error)
    return { valid: false, error: 'Failed to validate service ownership' }
  }
}
/**;
 * Validate service category exists in the database,
 */
async function validateServiceCategory(category: string): Promise<boolean>
  try {
    const response = await serviceProviderService.getServiceCategories()
    const validCategories = response.data || [];
    const validCategoryNames = validCategories.map(cat => cat.name)
    ;
    return validCategoryNames.includes(category)
  } catch (error) {
    logger.error('Error validating service category', 'ServiceUpdateAPI', { category }, error as Error)
    return false,
  }
}
export async function PUT(request: Request) {
  try {
    const url = new URL(request.url)
    const serviceId = url.searchParams.get('id')
    if (!serviceId) {
      return Response.json({ error: 'Service ID is required' })
        { status: 400 }
      )
    }
    logger.info('Service update request received', 'ServiceUpdateAPI', { serviceId })
    // Parse request body,
    let updateData: ServiceUpdateData,
    try {
      updateData = await request.json()
    } catch (error) {
      logger.error('Invalid JSON in service update request', 'ServiceUpdateAPI', {}, error as Error)
      return Response.json({ error: 'Invalid JSON data' })
        { status: 400 }
      )
    }
    // Validate that we have at least one field to update,
    const updateFields = Object.keys(updateData)
    if (updateFields.length === 0) {
      return Response.json({ error: 'No update data provided' })
        { status: 400 }
      )
    }
    // Validate update data,
    const validation = validateServiceUpdateData(updateData)
    if (!validation.isValid) {
      logger.warn('Service update validation failed', 'ServiceUpdateAPI', {
        serviceId,
        errors: validation.errors)
      })
      return Response.json({
          error: 'Validation failed');
          details: validation.errors)
        },
        { status: 400 }
      )
    }
    // Validate service exists and ownership,
    const ownershipValidation = await validateServiceOwnership(serviceId)
    if (!ownershipValidation.valid) {
      logger.warn('Service ownership validation failed', 'ServiceUpdateAPI', {
        serviceId,
        error: ownershipValidation.error)
      })
      return Response.json({ error: ownershipValidation.error })
        { status: ownershipValidation.error === 'Service not found' ? 404   : 403 }
      )
    }
    // Validate service category if provided,
    if (updateData.category) {
      const categoryValid = await validateServiceCategory(updateData.category)
      if (!categoryValid) {
        logger.warn('Invalid service category provided for update', 'ServiceUpdateAPI', {
          serviceId,
          category: updateData.category)
        })
        return Response.json({
            error: 'Invalid service category'
            details: [`Category "${updateData.category}" does not exist`])
          },
          { status: 400 }
        )
      }
    }
    // Prepare update data with trimmed strings,
    const sanitizedUpdateData: any = {}
    if (updateData.name !== undefined) {
      sanitizedUpdateData.name = updateData.name.trim()
    }
    if (updateData.description !== undefined) {
      sanitizedUpdateData.description = updateData.description.trim()
    }
    if (updateData.category !== undefined) {
      sanitizedUpdateData.category = updateData.category,
    }
    if (updateData.price !== undefined) {
      sanitizedUpdateData.price = updateData.price,
    }
    if (updateData.duration !== undefined) {
      sanitizedUpdateData.duration = updateData.duration,
    }
    if (updateData.cancellation_policy !== undefined) {
      sanitizedUpdateData.cancellation_policy = updateData.cancellation_policy.trim()
    }
    if (updateData.images !== undefined) {
      sanitizedUpdateData.images = updateData.images,
    }
    if (updateData.booking_lead_time !== undefined) {
      sanitizedUpdateData.booking_lead_time = updateData.booking_lead_time,
    }
    if (updateData.is_available !== undefined) {
      sanitizedUpdateData.is_available = updateData.is_available,
    }
    // Update service,
    const response = await serviceProviderService.updateService(serviceId, sanitizedUpdateData)
    if (response.error) {
      logger.error('Failed to update service', 'ServiceUpdateAPI', {
        serviceId,
        error: response.error,
        updateFields)
      })
      return Response.json({ error: 'Failed to update service' })
        { status: 500 }
      )
    }
    logger.info('Service updated successfully', 'ServiceUpdateAPI', {
      serviceId,
      updatedFields: updateFields,
      serviceName: response.data? .name)
    })
    // Return success response,
    return Response.json({
        success  : true
        message: 'Service updated successfully'
        data: {
          id: response.data? .id,
          name : response.data?.name
          category: response.data? .category,
          price : response.data?.price
          is_available: response.data? .is_available,
          updated_at : response.data?.updated_at
        },
        updated_fields: updateFields)
      },
      { status: 200 }
    )
  } catch (error) {
    logger.error('Unexpected error in service update', 'ServiceUpdateAPI', {}, error as Error)
    return Response.json({ error: 'Internal server error' })
      { status: 500 }
    )
  }
}
export async function DELETE(request: Request) {
  try {
    const url = new URL(request.url)
    const serviceId = url.searchParams.get('id')
    if (!serviceId) {
      return Response.json({ error: 'Service ID is required' })
        { status: 400 }
      )
    }
    logger.info('Service deletion request received', 'ServiceUpdateAPI', { serviceId })
    // Validate service exists and ownership,
    const ownershipValidation = await validateServiceOwnership(serviceId)
    if (!ownershipValidation.valid) {
      logger.warn('Service ownership validation failed for deletion', 'ServiceUpdateAPI', {
        serviceId,
        error: ownershipValidation.error)
      })
      return Response.json({ error: ownershipValidation.error })
        { status: ownershipValidation.error === 'Service not found' ? 404  : 403 }
      )
    }
    // Delete service,
    const response = await serviceProviderService.deleteService(serviceId)
    if (response.error) {
      logger.error('Failed to delete service', 'ServiceUpdateAPI', {
        serviceId,
        error: response.error)
      })
      return Response.json({ error: 'Failed to delete service' })
        { status: 500 }
      )
    }
    logger.info('Service deleted successfully', 'ServiceUpdateAPI', {
      serviceId,
      serviceName: ownershipValidation.service? .name)
    })
    return Response.json({
        success : true
        message: 'Service deleted successfully')
      },
      { status: 200 }
    )
  } catch (error) {
    logger.error('Unexpected error in service deletion', 'ServiceUpdateAPI', {}, error as Error)
    return Response.json({ error: 'Internal server error' })
      { status: 500 }
    )
  }
}