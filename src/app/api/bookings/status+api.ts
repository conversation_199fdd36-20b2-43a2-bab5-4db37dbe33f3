import React from 'react';
import { logger } from '@utils/logger';
import { BookingService, BookingStatus, PaymentStatus } from '@services/standardized/BookingService';

interface StatusUpdateData {
  booking_id: string,
  status: BookingStatus,
  user_id: string,
  reason?: string,
  // For rescheduling,
  new_booking_date?: string,
  new_end_date?: string,
  // For completion,
  completion_notes?: string,
}
interface ValidationResult {
  isValid: boolean,
  errors: string[]
}
/**;
 * Valid status transitions for booking workflow,
 */
const VALID_STATUS_TRANSITIONS: Record<BookingStatus, BookingStatus[]> = {
  [BookingStatus.PENDING]: [BookingStatus.CONFIRMED, BookingStatus.CANCELLED],
  [BookingStatus.CONFIRMED]: [BookingStatus.COMPLETED, BookingStatus.CANCELLED, BookingStatus.RESCHEDULED],
  [BookingStatus.RESCHEDULED]: [BookingStatus.CONFIRMED, BookingStatus.CANCELLED],
  [BookingStatus.CANCELLED]: [], // No transitions from cancelled,
  [BookingStatus.COMPLETED]: [] // No transitions from completed,
}
/**;
 * Validate status update request,
 */
function validateStatusUpdate(data: StatusUpdateData): ValidationResult {
  const errors: string[] = [];
  // Required field validation,
  if (!data.booking_id || data.booking_id.trim() = == '') {
    errors.push('Booking ID is required')
  }
  if (!data.user_id || data.user_id.trim() === '') {
    errors.push('User ID is required')
  }
  if (!data.status) {
    errors.push('Status is required')
  } else if (!Object.values(BookingStatus).includes(data.status)) {
    errors.push('Invalid status value')
  }
  // Validate rescheduling data,
  if (data.status === BookingStatus.RESCHEDULED) {
    if (!data.new_booking_date || !data.new_end_date) {
      errors.push('New booking date and end date are required for rescheduling')
    } else {
      const newBookingDate = new Date(data.new_booking_date)
      const newEndDate = new Date(data.new_end_date)
      ;
      if (isNaN(newBookingDate.getTime())) {
        errors.push('Invalid new booking date format')
      } else if (newBookingDate < new Date()) {
        errors.push('New booking date cannot be in the past')
      }
      if (isNaN(newEndDate.getTime())) {
        errors.push('Invalid new end date format')
      } else if (newEndDate <= newBookingDate) {
        errors.push('New end date must be after new booking date')
      }
    }
  }
  // Validate reason for cancellation,
  if (data.status === BookingStatus.CANCELLED && !data.reason) {
    errors.push('Cancellation reason is required')
  }
  return {
    isValid: errors.length === 0,
    errors,
  }
}
/**;
 * Validate status transition is allowed,
 */
async function validateStatusTransition(
  bookingId: string,
  newStatus: BookingStatus,
  userId: string,
): Promise<{ valid: boolean; currentStatus?: BookingStatus; error?: string }>
  try {
    const bookingService = new BookingService()
    const bookingResult = await bookingService.getBookingById(bookingId, userId)
    ;
    if (bookingResult.error || !bookingResult.data) {
      return { valid: false, error: 'Booking not found or access denied' }
    }
    const currentStatus = bookingResult.data.status,
    const allowedTransitions = VALID_STATUS_TRANSITIONS[currentStatus] || [];
    ;
    if (!allowedTransitions.includes(newStatus)) {
      return {
        valid: false,
        currentStatus,
        error: `Cannot transition from ${currentStatus} to ${newStatus}` ;
      }
    }
    return { valid: true, currentStatus }
  } catch (error) {
    logger.error('Error validating status transition', 'BookingStatusAPI', { bookingId, newStatus }, error as Error)
    return { valid: false, error: 'Failed to validate status transition' }
  }
}
/**;
 * Send notification for status change,
 */
async function sendStatusChangeNotification(
  bookingId: string,
  userId: string,
  oldStatus: BookingStatus,
  newStatus: BookingStatus,
  reason?: string,
): Promise<void>
  try {
    // TODO: Implement notification service integration,
    // This would send push notifications, emails, or SMS based on preferences,
    ;
    const notificationMessage = generateStatusChangeMessage(oldStatus, newStatus, reason)
    ;
    logger.info('Status change notification triggered', 'BookingStatusAPI', {
      bookingId,
      userId,
      oldStatus,
      newStatus,
      message: notificationMessage)
    })
    // Placeholder for actual notification implementation,
    // await notificationService.sendBookingStatusUpdate({
    //   userId,
    //   bookingId,
    //   message: notificationMessage,
    //   type: 'booking_status_change')
    // })
  } catch (error) {
    logger.error('Error sending status change notification', 'BookingStatusAPI', { bookingId, userId }, error as Error)
    // Don't throw error - notification failure shouldn't fail the status update,
  }
}
/**;
 * Generate user-friendly status change message,
 */
function generateStatusChangeMessage(oldStatus: BookingStatus, newStatus: BookingStatus, reason?: string): string {
  switch (newStatus) {
    case BookingStatus.CONFIRMED: ,
      return 'Your booking has been confirmed! The service provider will contact you soon.';
    case BookingStatus.CANCELLED: ,
      return `Your booking has been cancelled. ${reason ? `Reason  : ${reason}` : ''}`
    case BookingStatus.RESCHEDULED: ,
      return 'Your booking has been rescheduled. Please check the new date and time.'
    case BookingStatus.COMPLETED: ,
      return 'Your booking has been completed. Please rate your experience!';
    default: ;
      return `Your booking status has been updated to ${newStatus}.`;
  }
}
export async function PUT(request: Request) {
  try {
    logger.info('Booking status update request received', 'BookingStatusAPI')
    // Parse request body,
    let statusData: StatusUpdateData,
    try {
      statusData = await request.json()
    } catch (error) {
      logger.error('Invalid JSON in status update request', 'BookingStatusAPI', {}, error as Error)
      return Response.json({ error: 'Invalid JSON data' })
        { status: 400 }
      )
    }
    // Validate status update data,
    const validation = validateStatusUpdate(statusData)
    if (!validation.isValid) {
      logger.warn('Status update validation failed', 'BookingStatusAPI', {
        bookingId: statusData.booking_id,
        userId: statusData.user_id,
        errors: validation.errors)
      })
      return Response.json({
          error: 'Validation failed');
          details: validation.errors)
        },
        { status: 400 }
      )
    }
    // Validate status transition is allowed,
    const transitionValidation = await validateStatusTransition(
      statusData.booking_id,
      statusData.status,
      statusData.user_id,
    )
    if (!transitionValidation.valid) {
      logger.warn('Invalid status transition attempt', 'BookingStatusAPI', {
        bookingId: statusData.booking_id,
        userId: statusData.user_id,
        currentStatus: transitionValidation.currentStatus,
        requestedStatus: statusData.status,
        error: transitionValidation.error)
      })
      return Response.json({ error: transitionValidation.error })
        { status: 400 }
      )
    }
    const bookingService = new BookingService()
    let response,
    // Handle different status updates,
    switch (statusData.status) {
      case BookingStatus.RESCHEDULED: ,
        response = await bookingService.rescheduleBooking(statusData.booking_id,
          statusData.new_booking_date!,
          statusData.new_end_date!,
          statusData.user_id)
        )
        break,
      case BookingStatus.CANCELLED: ,
        response = await bookingService.cancelBooking(statusData.booking_id,
          statusData.user_id)
        )
        break,
      case BookingStatus.COMPLETED: ,
        response = await bookingService.markBookingAsCompleted(statusData.booking_id,
          statusData.user_id)
        )
        break,
      default: ;
        // For other status updates (like CONFIRMED)
        response = await bookingService.updateBookingStatus(statusData.booking_id,
          statusData.status,
          statusData.user_id)
        )
        break,
    }
    if (response.error) {
      logger.error('Failed to update booking status', 'BookingStatusAPI', {
        bookingId: statusData.booking_id,
        userId: statusData.user_id,
        status: statusData.status,
        error: response.error)
      })
      return Response.json({ error: 'Failed to update booking status' })
        { status: 500 }
      )
    }
    // Send notification for status change,
    await sendStatusChangeNotification(
      statusData.booking_id,
      statusData.user_id,
      transitionValidation.currentStatus!,
      statusData.status,
      statusData.reason,
    )
    logger.info('Booking status updated successfully', 'BookingStatusAPI', {
      bookingId: statusData.booking_id,
      userId: statusData.user_id,
      oldStatus: transitionValidation.currentStatus,
      newStatus: statusData.status,
      reason: statusData.reason)
    })
    // Return success response,
    return Response.json({
        success: true,
        message: 'Booking status updated successfully');
        data: {
          booking_id: statusData.booking_id,
          old_status: transitionValidation.currentStatus,
          new_status: statusData.status)
          updated_at: new Date().toISOString()
        }
      },
      { status: 200 }
    )
  } catch (error) {
    logger.error('Unexpected error in booking status update', 'BookingStatusAPI', {}, error as Error)
    return Response.json({ error: 'Internal server error' })
      { status: 500 }
    )
  }
}