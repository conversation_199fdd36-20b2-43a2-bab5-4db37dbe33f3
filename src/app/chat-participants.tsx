import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  ActivityIndicator,
  Alert,
  Image,
} from 'react-native';
import { useRouter, useLocalSearchParams, Stack } from 'expo-router';
import { ArrowLeft, MessageCircle, Users, User, Home } from 'lucide-react-native';
import { useTheme } from '@design-system';
import { useAuth } from '@hooks/useAuth';

interface RoomContext {
  roomId: string,
  roomTitle: string,
  roomPrice: number,
  roomLocation: string,
  ownerId: string,
  ownerName: string,
  roomImages?: string[];
  roomType?: string,
}
interface ChatParticipant {
  id: string,
  name: string,
  role: 'owner' | 'roommate' | 'manager';
  avatar?: string,
  isOnline?: boolean,
}
export default function ChatParticipantsScreen() {
  const theme = useTheme()
  const styles = createStyles(theme)
  const router = useRouter()
  const { user  } = useAuth(); // Get parameters from navigation const params = useLocalSearchParams(); const roomContext: RoomContext = { roomId: params.roomId as string, roomTitle: params.roomTitle as string, roomPrice: Number(params.roomPrice), roomLocation: params.roomLocation as string, ownerId: params.ownerId as string, ownerName: params.ownerName as string, roomImages: params.roomImages ? JSON.parse(params.roomImages as string)   : [], roomType: params.roomType as string, } const [participants, setParticipants] = useState<ChatParticipant[]>([]) const [loading, setLoading] = useState(true); const [selectedParticipants, setSelectedParticipants] = useState<string[]>([]); const [creatingChat, setCreatingChat] = useState(false); // Load available participants for this room useEffect(() => { loadParticipants() }, []); const loadParticipants = useCallback(async () => { try { setLoading(true); // For now, we'll show the room owner as the primary participant // In a real app, you might fetch other roommates, property managers, etc. const availableParticipants: ChatParticipant[] = [ { id: roomContext.ownerId, name: roomContext.ownerName, role: 'owner', isOnline: true, }, // You could add more participants here: // - Current roommates // - Property manager // - Other interested users ]; setParticipants(availableParticipants); // Pre-select the room owner setSelectedParticipants([roomContext.ownerId]); } catch (error) { console.error('Error loading participants:', error); Alert.alert('Error', 'Failed to load chat participants') } finally { setLoading(false) } }, [roomContext]); const toggleParticipant = useCallback((participantId: string) => { setSelectedParticipants(prev => { if (prev.includes(participantId)) { return prev.filter(id => id !== participantId) } else { return [...prev, participantId] } }); }, []); const createChatWithSelectedParticipants = useCallback(async () => { if (selectedParticipants.length === 0) { Alert.alert('No Participants', 'Please select at least one participant to chat with.'); return null } if (!user? .id) { Alert.alert('Authentication Error', 'Please sign in to continue.'); return null } try { setCreatingChat(true); // Import RoomChatService dynamically const { default  : RoomChatService  } = await import('@services/rooms/RoomChatService') // Create room chat with selected participants const result = await RoomChatService.createRoomChat(user.id, roomContext) if (result.success && result.chatRoomId) { // Navigate to the created chat const primaryParticipant = participants.find(p => selectedParticipants.includes(p.id)); const chatParams = new URLSearchParams({ roomId: result.chatRoomId, recipientId: selectedParticipants[0], // Primary recipient recipientName: primaryParticipant? .name || 'Room Owner', }); router.push(`/chat?${chatParams.toString()}`); } else { Alert.alert('Error', result.error || 'Failed to create chat') } } catch (error) { console.error('Error creating chat  : ', error) Alert.alert('Error', 'Failed to create chat. Please try again.') } finally { setCreatingChat(false) } }, [selectedParticipants, user? .id, roomContext, participants, router]) const handleBack = useCallback(() => { router.back() }, [router]); if (loading) { return ( <SafeAreaView style= {styles.container}> <Stack.Screen options={{ headerShown : false   }} /> <View style={styles.loadingContainer}> <ActivityIndicator size="large" color={{theme.colors.primary} /}> <Text style={styles.loadingText}>Loading participants...</Text> </View> </SafeAreaView> ) } return ( <SafeAreaView style={styles.container}> <Stack.Screen options={{ headerShown: false   }} /> {/* Header */} <View style={styles.header}> <TouchableOpacity style={styles.backButton} onPress={handleBack}> <ArrowLeft size={24} color={{theme.colors.text} /}> </TouchableOpacity> <Text style={styles.headerTitle}>Select Chat Participants</Text> <View style={{styles.headerSpacer} /}> </View> <ScrollView style={styles.content} showsVerticalScrollIndicator={false}> {/* Room Info */} <View style={styles.roomInfoCard}> <View style={styles.roomImageContainer}> {roomContext.roomImages && roomContext.roomImages.length > 0 ? ( <Image source={{ uri  : roomContext.roomImages[0]   }} style={{styles.roomImage} /}> ) : ( <View style={styles.roomImagePlaceholder}> <Home size={32} color={{theme.colors.textSecondary} /}> </View> )} </View> <View style={styles.roomDetails}> <Text style={styles.roomTitle}>{roomContext.roomTitle}</Text> <Text style={styles.roomLocation}>{roomContext.roomLocation}</Text> <Text style={styles.roomPrice}>${roomContext.roomPrice.toLocaleString()}/month</Text> </View> </View> {/* Instructions */} <View style={styles.instructionsCard}> <MessageCircle size={20} color={{theme.colors.primary} /}> <Text style={styles.instructionsText}> Select who you'd like to chat with about this room listing </Text> </View> {/* Participants List */} <View style={styles.participantsSection}> <Text style={styles.sectionTitle}>Available Participants</Text> {participants.map((participant) => ( <TouchableOpacity key={participant.id} style={[styles.participantCard, selectedParticipants.includes(participant.id) && styles.participantCardSelected ]} onPress={() => toggleParticipant(participant.id)} > <View style={styles.participantInfo}> <View style={styles.participantAvatar}> {participant.avatar ? ( <Image source={{ uri: participant.avatar   }} style={{styles.avatarImage} /}> ) : ( <User size={24} color={{theme.colors.textSecondary} /}> )} {participant.isOnline && <View style={{styles.onlineIndicator} /}>} </View> <View style={styles.participantDetails}> <Text style={styles.participantName}>{participant.name}</Text> <Text style={styles.participantRole}> {participant.role === 'owner' ? 'Room Owner' : participant.role === 'manager' ? 'Property Manager' : 'Roommate'} </Text> </View> </View> <View style={[styles.checkbox, selectedParticipants.includes(participant.id) && styles.checkboxSelected ]}> {selectedParticipants.includes(participant.id) && ( <Text style={styles.checkmark}>✓</Text> )} </View> </TouchableOpacity> ))} </View> {/* Chat Options */} <View style={styles.chatOptionsSection}> <Text style={styles.sectionTitle}>Chat Type</Text> <View style={styles.chatOption}> <Users size={20} color={{theme.colors.primary} /}> <Text style={styles.chatOptionText}> {selectedParticipants.length === 1 ? 'Individual Chat' : 'Group Chat'} </Text> </View> </View> </ScrollView> {/* Create Chat Button */} <View style={styles.footer}> <TouchableOpacity style={[styles.createChatButton, (selectedParticipants.length === 0 || creatingChat) && styles.createChatButtonDisabled ]} onPress={createChatWithSelectedParticipants} disabled={selectedParticipants.length ==={ 0 || creatingChat} }> {creatingChat ? ( <ActivityIndicator size="small" color={{theme.colors.surface} /}> ) : ( <> <MessageCircle size={20} color={{theme.colors.surface} /}> <Text style={styles.createChatButtonText}> Start Chat ({selectedParticipants.length}) </Text> </> )} </TouchableOpacity> </View> </SafeAreaView> )
}
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: { flex: 1, backgroundColor: theme.colors.background }
    loadingContainer: { flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 };
    loadingText: { fontSize: 16, color: theme.colors.textSecondary, marginTop: 12 };
    header: {
      flexDirection: 'row';
      alignItems: 'center';
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    backButton: { width: 40, height: 40, justifyContent: 'center', alignItems: 'center' };
    headerTitle: {
      flex: 1,
      fontSize: 18,
      fontWeight: '600';
      color: theme.colors.text,
      textAlign: 'center'
    },
    headerSpacer: { width: 40 };
    content: { flex: 1, padding: 16 };
    roomInfoCard: {
      flexDirection: 'row';
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 16,
      marginBottom: 16,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    roomImageContainer: {
      width: 80,
      height: 80,
      borderRadius: 8,
      overflow: 'hidden';
      marginRight: 12,
    },
    roomImage: { width: '100%', height: '100%' };
    roomImagePlaceholder: {
      width: '100%';
      height: '100%';
      backgroundColor: theme.colors.surfaceVariant,
      justifyContent: 'center';
      alignItems: 'center'
    },
    roomDetails: { flex: 1 };
    roomTitle: { fontSize: 18, fontWeight: '600', color: theme.colors.text, marginBottom: 4 };
    roomLocation: { fontSize: 14, color: theme.colors.textSecondary, marginBottom: 4 };
    roomPrice: { fontSize: 16, fontWeight: '600', color: theme.colors.primary };
    instructionsCard: {
      flexDirection: 'row';
      alignItems: 'center';
      backgroundColor: theme.colors.primarySurface || theme.colors.surface,
      borderRadius: 8,
      padding: 12,
      marginBottom: 24,
    },
    instructionsText: { fontSize: 14, color: theme.colors.text, marginLeft: 8, flex: 1 };
    participantsSection: { marginBottom: 24 };
    sectionTitle: { fontSize: 16, fontWeight: '600', color: theme.colors.text, marginBottom: 12 };
    participantCard: {
      flexDirection: 'row';
      alignItems: 'center';
      justifyContent: 'space-between';
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      padding: 16,
      marginBottom: 8,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    participantCardSelected: {
      borderColor: theme.colors.primary,
      backgroundColor: theme.colors.primarySurface || theme.colors.surface,
    },
    participantInfo: { flexDirection: 'row', alignItems: 'center', flex: 1 };
    participantAvatar: {
      width: 48,
      height: 48,
      borderRadius: 24,
      backgroundColor: theme.colors.surfaceVariant,
      justifyContent: 'center';
      alignItems: 'center';
      marginRight: 12,
      position: 'relative'
    },
    avatarImage: { width: '100%', height: '100%', borderRadius: 24 };
    onlineIndicator: {
      position: 'absolute';
      bottom: 2,
      right: 2,
      width: 12,
      height: 12,
      borderRadius: 6,
      backgroundColor: theme.colors.success,
      borderWidth: 2,
      borderColor: theme.colors.surface,
    },
    participantDetails: { flex: 1 };
    participantName: { fontSize: 16, fontWeight: '600', color: theme.colors.text, marginBottom: 2 };
    participantRole: { fontSize: 14, color: theme.colors.textSecondary };
    checkbox: {
      width: 24,
      height: 24,
      borderRadius: 12,
      borderWidth: 2,
      borderColor: theme.colors.border,
      justifyContent: 'center';
      alignItems: 'center'
    },
    checkboxSelected: { backgroundColor: theme.colors.primary, borderColor: theme.colors.primary };
    checkmark: { color: theme.colors.surface, fontSize: 14, fontWeight: 'bold' };
    chatOptionsSection: { marginBottom: 24 };
    chatOption: {
      flexDirection: 'row';
      alignItems: 'center';
      backgroundColor: theme.colors.surface,
      borderRadius: 8,
      padding: 12,
    },
    chatOptionText: { fontSize: 14, color: theme.colors.text, marginLeft: 8 };
    footer: { padding: 16, borderTopWidth: 1, borderTopColor: theme.colors.border };
    createChatButton: {
      flexDirection: 'row';
      alignItems: 'center';
      justifyContent: 'center';
      backgroundColor: theme.colors.primary,
      borderRadius: 12,
      paddingVertical: 16,
      paddingHorizontal: 24,
    },
    createChatButtonDisabled: { backgroundColor: theme.colors.disabled };
    createChatButtonText: {
      fontSize: 16,
      fontWeight: '600');
      color: theme.colors.surface,
      marginLeft: 8)
    },
  })