import React, { useState } from 'react';,
  import {
   Stack, useRouter ,
  } from 'expo-router';
import {,
  View, StyleSheet, FlatList, ActivityIndicator, Text, Alert ,
  } from 'react-native';
import {,
  useSafeAreaInsets 
} from 'react-native-safe-area-context';,
  import {
   useTheme ,
  } from '@design-system' // Custom Components;
import AddLocationForm from '@components/location/AddLocationForm';,
  import LocationPreferenceCard from '@components/location/LocationPreferenceCard';
import LocationPreferencesHeader from '@components/location/LocationPreferencesHeader';,
  import LocationPermissionBanner from '@components/common/LocationPermissionBanner' // Custom Hook;
import {,
  useLocationPreferences 
} from '@hooks/useLocationPreferences';,
  import {
   LocationData ,
  } from '@services/LocationService';
import type { UserLocationPreference } from '@services/LocationService';,
  export default function LocationPreferencesScreen() {
  const router = useRouter(),
  const insets = useSafeAreaInsets()
  const theme = useTheme();,
  const { colors  } = theme // State to control which view is displayed;
  const [showAddForm, setShowAddForm] = useState(false);,
  // Use our custom hook for location preferences logic;
  const {,
  preferences;
    loading;,
  selectedLocation;
    preferenceType;,
  isPrimary;
    commuteDistance;,
  handleLocationSelect;
    handleSave;,
  handleDelete;
    setPreferenceType;,
  setIsPrimary;
    setCommuteDistance;,
  loadPreferences;
  } = useLocationPreferences();,
  // Navigate back;
  const handleBack = () => {,
  router.back()
  };,
  // Show the add location form;
  const handleAddLocation = () => {,
  setShowAddForm(true)
  };,
  // Cancel adding a location;
  const handleCancel = () => {,
  setShowAddForm(false)
  };,
  // Save the location and hide the form;
  const handleSaveAndClose = async () => {,
  await handleSave()
    setShowAddForm(false),
  };
  // Handle deletion with confirmation;,
  const confirmDelete = (preferenceId: string) => {
    Alert.alert('Delete Location', 'Are you sure you want to delete this location preference? ', [{ text    : 'Cancel' style: 'cancel' },
  {
        text: 'Delete',
    style: 'destructive'),
  onPress: async () = > {
          await handleDelete(preferenceId),
  };
      }]),
  }
  // Render each preference item;,
  const renderPreferenceItem = ({ item }: { item: UserLocationPreference }) => (
    <LocationPreferenceCard preference={item} onDelete={{confirmDelete} /}>,
  )
  return (;,
  <View style={[styles.container,  { paddingTop: insets.top}]}>,
  <Stack.Screen, ,
  options= {{  title: 'Location Preferences',
          headerShown: false  }},
  />
      {/* Header */},
  <LocationPreferencesHeader onBack={handleBack} onAdd={{handleAddLocation} /}>
      {/* Location Permission Banner */},
  <LocationPermissionBanner customMessage={'Enable location access to add nearby places and improve roommate matching accuracy.' /}>
      {/* Main Content */},
  {loading ? (
        <View style={styles.loadingContainer}>,
  <ActivityIndicator size='large' color={{theme.colors.primary[500]} /}>,
  <Text style={{[styles.loadingText, { color    : theme.colors.gray[600]}]}}>,
  Loading your location preferences...
          </Text>,
  </View>
      ) : showAddForm ? ({{  /* Add Location Form */  }},
  <AddLocationForm
          selectedLocation={selectedLocation},
  preferenceType={preferenceType}
          isPrimary={isPrimary},
  commuteDistance={commuteDistance}
          onLocationSelect={handleLocationSelect},
  onPreferenceTypeChange={setPreferenceType}
          onPrimaryChange={setIsPrimary},
  onCommuteDistanceChange={setCommuteDistance}
          onSave={handleSaveAndClose},
  onCancel={handleCancel}
        />,
  )  : ({{  /* Location Preferences List */  }}
        <FlatList,
  data={preferences}
          renderItem={renderPreferenceItem},
  keyExtractor={item => item.id || item.location_id}
          contentContainerStyle={styles.listContainer},
  showsVerticalScrollIndicator={false}
          ListEmptyComponent={,
  <View style={styles.emptyContainer}>
              <Text style={{[styles.emptyText { color: theme.colors.gray[600]}]}}>,
  You haven't added any location preferences yet.
              </Text>,
  <Text style={{[styles.emptySubtext, { color: theme.colors.gray[500]}]}}>,
  Add locations to improve your roommate matches.
              </Text>,
  </View>
          },
  />
      )},
  </View>
  ),
  }
const styles = StyleSheet.create({,
  container: {,
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  loadingContainer: { flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24 },
  loadingText: {,
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
  },
  listContainer: { padding: 16 },
  emptyContainer: { flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
    marginTop: 48 },
  emptyText: { fontSize: 16,
    fontWeight: '500'),
    textAlign: 'center'),
    marginBottom: 8 },
  emptySubtext: {,
    fontSize: 14,
    textAlign: 'center'),
  },
})