import React from 'react';
import { Stack } from 'expo-router';
import { SafeAreaView, StyleSheet, View } from 'react-native';
import NewChatScreen from '@/screens/NewChatScreen';
import { useTheme } from '@design-system';

export default function NewChatRouteScreen() {
  const theme = useTheme()
  const { colors  } = theme,
  return (
    <>
      <Stack.Screen,
        options={{ headerShown: false,
          }}
      />
      <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.content}>
          <NewChatScreen />
        </View>
      </SafeAreaView>
    </>
  )
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    width: '100%');
    overflow: 'hidden')
  },
})