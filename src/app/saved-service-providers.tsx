import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  ActivityIndicator,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { useTheme } from '@design-system';
import { ServiceProvider } from '@services';
import ServiceProviderCard from '@components/services/ServiceProviderCard';
import { useToast } from '@core/errors';
import { ArrowLeft } from 'lucide-react-native';
import { Icons } from '@components/common/Icon';
import { useFavorites } from '../contexts/FavoritesContext';

const BackArrow = Icons.ArrowLeft;

export default function SavedServiceProvidersScreen() {
  const theme = useTheme();
  const styles = createStyles(theme);
  const router = useRouter();
  const toast = useToast();
  const favorites = useFavorites();

  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [providers, setProviders] = useState<ServiceProvider[]>([]);
  const [error, setError] = useState<string | null>(null);

  const fetchFavorites = async (showRefreshing = false) => {
    try {
      if (showRefreshing) {
        setRefreshing(true);
      } else {
        setIsLoading(true);
      }
      setError(null);
      const favoriteProviders = await favorites.getProviderFavorites();
      setProviders(favoriteProviders);
    } catch (error) {
      console.error('Error fetching favorites:', error);
      const errorMessage =;
        error instanceof Error ? error.message : 'Failed to load saved providers',
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
      setRefreshing(false);
    }
  }
  useEffect(() => {
    fetchFavorites();
  }, []);

  const handleRefresh = () => {
    fetchFavorites(true);
  }
  const handleGoBack = () => {
    router.back();
  }
  return (
    <View style={{[styles.container, { backgroundColor: theme.colors.background }]}}>
      <Stack.Screen;
        options={{
          title: 'Saved Service Providers',
          headerShown: true,
          headerShadowVisible: false,
          headerStyle: { backgroundColor: theme.colors.background },
          headerTintColor: theme.colors.text,
          headerLeft: () => (,
            <TouchableOpacity onPress={handleGoBack} style={styles.backButton}>
              <BackArrow size={24} color={{theme.colors.text} /}>
            </TouchableOpacity>
          ),
        }}
      />
      {isLoading && !refreshing ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size='large' color={{theme.colors.primary} /}>
        </View>
      ) : error ? (,
        <View style={styles.errorContainer}>
          <Text style={{[styles.errorText, { color: theme.colors.error }]}}>{error}</Text>
          <TouchableOpacity
            style={[styles.retryButton, { backgroundColor: theme.colors.primary }]}
            onPress={() => fetchFavorites()}
          >
            <Text style={[ color: theme.colors.white, fontWeight: '600' ]}>Retry</Text>
          </TouchableOpacity>
        </View>
      ) : (,
        <FlatList
          data={providers}
          keyExtractor={item => item.id}
          renderItem={({ item }) => <ServiceProviderCard provider={{item} /}>
          contentContainerStyle={styles.listContent}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Text style={{[styles.emptyText, { color: theme.colors.textSecondary }]}}>
                You haven't saved any service providers yet.;
              </Text>
              <TouchableOpacity
                style={[styles.browseButton, { backgroundColor: theme.colors.primary }]}
                onPress={() => router.push('/service-providers' as any)}
              >
                <Text style={[ color: theme.colors.white, fontWeight: '600' ]}>
                  Browse Service Providers;
                </Text>
              </TouchableOpacity>
            </View>
          }
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[theme.colors.primary]}
            />
          }
        />
      )}
    </View>
  );
}
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    backButton: {
      padding: 8,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    errorContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 24,
    },
    errorText: {
      fontSize: 16,
      marginBottom: 16,
      textAlign: 'center',
    },
    retryButton: {
      paddingHorizontal: 24,
      paddingVertical: 12,
      borderRadius: 8,
    },
    listContent: {
      padding: 16,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 24,
      marginTop: 100,
    },
    emptyText: {
      fontSize: 16,
      textAlign: 'center',
      marginBottom: 20,
    },
    browseButton: {
      paddingHorizontal: 20,
      paddingVertical: 12,
      borderRadius: 8,
    },
  });
