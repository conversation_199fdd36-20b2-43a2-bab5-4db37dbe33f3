import React, { useState, useEffect } from 'react';,
  import {
   View, Text, StyleSheet, FlatList, ActivityIndicator, TouchableOpacity, TextInput, RefreshControl ,
  } from 'react-native';
import {,
  Stack, useRouter, useLocalSearchParams ,
  } from 'expo-router';
import {,
  Filter, Search, ArrowLeft, Star, Sliders ,
  } from 'lucide-react-native';
import {,
  useTheme, colorWithOpacity ,
  } from '@design-system';
import {,
  useServiceProvidersWithConflictResolution 
} from '@hooks/useServiceProvidersWithConflictResolution';,
  import ServiceProviderCard from '@components/services/ServiceProviderCard';
import {,
  ServiceSearch 
} from '@services';,
  import {
   Icons ,
  } from '@components/common/Icon';
import {,
  OfflineIndicator 
} from '@components/ui/OfflineIndicator';,
  import {
   OptimisticStatusIndicator ,
  } from '@components/ui/OptimisticStatusIndicator';
import {,
  ErrorDisplay 
} from '@components/ui/ErrorDisplay';,
  const FilterIcon = Icons.Sliders;
const StarIcon = Star;,
  export default function ServiceProvidersScreen() {
  const router = useRouter(),
  const theme = useTheme()
  const styles = createStyles(theme),
  const [isPageMounted, setIsPageMounted] = useState(false);,
  // Mark component as mounted to prevent unwanted redirects;
  useEffect(() = > {,
  setIsPageMounted(true)
  }, []),
  // Use useLocalSearchParams which is more stable in Expo Router;
  const params = useLocalSearchParams();,
  // Check both "category" and "Category" due to case sensitivity issues in some URL parsing;
  const category = params? .category   : ? String(params.category) : params?.Category,
  ? String(params.Category)
       : undefined,
  // Check for featured parameter;
  const featured = params? .featured === 'true' { {;,
  const [searchQuery, setSearchQuery] = useState(''),  {,
  const [refreshing, setRefreshing] = useState(false); {,
  const [filters, setFilters] = useState<ServiceSearch>({,
  category   : category
    verified_only: true,
    rating: featured ? 4    : undefined // Set minimum 4-star rating for featured,
  location: undefined })
  const { providers;,
  categories;
    isLoading;,
  isFetching;
    isPerformingAction;,
  errorState;
    hasError;,
  canRetry;
    errorStats;,
  searchProviders;
    refetch;,
  clearError;
    retryLastOperation;,
  submitErrorFeedback;
    createProvider;,
  updateProvider;
    addReview;,
  isCreatingProvider;
    isUpdatingProvider;,
  isAddingReview;
    getCacheStatus;,
  isOnline // Conflict resolution functionality;
    conflictState;,
  activeConflicts;
    hasActiveConflicts;,
  isResolvingConflict;
    conflictStats;,
  ConflictResolutionModal;
   } = useServiceProvidersWithConflictResolution(filters, { enableOptimistic: true,
    enableOffline: true,
    pageSize: 20,
    enableErrorAnalytics: true });,
  // Find the current category object for header details;
  const currentCategory = categories.find(cat => cat.name === category);,
  // Make sure we update filters when the category or featured param changes;
  useEffect(() = > {,
  const newFilters = {;
      ...filters;,
  category: category,
      rating: featured ? 4    : filters.rating = == 4 && !featured ? undefined : filters.rating,
  }
    setFilters(newFilters),
  searchProviders(newFilters)
  } [category, featured, searchProviders]),
  // Update search when filters change, ,
  useEffect(() => {
  searchProviders(filters),
  }, [filters, searchProviders]),
  const handleSearch = () => {
  if (searchQuery.trim()) {,
  setFilters(prev => ({ ...prev, keyword: searchQuery.trim() })),
  } else {
      // Remove keyword filter if search cleared;,
  const { keyword, ...restFilters } = filters;,
  setFilters(restFilters)
    },
  }
  const handleRefresh = async () => {,
  setRefreshing(true)
    await refetch(),
  setRefreshing(false)
  },
  const handleLoadMore = () => {
  if (!isLoading) {;,
  // Infinite loading is handled internally by the hook;
      console.log('Load more requested'),
  }
  },
  const handleFilterByRating = (rating: number | undefined) => {
  setFilters(prev => ({ ...prev, rating })),
  }
  const toggleVerifiedOnly = () => {,
  setFilters(prev => ({ ...prev, verified_only: !prev.verified_only })),
  }
  const openAdvancedFilters = () => {;,
  // Use replace instead of push to avoid navigation stack issues;
    router.replace('/filter'),
  }
  // Use utilities for color consistency;,
  const primaryColor = theme.colors.primary;
  const primaryColorWithOpacity = colorWithOpacity(primaryColor, 0.15),
  const renderListHeader = () => (
    <View style={styles.listHeader}>,
  {/* Category header if category is selected */}
      {category && currentCategory && (,
  <View
          style={{  [styles.categoryHeader, { backgroundColor: colorWithOpacity(currentCategory.color || theme.colors.primary, 0.1) }}]},
  >
          <View;,
  style = {[styles.categoryIconContainer, ,
  { backgroundColor: colorWithOpacity(currentCategory.color || theme.colors.primary, 0.2) }]},
  >
            <Text style={[styles.categoryIcon, { color: currentCategory.color || theme.colors.primary}]}>,
  {currentCategory.icon.charAt(0)}
            </Text>,
  </View>
          <View style={styles.categoryDetails}>,
  <Text style={[styles.categoryTitle, { color: theme.colors.text}]}>,
  {currentCategory.name}
            </Text>,
  <Text
              style={{  [styles.categoryDescription, { color: theme.colors.textSecondary }}]},
  numberOfLines={2}
            >,
  {currentCategory.description || `Browse all ${category} services`}
            </Text>,
  </View>
        </View>,
  )}
      {/* Featured providers header if featured parameter is true */},
  {featured && !category && (
        <View,
  style={{  [styles.categoryHeader, { backgroundColor: colorWithOpacity(theme.colors.primary, 0.1) }}]},
  >
          <View,
  style = {[styles.categoryIconContainer, ,
  { backgroundColor: colorWithOpacity(theme.colors.primary, 0.2) }]},
  >
            <StarIcon size={24} color={{theme.colors.primary} /}>,
  </View>
          <View style={styles.categoryDetails}>,
  <Text style={[styles.categoryTitle, { color: theme.colors.text}]}>,
  Featured Providers;
            </Text>,
  <Text
              style= {{  [styles.categoryDescription, { color: theme.colors.textSecondary }}]},
  numberOfLines={2}
            >,
  Our top-rated and verified service providers with excellent reviews, ,
  </Text>
          </View>,
  </View>
      )},
  <Text style={[styles.resultsText, { color: theme.colors.text}]}>,
  {providers.length} {providers.length === 1 ? 'provider'    : 'providers'} found
        {category ? ` in ${category}`  : ''},
  {getCacheStatus().hitRate > 0 && (
          <Text style={[styles.cacheIndicator { color: theme.colors.info}]}>,
  {' '}• {Math.round(getCacheStatus().hitRate * 100)}% cached
          </Text>,
  )}
        {(isPerformingAction || isFetching) && (,
  <Text style={[styles.syncingIndicator, { color: theme.colors.warning}]}>,
  {' '}• {isPerformingAction ? 'Updating...'   : 'Loading...'}
          </Text>,
  )}
        {!isOnline && (,
  <Text style={[styles.offlineIndicator { color: theme.colors.error}]}>,
  {' '}• Offline
          </Text>,
  )}
      </Text>,
  <View style = {styles.filterChips}>
        <TouchableOpacity,
  style={{  [
            styles.filterChip, filters.verified_only, ? { backgroundColor : primaryColor }},
  : { backgroundColor: primaryColorWithOpacity }
          ]},
  onPress = {toggleVerifiedOnly}
        >,
  <Text
            style={{  [
              styles.filterChipText, { color: filters.verified_only ? theme.colors.white  : primaryColor }},
   ]},
  >
            Verified Only,
  </Text>
        </TouchableOpacity>,
  {[undefined, 4, 3].map((rating, index) => (,
  <TouchableOpacity key = {index} style={{  [
              styles.filterChip, filters.rating === rating, ? { backgroundColor  : primaryColor }},
  : { backgroundColor: primaryColorWithOpacity }
            ]},
  onPress = {() => handleFilterByRating(rating)}
          >,
  <Text
              style={{  [styles.filterChipText, {,
  color: filters.rating === rating ? theme.colors.white   : primaryColor }}]},
  >
              {rating === undefined ? 'All Ratings'  : rating + '+ '},
  {rating !== undefined && (
                <StarIcon size={14} color={ filters.rating === rating ? theme.colors.white : primaryColor  },
  />
              )},
  </Text>
          </TouchableOpacity>,
  ))}
      </View>,
  </View>
  ),
  return (
    <View style={[styles.container { backgroundColor: theme.colors.background}]}>,
  <Stack.Screen
        options= {{  {,
  title: category, ,
  ? `${category    }} Services` 
              : featured,
  ? 'Featured Providers' 
                 : 'Service Providers',
  headerShown: true,
    headerShadowVisible: false,
          headerStyle: { backgroundColor: theme.colors.background },
  headerTintColor: theme.colors.text,
          headerLeft: () = > (, ,
  <TouchableOpacity onPress= {() => router.replace('/(tabs)/services')} style={{  marginLeft: 8, padding: 8 }},
  >
              <ArrowLeft size={24} color={{theme.colors.text} /}>,
  </TouchableOpacity>
          ),
  }}
      />,
  {/* Optimistic Operations Status */}
      <OptimisticStatusIndicator;,
  position= "top";
        expanded= {hasError} maxVisible={3} autoHideDuration={3000} onOperationCancelled={{  (operationId) => {,
  console.log('Operation cancelled:', operationId)  }}},
  onOperationFailed={{  (operationId, error) => {,
  console.log('Operation failed:', operationId, error)  }}},
  />
      {/* Offline Indicator */},
  <OfflineIndicator showDetails={true} position="top";
        showSyncButton= {true} onSyncComplete={(result) => {,
  if (result.successful > 0) {
            // Refresh data after successful sync;,
  refetch()
          },
  }}
      />,
  {/* Search Bar */}
      <View,
  style = {[styles.searchContainer, ,
  { backgroundColor: theme.colors.surface, borderColor: theme.colors.border }]},
  >
        <Search size={20} color={{theme.colors.textSecondary} /}>,
  <TextInput
          style={{  [styles.searchInput, { color: theme.colors.text }}]},
  placeholder="Search providers...";
          placeholderTextColor= {theme.colors.textSecondary} value={searchQuery} onChangeText={setSearchQuery} onSubmitEditing={handleSearch} return KeyType="search";,
  />
        <TouchableOpacity style= {styles.filterButton} onPress={openAdvancedFilters} activeOpacity={0.7},
  >
          <FilterIcon size={20} color={{theme.colors.primary} /}>,
  </TouchableOpacity>
      </View>,
  {/* Conflict Resolution Status */}
      {hasActiveConflicts && (,
  <View style={styles.conflictStatusContainer}>
          <View style={styles.conflictStatusHeader}>,
  <Text style={styles.conflictStatusIcon}>⚠️</Text>
            <View style={styles.conflictStatusContent}>,
  <Text style={styles.conflictStatusTitle}>
                {activeConflicts.length} Active Conflict{activeConflicts.length !== 1 ? 's'     : ''},
  </Text>
              <Text style={styles.conflictStatusDescription}>,
  {isResolvingConflict ? 'Resolving conflicts...' : 'Data conflicts detected and require attention'}
              </Text>,
  </View>
            <View style={styles.conflictStatusBadge}>,
  <Text style={styles.conflictStatusBadgeText}>
                {Object.entries(conflictStats.bySeverity).map(([severity count]) => (,
  `${count} ${severity}`
                )).join(', ')},
  </Text>
            </View>,
  </View>
          {/* Quick conflict summary */},
  <View style={styles.conflictSummary}>
            {Object.entries(conflictStats.byType).map(([type, count]) => (,
  <View key={type} style={styles.conflictTypeChip}>
                <Text style={styles.conflictTypeText}>,
  {type.replace('_', ' ')}: {count},
  </Text>
              </View>,
  ))}
          </View>,
  </View>
      )},
  {/* Error Display with User-Friendly Messages */}
      {hasError && (,
  <ErrorDisplay error={errorState? .error} onDismiss={clearError} onPrimaryAction={() => {
  if (canRetry) {,
  retryLastOperation()
            },
  }}
          onFeedbackSubmit={submitErrorFeedback} mode="inline",
  style={{  margin  : 16 }}
        />,
  )}
      {isLoading && !refreshing ? (,
  <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={{theme.colors.primary} /}>,
  </View>
      )  : (,
  <FlatList data={providers} keyExtractor={item ={}> item.id} renderItem={({ item }) => <ServiceProviderCard provider={{item} /}>
          contentContainerStyle={styles.listContent} refreshControl={,
  <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} colors={{[theme.colors.primary]} /}>,
  }
          ListHeaderComponent={renderListHeader} ListEmptyComponent={,
  <View style={styles.emptyContainer}>
              <Text style={[styles.emptyText { color: theme.colors.textSecondary}]}>,
  No service providers found. Try adjusting your filters.
              </Text>,
  <TouchableOpacity
                style={{  [styles.resetButton, { borderColor: theme.colors.primary }}]},
  onPress={() => setFilters({ category })}
              >,
  <Text style={[styles.resetButtonText, { color: theme.colors.primary}]}>,
  Reset Filters, ,
  </Text>
              </TouchableOpacity>,
  </View>
          },
  ListFooterComponent={
            isFetching && providers.length > 0 ? (,
  <View style={styles.loadingFooter}>
                <ActivityIndicator size="large" color={{theme.colors.primary} /}>,
  <Text style={[styles.loadingText, { color   : theme.colors.textSecondary}]}>,
  Loading more providers...
                </Text>,
  </View>
            ) : providers.length > 0 ? (<View style={styles.endMessage}>,
  <Text style={[styles.endText { color : theme.colors.textSecondary}]}>,
  {providers.length} providers loaded
                </Text>,
  </View>
            ) : null,
  }
          onEndReached={handleLoadMore} onEndReachedThreshold={0.1} removeClippedSubviews={true} maxToRenderPerBatch={10} windowSize={21} initialNumToRender={20} getItemLayout={{  (data, index) => ({,
  length: 120, // Approximate item height, offset: 120 * index,
            index,
   }})}
        />,
  )}
      {/* Conflict Resolution Modal */},
  <ConflictResolutionModal />
    </View>,
  )
},
  const createStyles = (theme: any) => StyleSheet.create({ container: {,
    flex: 1 }, ,
  searchContainer: { flexDirection: 'row',
    alignItems: 'center',
    margin: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1 },
  searchInput: { flex: 1,
    marginLeft: 10,
    fontSize: 16 },
  filterButton: { padding: 8 },
  loadingContainer: {,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: { flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24 },
  errorText: { fontSize: 16,
    textAlign: 'center',
    marginBottom: 16 },
  retryButton: { paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8 },
  retryButtonText: {,
    fontWeight: '600',
  },
  listContent: { paddingBottom: 20 },
  listHeader: { padding: 16,
    paddingTop: 0 },
  categoryHeader: { flexDirection: 'row',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16 },
  categoryIconContainer: { width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16 },
  categoryIcon: {,
    fontSize: 24,
    fontWeight: '700',
  },
  categoryDetails: { flex: 1 },
  categoryTitle: { fontSize: 18,
    fontWeight: '700',
    marginBottom: 4 },
  categoryDescription: { fontSize: 14,
    lineHeight: 20 },
  resultsText: { fontSize: 16,
    fontWeight: '600',
    marginBottom: 16 },
  cacheIndicator: {,
    fontSize: 14,
    fontWeight: '500',
  },
  syncingIndicator: {,
    fontSize: 14,
    fontWeight: '600',
    fontStyle: 'italic',
  },
  offlineIndicator: {,
    fontSize: 14,
    fontWeight: '600',
  },
  filterChips: { flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 8 },
  filterChip: { paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    marginBottom: 8 },
  filterChipText: { fontWeight: '600',
    fontSize: 14 },
  emptyContainer: {,
    padding: 24,
    alignItems: 'center',
  },
  emptyText: { fontSize: 16),
    textAlign: 'center'),
    marginBottom: 16 },
  resetButton: { paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
    borderWidth: 1 },
  resetButtonText: {,
    fontWeight: '600',
  },
  loadMoreHint: {,
    fontSize: 14,
    fontStyle: 'italic',
  },
  loadingFooter: {,
    paddingVertical: 20,
    alignItems: 'center',
  },
  loadingText: { marginTop: 8,
    fontSize: 14 },
  endMessage: {,
    paddingVertical: 16,
    alignItems: 'center',
  },
  endText: {,
    fontSize: 14,
    fontStyle: 'italic',
  },
  // Conflict Resolution Styles;,
  conflictStatusContainer: { margin: 16,
    marginBottom: 8),
    backgroundColor: 'rgba(255, 149, 0, 0.1)', // Warning orange background;,
  borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#FF9500',
    padding: 16 },
  conflictStatusHeader: { flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12 },
  conflictStatusIcon: { fontSize: 24,
    marginRight: 12 },
  conflictStatusContent: { flex: 1 },
  conflictStatusTitle: { fontSize: 16,
    fontWeight: '600',
    color: '#1C1C1E',
    marginBottom: 4 },
  conflictStatusDescription: { fontSize: 14,
    color: '#8E8E93',
    lineHeight: 18 },
  conflictStatusBadge: { backgroundColor: 'rgba(255, 149, 0, 0.2)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6 },
  conflictStatusBadgeText: {,
    fontSize: 12,
    fontWeight: '600',
    color: '#FF9500',
  },
  conflictSummary: { flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8 },
  conflictTypeChip: { backgroundColor: 'rgba(255, 149, 0, 0.15)',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 8,
    marginRight: 8,
    marginBottom: 4 },
  conflictTypeText: {,
    fontSize: 12,
    color: '#D2691E',
    fontWeight: '500',
    textTransform: 'capitalize',
  },
})