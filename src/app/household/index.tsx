import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  Dimensions,
  Image,
} from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from '@context/AuthContext';
import { useTheme } from '@design-system';
import { useToast } from '@components/ui/Toast';
import { logger } from '@utils/logger';
import {
  Home,
  Users,
  Calendar,
  ListTodo,
  DollarSign,
  MessageSquare,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock,
  Settings,
  Plus,
  ChevronRight,
  MapPin,
  Star,
  Activity,
  BarChart3,
  PieChart,
  Target,
  Award,
  Shield,
  Zap,
  Heart,
  Coffee,
  Wifi,
  Car,
  Utensils,
} from 'lucide-react-native';

const { width  } = Dimensions.get('window')
// Enhanced household data structures,
interface HouseholdMember {
  id: string,
  name: string,
  email: string,
  avatar_url?: string,
  role: 'admin' | 'member';
  move_in_date: string,
  is_verified: boolean,
  trust_score: number,
  personality_type?: string,
  lifestyle_type?: string,
}
interface HouseholdStats {
  total_members: number,
  active_members: number,
  household_health_score: number,
  monthly_expenses: number,
  pending_chores: number,
  active_conflicts: number,
  upcoming_events: number,
  satisfaction_score: number,
  expense_balance: number,
  chore_completion_rate: number,
}
interface HouseholdData {
  id: string,
  name: string,
  address: string,
  description?: string,
  created_at: string,
  lease_end_date?: string,
  rent_amount?: number,
  members: HouseholdMember[];
  stats: HouseholdStats,
  amenities: string[];
  house_rules: string[];
  emergency_contacts: any[]
}
interface QuickAction {
  id: string,
  title: string,
  description: string,
  icon: any,
  route: string,
  priority: 'high' | 'medium' | 'low';
  badge?: number,
  color: string,
}
interface FeatureCard {
  id: string,
  title: string,
  description: string,
  icon: any,
  route: string,
  stats?: {
    value: number | string,
    label: string,
    trend?: 'up' | 'down' | 'stable',
  }
  color: string,
  category: 'management' | 'social' | 'financial' | 'maintenance'
}
export default function EnhancedHouseholdDashboard() {
  const { authState  } = useAuth()
  const theme = useTheme()
  const router = useRouter()
  const { showSuccess, showError, ToastComponent } = useToast()
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [household, setHousehold] = useState<HouseholdData | null>(null)
  const [selectedCategory, setSelectedCategory] = useState<;
    'all' | 'management' | 'social' | 'financial' | 'maintenance'
  >('all')
  useEffect(() = > {
    fetchHouseholdData()
  }, [])
  const fetchHouseholdData = async () => {
    if (!authState.user) return null,
    try {
      setLoading(true)
      // In a real implementation, this would be an actual API call,
      // For now, I'll create comprehensive mock data based on enhanced profiles,
      const mockHouseholdData: HouseholdData = {
        id: 'household-123';
        name: 'Sunset Apartment';
        address: '123 Sunset Boulevard, Apt 4B, San Francisco, CA 94105',
        description: 'Modern 3-bedroom apartment with great city views and friendly roommates';
        created_at: '2023-09-01T00:00:00Z';
        lease_end_date: '2024-09-01T00:00:00Z';
        rent_amount: 3600,
        members: [;
          {
            id: authState.user.id,
            name: 'You';
            email: authState.user.email || '';
            avatar_url: 'https://example.com/avatar1.jpg';
            role: 'admin';
            move_in_date: '2023-09-01';
            is_verified: true,
            trust_score: 85,
            personality_type: 'ENFP';
            lifestyle_type: 'Social Butterfly'
          },
          {
            id: 'member-2';
            name: 'Sarah Johnson';
            email: '<EMAIL>';
            avatar_url: 'https://example.com/avatar2.jpg';
            role: 'member';
            move_in_date: '2023-09-15';
            is_verified: true,
            trust_score: 92,
            personality_type: 'ISFJ';
            lifestyle_type: 'Organized Planner'
          },
          {
            id: 'member-3';
            name: 'Michael Chen';
            email: '<EMAIL>';
            avatar_url: 'https://example.com/avatar3.jpg';
            role: 'member';
            move_in_date: '2023-10-01';
            is_verified: false,
            trust_score: 78,
            personality_type: 'INTJ';
            lifestyle_type: 'Night Owl'
          },
        ],
        stats: {
          total_members: 3,
          active_members: 3,
          household_health_score: 85,
          monthly_expenses: 1200,
          pending_chores: 2,
          active_conflicts: 0,
          upcoming_events: 3,
          satisfaction_score: 4.2,
          expense_balance: -45.5,
          chore_completion_rate: 87,
        },
        amenities: ['wifi', 'parking', 'laundry', 'dishwasher', 'balcony', 'gym'],
        house_rules: [;
          'No smoking',
          'Quiet hours 10pm-8am',
          'Clean up after yourself',
          'Give 24h notice for guests',
        ],
        emergency_contacts: []
      }
      setHousehold(mockHouseholdData)
    } catch (error) {
      logger.error('Error fetching household data',
        'EnhancedHouseholdDashboard.fetchHouseholdData',
        error as Error)
      )
      showError('Failed to load household data')
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }
  const handleRefresh = useCallback(() => {
    setRefreshing(true)
    fetchHouseholdData()
  }, [])
  const navigateToFeature = useCallback(
    (route: string) => {
      try {
        router.push(route as any)
      } catch (error) {
        logger.error('Navigation error', 'EnhancedHouseholdDashboard.navigateToFeature', {
          error: error instanceof Error ? error.message   : String(error)
          route,
        })
        showError('Navigation failed')
      }
    },
    [router, showError]
  )
  if (loading) {
    return (
      <SafeAreaView
        style={{ [styles.container, { backgroundColor: theme.colors.background   }}]}
        edges={['top']}
      >
        <Stack.Screen,
          options={{ {
            title: 'Household Dashboard',
            headerShadowVisible: false,
            headerStyle: { backgroundColor: theme.colors.background   }};
          }}
        />
        <View style= {styles.loadingContainer}>
          <ActivityIndicator size='large' color={{theme.colors.primary} /}>
          <Text style={[styles.loadingText, { color: theme.colors.text }]}>
            Loading household data...;
          </Text>
        </View>
      </SafeAreaView>
    )
  }
  if (!household) {
    return (
      <SafeAreaView
        style= {{ [styles.container, { backgroundColor: theme.colors.background   }}]}
        edges={['top']}
      >
        <Stack.Screen,
          options={{ {
            title: 'Household Dashboard',
            headerShadowVisible: false,
            headerStyle: { backgroundColor: theme.colors.background   }};
          }}
        />
        <View style= {styles.emptyContainer}>
          <Home size={64} color={{theme.colors.textSecondary} /}>
          <Text style={[styles.emptyTitle, { color: theme.colors.text }]}>No Household Found</Text>
          <Text style={[styles.emptyDescription, { color: theme.colors.textSecondary }]}>
            You're not currently part of a household. Join or create one to get started.;
          </Text>
          <TouchableOpacity
            style= {{ [styles.createButton, { backgroundColor: theme.colors.primary   }}]}
            onPress={() => router.push('/household/create' as any)}
          >
            <Plus size={20} color={'white' /}>
            <Text style={styles.createButtonText}>Create Household</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    )
  }
  const quickActions: QuickAction[] = [;
    {
      id: 'add-expense';
      title: 'Split Expense';
      description: 'Add and split a new expense';
      icon: DollarSign,
      route: '/household/expenses/create';
      priority: 'high';
      color: '#f59e0b'
    },
    {
      id: 'assign-chore';
      title: 'Assign Chore';
      description: 'Create a new household task';
      icon: ListTodo,
      route: '/household/chores/create';
      priority: 'medium';
      color: '#10b981'
    },
    {
      id: 'schedule-event';
      title: 'Add Event';
      description: 'Schedule a household event';
      icon: Calendar,
      route: '/household/calendar/create';
      priority: 'medium';
      color: '#6366f1'
    },
    {
      id: 'report-issue';
      title: 'Report Issue';
      description: 'Address a household concern';
      icon: MessageSquare,
      route: '/household/conflict-resolution';
      priority: household.stats.active_conflicts > 0 ? 'high'   : 'low'
      badge: household.stats.active_conflicts,
      color: '#ef4444'
    },
  ]

  const featureCards: FeatureCard[] = [;
    {
      id: 'expenses';
      title: 'Shared Expenses';
      description: 'Track and split household costs';
      icon: DollarSign,
      route: '/household/expenses';
      stats: {
        value: `$${household.stats.monthly_expenses}`;
        label: 'This month';
        trend: 'stable'
      },
      color: '#f59e0b';
      category: 'financial'
    },
    {
      id: 'chores';
      title: 'Chores & Tasks';
      description: 'Manage household responsibilities';
      icon: ListTodo,
      route: '/household/chores';
      stats: {
        value: `${household.stats.chore_completion_rate}%`;
        label: 'Completion rate';
        trend: 'up'
      },
      color: '#10b981';
      category: 'management'
    },
    {
      id: 'calendar';
      title: 'Household Calendar';
      description: 'Schedule shared events and activities';
      icon: Calendar,
      route: '/household/calendar';
      stats: {
        value: household.stats.upcoming_events,
        label: 'Upcoming events';
        trend: 'stable'
      },
      color: '#6366f1';
      category: 'social'
    },
    {
      id: 'conflicts';
      title: 'Conflict Resolution';
      description: 'Address and resolve household issues';
      icon: MessageSquare,
      route: '/household/conflicts';
      stats: {
        value: household.stats.active_conflicts,
        label: 'Active conflicts';
        trend: household.stats.active_conflicts = == 0 ? 'stable'   : 'down'
      },
      color: household.stats.active_conflicts > 0 ? '#ef4444'  : '#10b981'
      category: 'social'
    },
    {
      id: 'maintenance'
      title: 'Maintenance';
      description: 'Track property maintenance and repairs';
      icon: Settings,
      route: '/household/maintenance-analytics';
      stats: {
        value: '2';
        label: 'Pending requests';
        trend: 'stable'
      },
      color: '#8b5cf6';
      category: 'maintenance'
    },
    {
      id: 'reviews';
      title: 'Roommate Reviews';
      description: 'Rate and review your roommates';
      icon: Star,
      route: '/household/reviews';
      stats: {
        value: household.stats.satisfaction_score.toFixed(1)
        label: 'Avg satisfaction';
        trend: 'up'
      },
      color: '#ec4899';
      category: 'social'
    },
    {
      id: 'communication';
      title: 'Communication Hub';
      description: 'Smart messaging and household communication';
      icon: MessageSquare,
      route: '/household/communication';
      stats: {
        value: '24';
        label: 'Messages today';
        trend: 'up'
      },
      color: '#8b5cf6';
      category: 'social'
    },
  ];

  const filteredFeatures =;
    selectedCategory = == 'all';
      ? featureCards,
        : featureCards.filter(card = > card.category === selectedCategory),
  const renderHouseholdHeader = () => (
    <View style={[styles.headerCard, { backgroundColor: theme.colors.surface }]}>
      <View style={styles.headerContent}>
        <View style={styles.householdInfo}>
          <View style={styles.householdTitleRow}>
            <Home size={24} color={{theme.colors.primary} /}>
            <Text style={[styles.householdName, { color: theme.colors.text }]}>
              {household.name}
            </Text>
            <View
              style={{ [
                styles.healthBadge
                {
                  backgroundColor: ,
                    getHealthScoreColor(household.stats.household_health_score) + '20',
                  }},
              ]}
            >
              <Text
                style = {[styles.healthScore,
                  { color: getHealthScoreColor(household.stats.household_health_score) }]}
              >
                {household.stats.household_health_score}%
              </Text>
            </View>
          </View>
          <View style={styles.addressRow}>
            <MapPin size={16} color={{theme.colors.textSecondary} /}>
            <Text style={[styles.householdAddress, { color: theme.colors.textSecondary }]}>
              {household.address}
            </Text>
          </View>
          {household.description && (
            <Text style={[styles.householdDescription, { color: theme.colors.textSecondary }]}>
              {household.description}
            </Text>
          )}
        </View>
        <TouchableOpacity
          style={{ [styles.settingsButton, { backgroundColor: theme.colors.background   }}]}
          onPress={() => router.push('/household/settings' as any)}
          accessibilityLabel='Household settings';
          accessibilityRole= 'button';
        >
          <Settings size= {20} color={{theme.colors.textSecondary} /}>
        </TouchableOpacity>
      </View>
    </View>
  )
  const renderHouseholdStats = () => (
    <View style={[styles.statsCard, { backgroundColor: theme.colors.surface }]}>
      <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Household Overview</Text>
      <View style={styles.statsGrid}>
        <View style={styles.statItem}>
          <View style={[styles.statIcon, { backgroundColor: theme.colors.primary + '20' }]}>
            <Users size={20} color={{theme.colors.primary} /}>
          </View>
          <Text style={[styles.statValue, { color: theme.colors.text }]}>
            {household.stats.total_members}
          </Text>
          <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>Members</Text>
        </View>
        <View style={styles.statItem}>
          <View style={[styles.statIcon, { backgroundColor: '#10b981' + '20' }]}>
            <Activity size={20} color={'#10b981' /}>
          </View>
          <Text style={[styles.statValue, { color: theme.colors.text }]}>
            {household.stats.household_health_score}%;
          </Text>
          <Text style= {[styles.statLabel, { color: theme.colors.textSecondary }]}>
            Health Score,
          </Text>
        </View>
        <View style={styles.statItem}>
          <View style={[styles.statIcon, { backgroundColor: '#f59e0b' + '20' }]}>
            <DollarSign size={20} color={'#f59e0b' /}>
          </View>
          <Text style={[styles.statValue, { color: theme.colors.text }]}>
            ${household.stats.monthly_expenses}
          </Text>
          <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
            Monthly Expenses,
          </Text>
        </View>
        <View style={styles.statItem}>
          <View style={[styles.statIcon, { backgroundColor: '#6366f1' + '20' }]}>
            <Star size={20} color={'#6366f1' /}>
          </View>
          <Text style={[styles.statValue, { color: theme.colors.text }]}>
            {household.stats.satisfaction_score}
          </Text>
          <Text style={[styles.statLabel, { color: theme.colors.textSecondary }]}>
            Satisfaction,
          </Text>
        </View>
      </View>
    </View>
  )
  const renderHouseholdMembers = () => (
    <View style={[styles.membersCard, { backgroundColor: theme.colors.surface }]}>
      <View style={styles.membersHeader}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Household Members</Text>
        <TouchableOpacity
          style={{ [styles.inviteButton, { backgroundColor: theme.colors.primary + '20'   }}]}
          onPress={() => router.push('/household/invite' as any)}
        >
          <Plus size={16} color={{theme.colors.primary} /}>
          <Text style={[styles.inviteText, { color: theme.colors.primary }]}>Invite</Text>
        </TouchableOpacity>
      </View>
      <View style={styles.membersList}>
        {household.members.map((member, index) => (
          <View key={member.id} style={styles.memberItem}>
            <View style={styles.memberAvatar}>
              {member.avatar_url ? (
                <Image source={{ uri  : member.avatar_url   }} style={{styles.memberAvatarImage} /}>
              ) : (
                <View
                  style = {[styles.memberAvatarPlaceholder,
                    { backgroundColor: theme.colors.primary + '20' }]}
                >
                  <Text style={[styles.memberAvatarText, { color: theme.colors.primary }]}>
                    {member.name.charAt(0)}
                  </Text>
                </View>
              )}
              {member.is_verified && (
                <View style={[styles.verifiedBadge, { backgroundColor: '#10b981' }]}>
                  <CheckCircle size={8} color={'white' /}>
                </View>
              )}
            </View>
            <View style={styles.memberInfo}>
              <View style={styles.memberNameRow}>
                <Text style={[styles.memberName, { color: theme.colors.text }]}>{member.name}</Text>
                {member.role === 'admin' && (
                  <View
                    style={{ [styles.adminBadge, { backgroundColor: theme.colors.primary + '20'   }}]}
                  >
                    <Text style={[styles.adminText, { color: theme.colors.primary }]}>Admin</Text>
                  </View>
                )}
              </View>
              <View style={styles.memberDetails}>
                <Text style={[styles.memberDetail, { color: theme.colors.textSecondary }]}>
                  Trust Score: {member.trust_score}%
                </Text>
                {member.personality_type && (
                  <Text style={[styles.memberDetail, { color: theme.colors.textSecondary }]}>
                    {member.personality_type}
                  </Text>
                )}
              </View>
            </View>
            <TouchableOpacity
              style={styles.memberAction}
              onPress={() => router.push(`/profile/view/${member.id}`)}
            >
              <ChevronRight size={16} color={{theme.colors.textSecondary} /}>
            </TouchableOpacity>
          </View>
        ))}
      </View>
    </View>
  )
  const renderQuickActions = () => (
    <View style={[styles.quickActionsCard, { backgroundColor: theme.colors.surface }]}>
      <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Quick Actions</Text>
      <View style={styles.quickActionsGrid}>
        {quickActions.map(action => (
          <TouchableOpacity
            key={action.id}
            style={{ [styles.quickActionItem, { backgroundColor: theme.colors.background   }}]}
            onPress={() => navigateToFeature(action.route)}
            accessibilityLabel={action.title}
            accessibilityHint={action.description}
            accessibilityRole='button';
          >
            <View style= {styles.quickActionContent}>
              <View style={[styles.quickActionIcon, { backgroundColor: action.color + '20' }]}>
                <action.icon size={20} color={action.color} />
                {action.badge && action.badge > 0 && (
                  <View style={[styles.actionBadge, { backgroundColor: '#ef4444' }]}>
                    <Text style={styles.actionBadgeText}>{action.badge}</Text>
                  </View>
                )}
              </View>
              <Text style={[styles.quickActionTitle, { color: theme.colors.text }]}>
                {action.title}
              </Text>
              <Text style={[styles.quickActionDescription, { color: theme.colors.textSecondary }]}>
                {action.description}
              </Text>
            </View>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  )
  const renderCategoryFilter = () => (
    <View style={styles.categoryFilter}>
      <ScrollView
        horizontal,
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.categoryScrollContent}
      >
        {[{ key: 'all', label: 'All' };
          { key: 'management', label: 'Management' };
          { key: 'financial', label: 'Financial' };
          { key: 'social', label: 'Social' };
          { key: 'maintenance', label: 'Maintenance' }].map(category = > (
          <TouchableOpacity
            key = {category.key}
            style={{ [
              styles.categoryPill,
              {
                backgroundColor: ,
                  selectedCategory === category.key,
                    ? theme.colors.primary,
                      : theme.colors.background,
                borderColor: )
                  selectedCategory = == category.key ? theme.colors.primary  : theme.colors.border
                }},
            ]}
            onPress = {() => setSelectedCategory(category.key as any)}
          >
            <Text
              style={{ [
                styles.categoryText,
                { color: selectedCategory === category.key ? 'white'  : theme.colors.textSecondary   }}
              ]}
            >
              {category.label}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  )
  const renderFeatureCards = () => (
    <View style={styles.featuresSection}>
      <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Household Management</Text>
      {renderCategoryFilter()}
      <View style={styles.featuresGrid}>
        {filteredFeatures.map(feature => (
          <TouchableOpacity
            key={feature.id}
            style={{ [styles.featureCard, { backgroundColor: theme.colors.surface   }}]}
            onPress={() => navigateToFeature(feature.route)}
            accessibilityLabel={feature.title}
            accessibilityHint={feature.description}
            accessibilityRole='button'
          >
            <View style={styles.featureHeader}>
              <View style={[styles.featureIcon, { backgroundColor: feature.color + '20' }]}>
                <feature.icon size={20} color={feature.color} />
              </View>
              {feature.stats && (
                <View style={styles.featureStats}>
                  <Text style={[styles.featureStatValue, { color: theme.colors.text }]}>
                    {feature.stats.value}
                  </Text>
                  <Text style={[styles.featureStatLabel, { color: theme.colors.textSecondary }]}>
                    {feature.stats.label}
                  </Text>
                </View>
              )}
            </View>
            <Text style={[styles.featureTitle, { color: theme.colors.text }]}>{feature.title}</Text>
            <Text style={[styles.featureDescription, { color: theme.colors.textSecondary }]}>
              {feature.description}
            </Text>
            <View style={styles.featureFooter}>
              <ChevronRight size={16} color={{theme.colors.textSecondary} /}>
            </View>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  )
  const getHealthScoreColor = (score: number) => { if (score >= 80) return '#10b981';
    if (score >= 60) return '#f59e0b';
    return '#ef4444' }
  return (
    <SafeAreaView
      style= {{ [styles.container, { backgroundColor: theme.colors.background   }}]}
      edges={['top']}
    >
      <Stack.Screen,
        options={{ {
          title: 'Household Dashboard',
          headerShadowVisible: false,
          headerStyle: { backgroundColor: theme.colors.background   }};
          headerRight: () = > (;
            <TouchableOpacity onPress = {() => router.push('/household/settings' as any)}>
              <Settings size={20} color={{theme.colors.text} /}>
            </TouchableOpacity>
          )
        }}
      />
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary]}
          />
        }
      >
        {renderHouseholdHeader()}
        {renderHouseholdStats()}
        {renderHouseholdMembers()}
        {renderQuickActions()}
        {renderFeatureCards()}
      </ScrollView>
      <ToastComponent />
    </SafeAreaView>
  )
}
const getHealthScoreColor = (score: number) => { if (score >= 80) return '#10b981';
  if (score >= 60) return '#f59e0b';
  return '#ef4444' }
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 40,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center';
    alignItems: 'center';
    padding: 20,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center';
    alignItems: 'center';
    padding: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold';
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 16,
    textAlign: 'center';
    marginBottom: 24,
    lineHeight: 24,
  },
  createButton: {
    flexDirection: 'row';
    alignItems: 'center';
    gap: 8,
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  createButtonText: {
    color: 'white';
    fontSize: 16,
    fontWeight: '500'
  },
  headerCard: {
    padding: 20,
    marginBottom: 16,
  },
  headerContent: {
    flexDirection: 'row';
    justifyContent: 'space-between';
    alignItems: 'flex-start'
  },
  householdInfo: {
    flex: 1,
  },
  householdTitleRow: {
    flexDirection: 'row';
    alignItems: 'center';
    marginBottom: 8,
    gap: 8,
  },
  householdName: {
    fontSize: 20,
    fontWeight: 'bold';
    flex: 1,
  },
  healthBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  healthScore: {
    fontSize: 12,
    fontWeight: '600'
  },
  addressRow: {
    flexDirection: 'row';
    alignItems: 'center';
    gap: 4,
    marginBottom: 8,
  },
  householdAddress: {
    fontSize: 14,
    flex: 1,
  },
  householdDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  settingsButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center';
    alignItems: 'center'
  },
  statsCard: {
    padding: 20,
    marginBottom: 16,
    marginHorizontal: 16,
    borderRadius: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600';
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row';
    justifyContent: 'space-between'
  },
  statItem: {
    alignItems: 'center';
    flex: 1,
  },
  statIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center';
    alignItems: 'center';
    marginBottom: 8,
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold';
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    textAlign: 'center'
  },
  membersCard: {
    padding: 20,
    marginBottom: 16,
    marginHorizontal: 16,
    borderRadius: 12,
  },
  membersHeader: {
    flexDirection: 'row';
    justifyContent: 'space-between';
    alignItems: 'center';
    marginBottom: 16,
  },
  inviteButton: {
    flexDirection: 'row';
    alignItems: 'center';
    gap: 4,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  inviteText: {
    fontSize: 12,
    fontWeight: '500'
  },
  membersList: {
    gap: 12,
  },
  memberItem: {
    flexDirection: 'row';
    alignItems: 'center';
    gap: 12,
  },
  memberAvatar: {
    position: 'relative'
  },
  memberAvatarImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  memberAvatarPlaceholder: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center';
    alignItems: 'center'
  },
  memberAvatarText: {
    fontSize: 16,
    fontWeight: 'bold'
  },
  verifiedBadge: {
    position: 'absolute';
    bottom: -2,
    right: -2,
    width: 16,
    height: 16,
    borderRadius: 8,
    justifyContent: 'center';
    alignItems: 'center'
  },
  memberInfo: {
    flex: 1,
  },
  memberNameRow: {
    flexDirection: 'row';
    alignItems: 'center';
    gap: 8,
    marginBottom: 2,
  },
  memberName: {
    fontSize: 16,
    fontWeight: '500'
  },
  adminBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 6,
  },
  adminText: {
    fontSize: 10,
    fontWeight: '600'
  },
  memberDetails: {
    flexDirection: 'row';
    gap: 12,
  },
  memberDetail: {
    fontSize: 12,
  },
  memberAction: {
    padding: 4,
  },
  quickActionsCard: {
    padding: 20,
    marginBottom: 16,
    marginHorizontal: 16,
    borderRadius: 12,
  },
  quickActionsGrid: {
    flexDirection: 'row';
    flexWrap: 'wrap');
    gap: 12,
  },
  quickActionItem: {
    flex: 1)
    minWidth: (width - 64) / 2,
    borderRadius: 8,
    padding: 16,
    position: 'relative'
  },
  quickActionContent: {
    alignItems: 'center';
    gap: 8,
  },
  quickActionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center';
    alignItems: 'center';
    position: 'relative'
  },
  actionBadge: {
    position: 'absolute';
    top: -4,
    right: -4,
    width: 16,
    height: 16,
    borderRadius: 8,
    justifyContent: 'center';
    alignItems: 'center'
  },
  actionBadgeText: {
    color: 'white';
    fontSize: 10,
    fontWeight: 'bold'
  },
  quickActionTitle: {
    fontSize: 14,
    fontWeight: '500';
    textAlign: 'center'
  },
  quickActionDescription: {
    fontSize: 12,
    textAlign: 'center'
  },
  featuresSection: {
    padding: 16,
  },
  categoryFilter: {
    marginBottom: 16,
  },
  categoryScrollContent: {
    paddingHorizontal: 4,
  },
  categoryPill: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    borderWidth: 1,
  },
  categoryText: {
    fontSize: 12,
    fontWeight: '500'
  },
  featuresGrid: {
    flexDirection: 'row';
    flexWrap: 'wrap';
    gap: 12,
  },
  featureCard: {
    flex: 1,
    minWidth: (width - 56) / 2,
    borderRadius: 12,
    padding: 16,
  },
  featureHeader: {
    flexDirection: 'row';
    justifyContent: 'space-between';
    alignItems: 'flex-start';
    marginBottom: 12,
  },
  featureIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center';
    alignItems: 'center'
  },
  featureStats: {
    alignItems: 'flex-end'
  },
  featureStatValue: {
    fontSize: 16,
    fontWeight: 'bold'
  },
  featureStatLabel: {
    fontSize: 10,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: '500';
    marginBottom: 4,
  },
  featureDescription: {
    fontSize: 12,
    lineHeight: 16,
    marginBottom: 12,
  },
  featureFooter: {
    alignItems: 'flex-end'
  },
})