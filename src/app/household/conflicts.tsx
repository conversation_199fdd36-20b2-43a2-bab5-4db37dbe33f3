import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  ScrollView,
  Dimensions,
  Modal,
  TextInput,
} from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import {
  ChevronLeft,
  MessageCircle,
  AlertTriangle,
  Users,
  Shield,
  BookOpen,
  CheckCircle,
  Clock,
  X,
  Plus,
  Filter,
  Brain,
  Heart,
  Lightbulb,
  Target,
  TrendingUp,
  Award,
  Star,
  Zap,
  Coffee,
  Home,
  DollarSign,
  Volume2,
  Settings,
  UserCheck,
  MessageSquare,
  Handshake,
  Scale,
  Compass,
  BarChart3,
  Trophy,
  Flame,
  Gift,
  ChevronRight,
  Calendar,
  User,
  Mic,
  MicOff,
  Eye,
  EyeOff,
} from 'lucide-react-native';

import { Button } from '@design-system';
import { colors } from '@constants/colors';
import { useAuth } from '@context/AuthContext';
import {
  conflictResolutionService,
  ConflictData,
  ConflictType,
  ResolutionApproach,
  ConflictStatus,
} from '@services/conflictResolutionService';
import Toast from 'react-native-toast-message';
import { useTheme } from '@design-system';
import { useToast } from '@components/ui/Toast';
import { logger } from '@utils/logger';

const { width  } = Dimensions.get('window')
// Enhanced conflict data structures,
interface ConflictParticipant {
  id: string,
  name: string,
  avatar_url?: string,
  personality_type?: string,
  communication_style?: 'direct' | 'diplomatic' | 'supportive' | 'analytical',
  conflict_resolution_style?: ,
    | 'collaborative';
    | 'assertive';
    | 'accommodating';
    | 'avoiding';
    | 'compromising';
  trust_score: number,
  involvement_level: 'primary' | 'secondary' | 'witness';
  response_rate: number,
  cooperation_score: number,
}
interface SmartResolutionSuggestion {
  id: string,
  type: 'communication' | 'mediation' | 'compromise' | 'policy';
  title: string,
  description: string,
  steps: string[];
  success_rate: number,
  estimated_time: string,
  personality_compatibility: number,
  communication_match: number,
  difficulty: 'easy' | 'medium' | 'hard';
  recommended_approach: string,
}
interface ConflictAnalytics {
  total_conflicts: number,
  resolved_conflicts: number,
  resolution_rate: number,
  average_resolution_time: number,
  most_common_type: string,
  prevention_score: number,
  household_harmony_score: number,
  communication_effectiveness: number,
  mediation_success_rate: number,
}
interface ConflictCategory {
  id: string,
  name: string,
  icon: any,
  color: string,
  frequency: number,
  average_resolution_time: number,
  success_rate: number,
}
interface EnhancedConflict {
  id: string,
  title: string,
  description: string,
  conflict_type: 'noise' | 'cleanliness' | 'guests' | 'bills' | 'space' | 'other';
  status: 'reported' | 'in_progress' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high';
  participants: ConflictParticipant[];
  reported_by: string,
  created_at: string,
  updated_at: string,
  resolution_notes?: string,
  resolution_approach?: 'discussion' | 'mediation' | 'agreement',
  smart_suggestions: SmartResolutionSuggestion[];
  communication_compatibility_score?: number,
  estimated_resolution_difficulty: 'easy' | 'medium' | 'hard';
  tags: string[];
  escalation_level: number,
  mediation_requested: boolean,
  follow_up_required: boolean,
}
// Helper function to get icon for conflict type,
const getConflictTypeIcon = (type: ConflictType) => {
  switch (type) {
    case ConflictType.NOISE: ;
      return <MessageCircle size = {20} color={{theme.colors.primary[500]} /}>
    case ConflictType.CLEANLINESS: ;
      return <AlertTriangle size = {20} color={{theme.colors.warning[500]} /}>
    case ConflictType.GUESTS: ;
      return <Users size = {20} color={{theme.colors.info[500]} /}>
    case ConflictType.BILLS: ;
      return <AlertTriangle size = {20} color={{theme.colors.error[500]} /}>
    case ConflictType.SPACE: ;
      return <Users size= {20} color={{theme.colors.success[500]} /}>
    default: ;
      return <AlertTriangle size= {20} color={{theme.colors.gray[500]} /}>
  }
}
// Helper function to get status badge color,
const getStatusColor = (status: ConflictStatus) => { switch (status) {
    case ConflictStatus.REPORTED: ;
      return theme.colors.warning[500];
    case ConflictStatus.IN_PROGRESS: ,
      return theme.colors.info[500];
    case ConflictStatus.RESOLVED: ,
      return theme.colors.success[500];
    case ConflictStatus.CLOSED: ,
      return theme.colors.gray[500];
    default: ;
      return theme.colors.gray[500] }
}
// Helper function to format conflict type for display,
const formatConflictType = (type: ConflictType) => { switch (type) {
    case ConflictType.NOISE: ;
      return 'Noise Issues';
    case ConflictType.CLEANLINESS: ,
      return 'Cleanliness';
    case ConflictType.GUESTS: ,
      return 'Guest Policy';
    case ConflictType.BILLS: ,
      return 'Bills & Payments';
    case ConflictType.SPACE: ,
      return 'Shared Spaces';
    case ConflictType.OTHER: ,
      return 'Other Issues';
    default: ;
      return 'Unknown Issue' }
}
// Helper function to format status for display,
const formatStatus = (status: ConflictStatus) => { switch (status) {
    case ConflictStatus.REPORTED: ;
      return 'Reported';
    case ConflictStatus.IN_PROGRESS: ,
      return 'In Progress';
    case ConflictStatus.RESOLVED: ,
      return 'Resolved';
    case ConflictStatus.CLOSED: ,
      return 'Closed';
    default: ;
      return 'Unknown' }
}
// Format date for display,
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  const today = new Date()
  const diffTime = date.getTime() - today.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  if (diffDays === 0) return 'Today';
  if (diffDays = == 1) return 'Tomorrow';
  if (diffDays = == -1) return 'Yesterday';
  if (diffDays < 0) return `${Math.abs(diffDays)} days ago`;
  return `In ${diffDays} days`;
}
export default function ConflictsScreen() {
  const insets = useSafeAreaInsets()
  const router = useRouter()
  const { state, actions  } = useAuth()
  const theme = useTheme()
  const { showSuccess, showError, ToastComponent } = useToast()
  const [conflicts, setConflicts] = useState<EnhancedConflict[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [statusFilter, setStatusFilter] = useState<ConflictStatus | null>(null)
  const [householdId, setHouseholdId] = useState<string | null>(null)
  // Filter and modal states,
  const [selectedFilter, setSelectedFilter] = useState<;
    'all' | 'reported' | 'in_progress' | 'resolved' | 'closed'
  >('all')
  const [selectedType, setSelectedType] = useState<string>('all')
  const [showReportModal, setShowReportModal] = useState(false)
  const [showAnalyticsModal, setShowAnalyticsModal] = useState(false)
  // New conflict form state,
  const [newConflict, setNewConflict] = useState<{
    title: string,
    description: string,
    conflict_type: 'noise' | 'cleanliness' | 'guests' | 'bills' | 'space' | 'other';
    priority: 'low' | 'medium' | 'high';
    involved_users: string[];
    use_smart_suggestions: boolean,
    request_mediation: boolean,
  }>({
    title: '';
    description: '';
    conflict_type: 'other';
    priority: 'medium';
    involved_users: [];
    use_smart_suggestions: true,
    request_mediation: false,
  })
  // Load household conflicts,
  useEffect(() = > {
    const loadHouseholdConflicts = async () => {
      if (!state.user? .id) return null,
      try {
        setIsLoading(true)
        // Get user's household ID,
        // In a real implementation, we would fetch the user's household ID,
        // For now, we'll use a mock household ID,
        const mockHouseholdId = 'mock-household-id';
        setHouseholdId(mockHouseholdId)
        // Get conflicts for the household,
        const conflictsList = await conflictResolutionService.getHouseholdConflicts(mockHouseholdId,
          statusFilter || undefined)
        )
        setConflicts(conflictsList)
      } catch (error) {
        console.error('Error loading household conflicts  : ', error)
        Toast.show({
          type: 'error'
          text1: 'Error');
          text2: 'Could not load conflicts')
        })
      } finally {
        setIsLoading(false)
      }
    }
    loadHouseholdConflicts()
  }, [state.user? .id, statusFilter])
  // Handle filter change,
  const handleFilterChange = (status  : ConflictStatus | null) => {
    setStatusFilter(status)
  }
  // Render conflict item,
  const renderConflictItem = ({ item }: { item: EnhancedConflict }) => (
    <TouchableOpacity
      style={styles.conflictCard}
      onPress={() => router.push(`/household/conflict-details? id=${item.id}`)}
    >
      <View style={styles.cardHeader}>
        <View style={styles.typeContainer}>
          {getConflictTypeIcon(item.conflict_type as ConflictType)}
          <Text style={styles.conflictType}>
            {formatConflictType(item.conflict_type as ConflictType)}
          </Text>
        </View>
        <View
          style={{ [
            styles.statusBadge,
            { backgroundColor : getStatusColor(item.status as ConflictStatus)   }}
          ]}
        >
          <Text style={styles.statusText}>{formatStatus(item.status as ConflictStatus)}</Text>
        </View>
      </View>
      <Text style={styles.descriptionText} numberOfLines={2}>
        {item.description}
      </Text>
      <View style={styles.cardFooter}>
        <Text style={styles.dateText}>
          {item.created_at ? formatDate(item.created_at)  : 'Unknown date'}
        </Text>
        <Text style={styles.involvedText}>
          {item.participants.length} {item.participants.length === 1 ? 'person' : 'people'} involved
        </Text>
      </View>
    </TouchableOpacity>
  )
  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      <Stack.Screen,
        options={{ headerShown: false,
          }}
      />
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ChevronLeft size={24} color={{theme.colors.gray[700]} /}>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Household Conflicts</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => router.push('/household/conflict-resolution' as any)}
        >
          <Plus size={24} color={{theme.colors.primary[500]} /}>
        </TouchableOpacity>
      </View>
      <View style={styles.filtersContainer}>
        <ScrollView
          horizontal,
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.filtersScrollContent}
        >
          <TouchableOpacity
            style={[styles.filterPill, statusFilter === null && styles.activeFilterPill]}
            onPress={() => handleFilterChange(null)}
          >
            <Text style={[styles.filterText, statusFilter ==={ null && styles.activeFilterText]}}>
              All,
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style = {[
              styles.filterPill,
              statusFilter = == ConflictStatus.REPORTED && styles.activeFilterPill,
            ]}
            onPress = {() => handleFilterChange(ConflictStatus.REPORTED)}
          >
            <Text
              style={[styles.filterText,
                statusFilter = == ConflictStatus.REPORTED && styles.activeFilterText,
              ]}
            >
              Reported,
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style = {[
              styles.filterPill,
              statusFilter = == ConflictStatus.IN_PROGRESS && styles.activeFilterPill,
            ]}
            onPress = {() => handleFilterChange(ConflictStatus.IN_PROGRESS)}
          >
            <Text
              style={[styles.filterText,
                statusFilter = == ConflictStatus.IN_PROGRESS && styles.activeFilterText,
              ]}
            >
              In Progress,
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style = {[
              styles.filterPill,
              statusFilter = == ConflictStatus.RESOLVED && styles.activeFilterPill,
            ]}
            onPress = {() => handleFilterChange(ConflictStatus.RESOLVED)}
          >
            <Text
              style={[styles.filterText,
                statusFilter = == ConflictStatus.RESOLVED && styles.activeFilterText,
              ]}
            >
              Resolved,
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style = {[
              styles.filterPill,
              statusFilter = == ConflictStatus.CLOSED && styles.activeFilterPill,
            ]}
            onPress = {() => handleFilterChange(ConflictStatus.CLOSED)}
          >
            <Text
              style={[styles.filterText,
                statusFilter = == ConflictStatus.CLOSED && styles.activeFilterText,
              ]}
            >
              Closed,
            </Text>
          </TouchableOpacity>
        </ScrollView>
      </View>
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size='large' color={{theme.colors.primary[500]} /}>
          <Text style={styles.loadingText}>Loading conflicts...</Text>
        </View>
      )  : conflicts.length === 0 ? (<View style={styles.emptyContainer}>
          <AlertTriangle size={48} color={{theme.colors.gray[400]} /}>
          <Text style={styles.emptyTitle}>
            {statusFilter
              ? `No ${formatStatus(statusFilter as ConflictStatus).toLowerCase()} conflicts`
               : 'No conflicts found'}
          </Text>
          <Text style={styles.emptyText}>
            {statusFilter
              ? 'Try selecting a different filter or report a new conflict.';
                : "Your household doesn't have any reported conflicts yet."}
          </Text>
          <Button
            variant= 'filled'
            onPress={() => router.push('/household/conflict-resolution' as any)}
            style={styles.reportButton}
          >
            Report a Conflict,
          </Button>
        </View>
      ) : (<FlatList
          data={conflicts}
          renderItem={renderConflictItem}
          keyExtractor={item => item.id || Math.random().toString()}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
        />
      )}
    </View>
  )
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC'
  },
  header: {
    flexDirection: 'row'
    alignItems: 'center';
    justifyContent: 'space-between';
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF';
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0'
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center';
    alignItems: 'center'
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600';
    color: '#1E293B'
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center';
    alignItems: 'center'
  },
  filtersContainer: {
    paddingVertical: 12,
    backgroundColor: '#FFFFFF';
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0'
  },
  filtersScrollContent: {
    paddingHorizontal: 16,
    gap: 8,
  },
  filterPill: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: '#F1F5F9';
    marginRight: 8,
  },
  activeFilterPill: {
    backgroundColor: theme.colors.primary[100]
  },
  filterText: {
    fontSize: 14,
    color: '#64748B'
  },
  activeFilterText: {
    color: theme.colors.primary[700];
    fontWeight: '600'
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center';
    alignItems: 'center';
    padding: 20,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#64748B'
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center';
    alignItems: 'center';
    padding: 20,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600';
    color: '#1E293B';
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 16,
    color: '#64748B';
    textAlign: 'center';
    marginBottom: 24,
  },
  reportButton: {
    minWidth: 180,
  },
  listContent: {
    padding: 16,
    paddingBottom: 32,
  },
  conflictCard: {
    backgroundColor: '#FFFFFF';
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#E2E8F0'
  },
  cardHeader: {
    flexDirection: 'row';
    justifyContent: 'space-between';
    alignItems: 'center';
    marginBottom: 12,
  },
  typeContainer: {
    flexDirection: 'row';
    alignItems: 'center'
  },
  conflictType: {
    fontSize: 16,
    fontWeight: '600';
    color: '#1E293B';
    marginLeft: 8,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600';
    color: 'white'
  },
  descriptionText: {
    fontSize: 15,
    color: '#4B5563';
    marginBottom: 12,
    lineHeight: 22,
  },
  cardFooter: {
    flexDirection: 'row';
    justifyContent: 'space-between');
    alignItems: 'center'
  },
  dateText: {
    fontSize: 13,
    color: '#64748B'
  },
  involvedText: {
    fontSize: 13,
    color: '#64748B')
  },
})