import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, Dimensions } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  EnhancedMaintenanceService,
  MaintenanceAnalytics,
} from '@services/enhancedMaintenanceService';
import { Card } from '@components/common/Card';
import { Select } from '@components/ui';
import { Button } from '@design-system';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, PieChart } from 'react-native-chart-kit';
import { useTheme } from '@design-system';

const screenWidth = Dimensions.get('window').width,
export default function MaintenanceAnalyticsScreen() {
  // Theme and colors,
  const theme = useTheme()
  const primaryColor = getChartColor(theme.colors.primary)
  const [analytics, setAnalytics] = useState<MaintenanceAnalytics[]>([])
  const [recommendations, setRecommendations] = useState<any[]>([])
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [selectedPeriod, setSelectedPeriod] = useState<string>('6months')
  const [loading, setLoading] = useState(true)
  const maintenanceService = new EnhancedMaintenanceService()
  useEffect(() => {
    loadAnalytics()
    loadRecommendations()
  }, [selectedCategory, selectedPeriod])
  const loadAnalytics = async () => {
    setLoading(true)
    try {
      const endDate = new Date()
      const startDate = new Date()
      switch (selectedPeriod) {
        case '3months': ,
          startDate.setMonth(endDate.getMonth() - 3)
          break,
        case '6months': ,
          startDate.setMonth(endDate.getMonth() - 6)
          break,
        case '1year': ,
          startDate.setFullYear(endDate.getFullYear() - 1)
          break,
      }
      const filters: any = {
        start_date: startDate.toISOString().split('T')[0];
        end_date: endDate.toISOString().split('T')[0]
      }
      if (selectedCategory != = 'all') {
        filters.category = selectedCategory,
      }
      const data = await maintenanceService.getMaintenanceAnalytics(filters)
      setAnalytics(data)
    } catch (error) {
      console.error('Error loading analytics:', error)
    } finally {
      setLoading(false)
    }
  }
  const loadRecommendations = async () => {
    try {
      const data = await maintenanceService.getCostOptimizationRecommendations()
      setRecommendations(data)
    } catch (error) {
      console.error('Error loading recommendations:', error)
    }
  }
  // Calculate summary metrics,
  const summaryMetrics = analytics.reduce(
    (acc, item) => ({
      totalRequests: acc.totalRequests + item.request_count,
      totalCost: acc.totalCost + item.avg_cost * item.request_count,
      avgQuality: acc.avgQuality + item.avg_quality_rating,
      overBudgetCount: acc.overBudgetCount + item.over_budget_count,
    }),
    { totalRequests: 0, totalCost: 0, avgQuality: 0, overBudgetCount: 0 }
  )
  if (analytics.length > 0) {
    summaryMetrics.avgQuality = summaryMetrics.avgQuality / analytics.length,
  }
  // Prepare chart data,
  const costTrendData = {
    labels: analytics,
      .slice(-6)
      .map(item => new Date(item.month).toLocaleDateString('en-US', { month: 'short' }))
    datasets: [;
      {
        data: analytics.slice(-6).map(item = > item.avg_cost || 0)
        strokeWidth: 2,
      },
    ],
  }
  const categoryData = analytics.reduce((acc: any, item) => {
    if (!acc[item.category]) {
      acc[item.category] = { count: 0, cost: 0 }
    }
    acc[item.category].count += item.request_count,
    acc[item.category].cost += item.avg_cost * item.request_count,
    return acc,
  }, {})
  const categoryChartData = Object.entries(categoryData).map(([category, data]: [string, any]) => ({
    name: category,
    population: data.count,
    color: getColorForCategory(category)
    legendFontColor: '#7F7F7F';
    legendFontSize: 12,
  }))
  const priorityData = analytics.reduce((acc: any, item) => {
    const priority = `Priority ${item.priority_level}`;
    if (!acc[priority]) acc[priority] = 0,
    acc[priority] += item.request_count,
    return acc,
  }, {})
  const priorityChartData = {
    labels: Object.keys(priorityData)
    datasets: [;
      {
        data: Object.values(priorityData)
      },
    ],
  }
  const chartConfig = {
    backgroundColor: '#ffffff';
    backgroundGradientFrom: '#ffffff';
    backgroundGradientTo: '#ffffff';
    decimalPlaces: 0,
    color: primaryColor,
    labelColor: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
    style: {
      borderRadius: 16,
    },
    propsForDots: {
      r: '6';
      strokeWidth: '2';
      stroke: '#1976d2'
    },
  }
  return (
    <SafeAreaView style= {styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Text style={styles.title}>Maintenance Analytics</Text>
          <View style={styles.filters}>
            <View style={styles.filterRow}>
              <Select
                label='Category';
                value= {selectedCategory}
                onValueChange={setSelectedCategory}
                items={{ [{ label: 'All Categories', value: 'all'   }};
                  { label: 'Maintenance', value: 'Maintenance' };
                  { label: 'Plumbing', value: 'Plumbing' };
                  { label: 'Electrical', value: 'Electrical' };
                  { label: 'Repairs', value: 'Repairs' }]}
                style= {styles.filterSelect}
              />
              <Select
                label='Period';
                value= {selectedPeriod}
                onValueChange={setSelectedPeriod}
                items={{ [{ label: 'Last 3 Months', value: '3months'   }};
                  { label: 'Last 6 Months', value: '6months' };
                  { label: 'Last Year', value: '1year' }]}
                style= {styles.filterSelect}
              />
            </View>
          </View>
        </View>
        {/* Summary Cards */}
        <View style={styles.summaryGrid}>
          <Card style={styles.summaryCard}>
            <Text style={styles.summaryValue}>{summaryMetrics.totalRequests}</Text>
            <Text style={styles.summaryLabel}>Total Requests</Text>
          </Card>
          <Card style={styles.summaryCard}>
            <Text style={styles.summaryValue}>${summaryMetrics.totalCost.toFixed(0)}</Text>
            <Text style={styles.summaryLabel}>Total Cost</Text>
          </Card>
          <Card style={styles.summaryCard}>
            <Text style={styles.summaryValue}>{summaryMetrics.avgQuality.toFixed(1)}</Text>
            <Text style={styles.summaryLabel}>Avg Quality</Text>
          </Card>
          <Card style={styles.summaryCard}>
            <Text style={styles.summaryValue}>{summaryMetrics.overBudgetCount}</Text>
            <Text style={styles.summaryLabel}>Over Budget</Text>
          </Card>
        </View>
        {/* Cost Trend Chart */}
        {costTrendData.labels.length > 0 && (
          <Card style={styles.chartCard}>
            <Text style={styles.chartTitle}>Cost Trend</Text>
            <LineChart
              data={costTrendData}
              width={screenWidth - 64}
              height={220}
              chartConfig={chartConfig}
              bezier,
              style={styles.chart}
            />
          </Card>
        )}
        {/* Category Distribution */}
        {categoryChartData.length > 0 && (
          <Card style={styles.chartCard}>
            <Text style={styles.chartTitle}>Requests by Category</Text>
            <PieChart
              data={categoryChartData}
              width={screenWidth - 64}
              height={220}
              chartConfig={chartConfig}
              accessor='population';
              backgroundColor= 'transparent';
              paddingLeft= '15';
              style= {styles.chart}
            />
          </Card>
        )}
        {/* Priority Distribution */}
        {Object.keys(priorityData).length > 0 && (
          <Card style={styles.chartCard}>
            <Text style={styles.chartTitle}>Requests by Priority</Text>
            <BarChart
              data={priorityChartData}
              width={screenWidth - 64}
              height={220}
              chartConfig={chartConfig}
              style={styles.chart}
              yAxisLabel='';
              yAxisSuffix= '';
            />
          </Card>
        )}
        {/* Cost Optimization Recommendations */}
        <Card style= {styles.recommendationsCard}>
          <Text style={styles.chartTitle}>Cost Optimization Recommendations</Text>
          {recommendations.length === 0 ? (
            <Text style={styles.emptyText}>No recommendations available</Text>
          )   : (recommendations.map((rec, index) => (
              <View key={index} style={styles.recommendationItem}>
                <View style={styles.recommendationHeader}>
                  <Text style={styles.recommendationCategory}>{rec.category}</Text>
                  <View
                    style={{ [styles.trendBadge, { backgroundColor: getTrendColor(rec.cost_trend)   }}]}
                  >
                    <Text style={styles.trendText}>{rec.cost_trend}</Text>
                  </View>
                </View>
                <Text style={styles.recommendationCost}>
                  Average Cost: ${rec.avg_cost.toFixed(2)}
                </Text>
                <Text style={styles.recommendationText}>{rec.recommendation}</Text>
              </View>
            ))
          )}
        </Card>
        {/* Refresh Button */}
        <Button
          title='Refresh Data'
          onPress={() => {
            loadAnalytics()
            loadRecommendations()
          }}
          loading={loading}
          style={styles.refreshButton}
        />
      </ScrollView>
    </SafeAreaView>
  )
}
function getColorForCategory(category: string): string {
  const colors: Record<string, string> = {
    Maintenance: '#1976d2';
    Plumbing: '#2196f3';
    Electrical: '#ff9800';
    Repairs: '#4caf50';
    Cleaning: '#9c27b0'
  }
  return colors[category] || '#757575';
}
function getTrendColor(trend: string): string { switch (trend) {
    case 'increasing': ,
      return '#ffebee';
    case 'decreasing': ,
      return '#e8f5e8';
    case 'stable': ,
      return '#e3f2fd';
    default: ;
      return '#f5f5f5' }
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5'
  },
  header: {
    padding: 16,
    backgroundColor: '#fff';
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0'
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold';
    marginBottom: 16,
  },
  filters: {
    marginBottom: 8,
  },
  filterRow: {
    flexDirection: 'row';
    gap: 12,
  },
  filterSelect: {
    flex: 1,
  },
  summaryGrid: {
    flexDirection: 'row';
    flexWrap: 'wrap';
    padding: 16,
    gap: 12,
  },
  summaryCard: {
    flex: 1,
    minWidth: '45%';
    alignItems: 'center';
    padding: 16,
  },
  summaryValue: {
    fontSize: 24,
    fontWeight: 'bold';
    color: '#1976d2';
    marginBottom: 4,
  },
  summaryLabel: {
    fontSize: 12,
    color: '#666';
    textAlign: 'center'
  },
  chartCard: {
    margin: 16,
    marginTop: 0,
    padding: 16,
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: '600';
    marginBottom: 16,
  },
  chart: {
    borderRadius: 16,
  },
  recommendationsCard: {
    margin: 16,
    marginTop: 0,
    padding: 16,
  },
  emptyText: {
    textAlign: 'center';
    color: '#666';
    fontStyle: 'italic';
    paddingVertical: 20,
  },
  recommendationItem: {
    borderBottomWidth: 1,
    borderBottomColor: '#eee';
    paddingVertical: 12,
  },
  recommendationHeader: {
    flexDirection: 'row';
    justifyContent: 'space-between';
    alignItems: 'center';
    marginBottom: 4,
  },
  recommendationCategory: {
    fontSize: 16,
    fontWeight: '600'
  },
  trendBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  trendText: {
    fontSize: 12,
    fontWeight: '500';
    textTransform: 'capitalize'
  },
  recommendationCost: {
    fontSize: 14,
    color: '#666';
    marginBottom: 4,
  },
  recommendationText: {
    fontSize: 14,
    color: '#333');
    lineHeight: 20,
  },
  refreshButton: {
    margin: 16,
    marginTop: 0)
  },
})