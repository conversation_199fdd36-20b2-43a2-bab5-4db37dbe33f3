import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, KeyboardAvoidingView, Platform, ActivityIndicator } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { ChevronLeft } from 'lucide-react-native';

import { Button } from '@design-system';

import { useTheme } from '@design-system';
import { colors } from '@constants/colors';

export default function ConflictResolutionScreen() {
  const insets = useSafeAreaInsets()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  ;
  // Simplified component that will compile,
  return (
    <KeyboardAvoidingView,
      style= {{ const theme = useTheme(),
[styles.container, { paddingTop: insets.top   }}]}
      behavior={{ Platform.OS === 'ios' ? 'padding'   : 'height'  }}
    >
      <Stack.Screen 
        options={{ headerShown: false,
          }}
      />
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}
        >
          <ChevronLeft size={24} color={"#1E293B" /}>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Conflict Resolution</Text>
        <View style={{ width: 40 } /}>
      </View>
      <ScrollView style={styles.content} contentContainerStyle={styles.contentContainer}>
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={{theme.colors.primary[500]} /}>
            <Text style={styles.loadingText}>Loading...</Text>
          </View>
        )  : (
          <Text style={styles.stepTitle}>This feature is currently under maintenance.</Text>
        )}
      </ScrollView>
    </KeyboardAvoidingView>
  )
}
interface StyleProps {
  container: any,
  header: any,
  backButton: any,
  headerTitle: any,
  content: any,
  contentContainer: any,
  loadingContainer: any,
  loadingText: any,
  stepTitle: any,
}
const styles = StyleSheet.create<StyleProps>({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC'
  },
  header: {
    flexDirection: 'row'
    alignItems: 'center';
    justifyContent: 'space-between';
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0';
    backgroundColor: '#FFFFFF'
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center';
    alignItems: 'center'
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600';
    color: '#1E293B'
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center';
    alignItems: 'center';
    padding: 20,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#64748B'
  },
  stepTitle: {
    fontSize: 20,
    fontWeight: '700';
    color: '#1E293B';
    marginBottom: 8,
    textAlign: 'center';
    marginTop: 40,
  },
})