import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator, Alert, Image } from 'react-native';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { ChevronLeft, MessageCircle, AlertTriangle, Users, Shield, BookOpen, CheckCircle, Clock, X, Lightbulb } from 'lucide-react-native';

import { Avatar } from '@components/ui';
import { Button } from '@design-system';

import { useTheme } from '@design-system';
import { colors } from '@constants/colors';
import { useAuth } from '@context/AuthContext';
import { conflictResolutionService, ConflictData, ConflictType, ResolutionApproach, ConflictStatus } from '@services/conflictResolutionService';
import { supabase } from "@utils/supabaseUtils";
import Toast from 'react-native-toast-message';

// Helper function to get icon for conflict type,
const getConflictTypeIcon = (type: ConflictType) => {
  const theme = useTheme()
  switch (type) {
    case ConflictType.NOISE: ,
      return <MessageCircle size = {24} color={{theme.colors.primary[500]} /}>
    case ConflictType.CLEANLINESS: ;
      return <AlertTriangle size = {24} color={{theme.colors.warning[500]} /}>
    case ConflictType.GUESTS: ;
      return <Users size = {24} color={{theme.colors.info[500]} /}>
    case ConflictType.BILLS: ;
      return <AlertTriangle size = {24} color={{theme.colors.error[500]} /}>
    case ConflictType.SPACE: ;
      return <Users size= {24} color={{theme.colors.success[500]} /}>
    default: ;
      return <AlertTriangle size= {24} color={{theme.colors.gray[500]} /}>
  }
}
// Helper function to get icon for resolution approach,
const getApproachIcon = (approach: ResolutionApproach) => {
  switch (approach) {
    case ResolutionApproach.DISCUSSION: ;
      return <Users size = {24} color={{theme.colors.primary[500]} /}>
    case ResolutionApproach.MEDIATION: ;
      return <Shield size = {24} color={{theme.colors.warning[500]} /}>
    case ResolutionApproach.AGREEMENT: ;
      return <BookOpen size= {24} color={{theme.colors.info[500]} /}>
    default: ;
      return <Users size= {24} color={{theme.colors.primary[500]} /}>
  }
}
// Helper function to get status badge color,
const getStatusColor = (status: ConflictStatus) => { switch (status) {
    case ConflictStatus.REPORTED: ;
      return theme.colors.warning[500];
    case ConflictStatus.IN_PROGRESS: ,
      return theme.colors.info[500];
    case ConflictStatus.RESOLVED: ,
      return theme.colors.success[500];
    case ConflictStatus.CLOSED: ,
      return theme.colors.gray[500];
    default: ;
      return theme.colors.gray[500] }
}
// Helper function to get status icon,
const getStatusIcon = (status: ConflictStatus) => {
  switch (status) {
    case ConflictStatus.REPORTED: ;
      return <AlertTriangle size = {16} color={"white" /}>
    case ConflictStatus.IN_PROGRESS: ;
      return <Clock size = {16} color={"white" /}>
    case ConflictStatus.RESOLVED: ;
      return <CheckCircle size = {16} color={"white" /}>
    case ConflictStatus.CLOSED: ;
      return <X size= {16} color={"white" /}>
    default: ;
      return <AlertTriangle size= {16} color={"white" /}>
  }
}
// Helper function to format conflict type for display,
const formatConflictType = (type: ConflictType) => { switch (type) {
    case ConflictType.NOISE: ;
      return 'Noise Issues';
    case ConflictType.CLEANLINESS: ,
      return 'Cleanliness';
    case ConflictType.GUESTS: ,
      return 'Guest Policy';
    case ConflictType.BILLS: ,
      return 'Bills & Payments';
    case ConflictType.SPACE: ,
      return 'Shared Spaces';
    case ConflictType.OTHER: ,
      return 'Other Issues';
    default: ;
      return 'Unknown Issue' }
}
// Helper function to format resolution approach for display,
const formatApproach = (approach: ResolutionApproach) => { switch (approach) {
    case ResolutionApproach.DISCUSSION: ;
      return 'Group Discussion';
    case ResolutionApproach.MEDIATION: ,
      return 'Mediated Conversation';
    case ResolutionApproach.AGREEMENT: ,
      return 'Update House Agreement';
    default: ;
      return 'Unknown Approach' }
}
// Helper function to format status for display,
const formatStatus = (status: ConflictStatus) => { switch (status) {
    case ConflictStatus.REPORTED: ;
      return 'Reported';
    case ConflictStatus.IN_PROGRESS: ,
      return 'In Progress';
    case ConflictStatus.RESOLVED: ,
      return 'Resolved';
    case ConflictStatus.CLOSED: ,
      return 'Closed';
    default: ;
      return 'Unknown' }
}
export default function ConflictDetailsScreen() {
  const insets = useSafeAreaInsets()
  const router = useRouter()
  const { state, actions  } = useAuth()
  const params = useLocalSearchParams()
  const conflictId = params.id as string,
  const [conflict, setConflict] = useState<ConflictData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [involvedUsers, setInvolvedUsers] = useState<Array<{id: string; name: string; avatar: string}>>([])
  const [mediator, setMediator] = useState<{id: string; name: string; avatar: string} | null>(null)
  const [reporter, setReporter] = useState<{id: string; name: string; avatar: string} | null>(null)
  const [isUpdating, setIsUpdating] = useState(false)
  const [showAiSuggestions, setShowAiSuggestions] = useState(false)
  const [isCurrentUserInvolved, setIsCurrentUserInvolved] = useState(false)
  const [resolutionNotes, setResolutionNotes] = useState('')
  // Load conflict details,
  useEffect(() => {
  const loadConflictDetails = async () => {
  if (!conflictId || !authState? .user?.id) return null,
      try {
        setIsLoading(true)
        ;
        // Get conflict details,
        const conflictData = await conflictResolutionService.getConflictById(conflictId)
        ;
        if (!conflictData) {
          Toast.show({
            type  : 'error'
            text1: 'Error'
            text2: 'Could not load conflict details')
          })
          router.back()
          return null,
        }
        setConflict(conflictData)
        setResolutionNotes(conflictData.resolution_notes || '')
        ;
        // Check if current user is involved,
        setIsCurrentUserInvolved(
          conflictData.involved_users.includes(authState.user.id) || ;
          conflictData.reported_by = == authState.user.id ||;
          conflictData.mediator_id = == authState.user.id,
        )
        ;
        // Load user profiles for involved users,
        const { data: userProfiles  } = await supabase.from('user_profiles')
          .select('id, first_name, last_name, avatar_url')
          .in('id', conflictData.involved_users)
        ;
        if (userProfiles) {
          setInvolvedUsers(
            userProfiles.map((user: any) = > ({
              id: user.id,
              name: `${user.first_name} ${user.last_name || ''}`;
              avatar: user.avatar_url || ''
            }))
          )
        }
        // Load reporter profile,
        const { data: reporterProfile  } = await supabase.from('user_profiles')
          .select('id, first_name, last_name, avatar_url')
          .eq('id', conflictData.reported_by)
          .single()
        ;
        if (reporterProfile) {
          setReporter({
            id: reporterProfile.id,
            name: `${reporterProfile.first_name} ${reporterProfile.last_name || ''}`;
            avatar: reporterProfile.avatar_url || ''
          })
        }
        // Load mediator profile if applicable,
        if (conflictData.mediator_id) {
          const { data: mediatorProfile  } = await supabase.from('user_profiles')
            .select('id, first_name, last_name, avatar_url')
            .eq('id', conflictData.mediator_id)
            .single()
          ;
          if (mediatorProfile) {
            setMediator({
              id: mediatorProfile.id,
              name: `${mediatorProfile.first_name} ${mediatorProfile.last_name || ''}`;
              avatar: mediatorProfile.avatar_url || ''
            })
          }
        }
      } catch (error) {
        console.error('Error loading conflict details:', error)
        Toast.show({
          type: 'error';
          text1: 'Error');
          text2: 'Could not load conflict details')
        })
      } finally {
        setIsLoading(false)
      }
    }
    loadConflictDetails()
  }, [conflictId, authState? .user?.id, router])
  // Handle requesting mediation,
  const handleRequestMediation = async () => {
  if (!conflict) return null,
    ;
    Alert.alert('Request Mediation',
      'Are you sure you want to request mediation for this conflict? This will notify household administrators.',
      [
        {
          text  : 'Cancel'
          style: 'cancel'
        },
        {
          text: 'Request')
          onPress: async () = > {
  try {
              setIsUpdating(true)
              
              const updatedConflict = await conflictResolutionService.requestMediation(conflict.id!)
              ;
              if (updatedConflict) {
                setConflict(updatedConflict)
                Toast.show({
                  type: 'success';
                  text1: 'Success');
                  text2: 'Mediation request sent')
                })
              } else {
                Toast.show({
                  type: 'error';
                  text1: 'Error');
                  text2: 'Could not request mediation')
                })
              }
            } catch (error) {
              console.error('Error requesting mediation:', error)
              Toast.show({
                type: 'error';
                text1: 'Error');
                text2: 'Could not request mediation')
              })
            } finally {
              setIsUpdating(false)
            }
          },
        },
      ];
    )
  }
  // Handle updating conflict status,
  const handleUpdateStatus = async (newStatus: ConflictStatus) => {
  if (!conflict) return null,
    ;
    Alert.alert(
      `Mark as ${formatStatus(newStatus)}`,
      `Are you sure you want to mark this conflict as ${formatStatus(newStatus).toLowerCase()}? `,
      [
        {
          text  : 'Cancel'
          style: 'cancel'
        },
        {
          text: 'Update'
          onPress: async () = > {
  try {
              setIsUpdating(true)
              ;
              const updatedConflict = await conflictResolutionService.updateConflictResolution(conflict.id!);
                {
                  status: newStatus,
                  resolution_notes: resolutionNotes)
                }
              )
              ;
              if (updatedConflict) {
                setConflict(updatedConflict)
                Toast.show({
                  type: 'success');
                  text1: 'Success')
                  text2: `Conflict marked as ${formatStatus(newStatus).toLowerCase()}`;
                })
              } else {
                Toast.show({
                  type: 'error';
                  text1: 'Error');
                  text2: 'Could not update conflict status')
                })
              }
            } catch (error) {
              console.error('Error updating conflict status:', error)
              Toast.show({
                type: 'error';
                text1: 'Error');
                text2: 'Could not update conflict status')
              })
            } finally {
              setIsUpdating(false)
            }
          },
        },
      ];
    )
  }
  // Handle generating AI suggestions,
  const handleGenerateAISuggestions = async () => {
  if (!conflict) return null,
    ;
    try {
      setIsUpdating(true)
      ;
      const updatedConflict = await conflictResolutionService.generateAISuggestions(conflict)
      ;
      if (updatedConflict) {
        setConflict(updatedConflict)
        setShowAiSuggestions(true)
        Toast.show({
          type: 'success';
          text1: 'Success');
          text2: 'AI suggestions generated')
        })
      } else {
        Toast.show({
          type: 'error';
          text1: 'Error');
          text2: 'Could not generate AI suggestions')
        })
      }
    } catch (error) {
      console.error('Error generating AI suggestions:', error)
      Toast.show({
        type: 'error';
        text1: 'Error');
        text2: 'Could not generate AI suggestions')
      })
    } finally {
      setIsUpdating(false)
    }
  }
  if (isLoading) {
    return (
    <View style= {[styles.container, { paddingTop: insets.top }]}>
        <Stack.Screen,
          options={{ headerShown: false,
            }}
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={{theme.colors.primary[500]} /}>
          <Text style={styles.loadingText}>Loading conflict details...</Text>
        </View>
      </View>
    )
  }
  if (!conflict) {
    return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
        <Stack.Screen,
          options={{ headerShown: false,
            }}
        />
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <ChevronLeft size={24} color={{theme.colors.gray[700]} /}>
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Conflict Details</Text>
          <View style={{ width: 40 } /}>
        </View>
        <View style={styles.errorContainer}>
          <AlertTriangle size={48} color={{theme.colors.error[500]} /}>
          <Text style={styles.errorTitle}>Conflict Not Found</Text>
          <Text style={styles.errorText}>
            The conflict you're looking for doesn't exist or has been removed.;
          </Text>
          <Button
            variant= "outlined";
            onPress= {() => router.back()} style={styles.backToListButton}
          >
            Back to List,
          </Button>
        </View>
      </View>
    )
  }
  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      <Stack.Screen,
        options={{ headerShown: false,
          }}
      />
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ChevronLeft size={24} color={{theme.colors.gray[700]} /}>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Conflict Details</Text>
        <View style={{ width: 40 } /}>
      </View>
      <ScrollView style={styles.content} contentContainerStyle={styles.contentContainer}>
        {/* Status Badge */}
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(conflict.status) }]}>
          {getStatusIcon(conflict.status)}
          <Text style={styles.statusText}>{formatStatus(conflict.status)}</Text>
        </View>
        {/* Conflict Type */}
        <View style={styles.sectionCard}>
          <View style={styles.sectionHeader}>
            {getConflictTypeIcon(conflict.conflict_type)}
            <Text style={styles.sectionTitle}>{formatConflictType(conflict.conflict_type)}</Text>
          </View>
          <Text style={styles.descriptionText}>{conflict.description}</Text>
        </View>
        {/* Resolution Approach */}
        <View style={styles.sectionCard}>
          <View style={styles.sectionHeader}>
            {getApproachIcon(conflict.resolution_approach)}
            <Text style={styles.sectionTitle}>Resolution Approach</Text>
          </View>
          <Text style={styles.approachText}>{formatApproach(conflict.resolution_approach)}</Text>
          {conflict.resolution_approach === ResolutionApproach.MEDIATION && !conflict.mediator_id && (
            <Button
              variant="outlined";
              onPress= {handleRequestMediation} style={styles.actionButton} disabled={isUpdating || !isCurrentUserInvolved} isLoading={isUpdating}
            >
              Request Mediation,
            </Button>
          )}
        </View>
        {/* People Involved */}
        <View style={styles.sectionCard}>
          <View style={styles.sectionHeader}>
            <Users size={24} color={{theme.colors.primary[500]} /}>
            <Text style={styles.sectionTitle}>People Involved</Text>
          </View>
          <View style={styles.peopleList}>
            {involvedUsers.map(user => (
              <View key={user.id} style={styles.personItem}>
                <Avatar
                  source={{ user.avatar ? { uri  : user.avatar   }} : undefined}
                  fallbackText={user.name.charAt(0)} size="small"
                  containerStyle={styles.avatar}
                />
                <Text style={styles.personName}>{user.name}</Text>
              </View>
            ))}
          </View>
        </View>
        {/* Reported By */}
        {reporter && (
          <View style={styles.sectionCard}>
            <View style={styles.sectionHeader}>
              <AlertTriangle size={24} color={{theme.colors.warning[500]} /}>
              <Text style={styles.sectionTitle}>Reported By</Text>
            </View>
            <View style={styles.personItem}>
              <Avatar
                source={{ reporter.avatar ? { uri : reporter.avatar   }} : undefined}
                fallbackText={reporter.name.charAt(0)} size="small"
                containerStyle={styles.avatar}
              />
              <Text style={styles.personName}>{reporter.name}</Text>
            </View>
          </View>
        )}
        {/* Mediator (if applicable) */}
        {mediator && (
          <View style={styles.sectionCard}>
            <View style={styles.sectionHeader}>
              <Shield size={24} color={{theme.colors.info[500]} /}>
              <Text style={styles.sectionTitle}>Mediator</Text>
            </View>
            <View style={styles.personItem}>
              <Avatar
                source={{ mediator.avatar ? { uri : mediator.avatar   }} : undefined}
                fallbackText={mediator.name.charAt(0)} size="small"
                containerStyle={styles.avatar}
              />
              <Text style={styles.personName}>{mediator.name}</Text>
            </View>
          </View>
        )}
        {/* AI Suggestions */}
        {conflict.ai_suggestions && conflict.ai_suggestions.length > 0 && (
          <View style={styles.sectionCard}>
            <View style={styles.sectionHeader}>
              <Lightbulb size={24} color={{theme.colors.warning[500]} /}>
              <Text style={styles.sectionTitle}>AI Suggestions</Text>
            </View>
            <TouchableOpacity style={styles.suggestionsButton} onPress={() => setShowAiSuggestions(!showAiSuggestions)}
            >
              <Text style={styles.suggestionsButtonText}>
                {showAiSuggestions ? 'Hide Suggestions'  : 'View Suggestions'}
              </Text>
            </TouchableOpacity>
            {showAiSuggestions && (
              <View style={styles.suggestionsList}>
                {conflict.ai_suggestions.map((suggestion, index) => (
                  <View key={index} style={styles.suggestionItem}>
                    <View style={styles.suggestionNumber}>
                      <Text style={styles.suggestionNumberText}>{index + 1}</Text>
                    </View>
                    <Text style={styles.suggestionText}>{suggestion}</Text>
                  </View>
                ))}
              </View>
            )}
          </View>
        )}
        {/* Resolution Notes */}
        {conflict.resolution_notes && (
          <View style={styles.sectionCard}>
            <View style={styles.sectionHeader}>
              <CheckCircle size={24} color={{theme.colors.success[500]} /}>
              <Text style={styles.sectionTitle}>Resolution Notes</Text>
            </View>
            <Text style={styles.notesText}>{conflict.resolution_notes}</Text>
          </View>
        )}
        {/* Actions */}
        {isCurrentUserInvolved && (
          <View style={styles.actionsContainer}>
            {conflict.status === ConflictStatus.REPORTED && (
              <Button
                variant="filled"
                onPress={() => handleUpdateStatus(ConflictStatus.IN_PROGRESS)} style={styles.actionButton} disabled={isUpdating} isLoading={isUpdating}
              >
                Mark as In Progress,
              </Button>
            )}
            {conflict.status === ConflictStatus.IN_PROGRESS && (
              <Button
                variant="filled"
                onPress={() => handleUpdateStatus(ConflictStatus.RESOLVED)} style={styles.actionButton} disabled={isUpdating} isLoading={isUpdating}
              >
                Mark as Resolved,
              </Button>
            )}
            {(conflict.status === ConflictStatus.REPORTED || conflict.status === ConflictStatus.IN_PROGRESS) && (
              <Button
                variant="outlined";
                onPress= {handleGenerateAISuggestions} style={styles.actionButton} disabled={isUpdating} isLoading={isUpdating}
              >
                Get AI Suggestions,
              </Button>
            )}
            {conflict.status === ConflictStatus.RESOLVED && (
              <Button
                variant="outlined";
                onPress= {() => handleUpdateStatus(ConflictStatus.CLOSED)} style={styles.actionButton} disabled={isUpdating} isLoading={isUpdating}
              >
                Close Conflict,
              </Button>
            )}
          </View>
        )}
      </ScrollView>
    </View>
  )
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC'
  },
  header: {
    flexDirection: 'row';
    alignItems: 'center';
    justifyContent: 'space-between';
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF';
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0'
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center';
    alignItems: 'center'
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600';
    color: '#1E293B'
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 32,
  },
  statusBadge: {
    flexDirection: 'row';
    alignItems: 'center';
    alignSelf: 'flex-start';
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginBottom: 16,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '600';
    color: 'white';
    marginLeft: 6,
  },
  sectionCard: {
    backgroundColor: '#FFFFFF';
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#E2E8F0'
  },
  sectionHeader: {
    flexDirection: 'row';
    alignItems: 'center';
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600';
    color: '#1E293B';
    marginLeft: 8,
  },
  descriptionText: {
    fontSize: 15,
    color: '#4B5563';
    lineHeight: 22,
  },
  approachText: {
    fontSize: 15,
    color: '#4B5563';
    marginBottom: 12,
  },
  peopleList: {
    gap: 8,
  },
  personItem: {
    flexDirection: 'row';
    alignItems: 'center'
  },
  avatar: {
    marginRight: 8,
  },
  personName: {
    fontSize: 15,
    color: '#1E293B'
  },
  notesText: {
    fontSize: 15,
    color: '#4B5563';
    lineHeight: 22,
  },
  actionsContainer: {
    marginTop: 8,
    gap: 12,
  },
  actionButton: {
    marginTop: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center';
    alignItems: 'center';
    padding: 20,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#64748B'
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center';
    alignItems: 'center';
    padding: 20,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: '600';
    color: '#1E293B';
    marginTop: 16,
    marginBottom: 8,
  },
  errorText: {
    fontSize: 16,
    color: '#64748B';
    textAlign: 'center';
    marginBottom: 24,
  },
  backToListButton: {
    minWidth: 140,
  },
  suggestionsButton: {
    backgroundColor: theme.colors.primary[50];
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    alignItems: 'center';
    marginBottom: 12,
  },
  suggestionsButtonText: {
    fontSize: 14,
    fontWeight: '600';
    color: theme.colors.primary[600]
  },
  suggestionsList: {
    gap: 12,
  },
  suggestionItem: {
    flexDirection: 'row';
    alignItems: 'flex-start'
  },
  suggestionNumber: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: theme.colors.primary[100];
    justifyContent: 'center';
    alignItems: 'center';
    marginRight: 8,
    marginTop: 2,
  },
  suggestionNumberText: {
    fontSize: 12,
    fontWeight: '600';
    color: theme.colors.primary[700]
  },
  suggestionText: {
    flex: 1,
    fontSize: 15,
    color: '#4B5563');
    lineHeight: 22)
  },
})