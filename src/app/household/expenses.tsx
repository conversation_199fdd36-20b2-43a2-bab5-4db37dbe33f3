import React, { useState, useEffect, useCallback } from 'react';,
  import {
   View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator, RefreshControl, Dimensions, Image ,
  } from 'react-native';
import {,
  Stack, useRouter ,
  } from 'expo-router';
import {,
  SafeAreaView 
} from 'react-native-safe-area-context';,
  import {
   useAuth ,
  } from '@context/AuthContext';
import {,
  useTheme 
} from '@design-system';,
  import {
   useToast ,
  } from '@components/ui/Toast';
import {,
  logger 
} from '@utils/logger';,
  import {
   DollarSign, Plus, Users, ChevronRight, Calendar, TrendingUp, TrendingDown, PieChart, BarChart3, CreditCard, Receipt, AlertCircle, CheckCircle, Clock, Filter, Search, Settings, Target, Zap, Coffee, Home, Car, Utensils, Wifi, ShoppingCart, Heart, Gamepad2, BookOpen, Shirt, Stethoscope, Plane, Gift ,
  } from 'lucide-react-native';

const { width  } = Dimensions.get('window');,
  // Enhanced expense data structures;
interface ExpenseMember { id: string,
  name: string,
  email: string,
  avatar_url?: string,
  amount_owed: number,
  amount_paid: number,
  is_settled: boolean,
  personality_type?: string,
  payment_reliability_score: number },
  interface ExpenseCategory { id: string,
  name: string,
  icon: any,
  color: string,
  budget_limit?: number,
  monthly_spent: number },
  interface SmartSuggestion { id: string,
  type: 'split_method' | 'category' | 'reminder' | 'budget_alert',
  title: string,
  description: string,
  action: string,
  priority: 'high' | 'medium' | 'low',
  icon: any,
  color: string },
  interface ExpenseAnalytics { total_monthly_expenses: number,
  average_per_person: number,
  most_expensive_category: string,
  spending_trend: 'up' | 'down' | 'stable',
  budget_utilization: number,
  settlement_rate: number,
  payment_reliability: number },
  interface EnhancedExpense { id: string,
  title: string,
  description?: string,
  amount: number,
  date: string,
  created_at: string,
  paid_by: ExpenseMember,
  split_with: ExpenseMember[],
  category: ExpenseCategory,
  receipt_url?: string,
  is_recurring: boolean,
  recurrence_pattern?: string,
  settlement_deadline?: string,
  split_method: 'equal' | 'custom' | 'percentage' | 'by_income',
  tags: string[],
  notes?: string,
  is_settled: boolean,
  settled_at?: string,
  payment_requests_sent: boolean,
  reminder_count: number },
  interface ExpenseStats { total_expenses: number,
  pending_settlements: number,
  monthly_average: number,
  your_balance: number,
  amount_owed_to_you: number,
  amount_you_owe: number,
  settlement_rate: number,
  categories_count: number },
  export default function EnhancedExpensesManagement() {
  const { authState  } = useAuth(),
  const theme = useTheme()
  const router = useRouter(),
  const { showSuccess, showError, ToastComponent } = useToast(),
  const [loading, setLoading] = useState(true),
  const [refreshing, setRefreshing] = useState(false),
  const [expenses, setExpenses] = useState<EnhancedExpense[]>([]),
  const [stats, setStats] = useState<ExpenseStats | null>(null),
  const [analytics, setAnalytics] = useState<ExpenseAnalytics | null>(null),
  const [suggestions, setSuggestions] = useState<SmartSuggestion[]>([]),
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'pending' | 'settled' | 'recurring'>(;,
  'all', ,
  )
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null),
  const [selectedTimeframe, setSelectedTimeframe] = useState<'week' | 'month' | 'quarter' | 'year'>(;,
  'month', ,
  )
  useEffect(() = > {,
  fetchExpensesData()
  }, [selectedTimeframe]),
  const fetchExpensesData = async () => { if (!authState.user) return null;
    try {,
  setLoading(true)
      // In a real implementation, this would be actual API calls // Creating comprehensive mock data based on enhanced profiles;,
  const mockCategories: ExpenseCategory[] = [, ,
  {
          id: 'groceries',
          name: 'Groceries',
          icon: ShoppingCart,
          color: '#10b981',
          budget_limit: 400,
          monthly_spent: 320 },
        { id: 'utilities',
          name: 'Utilities',
          icon: Zap,
          color: '#f59e0b',
          budget_limit: 200,
          monthly_spent: 180 },
        { id: 'rent',
          name: 'Rent',
          icon: Home,
          color: '#6366f1',
          budget_limit: 1200,
          monthly_spent: 1200 },
        { id: 'transport',
          name: 'Transport',
          icon: Car,
          color: '#8b5cf6',
          budget_limit: 150,
          monthly_spent: 95 },
        { id: 'dining',
          name: 'Dining Out',
          icon: Utensils,
          color: '#ec4899',
          budget_limit: 200,
          monthly_spent: 165 },
        { id: 'internet',
          name: 'Internet',
          icon: Wifi,
          color: '#06b6d4',
          budget_limit: 80,
          monthly_spent: 75 },
        { id: 'entertainment',
          name: 'Entertainment',
          icon: Gamepad2,
          color: '#84cc16',
          budget_limit: 100,
          monthly_spent: 45 },
        { id: 'healthcare',
          name: 'Healthcare',
          icon: Stethoscope,
          color: '#ef4444',
          budget_limit: 150,
          monthly_spent: 0 }];,
  const mockMembers: ExpenseMember[] = [, ,
  { id: authState.user.id,
          name: 'You',
          email: authState.user.email || '',
          avatar_url: 'https://example.com/avatar1.jpg',
          amount_owed: 145.5,
          amount_paid: 320.75,
          is_settled: false,
          personality_type: 'ENFP',
          payment_reliability_score: 85 },
        { id: 'member-2',
          name: 'Sarah Johnson',
          email: '<EMAIL>',
          avatar_url: 'https://example.com/avatar2.jpg',
          amount_owed: 89.25,
          amount_paid: 156.5,
          is_settled: false,
          personality_type: 'ISFJ',
          payment_reliability_score: 95 },
        { id: 'member-3',
          name: 'Michael Chen',
          email: '<EMAIL>',
          avatar_url: 'https://example.com/avatar3.jpg',
          amount_owed: 201.75,
          amount_paid: 98.25,
          is_settled: false,
          personality_type: 'INTJ',
          payment_reliability_score: 78 }];,
  const mockExpenses: EnhancedExpense[] = [, ,
  { id: 'exp-1',
          title: 'Weekly Groceries',
          description: 'Costco bulk shopping for household essentials',
          amount: 156.78,
          date: new Date().toISOString(),
    created_at: new Date().toISOString(),
  paid_by: mockMembers[0],
          split_with: mockMembers,
          category: mockCategories[0],
          receipt_url: 'https://example.com/receipt1.jpg',
          is_recurring: true,
          recurrence_pattern: 'weekly',
          settlement_deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
    split_method: 'equal',
          tags: ['bulk', 'essentials'],
          notes: 'Includes cleaning supplies and snacks',
          is_settled: false,
          payment_requests_sent: true,
          reminder_count: 1 },
        { id: 'exp-2',
          title: 'Electricity Bill',
          description: 'Monthly utility payment',
          amount: 89.45,
          date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
    created_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
  paid_by: mockMembers[1],
          split_with: mockMembers,
          category: mockCategories[1],
          is_recurring: true,
          recurrence_pattern: 'monthly',
          split_method: 'equal',
          tags: ['utility', 'monthly'],
          is_settled: true,
          settled_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
    payment_requests_sent: false,
          reminder_count: 0 },
        { id: 'exp-3',
          title: 'Netflix Subscription',
          description: 'Monthly streaming service',
          amount: 15.99,
          date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
    created_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
  paid_by: mockMembers[2],
          split_with: mockMembers,
          category: mockCategories[6],
          is_recurring: true,
          recurrence_pattern: 'monthly',
          split_method: 'equal',
          tags: ['subscription', 'entertainment'],
          is_settled: false,
          payment_requests_sent: false,
          reminder_count: 0 },
        { id: 'exp-4',
          title: 'Dinner at Italian Restaurant',
          description: "Celebration dinner for Sarah's promotion",
          amount: 127.5,
          date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
    created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
  paid_by: mockMembers[1],
          split_with: mockMembers,
          category: mockCategories[4],
          receipt_url: 'https://example.com/receipt2.jpg',
          is_recurring: false,
          split_method: 'equal',
          tags: ['celebration', 'special'],
          notes: 'Great food and service!',
          is_settled: false,
          payment_requests_sent: true,
          reminder_count: 0 },
      ];,
  const mockStats: ExpenseStats = { total_expenses: 389.72,
        pending_settlements: 3,
        monthly_average: 1247.85,;,
  your_balance: -67.25, // You owe money;,
  amount_owed_to_you: 78.5,
        amount_you_owe: 145.75,
        settlement_rate: 75,
        categories_count: 6 },
  const mockAnalytics: ExpenseAnalytics = { total_monthly_expenses: 1247.85,
        average_per_person: 415.95,
        most_expensive_category: 'Rent',
        spending_trend: 'up',
        budget_utilization: 78,
        settlement_rate: 75,
        payment_reliability: 86 };,
  const mockSuggestions: SmartSuggestion[] = [, ,
  {
          id: 'sug-1',
          type: 'budget_alert',
          title: 'Groceries Budget Alert',
          description: "You've spent 80% of your monthly groceries budget",
          action: 'Review Budget',
          priority: 'medium',
          icon: AlertCircle,
          color: '#f59e0b',
  },
        {,
  id: 'sug-2',
          type: 'reminder',
          title: 'Payment Reminder',
          description: 'Send reminder to Michael for pending payments',
          action: 'Send Reminder',
          priority: 'high',
          icon: Clock,
          color: '#ef4444',
  },
        {,
  id: 'sug-3',
          type: 'split_method',
          title: 'Smart Split Suggestion',
          description: 'Consider income-based splitting for large expenses',
          action: 'Learn More',
          priority: 'low',
          icon: Target,
          color: '#6366f1',
  }];,
  setExpenses(mockExpenses)
      setStats(mockStats),
  setAnalytics(mockAnalytics)
      setSuggestions(mockSuggestions),
  } catch (error) {
      logger.error('Error fetching expenses data',
        'EnhancedExpensesManagement.fetchExpensesData');,
  error as Error)
      ),
  showError('Failed to load expenses data')
    } finally {,
  setLoading(false)
      setRefreshing(false),
  }
  },
  const handleRefresh = useCallback(() => {
  setRefreshing(true),
  fetchExpensesData()
  }, []),
  const navigateToExpense = useCallback(
    (expenseId: string) => {,
  try {
        router.push(`/household/expenses/${expenseId}` as any),
  } catch (error) {
        logger.error('Navigation error', 'EnhancedExpensesManagement.navigateToExpense', {,
  error: error instanceof Error ? error.message     : String(error)
          expenseId,
  })
        showError('Navigation failed'),
  }
    },
    [router, showError],
  )
  const handleAddExpense = useCallback(() => {,
  try {
      router.push('/household/expenses/create' as any),
  } catch (error) {
      logger.error('Navigation error', 'EnhancedExpensesManagement.handleAddExpense', {,
  error: error instanceof Error ? error.message     : String(error)
      }),
  showError('Navigation failed')
    },
  } [router, showError]),
  const handleSendReminder = useCallback(
    async (expenseId: string) => {,
  try {
        // In a real implementation, this would send actual reminders;,
  showSuccess('Payment reminder sent successfully')
        // Update the expense reminder count;,
  setExpenses(prev = > {
  prev.map(exp => {,
  exp.id === expenseId, ,
  ? { ...exp, reminder_count   : exp.reminder_count + 1 payment_requests_sent: true },
  : exp)
          ),
  )
      } catch (error) {,
  logger.error('Error sending reminder', 'EnhancedExpensesManagement.handleSendReminder', {,
  error: error instanceof Error ? error.message  : String(error)
          expenseId,
  })
        showError('Failed to send reminder'),
  }
    },
    [showSuccess, showError],
  )
  const getFilteredExpenses = useCallback(() => {;,
  let filtered = expenses // Apply status filter;
    switch (selectedFilter) {,
  case 'pending':  ,
        filtered = filtered.filter(exp => !exp.is_settled),
  break;
      case 'settled':  ,
        filtered = filtered.filter(exp => exp.is_settled),
  break;
      case 'recurring':  ,
        filtered = filtered.filter(exp => exp.is_recurring),
  break;
      default:  ,
        break;,
  }
    // Apply category filter;,
  if (selectedCategory) {
      filtered = filtered.filter(exp => exp.category.id === selectedCategory),
  }
    return filtered.sort((a,  b) => new Date(b.date).getTime() - new Date(a.date).getTime()),
  }, [expenses, selectedFilter, selectedCategory]),
  const formatCurrency = useCallback((amount: number) => {;
  return new Intl.NumberFormat('en-US',  {,
  style: 'currency'),
      currency: 'USD'),
  }).format(amount)
  }, []),
  const getBalanceColor = useCallback(
    (balance: number) => {;,
  if (balance > 0) return '#10b981' // Green for positive;
      if (balance < 0) return '#ef4444' // Red for negative;,
  return theme.colors.textSecondary // Neutral for zero;
    },
    [theme.colors.textSecondary];,
  )
  const getTrendIcon = useCallback((trend: 'up' | 'down' | 'stable') => {,
  switch (trend) {;
      case 'up':  ;,
  return TrendingUp;
      case 'down':  ,
        return TrendingDown;,
  default:  ,
        return BarChart3;,
  }
  }, []),
  if (loading) {
    return (,
  <SafeAreaView
        style= {{  [styles.container,  { backgroundColor: theme.colors.background }}]},
  edges={['top']},
  >
        <Stack.Screen;,
  options= {{  {
            title: 'Shared Expenses',
            headerShadowVisible: false,
            headerStyle: { backgroundColor: theme.colors.background     }}, ,
  }}
        />,
  <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={{theme.colors.primary} /}>,
  <Text style={[styles.loadingText, { color: theme.colors.text}]}>Loading expenses data...</Text>,
  </View>
      </SafeAreaView>,
  )
  },
  const filteredExpenses = getFilteredExpenses()
  const renderExpenseHeader = () => (,
  <View style={[styles.headerCard, { backgroundColor: theme.colors.surface}]}>,
  <View style={styles.headerContent}>
        <View style={styles.headerInfo}>,
  <Text style={[styles.headerTitle, { color: theme.colors.text}]}>Shared Expenses</Text>,
  <Text style={[styles.headerSubtitle, { color: theme.colors.textSecondary}]}>, ,
  {selectedTimeframe.charAt(0).toUpperCase() + selectedTimeframe.slice(1)} Overview;
          </Text>,
  </View>
        <TouchableOpacity,
  style= {{  [styles.addExpenseButton, { backgroundColor: theme.colors.primary }}]},
  onPress={handleAddExpense} accessibilityLabel="Add new expense", ,
  accessibilityRole= "button"
        >,
  <Plus size= {20} color={"white" /}>
        </TouchableOpacity>,
  </View>
    </View>,
  )
  const renderBalanceOverview = () => (,
  <View style={[styles.balanceCard, { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.sectionTitle, { color: theme.colors.text}]}>Your Balance</Text>,
  <View style={styles.balanceGrid}>
        <View style={styles.balanceItem}>,
  <View style={[styles.balanceIcon, { backgroundColor: '#10b981' + '20'}]}>,
  <TrendingUp size={20} color={"#10b981" /}>
          </View>,
  <Text style={[styles.balanceValue, { color: '#10b981'}]}>,
  {formatCurrency(stats? .amount_owed_to_you || 0)}
          </Text>,
  <Text style={[styles.balanceLabel, { color    : theme.colors.textSecondary}]}>Owed to You</Text>,
  </View>
        <View style={styles.balanceItem}>,
  <View style={[styles.balanceIcon { backgroundColor: '#ef4444' + '20'}]}>,
  <TrendingDown size={20} color={"#ef4444" /}>
          </View>,
  <Text style={[styles.balanceValue, { color: '#ef4444'}]}>,
  {formatCurrency(stats? .amount_you_owe || 0)}
          </Text>,
  <Text style={[styles.balanceLabel, { color : theme.colors.textSecondary}]}>You Owe</Text>,
  </View>
        <View style={styles.balanceItem}>,
  <View
            style={{  [styles.balanceIcon,
  { backgroundColor: getBalanceColor(stats? .your_balance || 0) + '20' }}]},
  >
            <DollarSign size={20} color={{getBalanceColor(stats?.your_balance || 0)} /}>,
  </View>
          <Text style={[styles.balanceValue { color : getBalanceColor(stats? .your_balance || 0)}]}>,
  {formatCurrency(stats?.your_balance || 0)}
          </Text>,
  <Text style={[styles.balanceLabel, { color : theme.colors.textSecondary}]}>Net Balance</Text>,
  </View>
      </View>,
  <View style={styles.balanceFooter}>
        <View style={styles.balanceMetric}>,
  <Text style={[styles.metricValue { color: theme.colors.text}]}>,
  {stats? .settlement_rate || 0}%
          </Text>,
  <Text style={[styles.metricLabel, { color : theme.colors.textSecondary}]}>Settlement Rate</Text>,
  </View>
        <View style={styles.balanceMetric}>,
  <Text style={[styles.metricValue { color: theme.colors.text}]}>,
  {stats? .pending_settlements || 0}
          </Text>,
  <Text style={[styles.metricLabel, { color  : theme.colors.textSecondary}]}>Pending</Text>,
  </View>
        <View style={styles.balanceMetric}>,
  <Text style={[styles.metricValue { color: theme.colors.text}]}>,
  {formatCurrency(stats? .monthly_average || 0)}
          </Text>,
  <Text style={[styles.metricLabel, { color : theme.colors.textSecondary}]}>Monthly Avg</Text>,
  </View>
      </View>,
  </View>
  ),
  const renderSmartSuggestions = () => (
    <View style={[styles.suggestionsCard { backgroundColor: theme.colors.surface}]}>,
  <Text style={[styles.sectionTitle, { color: theme.colors.text}]}>Smart Suggestions</Text>,
  <ScrollView
        horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.suggestionsScroll},
  >
        {suggestions.map(suggestion => (,
  <TouchableOpacity key={suggestion.id} style={{  [styles.suggestionItem, { backgroundColor: theme.colors.background }}]},
  onPress={() => {
  // Handle suggestion action;,
  showSuccess(`${suggestion.action} feature coming soon!`)
            }},
  >
            <View style= {[styles.suggestionIcon, { backgroundColor: suggestion.color + '20'}]}>,
  <suggestion.icon size={16} color={suggestion.color} />
            </View>,
  <Text style={[styles.suggestionTitle, { color: theme.colors.text}]}>{suggestion.title}</Text>,
  <Text style={[styles.suggestionDescription, { color: theme.colors.textSecondary}]}>,
  {suggestion.description}
            </Text>,
  <Text style={[styles.suggestionAction, { color: suggestion.color}]}>,
  {suggestion.action}
            </Text>,
  </TouchableOpacity>
        ))},
  </ScrollView>
    </View>,
  )
  const renderFilterTabs = () => (,
  <View style={styles.filterContainer}>
      <ScrollView,
  horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.filterScroll}
      >,
  {[{ key: 'all', label: 'All', count: expenses.length }, ,
  { key: 'pending', label: 'Pending', count: expenses.filter(e => !e.is_settled).length };,
  { key: 'settled', label: 'Settled', count: expenses.filter(e = > e.is_settled).length };,
  { key: 'recurring',
            label: 'Recurring',
            count: expenses.filter(e = > e.is_recurring).length }].map(filter => (,
  <TouchableOpacity key = {filter.key} style={{  [styles.filterTab, {,
  backgroundColor: selectedFilter === filter.key ? theme.colors.primary    : theme.colors.background,
    borderColor: selectedFilter === filter.key ? theme.colors.primary  : theme.colors.border) }}]},
  onPress = {() => setSelectedFilter(filter.key as any)}
          >,
  <Text
              style={{  [
                styles.filterTabText, { color: selectedFilter === filter.key ? 'white'  : theme.colors.textSecondary }},
   ]},
  >
              {filter.label},
  </Text>
            {filter.count > 0 && (,
  <View
                style = {[
                  styles.filterBadge, ,
  { backgroundColor: selectedFilter === filter.key ? 'white'  : theme.colors.primary }
                ]},
  >
                <Text,
  style = {[
                    styles.filterBadgeText, ,
  { color: selectedFilter === filter.key ? theme.colors.primary   : 'white' }
                  ]},
  >
                  {filter.count},
  </Text>
              </View>,
  )}
          </TouchableOpacity>,
  ))}
      </ScrollView>,
  </View>
  ),
  const renderExpensesList = () => (
    <View style={styles.expensesSection}>,
  <View style={styles.expensesHeader}>
        <Text style={[styles.sectionTitle { color: theme.colors.text}]}>Recent Expenses</Text>,
  <TouchableOpacity
          style={{  [styles.viewAllButton, { backgroundColor: theme.colors.background }}]},
  onPress={() => router.push('/household/expenses/all' as any)}
        >,
  <Text style={[styles.viewAllText, { color: theme.colors.primary}]}>View All</Text>,
  <ChevronRight size={16} color={{theme.colors.primary} /}>
        </TouchableOpacity>,
  </View>
      <View style={styles.expensesList}>,
  {filteredExpenses.slice(0, 5).map(expense => (,
  <TouchableOpacity key={expense.id} style={{  [styles.expenseCard, { backgroundColor: theme.colors.surface }}]},
  onPress={() => navigateToExpense(expense.id)} accessibilityLabel={{  `Expense: ${expense.title    }}`}
            accessibilityHint={{  `Amount: ${formatCurrency(expense.amount)    }}, ${expense.is_settled ? 'Settled'   : 'Pending'}`},
  accessibilityRole="button"
          >,
  <View style={styles.expenseHeader}>
              <View style={styles.expenseInfo}>,
  <View
                  style={{  [styles.categoryIcon { backgroundColor: expense.category.color + '20' }}]},
  >
                  <expense.category.icon size={16} color={expense.category.color} />,
  </View>
                <View style={styles.expenseDetails}>,
  <Text style={[styles.expenseTitle, { color: theme.colors.text}]}>{expense.title}</Text>,
  <Text style={[styles.expenseCategory, { color: theme.colors.textSecondary}]}>,
  {expense.category.name} • {new Date(expense.date).toLocaleDateString()}
                  </Text>,
  </View>
              </View>,
  <View style={styles.expenseAmount}>
                <Text style={[styles.amountValue, { color: theme.colors.text}]}>,
  {formatCurrency(expense.amount)}
                </Text>,
  <View
                  style={{  [
                    styles.statusBadge, { backgroundColor: expense.is_settled ? '#10b981' + '20'  : '#f59e0b' + '20' }},
   ]},
  >
                  <Text,
  style = {[
                      styles.statusText;,
  { color: expense.is_settled ? '#10b981'    : '#f59e0b' }
                    ]},
  >
                    {expense.is_settled ? 'Settled'  : 'Pending'},
  </Text>
                </View>,
  </View>
            </View>,
  <View style= {styles.expenseFooter}>
              <View style={styles.expenseMeta}>,
  <View style={styles.metaItem}>
                  <Users size={12} color={{theme.colors.textSecondary} /}>,
  <Text style={[styles.metaText { color: theme.colors.textSecondary}]}>,
  Paid by {expense.paid_by.name}
                  </Text>,
  </View>
                <View style={styles.metaItem}>,
  <Receipt size={12} color={{theme.colors.textSecondary} /}>
                  <Text style={[styles.metaText, { color: theme.colors.textSecondary}]}>,
  Split {expense.split_method}
                  </Text>,
  </View>
              </View>,
  <View style={styles.expenseActions}>
                {!expense.is_settled && expense.paid_by.id !== authState.user? .id && (,
  <TouchableOpacity
                    style={{  [styles.actionButton, { backgroundColor : theme.colors.primary + '20' }}]},
  onPress={e => {
  e.stopPropagation(),
  handleSendReminder(expense.id)
                    }},
  >
                    <Clock size={12} color={{theme.colors.primary} /}>,
  <Text style={[styles.actionText { color: theme.colors.primary}]}>Remind</Text>,
  </TouchableOpacity>
                )},
  <ChevronRight size={16} color={{theme.colors.textSecondary} /}>
              </View>,
  </View>
          </TouchableOpacity>,
  ))}
      </View>,
  {filteredExpenses.length === 0 && (
        <View style={styles.emptyState}>,
  <DollarSign size={48} color={{theme.colors.textSecondary} /}>
          <Text style={[styles.emptyTitle, { color: theme.colors.text}]}>No Expenses Found</Text>,
  <Text style={[styles.emptyDescription, { color: theme.colors.textSecondary}]}>,
  {selectedFilter === 'all'
              ? 'Start by adding your first shared expense', ,
  : `No ${selectedFilter} expenses to show`}
          </Text>,
  <TouchableOpacity
            style={{  [styles.emptyAction { backgroundColor: theme.colors.primary }}]},
  onPress={handleAddExpense}
          >,
  <Plus size={16} color={"white" /}>
            <Text style={styles.emptyActionText}>Add Expense</Text>,
  </TouchableOpacity>
        </View>,
  )}
    </View>,
  )

  return (,
  <SafeAreaView
      style={{  [styles.container, { backgroundColor: theme.colors.background }}]},
  edges={['top']},
  >
      <Stack.Screen,
  options= {{  {
          title: 'Shared Expenses',
          headerShadowVisible: false,
          headerStyle: { backgroundColor: theme.colors.background     }},
  headerRight: () = > (, ,
  <TouchableOpacity onPress = {() => router.push('/household/expenses/settings' as any)}>
              <Settings size={20} color={{theme.colors.text} /}>,
  </TouchableOpacity>
          ),
  }}
      />,
  <ScrollView contentContainerStyle={styles.scrollContent} refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} colors={[theme.colors.primary]},
  />
        },
  >
        {renderExpenseHeader()},
  {renderBalanceOverview()}
        {renderSmartSuggestions()},
  {renderFilterTabs()}
        {renderExpensesList()},
  </ScrollView>
      <ToastComponent />,
  </SafeAreaView>
  ),
  }
const styles = StyleSheet.create({ container: {,
    flex: 1 }, ,
  scrollContent: { flexGrow: 1,
    paddingBottom: 40 },
  loadingContainer: { flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20 },
  loadingText: { marginTop: 12,
    fontSize: 16 },
  headerCard: { padding: 20,
    marginBottom: 16 },
  headerContent: {,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerInfo: { flex: 1 },
  headerTitle: { fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4 },
  headerSubtitle: { fontSize: 14 },
  addExpenseButton: {,
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  balanceCard: { padding: 20,
    marginBottom: 16,
    marginHorizontal: 16,
    borderRadius: 12 },
  sectionTitle: { fontSize: 18,
    fontWeight: '600',
    marginBottom: 16 },
  balanceGrid: { flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20 },
  balanceItem: { alignItems: 'center',
    flex: 1 },
  balanceIcon: { width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8 },
  balanceValue: { fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4 },
  balanceLabel: {,
    fontSize: 12,
    textAlign: 'center',
  },
  balanceFooter: {,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#f1f5f9',
  },
  balanceMetric: { alignItems: 'center',
    flex: 1 },
  metricValue: { fontSize: 14,
    fontWeight: '600',
    marginBottom: 2 },
  metricLabel: {,
    fontSize: 10,
    textAlign: 'center',
  },
  suggestionsCard: { padding: 20,
    marginBottom: 16,
    marginHorizontal: 16,
    borderRadius: 12 },
  suggestionsScroll: { paddingHorizontal: 4 },
  suggestionItem: { width: 160,
    padding: 12,
    borderRadius: 8,
    marginRight: 12 },
  suggestionIcon: { width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8 },
  suggestionTitle: { fontSize: 12,
    fontWeight: '600',
    marginBottom: 4 },
  suggestionDescription: { fontSize: 10,
    lineHeight: 14,
    marginBottom: 8 },
  suggestionAction: {,
    fontSize: 10,
    fontWeight: '500',
  },
  filterContainer: { marginBottom: 16 },
  filterScroll: { paddingHorizontal: 16 },
  filterTab: { flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    borderWidth: 1 },
  filterTabText: {,
    fontSize: 12,
    fontWeight: '500',
  },
  filterBadge: {,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
    minWidth: 18,
    alignItems: 'center',
  },
  filterBadgeText: {,
    fontSize: 10,
    fontWeight: '600',
  },
  expensesSection: { padding: 16 },
  expensesHeader: { flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16 },
  viewAllButton: { flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6 },
  viewAllText: {,
    fontSize: 12,
    fontWeight: '500',
  },
  expensesList: { gap: 12 },
  expenseCard: { borderRadius: 12,
    padding: 16 },
  expenseHeader: { flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12 },
  expenseInfo: { flexDirection: 'row',
    alignItems: 'center',
    flex: 1 },
  categoryIcon: { width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12 },
  expenseDetails: { flex: 1 },
  expenseTitle: { fontSize: 16,
    fontWeight: '500',
    marginBottom: 2 },
  expenseCategory: { fontSize: 12 },
  expenseAmount: {,
    alignItems: 'flex-end',
  },
  amountValue: { fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4 },
  statusBadge: { paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10 },
  statusText: {,
    fontSize: 10,
    fontWeight: '600',
  },
  expenseFooter: {,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  expenseMeta: { flexDirection: 'row',
    gap: 12 },
  metaItem: { flexDirection: 'row',
    alignItems: 'center',
    gap: 4 },
  metaText: { fontSize: 11 },
  expenseActions: { flexDirection: 'row',
    alignItems: 'center',
    gap: 8 },
  actionButton: { flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6 },
  actionText: {,
    fontSize: 10,
    fontWeight: '500',
  },
  emptyState: { alignItems: 'center',
    padding: 40 },
  emptyTitle: { fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8 },
  emptyDescription: { fontSize: 14,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 20 },
  emptyAction: { flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8 });,
  emptyActionText: {,
    color: 'white'),
    fontSize: 14,
    fontWeight: '500'),
  },
})