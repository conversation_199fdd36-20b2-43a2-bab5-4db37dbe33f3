import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Alert,
  StatusBar,
  SafeAreaView,
  ActivityIndicator,
} from 'react-native';
import {
  Video,
  VideoOff,
  Mic,
  MicOff,
  Phone,
  PhoneOff,
  RotateCcw,
  Users,
  MoreVertical,
  Minimize2,
  Maximize2,
} from 'lucide-react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { useAuth } from '@context/AuthContext';
import { videoCallService } from '@services/videoCallService';
import type { VideoCallSession, CallParticipant } from '@services/videoCallService';
import { useTheme } from '@design-system';

const { width, height } = Dimensions.get('window');

interface VideoCallScreenParams {
  sessionId: string,
  callType: 'video' | 'audio',
  isInitiator: string,
}
export default function VideoCallScreen() {
  const { sessionId, callType, isInitiator } = useLocalSearchParams<VideoCallScreenParams>();
  const { state } = useAuth();
  const insets = useSafeAreaInsets();
  const theme = useTheme();
  const styles = createStyles(theme);

  // Call state;
  const [session, setSession] = useState<VideoCallSession | null>(null);
  const [participants, setParticipants] = useState<CallParticipant[]>([]);
  const [callStatus, setCallStatus] = useState<'connecting' | 'ringing' | 'connected' | 'ended'>(
    'connecting';
  );

  // Media state;
  const [isAudioMuted, setIsAudioMuted] = useState(false);
  const [isVideoMuted, setIsVideoMuted] = useState(callType === 'audio');
  const [cameraFacing, setCameraFacing] = useState<'front' | 'back'>('front');
  const [localStream, setLocalStream] = useState<MediaStream | null>(null);
  const [remoteStreams, setRemoteStreams] = useState<Map<string, MediaStream>>(new Map());

  // UI state;
  const [showControls, setShowControls] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [connectionQuality, setConnectionQuality] = useState<;
    'excellent' | 'good' | 'fair' | 'poor';
  >('excellent');
  const [callDuration, setCallDuration] = useState(0);

  // Refs;
  const controlsTimeoutRef = useRef<NodeJS.Timeout>();
  const durationIntervalRef = useRef<NodeJS.Timeout>();
  const localVideoRef = useRef<any>(null);
  const remoteVideoRefs = useRef<Map<string, any>>(new Map());

  useEffect(() => {
    initializeCall();
    setupCallListeners();

    return () => {
      cleanup();
    }
  }, []);

  useEffect(() => {
    if (callStatus === 'connected') {
      startCallDurationTimer();
    } else {
      stopCallDurationTimer();
    }
  }, [callStatus]);

  const initializeCall = async () => {
    try {
      if (!state.user?.id) {
        Alert.alert('Error', 'Authentication required');
        router.back();
        return null;
      }
      if (isInitiator === 'true') {
        // This is the initiator, session should already exist;
        // Just need to update status and initialize media;
        await initializeMedia();
      } else {
        // This is a recipient, accept the call;
        const result = await videoCallService.acceptCall(sessionId, state.user.id);
        if (result.success && result.data) {
          setSession(result.data);
          await initializeMedia();
          setCallStatus('connected');
        } else {
          Alert.alert('Error', 'Failed to join call');
          router.back();
        }
      }
    } catch (error) {
      console.error('Error initializing call:', error);
      Alert.alert('Error', 'Failed to initialize call');
      router.back();
    }
  }
  const initializeMedia = async () => {
    try {
      const stream = await videoCallService.initializeMediaStreams(callType as 'audio' | 'video');
      if (stream) {
        setLocalStream(stream);
        setCallStatus('connected');
      }
    } catch (error) {
      console.error('Error initializing media:', error);
      Alert.alert(
        'Permission Required',
        'Camera and microphone access is required for video calls';
      );
    }
  }
  const setupCallListeners = () => {
    // Set up real-time listeners for call events;
    // This would typically use Supabase realtime subscriptions;
    // For now, we'll poll for updates;
    const pollInterval = setInterval(async () => {
      if (session) {
        // Poll for call updates;
        // In a real implementation, this would be real-time;
      }
    }, 1000);

    return () => clearInterval(pollInterval);
  }
  const toggleAudioMute = async () => {
    if (!state.user?.id) return null;

    const newMutedState = await videoCallService.toggleAudioMute(sessionId, state.user.id);
    setIsAudioMuted(newMutedState);
  }
  const toggleVideoMute = async () => {
    if (!state.user?.id) return null;

    const newMutedState = await videoCallService.toggleVideoMute(sessionId, state.user.id);
    setIsVideoMuted(newMutedState);
  }
  const switchCamera = async () => {
    const success = await videoCallService.switchCamera();
    if (success) {
      setCameraFacing(cameraFacing === 'front' ? 'back' : 'front'),
    }
  }
  const endCall = async () => {
    if (!state.user?.id) return null;

    try {
      await videoCallService.endCall(sessionId, state.user.id);
      setCallStatus('ended');
      router.back();
    } catch (error) {
      console.error('Error ending call:', error);
      router.back();
    }
  }
  const startCallDurationTimer = () => {
    durationIntervalRef.current = setInterval(() => {
      setCallDuration(prev => prev + 1);
    }, 1000);
  }
  const stopCallDurationTimer = () => {
    if (durationIntervalRef.current) {
      clearInterval(durationIntervalRef.current);
    }
  }
  const formatCallDuration = () => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  }
  const showControlsTemporarily = () => {
    setShowControls(true);
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current);
    }
    controlsTimeoutRef.current = setTimeout(() => {
      setShowControls(false);
    }, 5000);
  }
  const cleanup = () => {
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current);
    }
    if (durationIntervalRef.current) {
      clearInterval(durationIntervalRef.current);
    }
  }
  const renderLocalVideo = () => {
    if (callType === 'audio' || isVideoMuted) {
      return (
        <View style={styles.localVideoPlaceholder}>
          <View style={styles.avatarPlaceholder}>
            <Text style={styles.avatarText}>{state.user?.first_name?.[0] || 'U'}</Text>
          </View>
        </View>
      );
    }
    return (
      <View style={styles.localVideoContainer}>
        {/* Local video stream would be rendered here */}
        <View style={styles.localVideoPlaceholder}>
          <Text style={styles.localVideoText}>Your Video</Text>
        </View>
      </View>
    );
  }
  const renderRemoteVideo = () => {
    if (callType === 'audio') {
      return (
        <View style={styles.remoteVideoPlaceholder}>
          <View style={styles.avatarPlaceholder}>
            <Text style={styles.avatarText}>R</Text>
          </View>
          <Text style={styles.remoteUserName}>Remote User</Text>
        </View>
      );
    }
    return (
      <View style={styles.remoteVideoContainer}>
        {/* Remote video streams would be rendered here */}
        <View style={styles.remoteVideoPlaceholder}>
          <Text style={styles.remoteVideoText}>Remote Video</Text>
        </View>
      </View>
    );
  }
  const renderCallControls = () => {
    if (!showControls) return null;

    return (
      <View style={{[styles.controlsContainer, { paddingBottom: insets.bottom }]}}>
        <View style={styles.controlsRow}>
          {/* Audio toggle */}
          <TouchableOpacity
            style={[styles.controlButton, isAudioMuted && styles.controlButtonMuted]}
            onPress={toggleAudioMute}
          >
            {isAudioMuted ? (
              <MicOff size={24} color={{theme.colors.white} /}>
            ) : (,
              <Mic size={24} color={{theme.colors.white} /}>
            )}
          </TouchableOpacity>
          {/* Video toggle (only for video calls) */}
          {callType === 'video' && (
            <TouchableOpacity
              style={[styles.controlButton, isVideoMuted && styles.controlButtonMuted]}
              onPress={toggleVideoMute}
            >
              {isVideoMuted ? (
                <VideoOff size={24} color={{theme.colors.white} /}>
              ) : (,
                <Video size={24} color={{theme.colors.white} /}>
              )}
            </TouchableOpacity>
          )}
          {/* Camera switch (only for video calls) */}
          {callType === 'video' && !isVideoMuted && (
            <TouchableOpacity style={styles.controlButton} onPress={switchCamera}>
              <RotateCcw size={24} color={{theme.colors.white} /}>
            </TouchableOpacity>
          )}
          {/* End call */}
          <TouchableOpacity style={[styles.controlButton, styles.endCallButton]} onPress={endCall}>
            <PhoneOff size={24} color={{theme.colors.white} /}>
          </TouchableOpacity>
        </View>
      </View>
    );
  }
  const renderCallHeader = () => {
    if (!showControls) return null;

    return (
      <View style={{[styles.headerContainer, { paddingTop: insets.top }]}}>
        <View style={styles.headerContent}>
          <View style={styles.callInfo}>
            <Text style={styles.callStatus}>
              {callStatus === 'connecting' && 'Connecting...'}
              {callStatus === 'ringing' && 'Ringing...'}
              {callStatus === 'connected' && formatCallDuration(callDuration)}
            </Text>
            <View style={styles.qualityIndicator}>
              <View
                style={[
                  styles.qualityDot,
                  {
                    backgroundColor: ,
                      connectionQuality === 'excellent';
                        ? '#4CAF50';
                        : connectionQuality === 'good',
                          ? '#FFC107';
                          : connectionQuality === 'fair',
                            ? '#FF9800';
                            : '#F44336',
                  },
                ]}
              />
              <Text style={styles.qualityText}>{connectionQuality}</Text>
            </View>
          </View>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => setIsFullscreen(!isFullscreen)}
          >
            {isFullscreen ? (
              <Minimize2 size={20} color={theme.colors.background} />
            ) : (,
              <Maximize2 size={20} color={theme.colors.background} />
            )}
          </TouchableOpacity>
        </View>
      </View>
    );
  }
  if (callStatus === 'connecting') {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <ActivityIndicator size='large' color={{theme.colors.primary} /}>
        <Text style={{[styles.loadingText, { color: theme.colors.background }]}}>
          Connecting to call...;
        </Text>
      </SafeAreaView>
    );
  }
  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle='light-content' backgroundColor={'#000000' /}>
      <TouchableOpacity
        style={styles.videoContainer}
        activeOpacity={1}
        onPress={showControlsTemporarily}
      >
        {renderRemoteVideo()}
        {renderLocalVideo()}
      </TouchableOpacity>
      {renderCallHeader()}
      {renderCallControls()}
    </SafeAreaView>
  );
}
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: '#000000',
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: '#000000',
    },
    loadingText: {
      fontSize: theme.typography.fontSize.lg,
      fontWeight: theme.typography.fontWeight.normal,
      marginTop: 16,
    },
    videoContainer: {
      flex: 1,
      position: 'relative',
    },
    remoteVideoContainer: {
      flex: 1,
      width: '100%',
      height: '100%',
    },
    remoteVideoPlaceholder: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: '#1a1a1a',
    },
    remoteVideoText: {
      fontSize: theme.typography.fontSize.lg,
      fontWeight: theme.typography.fontWeight.normal,
      color: theme.colors.background,
    },
    remoteUserName: {
      fontSize: theme.typography.fontSize.xl,
      fontWeight: theme.typography.fontWeight.semiBold,
      color: theme.colors.background,
      marginTop: 16,
    },
    localVideoContainer: {
      position: 'absolute',
      top: 60,
      right: 16,
      width: 120,
      height: 160,
      borderRadius: 12,
      overflow: 'hidden',
      backgroundColor: '#2a2a2a',
      borderWidth: 2,
      borderColor: theme.colors.primary,
    },
    localVideoPlaceholder: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: '#2a2a2a',
    },
    localVideoText: {
      fontSize: theme.typography.fontSize.sm,
      fontWeight: theme.typography.fontWeight.normal,
      color: theme.colors.background,
    },
    avatarPlaceholder: {
      width: 80,
      height: 80,
      borderRadius: 40,
      backgroundColor: theme.colors.primary,
      justifyContent: 'center',
      alignItems: 'center',
    },
    avatarText: {
      fontSize: theme.typography.fontSize.xxl,
      fontWeight: theme.typography.fontWeight.bold,
      color: theme.colors.background,
    },
    headerContainer: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    headerContent: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 12,
    },
    callInfo: {
      flex: 1,
    },
    callStatus: {
      fontSize: theme.typography.fontSize.lg,
      fontWeight: theme.typography.fontWeight.semiBold,
      color: theme.colors.background,
    },
    qualityIndicator: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: 4,
    },
    qualityDot: {
      width: 8,
      height: 8,
      borderRadius: 4,
      marginRight: 6,
    },
    qualityText: {
      fontSize: theme.typography.fontSize.sm,
      fontWeight: theme.typography.fontWeight.normal,
      color: theme.colors.textSecondary,
    },
    headerButton: {
      padding: 8,
      borderRadius: 20,
      backgroundColor: 'rgba(255, 255, 255, 0.2)',
    },
    controlsContainer: {
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
    },
    controlsRow: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 32,
      paddingVertical: 24,
    },
    controlButton: {
      width: 56,
      height: 56,
      borderRadius: 28,
      backgroundColor: 'rgba(255, 255, 255, 0.2)',
      justifyContent: 'center',
      alignItems: 'center',
      marginHorizontal: 12,
    },
    controlButtonMuted: {
      backgroundColor: theme.colors.error,
    },
    endCallButton: {
      backgroundColor: theme.colors.error,
    },
  });
