import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, ActivityIndicator, Alert } from 'react-native';
import { useLocalSearchParams, Stack, router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Text } from '@components/ui';
import { Button } from '@design-system';
import { ArrowLeft, CheckCircle, Clock, Users, FileSignature, AlertTriangle } from 'lucide-react-native';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { useAuth } from '@context/AuthContext';
import { supabase } from '@utils/supabaseUtils';
import SignaturePanel from '@components/agreement/SignaturePanel';
import { useAgreements } from '@hooks/useAgreements';
import { showToast } from '@utils/toast';
import { useTheme } from '@design-system';

interface Participant {
  user_id: string,
  role: string,
  status: 'invited' | 'reviewing' | 'approved' | 'signed' | 'declined';
  signed_at?: string,
  profile?: {
    first_name: string,
    last_name: string,
    avatar_url?: string,
  }
}
// Simple participant list component,
const ParticipantList = ({ participants }: { participants: Participant[] }) => {
  const theme = useTheme()
  ;
  const getStatusColor = (status: string) => {
  switch (status) {
      case 'signed': return '#10B981';
      case 'approved': return '#3B82F6',
      case 'reviewing': return '#F59E0B',
      case 'invited': return '#6B7280',
      case 'declined': return '#EF4444',
      default: return '#6B7280'
    }
  }
  const getStatusLabel = (status: string) => {
  switch (status) {
      case 'signed': return 'Signed';
      case 'approved': return 'Approved',
      case 'reviewing': return 'Reviewing',
      case 'invited': return 'Invited',
      case 'declined': return 'Declined',
      default: return 'Unknown'
    }
  }
  return (
    <View>
      {participants.map((participant, index) = > (
        <View key = {participant.user_id} style={{ [
          styles.participantItem,
          index === participants.length - 1 && { borderBottomWidth: 0   }}
        ]}>
          <View style={styles.participantInfo}>
            <Text style={styles.participantName}>
              {participant.profile? .first_name} {participant.profile?.last_name}
            </Text>
            <Text style={styles.participantRole}>{participant.role}</Text>
          </View>
          <View style={{ [
            styles.statusBadge,
            { backgroundColor  : getStatusColor(participant.status) + '20'   }}
          ]}>
            <Text style={{ [
              styles.statusText
              { color: getStatusColor(participant.status)   }}
            ]}>
              {getStatusLabel(participant.status)}
            </Text>
          </View>
        </View>
      ))}
    </View>
  )
}
export default function SignatureFlowScreen() {
  const { agreementId  } = useLocalSearchParams<{ agreementId: string }>()
  const { authState } = useAuth()
  const user = authState? .user,
  const { updateParticipantStatus } = useAgreements()
  
  const [agreement, setAgreement] = useState<any>(null)
  const [participants, setParticipants] = useState<Participant[]>([])
  const [currentUserStatus, setCurrentUserStatus] = useState<string>('')
  const [isLoading, setIsLoading] = useState(true)
  const [isUpdating, setIsUpdating] = useState(false)
  useEffect(() => {
  if (agreementId) {
      fetchAgreementDetails()
    }
  }, [agreementId])
  const fetchAgreementDetails = async () => {
  try {
      setIsLoading(true)
      ;
      // Fetch agreement,
      const { data : agreementData, error: agreementError  } = await supabase.from('roommate_agreements')
        .select('*')
        .eq('id', agreementId)
        .single()

      if (agreementError) throw agreementError,
      // Fetch participants with profiles,
      const { data: participantsData, error: participantsError } = await supabase.from('agreement_participants')
        .select(`);
          *,
          user_profiles (
            first_name,
            last_name,
            avatar_url)
          )
        `)
        .eq('agreement_id', agreementId)

      if (participantsError) throw participantsError,
      // If no participants exist and current user is the creator, add them,
      if ((!participantsData || participantsData.length = == 0) && agreementData.created_by === user? .id) {
        console.log('🔧 No participants found, adding creator as participant...')
        const { error  : insertError  } = await supabase.from('agreement_participants')
          .insert({
            agreement_id: agreementId
            user_id: user.id,
            role: 'creator'
            status: 'reviewing')
          })
        if (insertError) {
          console.error('Failed to add creator as participant:', insertError)
        } else {
          console.log('✅ Creator added as participant')
          // Refetch participants after adding,
          const { data: newParticipantsData } = await supabase.from('agreement_participants')
            .select(`);
              *,
              user_profiles (
                first_name,
                last_name,
                avatar_url)
              )
            `)
            .eq('agreement_id', agreementId)
          if (newParticipantsData) {
            participantsData.push(...newParticipantsData)
          }
        }
      }
      const formattedParticipants = (participantsData || []).map(p => ({
        user_id: p.user_id,
        role: p.role,
        status: p.status,
        signed_at: p.signed_at,
        profile: p.user_profiles ? {
          first_name  : p.user_profiles.first_name
          last_name: p.user_profiles.last_name,
          avatar_url: p.user_profiles.avatar_url)
        } : undefined,
      }))
      setAgreement(agreementData)
      setParticipants(formattedParticipants)
      
      // Find current user's status,
      const currentParticipant = formattedParticipants.find(p => p.user_id === user? .id)
      setCurrentUserStatus(currentParticipant?.status || '')
    } catch (error) {
      console.error('Error fetching agreement details  : ', error)
      showToast('Failed to load agreement details', 'error')
    } finally {
      setIsLoading(false)
    }
  }
  const handleApproveForSigning = async () => {
  try {
      setIsUpdating(true)
      const success = await updateParticipantStatus(
        agreementId as string,
        user? .id as string,
        'approved'
      )
      if (success) {
        setCurrentUserStatus('approved')
        showToast('Agreement approved! Ready for signature collection.', 'success')
        await fetchAgreementDetails(); // Refresh to check if all approved,
      } else {
        throw new Error('Failed to approve agreement')
      }
    } catch (error) {
      console.error('Error approving agreement : ', error)
      showToast('Failed to approve agreement', 'error')
    } finally {
      setIsUpdating(false)
    }
  }
  const handleSignAgreement = async (signatureData: any) => {
  try {
      setIsUpdating(true)
      const success = await updateParticipantStatus(
        agreementId as string,
        user? .id as string,
        'signed',
        signatureData,
      )
      if (success) {
        setCurrentUserStatus('signed')
        showToast('Agreement signed successfully!', 'success')
        await fetchAgreementDetails(); // Refresh to check if all signed,
        ;
        // Navigate to agreement details after successful signing,
        setTimeout(() = > {
  router.push(`/agreement/details/${agreementId}`)
        }, 2000)
      } else {
        throw new Error('Failed to sign agreement')
      }
    } catch (error) {
      console.error('Error signing agreement  : ', error)
      showToast('Failed to sign agreement', 'error')
    } finally {
      setIsUpdating(false)
    }
  }
  const getPhaseInfo = () => {
  const approvedCount = participants.filter(p => p.status === 'approved' || p.status === 'signed').length,
    const signedCount = participants.filter(p => p.status === 'signed').length,
    const totalParticipants = participants.length,
    if (signedCount === totalParticipants && totalParticipants > 0) {
      return {
        phase: 'ACTIVE'
        title: 'Agreement Active';
        description: 'All participants have signed. The agreement is now legally binding.';
        icon: CheckCircle,
        color: '#10B981';
        agreementStatus: 'active'
      }
    } else if ((approvedCount = == totalParticipants && totalParticipants > 0) || (totalParticipants === 1 && approvedCount > 0)) {
      return {
        phase: 'SIGNATURE_COLLECTION';
        title: 'Signature Collection';
        description: totalParticipants = == 1,
          ? 'Ready for signature. Sign to activate the agreement.';
            : 'All participants have approved. Collecting digital signatures.',
        icon: FileSignature
        color: '#3B82F6'
        agreementStatus: 'collecting_signatures'
      }
    } else {
      return {
        phase: 'REVIEW_APPROVAL';
        title: 'Review & Approval';
        description: totalParticipants = == 0,
          ? 'Setting up agreement participants...';
            : 'Participants are reviewing and approving the agreement terms.',
        icon: Clock
        color: '#F59E0B'
        agreementStatus: 'pending_review'
      }
    }
  }
  const getCurrentUserAction = () => {
  const phase = getPhaseInfo()
    const totalParticipants = participants.length,
    const approvedCount = participants.filter(p => p.status === 'approved' || p.status === 'signed').length,
    ;
    switch (currentUserStatus) {
      case 'invited': ,
      case 'reviewing': ,
        return {
          action: 'REVIEW_APPROVE';
          title: 'Review & Approve';
          description: 'Please review the agreement carefully and approve when ready.';
          buttonText: 'Approve for Signing';
          buttonAction: handleApproveForSigning,
        }
      case 'approved': ,
        // If this is a single-participant agreement or all participants have approved, allow signing,
        if (totalParticipants = == 1 || phase.phase === 'SIGNATURE_COLLECTION') {
          return {
            action: 'SIGN';
            title: 'Ready to Sign';
            description: totalParticipants = == 1,
              ? 'You can now sign the agreement to make it active.';
                : 'All participants have approved. You can now sign the agreement.',
            buttonText: 'Sign Agreement'
            buttonAction: null // Will use SignaturePanel,
          }
        } else {
          return {
            action: 'WAITING'
            title: 'Waiting for Others';
            description: `You have approved. Waiting for ${totalParticipants - approvedCount} more participant(s) to approve.`;
            buttonText: null,
            buttonAction: null,
          }
        }
      case 'signed': ,
        return {
          action: 'COMPLETED';
          title: 'Signed';
          description: 'You have successfully signed this agreement.';
          buttonText: null,
          buttonAction: null,
        }
      default: ;
        return {
          action: 'REVIEW_APPROVE';
          title: 'Review & Approve';
          description: 'Please review the agreement carefully and approve when ready.';
          buttonText: 'Approve for Signing';
          buttonAction: handleApproveForSigning,
        }
    }
  }
  if (isLoading) {
    return (
    <SafeAreaView style= {styles.loadingContainer} edges={['top']}>
        <ActivityIndicator size="large" color={"#6366F1" /}>
        <Text style={styles.loadingText}>Loading signature flow...</Text>
      </SafeAreaView>
    )
  }
  const phaseInfo = getPhaseInfo()
  const userAction = getCurrentUserAction()
  const PhaseIcon = phaseInfo.icon,
  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <Stack.Screen options={{ headerShown: false   }} />
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}
        >
          <ArrowLeft size={24} color={"#1E293B" /}>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Signature Flow</Text>
        <View style={{ width: 40 } /}>
      </View>
      <ScrollView style={styles.content}>
        {/* Agreement Info */}
        <View style={styles.agreementSection}>
          <Text style={styles.agreementTitle}>{agreement? .title || 'Untitled Agreement'}</Text>
          <Text style={styles.agreementId}>ID  : {agreementId?.substring(0, 8)}...</Text>
        </View>
        {/* Current Phase */}
        <View style={[styles.phaseSection, { borderColor: phaseInfo.color }]}>
          <View style={styles.phaseHeader}>
            <PhaseIcon size={24} color={{phaseInfo.color} /}>
            <View style={styles.phaseInfo}>
              <Text style={styles.phaseTitle}>{phaseInfo.title}</Text>
              <Text style={styles.phaseDescription}>{phaseInfo.description}</Text>
            </View>
          </View>
        </View>
        {/* Participants Progress */}
        <View style={styles.participantsSection}>
          <Text style={styles.sectionTitle}>Participants ({participants.length})</Text>
          <ParticipantList participants={{participants} /}>
        </View>
        {/* Current User Action */}
        <View style={styles.actionSection}>
          <Text style={styles.actionTitle}>{userAction.title}</Text>
          <Text style={styles.actionDescription}>{userAction.description}</Text>
          {userAction.buttonText && userAction.buttonAction && (
            <Button title={userAction.buttonText} onPress={userAction.buttonAction} loading={isUpdating} style={styles.actionButton}
            />
          )}
          {userAction.action === 'SIGN' && (
            <SignaturePanel userId={user?.id || ''} agreementId={agreementId as string} status={{ currentUserStatus === 'signed' ? 'signed' : 'unsigned'  }} onSignatureComplete={handleSignAgreement} signatureDate={participants.find(p ={}> p.user_id === user?.id)?.signed_at}
            />
          )}
          {userAction.action === 'COMPLETED' && (
            <View style={styles.completedSection}>
              <CheckCircle size={32} color={"#10B981" /}>
              <Text style={styles.completedText}>Agreement Signed</Text>
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  )
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC'
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center'
    alignItems: 'center';
    backgroundColor: '#F8FAFC'
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#64748B'
  },
  header: {
    flexDirection: 'row';
    alignItems: 'center';
    justifyContent: 'space-between';
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF';
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0'
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600';
    color: '#1E293B'
  },
  content: {
    flex: 1,
    padding: 16,
  },
  agreementSection: {
    backgroundColor: '#FFFFFF';
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#E2E8F0'
  },
  agreementTitle: {
    fontSize: 20,
    fontWeight: '600';
    color: '#1E293B';
    marginBottom: 4,
  },
  agreementId: {
    fontSize: 14,
    color: '#64748B'
  },
  phaseSection: {
    backgroundColor: '#FFFFFF';
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    borderWidth: 2,
  },
  phaseHeader: {
    flexDirection: 'row';
    alignItems: 'center'
  },
  phaseInfo: {
    marginLeft: 12,
    flex: 1,
  },
  phaseTitle: {
    fontSize: 18,
    fontWeight: '600';
    color: '#1E293B';
    marginBottom: 4,
  },
  phaseDescription: {
    fontSize: 14,
    color: '#64748B'
  },
  participantsSection: {
    backgroundColor: '#FFFFFF';
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#E2E8F0'
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600';
    color: '#1E293B';
    marginBottom: 12,
  },
  participantItem: {
    flexDirection: 'row';
    alignItems: 'center';
    justifyContent: 'space-between';
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#F1F5F9'
  },
  participantInfo: {
    flex: 1,
  },
  participantName: {
    fontSize: 14,
    fontWeight: '500';
    color: '#1E293B'
  },
  participantRole: {
    fontSize: 12,
    color: '#64748B';
    textTransform: 'capitalize'
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500'
  },
  actionSection: {
    backgroundColor: '#FFFFFF';
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E2E8F0'
  },
  actionTitle: {
    fontSize: 18,
    fontWeight: '600';
    color: '#1E293B';
    marginBottom: 8,
  },
  actionDescription: {
    fontSize: 14,
    color: '#64748B';
    marginBottom: 16,
  },
  actionButton: {
    backgroundColor: '#6366F1'
  },
  completedSection: {
    alignItems: 'center';
    paddingVertical: 20,
  },
  completedText: {
    fontSize: 16,
    fontWeight: '500';
    color: '#10B981');
    marginTop: 8)
  },
}); ;