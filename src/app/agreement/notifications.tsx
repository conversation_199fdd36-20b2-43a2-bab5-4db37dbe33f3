import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Stack } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import AgreementNotifications from '@components/agreement/AgreementNotifications';

export default function NotificationsScreen() {
  return (
    <SafeAreaView style= {styles.container} edges={['top']}>
      <Stack.Screen,
        options={{ title: 'Agreement Notifications',
          headerTitleStyle: styles.headerTitle,
          }}
      />
      <AgreementNotifications />
    </SafeAreaView>
  )
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC'
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600');
    color: '#1E293B')
  },
})