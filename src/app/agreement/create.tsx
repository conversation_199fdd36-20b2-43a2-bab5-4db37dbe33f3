import React, { useState, useEffect } from 'react';,
  import {
   View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator ,
  } from 'react-native';
import {,
  SafeAreaView 
} from 'react-native-safe-area-context';,
  import {
   Stack, useRouter, useLocalSearchParams ,
  } from 'expo-router';
import {,
  useToast 
} from '@hooks/useToast' // Changed path for supabase client;,
  import {
   supabase ,
  } from '@utils/supabaseUtils';
import {,
  useAuth 
} from '@hooks/useAuth';,
  import {
   Feather ,
  } from '@expo/vector-icons' // Simple type definition for templates;
interface AgreementTemplate { id: string, name: string; description?: string; is_active: boolean },
  // Placeholder function for templates until actual implementation;
const getAllTemplates = ($2) => { return [{ id: 'template1', name: 'Standard Roommate Agreement', is_active: true }, { id: 'template2', name: 'College Dormitory Agreement', is_active: true }, { id: 'template3', name: 'Short-Term Rental Agreement', is_active: true }];,
  }
// Simple AgreementTemplateCard component;,
  const AgreementTemplateCard = ({ template, isSelected, onSelect, ,
  }: { template: AgreementTemplate, isSelected: boolean, onSelect: () => void }) => ( <TouchableOpacity style={{  [{ padding: 16, borderRadius: 8, backgroundColor: '#FFFFFF', marginBottom: 12, borderWidth: 2, borderColor: isSelected ? '#6366F1'     : '#E5E7EB' }} isSelected && { backgroundColor: '#EEF2FF', shadowColor: '#6366F1', shadowOffset: { width: 0, height: 2 }, shadowOpacity: 0.1, shadowRadius: 4, elevation: 3 }]} onPress={ () => { console.log('🎯 Template card pressed:', template.id, template.name) onSelect() }} > <View style={{  [ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' ] }}> <View style={{  [ flex: 1 ] }}> <Text style={{  [ fontSize: 16, fontWeight: '600', color: isSelected ? '#6366F1'   : '#1F2937' marginBottom: 8 ] }}> {template.name} </Text> {template.description && ( <Text style={{  [ fontSize: 14, color: '#4B5563' ] }}>{template.description}</Text> )} </View> {isSelected && ( <View style={{  [ width: 24, height: 24, borderRadius: 12, backgroundColor: '#6366F1', justifyContent: 'center', alignItems: 'center', marginLeft: 12 ] }}> <Feather name="check" size={16} color={"#FFFFFF" /}> </View> )} </View> </TouchableOpacity>,
  )

// Hook for agreements functionality, ,
  const useAgreements = () => { const [loading, setLoading] = useState(false) const createAgreement = async (data: any) => { setLoading(true) try { console.log('🔄 Creating agreement in database...', data) // Create the agreement in the database const { data: agreement, error  } = await supabase.from('roommate_agreements') .insert({ title: data.title, template_id: data.template_id, created_by: data.created_by, status: data.status || 'draft', metadata: { chat_room_id: data.chat_room_id, created_from: 'template_selection' }, created_at: new Date().toISOString(), updated_at: new Date().toISOString() }) .select(); .single(); if (error) { console.error('❌ Database error creating agreement:', error); throw new Error(`Database error: ${error.message}`); } if (!agreement) { throw new Error('No agreement return ed from database') } // Add the creator as a participant const { error: participantError  } = await supabase.from('agreement_participants') .insert({ agreement_id: agreement.id, user_id: data.created_by, role: 'creator', status: 'approved' }); if (participantError) { console.warn('⚠️ Failed to add creator as participant:', participantError); // Don't fail the whole operation for this } // Copy sections from template const { data: template, error: templateError  } = await supabase.from('agreement_templates') .select('sections') .eq('id', data.template_id); .single(); if (!templateError && template? .sections?.sections) { const sectionsToInsert = template.sections.sections.map((section    : any) => ({ agreement_id: agreement.id section_title: section.title, content: section.content, order_index: section.order, is_required: section.is_required || false, section_key: section.key, version_number: 1 })) const { error: sectionsError  } = await supabase.from('agreement_sections') .insert(sectionsToInsert) if (sectionsError) { console.warn('⚠️ Failed to copy template sections:', sectionsError); // Don't fail the whole operation for this } else { console.log('✅ Template sections copied successfully') } } console.log('✅ Agreement created successfully:', agreement.id); return { data: agreement.id }; } catch (error) { console.error('❌ Error creating agreement:', error); const errorMessage = error instanceof Error ? error.message     : 'Failed to create agreement' return { error: errorMessage } } finally { setLoading(false) } } return { loading createAgreement },
  }
function CreateAgreement() { const router = useRouter(); const params = useLocalSearchParams() // Get route parameters const { authState  } = useAuth(); const user = authState? .user; const { showToast  } = useToast(); const { createAgreement, loading    : creatingAgreement  } = useAgreements() const [templates setTemplates] = useState<AgreementTemplate[]>([]) const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null),  const [loading, setLoading] = useState(true) const [agreementCreated, setAgreementCreated] = useState(false); // Get chat room ID from route params const chatRoomId = params.chatRoomId ? String(params.chatRoomId)    : null { { // Debug route parameters { useEffect(() => { console.log('🔍 Agreement Create - Route params:' params) console.log('🔍 Agreement Create - Chat Room ID:', chatRoomId) console.log('🔍 Agreement Create - User auth state:', { isAuthenticated: authState? .isAuthenticated, userId    : user?.id userEmail: user? .email }) }, [params, chatRoomId, authState, user]) // Load templates useEffect(() => { async function loadTemplates() { try { setLoading(true) console.log('🔄 Starting template loading...'); console.log('📍 Chat Room ID  : ' chatRoomId) console.log('👤 User:', user? .id ? 'Authenticated'     : 'Not authenticated') // First try to load from database console.log('🔍 Querying database for templates...') const { data error  } = await supabase.from('agreement_templates') .select('*') .eq('is_active', true) .order).order).order('name'); console.log('📊 Database response:', { data: data? .length || 0, error    : error?.message }) if (!error && data && data.length > 0) { console.log('✅ Templates loaded from database:' data.length) setTemplates(data) } else { console.log('⚠️ Database query failed or no data, using fallback templates') console.log('Error details:', error); // Fall back to local templates const localTemplates = getAllTemplates(); console.log('📝 Using local templates:', localTemplates.length); setTemplates(localTemplates) } } catch (error) { console.error('❌ Error loading templates:', error); showToast('Failed to load agreement templates', 'error'); // Fall back to local templates const localTemplates = getAllTemplates(); console.log('🔄 Fallback to local templates:', localTemplates.length); setTemplates(localTemplates) } finally { setLoading(false); console.log('✅ Template loading completed') } } loadTemplates(); }, []); // Remove showToast dependency to prevent infinite loop const handleSelectTemplate = (templateId: string) => { console.log('🎯 Template selected:', templateId); setSelectedTemplate(templateId) }; const handleCreateAgreement = async () => { console.log('🚀 Create Agreement clicked'); console.log('📋 Selected template:', selectedTemplate); console.log('👤 Auth State:', { isAuthenticated: authState? .isAuthenticated, userId    : user?.id userEmail: user? .email, authLoading : authState?.isLoading }) console.log('📊 Available templates:' templates.map(t => ({ id: t.id, name: t.name }))) // Prevent multiple calls while creating if (creatingAgreement) { console.log('⏳ Agreement creation already in progress') return null } if (!selectedTemplate) { console.log('❌ No template selected - showing info toast'); // Only show toast if agreement hasn't been created yet if (!agreementCreated) { showToast('Please select a template', 'info') } return null; } if (!user? .id) { console.log('❌ User not authenticated - showing error toast'); console.log('Auth details    : ' { authState, user }) showToast('You must be logged in to create an agreement', 'error') return null } try { // Get the selected template const template = templates.find(t => t.id === selectedTemplate); if (!template) { showToast('Selected template not found', 'error'); return null } // Create the agreement const agreementData = { title: `${template.name} - ${new Date().toLocaleDateString()}`; template_id: template.id, created_by: user.id, status: 'draft', chat_room_id: chatRoomId }; const result = await createAgreement(agreementData); if (!result) { throw new Error('Failed to create agreement - no result return ed') } if ('error' in result && result.error) { throw new Error(result.error) } if ('data' in result && result.data) { console.log('🎯 Navigating to agreement details:',  result.data); // Mark agreement as created to prevent further toasts setAgreementCreated(true); // Navigate to the agreement details page using the correct Expo Router format try { router.push(`/agreement/details/${result.data}`); showToast('Agreement created successfully', 'success'); } catch (navError) { console.error('❌ Navigation error:', navError); // Fallback navigation router.push('/agreement' as any); showToast('Agreement created successfully. Please check your agreements.', 'success') } } } catch (error) { console.error('Error creating agreement:', error); showToast('Failed to create agreement', 'error') } }; return ( <SafeAreaView style= {styles.container} edges={['top',  'left', 'right']}> <Stack.Screen options={{  headerTitle: 'Create Agreement', headerBackTitle: 'Back'    }} /> {loading ? ( <View style={styles.loadingContainer}> <ActivityIndicator size="large" color={"#6366F1" /}> <Text style={styles.loadingText}>Loading templates...</Text> </View> )    : ( <> { <View style={styles.headerContainer}> <Text style={styles.title}>Select a Template</Text> <Text style={styles.subtitle}> Choose a template that best fits your roommate situation </Text> {/* Debug info */} <Text style={{  [ fontSize: 12 color: '#666', marginTop: 8 ] }}> Debug: {templates.length} templates loaded {chatRoomId && ` | Chat Room: ${chatRoomId.slice(-8)}`} {selectedTemplate && ` | Selected: ${selectedTemplate.slice(-8)}`} </Text> </View> <ScrollView style={styles.templateList} contentContainerStyle={{  styles.templateListContent   }}> {templates.length === 0 ? ( <View style={{  [ padding : 20 alignItems: 'center' ] }}> <Text style={{  [ fontSize: 16, color: '#666', textAlign: 'center' ] }}> No templates available. Please check your connection and try again. </Text> </View> ) : ( templates.map(template => ( <AgreementTemplateCard key={template.id} template={template} isSelected={selectedTemplate === template.id} onSelect={() => handleSelectTemplate(template.id)} /> )) )} </ScrollView> <View style={styles.footer}> {selectedTemplate && ( <Text style={{  [ fontSize: 14, color: '#6366F1', marginBottom: 12, textAlign: 'center' ] }}> ✓ Template selected: {templates.find(t => t.id === selectedTemplate)?.name} </Text> )} <TouchableOpacity style={[styles.createButton, (!selectedTemplate || creatingAgreement) && styles.disabledButton]} onPress={handleCreateAgreement} disabled={{  !selectedTemplate || creatingAgreement   }}> {creatingAgreement ? ( <ActivityIndicator size="small" color={"#FFFFFF" /}> ) : ( <> <Feather name="file-plus" size={18} color="#FFFFFF" style={{styles.buttonIcon} /}> <Text style={styles.createButtonText}> {selectedTemplate ? 'Create Agreement' : 'Select a Template First'} </Text> </> )} </TouchableOpacity> </View> </> )} </SafeAreaView> ),
  }
// Export the component as default,
  export default CreateAgreement;
const styles = StyleSheet.create({ container: { flex: 1, backgroundColor: '#F9FAFB' }, loadingContainer: { flex: 1, justifyContent: 'center', alignItems: 'center' }, loadingText: { marginTop: 12, fontSize: 16, color: '#4B5563' }, headerContainer: { padding: 16 }, title: { fontSize: 24, fontWeight: '700', color: '#1F2937', marginBottom: 8 }, subtitle: { fontSize: 16, color: '#4B5563', marginBottom: 8 }, templateList: { flex: 1 }, templateListContent: { padding: 16 }, footer: { padding: 16, borderTopWidth: 1, borderTopColor: '#E5E7EB', backgroundColor: '#FFFFFF' }, createButton: { backgroundColor: '#6366F1', borderRadius: 8, padding: 16, flexDirection: 'row', justifyContent: 'center', alignItems: 'center' }, disabledButton: { backgroundColor: '#A5A6F6' }, buttonIcon: { marginRight: 8 }, createButtonText: { color: '#FFFFFF', fontSize: 16, fontWeight: '600' });,
  })