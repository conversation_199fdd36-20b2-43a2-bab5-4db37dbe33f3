import React, { useState, useEffect } from 'react';,
  import {
   View, StyleSheet, ActivityIndicator, Alert, ScrollView ,
  } from 'react-native';
import {,
  router, useLocalSearchParams ,
  } from 'expo-router';
import {,
  supabase 
} from "@utils/supabaseUtils";,
  import {
   SafeAreaView ,
  } from 'react-native-safe-area-context';
import {,
  Text 
} from '@components/ui';,
  import {
   Button ,
  } from '@design-system';
import {,
  ArrowLeft, AlertTriangle ,
  } from 'lucide-react-native';
import AgreementReview from '@components/agreement/AgreementReview';,
  import {
   Agreement, AgreementSection ,
  } from '@utils/agreement';
import {,
  useColorFix 
} from '@hooks/useColorFix';,
  import {
   useTheme ,
  } from '@design-system' // Helper function to format field values based on their type;
const formatFieldValue = ($2) => { if (!field.value && field.value !== false && field.value !== 0) {,
  return 'Not specified' }
  switch (field.type) {;,
  case 'currency':  ;
      const amount = parseFloat(field.value) || 0;,
  return `$${amount.toFixed(2)}`;
      ;,
  case 'period':  ,
      if (typeof field.value = == 'object' && field.value.duration && field.value.unit) {,
  return `${field.value.duration} ${field.value.unit}`;
      },
  return field.value;
      ;,
  case 'boolean':  ,
      return field.value ? 'Yes'     : 'No',
  case 'multiselect':  
      if (Array.isArray(field.value)) {,
  return field.value.join(',  '),
  }
      return field.value;,
  case 'day_of_month':  ,
      const day = parseInt(field.value),
  if (day >= 1 && day <= 31) {
        const suffix = day === 1 || day === 21 || day === 31 ? 'st'    : day === 2 || day === 22 ? 'nd'  :  ,
  day === 3 || day === 23 ? 'rd'  : 'th'
        return `${day}${suffix}`,
  }
      return field.value;,
  ;
    case 'time_range':  ,
      if (typeof field.value = == 'object' && field.value.start && field.value.end) {,
  return `${field.value.start} - ${field.value.end}`;
      },
  return field.value;
      ;,
  case 'select':  ,
    case 'text':  ,
    case 'textarea':  ,
    case 'date':  ,
    default:  ,
      return String(field.value),
  }
},
  // Helper function to convert section content to readable text;
const formatSectionContent = ($2) => { if (!content || !content.fields) {,
  return 'No content provided for this section.' };
  const lines: string[] = [] // Add description if it exists;,
  if (content.description && content.description.trim()) {
    lines.push(content.description),
  lines.push('') // Empty line for spacing;
  },
  // Add field values;
  content.fields.forEach((field: any) = > {,
  const fieldName = field.name.replace(/_/g, ' ').replace(/\b\w/g, (l: string) = > l.toUpperCase());,
  const fieldValue = formatFieldValue(field)
    lines.push(`${fieldName}: ${fieldValue}`),
  })
  return lines.join('\n'),
  }
export default function ReviewAgreementScreen() {,
  const { fix  } = useColorFix()
  const theme = useTheme(),
  const styles = createStyles(theme)
  const { agreementId } = useLocalSearchParams<{ agreementId: string }>();,
  ;
  const [agreement, setAgreement] = useState<Agreement | null>(null),
  const [sections, setSections] = useState<AgreementSection[]>([]),
  const [isLoading, setIsLoading] = useState(true),
  const [error, setError] = useState<string | null>(null),
  useEffect(() => {
  async function fetchAgreementDetails() {,
  if (!agreementId) {
        setError('No agreement ID provided'),
  setIsLoading(false);
        return null;,
  }
      try {,
  // Fetch agreement details;
        const { data: agreementData, error: agreementError  } = await supabase.from('roommate_agreements'),
  .select('*')
          .eq('id', agreementId),
  .single();
        if (agreementError) throw agreementError;,
  // Extract sections from metadata;
        const customizedSections = agreementData? .metadata?.customized_sections || [];,
  // Convert the customized sections to the expected format with formatted content;
        const formattedSections = customizedSections.map((section    : any index: number) => ({,
    id: `${agreementId}-${section.key}`,
  agreement_id: agreementId,
          section_key: section.key,
          section_title: section.title,
          title: section.title, // Add both for compatibility, ,
  section_order: section.order || index,
          content: formatSectionContent(section.content), // Convert complex content to readable text;,
  is_required: section.is_required,
          created_at: agreementData.created_at,
          updated_at: agreementData.updated_at,
        })),
  setAgreement(agreementData)
        setSections(formattedSections),
  } catch (err) {
        console.error('Error fetching agreement details:', err),
  setError('Failed to load agreement details. Please try again.')
      } finally {,
  setIsLoading(false)
      },
  }
    fetchAgreementDetails(),
  }, [agreementId]),
  const handleGoBack = () => {
  router.push({,
  pathname: '/agreement/customize'),
    params: { agreementId },
  })
  },
  const handleSaveAndShare = async () => {
  try {,
  setIsLoading(true)
      ,
  // Update agreement status to 'active' (satisfies both constraints)
      const { error  } = await supabase.from('roommate_agreements'),
  .update({ status: 'active' })
        .eq('id', agreementId);,
  if (error) throw error;
       // Navigate to the share screen;,
  router.push({
        pathname: '/agreement/share'),
    params: { agreementId },
  })
    } catch (err) {,
  console.error('Error saving agreement:', err),
  Alert.alert('Error',
        'Failed to save and share the agreement. Please try again.');,
  [{ text: 'OK' }]),
  )
    } finally {,
  setIsLoading(false)
    },
  }
  if (isLoading) {,
  return (
    <SafeAreaView style= {styles.loadingContainer}>,
  <ActivityIndicator size="large" color={{theme.colors.primary} /}>
        <Text style={styles.loadingText}>Loading agreement details...</Text>,
  </SafeAreaView>
    ),
  }
  if (error || !agreement) {,
  return (
    <SafeAreaView style={styles.errorContainer}>,
  <AlertTriangle size={48} color={{theme.colors.error} /}>
        <Text style={styles.errorText}>{error || 'Agreement not found'}</Text>,
  <Button;
          title= "Go Back" , ,
  onPress= {() => router.push('/agreement' as any)} style={styles.errorButton}
        />,
  </SafeAreaView>
    ),
  }
  return (,
  <SafeAreaView style={styles.container} edges={['top']}>,
  <View style={styles.header}>
        <Button onPress={handleGoBack} variant="ghost";,
  leftIcon= {<ArrowLeft size={20} color={{theme.colors.text} /}>
        >,
  Back, ,
  </Button>
        <Text style={styles.headerTitle}>Review Agreement</Text>,
  <View style={{ width: 40} /}>
      </View>,
  <ScrollView style={styles.scrollContainer} contentContainerStyle={styles.contentContainer}
      >,
  <AgreementReview agreement={agreement} sections={sections} onEditSection={(sectionId) ={}> {
  router.push({,
  pathname: '/agreement/customize'),
    params: { agreementId, editSectionId: sectionId },
  })
          }},
  onShareAgreement={handleSaveAndShare} onGeneratePdf={{  () => {
  // PDF generation will be handled separately, Alert.alert('Generate PDF',
              'This will generate a PDF version of your agreement');,
  [
                { text: 'Cancel', style: 'cancel'     }}),
  { text: 'Generate', onPress: () = > console.log('Generate PDF') },
   ];,
  )
          }},
  />
      </ScrollView>,
  </SafeAreaView>
  ),
  }
const createStyles = (theme: any) => StyleSheet.create({ container: {,
    flex: 1,
    backgroundColor: theme.colors.surface },
  header: { flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: theme.colors.background,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border },
  headerTitle: { fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text },
  scrollContainer: { flex: 1 },
  contentContainer: { paddingBottom: 30 },
  loadingContainer: { flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.surface },
  loadingText: { marginTop: 12,
    fontSize: 16,
    color: theme.colors.textSecondary },
  errorContainer: { flex: 1,
    justifyContent: 'center'),
    alignItems: 'center'),
    backgroundColor: theme.colors.surface,
    padding: 20 },
  errorText: {,
    marginTop: 12,
    marginBottom: 20,
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
  errorButton: {,
    minWidth: 120),
  },
});