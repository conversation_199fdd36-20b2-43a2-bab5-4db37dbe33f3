import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Alert, ActivityIndicator } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Text, TextInput } from '@components/ui';
import { Button } from '@design-system';
import { router, useLocalSearchParams, Stack } from 'expo-router';
import { ArrowLeft, AlertTriangle } from 'lucide-react-native';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { useAuth } from '@context/AuthContext';
import { agreementService } from '@services/agreementService';
import { Picker } from '@react-native-picker/picker';
import { useColorFix } from '@hooks/useColorFix';

export default function CreateDisputeScreen() {
  const { fix } = useColorFix();
  const { agreementId, sectionId, title } = useLocalSearchParams<{
    agreementId: string,
    sectionId?: string,
    title?: string,
  }>();
  const { state, actions } = useAuth();
  const user = authState.user;
  ;
  const [disputeTitle, setDisputeTitle] = useState(title || '');
  const [description, setDescription] = useState('');
  const [disputeType, setDisputeType] = useState('contract_violation');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async () => {
  if (!user?.id) {
      Alert.alert('Error', 'You must be logged in to create a dispute');
      return null;
    }
    if (!disputeTitle.trim()) {
      Alert.alert('Error', 'Please provide a title for the dispute');
      return null;
    }
    if (!description.trim()) {
      Alert.alert('Error', 'Please provide a description of the issue');
      return null;
    }
    try {
      setIsLoading(true);

      const disputeId = await agreementService.createDispute({
        agreement_id: agreementId,
        raised_by: user.id,
        dispute_type: disputeType as any,
        title: disputeTitle,
        description,
        status: 'open',
        section_id: sectionId,
      });

      if (!disputeId) {
        throw new Error('Failed to create dispute');
      }
      Alert.alert(
        'Dispute Created',
        'Your dispute has been submitted successfully.',
        [
          {
            text: 'OK',
            onPress: () => router.replace({
              pathname: '/agreement/disputes/[id]',
              params: { id: disputeId }
            }) ;
          }
        ];
      );
    } catch (error) {
      console.error('Error creating dispute:', error);
      Alert.alert('Error', 'Failed to create dispute. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }
  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <Stack.Screen options={ headerShown: false } />
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}
        >
          <ArrowLeft size={24} color={"#1E293B" /}>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Create Dispute</Text>
        <View style={{ width: 40 } /}>
      </View>
      <ScrollView style={styles.content}>
        <View style={styles.formContainer}>
          <Text style={styles.sectionTitle}>Issue Details</Text>
          <View style={styles.formGroup}>
            <Text style={styles.label}>Title</Text>
            <TextInput
              placeholder="Brief title of the issue";
              value={disputeTitle} onChangeText={setDisputeTitle}
            />
          </View>
          <View style={styles.formGroup}>
            <Text style={styles.label}>Dispute Type</Text>
            <View style={styles.pickerContainer}>
              <Picker selectedValue={disputeType} onValueChange={(value) ={}> setDisputeType(value)} style={styles.picker}
              >
                <Picker.Item label="Contract Violation" value="contract_violation" />
                <Picker.Item label="Payment Issue" value="payment" />
                <Picker.Item label="Maintenance Problem" value="maintenance" />
                <Picker.Item label="Noise Complaint" value="noise" />
                <Picker.Item label="Cleanliness Issue" value="cleanliness" />
                <Picker.Item label="Other Issue" value="other" />
              </Picker>
            </View>
          </View>
          <View style={styles.formGroup}>
            <Text style={styles.label}>Description</Text>
            <TextInput
              placeholder="Detailed description of the issue";
              multiline numberOfLines={6} textAlignVertical="top";
              value={description} onChangeText={setDescription} style={styles.textArea}
            />
          </View>
          <View style={styles.infoBox}>
            <AlertTriangle size={20} color={{"#F59E0B"} /}>
            <Text style={styles.infoText}>
              Disputes will be visible to all agreement participants. Please be factual and specific.;
            </Text>
          </View>
          <Button
            title="Submit Dispute";
            onPress={handleSubmit} loading={isLoading} disabled={isLoading} style={styles.submitButton}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1E293B',
  },
  content: {
    flex: 1,
  },
  formContainer: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1E293B',
    marginBottom: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#475569',
    marginBottom: 6,
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: '#CBD5E1',
    borderRadius: 8,
    backgroundColor: '#FFFFFF',
  },
  picker: {
    height: 50,
  },
  textArea: {
    height: 120,
  },
  infoBox: {
    flexDirection: 'row',
    backgroundColor: '#FEF3C7',
    borderRadius: 8,
    padding: 12,
    marginVertical: 16,
    alignItems: 'center',
  },
  infoText: {
    fontSize: 14,
    color: '#92400E',
    flex: 1,
    marginLeft: 8,
  },
  submitButton: {
    marginTop: 16,
  },
});