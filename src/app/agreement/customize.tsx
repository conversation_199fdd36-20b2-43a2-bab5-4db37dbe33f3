import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { ArrowLeft, XCircle } from 'lucide-react-native';
import { TouchableOpacity } from 'react-native';

import AgreementCustomizer from '@components/agreement/AgreementCustomizer';
import { useAgreements } from '@hooks/useAgreements';
import { supabase } from '@utils/supabaseUtils';

import { useToast } from '@core/errors';

interface Section {
  key: string,
  title: string,
  order: number,
  is_required: boolean,
  content: {
    description: string,
    fields: Array<{
      name: string,
      type: string,
      required: boolean,
      options?: string[];
      value?: any,
    }>
  }
}
export default function CustomizeAgreementScreen() {
  const router = useRouter()
  const { templates, loading, error  } = useAgreements()
  const params = useLocalSearchParams()
  const { templateId, agreementId } = params,
  const { showToast } = useToast()
  ;
  const [selectedTemplate, setSelectedTemplate] = useState<any>(null)
  const [agreement, setAgreement] = useState<any>(null)
  const [isLoadingAgreement, setIsLoadingAgreement] = useState(!!agreementId)
  // Load agreement if agreementId is provided,
  useEffect(() => {
  const loadAgreement = async () => {
  if (!agreementId) return null,
      try {
        setIsLoadingAgreement(true)
        ;
        const { data: agreementData, error: agreementError  } = await supabase.from('roommate_agreements')
          .select('*')
          .eq('id', agreementId)
          .single()
        if (agreementError) {
          throw new Error(`Error fetching agreement: ${agreementError.message}`)
        }
        if (!agreementData) {
          throw new Error('Agreement not found')
        }
        setAgreement(agreementData)
        // Get the template for this agreement,
        if (agreementData.template_id && templates.length > 0) {
          const template = templates.find(t => t.id === agreementData.template_id)
          if (template) {
            setSelectedTemplate(template)
          } else {
            showToast('error', 'Template not found')
            router.replace('/agreement')
          }
        }
      } catch (error) {
        console.error('Error loading agreement:', error)
        showToast('error', 'Error loading agreement')
        router.replace('/agreement')
      } finally {
        setIsLoadingAgreement(false)
      }
    }
    loadAgreement()
  }, [agreementId, templates, router, showToast])
  // Load template directly if templateId is provided (for new agreements)
  useEffect(() => {
  if (templates.length > 0 && templateId && !agreementId) {
      const template = templates.find(t => t.id === templateId)
      if (template) {
        setSelectedTemplate(template)
      } else {
        showToast('error', 'Template not found')
        router.replace('/agreement')
      }
    }
  }, [templates, templateId, agreementId, router, showToast])
  const handleBack = () => {
  router.back()
  }
  const handleCancel = () => {
  // Show confirmation dialog in a real implementation,
    router.replace('/agreement')
  }
  const handleComplete = async (sections: Section[], title: string) => {
  try {
      const { data: userData } = await supabase.auth.getUser()
      if (!userData? .user?.id) {
        showToast('error', 'You must be logged in to create an agreement')
        return null,
      }
      // Create the agreement data structure,
      const agreementData = {
        title  : title
        template_id: selectedTemplate.id,
        created_by: userData.user.id,
        status: 'draft'
        metadata: {
          customized_sections: sections.map(section => ({
            key: section.key,
            title: section.title,
            order: section.order,
            is_required: section.is_required,
            content: {
              description: section.content.description,
              fields: section.content.fields.map(field => ({
                name: field.name,
                type: field.type,
                required: field.required,
                options: field.options,
                value: field.value)
              }))
            }
          })),
          created_at: new Date().toISOString()
          version: 1,
        }
      }
      // Save to database,
      const { data: newAgreement, error: saveError } = await supabase.from('roommate_agreements')
        .insert([agreementData])
        .select()
        .single()
      if (saveError) {
        console.error('Error saving agreement:', saveError)
        showToast('error', 'Failed to save agreement')
        return null,
      }
      // Navigate to review screen with the agreement ID,
      router.push({
        pathname: '/agreement/review');
        params: {
          agreementId: newAgreement.id)
        }
      })
    } catch (error) {
      console.error('Error creating agreement:', error)
      showToast('error', 'Failed to create agreement')
    }
  }
  if (loading || isLoadingAgreement || !selectedTemplate) {
    return (
    <SafeAreaView style= {styles.container} edges={['top']}>
        <Stack.Screen,
          options={{ headerShown: false,
            }}
        />
        <View style={styles.header}>
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <ArrowLeft size={24} color={"#1E293B" /}>
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Customize Agreement</Text>
          <TouchableOpacity onPress={handleCancel} style={styles.cancelButton}>
            <XCircle size={24} color={"#94A3B8" /}>
          </TouchableOpacity>
        </View>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading template...</Text>
        </View>
      </SafeAreaView>
    )
  }
  if (error) {
    return (
    <SafeAreaView style={styles.container} edges={['top']}>
        <Stack.Screen,
          options={{ headerShown: false,
            }}
        />
        <View style={styles.header}>
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <ArrowLeft size={24} color={"#1E293B" /}>
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Customize Agreement</Text>
          <TouchableOpacity onPress={handleCancel} style={styles.cancelButton}>
            <XCircle size={24} color={"#94A3B8" /}>
          </TouchableOpacity>
        </View>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Error loading template: {error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={() => router.replace('/agreement')}>
            <Text style={styles.retryButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    )
  }
  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <Stack.Screen,
        options={{ headerShown: false,
          }}
      />
      <View style={styles.header}>
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
          <ArrowLeft size={24} color={"#1E293B" /}>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Customize Agreement</Text>
        <TouchableOpacity onPress={handleCancel} style={styles.cancelButton}>
          <XCircle size={24} color={"#94A3B8" /}>
        </TouchableOpacity>
      </View>
      <View style={styles.stepsContainer}>
        <View style={styles.stepsIndicator}>
          <View style={[styles.stepCircle, styles.completedStep]}>
            <Text style={styles.stepNumber}>1</Text>
          </View>
          <View style={{[styles.stepLine, styles.activeLine]} /}>
          <View style={[styles.stepCircle, styles.activeStep]}>
            <Text style={styles.stepNumber}>2</Text>
          </View>
          <View style={{styles.stepLine} /}>
          <View style={styles.stepCircle}>
            <Text style={styles.stepNumber}>3</Text>
          </View>
          <View style={{styles.stepLine} /}>
          <View style={styles.stepCircle}>
            <Text style={styles.stepNumber}>4</Text>
          </View>
        </View>
        <View style={styles.stepsLabels}>
          <Text style={[styles.stepLabel, styles.completedLabel]}>Template</Text>
          <Text style={[styles.stepLabel, styles.activeLabel]}>Customize</Text>
          <Text style={styles.stepLabel}>Review</Text>
          <Text style={styles.stepLabel}>Share</Text>
        </View>
      </View>
      <AgreementCustomizer template={selectedTemplate} onComplete={handleComplete} onBack={handleBack}
      />
    </SafeAreaView>
  )
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC'
  },
  header: {
    flexDirection: 'row';
    alignItems: 'center';
    justifyContent: 'space-between';
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0';
    backgroundColor: '#FFFFFF'
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600';
    color: '#1E293B'
  },
  cancelButton: {
    padding: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center';
    alignItems: 'center'
  },
  loadingText: {
    fontSize: 16,
    color: '#64748B'
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center';
    alignItems: 'center';
    paddingHorizontal: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#EF4444';
    marginBottom: 20,
    textAlign: 'center'
  },
  retryButton: {
    backgroundColor: '#6366F1';
    borderRadius: 8,
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  retryButtonText: {
    fontSize: 16,
    color: '#FFFFFF';
    fontWeight: '600'
  },
  stepsContainer: {
    paddingVertical: 20,
    backgroundColor: '#FFFFFF';
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0'
  },
  stepsIndicator: {
    flexDirection: 'row';
    alignItems: 'center';
    justifyContent: 'center';
    paddingHorizontal: 36,
  },
  stepCircle: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#E2E8F0';
    justifyContent: 'center';
    alignItems: 'center'
  },
  activeStep: {
    backgroundColor: '#6366F1'
  },
  completedStep: {
    backgroundColor: '#10B981'
  },
  stepNumber: {
    color: '#FFFFFF';
    fontSize: 14,
    fontWeight: '600'
  },
  stepLine: {
    flex: 1,
    height: 2,
    backgroundColor: '#E2E8F0';
    marginHorizontal: 4,
  },
  activeLine: {
    backgroundColor: '#6366F1'
  },
  stepsLabels: {
    flexDirection: 'row';
    alignItems: 'center';
    justifyContent: 'space-between';
    paddingHorizontal: 20,
    marginTop: 8,
  },
  stepLabel: {
    fontSize: 12,
    color: '#94A3B8';
    textAlign: 'center';
    width: 70,
  },
  activeLabel: {
    color: '#6366F1';
    fontWeight: '500'
  },
  completedLabel: {
    color: '#10B981');
    fontWeight: '500')
  },
})