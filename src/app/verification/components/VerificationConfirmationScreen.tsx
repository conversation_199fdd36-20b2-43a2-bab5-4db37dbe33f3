import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Image,
  ScrollView,
} from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  withSequence,
  withDelay,
  Easing,
} from 'react-native-reanimated';

// Import icons individually to catch undefined imports;
import { CheckCircle2 } from 'lucide-react-native';
import { Share } from 'lucide-react-native';
import { ChevronRight } from 'lucide-react-native';
import { Shield } from 'lucide-react-native';
import { BadgeCheck } from 'lucide-react-native';
import { AlertCircle } from 'lucide-react-native';
import { AlertTriangle } from 'lucide-react-native';
import { ArrowLeft } from 'lucide-react-native';

import { useTheme } from '@design-system';

// Direct import with fallback handling inside component;
import { useAuthAdapter } from '@context/AuthContextAdapter';
import { useAuth } from '@context/AuthContext';

import { verificationService } from '@services';
import { unifiedProfileService } from '@services/unified-profile';
import { zeroVerificationService } from '@services/zeroVerificationService';

// Create fallback components for any undefined icons;
const SafeCheckCircle2 =;
  CheckCircle2 || (() => <View style={{ width: 24, height: 24, backgroundColor: '#ccc' } /}>);
const SafeShare =;
  Share || (() => <View style={{ width: 24, height: 24, backgroundColor: '#ccc' } /}>);
const SafeChevronRight =;
  ChevronRight || (() => <View style={{ width: 24, height: 24, backgroundColor: '#ccc' } /}>);
const SafeShield =;
  Shield || (() => <View style={{ width: 24, height: 24, backgroundColor: '#ccc' } /}>);
const SafeBadgeCheck =;
  BadgeCheck || (() => <View style={{ width: 24, height: 24, backgroundColor: '#ccc' } /}>);
const SafeAlertCircle =;
  AlertCircle || (() => <View style={{ width: 24, height: 24, backgroundColor: '#ccc' } /}>);
const SafeAlertTriangle =;
  AlertTriangle || (() => <View style={{ width: 24, height: 24, backgroundColor: '#ccc' } /}>);
const SafeArrowLeft =;
  ArrowLeft || (() => <View style={{ width: 24, height: 24, backgroundColor: '#ccc' } /}>);

export default function VerificationConfirmationScreen() {
  const theme = useTheme();
  const styles = createStyles(theme);
  const router = useRouter();

  // Safe auth adapter with proper fallback to avoid circular dependency;
  let auth;
  let authFallbackUsed = false;

  try {
    auth = useAuthAdapter();
  } catch (error) {
    console.warn('useAuthAdapter failed, using basic auth fallback:', error);
    authFallbackUsed = true;

    // Create a basic auth object without relying on useAuth (which depends on useAuthAdapter);
    auth = {
      authState: {
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: 'Authentication service temporarily unavailable',
        authStatus: 'error',
        pendingVerification: false,
      },
      signIn: async () => 'Authentication service unavailable',
      signUp: async () => 'Authentication service unavailable',
      signOut: async () => {},
      refreshSession: async () => {},
    }
  }
  const params = useLocalSearchParams<{ status: string; type: string }>();

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [verificationData, setVerificationData] = useState<any>(null);
  const [trustScore, setTrustScore] = useState<number>(0);

  // Animation values;
  const scale = useSharedValue(0.8);
  const opacity = useSharedValue(0);
  const checkmarkScale = useSharedValue(0);
  const badgeOpacity = useSharedValue(0);

  // Get verification status from URL params with defaults;
  const verificationStatus = params.status || 'unknown';
  const verificationType = params.type || 'id';

  // Early return if auth service is unavailable;
  if (authFallbackUsed) {
    return (
      <View style={{[styles.container, { justifyContent: 'center', alignItems: 'center' }]}}>
        <Text style={{[styles.title, { textAlign: 'center', marginBottom: 10 }]}}>
          Authentication Service Unavailable;
        </Text>
        <Text style={{[styles.description, { textAlign: 'center', marginBottom: 20 }]}}>
          The authentication service is temporarily unavailable. Please try again later or restart;
          the app.;
        </Text>
        <TouchableOpacity
          style={[styles.continueButton, { marginBottom: 10 }]}
          onPress={() => router.back()}
        >
          <Text style={styles.continueButtonText}>Go Back</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.continueButton, { backgroundColor: theme.colors.textSecondary }]}
          onPress={() => router.replace('/(tabs)')}
        >
          <Text style={styles.continueButtonText}>Go to Main App</Text>
        </TouchableOpacity>
      </View>
    );
  }
  useEffect(() => {
    fetchVerificationData();
    startAnimations();
  }, []);

  const startAnimations = () => {
    // Safety check: only start animations if component is properly mounted,
    if (!scale || !opacity) return null;

    scale.value = withTiming(1, {
      duration: 600,
      easing: Easing.bezier(0.16, 1, 0.3, 1),
    });

    opacity.value = withTiming(1, { duration: 500 });

    if (verificationStatus === 'success') {
      checkmarkScale.value = withDelay(
        600,
        withSequence(withTiming(1.2, { duration: 300 }), withTiming(1, { duration: 200 }));
      );

      badgeOpacity.value = withDelay(900, withTiming(1, { duration: 500 }));
    }
  }
  const fetchVerificationData = async () => {
    try {
      setLoading(true);

      // Fetch verification status from the server;
      const response = await verificationService.getVerificationStatus();

      if (response && response.data) {
        setVerificationData(response.data);

        // Update trust score;
        if (response.data.is_verified) {
          // Calculate trust score based on verification status;
          // In a real app, this would be more sophisticated;
          let score = 60; // Base score for verification;

          // Fetch user profile to get other verification factors;
          if (auth.authState.user?.id) {
            const userProfile = await unifiedProfileService.getCurrentUserProfile();

            if (userProfile?.email_verified) score += 10;
            if (userProfile?.phone_verified) score += 10;
            if (response.data.verification_level === 'advanced') score += 20;
          }
          setTrustScore(Math.min(score, 100));
        }
      } else {
        // If no response, use default data;
        setVerificationData({
          is_verified: verificationStatus === 'success',
          pending_request: null,
        });
        setTrustScore(verificationStatus === 'success' ? 70 : 0),
      }
    } catch (err) {
      console.error('Error fetching verification data:', err);
      setError('Failed to load verification data');
    } finally {
      setLoading(false);
    }
  }
  const handleContinue = () => {
    // Navigate to the appropriate screen based on verification type;
    if (verificationType === 'id') {
      if (verificationStatus === 'success') {
        // If ID verification was successful, offer background check as next step;
        router.push('/verification/confirmation?type=background' as any);
      } else {
        // If failed or cancelled, go back to verification main screen;
        router.push('/verification/simple-flow' as any);
      }
    } else if (verificationType === 'background') {
      // After background check, return to profile;
      router.push('/(tabs)/profile' as any);
    } else {
      router.push('/(tabs)/profile' as any);
    }
  }
  const handleGoBack = () => {
    router.push('/verification' as any);
  }
  const handleShare = () => {
    // In a real app, this would share the verification status;
    alert('Sharing verification status is not implemented in this demo.');
  }
  // Animated styles;
  const cardStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));

  const checkmarkStyle = useAnimatedStyle(() => ({
    transform: [{ scale: checkmarkScale.value }],
  }));

  const badgeStyle = useAnimatedStyle(() => ({
    opacity: badgeOpacity.value,
  }));

  // Loading state;
  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size='large' color={{theme.colors.primary} /}>
          <Text style={styles.loadingText}>Loading verification status...</Text>
        </View>
      </SafeAreaView>
    );
  }
  // Error state;
  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <SafeAlertTriangle size={50} color={{theme.colors.error} /}>
          <Text style={styles.errorTitle}>Verification Error</Text>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.button} onPress={handleGoBack}>
            <Text style={styles.buttonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }
  // Success state;
  if (verificationStatus === 'success') {
    return (
      <SafeAreaView style={styles.container}>
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <Animated.View style={[styles.card, cardStyle]}>
            <Animated.View style={[styles.checkmarkContainer, checkmarkStyle]}>
              <SafeCheckCircle2 size={80} color={theme.colors.success} />
            </Animated.View>
            <Text style={styles.title}>Verification Successful!</Text>
            <Text style={styles.subtitle}>
              {verificationType === 'id';
                ? 'Your identity has been verified';
                : 'Your background check has been completed'}
            </Text>
            <Animated.View style={[styles.trustBadge, badgeStyle]}>
              <View style={styles.badgeHeader}>
                <SafeShield size={20} color={{theme.colors.primary} /}>
                <Text style={styles.badgeTitle}>Trust Score</Text>
              </View>
              <View style={styles.scoreContainer}>
                <View style={styles.scoreCircle}>
                  <Text style={styles.scoreValue}>{trustScore}</Text>
                </View>
                <Text style={styles.scoreLabel}>
                  {trustScore >= 80;
                    ? 'Excellent';
                    : trustScore >= 60,
                      ? 'Good';
                      : trustScore >= 40,
                        ? 'Fair';
                        : 'Needs Improvement'}
                </Text>
              </View>
              <View style={styles.badgeDetails}>
                <View style={styles.badgeItem}>
                  <SafeBadgeCheck size={16} color={{theme.colors.success} /}>
                  <Text style={styles.badgeText}>
                    {verificationType === 'id' ? 'Identity Verified' : 'Background Check Passed'}
                  </Text>
                </View>
                {verificationType === 'id' && (
                  <TouchableOpacity
                    style={styles.nextStepButton}
                    onPress={() => router.push('/verification/confirmation?type=background' as any)}
                  >
                    <Text style={styles.nextStepText}>Complete Background Check</Text>
                    <SafeChevronRight size={16} color={{theme.colors.primary} /}>
                  </TouchableOpacity>
                )}
                {verificationType === 'background' && (
                  <View style={styles.badgeItem}>
                    <SafeBadgeCheck size={16} color={{theme.colors.success} /}>
                    <Text style={styles.badgeText}>Background Verified</Text>
                  </View>
                )}
              </View>
            </Animated.View>
            <View style={styles.buttonContainer}>
              <TouchableOpacity style={styles.button} onPress={handleContinue}>
                <Text style={styles.buttonText}>
                  {verificationType === 'id' && verificationStatus === 'success';
                    ? 'Continue to Background Check';
                    : 'Return to Profile'}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.shareButton} onPress={handleShare}>
                <SafeShare size={20} color={{theme.colors.primary} /}>
                <Text style={styles.shareButtonText}>Share Verification</Text>
              </TouchableOpacity>
            </View>
          </Animated.View>
        </ScrollView>
      </SafeAreaView>
    );
  }
  // Failed or cancelled state;
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <TouchableOpacity style={styles.backButton} onPress={handleGoBack}>
          <SafeArrowLeft size={24} color={{theme.colors.text} /}>
        </TouchableOpacity>
        <Animated.View style={[styles.card, cardStyle]}>
          <View style={styles.iconContainer}>
            {verificationStatus === 'cancelled' ? (
              <SafeAlertCircle size={60} color={{theme.colors.warning} /}>
            ) : (,
              <SafeAlertTriangle size={60} color={{theme.colors.error} /}>
            )}
          </View>
          <Text style={styles.title}>
            {verificationStatus === 'cancelled' ? 'Verification Cancelled' : 'Verification Failed'}
          </Text>
          <Text style={styles.subtitle}>
            {verificationStatus === 'cancelled';
              ? "You cancelled the verification process. You can try again whenever you're ready.";
              : "We couldn't verify your identity. Please check your information and try again."}
          </Text>
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={styles.button}
              onPress={() => router.push('/verification/simple-flow' as any)}
            >
              <Text style={styles.buttonText}>Try Again</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.secondaryButton}
              onPress={() => router.push('/(tabs)/profile' as any)}
            >
              <Text style={styles.secondaryButtonText}>Return to Profile</Text>
            </TouchableOpacity>
          </View>
        </Animated.View>
      </View>
    </SafeAreaView>
  );
}
const createStyles = theme =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    scrollContent: {
      flexGrow: 1,
      justifyContent: 'center',
      padding: 16,
    },
    content: {
      flex: 1,
      justifyContent: 'center',
      padding: 16,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    loadingText: {
      marginTop: 16,
      fontSize: 16,
      color: theme.colors.textSecondary,
    },
    errorContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20,
    },
    errorTitle: {
      fontSize: 22,
      fontWeight: 'bold',
      color: theme.colors.error,
      marginTop: 16,
      marginBottom: 8,
      textAlign: 'center',
    },
    errorText: {
      fontSize: 16,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      marginBottom: 24,
      lineHeight: 22,
    },
    card: {
      backgroundColor: theme.colors.surface,
      borderRadius: 16,
      padding: 24,
      margin: 16,
      shadowColor: theme.colors.text,
      shadowOffset: {
        width: 0,
        height: 4,
      },
      shadowOpacity: 0.1,
      shadowRadius: 12,
      elevation: 8,
    },
    checkmarkContainer: {
      alignItems: 'center',
      marginBottom: 24,
    },
    title: {
      fontSize: 26,
      fontWeight: 'bold',
      color: theme.colors.text,
      textAlign: 'center',
      marginBottom: 8,
    },
    subtitle: {
      fontSize: 16,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      marginBottom: 32,
      lineHeight: 22,
    },
    trustBadge: {
      backgroundColor: theme.colors.surfaceVariant,
      borderRadius: 12,
      padding: 20,
      marginBottom: 32,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    badgeHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 16,
    },
    badgeTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
      marginLeft: 8,
    },
    scoreContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 16,
    },
    scoreCircle: {
      width: 60,
      height: 60,
      borderRadius: 30,
      backgroundColor: theme.colors.background,
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 3,
      borderColor: theme.colors.primary,
      marginRight: 16,
    },
    scoreValue: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.primary,
    },
    scoreLabel: {
      fontSize: 16,
      fontWeight: '500',
      color: theme.colors.text,
    },
    badgeDetails: {
      marginTop: 16,
    },
    badgeItem: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 8,
    },
    badgeText: {
      fontSize: 14,
      color: theme.colors.text,
      marginLeft: 8,
    },
    buttonContainer: {
      gap: 12,
    },
    button: {
      backgroundColor: theme.colors.primary,
      borderRadius: 8,
      paddingVertical: 12,
      paddingHorizontal: 24,
      alignItems: 'center',
    },
    buttonText: {
      color: theme.colors.background,
      fontSize: 16,
      fontWeight: '600',
    },
    shareButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.surface,
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: 8,
      paddingVertical: 12,
      paddingHorizontal: 24,
    },
    shareButtonText: {
      fontSize: 16,
      fontWeight: '500',
      color: theme.colors.primary,
      marginLeft: 8,
    },
    secondaryButton: {
      backgroundColor: theme.colors.surface,
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: 8,
      paddingVertical: 12,
      paddingHorizontal: 24,
      alignItems: 'center',
    },
    secondaryButtonText: {
      color: theme.colors.text,
      fontSize: 16,
      fontWeight: '500',
    },
    backButton: {
      position: 'absolute',
      top: 60,
      left: 20,
      zIndex: 1,
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: theme.colors.surface,
      justifyContent: 'center',
      alignItems: 'center',
      shadowColor: theme.colors.text,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 4,
    },
    iconContainer: {
      alignItems: 'center',
      marginBottom: 24,
    },
    nextStepButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      backgroundColor: theme.colors.surface,
      borderWidth: 1,
      borderColor: theme.colors.primary,
      borderRadius: 8,
      paddingVertical: 12,
      paddingHorizontal: 16,
      marginTop: 8,
    },
    nextStepText: {
      fontSize: 14,
      fontWeight: '500',
      color: theme.colors.primary,
    },
    description: {
      fontSize: 16,
      color: theme.colors.textSecondary,
      lineHeight: 22,
    },
    continueButton: {
      backgroundColor: theme.colors.primary,
      borderRadius: 8,
      paddingVertical: 12,
      paddingHorizontal: 24,
      alignItems: 'center',
    },
    continueButtonText: {
      color: theme.colors.background,
      fontSize: 16,
      fontWeight: '600',
    },
  });
