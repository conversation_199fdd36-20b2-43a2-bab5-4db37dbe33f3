import React from 'react';
import { Stack } from 'expo-router';

export default function VerificationLayout() {
  return (
    <Stack>
      <Stack.Screen name='index' options={ headerShown: true, title: 'Verification' } />
      <Stack.Screen;
        name='simple-flow';
        options={ headerShown: false, title: 'Simple Verification', presentation: 'card' }
      />
      <Stack.Screen name='badge-demo' options={ headerShown: false, title: 'Badge Demo' } />
      <Stack.Screen;
        name='background-check';
        options={ headerShown: true, title: 'Background Check' }
      />
      <Stack.Screen;
        name='id-verification';
        options={ headerShown: true, title: 'ID Verification' }
      />
      <Stack.Screen;
        name='confirmation';
        options={ headerShown: true, title: 'Verification Complete' }
      />
      <Stack.Screen name='trust-score' options={ headerShown: true, title: 'Trust Score' } />
    </Stack>
  );
}