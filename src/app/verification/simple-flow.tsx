import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, Dimensions, SafeAreaView } from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Shield, CheckCircle, Clock, ArrowRight, Star, Users, Home, Lock, ArrowLeft } from 'lucide-react-native';

// Design system imports;
import { useTheme } from '@design-system';

// Auth imports with fallback;
import { useAuthAdapter } from '@context/AuthContextAdapter';
import { zeroVerificationService } from '@services/zeroVerificationService';

const { width } = Dimensions.get('window');

interface VerificationStep { id: string; title: string; description: string; benefit: string; icon: React.ComponentType<any>; status: 'pending' | 'in_progress' | 'completed' | 'optional'; estimatedTime: string; required: boolean,
}
export default function SimpleVerificationFlow() { const theme = useTheme(); const styles = createStyles(theme); const router = useRouter(); const insets = useSafeAreaInsets(); const params = useLocalSearchParams(); // Auth with proper fallback const auth = useAuthAdapter(); const [loading, setLoading] = useState(true); const [currentStep, setCurrentStep] = useState(0); const [completedSteps, setCompletedSteps] = useState<string[]>([]); // Check if we came from onboarding const fromOnboarding = params.from === 'onboarding'; // Simplified verification steps - only 3 essential steps const [verificationSteps, setVerificationSteps] = useState<VerificationStep[]>([ { id: 'email', title: 'Verify Your Email', description: 'Confirm your email address to secure your account', benefit: 'Get important updates and password reset options', icon: Shield, status: 'pending', estimatedTime: '1 minute', required: true, }, { id: 'phone', title: 'Add Your Phone Number', description: 'Add a phone number for account security', benefit: 'Quick login and emergency contact for roommates', icon: Lock, status: 'pending', estimatedTime: '2 minutes', required: true, }, { id: 'identity', title: 'Verify Your Identity', description: 'Upload a photo ID to build trust with roommates', benefit: 'Show you\'re a real person and increase match success by 3x', icon: Users, status: 'pending', estimatedTime: '3 minutes', required: false, // Make this optional to reduce friction }, ]); useEffect(() => { if (auth?.authState?.user?.id) { loadVerificationStatus(); } }, [auth?.authState?.user?.id]); const loadVerificationStatus = async () => { try { setLoading(true); if (!auth?.authState?.user?.id) { console.warn('User not available for verification status check'); setLoading(false); return null; } // Get current verification status const statusResponse = await zeroVerificationService.getVerificationStatus( auth.authState.user.id ); if (statusResponse.success && statusResponse.data) { const status = statusResponse.data; const completed: string[] = []; // Update step statuses based on current verification state const updatedSteps = verificationSteps.map(step => { let newStatus: VerificationStep['status'] = 'pending'; switch (step.id) { case 'email': if (status.email) { newStatus = 'completed'; completed.push(step.id); } break; case 'phone': if (status.phone) { newStatus = 'completed'; completed.push(step.id); } break; case 'identity': if (status.identity) { newStatus = 'completed'; completed.push(step.id); } break; } return { ...step, status: newStatus }; }); setVerificationSteps(updatedSteps); setCompletedSteps(completed); // Set current step to first incomplete required step const nextStepIndex = updatedSteps.findIndex( step => step.status !== 'completed' && step.required ); setCurrentStep(nextStepIndex >= 0 ? nextStepIndex : updatedSteps.length); } } catch (error) { console.error('Error loading verification status:', error); Alert.alert( 'Connection Issue', 'We couldn\'t load your verification status. Please try again.', [{ text: 'OK' }] ); } finally { setLoading(false); } }; const handleBackNavigation = () => { try { if (fromOnboarding) { // Go back to onboarding if (router.canGoBack()) { router.back(); } else { router.replace('/unified-onboarding'); } } else { // Check if user can access main app or should go to onboarding const profileCompletion = auth?.authState?.user?.profile_completion || 0; if (profileCompletion >= 60) { // User can access main app router.replace('/(tabs)'); } else { // User needs to complete onboarding router.replace('/unified-onboarding'); } } } catch (error) { console.error('Navigation error:', error); // Fallback navigation router.replace('/unified-onboarding'); } }; const handleStepPress = async (stepIndex: number) => { const step = verificationSteps[stepIndex]; if (step.status === 'completed') { Alert.alert( 'Already Completed', `You've already completed ${step.title}. Great job!`, [{ text: 'OK' }] ); return null; } setCurrentStep(stepIndex); // Navigate to appropriate verification screen based on step switch (step.id) { case 'email': await handleEmailVerification(); break; case 'phone': await handlePhoneVerification(); break; case 'identity': await handleIdentityVerification(); break; } }; const handleEmailVerification = async () => { try { if (!auth?.authState?.user?.email) { Alert.alert('Error', 'No email address found. Please update your profile.'); return null; } const response = await zeroVerificationService.startEmailVerification( auth.authState.user.email ); if (response.success) { Alert.alert( 'Email Sent!', 'We\'ve sent a verification link to your email. Please check your inbox and click the link to verify.', [ { text: 'OK', onPress: () => { // Mark as in progress and refresh status in a few seconds setTimeout(loadVerificationStatus, 3000); } } ] ); } else { Alert.alert('Error', response.error || 'Failed to send verification email'); } } catch (error) { console.error('Email verification error:', error); Alert.alert('Error', 'Something went wrong. Please try again.'); } }; const handlePhoneVerification = async () => { Alert.alert( 'Add Phone Number', 'You\'ll be redirected to add and verify your phone number.', [ { text: 'Cancel', style: 'cancel' }, { text: 'Continue', onPress: () => { // For now, navigate to profile to add phone number // In future, could create dedicated phone verification screen router.push('/(tabs)/profile' as any); } } ] ); }; const handleIdentityVerification = async () => { try { setLoading(true); if (!auth?.authState?.user?.id) { Alert.alert('Error', 'Please log in to continue verification'); return null; } // Instead of navigating to complex ID verification, use manual verification Alert.alert( 'Identity Verification', 'We\'ll guide you through a simple manual verification process. This helps build trust with potential roommates.', [ { text: 'Cancel', style: 'cancel', }, { text: 'Continue', onPress: () => { // Navigate to manual verification dashboard instead router.push('/verification/confirmation?type=identity' as any); }, }, ] ); } catch (error) { console.error('Error starting identity verification:', error); Alert.alert('Error', 'Unable to start verification. Please try again.'); } finally { setLoading(false); } }; const handleSkipOptional = () => { Alert.alert( 'Skip Identity Verification?', 'You can always complete this later, but verified users get 3x more matches and are more trusted by potential roommates.', [ { text: 'Skip for Now', onPress: () => router.push('/(tabs)/browse' as any) }, { text: 'Continue Verification', style: 'default' } ] ); }; const handleContinueToApp = () => { try { Alert.alert( 'Great Progress!', 'You\'ve completed the essential verifications. You can always add more verification later to increase your trust score.', [ { text: 'Start Browsing', onPress: () => { try { router.replace('/(tabs)/browse'); } catch (navError) { console.error('Navigation error to browse:', navError); router.replace('/(tabs)'); } } } ] ); } catch (error) { console.error('Error in handleContinueToApp:', error); // Fallback navigation router.replace('/(tabs)'); } }; const renderProgressBar = () => { const requiredSteps = verificationSteps.filter(step => step.required); const completedRequired = completedSteps.filter(stepId => requiredSteps.some(step => step.id === stepId) ).length; const progress = completedRequired / requiredSteps.length; return ( <View style={styles.progressContainer}> <View style={styles.progressBar}> <View style={{[ styles.progressFill, { width: `${progress * 100}%` } ]} /}> </View> <Text style={styles.progressText}> {completedRequired} of {requiredSteps.length} required steps completed </Text> </View> ); }; const renderVerificationStep = (step: VerificationStep, index: number) => { const IconComponent = step.icon; const isCompleted = step.status === 'completed'; const isCurrent = index === currentStep; const isOptional = !step.required; return ( <TouchableOpacity key={step.id} style={[ styles.stepContainer, isCurrent && styles.currentStepContainer, isCompleted && styles.completedStepContainer, ]} onPress={() => handleStepPress(index)} activeOpacity={0.7} > <View style={styles.stepHeader}> <View style={[ styles.stepIcon, isCompleted && styles.completedStepIcon, isCurrent && styles.currentStepIcon, ]}> {isCompleted ? ( <CheckCircle size={24} color={{theme.colors.success} /}> ) : ( <IconComponent size={24} color={{isCurrent ? theme.colors.primary : theme.colors.textSecondary} /}> )} </View> <View style={styles.stepContent}> <View style={styles.stepTitleRow}> <Text style={[ styles.stepTitle, isCompleted && styles.completedStepTitle, isCurrent && styles.currentStepTitle, ]}> {step.title} </Text> {isOptional && ( <Text style={styles.optionalBadge}>Optional</Text> )} </View> <Text style={styles.stepDescription}>{step.description}</Text> <View style={styles.stepBenefitContainer}> <Star size={14} color={{theme.colors.warning} /}> <Text style={styles.stepBenefit}>{step.benefit}</Text> </View> <Text style={styles.stepTime}>⏱️ {step.estimatedTime}</Text> </View> <ArrowRight size={20} color={{isCompleted ? theme.colors.success : theme.colors.textSecondary} /}> </View> </TouchableOpacity> ); }; const renderActionButtons = () => { const requiredSteps = verificationSteps.filter(step => step.required); const completedRequired = completedSteps.filter(stepId => requiredSteps.some(step => step.id === stepId) ).length; const allRequiredCompleted = completedRequired === requiredSteps.length; return ( <View style={styles.actionContainer}> {allRequiredCompleted ? ( <TouchableOpacity style={styles.primaryButton} onPress={{handleContinueToApp} }> <Text style={styles.primaryButtonText}>Start Finding Roommates</Text> <Home size={20} color={{theme.colors.background} /}> </TouchableOpacity> ) : ( <TouchableOpacity { style={styles.secondaryButton} onPress={{handleSkipOptional} }> <Text style={styles.secondaryButtonText}>Skip Optional Steps</Text> </TouchableOpacity> )} </View> ); }; if (loading) { return ( <SafeAreaView style={styles.container}> <View style={styles.loadingContainer}> <Clock size={48} color={{theme.colors.primary} /}> <Text style={styles.loadingText}>Loading your verification status...</Text> </View> </SafeAreaView> ); } return ( <SafeAreaView style={styles.container}> {/* Custom Header with Back Navigation */} <View style={styles.customHeader}> <TouchableOpacity style={styles.backButton} onPress={{handleBackNavigation} }> <ArrowLeft size={24} color={{theme.colors.text} /}> </TouchableOpacity> <Text style={styles.headerTitle}>Verification</Text> <View style={{styles.headerRight} /}> </View> <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}> <View style={styles.header}> <Shield size={32} color={{theme.colors.primary} /}> <Text style={styles.title}>Build Trust with Roommates</Text> <Text style={styles.subtitle}> Complete these simple steps to show you're a real, trustworthy person. Verified users get 3x more matches! </Text> </View> {renderProgressBar()} <View style={styles.stepsContainer}> {verificationSteps.map((step, index) => renderVerificationStep(step, index))} </View> {renderActionButtons()} <View style={styles.helpContainer}> <Text style={styles.helpText}> Need help? Contact our support team anytime. </Text> </View> </ScrollView> </SafeAreaView> );
}
const createStyles = (theme: any) => StyleSheet.create({ container: { flex: 1, backgroundColor: theme.colors.background, }, customHeader: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', paddingHorizontal: theme.spacing.lg, paddingVertical: theme.spacing.md, borderBottomWidth: 1, borderBottomColor: theme.colors.border, backgroundColor: theme.colors.background, }, backButton: { padding: theme.spacing.sm, borderRadius: theme.borderRadius.md, backgroundColor: theme.colors.surface, }, headerTitle: { fontSize: 18, fontWeight: '600', color: theme.colors.text, }, headerRight: { width: 40, // Same width as back button for centering }, scrollView: { flex: 1, }, loadingContainer: { flex: 1, justifyContent: 'center', alignItems: 'center', padding: theme.spacing.xl, }, loadingText: { fontSize: 16, color: theme.colors.text, marginTop: theme.spacing.md, textAlign: 'center', }, header: { padding: theme.spacing.xl, alignItems: 'center', }, title: { fontSize: 24, fontWeight: 'bold', color: theme.colors.text, marginTop: theme.spacing.md, textAlign: 'center', }, subtitle: { fontSize: 16, color: theme.colors.textSecondary, marginTop: theme.spacing.sm, textAlign: 'center', lineHeight: 22, }, progressContainer: { paddingHorizontal: theme.spacing.xl, marginBottom: theme.spacing.lg, }, progressBar: { height: 8, backgroundColor: theme.colors.border, borderRadius: 4, overflow: 'hidden', }, progressFill: { height: '100%', backgroundColor: theme.colors.success, borderRadius: 4, }, progressText: { fontSize: 14, color: theme.colors.textSecondary, marginTop: theme.spacing.sm, textAlign: 'center', }, stepsContainer: { paddingHorizontal: theme.spacing.lg, }, stepContainer: { backgroundColor: theme.colors.surface, borderRadius: theme.borderRadius.lg, marginBottom: theme.spacing.md, padding: theme.spacing.lg, borderWidth: 1, borderColor: theme.colors.border, }, currentStepContainer: { borderColor: theme.colors.primary, backgroundColor: theme.colors.primaryLight, }, completedStepContainer: { borderColor: theme.colors.success, backgroundColor: theme.colors.successLight, }, stepHeader: { flexDirection: 'row', alignItems: 'flex-start', }, stepIcon: { width: 48, height: 48, borderRadius: 24, backgroundColor: theme.colors.background, justifyContent: 'center', alignItems: 'center', marginRight: theme.spacing.md, }, currentStepIcon: { backgroundColor: theme.colors.primary, }, completedStepIcon: { backgroundColor: theme.colors.success, }, stepContent: { flex: 1, }, stepTitleRow: { flexDirection: 'row', alignItems: 'center', marginBottom: theme.spacing.xs, }, stepTitle: { fontSize: 18, fontWeight: '600', color: theme.colors.text, flex: 1, }, currentStepTitle: { color: theme.colors.primary, }, completedStepTitle: { color: theme.colors.success, }, optionalBadge: { fontSize: 12, color: theme.colors.warning, backgroundColor: theme.colors.warningLight, paddingHorizontal: theme.spacing.sm, paddingVertical: 2, borderRadius: theme.borderRadius.sm, marginLeft: theme.spacing.sm, }, stepDescription: { fontSize: 14, color: theme.colors.textSecondary, marginBottom: theme.spacing.sm, lineHeight: 20, }, stepBenefitContainer: { flexDirection: 'row', alignItems: 'center', marginBottom: theme.spacing.sm, }, stepBenefit: { fontSize: 14, color: theme.colors.text, marginLeft: theme.spacing.xs, fontStyle: 'italic', flex: 1, }, stepTime: { fontSize: 12, color: theme.colors.textSecondary, }, actionContainer: { padding: theme.spacing.xl, }, primaryButton: { backgroundColor: theme.colors.primary, paddingVertical: theme.spacing.lg, paddingHorizontal: theme.spacing.xl, borderRadius: theme.borderRadius.lg, flexDirection: 'row', justifyContent: 'center', alignItems: 'center', }, primaryButtonText: { color: theme.colors.background, fontSize: 16, fontWeight: '600', marginRight: theme.spacing.sm, }, secondaryButton: { backgroundColor: 'transparent', paddingVertical: theme.spacing.md, paddingHorizontal: theme.spacing.lg, borderRadius: theme.borderRadius.lg, borderWidth: 1, borderColor: theme.colors.border, }, secondaryButtonText: { color: theme.colors.textSecondary, fontSize: 16, textAlign: 'center', }, helpContainer: { padding: theme.spacing.xl, alignItems: 'center', }, helpText: { fontSize: 14, color: theme.colors.textSecondary, textAlign: 'center', },
}); ;