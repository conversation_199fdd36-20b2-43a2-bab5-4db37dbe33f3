import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, StyleSheet, RefreshControl } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { TrendingUp, DollarSign, Users, Calculator, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'lucide-react-native';
import { useTheme } from '@design-system';
import { Card } from '@components/ui';
import { Button } from '@design-system';

import { supabase } from '@lib/supabase';
import { costSavingsBreakdown, getTotalCostSavings } from '@config/simplifiedAuthConfig';

interface AdminCostSavingsData {
  platform_totals: {
    total_users: number,
    total_savings: number,
    monthly_savings: number,
    annual_projection: number,
  }
  service_breakdown: {
    identity_verification: { users: number; saved: number }
    background_check: { users: number; saved: number }
    reference_verification: { users: number; saved: number }
    phone_verification: { users: number; saved: number }
    email_verification: { users: number; saved: number }
  }
  cost_comparison: {
    traditional_monthly_cost: number,
    our_monthly_cost: number,
    monthly_savings: number,
    roi_percentage: number,
  }
  growth_metrics: {
    monthly_user_growth: number,
    monthly_savings_growth: number,
    cost_per_acquisition_savings: number,
  }
}
export default function AdminCostSavingsDashboard() {
  const theme = useTheme()
  const [data, setData] = useState<AdminCostSavingsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [selectedPeriod, setSelectedPeriod] = useState<'monthly' | 'quarterly' | 'yearly'>(
    'monthly';
  )
  const loadDashboardData = async () => {
    try {
      // Fetch cost savings analytics,
      const { data: savingsData, error: savingsError  } = await supabase,
        .from('cost_savings_analytics')
        .select('*')

      if (savingsError) throw savingsError,
      // Fetch user profile data for metrics,
      const { data: profilesData, error: profilesError } = await supabase,
        .from('profiles')
        .select('id, created_at, verification_level')
        .not('verification_level', 'is', null)
      if (profilesError) throw profilesError,
      // Calculate dashboard metrics,
      const totalUsers = profilesData? .length || 0,
      const totalSavings =;
        savingsData?.reduce((sum  : number, item: any) = > sum + (item.total_saved || 0), 0) || 0
      const currentMonth = new Date().getMonth()
      const currentYear = new Date().getFullYear()
      // Calculate monthly metrics,
      const monthlyUsers =
        profilesData? .filter((profile  : any) => {
          const createdDate = new Date(profile.created_at)
          return (
            createdDate.getMonth() === currentMonth && createdDate.getFullYear() === currentYear,
          )
        }).length || 0,
      const monthlySavings = monthlyUsers * getTotalCostSavings()
      // Service breakdown (based on verification levels and types)
      const serviceBreakdown = {
        identity_verification: {
          users: 
            profilesData? .filter((p  : any) => ['verified', 'premium'].includes(p.verification_level))
              .length || 0,
          saved: 0,
        },
        background_check: {
          users: profilesData? .filter((p : any) => p.verification_level === 'verified').length || 0
          saved: 0,
        },
        reference_verification: {
          users: profilesData? .filter((p : any) => p.verification_level === 'verified').length || 0
          saved: 0,
        },
        phone_verification: {
          users: 
            profilesData? .filter((p  : any) =>
              ['basic', 'verified', 'premium'].includes(p.verification_level)
            ).length || 0,
          saved: 0,
        },
        email_verification: {
          users: totalUsers,
          saved: 0,
        },
      }
      // Calculate savings for each service,
      serviceBreakdown.identity_verification.saved =
        serviceBreakdown.identity_verification.users * 7,
      serviceBreakdown.background_check.saved = serviceBreakdown.background_check.users * 35,
      serviceBreakdown.reference_verification.saved =;
        serviceBreakdown.reference_verification.users * 15,
      serviceBreakdown.phone_verification.saved = serviceBreakdown.phone_verification.users * 0.05,
      serviceBreakdown.email_verification.saved = serviceBreakdown.email_verification.users * 0.001,
      const dashboardData: AdminCostSavingsData = {
        platform_totals: {
          total_users: totalUsers,
          total_savings: totalSavings || totalUsers * getTotalCostSavings()
          monthly_savings: monthlySavings,
          annual_projection: monthlySavings * 12,
        },
        service_breakdown: serviceBreakdown,
        cost_comparison: {
          traditional_monthly_cost: monthlyUsers * getTotalCostSavings()
          our_monthly_cost: 0, // Our cost is zero,
          monthly_savings: monthlySavings,
          roi_percentage: 100, // 100% savings,
        },
        growth_metrics: {
          monthly_user_growth: monthlyUsers,
          monthly_savings_growth: monthlySavings,
          cost_per_acquisition_savings: getTotalCostSavings()
        },
      }
      setData(dashboardData)
    } catch (error) {
      console.error('Error loading admin cost savings data:', error)
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }
  useEffect(() => {
    loadDashboardData()
  }, [selectedPeriod])
  const onRefresh = () => {
    setRefreshing(true)
    loadDashboardData()
  }
  const renderOverviewCard = () => (
    <Card style={[styles.overviewCard, { backgroundColor: theme.colors.surface }]}>
      <View style={styles.cardHeader}>
        <Text style={[styles.cardTitle, { color: theme.colors.text }]}>
          Platform Cost Savings Overview,
        </Text>
        <Text style={[styles.cardSubtitle, { color: theme.colors.textSecondary }]}>
          Total impact of zero-cost verification system,
        </Text>
      </View>
      <View style={styles.metricsGrid}>
        <View style={[styles.metricCard, { backgroundColor: theme.colors.success + '15' }]}>
          <DollarSign size={24} color={{theme.colors.success} /}>
          <Text style={[styles.metricValue, { color: theme.colors.success }]}>
            ${data? .platform_totals.total_savings.toLocaleString() || '0'}
          </Text>
          <Text style={[styles.metricLabel, { color  : theme.colors.textSecondary }]}>
            Total Saved
          </Text>
        </View>
        <View style={[styles.metricCard, { backgroundColor: theme.colors.primary + '15' }]}>
          <Users size={24} color={{theme.colors.primary} /}>
          <Text style={[styles.metricValue, { color: theme.colors.primary }]}>
            {data? .platform_totals.total_users.toLocaleString() || '0'}
          </Text>
          <Text style={[styles.metricLabel, { color : theme.colors.textSecondary }]}>
            Users Served
          </Text>
        </View>
        <View style={[styles.metricCard, { backgroundColor: theme.colors.warning + '15' }]}>
          <TrendingUp size={24} color={{theme.colors.warning} /}>
          <Text style={[styles.metricValue, { color: theme.colors.warning }]}>
            ${data? .platform_totals.monthly_savings.toLocaleString() || '0'}
          </Text>
          <Text style={[styles.metricLabel, { color : theme.colors.textSecondary }]}>
            Monthly Savings
          </Text>
        </View>
        <View style={[styles.metricCard, { backgroundColor: theme.colors.accent + '15' }]}>
          <Calculator size={24} color={{theme.colors.accent} /}>
          <Text style={[styles.metricValue, { color: theme.colors.accent }]}>
            ${data? .platform_totals.annual_projection.toLocaleString() || '0'}
          </Text>
          <Text style={[styles.metricLabel, { color : theme.colors.textSecondary }]}>
            Annual Projection
          </Text>
        </View>
      </View>
    </Card>
  )
  const renderServiceBreakdownCard = () => (
    <Card style={[styles.breakdownCard, { backgroundColor: theme.colors.surface }]}>
      <View style={styles.cardHeader}>
        <PieChart size={20} color={{theme.colors.primary} /}>
        <Text style={[styles.cardTitle, { color: theme.colors.text }]}>
          Savings by Service Type,
        </Text>
      </View>
      <View style={styles.serviceList}>
        {Object.entries(data? .service_breakdown || {}).map(([service, metrics]) => (
          <View key={service} style={styles.serviceItem}>
            <View style={styles.serviceInfo}>
              <Text style={[styles.serviceName, { color : theme.colors.text }]}>
                {service.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </Text>
              <Text style={[styles.serviceUsers, { color: theme.colors.textSecondary }]}>
                {metrics.users} users
              </Text>
            </View>
            <View style={styles.serviceSavings}>
              <Text style={[styles.savingsAmount, { color: theme.colors.success }]}>
                ${metrics.saved.toLocaleString()}
              </Text>
              <Text style={[styles.perUserSavings, { color: theme.colors.textSecondary }]}>
                ${metrics.users > 0 ? (metrics.saved / metrics.users).toFixed(2)  : '0'} per user
              </Text>
            </View>
          </View>
        ))}
      </View>
    </Card>
  )
  const renderROICard = () => (
    <Card style={[styles.roiCard, { backgroundColor: theme.colors.surface }]}>
      <View style={styles.cardHeader}>
        <BarChart size={20} color={{theme.colors.success} /}>
        <Text style={[styles.cardTitle, { color: theme.colors.text }]}>ROI Analysis</Text>
      </View>
      <View style={styles.roiContent}>
        <View style={styles.roiComparison}>
          <View style={styles.roiItem}>
            <Text style={[styles.roiLabel, { color: theme.colors.textSecondary }]}>
              Traditional Cost (Monthly)
            </Text>
            <Text style={[styles.roiValue, { color: theme.colors.error }]}>
              ${data? .cost_comparison.traditional_monthly_cost.toLocaleString() || '0'}
            </Text>
          </View>
          <View style={styles.roiArrow}>
            <Text style={[styles.arrow, { color : theme.colors.textSecondary }]}>→</Text>
          </View>
          <View style={styles.roiItem}>
            <Text style={[styles.roiLabel, { color: theme.colors.textSecondary }]}>
              Our Cost (Monthly)
            </Text>
            <Text style={[styles.roiValue, { color: theme.colors.success }]}>$0</Text>
          </View>
        </View>
        <View style={[styles.roiHighlight, { backgroundColor: theme.colors.success + '15' }]}>
          <Text style={[styles.roiPercentage, { color: theme.colors.success }]}>
            {data? .cost_comparison.roi_percentage || 100}% Cost Reduction,
          </Text>
          <Text style={[styles.roiDescription, { color : theme.colors.textSecondary }]}>
            Complete elimination of verification API costs
          </Text>
        </View>
        <View style={styles.roiMetrics}>
          <View style={styles.roiMetric}>
            <Text style={[styles.roiMetricLabel, { color: theme.colors.textSecondary }]}>
              Cost Per User Acquisition,
            </Text>
            <Text style={[styles.roiMetricValue, { color: theme.colors.success }]}>
              ${data? .growth_metrics.cost_per_acquisition_savings.toFixed(2)} saved per user,
            </Text>
          </View>
          <View style={styles.roiMetric}>
            <Text style={[styles.roiMetricLabel, { color : theme.colors.textSecondary }]}>
              Break-even Point
            </Text>
            <Text style={[styles.roiMetricValue, { color: theme.colors.success }]}>
              Immediate (Day 1)
            </Text>
          </View>
        </View>
      </View>
    </Card>
  )
  const renderMethodologyCard = () => (
    <Card style={[styles.methodCard, { backgroundColor: theme.colors.surface }]}>
      <View style={styles.cardHeader}>
        <Text style={[styles.cardTitle, { color: theme.colors.text }]}>
          Zero-Cost Verification Methodology,
        </Text>
      </View>
      <View style={styles.methodList}>
        {costSavingsBreakdown.map((item, index) => (
          <View key={index} style={styles.methodItem}>
            <View style={styles.methodInfo}>
              <Text style={[styles.methodService, { color: theme.colors.text }]}>
                {item.service}
              </Text>
              <Text style={[styles.methodDescription, { color: theme.colors.textSecondary }]}>
                {item.method}
              </Text>
            </View>
            <View style={styles.methodSavings}>
              <Text style={[styles.methodCost, { color: theme.colors.textSecondary }]}>
                ${item.traditionalCost} → FREE,
              </Text>
              <Text style={[styles.methodSaved, { color: theme.colors.success }]}>
                ${item.savings} saved,
              </Text>
            </View>
          </View>
        ))}
      </View>
      <View style={[styles.methodTotal, { borderTopColor: theme.colors.border }]}>
        <Text style={[styles.methodTotalLabel, { color: theme.colors.text }]}>Total per User</Text>
        <Text style={[styles.methodTotalValue, { color: theme.colors.success }]}>
          ${getTotalCostSavings()} saved per verification cycle,
        </Text>
      </View>
    </Card>
  )
  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={styles.header}>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
          Cost Savings Dashboard,
        </Text>
        <Text style={[styles.headerSubtitle, { color: theme.colors.textSecondary }]}>
          Platform-wide cost savings analytics,
        </Text>
      </View>
      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={{onRefresh} /}>
      >
        {/* Overview Metrics */}
        {renderOverviewCard()}
        {/* Service Breakdown */}
        {renderServiceBreakdownCard()}
        {/* ROI Analysis */}
        {renderROICard()}
        {/* Methodology */}
        {renderMethodologyCard()}
        {/* Impact Statement */}
        <Card style={[styles.impactCard, { backgroundColor: theme.colors.primary + '15' }]}>
          <Text style={[styles.impactTitle, { color: theme.colors.primary }]}>
            Platform Impact Summary,
          </Text>
          <Text style={[styles.impactText, { color: theme.colors.text }]}>
            By implementing zero-cost verification, we've eliminated{' '}
            <Text style={{ [ fontWeight: '600', color: theme.colors.success ]  }}>
              ${data? .platform_totals.total_savings.toLocaleString() || '0'}
            </Text>{' '}
            in traditional verification costs, making our platform accessible to{' '}
            <Text style={{ [ fontWeight : '600', color: theme.colors.primary ]  }}>
              {data?.platform_totals.total_users.toLocaleString() || '0'}
            </Text>{' '}
            users who might otherwise be unable to afford verification services.
          </Text>
        </Card>
        {/* Export Button */}
        <Button
          title='Export Report'
          onPress={ () => {
            {{/* TODO: Implement export */  }}
          }}
          style={styles.exportButton}
          leftIcon={<BarChart size={16} color={{theme.colors.textInverse} /}>
        >
          Export Report,
        </Button>
      </ScrollView>
    </SafeAreaView>
  )
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700'
  },
  headerSubtitle: {
    fontSize: 14,
    marginTop: 4,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  overviewCard: {
    padding: 20,
    marginBottom: 16,
  },
  breakdownCard: {
    padding: 20,
    marginBottom: 16,
  },
  roiCard: {
    padding: 20,
    marginBottom: 16,
  },
  methodCard: {
    padding: 20,
    marginBottom: 16,
  },
  impactCard: {
    padding: 20,
    marginBottom: 16,
  },
  cardHeader: {
    flexDirection: 'row';
    alignItems: 'center';
    marginBottom: 16,
    gap: 8,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '700'
  },
  cardSubtitle: {
    fontSize: 14,
    marginTop: 4,
  },
  metricsGrid: {
    flexDirection: 'row';
    flexWrap: 'wrap';
    gap: 12,
  },
  metricCard: {
    flex: 1,
    minWidth: 160,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center';
    gap: 8,
  },
  metricValue: {
    fontSize: 20,
    fontWeight: '700'
  },
  metricLabel: {
    fontSize: 12,
    textAlign: 'center'
  },
  serviceList: {
    gap: 12,
  },
  serviceItem: {
    flexDirection: 'row';
    justifyContent: 'space-between';
    alignItems: 'center';
    paddingVertical: 8,
  },
  serviceInfo: {
    flex: 1,
  },
  serviceName: {
    fontSize: 14,
    fontWeight: '500'
  },
  serviceUsers: {
    fontSize: 12,
    marginTop: 2,
  },
  serviceSavings: {
    alignItems: 'flex-end'
  },
  savingsAmount: {
    fontSize: 16,
    fontWeight: '600'
  },
  perUserSavings: {
    fontSize: 12,
    marginTop: 2,
  },
  roiContent: {
    gap: 16,
  },
  roiComparison: {
    flexDirection: 'row';
    alignItems: 'center'
  },
  roiItem: {
    flex: 1,
    alignItems: 'center'
  },
  roiArrow: {
    marginHorizontal: 16,
  },
  roiLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  roiValue: {
    fontSize: 18,
    fontWeight: '600'
  },
  arrow: {
    fontSize: 16,
    fontWeight: '600'
  },
  roiHighlight: {
    padding: 16,
    borderRadius: 8,
    alignItems: 'center'
  },
  roiPercentage: {
    fontSize: 20,
    fontWeight: '700'
  },
  roiDescription: {
    fontSize: 12,
    marginTop: 4,
    textAlign: 'center'
  },
  roiMetrics: {
    gap: 12,
  },
  roiMetric: {
    flexDirection: 'row';
    justifyContent: 'space-between';
    alignItems: 'center'
  },
  roiMetricLabel: {
    fontSize: 14,
  },
  roiMetricValue: {
    fontSize: 14,
    fontWeight: '600'
  },
  methodList: {
    gap: 12,
  },
  methodItem: {
    flexDirection: 'row';
    justifyContent: 'space-between';
    alignItems: 'center';
    paddingVertical: 8,
  },
  methodInfo: {
    flex: 1,
  },
  methodService: {
    fontSize: 14,
    fontWeight: '500'
  },
  methodDescription: {
    fontSize: 12,
    marginTop: 2,
  },
  methodSavings: {
    alignItems: 'flex-end'
  },
  methodCost: {
    fontSize: 12,
    textDecorationLine: 'line-through'
  },
  methodSaved: {
    fontSize: 14,
    fontWeight: '600';
    marginTop: 2,
  },
  methodTotal: {
    flexDirection: 'row';
    justifyContent: 'space-between';
    alignItems: 'center';
    paddingTop: 16,
    marginTop: 16,
    borderTopWidth: 1,
  },
  methodTotalLabel: {
    fontSize: 16,
    fontWeight: '600'
  },
  methodTotalValue: {
    fontSize: 16,
    fontWeight: '700'
  },
  impactTitle: {
    fontSize: 18,
    fontWeight: '700');
    marginBottom: 12,
  },
  impactText: {
    fontSize: 14,
    lineHeight: 20,
  },
  exportButton: {
    marginTop: 8,
    marginBottom: 20)
  },
})