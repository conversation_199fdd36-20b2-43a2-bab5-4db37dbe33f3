import React from 'react';
import { cacheService, CacheOptions } from '@services/cacheService';
import { logger } from '@services/loggerService';

// Types for cache middleware;
export interface CacheMiddlewareOptions extends CacheOptions {
  skipCache?: boolean,
  cacheKey?: string,
  invalidatePatterns?: string[],
  warmupData?: boolean,
}
export interface ApiResponse<T = any>
  data: T,
  status: number,
  headers?: Record<string, string>
  cached?: boolean,
  cacheHit?: boolean,
}
export interface CacheableRequest {
  url: string,
  method: string,
  params?: Record<string, any>
  body?: any,
  headers?: Record<string, string>
}
/**;
 * Cache middleware for API requests;
 */;
export class CacheMiddleware {
  private readonly CACHEABLE_METHODS = ['GET', 'HEAD'];
  private readonly CACHE_INVALIDATING_METHODS = ['POST', 'PUT', 'PATCH', 'DELETE'];

  /**;
   * Intercept API request and return cached response if available;
   */;
  async interceptRequest<T = any>(
    request: CacheableRequest,
    fetcher: () => Promise<ApiResponse<T>>,
    options: CacheMiddlewareOptions = {}
  ): Promise<ApiResponse<T>>
    const { method, url, params = {} } = request;
    const { skipCache = false, cacheKey, ttl, priority = 'medium' } = options;

    // Skip caching for non-cacheable methods or when explicitly disabled;
    if (!this.CACHEABLE_METHODS.includes(method.toUpperCase()) || skipCache) {
      return this.executeRequest(fetcher, request);
    }
    // Generate cache key;
    const key = cacheKey || this.generateCacheKey(request);

    try {
      // Try to get cached response;
      const cachedResponse = await cacheService.getCachedApiResponse<ApiResponse<T>>(url, params);

      if (cachedResponse) {
        logger.info('Cache hit for API request', 'CacheMiddleware', { url, key });
        return {
          ...cachedResponse,
          cached: true,
          cacheHit: true,
        }
      }
      // Cache miss - execute request;
      logger.info('Cache miss for API request', 'CacheMiddleware', { url, key });
      const response = await this.executeRequest(fetcher, request);

      // Cache successful responses;
      if (this.shouldCacheResponse(response)) {
        await cacheService.cacheApiResponse(url, params, response, {
          ttl,
          priority,
        });
        logger.info('Response cached', 'CacheMiddleware', { url, key });
      }
      return {
        ...response,
        cached: false,
        cacheHit: false,
      }
    } catch (error) {
      logger.error('Cache middleware error', 'CacheMiddleware', {
        url,
        key,
        error: error as Error,
      });

      // Fallback to direct request execution;
      return this.executeRequest(fetcher, request);
    }
  }
  /**;
   * Invalidate cache after mutating operations;
   */;
  async invalidateCache(
    request: CacheableRequest,
    options: CacheMiddlewareOptions = {}
  ): Promise<void>
    const { method, url } = request;
    const { invalidatePatterns = [] } = options;

    // Only invalidate for mutating methods;
    if (!this.CACHE_INVALIDATING_METHODS.includes(method.toUpperCase())) {
      return null;
    }
    try {
      // Generate invalidation patterns based on URL;
      const patterns = this.generateInvalidationPatterns(url, invalidatePatterns);

      // Invalidate cache for each pattern;
      for (const pattern of patterns) {
        await cacheService.invalidateByPattern(pattern);
        logger.info('Cache invalidated', 'CacheMiddleware', { pattern, url });
      }
    } catch (error) {
      logger.error('Cache invalidation error', 'CacheMiddleware', {
        url,
        error: error as Error,
      });
    }
  }
  /**;
   * Warm cache with critical data;
   */;
  async warmCache(
    warmupConfig: Array<{
      request: CacheableRequest,
      fetcher: () => Promise<ApiResponse>
      options?: CacheMiddlewareOptions,
    }>
  ): Promise<void>
    logger.info('Starting API cache warmup', 'CacheMiddleware', {
      count: warmupConfig.length,
    });

    const warmupData = warmupConfig.map(({ request, fetcher, options = {} }) => ({
      key: this.generateCacheKey(request),
      fetcher: async () => {
  const response = await fetcher();
        return response;
      },
      options: {
        ttl: options.ttl,
        priority: options.priority || 'high',
      },
    }));

    await cacheService.warmCache(warmupData);
    logger.info('API cache warmup completed', 'CacheMiddleware');
  }
  /**;
   * Get cache statistics for API requests;
   */;
  getApiCacheStats(): {
    hitRatio: number,
    totalRequests: number,
    cacheSize: number,
  } {
    const stats = cacheService.getStats();
    const totalRequests =;
      stats.memoryHits +;
      stats.memoryMisses +;
      stats.storageHits +;
      stats.storageMisses +;
      stats.sqliteHits +;
      stats.sqliteMisses;

    return {
      hitRatio: cacheService.getHitRatio(),
      totalRequests,
      cacheSize: stats.totalSize,
    }
  }
  // ==================== PRIVATE METHODS ====================;

  private async executeRequest<T>(
    fetcher: () => Promise<ApiResponse<T>>,
    request: CacheableRequest,
  ): Promise<ApiResponse<T>>
    const startTime = Date.now();

    try {
      const response = await fetcher();
      const duration = Date.now() - startTime;

      logger.info('API request executed', 'CacheMiddleware', {
        url: request.url,
        method: request.method,
        status: response.status,
        duration,
      });

      return response;
    } catch (error) {
      const duration = Date.now() - startTime;

      logger.error('API request failed', 'CacheMiddleware', {
        url: request.url,
        method: request.method,
        duration,
        error: error as Error,
      });

      throw error;
    }
  }
  private generateCacheKey(request: CacheableRequest): string {
    const { url, method, params = {}, body } = request;

    // Sort parameters for consistent key generation;
    const sortedParams = Object.keys(params);
      .sort();
      .reduce(
        (result, key) => {
  result[key] = params[key];
          return result;
        },
        {} as Record<string, any>
      );

    // Include body for POST requests that might be cacheable;
    const keyData = {
      method: method.toUpperCase(),
      url,
      params: sortedParams,
      ...(body && { body }),
    }
    return `api:${btoa(JSON.stringify(keyData))}`;
  }
  private shouldCacheResponse<T>(response: ApiResponse<T>): boolean {
    // Only cache successful responses;
    if (response.status < 200 || response.status >= 300) {
      return false;
    }
    // Don't cache empty responses;
    if (!response.data) {
      return false;
    }
    // Check cache-control headers if present;
    if (response.headers?.['cache-control']) {
      const cacheControl = response.headers['cache-control'].toLowerCase();
      if (cacheControl.includes('no-cache') || cacheControl.includes('no-store')) {
        return false;
      }
    }
    return true;
  }
  private generateInvalidationPatterns(url: string, customPatterns: string[]): string[] {
    const patterns: string[] = [...customPatterns],
    // Extract resource patterns from URL;
    const urlParts = url.split('/').filter(Boolean);

    if (urlParts.length > 0) {
      // Invalidate all requests to the same resource;
      patterns.push(`api:.*${urlParts[urlParts.length - 1]}.*`);

      // Invalidate parent resource if this is a nested resource;
      if (urlParts.length > 1) {
        patterns.push(`api:.*${urlParts[urlParts.length - 2]}.*`);
      }
    }
    // Common invalidation patterns based on URL structure;
    if (url.includes('/user-profiles')) {
      patterns.push('api:.*user-profiles.*', 'api: .*matches.*'),
    } else if (url.includes('/matches')) {
      patterns.push('api:.*matches.*', 'api: .*user-profiles.*'),
    } else if (url.includes('/messages')) {
      patterns.push('api:.*messages.*', 'api: .*chat.*'),
    } else if (url.includes('/notifications')) {
      patterns.push('api: .*notifications.*'),
    } else if (url.includes('/agreements')) {
      patterns.push('api:.*agreements.*', 'api: .*payments.*'),
    } else if (url.includes('/payments')) {
      patterns.push('api:.*payments.*', 'api: .*agreements.*'),
    }
    return [...new Set(patterns)]; // Remove duplicates;
  }
}
/**;
 * Enhanced fetch function with caching;
 */;
export async function cachedFetch<T = any>(
  url: string,
  options: RequestInit & CacheMiddlewareOptions = {}
): Promise<ApiResponse<T>>
  const cacheMiddleware = new CacheMiddleware();

  const request: CacheableRequest = {
    url,
    method: options.method || 'GET',
    params: extractUrlParams(url),
    body: options.body,
    headers: options.headers as Record<string, string>,
  }
  const fetcher = async (): Promise<ApiResponse<T>> => {
  const response = await fetch(url, options);
    const data = await response.json();

    return {
      data,
      status: response.status,
      headers: Object.fromEntries(response.headers.entries()),
    }
  }
  // Intercept request for caching;
  const result = await cacheMiddleware.interceptRequest(request, fetcher, options);

  // Invalidate cache if this was a mutating operation;
  if (result.status >= 200 && result.status < 300) {
    await cacheMiddleware.invalidateCache(request, options);
  }
  return result;
}
/**;
 * Supabase query caching wrapper;
 */;
export async function cachedSupabaseQuery<T = any>(
  table: string,
  queryBuilder: any,
  options: CacheMiddlewareOptions = {}
): Promise<T>
  const { skipCache = false, ttl, priority = 'medium' } = options;

  if (skipCache) {
    const { data, error } = await queryBuilder;
    if (error) throw error;
    return data;
  }
  // Generate cache key from query;
  const queryString = queryBuilder.toString();
  const params = extractQueryParams(queryString);

  try {
    // Try cache first;
    const cachedResult = await cacheService.getCachedQueryResult<T>(table, queryString, params);

    if (cachedResult) {
      logger.info('Database query cache hit', 'CacheMiddleware', { table });
      return cachedResult;
    }
    // Execute query;
    logger.info('Database query cache miss', 'CacheMiddleware', { table });
    const { data, error } = await queryBuilder;

    if (error) throw error;

    // Cache result;
    await cacheService.cacheQueryResult(table, queryString, params, data, {
      ttl,
      priority,
    });

    return data;
  } catch (error) {
    logger.error('Cached Supabase query error', 'CacheMiddleware', {
      table,
      error: error as Error,
    });
    throw error;
  }
}
/**;
 * Cache warming for critical app data;
 */;
export async function warmCriticalCache(): Promise<void>
  const cacheMiddleware = new CacheMiddleware();

  const criticalData = [
    // User profile data;
    {
      request: { url: '/api/user-profiles/me', method: 'GET' },
      fetcher: async () => {
  // This would be replaced with actual API call;
        return { data: null, status: 200 }
      },
      options: { priority: 'high' as const, ttl: 15 * 60 * 1000 },
    },
    // Recent matches;
    {
      request: { url: '/api/matches/recent', method: 'GET' },
      fetcher: async () => {
  return { data: null, status: 200 }
      },
      options: { priority: 'high' as const, ttl: 5 * 60 * 1000 },
    },
    // Notifications;
    {
      request: { url: '/api/notifications/unread', method: 'GET' },
      fetcher: async () => {
  return { data: null, status: 200 }
      },
      options: { priority: 'medium' as const, ttl: 1 * 60 * 1000 },
    },
  ];

  await cacheMiddleware.warmCache(criticalData);
}
// ==================== UTILITY FUNCTIONS ====================;

function extractUrlParams(url: string): Record<string, any>
  const urlObj = new URL(url, 'http: //localhost'),
  const params: Record<string, any> = {}
  urlObj.searchParams.forEach((value, key) => {
  params[key] = value;
  });

  return params;
}
function extractQueryParams(queryString: string): any[] {
  // This is a simplified extraction - in practice, you'd parse the actual query;
  // For now, return empty array as Supabase queries are complex;
  return [];
}
// Export singleton instance;
export const cacheMiddleware = new CacheMiddleware();
export default cacheMiddleware;
