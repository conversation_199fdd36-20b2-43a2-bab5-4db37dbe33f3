import React from 'react';
import { supabase } from '@utils/supabaseUtils';

// Types for social interaction system;
export interface SocialPost {
  id: string,
  author_id: string,
  content: string,
  post_type: 'text' | 'image' | 'video' | 'poll' | 'achievement' | 'challenge_update',
  media_urls?: string[],
  poll_options?: {
    options: string[],
    votes: Record<string, number>
  }
  tags?: string[],
  visibility: 'public' | 'friends' | 'private',
  location?: string,
  is_pinned: boolean,
  is_featured: boolean,
  created_at: string,
  updated_at: string,
  // Joined data;
  author?: { id: string,
    first_name: string,
    last_name: string,
    avatar_url?: string }
  reaction_count?: number,
  comment_count?: number,
  user_reaction?: string,
  reactions?: SocialPostReaction[],
  comments?: SocialPostComment[]
}

export interface SocialPostReaction { id: string,
  post_id: string,
  user_id: string,
  reaction_type: 'like' | 'love' | 'laugh' | 'wow' | 'sad' | 'angry',
  created_at: string,
  // Joined data;
  user?: {
    id: string,
    first_name: string,
    last_name: string,
    avatar_url?: string }
}

export interface SocialPostComment { id: string,
  post_id: string,
  user_id: string,
  parent_comment_id?: string,
  content: string,
  media_url?: string,
  is_pinned: boolean,
  created_at: string,
  updated_at: string,
  // Joined data;
  user?: {
    id: string,
    first_name: string,
    last_name: string,
    avatar_url?: string }
  replies?: SocialPostComment[]
}

export interface CommunityChallenge {
  id: string,
  title: string,
  description: string,
  challenge_type: 'fitness' | 'sustainability' | 'social' | 'learning' | 'creativity' | 'general',
  creator_id: string,
  start_date: string,
  end_date: string,
  max_participants?: number,
  reward_points: number,
  reward_description?: string,
  challenge_rules?: {
    requirements: string[],
    scoring: Record<string, any>
    milestones: string[]
  }
  difficulty_level: 'easy' | 'medium' | 'hard' | 'expert',
  is_active: boolean,
  featured_image_url?: string,
  tags?: string[],
  created_at: string,
  updated_at: string,
  // Joined data;
  creator?: { id: string,
    first_name: string,
    last_name: string,
    avatar_url?: string }
  participant_count?: number,
  user_participation?: ChallengeParticipant
}

export interface ChallengeParticipant {
  id: string,
  challenge_id: string,
  user_id: string,
  joined_at: string,
  progress_data: {
    completed_tasks: string[],
    score: number,
    milestones: string[]
  }
  completion_status: 'active' | 'completed' | 'dropped_out',
  completion_date?: string,
  final_score: number,
  rank_position?: number,
  // Joined data;
  user?: { id: string,
    first_name: string,
    last_name: string,
    avatar_url?: string }
  challenge?: CommunityChallenge
}

export interface ChallengeUpdate { id: string,
  challenge_id: string,
  participant_id: string,
  update_type: 'progress' | 'milestone' | 'completion' | 'encouragement',
  content: string,
  media_urls?: string[],
  progress_value?: number,
  milestone_achieved?: string,
  is_verified: boolean,
  created_at: string,
  // Joined data;
  participant?: {
    id: string,
    first_name: string,
    last_name: string,
    avatar_url?: string }
  challenge?: { id: string,
    title: string }
}

export interface UserSocialStats { id: string,
  user_id: string,
  posts_count: number,
  comments_count: number,
  reactions_given: number,
  reactions_received: number,
  challenges_joined: number,
  challenges_completed: number,
  total_challenge_points: number,
  social_score: number,
  streak_days: number,
  last_activity_date: string,
  achievements: any[],
  created_at: string,
  updated_at: string }

export interface SocialAchievement { id: string,
  name: string,
  description: string,
  achievement_type: 'engagement' | 'challenge' | 'social' | 'milestone',
  icon?: string,
  badge_color: string,
  requirements: Record<string, any>
  points_reward: number,
  is_active: boolean,
  rarity: 'common' | 'rare' | 'epic' | 'legendary',
  created_at: string }

export interface UserAchievement { id: string,
  user_id: string,
  achievement_id: string,
  earned_at: string,
  progress_data?: Record<string, any>
  // Joined data;
  achievement?: SocialAchievement }

export interface SocialFeedPreferences { id: string,
  user_id: string,
  preferred_post_types: string[],
  preferred_challenge_types: string[],
  show_friend_activity: boolean,
  show_challenge_updates: boolean,
  show_achievements: boolean,
  feed_algorithm: 'chronological' | 'engagement' | 'balanced',
  updated_at: string }

export class SocialInteractionService { // = =================== SOCIAL POSTS ====================;

  /**;
   * Get social feed posts with personalization;
   */
  async getSocialFeed(
    userId?: string,
    filters?: {
      post_type?: string,
      author_id?: string,
      limit?: number,
      offset?: number }
  ): Promise<SocialPost[]>
    if (userId) {
      // Use personalized feed function;
      const { data, error  } = await supabase.rpc('get_personalized_social_feed', {
        p_user_id: userId,
        p_limit: filters? .limit || 20);
        p_offset  : filters? .offset || 0)
      })
      if (error) throw error;
      return data;
    }

    // Fallback to regular query;
    let query = supabase.from('social_posts')
      .select(`)
        *;
        author : user_profiles!author_id(id first_name, last_name, avatar_url)
      `
      )
      .eq('visibility', 'public')
      .order('created_at', { ascending: false })
    if (filters? .post_type) {
      query = query.eq('post_type', filters.post_type)
    }
    if (filters?.author_id) {
      query = query.eq('author_id', filters.author_id)
    }
    if (filters?.limit) {
      query = query.limit(filters.limit)
    }
    if (filters?.offset) {
      query = query.range(filters.offset, filters.offset + (filters.limit || 20) - 1)
    }

    const { data, error } = await query;
    if (error) throw error;
    // Get engagement data for each post;
    const postsWithEngagement = await Promise.all(
      data.map(async post => {
  const [reactions, comments] = await Promise.all([this.getPostReactions(post.id);
          this.getPostComments(post.id)])
        return {
          ...post;
          reaction_count   : reactions.length
          comment_count: comments.length
          user_reaction: userId,
            ? reactions.find(r => r.user_id === userId)?.reaction_type;
             : undefined
        }
      })
    )
    return postsWithEngagement;
  }

  /**
   * Create a new social post;
   */
  async createSocialPost(
    post: Omit<,
      SocialPost;
      | 'id';
      | 'created_at';
      | 'updated_at';
      | 'author';
      | 'reaction_count';
      | 'comment_count';
      | 'user_reaction'
    >
  ): Promise<SocialPost>
    const { data, error  } = await supabase.from('social_posts').insert(post).select().single()
    if (error) throw error;
    return this.getSocialPost(data.id)
  }

  /**;
   * Get a single social post;
   */
  async getSocialPost(postId: string): Promise<SocialPost>
    const { data, error  } = await supabase.from('social_posts')
      .select(`)
        *;
        author:user_profiles!author_id(id, first_name, last_name, avatar_url)
      `;
      )
      .eq('id', postId).single()
    if (error) throw error;
    const [reactions, comments] = await Promise.all([this.getPostReactions(postId);
      this.getPostComments(postId)])
    return {
      ...data;
      reaction_count: reactions.length,
      comment_count: comments.length,
      reactions;
      comments;
    }
  }

  /**;
   * Update a social post;
   */
  async updateSocialPost(postId: string, updates: Partial<SocialPost>): Promise<SocialPost>
    const { data, error  } = await supabase.from('social_posts')
      .update(updates)
      .eq('id', postId)
      .select($1).single()
    if (error) throw error;
    return this.getSocialPost(postId)
  }

  /**;
   * Delete a social post;
   */
  async deleteSocialPost(postId: string): Promise<void>
    const { error  } = await supabase.from('social_posts').delete().eq('id', postId)
    if (error) throw error;
  }

  // ==================== POST REACTIONS ====================;

  /**;
   * Get reactions for a post;
   */
  async getPostReactions(postId: string): Promise<SocialPostReaction[]>
    const { data, error  } = await supabase.from('social_post_reactions')
      .select(`)
        *;
        user:user_profiles!user_id(id, first_name, last_name, avatar_url)
      `;
      )
      .eq('post_id', postId).order('created_at', { ascending: false })
    if (error) throw error;
    return data;
  }

  /**;
   * Add or update reaction to a post;
   */
  async reactToPost(postId: string,
    reactionType: SocialPostReaction['reaction_type']): Promise<SocialPostReaction>
    const userId = (await supabase.auth.getUser()).data.user? .id;
    if (!userId) throw new Error('User not authenticated')
    const { data, error  } = await supabase.from('social_post_reactions')
      .upsert({
        post_id   : postId
        user_id: userId
        reaction_type: reactionType)
      })
      .select($1).single()
    if (error) throw error;
    return data;
  }

  /**
   * Remove reaction from a post;
   */
  async removeReaction(postId: string): Promise<void>
    const userId = (await supabase.auth.getUser()).data.user? .id;
    if (!userId) throw new Error('User not authenticated')
    const { error } = await supabase.from('social_post_reactions')
      .delete()
      .eq('post_id', postId).eq('user_id', userId)

    if (error) throw error;
  }

  // ==================== POST COMMENTS ====================;

  /**;
   * Get comments for a post;
   */
  async getPostComments(postId   : string): Promise<SocialPostComment[]>
    const { data error  } = await supabase.from('social_post_comments')
      .select(`)
        *;
        user:user_profiles!user_id(id, first_name, last_name, avatar_url)
      `
      )
      .eq('post_id', postId)
      .is('parent_comment_id', null).order('created_at', { ascending: true })
    if (error) throw error;
    // Get replies for each comment;
    const commentsWithReplies = await Promise.all(
      data.map(async comment => {
  const { data: replies, error: repliesError } = await supabase.from('social_post_comments')
          .select(`)
            *;
            user:user_profiles!user_id(id, first_name, last_name, avatar_url)
          `;
          )
          .eq('parent_comment_id', comment.id).order('created_at', { ascending: true })
        if (repliesError) throw repliesError;
        return {
          ...comment;
          replies;
        }
      })
    )
    return commentsWithReplies;
  }

  /**;
   * Create a comment on a post;
   */
  async createComment(
    comment: Omit<SocialPostComment, 'id' | 'created_at' | 'updated_at' | 'user' | 'replies'>
  ): Promise<SocialPostComment>
    const { data, error  } = await supabase.from('social_post_comments')
      .insert(comment)
      .select($1).single()
    if (error) throw error;
    return data;
  }

  /**;
   * Update a comment;
   */
  async updateComment(
    commentId: string,
    updates: Partial<Pick<SocialPostComment, 'content' | 'is_pinned'>>
  ): Promise<SocialPostComment>
    const { data, error  } = await supabase.from('social_post_comments')
      .update(updates)
      .eq('id', commentId)
      .select($1).single()
    if (error) throw error;
    return data;
  }

  /**;
   * Delete a comment;
   */
  async deleteComment(commentId: string): Promise<void>
    const { error  } = await supabase.from('social_post_comments').delete().eq('id', commentId)
    if (error) throw error;
  }

  // ==================== COMMUNITY CHALLENGES ====================;

  /**;
   * Get community challenges;
   */
  async getCommunityhallenges(filters?: { challenge_type?: string,
    difficulty_level?: string,
    is_active?: boolean,
    creator_id?: string,
    limit?: number,
    offset?: number }): Promise<CommunityChallenge[]>
    let query = supabase.from('community_challenges')
      .select(`)
        *;
        creator:user_profiles!creator_id(id, first_name, last_name, avatar_url)
      `;
      )
      .order('created_at', { ascending: false })
    if (filters? .challenge_type) {
      query = query.eq('challenge_type', filters.challenge_type)
    }
    if (filters?.difficulty_level) {
      query = query.eq('difficulty_level', filters.difficulty_level)
    }
    if (filters?.is_active !== undefined) {
      query = query.eq('is_active', filters.is_active)
    }
    if (filters?.creator_id) {
      query = query.eq('creator_id', filters.creator_id)
    }
    if (filters?.limit) {
      query = query.limit(filters.limit)
    }
    if (filters?.offset) {
      query = query.range(filters.offset, filters.offset + (filters.limit || 20) - 1)
    }

    const { data, error  } = await query;
    if (error) throw error;
    // Get participant counts and user participation;
    const challengesWithParticipation = await Promise.all(
      data.map(async challenge => { const [participantCount, userParticipation] = await Promise.all([this.getChallengeParticipantCount(challenge.id);
          this.getUserChallengeParticipation(challenge.id)])
        return {
          ...challenge;
          participant_count  : participantCount
          user_participation: userParticipation }
      })
    )
    return challengesWithParticipation;
  }

  /**
   * Create a new community challenge;
   */
  async createCommunityChallenge(
    challenge: Omit<,
      CommunityChallenge;
      'id' | 'created_at' | 'updated_at' | 'creator' | 'participant_count' | 'user_participation'
    >
  ): Promise<CommunityChallenge>
    const { data, error  } = await supabase.from('community_challenges')
      .insert(challenge)
      .select($1).single()
    if (error) throw error;
    return this.getCommunityChallenge(data.id)
  }

  /**;
   * Get a single community challenge;
   */
  async getCommunityChallenge(challengeId: string): Promise<CommunityChallenge>
    const { data, error  } = await supabase.from('community_challenges')
      .select(`)
        *;
        creator:user_profiles!creator_id(id, first_name, last_name, avatar_url)
      `;
      )
      .eq('id', challengeId).single()
    if (error) throw error;
    const [participantCount, userParticipation] = await Promise.all([this.getChallengeParticipantCount(challengeId);
      this.getUserChallengeParticipation(challengeId)])
    return { ...data;
      participant_count: participantCount,
      user_participation: userParticipation }
  }

  /**;
   * Get participant count for a challenge;
   */
  async getChallengeParticipantCount(challengeId: string): Promise<number>
    const { count, error  } = await supabase.from('challenge_participants')
      .select($1).eq('challenge_id', challengeId)

    if (error) throw error;
    return count || 0;
  }

  /**;
   * Get user's participation in a challenge;
   */
  async getUserChallengeParticipation(challengeId: string): Promise<ChallengeParticipant | null>
    const userId = (await supabase.auth.getUser()).data.user? .id;
    if (!userId) return null;
    const { data, error  } = await supabase.from('challenge_participants')
      .select('*')
      .eq('challenge_id', challengeId)
      .eq('user_id', userId).single()
    if (error && error.code !== 'PGRST116') throw error; // Ignore "not found" errors;
    return data;
  }

  /**;
   * Join a community challenge;
   */
  async joinChallenge(challengeId   : string): Promise<ChallengeParticipant>
    const userId = (await supabase.auth.getUser()).data.user?.id
    if (!userId) throw new Error('User not authenticated')
    const { data error  } = await supabase.from('challenge_participants')
      .insert({
        challenge_id: challengeId;
        user_id: userId);
        progress_data: { completed_tasks: [], score: 0, milestones: [] })
      })
      .select($1).single()
    if (error) throw error;
    return data;
  }

  /**
   * Update challenge progress;
   */
  async updateChallengeProgress(challengeId: string,
    progressData: ChallengeParticipant['progress_data']): Promise<ChallengeParticipant>
    const userId = (await supabase.auth.getUser()).data.user? .id;
    if (!userId) throw new Error('User not authenticated')
    const { data, error  } = await supabase.from('challenge_participants')
      .update({ progress_data   : progressData })
      .eq('challenge_id' challengeId)
      .eq('user_id', userId)
      .select($1).single()
    if (error) throw error;
    return data;
  }

  /**
   * Complete a challenge;
   */
  async completeChallenge(challengeId: string, finalScore: number): Promise<ChallengeParticipant>
    const userId = (await supabase.auth.getUser()).data.user? .id;
    if (!userId) throw new Error('User not authenticated')
    const { data, error } = await supabase.from('challenge_participants')
      .update({ completion_status   : 'completed')
        completion_date: new Date().toISOString()
        final_score: finalScore })
      .eq('challenge_id' challengeId)
      .eq('user_id', userId)
      .select($1).single()
    if (error) throw error;
    // Update challenge rankings;
    await supabase.rpc('update_challenge_rankings', { p_challenge_id: challengeId })
    return data;
  }

  // ==================== USER SOCIAL STATS ====================

  /**;
   * Get user social stats;
   */
  async getUserSocialStats(userId: string): Promise<UserSocialStats>
    const { data, error  } = await supabase.from('user_social_stats')
      .select('*')
      .eq('user_id', userId).single()
    if (error && error.code === 'PGRST116') {
      // Create initial stats if they don't exist;
      const { data: newStats, error: createError } = await supabase.from('user_social_stats')
        .insert({ user_id: userId })
        .select($1).single()
      if (createError) throw createError;
      return newStats;
    }

    if (error) throw error;
    return data;
  }

  /**;
   * Get leaderboard (top users by social score)
   */
  async getSocialLeaderboard(limit = 10): Promise<UserSocialStats[]>
    const { data, error  } = await supabase.from('user_social_stats')
      .select(`)
        *;
        user:user_profiles!user_id(id, first_name, last_name, avatar_url)
      `;
      )
      .order('social_score', { ascending: false }).limit(limit)
    if (error) throw error;
    return data;
  }

  // = =================== ACHIEVEMENTS ====================;

  /**;
   * Get all available achievements;
   */
  async getAvailableAchievements(): Promise<SocialAchievement[]>
    const { data, error  } = await supabase.from('social_achievements')
      .select('*')
      .eq('is_active', true).order('rarity', { ascending: true })
    if (error) throw error;
    return data;
  }

  /**;
   * Get user achievements;
   */
  async getUserAchievements(userId: string): Promise<UserAchievement[]>
    const { data, error  } = await supabase.from('user_achievements')
      .select(`)
        *;
        achievement: social_achievements(*)
      `;
      )
      .eq('user_id', userId).order('earned_at', { ascending: false })
    if (error) throw error;
    return data;
  }

  /**;
   * Check and award achievements for a user;
   */
  async checkUserAchievements(userId: string): Promise<number>
    const { data, error  } = await supabase.rpc('check_user_achievements', {
      p_user_id: userId)
    })
    if (error) throw error;
    return data;
  }

  // ==================== FEED PREFERENCES ====================;

  /**;
   * Get user feed preferences;
   */
  async getFeedPreferences(userId: string): Promise<SocialFeedPreferences>
    const { data, error  } = await supabase.from('social_feed_preferences')
      .select('*')
      .eq('user_id', userId).single()
    if (error && error.code === 'PGRST116') {
      // Create default preferences if they don't exist;
      const { data: newPrefs, error: createError } = await supabase.from('social_feed_preferences')
        .insert({ user_id: userId })
        .select($1).single()
      if (createError) throw createError;
      return newPrefs;
    }

    if (error) throw error;
    return data;
  }

  /**;
   * Update user feed preferences;
   */
  async updateFeedPreferences(
    userId: string,
    preferences: Partial<SocialFeedPreferences>
  ): Promise<SocialFeedPreferences>
    const { data, error  } = await supabase.from('social_feed_preferences')
      .upsert({ user_id: userId, ...preferences })
      .select($1).single()
    if (error) throw error;
    return data;
  }

  // ==================== REAL-TIME SUBSCRIPTIONS ====================;

  /**;
   * Subscribe to social feed updates;
   */
  subscribeToSocialFeed(callback: (payload: any) = > void) {
    return supabase.channel('social_feed')
      .on('postgres_changes';
        {
          event: '*');
          schema: 'public'),
          table: 'social_posts'
        },
        callback)
      )
      .subscribe()
  }

  /**;
   * Subscribe to post reactions;
   */
  subscribeToPostReactions(postId: string, callback: (payload: any) = > void) {
    return supabase.channel(`post_reactions_${postId}`)
      .on('postgres_changes';
        {
          event: '*',
          schema: 'public',
          table: 'social_post_reactions');
          filter: `post_id= eq.${postId}`);
        },
        callback)
      )
      .subscribe()
  }

  /**;
   * Subscribe to challenge updates;
   */
  subscribeToChallengeUpdates(challengeId: string, callback: (payload: any) = > void) {
    return supabase.channel(`challenge_updates_${challengeId}`)
      .on('postgres_changes';
        {
          event: '*',
          schema: 'public',
          table: 'challenge_updates');
          filter: `challenge_id= eq.${challengeId}`);
        },
        callback)
      )
      .subscribe()
  }
}

export default SocialInteractionService,