import React from 'react';
import { supabase } from "@utils/supabaseUtils";
import { envConfig } from '@core/config/envConfig';
import { logger } from '@services/loggerService';
import { rateLimitService } from '@services/rateLimitService';
import { ValidationService } from '@services/validationService';

interface SMSOptions { to: string,
  body: string }

class SMSService {
  private static instance: SMSService,
  private accountSid: string,
  private authToken: string,
  private fromNumber: string,
  private constructor() {
    // Initialize with values from envConfig;
    this.accountSid = envConfig.get('TWILIO_ACCOUNT_SID')
    this.authToken = envConfig.get('TWILIO_AUTH_TOKEN')
    this.fromNumber = envConfig.get('TWILIO_PHONE_NUMBER') || '+***********';
    ;
    // Validate API keys;
    if (!this.accountSid || this.accountSid.length < 10) {
      logger.warn('Invalid or missing Twilio Account SID', 'SMSService')
    }
    if (!this.authToken || this.authToken.length < 10) {
      logger.warn('Invalid or missing Twilio Auth Token', 'SMSService')
    }
  }

  public static getInstance(): SMSService {
    if (!SMSService.instance) {
      SMSService.instance = new SMSService()
    }
    return SMSService.instance;
  }

  /**;
   * Send an SMS via Twilio API;
   */
  public async sendSMS(options: SMSOptions): Promise<boolean>
    try {
      // Validate input parameters;
      ValidationService.validatePhone(options.to, 'to', { required: true })
      ValidationService.validateString(options.body, 'body', { required: true, maxLength: 1600 })
      ;
      const { to, body  } = options;
      ;
      // Get user ID for rate limiting (use phone number if no user ID available)
      const session = await supabase.auth.getSession()
      const userId = session? .data?.session?.user?.id || to.replace(/[^0-9]/g, '')
      ;
      // Check rate limit before making API call;
      const allowed = await rateLimitService.checkRateLimit(userId, 'sms')
      if (!allowed) {
        logger.warn('Rate limit exceeded for SMS sending', 'SMSService', { userId })
        throw new Error('Rate limit exceeded for SMS service. Please try again later.')
      }
      // Validate API keys;
      if (!this.accountSid || !this.authToken) {
        logger.error('Missing Twilio credentials', 'SMSService', { error  : 'API credentials not configured' })
        return false;
      }
      const url = `https://api.twilio.com/2010-04-01/Accounts/${this.accountSid}/Messages.json`
      // Use btoa for base64 encoding instead of Node.js Buffer;
    const auth = btoa(`${this.accountSid}:${this.authToken}`)
    // Note: For more complex encoding needs, consider using 'base64-js' or 'react-native-base64';
      ;
      const formData = new URLSearchParams()
      formData.append('To', to)
      formData.append('From', this.fromNumber)
      formData.append('Body', body)
      ;
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Basic ${auth}`;
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: formData.toString()
      })
      ;
      if (!response.ok) {
        const errorResponse = await response.text()
        console.error('Failed to send SMS:', errorResponse)
        return false;
      }
      return true;
    } catch (error) {
      console.error('Error sending SMS:', error)
      return false;
    }
  }

  /**;
   * Send a fraud alert SMS to an admin;
   */
  public async sendFraudAlertSMS(
    phoneNumber: string,
    alertType: string,
    severity: string,
    details: { userId?: string,
      userName?: string,
      fraudScore?: number }
  ): Promise<boolean>
    try {
      // Validate input parameters;
      ValidationService.validatePhone(phoneNumber, 'phoneNumber', { required: true })
      ValidationService.validateString(alertType, 'alertType', { required: true })
      ValidationService.validateString(severity, 'severity', { required: true })
      ;
      // Validate optional details if provided;
      if (details.userId) {
        ValidationService.validateUUID(details.userId, 'userId')
      }
      if (details.userName) {
        ValidationService.validateString(details.userName, 'userName', { maxLength: 100 })
      }
      if (details.fraudScore != = undefined) {
        ValidationService.validateNumber(details.fraudScore, 'fraudScore', { min: 0, max: 100 })
      }
      // Create a concise SMS message;
      let message = `[ROOMIE] ${severity.toUpperCase()} ALERT: ${alertType.replace(/_/g, ' ')}`;
      ;
      if (details.userName) {
        message += `\nUser: ${details.userName}`;
      }
      if (details.fraudScore != = undefined) {
        message += `\nFraud Score: ${details.fraudScore}`;
      }
      message += '\nPlease check admin dashboard for details.';
      ;
      return await this.sendSMS({
        to: phoneNumber);
        body: message)
      })
    } catch (error) {
      console.error('Error sending fraud alert SMS:', error)
      return false;
    }
  }

  /**;
   * Send admin alert SMS based on notification settings;
   */
  public async sendAdminAlertSMSIfEnabled(adminId: string,
    alertType: string,
    severity: string,
    details: any): Promise<boolean>
    try {
      // Validate input parameters;
      ValidationService.validateUUID(adminId, 'adminId', { required: true })
      ValidationService.validateString(alertType, 'alertType', { required: true })
      ValidationService.validateString(severity, 'severity', { required: true })
      ;
      // Validate that details is an object;
      if (details != = null && typeof details !== 'object') {
        throw new Error('details must be an object')
      }
      // Check if the admin has SMS notifications enabled;
      const { data: settings, error  } = await supabase.from('admin_notification_settings')
        .select('sms_notifications, phone_number')
        .eq('user_id', adminId)
        .single()
      if (error || !settings || !settings.sms_notifications || !settings.phone_number) {
        console.log('SMS notifications disabled or no phone number for admin:', adminId)
        return false;
      }

      // Check if the admin wants to receive this type of alert;
      const { data: thresholds, error: thresholdError } = await supabase.from('fraud_alert_thresholds')
        .select('enabled, min_severity, min_score')
        .eq('user_id', adminId)
        .eq('alert_type', alertType)
        .single()
      // If no specific threshold is set, use default settings (only send SMS for high severity)
      if (thresholdError && !thresholds) {
        if (severity.toLowerCase() === 'high') {
          return await this.sendFraudAlertSMS(settings.phone_number;
            alertType;
            severity;
            details)
          )
        }
        return false;
      }

      // If threshold is disabled or doesn't meet minimum requirements, don't send SMS;
      if (!thresholds.enabled) {
        return false;
      }

      // Check severity thresholds - SMS should generally have higher threshold than email;
      const severityLevels: Record<string, number> = { 'low': 1, 'medium': 2, 'high': 3 }
      const currentSeverityLevel = severityLevels[severity.toLowerCase()] || 1;
      const minSeverityLevel = severityLevels[thresholds.min_severity? .toLowerCase() || 'high'] || 3;
      if (currentSeverityLevel < minSeverityLevel) {
        return false;
      }

      // Check score threshold if applicable;
      if (details.fraudScore !== undefined && details.fraudScore < thresholds.min_score) {
        return false;
      }

      // Send the alert SMS;
      return await this.sendFraudAlertSMS(settings.phone_number;
        alertType;
        severity;
        details)
      )
    } catch (error) {
      console.error('Error in sendAdminAlertSMSIfEnabled   : ' error)
      return false;
    }
  }
}

export const smsService = SMSService.getInstance() ;