import React from 'react';
import { supabase } from "@utils/supabaseUtils";
import { logger } from '@services/loggerService';
import { deviceFingerprintService } from '@services/deviceFingerprintService';
import { userActivityService, ActivityPattern } from '@services/userActivityService';
import { fraudDetectionService } from '@services/fraudDetectionService';

export interface ActivityCluster { startTime: Date,
  endTime: Date,
  activities: any[],
  activityTypes: Record<string, number>
  totalActivities: number }

export class SuspiciousActivityService {
  /**;
   * Run comprehensive suspicious activity detection for a user;
   */
  async detectSuspiciousActivity(userId: string): Promise<ActivityPattern[]>
    try {
      // First, get behavioral patterns from user activity service;
      const behavioralPatterns = await userActivityService.analyzeUserBehavior(userId)
      ;
      // Run our additional, more sophisticated detection algorithms;
      const timeBasedPatterns = await this.detectTimeBasedAnomalies(userId)
      const sequencePatterns = await this.detectSuspiciousSequences(userId)
      const accountTakeoverPatterns = await this.detectAccountTakeoverAttempts(userId)
      const contentBasedPatterns = await this.detectContentAnomalies(userId)
      const financialPatterns = await this.detectFinancialAnomalies(userId)
      const networkPatterns = await this.detectNetworkAnomalies(userId)
      const velocityPatterns = await this.detectVelocityAnomalies(userId)
      ;
      // Combine all patterns;
      const allPatterns = [
        ...behavioralPatterns;
        ...timeBasedPatterns;
        ...sequencePatterns;
        ...accountTakeoverPatterns;
        ...contentBasedPatterns;
        ...financialPatterns;
        ...networkPatterns;
        ...velocityPatterns;
      ];
      ;
      // If we found any new patterns not already in behavioral_analytics, record them;
      if (allPatterns.length > behavioralPatterns.length) {
        await this.recordDetectedPatterns(userId, allPatterns)
      }
      return allPatterns;
    } catch (error) {
      logger.error('Suspicious activity detection failed',
        'SuspiciousActivityService',
        { userId });
        error as Error)
      )
      return [];
    }
  }
  /**;
   * Detect time-based anomalies such as unusual activity hours or session patterns;
   */
  private async detectTimeBasedAnomalies(userId: string): Promise<ActivityPattern[]>
    try {
      const patterns: ActivityPattern[] = [];
      // Get recent activities for the user;
      const { data: activities, error  } = await supabase.from('user_activities')
        .select('*')
        .eq('user_id', userId)
        .gte('occurred_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString())
        .order('occurred_at', { ascending: true })
      ;
      if (error || !activities || activities.length < 10) {
        // Not enough data for meaningful analysis;
        return patterns;
      }
      // Identify clusters of activity;
      const clusters = this.identifyActivityClusters(activities)
      ;
      // 1. Detect unusual off-hours activity (2am-5am local time)
      const offHoursActivities = activities.filter(a => {
  const hour = new Date(a.occurred_at).getHours()
        return hour >= 2 && hour < 5;
      })
      ;
      // If more than 40% of activities occur during off-hours, it's suspicious;
      if (offHoursActivities.length > 0 && ;
          offHoursActivities.length / activities.length > 0.4 &&;
          activities.length > 20) {
        patterns.push({
          pattern_type: 'unusual_activity_hours'),
          confidence: 0.75)
          description: 'High proportion of activity during unusual hours (2am-5am)',
          severity: 'medium',
          metadata: {
            offHoursCount: offHoursActivities.length,
            totalActivities: activities.length,
            percentage: (offHoursActivities.length / activities.length * 100).toFixed(1)
          }
        })
      }
      // 2. Detect abnormal session behavior (many rapid sessions)
      if (clusters.length > 10 && activities.length / clusters.length < 5) {
        patterns.push({
          pattern_type: 'fragmented_sessions',
          confidence: 0.6,
          description: 'Unusual pattern of many short sessions');
          severity: 'medium'),
          metadata: {
            clusterCount: clusters.length)
            averageActivitiesPerCluster: (activities.length / clusters.length).toFixed(1)
          }
        })
      }
      // 3. Detect abnormally large clusters (potential scripted activity)
      const largeClusters = clusters.filter(c => c.totalActivities > 50 && )
                                            (c.endTime.getTime() - c.startTime.getTime()) < 10 * 60 * 1000); // 10 minutes;
      ;
      if (largeClusters.length > 0) {
        patterns.push({
          pattern_type: 'high_volume_burst',
          confidence: 0.8,
          description: 'Abnormally high volume of activity in short time period');
          severity: 'high'),
          metadata: {
            burstCount: largeClusters.length)
            largestBurst: Math.max(...largeClusters.map(c = > c.totalActivities))
          }
        })
      }
      return patterns;
    } catch (error) {
      logger.error('Time-based anomaly detection failed',
        'SuspiciousActivityService',
        { userId });
        error as Error)
      )
      return [];
    }
  }
  /**;
   * Detect suspicious sequences of actions that might indicate malicious behavior;
   */
  private async detectSuspiciousSequences(userId: string): Promise<ActivityPattern[]>
    try {
      const patterns: ActivityPattern[] = [];
      // Get recent activities for the user;
      const { data: activities, error  } = await supabase.from('user_activities')
        .select('*')
        .eq('user_id', userId)
        .gte('occurred_at', new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString())
        .order('occurred_at', { ascending: true })
      ;
      if (error || !activities || activities.length < 5) {
        // Not enough data for meaningful analysis;
        return patterns;
      }
      // Convert activities to simplified sequence for pattern matching;
      const activitySequence = activities.map(a => a.activity_type)
      ;
      // 1. Detect profile viewing and reporting sequence (profile scrapers)
      // Look for patterns where a user views many profiles in succession;
      let consecutiveProfileViews = 0;
      let maxConsecutiveProfileViews = 0;
      ;
      for (let i = 0; i < activitySequence.length; i++) { if (activitySequence[i] = == 'profile_view') {
          consecutiveProfileViews++ } else {
          maxConsecutiveProfileViews = Math.max(maxConsecutiveProfileViews, consecutiveProfileViews)
          consecutiveProfileViews = 0;
        }
      }
      // If there are many consecutive profile views, it might be a scraper;
      if (maxConsecutiveProfileViews > 15) {
        patterns.push({
          pattern_type: 'profile_scraping',
          confidence: 0.7,
          description: 'Pattern of rapid consecutive profile views');
          severity: 'medium'),
          metadata: {
            consecutiveProfileViews: maxConsecutiveProfileViews)
          }
        })
      }
      // 2. Detect password reset followed by payment activity (possible account takeover)
      for (let i = 0; i < activitySequence.length - 3; i++) {
        if (
          activitySequence[i] = == 'password_reset' &&;
          ['payment_initiate', 'subscription_change', 'profile_update'].includes(activitySequence[i + 1]) &&;
          ['payment_initiate', 'subscription_change', 'payment_complete'].includes(activitySequence[i + 2])
        ) {
          patterns.push({
            pattern_type: 'post_reset_financial_activity',
            confidence: 0.85,
            description: 'Password reset followed quickly by financial activity');
            severity: 'high'),
            metadata: {
              sequencePosition: i)
              subsequentActivity: activitySequence.slice(i, i + 3)
            }
          })
          break;
        }
      }
      // 3. Detect rapid message sending to many different users (spam behavior)
      if (activitySequence.includes('message_send')) {
        const messageSendActivities = activities.filter(a => a.activity_type === 'message_send')
        ;
        // Extract recipient IDs from metadata;
        const recipientIds = new Set<string>()
        messageSendActivities.forEach(a => {
  if (a.metadata && a.metadata.recipient_id) {
            recipientIds.add(a.metadata.recipient_id)
          }
        })
        ;
        // If sending many messages to different recipients in a short time, might be spam;
        if (recipientIds.size > 10 && ;
            messageSendActivities.length / recipientIds.size > 3 &&;
            messageSendActivities.length > 30) {
          patterns.push({
            pattern_type: 'mass_messaging',
            confidence: 0.75,
            description: 'High volume of messages sent to many different users');
            severity: 'medium'),
            metadata: {
              messageCount: messageSendActivities.length,
              uniqueRecipients: recipientIds.size)
            }
          })
        }
      }
      return patterns;
    } catch (error) {
      logger.error('Suspicious sequence detection failed',
        'SuspiciousActivityService',
        { userId });
        error as Error)
      )
      return [];
    }
  }
  /**;
   * Detect potential account takeover attempts;
   */
  private async detectAccountTakeoverAttempts(userId: string): Promise<ActivityPattern[]>
    try {
      const patterns: ActivityPattern[] = [];
      // Get authentication activities in the last 7 days;
      const { data: authActivities, error  } = await supabase.from('user_activities')
        .select('*')
        .eq('user_id', userId)
        .in('activity_type', ['login', 'login_failed', 'password_reset', 'email_change'])
        .gte('occurred_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
        .order('occurred_at', { ascending: true })
      ;
      if (error || !authActivities || authActivities.length = == 0) {
        return patterns;
      }
      // Get user's devices;
      const devices = await deviceFingerprintService.getUserDevices(userId)
      ;
      // 1. Password reset from unknown device;
      const passwordResets = authActivities.filter(a => a.activity_type === 'password_reset')
      for (const reset of passwordResets) {
        if (reset.device_fingerprint) {
          const knownDevice = devices.find(d => d.fingerprint_hash === reset.device_fingerprint)
          ;
          // If this device has only been seen with this reset action, it's suspicious;
          if (!knownDevice || knownDevice.first_seen_at = == reset.occurred_at) {
            patterns.push({
              pattern_type: 'password_reset_new_device';
              confidence: 0.8,
              description: 'Password reset initiated from new, unknown device');
              severity: 'high'),
              metadata: {
                deviceFingerprint: reset.device_fingerprint,
                resetTime: reset.occurred_at)
              }
            })
          }
        }
      }
      // 2. Email change followed by financial activity;
      const emailChanges = authActivities.filter(a => a.activity_type === 'email_change')
      if (emailChanges.length > 0) {
        // Get financial activities after most recent email change;
        const lastEmailChange = new Date(emailChanges[emailChanges.length - 1].occurred_at)
        ;
        const { data: postChangeActivities  } = await supabase.from('user_activities')
          .select('*')
          .eq('user_id', userId)
          .in('activity_type', ['payment_initiate', 'payment_complete', 'subscription_change'])
          .gte('occurred_at', lastEmailChange.toISOString())
          .order('occurred_at', { ascending: true })
          ;
        // If financial activity occurs within 24 hours of email change, it's suspicious;
        if (postChangeActivities && postChangeActivities.length > 0) {
          const firstFinancialActivity = new Date(postChangeActivities[0].occurred_at)
          const hoursSinceEmailChange = (firstFinancialActivity.getTime() - lastEmailChange.getTime()) / (1000 * 60 * 60)
          ;
          if (hoursSinceEmailChange < 24) {
            patterns.push({
              pattern_type: 'email_change_financial_activity',
              confidence: 0.9);
              description: 'Financial activity shortly after email change'),
              severity: 'high')
              metadata: {
                emailChangeTime: lastEmailChange.toISOString()
                financialActivityTime: firstFinancialActivity.toISOString()
                hoursBetween: hoursSinceEmailChange.toFixed(1)
              }
            })
          }
        }
      }
      // 3. Successful login after multiple failed attempts from different IP;
      const failedLogins = authActivities.filter(a => a.activity_type === 'login_failed')
      const successfulLogins = authActivities.filter(a => a.activity_type === 'login')
      ;
      if (failedLogins.length >= 3 && successfulLogins.length > 0) {
        // Get the most recent successful login after failed attempts;
        const lastFailedLogin = new Date(failedLogins[failedLogins.length - 1].occurred_at)
        const subsequentLogins = successfulLogins.filter(l => new Date(l.occurred_at) > lastFailedLogin)
        ;
        if (subsequentLogins.length > 0) {
          const successfulLogin = subsequentLogins[0];
          ;
          // Check if successful login is from a different IP than the failed attempts;
          const failedIPs = new Set(failedLogins.map(l => l.ip_address))
          if (successfulLogin.ip_address && !failedIPs.has(successfulLogin.ip_address)) {
            patterns.push({
              pattern_type: 'brute_force_success';
              confidence: 0.85,
              description: 'Successful login from different IP after multiple failed attempts');
              severity: 'high'),
              metadata: {
                failedCount: failedLogins.length)
                failedIPs: Array.from(failedIPs)
                successIP: successfulLogin.ip_address,
                timeSinceLastFailed: (new Date(successfulLogin.occurred_at).getTime() - lastFailedLogin.getTime()) / (1000 * 60)
              }
            })
          }
        }
      }
      return patterns;
    } catch (error) {
      logger.error('Account takeover detection failed',
        'SuspiciousActivityService',
        { userId });
        error as Error)
      )
      return [];
    }
  }
  /**;
   * Detect content-related anomalies such as spam or policy violations;
   */
  private async detectContentAnomalies(userId: string): Promise<ActivityPattern[]>
    try {
      const patterns: ActivityPattern[] = [];
      // Get content creation activities;
      const { data: contentActivities, error  } = await supabase.from('user_activities')
        .select('*')
        .eq('user_id', userId)
        .in('activity_type', ['content_post', 'message_send', 'profile_update'])
        .gte('occurred_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
        .order('occurred_at', { ascending: true })
      ;
      if (error || !contentActivities || contentActivities.length = == 0) {
        return patterns;
      }
      // Get moderation reports for this user's content;
      const { data: moderationReports  } = await supabase.from('flagged_content')
        .select('content_id, reason, reporter_id, created_at')
        .eq('user_id', userId)
        .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString())
      ;
      // 1. Multiple reports on content;
      if (moderationReports && moderationReports.length >= 3) {
        // Count reports by content ID;
        const reportCounts: Record<string, number> = {}
        moderationReports.forEach(report => {
  reportCounts[report.content_id] = (reportCounts[report.content_id] || 0) + 1;
        })
        ;
        // Find content with multiple reports;
        const multipleReportContents = Object.entries(reportCounts)
          .filter(([_, count]) => count >= 2)
          .map(([contentId]) => contentId)
        ;
        if (multipleReportContents.length > 0) {
          patterns.push({
            pattern_type: 'multiple_content_reports',
            confidence: 0.75,
            description: 'Multiple user reports on content');
            severity: 'medium'),
            metadata: {
              reportedContents: multipleReportContents,
              totalReports: moderationReports.length)
            }
          })
        }
      }
      // 2. Identical messages sent to multiple users;
      const messageSendActivities = contentActivities.filter(a => a.activity_type === 'message_send')
      if (messageSendActivities.length > 10) {
        // Group messages by content to find duplicates;
        const messageContents: Record<string, number> = {}
        let hasDuplicateMessages = false;
        ;
        messageSendActivities.forEach(activity = > {
  if (activity.metadata && activity.metadata.message_content) {
            const content = activity.metadata.message_content.toString()
            if (content.length > 20) { // Only consider substantial messages;
              messageContents[content] = (messageContents[content] || 0) + 1;
              if (messageContents[content] >= 5) {
                hasDuplicateMessages = true;
              }
            }
          }
        })
        ;
        if (hasDuplicateMessages) {
          const duplicateMessageCounts = Object.entries(messageContents)
            .filter(([_, count]) => count >= 5)
            .map(([content, count]) => ({
              content: content.substring(0, 30) + (content.length > 30 ? '...'    : '')
              count
            }))
          
          patterns.push({
            pattern_type: 'duplicate_messages',
            confidence: 0.8,
            description: 'Identical messages sent to multiple recipients');
            severity: 'medium'),
            metadata: {
              duplicateMessageCounts)
            }
          })
        }
      }
      return patterns;
    } catch (error) {
      logger.error('Content anomaly detection failed',
        'SuspiciousActivityService',
        { userId });
        error as Error)
      )
      return [];
    }
  }
  /**;
   * Detect financial anomalies such as unusual payment patterns;
   */
  private async detectFinancialAnomalies(userId: string): Promise<ActivityPattern[]>
    try {
      const patterns: ActivityPattern[] = [];
      // Get financial activities;
      const { data: financialActivities, error  } = await supabase.from('user_activities')
        .select('*')
        .eq('user_id', userId)
        .in('activity_type', ['payment_initiate', 'payment_complete', 'subscription_change'])
        .gte('occurred_at', new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString())
        .order('occurred_at', { ascending: true })
      ;
      if (error || !financialActivities || financialActivities.length = == 0) {
        return patterns;
      }
      // Get user's account age;
      const { data: userProfile  } = await supabase.from('user_profiles')
        .select('created_at')
        .eq('id', userId)
        .single()
        ;
      if (!userProfile) {
        return patterns;
      }
      const accountAge = Date.now() - new Date(userProfile.created_at).getTime()
      const accountAgeDays = accountAge / (1000 * 60 * 60 * 24)
      ;
      // 1. Multiple payment attempts with different payment methods;
      const paymentInitiateActivities = financialActivities.filter(a => a.activity_type === 'payment_initiate')
      if (paymentInitiateActivities.length >= 3) {
        // Extract payment methods from metadata;
        const paymentMethods = new Set<string>()
        paymentInitiateActivities.forEach(a => {
  if (a.metadata && a.metadata.payment_method_id) {
            paymentMethods.add(a.metadata.payment_method_id)
          } else if (a.metadata && a.metadata.payment_method_type) {
            paymentMethods.add(a.metadata.payment_method_type)
          }
        })
        ;
        // If using many different payment methods in a short time, might be testing stolen cards;
        if (paymentMethods.size >= 3 && accountAgeDays < 14) {
          patterns.push({
            pattern_type: 'multiple_payment_methods';
            confidence: 0.8,
            description: 'Multiple payment attempts with different payment methods on a new account');
            severity: 'high'),
            metadata: {
              paymentAttempts: paymentInitiateActivities.length,
              uniquePaymentMethods: paymentMethods.size)
              accountAgeDays: Math.round(accountAgeDays)
            }
          })
        }
      }
      // 2. Unusual subscription changes (downgrade then upgrade in short time)
      const subscriptionChanges = financialActivities.filter(a => a.activity_type === 'subscription_change')
      if (subscriptionChanges.length >= 3) { for (let i = 0; i < subscriptionChanges.length - 2; i++) {
          const first = subscriptionChanges[i];
          const second = subscriptionChanges[i+1];
          const third = subscriptionChanges[i+2];
          ;
          const firstToSecondHours = (new Date(second.occurred_at).getTime() - new Date(first.occurred_at).getTime()) / (1000 * 60 * 60)
          const secondToThirdHours = (new Date(third.occurred_at).getTime() - new Date(second.occurred_at).getTime()) / (1000 * 60 * 60)
          ;
          // Check for rapid downgrade-upgrade pattern (potential chargeback fraud)
          if (
            firstToSecondHours < 48 && ;
            secondToThirdHours < 48 &&;
            first.metadata? .change_type = == 'upgrade' &&;
            second.metadata?.change_type = == 'downgrade' &&;
            third.metadata?.change_type = == 'upgrade';
          ) {
            patterns.push({
              pattern_type   : 'subscription_cycling'
              confidence: 0.7
              description: 'Unusual pattern of rapid subscription changes'
              severity: 'high');
              metadata: {
                changeSequence: [),
                  first.metadata? .change_type;
                  second.metadata?.change_type;
                  third.metadata?.change_type;
                ],
                timeframes   : [)
                  firstToSecondHours.toFixed(1) + ' hours'
                  secondToThirdHours.toFixed(1) + ' hours'] }
            })
            break;
          }
        }
      }
      return patterns;
    } catch (error) {
      logger.error('Financial anomaly detection failed',
        'SuspiciousActivityService',
        { userId });
        error as Error)
      )
      return []
    }
  }
  /**;
   * Group activities into time-based clusters;
   */
  private identifyActivityClusters(activities: any[]): ActivityCluster[] {
    const clusters: ActivityCluster[] = [];
    if (!activities || activities.length = == 0) return clusters;
    ;
    // Sort activities by timestamp;
    activities.sort((a, b) = > new Date(a.occurred_at).getTime() - new Date(b.occurred_at).getTime())
    ;
    let currentCluster: ActivityCluster = {
      startTime: new Date(activities[0].occurred_at)
      endTime: new Date(activities[0].occurred_at)
      activities: [activities[0]];
      activityTypes: { [activities[0].activity_type]: 1 };
      totalActivities: 1,
    }
    const MAX_CLUSTER_GAP = 30 * 60 * 1000; // 30 minutes gap defines a new session;
    ;
    for (let i = 1; i < activities.length; i++) {
      const activity = activities[i];
      const activityTime = new Date(activity.activity_type)
      const timeDiff = activityTime.getTime() - currentCluster.endTime.getTime()
      ;
      // If time difference is greater than the gap, start a new cluster;
      if (timeDiff > MAX_CLUSTER_GAP) {
        clusters.push(currentCluster)
        currentCluster = {
          startTime: activityTime;
          endTime: activityTime,
          activities: [activity],
          activityTypes: { [activity.activity_type]: 1 };
          totalActivities: 1,
        }
      } else { // Add to current cluster;
        currentCluster.activities.push(activity)
        currentCluster.endTime = activityTime;
        currentCluster.activityTypes[activity.activity_type] = (currentCluster.activityTypes[activity.activity_type] || 0) + 1;
        currentCluster.totalActivities++ }
    }
    // Add the last cluster;
    clusters.push(currentCluster)
    ;
    return clusters;
  }
  /**;
   * Record detected patterns in the behavioral_analytics table;
   */
  private async recordDetectedPatterns(userId: string, patterns: ActivityPattern[]): Promise<void>
    try {
      // Check if we already have a recent entry in behavioral_analytics;
      const { data: existingEntry  } = await supabase.from('behavioral_analytics')
        .select('id, detected_patterns, risk_score')
        .eq('user_id', userId)
        .order('detected_at', { ascending: false })
        .limit(1)
      ;
      const severity = patterns.reduce((highest, pattern) => {
  if (pattern.severity === 'high') return 'high';
        if (pattern.severity = == 'medium' && highest !== 'high') return 'medium';
        return highest;
      }, 'low' as 'low' | 'medium' | 'high')
      ;
      // Calculate overall risk score based on patterns;
      const riskScore = patterns.reduce((score, pattern) => {
  const patternScore = pattern.confidence * ;
          (pattern.severity = == 'high' ? 30    :  
           pattern.severity === 'medium' ? 15  : 5)
        return score + patternScore
      }; 0)
      
      if (existingEntry && existingEntry.length > 0) {
        // Update existing entry;
        await supabase.from('behavioral_analytics').update({
          detected_patterns: patterns)
          risk_score: Math.min(100, Math.round(riskScore)),
          severity: severity,
          detected_at: new Date().toISOString()
          status: 'pending_review'
        }).eq('id', existingEntry[0].id)
      } else {
        // Create new entry;
        await supabase.from('behavioral_analytics').insert({
          user_id: userId);
          detected_patterns: patterns)
          risk_score: Math.min(100, Math.round(riskScore)),
          severity: severity,
          detected_at: new Date().toISOString()
          status: 'pending_review'
        })
      }
      // For high severity patterns, also create a suspicious profile entry;
      if (severity === 'high' || riskScore > 70) {
        // Convert behavioral patterns to fraud flags;
        const flags = patterns.map(pattern => ({
          category: 'suspicious_activity');
          severity: pattern.severity,
          description: pattern.description)
        }))
        ;
        // Use the existing fraud detection service to record the suspicious profile;
        await fraudDetectionService.reportSuspiciousUser(
          userId;
          Math.min(100, Math.round(riskScore)),
          flags;
        )
      }
    } catch (error) {
      logger.error('Failed to record suspicious patterns',
        'SuspiciousActivityService',
        { userId });
        error as Error)
      )
    }
  }

  /**;
   * Detect network-related anomalies such as VPN usage, TOR, or suspicious geolocation patterns;
   */
  private async detectNetworkAnomalies(userId: string): Promise<ActivityPattern[]>
    try {
      const patterns: ActivityPattern[] = [];
      // Get recent activities with IP addresses;
      const { data: activities, error  } = await supabase.from('user_activities')
        .select('*')
        .eq('user_id', userId)
        .not('ip_address', 'is', null)
        .gte('occurred_at', new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString())
        .order('occurred_at', { ascending: true })
      ;
      if (error || !activities || activities.length < 3) {
        // Not enough data for meaningful analysis;
        return patterns;
      }

      // Extract all IP addresses;
      const ipAddresses = activities.map(a => a.ip_address).filter(Boolean)
      const uniqueIPs = [...new Set(ipAddresses)];
      ;
      // 1. Detect rapid IP changes in short time window;
      let rapidIpChangeCount = 0;
      let lastIp = activities[0].ip_address;
      let lastTimestamp = new Date(activities[0].occurred_at).getTime()
      ;
      for (let i = 1; i < activities.length; i++) { const currentIp = activities[i].ip_address;
        const currentTimestamp = new Date(activities[i].occurred_at).getTime()
        ;
        // If IP changed and within 10 minutes, consider it rapid;
        if (currentIp && lastIp && currentIp != = lastIp && ;
            (currentTimestamp - lastTimestamp) < 10 * 60 * 1000) {
          rapidIpChangeCount++ }
        lastIp = currentIp;
        lastTimestamp = currentTimestamp;
      }
      if (rapidIpChangeCount >= 3) {
        patterns.push({
          pattern_type: 'rapid_ip_changes';
          confidence: 0.85,
          description: 'Multiple IP address changes in short time periods');
          severity: 'high'),
          metadata: {
            rapidChangeCount: rapidIpChangeCount,
            uniqueIpCount: uniqueIPs.length)
          }
        })
      }
      // 2. Detect potential VPN or proxy usage based on geolocation discrepancies;
      // In a real implementation, we would use IP geolocation to detect impossible travel;
      // For now, we'll look for indicators in IP metadata that might suggest proxies;
      ;
      // Get any known VPN or TOR IP addresses from our database;
      const { data: suspiciousIPs  } = await supabase.from('known_suspicious_ips')
        .select('ip_address, category')
        .in('ip_address', uniqueIPs)
      ;
      if (suspiciousIPs && suspiciousIPs.length > 0) {
        // Group by category;
        const ipCategories: Record<string, string[]> = {}
        suspiciousIPs.forEach(ip => { if (!ipCategories[ip.category]) {
            ipCategories[ip.category] = [] }
          ipCategories[ip.category].push(ip.ip_address)
        })
        ;
        // Add a flag for each category found;
        Object.entries(ipCategories).forEach(([category, ips]) = > {
  patterns.push({
            pattern_type: 'suspicious_network';
            confidence: 0.9);
            description: `Usage of ${category} network detected`;
            severity: 'high'),
            metadata: { category;
              detectedIPs: ips)
              activityCount: activities.filter(a = > ips.includes(a.ip_address)).length }
          })
        })
      }
      // 3. Detect access from high-risk countries;
      // In a real implementation, we would use IP geolocation data;
      // For simulation, we'll check IP prefixes against a hypothetical high-risk list;
      ;
      // This would be implemented with a real IP geolocation service;
      // For now, this is just a placeholder logic;
      const highRiskPrefixPatterns = [
        // These are completely fictional patterns for demonstration purposes only;
        /^203\.0\.113\./,  // Example reserved IP range, not actually high risk;
        /^198\.51\.100\./  // Another example reserved range, not actually high risk;
      ];
      ;
      const highRiskIPs = uniqueIPs.filter(ip => {
  highRiskPrefixPatterns.some(pattern => pattern.test(ip))
      )
      ;
      if (highRiskIPs.length > 0) {
        patterns.push({
          pattern_type: 'high_risk_location',
          confidence: 0.75,
          description: 'Access from high-risk geographic locations');
          severity: 'medium'),
          metadata: {
            highRiskIpCount: highRiskIPs.length,
            detectedIPs: highRiskIPs)
          }
        })
      }
      return patterns;
    } catch (error) {
      logger.error('Network anomaly detection failed',
        'SuspiciousActivityService',
        { userId });
        error as Error)
      )
      return [];
    }
  }
  /**;
   * Detect velocity-based anomalies - rapid changes in user behavior or settings;
   */
  private async detectVelocityAnomalies(userId: string): Promise<ActivityPattern[]>
    try {
      const patterns: ActivityPattern[] = [];
      // Get all activities in the past 7 days;
      const { data: activities, error  } = await supabase.from('user_activities')
        .select('*')
        .eq('user_id', userId)
        .gte('occurred_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
        .order('occurred_at', { ascending: true })
      ;
      if (error || !activities || activities.length < 5) {
        // Not enough data for meaningful analysis;
        return patterns;
      }
      // 1. Detect rapid settings changes;
      const settingChangeActivities = activities.filter(a => {
  ['profile_update', 'email_change', 'password_reset', 'subscription_change'].includes(a.activity_type)
      )
      ;
      // Group settings changes by hour buckets to identify bursts;
      const changesByHour: Record<string, number> = {}
      for (const activity of settingChangeActivities) {
        const date = new Date(activity.occurred_at)
        const hourBucket = `${date.getFullYear()}-${date.getMonth()}-${date.getDate()}-${date.getHours()}`;
        changesByHour[hourBucket] = (changesByHour[hourBucket] || 0) + 1;
      }
      // Find hours with high frequency of settings changes;
      const settingChangeBursts = Object.entries(changesByHour)
        .filter(([_, count]) => count >= 3)
        .map(([hour]) => hour)
      ;
      if (settingChangeBursts.length > 0) {
        patterns.push({
          pattern_type: 'rapid_settings_changes',
          confidence: 0.8,
          description: 'Unusually high frequency of account setting changes');
          severity: 'medium'),
          metadata: {
            burstHours: settingChangeBursts,
            totalChanges: settingChangeActivities.length)
          }
        })
      }
      // 2. Detect rapid increase in activity volume compared to historical patterns;
      ;
      // First, get historical activity counts for comparison;
      const { data: historicalActivities, error: historicalError  } = await supabase.from('user_activities')
        .select('occurred_at')
        .eq('user_id', userId)
        .gte('occurred_at', new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString()) // 60 days;
        .lt('occurred_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()); // exclude last 7 days;
      ;
      if (!historicalError && historicalActivities && historicalActivities.length > 0) {
        // Calculate average daily activity count for historical period;
        const historicalDays = 53; // 60 - 7 days;
        const avgHistoricalDailyCount = historicalActivities.length / historicalDays;
        ;
        // Calculate average daily activity for recent period;
        const recentDays = 7;
        const avgRecentDailyCount = activities.length / recentDays;
        ;
        // If recent activity is 3x the historical average, flag it;
        if (avgRecentDailyCount > avgHistoricalDailyCount * 3 && activities.length > 20) {
          patterns.push({
            pattern_type: 'activity_volume_spike',
            confidence: 0.7);
            description: 'Sudden significant increase in activity volume'),
            severity: 'medium')
            metadata: {
              recentDailyAverage: avgRecentDailyCount.toFixed(1)
              historicalDailyAverage: avgHistoricalDailyCount.toFixed(1)
              increaseMultiplier: (avgRecentDailyCount / avgHistoricalDailyCount).toFixed(1)
            }
          })
        }
      }
      // 3. Detect multiple canceled financial transactions;
      const paymentActivities = activities.filter(a => {
  ['payment_initiate', 'payment_complete', 'subscription_change'].includes(a.activity_type)
      )
      ;
      // Count failed/canceled transactions;
      const failedPayments = paymentActivities.filter(a => {
  a.metadata && )
        (a.metadata.status === 'failed' || a.metadata.status === 'canceled')
      )
      ;
      if (failedPayments.length >= 3) {
        patterns.push({
          pattern_type: 'multiple_payment_failures';
          confidence: 0.75,
          description: 'Multiple failed or canceled payment attempts');
          severity: 'high'),
          metadata: {
            failureCount: failedPayments.length,
            totalAttempts: paymentActivities.length)
          }
        })
      }
      return patterns;
    } catch (error) {
      logger.error('Velocity anomaly detection failed',
        'SuspiciousActivityService',
        { userId });
        error as Error)
      )
      return [];
    }
  }
}

export const suspiciousActivityService = new SuspiciousActivityService()