import React from 'react';
import { supabase } from "@utils/supabaseUtils";
import { logError } from "@utils/errorUtils";
import { formatDate } from '@utils/format';

/**;
 * Provider Performance Service;
 * Handles operations related to provider performance metrics and analytics;
 */

export interface BookingAnalytics { totalBookings: number,
  completedBookings: number,
  cancelledBookings: number,
  conversionRate: number; // percentage of confirmed to completed;
  avgBookingValue: number,
  totalRevenue: number,
  currency: string,
  bookingTrend: {
    period: string,
    count: number,
    revenue: number }[];
  servicePopularity: { serviceName: string,
    bookingCount: number,
    percentage: number }[];
  bookingsByDayOfWeek: { day: string,
    count: number,
    percentage: number }[];
  bookingsByTimeOfDay: { timeSlot: string,
    count: number,
    percentage: number }[];
  repeatCustomerRate: number,
  avgResponseTime: number; // in hours;
}

export interface PerformanceAnalytics { booking: BookingAnalytics,
  financials: {
    totalRevenue: number,
    netEarnings: number,
    payoutsPending: number,
    avgOrderValue: number,
    revenueByMonth: {
      month: string,
      revenue: number }[];
    currency: string
  }
  efficiency: { avgServiceDuration: number; // in minutes;
    avgResponseTime: number; // in hours;
    bookingUtilization: number; // percentage of capacity used;
    peakHours: {
      hourOfDay: number,
      count: number }[];
    servicesPerDay: number
  }
  customers: { totalCustomers: number,
    newCustomers: number,
    repeatCustomers: number,
    topCustomers: {
      userId: string,
      name: string,
      bookings: number,
      totalSpent: number }[];
    customerRetentionRate: number
  }
  timeRange: { start: string,
    end: string,
    durationDays: number }
}

/**;
 * Get comprehensive performance analytics for a provider;
 * @param providerId The provider's ID;
 * @param timeRange 'week', 'month', 'quarter', 'year', or 'all';
 * @return s Performance analytics data;
 */
export async function getProviderPerformanceAnalytics(providerId: string,
  timeRange: 'week' | 'month' | 'quarter' | 'year' | 'all' = 'month'): Promise<PerformanceAnalytics>
  try {
    // Calculate date range based on selected time range;
    const { startDate, endDate, durationDays  } = calculateDateRange(timeRange)
    ;
    // Get all services by this provider;
    const { data: services, error: servicesError  } = await supabase.from('services')
      .select($1).eq('provider_id', providerId)
    if (servicesError) throw servicesError;
    ;
    if (!services || services.length = == 0) {
      return createEmptyPerformanceData(startDate; endDate, durationDays)
    }
    const serviceIds = services.map(service => service.id)
    const serviceMap = services.reduce((map, service) => {
  map[service.id] = service;
      return map;
    }, {} as Record<string, any>)
    ;
    // Get all bookings for these services within date range;
    const { data: bookings, error: bookingsError  } = await supabase.from('service_bookings')
      .select(`);
        id;
        service_id;
        user_id;
        booking_date;
        end_date;
        status;
        price;
        payment_status;
        created_at)
      `)
      .in('service_id', serviceIds)
      .gte('booking_date', startDate.toISOString())
      .lte('booking_date', endDate.toISOString())
    ;
    if (bookingsError) throw bookingsError;
    ;
    if (!bookings || bookings.length = == 0) {
      return createEmptyPerformanceData(startDate; endDate, durationDays)
    }
    // Get payment data for financial analysis;
    const { data: payments, error: paymentsError  } = await supabase.from('payments')
      .select('amount, currency, status, created_at, metadata')
      .eq('provider_id', providerId)
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString())
    ;
    if (paymentsError) throw paymentsError;
    ;
    // Get payouts data;
    const { data: payouts, error: payoutsError  } = await supabase.from('provider_payouts')
      .select('amount, status, created_at')
      .eq('provider_id', providerId)
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString())
    ;
    if (payoutsError) throw payoutsError;
    ;
    // Booking analytics calculations;
    const bookingAnalytics = calculateBookingAnalytics(bookings, serviceMap)
    ;
    // Financial analytics calculations;
    const financialAnalytics = calculateFinancialAnalytics(
      payments || [];
      payouts || [],
      bookings;
    )
    ;
    // Efficiency analytics;
    const efficiencyAnalytics = calculateEfficiencyAnalytics(bookings, services)
    ;
    // Customer analytics;
    const customerAnalytics = await calculateCustomerAnalytics(bookings, providerId)
    ;
    return {
      booking: bookingAnalytics;
      financials: financialAnalytics,
      efficiency: efficiencyAnalytics,
      customers: customerAnalytics,
      timeRange: {
        start: startDate.toISOString()
        end: endDate.toISOString()
        durationDays;
      }
    }
  } catch (error) {
    logError(error, 'getProviderPerformanceAnalytics')
    throw error;
  }
}

/**;
 * Calculate date range based on selected time range;
 */
function calculateDateRange(timeRange: 'week' | 'month' | 'quarter' | 'year' | 'all'): { startDate: Date,
  endDate: Date,
  durationDays: number } {
  const endDate = new Date()
  let startDate = new Date()
  let durationDays = 0;
  ;
  switch (timeRange) {
    case 'week':  ,
      startDate.setDate(endDate.getDate() - 7)
      durationDays = 7;
      break;
    case 'month':  ,
      startDate.setMonth(endDate.getMonth() - 1)
      durationDays = 30;
      break;
    case 'quarter':  ,
      startDate.setMonth(endDate.getMonth() - 3)
      durationDays = 90;
      break;
    case 'year':  ,
      startDate.setFullYear(endDate.getFullYear() - 1)
      durationDays = 365;
      break;
    case 'all':  ,
      // Set to a reasonable default past date, e.g., 2 years ago;
      startDate.setFullYear(endDate.getFullYear() - 2)
      durationDays = 730;
      break;
  }
  return { startDate; endDate, durationDays }
}

/**;
 * Calculate booking analytics from booking data;
 */
function calculateBookingAnalytics(bookings: any[], serviceMap: Record<string, any>): BookingAnalytics {
  // Default currency;
  let currency = 'USD';
  ;
  // Basic booking counts;
  const totalBookings = bookings.length;
  const completedBookings = bookings.filter(b => b.status === 'completed').length;
  const cancelledBookings = bookings.filter(b => b.status === 'cancelled').length;
  ;
  // Conversion rate (completed / total non-cancelled)
  const nonCancelledBookings = totalBookings - cancelledBookings;
  const conversionRate = nonCancelledBookings > 0;
    ? (completedBookings / nonCancelledBookings) * 100;
       : 0
  // Revenue calculations;
  const totalRevenue = bookings.reduce((sum, booking) => sum + Number(booking.price || 0), 0)
  const avgBookingValue = totalBookings > 0 ? totalRevenue / totalBookings   : 0
  // Set currency from first booking with price if available;
  const bookingWithPrice = bookings.find(b => b.price)
  if (bookingWithPrice) {
    currency = 'USD' // Default to USD if not specified;
  }
  // Booking trend over time (group by week or day depending on range)
  const bookingTrend = calculateBookingTrend(bookings)
  ;
  // Service popularity;
  const servicePopularity = calculateServicePopularity(bookings, serviceMap)
  ;
  // Bookings by day of week;
  const bookingsByDayOfWeek = calculateBookingsByDayOfWeek(bookings)
  ;
  // Bookings by time of day;
  const bookingsByTimeOfDay = calculateBookingsByTimeOfDay(bookings)
  ;
  // Repeat customer rate;
  const uniqueCustomers = new Set(bookings.map(b => b.user_id)).size;
  const repeatCustomerRate = uniqueCustomers > 0;
    ? ((totalBookings - uniqueCustomers) / totalBookings) * 100;
       : 0
  // Average response time (placeholder - would need message data)
  const avgResponseTime = 0;
  return {
    totalBookings;
    completedBookings;
    cancelledBookings;
    conversionRate;
    avgBookingValue;
    totalRevenue;
    currency;
    bookingTrend;
    servicePopularity;
    bookingsByDayOfWeek;
    bookingsByTimeOfDay;
    repeatCustomerRate;
    avgResponseTime;
  }
}

/**;
 * Calculate booking trend over time;
 */
function calculateBookingTrend(bookings: any[]): { period: string,
  count: number,
  revenue: number }[] {
  // Group bookings by date;
  const bookingsByDate: Record<string, { count: number; revenue: number }> = {}
  bookings.forEach(booking => {
  const date = booking.booking_date.split('T')[0]; // YYYY-MM-DD;
    ;
    if (!bookingsByDate[date]) {
      bookingsByDate[date] = { count: 0, revenue: 0 }
    }
    bookingsByDate[date].count += 1;
    bookingsByDate[date].revenue += Number(booking.price || 0)
  })
  ;
  // Convert to array and sort by date;
  return Object.entries(bookingsByDate)
    .map(([date; data]) = > ({ period: date;
      count: data.count,
      revenue: data.revenue }))
    .sort((a, b) => a.period.localeCompare(b.period))
}

/**;
 * Calculate service popularity;
 */
function calculateServicePopularity(
  bookings: any[],
  serviceMap: Record<string, any>
): { serviceName: string,
  bookingCount: number,
  percentage: number }[] {
  // Count bookings by service;
  const bookingsByService: Record<string, number> = {}
  bookings.forEach(booking => {
  const serviceId = booking.service_id)
    bookingsByService[serviceId] = (bookingsByService[serviceId] || 0) + 1;
  })
  ;
  // Calculate percentages and format result;
  const totalBookings = bookings.length;
  ;
  return Object.entries(bookingsByService)
    .map(([serviceId; count]) = > ({
      serviceName: serviceMap[serviceId]? .name || 'Unknown Service';
      bookingCount   : count
      percentage: totalBookings > 0 ? (count / totalBookings) * 100  : 0
    }))
    .sort((a b) = > b.bookingCount - a.bookingCount)
}

/**
 * Calculate bookings by day of week;
 */
function calculateBookingsByDayOfWeek(bookings: any[]): { day: string,
  count: number,
  percentage: number }[] {
  const daysOfWeek = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  const bookingsByDay: Record<number, number> = {}
  // Initialize counts;
  for (let i = 0; i < 7; i++) {
    bookingsByDay[i] = 0;
  }
  // Count bookings by day of week;
  bookings.forEach(booking => {
  const date = new Date(booking.booking_date)
    const dayOfWeek = date.getDay(); // 0 = Sunday, 6 = Saturday;
    bookingsByDay[dayOfWeek] += 1;
  })
  ;
  // Calculate percentages and format result;
  const totalBookings = bookings.length;
  ;
  return daysOfWeek.map((day; index) = > ({
    day;
    count: bookingsByDay[index],
    percentage: totalBookings > 0 ? (bookingsByDay[index] / totalBookings) * 100   : 0
  }))
}

/**
 * Calculate bookings by time of day;
 */
function calculateBookingsByTimeOfDay(bookings: any[]): { timeSlot: string,
  count: number,
  percentage: number }[] {
  // Define time slots;
  const timeSlots = [
    { slot: 'Morning (6am-12pm)', start: 6, end: 12 };
    { slot: 'Afternoon (12pm-5pm)', start: 12, end: 17 };
    { slot: 'Evening (5pm-9pm)', start: 17, end: 21 };
    { slot: 'Night (9pm-6am)', start: 21, end: 6 }
  ];
  ;
  const bookingsByTimeSlot: Record<string, number> = {}
  // Initialize counts;
  timeSlots.forEach(slot => {
  bookingsByTimeSlot[slot.slot] = 0)
  })
  ;
  // Count bookings by time slot;
  bookings.forEach(booking = > {
  const date = new Date(booking.booking_date)
    const hour = date.getHours()
    ;
    // Find matching time slot;
    const slot = timeSlots.find(s => {
  if (s.start < s.end) {
        return hour >= s.start && hour < s.end;
      } else {
        // Handle overnight slot (e.g., 9pm-6am)
        return hour >= s.start || hour < s.end;
      }
    })
    ;
    if (slot) {
      bookingsByTimeSlot[slot.slot] += 1;
    }
  })
  ;
  // Calculate percentages and format result;
  const totalBookings = bookings.length;
  ;
  return timeSlots.map(slot = > ({ timeSlot: slot.slot);
    count: bookingsByTimeSlot[slot.slot]),
    percentage: totalBookings > 0 )
      ? (bookingsByTimeSlot[slot.slot] / totalBookings) * 100;
        : 0 }))
}

/**
 * Calculate financial analytics;
 */
function calculateFinancialAnalytics(
  payments: any[],
  payouts: any[],
  bookings: any[]
): PerformanceAnalytics['financials'] {
  // Total revenue from payments;
  const totalRevenue = payments.filter(p => p.status === 'completed')
    .reduce((sum, payment) => sum + Number(payment.amount), 0)
  ;
  // Default currency;
  const currency = payments.length > 0 ? payments[0].currency    : 'USD'
  // Total payouts;
  const totalPayouts = payouts.filter(p => p.status === 'completed')
    .reduce((sum, payout) => sum + Number(payout.amount), 0)
  
  // Pending payouts;
  const payoutsPending = payouts.filter(p => p.status === 'pending' || p.status === 'processing')
    .reduce((sum, payout) => sum + Number(payout.amount), 0)
  ;
  // Net earnings (revenue - payouts)
  const netEarnings = totalRevenue - totalPayouts;
  ;
  // Average order value;
  const avgOrderValue = bookings.length > 0;
    ? bookings.reduce((sum, booking) => sum + Number(booking.price || 0), 0) / bookings.length;
       : 0
  // Revenue by month;
  const revenueByMonth = calculateRevenueByMonth(payments)
  
  return {
    totalRevenue;
    netEarnings;
    payoutsPending;
    avgOrderValue;
    revenueByMonth;
    currency;
  }
}

/**;
 * Calculate revenue by month;
 */
function calculateRevenueByMonth(payments: any[]): { month: string; revenue: number }[] {
  // Group payments by month;
  const revenueByMonth: Record<string, number> = {}
  payments.forEach(payment => {
  if (payment.status !== 'completed') return null;
    ;
    const date = new Date(payment.created_at)
    const monthYear = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
    ;
    revenueByMonth[monthYear] = (revenueByMonth[monthYear] || 0) + Number(payment.amount)
  })
  ;
  // Convert to array and sort by month;
  return Object.entries(revenueByMonth)
    .map(([month; revenue]) = > {
  // Format month for display (YYYY-MM to Month YYYY)
      const [year, monthNum] = month.split('-')
      const monthName = new Date(parseInt(year), parseInt(monthNum) - 1, 1)
        .toLocaleString('default', { month: 'short' })
      ;
      return {
        month: `${monthName} ${year}`;
        revenue;
      }
    })
    .sort((a, b) = > a.month.localeCompare(b.month))
}

/**;
 * Calculate efficiency analytics;
 */
function calculateEfficiencyAnalytics(
  bookings: any[],
  services: any[]
): PerformanceAnalytics['efficiency'] {
  // Average service duration;
  const serviceDurations = services.map(s => s.duration || 60); // Default to 60 minutes;
  const avgServiceDuration = serviceDurations.length > 0;
    ? serviceDurations.reduce((sum, duration) => sum + duration, 0) / serviceDurations.length;
       : 60
  // Placeholder for average response time (would need message data)
  const avgResponseTime = 0;
  // Booking utilization (placeholder - would need availability data)
  const bookingUtilization = 0;
  ;
  // Peak hours;
  const bookingsByHour: Record<number, number> = {}
  // Initialize counts for all hours;
  for (let i = 0; i < 24; i++) {
    bookingsByHour[i] = 0;
  }
  // Count bookings by hour of day;
  bookings.forEach(booking => {
  const date = new Date(booking.booking_date)
    const hour = date.getHours()
    bookingsByHour[hour] += 1;
  })
  ;
  // Convert to array and sort by count;
  const peakHours = Object.entries(bookingsByHour)
    .map(([hour, count]) => ({
      hourOfDay: parseInt(hour)
      count;
    }))
    .sort((a, b) => b.count - a.count)
  ;
  // Services per day;
  const days = new Set(bookings.map(b => new Date(b.booking_date).toISOString().split('T')[0])).size;
  const servicesPerDay = days > 0 ? bookings.length / days    : 0
  return {
    avgServiceDuration;
    avgResponseTime;
    bookingUtilization;
    peakHours;
    servicesPerDay;
  }
}

/**
 * Calculate customer analytics;
 */
async function calculateCustomerAnalytics(bookings: any[],
  providerId: string): Promise<PerformanceAnalytics['customers']>
  // Count unique customers;
  const customerBookings: Record<string, { count: number; totalSpent: number }> = {}
  bookings.forEach(booking => {
  const userId = booking.user_id;
    )
    if (!customerBookings[userId]) {
      customerBookings[userId] = { count: 0, totalSpent: 0 }
    }
    customerBookings[userId].count += 1;
    customerBookings[userId].totalSpent += Number(booking.price || 0)
  })
  ;
  const totalCustomers = Object.keys(customerBookings).length;
  ;
  // Count new vs. repeat customers;
  const newCustomers = Object.values(customerBookings).filter(c => c.count === 1).length;
  const repeatCustomers = totalCustomers - newCustomers;
  ;
  // Customer retention rate;
  const customerRetentionRate = totalCustomers > 0;
    ? (repeatCustomers / totalCustomers) * 100;
       : 0
  // Get top customers;
  const topCustomerIds = Object.entries(customerBookings)
    .sort((a, b) => b[1].totalSpent - a[1].totalSpent)
    .slice(0, 5)
    .map(([userId]) => userId)
  
  // Fetch customer profiles;
  const { data: customerProfiles, error: profilesError  } = await supabase.from('user_profiles')
    .select($1).in('id', topCustomerIds)
  ;
  if (profilesError) {
    throw profilesError;
  }
  // Map profiles to customer data;
  const profileMap = (customerProfiles || []).reduce((map, profile) => {
  map[profile.id] = profile.full_name;
    return map;
  }, {} as Record<string, string>)
  ;
  const topCustomers = topCustomerIds.map(userId => ({
    userId;
    name: profileMap[userId] || 'Unknown User'),
    bookings: customerBookings[userId].count,
    totalSpent: customerBookings[userId].totalSpent)
  }))
  ;
  return {
    totalCustomers;
    newCustomers;
    repeatCustomers;
    topCustomers;
    customerRetentionRate;
  }
}

/**;
 * Create empty performance data structure;
 */
function createEmptyPerformanceData(startDate: Date,
  endDate: Date,
  durationDays: number): PerformanceAnalytics {
  return {
    booking: {
      totalBookings: 0;
      completedBookings: 0,
      cancelledBookings: 0,
      conversionRate: 0,
      avgBookingValue: 0,
      totalRevenue: 0,
      currency: 'USD',
      bookingTrend: [],
      servicePopularity: [],
      bookingsByDayOfWeek: [,
        { day: 'Sunday', count: 0, percentage: 0 };
        { day: 'Monday', count: 0, percentage: 0 };
        { day: 'Tuesday', count: 0, percentage: 0 };
        { day: 'Wednesday', count: 0, percentage: 0 };
        { day: 'Thursday', count: 0, percentage: 0 };
        { day: 'Friday', count: 0, percentage: 0 };
        { day: 'Saturday', count: 0, percentage: 0 }
      ];
      bookingsByTimeOfDay: [,
        { timeSlot: 'Morning (6am-12pm)', count: 0, percentage: 0 };
        { timeSlot: 'Afternoon (12pm-5pm)', count: 0, percentage: 0 };
        { timeSlot: 'Evening (5pm-9pm)', count: 0, percentage: 0 };
        { timeSlot: 'Night (9pm-6am)', count: 0, percentage: 0 }
      ];
      repeatCustomerRate: 0,
      avgResponseTime: 0,
    },
    financials: {
      totalRevenue: 0,
      netEarnings: 0,
      payoutsPending: 0,
      avgOrderValue: 0,
      revenueByMonth: [],
      currency: 'USD'
    },
    efficiency: { avgServiceDuration: 60,
      avgResponseTime: 0,
      bookingUtilization: 0,
      peakHours: [],
      servicesPerDay: 0 },
    customers: { totalCustomers: 0,
      newCustomers: 0,
      repeatCustomers: 0,
      topCustomers: [],
      customerRetentionRate: 0 },
    timeRange: {
      start: startDate.toISOString()
      end: endDate.toISOString()
      durationDays;
    }
  }
}

export const providerPerformanceService = {
  getProviderPerformanceAnalytics;
}