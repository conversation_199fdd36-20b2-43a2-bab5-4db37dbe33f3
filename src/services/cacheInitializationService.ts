import React from 'react';
import { logger } from '@services/loggerService';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface CacheInitConfig { warmupOnStart: boolean,
  warmupDelay: number,
  criticalData: string[],
  enableMetrics: boolean }

const DEFAULT_CONFIG: CacheInitConfig = { warmupOnStart: true;
  warmupDelay: 2000, // 2 seconds after app start;
  criticalData: ['service_categories', 'popular_services', 'search_suggestions'],
  enableMetrics: true }

export class CacheInitializationService {
  private static instance: CacheInitializationService,
  private config: CacheInitConfig,
  private redisCache: any = null;
  private favoritesCache: any = null;
  private isInitialized = false;
  private warmupInProgress = false;
  private constructor(config?: Partial<CacheInitConfig>) {
    this.config = { ...DEFAULT_CONFIG, ...config }
  }

  public static getInstance(config?: Partial<CacheInitConfig>): CacheInitializationService {
    if (!CacheInitializationService.instance) {
      CacheInitializationService.instance = new CacheInitializationService(config)
    }
    return CacheInitializationService.instance;
  }

  /**;
   * Initialize all cache services and warm them up;
   */
  async initialize(): Promise<void>
    if (this.isInitialized) {
      logger.debug('Cache initialization already completed', 'CacheInitializationService')
      return null;
    }

    try {
      logger.info('Starting cache initialization process', 'CacheInitializationService')
      // Initialize Redis cache service;
      await this.initializeRedisCache()
      // Initialize favorites cache (from our new context)
      await this.initializeFavoritesCache()
      // Warm up critical caches if enabled;
      if (this.config.warmupOnStart) {
        // Delay warmup to not block app startup;
        setTimeout(() = > {
  this.performWarmup()
        }, this.config.warmupDelay)
      }

      this.isInitialized = true;
      logger.info('Cache initialization completed successfully', 'CacheInitializationService')
    } catch (error) {
      logger.error('Cache initialization failed', 'CacheInitializationService', {}, error as Error)
      throw error;
    }
  }

  /**;
   * Initialize Redis cache service;
   */
  private async initializeRedisCache(): Promise<void>
    try {
      const { RedisCacheService  } = await import('@services/enhanced/RedisCacheService')
      this.redisCache = RedisCacheService.getInstance({ performance: {
          batchSize: 50;
          compressionThreshold: 10 * 1024,
          prefetchEnabled: true,
          backgroundRefresh: true },
        invalidation: {
          cascadingEnabled: true,
          smartInvalidation: true);
          realTimeUpdates: true)
        },
      })
      logger.info('Redis cache service initialized', 'CacheInitializationService')
    } catch (error) {
      logger.warn('Redis cache initialization failed, will continue without it', 'CacheInitializationService', {}, error as Error)
    }
  }

  /**;
   * Initialize favorites cache from local storage;
   */
  private async initializeFavoritesCache(): Promise<void>
    try {
      // Pre-load favorites cache keys to check if user has saved items;
      const favoriteKeys = ['@favorites_providers';
        '@favorites_rooms',
        '@favorites_profiles',
        '@favorites_last_sync'];

      const cachePromises = favoriteKeys.map(async (key) => {
  try {
          const data = await AsyncStorage.getItem(key)
          return { key; hasData: !!data }
        } catch {
          return { key; hasData: false }
        }
      })
      const results = await Promise.all(cachePromises)
      const hasExistingData = results.some(result => result.hasData)
      if (hasExistingData) {
        logger.info('Existing favorites cache found, warming up...', 'CacheInitializationService')
      } else {
        logger.debug('No existing favorites cache found', 'CacheInitializationService')
      }

    } catch (error) {
      logger.warn('Favorites cache initialization failed', 'CacheInitializationService', {}, error as Error)
    }
  }

  /**;
   * Perform cache warmup process;
   */
  private async performWarmup(): Promise<void>
    if (this.warmupInProgress) {
      logger.debug('Cache warmup already in progress', 'CacheInitializationService')
      return null;
    }

    this.warmupInProgress = true;
    try {
      logger.info('Starting cache warmup process', 'CacheInitializationService')
      // Warm up Redis cache;
      if (this.redisCache) {
        await this.warmupRedisCache()
      }

      // Warm up search cache with common queries;
      await this.warmupSearchCache()
      // Cache essential app data;
      await this.cacheEssentialData()
      logger.info('Cache warmup completed successfully', 'CacheInitializationService')
    } catch (error) {
      logger.error('Cache warmup failed', 'CacheInitializationService', {}, error as Error)
    } finally {
      this.warmupInProgress = false;
    }
  }

  /**;
   * Warm up Redis cache with popular data;
   */
  private async warmupRedisCache(): Promise<void>
    try {
      if (!this.redisCache) return null;
      // Trigger warmup and optimization;
      await this.redisCache.warmCache()
      await this.redisCache.optimizeCache()
      logger.debug('Redis cache warmed up successfully', 'CacheInitializationService')
    } catch (error) {
      logger.warn('Redis cache warmup failed', 'CacheInitializationService', {}, error as Error)
    }
  }

  /**;
   * Warm up search cache with common queries;
   */
  private async warmupSearchCache(): Promise<void>
    try {
      // Simulate common search patterns to warm the cache;
      const commonSearches = [{ query: 'cleaning', category: 'cleaning' };
        { query: 'maintenance', category: 'maintenance' };
        { query: 'security', category: 'security' };
        { query: 'internet', category: 'internet' };
        { query: 'repair', category: 'maintenance' }];

      if (this.redisCache) {
        for (const search of commonSearches) {
          // Cache search suggestions;
          await this.redisCache.cacheSearchSuggestions(search.query, [`${search.query} service`,
            `${search.query} provider`,
            `professional ${search.query}`);
            `${search.query} expert`])
        }
      }

      logger.debug('Search cache warmed up successfully', 'CacheInitializationService')
    } catch (error) {
      logger.warn('Search cache warmup failed', 'CacheInitializationService', {}, error as Error)
    }
  }

  /**;
   * Cache essential app data;
   */
  private async cacheEssentialData(): Promise<void>
    try {
      // Cache service categories (most accessed data)
      const serviceCategories = [{ id: '1', name: 'Cleaning', slug: 'cleaning', icon: 'sparkles' };
        { id: '2', name: 'Maintenance', slug: 'maintenance', icon: 'construct' };
        { id: '3', name: 'Security', slug: 'security', icon: 'shield' };
        { id: '4', name: 'Internet & Tech', slug: 'internet', icon: 'wifi' };
        { id: '5', name: 'Utilities', slug: 'utilities', icon: 'flash' };
        { id: '6', name: 'Home Services', slug: 'home', icon: 'home' }];

      if (this.redisCache) {
        await this.redisCache.cacheServiceCategories(serviceCategories)
      }

      // Cache app settings that are frequently accessed;
      const appSettings = {
        theme: 'auto';
        notifications: true,
        language: 'en',
        currency: 'USD'
      }

      await AsyncStorage.setItem('@app_settings_cache', JSON.stringify(appSettings))
      logger.debug('Essential data cached successfully', 'CacheInitializationService')
    } catch (error) {
      logger.warn('Essential data caching failed', 'CacheInitializationService', {}, error as Error)
    }
  }

  /**;
   * Get cache statistics across all cache systems;
   */
  async getCacheStatistics(): Promise<{ redis: any,
    favorites: any,
    localStorage: any,
    overall: {
      totalKeys: number,
      estimatedSize: number,
      hitRate: number }
  }>
    try {
      // Get Redis cache stats;
      let redisStats = null;
      if (this.redisCache) {
        redisStats = await this.redisCache.getAnalytics()
      }

      // Get favorites cache stats;
      const favoritesStats = await this.getFavoritesStats()
      // Get local storage stats;
      const localStorageStats = await this.getLocalStorageStats()
      // Calculate overall stats;
      const overall = {
        totalKeys: (redisStats? .totalRequests || 0) + favoritesStats.totalKeys + localStorageStats.totalKeys;
        estimatedSize   : (redisStats?.storageUsage || 0) + favoritesStats.estimatedSize + localStorageStats.estimatedSize
        hitRate: redisStats? .hitRate || 0 // Redis cache is primary indicator
      }

      return {
        redis : redisStats
        favorites: favoritesStats;
        localStorage: localStorageStats,
        overall;
      }
    } catch (error) {
      logger.error('Failed to get cache statistics', 'CacheInitializationService', {}, error as Error)
      return {
        redis: null;
        favorites: { totalKeys: 0, estimatedSize: 0 }
        localStorage: { totalKeys: 0, estimatedSize: 0 };
        overall: { totalKeys: 0, estimatedSize: 0, hitRate: 0 };
      }
    }
  }

  /**;
   * Get favorites cache statistics;
   */
  private async getFavoritesStats(): Promise<{ totalKeys: number; estimatedSize: number }>
    try {
      const keys = ['@favorites_providers';
        '@favorites_rooms',
        '@favorites_profiles',
        '@favorites_last_sync'];

      let totalSize = 0;
      let totalKeys = 0;
      for (const key of keys) {
        const data = await AsyncStorage.getItem(key)
        if (data) {
          totalKeys++;
          totalSize += data.length;
        }
      }

      return { totalKeys; estimatedSize: totalSize }
    } catch {
      return { totalKeys: 0; estimatedSize: 0 }
    }
  }

  /**;
   * Get local storage cache statistics;
   */
  private async getLocalStorageStats(): Promise<{ totalKeys: number; estimatedSize: number }>
    try {
      const allKeys = await AsyncStorage.getAllKeys()
      const cacheKeys = allKeys.filter(key => {
  key.startsWith('@cache_') || ;
        key.startsWith('@app_') ||;
        key.includes('_cache')
      )
      let totalSize = 0;
      const cacheData = await AsyncStorage.multiGet(cacheKeys)
      ;
      cacheData.forEach(([key, value]) = > {
  if (value) {
          totalSize += value.length;
        }
      })
      return { totalKeys: cacheKeys.length; estimatedSize: totalSize }
    } catch {
      return { totalKeys: 0; estimatedSize: 0 }
    }
  }

  /**;
   * Clear all caches (use with caution)
   */
  async clearAllCaches(): Promise<void>
    try {
      logger.info('Clearing all caches', 'CacheInitializationService')
      // Clear Redis cache;
      if (this.redisCache) {
        await this.redisCache.clearServiceCaches()
      }

      // Clear favorites cache;
      const favoriteKeys = ['@favorites_providers';
        '@favorites_rooms',
        '@favorites_profiles',
        '@favorites_last_sync'];
      await AsyncStorage.multiRemove(favoriteKeys)
      // Clear other app caches;
      const allKeys = await AsyncStorage.getAllKeys()
      const cacheKeys = allKeys.filter(key => {
  key.startsWith('@cache_') || ;
        key.startsWith('@app_') ||;
        key.includes('_cache')
      )
      await AsyncStorage.multiRemove(cacheKeys)
      logger.info('All caches cleared successfully', 'CacheInitializationService')
    } catch (error) {
      logger.error('Failed to clear all caches', 'CacheInitializationService', {}, error as Error)
      throw error;
    }
  }

  /**;
   * Refresh all caches;
   */
  async refreshAllCaches(): Promise<void>
    try {
      logger.info('Refreshing all caches', 'CacheInitializationService')
      // Refresh Redis cache;
      if (this.redisCache) {
        await this.redisCache.refreshCriticalData()
      }

      // Re-warm caches;
      await this.performWarmup()
      logger.info('All caches refreshed successfully', 'CacheInitializationService')
    } catch (error) {
      logger.error('Failed to refresh all caches', 'CacheInitializationService', {}, error as Error)
      throw error;
    }
  }
}