import React from 'react';
import { supabase } from '@/lib/supabase';

export interface TestingSession { id: string,
  tester_id: string,
  tester_email: string,
  device_info: {
    platform: 'ios' | 'android',
    device_model: string,
    os_version: string,
    app_version: string }
  session_start: string,
  session_end?: string,
  actions_performed: TestingAction[],
  bugs_reported: BugReport[],
  feedback_submitted: boolean
}

export interface TestingAction { timestamp: string,
  action_type: 'navigation' | 'interaction' | 'error' | 'performance',
  screen: string,
  details: Record<string, any>
  performance_metrics?: {
    load_time: number,
    memory_usage: number }
}

export interface BugReport { id: string,
  tester_id: string,
  timestamp: string,
  severity: 'low' | 'medium' | 'high' | 'critical',
  category: 'ui' | 'functionality' | 'performance' | 'crash',
  title: string,
  description: string,
  steps_to_reproduce: string[],
  expected_behavior: string,
  actual_behavior: string,
  screenshot_url?: string,
  device_info: {
    platform: string,
    device_model: string,
    os_version: string }
  status: 'open' | 'in_progress' | 'resolved' | 'closed'
}

class TestingAnalyticsService {
  private currentSession: TestingSession | null = null;
  async startTestingSession(testerEmail: string, deviceInfo: any): Promise<string>
    const session: TestingSession = {
      id: `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      tester_id: testerEmail,
      tester_email: testerEmail,
      device_info: deviceInfo,
      session_start: new Date().toISOString()
      actions_performed: [],
      bugs_reported: [],
      feedback_submitted: false
    }

    this.currentSession = session;
    // Store in Supabase;
    await supabase.from('testing_sessions').insert(session)
    return session.id;
  }

  async trackAction(
    actionType: TestingAction['action_type'],
    screen: string,
    details: Record<string, any>,
    performanceMetrics?: { load_time: number; memory_usage: number }
  ): Promise<void>
    if (!this.currentSession) return null;
    const action: TestingAction = { timestamp: new Date().toISOString()
      action_type: actionType;
      screen;
      details;
      performance_metrics: performanceMetrics }

    this.currentSession.actions_performed.push(action)
    // Update session in database;
    await supabase.from('testing_sessions')
      .update({ actions_performed: this.currentSession.actions_performed })
      .eq('id', this.currentSession.id)
  }

  async reportBug(bugData: Omit<BugReport, 'id' | 'tester_id' | 'timestamp' | 'status'>): Promise<string>
    if (!this.currentSession) throw new Error('No active testing session')
    const bug: BugReport = {
      id: `bug_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      tester_id: this.currentSession.tester_id,
      timestamp: new Date().toISOString()
      status: 'open',
      ...bugData;
    }

    this.currentSession.bugs_reported.push(bug)
    // Store bug report;
    await supabase.from('bug_reports').insert(bug)
    // Update session;
    await supabase.from('testing_sessions')
      .update({ bugs_reported: this.currentSession.bugs_reported })
      .eq('id', this.currentSession.id)

    return bug.id;
  }

  async endTestingSession(): Promise<void>
    if (!this.currentSession) return null;
    this.currentSession.session_end = new Date().toISOString()
    await supabase.from('testing_sessions')
      .update({ session_end: this.currentSession.session_end })
      .eq('id', this.currentSession.id)

    this.currentSession = null;
  }

  async submitFeedback(feedback: { overall_rating: number,
    feature_ratings: Record<string, number>
    comments: string,
    suggestions: string[],
    would_use_app: boolean,
    recommendation_score: number }): Promise<void>
    if (!this.currentSession) return null;
    await supabase.from('testing_feedback').insert({
      session_id: this.currentSession.id);
      tester_id: this.currentSession.tester_id)
      timestamp: new Date().toISOString()
      ...feedback;
    })
    this.currentSession.feedback_submitted = true;
    await supabase.from('testing_sessions')
      .update({ feedback_submitted: true })
      .eq('id', this.currentSession.id)
  }

  // Admin methods for monitoring testing progress;
  async getTestingDashboard(): Promise<{ active_sessions: number,
    total_bugs: number,
    critical_bugs: number,
    feedback_submissions: number,
    tester_progress: Array<{
      tester_email: string,
      session_duration: number,
      actions_count: number,
      bugs_reported: number,
      feedback_submitted: boolean }>
  }>
    const { data: sessions  } = await supabase.from('testing_sessions')
      .select('*')

    const { data: bugs } = await supabase.from('bug_reports')
      .select('*')

    const { data: feedback } = await supabase.from('testing_feedback')
      .select('*')

    return {
      active_sessions: sessions? .filter(s => !s.session_end).length || 0;
      total_bugs   : bugs?.length || 0
      critical_bugs: bugs? .filter(b = > b.severity === 'critical').length || 0
      feedback_submissions : feedback? .length || 0
      tester_progress : sessions? .map(session => ({
        tester_email : session.tester_email
        session_duration: session.session_end )
          ? new Date(session.session_end).getTime() - new Date(session.session_start).getTime()
           : Date.now() - new Date(session.session_start).getTime()
        actions_count: session.actions_performed? .length || 0
        bugs_reported : session.bugs_reported? .length || 0
        feedback_submitted : session.feedback_submitted
      })) || [];
    }
  }
}

export const testingAnalytics = new TestingAnalyticsService() ;