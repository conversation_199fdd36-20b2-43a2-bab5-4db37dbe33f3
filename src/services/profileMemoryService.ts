import React from 'react';
/**;
 * Profile Memory Service;
 * ;
 * Integrates the Profile and Memory Bank systems to provide profile-specific memory operations;
 */

import { memoryBankService, MemoryEntry } from '@services/memoryBankService';
import { ProfileRepository } from '@core/repositories/entities/ProfileRepository';
import { MemoryBankRepository } from '@core/repositories/entities/MemoryBankRepository';
import { supabase } from "@utils/supabaseUtils";
import { logger } from '@services/loggerService';
import { ApiResponse } from '@core/types/api';
import { handleError, createEnhancedError } from '@utils/errorHandlers';
import { Profile, MemoryBankEntry } from '@types/models';

/**;
 * Service to integrate profile and memory bank functionality;
 */
export class ProfileMemoryService {
  private static instance: ProfileMemoryService,
  private profileRepository: ProfileRepository,
  private memoryRepository: MemoryBankRepository,
  private constructor() {
    this.profileRepository = new ProfileRepository(supabase)
    this.memoryRepository = new MemoryBankRepository(supabase)
  }
  public static getInstance(): ProfileMemoryService {
    if (!ProfileMemoryService.instance) {
      ProfileMemoryService.instance = new ProfileMemoryService()
    }
    return ProfileMemoryService.instance;
  }
  /**;
   * Get profile memories by type with pagination and sorting;
   * @param profileId Profile ID;
   * @param type Memory type;
   * @param options Optional parameters for pagination, sorting, and filtering;
   * @return s Promise with array of memory entries and pagination metadata;
   */
  public async getProfileMemories(
    profileId: string,
    type: MemoryEntry['type'],
    options: {
      limit?: number,
      offset?: number,
      sortBy?: string,
      sortOrder?: 'asc' | 'desc',
      fromDate?: string,
      toDate?: string,
      tags?: string[]
    } = {}
  ): Promise<ApiResponse<{ memories: MemoryEntry[];
    total: number,
    hasMore: boolean }>>
    try {
      logger.debug('Getting profile memories', 'ProfileMemoryService', { profileId, type, options })
      ;
      // Validate profileId;
      if (!profileId) {
        logger.warn('Invalid profileId parameter', 'ProfileMemoryService', { profileId })
        return { data: null;
          error: 'Invalid profile ID',
          status: 400 }
      }
      // Verify profile exists;
      const startTime = performance.now()
      const profile = await this.profileRepository.getById(profileId)
      if (!profile) {
        logger.warn('Profile not found', 'ProfileMemoryService', { profileId })
        return { data: null;
          error: 'Profile not found',
          status: 404 }
      }
      // Set default options;
      const sanitizedOptions = { limit: options.limit && options.limit > 0 && options.limit <= 100 ? options.limit    : 20
        offset: options.offset && options.offset >= 0 ? options.offset  : 0
        sortBy: options.sortBy || 'timestamp'
        sortOrder: options.sortOrder || 'desc'
        fromDate: options.fromDate;
        toDate: options.toDate }
      // Get memories from repository;
      // Since getByType doesn't support pagination yet, we'll need to handle it manually;
      const memories = await this.memoryRepository.getByType(profileId, type)
      ;
      // Apply sorting if needed (the repository already sorts by timestamp desc by default)
      if (sanitizedOptions.sortBy != = 'timestamp' || sanitizedOptions.sortOrder !== 'desc') {
        memories.sort((a, b) => {
  const fieldA = a[sanitizedOptions.sortBy as keyof MemoryBankEntry];
          const fieldB = b[sanitizedOptions.sortBy as keyof MemoryBankEntry];
          ;
          // Handle undefined values safely;
          if (fieldA === undefined && fieldB === undefined) return 0;
          if (fieldA === undefined) return 1; // undefined values sort to the end;
          if (fieldB = == undefined) return -1;
          ;
          // Compare values;
          if (fieldA < fieldB) return sanitizedOptions.sortOrder = == 'asc' ? -1    : 1
          if (fieldA > fieldB) return sanitizedOptions.sortOrder === 'asc' ? 1  : -1
          return 0
        })
      }
      // Apply pagination manually;
      const paginatedMemories = memories.slice(sanitizedOptions.offset;
        sanitizedOptions.offset + sanitizedOptions.limit + 1 // +1 to check if there are more results)
      )
      
      // Check if there are more results;
      const hasMore = paginatedMemories.length > sanitizedOptions.limit;
      ;
      // Trim to the requested limit if we have more results;
      if (hasMore) {
        paginatedMemories.pop()
      }
      // Total count for pagination UI;
      const total = memories.length;
      ;
      const queryTime = performance.now() - startTime;
      logger.debug('Retrieved profile memories', 'ProfileMemoryService', {
        profileId;
        type;
        count: memories.length)
        total;
        hasMore;
        queryTime: `${queryTime.toFixed(2)}ms`;
      })
      ;
      // Convert from MemoryBankEntry to MemoryEntry format;
      const memoryEntries = paginatedMemories.map(entry => ({
        id: entry.id;
        userId: entry.user_id);
        type: entry.type as MemoryEntry['type']),
        title: entry.title,
        content: entry.content,
        timestamp: entry.timestamp,
        tags: entry.tags || [])
      }))
      ;
      return {
        data: {
          memories: memoryEntries;
          total;
          hasMore;
        },
        error: null,
        status: 200,
      }
    } catch (error) {
      logger.error('Error in getProfileMemories', 'ProfileMemoryService', {
        error: error as Error);
        profileId;
        type;
        options )
      })
      return { data: null;
        error: 'Failed to retrieve profile memories',
        status: 500 }
    }
  }
  /**;
   * Add a memory entry for a profile with enhanced validation and error handling;
   * @param profileId Profile ID;
   * @param entry Memory entry to add;
   * @return s Promise with the created memory entry;
   */
  public async addProfileMemory(
    profileId: string,
    entry: Omit<MemoryEntry, 'userId' | 'timestamp'>
  ): Promise<ApiResponse<MemoryEntry>>
    try {
      logger.debug('Adding profile memory', 'ProfileMemoryService', { profileId, type: entry.type, title: entry.title })
      ;
      // Validate profileId;
      if (!profileId) {
        logger.warn('Invalid profileId parameter', 'ProfileMemoryService', { profileId })
        return { data: null;
          error: 'Invalid profile ID',
          status: 400 }
      }
      // Validate entry;
      if (!entry.type) {
        logger.warn('Missing required field: type', 'ProfileMemoryService', { entry })
        return { data: null;
          error: 'Missing required field: type',
          status: 400 }
      }
      if (!entry.title) {
        logger.warn('Missing required field: title', 'ProfileMemoryService', { entry })
        return { data: null;
          error: 'Missing required field: title',
          status: 400 }
      }
      if (!entry.content) {
        logger.warn('Missing required field: content', 'ProfileMemoryService', { entry })
        return { data: null;
          error: 'Missing required field: content',
          status: 400 }
      }
      // Verify valid memory type;
      const validTypes = ['decision', 'context', 'progress', 'pattern'];
      if (!validTypes.includes(entry.type)) {
        logger.warn('Invalid memory type', 'ProfileMemoryService', { type: entry.type })
        return {
          data: null;
          error: `Invalid memory type: ${entry.type}. Must be one of: ${validTypes.join(', ')}`,
          status: 400,
        }
      }
      // Performance monitoring;
      const startTime = performance.now()
      ;
      // Verify profile exists - direct database query for reliability;
      const { data: profile, error: profileError  } = await this.profileRepository.getClient()
        .from('user_profiles')
        .select('*')
        .eq('id', profileId)
        .maybeSingle()
      ;
      if (profileError || !profile) {
        logger.warn('Profile not found', 'ProfileMemoryService', { profileId, profileError })
        return { data: null;
          error: 'Profile not found',
          status: 404 }
      }
      // Prepare memory entry for repository;
      const memoryBankEntry: Omit<MemoryBankEntry, 'id' | 'created_at' | 'updated_at'> = {
        user_id: profileId;
        type: entry.type,
        title: entry.title.trim()
        content: entry.content.trim()
        timestamp: new Date().toISOString()
        tags: Array.isArray(entry.tags) ? entry.tags.map(tag => tag.trim()).filter(tag => tag.length > 0)   : []
      }
      // Use repository directly instead of memoryBankService;
      const result = await this.memoryRepository.createMemory(memoryBankEntry)
      
      const createTime = performance.now() - startTime;
      logger.debug('Added profile memory', 'ProfileMemoryService', {
        profileId;
        type: entry.type);
        id: result.id)
        createTime: `${createTime.toFixed(2)}ms`;
      })
      ;
      // Convert from MemoryBankEntry to MemoryEntry format;
      const memoryEntry: MemoryEntry = {
        id: result.id;
        userId: result.user_id,
        type: result.type as MemoryEntry['type'],
        title: result.title,
        content: result.content,
        timestamp: result.timestamp,
        tags: result.tags || []
      }
      return { data: memoryEntry;
        error: null,
        status: 201 // Created }
    } catch (error) {
      logger.error('Error in addProfileMemory', 'ProfileMemoryService', {
        error: error as Error);
        profileId;
        entry )
      })
      return { data: null;
        error: 'Failed to add profile memory',
        status: 500 }
    }
  }
  /**;
   * Get profile summary with key memories with enhanced validation and error handling;
   * @param profileId Profile ID;
   * @param options Optional parameters for filtering and limiting results;
   * @return s Promise with profile and key memories;
   */
  public async getProfileWithMemories(
    profileId: string,
    options: {
      limit?: number,
      includeTypes?: Array<'decision' | 'context' | 'progress' | 'pattern'>
    } = {}
  ): Promise<ApiResponse<{
    profile: Profile;
    decisions: MemoryEntry[],
    context: MemoryEntry[],
    progress: MemoryEntry[],
    pattern?: MemoryEntry[]
  }>>
    try {
      logger.debug('Getting profile with memories', 'ProfileMemoryService', { profileId, options })
      ;
      // Validate profileId;
      if (!profileId) {
        logger.warn('Invalid profileId parameter', 'ProfileMemoryService', { profileId })
        return { data: null;
          error: 'Invalid profile ID',
          status: 400 }
      }
      // Performance monitoring;
      const startTime = performance.now()
      ;
      // Sanitize options with defaults;
      const sanitizedOptions = { limit: options.limit && options.limit > 0 ? options.limit    : 5
        includeTypes: options.includeTypes || ['decision' 'context', 'progress'] }
      // Get profile directly from database for more reliable access;
      const { data: profile, error: profileError  } = await this.profileRepository.getClient()
        .from('user_profiles')
        .select('*')
        .eq('id', profileId)
        .maybeSingle()
      
      if (profileError || !profile) {
        logger.warn('Profile not found', 'ProfileMemoryService', { profileId, profileError })
        return { data: null;
          error: 'Profile not found',
          status: 404 }
      }
      // Prepare result object;
      const result: {
        profile: Profile,
        decisions: MemoryEntry[],
        context: MemoryEntry[],
        progress: MemoryEntry[],
        pattern?: MemoryEntry[]
      } = {
        profile;
        decisions: [],
        context: [],
        progress: []
      }
      // Get memories for each requested type directly from repository;
      const memoryPromises = sanitizedOptions.includeTypes.map(async type => {
  const memories = await this.memoryRepository.getByType(profileId, type)
        ;
        // Sort by timestamp (newest first) and limit results;
        const sortedMemories = memories.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
          .slice(0, sanitizedOptions.limit)
        ;
        // Convert from MemoryBankEntry to MemoryEntry format;
        const memoryEntries = sortedMemories.map(entry => ({
          id: entry.id;
          userId: entry.user_id);
          type: entry.type as MemoryEntry['type']),
          title: entry.title,
          content: entry.content,
          timestamp: entry.timestamp,
          tags: entry.tags || [])
        }))
        ;
        // Add to appropriate result array;
        if (type = == 'decision') {
          result.decisions = memoryEntries;
        } else if (type === 'context') {
          result.context = memoryEntries;
        } else if (type === 'progress') {
          result.progress = memoryEntries;
        } else if (type === 'pattern') {
          result.pattern = memoryEntries;
        }
      })
      ;
      // Wait for all memory fetches to complete;
      await Promise.all(memoryPromises)
      ;
      const queryTime = performance.now() - startTime;
      logger.debug('Retrieved profile with memories', 'ProfileMemoryService', {
        profileId;
        memoryTypes: sanitizedOptions.includeTypes)
        queryTime: `${queryTime.toFixed(2)}ms`;
      })
      ;
      return { data: result;
        error: null,
        status: 200 }
    } catch (error) {
      logger.error('Error in getProfileWithMemories', 'ProfileMemoryService', {
        error: error as Error);
        profileId;
        options )
      })
      return { data: null;
        error: 'Failed to retrieve profile with memories',
        status: 500 }
    }
  }
  /**;
   * Export profile memories to markdown with enhanced validation and error handling;
   * @param profileId Profile ID;
   * @param options Optional export options (filtering, sorting)
   * @return s Promise with export path;
   */
  public async exportProfileMemories(
    profileId: string,
    options: { types?: Array<'decision' | 'context' | 'progress' | 'pattern'>
      sortBy?: 'timestamp' | 'title' | 'type',
      sortOrder?: 'asc' | 'desc',
      fromDate?: string,
      toDate?: string } = {}
  ): Promise<ApiResponse<string>>
    try {
      logger.debug('Exporting profile memories', 'ProfileMemoryService', { profileId, options })
      ;
      // Validate profileId;
      if (!profileId) {
        logger.warn('Invalid profileId parameter', 'ProfileMemoryService', { profileId })
        return { data: null;
          error: 'Invalid profile ID',
          status: 400 }
      }
      // Performance monitoring;
      const startTime = performance.now()
      ;
      // Verify profile exists - direct database query for reliability;
      const { data: profile, error: profileError  } = await this.profileRepository.getClient()
        .from('user_profiles')
        .select('*')
        .eq('id', profileId)
        .maybeSingle()
      ;
      if (profileError || !profile) {
        logger.warn('Profile not found', 'ProfileMemoryService', { profileId, profileError })
        return { data: null;
          error: 'Profile not found',
          status: 404 }
      }
      // Sanitize options with defaults;
      const sanitizedOptions = { types: options.types || ['decision', 'context', 'progress', 'pattern'],
        sortBy: options.sortBy || 'timestamp',
        sortOrder: options.sortOrder || 'desc',
        fromDate: options.fromDate,
        toDate: options.toDate }
      // Get all memory entries for the profile using repository directly;
      const allMemories: MemoryBankEntry[] = [];
      // Get memories for each requested type;
      for (const type of sanitizedOptions.types) {
        const typeMemories = await this.memoryRepository.getByType(profileId, type)
        allMemories.push(...typeMemories)
      }
      // Apply date filtering if specified;
      let filteredMemories = allMemories;
      if (sanitizedOptions.fromDate || sanitizedOptions.toDate) {
        filteredMemories = allMemories.filter(entry => {
  const entryDate = new Date(entry.timestamp)
          ;
          if (sanitizedOptions.fromDate && sanitizedOptions.toDate) {
            return entryDate >= new Date(sanitizedOptions.fromDate) && ;
                   entryDate <= new Date(sanitizedOptions.toDate)
          } else if (sanitizedOptions.fromDate) {
            return entryDate >= new Date(sanitizedOptions.fromDate)
          } else if (sanitizedOptions.toDate) {
            return entryDate <= new Date(sanitizedOptions.toDate)
          }
          return true;
        })
      }
      // Apply sorting;
      filteredMemories.sort((a, b) => {
  const fieldA = a[sanitizedOptions.sortBy as keyof MemoryBankEntry];
        const fieldB = b[sanitizedOptions.sortBy as keyof MemoryBankEntry];
        ;
        // Handle undefined values safely;
        if (fieldA === undefined && fieldB === undefined) return 0;
        if (fieldA === undefined) return 1; // undefined values sort to the end;
        if (fieldB = == undefined) return -1;
        ;
        // Compare values;
        if (fieldA < fieldB) return sanitizedOptions.sortOrder = == 'asc' ? -1    : 1
        if (fieldA > fieldB) return sanitizedOptions.sortOrder === 'asc' ? 1  : -1
        return 0
      })
      
      // Convert to MemoryEntry format for the service;
      const memoryEntries = filteredMemories.map(entry => ({
        id: entry.id;
        userId: entry.user_id);
        type: entry.type as MemoryEntry['type']),
        title: entry.title,
        content: entry.content,
        timestamp: entry.timestamp,
        tags: entry.tags || [])
      }))
      ;
      // Temporarily override the entries in the service for export;
      // Note: In a real implementation, we would modify the memoryBankService to accept entries parameter;
      const originalGetEntries = memoryBankService.getEntries;
      memoryBankService.getEntries = async () => ({ data: memoryEntries, error: null, status: 200 })
      ;
      // Export memories using the service;
      const exportPath = await memoryBankService.exportToMarkdown()
      ;
      // Restore original method;
      memoryBankService.getEntries = originalGetEntries;
      ;
      const exportTime = performance.now() - startTime;
      logger.debug('Exported profile memories', 'ProfileMemoryService', {
        profileId;
        count: memoryEntries.length)
        exportTime: `${exportTime.toFixed(2)}ms`;
        exportPath;
      })
      ;
      return { data: exportPath;
        error: null,
        status: 200 }
    } catch (error) {
      logger.error('Error in exportProfileMemories', 'ProfileMemoryService', {
        error: error as Error);
        profileId;
        options )
      })
      return { data: null;
        error: 'Failed to export profile memories',
        status: 500 }
    }
  }
}

export const profileMemoryService = ProfileMemoryService.getInstance()