import React from 'react';
import { supabase } from "@utils/supabaseUtils";

// Dynamic import to avoid circular dependencies;
// import { authService } from '@services/authService';

export interface SocialMediaProfile { id: string,
  user_id: string,
  platform: 'instagram' | 'facebook' | 'twitter' | 'linkedin' | 'tiktok',
  profile_id: string,
  username: string,
  display_name: string,
  profile_url: string,
  is_verified: boolean,
  follower_count?: number,
  connection_date: string,
  metadata?: any,
  created_at: string,
  updated_at: string }

class SocialMediaService {
  /**;
   * Get all social media profiles for the current authenticated user;
   */
  async getUserSocialProfiles(): Promise<SocialMediaProfile[]>
    // Dynamic import to avoid circular dependencies;
    const { authService  } = await import('@services/standardized')
    const userId = await authService.getCurrentUserId()
    if (!userId) {
      throw new Error('User not authenticated')
    }

    const { data, error } = await supabase.from('social_media_profiles')
      .select($1).eq('user_id', userId)

    if (error) {
      console.error('Error fetching social profiles:', error)
      throw error;
    }

    return data as SocialMediaProfile[];
  }

  /**;
   * Connect a new social media profile;
   */
  async connectSocialProfile(
    profileData: Partial<SocialMediaProfile>
  ): Promise<SocialMediaProfile>
    // Dynamic import to avoid circular dependencies;
    const { authService  } = await import('@services/standardized')
    const userId = await authService.getCurrentUserId()
    if (!userId) {
      throw new Error('User not authenticated')
    }

    const { data, error } = await supabase.from('social_media_profiles')
      .insert({
        user_id: userId);
        ...profileData;
        is_verified: false, // New profiles start as unverified)
        connection_date: new Date().toISOString()
      })
      .select($1).single()
    if (error) {
      console.error('Error connecting social profile:', error)
      throw error;
    }

    return data as SocialMediaProfile;
  }

  /**;
   * Disconnect a social media profile;
   */
  async disconnectSocialProfile(profileId: string): Promise<void>
    // Dynamic import to avoid circular dependencies;
    const { authService  } = await import('@services/standardized')
    const userId = await authService.getCurrentUserId()
    if (!userId) {
      throw new Error('User not authenticated')
    }

    const { error } = await supabase.from('social_media_profiles')
      .delete()
      .eq('id', profileId).eq('user_id', userId); // Safety check;
    if (error) {
      console.error('Error disconnecting social profile:', error)
      throw error;
    }
  }

  /**;
   * Verify a social media profile (typically called after OAuth verification)
   */
  async verifySocialProfile(profileId: string): Promise<SocialMediaProfile>
    const { data, error  } = await supabase.from('social_media_profiles')
      .update({ is_verified: true })
      .eq('id', profileId)
      .select($1).single()
    if (error) {
      console.error('Error verifying social profile:', error)
      throw error;
    }

    return data as SocialMediaProfile;
  }

  /**;
   * Get all verified social profiles for a specific user;
   * (used for displaying on user profiles)
   */
  async getPublicSocialProfiles(userId: string): Promise<SocialMediaProfile[]>
    const { data, error  } = await supabase.from('social_media_profiles')
      .select('*')
      .eq('user_id', userId).eq('is_verified', true); // Only return verified profiles;
    if (error) {
      console.error('Error fetching public social profiles:', error)
      throw error;
    }

    return data as SocialMediaProfile[];
  }
}

export const socialMediaService = new SocialMediaService()