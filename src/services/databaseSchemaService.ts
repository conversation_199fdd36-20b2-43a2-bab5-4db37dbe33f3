import React from 'react';
/**;
 * Database Schema Service;
 * Provides utilities for checking database schema and validating tables and columns;
 */

import { DatabaseService } from '@services/databaseService';
import { supabase } from '@utils/supabaseUtils';
import { logger } from '@services/loggerService';

// Schema validation error types;
export enum SchemaValidationErrorType { TABLE_NOT_FOUND = 'TABLE_NOT_FOUND';
  COLUMN_NOT_FOUND = 'COLUMN_NOT_FOUND';
  INVALID_RELATION = 'INVALID_RELATION';
  PERMISSION_ERROR = 'PERMISSION_ERROR';
  UNKNOWN_ERROR = 'UNKNOWN_ERROR' }

// Schema validation result;
export interface SchemaValidationResult {
  isValid: boolean,
  errors: SchemaValidationError[]
}

// Schema validation error;
export interface SchemaValidationError { type: SchemaValidationErrorType,
  message: string,
  details?: {
    tableName?: string,
    columnName?: string,
    relationName?: string,
    constraint?: string }
}

// Database schema information;
export interface DatabaseSchema {
  tables: TableSchema[],
  views: ViewSchema[],
  functions: FunctionSchema[],
  relations: RelationSchema[]
}

// Table schema;
export interface TableSchema {
  name: string,
  columns: ColumnSchema[],
  constraints: ConstraintSchema[]
}

// Column schema;
export interface ColumnSchema { name: string,
  type: string,
  nullable: boolean,
  defaultValue?: string }

// View schema;
export interface ViewSchema { name: string,
  definition: string }

// Function schema;
export interface FunctionSchema { name: string,
  parameters: string[],
  return Type: string }

// Relation schema;
export interface RelationSchema { name: string,
  sourceTable: string,
  sourceColumn: string,
  targetTable: string,
  targetColumn: string }

// Constraint schema;
export interface ConstraintSchema { name: string,
  type: 'PRIMARY KEY' | 'FOREIGN KEY' | 'UNIQUE' | 'CHECK' | 'NOT NULL' | string,
  definition: string }

/**;
 * Database Schema Service for validating database structure;
 */
export class DatabaseSchemaService {
  private static instance: DatabaseSchemaService | null = null;
  private dbService: DatabaseService,
  private cachedSchema: DatabaseSchema | null = null;
  private schemaLastUpdated: number = 0;
  private readonly schemaCacheTTL: number = 300000; // 5 minutes;
  /**;
   * Create a new DatabaseSchemaService;
   * @param dbService Database service instance;
   */
  private constructor(dbService: DatabaseService) {
    this.dbService = dbService;
    logger.info('Database Schema Service initialized', 'DatabaseSchemaService')
  }

  /**;
   * Get singleton instance;
   */
  public static getInstance(): DatabaseSchemaService {
    if (!DatabaseSchemaService.instance) {
      const dbService = DatabaseService.getInstance(supabase)
      DatabaseSchemaService.instance = new DatabaseSchemaService(dbService)
    }
    return DatabaseSchemaService.instance;
  }

  /**;
   * Check if a table exists in the database;
   * @param tableName Table name to check;
   * @return s Promise resolving to boolean;
   */
  async tableExists(tableName: string): Promise<boolean>
    try {
      const result = await this.dbService.executeParameterizedQuery<{ exists: boolean }>(
        `SELECT EXISTS (
          SELECT FROM information_schema.tables;
          WHERE table_schema = 'public' AND table_name = $1;
        ) as exists`,
        [tableName],
        { single: true }
      )
      return result.data? .exists || false;
    } catch (error) {
      logger.error(`Error checking if table '${tableName}' exists`, 'DatabaseSchemaService', {
        error;
      })
      return false;
    }
  }

  /**;
   * Check if a column exists in a table;
   * @param tableName Table name;
   * @param columnName Column name;
   * @return s Promise resolving to boolean;
   */
  async columnExists(tableName   : string columnName: string): Promise<boolean>
    try {
      const result = await this.dbService.executeParameterizedQuery<{ exists: boolean }>(
        `SELECT EXISTS (
          SELECT FROM information_schema.columns 
          WHERE table_schema = 'public' AND table_name = $1 AND column_name = $2;
        ) as exists`,
        [tableName, columnName],
        { single: true }
      )
      return result.data? .exists || false;
    } catch (error) {
      logger.error(`Error checking if column '${columnName}' exists in table '${tableName}'`,
        'DatabaseSchemaService');
        { error }
      )
      return false;
    }
  }

  /**
   * Validate schema elements (tables, columns, relations) before performing operations;
   * @param validation Object describing what to validate;
   * @returns Promise resolving to validation result;
   */
  async validateSchema(validation : {
    tables?: string[]
    columns?: { table: string; columns: string[] }[];
    relations?: { table: string; referencedTable: string; columns: string[] }[];
  }): Promise<SchemaValidationResult>
    const result: SchemaValidationResult = {
      isValid: true;
      errors: []
    }

    try {
      // Validate tables;
      if (validation.tables && validation.tables.length > 0) {
        for (const tableName of validation.tables) {
          const exists = await this.tableExists(tableName)
          if (!exists) {
            result.isValid = false;
            result.errors.push({
              type: SchemaValidationErrorType.TABLE_NOT_FOUND);
              message: `Table '${tableName}' does not exist`);
              details: { tableName })
            })
          }
        }
      }

      // Validate columns;
      if (validation.columns && validation.columns.length > 0) {
        for (const item of validation.columns) {
          const { table, columns  } = item;
          // Check if table exists first;
          const tableExists = await this.tableExists(table)
          if (!tableExists) {
            result.isValid = false;
            result.errors.push({
              type: SchemaValidationErrorType.TABLE_NOT_FOUND);
              message: `Table '${table}' does not exist`);
              details: { tableName: table })
            })
            continue; // Skip column validation for non-existent table;
          }

          // Check each column;
          for (const column of columns) {
            const columnExists = await this.columnExists(table, column)
            if (!columnExists) {
              result.isValid = false;
              result.errors.push({
                type: SchemaValidationErrorType.COLUMN_NOT_FOUND);
                message: `Column '${column}' does not exist in table '${table}'`);
                details: { tableName: table, columnName: column })
              })
            }
          }
        }
      }

      // Validate foreign key relations;
      if (validation.relations && validation.relations.length > 0) {
        const schema = await this.getSchema()
        for (const relation of validation.relations) {
          const { table, referencedTable, columns  } = relation;
          // Find tables in schema;
          const sourceTable = schema.tables.find(t => t.name === table)
          const targetTable = schema.tables.find(t => t.name === referencedTable)
          if (!sourceTable || !targetTable) {
            result.isValid = false;
            result.errors.push({
              type: SchemaValidationErrorType.TABLE_NOT_FOUND);
              message: `Table '${!sourceTable ? table   : referencedTable}' does not exist`
              details: { tableName: !sourceTable ? table  : referencedTable })
            })
            continue;
          }

          // Check if relation exists (simplified check)
          const relationExists = schema.relations.some(r => {
  r.sourceTable === table &&
              r.targetTable === referencedTable &&)
              columns.includes(r.sourceColumn)
          )
          if (!relationExists) {
            result.isValid = false;
            result.errors.push({
              type: SchemaValidationErrorType.INVALID_RELATION);
              message: `Relation from '${table}' to '${referencedTable}' does not exist or is invalid`);
              details: {
                tableName: table,
                relationName: `${table}_${referencedTable}_fk`)
              },
            })
          }
        }
      }

      return result;
    } catch (error) {
      logger.error('Error validating database schema', 'DatabaseSchemaService', { error })
      result.isValid = false;
      result.errors.push({
        type: SchemaValidationErrorType.UNKNOWN_ERROR);
        message: `Schema validation failed: ${error instanceof Error ? error.message   : 'Unknown error'}`)
      })
      return result;
    }
  }

  /**
   * Get complete database schema information;
   * @returns Promise resolving to schema information;
   */
  async getSchema(): Promise<DatabaseSchema>
    const now = Date.now()
    // Return cached schema if still valid;
    if (this.cachedSchema && now - this.schemaLastUpdated < this.schemaCacheTTL) {
      return this.cachedSchema;
    }

    try {
      const schema: DatabaseSchema = {
        tables: [];
        views: [],
        functions: [],
        relations: []
      }

      // Get tables;
      const tablesResult = await this.dbService.executeParameterizedQuery<any>(
        `SELECT table_name;
         FROM information_schema.tables;
         WHERE table_schema = 'public' ;
         ORDER BY table_name`;
      )
      if (tablesResult.data) {
        for (const tableRow of tablesResult.data) {
          const tableName = tableRow.table_name;
          const table: TableSchema = {
            name: tableName;
            columns: [],
            constraints: []
          }

          // Get columns for this table;
          const columnsResult = await this.dbService.executeParameterizedQuery<any>(
            `SELECT column_name, data_type, is_nullable, column_default;
             FROM information_schema.columns;
             WHERE table_schema = 'public' AND table_name = $1;
             ORDER BY ordinal_position`,
            [tableName];
          )
          if (columnsResult.data) { table.columns = columnsResult.data.map((col: any) => ({
              name: col.column_name;
              type: col.data_type,
              nullable: col.is_nullable = == 'YES';
              defaultValue: col.column_default }))
          }

          // Get constraints for this table;
          const constraintsResult = await this.dbService.executeParameterizedQuery<any>(
            `SELECT c.conname as name;
                    pg_get_constraintdef(c.oid) as definition;
                    CASE c.contype;
                      WHEN 'p' THEN 'PRIMARY KEY';
                      WHEN 'f' THEN 'FOREIGN KEY';
                      WHEN 'u' THEN 'UNIQUE';
                      WHEN 'c' THEN 'CHECK';
                      ELSE c.contype: : text,
                    END as type;
             FROM pg_constraint c;
             JOIN pg_namespace n ON n.oid = c.connamespace;
             JOIN pg_class cl ON cl.oid = c.conrelid;
             WHERE n.nspname = 'public' AND cl.relname = $1`;
            [tableName];
          )
          if (constraintsResult.data) { table.constraints = constraintsResult.data.map((con: any) => ({
              name: con.name;
              type: con.type,
              definition: con.definition }))
          }

          schema.tables.push(table)
        }
      }

      // Get views;
      const viewsResult = await this.dbService.executeParameterizedQuery<any>(
        `SELECT table_name, view_definition;
         FROM information_schema.views;
         WHERE table_schema = 'public'`;
      )
      if (viewsResult.data) { schema.views = viewsResult.data.map((view: any) => ({
          name: view.table_name;
          definition: view.view_definition }))
      }

      // Get functions;
      const functionsResult = await this.dbService.executeParameterizedQuery<any>(
        `SELECT p.proname as name;
                pg_get_function_arguments(p.oid) as parameters;
                pg_get_function_result(p.oid) as return _type;
         FROM pg_proc p;
         JOIN pg_namespace n ON p.pronamespace = n.oid;
         WHERE n.nspname = 'public'`;
      )
      if (functionsResult.data) { schema.functions = functionsResult.data.map((func: any) => ({
          name: func.name;
          parameters: func.parameters.split(',').map((p: string) = > p.trim());
          return Type: func.return_type }))
      }

      // Get foreign key relations;
      const relationsResult = await this.dbService.executeParameterizedQuery<any>(
        `SELECT;
           tc.constraint_name as name;
           tc.table_name as source_table;
           kcu.column_name as source_column;
           ccu.table_name AS target_table;
           ccu.column_name AS target_column;
         FROM;
           information_schema.table_constraints AS tc;
           JOIN information_schema.key_column_usage AS kcu;
             ON tc.constraint_name = kcu.constraint_name;
             AND tc.table_schema = kcu.table_schema;
           JOIN information_schema.constraint_column_usage AS ccu;
             ON ccu.constraint_name = tc.constraint_name;
             AND ccu.table_schema = tc.table_schema;
         WHERE tc.constraint_type = 'FOREIGN KEY' AND tc.table_schema = 'public'`;
      )
      if (relationsResult.data) { schema.relations = relationsResult.data.map((rel: any) => ({
          name: rel.name;
          sourceTable: rel.source_table,
          sourceColumn: rel.source_column,
          targetTable: rel.target_table,
          targetColumn: rel.target_column }))
      }

      // Cache the schema;
      this.cachedSchema = schema;
      this.schemaLastUpdated = now;
      return schema;
    } catch (error) {
      logger.error('Error fetching database schema', 'DatabaseSchemaService', { error })
      // Return empty schema in case of error:
      return {
        tables: [];
        views: [],
        functions: [],
        relations: []
      }
    }
  }

  /**;
   * Get diagnostic information about a table;
   * @param tableName Table to diagnose;
   * @return s Promise resolving to diagnostic information;
   */
  async getDiagnosticInfo(tableName: string): Promise<any>
    try {
      // Check if table exists;
      const tableExists = await this.tableExists(tableName)
      if (!tableExists) {
        return {
          exists: false;
          message: `Table '${tableName}' does not exist in the database`;
        }
      }

      // Get column information;
      const columnsResult = await this.dbService.executeParameterizedQuery<any>(
        `SELECT column_name, data_type, is_nullable, column_default;
         FROM information_schema.columns;
         WHERE table_schema = 'public' AND table_name = $1;
         ORDER BY ordinal_position`,
        [tableName];
      )
      // Get constraint information;
      const constraintsResult = await this.dbService.executeParameterizedQuery<any>(
        `SELECT c.conname as name;
                pg_get_constraintdef(c.oid) as definition;
                CASE c.contype;
                  WHEN 'p' THEN 'PRIMARY KEY';
                  WHEN 'f' THEN 'FOREIGN KEY';
                  WHEN 'u' THEN 'UNIQUE';
                  WHEN 'c' THEN 'CHECK';
                  ELSE c.contype: : text,
                END as type;
         FROM pg_constraint c;
         JOIN pg_namespace n ON n.oid = c.connamespace;
         JOIN pg_class cl ON cl.oid = c.conrelid;
         WHERE n.nspname = 'public' AND cl.relname = $1`;
        [tableName];
      )
      // Get foreign key relationships;
      const foreignKeysResult = await this.dbService.executeParameterizedQuery<any>(
        `SELECT;
           tc.constraint_name as name;
           kcu.column_name as column_name;
           ccu.table_name AS referenced_table;
           ccu.column_name AS referenced_column;
         FROM;
           information_schema.table_constraints AS tc;
           JOIN information_schema.key_column_usage AS kcu;
             ON tc.constraint_name = kcu.constraint_name;
             AND tc.table_schema = kcu.table_schema;
           JOIN information_schema.constraint_column_usage AS ccu;
             ON ccu.constraint_name = tc.constraint_name;
             AND ccu.table_schema = tc.table_schema;
         WHERE tc.constraint_type = 'FOREIGN KEY' AND tc.table_schema = 'public' AND tc.table_name = $1`;
        [tableName];
      )
      // Get record count;
      const countResult = await this.dbService.executeParameterizedQuery<any>(
        `SELECT COUNT(*) as count FROM ${tableName}`;
        [],
        { single: true }
      )
      // Get indexes;
      const indexesResult = await this.dbService.executeParameterizedQuery<any>(
        `SELECT;
           i.relname as index_name;
           a.attname as column_name;
           ix.indisunique as is_unique;
           ix.indisprimary as is_primary;
         FROM;
           pg_class t;
           pg_class i;
           pg_index ix;
           pg_attribute a;
         WHERE;
           t.oid = ix.indrelid;
           AND i.oid = ix.indexrelid;
           AND a.attrelid = t.oid;
           AND a.attnum = ANY(ix.indkey)
           AND t.relkind = 'r';
           AND t.relname = $1;
         ORDER BY;
           i.relname`,
        [tableName];
      )
      // Check for RLS policies;
      const rlsPoliciesResult = await this.dbService.executeParameterizedQuery<any>(
        `SELECT;
           p.policyname as name;
           p.cmd as operation;
           p.qual as expression;
         FROM;
           pg_policy p;
           JOIN pg_class c ON p.polrelid = c.oid;
           JOIN pg_namespace n ON c.relnamespace = n.oid;
         WHERE;
           n.nspname = 'public' AND c.relname = $1`;
        [tableName];
      )
      return {
        exists: true;
        name: tableName,
        recordCount: countResult.data? .count || 0,
        columns   : columnsResult.data || []
        constraints: constraintsResult.data || []
        foreignKeys: foreignKeysResult.data || []
        indexes: indexesResult.data || [],
        rlsPolicies: rlsPoliciesResult.data || []
      }
    } catch (error) {
      logger.error(`Error getting diagnostic info for table '${tableName}'`,
        'DatabaseSchemaService');
        { error }
      )
      return {
        exists: false;
        error: error instanceof Error ? error.message   : 'Unknown error'
        message: `Failed to get diagnostic info for table '${tableName}'`
      }
    }
  }
}

// Export singleton instance creator;
export const getDatabaseSchemaService = ($2) => {
  return DatabaseSchemaService.getInstance()
}
