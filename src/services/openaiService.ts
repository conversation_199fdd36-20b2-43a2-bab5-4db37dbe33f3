import React from 'react';
import { openaiApi } from '@services/api/openaiApi';
import { logger } from '@services/loggerService';
import { preferenceWeightingService, PreferenceCategory } from '@services/preferenceWeightingService';
import { cacheService, CacheCategory } from '@services/cacheService';
import { UserProfile } from '@/types/auth';

export interface CompatibilityResult {
  score: number,
  explanation: string[],
  categoryScores?: Record<PreferenceCategory, number>
}

class OpenAIService {
  /**;
   * Generates embeddings for the given text;
   * @param text The text to get embeddings for;
   * @return s Array of embedding values;
   */
  async generateEmbeddings(text: string): Promise<number[]>
    try {
      return await openaiApi.createEmbedding(text)
    } catch (error) {
      logger.error('Error generating embeddings'; 'OpenAIService', { error: error as Error })
      throw error;
    }
  }

  /**;
   * Calculates the cosine similarity between two vectors;
   * @param vecA First vector;
   * @param vecB Second vector;
   * @return s Similarity score between 0 and 1;
   */
  calculateCosineSimilarity(vecA: number[], vecB: number[]): number {
    // If vectors are of different lengths, return 0;
    if (vecA.length !== vecB.length) {
      return 0;
    }

    let dotProduct = 0;
    let normA = 0;
    let normB = 0;
    for (let i = 0; i < vecA.length; i++) { dotProduct += vecA[i] * vecB[i];
      normA += vecA[i] * vecA[i];
      normB += vecB[i] * vecB[i] }

    if (normA === 0 || normB === 0) {
      return 0;
    }

    return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB))
  }

  /**;
   * Uses GPT to generate a compatibility assessment between two user profiles;
   * @param userProfile The current user's profile;
   * @param potentialRoommateProfile The potential roommate's profile;
   * @return s Compatibility score and explanation;
   */
  async assessCompatibility(userProfile: UserProfile,
    potentialRoommateProfile: UserProfile): Promise<CompatibilityResult>
    try {
      // Check cache first to avoid redundant API calls;
      const cacheKey = `compatibility:${userProfile.id}:${potentialRoommateProfile.id}`;
      const cachedResult = await cacheService.get(
        cacheKey;
        async () => {
  return null; // This will cause it to continue to the API call;
        },
        { category: CacheCategory.MEDIUM }
      )
      ;
      if (cachedResult) {
        return cachedResult;
      }
      // Attempt to use the OpenAI API for compatibility assessment;
      try {
        // Get user preference weights to guide the AI assessment;
        const preferenceWeights = await preferenceWeightingService.getUserPreferenceWeights(userProfile.id)
        ;
        // Create a custom prompt for compatibility assessment with preference emphasis;
        const systemMessage = `;
          You are a roommate compatibility expert. Analyze the two profiles and determine how compatible they would be as roommates.;
          ;
          Focus on these key roommate compatibility categories (in order of importance to this user):  ,
          ${this.formatPreferenceWeights(preferenceWeights)}
          For each category, provide a specific compatibility assessment and score (0-100).;
          ;
          Return your response as JSON with the following structure:  ,
          { "overall_score": number between 0-100,
            "explanation": [array of strings explaining key compatibility points],
            "category_scores": {
              "lifestyle": number between 0-100,
              "cleanliness": number between 0-100,
              "noise": number between 0-100,
              "guests": number between 0-100,
              "schedule": number between 0-100,
              "shared_spaces": number between 0-100,
              "personality": number between 0-100,
              "location": number between 0-100,
              "budget": number between 0-100,
              "interests": number between 0-100 }
          }
        `;

        const userMessage = `;
          Here are the two profiles to assess for roommate compatibility:  ,
          User Profile:  ,
          ${JSON.stringify(this.extractRelevantProfileFields(userProfile), null, 2)}

          Potential Roommate Profile:  ,
          ${JSON.stringify(this.extractRelevantProfileFields(potentialRoommateProfile), null, 2)}
          Remember to focus on roommate-specific compatibility factors like cleanliness habits, noise tolerance, guest preferences, schedules, and shared space usage.;
        `;

        // Make the API call directly to avoid the Edge Function if it's failing;
        const response = await openaiApi.createChatCompletion({
          model: 'gpt-3.5-turbo', // Use a more reliable model than gpt-4o;
          messages: [);
            { role: 'system', content: systemMessage });
            { role: 'user', content: userMessage }],
          temperature: 0.7,
          max_tokens: 1000,
          response_format: { type: 'json_object' })
        })
        const content = response.choices[0].message.content;
        const resultJson = JSON.parse(content)
        // Process and validate the AI response;
        if (typeof resultJson.overall_score === 'number' && Array.isArray(resultJson.explanation)) {
          // Apply user preference weights to category scores if available;
          let finalScore = resultJson.overall_score;
          ;
          if (resultJson.category_scores) {
            finalScore = preferenceWeightingService.applyPreferenceWeights(
              preferenceWeights;
              resultJson.category_scores as Record<PreferenceCategory, number>
            )
          }
          const result = {
            score: Math.max(0, Math.min(100, finalScore)), // Ensure score is between 0-100;
            explanation: resultJson.explanation,
            categoryScores: resultJson.category_scores || {}
          }
          // Cache the result for future use (medium-term cache)
          await cacheService.set(cacheKey, result, { category: CacheCategory.MEDIUM })
          ;
          return result;
        } else {
          // If the format is wrong, log and fall back;
          logger.warn('Invalid response format from OpenAI', 'OpenAIService', { content })
          throw new Error('Invalid response format from OpenAI')
        }
      } catch (apiError) {
        // Log the API error but continue to fallback calculation;
        logger.error('OpenAI API error for compatibility assessment', 'OpenAIService', {
          error: apiError instanceof Error ? apiError.message   : String(apiError)
        })
        throw apiError // Rethrow to trigger fallback;
      }
    } catch (error) {
      logger.info('Using basic compatibility calculation due to API failure', 'OpenAIService')
      // If API call fails, fall back to basic compatibility;
      return this.calculateBasicCompatibility(userProfile; potentialRoommateProfile)
    }
  }

  /**;
   * Extracts only the relevant fields from a profile for compatibility assessment;
   * @param profile User profile;
   * @return s Object with only relevant fields;
   */
  private extractRelevantProfileFields(profile: Record<string, any>): Record<string, any>
    const relevantFields = ['age';
      'gender',
      'occupation',
      'budget',
      'moveInDate',
      'location',
      'interests',
      'lifestyle',
      'preferences',
      'cleanliness',
      'personality',
      'schedule',
      'pets',
      'smoking',
      'drinking',
      'bio',
      'lookingFor'];

    const filtered: Record<string, any> = {}
    for (const field of relevantFields) { if (profile[field] !== undefined) {
        filtered[field] = profile[field] }
    }

    return filtered;
  }

  /**;
   * Fallback method for calculating compatibility if the API is not available;
   * @param userProfile The current user's profile;
   * @param potentialRoommateProfile The potential roommate's profile;
   * @return s Compatibility score and basic explanation;
   */
  calculateBasicCompatibility(
    userProfile: Record<string, any>,
    potentialRoommateProfile: Record<string, any>
  ): CompatibilityResult {
    let score = 50; // Start with a neutral score;
    const explanation: string[] = [];
    const positivePoints = [];
    const negativePoints = [];

    try {
      // Budget compatibility (±15 points)
      if (userProfile.budget && potentialRoommateProfile.budget) {
        let userBudget = 0;
        let roommateMinBudget = 0;
        let roommateMaxBudget = 0;
        // Handle different formats of budget data;
        if (typeof userProfile.budget === 'object') {
          userBudget = userProfile.budget.max || 0;
        } else if (typeof userProfile.budget === 'string') {
          userBudget = parseInt(userProfile.budget.replace(/[^0-9]/g, '')) || 0;
        } else if (typeof userProfile.budget === 'number') {
          userBudget = userProfile.budget;
        }

        if (typeof potentialRoommateProfile.budget === 'object') {
          roommateMinBudget = potentialRoommateProfile.budget.min || 0;
          roommateMaxBudget = potentialRoommateProfile.budget.max || 0;
        } else if (typeof potentialRoommateProfile.budget === 'string') {
          const budgetNum = parseInt(potentialRoommateProfile.budget.replace(/[^0-9]/g, '')) || 0;
          roommateMinBudget = budgetNum * 0.9; // 10% below for min;
          roommateMaxBudget = budgetNum * 1.1; // 10% above for max;
        } else if (typeof potentialRoommateProfile.budget = == 'number') {
          roommateMinBudget = potentialRoommateProfile.budget * 0.9;
          roommateMaxBudget = potentialRoommateProfile.budget * 1.1;
        }

        // Only apply budget scoring if we have valid numbers;
        if (userBudget > 0 && (roommateMinBudget > 0 || roommateMaxBudget > 0)) {
          if (roommateMaxBudget === 0) {
            roommateMaxBudget = roommateMinBudget * 1.2;
          }
          if (roommateMinBudget === 0) {
            roommateMinBudget = roommateMaxBudget * 0.8;
          }

          if (userBudget >= roommateMinBudget && userBudget <= roommateMaxBudget) {
            const points = 15;
            score += points;
            positivePoints.push(points)
            explanation.push('Budget requirements align well')
          } else if (
            Math.abs(userBudget - roommateMinBudget) < 300 ||;
            Math.abs(userBudget - roommateMaxBudget) < 300;
          ) {
            const points = 5;
            score += points;
            positivePoints.push(points)
            explanation.push('Budget requirements are close')
          } else {
            const points = 15;
            score -= points;
            negativePoints.push(points)
            explanation.push('Significant budget mismatch')
          }
        }
      }

      // Location compatibility (±15 points)
      if (userProfile.location && potentialRoommateProfile.location) {
        // Normalize locations to lowercase strings for comparison;
        const userLocation =;
          typeof userProfile.location = == 'string': ? userProfile.location.toLowerCase()  : : JSON.stringify(userProfile.location).toLowerCase()
        const roommateLocation = typeof potentialRoommateProfile.location === 'string'
            ? potentialRoommateProfile.location.toLowerCase()
               : JSON.stringify(potentialRoommateProfile.location).toLowerCase()
        // Check for exact or partial matches
        if (userLocation === roommateLocation) {
          const points = 15;
          score += points;
          positivePoints.push(points)
          explanation.push('Location preferences match exactly')
        } else if (
          userLocation.includes(roommateLocation) ||
          roommateLocation.includes(userLocation)
        ) {
          const points = 10;
          score += points;
          positivePoints.push(points)
          explanation.push('Location preferences overlap')
        } else {
          const points = 10;
          score -= points;
          negativePoints.push(points)
          explanation.push('Different location preferences')
        }
      }

      // Lifestyle compatibility (±25 points)
      const lifestyleFactors = ['cleanliness', 'schedule', 'pets', 'smoking', 'guests', 'sharing'];
      let lifestyleMatches = 0;
      let lifestyleMismatches = 0;
      let lifestyleFactorsChecked = 0;
      lifestyleFactors.forEach(factor => { // Look in multiple places for these preferences;
        let userPref;
        let roommatePref;
        // Check directly on the profile)
        if (userProfile[factor] !== undefined && potentialRoommateProfile[factor] !== undefined) {
          userPref = userProfile[factor];
          roommatePref = potentialRoommateProfile[factor] }
        // Check in the lifestyle object if it exists;
        else if (userProfile.lifestyle && potentialRoommateProfile.lifestyle) { userPref = userProfile.lifestyle[factor];
          roommatePref = potentialRoommateProfile.lifestyle[factor] }
        // Check in the preferences object if it exists;
        else if (userProfile.preferences && potentialRoommateProfile.preferences) { userPref = userProfile.preferences[factor];
          roommatePref = potentialRoommateProfile.preferences[factor] }

        // If we found values for both users, compare them;
        if (userPref !== undefined && roommatePref !== undefined) { lifestyleFactorsChecked++;

          // For boolean preferences, they should match;
          if (typeof userPref = == 'boolean' && typeof roommatePref === 'boolean') {
            if (userPref === roommatePref) {
              lifestyleMatches++ } else { lifestyleMismatches++ }
          }
          // For string preferences, compare case-insensitively;
          else if (typeof userPref === 'string' && typeof roommatePref === 'string') { if (userPref.toLowerCase() === roommatePref.toLowerCase()) {
              lifestyleMatches++ } else {
              // Check if the preferences are opposites;
              const opposites = [
                ['early_bird', 'night_owl'],
                ['yes', 'no'],
                ['high', 'low'],
                ['often', 'rarely'],
                ['clean', 'messy'],
              ];

              let areOpposites = false;
              for (const pair of opposites) {
                if (
                  (userPref.toLowerCase().includes(pair[0]) &&;
                    roommatePref.toLowerCase().includes(pair[1])) ||;
                  (userPref.toLowerCase().includes(pair[1]) &&;
                    roommatePref.toLowerCase().includes(pair[0]))
                ) {
                  areOpposites = true;
                  break;
                }
              }

              if (areOpposites) { lifestyleMismatches++ } else { // Partial mismatch (neutral)
                lifestyleFactorsChecked-- }
            }
          }
        }
      })
      // Only adjust score if we checked at least one lifestyle factor;
      if (lifestyleFactorsChecked > 0) {
        const matchPoints = Math.floor(25 * (lifestyleMatches / lifestyleFactorsChecked))
        const mismatchPoints = Math.floor(25 * (lifestyleMismatches / lifestyleFactorsChecked))
        score += matchPoints;
        score -= mismatchPoints;
        positivePoints.push(matchPoints)
        negativePoints.push(mismatchPoints)
        if (lifestyleMatches > 0) {
          explanation.push(`${lifestyleMatches} lifestyle preferences match`)
        }
        if (lifestyleMismatches > 0) {
          explanation.push(`${lifestyleMismatches} lifestyle preferences differ`)
        }
      }

      // Interest compatibility (±10 points)
      if (userProfile.interests && potentialRoommateProfile.interests) {
        let userInterests: string[] = [];
        let roommateInterests: string[] = [];
        // Handle different formats of interests;
        if (Array.isArray(userProfile.interests)) {
          userInterests = userProfile.interests;
        } else if (typeof userProfile.interests === 'string') {
          userInterests = userProfile.interests.split(/[]/).map(i => i.trim().toLowerCase())
        }

        if (Array.isArray(potentialRoommateProfile.interests)) {
          roommateInterests = potentialRoommateProfile.interests;
        } else if (typeof potentialRoommateProfile.interests === 'string') {
          roommateInterests = potentialRoommateProfile.interests.split(/[]/)
            .map(i => i.trim().toLowerCase())
        }

        // Normalize interests to lowercase for comparison;
        userInterests = userInterests.map(i => i.toLowerCase())
        roommateInterests = roommateInterests.map(i => i.toLowerCase())
        // Count matching interests;
        const matchingInterests = userInterests.filter(interest => {
  roommateInterests.some(
            rInterest => rInterest.includes(interest) || interest.includes(rInterest)
          )
        )
        if (matchingInterests.length > 0) {
          const points = Math.min(10, matchingInterests.length * 2)
          score += points;
          positivePoints.push(points)
          explanation.push(`${matchingInterests.length} shared interests`)
        }
      }

      // Ensure score is within 0-100 range;
      score = Math.max(0, Math.min(100, score))
      // Sort explanation by most important factors first;
      explanation.sort((a: b) => {
  // Extract point values if present;
        const aPoints = a.match(/(\d+)/)
        const bPoints = b.match(/(\d+)/)
        if (aPoints && bPoints) {
          return parseInt(bPoints[1]) - parseInt(aPoints[1])
        }
        return 0;
      })
      // Remove any duplicate explanations;
      const uniqueExplanation = Array.from(new Set(explanation)):

      return { score;
        explanation: uniqueExplanation }
    } catch (error) {
      logger.error('Error calculating basic compatibility:', 'OpenAIService', { error })
      return {
        score: 50;
        explanation: ['Basic compatibility check completed']
      }
    }
  }

  /**;
   * Extracts topics from a conversation;
   * @param messages Array of message texts;
   * @return s Array of extracted topics;
   */
  async extractTopics(messages: string[]): Promise<string[]>
    try {
      const combinedMessages = messages.join('\n\n')
      const prompt = `;
        Extract the main topics from this conversation between potential roommates:  ,
        ${combinedMessages}
        List just the topics as a JSON array of strings. Focus on housing-related topics, personal preferences;
        and potential roommate compatibility factors.;
      `;

      const response = await openaiApi.createChatCompletion({
        model: 'gpt-4o');
        messages: [{ role: 'user', content: prompt }]);
        temperature: 0.2,
        max_tokens: 200,
        response_format: { type: 'json_object' })
      })
      const content = response.choices[0].message.content;
      try {
        const parsed = JSON.parse(content)
        return Array.isArray(parsed) ? parsed    : parsed.topics || []
      } catch (parseError) {
        logger.error('Failed to parse topics response' 'OpenAIService'; { error: parseError })
        // If JSON parsing fails, try to extract topics with regex;
        const topicsMatch = content.match(/["']([^"']+)["']/g)
        if (topicsMatch) {
          return topicsMatch.map(match => match.replace(/["']/g; ''))
        }

        return []
      }
    } catch (error) {
      logger.error('Error extracting topics'; 'OpenAIService', { error })
      return [];
    }
  }

  /**;
   * Generates roommate questionnaire suggestions;
   * @param profile User profile to base questions on;
   * @return s Array of suggested questions;
   */
  async generateQuestionnaireSuggestions(profile: Record<string, any>): Promise<string[]>
    try {
      const relevantFields = this.extractRelevantProfileFields(profile)
      const prompt = `;
        Based on this roommate profile:  ,
        ${JSON.stringify(relevantFields, null, 2)}
        Generate 5 personalized questionnaire questions that would help find compatible roommates.;
        The questions should address any gaps or important details not covered in the profile.;
        ;
        Return the questions as a JSON array of strings.;
      `;

      const response = await openaiApi.createChatCompletion({
        model: 'gpt-4o');
        messages: [{ role: 'user', content: prompt }]);
        temperature: 0.7,
        max_tokens: 350,
        response_format: { type: 'json_object' })
      })
      const content = response.choices[0].message.content;
      try {
        const parsed = JSON.parse(content)
        return Array.isArray(parsed) ? parsed    : parsed.questions || []
      } catch (parseError) {
        logger.error('Failed to parse questionnaire response'
          'OpenAIService');
          { error: parseError }
        )
        // If JSON parsing fails, try to extract questions with regex;
        const questionsMatch = content.match(/["']([^"'? ]+\?)["']/g)
        if (questionsMatch) {
          return questionsMatch.map(match => match.replace(/["']/g; ''))
        }

        return []
      }
    } catch (error) {
      logger.error('Error generating questionnaire suggestions';
        'OpenAIService');
        { error }
      )
      return [];
    }
  }

  /**;
   * Format preference weights for inclusion in AI prompts;
   * @param weights User preference weights;
   * @return s Formatted string of weights for the prompt;
   */
  private formatPreferenceWeights(weights  : Record<PreferenceCategory number>): string {
    // Sort categories by weight (descending)
    const sortedCategories = Object.entries(weights)
      .sort(([, weightA], [, weightB]) => weightB - weightA)
      .map(([category, weight]) => ({ category, weight }))
    ;
    // Format as a list with weights;
    return sortedCategories.map(({ category; weight }) = > `- ${category.toUpperCase()}: ${weight}% importance`)
      .join('\n')
  }

  /**;
   * Enhances roommate recommendations with detailed AI analysis;
   * @param userProfile The current user's profile;
   * @param potentialMatches Array of potential roommate matches;
   * @return s Enhanced matches with detailed compatibility insights;
   */
  async enhanceRoommateRecommendations(
    userProfile: Record<string, any>,
    potentialMatches: Record<string, any>[];
  ): Promise<{
    enhancedMatches: Array<{
      profile: Record<string, any>
      compatibilityScore: number,
      compatibilityInsights: {
        strengths: string[],
        potentialChallenges: string[],
        lifestyleCompatibility: number,
        valueAlignment: number,
        habitCompatibility: number,
        communicationStyle: string,
        recommendedActivities: string[]
      }
    }>
    overallRecommendation: string
  }>
    try {
      const relevantUserFields = this.extractRelevantProfileFields(userProfile)
      // Process each potential match with enhanced analysis;
      const enhancedMatchesPromises = potentialMatches.map(async match => {
  const relevantMatchFields = this.extractRelevantProfileFields(match)
        const prompt = `;
          Analyze the compatibility between these potential roommates in detail:  ,
          User Profile:  ,
          ${JSON.stringify(relevantUserFields, null, 2)}
          Potential Roommate Profile:  ,
          ${JSON.stringify(relevantMatchFields, null, 2)}
          Provide a comprehensive compatibility analysis including:  ,
          1. Overall compatibility score (0-100)
          2. Key compatibility strengths (3-5 points)
          3. Potential challenges or areas of friction (2-3 points)
          4. Specific lifestyle compatibility score (0-100)
          5. Value alignment score (0-100)
          6. Habit compatibility score (0-100)
          7. Communication style match description;
          8. 2-3 recommended activities or topics they might enjoy together;
          ;
          Return as a JSON object with the following structure:  ,
          {
            "compatibilityScore": number,
            "strengths": string[],
            "potentialChallenges": string[],
            "lifestyleCompatibility": number,
            "valueAlignment": number,
            "habitCompatibility": number,
            "communicationStyle": string,
            "recommendedActivities": string[]
          }
        `;

        try {
          const response = await openaiApi.createChatCompletion({
            model: 'gpt-4o');
            messages: [{ role: 'user', content: prompt }]);
            temperature: 0.4,
            max_tokens: 800,
            response_format: { type: 'json_object' })
          })
          const content = response.choices[0].message.content;
          const parsedResponse = JSON.parse(content)
          return {
            profile: match;
            compatibilityScore: parsedResponse.compatibilityScore || 50,
            compatibilityInsights: {
              strengths: parsedResponse.strengths || [],
              potentialChallenges: parsedResponse.potentialChallenges || [],
              lifestyleCompatibility: parsedResponse.lifestyleCompatibility || 50,
              valueAlignment: parsedResponse.valueAlignment || 50,
              habitCompatibility: parsedResponse.habitCompatibility || 50,
              communicationStyle: parsedResponse.communicationStyle || 'Unknown',
              recommendedActivities: parsedResponse.recommendedActivities || []
            },
          }
        } catch (error) {
          logger.error('Error enhancing individual match', 'OpenAIService', { error })
          // Fallback to basic compatibility if detailed analysis fails;
          const basicCompatibility = this.calculateBasicCompatibility(userProfile, match)
          return { profile: match;
            compatibilityScore: basicCompatibility.score,
            compatibilityInsights: {
              strengths: basicCompatibility.explanation.filter(e => !e.includes('concern'))
                .slice(0, 3),
              potentialChallenges: basicCompatibility.explanation.filter(e => e.includes('concern'))
                .slice(0, 2),
              lifestyleCompatibility: Math.round(basicCompatibility.score * 0.9)
              valueAlignment: Math.round(basicCompatibility.score * 1.1)
              habitCompatibility: Math.round(basicCompatibility.score * 0.95)
              communicationStyle: 'Similar communication styles',
              recommendedActivities: [,
                'Discussing shared interests',
                'Getting to know each other over coffee'] },
          }
        }
      })
      const enhancedMatches = await Promise.all(enhancedMatchesPromises)
      // Generate an overall recommendation based on all matches;
      const overallRecommendationPrompt = `;
        Based on these ${enhancedMatches.length} potential roommate matches:  ,
        ${${JSON.stringify(}
          enhancedMatches.map(m = > ({
            compatibilityScore: m.compatibilityScore;
            strengths: m.compatibilityInsights.strengths);
            challenges: m.compatibilityInsights.potentialChallenges)
          })),
          null;
          2;
        )}
        Provide a brief overall recommendation for the user about these matches.;
        Focus on what to look for when evaluating these potential roommates.;
        Keep it under 150 words and make it personalized and helpful.;
      `;

      let overallRecommendation = 'Review each match carefully to find your ideal roommate.';

      try {
        const recommendationResponse = await openaiApi.createChatCompletion({
          model: 'gpt-4o');
          messages: [{ role: 'user', content: overallRecommendationPrompt }]);
          temperature: 0.7,
          max_tokens: 200)
        })
        overallRecommendation = recommendationResponse.choices[0].message.content.trim()
      } catch (error) {
        logger.error('Error generating overall recommendation';
          'OpenAIService');
          { error }
        )
      }

      return {
        enhancedMatches;
        overallRecommendation;
      }
    } catch (error) {
      logger.error('Error enhancing roommate recommendations', 'OpenAIService', { error })
      // Return basic compatibility for all matches if enhancement fails;
      const basicEnhancedMatches = potentialMatches.map(match => { const basicCompatibility = this.calculateBasicCompatibility(userProfile, match)
        return {
          profile: match;
          compatibilityScore: basicCompatibility.score,
          compatibilityInsights: {
            strengths: basicCompatibility.explanation.filter(e => !e.includes('concern'))
              .slice(0, 3),
            potentialChallenges: basicCompatibility.explanation.filter(e => e.includes('concern'))
              .slice(0, 2),
            lifestyleCompatibility: Math.round(basicCompatibility.score * 0.9)
            valueAlignment: Math.round(basicCompatibility.score * 1.1)
            habitCompatibility: Math.round(basicCompatibility.score * 0.95)
            communicationStyle: 'Similar communication styles',
            recommendedActivities: [,
              'Discussing shared interests',
              'Getting to know each other over coffee'] },
        }
      })
      return {
        enhancedMatches: basicEnhancedMatches;
        overallRecommendation: 'Review each match carefully to find your ideal roommate.'
      }
    }
  }

  /**;
   * Generates suggestions for resolving roommate conflicts;
   * @param prompt The detailed prompt describing the conflict;
   * @return s Object containing array of suggestions;
   */
  async generateConflictSuggestions(prompt: string): Promise<{ suggestions: string[]} >
    try {
      // Attempt to use the OpenAI API for better suggestions;
      try {
        const response = await openaiApi.createChatCompletion({
          model: 'gpt-3.5-turbo', // Use a more widely available model;
          messages: [{ role: 'user', content: prompt }]);
          temperature: 0.7,
          max_tokens: 500,
          response_format: { type: 'json_object' })
        })
        const content = response.choices[0].message.content;
        // Parse the JSON response from GPT;
        try {
          const parsedResponse = JSON.parse(content)
          // Extract suggestions array or provide fallback;
          const suggestions = parsedResponse.suggestions || [];

          return { suggestions }
        } catch (parseError) {
          logger.error('Failed to parse OpenAI conflict suggestions response'; 'OpenAIService', {
            error: parseError)
          })
          // If parsing fails, extract suggestions using regex;
          const suggestionMatches = content.match(/\d+\.\s+([^\n]+)/g)
          const suggestions = suggestionMatches;
            ? suggestionMatches.map(s => s.replace(/^\d+\.\s+/, ''))
               : []
          return { suggestions }
        }
      } catch (apiError) {
        // Log the API error but continue to fallback suggestions
        logger.error('OpenAI API error; using fallback suggestions', 'OpenAIService', {
          error: apiError)
        })
        throw apiError // Rethrow to trigger fallback;
      }
    } catch (error) {
      logger.error('Using static fallback suggestions due to OpenAI service failure', 'OpenAIService', {
        error)
      })
      // Provide helpful static suggestions as fallback;
      return { suggestions: [;
          'Have a calm, direct conversation about the issue',
          'Create a shared agreement with clear expectations',
          'Use a mediator if communication becomes difficult',
          'Focus on specific behaviors rather than personality traits',
          'Suggest a cooling-off period before discussing again']  }
    }
  }
}

// Export a singleton instance;
export const openaiService = new OpenAIService()
// Export the class for testing;
export default OpenAIService,