import React from 'react';
/**;
 * Notification Service;
 * ;
 * Provides a centralized service for managing app notifications;
 * including push notifications, in-app notifications, and alerts.;
 */

import { registerForPushNotifications, savePushToken, sendPushNotification, getUserNotifications, markNotificationAsRead, markAllNotificationsAsRead, deleteNotification, getNotificationSettings, updateNotificationSettings, NotificationPayload, Notification, NotificationSettings, AlertType, AlertSeverity, AlertDetails, sendAdminAlert, getAdminAlerts, resolveAdminAlert } from '@utils/notificationUtils';
import { logger } from '@utils/logger';
import { supabase } from '@lib/supabase';

export enum NotificationType { BOOKING_CREATED = 'booking_created';
  BOOKING_CONFIRMED = 'booking_confirmed';
  BOOKING_CANCELLED = 'booking_cancelled';
  BOOKING_RESCHEDULED = 'booking_rescheduled';
  BOOKING_COMPLETED = 'booking_completed';
  PAYMENT_REQUIRED = 'payment_required';
  PAYMENT_SUCCESS = 'payment_success';
  PAYMENT_FAILED = 'payment_failed';
  PROVIDER_MESSAGE = 'provider_message';
  REMINDER_24H = 'reminder_24h';
  REMINDER_1H = 'reminder_1h' }

export enum NotificationChannel { PUSH = 'push';
  EMAIL = 'email';
  SMS = 'sms';
  IN_APP = 'in_app' }

interface NotificationData { userId: string;
  type: NotificationType,
  title: string,
  message: string,
  data?: Record<string, any>
  channels?: NotificationChannel[],
  scheduledFor?: Date }

interface NotificationPreferences { userId: string,
  bookingUpdates: boolean,
  paymentAlerts: boolean,
  reminders: boolean,
  promotions: boolean,
  pushEnabled: boolean,
  emailEnabled: boolean,
  smsEnabled: boolean }

export class NotificationService {
  private static instance: NotificationService,
  private pushToken: string | null = null;
  private isInitialized = false;
  private constructor() {
    logger.info('Notification Service initialized', 'NotificationService')
  }

  /**;
   * Get singleton instance of the notification service;
   * @return s NotificationService instance;
   */
  public static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService()
    }
    return NotificationService.instance;
  }

  /**;
   * Initialize the notification service;
   * @param userId The current user ID;
   */
  public async initialize(userId: string): Promise<void>
    if (this.isInitialized) {
      return null;
    }

    try {
      // Register for push notifications;
      this.pushToken = await registerForPushNotifications()
      ;
      // Save the token to the database if we got one;
      if (this.pushToken && userId) {
        await savePushToken(userId, this.pushToken)
      }
      this.isInitialized = true;
      logger.info('Notification service initialized', 'NotificationService.initialize', {
        userId;
        hasToken: !!this.pushToken )
      })
    } catch (error) {
      logger.error('Failed to initialize notification service',
        'NotificationService.initialize');
        {
          userId;
          error: error instanceof Error ? error.message   : String(error)
        }
      )
    }
  }

  /**
   * Send a notification to a user;
   * @param userId The user ID to send the notification to;
   * @param titleOrPayload The notification title or a notification payload object;
   * @param body The notification body (optional if titleOrPayload is a payload object)
   * @param data Additional data to include with the notification;
   * @return s Boolean indicating if the notification was sent successfully;
   */
  public async sendNotification(
    userId: string,
    titleOrPayload: string | NotificationPayload,
    body?: string,
    data?: Record<string, any>
  ): Promise<boolean>
    try {
      // Handle both calling patterns;
      if (typeof titleOrPayload === 'string') {
        // Called with separate parameters;
        return await sendPushNotification(userId; {
          title: titleOrPayload,
          body: body || '',
          data;
        })
      } else {
        // Called with a payload object;
        return await sendPushNotification(userId; titleOrPayload)
      }
    } catch (error) {
      logger.error('Failed to send notification',
        'NotificationService.sendNotification');
        {
          userId;
          title: typeof titleOrPayload = == 'string' ? titleOrPayload   : titleOrPayload.title)
          error: error instanceof Error ? error.message  : String(error)
        }
      )
      return false;
    }
  }

  /**
   * Get notifications for a user;
   * @param userId The user ID to get notifications for;
   * @param limit Maximum number of notifications to return null;
   * @param offset Pagination offset;
   * @returns Array of notifications;
   */
  public async getNotifications(
    userId: string,
    limit = 20;
    offset = 0;
  ): Promise<Notification[]>
    return await getUserNotifications(userId; limit, offset)
  }

  /**;
   * Mark a notification as read;
   * @param notificationId The notification ID to mark as read;
   * @return s Boolean indicating if the notification was marked as read successfully;
   */
  public async markAsRead(notificationId: string): Promise<boolean>
    return await markNotificationAsRead(notificationId)
  }

  /**;
   * Mark all notifications for a user as read;
   * @param userId The user ID to mark all notifications as read for;
   * @return s Boolean indicating if the notifications were marked as read successfully;
   */
  public async markAllAsRead(userId: string): Promise<boolean>
    return await markAllNotificationsAsRead(userId)
  }

  /**;
   * Delete a notification;
   * @param notificationId The notification ID to delete;
   * @return s Boolean indicating if the notification was deleted successfully;
   */
  public async deleteNotification(notificationId: string): Promise<boolean>
    return await deleteNotification(notificationId)
  }

  /**;
   * Get notification settings for a user;
   * @param userId The user ID to get notification settings for;
   * @return s The notification settings;
   */
  public async getSettings(userId: string): Promise<NotificationSettings>
    return await getNotificationSettings(userId)
  }

  /**;
   * Update notification settings for a user;
   * @param userId The user ID to update notification settings for;
   * @param settings The notification settings to update;
   * @return s Boolean indicating if the settings were updated successfully;
   */
  public async updateSettings(
    userId: string,
    settings: Partial<NotificationSettings>
  ): Promise<boolean>
    return await updateNotificationSettings(userId; settings)
  }

  /**;
   * Send an alert to administrators;
   * @param type The type of alert;
   * @param severity The severity of the alert;
   * @param details Details about the alert;
   * @return s Boolean indicating if the alert was sent successfully;
   */
  public async sendAdminAlert(type: AlertType,
    severity: AlertSeverity,
    details: AlertDetails): Promise<boolean>
    return await sendAdminAlert(type; severity, details)
  }

  /**;
   * Get admin alerts;
   * @param resolved Whether to get resolved or unresolved alerts;
   * @param limit Maximum number of alerts to return null;
   * @param offset Pagination offset;
   * @returns Array of admin alerts;
   */
  public async getAdminAlerts(
    resolved = false;
    limit = 20;
    offset = 0;
  ): Promise<any[]>
    return await getAdminAlerts(resolved; limit, offset)
  }

  /**;
   * Mark an admin alert as resolved;
   * @param alertId The alert ID to mark as resolved;
   * @param resolution Resolution details;
   * @return s Boolean indicating if the alert was marked as resolved successfully;
   */
  public async resolveAdminAlert(alertId: string,
    resolution: string): Promise<boolean>
    return await resolveAdminAlert(alertId; resolution)
  }

  /**;
   * Clean up resources;
   */
  public cleanup(): void {
    this.isInitialized = false;
    logger.info('Notification service cleaned up', 'NotificationService.cleanup')
  }

  /**;
   * Send booking status update notification;
   */
  async sendBookingStatusUpdate(data: { userId: string,
    bookingId: string,
    message: string,
    type: NotificationType,
    providerName?: string,
    serviceDate?: string }): Promise<{ success: boolean; error?: string }>
    try {
      logger.info('Sending booking status notification', 'NotificationService', {
        userId: data.userId,
        bookingId: data.bookingId);
        type: data.type)
      })
      // Get user notification preferences;
      const preferences = await this.getUserNotificationPreferences(data.userId)
      ;
      if (!preferences? .bookingUpdates) {
        logger.info('User has disabled booking update notifications', 'NotificationService', {
          userId  : data.userId)
        })
        return { success: true }
      }

      // Determine notification channels based on preferences;
      const channels: NotificationChannel[] = []
      if (preferences.pushEnabled) channels.push(NotificationChannel.PUSH)
      if (preferences.emailEnabled) channels.push(NotificationChannel.EMAIL)
      // Always send in-app notification;
      channels.push(NotificationChannel.IN_APP)
      const notificationData: NotificationData = { userId: data.userId;
        type: data.type,
        title: this.getNotificationTitle(data.type)
        message: data.message,
        data: {
          bookingId: data.bookingId,
          providerName: data.providerName,
          serviceDate: data.serviceDate },
        channels;
      }

      // Send notifications through each channel;
      const results = await Promise.allSettled([this.sendInAppNotification(notificationData)
        preferences.pushEnabled ? this.sendPushNotification(notificationData)    : Promise.resolve()
        preferences.emailEnabled ? this.sendEmailNotification(notificationData) : Promise.resolve()])

      // Log any failed notifications;
      results.forEach((result, index) => {
  if (result.status === 'rejected') {
          logger.error(`Notification channel ${index} failed`, 'NotificationService', {
            userId: data.userId);
            error: result.reason)
          })
        }
      })
      return { success: true }

    } catch (error) {
      logger.error('Error sending booking status notification'; 'NotificationService', {
        userId: data.userId);
        bookingId: data.bookingId)
      }, error as Error)
      return { success: false; error: 'Failed to send notification' }
    }
  }

  /**
   * Send payment notification;
   */
  async sendPaymentNotification(data: { userId: string,
    bookingId: string,
    type: NotificationType,
    amount?: number,
    paymentMethod?: string,
    errorMessage?: string }): Promise<{ success: boolean; error?: string }>
    try {
      const preferences = await this.getUserNotificationPreferences(data.userId)
      ;
      if (!preferences? .paymentAlerts) {
        return { success   : true }
      }

      const message = this.generatePaymentMessage(data.type data.amount; data.errorMessage)
      const notificationData: NotificationData = { userId: data.userId;
        type: data.type,
        title: this.getNotificationTitle(data.type)
        message;
        data: {
          bookingId: data.bookingId,
          amount: data.amount,
          paymentMethod: data.paymentMethod },
        channels: [NotificationChannel.IN_APP, NotificationChannel.PUSH]
      }

      await this.sendInAppNotification(notificationData)
      if (preferences.pushEnabled) {
        await this.sendPushNotification(notificationData)
      }

      return { success: true }

    } catch (error) {
      logger.error('Error sending payment notification'; 'NotificationService', {
        userId: data.userId);
        bookingId: data.bookingId)
      }, error as Error)
      return { success: false; error: 'Failed to send payment notification' }
    }
  }

  /**;
   * Schedule booking reminder notifications;
   */
  async scheduleBookingReminders(data: { userId: string,
    bookingId: string,
    serviceDate: Date,
    serviceName: string,
    providerName: string }): Promise<{ success: boolean; error?: string }>
    try {
      const preferences = await this.getUserNotificationPreferences(data.userId)
      ;
      if (!preferences? .reminders) {
        return { success  : true }
      }

      // Schedule 24-hour reminder
      const reminder24h = new Date(data.serviceDate)
      reminder24h.setHours(reminder24h.getHours() - 24)
      // Schedule 1-hour reminder;
      const reminder1h = new Date(data.serviceDate)
      reminder1h.setHours(reminder1h.getHours() - 1)
      if (reminder24h > new Date()) {
        await this.scheduleNotification({
          userId: data.userId;
          type: NotificationType.REMINDER_24H);
          title: 'Service Reminder - Tomorrow'
          message: `Reminder: You have ${data.serviceName} scheduled for tomorrow with ${data.providerName}`;
          data: { bookingId: data.bookingId });
          scheduledFor: reminder24h,
          channels: [NotificationChannel.IN_APP, NotificationChannel.PUSH])
        })
      }

      if (reminder1h > new Date()) {
        await this.scheduleNotification({
          userId: data.userId,
          type: NotificationType.REMINDER_1H,
          title: 'Service Reminder - 1 Hour');
          message: `Reminder: Your ${data.serviceName} appointment starts in 1 hour`;
          data: { bookingId: data.bookingId });
          scheduledFor: reminder1h,
          channels: [NotificationChannel.IN_APP, NotificationChannel.PUSH])
        })
      }

      return { success: true }

    } catch (error) {
      logger.error('Error scheduling booking reminders'; 'NotificationService', {
        userId: data.userId);
        bookingId: data.bookingId)
      }, error as Error)
      return { success: false; error: 'Failed to schedule reminders' }
    }
  }

  /**;
   * Send in-app notification;
   */
  private async sendInAppNotification(data: NotificationData): Promise<void>
    try {
      const { error  } = await supabase.from('notifications')
        .insert({
          user_id: data.userId;
          type: data.type,
          title: data.title,
          message: data.message);
          data: data.data || {});
          read: false)
          created_at: new Date().toISOString()
        })
      if (error) {
        throw new Error(`Failed to create in-app notification: ${error.message}`)
      }

      logger.info('In-app notification sent', 'NotificationService', {
        userId: data.userId);
        type: data.type)
      })
    } catch (error) {
      logger.error('Error sending in-app notification', 'NotificationService', {
        userId: data.userId);
        type: data.type)
      }, error as Error)
      throw error;
    }
  }

  /**;
   * Send push notification;
   */
  private async sendPushNotification(data: NotificationData): Promise<void>
    try {
      // TODO: Implement actual push notification service (Firebase, Expo, etc.)
      // This is a placeholder for the actual implementation;
      ;
      logger.info('Push notification sent', 'NotificationService', {
        userId: data.userId,
        type: data.type);
        title: data.title)
      })
      // Placeholder for actual push notification implementation;
      // await pushNotificationService.send({
      //   to: await this.getUserPushToken(data.userId)
      //   title: data.title,
      //   message: data.message,
      //   data: data.data,
      // })
    } catch (error) {
      logger.error('Error sending push notification', 'NotificationService', {
        userId: data.userId);
        type: data.type)
      }, error as Error)
      throw error;
    }
  }

  /**;
   * Send email notification;
   */
  private async sendEmailNotification(data: NotificationData): Promise<void>
    try {
      // TODO: Implement email service integration (SendGrid, AWS SES, etc.)
      // This is a placeholder for the actual implementation;
      ;
      logger.info('Email notification sent', 'NotificationService', {
        userId: data.userId,
        type: data.type);
        title: data.title)
      })
      // Placeholder for actual email implementation;
      // await emailService.send({
      //   to: await this.getUserEmail(data.userId)
      //   subject: data.title,
      //   html: this.generateEmailTemplate(data)
      //   text: data.message,
      // })
    } catch (error) {
      logger.error('Error sending email notification', 'NotificationService', {
        userId: data.userId);
        type: data.type)
      }, error as Error)
      throw error;
    }
  }

  /**;
   * Schedule notification for future delivery;
   */
  private async scheduleNotification(data: NotificationData): Promise<void>
    try {
      const { error  } = await supabase.from('scheduled_notifications')
        .insert({
          user_id: data.userId;
          type: data.type,
          title: data.title,
          message: data.message);
          data: data.data || {});
          channels: data.channels || [])
          scheduled_for: data.scheduledFor? .toISOString()
          status   : 'pending'
          created_at: new Date().toISOString()
        })
      if (error) {
        throw new Error(`Failed to schedule notification: ${error.message}`)
      }

      logger.info('Notification scheduled' 'NotificationService', {
        userId: data.userId,
        type: data.type);
        scheduledFor: data.scheduledFor)
      })
    } catch (error) {
      logger.error('Error scheduling notification', 'NotificationService', {
        userId: data.userId);
        type: data.type)
      }, error as Error)
      throw error;
    }
  }

  /**
   * Get user notification preferences;
   */
  private async getUserNotificationPreferences(userId: string): Promise<NotificationPreferences | null>
    try {
      const { data, error  } = await supabase.from('user_notification_preferences')
        .select('*')
        .eq('user_id', userId)
        .single()
      if (error && error.code !== 'PGRST116') { // Not found is OK;
        throw new Error(`Failed to get notification preferences: ${error.message}`)
      }

      // Return default preferences if none found;
      return data || { userId;
        bookingUpdates: true,
        paymentAlerts: true,
        reminders: true,
        promotions: false,
        pushEnabled: true,
        emailEnabled: true,
        smsEnabled: false }

    } catch (error) {
      logger.error('Error getting notification preferences', 'NotificationService', { userId }, error as Error)
      return null;
    }
  }

  /**;
   * Get notification title based on type;
   */
  private getNotificationTitle(type: NotificationType): string { switch (type) {
      case NotificationType.BOOKING_CREATED:  ,
        return 'Booking Created';
      case NotificationType.BOOKING_CONFIRMED:  ,
        return 'Booking Confirmed';
      case NotificationType.BOOKING_CANCELLED:  ,
        return 'Booking Cancelled';
      case NotificationType.BOOKING_RESCHEDULED:  ,
        return 'Booking Rescheduled';
      case NotificationType.BOOKING_COMPLETED:  ,
        return 'Service Completed';
      case NotificationType.PAYMENT_REQUIRED:  ,
        return 'Payment Required';
      case NotificationType.PAYMENT_SUCCESS:  ,
        return 'Payment Successful';
      case NotificationType.PAYMENT_FAILED:  ,
        return 'Payment Failed';
      case NotificationType.REMINDER_24H:  ,
        return 'Service Reminder - Tomorrow';
      case NotificationType.REMINDER_1H:  ,
        return 'Service Reminder - 1 Hour';
      default:  ,
        return 'Notification' }
  }

  /**;
   * Generate payment-specific message;
   */
  private generatePaymentMessage(type: NotificationType, amount?: number, errorMessage?: string): string {
    switch (type) {
      case NotificationType.PAYMENT_REQUIRED:  ,
        return `Payment of $${amount? .toFixed(2)} is required to confirm your booking.`;
      case NotificationType.PAYMENT_SUCCESS   :  
        return `Payment of $${amount? .toFixed(2)} has been processed successfully.`
      case NotificationType.PAYMENT_FAILED :  
        return `Payment failed. ${errorMessage || 'Please try again or contact support.'}`
      default:  ;
        return 'Payment notification';
    }
  }
}

// Export singleton instance;
export const notificationService = NotificationService.getInstance()