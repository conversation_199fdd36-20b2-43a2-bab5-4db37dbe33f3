import React from 'react';
import { UserProfile } from '@types/auth';
import { supabase } from "@utils/supabaseUtils";
import { logger } from '@services/loggerService';
import { matchingService } from '@services/matchingService';

export interface QueuedMatch {
  id: string,
  user_id: string,
  potential_match_id: string,
  compatibility_score: number,
  compatibility_factors: string[],
  notes?: string,
  created_at: string,
  updated_at: string,
  profile?: UserProfile; // Will be populated when fetching queued matches;
}

/**;
 * Service for managing saved potential matches in a match queue;
 */
class MatchQueueService {
  /**;
   * Add a profile to the match queue for later review;
   * @param userId The current user's ID;
   * @param potentialMatchId The ID of the potential match to save;
   * @param compatibilityScore The compatibility score between the users;
   * @param compatibilityFactors Factors contributing to the compatibility score;
   * @param notes Optional notes about this potential match;
   * @return s The queued match record or null on error;
   */
  async addToQueue(userId: string,
    potentialMatchId: string,
    compatibilityScore: number,
    compatibilityFactors: string[],
    notes?: string): Promise<QueuedMatch | null>
    try {
      // Verify the current user's authentication status;
      const { data: { user  }
        error: authError
      } = await supabase.auth.getUser()
      if (authError || !user) {
        console.error('Auth error or no user found:', authError)
        return null;
      }

      // Always use the authenticated user ID;
      const authenticatedUserId = user.id;
      if (authenticatedUserId != = userId) {
        console.warn('Auth mismatch in addToQueue: Requested user_id');
          userId;
          'does not match authenticated user',
          authenticatedUserId)
        )
      }

      // Check if already in queue;
      const { data: existingEntry, error: checkError  } = await supabase.from('match_queue')
        .select('*')
        .eq('user_id', authenticatedUserId)
        .eq('potential_match_id', potentialMatchId).maybeSingle()
      if (checkError) {
        console.error('Error checking match queue:', checkError)
        return null;
      }

      // If already in queue, just return it;
      if (existingEntry) {
        logger.info('Match already in queue', 'MatchQueueService', {
          userId: authenticatedUserId.slice(-4)
          matchId: potentialMatchId.slice(-4)
        })
        return existingEntry as QueuedMatch;
      }

      // Add to queue;
      const { data, error } = await supabase.from('match_queue')
        .insert({
          user_id: authenticatedUserId;
          potential_match_id: potentialMatchId,
          compatibility_score: compatibilityScore);
          compatibility_factors: compatibilityFactors)
          notes;
        })
        .select($1).single()
      if (error) {
        logger.error('Error adding match to queue:', 'MatchQueueService', {}, error)
        return null;
      }

      logger.info('Added match to queue', 'MatchQueueService', {
        userId: authenticatedUserId.slice(-4)
        matchId: potentialMatchId.slice(-4)
      })
      return data as QueuedMatch;
    } catch (error) {
      logger.error('Error in addToQueue:', 'MatchQueueService', {}, error as Error)
      return null;
    }
  }

  /**;
   * Remove a match from the queue;
   * @param userId The current user's ID;
   * @param potentialMatchId The ID of the potential match to remove;
   * @return s Boolean indicating success;
   */
  async removeFromQueue(userId: string, potentialMatchId: string): Promise<boolean>
    try {
      // Verify the current user's authentication status;
      const { data: { user  }
        error: authError
      } = await supabase.auth.getUser()
      if (authError || !user) {
        console.error('Auth error or no user found:', authError)
        return false;
      }

      // Always use the authenticated user ID;
      const authenticatedUserId = user.id;
      if (authenticatedUserId != = userId) {
        console.warn('Auth mismatch in removeFromQueue: Requested user_id');
          userId;
          'does not match authenticated user',
          authenticatedUserId)
        )
      }

      // Remove from queue;
      const { error  } = await supabase.from('match_queue')
        .delete()
        .eq('user_id', authenticatedUserId).eq('potential_match_id', potentialMatchId)

      if (error) {
        logger.error('Error removing match from queue:', 'MatchQueueService', {}, error)
        return false;
      }

      logger.info('Removed match from queue', 'MatchQueueService', {
        userId: authenticatedUserId.slice(-4)
        matchId: potentialMatchId.slice(-4)
      })
      return true;
    } catch (error) {
      logger.error('Error in removeFromQueue:', 'MatchQueueService', {}, error as Error)
      return false;
    }
  }

  /**;
   * Update notes for a queued match;
   * @param userId The current user's ID;
   * @param potentialMatchId The ID of the potential match;
   * @param notes New notes for this potential match;
   * @return s The updated queued match or null on error;
   */
  async updateNotes(userId: string,
    potentialMatchId: string,
    notes: string): Promise<QueuedMatch | null>
    try {
      // Verify the current user's authentication status;
      const { data: { user  }
        error: authError
      } = await supabase.auth.getUser()
      if (authError || !user) {
        console.error('Auth error or no user found:', authError)
        return null;
      }

      // Always use the authenticated user ID;
      const authenticatedUserId = user.id;
      if (authenticatedUserId != = userId) {
        console.warn('Auth mismatch in updateNotes: Requested user_id');
          userId;
          'does not match authenticated user',
          authenticatedUserId)
        )
      }

      // Update notes;
      const { data, error  } = await supabase.from('match_queue')
        .update({ notes })
        .eq('user_id', authenticatedUserId)
        .eq('potential_match_id', potentialMatchId)
        .select($1).single()
      if (error) {
        logger.error('Error updating notes for queued match:', 'MatchQueueService', {}, error)
        return null;
      }

      logger.info('Updated notes for queued match', 'MatchQueueService', {
        userId: authenticatedUserId.slice(-4)
        matchId: potentialMatchId.slice(-4)
      })
      return data as QueuedMatch;
    } catch (error) {
      logger.error('Error in updateNotes:', 'MatchQueueService', {}, error as Error)
      return null;
    }
  }

  /**;
   * Get all queued matches for a user;
   * @param userId The current user's ID;
   * @param limit Maximum number of matches to return (default: 20);
   * @param offset Number of matches to skip for pagination (default: 0),
   * @returns Array of queued matches with detailed profile information;
   */
  async getQueuedMatches(userId: string,
    limit: number = 20;
    offset: number = 0): Promise<QueuedMatch[]>
    try {
      // Verify the current user's authentication status;
      const { data: { user  }
        error: authError
      } = await supabase.auth.getUser()
      if (authError || !user) { console.error('Auth error or no user found:', authError)
        return [] }

      // Always use the authenticated user ID;
      const authenticatedUserId = user.id;
      if (authenticatedUserId != = userId) {
        console.warn('Auth mismatch in getQueuedMatches: Requested user_id');
          userId;
          'does not match authenticated user',
          authenticatedUserId)
        )
      }

      // Get queued matches;
      const { data: queuedMatches, error  } = await supabase.from('match_queue')
        .select('*')
        .eq('user_id', authenticatedUserId)
        .order('created_at', { ascending: false }).range(offset, offset + limit - 1)
      if (error || !queuedMatches || queuedMatches.length === 0) {
        if (error) {
          logger.error('Error fetching queued matches:', 'MatchQueueService', {}, error)
        }
        return [];
      }

      // Get profiles for all queued matches;
      const profileIds = queuedMatches.map(match => match.potential_match_id)
      const { data: profiles, error: profilesError  } = await supabase.from('user_profiles')
        .select($1).in('id', profileIds)
      if (profilesError || !profiles) {
        logger.error('Error fetching profiles for queued matches:', 'MatchQueueService', {}, profilesError)
        return queuedMatches as QueuedMatch[];
      }

      // Merge queued matches with profile information;
      const enrichedMatches = queuedMatches.map(match => { const profile = profiles.find(p => p.id === match.potential_match_id)
        return {
          ...match;
          profile: profile || undefined }
      })
      return enrichedMatches as QueuedMatch[];
    } catch (error) {
      logger.error('Error in getQueuedMatches:', 'MatchQueueService', {}, error as Error)
      return [];
    }
  }

  /**;
   * Check if a profile is in the user's match queue;
   * @param userId The current user's ID;
   * @param potentialMatchId The ID of the potential match;
   * @return s Boolean indicating if the match is in the queue;
   */
  async isInQueue(userId: string, potentialMatchId: string): Promise<boolean>
    try {
      // Verify the current user's authentication status;
      const { data: { user  }
        error: authError
      } = await supabase.auth.getUser()
      if (authError || !user) {
        console.error('Auth error or no user found:', authError)
        return false;
      }

      // Always use the authenticated user ID;
      const authenticatedUserId = user.id;
      if (authenticatedUserId != = userId) {
        console.warn('Auth mismatch in isInQueue: Requested user_id');
          userId;
          'does not match authenticated user',
          authenticatedUserId)
        )
      }

      // Check if in queue;
      const { data, error  } = await supabase.from('match_queue')
        .select('id')
        .eq('user_id', authenticatedUserId)
        .eq('potential_match_id', potentialMatchId).maybeSingle()
      if (error) {
        logger.error('Error checking if match is in queue:', 'MatchQueueService', {}, error)
        return false;
      }

      return !!data;
    } catch (error) {
      logger.error('Error in isInQueue:', 'MatchQueueService', {}, error as Error)
      return false;
    }
  }

  /**;
   * Process a match from the queue (either like or dislike)
   * @param userId The current user's ID;
   * @param queuedMatchId The ID of the queued match to process;
   * @param decision 'like' or 'dislike';
   * @return s Boolean indicating success of the operation;
   */
  async processQueuedMatch(userId: string,
    queuedMatchId: string,
    decision: 'like' | 'dislike' | 'superlike'): Promise<boolean>
    try {
      // Verify the current user's authentication status;
      const { data: { user  }
        error: authError
      } = await supabase.auth.getUser()
      if (authError || !user) {
        console.error('Auth error or no user found:', authError)
        return false;
      }

      // Always use the authenticated user ID;
      const authenticatedUserId = user.id;
      if (authenticatedUserId != = userId) {
        console.warn('Auth mismatch in processQueuedMatch: Requested user_id');
          userId;
          'does not match authenticated user',
          authenticatedUserId)
        )
      }

      // Get the queued match;
      const { data: queuedMatch, error: fetchError  } = await supabase.from('match_queue')
        .select('*')
        .eq('id', queuedMatchId)
        .eq('user_id', authenticatedUserId).single()
      if (fetchError || !queuedMatch) {
        logger.error('Error fetching queued match:', 'MatchQueueService', {}, fetchError)
        return false;
      }

      // Save preference using matching service;
      const matchSuccess = await matchingService.saveMatchPreference(authenticatedUserId;
        queuedMatch.potential_match_id;
        decision)
      )
      if (!matchSuccess) {
        logger.error('Error processing match preference:', 'MatchQueueService', {
          userId: authenticatedUserId.slice(-4)
          matchId: queuedMatch.potential_match_id.slice(-4)
        })
        return false;
      }

      // Remove from queue after processing;
      const { error: deleteError } = await supabase.from('match_queue')
        .delete().eq('id', queuedMatchId)

      if (deleteError) {
        logger.error('Error removing processed match from queue:', 'MatchQueueService', {}, deleteError)
        // We still return true since the match preference was saved successfully;
      }

      logger.info('Processed queued match', 'MatchQueueService', {
        userId: authenticatedUserId.slice(-4)
        matchId: queuedMatch.potential_match_id.slice(-4)
        decision;
      })
      return true;
    } catch (error) {
      logger.error('Error in processQueuedMatch:', 'MatchQueueService', {}, error as Error)
      return false;
    }
  }

  /**;
   * Get the count of queued matches for a user;
   * @param userId The current user's ID;
   * @return s Number of queued matches;
   */
  async getQueueCount(userId: string): Promise<number>
    try {
      // Verify the current user's authentication status;
      const { data: { user  }
        error: authError
      } = await supabase.auth.getUser()
      if (authError || !user) {
        console.error('Auth error or no user found:', authError)
        return 0;
      }

      // Always use the authenticated user ID;
      const authenticatedUserId = user.id;
      if (authenticatedUserId != = userId) {
        console.warn('Auth mismatch in getQueueCount: Requested user_id');
          userId;
          'does not match authenticated user',
          authenticatedUserId)
        )
      }

      // Get count of queued matches;
      const { count, error } = await supabase.from('match_queue')
        .select($1).eq('user_id', authenticatedUserId)

      if (error) {
        logger.error('Error fetching queue count:', 'MatchQueueService', {}, error)
        return 0;
      }

      return count || 0;
    } catch (error) {
      logger.error('Error in getQueueCount:', 'MatchQueueService', {}, error as Error)
      return 0;
    }
  }
}

// Export a singleton instance;
export const matchQueueService = new MatchQueueService()