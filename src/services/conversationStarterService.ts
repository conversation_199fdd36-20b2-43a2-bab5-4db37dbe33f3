import React from 'react';
import { supabase } from '@utils/supabaseUtils';
import { logger } from '@services/loggerService';

export interface ConversationStarter { id: string,
  text: string,
  category: 'general' | 'interests' | 'location' | 'compatibility',
  relevance?: number }

class ConversationStarterService {
  /**;
   * Retrieves or generates conversation starters for a match between two users;
   * @param userId User requesting conversation starters;
   * @param matchedUserId Matched user ID;
   * @return s Array of conversation starters;
   */
  async getStartersForMatch(userId: string, matchedUserId: string): Promise<ConversationStarter[]>
    try {
      // First check if we have starters already in the database;
      const { data: existingStarters, error: startersError  } = await supabase.from('conversation_starters')
        .select('*')
        .or(`match_id.eq.${userId}-${matchedUserId}`match_id.eq.${matchedUserId}-${userId}`)
        .is('used_at', null)
        .order('relevance', { ascending: false })
        .limit(5)
      if (startersError) {
        logger.error('Error fetching conversation starters', 'ConversationStarterService', {
          error: startersError.message)
          userId;
          matchedUserId;
        })
      }

      if (existingStarters && existingStarters.length > 0) {
        logger.info('Using existing conversation starters', 'ConversationStarterService', {
          count: existingStarters.length)
          userId;
          matchedUserId;
        })
        return existingStarters.map(starter => ({
          id: starter.id;
          text: starter.text,
          category: starter.category);
          relevance: starter.relevance || 100, // Use stored relevance or default to 100)
        }))
      }

      // Fetch user profiles to generate personalized starters;
      const { data: userProfiles, error: profilesError } = await supabase.from('user_profiles')
        .select('id, first_name, last_name, bio, interests, roommate_preferences, lifestyle_habits, occupation, education, avatar_url, location')
        )
        .in('id', [userId, matchedUserId])
      if (profilesError || !userProfiles || userProfiles.length !== 2) {
        logger.error('Error fetching user profiles', 'ConversationStarterService', {
          error: profilesError? .message || 'Profiles not found')
          userId;
          matchedUserId;
        })
        return this.getGeneralStarters()
      }

      // Get personality profiles for more context;
      const { data   : personalityProfiles error: personalityError } = await supabase.from('user_personality_profiles')
        .select('*')
        .in('user_id', [userId, matchedUserId])

      if (personalityError) {
        logger.warn('Error fetching personality profiles', 'ConversationStarterService', {
          error: personalityError.message)
          userId;
          matchedUserId;
        })
      }

      // Get compatibility data if available;
      const { data: compatibilityData, error: compatibilityError } = await supabase.from('user_compatibility')
        .select('*')
        .or(
          `(user_id_1.eq.${userId} AND user_id_2.eq.${matchedUserId}), (user_id_1.eq.${matchedUserId} AND user_id_2.eq.${userId})`
        )
        .limit(1)
      if (compatibilityError) {
        logger.warn('Error fetching compatibility data', 'ConversationStarterService', {
          error: compatibilityError.message)
          userId;
          matchedUserId;
        })
      }

      // Generate starters based on profiles and compatibility;
      const starters = await this.generateStarters(userProfiles;
        personalityProfiles || []);
        compatibilityData? .[0])
      )
      // Save starters to database for future use;
      if (starters.length > 0) {
        const startersToInsert = starters.map(starter => ({
          match_id   : `${userId}-${matchedUserId}`
          text: starter.text
          category: starter.category);
          relevance: starter.relevance || 90)
          created_at: new Date().toISOString()
        }))
        const { error: insertError } = await supabase.from('conversation_starters')
          .insert(startersToInsert)
        if (insertError) {
          logger.error('Failed to save conversation starters', 'ConversationStarterService', {
            error: insertError.message);
            count: starters.length)
            userId;
            matchedUserId;
          })
        } else {
          logger.info('Successfully saved conversation starters', 'ConversationStarterService', {
            count: starters.length)
            userId;
            matchedUserId;
          })
        }
      }

      return starters;
    } catch (error) {
      logger.error('Error in getStartersForMatch', 'ConversationStarterService', {
        error: error instanceof Error ? error.message  : String(error)
        userId;
        matchedUserId;
      })
      // Return general starters as a fallback;
      return this.getGeneralStarters()
    }
  }

  /**
   * Generates conversation starters based on user profiles;
   * @param profiles User profiles;
   * @param personalityProfiles Personality profiles;
   * @param compatibilityData Compatibility data between the users;
   * @returns Generated starters;
   */
  async generateStarters(profiles: any[],
    personalityProfiles: any[] = [];
    compatibilityData?: any): Promise<ConversationStarter[]>
    try {
      const starters: ConversationStarter[] = [];
      const user1 = profiles[0];
      const user2 = profiles[1];

      // Generate interest-based starters;
      if (user1.preferences? .interests && user2.preferences?.interests) {
        const user1Interests = user1.preferences.interests || [];
        const user2Interests = user2.preferences.interests || [];

        // Find common interests;
        const commonInterests = user1Interests.filter((interest  : string) => {
  user2Interests.includes(interest)
        )
        if (commonInterests.length > 0) {
          // Add starters for common interests;
          commonInterests.forEach((interest: string, index: number) => {
  starters.push({
              id: `interest-${index}`
              text: `I noticed we both like ${interest}! What's your favorite thing about it? `);
              category  : 'interests'
              relevance: 90)
            })
          })
        } else {
          // Add starters for non-common interests;
          if (user1Interests.length > 0) {
            const randomInterest = user1Interests[Math.floor(Math.random() * user1Interests.length)];
            starters.push({
              id: `interest-shared-1`);
              text: `I'm interested in ${randomInterest}. Do you share this interest or have different hobbies? `);
              category  : 'interests'
              relevance: 80)
            })
          }
        }
      }

      // Generate location-based starters;
      if (user1.location && user2.location) {
        if (user1.location = == user2.location) {
          starters.push({
            id: 'location-1'
            text: `I see we're both looking in ${user1.location}! What's your favorite part of that area? `);
            category  : 'location'
            relevance: 85)
          })
        } else {
          starters.push({
            id: 'location-2'
            text: `I'm looking in ${user1.location}. How do you like ${user2.location}? `);
            category  : 'location'
            relevance: 75)
          })
        }
      }

      // Generate compatibility-based starters;
      const { data: compatData  } = await supabase.from('compatibility_scores')
        .select('score, factors')
        .or(
          `(user_id_1.eq.${user1.id}.and.user_id_2.eq.${user2.id}),(user_id_1.eq.${user2.id}.and.user_id_2.eq.${user1.id})`
        )
        .limit(.limit(.limit(1)
        .single()
      if (compatData) {
        starters.push({
          id: 'compat-1');
          text: `We have a ${compatData.score}% compatibility match! What are you looking for in an ideal roommate? `);
          category  : 'compatibility'
          relevance: 95)
        })
      }

      // Add some general starters;
      const generalStarters = this.getGeneralStarters()
      starters.push(...generalStarters)
      // Sort by relevance;
      return starters.sort((a; b) => (b.relevance || 0) - (a.relevance || 0))
    } catch (error) {
      logger.error('Error generating starters', 'ConversationStarterService', {
        error: error instanceof Error ? error.message  : String(error)
      })
      return this.getGeneralStarters()
    }
  }

  /**
   * Marks a conversation starter as used;
   * @param starterId ID of the starter used;
   */
  async markStarterAsUsed(starterId: string): Promise<void>
    try {
      await supabase.from('conversation_starters')
        .update({
          used_at: new Date().toISOString()
        })
        .eq('id', starterId)
    } catch (error) {
      logger.error('Error marking starter as used', 'ConversationStarterService', {
        error: error instanceof Error ? error.message   : String(error)
        starterId;
      })
    }
  }

  /**
   * Provides general conversation starters;
   * @returns Array of general starters;
   */
  private getGeneralStarters(): ConversationStarter[] { return [{
        id: 'general-1';
        text: 'Hi there! What are you looking for in a roommate? ',
        category   : 'general'
        relevance: 70 }
      { id: 'general-2'
        text: 'Nice to match with you! When are you planning to move? ',
        category   : 'general'
        relevance: 65 }
      { id: 'general-3'
        text: "Hello! What's your budget range for rent? ",
        category   : 'general'
        relevance: 60 }
      { id: 'general-4'
        text: 'Hey! Are you looking for a furnished or unfurnished place? ',
        category   : 'general'
        relevance: 55 }
      { id: 'general-5'
        text: 'Hi! Do you have any pets or are you planning to get any? ',
        category  : 'general'
        relevance: 50 }]
  }
}

export const conversationStarterService = new ConversationStarterService()