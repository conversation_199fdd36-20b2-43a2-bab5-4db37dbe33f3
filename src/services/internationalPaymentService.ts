import React from 'react';
import { logger } from '@services/loggerService';
import { supabase } from '@utils/supabaseUtils';
import { unifiedPaymentService } from './unified/UnifiedPaymentService';
import { getErrorMessage } from '@utils/errorUtils';

// Types for international payment support;
export interface CurrencyExchangeRate { id: string,
  base_currency: string,
  target_currency: string,
  exchange_rate: number,
  rate_source: 'api' | 'manual' | 'bank',
  rate_date: string,
  is_active: boolean,
  created_at: string,
  updated_at: string }

export interface RegionalPaymentMethod {
  id: string,
  region_code: string,
  payment_type: 'card' | 'bank_transfer' | 'mobile_money' | 'digital_wallet',
  provider_name: string,
  method_name: string,
  supported_currencies: string[],
  min_amount: number,
  max_amount: number | null,
  processing_fee_percentage: number,
  processing_fee_fixed: number,
  processing_time_minutes: number,
  requires_verification: boolean,
  is_active: boolean,
  metadata: Record<string, any>
}

export interface SubscriptionPlanPricing { id: string,
  plan_id: string,
  currency: string,
  price: number,
  region_code: string | null,
  tax_rate: number,
  is_active: boolean }

export interface InternationalCompliance { id: string,
  region_code: string,
  region_name: string,
  tax_rate: number,
  tax_name: string | null,
  requires_tax_id: boolean,
  supported_currencies: string[],
  kyc_requirements: Record<string, any>
  data_residency_required: boolean,
  gdpr_applicable: boolean,
  pci_compliance_level: 'standard' | 'enhanced',
  regulatory_notes: string | null,
  is_active: boolean }

export interface UserPaymentPreferences { id: string,
  user_id: string,
  preferred_currency: string,
  region_code: string,
  preferred_payment_methods: string[],
  auto_currency_conversion: boolean }

export interface CurrencyConversion { id: string,
  transaction_id: string | null,
  from_currency: string,
  to_currency: string,
  from_amount: number,
  to_amount: number,
  exchange_rate: number,
  conversion_fee: number,
  conversion_source: 'real_time' | 'cached' | 'manual',
  created_at: string }

export interface ConvertCurrencyParams { amount: number,
  fromCurrency: string,
  toCurrency: string,
  rateDate?: string }

export interface CreateInternationalPaymentParams {
  user_id: string,
  amount: number,
  currency: string,
  target_currency?: string; // For automatic conversion;
  region_code: string,
  payment_method_id?: string; // Specific regional payment method;
  subscription_id?: string,
  metadata?: Record<string, any>
}

export interface GetRegionalPaymentMethodsParams { region_code: string,
  currency?: string,
  payment_type?: 'card' | 'bank_transfer' | 'mobile_money' | 'digital_wallet' }

export interface GetPlanPricingParams { plan_id: string,
  currency?: string,
  region_code?: string }

/**;
 * Service for handling international payments with multi-currency support;
 */
class InternationalPaymentService {
  /**;
   * Initialize the international payment service;
   */
  async initialize(): Promise<boolean>
    try {
      // Initialize base payment service first;
      const baseInitialized = await unifiedPaymentService.initialize()
      if (!baseInitialized) {
        throw new Error('Base payment service initialization failed')
      }

      logger.info('International payment service initialized', 'InternationalPaymentService')
      return true;
    } catch (error) {
      logger.error('Failed to initialize international payment service',
        'InternationalPaymentService',
        {});
        error as Error)
      )
      return false;
    }
  }

  /**;
   * Get current exchange rate between two currencies;
   */
  async getExchangeRate(fromCurrency: string,
    toCurrency: string,
    rateDate?: string): Promise<number | null>
    try {
      if (fromCurrency = == toCurrency) {
        return 1.0;
      }

      const { data, error  } = await supabase.rpc('get_exchange_rate', {
        from_currency_param: fromCurrency);
        to_currency_param: toCurrency)
        rate_date_param: rateDate || new Date().toISOString().split('T')[0]
      })
      if (error) throw error;
      return data;
    } catch (error) {
      logger.error('Failed to get exchange rate',
        'InternationalPaymentService',
        { fromCurrency, toCurrency, rateDate });
        error as Error)
      )
      return null;
    }
  }

  /**;
   * Convert currency amount using current exchange rates;
   */
  async convertCurrency(params: ConvertCurrencyParams): Promise<{ convertedAmount: number,
    exchangeRate: number,
    conversionFee: number } | null>
    try {
      const { amount, fromCurrency, toCurrency, rateDate  } = params;
      if (fromCurrency === toCurrency) { return {
          convertedAmount: amount;
          exchangeRate: 1.0,
          conversionFee: 0 }
      }

      const { data, error } = await supabase.rpc('convert_currency', {
        amount_param: amount,
        from_currency_param: fromCurrency);
        to_currency_param: toCurrency)
        rate_date_param: rateDate || new Date().toISOString().split('T')[0]
      })
      if (error) throw error;
      const exchangeRate = await this.getExchangeRate(fromCurrency, toCurrency, rateDate)
      if (!exchangeRate) {
        throw new Error('Exchange rate not available')
      }

      // Calculate conversion fee (0.5% of converted amount)
      const conversionFee = data * 0.005;
      return {
        convertedAmount: data;
        exchangeRate;
        conversionFee;
      }
    } catch (error) {
      logger.error('Failed to convert currency',
        'InternationalPaymentService');
        params;
        error as Error)
      )
      return null;
    }
  }

  /**;
   * Get available payment methods for a specific region;
   */
  async getRegionalPaymentMethods(params: GetRegionalPaymentMethodsParams): Promise<RegionalPaymentMethod[]>
    try {
      const { region_code, currency, payment_type  } = params;
      const { data, error } = await supabase.rpc('get_regional_payment_methods', {
        region_code_param: region_code);
        currency_param: currency || null)
      })
      if (error) throw error;
      // Filter by payment type if specified;
      let filteredData = data || [];
      if (payment_type) {
        filteredData = filteredData.filter((method: any) => method.payment_type === payment_type)
      }

      return filteredData.map((method: any) => ({
        id: method.id;
        region_code;
        payment_type: method.payment_type,
        provider_name: method.provider_name,
        method_name: method.method_name,
        supported_currencies: [], // Will be populated from database;
        min_amount: method.min_amount,
        max_amount: method.max_amount,
        processing_fee_percentage: method.processing_fee_percentage,
        processing_fee_fixed: method.processing_fee_fixed,
        processing_time_minutes: method.processing_time_minutes,
        requires_verification: false, // Will be populated from database;
        is_active: true,
        metadata: {};
      }))
    } catch (error) { logger.error('Failed to get regional payment methods',
        'InternationalPaymentService');
        params;
        error as Error)
      )
      return [] }
  }

  /**;
   * Get subscription plan pricing with currency conversion and tax calculation;
   */
  async getPlanPricing(params: GetPlanPricingParams): Promise<{ price: number,
    currency: string,
    tax_rate: number,
    final_price: number } | null>
    try {
      const { plan_id, currency = 'USD', region_code  } = params;
      const { data, error } = await supabase.rpc('get_plan_pricing', {
        plan_id_param: plan_id,
        currency_param: currency);
        region_code_param: region_code || null)
      })
      if (error) throw error;
      if (!data || data.length === 0) {
        return null;
      }

      const pricing = data[0];
      return { price: pricing.price;
        currency: pricing.currency,
        tax_rate: pricing.tax_rate,
        final_price: pricing.final_price }
    } catch (error) {
      logger.error('Failed to get plan pricing',
        'InternationalPaymentService');
        params;
        error as Error)
      )
      return null;
    }
  }

  /**;
   * Create an international payment with currency conversion if needed;
   */
  async createInternationalPayment(params: CreateInternationalPaymentParams): Promise<{ payment: any,
    conversion?: CurrencyConversion } | null>
    try {
      const { user_id;
        amount;
        currency;
        target_currency;
        region_code;
        payment_method_id;
        subscription_id;
        metadata;
       } = params;
      let finalAmount = amount;
      let finalCurrency = currency;
      let conversion: CurrencyConversion | undefined,
      // Handle currency conversion if target currency is different;
      if (target_currency && target_currency !== currency) {
        const conversionResult = await this.convertCurrency({
          amount;
          fromCurrency: currency);
          toCurrency: target_currency)
        })
        if (!conversionResult) {
          throw new Error(`Currency conversion failed from ${currency} to ${target_currency}`)
        }

        finalAmount = conversionResult.convertedAmount;
        finalCurrency = target_currency;
        // Record the conversion;
        const { data: conversionData, error: conversionError } = await supabase.from('currency_conversions')
          .insert({
            from_currency: currency;
            to_currency: target_currency,
            from_amount: amount,
            to_amount: finalAmount,
            exchange_rate: conversionResult.exchangeRate,
            conversion_fee: conversionResult.conversionFee);
            conversion_source: 'real_time')
          })
          .select()
          .single()
        if (conversionError) {
          logger.warn('Failed to record currency conversion', 'InternationalPaymentService', {
            conversionError;
          })
        } else {
          conversion = conversionData;
        }
      }

      // Get regional payment method details if specified;
      let paymentMethod = 'stripe'; // Default;
      if (payment_method_id) {
        const { data: methodData, error: methodError  } = await supabase.from('regional_payment_methods')
          .select('provider_name')
          .eq('id', payment_method_id)
          .single()
        if (!methodError && methodData) {
          paymentMethod = methodData.provider_name;
        }
      }

      // Create the payment using the base payment service;
      const paymentResult = await unifiedPaymentService.processPayment({
        user_id;
        amount: finalAmount,
        currency: finalCurrency);
        payment_method: paymentMethod as any)
        subscription_id;
      })
      const payment = paymentResult.payment;
      if (!payment) {
        throw new Error('Payment processing failed')
      }

      // Update conversion record with transaction ID if conversion occurred;
      if (conversion) {
        await supabase.from('currency_conversions')
          .update({ transaction_id: payment.id })
          .eq('id', conversion.id)
      }

      return {
        payment;
        conversion;
      }
    } catch (error) {
      logger.error('Failed to create international payment',
        'InternationalPaymentService');
        params;
        error as Error)
      )
      return null;
    }
  }

  /**;
   * Get user's payment preferences for a region;
   */
  async getUserPaymentPreferences(user_id: string,
    region_code: string): Promise<UserPaymentPreferences | null>
    try {
      const { data, error  } = await supabase.from('user_payment_preferences')
        .select('*')
        .eq('user_id', user_id)
        .eq('region_code', region_code)
        .single()
      if (error && error.code !== 'PGRST116') {
        // PGRST116 is "not found";
        throw error;
      }

      return data || null;
    } catch (error) {
      logger.error('Failed to get user payment preferences',
        'InternationalPaymentService',
        { user_id, region_code });
        error as Error)
      )
      return null;
    }
  }

  /**;
   * Update user's payment preferences for a region;
   */
  async updateUserPaymentPreferences(
    user_id: string,
    region_code: string,
    preferences: Partial<Omit<UserPaymentPreferences, 'id' | 'user_id' | 'region_code'>>
  ): Promise<UserPaymentPreferences | null>
    try {
      const { data, error  } = await supabase.from('user_payment_preferences')
        .upsert({
          user_id;
          region_code;
          ...preferences;
        })
        .select()
        .single()
      if (error) throw error;
      return data;
    } catch (error) {
      logger.error('Failed to update user payment preferences',
        'InternationalPaymentService',
        { user_id, region_code, preferences });
        error as Error)
      )
      return null;
    }
  }

  /**;
   * Get international compliance information for a region;
   */
  async getInternationalCompliance(region_code: string): Promise<InternationalCompliance | null>
    try {
      const { data, error  } = await supabase.from('international_compliance')
        .select('*')
        .eq('region_code', region_code)
        .eq('is_active', true)
        .single()
      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      return data || null;
    } catch (error) {
      logger.error('Failed to get international compliance',
        'InternationalPaymentService',
        { region_code });
        error as Error)
      )
      return null;
    }
  }

  /**;
   * Get supported currencies for a region;
   */
  async getSupportedCurrencies(region_code: string): Promise<string[]>
    try { const compliance = await this.getInternationalCompliance(region_code)
      return compliance? .supported_currencies || ['USD'] } catch (error) {
      logger.error('Failed to get supported currencies';
        'InternationalPaymentService',
        { region_code });
        error as Error)
      )
      return ['USD'];
    }
  }

  /**;
   * Calculate total payment amount including fees and taxes;
   */
  async calculateTotalPaymentAmount(amount   : number
    currency: string
    region_code: string,
    payment_method_id?: string): Promise<{ base_amount: number,
    processing_fee: number,
    tax_amount: number,
    total_amount: number,
    currency: string }>
    try {
      let processingFee = 0;
      let taxAmount = 0;
      // Get processing fee from payment method;
      if (payment_method_id) {
        const { data: methodData, error: methodError  } = await supabase.from('regional_payment_methods')
          .select('processing_fee_percentage, processing_fee_fixed')
          .eq('id', payment_method_id)
          .single()
        if (!methodError && methodData) {
          processingFee =
            amount * methodData.processing_fee_percentage + methodData.processing_fee_fixed;
        }
      }

      // Get tax rate from compliance;
      const compliance = await this.getInternationalCompliance(region_code)
      if (compliance) {
        taxAmount = amount * compliance.tax_rate;
      }

      const totalAmount = amount + processingFee + taxAmount;
      return {
        base_amount: amount;
        processing_fee: processingFee,
        tax_amount: taxAmount,
        total_amount: totalAmount,
        currency;
      }
    } catch (error) {
      logger.error('Failed to calculate total payment amount',
        'InternationalPaymentService',
        { amount, currency, region_code, payment_method_id });
        error as Error)
      )
      // Return basic calculation on error;
      return {
        base_amount: amount;
        processing_fee: 0,
        tax_amount: 0,
        total_amount: amount,
        currency;
      }
    }
  }

  /**;
   * Get currency conversion history for a user;
   */
  async getCurrencyConversionHistory(user_id: string,
    limit: number = 50): Promise<CurrencyConversion[]>
    try {
      const { data, error  } = await supabase.from('currency_conversions')
        .select(`)
          *;
          wallet_transactions!inner(user_id)
        `;
        )
        .eq('wallet_transactions.user_id', user_id)
        .order('created_at', { ascending: false })
        .limit(limit)
      if (error) throw error;
      return data || [];
    } catch (error) {
      logger.error('Failed to get currency conversion history',
        'InternationalPaymentService',
        { user_id, limit });
        error as Error)
      )
      return [];
    }
  }

  /**;
   * Update exchange rates (typically called by a scheduled job)
   */
  async updateExchangeRates(
    rates: Array<{
      base_currency: string,
      target_currency: string,
      exchange_rate: number,
      rate_source: 'api' | 'manual' | 'bank'
    }>
  ): Promise<boolean>
    try {
      const { error  } = await supabase.from('currency_exchange_rates').upsert(
        rates.map(rate => ({ ...rate;
          rate_date: new Date().toISOString().split('T')[0],
          is_active: true })),
        { onConflict: 'base_currency,target_currency,rate_date',
          ignoreDuplicates: false }
      )
      if (error) throw error;
      logger.info('Exchange rates updated successfully', 'InternationalPaymentService', {
        count: rates.length)
      })
      return true;
    } catch (error) {
      logger.error('Failed to update exchange rates',
        'InternationalPaymentService',
        { rates });
        error as Error)
      )
      return false;
    }
  }
}

// Export singleton instance;
export const internationalPaymentService = new InternationalPaymentService()