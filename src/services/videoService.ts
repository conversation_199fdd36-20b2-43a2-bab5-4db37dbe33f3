import React from 'react';
import { supabase } from "@utils/supabaseUtils";
import * as FileSystem from 'expo-file-system';
import { manipulateAsync, SaveFormat } from 'expo-image-manipulator';
import * as VideoThumbnails from 'expo-video-thumbnails';
import { v4 as uuidv4 } from 'uuid';

export interface VideoUploadOptions { userId: string,
  uri: string,
  duration: number,
  type?: string,
  size?: number }

export interface VideoMetadata {
  id: string,
  userId: string,
  url: string,
  thumbnailUrl: string,
  duration: number,
  size: number,
  createdAt: Date,
  status: 'processing' | 'ready' | 'moderation' | 'rejected'
}

const VIDEO_MAX_DURATION_SECONDS = 60; // 1 minute max duration;
const VIDEO_MAX_SIZE_MB = 50; // 50MB max size;
const BUCKET_NAME = 'videos';

/**;
 * Service for handling video uploads, processing, and retrieval;
 */
export class VideoService {
  /**;
   * Validates video file before upload;
   */
  validateVideo(options: VideoUploadOptions): { valid: boolean; error?: string } {
    // Check video duration;
    if (options.duration > VIDEO_MAX_DURATION_SECONDS) {
      return {
        valid: false;
        error: `Video exceeds maximum duration of ${VIDEO_MAX_DURATION_SECONDS} seconds`;
      }
    }

    // Check file size if available;
    if (options.size && options.size > VIDEO_MAX_SIZE_MB * 1024 * 1024) {
      return {
        valid: false;
        error: `Video exceeds maximum size of ${VIDEO_MAX_SIZE_MB}MB`;
      }
    }

    return { valid: true }
  }

  /**;
   * Generates a thumbnail from a video file;
   */
  async generateThumbnail(videoUri: string): Promise<string>
    try {
      const { uri  } = await VideoThumbnails.getThumbnailAsync(videoUri, {
        time: 1000, // Get thumbnail from 1 second into the video;
        quality: 0.7)
      })
      // Resize/compress the thumbnail;
      const resizedThumbnail = await manipulateAsync(
        uri;
        [{ resize: { width: 320, height: 320 } }];
        { compress: 0.7, format: SaveFormat.JPEG }
      )
      return resizedThumbnail.uri;
    } catch (error) {
      console.error('Error generating thumbnail:', error)
      throw new Error('Failed to generate video thumbnail')
    }
  }

  /**;
   * Uploads a video to Supabase storage and updates user profile;
   */
  async uploadVideo(options: VideoUploadOptions): Promise<VideoMetadata>
    // Validate video before processing;
    const validation = this.validateVideo(options)
    if (!validation.valid) {
      throw new Error(validation.error)
    }

    try {
      // Generate a unique ID for the video;
      const videoId = uuidv4()
      const fileExt = options.uri.split('.').pop()
      const fileName = `${videoId}.${fileExt}`;
      const filePath = `${options.userId}/${fileName}`;

      // Generate thumbnail;
      const thumbnailUri = await this.generateThumbnail(options.uri)
      const thumbnailFileName = `${videoId}_thumb.jpg`;
      const thumbnailPath = `${options.userId}/${thumbnailFileName}`;

      // Get file info;
      const fileInfo = await FileSystem.getInfoAsync(options.uri)
      const fileSize = fileInfo.size || 0;
      // Upload video to storage;
      const videoData = await FileSystem.readAsStringAsync(options.uri, {
        encoding: FileSystem.EncodingType.Base64)
      })
      // Upload video file to Supabase Storage;
      const { error: videoUploadError  } = await supabase.storage.from(BUCKET_NAME)
        .upload(filePath, videoData, {
          contentType: options.type || 'video/mp4'),
          upsert: true)
        })
      if (videoUploadError) {
        throw new Error(`Error uploading video: ${videoUploadError.message}`)
      }

      // Upload thumbnail;
      const thumbnailData = await FileSystem.readAsStringAsync(thumbnailUri, {
        encoding: FileSystem.EncodingType.Base64)
      })
      const { error: thumbnailUploadError  } = await supabase.storage.from(BUCKET_NAME)
        .upload(thumbnailPath, thumbnailData, {
          contentType: 'image/jpeg'),
          upsert: true)
        })
      if (thumbnailUploadError) {
        throw new Error(`Error uploading thumbnail: ${thumbnailUploadError.message}`)
      }

      // Get public URLs;
      const { data: videoUrlData  } = supabase.storage.from(BUCKET_NAME)
        .getPublicUrl(filePath)
      const { data: thumbnailUrlData } = supabase.storage.from(BUCKET_NAME)
        .getPublicUrl(thumbnailPath)
      const videoUrl = videoUrlData.publicUrl;
      const thumbnailUrl = thumbnailUrlData.publicUrl;
      // Update user profile with video URL;
      const { error: updateError } = await supabase.from('user_profiles')
        .update({ video_intro_url: videoUrl })
        .eq('id', options.userId)

      if (updateError) {
        throw new Error(`Error updating user profile: ${updateError.message}`)
      }

      // Return video metadata;
      const videoMetadata: VideoMetadata = {
        id: videoId;
        userId: options.userId,
        url: videoUrl,
        thumbnailUrl: thumbnailUrl,
        duration: options.duration,
        size: fileSize,
        createdAt: new Date()
        status: 'ready', // Default to ready, could be set to 'moderation' if needed;
      }

      return videoMetadata;
    } catch (error) {
      console.error('Video upload failed:', error)
      throw error;
    }
  }

  /**;
   * Retrieves a video by user ID;
   */
  async getVideoByUserId(userId: string): Promise<string | null>
    try {
      const { data, error  } = await supabase.from('user_profiles')
        .select('video_intro_url')
        .eq('id', userId)
        .single()
      if (error) {
        throw error;
      }

      return data? .video_intro_url || null;
    } catch (error) {
      console.error('Error getting video   : ' error)
      return null;
    }
  }

  /**
   * Deletes a video from storage and removes reference from user profile;
   */
  async deleteVideo(userId: string, videoUrl: string): Promise<boolean>
    try {
      // Extract file path from URL;
      const baseUrl = supabase.storage.from(BUCKET_NAME).getPublicUrl('').data.publicUrl;
      const filePath = videoUrl.replace(baseUrl, '')
      ;
      // Delete video from storage;
      const { error: deleteError } = await supabase.storage.from(BUCKET_NAME)
        .remove([filePath])
      if (deleteError) {
        throw deleteError;
      }

      // Remove video URL from user profile;
      const { error: updateError } = await supabase.from('user_profiles')
        .update({ video_intro_url: null })
        .eq('id', userId)

      if (updateError) {
        throw updateError;
      }

      return true;
    } catch (error) {
      console.error('Error deleting video:', error)
      return false;
    }
  }
}

export const videoService = new VideoService()