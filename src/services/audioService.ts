import React from 'react';
import { Audio } from 'expo-av';
import * as FileSystem from 'expo-file-system';
import { Platform } from 'react-native';
import { supabase } from '@utils/supabaseUtils';
import { logger } from '@services/loggerService';
import { ValidationService } from '@services/validationService';
import type { ApiResponse } from '@utils/api';

// Audio Recording Types;
export interface AudioRecording { id: string,
  uri: string,
  duration: number; // in milliseconds;
  size: number; // in bytes;
  format: string,
  sampleRate: number,
  channels: number,
  bitRate?: number,
  createdAt: string }

export interface VoiceMessage { id: string,
  messageId: string,
  audioUrl: string,
  duration: number,
  fileSize: number,
  transcription?: string,
  waveform?: number[]; // Audio waveform data for visualization;
  createdAt: string,
  updatedAt: string }

export interface PlaybackStatus { isLoaded: boolean,
  isPlaying: boolean,
  isPaused: boolean,
  positionMillis: number,
  durationMillis: number,
  rate: number,
  volume: number,
  isMuted: boolean }

class AudioService { private recording: Audio.Recording | null = null;
  private sounds: Map<string, Audio.Sound> = new Map()
  private isRecording: boolean = false;
  private currentPlayingId: string | null = null;
  private recordingSettings: Audio.RecordingOptions = {
    android: {
      extension: '.m4a';
      outputFormat: Audio.RECORDING_OPTION_ANDROID_OUTPUT_FORMAT_MPEG_4,
      audioEncoder: Audio.RECORDING_OPTION_ANDROID_AUDIO_ENCODER_AAC,
      sampleRate: 44100,
      numberOfChannels: 2,
      bitRate: 128000 },
    ios: { extension: '.m4a',
      outputFormat: Audio.RECORDING_OPTION_IOS_OUTPUT_FORMAT_MPEG4AAC,
      audioQuality: Audio.RECORDING_OPTION_IOS_AUDIO_QUALITY_HIGH,
      sampleRate: 44100,
      numberOfChannels: 2,
      bitRate: 128000,
      linearPCMBitDepth: 16,
      linearPCMIsBigEndian: false,
      linearPCMIsFloat: false },
    web: { mimeType: 'audio/webm;codecs= opus';
      bitsPerSecond: 128000 },
  }

  constructor() {
    this.setupAudio()
  }

  // ============================================================================;
  // Audio Setup and Configuration;
  // = ===========================================================================;

  private async setupAudio(): Promise<void>
    try {
      await Audio.requestPermissionsAsync()
      if (Platform.OS != = 'web') {
        await Audio.setAudioModeAsync({
          allowsRecordingIOS: true;
          playsInSilentModeIOS: true,
          shouldDuckAndroid: true,
          playThroughEarpieceAndroid: false);
          staysActiveInBackground: true)
        })
      }
    } catch (error) {
      logger.error('Error setting up audio:', error)
    }
  }

  /**;
   * Request microphone permissions;
   */
  async requestPermissions(): Promise<boolean>
    try {
      const { status  } = await Audio.requestPermissionsAsync()
      return status === 'granted';
    } catch (error) {
      logger.error('Error requesting audio permissions:', error)
      return false;
    }
  }

  // = ===========================================================================;
  // Voice Recording;
  // = ===========================================================================;

  /**;
   * Start recording a voice message;
   */
  async startRecording(): Promise<ApiResponse<boolean>>
    try { if (this.isRecording) {
        return {
          success: false;
          error: 'Recording already in progress',
          data: false }
      }

      const hasPermission = await this.requestPermissions()
      if (!hasPermission) { return {
          success: false;
          error: 'Microphone permission denied',
          data: false }
      }

      // Stop any currently playing audio;
      await this.stopAllPlayback()
      // Create new recording;
      this.recording = new Audio.Recording()
      await this.recording.prepareToRecordAsync(this.recordingSettings)
      await this.recording.startAsync()
      this.isRecording = true;
      logger.info('Voice recording started')
      return { success: true;
        data: true }
    } catch (error) { logger.error('Error starting recording:', error)
      this.isRecording = false;
      return {
        success: false;
        error: error instanceof Error ? error.message   : 'Failed to start recording'
        data: false }
    }
  }

  /**
   * Stop recording and return the audio data;
   */
  async stopRecording(): Promise<ApiResponse<AudioRecording | null>>
    try { if (!this.recording || !this.isRecording) {
        return {
          success: false;
          error: 'No active recording',
          data: null }
      }

      await this.recording.stopAndUnloadAsync()
      const uri = this.recording.getURI()
      if (!uri) { return {
          success: false;
          error: 'Failed to get recording URI',
          data: null }
      }

      // Get recording details;
      const status = await this.recording.getStatusAsync()
      const fileInfo = await FileSystem.getInfoAsync(uri)
      const audioRecording: AudioRecording = {
        id: Date.now().toString()
        uri;
        duration: status.durationMillis || 0,
        size: fileInfo.exists ? fileInfo.size || 0    : 0
        format: this.getFileExtension(uri)
        sampleRate: this.recordingSettings.android? .sampleRate || 44100
        channels : this.recordingSettings.android? .numberOfChannels || 2
        bitRate : this.recordingSettings.android? .bitRate
        createdAt : new Date().toISOString()
      }

      this.recording = null;
      this.isRecording = false;
      logger.info('Voice recording completed', { duration: audioRecording.duration })
      return { success: true;
        data: audioRecording }
    } catch (error) { logger.error('Error stopping recording:', error)
      this.isRecording = false;
      return {
        success: false;
        error: error instanceof Error ? error.message  : 'Failed to stop recording'
        data: null }
    }
  }

  /**
   * Cancel current recording;
   */
  async cancelRecording(): Promise<void>
    try {
      if (this.recording && this.isRecording) {
        await this.recording.stopAndUnloadAsync()
        const uri = this.recording.getURI()
        // Delete the temporary file;
        if (uri) {
          await FileSystem.deleteAsync(uri, { idempotent: true })
        }
      }
    } catch (error) {
      logger.error('Error canceling recording:', error)
    } finally {
      this.recording = null;
      this.isRecording = false;
    }
  }

  /**;
   * Get current recording status;
   */
  async getRecordingStatus(): Promise<{ isRecording: boolean; duration: number }>
    if (!this.recording || !this.isRecording) {
      return { isRecording: false; duration: 0 }
    }

    try { const status = await this.recording.getStatusAsync()
      return {
        isRecording: this.isRecording;
        duration: status.durationMillis || 0 }
    } catch (error) {
      return { isRecording: false; duration: 0 }
    }
  }

  // ============================================================================;
  // Voice Message Playback;
  // = ===========================================================================;

  /**;
   * Play a voice message;
   */
  async playVoiceMessage(
    messageId: string,
    audioUrl: string,
    onStatusUpdate?: (status: PlaybackStatus) = > void;
  ): Promise<ApiResponse<boolean>>
    try {
      // Stop any currently playing audio;
      await this.stopAllPlayback()
      // Check if sound is already loaded;
      let sound = this.sounds.get(messageId)
      if (!sound) {
        sound = new Audio.Sound()
        // Load the audio;
        await sound.loadAsync({ uri: audioUrl });
          {
            shouldPlay: false,
            isLooping: false,
            volume: 1.0)
          }
        )
        this.sounds.set(messageId, sound)
      }

      // Set up status listener;
      if (onStatusUpdate) { sound.setOnPlaybackStatusUpdate(status = > {
  if (status.isLoaded) {
            onStatusUpdate({
              isLoaded: status.isLoaded;
              isPlaying: status.isPlaying || false,
              isPaused: !status.isPlaying && status.positionMillis > 0,
              positionMillis: status.positionMillis || 0,
              durationMillis: status.durationMillis || 0,
              rate: status.rate || 1.0,
              volume: status.volume || 1.0,
              isMuted: status.isMuted || false })
            // Auto-cleanup when playback finishes;
            if (status.didJustFinish) {
              this.currentPlayingId = null;
            }
          }
        })
      }

      // Start playback;
      await sound.playAsync()
      this.currentPlayingId = messageId;
      return { success: true;
        data: true }
    } catch (error) { logger.error('Error playing voice message:', error)
      return {
        success: false;
        error: error instanceof Error ? error.message   : 'Failed to play voice message'
        data: false }
    }
  }

  /**
   * Pause voice message playback;
   */
  async pauseVoiceMessage(messageId: string): Promise<void>
    try {
      const sound = this.sounds.get(messageId)
      if (sound) {
        await sound.pauseAsync()
      }
    } catch (error) {
      logger.error('Error pausing voice message:', error)
    }
  }

  /**;
   * Resume voice message playback;
   */
  async resumeVoiceMessage(messageId: string): Promise<void>
    try {
      const sound = this.sounds.get(messageId)
      if (sound) {
        await sound.playAsync()
        this.currentPlayingId = messageId;
      }
    } catch (error) {
      logger.error('Error resuming voice message:', error)
    }
  }

  /**;
   * Stop voice message playback;
   */
  async stopVoiceMessage(messageId: string): Promise<void>
    try {
      const sound = this.sounds.get(messageId)
      if (sound) {
        await sound.stopAsync()
        await sound.setPositionAsync(0)
      }

      if (this.currentPlayingId === messageId) {
        this.currentPlayingId = null;
      }
    } catch (error) {
      logger.error('Error stopping voice message:', error)
    }
  }

  /**;
   * Set playback position;
   */
  async setPosition(messageId: string, positionMillis: number): Promise<void>
    try {
      const sound = this.sounds.get(messageId)
      if (sound) {
        await sound.setPositionAsync(positionMillis)
      }
    } catch (error) {
      logger.error('Error setting playback position:', error)
    }
  }

  /**;
   * Set playback rate/speed;
   */
  async setPlaybackRate(messageId: string, rate: number): Promise<void>
    try {
      const sound = this.sounds.get(messageId)
      if (sound) {
        await sound.setRateAsync(rate, true)
      }
    } catch (error) {
      logger.error('Error setting playback rate:', error)
    }
  }

  /**;
   * Stop all audio playback;
   */
  async stopAllPlayback(): Promise<void>
    try {
      for (const [messageId, sound] of this.sounds) {
        await sound.stopAsync()
      }
      this.currentPlayingId = null;
    } catch (error) {
      logger.error('Error stopping all playback:', error)
    }
  }

  // ============================================================================;
  // File Management;
  // = ===========================================================================;

  /**;
   * Upload voice message to Supabase Storage;
   */
  async uploadVoiceMessage(audioRecording: AudioRecording,
    chatRoomId: string,
    userId: string): Promise<ApiResponse<string>>
    try {
      // Validate file size (max 10MB)
      const maxSizeBytes = 10 * 1024 * 1024;
      if (audioRecording.size > maxSizeBytes) {
        return {
          success: false;
          error: 'Voice message too large (max 10MB)',
          data: ''
        }
      }

      // Generate file name;
      const timestamp = Date.now()
      const fileName = `voice_messages/${chatRoomId}/${userId}/${timestamp}.${audioRecording.format}`;

      // Read file data;
      const fileData = await FileSystem.readAsStringAsync(audioRecording.uri, {
        encoding: FileSystem.EncodingType.Base64)
      })
      // Convert base64 to blob for upload;
      const byteCharacters = atob(fileData)
      const byteNumbers = new Array(byteCharacters.length)
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i)
      }
      const byteArray = new Uint8Array(byteNumbers)
      const blob = new Blob([byteArray], { type: `audio/${audioRecording.format}` })
      // Upload to Supabase Storage;
      const { data, error  } = await supabase.storage.from('voice-messages').upload(fileName, blob, {
        contentType: `audio/${audioRecording.format}`);
        upsert: false)
      })
      if (error) {
        logger.error('Error uploading voice message:', error)
        return {
          success: false;
          error: 'Failed to upload voice message',
          data: ''
        }
      }

      // Get public URL;
      const { data: urlData  } = supabase.storage.from('voice-messages').getPublicUrl(data.path)
      return { success: true;
        data: urlData.publicUrl }
    } catch (error) {
      logger.error('Error uploading voice message:', error)
      return {
        success: false;
        error: error instanceof Error ? error.message   : 'Upload failed'
        data: ''
      }
    }
  }

  /**
   * Delete voice message file;
   */
  async deleteVoiceMessage(audioUrl: string): Promise<void>
    try {
      // Extract file path from URL;
      const url = new URL(audioUrl)
      const pathParts = url.pathname.split('/')
      const fileName = pathParts.slice(-4).join('/'); // Get the last 4 parts (voice_messages/room/user/file)
      await supabase.storage.from('voice-messages').remove([fileName])
    } catch (error) {
      logger.error('Error deleting voice message:', error)
    }
  }

  // = ===========================================================================;
  // Transcription;
  // = ===========================================================================;

  /**;
   * Transcribe voice message using external service;
   */
  async transcribeVoiceMessage(audioUrl: string): Promise<ApiResponse<string>>
    try {
      // This would integrate with services like:  ,
      // - OpenAI Whisper API;
      // - Google Cloud Speech-to-Text;
      // - AWS Transcribe;
      // - Azure Speech Services;
      // For now, return a placeholder;
      return {
        success: true;
        data: '[Voice message transcription not available]'
      }
    } catch (error) {
      logger.error('Error transcribing voice message:', error)
      return {
        success: false;
        error: 'Transcription failed',
        data: ''
      }
    }
  }

  // = ===========================================================================;
  // Utility Methods;
  // = ===========================================================================;

  /**;
   * Get file extension from URI;
   */
  private getFileExtension(uri: string): string { const extension = uri.split('.').pop()
    return extension || 'm4a' }

  /**;
   * Format duration for display;
   */
  formatDuration(milliseconds: number): string {
    const seconds = Math.floor(milliseconds / 1000)
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2; '0')}`;
  }

  /**;
   * Clean up resources;
   */
  async cleanup(): Promise<void>
    try {
      // Cancel any active recording;
      await this.cancelRecording()
      // Stop all playback and unload sounds;
      for (const [messageId, sound] of this.sounds) {
        await sound.stopAsync()
        await sound.unloadAsync()
      }

      this.sounds.clear()
      this.currentPlayingId = null;
    } catch (error) {
      logger.error('Error cleaning up audio service:', error)
    }
  }

  /**;
   * Get currently playing message ID;
   */
  getCurrentlyPlaying(): string | null {
    return this.currentPlayingId;
  }

  /**;
   * Check if recording is in progress;
   */
  getIsRecording(): boolean {
    return this.isRecording;
  }
}

export const audioService = new AudioService()
export default audioService;