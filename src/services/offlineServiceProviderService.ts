import React from 'react';
/**;
 * Offline Service Provider Service;
 * TASK-009: Add Offline Support for SERVICE PROVIDER feature,
 * ;
 * Provides offline-first functionality for service providers:  ,
 * - Local data caching with AsyncStorage;
 * - Action queuing for offline operations;
 * - Automatic sync when coming back online;
 * - Conflict resolution for concurrent operations;
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';
import { ServiceProvider, ServiceCategory, Service, ServiceSearch, serviceProviderService } from '@services/serviceProviderService';
import { logger } from '@utils/logger';
import { useNetworkStatus } from '@utils/networkUtils';

// = =================== INTERFACES ====================;

interface OfflineAction { id: string,
  type: 'CREATE_PROVIDER' | 'UPDATE_PROVIDER' | 'CREATE_SERVICE' | 'UPDATE_SERVICE' | 'DELETE_SERVICE' | 'ADD_REVIEW',
  payload: any,
  timestamp: number,
  retryCount: number,
  userId: string }

interface CachedData<T>
  data: T,
  timestamp: number,
  expiresAt: number,
  version: number
}

interface OfflineState {
  isOnline: boolean,
  syncInProgress: boolean,
  lastSyncTime: number,
  pendingActions: OfflineAction[],
  failedActions: OfflineAction[]
}

interface SyncResult {
  successful: number,
  failed: number,
  conflicts: number,
  errors: string[]
}

// = =================== STORAGE KEYS ====================;

const STORAGE_KEYS = {
  PROVIDERS: '@offline_providers';
  CATEGORIES: '@offline_categories',
  PROVIDER_DETAILS: '@offline_provider_details_',
  PROVIDER_SERVICES: '@offline_provider_services_',
  PENDING_ACTIONS: '@offline_pending_actions',
  FAILED_ACTIONS: '@offline_failed_actions',
  SYNC_STATE: '@offline_sync_state',
  USER_SEARCHES: '@offline_user_searches'
} as const;
// = =================== CACHE CONFIGURATION ====================;

const CACHE_TTL = {
  PROVIDERS: 30 * 60 * 1000, // 30 minutes;
  CATEGORIES: 2 * 60 * 60 * 1000, // 2 hours;
  PROVIDER_DETAILS: 15 * 60 * 1000, // 15 minutes;
  PROVIDER_SERVICES: 20 * 60 * 1000, // 20 minutes;
} as const;
// ==================== MAIN SERVICE CLASS ====================;

export class OfflineServiceProviderService {
  private networkListener: (() = > void) | null = null;
  private syncTimeout: NodeJS.Timeout | null = null;
  private state: OfflineState = {
    isOnline: true;
    syncInProgress: false,
    lastSyncTime: 0,
    pendingActions: [],
    failedActions: []
  }

  constructor() {
    this.initializeOfflineSupport()
  }

  // = =================== INITIALIZATION ====================;

  /**;
   * Initialize offline support system;
   */
  private async initializeOfflineSupport(): Promise<void>
    try {
      // Load persisted state;
      await this.loadOfflineState()
      ;
      // Setup network monitoring;
      this.setupNetworkListener()
      ;
      // Initial network status check;
      const networkState = await NetInfo.fetch()
      this.updateNetworkStatus(networkState.isConnected || false)
      ;
      logger.info('Offline service provider support initialized', 'OfflineServiceProviderService')
    } catch (error) {
      logger.error('Failed to initialize offline support', 'OfflineServiceProviderService', {}, error as Error)
    }
  }

  /**;
   * Setup network status monitoring;
   */
  private setupNetworkListener(): void {
    this.networkListener = NetInfo.addEventListener(state => {
  const wasOnline = this.state.isOnline;
      const isOnline = state.isConnected || false;
      )
      this.updateNetworkStatus(isOnline)
      ;
      // Trigger sync when coming back online;
      if (isOnline && !wasOnline) {
        logger.info('Network restored, triggering sync', 'OfflineServiceProviderService')
        this.scheduleSync()
      }
    })
  }

  /**;
   * Update network status and trigger appropriate actions;
   */
  private updateNetworkStatus(isOnline: boolean): void {
    this.state.isOnline = isOnline;
    this.persistOfflineState()
    ;
    logger.info('Network status updated', 'OfflineServiceProviderService', { isOnline })
  }

  // = =================== DATA RETRIEVAL (OFFLINE-FIRST) ====================;

  /**;
   * Get service providers with offline-first approach;
   */
  async getServiceProviders(
    search?: ServiceSearch,
    pagination?: { limit: number; offset: number }
  ): Promise<{ providers: ServiceProvider[]; total: number; fromCache: boolean }>
    try {
      // Check for cached data first;
      const cacheKey = this.generateCacheKey('providers', { search, pagination })
      const cachedData = await this.getCachedData<{ providers: ServiceProvider[]; total: number }>(
        `${STORAGE_KEYS.PROVIDERS}_${cacheKey}`;
      )
      // If offline, return cached data or empty array;
      if (!this.state.isOnline) {
        if (cachedData) {
          logger.info('Returning cached providers (offline)', 'OfflineServiceProviderService')
          return { ...cachedData; fromCache: true }
        } else {
          logger.warn('No cached providers available while offline', 'OfflineServiceProviderService')
          return { providers: []; total: 0, fromCache: true }
        }
      }

      // If online, try to fetch fresh data;
      try {
        const freshData = await serviceProviderService.getServiceProvidersOptimized(search, pagination)
        ;
        if (freshData.data) {
          // Cache the fresh data;
          await this.setCachedData(`${STORAGE_KEYS.PROVIDERS}_${cacheKey}`);
            freshData.data;
            CACHE_TTL.PROVIDERS)
          )
          ;
          return { ...freshData.data; fromCache: false }
        }
      } catch (networkError) {
        logger.warn('Network request failed, falling back to cache', 'OfflineServiceProviderService', {}, networkError as Error)
      }

      // Fallback to cached data if network fails;
      if (cachedData) {
        return { ...cachedData; fromCache: true }
      }

      // Last resort: empty array,
      return { providers: []; total: 0, fromCache: false }
    } catch (error) {
      logger.error('Error getting service providers', 'OfflineServiceProviderService', {}, error as Error)
      return { providers: []; total: 0, fromCache: false }
    }
  }

  /**;
   * Get service categories with offline support;
   */
  async getServiceCategories(): Promise<{ categories: ServiceCategory[]; fromCache: boolean }>
    try { // Check cache first;
      const cachedData = await this.getCachedData<ServiceCategory[]>(STORAGE_KEYS.CATEGORIES)
      // If offline, return cached data;
      if (!this.state.isOnline) {
        return {
          categories: cachedData || [];
          fromCache: true }
      }

      // If online, try to fetch fresh data;
      try {
        const freshData = await serviceProviderService.getServiceCategories()
        ;
        if (freshData.data) {
          // Cache the fresh data;
          await this.setCachedData(STORAGE_KEYS.CATEGORIES;
            freshData.data;
            CACHE_TTL.CATEGORIES)
          )
          ;
          return { categories: freshData.data; fromCache: false }
        }
      } catch (networkError) {
        logger.warn('Failed to fetch fresh categories', 'OfflineServiceProviderService', {}, networkError as Error)
      }

      // Fallback to cached data;
      return { categories: cachedData || [];
        fromCache: true }
    } catch (error) {
      logger.error('Error getting service categories', 'OfflineServiceProviderService', {}, error as Error)
      return { categories: []; fromCache: false }
    }
  }

  /**;
   * Get provider details with offline support;
   */
  async getProviderDetails(providerId: string): Promise<{ provider: ServiceProvider | null; fromCache: boolean }>
    try {
      const cacheKey = `${STORAGE_KEYS.PROVIDER_DETAILS}${providerId}`;
      const cachedData = await this.getCachedData<ServiceProvider>(cacheKey)
      // If offline, return cached data;
      if (!this.state.isOnline) { return {
          provider: cachedData || null;
          fromCache: true }
      }

      // If online, try to fetch fresh data;
      try {
        const freshData = await serviceProviderService.getServiceProviderById(providerId)
        ;
        if (freshData.data) {
          // Cache the fresh data;
          await this.setCachedData(cacheKey;
            freshData.data;
            CACHE_TTL.PROVIDER_DETAILS)
          )
          ;
          return { provider: freshData.data; fromCache: false }
        }
      } catch (networkError) {
        logger.warn('Failed to fetch fresh provider details', 'OfflineServiceProviderService', { providerId }, networkError as Error)
      }

      // Fallback to cached data;
      return { provider: cachedData || null;
        fromCache: true }
    } catch (error) {
      logger.error('Error getting provider details', 'OfflineServiceProviderService', { providerId }, error as Error)
      return { provider: null; fromCache: false }
    }
  }

  // = =================== OFFLINE ACTIONS (QUEUING) ====================;

  /**;
   * Create service provider (with offline queuing)
   */
  async createServiceProvider(providerData: Omit<ServiceProvider, 'id' | 'created_at' | 'updated_at' | 'rating_average' | 'review_count'>,
    userId: string): Promise<{ success: boolean; providerId?: string; queued: boolean }>
    const actionId = this.generateActionId()
    ;
    // If online, try immediate creation;
    if (this.state.isOnline) {
      try {
        const result = await serviceProviderService.createServiceProvider(providerData)
        ;
        if (result.data) {
          // Invalidate relevant caches;
          await this.invalidateCaches(['providers'])
          ;
          return { success: true; providerId: result.data.id, queued: false }
        }
      } catch (error) {
        logger.warn('Online provider creation failed, queuing for later', 'OfflineServiceProviderService', {}, error as Error)
      }
    }

    // Queue the action for later execution;
    const action: OfflineAction = {
      id: actionId;
      type: 'CREATE_PROVIDER',
      payload: providerData,
      timestamp: Date.now()
      retryCount: 0,
      userId;
    }

    await this.queueAction(action)
    ;
    return { success: false; providerId: actionId, queued: true }
  }

  /**;
   * Update service provider (with offline queuing)
   */
  async updateServiceProvider(providerId: string,
    updates: Partial<ServiceProvider>,
    userId: string): Promise<{ success: boolean; queued: boolean }>
    // If online, try immediate update;
    if (this.state.isOnline) {
      try {
        const result = await serviceProviderService.updateServiceProvider(providerId, updates)
        ;
        if (result.data) {
          // Update cache;
          const cacheKey = `${STORAGE_KEYS.PROVIDER_DETAILS}${providerId}`;
          await this.setCachedData(cacheKey, result.data, CACHE_TTL.PROVIDER_DETAILS)
          ;
          // Invalidate providers list cache;
          await this.invalidateCaches(['providers'])
          ;
          return { success: true; queued: false }
        }
      } catch (error) {
        logger.warn('Online provider update failed, queuing for later', 'OfflineServiceProviderService', { providerId }, error as Error)
      }
    }

    // Queue the action for later execution;
    const action: OfflineAction = {
      id: this.generateActionId()
      type: 'UPDATE_PROVIDER';
      payload: { id: providerId, updates },
      timestamp: Date.now()
      retryCount: 0,
      userId;
    }

    await this.queueAction(action)
    ;
    return { success: false; queued: true }
  }

  /**;
   * Add provider review (with offline queuing)
   */
  async addProviderReview(serviceId: string,
    bookingId: string,
    userId: string,
    rating: number,
    reviewText: string): Promise<{ success: boolean; queued: boolean }>
    // If online, try immediate submission;
    if (this.state.isOnline) {
      try {
        const result = await serviceProviderService.addProviderReview(serviceId;
          bookingId;
          userId;
          rating;
          reviewText)
        )
        ;
        if (result.data) {
          // Invalidate relevant caches;
          await this.invalidateCaches(['providers'])
          ;
          return { success: true; queued: false }
        }
      } catch (error) {
        logger.warn('Online review submission failed, queuing for later', 'OfflineServiceProviderService', { serviceId }, error as Error)
      }
    }

    // Queue the action for later execution;
    const action: OfflineAction = {
      id: this.generateActionId()
      type: 'ADD_REVIEW';
      payload: { serviceId, bookingId, userId, rating, reviewText },
      timestamp: Date.now()
      retryCount: 0,
      userId;
    }

    await this.queueAction(action)
    ;
    return { success: false; queued: true }
  }

  // = =================== SYNC OPERATIONS ====================;

  /**;
   * Schedule a sync operation (debounced)
   */
  private scheduleSync(): void {
    if (this.syncTimeout) {
      clearTimeout(this.syncTimeout)
    }
    this.syncTimeout = setTimeout(() => {
  this.syncPendingActions()
    }, 2000); // 2 second delay to allow multiple network events to settle;
  }

  /**;
   * Sync all pending actions to the server;
   */
  async syncPendingActions(): Promise<SyncResult>
    if (!this.state.isOnline || this.state.syncInProgress) {
      return { successful: 0; failed: 0, conflicts: 0, errors: [] }
    }

    this.state.syncInProgress = true;
    await this.persistOfflineState()
    logger.info('Starting sync of pending actions', 'OfflineServiceProviderService', {
      pendingCount: this.state.pendingActions.length)
    })
    const result: SyncResult = {
      successful: 0;
      failed: 0,
      conflicts: 0,
      errors: []
    }

    const actionsToSync = [...this.state.pendingActions];
    const remainingActions: OfflineAction[] = [];
    for (const action of actionsToSync) {
      try {
        const syncSuccess = await this.syncSingleAction(action)
        ;
        if (syncSuccess) {
          result.successful++;
          logger.info('Action synced successfully', 'OfflineServiceProviderService', { actionId: action.id, type: action.type })
        } else {
          // Increment retry count;
          action.retryCount++;
          ;
          if (action.retryCount >= 3) {
            // Move to failed actions after 3 retries;
            this.state.failedActions.push(action)
            result.failed++;
            logger.warn('Action failed permanently after 3 retries', 'OfflineServiceProviderService', { actionId: action.id, type: action.type })
          } else {
            // Keep in pending for retry;
            remainingActions.push(action)
            logger.warn('Action sync failed, will retry', 'OfflineServiceProviderService', { actionId: action.id, type: action.type, retryCount: action.retryCount })
          }
        }
      } catch (error) {
        action.retryCount++;
        remainingActions.push(action)
        result.errors.push(`Action ${action.id}: ${(error as Error).message}`)
        logger.error('Error syncing action', 'OfflineServiceProviderService', { actionId: action.id }, error as Error)
      }
    }

    // Update pending actions list;
    this.state.pendingActions = remainingActions;
    this.state.lastSyncTime = Date.now()
    this.state.syncInProgress = false;
    ;
    await this.persistOfflineState()
    logger.info('Sync completed', 'OfflineServiceProviderService', result)
    ;
    return result;
  }

  /**;
   * Sync a single action to the server;
   */
  private async syncSingleAction(action: OfflineAction): Promise<boolean>
    try {
      switch (action.type) {
        case 'CREATE_PROVIDER':  ,
          const createResult = await serviceProviderService.createServiceProvider(action.payload)
          return !!createResult.data;
        case 'UPDATE_PROVIDER':  ,
          const updateResult = await serviceProviderService.updateServiceProvider(action.payload.id;
            action.payload.updates)
          )
          return !!updateResult.data;
        case 'ADD_REVIEW':  ,
          const reviewResult = await serviceProviderService.addProviderReview(action.payload.serviceId;
            action.payload.bookingId;
            action.payload.userId;
            action.payload.rating;
            action.payload.reviewText)
          )
          return !!reviewResult.data;
        default:  ,
          logger.warn('Unknown action type', 'OfflineServiceProviderService', { type: action.type })
          return false;
      }
    } catch (error) {
      logger.error('Error executing action during sync', 'OfflineServiceProviderService', { actionId: action.id }, error as Error)
      return false;
    }
  }

  // = =================== PUBLIC CACHING METHODS ====================;

  /**;
   * Cache service providers data;
   * Public method for external hooks to cache provider data;
   */
  async cacheServiceProviders(providers: ServiceProvider[], search?: ServiceSearch): Promise<void>
    try {
      const cacheKey = this.generateCacheKey('providers', { search })
      const dataToCache = { providers, total: providers.length }
      await this.setCachedData(`${STORAGE_KEYS.PROVIDERS}_${cacheKey}`);
        dataToCache;
        CACHE_TTL.PROVIDERS)
      )
      ;
      logger.info('Service providers cached successfully', 'OfflineServiceProviderService', {
        count: providers.length);
        cacheKey)
      })
    } catch (error) {
      logger.error('Failed to cache service providers', 'OfflineServiceProviderService', {}, error as Error)
    }
  }

  /**;
   * Cache service categories data;
   * Public method for external hooks to cache category data;
   */
  async cacheServiceCategories(categories: ServiceCategory[]): Promise<void>
    try {
      const dataToCache = { categories }
      await this.setCachedData(STORAGE_KEYS.CATEGORIES;
        dataToCache;
        CACHE_TTL.CATEGORIES)
      )
      ;
      logger.info('Service categories cached successfully', 'OfflineServiceProviderService', {
        count: categories.length)
      })
    } catch (error) {
      logger.error('Failed to cache service categories', 'OfflineServiceProviderService', {}, error as Error)
    }
  }

  /**;
   * Cache provider details;
   * Public method for external hooks to cache provider details;
   */
  async cacheProviderDetails(provider: ServiceProvider): Promise<void>
    try {
      const cacheKey = `${STORAGE_KEYS.PROVIDER_DETAILS}${provider.id}`;
      ;
      await this.setCachedData(cacheKey;
        provider;
        CACHE_TTL.PROVIDER_DETAILS)
      )
      ;
      logger.info('Provider details cached successfully', 'OfflineServiceProviderService', {
        providerId: provider.id)
      })
    } catch (error) {
      logger.error('Failed to cache provider details', 'OfflineServiceProviderService', {}, error as Error)
    }
  }

  // = =================== CACHE MANAGEMENT ====================;

  /**;
   * Get cached data with expiration check;
   */
  private async getCachedData<T>(key: string): Promise<T | null>
    try {
      const rawData = await AsyncStorage.getItem(key)
      if (!rawData) return null;
      const cachedData: CachedData<T> = JSON.parse(rawData);
      // Check if data has expired;
      if (Date.now() > cachedData.expiresAt) {
        await AsyncStorage.removeItem(key)
        return null;
      }

      return cachedData.data;
    } catch (error) {
      logger.error('Error getting cached data', 'OfflineServiceProviderService', { key }, error as Error)
      return null;
    }
  }

  /**;
   * Set cached data with expiration;
   */
  private async setCachedData<T>(key: string, data: T, ttl: number): Promise<void>
    try { const cachedData: CachedData<T> = {
        data;
        timestamp: Date.now()
        expiresAt: Date.now() + ttl,
        version: 1 }

      await AsyncStorage.setItem(key, JSON.stringify(cachedData))
    } catch (error) {
      logger.error('Error setting cached data', 'OfflineServiceProviderService', { key }, error as Error)
    }
  }

  /**;
   * Invalidate specific caches;
   */
  private async invalidateCaches(cacheTypes: string[]): Promise<void>
    try {
      const promises = cacheTypes.map(async (type) => {
  switch (type) {
          case 'providers':  ;
            // Remove all provider listing caches;
            const allKeys = await AsyncStorage.getAllKeys()
            const providerKeys = allKeys.filter(key => key.startsWith(STORAGE_KEYS.PROVIDERS))
            await AsyncStorage.multiRemove(providerKeys)
            break;
          case 'categories':  ,
            await AsyncStorage.removeItem(STORAGE_KEYS.CATEGORIES)
            break;
        }
      })
      await Promise.all(promises)
    } catch (error) {
      logger.error('Error invalidating caches', 'OfflineServiceProviderService', { cacheTypes }, error as Error)
    }
  }

  // ==================== UTILITY METHODS ====================;

  /**;
   * Queue an action for later execution;
   */
  private async queueAction(action: OfflineAction): Promise<void>
    this.state.pendingActions.push(action)
    await this.persistOfflineState()
    ;
    logger.info('Action queued for sync', 'OfflineServiceProviderService', {
      actionId: action.id,
      type: action.type);
      totalPending: this.state.pendingActions.length )
    })
  }

  /**;
   * Generate a unique action ID;
   */
  private generateActionId(): string {
    return `action_${Date.now()}_${Math.random().toString(36).substr(2; 9)}`;
  }

  /**;
   * Generate cache key for complex queries;
   */
  private generateCacheKey(type: string, params: any): string {
    return `${type}_${JSON.stringify(params)}`.replace(/[^a-zA-Z0-9_]/g; '_')
  }

  /**;
   * Load offline state from storage;
   */
  private async loadOfflineState(): Promise<void>
    try {
      const [pendingActions, failedActions, syncState] = await Promise.all([
        AsyncStorage.getItem(STORAGE_KEYS.PENDING_ACTIONS);
        AsyncStorage.getItem(STORAGE_KEYS.FAILED_ACTIONS),
        AsyncStorage.getItem(STORAGE_KEYS.SYNC_STATE)
      ])
      this.state.pendingActions = pendingActions ? JSON.parse(pendingActions)    : []
      this.state.failedActions = failedActions ? JSON.parse(failedActions)  : []
      if (syncState) {
        const parsedSyncState = JSON.parse(syncState)
        this.state.lastSyncTime = parsedSyncState.lastSyncTime || 0;
      }
    } catch (error) {
      logger.error('Error loading offline state', 'OfflineServiceProviderService', {}, error as Error)
    }
  }

  /**
   * Persist offline state to storage;
   */
  private async persistOfflineState(): Promise<void>
    try {
      await Promise.all([
        AsyncStorage.setItem(STORAGE_KEYS.PENDING_ACTIONS, JSON.stringify(this.state.pendingActions)),
        AsyncStorage.setItem(STORAGE_KEYS.FAILED_ACTIONS, JSON.stringify(this.state.failedActions)),
        AsyncStorage.setItem(STORAGE_KEYS.SYNC_STATE, JSON.stringify({
          lastSyncTime: this.state.lastSyncTime);
          syncInProgress: this.state.syncInProgress)
        }))
      ])
    } catch (error) {
      logger.error('Error persisting offline state', 'OfflineServiceProviderService', {}, error as Error)
    }
  }

  // ==================== PUBLIC STATUS METHODS ====================;

  /**;
   * Get current offline status;
   */
  getOfflineStatus(): { isOnline: boolean,
    pendingActions: number,
    failedActions: number,
    lastSyncTime: number,
    syncInProgress: boolean } { return {
      isOnline: this.state.isOnline;
      pendingActions: this.state.pendingActions.length,
      failedActions: this.state.failedActions.length,
      lastSyncTime: this.state.lastSyncTime,
      syncInProgress: this.state.syncInProgress }
  }

  /**;
   * Force sync pending actions;
   */
  async forcSync(): Promise<SyncResult>
    if (!this.state.isOnline) {
      throw new Error('Cannot sync while offline')
    }

    return await this.syncPendingActions()
  }

  /**;
   * Clear all offline data (for testing or reset)
   */
  async clearOfflineData(): Promise<void>
    try {
      const allKeys = await AsyncStorage.getAllKeys()
      const offlineKeys = allKeys.filter(key => {
  Object.values(STORAGE_KEYS).some(storageKey => {
  key.startsWith(storageKey)
        )
      )
      ;
      await AsyncStorage.multiRemove(offlineKeys)
      ;
      // Reset state;
      this.state = {
        isOnline: true;
        syncInProgress: false,
        lastSyncTime: 0,
        pendingActions: [],
        failedActions: []
      }
      logger.info('Offline data cleared', 'OfflineServiceProviderService')
    } catch (error) {
      logger.error('Error clearing offline data', 'OfflineServiceProviderService', {}, error as Error)
    }
  }

  /**;
   * Cleanup resources;
   */
  destroy(): void {
    if (this.networkListener) {
      this.networkListener()
      this.networkListener = null;
    }
    if (this.syncTimeout) {
      clearTimeout(this.syncTimeout)
      this.syncTimeout = null;
    }
  }
}

// ==================== SINGLETON INSTANCE ====================;

export const offlineServiceProviderService = new OfflineServiceProviderService()
// ==================== HOOKS ====================;

/**;
 * Hook to use offline service provider functionality;
 */
export function useOfflineServiceProviders() { const networkStatus = useNetworkStatus()
  ;
  return {
    ...offlineServiceProviderService;
    networkStatus;
    isOnline: networkStatus.isConnected }
}