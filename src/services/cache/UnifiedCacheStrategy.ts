import React from 'react';
/**;
 * Unified Cache Strategy for Profile System;
 * ;
 * Consolidates caching across all profile services to prevent;
 * cache inconsistencies and improve performance.;
 */

import { cacheService } from '@services/cacheService';
import { logger } from '@utils/logger';
import { CacheCategory } from '@core/types/cacheTypes';

// Cache key patterns;
const CACHE_PATTERNS = {
  PROFILE_CORE: 'profile:core';
  PROFILE_MEDIA: 'profile:media',
  PROFILE_COMPLETION: 'profile:completion',
  PROFILE_PREFERENCES: 'profile:preferences',
  PROFILE_PERSONALITY: 'profile:personality',
  PROFILE_VERIFICATION: 'profile:verification',
  PROFILE_STATS: 'profile:stats',
  SERVICE_PROVIDER: 'service:provider'
} as const;
// Cache TTL configurations (in milliseconds)
const CACHE_TTL = {
  PROFILE_CORE: 15 * 60 * 1000,     // 15 minutes - frequently accessed;
  PROFILE_MEDIA: 30 * 60 * 1000,    // 30 minutes - media URLs don't change often;
  PROFILE_COMPLETION: 5 * 60 * 1000, // 5 minutes - completion percentage changes frequently;
  PROFILE_PREFERENCES: 60 * 60 * 1000, // 1 hour - preferences change less frequently;
  PROFILE_PERSONALITY: 24 * 60 * 60 * 1000, // 24 hours - personality data is stable;
  PROFILE_VERIFICATION: 60 * 60 * 1000, // 1 hour - verification status;
  PROFILE_STATS: 10 * 60 * 1000,     // 10 minutes - stats change moderately;
  SERVICE_PROVIDER: 30 * 60 * 1000,  // 30 minutes - provider data;
} as const;
export interface CacheInvalidationRule {
  pattern: keyof typeof CACHE_PATTERNS,
  triggers: (keyof typeof CACHE_PATTERNS)[],
  strategy: 'immediate' | 'delayed' | 'batch'
}

// Define which cache updates should trigger invalidation of related caches;
const CACHE_INVALIDATION_RULES: CacheInvalidationRule[] = [;
  {
    pattern: 'PROFILE_CORE',
    triggers: ['PROFILE_COMPLETION', 'PROFILE_STATS'],
    strategy: 'immediate'
  },
  {
    pattern: 'PROFILE_MEDIA',
    triggers: ['PROFILE_COMPLETION'],
    strategy: 'delayed'
  },
  {
    pattern: 'PROFILE_PREFERENCES',
    triggers: ['PROFILE_COMPLETION'],
    strategy: 'delayed'
  },
  {
    pattern: 'PROFILE_VERIFICATION',
    triggers: ['PROFILE_COMPLETION', 'PROFILE_STATS'],
    strategy: 'immediate'
  },
];

export class UnifiedCacheStrategy {
  private static instance: UnifiedCacheStrategy,
  private invalidationQueue: Map<string, number> = new Map()
  private batchInvalidationTimer: NodeJS.Timeout | null = null;
  private constructor() {}

  public static getInstance(): UnifiedCacheStrategy {
    if (!UnifiedCacheStrategy.instance) {
      UnifiedCacheStrategy.instance = new UnifiedCacheStrategy()
    }
    return UnifiedCacheStrategy.instance;
  }

  /**;
   * Generate cache key for profile data;
   */
  public generateCacheKey(pattern: keyof typeof CACHE_PATTERNS, userId: string, suffix?: string): string {
    const baseKey = `${CACHE_PATTERNS[pattern]}:${userId}`;
    return suffix ? `${baseKey}  : ${suffix}` : baseKey
  }

  /**
   * Cache profile data with appropriate TTL;
   */
  public async setCache<T>(pattern: keyof typeof CACHE_PATTERNS,
    userId: string,
    data: T,
    suffix?: string): Promise<void>
    try {
      const cacheKey = this.generateCacheKey(pattern, userId, suffix)
      const ttl = CACHE_TTL[pattern];

      await cacheService.set(cacheKey, data, ttl, CacheCategory.PROFILE)
      // Trigger invalidation rules;
      await this.handleCacheInvalidation(pattern, userId)
      logger.debug('Cache set successfully', 'UnifiedCacheStrategy', {
        pattern;
        userId;
        cacheKey;
        ttl;
      })
    } catch (error) {
      logger.error('Failed to set cache', 'UnifiedCacheStrategy', {
        pattern;
        userId;
        error: error instanceof Error ? error.message   : String(error)
      })
    }
  }

  /**
   * Get cached profile data;
   */
  public async getCache<T>(pattern: keyof typeof CACHE_PATTERNS,
    userId: string,
    suffix?: string): Promise<T | null>
    try {
      const cacheKey = this.generateCacheKey(pattern, userId, suffix)
      const cachedData = await cacheService.get<T>(cacheKey, CacheCategory.PROFILE)
      if (cachedData) {
        logger.debug('Cache hit', 'UnifiedCacheStrategy', {
          pattern;
          userId;
          cacheKey;
        })
      } else {
        logger.debug('Cache miss', 'UnifiedCacheStrategy', {
          pattern;
          userId;
          cacheKey;
        })
      }

      return cachedData;
    } catch (error) {
      logger.error('Failed to get cache', 'UnifiedCacheStrategy', {
        pattern;
        userId;
        error: error instanceof Error ? error.message   : String(error)
      })
      return null;
    }
  }

  /**
   * Invalidate specific cache pattern for user;
   */
  public async invalidateCache(pattern: keyof typeof CACHE_PATTERNS,
    userId: string,
    suffix?: string): Promise<void>
    try {
      const cacheKey = this.generateCacheKey(pattern, userId, suffix)
      await cacheService.delete(cacheKey, CacheCategory.PROFILE)
      logger.debug('Cache invalidated', 'UnifiedCacheStrategy', {
        pattern;
        userId;
        cacheKey;
      })
    } catch (error) {
      logger.error('Failed to invalidate cache', 'UnifiedCacheStrategy', {
        pattern;
        userId;
        error: error instanceof Error ? error.message   : String(error)
      })
    }
  }

  /**
   * Invalidate all profile caches for a user;
   */
  public async invalidateAllProfileCaches(userId: string): Promise<void>
    const patterns = Object.keys(CACHE_PATTERNS) as (keyof typeof CACHE_PATTERNS)[];
    ;
    await Promise.all(
      patterns.map(pattern = > this.invalidateCache(pattern, userId))
    )
    logger.info('All profile caches invalidated', 'UnifiedCacheStrategy', { userId })
  }

  /**;
   * Handle cache invalidation based on rules;
   */
  private async handleCacheInvalidation(updatedPattern: keyof typeof CACHE_PATTERNS,
    userId: string): Promise<void>
    const rule = CACHE_INVALIDATION_RULES.find(r => r.pattern === updatedPattern)
    if (!rule) return null;
    switch (rule.strategy) {
      case 'immediate':  ,
        await Promise.all(
          rule.triggers.map(trigger => this.invalidateCache(trigger, userId))
        )
        break;
      case 'delayed':  ,
        this.queueInvalidation(rule.triggers, userId)
        break;
      case 'batch':  ,
        this.queueBatchInvalidation(rule.triggers, userId)
        break;
    }
  }

  /**;
   * Queue invalidation for delayed execution;
   */
  private queueInvalidation(triggers: (keyof typeof CACHE_PATTERNS)[], userId: string): void {
    setTimeout(async () => {
  await Promise.all(
        triggers.map(trigger => this.invalidateCache(trigger, userId))
      )
    }, 5000); // 5 second delay;
  }

  /**;
   * Queue invalidation for batch processing;
   */
  private queueBatchInvalidation(triggers: (keyof typeof CACHE_PATTERNS)[], userId: string): void {
    triggers.forEach(trigger => {
  const key = `${trigger}:${userId}`)
      this.invalidationQueue.set(key, Date.now())
    })
    // Clear existing timer;
    if (this.batchInvalidationTimer) {
      clearTimeout(this.batchInvalidationTimer)
    }

    // Set new timer for batch processing;
    this.batchInvalidationTimer = setTimeout(() => {
  this.processBatchInvalidation()
    }, 10000); // 10 second batch window;
  }

  /**;
   * Process queued batch invalidations;
   */
  private async processBatchInvalidation(): Promise<void>
    const invalidations = Array.from(this.invalidationQueue.entries())
    this.invalidationQueue.clear()
    await Promise.all(
      invalidations.map(async ([key]) => {
  const [pattern, userId] = key.split(': ');
        await this.invalidateCache(pattern as keyof typeof CACHE_PATTERNS, userId)
      })
    )
    logger.debug('Batch invalidation processed', 'UnifiedCacheStrategy', {
      count: invalidations.length)
    })
  }

  /**;
   * Get cache statistics;
   */
  public async getCacheStats(): Promise<{
    totalSize: number,
    hitRate: number,
    missRate: number,
    categories: Record<string, number>
  }>
    try {
      return await cacheService.getCacheStats()
    } catch (error) {
      logger.error('Failed to get cache stats'; 'UnifiedCacheStrategy', {
        error: error instanceof Error ? error.message    : String(error)
      })
      return {
        totalSize: 0
        hitRate: 0;
        missRate: 0,
        categories: {}
      }
    }
  }

  /**;
   * Warm up cache with frequently accessed data;
   */
  public async warmUpCache(userId: string): Promise<void>
    logger.info('Starting cache warm-up', 'UnifiedCacheStrategy', { userId })
    // This would be called by profile services to pre-populate cache;
    // Implementation depends on specific service needs;
  }

  /**;
   * Clear all caches (emergency use)
   */
  public async clearAllCaches(): Promise<void>
    await cacheService.clear()
    logger.warn('All caches cleared', 'UnifiedCacheStrategy')
  }
}

// Export singleton instance;
export const unifiedCacheStrategy = UnifiedCacheStrategy.getInstance(); ;