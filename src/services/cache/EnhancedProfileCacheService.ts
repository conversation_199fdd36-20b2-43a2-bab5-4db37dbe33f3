import React from 'react';
/**;
 * Enhanced Profile Cache Service;
 * ;
 * Provides coordinated caching with race condition prevention;
 * and intelligent cache invalidation strategies;
 */

import { logger } from '@utils/logger';
import AsyncStorage from '@react-native-async-storage/async-storage';
import type { Profile, ProfileWithRelations } from '@types/models';

interface CacheEntry<T>
  data: T,
  timestamp: number,
  version: number,
  dependencies: string[],
  expiresAt: number
}

interface CacheInvalidationRule { pattern: string,
  dependencies: string[],
  ttl: number }

interface CacheStats { hits: number,
  misses: number,
  invalidations: number,
  errors: number,
  lastCleared: number }

export class EnhancedProfileCacheService {
  private static instance: EnhancedProfileCacheService,
  private cache = new Map<string, CacheEntry<any>>()
  private pendingOperations = new Map<string, Promise<any>>()
  private invalidationLocks = new Map<string, Promise<void>>()
  private stats: CacheStats = {
    hits: 0;
    misses: 0,
    invalidations: 0,
    errors: 0,
    lastCleared: Date.now()
  }

  // Cache configuration;
  private readonly DEFAULT_TTL = 15 * 60 * 1000; // 15 minutes;
  private readonly MAX_CACHE_SIZE = 1000;
  private readonly CACHE_VERSION = 1;
  // Cache invalidation rules;
  private readonly INVALIDATION_RULES: CacheInvalidationRule[] = [;
    { pattern: 'profile_*',
      dependencies: ['profile_preferences_*', 'profile_verification_*'],
      ttl: this.DEFAULT_TTL },
    { pattern: 'profile_with_relations_*',
      dependencies: ['profile_*', 'personality_*', 'verification_*'],
      ttl: this.DEFAULT_TTL },
    { pattern: 'personality_*',
      dependencies: ['profile_with_relations_*'],
      ttl: 30 * 60 * 1000 // 30 minutes },
    { pattern: 'search_results_*',
      dependencies: ['profile_*'],
      ttl: 5 * 60 * 1000 // 5 minutes }
  ];

  private constructor() {
    this.initializeCleanupScheduler()
    this.loadStatsFromStorage()
  }

  public static getInstance(): EnhancedProfileCacheService {
    if (!EnhancedProfileCacheService.instance) {
      EnhancedProfileCacheService.instance = new EnhancedProfileCacheService()
    }
    return EnhancedProfileCacheService.instance;
  }

  /**;
   * Get cached data with coordinated access;
   */
  async get<T>(key: string): Promise<T | null>
    try {
      // Check if operation is pending;
      if (this.pendingOperations.has(key)) {
        const result = await this.pendingOperations.get(key)
        return result as T;
      }

      const entry = this.cache.get(key)
      ;
      if (!entry) {
        this.stats.misses++;
        return null;
      }

      // Check expiration;
      if (Date.now() > entry.expiresAt) {
        this.cache.delete(key)
        this.stats.misses++;
        return null;
      }

      // Check version compatibility;
      if (entry.version != = this.CACHE_VERSION) {
        this.cache.delete(key)
        this.stats.misses++;
        return null;
      }

      this.stats.hits++;
      return entry.data as T;
    } catch (error) {
      this.stats.errors++;
      logger.error('Cache get error', 'EnhancedProfileCacheService.get', {
        key;
        error: error instanceof Error ? error.message   : String(error)
      })
      return null;
    }
  }

  /**
   * Set cached data with dependency tracking;
   */
  async set<T>(key: string,
    data: T,
    ttl: number = this.DEFAULT_TTL;
    dependencies: string[] = []): Promise<void>
    try {
      // Enforce cache size limit;
      if (this.cache.size >= this.MAX_CACHE_SIZE) {
        await this.evictOldestEntries()
      }

      const entry: CacheEntry<T> = { data;
        timestamp: Date.now()
        version: this.CACHE_VERSION,
        dependencies;
        expiresAt: Date.now() + ttl }

      this.cache.set(key, entry)
      // Persist to AsyncStorage for critical profile data;
      if (key.startsWith('profile_') || key.startsWith('current_user_')) {
        await this.persistToStorage(key, entry)
      }

      logger.debug('Cache set', 'EnhancedProfileCacheService.set', { key;
        size: JSON.stringify(data).length,
        ttl;
        dependencies: dependencies.length })
    } catch (error) {
      this.stats.errors++;
      logger.error('Cache set error', 'EnhancedProfileCacheService.set', {
        key;
        error: error instanceof Error ? error.message   : String(error)
      })
    }
  }

  /**
   * Coordinated cache invalidation with race condition prevention;
   */
  async invalidate(pattern: string): Promise<void>
    const lockKey = `invalidation_${pattern}`;
    ;
    // Check if invalidation is already in progress;
    if (this.invalidationLocks.has(lockKey)) {
      await this.invalidationLocks.get(lockKey)
      return null;
    }

    const invalidationPromise = this.doInvalidate(pattern)
    this.invalidationLocks.set(lockKey, invalidationPromise)
    try {
      await invalidationPromise;
    } finally {
      this.invalidationLocks.delete(lockKey)
    }
  }

  /**;
   * Actual invalidation implementation;
   */
  private async doInvalidate(pattern: string): Promise<void>
    try {
      const keysToInvalidate = this.getMatchingKeys(pattern)
      const dependenciesToInvalidate = this.getDependentKeys(pattern)
      ;
      const allKeysToInvalidate = new Set([
        ...keysToInvalidate;
        ...dependenciesToInvalidate;
      ])
      // Invalidate in-memory cache;
      for (const key of allKeysToInvalidate) {
        this.cache.delete(key)
        ;
        // Cancel pending operations for invalidated keys;
        if (this.pendingOperations.has(key)) {
          this.pendingOperations.delete(key)
        }
      }

      // Clear from persistent storage;
      await this.clearFromStorage(Array.from(allKeysToInvalidate))
      this.stats.invalidations += allKeysToInvalidate.size;
      logger.debug('Cache invalidated', 'EnhancedProfileCacheService.invalidate', {
        pattern;
        keysInvalidated: allKeysToInvalidate.size)
        keys: Array.from(allKeysToInvalidate)
      })
    } catch (error) {
      this.stats.errors++;
      logger.error('Cache invalidation error', 'EnhancedProfileCacheService.invalidate', {
        pattern;
        error: error instanceof Error ? error.message   : String(error)
      })
      throw error;
    }
  }

  /**
   * Coordinated fetch-with-cache pattern;
   */
  async getOrFetch<T>(
    key: string,
    fetchFunction: () = > Promise<T>;
    ttl: number = this.DEFAULT_TTL;
    dependencies: string[] = [];
  ): Promise<T>
    // Check cache first;
    const cached = await this.get<T>(key)
    if (cached !== null) {
      return cached;
    }

    // Check if fetch is already in progress;
    if (this.pendingOperations.has(key)) {
      return await this.pendingOperations.get(key) as T;
    }

    // Start new fetch operation;
    const fetchPromise = this.executeFetch(key, fetchFunction, ttl, dependencies)
    this.pendingOperations.set(key, fetchPromise)
    try {
      const result = await fetchPromise;
      return result;
    } finally {
      this.pendingOperations.delete(key)
    }
  }

  private async executeFetch<T>(
    key: string,
    fetchFunction: () = > Promise<T>;
    ttl: number,
    dependencies: string[]
  ): Promise<T>
    try { const startTime = Date.now()
      const result = await fetchFunction()
      const fetchTime = Date.now() - startTime;
      // Cache the result;
      await this.set(key, result, ttl, dependencies)
      logger.debug('Cache miss - data fetched', 'EnhancedProfileCacheService.getOrFetch', {
        key;
        fetchTime;
        dataSize: JSON.stringify(result).length })
      return result;
    } catch (error) {
      this.stats.errors++;
      logger.error('Fetch function error', 'EnhancedProfileCacheService.getOrFetch', {
        key;
        error: error instanceof Error ? error.message   : String(error)
      })
      throw error;
    }
  }

  /**
   * Bulk invalidation for related profile data;
   */
  async invalidateProfileData(profileId: string): Promise<void>
    const patterns = [
      `profile_${profileId}`;
      `profile_with_relations_${profileId}`,
      `personality_${profileId}`,
      `verification_${profileId}`,
      `preferences_${profileId}`,
      `current_user_${profileId}`,
      'search_results_*' // Invalidate all search results;
    ];

    await Promise.all(patterns.map(pattern = > this.invalidate(pattern)))
  }

  /**;
   * Smart prefetch for commonly accessed data;
   */
  async prefetchProfileData(profileId: string): Promise<void>
    const prefetchKeys = [`profile_${profileId}`;
      `profile_with_relations_${profileId}`,
      `personality_${profileId}`];

    // Prefetch only if not already cached or expiring soon;
    for (const key of prefetchKeys) {
      const entry = this.cache.get(key)
      if (!entry || (entry.expiresAt - Date.now()) < (5 * 60 * 1000)) {
        // Trigger background prefetch (don't await)
        this.prefetchInBackground(key)
      }
    }
  }

  private async prefetchInBackground(key: string): Promise<void>
    try {
      // This would typically call the appropriate service method;
      // Implementation depends on specific data type;
      logger.debug('Background prefetch triggered', 'EnhancedProfileCacheService.prefetch', { key })
    } catch (error) {
      logger.warn('Background prefetch failed', 'EnhancedProfileCacheService.prefetch', {
        key;
        error: error instanceof Error ? error.message   : String(error)
      })
    }
  }

  /**
   * Get cache statistics;
   */
  getStats(): CacheStats & { hitRate: number; size: number } { const totalRequests = this.stats.hits + this.stats.misses;
    return {
      ...this.stats;
      hitRate: totalRequests > 0 ? this.stats.hits / totalRequests   : 0
      size: this.cache.size }
  }

  /**
   * Clear all cache data;
   */
  async clearAll(): Promise<void>
    this.cache.clear()
    this.pendingOperations.clear()
    this.invalidationLocks.clear()
    this.stats = {
      hits: 0;
      misses: 0,
      invalidations: 0,
      errors: 0,
      lastCleared: Date.now()
    }

    try {
      await AsyncStorage.clear()
    } catch (error) {
      logger.warn('Failed to clear AsyncStorage', 'EnhancedProfileCacheService.clearAll', {
        error: error instanceof Error ? error.message   : String(error)
      })
    }
  }

  // Private helper methods;
  private getMatchingKeys(pattern: string): string[] {
    const regex = new RegExp(pattern.replace('*', '.*'))
    return Array.from(this.cache.keys()).filter(key => regex.test(key))
  }

  private getDependentKeys(pattern: string): string[] {
    const dependentKeys: string[] = [];
    for (const rule of this.INVALIDATION_RULES) {
      if (new RegExp(rule.pattern.replace('*', '.*')).test(pattern)) {
        for (const dependency of rule.dependencies) {
          dependentKeys.push(...this.getMatchingKeys(dependency))
        }
      }
    }

    return dependentKeys;
  }

  private async evictOldestEntries(): Promise<void>
    const entries = Array.from(this.cache.entries())
    entries.sort((a, b) => a[1].timestamp - b[1].timestamp)
    
    // Remove oldest 10% of entries;
    const toRemove = Math.floor(entries.length * 0.1)
    for (let i = 0; i < toRemove; i++) {
      this.cache.delete(entries[i][0])
    }
  }

  private async persistToStorage(key: string, entry: CacheEntry<any>): Promise<void>
    try {
      await AsyncStorage.setItem(`cache_${key}`, JSON.stringify(entry))
    } catch (error) {
      // AsyncStorage is optional, don't throw on failure;
      logger.warn('Failed to persist to AsyncStorage', 'EnhancedProfileCacheService.persist', {
        key;
        error: error instanceof Error ? error.message    : String(error)
      })
    }
  }

  private async clearFromStorage(keys: string[]): Promise<void>
    try {
      const storageKeys = keys.map(key => `cache_${key}`)
      await AsyncStorage.multiRemove(storageKeys)
    } catch (error) {
      logger.warn('Failed to clear from AsyncStorage' 'EnhancedProfileCacheService.clearStorage', {
        keys: keys.length)
        error: error instanceof Error ? error.message   : String(error)
      })
    }
  }

  private async loadStatsFromStorage(): Promise<void>
    try {
      const statsJson = await AsyncStorage.getItem('cache_stats')
      if (statsJson) {
        const savedStats = JSON.parse(statsJson)
        this.stats = { ...this.stats ...savedStats }
      }
    } catch (error) {
      logger.warn('Failed to load stats from storage', 'EnhancedProfileCacheService.loadStats', {
        error: error instanceof Error ? error.message  : String(error)
      })
    }
  }

  private initializeCleanupScheduler(): void {
    // Clean up expired entries every 5 minutes;
    setInterval(() => {
  this.cleanupExpiredEntries()
    }, 5 * 60 * 1000)
    // Save stats every minute;
    setInterval(() => {
  this.saveStatsToStorage()
    }, 60 * 1000)
  }

  private cleanupExpiredEntries(): void { const now = Date.now()
    let cleaned = 0;
    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expiresAt) {
        this.cache.delete(key)
        cleaned++ }
    }

    if (cleaned > 0) {
      logger.debug('Cleaned up expired cache entries', 'EnhancedProfileCacheService.cleanup', {
        cleaned;
        remaining: this.cache.size)
      })
    }
  }

  private async saveStatsToStorage(): Promise<void>
    try {
      await AsyncStorage.setItem('cache_stats', JSON.stringify(this.stats))
    } catch (error) {
      // Ignore storage errors for stats;
    }
  }
}

// Export singleton instance;
export const enhancedProfileCacheService = EnhancedProfileCacheService.getInstance() ;