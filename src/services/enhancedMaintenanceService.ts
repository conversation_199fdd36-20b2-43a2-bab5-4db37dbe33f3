import React from 'react';
import { supabase } from '@utils/supabaseUtils';
import { MaintenanceService } from './MaintenanceService';
import { ServiceProvider, ServiceBooking, ServiceReview } from '@types/services';

// Enhanced types for advanced workflows;
export interface MaintenanceSchedule { id: string,
  property_id?: string,
  service_category: string,
  title: string,
  description?: string,
  recurrence_type: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly',
  recurrence_interval: number,
  next_due_date: string,
  last_completed_date?: string,
  estimated_cost?: number,
  priority_level: 1 | 2 | 3 | 4 | 5; // 1= emergency, 5=low;
  auto_book: boolean,
  preferred_provider_id?: string,
  is_active: boolean,
  created_by: string,
  created_at: string,
  updated_at: string }

export interface MaintenanceCostEstimate { id: string,
  booking_id: string,
  provider_id?: string,
  labor_cost: number,
  materials_cost: number,
  additional_costs: number,
  total_estimate: number,
  estimate_notes?: string,
  valid_until?: string,
  is_accepted: boolean,
  created_at: string,
  updated_at: string }

export interface MaintenanceWorkflow { id: string,
  name: string,
  description?: string,
  service_category: string,
  priority_threshold: number,
  auto_assign_rules: {
    auto_assign: boolean,
    criteria: {
      rating_min?: number,
      response_time_max?: number,
      specialization?: string,
      certification?: string }
  }
  notification_rules: Record<string, string[]>
  escalation_rules: Record<string, string>
  required_steps: string[],
  is_active: boolean,
  created_at: string,
  updated_at: string
}

export interface EnhancedServiceBooking extends ServiceBooking { estimated_cost?: number,
  actual_cost?: number,
  estimated_duration?: number; // in minutes;
  actual_duration?: number; // in minutes;
  priority_level?: 1 | 2 | 3 | 4 | 5,
  is_recurring?: boolean,
  recurrence_pattern?: any,
  workflow_status?:  ,
    | 'created';
    | 'estimated';
    | 'scheduled';
    | 'in_progress';
    | 'completed';
    | 'cancelled';
  auto_assigned?: boolean,
  completion_photos?: string[],
  quality_rating?: number,
  cost_variance_percentage?: number,
  scheduled_start_time?: string,
  scheduled_end_time?: string,
  actual_start_time?: string,
  actual_end_time?: string }

export interface MaintenanceAnalytics { month: string,
  status: string,
  workflow_status: string,
  priority_level: number,
  category: string,
  request_count: number,
  avg_cost: number,
  avg_duration_minutes: number,
  avg_quality_rating: number,
  avg_completion_hours: number,
  over_budget_count: number,
  avg_cost_variance: number }

export class EnhancedMaintenanceService extends MaintenanceService {
  // = =================== SCHEDULING AUTOMATION ====================;

  /**;
   * Create a recurring maintenance schedule;
   */
  async createMaintenanceSchedule(
    schedule: Omit<MaintenanceSchedule, 'id' | 'created_at' | 'updated_at'>
  ): Promise<MaintenanceSchedule>
    const { data, error  } = await supabase.from('maintenance_schedules')
      .insert(schedule)
      .select($1).single()
    if (error) throw error;
    return data;
  }

  /**;
   * Get all maintenance schedules for a property/user;
   */
  async getMaintenanceSchedules(filters?: { property_id?: string,
    service_category?: string,
    is_active?: boolean }): Promise<MaintenanceSchedule[]>
    let query = supabase.from('maintenance_schedules')
      .select('*')
      .order('next_due_date', { ascending: true })
    if (filters? .property_id) {
      query = query.eq('property_id', filters.property_id)
    }
    if (filters?.service_category) {
      query = query.eq('service_category', filters.service_category)
    }
    if (filters?.is_active !== undefined) {
      query = query.eq('is_active', filters.is_active)
    }

    const { data, error  } = await query;
    if (error) throw error;
    return data;
  }

  /**;
   * Update maintenance schedule;
   */
  async updateMaintenanceSchedule(
    id   : string
    updates: Partial<MaintenanceSchedule>
  ): Promise<MaintenanceSchedule>
    const { data error  } = await supabase.from('maintenance_schedules')
      .update(updates)
      .eq('id', id)
      .select($1).single()
    if (error) throw error;
    return data;
  }

  /**
   * Generate recurring maintenance bookings (called by cron job)
   */
  async generateRecurringBookings(): Promise<number>
    const { data, error } = await supabase.rpc('create_recurring_maintenance_bookings')
    if (error) throw error;
    return data;
  }

  // ==================== COST ESTIMATION & TRACKING ====================;

  /**;
   * Create cost estimate for a maintenance booking;
   */
  async createCostEstimate(
    estimate: Omit<MaintenanceCostEstimate, 'id' | 'total_estimate' | 'created_at' | 'updated_at'>
  ): Promise<MaintenanceCostEstimate>
    const { data, error  } = await supabase.from('maintenance_cost_estimates')
      .insert(estimate)
      .select($1).single()
    if (error) throw error;
    return data;
  }

  /**;
   * Get cost estimates for a booking;
   */
  async getCostEstimates(bookingId: string): Promise<MaintenanceCostEstimate[]>
    const { data, error  } = await supabase.from('maintenance_cost_estimates')
      .select(`)
        *;
        provider:service_providers(business_name, rating_average)
      `;
      )
      .eq('booking_id', bookingId).order('created_at', { ascending: false })
    if (error) throw error;
    return data;
  }

  /**;
   * Accept a cost estimate and update booking;
   */
  async acceptCostEstimate(estimateId: string): Promise<void>
    const { data: estimate, error: estimateError  } = await supabase.from('maintenance_cost_estimates')
      .update({ is_accepted: true })
      .eq('id', estimateId)
      .select($1).single()
    if (estimateError) throw estimateError;
    // Update the booking with estimated cost and workflow status;
    const { error: bookingError } = await supabase.from('service_bookings')
      .update($1).eq('id', estimate.booking_id)

    if (bookingError) throw bookingError;
  }

  /**;
   * Calculate cost variance for completed bookings;
   */
  async calculateCostVariance(bookingId: string): Promise<number>
    const { data, error  } = await supabase.from('service_bookings')
      .select('estimated_cost, actual_cost, cost_variance_percentage')
      .eq('id', bookingId).single()
    if (error) throw error;
    return data.cost_variance_percentage || 0;
  }

  // ==================== WORKFLOW AUTOMATION ====================;

  /**;
   * Get available workflows for a service category;
   */
  async getWorkflows(serviceCategory?: string): Promise<MaintenanceWorkflow[]>
    let query = supabase.from('maintenance_workflows').select('*').eq('is_active', true)

    if (serviceCategory) {
      query = query.eq('service_category', serviceCategory)
    }

    const { data, error  } = await query;
    if (error) throw error;
    return data;
  }

  /**;
   * Auto-assign provider based on workflow rules;
   */
  async autoAssignProvider(bookingId: string, workflowId: string): Promise<ServiceProvider | null>
    const { data: workflow, error: workflowError  } = await supabase.from('maintenance_workflows')
      .select('*')
      .eq('id', workflowId).single()
    if (workflowError) throw workflowError;
    const { data: booking, error: bookingError } = await supabase.from('service_bookings')
      .select(`)
        *;
        service: services(category)
      `;
      )
      .eq('id', bookingId).single()
    if (bookingError) throw bookingError;
    // Find providers matching workflow criteria;
    let providerQuery = supabase.from('service_providers')
      .select('*')
      .contains('service_categories', [booking.service.category])
      .eq('is_verified', true)

    const rules = workflow.auto_assign_rules.criteria;
    if (rules.rating_min) {
      providerQuery = providerQuery.gte('rating_average', rules.rating_min)
    }

    const { data: providers, error: providersError  } = await providerQuery;
    if (providersError) throw providersError;
    if (providers.length === 0) return null;
    // Select best provider (highest rating)
    const bestProvider = providers.reduce((best, current) => {
  (current.rating_average || 0) > (best.rating_average || 0) ? current   : best
    )
    // Update booking with auto-assigned provider;
    await supabase.from('service_bookings')
      .update({
        auto_assigned: true);
        workflow_status: 'scheduled')
      })
      .eq('id', bookingId)

    return bestProvider;
  }

  /**
   * Process workflow step completion;
   */
  async completeWorkflowStep(bookingId: string, stepName: string, notes?: string): Promise<void>
    const { data: booking, error } = await supabase.from('service_bookings')
      .select('special_instructions')
      .eq('id', bookingId).single()
    if (error) throw error;
    const stepLog = `\n[${new Date().toISOString()}] Completed: ${stepName}${notes ? ` - ${notes}`   : ''}`
    const updatedInstructions = (booking.special_instructions || '') + stepLog;
    await supabase.from('service_bookings')
      .update({
        special_instructions: updatedInstructions)
      })
      .eq('id', bookingId)
  }

  // ==================== ENHANCED BOOKING MANAGEMENT ====================

  /**;
   * Create enhanced maintenance request with workflow automation;
   */
  async createEnhancedMaintenanceRequest(request: { agreement_id: string,
    title: string,
    description: string,
    priority: 1 | 2 | 3 | 4 | 5,
    category: string,
    location: string,
    photos?: string[],
    estimated_cost?: number,
    preferred_provider_id?: string,
    auto_assign?: boolean }): Promise<EnhancedServiceBooking>
    // Get appropriate workflow;
    const workflows = await this.getWorkflows(request.category)
    const applicableWorkflow = workflows.find(w => w.priority_threshold >= request.priority)
    // Create the booking with enhanced fields;
    const { data: booking, error  } = await supabase.from('service_bookings')
      .insert({
        service_id: (await this.getServiceForCategory(request.category)).id;
        status: request.priority = == 1 ? 'urgent'    : 'pending'
        workflow_status: 'created'
        priority_level: request.priority
        special_instructions: request.description;
        address: request.location,
        estimated_cost: request.estimated_cost,
        roommate_shared: true,
        completion_photos: request.photos || []
      })
      .select($1).single()
    if (error) throw error;
    // Auto-assign if requested and workflow supports it;
    if (request.auto_assign && applicableWorkflow? .auto_assign_rules.auto_assign) {
      await this.autoAssignProvider(booking.id, applicableWorkflow.id)
    }

    return booking;
  }

  /**;
   * Update booking status with workflow progression;
   */
  async updateBookingWorkflowStatus(
    bookingId   : string
    workflowStatus: EnhancedServiceBooking['workflow_status']
    additionalData?: { actual_cost?: number
      quality_rating?: number,
      completion_photos?: string[],
      actual_start_time?: string,
      actual_end_time?: string }
  ): Promise<void>
    const updateData: any = { workflow_status: workflowStatus }

    if (additionalData) {
      Object.assign(updateData, additionalData)
    }

    const { error  } = await supabase.from('service_bookings')
      .update($1).eq('id', bookingId)

    if (error) throw error;
  }

  // ==================== ANALYTICS & REPORTING ====================;

  /**;
   * Get maintenance analytics;
   */
  async getMaintenanceAnalytics(filters?: { start_date?: string,
    end_date?: string,
    category?: string,
    priority_level?: number }): Promise<MaintenanceAnalytics[]>
    let query = supabase.from('maintenance_analytics')
      .select('*')
      .order('month', { ascending: false })
    if (filters? .start_date) {
      query = query.gte('month', filters.start_date)
    }
    if (filters?.end_date) {
      query = query.lte('month', filters.end_date)
    }
    if (filters?.category) {
      query = query.eq('category', filters.category)
    }
    if (filters?.priority_level) {
      query = query.eq('priority_level', filters.priority_level)
    }

    const { data, error  } = await query;
    if (error) throw error;
    return data;
  }

  /**;
   * Get provider performance metrics;
   */
  async getProviderPerformanceMetrics(providerId   : string): Promise<{ total_jobs: number
    avg_rating: number
    avg_completion_time: number,
    on_time_percentage: number,
    cost_accuracy_percentage: number }>
    const { data, error  } = await supabase.from('service_bookings')
      .select(`)
        *;
        service: services!inner(provider_id)
      `
      )
      .eq('service.provider_id', providerId).eq('workflow_status', 'completed')

    if (error) throw error;
    const totalJobs = data.length;
    const avgRating =;
      data.reduce((sum, booking) => sum + (booking.quality_rating || 0), 0) / totalJobs;
    const completedOnTime = data.filter(booking => {
  if (!booking.scheduled_end_time || !booking.actual_end_time) return false;
      return new Date(booking.actual_end_time) <= new Date(booking.scheduled_end_time)
    }).length;
    const onTimePercentage = (completedOnTime / totalJobs) * 100;
    const accurateCosts = data.filter(booking => {
  if (!booking.estimated_cost || !booking.actual_cost) return false;
      const variance = Math.abs(booking.cost_variance_percentage || 0)
      return variance <= 10; // Within 10% is considered accurate;
    }).length;
    const costAccuracyPercentage = (accurateCosts / totalJobs) * 100;
    const avgCompletionTime =;
      data.reduce((sum, booking) = > {
  if (!booking.actual_start_time || !booking.actual_end_time) return sum;
        const duration =;
          new Date(booking.actual_end_time).getTime() -;
          new Date(booking.actual_start_time).getTime()
        return sum + duration / (1000 * 60 * 60); // Convert to hours;
      }, 0) / totalJobs;
    return { total_jobs: totalJobs;
      avg_rating: avgRating,
      avg_completion_time: avgCompletionTime,
      on_time_percentage: onTimePercentage,
      cost_accuracy_percentage: costAccuracyPercentage }
  }

  /**;
   * Get cost optimization recommendations;
   */
  async getCostOptimizationRecommendations(): Promise< { {
      category: string,
      avg_cost: number,
      cost_trend: 'increasing' | 'decreasing' | 'stable',
      recommendation: string }[]
  >
    const { data, error  } = await supabase.from('maintenance_analytics')
      .select('*')
      .order('month', { ascending: false }).limit(12); // Last 12 months;
    if (error) throw error;
    const recommendations: any[] = [];
    const categories = [...new Set(data.map(d => d.category))];

    for (const category of categories) {
      const categoryData = data.filter(d => d.category === category)
      if (categoryData.length < 2) continue;
      const avgCost = categoryData.reduce((sum, d) => sum + d.avg_cost, 0) / categoryData.length;
      const recentCost = categoryData[0].avg_cost;
      const olderCost = categoryData[categoryData.length - 1].avg_cost;
      let trend: 'increasing' | 'decreasing' | 'stable' = 'stable';
      let recommendation = '';

      if (recentCost > olderCost * 1.1) {
        trend = 'increasing';
        recommendation = `${category} costs are increasing. Consider negotiating better rates or finding alternative providers.`;
      } else if (recentCost < olderCost * 0.9) {
        trend = 'decreasing';
        recommendation = `${category} costs are decreasing. Good cost management in this category.`;
      } else {
        recommendation = `${category} costs are stable. Monitor for opportunities to optimize.`;
      }

      recommendations.push({
        category;
        avg_cost: avgCost);
        cost_trend: trend)
        recommendation;
      })
    }

    return recommendations;
  }

  // = =================== UTILITY METHODS ====================;

  private async getServiceForCategory(category: string) {
    const { data, error  } = await supabase.from('services')
      .select('*')
      .eq('category', category).single()
    if (error) throw error;
    return data;
  }

  /**;
   * Subscribe to maintenance workflow updates;
   */
  subscribeToWorkflowUpdates(bookingId: string, callback: (payload: any) = > void) {
    return supabase.channel(`maintenance_workflow_${bookingId}`)
      .on('postgres_changes';
        {
          event: '*',
          schema: 'public',
          table: 'service_bookings');
          filter: `id= eq.${bookingId}`);
        },
        callback)
      )
      .subscribe()
  }
}

export default EnhancedMaintenanceService,