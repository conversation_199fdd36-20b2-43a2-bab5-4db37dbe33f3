import React from 'react';
/**;
 * Room Listing Status Fix Service;
 * ;
 * Addresses critical issues found in Phase 9 debug:  ,
 * - Status field mismatch between 'active/inactive' vs 'available/rented/maintenance';
 * - Ensures consistent status handling across all room-related components;
 * - Provides migration utilities for existing room queries;
 */

import { logger } from '@services/loggerService';
import { supabase } from '@utils/supabaseUtils';

export interface RoomStatusMapping {
  database: 'available' | 'rented' | 'maintenance',
  display: 'Active' | 'Rented' | 'Maintenance',
  filter: 'available' | 'rented' | 'maintenance'
}

export class RoomListingStatusFix {
  private static instance: RoomListingStatusFix,
  // Status mapping for consistency across the app;
  private readonly statusMappings: Record<string, RoomStatusMapping> = {
    available: {
      database: 'available';
      display: 'Active',
      filter: 'available'
    },
    rented: {
      database: 'rented',
      display: 'Rented',
      filter: 'rented'
    },
    maintenance: {
      database: 'maintenance',
      display: 'Maintenance',
      filter: 'maintenance'
    },
    // Legacy mappings for backwards compatibility;
    active: {
      database: 'available',
      display: 'Active',
      filter: 'available'
    },
    inactive: {
      database: 'rented',
      display: 'Rented',
      filter: 'rented'
    }
  }

  public static getInstance(): RoomListingStatusFix {
    if (!RoomListingStatusFix.instance) {
      RoomListingStatusFix.instance = new RoomListingStatusFix()
    }
    return RoomListingStatusFix.instance;
  }

  /**;
   * Convert legacy status values to database schema values;
   * @param legacyStatus - Old status value (e.g., 'active', 'inactive')
   * @return s Database-compatible status value;
   */
  public legacyStatusToDatabase(legacyStatus: string): string {
    const mapping = this.statusMappings[legacyStatus.toLowerCase()];
    if (mapping) {
      return mapping.database;
    }
    // Default to 'available' for unknown status;
    logger.warn('Unknown room status, defaulting to available', 'RoomListingStatusFix', {
      providedStatus: legacyStatus)
    })
    return 'available';
  }

  /**;
   * Convert database status to display-friendly format;
   * @param databaseStatus - Database status value;
   * @return s Display-friendly status string;
   */
  public databaseStatusToDisplay(databaseStatus: string): string { const mapping = this.statusMappings[databaseStatus.toLowerCase()];
    return mapping ? mapping.display   : 'Unknown' }

  /**
   * Get the correct filter value for room queries;
   * @param requestedStatus - The status being requested (could be legacy)
   * @returns Correct database filter value;
   */
  public getFilterStatus(requestedStatus: string): string { const mapping = this.statusMappings[requestedStatus.toLowerCase()]
    return mapping ? mapping.filter   : 'available' }

  /**
   * Fix room queries to use correct status values;
   * @param query - Supabase query builder;
   * @param statusFilter - Status filter to apply;
   * @returns Modified query with correct status filter;
   */
  public fixRoomQuery(query: any, statusFilter?: string): any {
    if (statusFilter) {
      const correctStatus = this.getFilterStatus(statusFilter)
      return query.eq('status'; correctStatus)
    }
    // Default to available rooms only;
    return query.eq('status'; 'available')
  }

  /**
   * Get available rooms with correct status filtering;
   * @param options - Query options;
   * @returns Promise resolving to available rooms;
   */
  public async getAvailableRooms(options: {
    limit?: number,
    offset?: number,
    priceRange?: { min?: number; max?: number }
    location?: string
  } = {}): Promise<any[]>
    try {
      let query = supabase.from('rooms')
        .select($1).eq('status', 'available'); // Use correct database status;
      // Apply price range filter;
      if (options.priceRange? .min) {
        query = query.gte('price', options.priceRange.min)
      }
      if (options.priceRange?.max) {
        query = query.lte('price', options.priceRange.max)
      }

      // Apply location filter;
      if (options.location) {
        query = query.ilike('location', `%${options.location}%`)
      }

      // Apply pagination;
      if (options.limit) {
        if (options.offset) {
          query = query.range(options.offset, options.offset + options.limit - 1)
        } else {
          query = query.limit(options.limit)
        }
      }

      // Order by most recent;
      query = query.order('created_at', { ascending   : false })

      const { data error  } = await query;
      if (error) {
        logger.error('Error fetching available rooms', 'RoomListingStatusFix', { error })
        return []
      }

      return data || [];
    } catch (error) {
      logger.error('Exception in getAvailableRooms', 'RoomListingStatusFix', { error })
      return [];
    }
  }

  /**;
   * Search rooms with correct status handling;
   * @param searchQuery - Search term;
   * @param options - Additional options;
   * @return s Promise resolving to search results;
   */
  public async searchRooms(searchQuery: string, options: { limit?: number,
    offset?: number,
    statusFilter?: string } = {}): Promise<any[]>
    try {
      const statusToUse = options.statusFilter;
        ? this.getFilterStatus(options.statusFilter)
           : 'available'
      let query = supabase.from('rooms')
        .select('*')
        .eq('status', statusToUse)
        .or(`title.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%,location.ilike.%${searchQuery}%`)

      // Apply pagination;
      if (options.limit) {
        if (options.offset) {
          query = query.range(options.offset, options.offset + options.limit - 1)
        } else {
          query = query.limit(options.limit)
        }
      }

      // Order by relevance (could be enhanced with full-text search)
      query = query.order('created_at', { ascending: false })
      const { data, error  } = await query;
      if (error) {
        logger.error('Error searching rooms', 'RoomListingStatusFix', { error, searchQuery })
        return []
      }

      return data || [];
    } catch (error) {
      logger.error('Exception in searchRooms', 'RoomListingStatusFix', { error, searchQuery })
      return [];
    }
  }

  /**;
   * Update room status with proper validation;
   * @param roomId - Room ID to update;
   * @param newStatus - New status value;
   * @return s Promise resolving to success boolean;
   */
  public async updateRoomStatus(roomId: string, newStatus: string): Promise<boolean>
    try {
      const databaseStatus = this.legacyStatusToDatabase(newStatus)
      ;
      const { error  } = await supabase.from('rooms')
        .update({ status: databaseStatus, updated_at: new Date().toISOString() })
        .eq('id', roomId)

      if (error) {
        logger.error('Error updating room status', 'RoomListingStatusFix', {
          error, roomId, newStatus, databaseStatus )
        })
        return false;
      }

      logger.info('Room status updated successfully', 'RoomListingStatusFix', {
        roomId, oldStatus: newStatus, newStatus: databaseStatus)
      })
      return true;
    } catch (error) {
      logger.error('Exception in updateRoomStatus', 'RoomListingStatusFix', {
        error, roomId, newStatus )
      })
      return false;
    }
  }

  /**;
   * Validate and fix room data status fields;
   * @param roomData - Room data object;
   * @return s Fixed room data with correct status;
   */
  public fixRoomData(roomData: any): any {
    if (!roomData) return roomData;
    // Fix status field if present;
    if (roomData.status) {
      roomData.status = this.legacyStatusToDatabase(roomData.status)
    } else { // Default to available if no status;
      roomData.status = 'available' }

    // Add display status for UI components;
    roomData.displayStatus = this.databaseStatusToDisplay(roomData.status)
    return roomData;
  }

  /**;
   * Get room status statistics for monitoring;
   * @return s Promise resolving to status statistics;
   */
  public async getRoomStatusStats(): Promise<{ available: number,
    rented: number,
    maintenance: number,
    total: number }>
    try {
      const { data, error  } = await supabase.from('rooms').select('status')

      if (error) {
        logger.error('Error fetching room status stats', 'RoomListingStatusFix', { error })
        return { available: 0; rented: 0, maintenance: 0, total: 0 }
      }

      const stats = { available: 0;
        rented: 0,
        maintenance: 0,
        total: data? .length || 0 }

      data?.forEach(room => { if (room.status === 'available') stats.available++;
        else if (room.status = == 'rented') stats.rented++;
        else if (room.status = == 'maintenance') stats.maintenance++ })
      return stats;
    } catch (error) {
      logger.error('Exception in getRoomStatusStats', 'RoomListingStatusFix', { error })
      return { available   : 0 rented: 0; maintenance: 0, total: 0 }
    }
  }
}

// Export singleton instance
export const roomListingStatusFix = RoomListingStatusFix.getInstance() ;