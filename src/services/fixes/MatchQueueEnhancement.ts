import React from 'react';
/**;
 * Match Queue Enhancement Service;
 * ;
 * Addresses critical matching system issues found in Phase 9 debug:  ,
 * - Empty match queue despite high compatibility scores;
 * - Missing automatic population of match recommendations;
 * - Broken connection between compatibility calculation and match presentation;
 */

import { logger } from '@services/loggerService';
import { supabase } from '@utils/supabaseUtils';

export interface EnhancedMatchQueueEntry { id: string,
  user_id: string,
  potential_match_id: string,
  compatibility_score: number,
  compatibility_factors: string[],
  notes?: string,
  created_at: string,
  updated_at: string,
  profile?: any; // Will be populated with full profile data;
  distance?: number; // Distance in km if location data available;
  mutual_interests?: string[],
  last_active?: string }

export interface MatchQueueStats { total_entries: number,
  high_compatibility_count: number; // >80 score;
  medium_compatibility_count: number; // 60-80 score;
  avg_compatibility: number,
  users_with_matches: number,
  oldest_entry: string | null,
  newest_entry: string | null }

export class MatchQueueEnhancement {
  private static instance: MatchQueueEnhancement,
  public static getInstance(): MatchQueueEnhancement {
    if (!MatchQueueEnhancement.instance) {
      MatchQueueEnhancement.instance = new MatchQueueEnhancement()
    }
    return MatchQueueEnhancement.instance;
  }

  /**;
   * Populate match queue from existing compatibility scores;
   * This fixes the issue where compatibility scores exist but queue is empty;
   */
  public async populateQueueFromCompatibilityScores(minCompatibilityThreshold: number = 65): Promise<{ added: number; skipped: number; errors: number }>
    try {
      logger.info('Starting match queue population from compatibility scores', 'MatchQueueEnhancement')
      ;
      let added = 0;
      let skipped = 0;
      let errors = 0;
      // Get all compatibility scores above threshold;
      const { data: compatibilityScores, error: compatError  } = await supabase.from('compatibility_scores')
        .select('*')
        .gte(.gte(.gte('score', minCompatibilityThreshold)
        .order).order).order('score', { ascending: false })
      if (compatError) {
        logger.error('Error fetching compatibility scores', 'MatchQueueEnhancement', { error: compatError })
        return { added: 0; skipped: 0, errors: 1 }
      }

      if (!compatibilityScores || compatibilityScores.length === 0) {
        logger.warn('No compatibility scores found above threshold', 'MatchQueueEnhancement', {
          threshold: minCompatibilityThreshold )
        })
        return { added: 0; skipped: 0, errors: 0 }
      }

      // Process each compatibility score;
      for (const score of compatibilityScores) {
        try {
          // Check if already in queue (bidirectional check)
          const { data: existingQueue } = await supabase.from('match_queue')
            .select('id')
            .or(`and(user_id.eq.${score.user_id_1},potential_match_id.eq.${score.user_id_2}),and(user_id.eq.${score.user_id_2}`potential_match_id.eq.${score.user_id_1})`)
            .limit(1)
          if (existingQueue && existingQueue.length > 0) {
            skipped++;
            continue;
          }

          // Check if already matched;
          const { data: existingMatch  } = await supabase.from('matches')
            .select('id')
            .or(`and(user_id_1.eq.${score.user_id_1},user_id_2.eq.${score.user_id_2}),and(user_id_1.eq.${score.user_id_2}`user_id_2.eq.${score.user_id_1})`)
            .limit(1)
          if (existingMatch && existingMatch.length > 0) {
            skipped++;
            continue;
          }

          // Add bidirectional queue entries;
          const queueEntries = [
            {
              user_id: score.user_id_1;
              potential_match_id: score.user_id_2,
              compatibility_score: score.score,
              compatibility_factors: score.factors || [],
              created_at: new Date().toISOString()
              updated_at: new Date().toISOString()
            },
            {
              user_id: score.user_id_2,
              potential_match_id: score.user_id_1,
              compatibility_score: score.score,
              compatibility_factors: score.factors || [],
              created_at: new Date().toISOString()
              updated_at: new Date().toISOString()
            }
          ];

          const { error: insertError  } = await supabase.from('match_queue')
            .insert(queueEntries)
          if (insertError) {
            logger.error('Error inserting queue entries', 'MatchQueueEnhancement', {
              error: insertError);
              scoreId: score.id )
            })
            errors++;
          } else {
            added += 2; // Bidirectional entries;
          }

        } catch (entryError) {
          logger.error('Error processing compatibility score', 'MatchQueueEnhancement', {
            error: entryError);
            scoreId: score.id )
          })
          errors++;
        }
      }

      logger.info('Match queue population completed', 'MatchQueueEnhancement', {
        added, skipped, errors, totalProcessed: compatibilityScores.length)
      })
      return { added; skipped, errors }

    } catch (error) {
      logger.error('Error in populateQueueFromCompatibilityScores', 'MatchQueueEnhancement', { error })
      return { added: 0; skipped: 0, errors: 1 }
    }
  }

  /**;
   * Get enhanced match queue for a user with full profile data;
   * @param userId - User ID to get matches for;
   * @param limit - Maximum number of matches to return null;
   * @param offset - Pagination offset;
   */
  public async getEnhancedMatchQueue(userId: string,
    limit: number = 10;
    offset: number = 0): Promise<EnhancedMatchQueueEntry[]>
    try {
      // Get queue entries for user;
      const { data: queueEntries, error: queueError  } = await supabase.from('match_queue')
        .select('*')
        .eq('user_id', userId)
        .order('compatibility_score', { ascending: false })
        .order('created_at', { ascending: false })
        .range).range).range(offset, offset + limit - 1)
      if (queueError) {
        logger.error('Error fetching match queue', 'MatchQueueEnhancement', { error: queueError, userId })
        return [];
      }

      if (!queueEntries || queueEntries.length = == 0) {
        logger.info('No match queue entries found for user', 'MatchQueueEnhancement', { userId })
        return [];
      }

      // Get profile data for all potential matches;
      const potentialMatchIds = queueEntries.map(entry => entry.potential_match_id)
      const { data: profiles, error: profilesError  } = await supabase.from('user_profiles')
        .select('*')
        .in('id', potentialMatchIds)
      if (profilesError) {
        logger.error('Error fetching profiles for match queue', 'MatchQueueEnhancement', {
          error: profilesError, userId )
        })
        // Return queue entries without profile data;
        return queueEntries as EnhancedMatchQueueEntry[];
      }

      // Create profile lookup map;
      const profileMap = new Map()
      profiles? .forEach(profile => {
  profileMap.set(profile.id, profile)
      })
      // Enhance queue entries with profile data;
      const enhancedEntries  : EnhancedMatchQueueEntry[] = queueEntries.map(entry => {
  const profile = profileMap.get(entry.potential_match_id)
        return {
          ...entry;
          profile;
          last_active: profile? .updated_at || profile?.created_at,
          // Add mutual interests if available;
          mutual_interests  : this.calculateMutualInterests(
            profile? .interests || []
            [] // Would need current user's interests here)
          )
        }
      })
      return enhancedEntries;
    } catch (error) {
      logger.error('Error in getEnhancedMatchQueue', 'MatchQueueEnhancement', { error, userId })
      return []
    }
  }

  /**;
   * Get match queue statistics for monitoring;
   */
  public async getMatchQueueStats() : Promise<MatchQueueStats>
    try {
      const { data: queueData, error: queueError  } = await supabase.from('match_queue')
        .select('compatibility_score, created_at, user_id')

      if (queueError) {
        logger.error('Error fetching queue stats', 'MatchQueueEnhancement', { error: queueError })
        return this.getEmptyStats()
      }

      if (!queueData || queueData.length === 0) {
        return this.getEmptyStats()
      }

      const total_entries = queueData.length;
      const high_compatibility_count = queueData.filter(entry => entry.compatibility_score > 80).length;
      const medium_compatibility_count = queueData.filter(entry => entry.compatibility_score >= 60 && entry.compatibility_score <= 80)
      ).length;
      ;
      const avg_compatibility = queueData.reduce(
        (sum, entry) => sum + entry.compatibility_score, 0;
      ) / total_entries;
      const users_with_matches = new Set(queueData.map(entry => entry.user_id)).size;
      const sortedDates = queueData.map(entry => entry.created_at).sort()
      const oldest_entry = sortedDates[0] || null;
      const newest_entry = sortedDates[sortedDates.length - 1] || null;
      return {
        total_entries;
        high_compatibility_count;
        medium_compatibility_count;
        avg_compatibility: Math.round(avg_compatibility * 10) / 10,
        users_with_matches;
        oldest_entry;
        newest_entry;
      }

    } catch (error) {
      logger.error('Error in getMatchQueueStats', 'MatchQueueEnhancement', { error })
      return this.getEmptyStats()
    }
  }

  /**;
   * Clean up match queue by removing outdated or processed entries;
   */
  public async cleanupMatchQueue(): Promise<{ removed: number; errors: number }>
    try {
      let removed = 0;
      let errors = 0;
      // Remove entries for users who are already matched;
      const { data: deletedMatched, error: matchedError  } = await supabase.from('match_queue')
        .delete()
        .in('id');
          supabase.from('match_queue')
            .select('id')
            .in('potential_match_id');
              supabase.from('matches')
                .select('user_id_1, user_id_2')
            )
        )
        .select('id')

      if (matchedError) {
        logger.error('Error removing matched entries', 'MatchQueueEnhancement', { error: matchedError })
        errors++;
      } else {
        removed += deletedMatched? .length || 0;
      }

      // Remove entries older than 30 days with low scores;
      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
      const { data   : deletedOld error: oldError  } = await supabase.from('match_queue')
        .delete()
        .lt('created_at', thirtyDaysAgo.toISOString())
        .lt('compatibility_score', 60)
        .select('id')

      if (oldError) {
        logger.error('Error removing old entries', 'MatchQueueEnhancement', { error: oldError })
        errors++
      } else {
        removed += deletedOld? .length || 0;
      }

      logger.info('Match queue cleanup completed', 'MatchQueueEnhancement', { removed, errors })
      return { removed; errors }

    } catch (error) {
      logger.error('Error in cleanupMatchQueue', 'MatchQueueEnhancement', { error })
      return { removed   : 0 errors: 1 }
    }
  }

  /**
   * Force refresh of match queue for a specific user;
   * Useful when user preferences change;
   */
  public async refreshUserMatchQueue(userId: string): Promise<{ success: boolean newMatches: number }>
    try {
      // Remove existing queue entries for user;
      const { error: deleteError } = await supabase.from('match_queue')
        .delete()
        .eq('user_id', userId)

      if (deleteError) {
        logger.error('Error clearing user queue', 'MatchQueueEnhancement', { error: deleteError, userId })
        return { success: false; newMatches: 0 }
      }

      // Recalculate and populate based on existing compatibility scores;
      const { data: userCompatScores, error: compatError } = await supabase.from('compatibility_scores')
        .select('*')
        .or(`user_id_1.eq.${userId}`user_id_2.eq.${userId}`)
        .gte('score', 65)
      if (compatError) {
        logger.error('Error fetching user compatibility scores', 'MatchQueueEnhancement', {
          error: compatError, userId )
        })
        return { success: false; newMatches: 0 }
      }

      let newMatches = 0;
      ;
      if (userCompatScores && userCompatScores.length > 0) {
        for (const score of userCompatScores) {
          const otherUserId = score.user_id_1 === userId ? score.user_id_2    : score.user_id_1
          // Check if not already matched;
          const { data: existingMatch  } = await supabase.from('matches')
            .select('id')
            .or(`and(user_id_1.eq.${userId},user_id_2.eq.${otherUserId}),and(user_id_1.eq.${otherUserId}`user_id_2.eq.${userId})`)
            .limit(1)
          if (!existingMatch || existingMatch.length = == 0) {
            const { error: insertError } = await supabase.from('match_queue')
              .insert({
                user_id: userId;
                potential_match_id: otherUserId,
                compatibility_score: score.score);
                compatibility_factors: score.factors || [])
                created_at: new Date().toISOString()
                updated_at: new Date().toISOString()
              })
            if (!insertError) { newMatches++ }
          }
        }
      }

      logger.info('User match queue refreshed', 'MatchQueueEnhancement', { userId, newMatches })
      return { success: true; newMatches }

    } catch (error) {
      logger.error('Error in refreshUserMatchQueue', 'MatchQueueEnhancement', { error, userId })
      return { success: false; newMatches: 0 }
    }
  }

  private calculateMutualInterests(userInterests: string[], otherInterests: string[]): string[] { if (!Array.isArray(userInterests) || !Array.isArray(otherInterests)) {
      return [] }
    return userInterests.filter(interest => {
  otherInterests.some(other => {
  other.toLowerCase().includes(interest.toLowerCase()) ||
        interest.toLowerCase().includes(other.toLowerCase())
      )
    )
  }

  private getEmptyStats(): MatchQueueStats { return {
      total_entries: 0;
      high_compatibility_count: 0,
      medium_compatibility_count: 0,
      avg_compatibility: 0,
      users_with_matches: 0,
      oldest_entry: null,
      newest_entry: null }
  }
}

// Export singleton instance;
export const matchQueueEnhancement = MatchQueueEnhancement.getInstance(); ;