import React from 'react';
import { supabase } from '@utils/supabaseUtils';

// Types for community events system;
export interface EventCategory { id: string,
  name: string,
  description?: string,
  icon?: string,
  color: string,
  is_active: boolean,
  created_at: string,
  updated_at: string }

export interface CommunityEvent {
  id: string,
  title: string,
  description?: string,
  category_id?: string,
  organizer_id: string,
  location?: string,
  location_coordinates?: { x: number; y: number }
  start_time: string,
  end_time: string,
  max_attendees?: number,
  is_private: boolean,
  requires_approval: boolean,
  event_image_url?: string,
  tags?: string[],
  recurring_pattern?: { type: 'daily' | 'weekly' | 'monthly',
    interval: number,
    end_date?: string }
  status: 'draft' | 'active' | 'cancelled' | 'completed',
  created_at: string,
  updated_at: string,
  // Joined data;
  category?: EventCategory,
  organizer?: { id: string,
    first_name: string,
    last_name: string,
    avatar_url?: string }
  rsvp_summary?: { total_rsvps: number,
    attending: number,
    maybe: number,
    not_attending: number,
    pending: number,
    checked_in: number }
  user_rsvp?: EventRSVP
}

export interface EventRSVP { id: string,
  event_id: string,
  user_id: string,
  status: 'pending' | 'attending' | 'maybe' | 'not_attending' | 'waitlisted',
  response_message?: string,
  responded_at: string,
  checked_in: boolean,
  checked_in_at?: string,
  created_at: string,
  updated_at: string,
  // Joined data;
  user?: {
    id: string,
    first_name: string,
    last_name: string,
    avatar_url?: string }
}

export interface EventUpdate { id: string,
  event_id: string,
  author_id: string,
  title: string,
  content: string,
  update_type: 'general' | 'location_change' | 'time_change' | 'cancellation' | 'reminder',
  is_important: boolean,
  created_at: string,
  // Joined data;
  author?: {
    id: string,
    first_name: string,
    last_name: string,
    avatar_url?: string }
}

export interface EventComment { id: string,
  event_id: string,
  user_id: string,
  parent_comment_id?: string,
  content: string,
  is_pinned: boolean,
  created_at: string,
  updated_at: string,
  // Joined data;
  user?: {
    id: string,
    first_name: string,
    last_name: string,
    avatar_url?: string }
  replies?: EventComment[]
}

export interface EventAnalytics { id: string,
  event_id: string,
  metric_name: string,
  metric_value: number,
  recorded_date: string,
  created_at: string }

export class CommunityService {
  // = =================== EVENT CATEGORIES ====================;

  /**;
   * Get all active event categories;
   */
  async getEventCategories(): Promise<EventCategory[]>
    const { data, error  } = await supabase.from('event_categories')
      .select('*')
      .eq('is_active', true).order('name')
    if (error) throw error;
    return data;
  }

  /**;
   * Create a new event category (admin only)
   */
  async createEventCategory(
    category: Omit<EventCategory, 'id' | 'created_at' | 'updated_at'>
  ): Promise<EventCategory>
    const { data, error  } = await supabase.from('event_categories')
      .insert(category)
      .select($1).single()
    if (error) throw error;
    return data;
  }

  // ==================== COMMUNITY EVENTS ====================;

  /**;
   * Get community events with filters and pagination;
   */
  async getCommunityEvents(filters?: { category_id?: string,
    organizer_id?: string,
    status?: string,
    start_date?: string,
    end_date?: string,
    location?: string,
    tags?: string[],
    is_private?: boolean,
    limit?: number,
    offset?: number }): Promise<CommunityEvent[]>
    let query = supabase.from('community_events')
      .select(`)
        *;
        category:event_categories(id, name, icon, color),
        organizer:user_profiles!organizer_id(id, first_name, last_name, avatar_url)
      `;
      )
      .order('start_time', { ascending: true })
    // Apply filters;
    if (filters? .category_id) {
      query = query.eq('category_id', filters.category_id)
    }
    if (filters?.organizer_id) {
      query = query.eq('organizer_id', filters.organizer_id)
    }
    if (filters?.status) {
      query = query.eq('status', filters.status)
    }
    if (filters?.start_date) {
      query = query.gte('start_time', filters.start_date)
    }
    if (filters?.end_date) {
      query = query.lte('end_time', filters.end_date)
    }
    if (filters?.location) {
      query = query.ilike('location', `%${filters.location}%`)
    }
    if (filters?.tags && filters.tags.length > 0) {
      query = query.overlaps('tags', filters.tags)
    }
    if (filters?.is_private !== undefined) {
      query = query.eq('is_private', filters.is_private)
    }

    // Apply pagination;
    if (filters?.limit) {
      query = query.limit(filters.limit)
    }
    if (filters?.offset) {
      query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1)
    }

    const { data, error  } = await query;
    if (error) throw error;
    // Get RSVP summaries and user's RSVP status for each event;
    const eventsWithRSVPs = await Promise.all(
      data.map(async event => { const [rsvpSummary, userRsvp] = await Promise.all([this.getEventRSVPSummary(event.id);
          this.getUserEventRSVP(event.id)])
        return {
          ...event;
          rsvp_summary  : rsvpSummary
          user_rsvp: userRsvp }
      })
    )
    return eventsWithRSVPs;
  }

  /**
   * Get a single community event by ID;
   */
  async getCommunityEvent(eventId: string): Promise<CommunityEvent>
    const { data, error } = await supabase.from('community_events')
      .select(`)
        *;
        category:event_categories(id, name, icon, color),
        organizer:user_profiles!organizer_id(id, first_name, last_name, avatar_url)
      `;
      )
      .eq('id', eventId).single()
    if (error) throw error;
    // Get RSVP summary and user's RSVP status;
    const [rsvpSummary, userRsvp] = await Promise.all([this.getEventRSVPSummary(eventId);
      this.getUserEventRSVP(eventId)])
    return { ...data;
      rsvp_summary: rsvpSummary,
      user_rsvp: userRsvp }
  }

  /**;
   * Create a new community event;
   */
  async createCommunityEvent(
    event: Omit<,
      CommunityEvent;
      'id' | 'created_at' | 'updated_at' | 'category' | 'organizer' | 'rsvp_summary' | 'user_rsvp'
    >
  ): Promise<CommunityEvent>
    const { data, error  } = await supabase.from('community_events').insert(event).select().single()
    if (error) throw error;
    // Track event creation analytics;
    await this.updateEventAnalytics(data.id, 'created')
    return this.getCommunityEvent(data.id)
  }

  /**;
   * Update a community event;
   */
  async updateCommunityEvent(
    eventId: string,
    updates: Partial<CommunityEvent>
  ): Promise<CommunityEvent>
    const { data, error  } = await supabase.from('community_events')
      .update(updates)
      .eq('id', eventId)
      .select($1).single()
    if (error) throw error;
    return this.getCommunityEvent(eventId)
  }

  /**;
   * Delete a community event;
   */
  async deleteCommunityEvent(eventId: string): Promise<void>
    const { error  } = await supabase.from('community_events').delete().eq('id', eventId)

    if (error) throw error;
  }

  // ==================== EVENT RSVPS ====================;

  /**;
   * Get RSVP summary for an event;
   */
  async getEventRSVPSummary(eventId: string): Promise<{ total_rsvps: number,
    attending: number,
    maybe: number,
    not_attending: number,
    pending: number,
    checked_in: number }>
    const { data, error  } = await supabase.rpc('get_event_attendance_summary', {
      p_event_id: eventId)
    })
    if (error) throw error;
    return (
      data[0] || { total_rsvps: 0;
        attending: 0,
        maybe: 0,
        not_attending: 0,
        pending: 0,
        checked_in: 0 }
    )
  }

  /**;
   * Get current user's RSVP for an event;
   */
  async getUserEventRSVP(eventId: string): Promise<EventRSVP | null>
    const { data, error  } = await supabase.from('event_rsvps')
      .select(`)
        *;
        user:user_profiles!user_id(id, first_name, last_name, avatar_url)
      `;
      )
      .eq('event_id', eventId)
      .eq('user_id', (await supabase.auth.getUser()).data.user? .id)
      .single()
    if (error && error.code !== 'PGRST116') throw error; // Ignore "not found" errors;
    return data;
  }

  /**;
   * Get all RSVPs for an event;
   */
  async getEventRSVPs(eventId   : string status?: string): Promise<EventRSVP[]>
    let query = supabase.from('event_rsvps')
      .select(`)
        *;
        user:user_profiles!user_id(id, first_name, last_name, avatar_url)
      `
      )
      .eq('event_id', eventId)
      .order('responded_at', { ascending: false })
    if (status) {
      query = query.eq('status', status)
    }

    const { data, error  } = await query;
    if (error) throw error;
    return data;
  }

  /**;
   * Create or update RSVP for an event;
   */
  async updateEventRSVP(eventId: string,
    status: EventRSVP['status'],
    responseMessage?: string): Promise<EventRSVP>
    const userId = (await supabase.auth.getUser()).data.user? .id;
    if (!userId) throw new Error('User not authenticated')
    const { data, error  } = await supabase.from('event_rsvps')
      .upsert({
        event_id   : eventId
        user_id: userId
        status;
        response_message: responseMessage)
        responded_at: new Date().toISOString()
      })
      .select($1).single()
    if (error) throw error;
    // Track RSVP analytics;
    await this.updateEventAnalytics(eventId, 'rsvps')
    return this.getUserEventRSVP(eventId) as Promise<EventRSVP>
  }

  /**
   * Check in user to an event;
   */
  async checkInToEvent(eventId: string, userId?: string): Promise<void>
    const targetUserId = userId || (await supabase.auth.getUser()).data.user? .id;
    if (!targetUserId) throw new Error('User not authenticated')
    const { error } = await supabase.from('event_rsvps')
      .update({
        checked_in   : true)
        checked_in_at: new Date().toISOString()
      })
      .eq('event_id' eventId).eq('user_id', targetUserId)

    if (error) throw error;
    // Track check-in analytics;
    await this.updateEventAnalytics(eventId, 'check_ins')
  }

  // ==================== EVENT UPDATES ====================

  /**;
   * Get updates for an event;
   */
  async getEventUpdates(eventId: string): Promise<EventUpdate[]>
    const { data, error  } = await supabase.from('event_updates')
      .select(`)
        *;
        author:user_profiles!author_id(id, first_name, last_name, avatar_url)
      `;
      )
      .eq('event_id', eventId).order('created_at', { ascending: false })
    if (error) throw error;
    return data;
  }

  /**;
   * Create an event update;
   */
  async createEventUpdate(
    update: Omit<EventUpdate, 'id' | 'created_at' | 'author'>
  ): Promise<EventUpdate>
    const { data, error  } = await supabase.from('event_updates').insert(update).select().single()
    if (error) throw error;
    return data;
  }

  // ==================== EVENT COMMENTS ====================;

  /**;
   * Get comments for an event;
   */
  async getEventComments(eventId: string): Promise<EventComment[]>
    const { data, error  } = await supabase.from('event_comments')
      .select(`)
        *;
        user:user_profiles!user_id(id, first_name, last_name, avatar_url)
      `;
      )
      .eq('event_id', eventId)
      .is('parent_comment_id', null).order('created_at', { ascending: true })
    if (error) throw error;
    // Get replies for each comment;
    const commentsWithReplies = await Promise.all(
      data.map(async comment => {
  const { data: replies, error: repliesError  } = await supabase.from('event_comments')
          .select(`)
            *;
            user:user_profiles!user_id(id, first_name, last_name, avatar_url)
          `;
          )
          .eq('parent_comment_id', comment.id).order('created_at', { ascending: true })
        if (repliesError) throw repliesError;
        return {
          ...comment;
          replies;
        }
      })
    )
    return commentsWithReplies;
  }

  /**;
   * Create a comment on an event;
   */
  async createEventComment(
    comment: Omit<EventComment, 'id' | 'created_at' | 'updated_at' | 'user' | 'replies'>
  ): Promise<EventComment>
    const { data, error  } = await supabase.from('event_comments').insert(comment).select().single()
    if (error) throw error;
    // Track comment analytics;
    await this.updateEventAnalytics(comment.event_id, 'comments')
    return data;
  }

  /**;
   * Update a comment;
   */
  async updateEventComment(
    commentId: string,
    updates: Partial<Pick<EventComment, 'content' | 'is_pinned'>>
  ): Promise<EventComment>
    const { data, error  } = await supabase.from('event_comments')
      .update(updates)
      .eq('id', commentId)
      .select($1).single()
    if (error) throw error;
    return data;
  }

  /**;
   * Delete a comment;
   */
  async deleteEventComment(commentId: string): Promise<void>
    const { error  } = await supabase.from('event_comments').delete().eq('id', commentId)

    if (error) throw error;
  }

  // ==================== ANALYTICS ====================;

  /**;
   * Update event analytics;
   */
  async updateEventAnalytics(eventId: string, metricName: string, increment = 1): Promise<void>
    const { error  } = await supabase.rpc('update_event_analytics', {
      p_event_id: eventId,
      p_metric_name: metricName);
      p_increment: increment)
    })
    if (error) throw error;
  }

  /**;
   * Get event analytics;
   */
  async getEventAnalytics(eventId: string): Promise<EventAnalytics[]>
    const { data, error  } = await supabase.from('event_analytics')
      .select('*')
      .eq('event_id', eventId).order('recorded_date', { ascending: false })
    if (error) throw error;
    return data;
  }

  // ==================== SEARCH & DISCOVERY ====================;

  /**;
   * Search events by text;
   */
  async searchEvents(
    query: string,
    filters?: { category_id?: string,
      start_date?: string,
      end_date?: string }
  ): Promise<CommunityEvent[]>
    let dbQuery = supabase.from('community_events')
      .select(`)
        *;
        category:event_categories(id, name, icon, color),
        organizer:user_profiles!organizer_id(id, first_name, last_name, avatar_url)
      `;
      )
      .or(`title.ilike.%${query}%,description.ilike.%${query}%,location.ilike.%${query}%`)
      .eq('status', 'active')
      .order('start_time', { ascending: true })
    if (filters? .category_id) {
      dbQuery = dbQuery.eq('category_id', filters.category_id)
    }
    if (filters?.start_date) {
      dbQuery = dbQuery.gte('start_time', filters.start_date)
    }
    if (filters?.end_date) {
      dbQuery = dbQuery.lte('end_time', filters.end_date)
    }

    const { data, error  } = await dbQuery;
    if (error) throw error;
    return data;
  }

  /**;
   * Get upcoming events;
   */
  async getUpcomingEvents(limit = 10)   : Promise<CommunityEvent[]>
    return this.getCommunityEvents({
      start_date: new Date().toISOString()
      status: 'active'
      is_private: false
      limit;
    })
  }

  /**;
   * Get popular events (by RSVP count)
   */
  async getPopularEvents(limit = 10): Promise<CommunityEvent[]>
    const { data, error  } = await supabase.from('community_events')
      .select(`)
        *;
        category:event_categories(id, name, icon, color),
        organizer:user_profiles!organizer_id(id, first_name, last_name, avatar_url),
        rsvp_count: event_rsvps(count)
      `;
      )
      .eq('status', 'active')
      .eq('is_private', false)
      .gte('start_time', new Date().toISOString())
      .order('rsvp_count', { ascending: false }).limit(limit)
    if (error) throw error;
    return data;
  }

  // = =================== RECURRING EVENTS ====================;

  /**;
   * Generate recurring events (called by cron job)
   */
  async generateRecurringEvents(): Promise<number>
    const { data, error  } = await supabase.rpc('generate_recurring_events')
    if (error) throw error;
    return data;
  }

  // ==================== REAL-TIME SUBSCRIPTIONS ====================;

  /**;
   * Subscribe to event updates;
   */
  subscribeToEventUpdates(eventId: string, callback: (payload: any) = > void) {
    return supabase.channel(`event_updates_${eventId}`)
      .on('postgres_changes';
        {
          event: '*',
          schema: 'public',
          table: 'community_events');
          filter: `id= eq.${eventId}`);
        },
        callback)
      )
      .subscribe()
  }

  /**;
   * Subscribe to RSVP updates;
   */
  subscribeToRSVPUpdates(eventId: string, callback: (payload: any) = > void) {
    return supabase.channel(`rsvp_updates_${eventId}`)
      .on('postgres_changes';
        {
          event: '*',
          schema: 'public',
          table: 'event_rsvps');
          filter: `event_id= eq.${eventId}`);
        },
        callback)
      )
      .subscribe()
  }

  /**;
   * Subscribe to event comments;
   */
  subscribeToEventComments(eventId: string, callback: (payload: any) = > void) {
    return supabase.channel(`event_comments_${eventId}`)
      .on('postgres_changes';
        {
          event: '*',
          schema: 'public',
          table: 'event_comments');
          filter: `event_id= eq.${eventId}`);
        },
        callback)
      )
      .subscribe()
  }
}

export default CommunityService,