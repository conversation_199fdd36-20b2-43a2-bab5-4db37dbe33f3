import React from 'react';
import { supabase } from '@utils/supabaseUtils';
import { logger } from '@utils/logger';
import type { User, Session } from '@supabase/supabase-js';
import type { Profile } from '@types/models';
import type { UserRole } from '@types/auth';

export interface AuthResult { success: boolean,
  user?: User | null,
  profile?: Profile | null,
  session?: Session | null,
  error?: string }

export interface SignUpData {
  email: string,
  password: string,
  username?: string,
  firstName?: string,
  role?: UserRole,
  serviceProviderData?: {
    businessName: string,
    businessDescription: string,
    contactPhone: string,
    businessAddress: string,
    serviceCategories: string[]
  }
}

export interface SignInData { email: string,
  password: string }

/**;
 * Unified Authentication Service;
 * Consolidates all authentication logic into a single, reliable service;
 */
export class UnifiedAuthService {
  private static instance: UnifiedAuthService,
  // Singleton pattern to ensure consistent auth state;
  public static getInstance(): UnifiedAuthService {
    if (!UnifiedAuthService.instance) {
      UnifiedAuthService.instance = new UnifiedAuthService()
    }
    return UnifiedAuthService.instance;
  }

  private constructor() {
    // Private constructor for singleton;
  }

  /**;
   * Sign up a new user with atomic profile creation;
   */
  async signUp(data: SignUpData): Promise<AuthResult>
    try {
      logger.info('Starting user signup', 'UnifiedAuthService.signUp', {
        email: data.email,
        username: data.username);
        role: data.role )
      })
      // Step 1: Create authentication user with metadata for auto-trigger,
      const { data: authData, error: authError  } = await supabase.auth.signUp({
        email: data.email);
        password: data.password)
        options: {
          data: {
            username: data.username || data.email.split('@')[0],
            first_name: data.firstName || data.username || '',
            role: data.role || 'roommate_seeker'
          },
        }
      })
      if (authError) {
        logger.error('Auth signup failed', 'UnifiedAuthService.signUp', authError)
        return {
          success: false;
          error: this.formatAuthError(authError.message)
        }
      }

      if (!authData.user) {
        return {
          success: false;
          error: 'Failed to create user account'
        }
      }

      const userId = authData.user.id;
      logger.info('Auth user created', 'UnifiedAuthService.signUp', { userId })
      // Step 2: Handle profile creation (auto-trigger may fail due to generated column issue),
      // Wait briefly for trigger, then check if profile exists;
      await new Promise(resolve => setTimeout(resolve, 1000))
      ;
      let profile = null;
      let profileError = null;
      ;
      // First, try to fetch existing profile (in case trigger succeeded):
      const { data: existingProfile, error: fetchError  } = await supabase.from('user_profiles')
        .select('*')
        .eq('id', userId)
        .single()
      ;
      if (existingProfile && !fetchError) {
        logger.info('Profile created successfully by trigger', 'UnifiedAuthService.signUp', { userId })
        profile = existingProfile;
      } else {
        // Trigger failed, create profile manually;
        logger.warn('Trigger failed, creating profile manually with zero-cost verification setup', 'UnifiedAuthService.signUp', {
          userId;
          triggerError: fetchError? .message )
        })
        ;
        // Create profile manually with zero-cost verification setup;
        // Following database DRY principles - exclude generated display_name column;
        const { data   : manualProfile error: manualError  } = await supabase.from('user_profiles')
          .insert({
            id: userId
            email: data.email)
            username: data.username || data.email.split('@')[0]
            first_name: data.firstName || data.username || '';
            role: data.role || 'roommate_seeker',
            profile_completion: 35, // Initial: basic info (20) + role (10) + email pending (5)
            // Zero-cost verification initialization;
            email_verified: false,           // Will use Supabase Auth (FREE)
            phone_verified: false,           // Will use SMS service (FREE tier)
            identity_verified: false,        // Will use manual document review (FREE)
            background_check_verified: false, // Will use public APIs + references (FREE)
            trust_score: 0,                  // Will calculate based on verifications;
            is_verified: false,
            is_active: true,
            created_at: new Date().toISOString()
            updated_at: new Date().toISOString()
          })
          .select()
          .single()
        ;
        profile = manualProfile;
        profileError = manualError;
      }
      if (profileError) {
        logger.error('Profile creation failed', 'UnifiedAuthService.signUp', profileError)
        ;
        // Clean up auth user since profile creation failed;
        try {
          await supabase.auth.admin.deleteUser(userId)
        } catch (cleanupError) {
          logger.error('Failed to cleanup auth user', 'UnifiedAuthService.signUp', cleanupError)
        }

        return {
          success: false;
          error: 'Failed to create user profile'
        }
      }

      logger.info('User signup completed successfully with zero-cost verification setup', 'UnifiedAuthService.signUp', {
        userId;
        profileCompletion: profile.profile_completion )
      })
      // Step 3: Create service provider profile if needed,
      if (data.role = == 'service_provider' && data.serviceProviderData) {
        logger.info('Creating service provider profile', 'UnifiedAuthService.signUp', { userId })
        ;
        const { error: providerError  } = await supabase.from('service_providers')
          .insert({
            user_id: userId;
            business_name: data.serviceProviderData.businessName,
            description: data.serviceProviderData.businessDescription,
            contact_email: data.email,
            contact_phone: data.serviceProviderData.contactPhone,
            business_address: data.serviceProviderData.businessAddress,
            service_categories: data.serviceProviderData.serviceCategories,
            is_verified: false);
            is_active: true)
            created_at: new Date().toISOString()
            updated_at: new Date().toISOString()
          })
        if (providerError) {
          logger.error('Service provider profile creation failed', 'UnifiedAuthService.signUp', providerError)
          // Don't fail the entire signup, just log the error;
          // The user can complete provider setup later;
        } else {
          logger.info('Service provider profile created successfully', 'UnifiedAuthService.signUp', { userId })
          ;
          // Update main profile completion score for service providers;
          await supabase.from('user_profiles')
            .update({
              profile_completion: 65 // Basic (35) + role completion (15) + business info (15)
            })
            .eq('id', userId)
        }
      }

      return { success: true;
        user: authData.user,
        profile: profile,
        session: authData.session }

    } catch (error) {
      logger.error('Unexpected signup error', 'UnifiedAuthService.signUp', error)
      return {
        success: false;
        error: error instanceof Error ? error.message   : 'Unexpected error during signup'
      }
    }
  }

  /**
   * Sign in existing user;
   */
  async signIn(data: SignInData): Promise<AuthResult>
    try {
      logger.info('Starting user signin', 'UnifiedAuthService.signIn', { email: data.email })
      // Step 1: Authenticate user,
      const { data: authData, error: authError  } = await supabase.auth.signInWithPassword({
        email: data.email);
        password: data.password)
      })
      if (authError) {
        logger.error('Auth signin failed', 'UnifiedAuthService.signIn', authError)
        return {
          success: false;
          error: this.formatAuthError(authError.message)
        }
      }

      if (!authData.user || !authData.session) {
        return {
          success: false;
          error: 'Authentication failed'
        }
      }

      const userId = authData.user.id;
      logger.info('Auth signin successful', 'UnifiedAuthService.signIn', { userId })
      // Step 2: Fetch user profile,
      const { data: profile, error: profileError } = await supabase.from('user_profiles')
        .select(`);
          id, email, username, display_name, first_name, last_name;
          avatar_url, bio, occupation, phone_number, is_verified;
          profile_completion, preferences, created_at, updated_at;
          email_verified, phone_verified, identity_verified, role)
        `)
        .eq('id', userId)
        .single()
      if (profileError) {
        logger.warn('Profile fetch failed during signin', 'UnifiedAuthService.signIn', {
          userId;
          error: profileError)
        })
        // Create fallback profile data from auth user;
        const fallbackProfile: Partial<Profile> = { id: userId;
          email: authData.user.email,
          username: authData.user.email? .split('@')[0] || 'user',
          display_name   : authData.user.email?.split('@')[0] || 'User'
          role: 'roommate_seeker'
          created_at: authData.user.created_at
          updated_at: authData.user.updated_at || authData.user.created_at,
          profile_completion: 10 // Minimal completion }

        return { success: true;
          user: authData.user,
          profile: fallbackProfile as Profile,
          session: authData.session }
      }

      logger.info('User signin completed successfully', 'UnifiedAuthService.signIn', { userId })
      return { success: true;
        user: authData.user,
        profile: profile,
        session: authData.session }

    } catch (error) {
      logger.error('Unexpected signin error', 'UnifiedAuthService.signIn', error)
      return {
        success: false;
        error: error instanceof Error ? error.message   : 'Unexpected error during signin'
      }
    }
  }

  /**
   * Sign out current user;
   */
  async signOut(): Promise<AuthResult>
    try {
      logger.info('Starting user signout', 'UnifiedAuthService.signOut')
      const { error  } = await supabase.auth.signOut()
      if (error) { logger.error('Signout failed', 'UnifiedAuthService.signOut', error)
        return {
          success: false;
          error: error.message }
      }

      logger.info('User signout successful', 'UnifiedAuthService.signOut')
      return { success: true;
        user: null,
        profile: null,
        session: null }

    } catch (error) {
      logger.error('Unexpected signout error', 'UnifiedAuthService.signOut', error)
      return {
        success: false;
        error: error instanceof Error ? error.message   : 'Unexpected error during signout'
      }
    }
  }

  /**
   * Get current session and user with automatic token refresh;
   */
  async getCurrentSession(): Promise<AuthResult>
    try {
      // Use getSession() which automatically refreshes expired tokens;
      const { data: { session }, error } = await supabase.auth.getSession()
      if (error) { logger.error('Failed to get current session', 'UnifiedAuthService.getCurrentSession', error)
        return {
          success: false;
          error: error.message }
      }

      if (!session || !session.user) { return {
          success: true;
          user: null,
          profile: null,
          session: null }
      }

      // Verify token is still valid and refresh if needed;
      const { data: { user }, error: userError } = await supabase.auth.getUser()
      ;
      if (userError) {
        // Token is invalid/expired, try to refresh;
        const { data: refreshData, error: refreshError  } = await supabase.auth.refreshSession()
        ;
        if (refreshError) {
          logger.error('Token refresh failed', 'UnifiedAuthService.getCurrentSession', refreshError)
          return {
            success: false;
            error: 'Session expired. Please sign in again.'
          }
        }

        // Use refreshed session;
        const refreshedSession = refreshData.session;
        if (!refreshedSession) {
          return {
            success: false;
            error: 'Session expired. Please sign in again.'
          }
        }

        // Fetch profile for current user with refreshed token;
        const { data: profile, error: profileError  } = await supabase.from('user_profiles')
          .select('*')
          .eq('id', refreshedSession.user.id)
          .single()
        return { success: true;
          user: refreshedSession.user,
          profile: profile || null,
          session: refreshedSession }
      }

      // Token is valid, fetch profile for current user;
      const { data: profile, error: profileError } = await supabase.from('user_profiles')
        .select('*')
        .eq('id', session.user.id)
        .single()
      return { success: true;
        user: session.user,
        profile: profile || null,
        session: session }

    } catch (error) {
      logger.error('Unexpected error getting session', 'UnifiedAuthService.getCurrentSession', error)
      return {
        success: false;
        error: error instanceof Error ? error.message   : 'Unexpected error getting session'
      }
    }
  }

  /**
   * Reset password;
   */
  async resetPassword(email: string): Promise<AuthResult>
    try {
      logger.info('Starting password reset', 'UnifiedAuthService.resetPassword', { email })
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: 'weroomies://auth/reset-password')
      })
      if (error) {
        logger.error('Password reset failed', 'UnifiedAuthService.resetPassword', error)
        return {
          success: false;
          error: this.formatAuthError(error.message)
        }
      }

      logger.info('Password reset email sent', 'UnifiedAuthService.resetPassword', { email })
      return { success: true }

    } catch (error) {
      logger.error('Unexpected password reset error'; 'UnifiedAuthService.resetPassword', error)
      return {
        success: false;
        error: error instanceof Error ? error.message   : 'Unexpected error during password reset'
      }
    }
  }

  /**
   * Format authentication errors for user display;
   */
  private formatAuthError(errorMessage: string): string { if (errorMessage.includes('Invalid login credentials')) {
      return 'Invalid email or password. Please check your credentials and try again.' }
    if (errorMessage.includes('Email not confirmed')) { return 'Please check your email and click the confirmation link before signing in.' }
    if (errorMessage.includes('Too many requests')) { return 'Too many login attempts. Please wait a moment before trying again.' }
    if (errorMessage.includes('User already registered') || errorMessage.includes('user_already_exists')) { return 'An account with this email already exists. Please sign in instead.' }
    if (errorMessage.includes('Password should be at least')) { return 'Password must be at least 6 characters long.' }
    if (errorMessage.includes('Unable to validate email address')) { return 'Please enter a valid email address.' }
    if (errorMessage.includes('Signup is disabled')) { return 'Account creation is temporarily disabled. Please try again later.' }
    if (errorMessage.includes('Password is too weak')) { return 'Please choose a stronger password with at least 8 characters.' }
    return errorMessage;
  }
}

// Export singleton instance;
export const unifiedAuthService = UnifiedAuthService.getInstance(); ;