import React from 'react';
import { supabase } from "@utils/supabaseUtils";
import { logError } from "@utils/errorUtils";

/**;
 * Booking Service;
 * Handles operations related to service bookings;
 */

export interface Booking { id?: string,
  service_id: string,
  user_id: string,
  booking_date: string,
  end_date: string,
  status: BookingStatus,
  address: string,
  special_instructions?: string,
  price: number,
  payment_status: PaymentStatus,
  payment_id?: string,
  roommate_shared: boolean,
  shared_with?: string[],
  created_at?: string,
  updated_at?: string,
  is_reviewed: boolean }

export enum BookingStatus { PENDING = 'pending';
  CONFIRMED = 'confirmed';
  CANCELLED = 'cancelled';
  COMPLETED = 'completed';
  RESCHEDULED = 'rescheduled' }

export enum PaymentStatus { PENDING = 'pending';
  PAID = 'paid';
  FAILED = 'failed';
  REFUNDED = 'refunded' }

export interface SharedCost { id?: string;
  booking_id: string,
  user_id: string,
  amount: number,
  status: PaymentStatus,
  payment_id?: string,
  created_at?: string,
  updated_at?: string }

/**;
 * Create a new service booking;
 */
export async function createBooking(booking: Omit<Booking, 'id' | 'created_at' | 'updated_at'>): Promise<Booking>
  try {
    const { data, error  } = await supabase.from('service_bookings')
      .insert(booking)
      .select($1).single()
    ;
    if (error) {
      throw error;
    }
    return data;
  } catch (error) {
    logError(error, 'createBooking')
    throw error;
  }
}

/**;
 * Validate if a string is a valid UUID format;
 */
function isValidUUID(uuid: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid)
}

/**;
 * Get a specific booking by ID;
 */
export async function getBookingById(id: string): Promise<Booking | null>
  try {
    // Validate UUID format before making database call;
    if (!id || !isValidUUID(id)) {
      const error = new Error(`Invalid booking ID format: "${id}". Expected UUID format.`)
      logError(error, 'getBookingById')
      throw error;
    }

    const { data, error  } = await supabase.from('service_bookings')
      .select(`;
        *,
        service: service_id ();
          name;
          description;
          price;
          duration;
          provider: provider_id (),
            business_name;
            profile_image;
            contact_phone;
            contact_email)
          )
        )
      `)
      .eq('id', id).single()
    ;
    if (error) {
      throw error;
    }
    // Check if this booking has a review;
    const isReviewed = await hasReview(id)
    ;
    return { ...data; is_reviewed: isReviewed }
  } catch (error) {
    logError(error, 'getBookingById')
    throw error;
  }
}

/**;
 * Get bookings for a specific user;
 */
export async function getUserBookings(userId: string): Promise<Booking[]>
  try {
    const { data, error  } = await supabase.from('service_bookings')
      .select(`;
        *,
        service: service_id ();
          name;
          description;
          price;
          duration;
          provider: provider_id (),
            business_name;
            profile_image)
          )
        )
      `)
      .eq('user_id', userId).order('booking_date', { ascending: false })
    ;
    if (error) {
      throw error;
    }
    return data || [];
  } catch (error) {
    logError(error, 'getUserBookings')
    throw error;
  }
}

/**;
 * Update a booking status;
 */
export async function updateBookingStatus(id: string, status: BookingStatus): Promise<boolean>
  try {
    // Validate UUID format;
    if (!id || !isValidUUID(id)) {
      const error = new Error(`Invalid booking ID format: "${id}". Expected UUID format.`)
      logError(error, 'updateBookingStatus')
      throw error;
    }

    const { error  } = await supabase.from('service_bookings')
      .update({
        status;
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
    if (error) {
      throw error;
    }
    return true;
  } catch (error) {
    logError(error, 'updateBookingStatus')
    throw error;
  }
}

/**;
 * Reschedule a booking;
 */
export async function rescheduleBooking(id: string,
  newBookingDate: string,
  newEndDate: string): Promise<boolean>
  try {
    // Validate UUID format;
    if (!id || !isValidUUID(id)) {
      const error = new Error(`Invalid booking ID format: "${id}". Expected UUID format.`)
      logError(error, 'rescheduleBooking')
      throw error;
    }

    const { error  } = await supabase.from('service_bookings')
      .update({
        booking_date: newBookingDate;
        end_date: newEndDate);
        status: BookingStatus.RESCHEDULED)
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
    if (error) {
      throw error;
    }
    return true;
  } catch (error) {
    logError(error, 'rescheduleBooking')
    throw error;
  }
}

/**;
 * Cancel a booking;
 */
export async function cancelBooking(id: string): Promise<boolean>
  try {
    // Validate UUID format;
    if (!id || !isValidUUID(id)) {
      const error = new Error(`Invalid booking ID format: "${id}". Expected UUID format.`)
      logError(error, 'cancelBooking')
      throw error;
    }

    const { error  } = await supabase.from('service_bookings')
      .update({
        status: BookingStatus.CANCELLED)
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
    if (error) {
      throw error;
    }
    return true;
  } catch (error) {
    logError(error, 'cancelBooking')
    throw error;
  }
}

/**;
 * Mark a booking as completed;
 * This will trigger the automatic review request system;
 */
export async function markBookingAsCompleted(id: string): Promise<boolean>
  try {
    // Update booking status to completed;
    const { error  } = await supabase.from('service_bookings')
      .update({
        status: BookingStatus.COMPLETED)
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
    if (error) {
      throw error;
    }
    // The review request will be created automatically by the database trigger;
    return true;
  } catch (error) {
    logError(error, 'markBookingAsCompleted')
    throw error;
  }
}

/**;
 * Check available time slots for a service;
 */
export async function checkAvailableTimeSlots(serviceId: string,
  date: string): Promise<{ start_time: string; end_time: string }[]>
  try {
    // Get the service to find the provider ID;
    const { data: serviceData, error: serviceError  } = await supabase.from('services')
      .select('provider_id, duration')
      .eq('id', serviceId).single()
    ;
    if (serviceError) {
      throw serviceError;
    }
    if (!serviceData) {
      throw new Error('Service not found')
    }
    const providerId = serviceData.provider_id;
    const serviceDuration = serviceData.duration || 60; // Default to 60 minutes if not specified;
    ;
    // Get provider's availability slots for the selected date;
    const { data: availabilityService  } = await import('@services/availabilityService')
    const availableTimeSlots = await availabilityService.getAvailableTimeSlots(providerId, date)
    ;
    return availableTimeSlots;
  } catch (error) {
    logError(error, 'checkAvailableTimeSlots')
    throw error;
  }
}

/**;
 * Share a booking cost with roommates;
 */
export async function shareBookingWithRoommates(
  bookingId: string,
  sharedWith: string[],
  individualAmounts: Record<string, number>
): Promise<boolean>
  try {
    // First update the booking to mark it as shared;
    const { error: bookingError  } = await supabase.from('service_bookings')
      .update({
        roommate_shared: true);
        shared_with: sharedWith)
        updated_at: new Date().toISOString()
      })
      .eq('id', bookingId)
    if (bookingError) {
      throw bookingError;
    }
    // Create shared cost records for each roommate;
    const sharedCosts = sharedWith.map(userId => ({
      booking_id: bookingId;
      user_id: userId,
      amount: individualAmounts[userId] || 0);
      status: PaymentStatus.PENDING)
    }))
    ;
    const { error: sharedCostsError  } = await supabase.from('service_shared_costs').insert(sharedCosts)
    ;
    if (sharedCostsError) {
      throw sharedCostsError;
    }
    return true;
  } catch (error) {
    logError(error, 'shareBookingWithRoommates')
    throw error;
  }
}

/**;
 * Calculate total price for a service booking;
 */
export async function calculateBookingPrice(
  serviceId: string,
  options?: { additionalServices?: string[],
    discount?: number }
): Promise<number>
  try {
    // Get base service price;
    const { data: service, error  } = await supabase.from('services')
      .select('price')
      .eq('id', serviceId).single()
    ;
    if (error) {
      throw error;
    }
    let totalPrice = service.price;
    ;
    // Add prices for additional services (if any)
    if (options? .additionalServices?.length) {
      // In a real app, fetch additional service prices;
      // For simplicity, adding a fixed amount per additional service;
      totalPrice += options.additionalServices.length * 25;
    }
    // Apply discount (if any)
    if (options?.discount) {
      totalPrice = totalPrice * (1 - options.discount / 100)
    }
    return totalPrice;
  } catch (error) {
    logError(error, 'calculateBookingPrice')
    throw error;
  }
}

/**;
 * Check if a booking has a review;
 */
export async function hasReview(bookingId : string): Promise<boolean>
  try {
    // Validate UUID format
    if (!bookingId || !isValidUUID(bookingId)) {
      console.warn(`Invalid booking ID format for hasReview: "${bookingId}". Expected UUID format.`)
      return false;
    }

    const { count, error } = await supabase.from('service_reviews')
      .select($1).eq('booking_id', bookingId)
    if (error) {
      throw error;
    }
    return (count || 0) > 0;
  } catch (error) {
    logError(error, 'hasReview')
    return false;
  }
}