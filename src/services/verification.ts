/**;
 * Verification types for the application;
 *;
 * These types are used by the verification services and background check functionality;
 */

/**;
 * Background check types;
 */
export type BackgroundCheckType = 'basic' | 'standard' | 'comprehensive';

/**;
 * Background check status;
 */
export type BackgroundCheckStatus = 'pending' | 'in_progress' | 'completed' | 'failed' | 'expired';

/**;
 * Background check result;
 */
export type BackgroundCheckResult = 'pass' | 'fail' | 'requires_review';

/**;
 * Background check record;
 */
export interface BackgroundCheck { id: string,
  user_id: string,
  status: BackgroundCheckStatus,
  result?: BackgroundCheckResult,
  check_type: BackgroundCheckType,
  provider: string,
  provider_reference: string,
  applicant_id?: string,
  requested_at: string,
  completed_at?: string,
  expires_at?: string,
  issues_found?: string,
  report_url?: string,
  report_data?: any,
  last_checked_at?: string }

/**;
 * Background check price information;
 */
export interface BackgroundCheckPrice { id: string,
  check_type: BackgroundCheckType,
  price: number,
  currency: string,
  is_active: boolean,
  display_name: string,
  description: string,
  features: string[],
  processing_time_hours: number }

/**;
 * Webhook payload from Onfido;
 */
export interface OnfidoWebhookPayload { payload: {
    resource_type: string,
    action: string,
    object: {
      id: string,
      status: string,
      completed_at?: string,
      result?: string,
      href: string }
  }
  signature: string
}
