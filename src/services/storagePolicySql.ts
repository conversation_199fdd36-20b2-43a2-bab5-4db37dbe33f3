/**;
 * SQL functions for managing storage policies;
 * These functions need to be executed in the Supabase SQL editor;
 */

export const storagePolicySql = { /**;
   * Function to get a storage policy;
   */
  getStoragePolicy: `,
    CREATE OR REPLACE FUNCTION get_storage_policy(bucket_name TEXT, policy_name TEXT)
    RETURNS JSON;
    LANGUAGE plpgsql;
    SECURITY DEFINER;
    AS $$;
    DECLARE;
      policy_exists BOOLEAN;
      policy_info JSON;
    BEGIN;
      -- Check if the policy exists;
      SELECT EXISTS (
        SELECT 1;
        FROM pg_policies;
        WHERE schemaname = 'storage' ;
        AND tablename = 'objects' ;
        AND policyname = policy_name || '_' || bucket_name;
      ) INTO policy_exists;
      ;
      IF policy_exists THEN;
        -- Get policy info;
        SELECT json_build_object(
          'name', policyname;
          'definition', qual;
          'check', with_check;
        )
        FROM pg_policies;
        WHERE schemaname = 'storage' ;
        AND tablename = 'objects' ;
        AND policyname = policy_name || '_' || bucket_name;
        INTO policy_info;
        ;
        RETURN policy_info;
      ELSE;
        RETURN NULL;
      END IF;
    END;
    $$;
  `,

  /**;
   * Function to create a storage policy;
   */
  createStoragePolicy: `,
    CREATE OR REPLACE FUNCTION create_storage_policy(
      bucket_name TEXT;
      policy_name TEXT;
      definition TEXT;
      allowed_operations TEXT[];
    )
    RETURNS VOID;
    LANGUAGE plpgsql;
    SECURITY DEFINER;
    AS $$;
    DECLARE;
      full_policy_name TEXT;
      operation TEXT;
      policy_sql TEXT;
    BEGIN;
      -- Create a unique policy name for the bucket;
      full_policy_name : = policy_name || '_' || bucket_name;
      -- Create policies for each allowed operation;
      FOREACH operation IN ARRAY allowed_operations;
      LOOP;
        -- Construct the policy SQL;
        IF operation = 'SELECT' THEN;
          policy_sql : = 'CREATE POLICY ' || full_policy_name || '_select ';
                     || 'ON storage.objects FOR SELECT ';
                     || 'TO authenticated ';
                     || 'USING (bucket_id = ' || quote_literal(bucket_name) || ' AND (' || definition || '))';
        ELSIF operation = 'INSERT' THEN;
          policy_sql : = 'CREATE POLICY ' || full_policy_name || '_insert ';
                     || 'ON storage.objects FOR INSERT ';
                     || 'TO authenticated ';
                     || 'WITH CHECK (bucket_id = ' || quote_literal(bucket_name) || ' AND (' || definition || '))';
        ELSIF operation = 'UPDATE' THEN;
          policy_sql : = 'CREATE POLICY ' || full_policy_name || '_update ';
                     || 'ON storage.objects FOR UPDATE ';
                     || 'TO authenticated ';
                     || 'USING (bucket_id = ' || quote_literal(bucket_name) || ' AND (' || definition || ')) ';
                     || 'WITH CHECK (bucket_id = ' || quote_literal(bucket_name) || ' AND (' || definition || '))';
        ELSIF operation = 'DELETE' THEN;
          policy_sql : = 'CREATE POLICY ' || full_policy_name || '_delete ';
                     || 'ON storage.objects FOR DELETE ';
                     || 'TO authenticated ';
                     || 'USING (bucket_id = ' || quote_literal(bucket_name) || ' AND (' || definition || '))';
        END IF;
        ;
        -- Execute the policy SQL;
        EXECUTE policy_sql;
      END LOOP;
    END;
    $$;
  `,

  /**;
   * Function to update a storage policy;
   */
  updateStoragePolicy: `,
    CREATE OR REPLACE FUNCTION update_storage_policy(
      bucket_name TEXT;
      policy_name TEXT;
      definition TEXT;
      allowed_operations TEXT[];
    )
    RETURNS VOID;
    LANGUAGE plpgsql;
    SECURITY DEFINER;
    AS $$;
    DECLARE;
      full_policy_name TEXT;
      operation TEXT;
      policy_sql TEXT;
    BEGIN;
      -- Create a unique policy name for the bucket;
      full_policy_name : = policy_name || '_' || bucket_name;
      -- Drop existing policies;
      EXECUTE 'DROP POLICY IF EXISTS ' || full_policy_name || '_select ON storage.objects';
      EXECUTE 'DROP POLICY IF EXISTS ' || full_policy_name || '_insert ON storage.objects';
      EXECUTE 'DROP POLICY IF EXISTS ' || full_policy_name || '_update ON storage.objects';
      EXECUTE 'DROP POLICY IF EXISTS ' || full_policy_name || '_delete ON storage.objects';
      ;
      -- Create new policies for each allowed operation;
      FOREACH operation IN ARRAY allowed_operations;
      LOOP;
        -- Construct the policy SQL;
        IF operation = 'SELECT' THEN;
          policy_sql : = 'CREATE POLICY ' || full_policy_name || '_select ';
                     || 'ON storage.objects FOR SELECT ';
                     || 'TO authenticated ';
                     || 'USING (bucket_id = ' || quote_literal(bucket_name) || ' AND (' || definition || '))';
        ELSIF operation = 'INSERT' THEN;
          policy_sql : = 'CREATE POLICY ' || full_policy_name || '_insert ';
                     || 'ON storage.objects FOR INSERT ';
                     || 'TO authenticated ';
                     || 'WITH CHECK (bucket_id = ' || quote_literal(bucket_name) || ' AND (' || definition || '))';
        ELSIF operation = 'UPDATE' THEN;
          policy_sql : = 'CREATE POLICY ' || full_policy_name || '_update ';
                     || 'ON storage.objects FOR UPDATE ';
                     || 'TO authenticated ';
                     || 'USING (bucket_id = ' || quote_literal(bucket_name) || ' AND (' || definition || ')) ';
                     || 'WITH CHECK (bucket_id = ' || quote_literal(bucket_name) || ' AND (' || definition || '))';
        ELSIF operation = 'DELETE' THEN;
          policy_sql : = 'CREATE POLICY ' || full_policy_name || '_delete ';
                     || 'ON storage.objects FOR DELETE ';
                     || 'TO authenticated ';
                     || 'USING (bucket_id = ' || quote_literal(bucket_name) || ' AND (' || definition || '))';
        END IF;
        ;
        -- Execute the policy SQL;
        EXECUTE policy_sql;
      END LOOP;
    END;
    $$;
  `,

  /**;
   * Function to delete a storage policy;
   */
  deleteStoragePolicy: `,
    CREATE OR REPLACE FUNCTION delete_storage_policy(bucket_name TEXT, policy_name TEXT)
    RETURNS VOID;
    LANGUAGE plpgsql;
    SECURITY DEFINER;
    AS $$;
    DECLARE;
      full_policy_name TEXT;
    BEGIN;
      -- Create a unique policy name for the bucket;
      full_policy_name : = policy_name || '_' || bucket_name;
      -- Drop existing policies;
      EXECUTE 'DROP POLICY IF EXISTS ' || full_policy_name || '_select ON storage.objects';
      EXECUTE 'DROP POLICY IF EXISTS ' || full_policy_name || '_insert ON storage.objects';
      EXECUTE 'DROP POLICY IF EXISTS ' || full_policy_name || '_update ON storage.objects';
      EXECUTE 'DROP POLICY IF EXISTS ' || full_policy_name || '_delete ON storage.objects';
    END;
    $$;
  ` }
