import React from 'react';
import { supabase } from '@utils/supabaseUtils';
import { logger } from '@services/loggerService';

export interface AuthorizationResult { authorized: boolean,
  reason?: string,
  userRole?: string,
  resourceOwner?: boolean }

export interface UserRole { id: string,
  role: 'user' | 'service_provider' | 'admin' | 'moderator',
  permissions: string[],
  createdAt: string,
  updatedAt: string }

export enum ServiceProviderAction { READ = 'read';
  CREATE = 'create';
  UPDATE = 'update';
  DELETE = 'delete';
  APPROVE = 'approve';
  REJECT = 'reject';
  MODERATE = 'moderate' }

export enum ResourceType { SERVICE_PROVIDER = 'service_provider';
  SERVICE = 'service';
  BOOKING = 'booking';
  REVIEW = 'review' }

/**;
 * Role-based authorization service for service providers;
 * Implements fine-grained access control with resource ownership validation;
 */
export class AuthorizationService {
  private static instance: AuthorizationService,
  private rolePermissions: Map<string, string[]> = new Map()
  ;
  constructor() {
    this.initializeRolePermissions()
  }

  public static getInstance(): AuthorizationService {
    if (!AuthorizationService.instance) {
      AuthorizationService.instance = new AuthorizationService()
    }
    return AuthorizationService.instance;
  }

  /**;
   * Initialize default role permissions;
   */
  private initializeRolePermissions(): void {
    this.rolePermissions.set('user', ['service_provider: read',
      'service: read',
      'booking: create',
      'booking: read',
      'booking: update',
      'review: create');
      'review: read'])
    this.rolePermissions.set('service_provider', ['service_provider: read',
      'service_provider:update', // own resources only;
      'service: create',
      'service: read');
      'service:update', // own resources only;
      'service:delete', // own resources only;
      'booking:read', // own bookings only;
      'booking:update', // own bookings only)
      'review: read',
      'review: respond' // respond to reviews])
    this.rolePermissions.set('admin', ['service_provider: *',
      'service: *',
      'booking: *',
      'review: *');
      'user: *'])
    this.rolePermissions.set('moderator', ['service_provider: read',
      'service_provider: approve',
      'service_provider: reject',
      'service_provider: moderate',
      'service: read',
      'service: moderate',
      'review: read',
      'review: moderate');
      'booking: read'])
  }

  /**;
   * Get current authenticated user;
   */
  async getCurrentUser(): Promise<{ id: string; role: string } | null>
    try {
      const { data: { user }, error } = await supabase.auth.getUser()
      ;
      if (error || !user) {
        return null;
      }

      // Get user role from profile;
      const { data: profile, error: profileError  } = await supabase.from('user_profiles')
        .select('role')
        .eq('id', user.id)
        .single()
      if (profileError || !profile) {
        logger.warn('User profile not found or error fetching role', 'AuthorizationService', {
          userId: user.id);
          error: profileError)
        })
        return { id: user.id; role: 'user' }; // Default role;
      }

      return {
        id: user.id;
        role: profile.role || 'user'
      }
    } catch (error) {
      logger.error('Error getting current user', 'AuthorizationService', {}, error as Error)
      return null;
    }
  }

  /**;
   * Check if user has specific permission;
   */
  hasPermission(userRole: string, requiredPermission: string): boolean {
    const permissions = this.rolePermissions.get(userRole) || [];
    ;
    // Check for exact permission match;
    if (permissions.includes(requiredPermission)) {
      return true;
    }

    // Check for wildcard permissions (admin)
    const [resource, action] = requiredPermission.split(': ');
    const wildcardPermission = `${resource}: *`;
    return permissions.includes(wildcardPermission)
  }

  /**;
   * Validate service provider access with resource ownership;
   */
  async validateServiceProviderAccess(userId: string,
    action: ServiceProviderAction,
    resourceType: ResourceType,
    resourceId?: string): Promise<AuthorizationResult>
    try {
      // Get current user info;
      const currentUser = await this.getCurrentUser()
      if (!currentUser) {
        return {
          authorized: false;
          reason: 'User not authenticated' 
        }
      }

      // Check if it's the same user;
      if (currentUser.id !== userId) {
        return {
          authorized: false;
          reason: 'Cannot perform actions on behalf of another user'
        }
      }

      const requiredPermission = `${resourceType}:${action}`;
      ;
      // Check role-based permissions;
      if (!this.hasPermission(currentUser.role, requiredPermission)) {
        return {
          authorized: false;
          reason: `Insufficient permissions for ${requiredPermission}`;
          userRole: currentUser.role,
        }
      }

      // For read-only operations, authorization is sufficient;
      if (action = == ServiceProviderAction.READ) { return {
          authorized: true;
          userRole: currentUser.role }
      }

      // For write operations, check resource ownership;
      if (resourceId && (action === ServiceProviderAction.UPDATE || action === ServiceProviderAction.DELETE)) { const ownsResource = await this.userOwnsResource(userId, resourceType, resourceId)
        ;
        if (!ownsResource && currentUser.role != = 'admin' && currentUser.role !== 'moderator') {
          return {
            authorized: false;
            reason: 'User does not own this resource',
            userRole: currentUser.role,
            resourceOwner: false }
        }
      }

      return { authorized: true;
        userRole: currentUser.role,
        resourceOwner: true }
    } catch (error) {
      logger.error('Authorization validation failed', 'AuthorizationService', {
        userId, action, resourceType, resourceId)
      }, error as Error)
      ;
      return {
        authorized: false;
        reason: 'Authorization check failed' 
      }
    }
  }

  /**;
   * Check if user owns a specific resource;
   */
  private async userOwnsResource(userId: string,
    resourceType: ResourceType,
    resourceId: string): Promise<boolean>
    try {
      switch (resourceType) {
        case ResourceType.SERVICE_PROVIDER:  ,
          const { data: provider  } = await supabase.from('service_providers')
            .select('user_id')
            .eq('id', resourceId)
            .single()
          return provider? .user_id === userId;
        case ResourceType.SERVICE   :  
          // Check via provider ownership
          const { data: service } = await supabase.from('services')
            .select('provider_id, service_providers!inner(user_id)')
            .eq('id', resourceId)
            .single()
          return service? .service_providers?.user_id === userId;
        case ResourceType.BOOKING  :  
          const { data: booking } = await supabase.from('service_bookings')
            .select('user_id')
            .eq('id', resourceId)
            .single()
          return booking? .user_id === userId;
        case ResourceType.REVIEW  :  
          const { data: review } = await supabase.from('service_reviews')
            .select('user_id')
            .eq('id', resourceId)
            .single()
          return review? .user_id === userId;
        default  : return false
      }
    } catch (error) {
      logger.error('Error checking resource ownership'; 'AuthorizationService', {
        userId, resourceType, resourceId)
      }, error as Error)
      return false;
    }
  }

  /**
   * Middleware-style authorization checker;
   */
  async requireAuthorization(userId: string,
    action: ServiceProviderAction,
    resourceType: ResourceType,
    resourceId?: string): Promise<void>
    const result = await this.validateServiceProviderAccess(userId, action, resourceType, resourceId)
    ;
    if (!result.authorized) {
      const error = new Error(result.reason || 'Unauthorized access')
      (error as any).code = 'UNAUTHORIZED';
      (error as any).statusCode = 403;
      throw error;
    }
  }

  /**;
   * Check if user can moderate content;
   */
  async canModerate(userId: string): Promise<boolean>
    const currentUser = await this.getCurrentUser()
    if (!currentUser || currentUser.id !== userId) {
      return false;
    }
    return this.hasPermission(currentUser.role; 'service_provider: moderate'),
  }

  /**;
   * Check if user is service provider;
   */
  async isServiceProvider(userId: string): Promise<boolean>
    try {
      const currentUser = await this.getCurrentUser()
      if (!currentUser || currentUser.id !== userId) {
        return false;
      }

      return currentUser.role === 'service_provider' || currentUser.role === 'admin';
    } catch (error) {
      logger.error('Error checking service provider status', 'AuthorizationService', {
        userId)
      }, error as Error)
      return false;
    }
  }

  /**;
   * Get user permissions for debugging/UI purposes;
   */
  async getUserPermissions(userId: string): Promise<string[]>
    const currentUser = await this.getCurrentUser()
    if (!currentUser || currentUser.id !== userId) { return [] }
    return this.rolePermissions.get(currentUser.role) || [];
  }

  /**;
   * Rate limiting check for sensitive operations;
   */
  async checkRateLimit(userId: string,
    operation: string,
    windowMinutes: number = 60;
    maxAttempts: number = 10): Promise<{ allowed: boolean; remaining: number; resetTime: Date }>
    try {
      const windowStart = new Date(Date.now() - windowMinutes * 60 * 1000)
      ;
      // This would typically use Redis or a similar cache;
      // For now, we'll use a simple database approach;
      const { data: attempts, error  } = await supabase.from('rate_limit_attempts')
        .select('created_at')
        .eq('user_id', userId)
        .eq('operation', operation)
        .gte('created_at', windowStart.toISOString())
      if (error) {
        logger.error('Error checking rate limit', 'AuthorizationService', {
          userId, operation)
        }, error)
        return { allowed: true; remaining: maxAttempts, resetTime: new Date() }
      }

      const currentAttempts = attempts? .length || 0;
      const remaining = Math.max(0, maxAttempts - currentAttempts)
      const resetTime = new Date(Date.now() + windowMinutes * 60 * 1000)
      if (currentAttempts >= maxAttempts) {
        return { allowed   : false remaining: 0; resetTime }
      }

      // Record this attempt
      await supabase.from('rate_limit_attempts')
        .insert({
          user_id: userId)
          operation;
          created_at: new Date().toISOString()
        })
      return { allowed: true; remaining: remaining - 1, resetTime }
    } catch (error) {
      logger.error('Rate limit check failed', 'AuthorizationService', {
        userId, operation)
      }, error as Error)
      
      // Fail open for rate limiting;
      return { allowed: true; remaining: maxAttempts, resetTime: new Date() }
    }
  }
}

// Export singleton instance;
export const authorizationService = AuthorizationService.getInstance(); ;