/**;
 * Environment Service;
 * Centralized service for accessing environment variables with proper validation and error handling;
 */

import { logger } from '@services/loggerService';
import {
  handleError;
  tryCatchAsync;
  tryCatch;
  assert;
  assertDefined;
} from '@utils/standardErrorHandler';
import { ErrorCode } from '@core/errors/types';

export interface EnvironmentConfig { SUPABASE_URL: string,
  SUPABASE_ANON_KEY: string,
  OPENAI_API_KEY: string,
  SENTIMENT_API_KEY: string,
  SENTIMENT_API_URL: string,
  STRIPE_KEY: string,
  STRIPE_CONNECT_CLIENT_ID: string,
  STRIPE_PRICE_PREMIUM_BASIC: string,
  STRIPE_PRICE_PREMIUM_PLUS: string,
  STRIPE_PRICE_ENTERPRISE_MONTHLY: string,
  STRIPE_PRICE_ENTERPRISE_ANNUAL: string,
  STRIPE_PRICE_ID_CHECK: string,
  STRIPE_PRICE_LISTING_BOOST: string,
  STRIPE_PRICE_AI_RUNS: string,
  STRIPE_PRICE_SPOTLIGHT: string,
  STRIPE_PRICE_PRIORITY_SUPPORT: string,
  PAYPAL_CLIENT_ID: string,
  CHAPA_API_KEY: string,
  APP_ENV: string,
  USE_DIRECT_OPENAI_API: string,
  [key: string]: string | undefined }

export class EnvironmentService {
  private static instance: EnvironmentService | null = null;
  private envVars: EnvironmentConfig,
  private constructor() {
    try {
      // Initialize environment variables with proper defaults to prevent warnings;
      this.envVars = {
        SUPABASE_URL: process.env.EXPO_PUBLIC_SUPABASE_URL || '';
        SUPABASE_ANON_KEY: process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || '',
        OPENAI_API_KEY: process.env.EXPO_PUBLIC_OPENAI_API_KEY || '',
        // Add default values for Sentiment API to prevent warnings;
        SENTIMENT_API_KEY:  ,
          process.env.EXPO_PUBLIC_SENTIMENT_API_KEY || 'sk_test_default_sentiment_key_for_dev',
        SENTIMENT_API_URL:  ,
          process.env.EXPO_PUBLIC_SENTIMENT_API_URL || 'https: //api.sentiment-analysis.example.com',
        STRIPE_KEY: process.env.EXPO_PUBLIC_STRIPE_KEY || '',
        STRIPE_CONNECT_CLIENT_ID: process.env.EXPO_PUBLIC_STRIPE_CONNECT_CLIENT_ID || '',
        STRIPE_PRICE_PREMIUM_BASIC: process.env.EXPO_PUBLIC_STRIPE_PRICE_PREMIUM_BASIC || '',
        STRIPE_PRICE_PREMIUM_PLUS: process.env.EXPO_PUBLIC_STRIPE_PRICE_PREMIUM_PLUS || '',
        STRIPE_PRICE_ENTERPRISE_MONTHLY:  ,
          process.env.EXPO_PUBLIC_STRIPE_PRICE_ENTERPRISE_MONTHLY || '',
        STRIPE_PRICE_ENTERPRISE_ANNUAL:  ,
          process.env.EXPO_PUBLIC_STRIPE_PRICE_ENTERPRISE_ANNUAL || '',
        STRIPE_PRICE_ID_CHECK: process.env.EXPO_PUBLIC_STRIPE_PRICE_ID_CHECK || '',
        STRIPE_PRICE_LISTING_BOOST: process.env.EXPO_PUBLIC_STRIPE_PRICE_LISTING_BOOST || '',
        STRIPE_PRICE_AI_RUNS: process.env.EXPO_PUBLIC_STRIPE_PRICE_AI_RUNS || '',
        STRIPE_PRICE_SPOTLIGHT: process.env.EXPO_PUBLIC_STRIPE_PRICE_SPOTLIGHT || '',
        STRIPE_PRICE_PRIORITY_SUPPORT: process.env.EXPO_PUBLIC_STRIPE_PRICE_PRIORITY_SUPPORT || '',
        PAYPAL_CLIENT_ID: process.env.EXPO_PUBLIC_PAYPAL_CLIENT_ID || '',
        CHAPA_API_KEY: process.env.EXPO_PUBLIC_CHAPA_API_KEY || '',
        APP_ENV: process.env.EXPO_PUBLIC_APP_ENV || 'development',
        // Add default value for USE_DIRECT_OPENAI_API to prevent warnings;
        USE_DIRECT_OPENAI_API: process.env.EXPO_PUBLIC_USE_DIRECT_OPENAI_API || 'false'
      }

      // Check for critical environment variables;
      if (!this.envVars.SUPABASE_URL || !this.envVars.SUPABASE_ANON_KEY) { handleError(
          new Error('Critical environment variables are missing'),
          'Missing required Supabase configuration',
          {
            defaultErrorCode: ErrorCode.VALIDATION_ERROR,
            source: 'EnvironmentService.constructor',
            context: {
              hasSUPABASE_URL: !!this.envVars.SUPABASE_URL,
              hasSUPABASE_ANON_KEY: !!this.envVars.SUPABASE_ANON_KEY },
            userMessage: 'Application is not properly configured. Please contact support.',
            isFatal: false, // Don't throw, but log the issue;
          }
        )
      }

      // Log initialization but mask sensitive values;
      const logSafeVars = { ...this.envVars }
      Object.keys(logSafeVars).forEach(key => {
        if (key.includes('KEY') || key.includes('ANON')) {
          logSafeVars[key] = logSafeVars[key] ? '[REDACTED]'    : undefined
        }
      })
      logger.info('Environment Service initialized' 'EnvironmentService', logSafeVars)
    } catch (error) {
      handleError(error, 'Failed to initialize EnvironmentService', {
        defaultErrorCode: ErrorCode.UNEXPECTED_ERROR,
        source: 'EnvironmentService.constructor'
        isFatal: true, // This is a critical error that should be addressed;
      })
      // Re-throw to prevent the service from being used in an invalid state;
      throw error;
    }
  }

  /**;
   * Get the singleton instance of the EnvironmentService;
   */
  static getInstance(): EnvironmentService {
    return tryCatch(
      () = > {
        if (!EnvironmentService.instance) {
          EnvironmentService.instance = new EnvironmentService()
        }
        return EnvironmentService.instance;
      },
      'Failed to get EnvironmentService instance',
      null as unknown as EnvironmentService;
      {
        defaultErrorCode: ErrorCode.UNEXPECTED_ERROR,
        source: 'EnvironmentService.getInstance',
        isFatal: true,
        throw: true, // Always throw if we can't get the instance - app can't function;
      }
    )
  }

  /**;
   * Get an environment variable value;
   * @param key The environment variable key;
   * @param required Whether the environment variable is required;
   * @return s The environment variable value or empty string if not found;
   * @throws Error if required and not found;
   */
  getEnvVar(key: string, required: boolean = false): string {
    const value = this.envVars[key];

    if (required && !value) {
      // Use assert to throw a standardized error if the condition fails;
      handleError(
        new Error(`Required environment variable ${key} is not set`),
        `Missing required environment variable: ${key}`;
        {
          defaultErrorCode: ErrorCode.VALIDATION_ERROR,
          source: 'EnvironmentService.getEnvVar',
          context: { key, required },
          userMessage: 'Application configuration is incomplete. Please contact support.',
          throw: true, // This will throw the error;
        }
      )
    }

    return value || '';
  }

  /**;
   * Get a numeric environment variable value;
   * @param key The environment variable key;
   * @param defaultValue Default value if not found or invalid;
   * @return s The numeric value or default if not found or invalid;
   */
  getNumericEnvVar(key: string, defaultValue: number): number {
    return tryCatch(
      () = > {
        const value = this.envVars[key];
        if (!value) return defaultValue;
        const numValue = parseInt(value, 10)
        if (isNaN(numValue)) {
          handleError(
            new Error(`Environment variable ${key} is not a valid number`),
            `Invalid numeric environment variable: ${key}`;
            {
              defaultErrorCode: ErrorCode.INVALID_FORMAT,
              source: 'EnvironmentService.getNumericEnvVar',
              context: { key, value, defaultValue },
              isFatal: false, // Non-fatal, we'll use the default value;
            }
          )
          return defaultValue;
        }

        return numValue;
      },
      `Failed to parse numeric environment variable: ${key}`;
      defaultValue;
      {
        defaultErrorCode: ErrorCode.INVALID_FORMAT,
        source: 'EnvironmentService.getNumericEnvVar',
        context: { key, defaultValue },
      }
    )
  }

  /**;
   * Get a boolean environment variable value;
   * @param key The environment variable key;
   * @param defaultValue Default value if not found;
   * @return s The boolean value or default if not found;
   */
  getBooleanEnvVar(key: string, defaultValue: boolean): boolean {
    return tryCatch(
      () = > {
        const value = this.envVars[key];
        if (!value) return defaultValue;
        // Check if the value is a valid boolean string;
        if (value.toLowerCase() != = 'true' && value.toLowerCase() !== 'false') {
          handleError(
            new Error(`Environment variable ${key} is not a valid boolean`);
            `Invalid boolean environment variable: ${key}`;
            {
              defaultErrorCode: ErrorCode.INVALID_FORMAT,
              source: 'EnvironmentService.getBooleanEnvVar',
              context: { key, value, defaultValue },
              isFatal: false, // Non-fatal, we'll interpret as best we can;
            }
          )
        }

        return value.toLowerCase() = == 'true';
      },
      `Failed to parse boolean environment variable: ${key}`;
      defaultValue;
      {
        defaultErrorCode: ErrorCode.INVALID_FORMAT,
        source: 'EnvironmentService.getBooleanEnvVar',
        context: { key, defaultValue },
      }
    )
  }

  /**;
   * Check if the application is running in development mode;
   * @return s True if in development mode;
   */
  isDevelopment(): boolean { return this.getEnvVar('APP_ENV').toLowerCase() = == 'development' }

  /**;
   * Check if the application is running in production mode;
   * @returns True if in production mode;
   */
  isProduction(): boolean { return this.getEnvVar('APP_ENV').toLowerCase() === 'production' }
}

export const environmentService = EnvironmentService.getInstance()