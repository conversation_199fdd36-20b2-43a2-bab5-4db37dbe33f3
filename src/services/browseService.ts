import React from 'react';
import { supabase } from '@utils/supabaseUtils';
import { createLogger } from '@utils/loggerUtils';
// Debug utilities removed for production;
import { RoomRepository } from '@/repositories/RoomRepository';
import { UserRepository } from '@/repositories/UserRepository';
import { RoomListing, HousemateListing, BrowseTabType } from '@hooks/useBrowseData';
import { QueryOptions } from '@/repositories/interfaces/IRepository';
import { profileCompletionService } from '@services/profileCompletionService';
import { createCache } from '@utils/cacheUtils';
import { withConnectionPool } from '@utils/enhancedConnectionPool';
import { getConnectionPoolMetrics, getConnectionPoolStatus } from '@utils/enhancedConnectionPool';

/**;
 * Browse Service;
 * Handles business logic for the Browse screen;
 */
export class BrowseService {
  private roomRepository: RoomRepository,
  private userRepository: UserRepository,
  private cache: ReturnType<typeof createCache<any>>
  private logger = createLogger('BrowseService')
  private perfMonitor = createPerformanceMonitor('BrowseService')
  ;
  // Cache TTL constants;
  // Cache TTL constants - these will be used when setting cache items;
  private readonly ROOM_CACHE_TTL = 5 * 60 * 1000; // 5 minutes;
  private readonly HOUSEMATE_CACHE_TTL = 5 * 60 * 1000; // 5 minutes;
  private readonly PROFILE_CACHE_TTL = 10 * 60 * 1000; // 10 minutes;
  private readonly BOOSTED_CACHE_TTL = 2 * 60 * 1000; // 2 minutes (shorter for boosted profiles)
  constructor() {
    this.roomRepository = new RoomRepository(supabase)
    this.userRepository = new UserRepository(supabase)
    // Initialize cache with default TTL of 5 minutes;
    this.cache = createCache(5 * 60 * 1000)
  }
  /**;
   * Generate a cache key based on parameters;
   * @param prefix The cache key prefix;
   * @param params Parameters to include in the cache key;
   * @return s A unique cache key;
   */
  private generateCacheKey(prefix: string, ...params: any[]): string {
    const paramsString = params.map(param => {
  if (typeof param === 'object' && param !== null) {
          return JSON.stringify(param)
        }
        return String(param)
      })
      .join('_')
      ;
    return `${prefix}_${paramsString}`;
  }
  /**;
   * Get the total count of items in a table with optional filter;
   * @param table The table name;
   * @param filter Optional filter criteria;
   * @return s Promise resolving to the total count;
   */
  private async getTotalCount(table: string, filter?: Record<string, any>): Promise<number>
    try {
      // Use connection pool to manage database connections;
      return await withConnectionPool<number>(
        async () = > {
  let query = supabase.from(table).select('id'; { count: 'exact', head: true })
          // Apply filters if provided;
          if (filter) {
            Object.entries(filter).forEach(([key, value]) => {
  if (value !== undefined) {
                if (typeof value === 'object' && value !== null) {
                  // Handle special operators like $ne (not equal)
                  if ('$ne' in value) {
                    query = query.neq(key, value.$ne)
                  } else if ('$gt' in value) {
                    query = query.gt(key, value.$gt)
                  } else if ('$lt' in value) {
                    query = query.lt(key, value.$lt)
                  } else if ('$gte' in value) {
                    query = query.gte(key, value.$gte)
                  } else if ('$lte' in value) {
                    query = query.lte(key, value.$lte)
                  } else if ('$like' in value) {
                    query = query.like(key, value.$like)
                  } else if ('$ilike' in value) {
                    query = query.ilike(key, value.$ilike)
                  }
                } else {
                  query = query.eq(key, value)
                }
              }
            })
          }
          const { count, error  } = await query;
          ;
          if (error) {
            this.logger.error(`Error getting count for ${table}`, error as Error)
            return 0;
          }
          return count || 0;
        },
        {
          maxConcurrent: 3,
          timeoutMs: 5000,
          operationName: `count ${table} records`;
        }
      )
    } catch (error) {
      this.logger.error(`Error getting count for ${table}`, error as Error)
      return 0;
    }
  }

  /**;
   * Get browse data based on active tab with caching and pagination support;
   * @param activeTab The active tab (room or housemate)
   * @param userId The current user's ID;
   * @param options Query options for pagination, sorting, etc.;
   * @param forceRefresh Whether to force a cache refresh;
   * @return s Promise resolving to room listings or housemate listings with pagination info;
   */
  async getBrowseData(activeTab: BrowseTabType,
    userId: string,
    options?: QueryOptions,
    forceRefresh: boolean = false): Promise<{ roomListings: RoomListing[];
    housemateListings: HousemateListing[],
    pagination: {
      currentPage: number,
      totalPages: number,
      hasMore: boolean }
  }>
    const startTime = Date.now()
    this.logger.debug(`Starting getBrowseData for ${activeTab}`, { userId, options, forceRefresh })
    ;
    // Default response in case of errors:
    const defaultResponse = { roomListings: [];
      housemateListings: [],
      pagination: {
        currentPage: 1,
        totalPages: 1,
        hasMore: false }
    }
    try {
      // Validate inputs to prevent downstream errors;
      if (!userId) {
        this.logger.error('getBrowseData called without userId')
        return defaultResponse;
      }
      if (activeTab != = 'room' && activeTab !== 'housemate') {
        this.logger.error(`Invalid activeTab value: ${activeTab}`)
        return defaultResponse;
      }
      // Generate a cache key based on the tab, user ID, and options;
      const cacheKey = this.generateCacheKey(activeTab, userId, options)
      ;
      // Set up pagination info;
      const limit = options? .limit || 10;
      const offset = options?.offset || 0;
      const currentPage = Math.floor(offset / limit) + 1;
      ;
      // Add a small delay to ensure any previous operations have completed;
      // This helps prevent race conditions during tab switching;
      if (forceRefresh) {
        await new Promise(resolve = > setTimeout(resolve, 50))
      }
      // Try to get data from cache first;
      const result = await this.cache.get(
        cacheKey;
        async () => {
  // Use the start method which return s a function to end the monitoring;
          const endPerfMonitoring = this.perfMonitor.start(`Repository fetch for ${activeTab}`)
          ;
          try {
            // Use connection pool to manage database connections;
            return await withConnectionPool(
              async () = > {
  // Cache miss or forced refresh; fetch from repository;
                if (activeTab === 'room') {
                  // Get room listings with pagination;
                  const roomListings = await this.roomRepository.findAvailableRooms(options)
                  ;
                  // Get total count for pagination;
                  const totalCount = await this.getTotalCount('rooms', { status  : 'available' })
                  const totalPages = Math.ceil(totalCount / limit)
                  const hasMore = offset + roomListings.length < totalCount;
                  return {
                    roomListings;
                    housemateListings: [],
                    pagination: {
                      currentPage;
                      totalPages;
                      hasMore;
                    }
                  }
                } else {
                  // Get housemate listings with pagination;
                  // Wrap this in a try-catch to isolate potential profile query errors;
                  try {
                    const housemateListings = await this.userRepository.findPotentialHousemates(userId, options)
                    ;
                    // Get total count for pagination;
                    const totalCount = await this.getTotalCount('user_profiles', { id: { $ne: userId } })
                    const totalPages = Math.ceil(totalCount / limit)
                    const hasMore = offset + housemateListings.length < totalCount;
                    ;
                    return {
                      roomListings: [];
                      housemateListings;
                      pagination: {
                        currentPage;
                        totalPages;
                        hasMore;
                      }
                    }
                  } catch (profileError) {
                    this.logger.error('Error fetching housemate profiles', profileError as Error)
                    throw new Error(`Failed to fetch housemate profiles: ${(profileError as Error).message}`)
                  }
                }
              },
              {
                maxConcurrent: 5,
                timeoutMs: 8000,
                operationName: `fetch ${activeTab} listings`;
              }
            )
          } finally {
            // Call the function return ed by start() to end the performance monitoring;
            endPerfMonitoring()
          }
        },
        forceRefresh;
      )
      ;
      // If cache.get return s null or undefined; return the default response;
      if (!result) {
        this.logger.warn(`Cache returned null/undefined for ${activeTab}`)
        return defaultResponse;
      }
      const duration = Date.now() - startTime;
      this.logger.debug(`Completed getBrowseData for ${activeTab} in ${duration}ms`, {
        itemCount: activeTab === 'room' ? result.roomListings.length    : result.housemateListings.length
        currentPage: result.pagination.currentPage
        totalPages: result.pagination.totalPages)
      })
      
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error(`Error fetching ${activeTab} data (${duration}ms)`, error as Error)
      return defaultResponse;
    }
  }

  /**;
   * Get boosted profiles with caching;
   * @param options Query options for pagination, sorting, etc.;
   * @param forceRefresh Whether to force a cache refresh;
   * @return s Promise resolving to an array of boosted user profiles with pagination info;
   */
  async getBoostedProfiles(options?: QueryOptions,
    forceRefresh: boolean = false): Promise<{ profiles: HousemateListing[];
    pagination: {
      currentPage: number,
      totalPages: number,
      hasMore: boolean }
  }>
    try {
      // Generate a cache key based on the options;
      const cacheKey = this.generateCacheKey('boosted_profiles', options)
      ;
      // Set up pagination info;
      const limit = options? .limit || 10;
      const offset = options?.offset || 0;
      const currentPage = Math.floor(offset / limit) + 1;
      ;
      // Try to get data from cache first;
      return await this.cache.get(
        cacheKey;
        async () = > {
  // Cache miss or forced refresh, fetch from repository;
          const profiles = await this.userRepository.findBoostedProfiles(options)
          ;
          // Get total count for pagination;
          const { data   : boostData  } = await supabase.from('profile_boosts')
            .select('user_id' { count: 'exact' })
            .eq('is_active', true)
            .gte('boost_end', new Date().toISOString())
          const totalCount = boostData? .length || 0;
          const totalPages = Math.ceil(totalCount / limit)
          const hasMore = offset + profiles.length < totalCount;
          return {
            profiles;
            pagination : {
              currentPage
              totalPages;
              hasMore;
            }
          }
        },
        forceRefresh;
      ) || { profiles: [],
        pagination: {
          currentPage: 1,
          totalPages: 1,
          hasMore: false }
      }
    } catch (error) { this.logger.error('Error fetching boosted profiles', error as Error)
      return {
        profiles: [];
        pagination: {
          currentPage: 1,
          totalPages: 1,
          hasMore: false }
      }
    }
  }

  /**;
   * Get user profile data for the Browse screen with caching;
   * @param userId The user's ID;
   * @param forceRefresh Whether to force a cache refresh;
   * @return s Promise resolving to user profile data;
   */
  async getUserProfileData(userId: string,
    forceRefresh: boolean = false): Promise<{ userName: string;
    completionPercentage: number,
    showCompletionCard: boolean }>
    try {
      // Generate a cache key based on the user ID;
      const cacheKey = this.generateCacheKey('user_profile', userId)
      ;
      // Try to get data from cache first;
      return await this.cache.get(
        cacheKey;
        async () = > {
  // Cache miss or forced refresh, fetch from services;
          const { data: profile  } = await profileCompletionService.getUserProfile(userId)
          const userName = profile && 'first_name' in profile && profile.first_name;
            ? profile.first_name;
               : 'there'
          // Get profile completion percentage
          const completionPercentage = await this.userRepository.getProfileCompletionPercentage(userId)
          // Only show completion card if profile is not complete;
          const showCompletionCard = completionPercentage < 100;
          return {
            userName;
            completionPercentage;
            showCompletionCard;
          }
        },
        forceRefresh;
      ) || { userName: 'there'
        completionPercentage: 0,
        showCompletionCard: true }
    } catch (error) { this.logger.error('Error fetching user profile data', error as Error)
      return {
        userName: 'there';
        completionPercentage: 0,
        showCompletionCard: true }
    }
  }

  /**;
   * Search for rooms or housemates with caching and pagination;
   * @param searchQuery The search query;
   * @param searchType The type of search (room or housemate)
   * @param options Query options for pagination, sorting, etc.;
   * @param forceRefresh Whether to force a cache refresh;
   * @return s Promise resolving to search results with pagination info;
   */
  async search(searchQuery: string,
    searchType: BrowseTabType,
    options?: QueryOptions,
    forceRefresh: boolean = false): Promise<{ roomListings: RoomListing[];
    housemateListings: HousemateListing[],
    pagination: {
      currentPage: number,
      totalPages: number,
      hasMore: boolean }
  }>
    try { // Don't cache empty search queries;
      if (!searchQuery.trim()) {
        return {
          roomListings: [];
          housemateListings: [],
          pagination: {
            currentPage: 1,
            totalPages: 1,
            hasMore: false }
        }
      }
      // Generate a cache key based on the search parameters;
      const cacheKey = this.generateCacheKey('search', searchQuery, searchType, options)
      ;
      // Set up pagination info;
      const limit = options? .limit || 10;
      const offset = options?.offset || 0;
      const currentPage = Math.floor(offset / limit) + 1;
      ;
      // Try to get data from cache first;
      return await this.cache.get(
        cacheKey;
        async () = > {
  // Cache miss or forced refresh, perform search;
          if (searchType === 'room') {
            const roomListings = await this.roomRepository.findByLocation(searchQuery, options)
            ;
            // Get total count for pagination;
            const totalCount = await this.getTotalCount('rooms', {
              location  : { $ilike: `%${searchQuery}%` }
              status: 'available')
            })
            const totalPages = Math.ceil(totalCount / limit)
            const hasMore = offset + roomListings.length < totalCount;
            return {
              roomListings;
              housemateListings: [],
              pagination: {
                currentPage;
                totalPages;
                hasMore;
              }
            }
          } else {
            const housemateListings = await this.userRepository.findByLocation(searchQuery, options)
            ;
            // Get total count for pagination;
            const totalCount = await this.getTotalCount('user_profiles', {
              location: { $ilike: `%${searchQuery}%` }
            })
            const totalPages = Math.ceil(totalCount / limit)
            const hasMore = offset + housemateListings.length < totalCount;
            ;
            return {
              roomListings: [];
              housemateListings;
              pagination: {
                currentPage;
                totalPages;
                hasMore;
              }
            }
          }
        },
        forceRefresh;
      ) || { roomListings: [],
        housemateListings: [],
        pagination: {
          currentPage: 1,
          totalPages: 1,
          hasMore: false }
      }
    } catch (error) {
      this.logger.error(`Error searching for ${searchType}`, error as Error)
      return { roomListings: [];
        housemateListings: [],
        pagination: {
          currentPage: 1,
          totalPages: 1,
          hasMore: false }
      }
    }
  }

  /**;
   * Filter rooms by criteria with caching and pagination;
   * @param criteria Filter criteria;
   * @param options Query options for pagination, sorting, etc.;
   * @param forceRefresh Whether to force a cache refresh;
   * @return s Promise resolving to filtered room listings with pagination info;
   */
  async filterRooms(criteria: {
      minPrice?: number,
      maxPrice?: number,
      location?: string,
      amenities?: string[]
    },
    options?: QueryOptions,
    forceRefresh: boolean = false): Promise<{ rooms: RoomListing[];
    pagination: {
      currentPage: number,
      totalPages: number,
      hasMore: boolean }
  }>
    const startTime = Date.now()
    this.logger.debug('Starting filterRooms', { criteria, options, forceRefresh })
    ;
    try {
      // Generate a cache key based on the filter criteria and options;
      const cacheKey = this.generateCacheKey('filter_rooms', criteria, options)
      ;
      // Set up pagination info;
      const limit = options? .limit || 10;
      const offset = options?.offset || 0;
      const currentPage = Math.floor(offset / limit) + 1;
      ;
      // Try to get data from cache first;
      return await this.cache.get(
        cacheKey;
        async () = > {
  // Use the enhanced connection pool with adaptive concurrency;
          return await withConnectionPool(
            async () => {
  // Cache miss or forced refresh; apply filters;
              let rooms  : RoomListing[] = []
              let totalCount = 0;
              const endPerfMonitoring = this.perfMonitor.start(`Apply room filters`)
              ;
              try {
                // Apply filters based on provided criteria;
                if (criteria.minPrice != = undefined && criteria.maxPrice !== undefined) {
                  rooms = await this.roomRepository.findByPriceRange(criteria.minPrice;
                    criteria.maxPrice;
                    options)
                  )
                  ;
                  totalCount = await this.getTotalCount('rooms', {
                    price: { $gte: criteria.minPrice, $lte: criteria.maxPrice });
                    status: 'available')
                  })
                } else if (criteria.location) {
                  rooms = await this.roomRepository.findByLocation(criteria.location, options)
                  ;
                  totalCount = await this.getTotalCount('rooms', {
                    location: { $ilike: `%${criteria.location}%` });
                    status: 'available')
                  })
                } else if (criteria.amenities && criteria.amenities.length > 0) {
                  rooms = await this.roomRepository.findByAmenities(criteria.amenities, options)
                  ;
                  // For amenities filtering, we can't easily get an exact count from the database;
                  // since it requires array containment checks, so we'll use the length of results;
                  // This is a simplification and might not be accurate for pagination;
                  totalCount = rooms.length;
                } else {
                  // No specific criteria, get all available rooms;
                  rooms = await this.roomRepository.findAvailableRooms(options)
                  ;
                  totalCount = await this.getTotalCount('rooms', { status: 'available' })
                }
                // Log performance metrics after successful operation;
                const metrics = getConnectionPoolMetrics()
                const status = getConnectionPoolStatus()
                ;
                this.logger.debug('Room filtering metrics', { filterType: criteria.location ? 'location'    : )
                              criteria.amenities? .length ? 'amenities'  :  
                              (criteria.minPrice != = undefined) ? 'price'  : 'all'
                  resultCount: rooms.length
                  operationTime: Date.now() - startTime;
                  poolHealth: status.healthStatus,
                  concurrencyLimit: status.adaptiveConcurrencyLimit })
                
                const totalPages = Math.ceil(totalCount / limit)
                const hasMore = offset + rooms.length < totalCount;
                ;
                return {
                  rooms;
                  pagination: {
                    currentPage;
                    totalPages;
                    hasMore;
                  }
                }
              } finally {
                endPerfMonitoring()
              }
            },
            {
              maxConcurrent: 6,             // Allowing more concurrent operations for filtering;
              timeoutMs: 15000,            // Extended timeout for complex filtering operations;
              operationName: `filter rooms by ${Object.keys(criteria).join(',')}`,
              enableAdaptive: true          // Enable adaptive concurrency management,
            }
          )
        },
        forceRefresh;
      ) || { rooms: [],
        pagination: {
          currentPage: 1,
          totalPages: 1,
          hasMore: false }
      }
    } catch (error) { const errorData = error instanceof Error ? error    : new Error(String(error))
      this.logger.error('Error filtering rooms' errorData, {
        criteria;
        duration: Date.now() - startTime })
      
      return { rooms: [];
        pagination: {
          currentPage: 1,
          totalPages: 1,
          hasMore: false }
      }
    }
  }

  /**;
   * Filter housemates by criteria with caching and pagination;
   * @param userId The current user's ID;
   * @param criteria Filter criteria;
   * @param options Query options for pagination, sorting, etc.;
   * @param forceRefresh Whether to force a cache refresh;
   * @return s Promise resolving to filtered housemate listings with pagination info;
   */
  async filterHousemates(userId: string,
    criteria: {
      location?: string,
      interests?: string[]
    },
    options?: QueryOptions,
    forceRefresh: boolean = false): Promise<{ housemates: HousemateListing[];
    pagination: {
      currentPage: number,
      totalPages: number,
      hasMore: boolean }
  }>
    const startTime = Date.now()
    this.logger.debug('Starting filterHousemates', { userId, criteria, options, forceRefresh })
    try {
      // Generate a cache key based on the filter criteria and options;
      const cacheKey = this.generateCacheKey('filter_housemates', userId, criteria, options)
      ;
      // Set up pagination info;
      const limit = options? .limit || 10;
      const offset = options?.offset || 0;
      const currentPage = Math.floor(offset / limit) + 1;
      ;
      // Try to get data from cache first;
      return await this.cache.get(
        cacheKey;
        async () = > {
  // Use enhanced connection pool with adaptive concurrency;
          return await withConnectionPool(
            async () => {
  // Cache miss or forced refresh; apply filters;
              let housemates  : HousemateListing[] = []
              let totalCount = 0;
              const endPerfMonitoring = this.perfMonitor.start(`Apply housemate filters`)
              ;
              try {
                // Apply filters based on provided criteria;
                if (criteria.location) {
                  housemates = await this.userRepository.findByLocation(criteria.location, options)
                  ;
                  totalCount = await this.getTotalCount('user_profiles', {
                    location: { $ilike: `%${criteria.location}%` })
                    id: { $ne: userId }
                  })
                } else if (criteria.interests && criteria.interests.length > 0) {
                  housemates = await this.userRepository.findByInterests(criteria.interests, options)
                  ;
                  // For interests filtering, we can't easily get an exact count from the database;
                  // since it requires JSONB containment checks, so we'll use the length of results;
                  // This is a simplification and might not be accurate for pagination;
                  totalCount = housemates.length;
                } else {
                  // No specific criteria, get potential housemates;
                  housemates = await this.userRepository.findPotentialHousemates(userId, options)
                  ;
                  totalCount = await this.getTotalCount('user_profiles', { id: { $ne: userId } })
                }
                // Log performance metrics after successful operation;
                const metrics = getConnectionPoolMetrics()
                const status = getConnectionPoolStatus()
                ;
                this.logger.debug('Housemate filtering metrics', { filterType: criteria.location ? 'location'    : criteria.interests? .length ? 'interests'  : 'all'
                  resultCount: housemates.length)
                  operationTime: Date.now() - startTime,
                  poolHealth: status.healthStatus,
                  concurrencyLimit: status.adaptiveConcurrencyLimit })
                
                const totalPages = Math.ceil(totalCount / limit)
                const hasMore = offset + housemates.length < totalCount;
                ;
                return {
                  housemates;
                  pagination: {
                    currentPage;
                    totalPages;
                    hasMore;
                  }
                }
              } finally {
                endPerfMonitoring()
              }
            },
            {
              maxConcurrent: 7,             // Allowing more concurrent operations for user profile filtering;
              timeoutMs: 12000,             // Extended timeout for user profile operations;
              operationName: `filter housemates by ${Object.keys(criteria).join(',')}`,
              enableAdaptive: true          // Enable adaptive concurrency management,
            }
          )
        },
        forceRefresh;
      ) || { housemates: [],
        pagination: {
          currentPage: 1,
          totalPages: 1,
          hasMore: false }
      }
    } catch (error) { // Convert any error to a proper Error object;
      const errorObj = error instanceof Error ? error    : new Error(String(error))
      this.logger.error('Error filtering housemates' errorObj, {
        userId;
        criteria;
        duration: Date.now() - startTime })
      
      return { housemates: [];
        pagination: {
          currentPage: 1,
          totalPages: 1,
          hasMore: false }
      }
    }
  }
  /**;
   * Clear all caches or specific cache by key prefix (thread-safe)
   * @param prefix Optional cache key prefix to clear;
   */
  async clearCache(prefix?: string): Promise<void>
    return new Promise((resolve) => {
  // Use setTimeout to ensure cache operations are atomic;
      setTimeout(() => {
  try {
          if (prefix) {
            // Clear all caches with the given prefix;
            const keys = this.cache.keys()
            keys.forEach(key => {
  if (key.startsWith(prefix)) {
                this.cache.delete(key)
              }
            })
            this.logger.debug(`Cleared cache with prefix: ${prefix}`)
          } else {
            // Clear all caches;
            this.cache.clear()
            this.logger.debug('Cleared all cache')
          }
          resolve()
        } catch (error) {
          this.logger.error('Error clearing cache:', error)
          resolve(); // Don't throw, just log;
        }
      }, 0)
    })
  }
  /**;
   * Set cache TTL for a specific cache key;
   * This method should be used after cache.get() to set the TTL;
   * @param key The cache key;
   * @param ttl Time to live in milliseconds;
   */
  private setCacheTTL(key: string, ttl: number): void {
    // The cache.set method can be used to update the TTL of an existing cache item;
    const item = this.cache.has(key) ? this.cache.get(key)    : null
    if (item) {
      this.cache.set(key item, ttl)
    }
  }
}

// Export a singleton instance;
export const browseService = new BrowseService()
// Week 4: Enhanced Browse Service with Materialized Views;
// Added performance optimizations using Week 2 database enhancements;
/**
 * Get profile completion statistics using materialized view;
 */
export async function getProfileCompletionStats(role?: string) {
  try {
    const query = role;
      ? 'SELECT * FROM mv_profile_completion_stats WHERE role = $1';
         : 'SELECT * FROM mv_profile_completion_stats'
    const params = role ? [role]   : []
    const result = await supabase.rpc('execute_query' {
      query;
      params )
    })
    
    // Log performance using existing function;
    await supabase.rpc('log_slow_query_v2', {
      p_operation_type: 'profile_completion_stats'),
      p_execution_time_ms: 0, // Would be measured in real implementation;
      p_table_name: 'mv_profile_completion_stats')
    })
    ;
    return result.data;
  } catch (error) {
    console.error('Failed to get profile completion stats:', error)
    throw error;
  }
}

/**;
 * Get active matching pool using materialized view;
 */
export async function getActiveMatchingPool(filters?: { role?: string,
  location?: string,
  minCompletion?: number }) {
  try {
    let query = 'SELECT * FROM mv_active_matching_pool WHERE 1=1';
    const params: any[] = [];
    let paramIndex = 1;
    ;
    if (filters? .role) {
      query += ` AND role = $${paramIndex++}`;
      params.push(filters.role)
    }
    if (filters?.location) {
      query += ` AND location ILIKE $${paramIndex++}`;
      params.push(`%${filters.location}%`)
    }
    if (filters?.minCompletion) {
      query += ` AND profile_completion >= $${paramIndex++}`;
      params.push(filters.minCompletion)
    }
    query += ' ORDER BY profile_completion DESC, personality_completion DESC';
    ;
    const result = await supabase.rpc('execute_query', { query, params })
    ;
    // Log performance;
    await supabase.rpc('log_slow_query_v2', {
      p_operation_type   : 'active_matching_pool'
      p_execution_time_ms: 0
      p_table_name: 'mv_active_matching_pool')
    })
    
    return result.data;
  } catch (error) {
    console.error('Failed to get active matching pool:', error)
    throw error;
  }
}
