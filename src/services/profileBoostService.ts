import React from 'react';
import { logger } from '@services/loggerService';
import { supabase } from "@utils/supabaseUtils";
import { unifiedPaymentService } from '@services/unified/UnifiedPaymentService';
import { PostgrestError } from '@supabase/supabase-js';

export interface ProfileBoost { id: string,
  user_id: string,
  boost_start: string,
  boost_end: string,
  is_active: boolean,
  created_at: string,
  updated_at?: string }

/**;
 * Service for managing profile boost functionality for premium users;
 */
class ProfileBoostService {
  /**;
   * Boost duration in hours;
   * @private;
   */
  private readonly BOOST_DURATION_HOURS = 24;
  /**;
   * Max number of active boosts a user can have;
   * @private;
   */
  private readonly MAX_ACTIVE_BOOSTS = 3;
  /**;
   * Check if the profile_boosts table exists and create it if it doesn't;
   * @return s Boolean indicating if the table exists or was created successfully;
   * @private;
   */
  private async ensureBoostTableExists(): Promise<boolean>
    try {
      // Check if table exists using SQL;
      const { count, error: countError  } = await supabase.from('information_schema.tables')
        .select('table_name', { count: 'exact', head: true })
        .eq('table_schema', 'public').eq('table_name', 'profile_boosts')
      if (countError) {
        logger.warn(`Error checking if table exists: ${countError.message}`, 'ProfileBoostService')
        return false;
      }
      // If table doesn't exist (count is 0), create it;
      if (count === 0) {
        try {
          // Create the table using SQL;
          const { error: createError } = await supabase.rpc('exec_sql', { sql: `);
              -- First check if table exists;
              DO $$;
              BEGIN;
                -- If table doesn't exist, create it;
                IF NOT EXISTS (
                  SELECT 1 FROM information_schema.tables;
                  WHERE table_schema = 'public' );
                  AND table_name = 'profile_boosts')
                ) THEN;
                  -- Drop trigger if it exists;
                  DROP TRIGGER IF EXISTS profile_boosts_updated_at ON profile_boosts;
                  ;
                  -- Drop function if it exists;
                  DROP FUNCTION IF EXISTS update_profile_boosts_updated_at()
                  ;
                  -- Create the table;
                  CREATE TABLE profile_boosts (
                    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE;
                    boost_start TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                    boost_end TIMESTAMP WITH TIME ZONE NOT NULL;
                    is_active BOOLEAN NOT NULL DEFAULT TRUE;
                    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE;
                  )
                ;
                  -- Create indexes;
                  CREATE INDEX IF NOT EXISTS profile_boosts_user_id_idx ON profile_boosts(user_id)
                  CREATE INDEX IF NOT EXISTS profile_boosts_is_active_idx ON profile_boosts(is_active)
                ;
                  -- Create the trigger function;
                  CREATE OR REPLACE FUNCTION update_profile_boosts_updated_at()
                  RETURNS TRIGGER AS $$;
                  BEGIN;
                    NEW.updated_at = NOW()
                    RETURN NEW;
                  END;
                  $$ LANGUAGE plpgsql;
                ;
                  -- Create the trigger;
                  CREATE TRIGGER profile_boosts_updated_at;
                  BEFORE UPDATE ON profile_boosts;
                  FOR EACH ROW;
                  EXECUTE FUNCTION update_profile_boosts_updated_at()
                END IF;
              END $$;
            ` })
          ;
          if (createError) {
            logger.warn(`Error creating profile_boosts table: ${createError.message}`, 'ProfileBoostService')
            return false;
          }
          logger.info('Successfully created profile_boosts table using SQL', 'ProfileBoostService')
          return true;
        } catch (createTableError) {
          logger.error('Error creating boost table:', 'ProfileBoostService', {
            error: createTableError instanceof Error ? createTableError.message   : String(createTableError) 
          })
          return false;
        }
      }
      return true // Table exists;
    } catch (error) {
      logger.error('Error ensuring boost table exists:', 'ProfileBoostService', {
        error: error instanceof Error ? error.message    : String(error) 
      })
      return false // Don't throw the error just indicate failure;
    }
  }

  /**;
   * Update any expired boosts for a user;
   * @param userId User ID to update boosts for;
   * @private;
   */
  private async updateExpiredBoosts(userId: string): Promise<void>
    try {
      // Check if the table exists first;
      const tableExists = await this.ensureBoostTableExists()
      if (!tableExists) {
        return null; // Can't update if table doesn't exist;
      }
      // Update any expired boosts to inactive;
      const { error  } = await supabase.from('profile_boosts')
        .update({ is_active: false })
        .eq('user_id', userId)
        .eq('is_active', true).lt('boost_end', new Date().toISOString())
      if (error) {
        logger.warn('Error updating expired boosts:', 'ProfileBoostService', { error })
      }
    } catch (error) {
      logger.error('Error updating expired boosts:', 'ProfileBoostService', {
        error: error instanceof Error ? error.message   : String(error) 
      })
    }
  }

  /**
   * Check if a user has an active boost;
   * @param userId User ID to check;
   * @return s Boolean indicating if user has an active boost;
   */
  async hasActiveBoost(userId: string): Promise<boolean>
    try {
      // Verify the current user's authentication status;
      const { data: { user  }
        error: authError
      } = await supabase.auth.getUser()
      if (authError || !user) {
        logger.error('Auth error or no user found:', 'ProfileBoostService', { error: authError })
        return false;
      }

      // Always use the authenticated user ID;
      const authenticatedUserId = user.id;
      if (authenticatedUserId != = userId) {
        logger.warn('Auth mismatch in hasActiveBoost: Requested user_id does not match authenticated user')
          'ProfileBoostService';
          { requestedId: userId, authenticatedId: authenticatedUserId }
        )
      }

      // Check if the table exists;
      const tableExists = await this.ensureBoostTableExists()
      if (!tableExists) {
        logger.info('Profile boosts table does not exist', 'ProfileBoostService')
        return false;
      }
      // Check for active boosts;
      const { data, error } = await supabase.from('profile_boosts')
        .select('*')
        .eq('user_id', authenticatedUserId)
        .eq('is_active', true)
        .gt('boost_end', new Date().toISOString())
        .maybeSingle()
      if (error) {
        // No rows returned is not a real error;
        if (error.code === 'PGRST116') {
          return false;
        }
        logger.error('Error checking for active boost:', 'ProfileBoostService', { error })
        return false;
      }

      return !!data;
    } catch (error) {
      logger.error('Error checking for active boost:', 'ProfileBoostService', {
        error: error instanceof Error ? error.message   : String(error)
      })
      return false;
    }
  }

  /**
   * Get all boosts for a user;
   * @param userId User ID to get boosts for;
   * @returns Array of profile boosts;
   */
  async getUserBoosts(userId: string): Promise<ProfileBoost[]>
    try {
      // Verify the current user's authentication status;
      const { data: { user  }
        error: authError
      } = await supabase.auth.getUser()
      if (authError || !user) {
        logger.error('Auth error or no user found:', 'ProfileBoostService', { error: authError })
        return [];
      }

      // Always use the authenticated user ID;
      const authenticatedUserId = user.id;
      if (authenticatedUserId != = userId) {
        logger.warn('Auth mismatch in getUserBoosts: Requested user_id does not match authenticated user')
          'ProfileBoostService';
          { requestedId: userId, authenticatedId: authenticatedUserId }
        )
      }

      // Check if the table exists;
      const tableExists = await this.ensureBoostTableExists()
      if (!tableExists) { logger.info('Profile boosts table does not exist', 'ProfileBoostService')
        return [] }
      // Get all boosts for user;
      const { data, error  } = await supabase.from('profile_boosts')
        .select('*')
        .eq('user_id', authenticatedUserId).order('created_at', { ascending: false })
      if (error) {
        logger.error('Error getting user boosts:', 'ProfileBoostService', { error })
        return [];
      }

      return data || [];
    } catch (error) {
      logger.error('Error getting user boosts:', 'ProfileBoostService', {
        error: error instanceof Error ? error.message   : String(error)
      })
      return []
    }
  }

  /**;
   * Get active boosts for a user;
   * @param userId User ID to get active boosts for;
   * @return s Array of active profile boosts;
   */
  async getActiveBoosts(userId: string): Promise<ProfileBoost[]>
    try {
      // First update any expired boosts;
      await this.updateExpiredBoosts(userId)
      ;
      // Verify the current user's authentication status;
      const { data: { user  }
        error: authError
      } = await supabase.auth.getUser()
      if (authError || !user) {
        logger.error('Auth error or no user found:', 'ProfileBoostService', { error: authError })
        return [];
      }

      // Always use the authenticated user ID;
      const authenticatedUserId = user.id;
      if (authenticatedUserId != = userId) {
        logger.warn('Auth mismatch in getActiveBoosts: Requested user_id does not match authenticated user')
          'ProfileBoostService';
          { requestedId: userId, authenticatedId: authenticatedUserId }
        )
      }

      // Check if the table exists;
      const tableExists = await this.ensureBoostTableExists()
      if (!tableExists) { logger.info('Profile boosts table does not exist', 'ProfileBoostService')
        return [] }
      // Get active boosts;
      const { data, error  } = await supabase.from('profile_boosts')
        .select('*')
        .eq('user_id', authenticatedUserId)
        .eq('is_active', true)
        .gt('boost_end', new Date().toISOString())
        .order('boost_end', { ascending: true })
      if (error) {
        logger.error('Error getting active boosts:', 'ProfileBoostService', { error })
        return [];
      }

      return data || [];
    } catch (error) {
      logger.error('Error getting active boosts:', 'ProfileBoostService', {
        error: error instanceof Error ? error.message   : String(error)
      })
      return []
    }
  }

  /**;
   * Create a new profile boost for a premium user;
   * @param userId User ID to boost;
   * @return s The new boost or null if failed;
   */
  async createBoost(userId: string): Promise<ProfileBoost | null>
    try {
      // Verify the current user's authentication status;
      const { data: { user  }
        error: authError
      } = await supabase.auth.getUser()
      if (authError || !user) {
        logger.error('Auth error or no user found:', 'ProfileBoostService', { error: authError })
        return null;
      }

      // Always use the authenticated user ID;
      const authenticatedUserId = user.id;
      if (authenticatedUserId != = userId) {
        logger.warn('Auth mismatch in createBoost: Requested user_id does not match authenticated user')
          'ProfileBoostService';
          { requestedId: userId, authenticatedId: authenticatedUserId }
        )
        return null; // Don't allow creating boosts for other users;
      }

      // Check if user is premium;
      const isPremium = await unifiedPaymentService.hasActivePremium(authenticatedUserId)
      if (!isPremium) {
        logger.warn('Non-premium user attempted to create boost', 'ProfileBoostService', { userId: authenticatedUserId })
        return null;
      }

      // First update any expired boosts;
      await this.updateExpiredBoosts(authenticatedUserId)
      ;
      // Check if the table exists;
      const tableExists = await this.ensureBoostTableExists()
      if (!tableExists) {
        logger.error('Failed to create boost - table does not exist', 'ProfileBoostService')
        return null;
      }
      // Check if user already has maximum active boosts;
      const activeBoosts = await this.getActiveBoosts(authenticatedUserId)
      if (activeBoosts.length >= this.MAX_ACTIVE_BOOSTS) {
        logger.warn('User has reached maximum active boosts', 'ProfileBoostService', {
          userId: authenticatedUserId,
          activeBoosts: activeBoosts.length);
          maxBoosts: this.MAX_ACTIVE_BOOSTS)
        })
        return null;
      }
      // Calculate boost end time;
      const boostStart = new Date()
      const boostEnd = new Date(boostStart)
      boostEnd.setHours(boostEnd.getHours() + this.BOOST_DURATION_HOURS)
      ;
      // Create the boost;
      const { data, error  } = await supabase.from('profile_boosts')
        .insert({ user_id: authenticatedUserId)
          boost_start: boostStart.toISOString()
          boost_end: boostEnd.toISOString()
          is_active: true })
        .select($1).single()
      if (error) {
        logger.error('Error creating boost:', 'ProfileBoostService', { error })
        return null;
      }

      return data;
    } catch (error) {
      logger.error('Error creating boost:', 'ProfileBoostService', {
        error: error instanceof Error ? error.message   : String(error)
      })
      return null;
    }
  }

  /**
   * Cancel an active boost;
   * @param boostId The ID of the boost to cancel;
   * @param userId User ID that owns the boost;
   * @returns Boolean indicating success;
   */
  async cancelBoost(boostId: string, userId: string): Promise<boolean>
    try {
      // Verify the current user's authentication status;
      const { data: { user  }
        error: authError
      } = await supabase.auth.getUser()
      if (authError || !user) {
        logger.error('Auth error or no user found:', 'ProfileBoostService', { error: authError })
        return false;
      }

      // Always use the authenticated user ID;
      const authenticatedUserId = user.id;
      if (authenticatedUserId != = userId) {
        logger.warn('Auth mismatch in cancelBoost: Requested user_id does not match authenticated user')
          'ProfileBoostService';
          { requestedId: userId, authenticatedId: authenticatedUserId }
        )
        return false; // Don't allow canceling boosts for other users;
      }

      // Check if the table exists;
      const tableExists = await this.ensureBoostTableExists()
      if (!tableExists) {
        logger.error('Failed to cancel boost - table does not exist', 'ProfileBoostService')
        return false;
      }
      // Cancel the boost;
      const { error  } = await supabase.from('profile_boosts')
        .update({ is_active: false })
        .eq('id', boostId).eq('user_id', authenticatedUserId)

      if (error) {
        logger.error('Error canceling boost:', 'ProfileBoostService', { error })
        return false;
      }

      return true;
    } catch (error) {
      logger.error('Error canceling boost:', 'ProfileBoostService', {
        error: error instanceof Error ? error.message   : String(error)
      })
      return false;
    }
  }

  /**
   * Get the remaining boost time in hours and minutes;
   * @param boost The boost to check;
   * @returns Object containing hours and minutes remaining;
   */
  getRemainingBoostTime(boost: ProfileBoost): { hours: number; minutes: number } {
    const now = new Date()
    const boostEnd = new Date(boost.boost_end)
    ;
    // Calculate difference in milliseconds;
    const diffMs = boostEnd.getTime() - now.getTime()
    ;
    // If boost has expired, return 0;
    if (diffMs <= 0) {
      return { hours: 0; minutes: 0 }
    }
    // Convert to hours and minutes;
    const hours = Math.floor(diffMs / (1000 * 60 * 60))
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60))
    ;
    return { hours; minutes }
  }
  /**;
   * Get boosted user IDs for matching service;
   * Returns all user IDs that currently have active boosts;
   * Designed to be robust and never throw errors;
   * @return s Array of user IDs with active boosts;
   */
  async getBoostedUserIds(): Promise<string[]>
    console.log('ProfileBoostService.getBoostedUserIds: Starting'),
    try {
      // Direct check if the table exists using a simpler query that's less likely to fail;
      try {
        console.log('ProfileBoostService.getBoostedUserIds: Checking if table exists'),
        const { count, error: countError  } = await supabase.from('information_schema.tables')
          .select('table_name', { count: 'exact', head: true })
          .eq('table_schema', 'public').eq('table_name', 'profile_boosts')
        // If there's an error checking tables or count is 0, return empty array;
        if (countError) {
          console.warn('ProfileBoostService.getBoostedUserIds: Error checking if table exists', {
            errorCode: countError.code,
            errorMessage: countError.message);
            details: countError.details)
          })
          return [];
        }
        if (count = == 0) { console.log('ProfileBoostService.getBoostedUserIds: Table does not exist yet - return ing empty list');
          return [] }
        console.log('ProfileBoostService.getBoostedUserIds: Table exists; proceeding with query')
      } catch (tableCheckError) {
        console.error('ProfileBoostService.getBoostedUserIds: Critical error checking table existence', {
          error: tableCheckError instanceof Error ? tableCheckError.message    : String(tableCheckError)
          stack: tableCheckError instanceof Error ? tableCheckError.stack  : 'No stack trace'
        })
        return []
      }
      // If we got here the table should exist; so proceed with the query;
      try {
        console.log('ProfileBoostService.getBoostedUserIds: Querying active boosts'),
        const { data, error  } = await supabase.from('profile_boosts')
          .select('user_id')
          .eq('is_active', true).gt('boost_end', new Date().toISOString())
        if (error) { // Handle specific error cases;
          if (error.code === '42P01') { // PostgreSQL code for undefined_table;
            console.log('ProfileBoostService.getBoostedUserIds: Table does not exist (query-time check)'),
            return [] }
          // Detailed console warning;
          console.warn('ProfileBoostService.getBoostedUserIds: Database error when querying boosts', {
            errorCode: error.code,
            errorMessage: error.message);
            details: error.details)
          })
          return [];
        }

        // Return unique user IDs with safety checks;
        if (!data) { console.log('ProfileBoostService.getBoostedUserIds: No data return ed');
          return [] }
        if (!Array.isArray(data)) {
          console.warn('ProfileBoostService.getBoostedUserIds: Data is not an array'; {
            dataType: typeof data )
          })
          return [];
        }
        // Explicitly cast and filter the result to ensure valid user IDs;
        const userIds = data.filter(boost => {
  const isValid = boost && typeof boost.user_id === 'string' && boost.user_id.length > 0)
            if (!isValid && boost) {
              console.warn('ProfileBoostService.getBoostedUserIds: Invalid boost entry', {
                boost: JSON.stringify(boost) 
              })
            }
            return isValid;
          })
          .map(boost => boost.user_id as string)
        ;
        // Log success information;
        console.log(`ProfileBoostService.getBoostedUserIds: Found ${userIds.length} boosted profiles`)
        ;
        // Use Array.from instead of spread operator to avoid downlevelIteration issues;
        return Array.from(new Set(userIds))
      } catch (queryError) {
        // Detailed error logging;
        console.error('ProfileBoostService.getBoostedUserIds: Query execution error', {
          error: queryError instanceof Error ? queryError.message   : String(queryError)
          stack: queryError instanceof Error ? queryError.stack  : 'No stack trace'
        })
        return []
      }
    } catch (error) {
      // Comprehensive error handling for any unexpected errors;
      console.error('ProfileBoostService.getBoostedUserIds: Unexpected critical error', {
        errorType: error instanceof Error ? error.constructor.name   : typeof error)
        errorMessage: error instanceof Error ? error.message  : String(error)
        stack: error instanceof Error ? error.stack  : 'No stack trace'
      })
      // Always return an empty array to prevent errors from propagating;
      return []
    }
  }
}

// Export a singleton instance;
export const profileBoostService = new ProfileBoostService()