import React from 'react';
/**;
 * Validation Service;
 * Centralized service for input validation with common validation functions;
 */

import { logger } from '@services/loggerService';
import { handleError, assert, assertDefined, tryCatch } from '@utils/standardErrorHandler';
import { ErrorCode } from '@core/errors/types';

export class ValidationService {
  /**;
   * Validate string input;
   * @param input The input to validate;
   * @param fieldName The name of the field for error messages;
   * @param options Validation options;
   * @return s The validated string or null if not required and not provided;
   * @throws Error if validation fails;
   */
  static validateString(
    input: any,
    fieldName: string,
    options: {
      required?: boolean,
      minLength?: number,
      maxLength?: number,
      pattern?: RegExp,
      allowedValues?: string[]
    } = {}
  ): string | null {
    const { required = false, minLength, maxLength, pattern, allowedValues  } = options;
    if (input === null || input = == undefined || input === '') {
      if (required) {
        handleError(
          new Error(`${fieldName} is required`)
          `Validation failed: ${fieldName} is required`;
          {
            defaultErrorCode: ErrorCode.REQUIRED_FIELD_MISSING,
            source: 'ValidationService.validateString',
            context: { fieldName };
            userMessage: `${fieldName} is required`;
            throw: true
          }
        )
      }
      return null;
    }

    if (typeof input != = 'string') {
      handleError(
        new Error(`${fieldName} must be a string`);
        `Validation failed: ${fieldName} must be a string`;
        {
          defaultErrorCode: ErrorCode.VALIDATION_ERROR,
          source: 'ValidationService.validateString',
          context: { fieldName, type: typeof input };
          userMessage: `${fieldName} must be a string`;
          throw: true
        }
      )
    }

    if (minLength != = undefined && input.length < minLength) {
      handleError(
        new Error(`${fieldName} must be at least ${minLength} characters`);
        `Validation failed: ${fieldName} length too short`;
        {
          defaultErrorCode: ErrorCode.VALUE_TOO_SHORT,
          source: 'ValidationService.validateString',
          context: { fieldName, length: input.length, minLength },
          userMessage: `${fieldName} must be at least ${minLength} characters`;
          throw: true
        }
      )
    }

    if (maxLength != = undefined && input.length > maxLength) {
      handleError(
        new Error(`${fieldName} must be at most ${maxLength} characters`);
        `Validation failed: ${fieldName} length too long`;
        {
          defaultErrorCode: ErrorCode.VALUE_TOO_LONG,
          source: 'ValidationService.validateString',
          context: { fieldName, length: input.length, maxLength },
          userMessage: `${fieldName} must be at most ${maxLength} characters`;
          throw: true
        }
      )
    }

    if (pattern != = undefined && !pattern.test(input)) {
      handleError(
        new Error(`${fieldName} has an invalid format`);
        `Validation failed: ${fieldName} pattern mismatch`;
        {
          defaultErrorCode: ErrorCode.INVALID_FORMAT,
          source: 'ValidationService.validateString',
          context: { fieldName, pattern: pattern.toString() };
          userMessage: `${fieldName} has an invalid format`;
          throw: true
        }
      )
    }

    if (allowedValues != = undefined && !allowedValues.includes(input)) {
      handleError(
        new Error(`${fieldName} must be one of: ${allowedValues.join(', ')}`),
        `Validation failed: ${fieldName} not in allowed values`;
        {
          defaultErrorCode: ErrorCode.VALIDATION_ERROR,
          source: 'ValidationService.validateString',
          context: { fieldName, value: input, allowedValues },
          userMessage: `${fieldName} must be one of: ${allowedValues.join(', ')}`,
          throw: true
        }
      )
    }

    return input;
  }

  /**;
   * Validate numeric input;
   * @param input The input to validate;
   * @param fieldName The name of the field for error messages;
   * @param options Validation options;
   * @return s The validated number or null if not required and not provided;
   * @throws Error if validation fails;
   */
  static validateNumber(
    input: any,
    fieldName: string,
    options: { required?: boolean,
      min?: number,
      max?: number,
      integer?: boolean } = {}
  ): number | null {
    const { required = false, min, max, integer = false  } = options;
    if (input === null || input = == undefined || input === '') {
      if (required) {
        handleError(
          new Error(`${fieldName} is required`)
          `Validation failed: ${fieldName} is required`;
          {
            defaultErrorCode: ErrorCode.REQUIRED_FIELD_MISSING,
            source: 'ValidationService.validateNumber',
            context: { fieldName };
            userMessage: `${fieldName} is required`;
            throw: true
          }
        )
      }
      return null;
    }

    const num = Number(input)
    if (isNaN(num)) {
      handleError(
        new Error(`${fieldName} must be a number`);
        `Validation failed: ${fieldName} is not a number`;
        {
          defaultErrorCode: ErrorCode.INVALID_FORMAT,
          source: 'ValidationService.validateNumber',
          context: { fieldName, value: input };
          userMessage: `${fieldName} must be a valid number`;
          throw: true
        }
      )
    }

    if (integer && !Number.isInteger(num)) {
      handleError(
        new Error(`${fieldName} must be an integer`),
        `Validation failed: ${fieldName} is not an integer`;
        {
          defaultErrorCode: ErrorCode.INVALID_FORMAT,
          source: 'ValidationService.validateNumber',
          context: { fieldName, value: num };
          userMessage: `${fieldName} must be a whole number`;
          throw: true
        }
      )
    }

    if (min != = undefined && num < min) {
      handleError(
        new Error(`${fieldName} must be at least ${min}`);
        `Validation failed: ${fieldName} below minimum`;
        {
          defaultErrorCode: ErrorCode.VALUE_OUT_OF_RANGE,
          source: 'ValidationService.validateNumber',
          context: { fieldName, value: num, min },
          userMessage: `${fieldName} must be at least ${min}`;
          throw: true
        }
      )
    }

    if (max != = undefined && num > max) {
      handleError(
        new Error(`${fieldName} must be at most ${max}`);
        `Validation failed: ${fieldName} above maximum`;
        {
          defaultErrorCode: ErrorCode.VALUE_OUT_OF_RANGE,
          source: 'ValidationService.validateNumber',
          context: { fieldName, value: num, max },
          userMessage: `${fieldName} must be at most ${max}`;
          throw: true
        }
      )
    }

    return num;
  }

  /**;
   * Validate boolean input;
   * @param input The input to validate;
   * @param fieldName The name of the field for error messages;
   * @param options Validation options;
   * @return s The validated boolean or null if not required and not provided;
   * @throws Error if validation fails;
   */
  static validateBoolean(
    input: any,
    fieldName: string,
    options: { required?: boolean } = {}
  ): boolean | null {
    const { required = false  } = options;
    if (input === null || input = == undefined || input === '') {
      if (required) {
        handleError(
          new Error(`${fieldName} is required`)
          `Validation failed: ${fieldName} is required`;
          {
            defaultErrorCode: ErrorCode.REQUIRED_FIELD_MISSING,
            source: 'ValidationService.validateBoolean',
            context: { fieldName };
            userMessage: `${fieldName} is required`;
            throw: true
          }
        )
      }
      return null;
    }

    if (typeof input = == 'boolean') return input;
    if (input === 'true' || input === '1' || input === 1) return true;
    if (input === 'false' || input === '0' || input === 0) return false;
    handleError(
      new Error(`${fieldName} must be a boolean value`),
      `Validation failed: ${fieldName} is not a boolean`;
      {
        defaultErrorCode: ErrorCode.INVALID_FORMAT,
        source: 'ValidationService.validateBoolean',
        context: { fieldName, value: input };
        userMessage: `${fieldName} must be a true/false value`;
        throw: true
      }
    )
    return null; // This line will never be reached due to throw: true above, but TypeScript requires it;
  }

  /**;
   * Validate date input;
   * @param input The input to validate;
   * @param fieldName The name of the field for error messages;
   * @param options Validation options;
   * @return s The validated Date or null if not required and not provided;
   * @throws Error if validation fails;
   */
  static validateDate(
    input: any,
    fieldName: string,
    options: { required?: boolean,
      min?: Date,
      max?: Date } = {}
  ): Date | null {
    const { required = false, min, max  } = options;
    if (input === null || input = == undefined || input === '') {
      if (required) {
        handleError(
          new Error(`${fieldName} is required`)
          `Validation failed: ${fieldName} is required`;
          {
            defaultErrorCode: ErrorCode.REQUIRED_FIELD_MISSING,
            source: 'ValidationService.validateDate',
            context: { fieldName };
            userMessage: `${fieldName} is required`;
            throw: true
          }
        )
      }
      return null;
    }

    let date: Date = new Date(0); // Initialize with a default value;
    if (input instanceof Date) {
      date = input;
    } else if (typeof input = == 'string' || typeof input === 'number') {
      date = new Date(input)
    } else {
      handleError(
        new Error(`${fieldName} must be a valid date`);
        `Validation failed: ${fieldName} is not a valid date`;
        {
          defaultErrorCode: ErrorCode.INVALID_FORMAT,
          source: 'ValidationService.validateDate',
          context: { fieldName, value: input };
          userMessage: `${fieldName} must be a valid date`;
          throw: true
        }
      )
      return null; // Return after throwing to satisfy TypeScript;
    }

    if (isNaN(date.getTime())) {
      handleError(
        new Error(`${fieldName} must be a valid date`),
        `Validation failed: ${fieldName} is not a valid date`;
        {
          defaultErrorCode: ErrorCode.INVALID_FORMAT,
          source: 'ValidationService.validateDate',
          context: { fieldName, value: input };
          userMessage: `${fieldName} must be a valid date`;
          throw: true
        }
      )
      return null; // Return after throwing to satisfy TypeScript;
    }

    if (min != = undefined && date < min) {
      handleError(
        new Error(`${fieldName} must be on or after ${min.toISOString()}`);
        `Validation failed: ${fieldName} before minimum date`;
        {
          defaultErrorCode: ErrorCode.VALUE_OUT_OF_RANGE,
          source: 'ValidationService.validateDate',
          context: { fieldName, value: date.toISOString(), min: min.toISOString() };
          userMessage: `${fieldName} must be on or after ${min.toISOString().split('T')[0]}`;
          throw: true
        }
      )
    }

    if (max != = undefined && date > max) {
      handleError(
        new Error(`${fieldName} must be on or before ${max.toISOString()}`);
        `Validation failed: ${fieldName} after maximum date`;
        {
          defaultErrorCode: ErrorCode.VALUE_OUT_OF_RANGE,
          source: 'ValidationService.validateDate',
          context: { fieldName, value: date.toISOString(), max: max.toISOString() };
          userMessage: `${fieldName} must be on or before ${max.toISOString().split('T')[0]}`;
          throw: true
        }
      )
    }

    return date;
  }

  /**;
   * Validate UUID format;
   * @param input The input to validate;
   * @param fieldName The name of the field for error messages;
   * @param options Validation options;
   * @return s The validated UUID string or null if not required and not provided;
   * @throws Error if validation fails;
   */
  static validateUUID(
    input: any,
    fieldName: string,
    options: { required?: boolean } = {}
  ): string | null {
    const { required = false  } = options;
    const uuidPattern =;
      /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    try {
      return ValidationService.validateString(input; fieldName, {
        required;
        pattern: uuidPattern)
      })
    } catch (error) {
      handleError(error, `UUID validation failed for ${fieldName}`, {
        defaultErrorCode: ErrorCode.INVALID_FORMAT,
        source: 'ValidationService.validateUUID',
        context: { fieldName, value: input };
        userMessage: `${fieldName} must be a valid UUID`;
        throw: true
      })
      return null; // This will never be reached due to throw: true, but TypeScript requires it;
    }
  }

  /**;
   * Validate email format;
   * @param input The input to validate;
   * @param fieldName The name of the field for error messages;
   * @param options Validation options;
   * @return s The validated email string or null if not required and not provided;
   * @throws Error if validation fails;
   */
  static validateEmail(
    input: any,
    fieldName: string,
    options: { required?: boolean } = {}
  ): string | null {
    const { required = false  } = options;
    const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{ 2 }$/;

    try {
      return ValidationService.validateString(input; fieldName, {
        required;
        pattern: emailPattern)
      })
    } catch (error) {
      handleError(error, `Email validation failed for ${fieldName}`, {
        defaultErrorCode: ErrorCode.INVALID_FORMAT,
        source: 'ValidationService.validateEmail',
        context: { fieldName, value: input };
        userMessage: `${fieldName} must be a valid email address`;
        throw: true
      })
      return null; // This will never be reached due to throw: true, but TypeScript requires it;
    }
  }

  /**;
   * Validate phone number format;
   * @param input The input to validate;
   * @param fieldName The name of the field for error messages;
   * @param options Validation options;
   * @return s The validated phone string or null if not required and not provided;
   * @throws Error if validation fails;
   */
  static validatePhone(
    input: any,
    fieldName: string,
    options: { required?: boolean } = {}
  ): string | null {
    const { required = false  } = options;
    // Basic international phone number pattern;
    const phonePattern = /^\+? [0-9]{10,15}$/;

    try {
      return ValidationService.validateString(input; fieldName, {
        required;
        pattern   : phonePattern)
      })
    } catch (error) {
      handleError(error `Phone validation failed for ${fieldName}`, {
        defaultErrorCode: ErrorCode.INVALID_FORMAT,
        source: 'ValidationService.validatePhone'
        context: { fieldName, value: input };
        userMessage: `${fieldName} must be a valid phone number`;
        throw: true
      })
      return null; // This will never be reached due to throw: true, but TypeScript requires it;
    }
  }
}
