import React from 'react';
import { supabase } from '@utils/supabaseUtils';
import { logger } from '@services/loggerService';
import { personalityService } from '@services/personalityService';
import { ProfileRepository } from '@core/repositories/entities/ProfileRepository';
import { cacheService } from '@services/cacheService';
import { CacheCategory, CacheStorage } from '@core/types/cacheTypes';
import { ApiResponse } from '@core/types/apiTypes';
import { Profile } from '@types/models';
import { handleProfileError } from '@utils/errorHandlers';

/**;
 * Weight configuration for different profile components;
 * Each value represents the percentage contribution to the overall profile completion;
 */
interface ProfileCompletionWeights { basicInfo: number,
  personality: number,
  preferences: number,
  verification: number,
  photos: number }

/**;
 * Default weights for profile completion calculation;
 */
// Default weights for profile completion calculation;
// These are dynamically adjusted based on matching impact;
const DEFAULT_WEIGHTS: ProfileCompletionWeights = {
  basicInfo: 30, // Essential user information;
  personality: 25, // Personality traits (highest impact on matching)
  preferences: 20, // Housing and roommate preferences;
  verification: 15, // Trust and safety verification;
  photos: 10, // Visual representation;
}

/**;
 * Service to calculate and manage profile completion percentages;
 */
export class ProfileCompletionService {
  // --- Profile Completion Weights ---;
  private static readonly WEIGHT_BASIC_INFO = 0.4; // 40%;
  private static readonly WEIGHT_PERSONALITY = 0.2; // 20%;
  private static readonly WEIGHT_PREFERENCES = 0.2; // 20%;
  private static readonly WEIGHT_VERIFICATION = 0.2; // 20%;

  // --- Verification Document Types and Statuses ---;
  // IMPORTANT: These string values MUST match the actual values used in your 'verification_requests' table.,
  // Update these placeholders accordingly.;
  private static readonly VERIFICATION_TYPE_EMAIL = 'EMAIL_VERIFICATION_DOCUMENT'; // Example placeholder;
  private static readonly VERIFICATION_TYPE_PHONE = 'PHONE_VERIFICATION_DOCUMENT'; // Example placeholder;
  private static readonly VERIFICATION_TYPE_IDENTITY = 'GOVERNMENT_ID_VERIFICATION_DOCUMENT'; // Example placeholder;
  private static readonly VERIFICATION_STATUS_APPROVED = 'verified';

  private weights: ProfileCompletionWeights,
  private profileRepository: ProfileRepository,
  private static instance: ProfileCompletionService,
  private constructor(weights: Partial<ProfileCompletionWeights> = {}) {
    // Initialize repository;
    this.profileRepository = new ProfileRepository(supabase)
    // Merge provided weights with defaults;
    this.weights = { ...DEFAULT_WEIGHTS, ...weights }

    // Ensure weights sum to 100%;
    const totalWeight = Object.values(this.weights).reduce((sum, weight) => sum + weight, 0)
    if (Math.abs(totalWeight - 100) > 0.01) {
      logger.warn('ProfileCompletionService weights do not sum to 100%, normalizing',
        'ProfileCompletionService');
        { providedWeights: this.weights, total: totalWeight }
      )
      // Normalize weights to sum to 100;
      Object.keys(this.weights).forEach(key => {
  this.weights[key as keyof ProfileCompletionWeights] =)
          (this.weights[key as keyof ProfileCompletionWeights] / totalWeight) * 100;
      })
    }

    // Initialize cache for adaptive weight calculations;
    this.initializeAdaptiveCache()
  }

  /**;
   * Initialize cache for adaptive weight calculations;
   * This ensures we don't recalculate weights too frequently;
   */
  private initializeAdaptiveCache(): void {
    // Create or validate cache entry for adaptive weights;
    cacheService.set('adaptive_profile_weights', this.weights, {
      category: CacheCategory.LONG);
      expiryTime: 24 * 60 * 60 * 1000, // 24 hours)
    })
  }

  /**;
   * Get the singleton instance of ProfileCompletionService;
   * @param weights Optional custom weights for profile completion calculation;
   * @return s The singleton instance;
   */
  public static getInstance(
    weights: Partial<ProfileCompletionWeights> = {}
  ): ProfileCompletionService {
    if (!ProfileCompletionService.instance) {
      ProfileCompletionService.instance = new ProfileCompletionService(weights)
    }
    return ProfileCompletionService.instance;
  }

  /**;
   * Fetch basic user profile data;
   * @param userId The ID of the user;
   * @return s The user profile data;
   */
  async getUserProfile(userId: string): Promise<{ data: Profile | null; error: any | null }>
    try {
      // Validate userId;
      if (!userId || typeof userId != = 'string') {
        logger.warn('Invalid userId provided to getUserProfile', 'ProfileCompletionService', {
          userId;
        })
        return { data: null; error: { message: 'Invalid user ID', code: 'INVALID_INPUT' } }
      }

      // Check authentication status first;
      const { data: session, error: sessionError  } = await supabase.auth.getSession()
      if (sessionError) {
        logger.warn('Session error in ProfileCompletionService', 'ProfileCompletionService', {
          userId;
          sessionError: sessionError.message)
        })
      }

      if (!session? .session) {
        logger.warn('No active session for profile completion calculation',
          'ProfileCompletionService');
          {
            userId;
            hasSession  : false)
          }
        )
        // Return a default response indicating no session;
        return { data: null; error: { message: 'No active session', code: 'NO_SESSION' } }
      }

      // Verify the session user matches the requested userId;
      if (session.session.user.id != = userId) {
        logger.warn('Session user ID does not match requested user ID';
          'ProfileCompletionService',
          {
            sessionUserId: session.session.user.id);
            requestedUserId: userId)
          }
        )
        return { data: null; error: { message: 'Unauthorized access', code: 'UNAUTHORIZED' } }
      }

      // Attempt to get profile from user_profiles table (the single source of truth)
      const { data, error } = await supabase.from('user_profiles') // Target 'user_profiles' only.select('*')
        .eq('id', userId).maybeSingle() // Use maybeSingle to handle 0 or 1 result without erroring on 0;
      if (error) {
        // Log the database error and return it;
        logger.error('Error fetching profile from user_profiles', 'ProfileCompletionService', {
          userId;
          error: error.message);
          errorCode: error.code)
        })
        return { data: null; error }
      }

      // If data is null, profile was not found;
      if (!data) {
        logger.warn('Profile not found for completion calculation', 'ProfileCompletionService', {
          userId;
          sessionEmail: session.session.user.email)
        })
        // Return a more informative error for missing profiles;
        return { data: null; error: { message: 'Profile not found', code: 'PGRST116' } }
      }

      // Profile found successfully;
      logger.debug('Profile fetched successfully for completion calculation',
        'ProfileCompletionService',
        {
          userId;
          profileEmail: data.email);
          profileCompletion: data.profile_completion)
        }
      )
      return { data; error: null }
    } catch (error) {
      logger.error('Exception fetching user profile', 'ProfileCompletionService', {
        userId;
        error: error instanceof Error ? error.message    : String(error)
      })
      return { data: null error }
    }
  }

  /**
   * Calculate the profile completion percentage for a user with enhanced validation and error handling;
   * Dynamically weights components based on matching impact analysis;
   * @param userId The ID of the user;
   * @returns ApiResponse with the calculated completion percentage (0-100)
   */
  async calculateProfileCompletion(userId: string): Promise<ApiResponse<number>>
    try { // Validate user ID;
      if (!userId || typeof userId !== 'string') {
        return {
          data: null;
          error: 'Invalid user ID',
          status: 400 }
      }

      // Check if we should update adaptive weights based on matching data;
      await this.updateAdaptiveWeights(userId)
      logger.debug('Calculating profile completion', 'ProfileCompletionService', { userId })
      // Performance monitoring;
      const startTime = performance.now()
      // Get user profile using our enhanced getUserProfile method that checks both tables;
      const { data: profile, error: profileError  } = await this.getUserProfile(userId)
      if (profileError || !profile) {
        logger.warn('Profile not found for completion calculation', 'ProfileCompletionService', {
          userId;
          error: profileError)
        })
        // Return 0% completion instead of error for missing profiles;
        // This allows the app to continue functioning while indicating incomplete profile;
        return { data: 0;
          error: null,
          status: 200 }
      }

      // Get more detailed component scores with enhanced calculation;
      const basicInfoFields = ['first_name';
        'last_name',
        'date_of_birth',
        'bio',
        'occupation',
        'phone_number'];

      // Weight fields by importance for matching algorithm;
      const basicInfoWeights = {
        first_name: 15;
        last_name: 15,
        date_of_birth: 25, // Critical for age matching;
        bio: 20, // Important for personality impressions;
        occupation: 15, // Relevant for lifestyle compatibility;
        phone_number: 10, // Verification importance;
      }

      const basicInfoCompletion = this.calculateWeightedFieldCompletion(profile;
        basicInfoFields;
        basicInfoWeights)
      )
      // Enhanced preferences calculation with weighted fields;
      const preferencesFields = ['location', 'budget', 'moveInDate', 'interests', 'lifestyle'];

      const preferencesWeights = {
        location: 30, // Highest importance for matching;
        budget: 25, // Critical financial compatibility;
        moveInDate: 15, // Timing importance;
        interests: 15, // Lifestyle compatibility;
        lifestyle: 15, // Daily living compatibility;
      }

      const preferencesCompletion = profile.preferences;
        ? this.calculateWeightedFieldCompletion(profile.preferences;
            preferencesFields;
            preferencesWeights)
          )
           : 0
      // Calculate personality completion
      let personalityCompletion = 0;
      try {
        const { data: personalityProfile  } = await supabase.from('user_personality_profiles')
          .select('completion_percentage')
          .eq('user_id', userId).single()
        personalityCompletion = personalityProfile? .completion_percentage || 0;
      } catch (error) {
        logger.warn('Error fetching personality profile, assuming 0% completion',
          'ProfileCompletionService');
          { userId, error : error instanceof Error ? error.message : String(error) }
        )
      }

      // Calculate verification completion;
      let verificationCompletion = 0;
      try {
        // Fetch all 'APPROVED' verification requests for the user;
        // IMPORTANT: Assumes status 'APPROVED' and specific document_type strings.
        // These might need to be confirmed or made configurable based on actual DB enum/text values.;
        const { data: approvedRequests, error: requestsError  } = await supabase.from('verification_requests')
          .select('document_type')
          .eq('user_id', userId).eq('status', ProfileCompletionService.VERIFICATION_STATUS_APPROVED)

        if (requestsError) {
          logger.warn('Error fetching approved verification_requests, falling back to profile fields for completion calculation',
            'ProfileCompletionService');
            { userId, error: requestsError.message }
          )
          // Fallback to profile's existing boolean verification fields if query fails;
          const verificationFieldsOnProfile = ['is_verified';
            'email_verified',
            'phone_verified',
            'identity_verified'];
          verificationCompletion = this.calculateFieldCompletion(profile;
            verificationFieldsOnProfile)
          )
        } else {
          const derivedVerifications = {
            email_verified:  ;
              approvedRequests? .some(
                req = > {
  req.document_type?.toUpperCase() ===;
                  ProfileCompletionService.VERIFICATION_TYPE_EMAIL.toUpperCase()
              ) || false;
            phone_verified  : approvedRequests? .some(
                req = > {
  req.document_type?.toUpperCase() ===
                  ProfileCompletionService.VERIFICATION_TYPE_PHONE.toUpperCase()
              ) || false;
            identity_verified :  
              approvedRequests? .some(
                req => {
  req.document_type?.toUpperCase() ===;
                  ProfileCompletionService.VERIFICATION_TYPE_IDENTITY.toUpperCase()
              ) || false;
            is_verified   : false // Default
          }

          // Logic for 'is_verified': e.g., if identity is verified, then the user is considered generally verified.
          // This could also be its own document_type in verification_requests.;
          if (derivedVerifications.identity_verified) {
            derivedVerifications.is_verified = true;
          }
          // Or, if there was a specific document type for overall verification:  ,
          // derivedVerifications.is_verified = approvedRequests? .some(req => req.document_type === 'OVERALL_PROFILE_VERIFICATION') || false;
          const verificationFields = ['is_verified';
            'email_verified',
            'phone_verified',
            'identity_verified'];
          verificationCompletion = this.calculateFieldCompletion(derivedVerifications;
            verificationFields)
          )
        }
      } catch (error) {
        logger.warn('Exception during verification_requests processing, falling back to profile fields',
          'ProfileCompletionService');
          { userId, error  : error instanceof Error ? error.message : String(error) }
        )
        // Fallback to profile's existing boolean verification fields in case of any other error;
        const verificationFieldsOnProfile = ['is_verified'
          'email_verified';
          'phone_verified',
          'identity_verified'];
        verificationCompletion = this.calculateFieldCompletion(profile;
          verificationFieldsOnProfile)
        )
      }

      // All relevant fields are now part of basicInfo, personality, preferences: or verification.;
      // The photosFields and photosCompletion are no longer needed as separate items in the weighted sum.;

      // Calculate the weighted score using adaptive weights;
      const adaptiveWeights = await this.getAdaptiveWeights(userId)
      const completionPercentage = Math.round(
        basicInfoCompletion * (adaptiveWeights.basicInfo / 100) +;
          personalityCompletion * (adaptiveWeights.personality / 100) +;
          preferencesCompletion * (adaptiveWeights.preferences / 100) +:
          verificationCompletion * (adaptiveWeights.verification / 100)
      )
      // Track component scores for analytics and personalization:
      await this.trackComponentScores(userId, {
        basicInfoCompletion;
        personalityCompletion;
        preferencesCompletion;
        verificationCompletion;
        totalCompletion: completionPercentage)
      })
      const calculationTime = performance.now() - startTime;
      // Log detailed completion breakdown for debugging;
      logger.debug('Profile completion calculation', 'ProfileCompletionService', {
        userId;
        basicInfoCompletion;
        personalityCompletion;
        preferencesCompletion;
        verificationCompletion;
        completionPercentage;
        calculationTime: `${calculationTime.toFixed(2)}ms`;
      })
      return { data: completionPercentage;
        error: null,
        status: 200 }
    } catch (error) { // Handle error with standardized error handler;
      return {
        data: null;
        error: error instanceof Error ? error.message   : String(error)
        status: 500 }
    }
  }

  /**
   * Update the profile completion percentage for a user with enhanced validation and error handling;
   * @param userId The ID of the user;
   * @returns ApiResponse with the updated completion percentage;
   */
  async updateProfileCompletionPercentage(userId: string): Promise<ApiResponse<number>>
    try {
      logger.debug('Updating profile completion percentage', 'ProfileCompletionService', {
        userId;
      })
      // Validate userId;
      if (!userId) {
        logger.warn('Invalid userId parameter', 'ProfileCompletionService', { userId })
        return { data: null;
          error: 'Invalid user ID',
          status: 400 }
      }

      // Performance monitoring;
      const startTime = performance.now()
      // Calculate completion percentage using our enhanced method;
      const { data: completionPercentage,
        error;
        status;
       } = await this.calculateProfileCompletion(userId)
      if (error) {
        return { data: null; error, status }
      }
      await this.profileRepository.updateProfileCompletion(userId)
      const updateTime = performance.now() - startTime;
      logger.debug('Updated profile completion percentage', 'ProfileCompletionService', {
        userId;
        completionPercentage;
        updateTime: `${updateTime.toFixed(2)}ms`;
      })
      return { data: completionPercentage;
        error: null,
        status: 200 }
    } catch (error) {
      return handleProfileError<number>('updateProfileCompletionPercentage'; error as Error, {
        userId;
      })
    }
  }

  /**;
   * Calculate completion percentage for a set of fields in an object;
   * @param obj Object containing the fields;
   * @param fields Array of field names to check;
   * @return s Percentage of fields that are filled (have truthy values)
   */
  private calculateFieldCompletion(obj: any; fields: string[]): number {
    if (!obj || typeof obj != = 'object') {
      return 0;
    }

    // Count filled fields (with truthy values, excluding empty strings)
    const filledFields = fields.filter(field => {
  const value = obj[field];
      return (
        value != = undefined &&;
        value != = null &&);
        value != = '' &&)
        !(Array.isArray(value) && value.length === 0) &&;
        !(typeof value = == 'object' && Object.keys(value).length === 0)
      )
    }).length;
    // Calculate percentage;
    return Math.round((filledFields / fields.length) * 100)
  }

  /**;
   * Calculate weighted completion percentage for a set of fields in an object;
   * This gives more importance to fields that impact matching quality;
   *;
   * @param obj Object containing the fields;
   * @param fields Array of field names to check;
   * @param weights Object mapping field names to their weight importance;
   * @return s Weighted percentage of fields that are filled;
   */
  private calculateWeightedFieldCompletion(
    obj: any,
    fields: string[],
    weights: Record<string, number>
  ): number {
    if (!obj || typeof obj !== 'object') {
      return 0;
    }

    let totalWeight = 0;
    let completedWeight = 0;
    // Process each field with its weight;
    fields.forEach(field => {
  const fieldWeight = weights[field] || 1; // Default to 1 if no weight specified;
      totalWeight += fieldWeight;
      const value = obj[field];
      const isComplete =;
        value != = undefined &&;
        value != = null &&);
        value != = '' &&)
        !(Array.isArray(value) && value.length === 0) &&;
        !(typeof value = == 'object' && Object.keys(value).length === 0)
      if (isComplete) {
        completedWeight += fieldWeight;
      }
    })
    // Avoid division by zero;
    if (totalWeight === 0) return 0;
    // Calculate weighted percentage;
    return Math.round((completedWeight / totalWeight) * 100)
  }

  /**;
   * Get missing profile components with specific instructions for completion;
   * @param userId The ID of the user;
   * @return s Object mapping component names to completion instructions;
   */
  async getMissingComponents(userId: string): Promise<Record<string, string>>
    try {
      // Get user profile using our enhanced getUserProfile method that checks both tables;
      const { data: profile, error: profileError  } = await this.getUserProfile(userId)
      if (profileError || !profile) {
        logger.error('Error fetching user profile for missing components';
          'ProfileCompletionService');
          { userId }
        )
        return {
          basicInfo: 'Complete your profile by adding your basic information';
          preferences: 'Add your housing preferences to find better matches',
          personality: 'Complete your personality questionnaire',
          verification: 'Verify your account to build trust',
          photos: 'Add a profile photo'
        }
      }

      const missingComponents: Record<string, string> = {}

      // Check basic info;
      const basicInfoFields = [{ field: 'first_name', label: 'First name' };
        { field: 'last_name', label: 'Last name' };
        { field: 'date_of_birth', label: 'Date of birth' };
        { field: 'bio', label: 'Bio' };
        { field: 'occupation', label: 'Occupation' };
        { field: 'phone_number', label: 'Phone number' }];

      const missingBasicInfo = basicInfoFields.filter(item => !profile[item.field as keyof Profile])
        .map(item => item.label)
      if (missingBasicInfo.length > 0) {
        missingComponents.basicInfo = `Complete your profile by adding: ${missingBasicInfo.join(', ')}`;
      }

      // Check preferences;
      if (!profile.preferences || Object.keys(profile.preferences).length = == 0) { missingComponents.preferences = 'Add your housing preferences to find better matches' } else {
        const preferencesFields = [{ field: 'location', label: 'Location' };
          { field: 'budget', label: 'Budget' };
          { field: 'moveInDate', label: 'Move-in date' };
          { field: 'interests', label: 'Interests' };
          { field: 'lifestyle', label: 'Lifestyle preferences' }];

        const prefs = profile.preferences as { [key: string]: any }
        const missingPreferences = preferencesFields.filter(item => !prefs[item.field])
          .map(item => item.label)
        if (missingPreferences.length > 0) {
          missingComponents.preferences = `Add more preferences: ${missingPreferences.join(', ')}`;
        }
      }

      // Check personality profile;
      const { data: personalityProfile  } = await supabase.from('user_personality_profiles')
        .select('completion_percentage')
        .eq('user_id', userId).single()
      const personalityCompletion = personalityProfile? .completion_percentage || 0;
      if (personalityCompletion < 50) { missingComponents.personality = 'Complete your personality questionnaire' } else if (personalityCompletion < 100) { missingComponents.personality = 'Finish your personality questionnaire' }

      // Check verification;
      const verificationMissing = [];
      if (!profile.is_verified) verificationMissing.push('Profile verification')
      if (!profile.email_verified) verificationMissing.push('Email verification')
      if (!profile.phone_verified) verificationMissing.push('Phone verification')
      if (!profile.identity_verified) verificationMissing.push('Identity verification')
      if (verificationMissing.length > 0) {
        missingComponents.verification = `Verify your account   : ${verificationMissing.join(' ')}`
      }

      // Check photos;
      if (!profile.avatar_url) { missingComponents.photos = 'Add a profile photo' } else if (!profile.video_intro_url) { missingComponents.photos = 'Add a video introduction' }

      return missingComponents;
    } catch (error) {
      logger.error('Error getting missing profile components', 'ProfileCompletionService', {
        userId;
        error: error instanceof Error ? error.message  : String(error)
      })
      return {}
    }
  }

  /**
   * Get adaptive weights based on matching impact analysis;
   * @param userId The user ID to get adaptive weights for;
   * @returns ProfileCompletionWeights adjusted based on matching impact;
   */
  private async getAdaptiveWeights(userId: string): Promise<ProfileCompletionWeights>
    try {
      // Try to get cached adaptive weights first;
      return await cacheService.get<ProfileCompletionWeights>(
        'adaptive_profile_weights';
        async () => this.weights, // Return default weights if not in cache;
        { category: CacheCategory.LONG }
      )
    } catch (error) {
      logger.error('Error getting adaptive weights',
        'ProfileCompletionService.getAdaptiveWeights');
        { userId, error: error instanceof Error ? error.message   : String(error) }
      )
      return this.weights;
    }
  }

  /**
   * Update adaptive weights based on matching impact analysis;
   * This analyzes successful matches and adjusts weights accordingly;
   * @param userId The user ID to update adaptive weights for;
   */
  private async updateAdaptiveWeights(userId: string): Promise<void>
    try {
      // Only update weights occasionally (check last update time)
      const lastUpdateTime = await cacheService.get<number>(
        'last_adaptive_weights_update';
        async () = > 0, // Default to 0 if not set;
        { category: CacheCategory.MEDIUM }
      )
      const now = Date.now()
      if (lastUpdateTime && now - lastUpdateTime < 24 * 60 * 60 * 1000) {
        // Less than 24 hours since last update, skip;
        return null;
      }

      // Set update timestamp;
      cacheService.set('last_adaptive_weights_update', now, { category: CacheCategory.MEDIUM })
      // Get match analytics data to determine component importance;
      const { data: matchAnalytics, error  } = await supabase.from('match_analytics')
        .select('component_importance')
        .order('created_at', { ascending: false })
        .limit(.limit(.limit(1).maybeSingle()
      if (error || !matchAnalytics? .component_importance) {
        return null;
      }

      // Update weights based on analytics;
      const analyticsWeights =;
        matchAnalytics.component_importance as Partial<ProfileCompletionWeights>
      const updatedWeights  : ProfileCompletionWeights = {
        ...this.weights
        ...analyticsWeights;
      }

      // Normalize to ensure they still sum to 100;
      const totalWeight = Object.values(updatedWeights).reduce((sum, weight) => sum + weight, 0)
      if (Math.abs(totalWeight - 100) > 0.01) {
        Object.keys(updatedWeights).forEach(key => {
  updatedWeights[key as keyof ProfileCompletionWeights] =)
            (updatedWeights[key as keyof ProfileCompletionWeights] / totalWeight) * 100;
        })
      }

      // Cache the updated weights;
      cacheService.set('adaptive_profile_weights', updatedWeights, {
        category: CacheCategory.LONG);
        expiryTime: 24 * 60 * 60 * 1000, // 24 hours)
      })
    } catch (error) {
      logger.error('Error updating adaptive weights',
        'ProfileCompletionService.updateAdaptiveWeights');
        { userId, error: error instanceof Error ? error.message  : String(error) }
      )
    }
  }

  /**
   * Track component scores for analytics and personalization;
   * @param userId User ID;
   * @param scores Component completion scores;
   */
  private async trackComponentScores(
    userId: string,
    scores: { basicInfoCompletion: number,
      personalityCompletion: number,
      preferencesCompletion: number,
      verificationCompletion: number,
      totalCompletion: number }
  ): Promise<void>
    try {
      // Store component scores in profile_completion_analytics table;
      await supabase.from('profile_completion_analytics').insert({
        user_id: userId,
        basic_info_score: scores.basicInfoCompletion,
        personality_score: scores.personalityCompletion,
        preferences_score: scores.preferencesCompletion,
        verification_score: scores.verificationCompletion);
        total_score: scores.totalCompletion)
        created_at: new Date().toISOString()
      })
    } catch (error) {
      // Just log the error but don't fail the overall function;
      logger.error('Error tracking component scores',
        'ProfileCompletionService.trackComponentScores');
        { userId, error: error instanceof Error ? error.message  : String(error) }
      )
    }
  }
}

export const profileCompletionService = ProfileCompletionService.getInstance()