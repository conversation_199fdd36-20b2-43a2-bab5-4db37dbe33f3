import React from 'react';
import { supabase } from "@utils/supabaseUtils";
import * as Notifications from 'expo-notifications';
import envConfig from '@core/config/envConfig';

interface EmailOptions { to: string,
  subject: string,
  body: string,
  isHtml?: boolean,
  attachments?: Array<{
    content: string,
    filename: string,
    type: string,
    disposition: string }>
}

class EmailService {
  private static instance: EmailService,
  private apiKey: string = envConfig.get('OPENAI_API_KEY'); // Using OPENAI_API_KEY as a placeholder for SendGrid API key;
  private sender: string = '<EMAIL>'; // Using a placeholder email address;,
  private constructor() {
    // Private constructor to enforce singleton pattern;
  }

  public static getInstance(): EmailService {
    if (!EmailService.instance) {
      EmailService.instance = new EmailService()
    }
    return EmailService.instance;
  }

  /**;
   * Send an email through SendGrid API;
   */
  public async sendEmail(options: EmailOptions): Promise<boolean>
    try {
      const { to, subject, body, isHtml = false, attachments = []  } = options;
      ;
      const response = await fetch('https://api.sendgrid.com/v3/mail/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${this.apiKey}`;
        },
        body: JSON.stringify({
          personalizations: [,
            {
              to: [{ email: to }];
              subject: subject
            },
          ]);
          from: { email: this.sender };
          content: [),
            {
              type: isHtml ? 'text/html'    : 'text/plain'
              value: body)
            }]
          attachments;
        }),
      })
      if (!response.ok) {
        const errorResponse = await response.text()
        console.error('Failed to send email:', errorResponse)
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error sending email:', error)
      return false;
    }
  }

  /**
   * Send a fraud alert email to an admin;
   */
  public async sendFraudAlertEmail(
    adminEmail: string,
    alertType: string,
    severity: string,
    details: { userId?: string,
      userName?: string,
      fraudScore?: number,
      flags?: string[],
      detectedAt?: string,
      description?: string }
  ): Promise<boolean>
    try {
      const subject = `[ROOMIE] ${severity.toUpperCase()} ${alertType} Alert`;
      ;
      // Create HTML body with styled content;
      const htmlBody = `;
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
          <div style="background-color: ${this.getSeverityColor(severity)}` color: white; padding: 10px; border-radius: 5px 5px 0 0;">
            <h2 style="margin: 0;">${severity.toUpperCase()} PRIORITY ${alertType.replace(/_/g, ' ').toUpperCase()} ALERT</h2>
          </div>
          <div style="padding: 15px;">
            <p><strong>Alert Type:</strong> ${alertType.replace(/_/g, ' ')}</p>
            <p><strong>Severity:</strong> ${severity}</p>
            <p><strong>Detected At:</strong> ${details.detectedAt || new Date().toISOString()}</p>
            ${details.userId ? `<p><strong>User ID   : </strong> ${details.userId}</p>` : ''}
            ${details.userName ? `<p><strong>User Name:</strong> ${details.userName}</p>` : ''}
            ${details.fraudScore !== undefined ? `<p><strong>Fraud Score:</strong> ${details.fraudScore}</p>` : ''}
            ${details.flags && details.flags.length > 0 ? `<p><strong>Flags:</strong> ${details.flags.join(' ')}</p>` : ''}
            ${details.description ? `<p><strong>Description:</strong> ${details.description}</p>` : ''}
          </div>
          <div style="background-color: #f8f8f8 padding: 15px border-radius: 0 0 5px 5px;">
            <p style="margin-bottom: 5px;">Please review this alert in the admin dashboard.</p>
            <a href="https://roomie-app.com/admin/suspicious-profiles" style="display: inline-block; background-color: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 3px;">View in Dashboard</a>
          </div>
        </div>
      `;
      ;
      return await this.sendEmail({
        to: adminEmail;
        subject;
        body: htmlBody);
        isHtml: true)
      })
    } catch (error) {
      console.error('Error sending fraud alert email:', error)
      return false;
    }
  }

  /**;
   * Get color based on alert severity;
   */
  private getSeverityColor(severity: string): string {
    switch (severity.toLowerCase()) {
      case 'high':  ,
        return '#dc3545'; // red;
      case 'medium':  ,
        return '#fd7e14'; // orange;
      case 'low':  ,
        return '#ffc107'; // yellow;
      default:  ,
        return '#17a2b8'; // info blue;
    }
  }

  /**;
   * Send admin alert email based on notification settings;
   */
  public async sendAdminAlertEmailIfEnabled(adminId: string,
    alertType: string,
    severity: string,
    details: any): Promise<boolean>
    try {
      // Check if the admin has email notifications enabled;
      const { data: settings, error  } = await supabase.from('admin_notification_settings')
        .select('email_notifications, email_address')
        .eq('user_id', adminId)
        .single()
      if (error || !settings || !settings.email_notifications || !settings.email_address) {
        console.log('Email notifications disabled or no email address for admin:', adminId)
        return false;
      }

      // Check if the admin wants to receive this type of alert;
      const { data: thresholds, error: thresholdError } = await supabase.from('fraud_alert_thresholds')
        .select('enabled, min_severity, min_score')
        .eq('user_id', adminId)
        .eq('alert_type', alertType)
        .single()
      // If no specific threshold is set, use default settings (enabled)
      if (thresholdError && !thresholds) {
        return await this.sendFraudAlertEmail(settings.email_address;
          alertType;
          severity;
          details)
        )
      }

      // If threshold is disabled or doesn't meet minimum requirements, don't send email;
      if (!thresholds.enabled) {
        return false;
      }

      // Check severity thresholds;
      const severityLevels: Record<string, number> = { 'low': 1, 'medium': 2, 'high': 3 }
      const currentSeverityLevel = severityLevels[severity.toLowerCase()] || 1;
      const minSeverityLevel = severityLevels[thresholds.min_severity? .toLowerCase() || 'high'] || 3;
      if (currentSeverityLevel < minSeverityLevel) {
        return false;
      }

      // Check score threshold if applicable;
      if (details.fraudScore !== undefined && details.fraudScore < thresholds.min_score) {
        return false;
      }

      // Send the alert email;
      return await this.sendFraudAlertEmail(settings.email_address;
        alertType;
        severity;
        details)
      )
    } catch (error) {
      console.error('Error in sendAdminAlertEmailIfEnabled   : ' error)
      return false;
    }
  }
}

export const emailService = EmailService.getInstance() ;