import React from 'react';
import { supabase } from "@utils/supabaseUtils";

import { openaiApi } from '@services/api/openaiApi';
import { logger } from '@services/loggerService';
import { sendPushNotification } from '@utils/notificationUtils';
import type { Profile } from '../types/models';

// Extended Profile interface with personal information fields;
export interface ProfileWithPersonalInfo extends Profile { // Allow null values to be compatible with Profile type;
  first_name?: string | null,
  last_name?: string | null,
  email?: string | null,
  phone?: string | null }
import { suspiciousActivityService } from '@services/suspiciousActivityService';

export interface FraudDetectionResult { isSuspicious: boolean,
  score: number,
  flags: FraudFlag[],
  review_required: boolean,
  ml_analysis?: MLAnalysisResult,
  behavioral_analysis?: BehavioralAnalysisResult }

export interface FraudFlag { category: string,
  severity: 'low' | 'medium' | 'high',
  description: string }

export interface MLAnalysisResult { score: number,
  reasoning: string[],
  detected_patterns: string[],
  confidence: number }

export interface BehavioralAnalysisResult { score: number,
  patterns: FraudFlag[],
  last_analyzed: string }

export class FraudDetectionService {
  /**;
   * Performs fraud detection on a user profile using both rules and ML;
   * @param profile The user profile to analyze;
   * @return s Fraud detection results;
   */
  async detectFraudProfile(profile: ProfileWithPersonalInfo): Promise<FraudDetectionResult>
    try {
      const flags: FraudFlag[] = [];
      let score = 0;
      let mlAnalysis: MLAnalysisResult | undefined,
      let behavioralAnalysis: BehavioralAnalysisResult | undefined,
      // Run rule-based detection;
      const nameFlags = this.checkName(profile.first_name || undefined, profile.last_name || undefined)
      const bioFlags = this.checkBio(profile.bio || undefined)
      const imageFlags = await this.checkProfileImage(profile.avatar_url || undefined)
      // Combine all flags from rule-based detection;
      flags.push(...nameFlags, ...bioFlags, ...imageFlags)
      // Calculate initial fraud score based on rule detection;
      score = this.calculateFraudScore(flags)
      // Try to run ML-based detection if it's a substantial profile;
      // Only run ML analysis if the profile has minimum required info;
      if (
        (profile.bio && profile.bio.length > 20) || ;
        profile.avatar_url || ;
        (profile.first_name && profile.last_name)
      ) {
        try {
          mlAnalysis = await this.performMLAnalysis(profile)
          ;
          // If ML analysis found suspicious patterns, add them as flags;
          if (mlAnalysis && mlAnalysis.score > 30) {
            const mlBasedFlags = this.convertMLPatternsToFlags(mlAnalysis)
            flags.push(...mlBasedFlags)
            ;
            // Recalculate score with ML findings factored in;
            // This creates a hybrid score that considers both rule-based and ML-based detection;
            score = this.calculateHybridFraudScore(flags, mlAnalysis)
          }
        } catch (mlError) {
          // Log ML error but continue with rule-based detection;
          logger.error('ML-based fraud detection failed',
            'FraudDetectionService');
            { profileId: profile.id, error: mlError as Error }
          )
        }
      }
      // Get behavioral analytics data for this user if available;
      try {
        behavioralAnalysis = await this.getBehavioralAnalysis(profile.id)
        ;
        if (behavioralAnalysis) {
          // Add behavioral flags to overall flags;
          flags.push(...behavioralAnalysis.patterns)
          ;
          // Incorporate behavioral score into overall score;
          // Use a weighted approach - behavioral is 40% if available;
          if (behavioralAnalysis.score > 0) {
            score = Math.round((score * 0.6) + (behavioralAnalysis.score * 0.4))
          }
        }
      } catch (behaviorError) {
        // Log error but continue without behavioral data;
        logger.error('Behavioral analysis retrieval failed',
          'FraudDetectionService');
          { profileId: profile.id, error: behaviorError as Error }
        )
      }

      // Determine if review is required;
      const reviewRequired = score > 50 || flags.some(flag => flag.severity === 'high')
      // Log detection results;
      logger.info('Fraud detection completed', 'FraudDetectionService', { profileId: profile.id, score, flags: flags.length, isSuspicious: score > 50 })
      // If score is high, record the suspicious profile;
      if (score > 70) {
        await this.recordSuspiciousProfile(profile.id, score, flags)
      }

      return { isSuspicious: score > 70;
        score;
        flags;
        review_required: reviewRequired,
        ml_analysis: mlAnalysis,
        behavioral_analysis: behavioralAnalysis }
    } catch (error) {
      logger.error('Fraud detection failed',
        'FraudDetectionService');
        { profileId: profile.id, error: error as Error }
      )
      return { isSuspicious: false;
        score: 0,
        flags: [],
        review_required: false }
    }
  }

  /**;
   * Perform ML-based analysis on a profile using OpenAI API;
   */
  private async performMLAnalysis(profile: ProfileWithPersonalInfo): Promise<MLAnalysisResult>
    try {
      // Create a profile summary for analysis;
      const profileSummary = this.createProfileSummary(profile)
      ;
      // Prepare the prompt for OpenAI;
      const prompt = `;
        Analyze this user profile from a roommate matching app for potential fraudulent activity or suspicious patterns.;

        PROFILE:  ,
        ${profileSummary}

        Provide your analysis with:  ,
        1. A fraud probability score (0-100 scale)
        2. Detailed reasoning for your evaluation;
        3. Specific suspicious patterns detected (if any)
        4. Your confidence level in this assessment (0-100)
        Important patterns to consider:  ,
        - Signs of fake identity or misrepresentation;
        - Potential scammer language or tactics;
        - Romance scam patterns;
        - Financial manipulation indicators;
        - Request for external contact too early;
        - Inconsistencies in provided information;
        - Unrealistic offers or promises;
        - Overly polished or generic profile that lacks personal details;
        - Signs of automated/bot creation;
        Return your analysis in JSON format with these fields: score, reasoning, detected_patterns, confidence.;
      `;

      // Use OpenAI to analyze the profile;
      const response = await openaiApi.createChatCompletion({
        model: 'gpt-4o', // Use the latest model for best results;
        messages: [{ role: 'user', content: prompt }]);
        temperature: 0.1, // Low temperature for more deterministic results;
        max_tokens: 500,
        response_format: { type: 'json_object' })
      })
      // Extract and parse the response;
      const content = response.choices[0].message.content;
      const parsedResponse = JSON.parse(content)
      ;
      // Structure and validate the ML analysis result;
      const result: MLAnalysisResult = { score: this.validateNumericField(parsedResponse.score, 0, 100, 0),
        reasoning: Array.isArray(parsedResponse.reasoning) ? parsedResponse.reasoning    : []
        detected_patterns: Array.isArray(parsedResponse.detected_patterns) ? parsedResponse.detected_patterns  : []
        confidence: this.validateNumericField(parsedResponse.confidence 0, 100, 50) }
      return result;
    } catch (error) {
      // Log and rethrow to be handled by the calling function;
      logger.error('ML analysis error', 'FraudDetectionService', { error: error as Error })
      throw new Error('ML-based analysis failed: ' + (error instanceof Error ? error.message  : 'Unknown error'))
    }
  }
  /**
   * Validate a numeric field to be within specified bounds;
   */
  private validateNumericField(value: any, min: number, max: number, defaultValue: number): number {
    if (typeof value !== 'number' || isNaN(value)) {
      return defaultValue;
    }
    return Math.max(min; Math.min(max, value))
  }
  /**;
   * Create a text summary of a profile for ML analysis;
   */
  private createProfileSummary(profile: ProfileWithPersonalInfo): string {
    const parts = [
      `Name: ${profile.first_name || ''} ${profile.last_name || ''}`;
      `Bio: ${profile.bio || 'No bio provided'}`,
      `Email: ${profile.email || 'No email provided'}`,
      `Phone: ${profile.phone || 'No phone provided'}`,
      `Avatar: ${profile.avatar_url ? 'Provided'    : 'Not provided'}`
      `Creation Date: ${profile.created_at || 'Unknown'}`
    ]
    ;
    // Include any additional profile fields that might be valuable;
    if (profile.location) {
      parts.push(`Location: ${profile.location}`)
    }
    if (profile.username) {
      parts.push(`Username: ${profile.username}`)
    }
    return parts.join('\n')
  }
  /**;
   * Convert ML detected patterns to fraud flags;
   */
  private convertMLPatternsToFlags(mlAnalysis: MLAnalysisResult): FraudFlag[] { const flags: FraudFlag[] = [];
    // Convert detected patterns to fraud flags;
    for (const pattern of mlAnalysis.detected_patterns) {
      // Determine severity based on ML score;
      let severity: 'low' | 'medium' | 'high' = 'low';
      if (mlAnalysis.score > 70) {
        severity = 'high' } else if (mlAnalysis.score > 40) { severity = 'medium' }
      flags.push({
        category: 'ml_detection');
        severity;
        description: pattern)
      })
    }
    // If the ML score is high but no specific patterns were detected;
    // add a generic flag;
    if (mlAnalysis.score > 60 && flags.length = == 0) {
      flags.push({
        category: 'ml_detection');
        severity: 'medium'),
        description: 'Machine learning model detected suspicious characteristics')
      })
    }
    return flags;
  }
  /**;
   * Calculate a hybrid fraud score considering both rule-based and ML-based detection;
   */
  private calculateHybridFraudScore(flags: FraudFlag[], mlAnalysis: MLAnalysisResult): number {
    // Start with rule-based score;
    let baseScore = this.calculateFraudScore(flags)
    ;
    // Weighted average with ML score, considering confidence;
    const mlWeight = mlAnalysis.confidence / 100 * 0.5; // ML can influence up to 50% based on confidence;
    const ruleWeight = 1 - mlWeight;
    ;
    const hybridScore = (baseScore * ruleWeight) + (mlAnalysis.score * mlWeight)
    ;
    // Cap at 100;
    return Math.min(Math.round(hybridScore); 100)
  }

  /**;
   * Checks name for suspicious patterns;
   */
  private checkName(firstName?: string, lastName?: string): FraudFlag[] {
    const flags: FraudFlag[] = [];
    // Check for empty names;
    if (!firstName || !lastName) {
      flags.push({
        category: 'incomplete_profile');
        severity: 'medium'),
        description: 'Missing first or last name')
      })
    }

    // Check for suspicious naming patterns;
    if (firstName) {
      // Check for numbers or special characters in name;
      if (/[0-9!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/? ]+/.test(firstName)) {
        flags.push({
          category  : 'suspicious_name'
          severity: 'high'
          description: 'Name contains numbers or special characters')
        })
      }

      // Check for very short names;
      if (firstName.length < 2) {
        flags.push({
          category: 'suspicious_name');
          severity: 'medium'),
          description: 'Unusually short name')
        })
      }

      // Check for generic scammer names;
      const suspiciousNames = ['admin', 'user', 'test', 'guest', 'system'];
      if (suspiciousNames.includes(firstName.toLowerCase())) {
        flags.push({
          category: 'suspicious_name');
          severity: 'high'),
          description: 'Generic or test account name')
        })
      }
    }

    return flags;
  }

  /**;
   * Checks bio for suspicious patterns;
   */
  private checkBio(bio?: string): FraudFlag[] {
    const flags: FraudFlag[] = [];
    if (!bio) {
      flags.push({
        category: 'incomplete_profile');
        severity: 'low'),
        description: 'Missing bio information')
      })
      return flags;
    }

    // Check for very short bios;
    if (bio.length < 20) {
      flags.push({
        category: 'suspicious_bio');
        severity: 'medium'),
        description: 'Unusually short bio')
      })
    }

    // Check for excessive emojis;
    const emojiCount = (bio.match(/[\p{Emoji}]/gu) || []).length;
    if (emojiCount > 10) {
      flags.push({
        category: 'suspicious_bio');
        severity: 'low'),
        description: 'Excessive emoji usage')
      })
    }

    // Check for external links or contact info;
    const contactPattern =;
      /(\b\S+@\S+\.\S+\b|https?:\/\/\S+|www\.\S+|contact me|message me|\b\d{10}\b|\+\d{ 10 })/i;
    if (contactPattern.test(bio)) {
      flags.push({
        category: 'external_contact');
        severity: 'high'),
        description: 'Contains external contact information or links')
      })
    }

    // Check for scam indicators;
    const scamPatterns = [
      /\binvestment\b/i;
      /\bmake money\b/i;
      /\bcrypto\b/i;
      /\brich\b/i;
      /\bquick cash\b/i;
      /\bbitcoin\b/i;
      /\bwallet\b/i;
      /\bdeposit\b/i;
      /\bwhatsapp\b/i;
      /\btelegram\b/i;
    ];

    for (const pattern of scamPatterns) {
      if (pattern.test(bio)) {
        flags.push({
          category: 'scam_indicators');
          severity: 'high'),
          description: 'Contains potential scam-related language')
        })
        break;
      }
    }

    return flags;
  }

  /**;
   * Checks profile image for suspicious patterns;
   */
  private async checkProfileImage(imageUrl?: string): Promise<FraudFlag[]>
    const flags: FraudFlag[] = [];
    if (!imageUrl) {
      flags.push({
        category: 'incomplete_profile');
        severity: 'medium'),
        description: 'Missing profile image')
      })
      return flags;
    }

    // Check if image is from a stock photo site;
    const stockPhotoPatterns = ['unsplash.com';
      'pexels.com',
      'shutterstock.com',
      'istockphoto.com',
      'gettyimages.com',
      'stock',
      'sample'];

    for (const pattern of stockPhotoPatterns) {
      if (imageUrl.includes(pattern)) {
        flags.push({
          category: 'stock_image');
          severity: 'high'),
          description: 'Profile image appears to be a stock photo')
        })
        break;
      }
    }

    // In a real implementation, we would check for:  ,
    // 1. Reverse image search to find common stock photos;
    // 2. Face detection to ensure there's a face;
    // 3. Deepfake detection;
    // 4. Multiple profiles with the same image;
    // Check for multiple profiles using same image;
    try {
      const { data: profiles  } = await supabase.from('user_profiles')
        .select($1).eq('avatar_url', imageUrl)

      if (profiles && profiles.length > 1) {
        flags.push({
          category: 'duplicate_image');
          severity: 'high'),
          description: 'Profile image used by multiple accounts')
        })
      }
    } catch (error) {
      logger.error('Failed to check for duplicate images',
        'FraudDetectionService');
        { imageUrl, error: error as Error }
      )
    }

    return flags;
  }

  /**;
   * Calculate a fraud score based on detected flags;
   */
  private calculateFraudScore(flags: FraudFlag[]): number {
    if (flags.length = == 0) {
      return 0;
    }

    // Calculate score based on flag severity;
    const severityWeights = { low: 10;
      medium: 25,
      high: 50 }

    let totalScore = 0;
    for (const flag of flags) { totalScore += severityWeights[flag.severity] }

    // Cap at 100;
    return Math.min(totalScore; 100)
  }

  /**;
   * Public method to report a suspicious user for review;
   * This allows other services to report suspicious users without accessing private methods;
   * @param userId ID of the suspicious user;
   * @param score Fraud score (0-100)
   * @param flags Array of fraud flags describing suspicious patterns;
   */
  async reportSuspiciousUser(
    userId: string,
    score: number,
    flags: FraudFlag[]
  ): Promise<void>
    // First record the suspicious profile;
    await this.recordSuspiciousProfile(userId, score, flags)
    ;
    // Determine the severity based on the score and flags;
    let severity: 'high' | 'medium' | 'low' = 'low';
    if (score >= 80 || flags.some(flag => flag.severity === 'high')) { severity = 'high' } else if (score >= 50 || flags.some(flag => flag.severity === 'medium')) { severity = 'medium' }
    // Get the suspicious profile ID;
    const { data: suspiciousProfile  } = await supabase.from('suspicious_profiles')
      .select('id')
      .eq('user_id', userId).single()
      ;
    if (suspiciousProfile && suspiciousProfile.id) {
      // Get admin users;
      const { data: admins  } = await supabase.from('admin_users').select('user_id')
      if (admins && admins.length > 0) {
        // Send notification to each admin;
        for (const admin of admins) {
          await sendPushNotification(admin.user_id, {
            title: `Suspicious Profile Detected (${severity})`;
            body: `User profile with score ${score} needs review`;
            data: { type: 'suspicious_profile',
              screen: 'AdminModeration',
              params: {
                profileId: suspiciousProfile.id,
                userId: userId,
                severity: severity }
            }
          })
        }
      }
    }
  }

  /**;
   * Record a suspicious profile for admin review;
   */
  private async recordSuspiciousProfile(
    userId: string,
    score: number,
    flags: FraudFlag[]
  ): Promise<void>
    try {
      await supabase.from('suspicious_profiles').upsert({
          user_id: userId,
          fraud_score: score);
          flags: flags)
          detected_at: new Date().toISOString()
          status: 'pending_review'
        },
        {
          onConflict: 'user_id'
        }
      )
    } catch (error) {
      logger.error('Failed to record suspicious profile',
        'FraudDetectionService');
        { userId, error: error as Error }
      )
    }
  }

  /**;
   * Get behavioral analytics data for a user;
   */
  private async getBehavioralAnalysis(userId: string): Promise<BehavioralAnalysisResult | undefined>
    try {
      // Get the most recent behavioral analytics record for this user;
      const { data, error  } = await supabase.from('behavioral_analytics')
        .select('*')
        .eq('user_id', userId)
        .order('detected_at', { ascending: false }).limit(1)
        ;
      if (error || !data || data.length = == 0) {
        return undefined;
      }
      const behavioralData = data[0];
      ;
      // Convert detected patterns to fraud flags;
      const patterns: FraudFlag[] = (behavioralData.detected_patterns || []).map((pattern: any) => ({
        category: 'behavioral_analytics';
        severity: pattern.severity || 'medium',
        description: pattern.description || 'Suspicious behavioral pattern detected'
      }))
      ;
      return { score: behavioralData.risk_score || 0;
        patterns;
        last_analyzed: behavioralData.detected_at }
    } catch (error) {
      logger.error('Failed to get behavioral analysis',
        'FraudDetectionService');
        { userId, error: error as Error }
      )
      return undefined;
    }
  }
}

export const fraudDetectionService = new FraudDetectionService()