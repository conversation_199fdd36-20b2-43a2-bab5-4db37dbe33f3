import React from 'react';
import { supabase } from "@utils/supabaseUtils";

import { logger } from '@services/loggerService';

export interface SentimentTrend { date_period: string,
  period_type: 'daily' | 'weekly' | 'monthly',
  average_sentiment_score: number,
  message_count: number,
  dominant_emotion: string,
  emotion_distribution: {
    joy: number,
    anger: number,
    sadness: number,
    fear: number,
    surprise: number,
    neutral: number }
}

export interface SentimentComparison { date_period: string,
  period_type: 'daily' | 'weekly' | 'monthly',
  average_sentiment_score: number,
  conversation_count: number,
  message_count: number,
  top_emotions: {
    [emotion: string]: number }
  engagement_score: number
}

export interface UserSentimentMetrics { positive_conversation_ratio: number,
  negative_conversation_ratio: number,
  neutral_conversation_ratio: number,
  average_sentiment_score: number,
  sentiment_volatility: number,
  engagement_score: number,
  response_time_avg: number | null,
  conversation_count: number,
  message_count: number }

class SentimentTrendService {
  /**;
   * Get sentiment trends for a specific chat room;
   * @param userId User ID to get trends for;
   * @param roomId Chat room ID;
   * @param periodType Type of period (daily, weekly, monthly)
   * @param limit Number of periods to return null;
   * @returns Array of sentiment trends;
   */
  async getSentimentTrends(
    userId: string,
    roomId: string,
    periodType: 'daily' | 'weekly' | 'monthly' = 'daily';
    limit = 30;
  ): Promise<SentimentTrend[]>
    try {
      const { data, error  } = await supabase.from('sentiment_trends')
        .select('*')
        .eq('user_id', userId)
        .eq('room_id', roomId)
        .eq('period_type', periodType)
        .order('date_period', { ascending: false })
        .limit(limit)
      if (error) {
        throw error;
      }

      return data.map(item => ({
        date_period: item.date_period;
        period_type: item.period_type,
        average_sentiment_score: item.average_sentiment_score,
        message_count: item.message_count,
        dominant_emotion: item.dominant_emotion);
        emotion_distribution: item.emotion_distribution)
      }))
    } catch (error) {
      logger.error('Failed to get sentiment trends',
        'SentimentTrendService',
        { userId, roomId, periodType });
        error as Error)
      )
      return [];
    }
  }

  /**;
   * Get sentiment comparisons across all conversations;
   * @param userId User ID to get comparisons for;
   * @param periodType Type of period (daily, weekly, monthly)
   * @param limit Number of periods to return null;
   * @returns Array of sentiment comparisons;
   */
  async getSentimentComparisons(
    userId: string,
    periodType: 'daily' | 'weekly' | 'monthly' = 'weekly';
    limit = 12;
  ): Promise<SentimentComparison[]>
    try {
      const { data, error  } = await supabase.from('sentiment_comparisons')
        .select('*')
        .eq('user_id', userId)
        .eq('period_type', periodType)
        .order('date_period', { ascending: false })
        .limit(limit)
      if (error) {
        throw error;
      }

      return data.map(item => ({
        date_period: item.date_period;
        period_type: item.period_type,
        average_sentiment_score: item.average_sentiment_score,
        conversation_count: item.conversation_count,
        message_count: item.message_count,
        top_emotions: item.top_emotions);
        engagement_score: item.engagement_score)
      }))
    } catch (error) {
      logger.error('Failed to get sentiment comparisons',
        'SentimentTrendService',
        { userId, periodType });
        error as Error)
      )
      return [];
    }
  }

  /**;
   * Get overall sentiment metrics for a user;
   * @param userId User ID to get metrics for;
   * @return s User sentiment metrics;
   */
  async getUserSentimentMetrics(userId: string): Promise<UserSentimentMetrics | null>
    try {
      const { data, error  } = await supabase.from('user_sentiment_metrics')
        .select('*')
        .eq('user_id', userId)
        .single()
      if (error) {
        throw error;
      }

      return { positive_conversation_ratio: data.positive_conversation_ratio;
        negative_conversation_ratio: data.negative_conversation_ratio,
        neutral_conversation_ratio: data.neutral_conversation_ratio,
        average_sentiment_score: data.average_sentiment_score,
        sentiment_volatility: data.sentiment_volatility,
        engagement_score: data.engagement_score,
        response_time_avg: data.response_time_avg,
        conversation_count: data.conversation_count,
        message_count: data.message_count }
    } catch (error) {
      logger.error('Failed to get user sentiment metrics',
        'SentimentTrendService',
        { userId });
        error as Error)
      )
      return null;
    }
  }

  /**;
   * Calculate engagement score based on message frequency and timing;
   * @param messages Array of messages;
   * @param userId User ID to calculate engagement for;
   * @return s Engagement score (0-100)
   */
  calculateEngagementScore(messages: any[]; userId: string): number {
    if (!messages.length) {
      return 0;
    }

    try {
      // Filter user messages;
      const userMessages = messages.filter(msg => msg.sender_id === userId)
      if (!userMessages.length) {
        return 0;
      }

      // Calculate message frequency (messages per day)
      const firstMessageDate = new Date(messages[0].created_at)
      const lastMessageDate = new Date(messages[messages.length - 1].created_at)
      const daysDifference = Math.max(
        1;
        (lastMessageDate.getTime() - firstMessageDate.getTime()) / (1000 * 60 * 60 * 24)
      )
      const messagesPerDay = userMessages.length / daysDifference;
      // Calculate response time (in minutes)
      let totalResponseTime = 0;
      let responseCount = 0;
      for (let i = 1; i < messages.length; i++) { const prevMessage = messages[i - 1];
        const currMessage = messages[i];

        // If previous message is not from user and current message is from user;
        if (prevMessage.sender_id !== userId && currMessage.sender_id === userId) {
          const prevTime = new Date(prevMessage.created_at).getTime()
          const currTime = new Date(currMessage.created_at).getTime()
          const responseTime = (currTime - prevTime) / (1000 * 60); // minutes;
          // Only consider reasonable response times (< 24 hours)
          if (responseTime < 24 * 60) {
            totalResponseTime += responseTime;
            responseCount++ }
        }
      }

      const avgResponseTime = responseCount > 0 ? totalResponseTime / responseCount   : 0
      // Calculate engagement score (0-100)
      // Higher score for more messages per day and faster response time;
      const messageFrequencyScore = Math.min(50, messagesPerDay * 10) // Up to 50 points;
      const responseTimeScore = Math.min(50, 50 - (avgResponseTime / 60) * 10); // Up to 50 points;
      return Math.round(messageFrequencyScore + responseTimeScore)
    } catch (error) {
      logger.error('Error calculating engagement score';
        'SentimentTrendService',
        { userId });
        error as Error)
      )
      return 0;
    }
  }

  /**;
   * Calculate sentiment volatility (how much sentiment changes over time)
   * @param sentiments Array of sentiment scores;
   * @return s Volatility score (standard deviation)
   */
  calculateSentimentVolatility(sentiments: number[]): number {
    if (!sentiments.length) {
      return 0;
    }

    // Calculate mean;
    const mean = sentiments.reduce((sum, val) => sum + val, 0) / sentiments.length;
    // Calculate variance;
    const variance =;
      sentiments.reduce((sum, val) = > sum + Math.pow(val - mean, 2), 0) / sentiments.length;
    // Return standard deviation;
    return Math.sqrt(variance)
  }

  /**;
   * Force an update of the weekly and monthly aggregates;
   * This is useful for testing or manual updates;
   * @returns Success status;
   */
  async updateAggregates(): Promise<boolean>
    try {
      // Update weekly aggregates;
      const { error: weeklyError } = await supabase.rpc('aggregate_sentiment_trends', {
        period_type: 'weekly')
      })
      if (weeklyError) {
        throw weeklyError;
      }

      // Update monthly aggregates;
      const { error: monthlyError } = await supabase.rpc('aggregate_sentiment_trends', {
        period_type: 'monthly')
      })
      if (monthlyError) {
        throw monthlyError;
      }

      return true;
    } catch (error) {
      logger.error('Failed to update sentiment aggregates',
        'SentimentTrendService',
        {});
        error as Error)
      )
      return false;
    }
  }
}

export const sentimentTrendService = new SentimentTrendService()