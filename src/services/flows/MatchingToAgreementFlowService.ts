import React from 'react';
/**;
 * MatchingToAgreementFlowService - Complete Reference Implementation;
 * ;
 * Orchestrates the entire flow from user matching to signed agreements:  ,
 * 1. User Discovery & Matching;
 * 2. Match Approval/Rejection;
 * 3. Chat Initiation;
 * 4. Agreement Proposal;
 * 5. Agreement Customization;
 * 6. Review & Approval;
 * 7. Digital Signature Collection;
 * 8. Agreement Activation;
 */

import { supabase } from "@utils/supabaseUtils";
import { logger } from '@utils/logger';
import { getCurrentUser } from '@utils/authUtils';
import { unifiedAgreementService } from '@services/unified/UnifiedAgreementService';
import { notificationService } from '@services/notificationService';
import { getErrorMessage } from '@utils/errorUtils';

// = ==== FLOW STATE TYPES =====;

export type FlowStage =  ;
  | 'discovery'           // Finding potential matches;
  | 'matching'           // Showing matches to user;
  | 'mutual_interest'    // Both users liked each other;
  | 'chat_initiated'     // Chat conversation started;
  | 'agreement_proposed' // Agreement proposal created;
  | 'agreement_customizing' // Customizing agreement terms;
  | 'agreement_reviewing'   // Reviewing final agreement;
  | 'agreement_approving'   // Waiting for approvals;
  | 'signature_collection'  // Collecting digital signatures;
  | 'agreement_active'      // Fully executed agreement;
  | 'flow_cancelled'        // Flow was cancelled;
  | 'flow_failed';          // Flow failed with error;
export interface FlowState { id: string,
  stage: FlowStage,
  user1_id: string,
  user2_id: string,
  match_id?: string,
  chat_room_id?: string,
  agreement_id?: string,
  progress_percentage: number,
  metadata: {
    compatibility_score?: number,
    match_timestamp?: string,
    chat_initiated_at?: string,
    agreement_proposed_at?: string,
    last_activity?: string,
    error_history?: Array<{
      stage: FlowStage,
      error: string,
      timestamp: string,
      resolved: boolean }>
  }
  created_at: string,
  updated_at: string
}

export interface MatchCandidate {
  user_id: string,
  compatibility_score: number,
  shared_interests: string[],
  personality_match: {
    overall_score: number,
    trait_scores: Record<string, number>
  }
  profile: { first_name: string,
    last_name: string,
    age: number,
    location: string,
    bio: string,
    avatar_url?: string,
    verification_status: string }
  preferences_match: { budget_compatible: boolean,
    location_compatible: boolean,
    lifestyle_compatible: boolean }
}

export interface FlowProgress { current_stage: FlowStage,
  completed_stages: FlowStage[],
  next_actions: Array<{
    action: string,
    description: string,
    required_by: 'user1' | 'user2' | 'both',
    deadline?: string }>
  estimated_completion: string,
  can_proceed: boolean,
  blocking_issues: string[]
}

// = ==== MAIN FLOW SERVICE =====;

export class MatchingToAgreementFlowService {
  private static instance: MatchingToAgreementFlowService,
  static getInstance(): MatchingToAgreementFlowService {
    if (!this.instance) {
      this.instance = new MatchingToAgreementFlowService()
    }
    return this.instance;
  }

  // ===== FLOW INITIATION =====;

  /**;
   * Start the matching flow for a user;
   */
  async startMatchingFlow(userId: string, preferences?: any): Promise<{ success: boolean,
    candidates: MatchCandidate[],
    flow_id?: string,
    error?: string }>
    try {
      logger.info('Starting matching flow', 'MatchingToAgreementFlow', { userId })
      // 1. Validate user authentication;
      const user = await getCurrentUser()
      if (!user || user.id !== userId) {
        throw new Error('User authentication required')
      }

      // 2. Get user profile and preferences;
      const userProfile = await this.getUserProfile(userId)
      if (!userProfile) {
        throw new Error('User profile not found')
      }

      // 3. Find compatible matches;
      const candidates = await this.findCompatibleMatches(userId, preferences)
      // 4. Create flow state record;
      const flowId = await this.createFlowState(userId, 'discovery', {
        candidates_found: candidates.length);
        search_preferences: preferences)
      })
      logger.info('Matching flow started successfully', 'MatchingToAgreementFlow', {
        userId;
        flowId;
        candidatesFound: candidates.length)
      })
      return { success: true;
        candidates;
        flow_id: flowId }

    } catch (error) {
      logger.error('Failed to start matching flow', 'MatchingToAgreementFlow', { userId }, error as Error)
      return {
        success: false;
        candidates: [],
        error: getErrorMessage(error)
      }
    }
  }

  /**;
   * Handle user action on a match (like/pass)
   */
  async handleMatchAction(userId: string,
    targetUserId: string,
    action: 'like' | 'pass',
    flowId?: string): Promise<{ success: boolean,
    is_mutual_match: boolean,
    chat_room_id?: string,
    flow_state?: FlowState,
    error?: string }>
    try {
      logger.info('Handling match action', 'MatchingToAgreementFlow', {
        userId;
        targetUserId;
        action)
      })
      // 1. Record the match action;
      const { data: matchRecord, error: matchError  } = await supabase.from('user_matches')
        .insert({
          user_id: userId);
          target_user_id: targetUserId)
          action;
          created_at: new Date().toISOString()
        })
        .select()
        .single()
      if (matchError) throw matchError;
      // 2. Check for mutual match;
      if (action === 'like') {
        const { data: mutualMatch } = await supabase.from('user_matches')
          .select('*')
          .eq('user_id', targetUserId)
          .eq('target_user_id', userId)
          .eq('action', 'like')
          .single()
        if (mutualMatch) {
          // 3. Create chat room for mutual match;
          const chatRoomId = await this.createChatRoom(userId, targetUserId)
          ;
          // 4. Update flow state;
          const flowState = await this.updateFlowState(flowId || `${userId}_${targetUserId}`);
            'mutual_interest',
            {
              match_id: matchRecord.id,
              chat_room_id: chatRoomId)
              mutual_match_at: new Date().toISOString()
            }
          )
          // 5. Send notifications;
          await this.notifyMutualMatch(userId, targetUserId, chatRoomId)
          return { success: true;
            is_mutual_match: true,
            chat_room_id: chatRoomId,
            flow_state: flowState }
        }
      }

      return { success: true;
        is_mutual_match: false }

    } catch (error) {
      logger.error('Failed to handle match action', 'MatchingToAgreementFlow', {
        userId;
        targetUserId;
        action)
      }, error as Error)
      return {
        success: false;
        is_mutual_match: false,
        error: getErrorMessage(error)
      }
    }
  }

  /**;
   * Initiate agreement proposal in chat;
   */
  async proposeAgreement(
    userId: string,
    chatRoomId: string,
    proposalData: { template_id: string,
      title: string,
      initial_terms?: any }
  ): Promise<{ success: boolean,
    agreement_id?: string,
    flow_state?: FlowState,
    error?: string }>
    try {
      logger.info('Proposing agreement', 'MatchingToAgreementFlow', {
        userId;
        chatRoomId;
        templateId: proposalData.template_id)
      })
      // 1. Validate chat room access;
      const chatRoom = await this.validateChatRoomAccess(userId, chatRoomId)
      if (!chatRoom) {
        throw new Error('Invalid chat room access')
      }

      // 2. Create agreement;
      const agreementResult = await unifiedAgreementService.createAgreement({ title: proposalData.title;
        template_id: proposalData.template_id,
        created_by: userId);
        status: 'draft'),
        metadata: {
          chat_room_id: chatRoomId)
          proposed_at: new Date().toISOString()
          initial_terms: proposalData.initial_terms }
      })
      if (!agreementResult.data) {
        throw new Error(agreementResult.error || 'Failed to create agreement')
      }

      const agreementId = agreementResult.data;
      // 3. Add participants;
      const otherUserId = chatRoom.user1_id === userId ? chatRoom.user2_id    : chatRoom.user1_id
      await unifiedAgreementService.addParticipantToAgreement(agreementId userId, 'creator')
      await unifiedAgreementService.addParticipantToAgreement(agreementId, otherUserId, 'roommate')
      // 4. Update flow state;
      const flowState = await this.updateFlowState(
        `${Math.min(userId, otherUserId)}_${Math.max(userId, otherUserId)}`,
        'agreement_proposed',
        {
          agreement_id: agreementId,
          proposed_by: userId,
          proposed_at: new Date().toISOString()
        }
      )
      // 5. Send notification to other user;
      await this.notifyAgreementProposed(userId, otherUserId, agreementId)
      // 6. Send chat message about proposal;
      await this.sendAgreementProposalMessage(chatRoomId, userId, agreementId, proposalData.title)
      logger.info('Agreement proposed successfully', 'MatchingToAgreementFlow', {
        userId;
        agreementId;
        chatRoomId)
      })
      return { success: true;
        agreement_id: agreementId,
        flow_state: flowState }

    } catch (error) {
      logger.error('Failed to propose agreement', 'MatchingToAgreementFlow', {
        userId;
        chatRoomId)
      }, error as Error)
      return {
        success: false;
        error: getErrorMessage(error)
      }
    }
  }

  /**
   * Get current flow progress for users;
   */
  async getFlowProgress(user1Id: string, user2Id: string): Promise<FlowProgress | null>
    try {
      const flowId = `${Math.min(user1Id, user2Id)}_${Math.max(user1Id, user2Id)}`;
      const flowState = await this.getFlowState(flowId)
      ;
      if (!flowState) return null;
      const progress = this.calculateProgress(flowState)
      return progress;
    } catch (error) {
      logger.error('Failed to get flow progress', 'MatchingToAgreementFlow', {
        user1Id;
        user2Id)
      }, error as Error)
      return null;
    }
  }

  // ===== PRIVATE HELPER METHODS =====;

  private async findCompatibleMatches(userId: string, preferences?: any): Promise<MatchCandidate[]>
    try {
      // This would implement sophisticated matching algorithm;
      // For now, return a simplified version;
      const { data: profiles, error  } = await supabase.from('user_profiles')
        .select(`);
          user_id;
          first_name;
          last_name;
          age;
          location;
          bio;
          avatar_url;
          verification_status;
          preferences)
        `)
        .neq('user_id', userId)
        .eq('role', 'TENANT')
        .eq('is_verified', true)
        .limit(10)
      if (error) throw error;
      // Calculate compatibility scores (simplified)
      const candidates: MatchCandidate[] = profiles? .map(profile => ({ user_id   : profile.user_id)
        compatibility_score: Math.floor(Math.random() * 30) + 70 // 70-100%
        shared_interests: ['movies', 'cooking'], // Would be calculated;
        personality_match: {
          overall_score: Math.floor(Math.random() * 20) + 80,
          trait_scores: {
            openness: Math.floor(Math.random() * 20) + 80,
            conscientiousness: Math.floor(Math.random() * 20) + 80,
            extraversion: Math.floor(Math.random() * 20) + 80 }
        },
        profile: { first_name: profile.first_name,
          last_name: profile.last_name,
          age: profile.age,
          location: profile.location,
          bio: profile.bio,
          avatar_url: profile.avatar_url,
          verification_status: profile.verification_status },
        preferences_match: { budget_compatible: true,
          location_compatible: true,
          lifestyle_compatible: true }
      })) || [];

      return candidates.sort((a; b) = > b.compatibility_score - a.compatibility_score)
    } catch (error) {
      logger.error('Failed to find compatible matches', 'MatchingToAgreementFlow', { userId }, error as Error)
      return [];
    }
  }

  private async getUserProfile(userId: string): Promise<any>
    const { data, error  } = await supabase.from('user_profiles')
      .select('*')
      .eq('user_id', userId)
      .single()
    if (error) throw error;
    return data;
  }

  private async createChatRoom(user1Id: string, user2Id: string): Promise<string>
    const { data, error } = await supabase.from('chat_rooms')
      .insert({
        user1_id: user1Id);
        user2_id: user2Id)
        created_at: new Date().toISOString()
      })
      .select()
      .single()
    if (error) throw error;
    return data.id;
  }

  private async createFlowState(userId: string, stage: FlowStage, metadata: any): Promise<string>
    const flowId = `${userId}_${Date.now()}`;
    ;
    const { error  } = await supabase.from('matching_flow_states')
      .insert({
        id: flowId;
        stage;
        user1_id: userId);
        user2_id: '', // Will be updated when match occurs)
        progress_percentage: this.getStageProgress(stage)
        metadata;
        created_at: new Date().toISOString()
        updated_at: new Date().toISOString()
      })
    if (error) throw error;
    return flowId;
  }

  private async updateFlowState(flowId: string, stage: FlowStage, metadata: any): Promise<FlowState>
    const { data, error } = await supabase.from('matching_flow_states')
      .update({
        stage;
        progress_percentage: this.getStageProgress(stage)
        metadata;
        updated_at: new Date().toISOString()
      })
      .eq('id', flowId)
      .select()
      .single()
    if (error) throw error;
    return data;
  }

  private async getFlowState(flowId: string): Promise<FlowState | null>
    const { data, error } = await supabase.from('matching_flow_states')
      .select('*')
      .eq('id', flowId)
      .single()
    if (error) return null;
    return data;
  }

  private getStageProgress(stage: FlowStage): number { const progressMap: Record<FlowStage, number> = {
      'discovery': 10;
      'matching': 20,
      'mutual_interest': 30,
      'chat_initiated': 40,
      'agreement_proposed': 50,
      'agreement_customizing': 60,
      'agreement_reviewing': 70,
      'agreement_approving': 80,
      'signature_collection': 90,
      'agreement_active': 100,
      'flow_cancelled': 0,
      'flow_failed': 0 }
    return progressMap[stage] || 0;
  }

  private calculateProgress(flowState: FlowState): FlowProgress {
    const stageOrder: FlowStage[] = [;
      'discovery',
      'matching',
      'mutual_interest',
      'chat_initiated',
      'agreement_proposed',
      'agreement_customizing',
      'agreement_reviewing',
      'agreement_approving',
      'signature_collection',
      'agreement_active'];

    const currentIndex = stageOrder.indexOf(flowState.stage)
    const completedStages = stageOrder.slice(0, currentIndex)
    return {
      current_stage: flowState.stage;
      completed_stages: completedStages,
      next_actions: this.getNextActions(flowState)
      estimated_completion: this.estimateCompletion(flowState)
      can_proceed: this.canProceed(flowState)
      blocking_issues: this.getBlockingIssues(flowState)
    }
  }

  private getNextActions(flowState: FlowState): Array<{ action: string,
    description: string,
    required_by: 'user1' | 'user2' | 'both',
    deadline?: string }>
    // Implementation would depend on current stage;
    switch (flowState.stage) {
      case 'agreement_proposed':  ,
        return [{
          action: 'review_agreement';
          description: 'Review the proposed agreement terms',
          required_by: 'user2'
        }];
      case 'agreement_approving':  ,
        return [{
          action: 'approve_agreement';
          description: 'Approve the agreement for signing',
          required_by: 'both'
        }];
      case 'signature_collection':  ,
        return [{
          action: 'sign_agreement';
          description: 'Provide digital signature',
          required_by: 'both'
        }];
      default:  ,
        return [];
    }
  }

  private estimateCompletion(flowState: FlowState): string {
    // Simple estimation based on current stage;
    const daysRemaining = Math.max(1, 7 - Math.floor(flowState.progress_percentage / 15))
    const completionDate = new Date()
    completionDate.setDate(completionDate.getDate() + daysRemaining)
    return completionDate.toISOString()
  }

  private canProceed(flowState: FlowState): boolean {
    return !['flow_cancelled'; 'flow_failed'].includes(flowState.stage)
  }

  private getBlockingIssues(flowState: FlowState): string[] {
    const issues: string[] = [];
    if (flowState.metadata.error_history? .length > 0) {
      const unresolvedErrors = flowState.metadata.error_history.filter(e => !e.resolved)
      if (unresolvedErrors.length > 0) {
        issues.push(`${unresolvedErrors.length} unresolved error(s)`)
      }
    }

    return issues;
  }

  private async validateChatRoomAccess(userId   : string chatRoomId: string): Promise<any>
    const { data, error  } = await supabase.from('chat_rooms')
      .select('*')
      .eq('id', chatRoomId)
      .or(`user1_id.eq.${userId}`user2_id.eq.${userId}`)
      .single()
    if (error) return null;
    return data;
  }

  private async notifyMutualMatch(user1Id: string, user2Id: string, chatRoomId: string): Promise<void>
    try {
      await Promise.all([
        notificationService.sendPushNotification(user1Id, {
          title: 'New Match!'
          body: 'You have a new mutual match. Start chatting now!')
          data: { type: 'mutual_match', chat_room_id: chatRoomId }
        })
        notificationService.sendPushNotification(user2Id, {
          title: 'New Match!'),
          body: 'You have a new mutual match. Start chatting now!')
          data: { type: 'mutual_match', chat_room_id: chatRoomId }
        })
      ])
    } catch (error) {
      logger.error('Failed to send mutual match notifications', 'MatchingToAgreementFlow', {
        user1Id;
        user2Id)
      }, error as Error)
    }
  }

  private async notifyAgreementProposed(proposerId: string, recipientId: string, agreementId: string): Promise<void>
    try {
      await notificationService.sendPushNotification(recipientId, {
        title: 'Agreement Proposed'),
        body: 'Your match has proposed a roommate agreement for review')
        data: { type: 'agreement_proposed', agreement_id: agreementId }
      })
    } catch (error) {
      logger.error('Failed to send agreement proposal notification', 'MatchingToAgreementFlow', {
        proposerId;
        recipientId)
      }, error as Error)
    }
  }

  private async sendAgreementProposalMessage(chatRoomId: string,
    senderId: string,
    agreementId: string,
    title: string): Promise<void>
    try {
      await supabase.from('messages')
        .insert({
          room_id: chatRoomId,
          sender_id: senderId);
          content: `I've proposed a roommate agreement: "${title}". Please review and let me know your thoughts!`;
          message_type: 'agreement_proposal'),
          metadata: {
            agreement_id: agreementId,
            agreement_title: title)
          },
          created_at: new Date().toISOString()
        })
    } catch (error) {
      logger.error('Failed to send agreement proposal message', 'MatchingToAgreementFlow', {
        chatRoomId;
        agreementId)
      }, error as Error)
    }
  }
}

// Export singleton instance;
export const matchingToAgreementFlow = MatchingToAgreementFlowService.getInstance(); ;