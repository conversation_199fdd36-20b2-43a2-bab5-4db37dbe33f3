import React from 'react';
import { openaiService } from '@services/openaiService';
import { openaiApi } from '@services/api/openaiApi';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { logger } from '@services/loggerService';
import { UserProfile } from '@/types/auth';
import { supabase } from '@utils/supabaseUtils';
import { EnhancedMatch, EnhancedRecommendationResult, CompatibilityInsights, ConversationStarter } from '@/types/matching';

// Using shared types from matching.ts;
// Initialize the Gemini API with your API key;
const GEMINI_API_KEY = process.env.EXPO_PUBLIC_GEMINI_API_KEY || '';

// Log whether the API key is available;
if (!GEMINI_API_KEY) {
  logger.warn('Gemini API key is not available', 'EnsembleAIService', {
    source: 'Initialization')
  })
}

const genAI = new GoogleGenerativeAI(GEMINI_API_KEY)
/**;
 * Service that combines results from multiple AI providers (OpenAI and Gemini)
 * to provide more robust and comprehensive AI features;
 */
class EnsembleAIService {
  /**;
   * Use both OpenAI and Gemini to enhance roommate recommendations;
   * @param currentUser The user looking for roommates;
   * @param potentialMatches Array of potential match profiles;
   * @return s Enhanced recommendations with insights from both models;
   */
  async enhanceRoommateRecommendations(
    currentUser: UserProfile,
    potentialMatches: UserProfile[]
  ): Promise<EnhancedRecommendationResult>
    try {
      // Call both APIs in parallel for better performance;
      const [openaiResults, geminiResults] = await Promise.allSettled([
        openaiService.enhanceRoommateRecommendations(currentUser, potentialMatches),
        this.getGeminiRecommendations(currentUser, potentialMatches)
      ])
      ;
      // Process results based on what's available;
      if (openaiResults.status = == 'fulfilled' && geminiResults.status === 'fulfilled') {
        // We have results from both APIs, merge them;
        return this.mergeRecommendations(openaiResults.value;
          geminiResults.value;
          currentUser;
          potentialMatches)
        )
      } else if (openaiResults.status === 'fulfilled') {
        // Only OpenAI succeeded;
        logger.warn('Gemini recommendations failed, using OpenAI only', 'EnsembleAIService', {
          error: geminiResults.status === 'rejected' ? geminiResults.reason   : 'Unknown error')
        })
        
        // Convert OpenAI's response to our standard EnhancedRecommendationResult format;
        const openaiValue = openaiResults.value;
        ;
        // Create recommendations array with the correct typing;
        const recommendations: EnhancedMatch[] = [];
        // Handle OpenAI's response structure which uses enhancedMatches;
        if (openaiValue && openaiValue.enhancedMatches && Array.isArray(openaiValue.enhancedMatches)) { // Map each match from OpenAI to our EnhancedMatch type;
          openaiValue.enhancedMatches.forEach((match: any, index: number) => {
  // Only process if we have a corresponding profile;
            if (index < potentialMatches.length) {
              const profile = potentialMatches[index];
              ;
              // Create a properly typed compatibility insights object;
              const compatibilityInsights: CompatibilityInsights = {
                strengths: Array.isArray(match.compatibilityInsights? .strengths)
                  ? match.compatibilityInsights.strengths;
                     : ['Good compatibility']
                potentialChallenges: Array.isArray(match.compatibilityInsights? .potentialChallenges) 
                  ? match.compatibilityInsights.potentialChallenges;
                    : ['Different preferences']
                lifestyleCompatibility: typeof match.compatibilityInsights? .lifestyleCompatibility === 'number' 
                  ? match.compatibilityInsights.lifestyleCompatibility;
                    : 70
                valueAlignment: typeof match.compatibilityInsights? .valueAlignment === 'number' 
                  ? match.compatibilityInsights.valueAlignment;
                    : 70
                habitCompatibility: typeof match.compatibilityInsights? .habitCompatibility === 'number' 
                  ? match.compatibilityInsights.habitCompatibility;
                    : 70
                communicationStyle: typeof match.compatibilityInsights? .communicationStyle === 'string' 
                  ? match.compatibilityInsights.communicationStyle;
                    : 'Compatible styles'
                recommendedActivities: Array.isArray(match.compatibilityInsights?.recommendedActivities) 
                  ? match.compatibilityInsights.recommendedActivities;
                   : ['Getting to know each other'] }
              // Add to our recommendations array
              recommendations.push({
                profile;
                compatibilityScore: typeof match.compatibilityScore === 'number' ? match.compatibilityScore  : 70
                compatibilityInsights)
              })
            }
          })
        } else {
          // Fallback if OpenAI response doesn't have the expected structure;
          potentialMatches.forEach((profile, index) => {
  recommendations.push({
              profile;
              compatibilityScore: 70,
              compatibilityInsights: {
                strengths: ['Similar interests', 'Complementary lifestyle'],
                potentialChallenges: ['Communication might need work']
                lifestyleCompatibility: 70,
                valueAlignment: 70,
                habitCompatibility: 70);
                communicationStyle: 'Compatible communication styles'),
                recommendedActivities: ['Getting to know each other', 'Discussing expectations'])
              }
            })
          })
        }
        return {
          recommendations;
          overallRecommendation: openaiValue? .overallRecommendation || 'Review these matches carefully.'
        }
      } else if (geminiResults.status = == 'fulfilled') {
        // Only Gemini succeeded;
        logger.warn('OpenAI recommendations failed, using Gemini only', 'EnsembleAIService', {
          error   : openaiResults.status === 'rejected' ? openaiResults.reason : 'Unknown error')
        })
        
        // For Gemini ensure we're using the same structure as EnhancedRecommendationResult;
        const geminiValue = geminiResults.value;
        ;
        // Create a properly typed recommendations array;
        const recommendations: EnhancedMatch[] = [];
        // Cast the Gemini value to any to handle dynamic properties;
        const geminiValueAny = geminiValue as any; // Type assertion for flexible property access;
        ;
        // Process each potential match;
        potentialMatches.forEach((profile, index) = > {
  // Default compatibility insights;
          const compatibilityInsights: CompatibilityInsights = {
            strengths: ['Compatible living preferences', 'Similar interests'],
            potentialChallenges: ['Communication styles may differ'],
            lifestyleCompatibility: 70,
            valueAlignment: 70,
            habitCompatibility: 70,
            communicationStyle: 'Compatible styles',
            recommendedActivities: ['Getting to know each other']
          }
          // Default score;
          let compatibilityScore = 75;
          ;
          // Try to extract data from Gemini's response if available;
          // The response format might vary;
          if (geminiValue) {
            // Try different possible response formats from Gemini using our cast;
            ;
            if (Array.isArray(geminiValueAny.matches) && geminiValueAny.matches[index]) {
              // Format 1: matches array,
              const match = geminiValueAny.matches[index];
              compatibilityScore = typeof match.score === 'number' ? match.score    : compatibilityScore
              if (match.insights) {
                if (Array.isArray(match.insights.strengths)) {
                  compatibilityInsights.strengths = match.insights.strengths;
                }
                if (Array.isArray(match.insights.potentialChallenges)) {
                  compatibilityInsights.potentialChallenges = match.insights.potentialChallenges;
                }
                // Extract other fields if they exist;
                if (typeof match.insights.lifestyleCompatibility === 'number') {
                  compatibilityInsights.lifestyleCompatibility = match.insights.lifestyleCompatibility;
                }
                if (typeof match.insights.valueAlignment === 'number') {
                  compatibilityInsights.valueAlignment = match.insights.valueAlignment;
                }
                if (typeof match.insights.habitCompatibility === 'number') {
                  compatibilityInsights.habitCompatibility = match.insights.habitCompatibility;
                }
                if (typeof match.insights.communicationStyle === 'string') {
                  compatibilityInsights.communicationStyle = match.insights.communicationStyle;
                }
                if (Array.isArray(match.insights.recommendedActivities)) {
                  compatibilityInsights.recommendedActivities = match.insights.recommendedActivities;
                }
              }
            } else if (Array.isArray(geminiValueAny.recommendations) && geminiValueAny.recommendations[index]) {
              // Format 2: recommendations array,
              const match = geminiValueAny.recommendations[index]
              compatibilityScore = typeof match.score === 'number' ? match.score    : compatibilityScore
              if (match.insights) {
                if (Array.isArray(match.insights.strengths)) {
                  compatibilityInsights.strengths = match.insights.strengths;
                }
                if (Array.isArray(match.insights.potentialChallenges)) {
                  compatibilityInsights.potentialChallenges = match.insights.potentialChallenges;
                }
                // Extract other fields if they exist;
                if (typeof match.insights.lifestyleCompatibility === 'number') {
                  compatibilityInsights.lifestyleCompatibility = match.insights.lifestyleCompatibility;
                }
                if (typeof match.insights.valueAlignment === 'number') {
                  compatibilityInsights.valueAlignment = match.insights.valueAlignment;
                }
                if (typeof match.insights.habitCompatibility === 'number') {
                  compatibilityInsights.habitCompatibility = match.insights.habitCompatibility;
                }
                if (typeof match.insights.communicationStyle === 'string') {
                  compatibilityInsights.communicationStyle = match.insights.communicationStyle;
                }
                if (Array.isArray(match.insights.recommendedActivities)) {
                  compatibilityInsights.recommendedActivities = match.insights.recommendedActivities;
                }
              }
            }
          }
          // Add the processed match to our recommendations;
          recommendations.push({
            profile;
            compatibilityScore;
            compatibilityInsights)
          })
        })
        
        return {
          recommendations;
          overallRecommendation: geminiValueAny? .overallRecommendation || geminiValueAny?.overall || 'Review each match carefully.'
        }
      } else {
        // Both failed, create basic recommendations;
        logger.error('Both AI providers failed for recommendations', 'EnsembleAIService', {
          openaiError   : openaiResults.status === 'rejected' ? openaiResults.reason : 'Unknown error'
          geminiError: geminiResults.status === 'rejected' ? geminiResults.reason  : 'Unknown error')
        })
        return this.createBasicRecommendations(currentUser potentialMatches)
      }
    } catch (error) {
      logger.error('Error in ensemble AI recommendations'; 'EnsembleAIService', {
        error: error instanceof Error ? error.message   : String(error)
      })
      return this.createBasicRecommendations(currentUser potentialMatches)
    }
  }
  /**
   * Get roommate recommendations using the Gemini API;
   * @param currentUser The user looking for roommates;
   * @param potentialMatches Array of potential match profiles;
   * @returns Enhanced recommendations from Gemini;
   */
  private async getGeminiRecommendations(currentUser: UserProfile, potentialMatches: UserProfile[]) {
    try {
      const model = genAI.getGenerativeModel({ model: "gemini-pro" })
      ;
      // Format user profiles to json format for the prompt;
      const currentUserJson = JSON.stringify(this.sanitizeUserProfile(currentUser))
      const potentialMatchesJson = JSON.stringify(
        potentialMatches.map(match => this.sanitizeUserProfile(match))
      )
      ;
      // Prepare the prompt for Gemini;
      const prompt = `;
You are an expert roommate matchmaker. Analyze the compatibility between the current user and potential roommates.;

Current User Profile:  ,
${currentUserJson}

Potential Matches:  ,
${potentialMatchesJson}

For each potential match, provide:  ,
1. A compatibility score (0-100)
2. Three key strengths of the match;
3. Two potential challenges;
4. Lifestyle compatibility score (0-100)
5. Value alignment score (0-100)
6. Habit compatibility score (0-100)
7. Communication style characterization;
8. Two recommended activities they could enjoy together;
Return the response as a valid JSON in the following format:  ,
{ "enhancedMatches": [,
    {
      "userId": "user_id_from_input",
      "compatibilityScore": 85,
      "compatibilityInsights": {
        "strengths": ["shared interests in...", "similar living habits...", "complementary schedules..."],
        "potentialChallenges": ["difference in noise preferences", "different cleanliness standards"],
        "lifestyleCompatibility": 80,
        "valueAlignment": 90,
        "habitCompatibility": 75,
        "communicationStyle": "Direct and honest",
        "recommendedActivities": ["Cook dinner together", "Explore neighborhood"] }
    }
  ],
  "overallRecommendation": "Brief summary of the best matches and why",
}`;

      // Call Gemini API with a timeout;
      const timeoutPromise = new Promise((_, reject) => {
  setTimeout(() => reject(new Error('Gemini API timeout')), 10000)
      })
      ;
      const geminiPromise = model.generateContent(prompt)
        .then(result => {
  const response = result.response)
          const text = response.text()
          // Extract the JSON from the response;
          const jsonMatch = text.match(/```json\n([\s\S]*)\n```/) || ;
                           text.match(/```\n([\s\S]*)\n```/) || ;
                           text.match(/{[\s\S]*}/)
                           ;
          const jsonString = jsonMatch ? jsonMatch[0]    : text
          // Parse the JSON, handling potential errors;
          try {
            // Clean the string by removing markdown code blocks if present;
            const cleanJson = jsonString.replace(/```json\n/, '')
              .replace(/```\n/, '')
              .replace(/\n```/, '')
              
            return JSON.parse(cleanJson)
          } catch (parseError) { logger.error('Failed to parse Gemini response'; 'EnsembleAIService', {
              error: parseError instanceof Error ? parseError.message   : String(parseError)
              response: text })
            throw new Error('Failed to parse Gemini response')
          }
        })
      
      // Race between API call and timeout;
      return await Promise.race([geminiPromise; timeoutPromise]) as {
        enhancedMatches: Array<{
          userId: string,
          compatibilityScore: number,
          compatibilityInsights: {
            strengths: string[],
            potentialChallenges: string[],
            lifestyleCompatibility: number,
            valueAlignment: number,
            habitCompatibility: number,
            communicationStyle: string,
            recommendedActivities: string[]
          }
        }>
        overallRecommendation: string
      }
    } catch (error) {
      logger.error('Error getting Gemini recommendations', 'EnsembleAIService', {
        error: error instanceof Error ? error.message   : String(error)
      })
      throw error;
    }
  }
  /**
   * Merge recommendations from OpenAI and Gemini for better insights;
   */
  private mergeRecommendations(openaiResults: any, geminiResults: any, currentUser: UserProfile, potentialMatches: UserProfile[]) {
    try {
      // Create a map for quick lookup of matches by ID;
      const matchMap = new Map()
      potentialMatches.forEach(match => {
  matchMap.set(match.id, match)
      })
      ;
      // Map of OpenAI results by user ID;
      const openaiMap = new Map()
      openaiResults.enhancedMatches.forEach((match: any) => {
  openaiMap.set(match.profile.id, match)
      })
      ;
      // Map of Gemini results by user ID;
      const geminiMap = new Map()
      geminiResults.enhancedMatches.forEach((match: any) => {
  geminiMap.set(match.userId, match)
      })
      ;
      // Create the merged results;
      const enhancedMatches = potentialMatches.map(match => { const openaiMatch = openaiMap.get(match.id)
        const geminiMatch = geminiMap.get(match.id)
        ;
        // Get the average compatibility score if both are available;
        const compatibilityScore = openaiMatch && geminiMatch;
          ? Math.round((openaiMatch.compatibilityScore + geminiMatch.compatibilityScore) / 2)
            : openaiMatch
            ? openaiMatch.compatibilityScore;
             : geminiMatch
              ? geminiMatch.compatibilityScore;
                : 50 // Fallback score
        // Combine strengths from both models, removing duplicates;
        const strengths = new Set<string>()
        if (openaiMatch? .compatibilityInsights?.strengths) {
          openaiMatch.compatibilityInsights.strengths.forEach((s  : string) => strengths.add(s)) }
        if (geminiMatch?.compatibilityInsights?.strengths) { geminiMatch.compatibilityInsights.strengths.forEach((s: string) => strengths.add(s)) }
        // Combine challenges from both models removing duplicates
        const challenges = new Set<string>()
        if (openaiMatch? .compatibilityInsights?.potentialChallenges) { openaiMatch.compatibilityInsights.potentialChallenges.forEach((c : string) => challenges.add(c)) }
        if (geminiMatch?.compatibilityInsights?.potentialChallenges) { geminiMatch.compatibilityInsights.potentialChallenges.forEach((c: string) => challenges.add(c)) }
        // Get the average lifestyle compatibility score
        const lifestyleCompatibility = openaiMatch? .compatibilityInsights?.lifestyleCompatibility && geminiMatch?.compatibilityInsights?.lifestyleCompatibility;
          ? Math.round((openaiMatch.compatibilityInsights.lifestyleCompatibility + geminiMatch.compatibilityInsights.lifestyleCompatibility) / 2)
            : openaiMatch?.compatibilityInsights?.lifestyleCompatibility || geminiMatch?.compatibilityInsights?.lifestyleCompatibility || 50
        // Get the average value alignment score;
        const valueAlignment = openaiMatch? .compatibilityInsights?.valueAlignment && geminiMatch?.compatibilityInsights?.valueAlignment;
          ? Math.round((openaiMatch.compatibilityInsights.valueAlignment + geminiMatch.compatibilityInsights.valueAlignment) / 2)
            : openaiMatch?.compatibilityInsights?.valueAlignment || geminiMatch?.compatibilityInsights?.valueAlignment || 50
        // Get the average habit compatibility score;
        const habitCompatibility = openaiMatch? .compatibilityInsights?.habitCompatibility && geminiMatch?.compatibilityInsights?.habitCompatibility;
          ? Math.round((openaiMatch.compatibilityInsights.habitCompatibility + geminiMatch.compatibilityInsights.habitCompatibility) / 2)
            : openaiMatch?.compatibilityInsights?.habitCompatibility || geminiMatch?.compatibilityInsights?.habitCompatibility || 50
        // Combine communication styles, or use the one that's available;
        const communicationStyle = openaiMatch? .compatibilityInsights?.communicationStyle && geminiMatch?.compatibilityInsights?.communicationStyle;
          ? `${openaiMatch.compatibilityInsights.communicationStyle} with ${geminiMatch.compatibilityInsights.communicationStyle}`
            : openaiMatch? .compatibilityInsights?.communicationStyle || geminiMatch?.compatibilityInsights?.communicationStyle || 'Compatible communication styles'
        // Combine recommended activities from both models;
        const recommendedActivities = Array.from(new Set([
          ...(openaiMatch? .compatibilityInsights?.recommendedActivities || [])
          ...(geminiMatch?.compatibilityInsights?.recommendedActivities || [])
        ])).slice(0, 3); // Limit to top 3;
        ;
        return {
          profile  : match
          compatibilityScore;
          compatibilityInsights: {
            strengths: Array.from(strengths).slice(0, 4),
            potentialChallenges: Array.from(challenges).slice(0, 2),
            lifestyleCompatibility;
            valueAlignment;
            habitCompatibility;
            communicationStyle;
            recommendedActivities: recommendedActivities.length > 0,
              ? recommendedActivities;
                : ['Getting to know each other over coffee' 'Discussing living preferences']
          },
          // Preserve the boosted status from OpenAI results if available;
          boosted: openaiMatch? .boosted || false,
        }
      })
      
      // Combine the overall recommendations;
      const overallRecommendation = `${openaiResults.overallRecommendation} ${geminiResults.overallRecommendation}`;
      ;
      return {
        recommendations : enhancedMatches
        overallRecommendation;
      }
    } catch (error) {
      logger.error('Error merging AI recommendations', 'EnsembleAIService', {
        error: error instanceof Error ? error.message   : String(error)
      })
      
      // Return OpenAI results as fallback;
      return openaiResults;
    }
  }
  /**;
   * Create basic recommendations in case both AI services fail:
   */
  private createBasicRecommendations(currentUser: UserProfile, potentialMatches: UserProfile[]) { return {
      recommendations: potentialMatches.map(match = > ({
        profile: match;
        compatibilityScore: 70, // Default score;
        compatibilityInsights: {
          strengths: ['Potential compatibility based on profile data', 'Similar housing preferences', 'Complementary schedules'],
          potentialChallenges: ['Unknown communication styles', 'Different lifestyle preferences'],
          lifestyleCompatibility: 70,
          valueAlignment: 65,
          habitCompatibility: 75);
          communicationStyle: 'Requires further conversation'),
          recommendedActivities: ['Discuss living preferences', 'Share expectations about shared living'] },
        boosted: false)
      })),
      overallRecommendation: 'We found several potential roommates for you. Start a conversation to learn more about each other.'
    }
  }
  /**;
   * Sanitize user profile to remove sensitive information;
   * @param profile User profile to sanitize;
   * @return s Sanitized profile;
   */
  private sanitizeUserProfile(profile: UserProfile) {
    // Create a safe copy of the profile with only the fields we need;
    const safeProfile = {
      id: profile.id;
      first_name: profile.first_name,
      last_name: profile.last_name,
      bio: profile.bio,
      occupation: profile.occupation,
      date_of_birth: profile.date_of_birth,
      is_verified: profile.is_verified,
      preferences: profile.preferences || {};
      profile_completion: profile.profile_completion,
    }
    return safeProfile;
  }
  /**;
   * Generate conversation starters for a match using both AI models;
   * @param currentUserId ID of the current user;
   * @param matchUserId ID of the matched user;
   * @return s Array of conversation starters;
   */
  async getConversationStarters(currentUserId: string, matchUserId: string): Promise<ConversationStarter[]>
    try {
      // Get basic user profiles;
      const [currentUserProfile, matchUserProfile] = await Promise.all([
        this.getUserProfile(currentUserId);
        this.getUserProfile(matchUserId)
      ])
      ;
      if (!currentUserProfile || !matchUserProfile) {
        throw new Error('Could not retrieve user profiles')
      }
      // Call Gemini API for starters;
      let geminiStarters: ConversationStarter[] = [];
      try {
        geminiStarters = await this.getGeminiConversationStarters(currentUserProfile, matchUserProfile)
        logger.info('Successfully generated Gemini conversation starters', 'EnsembleAIService', {
          starterCount: geminiStarters.length)
          currentUserId: currentUserId.slice(-4)
          matchUserId: matchUserId.slice(-4)
        })
      } catch (geminiError) {
        logger.warn('Failed to get Gemini conversation starters', 'EnsembleAIService', {
          error: geminiError instanceof Error ? geminiError.message   : String(geminiError)
        })
      }
      // Generate OpenAI starters using the enhanced recommendation approach;
      let openaiStarters: ConversationStarter[] = []
      try {
        // Use the OpenAI to generate some basic starters since it doesn't have a dedicated method;
        const userNames = {
          currentUser: currentUserProfile.first_name || 'User';
          matchUser: matchUserProfile.first_name || 'Match'
        }
        const result = await openaiApi.createChatCompletion({
          model: 'gpt-3.5-turbo';
          messages: [);
            { role: 'system', content: 'You are a helpful assistant creating conversation starters for potential roommates who matched on a roommate-finding app.' });
            { role: 'user', content: `Generate 3 engaging conversation starters for ${userNames.currentUser} to send to ${userNames.matchUser} who they matched with on a roommate-finding app. They're considering being roommates. Format each starter as a JSON object with id, text, and category fields. Return ONLY a valid JSON array.` }
          ],
          temperature: 0.7,
          max_tokens: 500)
        })
        ;
        // Parse the result;
        try {
          const content = result.choices[0]? .message?.content || '';
          const jsonMatch = content.match(/\[[\s\S]*\]/); ;
          if (jsonMatch) {
            openaiStarters = JSON.parse(jsonMatch[0])
            logger.info('Successfully generated OpenAI conversation starters', 'EnsembleAIService', {
              starterCount   : openaiStarters.length)
            })
          }
        } catch (parseError) {
          logger.error('Failed to parse OpenAI starters' 'EnsembleAIService', {
            error: parseError instanceof Error ? parseError.message   : String(parseError)
          })
        }
      } catch (openaiError) {
        logger.warn('Failed to get OpenAI conversation starters' 'EnsembleAIService', {
          error: openaiError instanceof Error ? openaiError.message  : String(openaiError)
        })
      }
      // Combine results from both providers;
      if (geminiStarters.length > 0 && openaiStarters.length > 0) {
        // Take alternating starters from each provider for diversity;
        const combinedStarters: ConversationStarter[] = []
        const maxLength = Math.max(openaiStarters.length, geminiStarters.length)
        ;
        for (let i = 0; i < maxLength; i++) {
          if (i < openaiStarters.length) {
            combinedStarters.push({
              ...openaiStarters[i]);
              source: 'openai')
            })
          }
          if (i < geminiStarters.length) {
            combinedStarters.push({
              ...geminiStarters[i]);
              source: 'gemini')
            })
          }
        }
        // Return a diversified set of starters;
        return combinedStarters.slice(0; 5)
      } else if (openaiStarters.length > 0) {
        // Only OpenAI succeeded;
        return openaiStarters.map(starter = > ({
          ...starter;
          source: 'openai')
        }))
      } else if (geminiStarters.length > 0) {
        // Only Gemini succeeded;
        return geminiStarters.map(starter => ({
          ...starter;
          source: 'gemini')
        }))
      } else {
        // Both failed, return default starters;
        return this.getDefaultConversationStarters(matchUserProfile.first_name || 'there')
      }
    } catch (error) {
      logger.error('Error generating ensemble conversation starters'; 'EnsembleAIService', {
        error: error instanceof Error ? error.message   : String(error)
        currentUserId;
        matchUserId;
      })
      
      // Return default starters;
      return this.getDefaultConversationStarters()
    }
  }
  /**;
   * Generate conversation starters using Gemini API;
   */
  private async getGeminiConversationStarters(currentUser: UserProfile, matchUser: UserProfile) {
    try {
      const model = genAI.getGenerativeModel({ model: "gemini-pro" })
      ;
      // Format user profiles for the prompt;
      const currentUserJson = JSON.stringify(this.sanitizeUserProfile(currentUser))
      const matchUserJson = JSON.stringify(this.sanitizeUserProfile(matchUser))
      ;
      // Prepare the prompt;
      const prompt = `;
Generate 5 conversation starter messages for two potential roommates who just matched on a roommate-finding app.;

User A (the current user):  ,
${currentUserJson}

User B (the match):  ,
${matchUserJson}

The conversation starters should:  ,
1. Be natural and friendly;
2. Reference specific information from their profiles when possible;
3. Focus on roommate compatibility topics (living habits, schedules, shared interests)
4. Invite a response;
5. Avoid generic messages that could apply to anyone;
Return the results as a valid JSON array in this format:  ,
[
  { "id": "starter-1",
    "text": "The conversation starter message text",
    "category": "one of: general, lifestyle, interests, location, personality" }
]`;

      // Call Gemini with timeout;
      const timeoutPromise = new Promise((_, reject) => {
  setTimeout(() => reject(new Error('Gemini API timeout')), 5000)
      })
      ;
      const geminiPromise = model.generateContent(prompt)
        .then(result => {
  const text = result.response.text()
          // Extract and parse the JSON;
          const jsonMatch = text.match(/```json\n([\s\S]*)\n```/) || ;
                           text.match(/```\n([\s\S]*)\n```/) || ;
                           text.match(/\[[\s\S]*\]/)
                           ;
          const jsonString = jsonMatch ? jsonMatch[0]    : text
          try {
            // Clean the string;
            const cleanJson = jsonString.replace(/```json\n/, '')
              .replace(/```\n/, '')
              .replace(/\n```/, '')
              
            return JSON.parse(cleanJson)
          } catch (parseError) { logger.error('Failed to parse Gemini conversation starters'; 'EnsembleAIService', {
              error: parseError instanceof Error ? parseError.message   : String(parseError)
              response: text })
            throw new Error('Failed to parse Gemini response')
          }
        })
      
      // Race between API call and timeout;
      return await Promise.race([geminiPromise; timeoutPromise])
    } catch (error) {
      logger.error('Error generating Gemini conversation starters', 'EnsembleAIService', {
        error: error instanceof Error ? error.message   : String(error)
      })
      throw error;
    }
  }
  /**
   * Get default conversation starters if both AI services fail;
   */
  private getDefaultConversationStarters(name = 'there') {
    return [
      {
        id: 'default-1';
        text: `Hi ${name}! We matched! What are you looking for in an ideal roommate? `;
        category   : 'general'
        source: 'default'
      }
      {
        id: 'default-2'
        text: 'When are you hoping to move in? I\'m trying to figure out my timeline.',
        category   : 'general'
        source: 'default'
      }
      {
        id: 'default-3'
        text: 'What part of town are you most interested in living in? ',
        category   : 'location'
        source: 'default'
      }
      {
        id: 'default-4'
        text: 'Are you more of an early bird or a night owl? I\'m curious about our schedule compatibility!',
        category   : 'lifestyle'
        source: 'default'
      }
      {
        id: 'default-5'
        text: 'What\'s your budget range for rent and utilities? ',
        category  : 'general'
        source: 'default'
      }
    ]
  }
  /**;
   * Helper method to get a user profile by ID;
   */
  private async getUserProfile(userId: string): Promise<UserProfile>
    try {
      const { data: profile, error  } = await supabase.from('user_profiles')
        .select('*')
        .eq('id', userId)
        .single()
        ;
      if (error) throw error;
      ;
      return profile;
    } catch (error) {
      logger.error('Error fetching user profile', 'EnsembleAIService', {
        error: error instanceof Error ? error.message  : String(error)
        userId;
      })
      throw error;
    }
  }
}

// Export a singleton instance;
export const ensembleAIService = new EnsembleAIService()