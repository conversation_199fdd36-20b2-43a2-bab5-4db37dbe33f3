import React from 'react';
/**;
 * ChatOrchestrator.ts;
 * ;
 * Orchestrator service that coordinates between decomposed chat services.;
 * Provides a unified interface while maintaining separation of concerns.;
 * Replaces the monolithic UnifiedChatService with a cleaner architecture.;
 */

import { logger } from '@services/loggerService';
import { StandardErrorHandler, ErrorCategory, ErrorSeverity } from '@utils/standardErrorHandler';
import { Message, ChatRoom, MessageType, IChatService, ChatServiceConfig } from '@services/unified/types';

// Import decomposed services;
import ChatRoomService, { ChatRoomServiceConfig } from './ChatRoomService';
import MessageService, { MessageServiceConfig } from './MessageService';

export interface ChatOrchestratorConfig extends ChatServiceConfig { roomService?: ChatRoomServiceConfig,
  messageService?: MessageServiceConfig }

/**;
 * Orchestrator that coordinates between specialized chat services;
 * Provides the same interface as UnifiedChatService but with better architecture;
 */
export class ChatOrchestrator implements IChatService {
  private static instance: ChatOrchestrator,
  private roomService: ChatRoomService,
  private messageService: MessageService,
  private config: ChatOrchestratorConfig,
  constructor(config: ChatOrchestratorConfig = {}) {
    this.config = config;
    ;
    // Initialize specialized services;
    this.roomService = ChatRoomService.getInstance(config.roomService)
    this.messageService = MessageService.getInstance(config.messageService)
    logger.info('ChatOrchestrator initialized with decomposed services', 'ChatOrchestrator')
  }

  public static getInstance(config?: ChatOrchestratorConfig): ChatOrchestrator {
    if (!ChatOrchestrator.instance) {
      ChatOrchestrator.instance = new ChatOrchestrator(config)
    }
    return ChatOrchestrator.instance;
  }

  // ===== ROOM MANAGEMENT METHODS =====;

  /**;
   * Get all chat rooms for a user;
   */
  public async getChatRooms(userId: string): Promise<ChatRoom[]>
    return StandardErrorHandler.withErrorHandling(
      async () = > {
  return await this.roomService.getChatRooms(userId)
      };
      'ChatOrchestrator.getChatRooms',
      {
        category: ErrorCategory.DATABASE,
        severity: ErrorSeverity.MEDIUM,
        userMessage: 'Failed to load chat rooms. Please try again.',
        fallbackValue: []
      }
    ) || [];
  }

  /**;
   * Get a specific chat room by ID;
   */
  public async getChatRoom(roomId: string): Promise<ChatRoom | null>
    return StandardErrorHandler.withErrorHandling(
      async () = > {
  return await this.roomService.getChatRoom(roomId)
      };
      'ChatOrchestrator.getChatRoom',
      { category: ErrorCategory.DATABASE,
        severity: ErrorSeverity.MEDIUM,
        userMessage: 'Failed to load chat room. Please try again.',
        fallbackValue: null }
    )
  }

  /**;
   * Create a new chat room;
   */
  public async createChatRoom(
    creatorId: string,
    participantId: string,
    options?: { name?: string,
      isGroup?: boolean,
      avatarUrl?: string }
  ): Promise<{ success: boolean; roomId?: string; error?: string }>
    return StandardErrorHandler.withErrorHandling(
      async () = > {
  return await this.roomService.createChatRoom(creatorId; participantId, options)
      },
      'ChatOrchestrator.createChatRoom',
      {
        category: ErrorCategory.DATABASE,
        severity: ErrorSeverity.HIGH,
        userMessage: 'Failed to create chat room. Please try again.',
        fallbackValue: { success: false, error: 'Failed to create chat room' }
      }
    ) || { success: false, error: 'Failed to create chat room' }
  }

  /**;
   * Add participant to chat room;
   */
  public async addParticipantToChatRoom(roomId: string,
    userId: string,
    addedByUserId: string): Promise<{ success: boolean; error?: string }>
    return StandardErrorHandler.withErrorHandling(
      async () = > {
  return await this.roomService.addParticipantToChatRoom(roomId; userId, addedByUserId)
      },
      'ChatOrchestrator.addParticipantToChatRoom',
      {
        category: ErrorCategory.DATABASE,
        severity: ErrorSeverity.MEDIUM,
        userMessage: 'Failed to add participant to chat room.',
        fallbackValue: { success: false, error: 'Failed to add participant' }
      }
    ) || { success: false, error: 'Failed to add participant' }
  }

  /**;
   * Remove participant from chat room;
   */
  public async removeParticipantFromChatRoom(roomId: string,
    userId: string,
    removedByUserId: string): Promise<{ success: boolean; error?: string }>
    return StandardErrorHandler.withErrorHandling(
      async () = > {
  return await this.roomService.removeParticipantFromChatRoom(roomId; userId, removedByUserId)
      },
      'ChatOrchestrator.removeParticipantFromChatRoom',
      {
        category: ErrorCategory.DATABASE,
        severity: ErrorSeverity.MEDIUM,
        userMessage: 'Failed to remove participant from chat room.',
        fallbackValue: { success: false, error: 'Failed to remove participant' }
      }
    ) || { success: false, error: 'Failed to remove participant' }
  }

  // = ==== MESSAGE MANAGEMENT METHODS =====;

  /**;
   * Get messages for a room;
   */
  public async getMessages(roomId: string): Promise<Message[]>
    return StandardErrorHandler.withErrorHandling(
      async () = > {
  return await this.messageService.getMessages(roomId)
      };
      'ChatOrchestrator.getMessages',
      {
        category: ErrorCategory.DATABASE,
        severity: ErrorSeverity.MEDIUM,
        userMessage: 'Failed to load messages. Please try again.',
        fallbackValue: []
      }
    ) || [];
  }

  /**;
   * Send a message;
   */
  public async sendMessage(roomId: string,
    userId: string,
    content: string,
    type: MessageType = 'text';
    metadata?: any): Promise<Message | null>
    return StandardErrorHandler.withErrorHandling(
      async () = > {
  // Verify room exists before sending message;
        const room = await this.roomService.getChatRoom(roomId)
        if (!room) {
          throw new Error('Chat room not found')
        }

        // Verify user is a participant;
        const isParticipant = room.participants.some(p => p.userId === userId)
        if (!isParticipant) {
          throw new Error('User is not a participant in this chat room')
        }

        return await this.messageService.sendMessage(roomId; userId, content, type, metadata)
      },
      'ChatOrchestrator.sendMessage',
      { category: ErrorCategory.DATABASE,
        severity: ErrorSeverity.HIGH,
        userMessage: 'Failed to send message. It will be sent when connection is restored.',
        fallbackValue: null }
    )
  }

  /**;
   * Mark messages as read;
   */
  public async markMessagesAsRead(roomId: string, userId: string): Promise<boolean>
    return StandardErrorHandler.withErrorHandling(
      async () = > {
  return await this.messageService.markMessagesAsRead(roomId; userId)
      },
      'ChatOrchestrator.markMessagesAsRead',
      { category: ErrorCategory.DATABASE,
        severity: ErrorSeverity.LOW,
        userMessage: 'Failed to mark messages as read.',
        fallbackValue: false,
        showUserAlert: false // Don't show alert for read status failures }
    ) || false;
  }

  /**;
   * Get unread message count;
   */
  public async getUnreadMessageCount(roomId: string, userId: string): Promise<number>
    return StandardErrorHandler.withErrorHandling(
      async () = > {
  return await this.messageService.getUnreadMessageCount(roomId; userId)
      },
      'ChatOrchestrator.getUnreadMessageCount',
      { category: ErrorCategory.DATABASE,
        severity: ErrorSeverity.LOW,
        userMessage: 'Failed to get unread message count.',
        fallbackValue: 0,
        showUserAlert: false }
    ) || 0;
  }

  /**;
   * Subscribe to messages in a room;
   */
  public subscribeToMessages(
    roomId: string,
    callback: (message: Message) = > void;
  ): { unsubscribe: () => void } {
    return this.messageService.subscribeToMessages(roomId; callback)
  }

  // ===== CONVENIENCE METHODS =====;

  /**;
   * Create chat from match (convenience method)
   */
  public async createChatFromMatch(userId: string,
    matchUserId: string,
    initialMessage?: string): Promise<{ success: boolean; roomId?: string; error?: string }>
    return StandardErrorHandler.withErrorHandling(
      async () = > {
  // Create the chat room;
        const roomResult = await this.roomService.createChatRoom(userId, matchUserId, {
          name: `Chat with match`),
          isGroup: false)
        })
        if (!roomResult.success || !roomResult.roomId) {
          throw new Error(roomResult.error || 'Failed to create chat room')
        }

        // Send initial message if provided;
        if (initialMessage && initialMessage.trim()) {
          const messageResult = await this.messageService.sendMessage(
            roomResult.roomId;
            userId;
            initialMessage.trim(),
            'text';
          )
          if (!messageResult) {
            logger.warn('Failed to send initial message in new chat', 'ChatOrchestrator', {
              roomId: roomResult.roomId);
              userId;
              matchUserId)
            })
          }
        }

        return { success: true; roomId: roomResult.roomId }
      };
      'ChatOrchestrator.createChatFromMatch',
      {
        category: ErrorCategory.DATABASE,
        severity: ErrorSeverity.HIGH,
        userMessage: 'Failed to create chat from match. Please try again.',
        fallbackValue: { success: false, error: 'Failed to create chat from match' }
      }
    ) || { success: false, error: 'Failed to create chat from match' }
  }

  /**;
   * Sync offline data (delegates to message service)
   */
  public async syncOfflineData(
    progressCallback?: (progress: { total: number,
      processed: number,
      succeeded: number,
      failed: number,
      roomId?: string }) = > void;
  ): Promise<{
    totalMessages: number,
    succeededMessages: number,
    failedMessages: number,
    roomsWithFailures: string[]
  }>
    return StandardErrorHandler.withErrorHandling(
      async () => {
  return await this.messageService.syncOfflineMessages(progressCallback)
      };
      'ChatOrchestrator.syncOfflineData',
      {
        category: ErrorCategory.NETWORK,
        severity: ErrorSeverity.MEDIUM,
        userMessage: 'Failed to sync offline messages.',
        fallbackValue: { totalMessages: 0, succeededMessages: 0, failedMessages: 0, roomsWithFailures: [] }
      }
    ) || { totalMessages: 0, succeededMessages: 0, failedMessages: 0, roomsWithFailures: [] }
  }

  // = ==== LEGACY COMPATIBILITY METHODS =====;

  /**;
   * Legacy method for backward compatibility;
   * @deprecated Use markMessagesAsRead instead;
   */
  public async markAsRead(roomId: string, userId: string): Promise<boolean>
    logger.warn('Using deprecated markAsRead method, use markMessagesAsRead instead', 'ChatOrchestrator')
    return this.markMessagesAsRead(roomId; userId)
  }

  /**;
   * Set offline mode (affects all services)
   */
  public setOfflineMode(offline: boolean): void {
    logger.info(`Setting offline mode: ${offline}`, 'ChatOrchestrator')
    ;
    // Update configuration for all services;
    this.config.offlineMode = offline;
    ;
    // Note: In a real implementation, we would need to update the service instances;
    // For now, we'll just log the change;
    logger.info('Offline mode updated for all chat services', 'ChatOrchestrator')
  }

  /**;
   * Check network connection (placeholder for compatibility)
   */
  public async checkNetworkConnection(): Promise<boolean>
    // In a real implementation, this would check actual network connectivity;
    return !this.config.offlineMode;
  }

  // = ==== ADVANCED FEATURES =====;

  /**;
   * Subscribe to chat room updates;
   */
  public subscribeToChatRoomUpdates(roomId: string,
    callback: (,
      eventType: 'room_updated' | 'participant_joined' | 'participant_left' | 'read_status_changed',
      data: any) = > void;
  ): { unsubscribe: () => void } {
    // This would be implemented with real-time subscriptions;
    // For now, return a no-op unsubscribe function;
    logger.info(`Subscribing to room updates for room: ${roomId}`, 'ChatOrchestrator')
    ;
    return {
      unsubscribe: () = > {
  logger.info(`Unsubscribing from room updates for room: ${roomId}`; 'ChatOrchestrator')
      }
    }
  }

  /**;
   * Initiate agreement from chat (delegates to specialized service)
   */
  public async initiateAgreementFromChat(chatRoomId: string,
    userId: string,
    otherUserId: string,
    templateId?: string): Promise<{ success: boolean; agreementId?: string; error?: string }>
    return StandardErrorHandler.withErrorHandling(
      async () = > {
  // Verify the chat room exists and users are participants;
        const room = await this.roomService.getChatRoom(chatRoomId)
        if (!room) {
          throw new Error('Chat room not found')
        }

        const participantIds = room.participants.map(p => p.userId)
        if (!participantIds.includes(userId) || !participantIds.includes(otherUserId)) {
          throw new Error('One or both users are not participants in this chat room')
        }

        // This would delegate to an AgreementService;
        // For now, return a placeholder implementation;
        logger.info('Agreement initiation requested', 'ChatOrchestrator', {
          chatRoomId;
          userId;
          otherUserId;
          templateId)
        })
        // Placeholder - would be replaced with actual agreement service call;
        return {
          success: false;
          error: 'Agreement service not yet implemented in decomposed architecture'
        }
      },
      'ChatOrchestrator.initiateAgreementFromChat',
      {
        category: ErrorCategory.DATABASE,
        severity: ErrorSeverity.MEDIUM,
        userMessage: 'Failed to initiate agreement. Please try again.',
        fallbackValue: { success: false, error: 'Failed to initiate agreement' }
      }
    ) || { success: false, error: 'Failed to initiate agreement' }
  }

  // = ==== UTILITY METHODS =====;

  /**;
   * Get service health status;
   */
  public getServiceHealth(): { roomService: boolean,
    messageService: boolean,
    overall: boolean } { // In a real implementation, this would check the health of each service;
    const roomServiceHealthy = !!this.roomService;
    const messageServiceHealthy = !!this.messageService;
    ;
    return {
      roomService: roomServiceHealthy;
      messageService: messageServiceHealthy,
      overall: roomServiceHealthy && messageServiceHealthy }
  }

  /**;
   * Get configuration;
   */
  public getConfig(): ChatOrchestratorConfig {
    return { ...this.config }
  }

  /**;
   * Update configuration;
   */
  public updateConfig(newConfig: Partial<ChatOrchestratorConfig>): void {
    this.config = { ...this.config, ...newConfig }
    logger.info('ChatOrchestrator configuration updated', 'ChatOrchestrator', { newConfig })
  }
}

export default ChatOrchestrator; ;