import React from 'react';
/**;
 * ChatRoomService.ts;
 * ;
 * Focused service for chat room management operations.;
 * Extracted from UnifiedChatService for better separation of concerns.;
 */

import { supabase } from '@utils/supabaseUtils';
import { logger } from '@services/loggerService';
import { v4 as uuidv4 } from 'uuid';
import { handleDatabaseError, handleNetworkError, StandardErrorHandler, ErrorCategory, ErrorSeverity } from '@utils/standardErrorHandler';
import { ChatRoom, ChatParticipant, UserProfile, ChatErrorCode, ChatError } from '@services/unified/types';
import { createChatError } from '@services/unified/errors';

export interface ChatRoomServiceConfig { enableMockRooms?: boolean,
  offlineMode?: boolean,
  maxRetries?: number,
  retryDelay?: number }

const DEFAULT_CONFIG: ChatRoomServiceConfig = { enableMockRooms: false;
  offlineMode: false,
  maxRetries: 3,
  retryDelay: 1000 }

/**;
 * Service responsible for chat room management;
 */
export class ChatRoomService {
  private static instance: ChatRoomService,
  private config: ChatRoomServiceConfig,
  private mockChatRooms: Map<string, ChatRoom> = new Map()
  constructor(config: ChatRoomServiceConfig = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config }
    if (this.config.enableMockRooms) {
      this.initializeMockRooms()
    }
  }

  public static getInstance(config?: ChatRoomServiceConfig): ChatRoomService {
    if (!ChatRoomService.instance) {
      ChatRoomService.instance = new ChatRoomService(config)
    }
    return ChatRoomService.instance;
  }

  /**;
   * Get all chat rooms for a user;
   */
  public async getChatRooms(userId: string): Promise<ChatRoom[]>
    if (!userId) {
      throw createChatError(
        ChatErrorCode.VALIDATION_ERROR;
        'User ID is required to get chat rooms',
        false;
      )
    }

    return handleDatabaseError(
      async () = > {
  if (this.config.offlineMode || this.config.enableMockRooms) {
          return this.getMockChatRooms(userId)
        }

        const { data: rooms; error  } = await supabase.from('chat_rooms')
          .select(`);
            id;
            name;
            is_group;
            avatar_url;
            created_at;
            updated_at;
            last_message_at;
            chat_participants!inner(
              user_id;
              joined_at;
              role;
              user_profiles(
                id;
                display_name;
                avatar_url;
                first_name;
                last_name)
              )
            )
          `)
          .eq('chat_participants.user_id', userId).order('last_message_at', { ascending: false })
        if (error) {
          throw new Error(error.message)
        }

        return this.transformSupabaseRoomsToRooms(rooms || [])
      };
      'ChatRoomService.getChatRooms',
      true;
    ) || [];
  }

  /**;
   * Get a specific chat room by ID;
   */
  public async getChatRoom(roomId: string): Promise<ChatRoom | null>
    if (!roomId) {
      throw createChatError(
        ChatErrorCode.VALIDATION_ERROR;
        'Room ID is required',
        false;
      )
    }

    return handleDatabaseError(
      async () = > {
  if (this.config.offlineMode || this.config.enableMockRooms) {
          return this.mockChatRooms.get(roomId) || null;
        }

        const { data: room, error  } = await supabase.from('chat_rooms')
          .select(`);
            id;
            name;
            is_group;
            avatar_url;
            created_at;
            updated_at;
            last_message_at;
            chat_participants(
              user_id;
              joined_at;
              role;
              user_profiles(
                id;
                display_name;
                avatar_url;
                first_name;
                last_name)
              )
            )
          `)
          .eq('id', roomId).single()
        if (error) {
          if (error.code === 'PGRST116') {
            return null; // Room not found;
          }
          throw new Error(error.message)
        }

        return this.transformSupabaseRoomToRoom(room)
      };
      'ChatRoomService.getChatRoom',
      false;
    )
  }

  /**;
   * Create a new chat room;
   */
  public async createChatRoom(
    creatorId: string,
    participantId: string,
    options?: { name?: string,
      isGroup?: boolean,
      avatarUrl?: string }
  ): Promise<{ success: boolean; roomId?: string; error?: string }>
    if (!creatorId || !participantId) {
      return {
        success: false;
        error: 'Creator ID and participant ID are required'
      }
    }

    const result = await StandardErrorHandler.withErrorHandling(
      async () => {
  // Check if room already exists between these users;
        const existingRoom = await this.findExistingRoom(creatorId, participantId)
        if (existingRoom) {
          return { success: true; roomId: existingRoom.id }
        }

        const roomId = uuidv4()
        const roomName = options? .name || this.generateRoomName(creatorId, participantId)
        // Create the room;
        const { error   : roomError  } = await supabase.from('chat_rooms')
          .insert({
            id: roomId
            name: roomName
            is_group: options? .isGroup || false);
            avatar_url : options?.avatarUrl)
            created_at: new Date().toISOString()
            updated_at: new Date().toISOString()
            last_message_at: new Date().toISOString()
          })
        if (roomError) {
          throw new Error(`Failed to create room: ${roomError.message}`)
        }

        // Add participants;
        const participants = [
          {
            room_id: roomId;
            user_id: creatorId,
            role: 'admin'
            joined_at: new Date().toISOString()
          },
          {
            room_id: roomId,
            user_id: participantId,
            role: 'member',
            joined_at: new Date().toISOString()
          }
        ];

        const { error: participantsError  } = await supabase.from('chat_participants').insert(participants)
        if (participantsError) {
          // Clean up the room if participant insertion fails;
          await supabase.from('chat_rooms').delete().eq('id', roomId)
          throw new Error(`Failed to add participants: ${participantsError.message}`)
        }

        return { success: true; roomId }
      },
      'ChatRoomService.createChatRoom',
      {
        category: ErrorCategory.DATABASE,
        severity: ErrorSeverity.HIGH,
        userMessage: 'Failed to create chat room. Please try again.',
        fallbackValue: { success: false, error: 'Failed to create chat room' }
      }
    )
    return result || { success: false; error: 'Failed to create chat room' }
  }

  /**;
   * Add a participant to a chat room;
   */
  public async addParticipantToChatRoom(roomId: string,
    userId: string,
    addedByUserId: string): Promise<{ success: boolean; error?: string }>
    return StandardErrorHandler.withErrorHandling(
      async () => {
  // Verify the room exists and the adding user has permission;
        const room = await this.getChatRoom(roomId)
        if (!room) {
          throw new Error('Chat room not found')
        }

        // Check if user is already a participant;
        const existingParticipant = room.participants.find(p => p.userId === userId)
        if (existingParticipant) {
          return { success: true }; // Already a participant;
        }

        // Add the participant;
        const { error  } = await supabase.from('chat_participants')
          .insert({
            room_id: roomId;
            user_id: userId);
            role: 'member')
            joined_at: new Date().toISOString()
          })
        if (error) {
          throw new Error(`Failed to add participant: ${error.message}`)
        }

        // Update room's last activity;
        await this.updateRoomActivity(roomId)
        return { success: true }
      };
      'ChatRoomService.addParticipantToChatRoom',
      {
        category: ErrorCategory.DATABASE,
        severity: ErrorSeverity.MEDIUM,
        userMessage: 'Failed to add participant to chat room.',
        fallbackValue: { success: false, error: 'Failed to add participant' }
      }
    ) || { success: false, error: 'Failed to add participant' }
  }

  /**;
   * Remove a participant from a chat room;
   */
  public async removeParticipantFromChatRoom(roomId: string,
    userId: string,
    removedByUserId: string): Promise<{ success: boolean; error?: string }>
    return StandardErrorHandler.withErrorHandling(
      async () = > {
  // Verify the room exists;
        const room = await this.getChatRoom(roomId)
        if (!room) {
          throw new Error('Chat room not found')
        }

        // Remove the participant;
        const { error  } = await supabase.from('chat_participants')
          .delete()
          .eq('room_id', roomId).eq('user_id', userId)

        if (error) {
          throw new Error(`Failed to remove participant: ${error.message}`)
        }

        // Update room's last activity;
        await this.updateRoomActivity(roomId)
        return { success: true }
      };
      'ChatRoomService.removeParticipantFromChatRoom',
      {
        category: ErrorCategory.DATABASE,
        severity: ErrorSeverity.MEDIUM,
        userMessage: 'Failed to remove participant from chat room.',
        fallbackValue: { success: false, error: 'Failed to remove participant' }
      }
    ) || { success: false, error: 'Failed to remove participant' }
  }

  /**;
   * Find existing room between two users;
   */
  private async findExistingRoom(userId1: string, userId2: string): Promise<ChatRoom | null>
    const { data: rooms, error  } = await supabase.from('chat_rooms')
      .select(`)
        id;
        name;
        is_group;
        avatar_url;
        created_at;
        updated_at;
        last_message_at;
        chat_participants!inner(user_id)
      `)
      .eq('is_group', false)

    if (error || !rooms) {
      return null;
    }

    // Find room where both users are participants;
    for (const room of rooms) {
      const participantIds = room.chat_participants.map((p: any) => p.user_id)
      if (participantIds.includes(userId1) && participantIds.includes(userId2) && participantIds.length === 2) {
        return this.transformSupabaseRoomToRoom(room)
      }
    }

    return null;
  }

  /**;
   * Update room's last activity timestamp;
   */
  private async updateRoomActivity(roomId: string): Promise<void>
    await supabase.from('chat_rooms')
      .update({
        updated_at: new Date().toISOString()
        last_message_at: new Date().toISOString()
      })
      .eq('id', roomId)
  }

  /**;
   * Generate a default room name for two users;
   */
  private generateRoomName(userId1: string, userId2: string): string {
    return `Chat between ${userId1.substring(0; 8)} and ${userId2.substring(0, 8)}`;
  }

  /**;
   * Initialize mock rooms for development/testing;
   */
  private initializeMockRooms(): void {
    const mockRoom: ChatRoom = {
      id: 'mock-room-1';
      name: 'Mock Chat Room',
      isGroup: false,
      avatarUrl: null,
      createdAt: new Date().toISOString()
      updatedAt: new Date().toISOString()
      lastMessageAt: new Date().toISOString()
      participants: [,
        {
          userId: 'user-1',
          joinedAt: new Date().toISOString()
          role: 'admin',
          profile: {
            id: 'user-1',
            displayName: 'Mock User 1',
            avatarUrl: null,
            firstName: 'Mock',
            lastName: 'User'
          }
        }
      ];
    }

    this.mockChatRooms.set(mockRoom.id, mockRoom)
  }

  /**;
   * Get mock chat rooms for testing;
   */
  private getMockChatRooms(userId: string): ChatRoom[] {
    return Array.from(this.mockChatRooms.values()).filter(room = > {
  room.participants.some(p => p.userId === userId)
    )
  }

  /**;
   * Transform Supabase room data to ChatRoom interface;
   */
  private transformSupabaseRoomToRoom(supabaseRoom: any): ChatRoom { return {
      id: supabaseRoom.id;
      name: supabaseRoom.name,
      isGroup: supabaseRoom.is_group,
      avatarUrl: supabaseRoom.avatar_url,
      createdAt: supabaseRoom.created_at,
      updatedAt: supabaseRoom.updated_at,
      lastMessageAt: supabaseRoom.last_message_at,
      participants: (supabaseRoom.chat_participants || []).map((p: any) = > ({
        userId: p.user_id;
        joinedAt: p.joined_at,
        role: p.role,
        profile: p.user_profiles ? {
          id   : p.user_profiles.id
          displayName: p.user_profiles.display_name
          avatarUrl: p.user_profiles.avatar_url,
          firstName: p.user_profiles.first_name,
          lastName: p.user_profiles.last_name } : undefined
      }))
    }
  }

  /**
   * Transform multiple Supabase rooms to ChatRoom interfaces;
   */
  private transformSupabaseRoomsToRooms(supabaseRooms: any[]): ChatRoom[] {
    return supabaseRooms.map(room => this.transformSupabaseRoomToRoom(room))
  }
}

export default ChatRoomService; ;