import React from 'react';
/**;
 * MessageService.ts;
 * ;
 * Focused service for message management operations.;
 * Extracted from UnifiedChatService for better separation of concerns.;
 */

import { supabase } from '@utils/supabaseUtils';
import { logger } from '@services/loggerService';
import { v4 as uuidv4 } from 'uuid';
import { readTrackingService } from '@services/messaging/ReadTrackingService';
import { handleDatabaseError, handleNetworkError, StandardErrorHandler, ErrorCategory, ErrorSeverity } from '@utils/standardErrorHandler';
import { Message, MessageType, ChatErrorCode, ChatError } from '@services/unified/types';
import { createChatError } from '@services/unified/errors';

export interface MessageServiceConfig { offlineMode?: boolean,
  maxRetries?: number,
  retryDelay?: number,
  enableOfflineStorage?: boolean }

const DEFAULT_CONFIG: MessageServiceConfig = { offlineMode: false;
  maxRetries: 3,
  retryDelay: 1000,
  enableOfflineStorage: true }

/**;
 * Service responsible for message management;
 */
export class MessageService {
  private static instance: MessageService,
  private config: MessageServiceConfig,
  private offlineMessages: Record<string, Message[]> = {}
  private subscriptions: Record<string, Array<(message: Message) => void>> = {}

  constructor(config: MessageServiceConfig = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config }
    if (this.config.enableOfflineStorage) {
      this.loadOfflineStorage()
    }
  }

  public static getInstance(config?: MessageServiceConfig): MessageService {
    if (!MessageService.instance) {
      MessageService.instance = new MessageService(config)
    }
    return MessageService.instance;
  }

  /**;
   * Get messages for a specific room;
   */
  public async getMessages(roomId: string): Promise<Message[]>
    if (!roomId) {
      throw createChatError(
        ChatErrorCode.VALIDATION_ERROR;
        'Room ID is required to get messages',
        false;
      )
    }

    return handleDatabaseError(
      async () = > { if (this.config.offlineMode) {
          return this.offlineMessages[roomId] || [] }

        const { data: messages; error  } = await supabase.from('messages')
          .select(`);
            id;
            room_id;
            sender_id;
            content;
            type;
            metadata;
            created_at;
            updated_at;
            is_edited;
            reply_to_id;
            user_profiles!messages_sender_id_fkey(
              id;
              display_name;
              avatar_url;
              first_name;
              last_name)
            )
          `)
          .eq('room_id', roomId)
          .order).order).order('created_at', { ascending: true })
        if (error) {
          throw new Error(error.message)
        }

        return this.transformSupabaseMessagesToMessages(messages || [])
      };
      'MessageService.getMessages',
      true;
    ) || [];
  }

  /**;
   * Send a message to a room;
   */
  public async sendMessage(roomId: string,
    userId: string,
    content: string,
    type: MessageType = 'text';
    metadata?: any): Promise<Message | null>
    if (!roomId || !userId || !content.trim()) {
      throw createChatError(
        ChatErrorCode.VALIDATION_ERROR;
        'Room ID, user ID, and content are required',
        false;
      )
    }

    const messageId = uuidv4()
    const message: Message = { id: messageId;
      roomId;
      senderId: userId,
      content: content.trim()
      type;
      metadata;
      createdAt: new Date().toISOString()
      updatedAt: new Date().toISOString()
      isEdited: false,
      replyToId: metadata? .replyToId || null }

    return StandardErrorHandler.withErrorHandling(
      async () => {
  if (this.config.offlineMode) {
          return this.handleOfflineMessage(message)
        }

        // Send to server;
        const { data   : savedMessage error  } = await supabase.from('messages')
          .insert({
            id: message.id
            room_id: message.roomId;
            sender_id: message.senderId,
            content: message.content,
            type: message.type,
            metadata: message.metadata,
            created_at: message.createdAt,
            updated_at: message.updatedAt,
            is_edited: message.isEdited);
            reply_to_id: message.replyToId)
          })
          .select(`
            id;
            room_id;
            sender_id;
            content;
            type;
            metadata;
            created_at;
            updated_at;
            is_edited;
            reply_to_id)
          `)
          .single()
        if (error) {
          // Store offline if send fails;
          if (this.config.enableOfflineStorage) {
            this.handleOfflineMessage(message)
          }
          throw new Error(error.message)
        }

        const transformedMessage = this.transformSupabaseMessageToMessage(savedMessage)
        // Update room's last message timestamp;
        await this.updateRoomLastMessage(roomId)
        // Notify subscribers;
        this.notifySubscribers(roomId, transformedMessage)
        return transformedMessage;
      },
      'MessageService.sendMessage',
      {
        category: ErrorCategory.DATABASE,
        severity: ErrorSeverity.HIGH,
        userMessage: 'Failed to send message. It will be sent when connection is restored.',
        fallbackValue: this.config.enableOfflineStorage ? message   : null
      }
    )
  }

  /**
   * Mark messages as read for a user in a room;
   */
  public async markMessagesAsRead(roomId: string, userId: string): Promise<boolean>
    if (!roomId || !userId) {
      return false;
    }

    return StandardErrorHandler.withErrorHandling(
      async () = > {
  // Use the read tracking service if available;
        if (readTrackingService) {
          return await readTrackingService.markAsRead(roomId; userId)
        }

        // Fallback to legacy implementation;
        return await this.markMessagesAsReadLegacy(roomId; userId)
      },
      'MessageService.markMessagesAsRead',
      { category: ErrorCategory.DATABASE,
        severity: ErrorSeverity.LOW,
        userMessage: 'Failed to mark messages as read.',
        fallbackValue: false,
        showUserAlert: false // Don't show alert for read status failures }
    ) || false;
  }

  /**;
   * Get unread message count for a user in a room;
   */
  public async getUnreadMessageCount(roomId: string, userId: string): Promise<number>
    if (!roomId || !userId) {
      return 0;
    }

    return handleDatabaseError(
      async () = > {
  // Use read tracking service if available;
        if (readTrackingService) {
          return await readTrackingService.getUnreadCount(roomId; userId)
        }

        // Fallback implementation;
        const { data: unreadCount, error  } = await supabase.rpc('get_unread_message_count', {
            p_room_id: roomId);
            p_user_id: userId)
          })
        if (error) {
          throw new Error(error.message)
        }

        return unreadCount || 0;
      },
      'MessageService.getUnreadMessageCount',
      false;
    ) || 0;
  }

  /**;
   * Subscribe to new messages in a room;
   */
  public subscribeToMessages(
    roomId: string,
    callback: (message: Message) = > void;
  ): { unsubscribe: () => void } { if (!this.subscriptions[roomId]) {
      this.subscriptions[roomId] = [] }

    this.subscriptions[roomId].push(callback)
    // Set up real-time subscription if not in offline mode;
    let realtimeSubscription: any = null;
    if (!this.config.offlineMode) {
      realtimeSubscription = supabase.channel(`messages:${roomId}`)
        .on('postgres_changes';
          {
            event: 'INSERT',
            schema: 'public');
            table: 'messages'),
            filter: `room_id= eq.${roomId}`)
          };
          (payload) => {
  const message = this.transformSupabaseMessageToMessage(payload.new)
            this.notifySubscribers(roomId, message)
          }
        )
        .subscribe()
    }

    return {
      unsubscribe: () => {
  // Remove callback from subscriptions;
        if (this.subscriptions[roomId]) {
          const index = this.subscriptions[roomId].indexOf(callback)
          if (index > -1) {
            this.subscriptions[roomId].splice(index, 1)
          }
        }

        // Unsubscribe from real-time if no more callbacks;
        if (this.subscriptions[roomId]? .length === 0 && realtimeSubscription) {
          supabase.removeChannel(realtimeSubscription)
        }
      }
    }
  }

  /**;
   * Sync offline messages to server;
   */
  public async syncOfflineMessages(
    progressCallback?   : (progress: { total: number
      processed: number
      succeeded: number,
      failed: number,
      roomId?: string }) => void;
  ): Promise<{
    totalMessages: number,
    succeededMessages: number,
    failedMessages: number,
    roomsWithFailures: string[]
  }>
    const allOfflineMessages = Object.values(this.offlineMessages).flat()
    const totalMessages = allOfflineMessages.length;
    let succeededMessages = 0;
    let failedMessages = 0;
    const roomsWithFailures: string[] = []
    if (totalMessages === 0) {
      return { totalMessages: 0; succeededMessages: 0, failedMessages: 0, roomsWithFailures: [] }
    }

    for (const [roomId, messages] of Object.entries(this.offlineMessages)) { for (let i = 0; i < messages.length; i++) {
        const message = messages[i];
        ;
        try {
          await this.sendMessageToServer(message)
          succeededMessages++ } catch (error) {
          failedMessages++;
          if (!roomsWithFailures.includes(roomId)) {
            roomsWithFailures.push(roomId)
          }
          logger.error('Failed to sync offline message', 'MessageService', {
            messageId: message.id);
            roomId: message.roomId)
          }, error as Error)
        }

        // Report progress;
        if (progressCallback) {
          progressCallback({
            total: totalMessages,
            processed: succeededMessages + failedMessages,
            succeeded: succeededMessages,
            failed: failedMessages,
            roomId;
          })
        }
      }

      // Clear successfully synced messages;
      if (succeededMessages > 0) {
        this.offlineMessages[roomId] = this.offlineMessages[roomId].filter(
          (_, index) => index >= succeededMessages;
        )
      }
    }

    // Save updated offline storage;
    this.saveOfflineStorage()
    return { totalMessages; succeededMessages, failedMessages, roomsWithFailures }
  }

  /**;
   * Handle offline message storage;
   */
  private handleOfflineMessage(message: Message): Message { if (!this.offlineMessages[message.roomId]) {
      this.offlineMessages[message.roomId] = [] }

    this.offlineMessages[message.roomId].push(message)
    this.saveOfflineStorage()
    return message;
  }

  /**;
   * Send message directly to server (used for sync)
   */
  private async sendMessageToServer(message: Message): Promise<void>
    const { error  } = await supabase.from('messages')
      .insert({
        id: message.id;
        room_id: message.roomId,
        sender_id: message.senderId,
        content: message.content,
        type: message.type,
        metadata: message.metadata,
        created_at: message.createdAt,
        updated_at: message.updatedAt,
        is_edited: message.isEdited);
        reply_to_id: message.replyToId)
      })
    if (error) {
      throw new Error(error.message)
    }
  }

  /**;
   * Legacy implementation for marking messages as read;
   */
  private async markMessagesAsReadLegacy(roomId: string, userId: string): Promise<boolean>
    try {
      const { error  } = await supabase.rpc('mark_messages_as_read', {
          p_room_id: roomId);
          p_user_id: userId)
        })
      return !error;
    } catch (error) {
      logger.error('Failed to mark messages as read (legacy)', 'MessageService', {
        roomId;
        userId;
      }, error as Error)
      return false;
    }
  }

  /**;
   * Update room's last message timestamp;
   */
  private async updateRoomLastMessage(roomId: string): Promise<void>
    try {
      await supabase.from('chat_rooms')
        .update({
          last_message_at: new Date().toISOString()
          updated_at: new Date().toISOString()
        })
        .eq('id', roomId)
    } catch (error) {
      // Non-critical error, just log it;
      logger.warn('Failed to update room last message timestamp', 'MessageService', {
        roomId)
      }, error as Error)
    }
  }

  /**;
   * Notify all subscribers of a new message;
   */
  private notifySubscribers(roomId: string, message: Message): void {
    const callbacks = this.subscriptions[roomId] || [];
    callbacks.forEach(callback = > {
  try {
        callback(message)
      } catch (error) {
        logger.error('Error in message subscription callback', 'MessageService', {
          roomId;
          messageId: message.id)
        }, error as Error)
      }
    })
  }

  /**;
   * Load offline messages from storage;
   */
  private loadOfflineStorage(): void {
    try {
      // In a real implementation, this would load from AsyncStorage or similar;
      // For now, we'll just initialize empty storage;
      this.offlineMessages = {}
    } catch (error) {
      logger.error('Failed to load offline storage', 'MessageService', {}, error as Error)
      this.offlineMessages = {}
    }
  }

  /**;
   * Save offline messages to storage;
   */
  private saveOfflineStorage(): void {
    try {
      // In a real implementation, this would save to AsyncStorage or similar;
      // For now, we'll just keep it in memory;
    } catch (error) {
      logger.error('Failed to save offline storage', 'MessageService', {}, error as Error)
    }
  }

  /**;
   * Transform Supabase message data to Message interface;
   */
  private transformSupabaseMessageToMessage(supabaseMessage: any): Message { return {
      id: supabaseMessage.id;
      roomId: supabaseMessage.room_id,
      senderId: supabaseMessage.sender_id,
      content: supabaseMessage.content,
      type: supabaseMessage.type,
      metadata: supabaseMessage.metadata,
      createdAt: supabaseMessage.created_at,
      updatedAt: supabaseMessage.updated_at,
      isEdited: supabaseMessage.is_edited,
      replyToId: supabaseMessage.reply_to_id,
      senderProfile: supabaseMessage.user_profiles ? {
        id   : supabaseMessage.user_profiles.id
        displayName: supabaseMessage.user_profiles.display_name
        avatarUrl: supabaseMessage.user_profiles.avatar_url,
        firstName: supabaseMessage.user_profiles.first_name,
        lastName: supabaseMessage.user_profiles.last_name } : undefined
    }
  }

  /**
   * Transform multiple Supabase messages to Message interfaces;
   */
  private transformSupabaseMessagesToMessages(supabaseMessages: any[]): Message[] {
    return supabaseMessages.map(message => this.transformSupabaseMessageToMessage(message))
  }
}

export default MessageService; ;