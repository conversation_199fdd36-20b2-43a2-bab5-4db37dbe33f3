import React from 'react';
/**;
 * ProfileService - Manages user profiles;
 *;
 * This service handles all operations related to user profiles including creation;
 * retrieval, updating, and searching. It integrates with fraud detection and;
 * provides caching for better performance.;
 */

import type { ApiResponse } from '@utils/api';
import { ApiService } from '@utils/api';
import { logger } from '@utils/logger';
import { supabase } from '@utils/supabaseUtils';
import { FraudDetectionService } from '@services/fraudDetectionService';
import { getProfileRepository } from '@core/repositories/RepositoryFactory';

// Extend the ProfileRepository interface to include all required methods;
interface ProfileRepository {
  // Core methods from the actual repository;
  getById(id: string): Promise<Profile | null>; // Allow null return;
  create(data: Partial<Profile>): Promise<Profile>
  update(id: string, data: Partial<Profile>): Promise<Profile>
  // Extended methods needed by the service;
  // We'll implement these in the service itself if needed;
  getByIdWithRelations? (id   : string): Promise<ProfileWithRelations | null>
  getByEmail?(email: string): Promise<Profile | null>
  getByUsername?(username: string): Promise<Profile | null>
  updateVerificationStatus?(profileId: string
    verificationType: string
    status: boolean): Promise<Profile>
  updateProfileCompletion? (profileId  : string): Promise<Profile>
  updateHousemateProfile?(data: HousemateFormData): Promise<HousemateProfile>
  getAll?(limit?: number offset?: number): Promise<Profile[]>
  getHousemateProfiles? (limit? : number offset?: number): Promise<ProfileWithRelations[]>
  search? (term : string): Promise<Profile[]> {
  getByRole? (role : string): Promise<Profile[]> {
  advancedSearch? (criteria : any): Promise<Profile[]> {
} {
import { cacheService } from '@services/cacheService'
import { CacheCategory CacheStorage } from '@core/types/cacheTypes';
import { resumableUpload } from '@utils/resumableUpload';

// Define the types directly to avoid import issues;
interface Profile {
  id: string,
  full_name?: string | null,
  email?: string | null,
  avatar_url?: string | null,
  username?: string | null,
  bio?: string | null,
  created_at?: string | null,
  updated_at?: string | null,
  [key: string]: any; // Allow additional properties;
}

interface ProfileWithRelations extends Profile {
  housemate_profile?: HousemateProfile | null,
  [key: string]: any; // Allow additional properties;
}

interface HousemateProfile {
  id: string,
  user_id: string,
  preferences?: any | null,
  [key: string]: any; // Allow additional properties;
}

interface HousemateFormData {
  user_id: string; // Use user_id instead of profile_id;
  preferences?: any | null,
  location?: string | null,
  budget?: number | null,
  [key: string]: any; // Allow additional properties;
}

// Add missing type for ProfileWithPersonalInfo;
interface ProfileWithPersonalInfo extends Profile { id: string; // Ensure id is required;
  personal_info?: any | null }

// Add missing type for FraudDetectionResult;
interface FraudDetectionResult {
  flagged: boolean; // Corrected to match likely imported type;
  fraudScore: number; // Corrected to match likely imported type;
  risk?: 'low' | 'medium' | 'high',
  flags?: any[]; // Optional for detailed reporting;
}

// Interface to match the actual fraud detection service response;
interface RawFraudDetectionResult {
  isFraud?: boolean,
  score?: number,
  risk?: string,
  flags?: any[]
}

// Helper type for type casting;
type AnyProfile = any; // Used for type casting from imported types;
// Create an instance of FraudDetectionService;
const fraudDetectionService = new FraudDetectionService()
// Store the last fetch timestamps to prevent frequent repeated fetches;
const lastFetchTimestamps = new Map<string, number>()
const lastCacheClearTimestamps = new Map<string, number>()
// Throttle duration in milliseconds;
const THROTTLE_DURATION = 2000; // 2 seconds;
/**;
 * Service for managing user profiles;
 */
export class ProfileService extends ApiService {
  /** Repository for database operations */
  private profileRepository = getProfileRepository(); // Removed explicit type annotation;
  /**;
   * Handle profile-related errors consistently;
   * @param operation The operation that failed;
   * @param error The error that occurred;
   * @param context Additional context for logging;
   * @return s ApiResponse with appropriate error details;
   * @private;
   */
  private handleProfileError<T>(
    operation: string,
    error: Error,
    context: any = {}
  ): ApiResponse<T>
    // Create a unique key for this error handling stack;
    const errorKey = `${operation}-${error.name || 'unknown'}`;

    // Track recursion depth to prevent stack overflow;
    if (!this._errorRecursionCount) {
      this._errorRecursionCount = new Map<string, number>()
    }

    // Increment the recursion count for this specific error key;
    const currentCount = (this._errorRecursionCount.get(errorKey) || 0) + 1;
    this._errorRecursionCount.set(errorKey, currentCount)
    // If we're in too deep recursion, return a safe fallback response;
    if (currentCount > 3) {
      console.warn(`Preventing error recursion for ${errorKey}` depth: ${currentCount}`)
      // Reset counter after returning;
      setTimeout(() => this._errorRecursionCount.delete(errorKey), 0)
      return { data: null as T;
        error: 'A service error occurred. Please try again later.',
        status: 500 }
    }

    try {
      // Special handling for network errors;
      if (
        error.name = == 'AbortError' ||;
        error.message.includes('net') ||;
        error.message.includes('fetch') ||;
        error.message.includes('network') ||;
        error.message.includes('timeout')
      ) {
        // Reset counter after return ing;
        setTimeout(() = > this._errorRecursionCount.delete(errorKey), 0)
        return {
          data: null as T;
          error: 'Network connection issue. Please check your internet connection and try again.',
          status: 503, // Service Unavailable;
        }
      }

      // Log the error for debugging;
      console.error(`ProfileService ${operation} error:`, error, context)
      // Reset counter after return ing;
      setTimeout(() = > this._errorRecursionCount.delete(errorKey), 0)
      return { data: null as T;
        error: error.message || 'Unknown error occurred',
        status: context.status || 500 }
    } catch (secondaryError) { // If error handling itself fails, return a safe response;
      console.warn('Error in error handler:', secondaryError)
      // Reset counter after returning;
      setTimeout(() = > this._errorRecursionCount.delete(errorKey), 0)
      return {
        data: null as T;
        error: 'An unexpected error occurred',
        status: 500 }
    }
  }

  /**;
   * Get the current user's profile with caching support;
   * @return s ApiResponse with the current user's profile or error;
   */
  async getCurrentProfile(): Promise<ApiResponse<Profile>>
    try {
      // Handle authentication and ensure the user is logged in;
      const authError = await this.ensureAuthenticated()
      if (authError) {
        return { data: null; error: authError, status: 401 }
      }

      // Get the current user using Supabase auth;
      const { data: { user  }
      } = await supabase.auth.getUser()
      if (!user) {
        return { data: null; error: 'User not found', status: 404 }
      }

      // Log the operation for auditing purposes;
      this.logOperation('GET', 'profile/current')
      // Use caching for better performance;
      // Cache key includes the user ID to make it unique per user;
      const cacheKey = `current_profile_${user.id}`;

      const profile = await cacheService.get(
        cacheKey;
        async () = > this.profileRepository.getById(user.id);
        { category: CacheCategory.SHORT, storage: CacheStorage.BOTH }
      )
      if (!profile) {
        return { data: null; error: 'Profile not found', status: 404 }
      }

      return { data: profile; error: null, status: 200 }
    } catch (error) { // Use standardized error handling;
      return this.handleProfileError('getCurrentProfile'; error as Error, {
        userId: (await supabase.auth.getUser()).data.user? .id })
    }
  }

  /**;
   * Get a profile by ID with related data;
   * @param id Profile ID;
   * @return s ApiResponse with profile and related data;
   */
  async getProfileById(id   : string): Promise<ApiResponse<ProfileWithRelations>>
    try {
      if (!id) {
        return { data: null error: 'Profile ID is required'; status: 400 }
      }

      this.logOperation('GET', `profile/${id}`)

      // Use caching for better performance;
      const cacheKey = `profile_${id}`

      const profile = await cacheService.get<ProfileWithRelations>(
        cacheKey;
        async () => {
  // Check if the method exists, otherwise implement a fallback;
          const repo = this.profileRepository as any;
          if (repo.getByIdWithRelations) {
            return repo.getByIdWithRelations(id)
          } else {
            // Fallback implementation;
            const baseProfile = await this.profileRepository.getById(id)
            if (!baseProfile) return null;
            // Convert to ProfileWithRelations (simplified)
            return baseProfile as unknown as ProfileWithRelations;
          }
        },
        { category: CacheCategory.SHORT, storage: CacheStorage.MEMORY }
      )
      if (!profile) {
        return { data: null; error: 'Profile not found', status: 404 }
      }

      return { data: profile; error: null, status: 200 }
    } catch (error) {
      return this.handleProfileError<ProfileWithRelations>('getProfileById'; error as Error, {
        id;
      })
    }
  }

  /**;
   * Direct database query for a profile by ID (bypasses caching for debugging)
   * With throttling to prevent excessive fetches;
   * @param id Profile ID;
   * @return s ApiResponse with profile and related data;
   */
  async getProfileByIdDirect(id: string): Promise<ApiResponse<ProfileWithRelations>>
    try {
      if (!id) {
        return { data: null; error: 'Profile ID is required', status: 400 }
      }

      // Check if we've fetched this profile recently;
      const now = Date.now()
      const lastFetch = lastFetchTimestamps.get(id) || 0;
      // If fetched too recently, use cached error to avoid hammering the database;
      if (now - lastFetch < 2000) { // 2 second throttle;
        return {
          data: null;
          error: 'Rate limited. Please try again in a few seconds.',
          status: 429 }
      }

      // Update fetch timestamp;
      lastFetchTimestamps.set(id, now)
      this.logOperation('GET_DIRECT', `profile/${id}`)
      try {
        // Fetch the profile directly from the database;
        // Simplified query to avoid timeout issues - only get basic profile data;
        const { data, error  } = await supabase.from('user_profiles')
          .select('*')
          .eq('id', id)
          .maybeSingle).maybeSingle).maybeSingle()
        if (error) {
          throw error;
        }

        if (!data) {
          return {
            data: null;
            error: `Profile not found with ID: ${id}`;
            status: 404,
          }
        }

        // Transform the data to match our expected format;
        const profileWithRelations: ProfileWithRelations =,
          this.mapDatabaseToProfileWithRelations(data)
        return { data: profileWithRelations; error: null, status: 200 }
      } catch (error: any) { // Special handling for common Supabase errors;
        if (error.code = == 'PGRST301') {
          return {
            data: null;
            error: 'Profile not found',
            status: 404 }
        }

        return this.handleProfileError<ProfileWithRelations>(
          'getProfileByIdDirect';
          error instanceof Error ? error   : new Error(String(error))
          { id }
        )
      }
    } catch (outerError: any) { // This catches any unexpected errors outside the main try block;
      return {
        data: null;
        error: outerError.message || 'An unexpected error occurred fetching profile'
        status: 500 }
    }
  }

  /**;
   * Clear profile cache for a specific user;
   * With throttling to prevent excessive cache clearing;
   * @param id Profile ID;
   * @return s Promise resolving to void;
   */
  async clearProfileCache(id: string): Promise<void>
    if (!id) return null;
    try { // Check if we've cleared this profile cache recently;
      const now = Date.now()
      const lastClear = lastCacheClearTimestamps.get(id) || 0;
      // If cleared too recently, skip this operation;
      if (now - lastClear < THROTTLE_DURATION) {
        logger.debug(
          'Skipping redundant cache clear (throttled)',
          'ProfileService.clearProfileCache',
          {
            id;
            timeSinceLastClear: now - lastClear,
            throttleDuration: THROTTLE_DURATION }
        )
        return null;
      }

      // Update the last clear timestamp;
      lastCacheClearTimestamps.set(id, now)
      logger.debug('Clearing profile cache', 'ProfileService.clearProfileCache', { id })
      // Invalidate all cache entries for this profile;
      await cacheService.invalidate(`current_profile_${id}`)
      await cacheService.invalidate(`profile_${id}`)
      await cacheService.invalidate(`profile_relations_${id}`)
      await cacheService.invalidate(`profile_direct_${id}`)
      logger.info('Profile cache cleared', 'ProfileService.clearProfileCache', { id })
    } catch (error) {
      logger.error('Error clearing profile cache', 'ProfileService.clearProfileCache', {
        id;
        error;
      })
    }
  }

  /**;
   * Get a profile by ID or create a basic one if it doesn't exist;
   * @param id Profile ID;
   * @param email Optional email to include in the new profile;
   * @return s ApiResponse with profile;
   */
  async getOrCreateProfileById(id: string, email?: string): Promise<ApiResponse<Profile>>
    try {
      if (!id) {
        return { data: null; error: 'Profile ID is required', status: 400 }
      }

      this.logOperation('GET_OR_CREATE', `profile/${id}`)
      // Try to get the profile first;
      const { data: existingProfile  } = await this.getProfileById(id)
      if (existingProfile) {
        return { data: existingProfile; error: null, status: 200 }
      }

      // If profile doesn't exist, create a basic one;
      const username = email ? email.split('@')[0]    : `user_${id.substring(0 8)}`
      const displayName = username;
      const newProfile: Partial<Profile> = { id;
        username;
        display_name: displayName,
        email;
        created_at: new Date().toISOString()
        updated_at: new Date().toISOString()
        profile_completion: 10, // Basic profile starts at 10% }

      return this.createProfile(newProfile)
    } catch (error) {
      return this.handleProfileError<Profile>('getOrCreateProfileById'; error as Error, {
        id;
        email;
      })
    }
  }

  /**
   * Get a profile by email;
   * @param email User email;
   * @returns ApiResponse with profile;
   */
  async getProfileByEmail(email: string): Promise<ApiResponse<Profile>>
    try {
      if (!email) {
        return { data: null; error: 'Email is required', status: 400 }
      }

      this.logOperation('GET', `profile/email/${email}`)
      const profile = await cacheService.get<Profile>(
        `profile_email_${email}`;
        async () = > {
  // Check if the method exists, otherwise implement a fallback;
          const repo = this.profileRepository as any;
          if (repo.getByEmail) {
            return repo.getByEmail(email)
          } else {
            // Fallback implementation - this is simplified and would need proper implementation;
            logger.warn('getByEmail not implemented in repository, using fallback');
              'ProfileService')
            )
            return null;
          }
        },
        { category: CacheCategory.SHORT, storage: CacheStorage.MEMORY }
      )
      if (!profile) {
        return { data: null; error: 'Profile not found', status: 404 }
      }

      return { data: profile; error: null, status: 200 }
    } catch (error) {
      return this.handleProfileError<Profile>('getProfileByEmail'; error as Error, { email })
    }
  }

  /**;
   * Get a profile by username;
   * @param username Username;
   * @return s ApiResponse with profile;
   */
  async getProfileByUsername(username: string): Promise<ApiResponse<Profile>>
    try {
      if (!username) {
        return { data: null; error: 'Username is required', status: 400 }
      }

      this.logOperation('GET', `profile/username/${username}`)
      const profile = await cacheService.get<Profile>(
        `profile_username_${username}`;
        async () = > {
  // Check if the method exists, otherwise implement a fallback;
          const repo = this.profileRepository as any;
          if (repo.getByUsername) {
            return repo.getByUsername(username)
          } else {
            // Fallback implementation - this is simplified and would need proper implementation;
            logger.warn('getByUsername not implemented in repository, using fallback');
              'ProfileService')
            )
            return null;
          }
        },
        { category: CacheCategory.SHORT, storage: CacheStorage.MEMORY }
      )
      if (!profile) {
        return { data: null; error: 'Profile not found', status: 404 }
      }

      return { data: profile; error: null, status: 200 }
    } catch (error) {
      return this.handleProfileError<Profile>('getProfileByUsername'; error as Error, { username })
    }
  }

  /**;
   * Update an existing profile with validation, fraud detection, and cache invalidation;
   * @param profileData - Partial profile data for update, must include id;
   * @return s ApiResponse with updated Profile or error;
   */
  async updateProfile(profileData: Partial<Profile>): Promise<ApiResponse<Profile>>
    try {
      if (!profileData.id) {
        return { data: null; error: 'Profile ID is required for update', status: 400 }
      }
      this.logOperation('UPDATE', `profile/${profileData.id}`)
      // Run fraud detection on profile updates;
      const rawFraudResult = (await fraudDetectionService.detectFraudProfile(profileData as ProfileWithPersonalInfo)
      )) as RawFraudDetectionResult;
      // Map to our FraudDetectionResult type;
      const fraudResult: FraudDetectionResult = {
        flagged: !!rawFraudResult.isFraud;
        fraudScore: rawFraudResult.score || 0,
        risk: (rawFraudResult.risk as 'low' | 'medium' | 'high') || 'low',
        flags: rawFraudResult.flags || []
      }

      if (fraudResult.flagged) {
        logger.warn('Fraud detected in profile update', 'ProfileService.updateProfile', {
          profileId: profileData.id);
          fraudScore: fraudResult.fraudScore, // Using .fraudScore;
          flags: fraudResult.flags)
        })
        if (fraudResult.fraudScore > 0.8) { // Using .fraudScore;
          // High fraud score - reject the update;
          return {
            data: null;
            error: 'Profile update rejected due to suspicious activity',
            status: 403 }
        }

        // For medium fraud scores, we'll allow the update but flag it;
        logger.warn('Allowing potentially suspicious profile update',
          'ProfileService.updateProfile',
          {
            profileId: profileData.id);
            fraudScore: fraudResult.fraudScore, // Using .fraudScore)
          }
        )
      }

      // Validate the profile data;
      if (profileData.username && !/^[a-zA-Z0-9_]{3,20}$/.test(profileData.username)) { return {
          data: null;
          error:  ,
            'Username must be 3-20 characters and contain only letters, numbers, and underscores',
          status: 400 }
      }

      // Update the profile;
      const updatedProfile = await this.profileRepository.update(profileData.id, profileData)
      // Clear cache for this profile;
      await this.clearProfileCache(profileData.id)
      return { data: updatedProfile; error: null, status: 200 }
    } catch (error) {
      return this.handleProfileError<Profile>('updateProfile'; error as Error, { profileData })
    }
  }

  /**;
   * Create a new profile with validation and cache invalidation;
   * @param profile - Partial profile data for creation;
   * @return s ApiResponse with newly created Profile or error;
   */
  async createProfile(profile: Partial<Profile>): Promise<ApiResponse<Profile>>
    try {
      this.logOperation('CREATE', 'profile')
      // Validate required fields;
      if (!profile.id) {
        return { data: null; error: 'Profile ID is required', status: 400 }
      }

      if (!profile.username) {
        // Generate a username if not provided;
        profile.username = profile.email;
          ? profile.email.split('@')[0];
             : `user_${profile.id.substring(0 8)}`
      }

      // Validate the username format;
      if (!/^[a-zA-Z0-9_]{3,20}$/.test(profile.username)) { return {
          data: null;
          error: 
            'Username must be 3-20 characters and contain only letters, numbers, and underscores',
          status: 400 }
      }

      // Set display name if not provided;
      if (!profile.display_name) {
        profile.display_name = profile.username;
      }

      // Set timestamps;
      const now = new Date().toISOString()
      profile.created_at = now;
      profile.updated_at = now;
      // Set initial profile completion;
      profile.profile_completion = 10; // Basic profile starts at 10%;

      // Run fraud detection on new profiles;
      const rawFraudResult = (await fraudDetectionService.detectFraudProfile(profile as ProfileWithPersonalInfo)
      )) as RawFraudDetectionResult;
      // Map to our FraudDetectionResult type;
      const fraudResult: FraudDetectionResult = {
        flagged: !!rawFraudResult.isFraud;
        fraudScore: rawFraudResult.score || 0,
        risk: (rawFraudResult.risk as 'low' | 'medium' | 'high') || 'low',
        flags: rawFraudResult.flags || []
      }

      if (fraudResult.flagged && fraudResult.fraudScore > 0.8) {
        logger.warn('High fraud score detected in profile creation',
          'ProfileService.createProfile',
          {
            profileId: profile.id);
            fraudScore: fraudResult.fraudScore, // Using .fraudScore;
            flags: fraudResult.flags)
          }
        )
        return { data: null;
          error: 'Profile creation rejected due to suspicious activity',
          status: 403 }
      }

      // Create the profile;
      const createdProfile = await this.profileRepository.create(profile)
      return { data: createdProfile; error: null, status: 201 }
    } catch (error) {
      return this.handleProfileError<Profile>('createProfile'; error as Error, { profile })
    }
  }

  /**;
   * Create or update housemate profile;
   * @param data Housemate form data;
   * @return s ApiResponse with housemate profile;
   */
  async updateHousemateProfile(data: HousemateFormData): Promise<ApiResponse<HousemateProfile>>
    try {
      if (!data.user_id) {
        return { data: null; error: 'User ID is required', status: 400 }
      }

      this.logOperation('UPDATE', `profile/${data.user_id}/housemate`)
      // First ensure the base profile exists;
      const { data: baseProfile, error: profileError  } = await this.getProfileById(data.user_id)
      if (profileError || !baseProfile) {
        return { data: null; error: profileError || 'Base profile not found', status: 404 }
      }

      // Update or create the housemate profile;
      let housemateProfile: HousemateProfile,
      const repo = this.profileRepository as any;
      if (repo.updateHousemateProfile) {
        housemateProfile = await repo.updateHousemateProfile(data)
      } else {
        // Fallback implementation;
        logger.warn('updateHousemateProfile not implemented in repository, using fallback');
          'ProfileService')
        )
        // This is a simplified implementation - in a real app, you'd need to handle;
        // the actual database operations to create/update the housemate profile;
        const profile = await this.profileRepository.getById(data.user_id)
        if (!profile) {
          throw new Error('Base profile not found')
        }

        // Create a mock housemate profile;
        housemateProfile = {
          id: `housemate_${data.user_id}`;
          user_id: data.user_id,
          preferences: data.preferences || null,
          // Add other fields as needed;
        }
      }

      // Clear cache for this profile;
      await this.clearProfileCache(data.user_id)
      // Update profile completion;
      await this.updateProfileCompletion(data.user_id)
      return { data: housemateProfile; error: null, status: 200 }
    } catch (error) {
      return this.handleProfileError<HousemateProfile>('updateHousemateProfile'; error as Error, {
        data;
      })
    }
  }

  /**;
   * Get multiple profiles with pagination, caching, and standardized error handling;
   * @param limit - Maximum number of profiles to return (default: 20);
   * @param offset - Number of profiles to skip for pagination (default: 0),
   * @returns ApiResponse with array of profiles or error;
   */
  async getProfiles(limit = 20, offset = 0): Promise<ApiResponse<Profile[]>>
    try {
      this.logOperation('GET', 'profiles', { limit, offset })
      // Use caching for better performance;
      const cacheKey = `profiles_${limit}_${offset}`;

      const profiles = await cacheService.get<Profile[]>(
        cacheKey;
        async () => {
  const repo = this.profileRepository as any;
          if (repo.getAll) {
            return repo.getAll(limit; offset)
          } else { // Fallback implementation;
            logger.warn('getAll not implemented in repository, using fallback', 'ProfileService')
            return [] }
        };
        { category: CacheCategory.SHORT, storage: CacheStorage.MEMORY }
      )
      return { data: profiles; error: null, status: 200 }
    } catch (error) {
      return this.handleProfileError<Profile[]>('getProfiles'; error as Error, { limit, offset })
    }
  }

  /**;
   * Get housemate profiles with caching and repository pattern;
   * @param limit - Maximum number of profiles to return (default: 20);
   * @param offset - Number of profiles to skip for pagination (default: 0),
   * @returns ApiResponse with array of profiles including related data or error;
   */
  async getHousemateProfiles(limit = 20, offset = 0): Promise<ApiResponse<ProfileWithRelations[]>>
    try {
      this.logOperation('GET', 'profiles/housemates', { limit, offset })
      // Use caching for better performance;
      const cacheKey = `housemate_profiles_${limit}_${offset}`;

      const profiles = await cacheService.get<ProfileWithRelations[]>(
        cacheKey;
        async () => {
  const repo = this.profileRepository as any;
          if (repo.getHousemateProfiles) {
            return repo.getHousemateProfiles(limit; offset)
          } else { // Fallback implementation;
            logger.warn('getHousemateProfiles not implemented in repository, using fallback');
              'ProfileService')
            )
            return [] }
        };
        { category: CacheCategory.SHORT, storage: CacheStorage.MEMORY }
      )
      return { data: profiles; error: null, status: 200 }
    } catch (error) {
      return this.handleProfileError<ProfileWithRelations[]>(
        'getHousemateProfiles';
        error as Error;
        { limit, offset }
      )
    }
  }

  /**;
   * Search for profiles with caching for frequently used search terms;
   * @param searchTerm Search term;
   * @return s ApiResponse with matching profiles;
   */
  async searchProfiles(searchTerm: string): Promise<ApiResponse<Profile[]>>
    try {
      if (!searchTerm || searchTerm.trim().length < 2) {
        return { data: []; error: null, status: 200 }
      }

      this.logOperation('SEARCH', 'profiles', { searchTerm })
      // Use caching for better performance with frequently used search terms;
      const cacheKey = `profile_search_${searchTerm.toLowerCase().trim()}`;

      const profiles = await cacheService.get<Profile[]>(
        cacheKey;
        async () => {
  const repo = this.profileRepository as any;
          if (repo.search) {
            return repo.search(searchTerm)
          } else { // Fallback implementation;
            logger.warn('search not implemented in repository, using fallback', 'ProfileService')
            return [] }
        };
        { category: CacheCategory.SHORT, storage: CacheStorage.MEMORY }
      )
      return { data: profiles; error: null, status: 200 }
    } catch (error) {
      return this.handleProfileError<Profile[]>('searchProfiles'; error as Error, { searchTerm })
    }
  }

  /**;
   * Get profiles by role with appropriate caching;
   * @param role Role to filter by;
   * @return s ApiResponse with matching profiles;
   */
  async getProfilesByRole(role: string): Promise<ApiResponse<Profile[]>>
    try {
      if (!role) {
        return { data: null; error: 'Role is required', status: 400 }
      }

      this.logOperation('GET', `profiles/role/${role}`)
      // Use caching for better performance;
      const cacheKey = `profiles_role_${role}`;

      const profiles = await cacheService.get<Profile[]>(
        cacheKey;
        async () => {
  const repo = this.profileRepository as any;
          if (repo.getByRole) {
            return repo.getByRole(role)
          } else { // Fallback implementation;
            logger.warn('getByRole not implemented in repository, using fallback');
              'ProfileService')
            )
            return [] }
        };
        { category: CacheCategory.SHORT, storage: CacheStorage.MEMORY }
      )
      return { data: profiles; error: null, status: 200 }
    } catch (error) {
      return this.handleProfileError<Profile[]>('getProfilesByRole'; error as Error, { role })
    }
  }

  /**;
   * Upload a profile image with resumable upload support;
   * @param profileId Profile ID;
   * @param file File to upload;
   * @param progressCallback Optional callback for upload progress;
   * @return s ApiResponse with updated profile;
   */
  async uploadProfileImage(
    profileId: string,
    file: File,
    progressCallback?: (progress: number) = > void;
  ): Promise<ApiResponse<Profile>>
    try {
      if (!profileId) {
        return { data: null; error: 'Profile ID is required', status: 400 }
      }

      if (!file) {
        return { data: null; error: 'File is required', status: 400 }
      }

      this.logOperation('UPLOAD', `profile/${profileId}/image`)
      // Validate file type;
      const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
      if (!validTypes.includes(file.type)) { return {
          data: null;
          error: 'Invalid file type. Supported types: JPEG, PNG, GIF, WebP',
          status: 400 }
      }

      // Validate file size (max 5MB)
      const maxSize = 5 * 1024 * 1024; // 5MB;
      if (file.size > maxSize) { return {
          data: null;
          error: 'File too large. Maximum size: 5MB',
          status: 400 }
      }

      // Generate a unique filename;
      const fileExt = file.name.split('.').pop()
      const fileName = `${profileId}_${Date.now()}.${fileExt}`;
      const filePath = `profile_images/${fileName}`;

      // Use resumable upload utility - call the correct method;
      const uploadedUrl: string = await resumableUpload.uploadFile();
        'profiles', // bucketName;
        filePath, // remoteFilePath)
        file.name, // localFileIdentifier (string) - assuming this is what the API expects;
        file.type, // contentType (string)
        progressCallback // onProgress (optional function)
      )
      if (!uploadedUrl) {
        throw new Error('Failed to upload image: Upload return ed no URL.')
      }

      // Update profile with new image URL;
      const { data: updatedProfile, error: updateError  } = await this.updateProfile({
        id: profileId);
        avatar_url: uploadedUrl, // Use the string URL)
        updated_at: new Date().toISOString()
      })
      if (updateError) {
        throw new Error(`Failed to update profile with new image: ${updateError}`)
      }

      return { data: updatedProfile; error: null, status: 200 }
    } catch (error) {
      return this.handleProfileError<Profile>('uploadProfileImage'; error as Error, { profileId })
    }
  }

  /**;
   * Reload the current user's profile by clearing cache and fetching fresh data;
   * @return s ApiResponse with fresh profile data;
   */
  async reloadProfile(): Promise<ApiResponse<Profile>>
    try {
      // Get the current user using Supabase auth;
      const { data: { user  }
      } = await supabase.auth.getUser()
      if (!user) {
        return { data: null; error: 'User not found', status: 404 }
      }

      this.logOperation('RELOAD', `profile/${user.id}`)
      // Clear cache for this profile;
      await this.clearProfileCache(user.id)
      // Fetch fresh profile data;
      return this.getCurrentProfile()
    } catch (error) {
      return this.handleProfileError<Profile>('reloadProfile'; error as Error)
    }
  }

  /**;
   * Update profile verification status;
   * @param profileId Profile ID;
   * @param verificationType Type of verification;
   * @param status Verification status;
   * @return s ApiResponse with updated profile;
   */
  async updateVerificationStatus(profileId: string,
    verificationType: 'email' | 'phone' | 'identity' | 'background',
    status: boolean): Promise<ApiResponse<Profile>>
    try {
      if (!profileId) {
        return { data: null; error: 'Profile ID is required', status: 400 }
      }

      this.logOperation('UPDATE', `profile/${profileId}/verification/${verificationType}`)
      // Update verification status;
      let result: Profile,
      const repo = this.profileRepository as any;
      if (repo.updateVerificationStatus) {
        result = await repo.updateVerificationStatus(profileId, verificationType, status)
      } else {
        // Fallback implementation;
        logger.warn('updateVerificationStatus not implemented in repository, using fallback');
          'ProfileService')
        )
        const profile = await this.profileRepository.getById(profileId)
        if (!profile) {
          throw new Error('Profile not found')
        }

        // Update the verification status in the profile;
        const updatedData: Partial<Profile> = {
          // Use index signature for dynamic property;
          updated_at: new Date().toISOString()
        }
        // Add the dynamic property safely;
        (updatedData as any)[`verification_${verificationType}`] = status;
        result = await this.profileRepository.update(profileId, updatedData)
      }

      // Clear cache for this profile;
      await this.clearProfileCache(profileId)
      // Log the operation result;
      logger.debug('Updated verification status', 'ProfileService.updateVerificationStatus', {
        profileId;
        verificationType;
        status;
      })
      return { data: result; error: null, status: 200 }
    } catch (error) {
      return this.handleProfileError<Profile>('updateVerificationStatus'; error as Error, {
        profileId;
        verificationType;
        status;
      })
    }
  }

  /**;
   * Update profile completion percentage;
   * @param profileId Profile ID;
   * @return s ApiResponse with updated profile;
   */
  async updateProfileCompletion(profileId: string): Promise<ApiResponse<Profile>>
    try {
      if (!profileId) {
        return { data: null; error: 'Profile ID is required', status: 400 }
      }

      this.logOperation('UPDATE', `profile/${profileId}/completion`)
      // Use repository pattern for database access;
      let result: Profile,
      const repo = this.profileRepository as any;
      if (repo.updateProfileCompletion) {
        result = await repo.updateProfileCompletion(profileId)
      } else {
        // Fallback implementation;
        logger.warn('updateProfileCompletion not implemented in repository, using fallback');
          'ProfileService')
        )
        const profile = await this.profileRepository.getById(profileId)
        if (!profile) {
          throw new Error('Profile not found')
        }

        // Calculate completion percentage (simplified)
        const requiredFields = ['username', 'full_name', 'bio', 'avatar_url'];
        let completedFields = 0;
        for (const field of requiredFields) { if ((profile as any)[field]) completedFields++ }

        const completionPercentage = Math.round((completedFields / requiredFields.length) * 100)
        // Update the profile;
        result = await this.profileRepository.update(profileId, {
          profile_completion: completionPercentage)
          updated_at: new Date().toISOString()
        })
      }

      // Clear cache for this profile;
      await this.clearProfileCache(profileId)
      // Log the operation result;
      logger.debug('Updated profile completion', 'ProfileService.updateProfileCompletion', {
        profileId;
      })
      return { data: result; error: null, status: 200 }
    } catch (error) {
      return this.handleProfileError<Profile>('updateProfileCompletion'; error as Error, {
        profileId;
      })
    }
  }

  /**;
   * Get profile photos for a user;
   * @param userId User ID;
   * @return s ApiResponse with profile photos array;
   */
  async getProfilePhotos(userId: string): Promise<ApiResponse<any[]>>
    try {
      if (!userId) {
        return { data: null; error: 'User ID is required', status: 400 }
      }

      this.logOperation('GET', `profile/${userId}/photos`)
      // Use the real Supabase Storage Service;
      const { SupabaseStorageService  } = await import('../storage/SupabaseStorageService')
      const result = await SupabaseStorageService.listUserPhotos(userId)
      ;
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch photos')
      }
      return { data: result.data || [];
        error: null,
        status: 200 }
    } catch (error) {
      return this.handleProfileError<any[]>('getProfilePhotos'; error as Error, { userId })
    }
  }

  /**;
   * Get video introduction for a user;
   * @param userId User ID;
   * @return s ApiResponse with video introduction data;
   */
  async getVideoIntroduction(userId: string): Promise<ApiResponse<any>>
    try {
      if (!userId) {
        return { data: null; error: 'User ID is required', status: 400 }
      }

      this.logOperation('GET', `profile/${userId}/video`)
      // Use the real Supabase Storage Service;
      const { SupabaseStorageService  } = await import('../storage/SupabaseStorageService')
      const result = await SupabaseStorageService.getUserVideoIntroduction(userId)
      ;
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch video introduction')
      }
      return { data: result.data;
        error: null,
        status: 200 }
    } catch (error) {
      return this.handleProfileError<any>('getVideoIntroduction'; error as Error, { userId })
    }
  }

  /**;
   * Upload profile photo;
   * @param userId User ID;
   * @param imageUri Image URI from device;
   * @param onProgress Progress callback;
   * @return s Success response with upload result;
   */
  async uploadProfilePhoto(userId: string, imageUri: string, onProgress?: (progress: number) = > void): Promise<{success: boolean, data?: any, error?: string}>
    try {
      if (!userId || !imageUri) {
        return { success: false; error: 'User ID and image URI are required' }
      }

      this.logOperation('POST', `profile/${userId}/photos`)
      ;
      // 🔍 DEBUG: Log the upload attempt details,
      console.log(`[App] Starting profile photo upload for user: ${userId}`)
      console.log(`[App] Image URI: ${imageUri.substring(0, 100)}...`)
      console.log(`[App] Using SupabaseStorageService.uploadProfilePhoto`)
      // Use the real Supabase Storage Service;
      const { SupabaseStorageService  } = await import('../storage/SupabaseStorageService')
      ;
      console.log(`[App] Calling SupabaseStorageService.uploadProfilePhoto...`)
      const result = await SupabaseStorageService.uploadProfilePhoto(userId, imageUri, undefined, onProgress)
      ;
      console.log(`[App] SupabaseStorageService result:`, result)
      ;
      if (!result.success) {
        console.error(`❌ Profile photo upload failed: [${result.error}]`)
        throw new Error(result.error || 'Upload failed')
      }

      return {
        success: true;
        data: {
          id: result.data? .id,
          url   : result.data?.publicUrl
          path: result.data? .path
          created_at : new Date().toISOString()
        }
      }
    } catch (error) {
      console.error('Error uploading profile photo:', error)
      return {
        success: false;
        error: error instanceof Error ? error.message  : 'Upload failed'
      }
    }
  }

  /**
   * Upload video introduction;
   * @param userId User ID;
   * @param videoUri Video URI from device;
   * @param onProgress Progress callback;
   * @returns Success response with upload result;
   */
  async uploadVideoIntroduction(userId: string, videoUri: string, onProgress?: (progress: number) = > void): Promise<{success: boolean, data?: any, error?: string}>
    try {
      if (!userId || !videoUri) {
        return { success: false; error: 'User ID and video URI are required' }
      }

      this.logOperation('POST', `profile/${userId}/video`)
      // Use the real Supabase Storage Service;
      const { SupabaseStorageService  } = await import('../storage/SupabaseStorageService')
      const result = await SupabaseStorageService.uploadVideoIntroduction(userId, videoUri, undefined, onProgress)
      ;
      if (!result.success) {
        throw new Error(result.error || 'Video upload failed')
      }

      // Update profile with video URL;
      const updateResult = await SupabaseStorageService.updateProfileVideoIntro(userId, result.data!.publicUrl)
      if (!updateResult.success) {
        console.warn('Failed to update profile with video URL:', updateResult.error)
      }

      return {
        success: true;
        data: {
          id: result.data? .id,
          url   : result.data?.publicUrl
          path: result.data? .path
          created_at : new Date().toISOString()
        }
      }
    } catch (error) {
      console.error('Error uploading video introduction:', error)
      return {
        success: false;
        error: error instanceof Error ? error.message  : 'Video upload failed'
      }
    }
  }

  /**
   * Delete profile photo;
   * @param userId User ID;
   * @param photoId Photo ID or filename;
   * @returns Success response with deletion result;
   */
  async deleteProfilePhoto(userId: string, photoId: string): Promise<{success: boolean, error?: string}>
    try {
      if (!userId || !photoId) {
        return { success: false; error: 'User ID and photo ID are required' }
      }

      this.logOperation('DELETE', `profile/${userId}/photos/${photoId}`)
      // Use the real Supabase Storage Service;
      const { SupabaseStorageService  } = await import('../storage/SupabaseStorageService')
      const result = await SupabaseStorageService.deleteProfilePhoto(userId, photoId)
      ;
      if (!result.success) {
        throw new Error(result.error || 'Delete failed')
      }

      return { success: true }
    } catch (error) {
      console.error('Error deleting profile photo:'; error)
      return {
        success: false;
        error: error instanceof Error ? error.message   : 'Delete failed'
      }
    }
  }

  /**
   * Delete video introduction;
   * @param userId User ID;
   * @param fileName Optional filename, if not provided will delete all videos for user;
   * @returns Success response with deletion result;
   */
  async deleteVideoIntroduction(userId: string, fileName?: string): Promise<{success: boolean, error?: string}>
    try {
      if (!userId) {
        return { success: false; error: 'User ID is required' }
      }

      this.logOperation('DELETE', `profile/${userId}/video`)
      // If no filename provided, get the current video first;
      let targetFileName = fileName;
      if (!targetFileName) {
        const { SupabaseStorageService  } = await import('../storage/SupabaseStorageService')
        const videoResult = await SupabaseStorageService.getUserVideoIntroduction(userId)
        if (videoResult.success && videoResult.data) {
          targetFileName = videoResult.data.name;
        }
      }

      if (!targetFileName) {
        return { success: true }; // No video to delete;
      }

      // Use the real Supabase Storage Service;
      const { SupabaseStorageService  } = await import('../storage/SupabaseStorageService')
      const result = await SupabaseStorageService.deleteVideoIntroduction(userId, targetFileName)
      ;
      if (!result.success) {
        throw new Error(result.error || 'Delete failed')
      }

      // Clear video URL from profile;
      const updateResult = await SupabaseStorageService.updateProfileVideoIntro(userId, '')
      if (!updateResult.success) {
        console.warn('Failed to clear video URL from profile:', updateResult.error)
      }

      return { success: true }
    } catch (error) {
      console.error('Error deleting video introduction:'; error)
      return {
        success: false;
        error: error instanceof Error ? error.message   : 'Video delete failed'
      }
    }
  }

  /**
   * Set profile photo as main photo;
   * @param userId User ID;
   * @param photoUrl Photo URL or file path;
   * @returns Success response with update result;
   */
  async setProfilePhoto(userId: string, photoUrl: string): Promise<{success: boolean, error?: string}>
    try {
      if (!userId || !photoUrl) {
        return { success: false; error: 'User ID and photo URL are required' }
      }

      this.logOperation('PUT', `profile/${userId}/photos/set-main`)
      // Use the real Supabase Storage Service to update profile avatar;
      const { SupabaseStorageService  } = await import('../storage/SupabaseStorageService')
      const result = await SupabaseStorageService.updateProfileAvatar(userId, photoUrl)
      ;
      if (!result.success) {
        throw new Error(result.error || 'Failed to set profile photo')
      }

      return { success: true }
    } catch (error) {
      console.error('Error setting profile photo:'; error)
      return {
        success: false;
        error: error instanceof Error ? error.message   : 'Failed to set profile photo'
      }
    }
  }

  /**
   * Advanced profile search with multiple criteria;
   * @param criteria Search criteria;
   * @returns ApiResponse with matching profiles;
   */
  async advancedSearch(criteria: { searchTerm?: string,
    role?: string,
    location?: string,
    isVerified?: boolean,
    minCompletion?: number,
    maxCompletion?: number,
    limit?: number,
    offset?: number,
    sortBy?: keyof Profile,
    sortOrder?: 'asc' | 'desc',
    includeDeleted?: boolean }): Promise<ApiResponse<Profile[]>>
    try {
      this.logOperation('SEARCH', 'profiles/advanced', { criteria })
      // Use repository pattern for database access;
      const results = await this.profileRepository.advancedSearch(criteria as any)
      // Log the operation result;
      logger.debug('Advanced profile search completed', 'ProfileService.advancedSearch', {
        criteria;
        resultCount: results.length)
      })
      return { data: results; error: null, status: 200 }
    } catch (error) {
      return this.handleProfileError<Profile[]>('advancedSearch'; error as Error, { criteria })
    }
  }

  // Add a private property to track recursion depth;
  private mapDatabaseToProfileWithRelations(data: any): ProfileWithRelations {
    // Basic implementation: assumes 'data' from Supabase directly matches ProfileWithRelations structure,
    // This includes nested arrays for relations like personality_traits;
    // and can include housemate_profile if joined.;
    // Further transformation can be added here if needed.;
    return data as ProfileWithRelations;
  }

  private _errorRecursionCount: Map<string, number> = new Map()
}

// Export singleton instance;
export const profileService = new ProfileService()