import React from 'react';
/**;
 * ChatService.ts - DEPRECATED;
 *;
 * This file exists for backward compatibility.;
 * It re-exports the UnifiedChatService and its types for older code that still imports from here.;
 *;
 * New code should import directly from '@services/unified/UnifiedChatService'.;
 *;
 * @deprecated Use UnifiedChatService instead;
 */

import { unifiedChatService } from '@services/unified/UnifiedChatService';
import { Message, MessageType, ChatRoom, ChatParticipant } from '@services/unified/types';

// Re-export types for backward compatibility;
export type { Message, MessageType, ChatRoom, ChatParticipant }

// Export an interface that matches the shape of the old ChatService;
export interface ChatRoomWithDetails extends ChatRoom {
  participants_details?: any[]
}

// Create a compatibility layer for old code;
class ChatServiceCompat {
  // Static methods for mock functionality;
  static mockChatRooms = new Map<string, ChatRoom>()
  static mockChatMessages = new Map<string, Message[]>()
  /**;
   * Get mock rooms for a user (for testing and development)
   */
  static getMockRoomsForUser(userId: string): ChatRoom[] {
    const mockRooms: ChatRoom[] = [];
    // Create a mock room if none exist;
    if (this.mockChatRooms.size = == 0) {
      const mockRoomId = `mock_${Date.now()}`;
      const mockRoom: ChatRoom = {
        id: mockRoomId;
        created_at: new Date().toISOString()
        updated_at: new Date().toISOString()
        created_by: userId,
        last_message: 'This is a mock conversation for testing',
        last_message_at: new Date().toISOString()
        is_group: false,
        name: 'Mock Conversation',
        unread_count: 0,
        participants: [,
          {
            id: 'mock-user-123',
            display_name: 'Mock User',
            avatar_url: 'https://via.placeholder.com/150'
          }],
        is_mock: true
      }

      this.mockChatRooms.set(mockRoomId, mockRoom)
      this.mockChatMessages.set(mockRoomId, [])
    }

    // Return all mock rooms;
    return Array.from(this.mockChatRooms.values())
  }

  /**;
   * Create a mock room for testing;
   */
  static createMockRoom(userId: string, otherUserId: string): string {
    const mockRoomId = `mock_${Date.now()}`;
    const mockRoom: ChatRoom = {
      id: mockRoomId;
      created_at: new Date().toISOString()
      updated_at: new Date().toISOString()
      created_by: userId,
      last_message: 'New mock conversation',
      last_message_at: new Date().toISOString()
      is_group: false,
      name: 'Mock Conversation',
      unread_count: 0,
      participants: [,
        {
          id: otherUserId,
          display_name: 'Mock User',
          avatar_url: 'https://via.placeholder.com/150'
        }],
      is_mock: true
    }

    this.mockChatRooms.set(mockRoomId, mockRoom)
    this.mockChatMessages.set(mockRoomId, [])
    return mockRoomId;
  }
}

// Export the unified service as the default chatService;
export const chatService = unifiedChatService;
// Export the ChatService class for static methods;
export const ChatService = ChatServiceCompat;