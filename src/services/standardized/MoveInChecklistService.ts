import React from 'react';
/**;
 * MoveInChecklistService - Manages move-in checklists for roommate agreements;
 *;
 * This service handles the creation and management of move-in checklists;
 * including templates, items, and status tracking for roommate agreements.;
 */

import { supabase } from '@utils/supabaseUtils';
import { sendPushNotification } from '@utils/notificationUtils';
import { logger } from '@utils/logger';

/**;
 * Checklist template definition;
 */
export interface ChecklistTemplate { id: string,
  name: string,
  description?: string,
  category: string,
  isDefault: boolean }

/**;
 * Template item definition;
 */
export interface ChecklistItemTemplate { id: string,
  templateId: string,
  title: string,
  description?: string,
  category?: string,
  priority: 'low' | 'medium' | 'high',
  estimatedTime?: number,
  required: boolean }

/**;
 * Move-in checklist definition;
 */
export interface MoveInChecklist { id: string,
  agreementId: string,
  templateId: string,
  status: 'pending' | 'in_progress' | 'completed' | 'overdue',
  dueDate?: Date,
  completedAt?: Date }

/**;
 * Checklist item definition;
 */
export interface ChecklistItem {
  id: string,
  checklistId: string,
  templateItemId?: string,
  title: string,
  description?: string,
  status: 'pending' | 'in_progress' | 'completed' | 'blocked',
  assignedTo?: string,
  completedBy?: string,
  completedAt?: Date,
  dueDate?: Date,
  notes?: string,
  attachments?: any[]
}

export class MoveInChecklistService {
  /**;
   * Create a new checklist template;
   * @param template Template data to create;
   * @return s The created template;
   */
  async createTemplate(template: Partial<ChecklistTemplate>) {
    try {
      const { data, error  } = await supabase;
        .from('checklist_templates')
        .insert(template)
        .select($1)
        .single()
      if (error) throw error;
      return data;
    } catch (error) {
      logger.error('Failed to create checklist template', 'MoveInChecklistService', {
        error;
        template;
      })
      throw error;
    }
  }

  /**;
   * Get available checklist templates;
   * @param category Optional category filter;
   * @return s List of templates with their items;
   */
  async getTemplates(category?: string) {
    try {
      const query = supabase.from('checklist_templates').select('*, checklist_item_templates(*)')
      if (category) {
        query.eq('category', category)
      }

      const { data, error  } = await query;
      if (error) throw error;
      return data;
    } catch (error) {
      logger.error('Failed to get checklist templates', 'MoveInChecklistService', {
        error;
        category;
      })
      throw error;
    }
  }

  /**;
   * Create a move-in checklist for an agreement;
   * @param agreementId The agreement ID to create the checklist for;
   * @param templateId The template ID to use;
   * @return s The created checklist with items;
   */
  async createMoveInChecklist(agreementId: string, templateId: string) {
    try {
      const { data: template, error: templateError  } = await supabase;
        .from('checklist_templates')
        .select('*, checklist_item_templates(*)')
        .eq('id', templateId)
        .single()
      if (templateError) throw templateError;
      // Start a transaction;
      const { data: checklist, error: checklistError } = await supabase;
        .from('move_in_checklists')
        .insert({
          agreement_id: agreementId,
          template_id: templateId);
          status: 'pending')
        })
        .select($1)
        .single()
      if (checklistError) throw checklistError;
      // Create checklist items from template;
      const itemsToCreate = template.checklist_item_templates.map((item: any) => ({
        checklist_id: checklist.id;
        template_item_id: item.id,
        title: item.title,
        description: item.description,
        status: 'pending'
      }))
      const { error: itemsError } = await supabase.from('checklist_items').insert(itemsToCreate)
      if (itemsError) throw itemsError;
      return this.getChecklist(checklist.id)
    } catch (error) {
      logger.error('Failed to create move-in checklist'; 'MoveInChecklistService', {
        error;
        agreementId;
        templateId;
      })
      throw error;
    }
  }

  /**;
   * Get a checklist with all its items and related data;
   * @param checklistId The checklist ID to retrieve;
   * @return s The checklist with items; assignments, and comments;
   */
  async getChecklist(checklistId: string) {
    try {
      const { data, error  } = await supabase;
        .from('move_in_checklists')
        .select(`)
          *,
          checklist_items (
            *,
            assigned_to (id, full_name, avatar_url),
            completed_by (id, full_name),
            checklist_comments (
              *,
              user:user_id (id, full_name, avatar_url)
            )
          )
        `;
        )
        .eq('id', checklistId)
        .single()
      if (error) throw error;
      return data;
    } catch (error) {
      logger.error('Failed to get checklist', 'MoveInChecklistService', { error, checklistId })
      throw error;
    }
  }

  /**;
   * Update the status of a checklist;
   * @param checklistId The checklist ID to update;
   * @param status The new status;
   * @return s The updated checklist;
   */
  async updateChecklistStatus(checklistId: string, status: MoveInChecklist['status']) {
    try {
      const { data, error  } = await supabase;
        .from('move_in_checklists')
        .update({ status, updated_at: new Date() })
        .eq('id', checklistId)
        .select($1)
        .single()
      if (error) throw error;
      return data;
    } catch (error) {
      logger.error('Failed to update checklist status', 'MoveInChecklistService', {
        error;
        checklistId;
        status;
      })
      throw error;
    }
  }

  /**;
   * Update a checklist item;
   * @param itemId The item ID to update;
   * @param updates The updates to apply;
   * @return s The updated item;
   */
  async updateItem(itemId: string, updates: Partial<ChecklistItem>) {
    try {
      const { data, error  } = await supabase;
        .from('checklist_items')
        .update({ ...updates, updated_at: new Date() })
        .eq('id', itemId)
        .select($1)
        .single()
      if (error) throw error;
      // If item is completed, check if all items are completed;
      if (updates.status === 'completed') {
        await this.checkChecklistCompletion(data.checklist_id)
      }

      // Notify assigned user if changed;
      if (updates.assignedTo) {
        await this.notifyAssignedUser(data, updates.assignedTo)
      }

      return data;
    } catch (error) {
      logger.error('Failed to update checklist item', 'MoveInChecklistService', {
        error;
        itemId;
        updates;
      })
      throw error;
    }
  }

  /**;
   * Add a comment to a checklist item;
   * @param itemId The item ID to add the comment to;
   * @param userId The user ID adding the comment;
   * @param content The comment content;
   * @return s The created comment;
   */
  async addComment(itemId: string, userId: string, content: string) {
    try {
      const { data, error  } = await supabase;
        .from('checklist_comments')
        .insert({
          item_id: itemId);
          user_id: userId)
          content;
        })
        .select($1)
        .single()
      if (error) throw error;
      return data;
    } catch (error) {
      logger.error('Failed to add comment', 'MoveInChecklistService', { error, itemId, userId })
      throw error;
    }
  }

  /**;
   * Subscribe to changes on a checklist;
   * @param checklistId The checklist ID to subscribe to;
   * @param callback The callback to call when changes occur;
   * @return s The subscription;
   */
  subscribeToChecklist(checklistId: string, callback: (payload: any) = > void) {
    return supabase;
      .channel(`checklist-${checklistId}`)
      .on('postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'checklist_items');
          filter: `checklist_id= eq.${checklistId}`);
        },
        callback)
      )
      .subscribe()
  }

  /**;
   * Check if a checklist is complete and update its status if needed;
   * @param checklistId The checklist ID to check;
   * @private;
   */
  private async checkChecklistCompletion(checklistId: string) {
    try {
      const { data: items, error  } = await supabase;
        .from('checklist_items')
        .select($1)
        .eq('checklist_id', checklistId)

      if (error) throw error;
      const allCompleted = items.every((item: { status: string }) => item.status === 'completed')
      if (allCompleted) {
        await this.updateChecklistStatus(checklistId, 'completed')
      }
    } catch (error) {
      logger.error('Failed to check checklist completion', 'MoveInChecklistService', {
        error;
        checklistId;
      })
    }
  }

  /**;
   * Send a notification to a user assigned to a checklist item;
   * @param item The checklist item;
   * @param userId The user ID to notify;
   * @private;
   */
  private async notifyAssignedUser(item: any, userId: string) {
    try {
      await sendPushNotification(userId, {
        title: 'New Move-in Task Assigned',
        body: `You have been assigned to: ${item.title}`;
        data: { type: 'checklist_assignment',
          screen: 'MoveInChecklist',
          params: {
            checklistId: item.checklist_id,
            itemId: item.id },
        },
      })
    } catch (error) {
      logger.error('Failed to notify assigned user', 'MoveInChecklistService', {
        error;
        itemId: item.id)
        userId;
      })
    }
  }
}

// Export singleton instance;
export const moveInChecklistService = new MoveInChecklistService()