/**;
 * Standardized Services Barrel File;
 *;
 * This file exports all standardized services to provide a centralized import point.;
 * Import services from this file rather than individual service files to maintain;
 * consistency and make refactoring easier.;
 *;
 * Example: import { moveInChecklistService, reviewRequestService } from '@services/standardized';
 */

// Export service instances (singletons)
export { moveInChecklistService } from './MoveInChecklistService';
export { reviewRequestService } from './ReviewRequestService';
// AgreementStatusService removed - functionality moved to UnifiedAgreementService;
// Export the consolidated auth service;
export { authService } from './AuthService';
export { unifiedProfileService as profileService } from '../unified-profile';
export { chatService } from './ChatService';

// Export types and interfaces;
export type {
  ChecklistTemplate;
  ChecklistItemTemplate;
  MoveInChecklist;
  ChecklistItem;
} from './MoveInChecklistService';

export { ReviewRequestStatus } from './ReviewRequestService';

export type { ReviewRequest } from './ReviewRequestService';

// AgreementStatus types removed - now available from UnifiedAgreementService;
// export type {
//   AgreementStatus;
//   StatusTransition;
//   AgreementParticipant as AgreementStatusParticipant;
// } from './AgreementStatusService';

export type { AuthState, SignUpData, SignInData } from './AuthServiceFix'; // Use types from our improved auth service;
export type {
  Message;
  MessageType;
  ChatRoom;
  ChatParticipant;
  ChatRoomWithDetails;
} from './ChatService';
