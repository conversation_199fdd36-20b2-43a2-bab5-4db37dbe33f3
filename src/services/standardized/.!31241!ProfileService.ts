import React from 'react';
/**;
 * ProfileService - Manages user profiles;
 *;
 * This service handles all operations related to user profiles including creation;
 * retrieval, updating, and searching. It integrates with fraud detection and;
 * provides caching for better performance.;
 */

import type { ApiResponse } from '@utils/api';
import { ApiService } from '@utils/api';
import { logger } from '@utils/logger';
import { supabase } from '@utils/supabaseUtils';
import { FraudDetectionService } from '@services/fraudDetectionService';
import { getProfileRepository } from '@core/repositories/RepositoryFactory';

// Extend the ProfileRepository interface to include all required methods;
interface ProfileRepository {
  // Core methods from the actual repository;
  getById(id: string): Promise<Profile | null>; // Allow null return;
  create(data: Partial<Profile>): Promise<Profile>
  update(id: string, data: Partial<Profile>): Promise<Profile>
  // Extended methods needed by the service;
  // We'll implement these in the service itself if needed;
  getByIdWithRelations? (id   : string): Promise<ProfileWithRelations | null>
  getByEmail?(email: string): Promise<Profile | null>
  getByUsername?(username: string): Promise<Profile | null>
  updateVerificationStatus?(profileId: string
    verificationType: string
    status: boolean): Promise<Profile>
  updateProfileCompletion? (profileId  : string): Promise<Profile>
  updateHousemateProfile?(data: HousemateFormData): Promise<HousemateProfile>
  getAll?(limit?: number offset?: number): Promise<Profile[]>
  getHousemateProfiles? (limit? : number offset?: number): Promise<ProfileWithRelations[]>
  search? (term : string): Promise<Profile[]> {
  getByRole? (role : string): Promise<Profile[]> {
  advancedSearch? (criteria : any): Promise<Profile[]> {
} {
import { cacheService } from '@services/cacheService'
import { CacheCategory CacheStorage } from '@core/types/cacheTypes';
import { resumableUpload } from '@utils/resumableUpload';

// Define the types directly to avoid import issues;
interface Profile {
  id: string,
  full_name?: string | null,
  email?: string | null,
  avatar_url?: string | null,
  username?: string | null,
  bio?: string | null,
  created_at?: string | null,
  updated_at?: string | null,
  [key: string]: any; // Allow additional properties;
}

interface ProfileWithRelations extends Profile {
  housemate_profile?: HousemateProfile | null,
  [key: string]: any; // Allow additional properties;
}

interface HousemateProfile {
  id: string,
  user_id: string,
  preferences?: any | null,
  [key: string]: any; // Allow additional properties;
}

interface HousemateFormData {
  user_id: string; // Use user_id instead of profile_id;
  preferences?: any | null,
  location?: string | null,
  budget?: number | null,
  [key: string]: any; // Allow additional properties;
}

// Add missing type for ProfileWithPersonalInfo;
interface ProfileWithPersonalInfo extends Profile { id: string; // Ensure id is required;
  personal_info?: any | null }

// Add missing type for FraudDetectionResult;
interface FraudDetectionResult {
  flagged: boolean; // Corrected to match likely imported type;
  fraudScore: number; // Corrected to match likely imported type;
  risk?: 'low' | 'medium' | 'high',
  flags?: any[]; // Optional for detailed reporting;
}

// Interface to match the actual fraud detection service response;
interface RawFraudDetectionResult {
  isFraud?: boolean,
  score?: number,
  risk?: string,
  flags?: any[]
}

// Helper type for type casting;
type AnyProfile = any; // Used for type casting from imported types;
// Create an instance of FraudDetectionService;
const fraudDetectionService = new FraudDetectionService()
// Store the last fetch timestamps to prevent frequent repeated fetches;
const lastFetchTimestamps = new Map<string, number>()
const lastCacheClearTimestamps = new Map<string, number>()
// Throttle duration in milliseconds;
const THROTTLE_DURATION = 2000; // 2 seconds;
/**;
 * Service for managing user profiles;
 */
export class ProfileService extends ApiService {
  /** Repository for database operations */
  private profileRepository = getProfileRepository(); // Removed explicit type annotation;
  /**;
   * Handle profile-related errors consistently;
   * @param operation The operation that failed;
   * @param error The error that occurred;
   * @param context Additional context for logging;
   * @return s ApiResponse with appropriate error details;
   * @private;
   */
  private handleProfileError<T>(
    operation: string,
    error: Error,
    context: any = {}
  ): ApiResponse<T>
    // Create a unique key for this error handling stack;
    const errorKey = `${operation}-${error.name || 'unknown'}`;

    // Track recursion depth to prevent stack overflow;
    if (!this._errorRecursionCount) {
      this._errorRecursionCount = new Map<string, number>()
    }

    // Increment the recursion count for this specific error key;
    const currentCount = (this._errorRecursionCount.get(errorKey) || 0) + 1;
    this._errorRecursionCount.set(errorKey, currentCount)
    // If we're in too deep recursion, return a safe fallback response;
    if (currentCount > 3) {
      console.warn(`Preventing error recursion for ${errorKey}` depth: ${currentCount}`)
      // Reset counter after returning;
      setTimeout(() => this._errorRecursionCount.delete(errorKey), 0)
      return { data: null as T;
        error: 'A service error occurred. Please try again later.',
        status: 500 }
    }

    try {
      // Special handling for network errors;
      if (
        error.name = == 'AbortError' ||;
        error.message.includes('net') ||;
        error.message.includes('fetch') ||;
        error.message.includes('network') ||;
        error.message.includes('timeout')
      ) {
        // Reset counter after return ing;
        setTimeout(() = > this._errorRecursionCount.delete(errorKey), 0)
        return {
          data: null as T;
          error: 'Network connection issue. Please check your internet connection and try again.',
          status: 503, // Service Unavailable;
        }
      }

      // Log the error for debugging;
      console.error(`ProfileService ${operation} error:`, error, context)
      // Reset counter after return ing;
      setTimeout(() = > this._errorRecursionCount.delete(errorKey), 0)
      return { data: null as T;
        error: error.message || 'Unknown error occurred',
        status: context.status || 500 }
    } catch (secondaryError) { // If error handling itself fails, return a safe response;
      console.warn('Error in error handler:', secondaryError)
      // Reset counter after returning;
      setTimeout(() = > this._errorRecursionCount.delete(errorKey), 0)
      return {
        data: null as T;
        error: 'An unexpected error occurred',
        status: 500 }
    }
  }

  /**;
   * Get the current user's profile with caching support;
   * @return s ApiResponse with the current user's profile or error;
   */
  async getCurrentProfile(): Promise<ApiResponse<Profile>>
    try {
      // Handle authentication and ensure the user is logged in;
      const authError = await this.ensureAuthenticated()
      if (authError) {
        return { data: null; error: authError, status: 401 }
      }

      // Get the current user using Supabase auth;
      const { data: { user  }
      } = await supabase.auth.getUser()
      if (!user) {
        return { data: null; error: 'User not found', status: 404 }
      }

      // Log the operation for auditing purposes;
      this.logOperation('GET', 'profile/current')
      // Use caching for better performance;
      // Cache key includes the user ID to make it unique per user;
      const cacheKey = `current_profile_${user.id}`;

      const profile = await cacheService.get(
        cacheKey;
        async () = > this.profileRepository.getById(user.id);
        { category: CacheCategory.SHORT, storage: CacheStorage.BOTH }
      )
      if (!profile) {
        return { data: null; error: 'Profile not found', status: 404 }
      }

      return { data: profile; error: null, status: 200 }
    } catch (error) { // Use standardized error handling;
      return this.handleProfileError('getCurrentProfile'; error as Error, {
        userId: (await supabase.auth.getUser()).data.user? .id })
    }
  }

  /**;
   * Get a profile by ID with related data;
   * @param id Profile ID;
   * @return s ApiResponse with profile and related data;
   */
  async getProfileById(id   : string): Promise<ApiResponse<ProfileWithRelations>>
    try {
      if (!id) {
        return { data: null error: 'Profile ID is required'; status: 400 }
      }

      this.logOperation('GET', `profile/${id}`)

      // Use caching for better performance;
      const cacheKey = `profile_${id}`

      const profile = await cacheService.get<ProfileWithRelations>(
        cacheKey;
        async () => {
  // Check if the method exists, otherwise implement a fallback;
          const repo = this.profileRepository as any;
          if (repo.getByIdWithRelations) {
            return repo.getByIdWithRelations(id)
          } else {
            // Fallback implementation;
            const baseProfile = await this.profileRepository.getById(id)
            if (!baseProfile) return null;
            // Convert to ProfileWithRelations (simplified)
            return baseProfile as unknown as ProfileWithRelations;
          }
        },
        { category: CacheCategory.SHORT, storage: CacheStorage.MEMORY }
      )
      if (!profile) {
        return { data: null; error: 'Profile not found', status: 404 }
      }

      return { data: profile; error: null, status: 200 }
    } catch (error) {
      return this.handleProfileError<ProfileWithRelations>('getProfileById'; error as Error, {
        id;
      })
    }
  }

  /**;
   * Direct database query for a profile by ID (bypasses caching for debugging)
   * With throttling to prevent excessive fetches;
   * @param id Profile ID;
   * @return s ApiResponse with profile and related data;
   */
  async getProfileByIdDirect(id: string): Promise<ApiResponse<ProfileWithRelations>>
    try {
      if (!id) {
        return { data: null; error: 'Profile ID is required', status: 400 }
      }

      // Check if we've fetched this profile recently;
      const now = Date.now()
      const lastFetch = lastFetchTimestamps.get(id) || 0;
      // If fetched too recently, use cached error to avoid hammering the database;
      if (now - lastFetch < 2000) { // 2 second throttle;
        return {
          data: null;
          error: 'Rate limited. Please try again in a few seconds.',
          status: 429 }
      }

      // Update fetch timestamp;
      lastFetchTimestamps.set(id, now)
      this.logOperation('GET_DIRECT', `profile/${id}`)
      try {
        // Fetch the profile directly from the database;
        // Simplified query to avoid timeout issues - only get basic profile data;
        const { data, error  } = await supabase.from('user_profiles')
          .select('*')
          .eq('id', id)
          .single()
        if (error) {
          throw error;
        }

        if (!data) {
          return {
            data: null;
            error: `Profile not found with ID: ${id}`;
            status: 404,
          }
        }

        // Transform the data to match our expected format;
        const profileWithRelations: ProfileWithRelations =,
          this.mapDatabaseToProfileWithRelations(data)
        return { data: profileWithRelations; error: null, status: 200 }
      } catch (error: any) { // Special handling for common Supabase errors;
        if (error.code = == 'PGRST301') {
          return {
            data: null;
            error: 'Profile not found',
            status: 404 }
        }

        return this.handleProfileError<ProfileWithRelations>(
          'getProfileByIdDirect';
          error instanceof Error ? error  : new Error(String(error))
          { id }
        )
      }
    } catch (outerError: any) {
      // This catches any unexpected errors outside the main try block;
      return {
        data: null;
        error: outerEr,