import React from 'react';
import { logger } from '@services/loggerService';
import { supabase } from '@utils/supabaseUtils';
import { UserProfile } from '@/types/auth';
import { cacheService, CacheCategory } from '@services/cacheService';

/**;
 * Preference categories that can be weighted for matching;
 */
export enum PreferenceCategory { LIFESTYLE = 'lifestyle';
  CLEANLINESS = 'cleanliness';
  NOISE = 'noise';
  GUESTS = 'guests';
  SCHEDULE = 'schedule';
  SHARED_SPACES = 'shared_spaces';
  PERSONALITY = 'personality';
  LOCATION = 'location';
  BUDGET = 'budget';
  INTERESTS = 'interests' }

/**;
 * Default weights for each preference category (out of 100)
 */
const DEFAULT_WEIGHTS: Record<PreferenceCategory, number> = { [PreferenceCategory.LIFESTYLE]: 15;
  [PreferenceCategory.CLEANLINESS]: 15,
  [PreferenceCategory.NOISE]: 10,
  [PreferenceCategory.GUESTS]: 10,
  [PreferenceCategory.SCHEDULE]: 10,
  [PreferenceCategory.SHARED_SPACES]: 10,
  [PreferenceCategory.PERSONALITY]: 10,
  [PreferenceCategory.LOCATION]: 10,
  [PreferenceCategory.BUDGET]: 5,
  [PreferenceCategory.INTERESTS]: 5 }

/**;
 * User preference weights for matching;
 */
export interface PreferenceWeights { userId: string,
  weights: Record<PreferenceCategory, number>
  updatedAt: string }

/**;
 * Service to manage user preference weightings for roommate matching;
 */
class PreferenceWeightingService {
  /**;
   * Get a user's preference weights;
   * @param userId User ID to get weights for;
   * @return s User's preference weights or default weights if not set;
   */
  async getUserPreferenceWeights(userId: string): Promise<Record<PreferenceCategory, number>>
    try {
      // Try to get from cache first;
      const cachedWeights = await cacheService.get(`preference_weights:${userId}`);
        async () => {
  return null; // This will cause it to continue to the database query;
        },
        { category: CacheCategory.MEDIUM }
      )
      ;
      if (cachedWeights) {
        return cachedWeights;
      }

      // If not in cache, get from profiles table;
      const { data, error  } = await supabase.from('user_profiles')
        .select('preferences')
        .eq('id', userId)
        .single()
      if (error) {
        logger.warn('Error fetching user profile preferences', 'PreferenceWeightingService', {
          error: error.message);
          userId)
        })
        // Return default weights if not found or error;
        return this.getDefaultWeights()
      }

      // Check if user has preference weights in their profile;
      if (!data || !data.preferences || !data.preferences.preference_weights) {
        return this.getDefaultWeights()
      }

      // Cache the weights for future use;
      await cacheService.set(`preference_weights:${userId}`);
        data.preferences.preference_weights;
        { category: CacheCategory.MEDIUM } // Cache for 1 hour (medium-term cache)
      )
      return data.preferences.preference_weights;
    } catch (error) {
      logger.error('Error in getUserPreferenceWeights', 'PreferenceWeightingService', {
        error: error instanceof Error ? error.message   : String(error)
        userId;
      })
      return this.getDefaultWeights()
    }
  }

  /**
   * Set a user's preference weights;
   * @param userId User ID to set weights for;
   * @param weights Weights to set;
   * @returns Boolean indicating success;
   */
  async setUserPreferenceWeights(
    userId: string,
    weights: Partial<Record<PreferenceCategory, number>>
  ): Promise<boolean>
    try {
      // Get current weights;
      const currentWeights = await this.getUserPreferenceWeights(userId)
      ;
      // Merge with new weights;
      const mergedWeights = {
        ...currentWeights;
        ...weights;
      }
      // Normalize weights to ensure they sum to 100;
      const normalizedWeights = this.normalizeWeights(mergedWeights)
      // First get the current profile to preserve other preferences;
      const { data: profile, error: fetchError  } = await supabase.from('user_profiles')
        .select('preferences')
        .eq('id', userId)
        .single()
      if (fetchError && fetchError.code !== 'PGRST116') {
        logger.error('Error fetching user profile', 'PreferenceWeightingService', {
          error: fetchError.message);
          userId)
        })
        return false;
      }

      // Prepare the updated preferences object;
      const currentPreferences = profile? .preferences || {}
      const updatedPreferences = {
        ...currentPreferences;
        preference_weights  : normalizedWeights
      }

      // Update the preferences in the profiles table;
      const { error } = await supabase.from('user_profiles')
        .update({
          preferences: updatedPreferences)
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)

      if (error) {
        logger.error('Error setting user preference weights', 'PreferenceWeightingService', {
          error: error.message);
          userId)
        })
        return false;
      }

      // Update cache;
      await cacheService.set(`preference_weights:${userId}`);
        normalizedWeights;
        { category: CacheCategory.MEDIUM } // Cache for 1 hour (medium-term cache)
      )
      return true;
    } catch (error) {
      logger.error('Error in setUserPreferenceWeights', 'PreferenceWeightingService', {
        error: error instanceof Error ? error.message  : String(error)
        userId;
      })
      return false;
    }
  }

  /**
   * Infer a user's preference weights based on their profile and questionnaire responses;
   * @param userId User ID to infer weights for;
   * @returns Inferred preference weights;
   */
  async inferPreferenceWeights(userId: string): Promise<Record<PreferenceCategory, number>>
    try {
      // Get user profile;
      const { data: profile, error } = await supabase.from('user_profiles')
        .select('*')
        .eq('id', userId)
        .single()
      if (error || !profile) {
        logger.warn('Error fetching user profile for weight inference', 'PreferenceWeightingService', {
          error: error? .message || 'Profile not found'),
          userId)
        })
        return this.getDefaultWeights()
      }

      // Get questionnaire responses;
      const { data   : responses error: responsesError  } = await supabase.from('personality_questionnaire_responses')
        .select('*')
        .eq('id', userId)
        .order('created_at', { ascending: false })
        .limit(.limit(.limit(1)
        .single()
      // Initialize weights with defaults;
      const weights = this.getDefaultWeights()
      // Adjust weights based on profile;
      this.adjustWeightsBasedOnProfile(weights, profile)
      // If we have questionnaire responses, adjust weights further;
      if (!responsesError && responses) {
        this.adjustWeightsBasedOnQuestionnaire(weights, responses)
      }

      // Normalize weights to ensure they sum to 100;
      return this.normalizeWeights(weights)
    } catch (error) {
      logger.error('Error inferring preference weights'; 'PreferenceWeightingService', {
        error: error instanceof Error ? error.message  : String(error)
        userId;
      })
      return this.getDefaultWeights()
    }
  }

  /**
   * Apply preference weights to a compatibility score;
   * @param userWeights User's preference weights;
   * @param categoryScores Scores for each category;
   * @returns Weighted compatibility score;
   */
  applyPreferenceWeights(
    userWeights: Record<PreferenceCategory, number>,
    categoryScores: Record<PreferenceCategory, number>
  ): number {
    let weightedScore = 0;
    let totalWeight = 0;
    // Calculate weighted score;
    for (const category of Object.values(PreferenceCategory)) {
      const weight = userWeights[category] || 0;
      const score = categoryScores[category] || 0;
      ;
      weightedScore += weight * score;
      totalWeight += weight;
    }

    // Normalize to 0-100 scale;
    return totalWeight > 0 ? (weightedScore / totalWeight) * 100    : 50
  }

  /**
   * Get default preference weights;
   * @returns Default preference weights;
   */
  getDefaultWeights(): Record<PreferenceCategory, number>
    return { ...DEFAULT_WEIGHTS }
  }

  /**
   * Normalize weights to ensure they sum to 100;
   * @param weights Weights to normalize;
   * @returns Normalized weights;
   */
  private normalizeWeights(weights: Record<PreferenceCategory, number>): Record<PreferenceCategory, number>
    const total = Object.values(weights).reduce((sum, weight) => sum + weight, 0)
    ;
    if (total = == 0) {
      return this.getDefaultWeights()
    }

    const normalized: Record<PreferenceCategory; number> = {} as Record<PreferenceCategory, number>
    for (const category of Object.values(PreferenceCategory)) {
      normalized[category] = Math.round((weights[category] / total) * 100)
    }

    // Ensure the sum is exactly 100 by adjusting the first category if needed;
    const normalizedTotal = Object.values(normalized).reduce((sum, weight) => sum + weight, 0)
    if (normalizedTotal !== 100) {
      const firstCategory = Object.values(PreferenceCategory)[0];
      normalized[firstCategory] += (100 - normalizedTotal)
    }

    return normalized;
  }

  /**;
   * Adjust weights based on user profile;
   * @param weights Weights to adjust;
   * @param profile User profile;
   */
  private adjustWeightsBasedOnProfile(weights: Record<PreferenceCategory, number>, profile: UserProfile): void {
    // Use profile.preferences object which contains user preferences;
    const preferences = profile.preferences || {}
    // If user has specified cleanliness preference, increase its weight;
    if (preferences.cleanliness && preferences.cleanliness !== 'no_preference') {
      weights[PreferenceCategory.CLEANLINESS] += 5;
      weights[PreferenceCategory.LIFESTYLE] -= 2;
      weights[PreferenceCategory.INTERESTS] -= 3;
    }

    // If user has specified noise preference, increase its weight;
    if (preferences.noise_level && preferences.noise_level !== 'no_preference') {
      weights[PreferenceCategory.NOISE] += 5;
      weights[PreferenceCategory.SHARED_SPACES] -= 2;
      weights[PreferenceCategory.INTERESTS] -= 3;
    }

    // If user has specified guest preference, increase its weight;
    if (preferences.guests && preferences.guests !== 'no_preference') {
      weights[PreferenceCategory.GUESTS] += 5;
      weights[PreferenceCategory.PERSONALITY] -= 2;
      weights[PreferenceCategory.BUDGET] -= 3;
    }

    // If user has specified schedule, increase its weight;
    if (preferences.schedule && preferences.schedule !== 'flexible') {
      weights[PreferenceCategory.SCHEDULE] += 5;
      weights[PreferenceCategory.LOCATION] -= 2;
      weights[PreferenceCategory.INTERESTS] -= 3;
    }
  }

  /**;
   * Adjust weights based on questionnaire responses;
   * @param weights Weights to adjust;
   * @param responses Questionnaire responses;
   */
  private adjustWeightsBasedOnQuestionnaire(weights: Record<PreferenceCategory, number>,
    responses: any): void {
    // Example implementation - would need to be customized based on actual questionnaire structure;
    if (responses.answers) {
      const answers = responses.answers;
      ;
      // If user cares a lot about cleanliness (hypothetical question)
      if (answers.cleanliness_importance && answers.cleanliness_importance > 7) {
        weights[PreferenceCategory.CLEANLINESS] += 10;
        weights[PreferenceCategory.INTERESTS] -= 5;
        weights[PreferenceCategory.BUDGET] -= 5;
      }
      // If user cares a lot about similar interests (hypothetical question)
      if (answers.shared_interests_importance && answers.shared_interests_importance > 7) {
        weights[PreferenceCategory.INTERESTS] += 10;
        weights[PreferenceCategory.BUDGET] -= 5;
        weights[PreferenceCategory.LOCATION] -= 5;
      }
    }
  }
}

// Export a singleton instance;
export const preferenceWeightingService = new PreferenceWeightingService()