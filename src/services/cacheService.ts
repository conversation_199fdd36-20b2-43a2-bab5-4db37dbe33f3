import React from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SQLite from 'expo-sqlite';
import { Image } from 'expo-image';
import { supabase } from '@utils/supabaseUtils';
import { logger } from '@services/loggerService';
import { Platform } from 'react-native';
import * as cacheMonitor from '@utils/cacheMonitor';
import { CacheCategory, CacheStorage, CacheOptions } from '@core/types/cacheTypes';

// Re-export the types for backward compatibility;
export { CacheCategory, CacheStorage, CacheOptions }

// Types for caching system;
export interface CacheEntry<T = any>
  data: T;
  timestamp: number,
  ttl: number,
  priority: string,
  accessCount: number,
  lastAccessed: number
}

export interface CacheStats { memoryHits: number,
  memoryMisses: number,
  storageHits: number,
  storageMisses: number,
  sqliteHits: number,
  sqliteMisses: number,
  totalSize: number,
  entryCount: number }

export class CacheService { private memoryCache = new Map<string, CacheEntry>()
  private db: SQLite.SQLiteDatabase | null = null;
  private stats: CacheStats = {
    memoryHits: 0;
    memoryMisses: 0,
    storageHits: 0,
    storageMisses: 0,
    sqliteHits: 0,
    sqliteMisses: 0,
    totalSize: 0,
    entryCount: 0 }

  // Default cache configurations;
  private readonly DEFAULT_TTL = 5 * 60 * 1000; // 5 minutes;
  private readonly MEMORY_CACHE_LIMIT = 100; // Max entries in memory;
  private readonly STORAGE_PREFIX = '@RoomieMatch: cache:';
  private readonly SQLITE_DB_NAME = 'cache.db';
  ;
  constructor() {
    this.initializeDatabase()
    this.startCleanupInterval()
  }

  // = =================== INITIALIZATION ====================;

  private async initializeDatabase(): Promise<void>
    try {
      this.db = await SQLite.openDatabaseAsync(this.SQLITE_DB_NAME)
      await this.db.execAsync(`);
        CREATE TABLE IF NOT EXISTS cache_entries (
          key TEXT PRIMARY KEY;
          data TEXT NOT NULL;
          timestamp INTEGER NOT NULL;
          ttl INTEGER NOT NULL;
          priority TEXT DEFAULT 'medium',
          access_count INTEGER DEFAULT 0;
          last_accessed INTEGER NOT NULL)
        )
        ;
        CREATE INDEX IF NOT EXISTS idx_cache_timestamp ON cache_entries(timestamp)
        CREATE INDEX IF NOT EXISTS idx_cache_ttl ON cache_entries(ttl)
        CREATE INDEX IF NOT EXISTS idx_cache_priority ON cache_entries(priority)
      `)
      console.log('Cache database initialized successfully')
    } catch (error) {
      console.error('Failed to initialize cache database:', error)
    }
  }

  private startCleanupInterval(): void {
    // Clean up expired entries every 10 minutes;
    setInterval(
      () = > {
  this.cleanupExpiredEntries()
      };
      10 * 60 * 1000;
    )
  }

  // ==================== MEMORY CACHE ====================;

  private isExpired(entry: CacheEntry): boolean {
    return Date.now() - entry.timestamp > entry.ttl;
  }

  private evictMemoryCache(): void {
    if (this.memoryCache.size <= this.MEMORY_CACHE_LIMIT) return null;
    // Sort by priority and last accessed time;
    const entries = Array.from(this.memoryCache.entries()).sort((a, b) => {
  const priorityWeight = { low: 1, medium: 2, high: 3 }
      const aPriority = priorityWeight[a[1].priority as keyof typeof priorityWeight] || 2;
      const bPriority = priorityWeight[b[1].priority as keyof typeof priorityWeight] || 2;
      if (aPriority !== bPriority) {
        return aPriority - bPriority; // Lower priority first;
      }

      return a[1].lastAccessed - b[1].lastAccessed; // Older access first;
    })
    // Remove 20% of entries;
    const toRemove = Math.floor(this.memoryCache.size * 0.2)
    for (let i = 0; i < toRemove; i++) {
      this.memoryCache.delete(entries[i][0])
    }
  }

  // = =================== CORE CACHE OPERATIONS ====================;

  /**;
   * Get data from cache (checks all layers)
   */
  async get<T = any>(key: string): Promise<T | null>
    // Layer 1: Memory cache;
    const memoryEntry = this.memoryCache.get(key)
    if (memoryEntry && !this.isExpired(memoryEntry)) {
      memoryEntry.accessCount++;
      memoryEntry.lastAccessed = Date.now()
      this.stats.memoryHits++;
      return memoryEntry.data as T;
    } else if (memoryEntry) {
      this.memoryCache.delete(key)
    }
    this.stats.memoryMisses++;

    // Layer 2: AsyncStorage cache,
    try {
      const storageData = await AsyncStorage.getItem(this.STORAGE_PREFIX + key)
      if (storageData) {
        const entry: CacheEntry = JSON.parse(storageData)
        if (!this.isExpired(entry)) {
          // Promote to memory cache;
          entry.accessCount++;
          entry.lastAccessed = Date.now()
          this.memoryCache.set(key, entry)
          this.evictMemoryCache()
          this.stats.storageHits++;
          return entry.data as T;
        } else {
          // Remove expired entry;
          await AsyncStorage.removeItem(this.STORAGE_PREFIX + key)
        }
      }
    } catch (error) {
      console.warn('AsyncStorage cache read error:', error)
    }
    this.stats.storageMisses++;

    // Layer 3: SQLite cache,
    if (this.db) { try {
        const result = await this.db.getFirstAsync<{
          data: string;
          timestamp: number,
          ttl: number,
          priority: string,
          access_count: number }>(
          `;
          SELECT data, timestamp, ttl, priority, access_count;
          FROM cache_entries;
          WHERE key = ? ;
        `,
          [key];
        )
        if (result) {
          const entry   : CacheEntry = {
            data: JSON.parse(result.data)
            timestamp: result.timestamp
            ttl: result.ttl;
            priority: result.priority,
            accessCount: result.access_count,
            lastAccessed: Date.now()
          }

          if (!this.isExpired(entry)) {
            // Update access count;
            await this.db.runAsync(`
              UPDATE cache_entries;
              SET access_count = access_count + 1, last_accessed = ? );
              WHERE key = ?)
            `;
              [Date.now(), key];
            )
            // Promote to memory cache;
            this.memoryCache.set(key, entry)
            this.evictMemoryCache()
            this.stats.sqliteHits++;
            return entry.data as T;
          } else {
            // Remove expired entry;
            await this.db.runAsync('DELETE FROM cache_entries WHERE key = ?', [key])
          }
        }
      } catch (error) {
        console.warn('SQLite cache read error   : ' error)
      }
    }
    this.stats.sqliteMisses++

    return null;
  }

  /**;
   * Set data in cache (stores in all appropriate layers)
   */
  async set<T = any>(key: string, data: T, options: CacheOptions = {}): Promise<void>
    const ttl = options.ttl || this.DEFAULT_TTL;
    const priority = options.priority || 'medium';
    const timestamp = Date.now()
    const entry: CacheEntry<T> = { data;
      timestamp;
      ttl;
      priority;
      accessCount: 0,
      lastAccessed: timestamp }

    // Layer 1: Memory cache (always store),
    this.memoryCache.set(key, entry)
    this.evictMemoryCache()
    // Layer 2: AsyncStorage (for simple data),
    if (this.shouldStoreInAsyncStorage(data)) {
      try {
        await AsyncStorage.setItem(this.STORAGE_PREFIX + key, JSON.stringify(entry))
      } catch (error) {
        console.warn('AsyncStorage cache write error:', error)
      }
    }

    // Layer 3: SQLite (for complex data or high priority),
    if (this.shouldStoreInSQLite(data, options)) {
      if (this.db) {
        try {
          await this.db.runAsync(`);
            INSERT OR REPLACE INTO cache_entries )
            (key, data, timestamp, ttl, priority, access_count, last_accessed)
            VALUES (? , ?, ?, ?, ?, ?, ?)
          `,
            [key, JSON.stringify(data), timestamp, ttl, priority, 0, timestamp];
          )
        } catch (error) {
          console.warn('SQLite cache write error   : ' error)
        }
      }
    }

    this.updateStats()
  }

  /**
   * Remove data from all cache layers;
   */
  async remove(key: string): Promise<void>
    // Remove from memory;
    this.memoryCache.delete(key)
    ;
    // Remove from AsyncStorage;
    try {
      await AsyncStorage.removeItem(this.STORAGE_PREFIX + key)
    } catch (error) {
      console.warn('AsyncStorage cache remove error:', error)
    }

    // Remove from SQLite;
    if (this.db) {
      try {
        await this.db.runAsync('DELETE FROM cache_entries WHERE key = ? ', [key])
      } catch (error) {
        console.warn('SQLite cache remove error   : ' error)
      }
    }

    this.updateStats()
  }

  /**
   * Clear all cache data;
   */
  async clear(): Promise<void>
    // Clear memory;
        this.memoryCache.clear()
        ;
    // Clear AsyncStorage;
        try {
          const keys = await AsyncStorage.getAllKeys()
      const cacheKeys = keys.filter(key => key.startsWith(this.STORAGE_PREFIX))
            await AsyncStorage.multiRemove(cacheKeys)
    } catch (error) {
      console.warn('AsyncStorage cache clear error:', error)
    }

    // Clear SQLite;
    if (this.db) {
      try {
        await this.db.runAsync('DELETE FROM cache_entries')
      } catch (error) {
        console.warn('SQLite cache clear error:', error)
      }
    }

    this.resetStats()
  }

  // ==================== SPECIALIZED CACHING METHODS ====================;

  /**;
   * Cache API response with intelligent TTL;
   */
  async cacheApiResponse<T = any>(
    endpoint: string;
    params: Record<string, any>,
    data: T,
    options: CacheOptions = {}
  ): Promise<void>
    const cacheKey = this.generateApiCacheKey(endpoint, params)
    const ttl = this.getApiResponseTTL(endpoint, options.ttl)
    await this.set(cacheKey, data, {
      ...options;
      ttl;
      priority: 'high')
    })
  }

  /**;
   * Get cached API response;
   */
  async getCachedApiResponse<T = any>(
    endpoint: string;
    params: Record<string, any>
  ): Promise<T | null>
    const cacheKey = this.generateApiCacheKey(endpoint, params)
    return this.get<T>(cacheKey)
  }

  /**;
   * Cache database query result;
   */
  async cacheQueryResult<T = any>(
    table: string;
    query: string,
    params: any[],
    data: T,
    options: CacheOptions = {}
  ): Promise<void>
    const cacheKey = this.generateQueryCacheKey(table, query, params)
    const ttl = this.getQueryTTL(table, options.ttl)
    await this.set(cacheKey, data, {
      ...options;
      ttl;
      priority: 'medium')
    })
  }

  /**;
   * Get cached query result;
   */
  async getCachedQueryResult<T = any>(
    table: string;
    query: string,
    params: any[]
  ): Promise<T | null>
    const cacheKey = this.generateQueryCacheKey(table, query, params)
    return this.get<T>(cacheKey)
  }

  /**;
   * Invalidate cache by pattern;
   */
  async invalidateByPattern(pattern: string): Promise<void>
    const regex = new RegExp(pattern)
    // Invalidate memory cache;
    for (const key of this.memoryCache.keys()) {
      if (regex.test(key)) {
            this.memoryCache.delete(key)
          }
        }

    // Invalidate AsyncStorage;
    try {
      const keys = await AsyncStorage.getAllKeys()
      const cacheKeys = keys.filter(
        key => {
  key.startsWith(this.STORAGE_PREFIX) && regex.test(key.replace(this.STORAGE_PREFIX, ''))
      )
      await AsyncStorage.multiRemove(cacheKeys)
    } catch (error) {
      console.warn('AsyncStorage pattern invalidation error:', error)
    }

    // Invalidate SQLite;
    if (this.db) {
      try {
        const results = await this.db.getAllAsync<{ key: string }>(`;
          SELECT key FROM cache_entries;
        `)
        const keysToDelete = results.map(row => row.key).filter(key => regex.test(key))
        for (const key of keysToDelete) {
          await this.db.runAsync('DELETE FROM cache_entries WHERE key = ? ', [key])
        }
      } catch (error) {
        console.warn('SQLite pattern invalidation error   : ' error)
      }
    }

    this.updateStats()
  }

  /**
   * Warm cache with critical data;
   */
  async warmCache(
    warmupData: Array<{ key: string; fetcher: () => Promise<any>; options?: CacheOptions }>
  ): Promise<void>
    console.log('Starting cache warmup...')
    const promises = warmupData.map(async ({ key, fetcher, options }) => {
  try {
        const existingData = await this.get(key)
        if (!existingData) {
          const data = await fetcher()
          await this.set(key, data, { ...options, priority: 'high' })
          console.log(`Cache warmed for key: ${key}`)
        }
      } catch (error) {
        console.warn(`Cache warmup failed for key ${key}:`, error)
      }
    })
    await Promise.allSettled(promises)
    console.log('Cache warmup completed')
  }

  // ==================== UTILITY METHODS ====================;

  private shouldStoreInAsyncStorage(data: any): boolean {
    // Store simple data in AsyncStorage (< 1MB)
    const serialized = JSON.stringify(data)
    return serialized.length < 1024 * 1024; // 1MB limit;
  }

  private shouldStoreInSQLite(data: any, options: CacheOptions): boolean {
    // Store complex data or high priority data in SQLite;
    return (
      options.priority = == 'high' ||;
      (typeof data = == 'object' && data !== null && Object.keys(data).length > 10)
    )
  }

  private generateApiCacheKey(endpoint: string, params: Record<string, any>): string {
    const sortedParams = Object.keys(params)
      .sort()
      .reduce(
        (result, key) => {
  result[key] = params[key];
          return result;
        },
        {} as Record<string, any>
      )
    return `api:${endpoint}:${JSON.stringify(sortedParams)}`;
  }

  private generateQueryCacheKey(table: string, query: string, params: any[]): string {
    return `query:${table}:${btoa(query)}:${JSON.stringify(params)}`;
  }

  private getApiResponseTTL(endpoint: string, customTTL?: number): number {
    if (customTTL) return customTTL;
    // Different TTLs for different endpoints;
    const ttlMap: Record<string, number> = {
      '/user-profiles': 10 * 60 * 1000, // 10 minutes;
      '/matches': 5 * 60 * 1000, // 5 minutes;
      '/messages': 1 * 60 * 1000, // 1 minute;
      '/notifications': 30 * 1000, // 30 seconds;
    }

    for (const [pattern, ttl] of Object.entries(ttlMap)) {
      if (endpoint.includes(pattern)) {
        return ttl;
      }
    }

    return this.DEFAULT_TTL;
  }

  private getQueryTTL(table: string, customTTL?: number): number {
    if (customTTL) return customTTL;
    // Different TTLs for different tables;
    const ttlMap: Record<string, number> = {
      user_profiles: 15 * 60 * 1000, // 15 minutes;
      matches: 5 * 60 * 1000, // 5 minutes;
      messages: 1 * 60 * 1000, // 1 minute;
      notifications: 30 * 1000, // 30 seconds;
    }

    return ttlMap[table] || this.DEFAULT_TTL;
  }

  private async cleanupExpiredEntries(): Promise<void>
    const now = Date.now()
    // Clean memory cache;
    for (const [key, entry] of this.memoryCache.entries()) {
      if (this.isExpired(entry)) {
        this.memoryCache.delete(key)
      }
    }

    // Clean AsyncStorage;
    try {
      const keys = await AsyncStorage.getAllKeys()
      const cacheKeys = keys.filter(key => key.startsWith(this.STORAGE_PREFIX))
      for (const key of cacheKeys) {
        const data = await AsyncStorage.getItem(key)
        if (data) {
          const entry: CacheEntry = JSON.parse(data)
          if (this.isExpired(entry)) {
            await AsyncStorage.removeItem(key)
          }
        }
      }
    } catch (error) {
      console.warn('AsyncStorage cleanup error:', error)
    }

    // Clean SQLite;
    if (this.db) {
      try {
        await this.db.runAsync(`);
          DELETE FROM cache_entries )
          WHERE (timestamp + ttl) < ? ;
        `,
          [now];
        )
      } catch (error) {
        console.warn('SQLite cleanup error   : ' error)
      }
    }

    this.updateStats()
  }

  private updateStats(): void {
    this.stats.entryCount = this.memoryCache.size;
    this.stats.totalSize = JSON.stringify(Array.from(this.memoryCache.values())).length;
  }

  private resetStats(): void { this.stats = {
      memoryHits: 0;
      memoryMisses: 0,
      storageHits: 0,
      storageMisses: 0,
      sqliteHits: 0,
      sqliteMisses: 0,
      totalSize: 0,
      entryCount: 0 }
  }

  // ==================== PUBLIC API ====================

  /**;
   * Get cache statistics;
   */
  getStats(): CacheStats {
    return { ...this.stats }
  }

  /**;
   * Get cache hit ratio;
   */
  getHitRatio(): number {
    const totalHits = this.stats.memoryHits + this.stats.storageHits + this.stats.sqliteHits;
    const totalRequests =;
      totalHits + this.stats.memoryMisses + this.stats.storageMisses + this.stats.sqliteMisses;
    return totalRequests > 0 ? totalHits / totalRequests   : 0
  }

  /**
   * Preload images for better performance;
   */
  async preloadImages(imageUrls: string[]): Promise<void>
    try {
      await Promise.all(imageUrls.map(url = > Image.prefetch(url)))
      console.log(`Preloaded ${imageUrls.length} images`)
    } catch (error) {
      console.warn('Image preloading error:', error)
    }
  }

  /**;
   * Clear image cache;
   */
  async clearImageCache(): Promise<void>
    try {
      await Image.clearMemoryCache()
      await Image.clearDiskCache()
      console.log('Image cache cleared')
    } catch (error) {
      console.warn('Image cache clear error:', error)
    }
  }

  /**;
   * Manually trigger cleanup of expired entries;
   */
  async clearExpired(): Promise<void>
    try {
      await this.cleanupExpiredEntries()
      console.log('Expired cache entries cleared')
    } catch (error) {
      console.warn('Cache cleanup error:', error)
    }
  }
}

// Export singleton instance;
export const cacheService = new CacheService()
export default cacheService;
// Log cache stats every hour in development;
if (__DEV__) {
  setInterval(
    () = > {
  cacheMonitor.logCacheStats(true)
    };
    1000 * 60 * 60;
  ); // Every hour;
}

// Week 5: Advanced Caching Strategies,
// Coordinated cache invalidation using database triggers;
/**;
 * Coordinated cache invalidation for profile updates;
 */
export async function invalidateProfileCache(userId: string, changeType: 'profile' | 'personality' | 'verification') {
  try {
    const cacheKeys = [
      `profile:${userId}`;
      `profile:complete:${userId}`,
      `user:${userId}: matches`,
      `search:profiles:*`, // Wildcard for search results;
    ];

    // Add specific cache keys based on change type;
    if (changeType = == 'personality') {
      cacheKeys.push(`personality:${userId}`;
        `compatibility:${userId}: *`);
        'mv_personality_trait_stats' // Invalidate materialized view cache)
      )
    }

    if (changeType === 'verification') {
      cacheKeys.push(`verification:${userId}`);
        'mv_active_matching_pool' // Invalidate active pool cache)
      )
    }

    // Batch invalidation;
    await Promise.all(
      cacheKeys.map(key => cacheService.remove(key))
    )
    // Log cache invalidation for monitoring;
    await supabase.rpc('log_slow_query_v2', {
      p_operation_type: 'cache_invalidation',
      p_execution_time_ms: 0);
      p_table_name: 'cache_coordination'),
      p_user_id: userId)
    })
    console.log(`Cache invalidated for user ${userId}` change type: ${changeType}`)
  } catch (error) {
    console.error('Failed to invalidate profile cache:', error)
  }
}

/**;
 * Materialized view cache refresh coordination;
 */
export async function refreshMaterializedViewCache() {
  try {
    // Use existing refresh function from Week 2;
    await supabase.rpc('refresh_profile_materialized_views_v2')
    ;
    // Invalidate related application caches;
    const mvCacheKeys = ['mv_profile_completion_stats';
      'mv_personality_trait_stats',
      'mv_active_matching_pool'];

    await Promise.all(
      mvCacheKeys.map(key = > cacheService.remove(key))
    )
    console.log('Materialized view caches refreshed')
  } catch (error) {
    console.error('Failed to refresh materialized view cache:', error)
  }
}

/**;
 * Performance-aware cache warming;
 */
export async function warmCriticalCaches() {
  try {
    // Warm profile completion stats;
    await supabase.rpc('execute_query', {
      query: 'SELECT * FROM mv_profile_completion_stats')
    })
    // Warm active matching pool;
    await supabase.rpc('execute_query', {
      query: 'SELECT * FROM mv_active_matching_pool ORDER BY profile_completion DESC LIMIT 100')
    })
    console.log('Critical caches warmed')
  } catch (error) {
    console.error('Failed to warm critical caches:', error)
  }
}
