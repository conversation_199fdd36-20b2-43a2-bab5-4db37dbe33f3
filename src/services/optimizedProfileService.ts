import React from 'react';
/**;
 * Optimized Profile Service;
 *;
 * Enhanced profile service with atomic updates, comprehensive error handling;
 * connection pooling, and proper validation;
 */

import { supabase } from '@utils/supabaseUtils';
import { ApiResponse, createSuccessResponse } from '@utils/errorHandling';
import { Profile, ProfileWithRelations } from '@types/models';
import { logger } from '@utils/logger';
import { ProfileErrorHandler, ProfileErrorContext } from '@utils/profileErrorHandler';
import { executeWithPool, OperationOptions } from '@utils/connectionPoolManager';
import { cacheService } from '@services/cacheService';
import { CacheCategory, CacheStorage } from '@core/types/cacheTypes';

// Constants;
const SERVICE_NAME = 'OptimizedProfileService';
const CACHE_TTL_SHORT = 300; // 5 minutes;
const CACHE_TTL_MEDIUM = 900; // 15 minutes;
/**;
 * Optimized Profile Service Class;
 */
export class OptimizedProfileService {
  private static instance: OptimizedProfileService,
  private constructor() {
    logger.info('Optimized Profile Service initialized', SERVICE_NAME)
  }

  /**;
   * Get singleton instance;
   */
  static getInstance(): OptimizedProfileService {
    if (!OptimizedProfileService.instance) {
      OptimizedProfileService.instance = new OptimizedProfileService()
    }
    return OptimizedProfileService.instance;
  }

  /**;
   * Get profile by ID with optimized fetching and error handling;
   */
  async getProfileById(
    id: string,
    options: { useCache?: boolean } = {}
  ): Promise<ApiResponse<Profile>>
    const context: ProfileErrorContext = {
      profileId: id;
      operation: 'getProfileById',
      timestamp: new Date().toISOString()
    }

    return ProfileErrorHandler.handleServiceError(
      async () = > { // Validate input;
        if (!id || typeof id !== 'string') {
          return {
            data: null;
            error: 'Valid profile ID is required',
            status: 400 }
        }

        // Check cache first if enabled;
        if (options.useCache != = false) {
          const cacheKey = `profile_${id}`;
          const cachedProfile = await cacheService.get(cacheKey;
            null, // No fallback function, just check cache)
            { category: CacheCategory.SHORT, storage: CacheStorage.MEMORY }
          )
          if (cachedProfile) {
            logger.debug('Profile retrieved from cache', SERVICE_NAME, { profileId: id })
            return createSuccessResponse(cachedProfile)
          }
        }

        // Fetch from database with connection pooling;
        const profile = await executeWithPool(
          async () => {
  const { data, error  } = await supabase.from('user_profiles')
              .select(`);
                id, first_name, last_name, email, username, avatar_url;
                role, is_verified, profile_completion, preferences;
                created_at, updated_at, bio, occupation, phone_number;
                date_of_birth, meta_data, version;
              `)
              )
              .eq('id', id)
              .limit(.limit(.limit(1)
              .maybeSingle).maybeSingle).maybeSingle()
            if (error) {
              throw error;
            }

            return data;
          },
          { priority: 'normal',
            operationName: 'getProfileById',
            timeoutMs: 5000 }
        )
        if (!profile) { return {
            data: null;
            error: 'Profile not found',
            status: 404 }
        }

        // Cache the result;
        if (options.useCache != = false) {
          const cacheKey = `profile_${id}`;
          await cacheService.set(cacheKey;
            profile;
            { category: CacheCategory.SHORT, storage: CacheStorage.BOTH });
            CACHE_TTL_SHORT)
          )
        }

        logger.debug('Profile retrieved from database', SERVICE_NAME, { profileId: id })
        return createSuccessResponse(profile as Profile)
      };
      'getProfileById',
      context;
    )
  }

  /**;
   * Create profile with comprehensive validation and atomic operations;
   */
  async createProfile(profileData: Partial<Profile>): Promise<ApiResponse<Profile>>
    const context: ProfileErrorContext = {
      profileId: profileData.id;
      operation: 'createProfile',
      timestamp: new Date().toISOString()
    }

    return ProfileErrorHandler.handleServiceError(
      async () = > {
  // Validate profile data;
        const validation = ProfileErrorHandler.validateProfileData(profileData, 'create')
        if (!validation.valid) {
          return {
            data: null;
            error: `Validation failed: ${validation.errors.join(', ')}`,
            status: 400,
          }
        }

        // Check if profile already exists;
        const existingProfile = await this.getProfileById(profileData.id!, { useCache: false })
        if (existingProfile.data) { return {
            data: existingProfile.data;
            error: 'Profile already exists',
            status: 409 }
        }

        // Create profile with atomic operation;
        const createdProfile = await executeWithPool(
          async () => { const profileToCreate = {
              ...profileData;
              created_at: new Date().toISOString()
              updated_at: new Date().toISOString()
              version: 1,
              profile_completion: 0 }

            const { data, error  } = await supabase.from('user_profiles')
              .insert(profileToCreate)
              .select()
              .single()
            if (error) {
              throw error;
            }

            return data;
          },
          { priority: 'high',
            operationName: 'createProfile',
            timeoutMs: 8000 }
        )
        // Invalidate related caches;
        await this.invalidateProfileCaches(profileData.id!)
        logger.info('Profile created successfully', SERVICE_NAME, { profileId: profileData.id })
        return createSuccessResponse(createdProfile as Profile; 201)
      },
      'createProfile',
      context;
    )
  }

  /**;
   * Update profile with atomic operations and optimistic locking;
   */
  async updateProfile(
    id: string,
    profileData: Partial<Profile>,
    options: { expectedVersion?: number } = {}
  ): Promise<ApiResponse<Profile>>
    const context: ProfileErrorContext = {
      profileId: id;
      operation: 'updateProfile',
      timestamp: new Date().toISOString()
    }

    return ProfileErrorHandler.handleServiceError(
      async () = > { // Validate input;
        if (!id) {
          return {
            data: null;
            error: 'Profile ID is required',
            status: 400 }
        }

        // Validate profile data;
        const validation = ProfileErrorHandler.validateProfileData(profileData, 'update')
        if (!validation.valid) {
          return {
            data: null;
            error: `Validation failed: ${validation.errors.join(', ')}`,
            status: 400,
          }
        }

        // Use atomic update function with optimistic locking;
        const updatedProfile = await executeWithPool(
          async () => {
  const { data, error  } = await supabase.rpc('update_profile_atomic', {
              profile_id: id);
              profile_data: {
                ...profileData)
                updated_at: new Date().toISOString()
              },
              expected_version: options.expectedVersion || null,
            })
            if (error) {
              throw error;
            }

            return data;
          },
          { priority: 'high',
            operationName: 'updateProfile',
            timeoutMs: 10000 }
        )
        // Invalidate related caches;
        await this.invalidateProfileCaches(id)
        logger.info('Profile updated successfully', SERVICE_NAME, {
          profileId: id);
          version: updatedProfile? .version)
        })
        return createSuccessResponse(updatedProfile as Profile)
      };
      'updateProfile',
      context;
    )
  }

  /**;
   * Update profile preferences with deep merge;
   */
  async updateProfilePreferences(
    id   : string
    preferences: Record<string any>
  ): Promise<ApiResponse<Profile>>
    const context: ProfileErrorContext = {
      profileId: id;
      operation: 'updateProfilePreferences'
      timestamp: new Date().toISOString()
    }

    return ProfileErrorHandler.handleServiceError(
      async () => { // Validate input;
        if (!id) {
          return {
            data: null;
            error: 'Profile ID is required',
            status: 400 }
        }

        if (!preferences || typeof preferences != = 'object') { return {
            data: null;
            error: 'Valid preferences object is required',
            status: 400 }
        }

        // Use atomic preference update function;
        const updatedProfile = await executeWithPool(
          async () => {
  const { data, error  } = await supabase.rpc('update_profile_preferences', {
              profile_id: id);
              new_preferences: preferences)
            })
            if (error) {
              throw error;
            }

            return data;
          },
          { priority: 'normal',
            operationName: 'updateProfilePreferences',
            timeoutMs: 8000 }
        )
        // Invalidate related caches;
        await this.invalidateProfileCaches(id)
        logger.info('Profile preferences updated successfully', SERVICE_NAME, { profileId: id })
        return createSuccessResponse(updatedProfile as Profile)
      };
      'updateProfilePreferences',
      context;
    )
  }

  /**;
   * Get current user's profile with enhanced caching;
   */
  async getCurrentProfile(): Promise<ApiResponse<Profile>>
    const context: ProfileErrorContext = {
      operation: 'getCurrentProfile';
      timestamp: new Date().toISOString()
    }

    return ProfileErrorHandler.handleServiceError(
      async () = > {
  // Get current user;
        const { data: authData, error: authError  } = await supabase.auth.getUser()
        if (authError || !authData.user) { return {
            data: null;
            error: 'User not authenticated',
            status: 401 }
        }

        const userId = authData.user.id;
        context.userId = userId;
        context.profileId = userId;
        // Use enhanced caching for current profile;
        const cacheKey = `current_profile_${userId}`;
        const profile = await cacheService.get(
          cacheKey;
          async () => {
  // Fetch profile or create if doesn't exist;
            const profileResult = await this.getProfileById(userId, { useCache: false })
            if (profileResult.data) {
              return profileResult.data;
            }

            // Create basic profile if doesn't exist;
            const createResult = await this.createProfile({
              id: userId);
              email: authData.user.email)
              created_at: new Date().toISOString()
              updated_at: new Date().toISOString()
            })
            return createResult.data;
          },
          { category: CacheCategory.SHORT, storage: CacheStorage.BOTH };
          CACHE_TTL_MEDIUM;
        )
        if (!profile) { return {
            data: null;
            error: 'Failed to get or create profile',
            status: 500 }
        }

        return createSuccessResponse(profile)
      };
      'getCurrentProfile',
      context;
    )
  }

  /**;
   * Search profiles with optimized queries;
   */
  async searchProfiles(criteria: { searchTerm?: string,
    role?: string,
    location?: string,
    isVerified?: boolean,
    minCompletion?: number,
    maxCompletion?: number,
    limit?: number,
    offset?: number }): Promise<ApiResponse<Profile[]>>
    const context: ProfileErrorContext = { operation: 'searchProfiles';
      timestamp: new Date().toISOString()
      additionalData: criteria }

    return ProfileErrorHandler.handleServiceError(
      async () = > { // Validate and sanitize criteria;
        const sanitizedCriteria = {
          ...criteria;
          limit: Math.min(criteria.limit || 20, 100),
          offset: Math.max(criteria.offset || 0, 0) }

        // Build cache key for search results;
        const cacheKey = `profile_search_${JSON.stringify(sanitizedCriteria)}`;

        // Check cache first;
        const cachedResults = await cacheService.get(cacheKey, null, {
          category: CacheCategory.SHORT);
          storage: CacheStorage.MEMORY)
        })
        if (cachedResults) {
          logger.debug('Profile search results retrieved from cache', SERVICE_NAME)
          return createSuccessResponse(cachedResults)
        }

        // Execute search with connection pooling;
        const profiles = await executeWithPool(
          async () => {
  let query = supabase.from('user_profiles').select(`);
                id, first_name, last_name, username, avatar_url;
                role, is_verified, profile_completion, location;
                created_at, updated_at)
              `)
            // Apply filters;
            if (sanitizedCriteria.searchTerm) {
              const searchTerm = sanitizedCriteria.searchTerm.replace(/[%_]/g, '\\$&')
              query = query.or(`username.ilike.%${searchTerm}%,` +;
                  `first_name.ilike.%${searchTerm}%,` +);
                  `last_name.ilike.%${searchTerm}%`)
              )
            }

            if (sanitizedCriteria.role) {
              query = query.eq('role', sanitizedCriteria.role)
            }

            if (sanitizedCriteria.location) {
              const location = sanitizedCriteria.location.replace(/[%_]/g, '\\$&')
              query = query.ilike('location', `%${location}%`)
            }

            if (sanitizedCriteria.isVerified !== undefined) {
              query = query.eq('is_verified', sanitizedCriteria.isVerified)
            }

            if (sanitizedCriteria.minCompletion !== undefined) {
              query = query.gte('profile_completion', sanitizedCriteria.minCompletion)
            }

            if (sanitizedCriteria.maxCompletion !== undefined) {
              query = query.lte('profile_completion', sanitizedCriteria.maxCompletion)
            }

            // Apply pagination and ordering;
            query = query.order('created_at', { ascending: false })
              .range(sanitizedCriteria.offset;
                sanitizedCriteria.offset + sanitizedCriteria.limit - 1)
              )
            const { data, error  } = await query;
            if (error) {
              throw error;
            }

            return data || [];
          },
          { priority: 'normal',
            operationName: 'searchProfiles',
            timeoutMs: 8000 }
        )
        // Cache results for short period;
        await cacheService.set(cacheKey;
          profiles;
          { category: CacheCategory.SHORT, storage: CacheStorage.MEMORY });
          CACHE_TTL_SHORT)
        )
        logger.debug('Profile search completed', SERVICE_NAME, {
          count: profiles.length);
          criteria: sanitizedCriteria)
        })
        return createSuccessResponse(profiles as Profile[])
      };
      'searchProfiles',
      context;
    )
  }

  /**;
   * Delete profile with proper cleanup;
   */
  async deleteProfile(id: string): Promise<ApiResponse<boolean>>
    const context: ProfileErrorContext = {
      profileId: id;
      operation: 'deleteProfile',
      timestamp: new Date().toISOString()
    }

    return ProfileErrorHandler.handleServiceError(
      async () = > { // Validate input;
        if (!id) {
          return {
            data: null;
            error: 'Profile ID is required',
            status: 400 }
        }

        // Check if profile exists;
        const existingProfile = await this.getProfileById(id, { useCache: false })
        if (!existingProfile.data) { return {
            data: null;
            error: 'Profile not found',
            status: 404 }
        }

        // Delete profile;
        await executeWithPool(
          async () = > {
  const { error  } = await supabase.from('user_profiles').delete().eq('id', id)

            if (error) {
              throw error;
            }
          },
          { priority: 'high',
            operationName: 'deleteProfile',
            timeoutMs: 8000 }
        )
        // Invalidate all related caches;
        await this.invalidateProfileCaches(id)
        logger.info('Profile deleted successfully', SERVICE_NAME, { profileId: id })
        return createSuccessResponse(true)
      };
      'deleteProfile',
      context;
    )
  }

  /**;
   * Invalidate profile-related caches;
   */
  private async invalidateProfileCaches(profileId: string): Promise<void>
    try {
      const cacheKeys = [`profile_${profileId}`;
        `current_profile_${profileId}`,
        `profile_with_relations_${profileId}`];

      await Promise.all(cacheKeys.map(key => cacheService.invalidate(key)))
      // Also clear search result caches (they might contain this profile)
      await cacheService.clearByPattern('profile_search_*')
      logger.debug('Profile caches invalidated', SERVICE_NAME, { profileId })
    } catch (error) {
      logger.warn('Failed to invalidate some profile caches', SERVICE_NAME, {
        profileId;
        error: error instanceof Error ? error.message  : String(error)
      })
    }
  }
}

// Export singleton instance;
export const optimizedProfileService = OptimizedProfileService.getInstance()