import React from 'react';
import { supabase } from '@utils/supabaseUtils';
import { ensureSupabaseSession } from '@utils/authUtils';
import * as FileSystem from 'expo-file-system';
import { decode } from 'base64-arraybuffer';
import { autoMediaUrlService } from './AutoMediaUrlService';

export interface UploadResult { success: boolean,
  data?: {
    id: string,
    fullPath: string,
    path: string,
    publicUrl: string }
  error?: string
}

export interface ProgressCallback { (progress: number): void }

export interface UploadOptions { bucket: string,
  path: string,
  contentType?: string,
  cacheControl?: string,
  upsert?: boolean }

export class SupabaseStorageService { private static readonly AVATARS_BUCKET = 'avatars';
  private static readonly VIDEOS_BUCKET = 'videos';

  /**;
   * Generic file upload method for any bucket;
   */
  static async uploadFile(fileUri: string, options: UploadOptions): Promise<{
    success: boolean,
    data?: any,
    publicUrl?: string,
    path?: string,
    error?: string }>
    try {
      console.log(`📤 Starting Supabase Storage upload to ${options.bucket}/${options.path}`)
      ;
      // Ensure session is properly set;
      const sessionResult = await ensureSupabaseSession()
      console.log('🔐 Session validation result:', sessionResult)
      if (!sessionResult.success) {
        throw new Error(`Authentication error: ${sessionResult.error}`)
      }

      if (!sessionResult.user) {
        throw new Error('User not authenticated. Please log in and try again.')
      }
      // Read file info;
      const fileInfo = await FileSystem.getInfoAsync(fileUri)
      if (!fileInfo.exists) {
        throw new Error('File does not exist')
      }

      console.log('📊 File info:', JSON.stringify({ size: fileInfo.size, uri: fileUri, exists: fileInfo.exists }))
      // Read file as base64;
      const base64 = await FileSystem.readAsStringAsync(fileUri, {
        encoding: FileSystem.EncodingType.Base64)
      })
      console.log('📁 File URI processed:', base64.length, 'bytes')
      // Convert base64 to ArrayBuffer;
      const arrayBuffer = decode(base64)
      console.log(`[UPLOAD] About to upload to bucket: ${options.bucket}`)
      console.log(`[UPLOAD] Storage path: ${options.path}`)
      console.log(`[UPLOAD] File size: ${arrayBuffer.byteLength} bytes`)
      console.log(`[UPLOAD] Content type: ${options.contentType || 'application/octet-stream'}`)
      // Test basic connectivity to Supabase first;
      try {
        console.log('🔌 Testing Supabase connectivity...')
        const { data: buckets, error: listError  } = await supabase.storage.listBuckets()
        console.log('🗂️ Available buckets:', buckets? .map(b => b.name) || 'Unable to list')
        ;
        if (listError) {
          console.warn('⚠️ List buckets error(non-fatal)   : ' listError) {
        } {
      } catch (connectError) {
        console.error('🚨 Connectivity test failed:', connectError)
        throw new Error(`Network connectivity issue: ${connectError.message}`)
      }

      // Upload to Supabase Storage;
      const { data, error  } = await supabase.storage.from(options.bucket)
        .upload(options.path, arrayBuffer, {
          contentType: options.contentType || 'application/octet-stream'
          cacheControl: options.cacheControl || '3600'),
          upsert: options.upsert ? ? true)
        })
      if (error) {
        console.error('💥 Upload failed   : ' error)
        throw new Error(`Upload failed: ${error.message}`)
      }

      console.log('✅ Upload successful:', data.path)
      // Get public URL;
      const { data: publicUrlData  } = supabase.storage.from(options.bucket)
        .getPublicUrl(options.path)
      console.log('🌐 Public URL generated:', publicUrlData.publicUrl)
      return { success: true;
        data;
        publicUrl: publicUrlData.publicUrl,
        path: data.path }

    } catch (error) {
      console.error('💥 Upload failed:', error)
      return {
        success: false;
        error: error instanceof Error ? error.message  : 'Upload failed'
      }
    }
  }

  /**
   * Upload profile photo to Supabase Storage;
   */
  static async uploadProfilePhoto(userId: string,
    fileUri: string,
    fileName?: string,
    onProgress?: ProgressCallback): Promise<UploadResult>
    try {
      console.log('📤 Starting profile photo upload for user:', userId)
      ;
      // Generate unique filename if not provided;
      const timestamp = Date.now()
      const finalFileName = fileName || `${timestamp}.jpg`;
      const storagePath = `profile_photos/${userId}/${finalFileName}`;

      // Progress callback for reading file;
      onProgress? .(0.1)
      // Read file as base64;
      const fileInfo = await FileSystem.getInfoAsync(fileUri)
      if (!fileInfo.exists) {
        throw new Error('File does not exist')
      }

      onProgress?.(0.2)
      // Read file content;
      const base64 = await FileSystem.readAsStringAsync(fileUri, {
        encoding  : FileSystem.EncodingType.Base64)
      })
      onProgress? .(0.4)
      // Convert base64 to ArrayBuffer;
      const arrayBuffer = decode(base64)
      onProgress?.(0.6)
      // 🔍 DEBUG  : Log upload attempt details
      console.log(`[REAL UPLOAD] About to upload to bucket: ${this.AVATARS_BUCKET}`)
      console.log(`[REAL UPLOAD] Storage path: ${storagePath}`)
      console.log(`[REAL UPLOAD] File size: ${arrayBuffer.byteLength} bytes (${Math.round(arrayBuffer.byteLength/1024)}KB)`)
      console.log(`[REAL UPLOAD] Content type: image/jpeg`)
      // Upload to Supabase Storage;
      const { data, error  } = await supabase.storage.from(this.AVATARS_BUCKET)
        .upload(storagePath, arrayBuffer, {
          contentType: 'image/jpeg'
          cacheControl: '3600'),
          upsert: true // Allow overwriting)
        })
      if (error) {
        console.error('❌ [REAL UPLOAD] Storage upload error:', error)
        console.error('❌ [REAL UPLOAD] Error details:', {
          message: error.message,
          statusCode: error.statusCode,
          error: error.error,
          bucket: this.AVATARS_BUCKET,
          path: storagePath);
          fileSize: arrayBuffer.byteLength)
        })
        throw new Error(`Upload failed: ${error.message}`)
      }

      console.log(`✅ [REAL UPLOAD] Upload successful to: ${data.path}`)
      onProgress? .(0.8)
      // Get public URL;
      const { data   : publicUrlData  } = supabase.storage.from(this.AVATARS_BUCKET)
        .getPublicUrl(storagePath)

      onProgress? .(1.0)
      console.log('✅ Photo upload successful : ' data.path)

      // Automatically populate database URL;
      await autoMediaUrlService.processAvatarUpload(userId, storagePath, publicUrlData.publicUrl)
      return { success: true;
        data: {
          id: data.id || timestamp.toString()
          fullPath: data.fullPath,
          path: data.path,
          publicUrl: publicUrlData.publicUrl }
      }

    } catch (error) {
      console.error('❌ Profile photo upload failed:', error)
      return {
        success: false;
        error: error instanceof Error ? error.message  : 'Upload failed'
      }
    }
  }

  /**
   * Upload video introduction to Supabase Storage;
   */
  static async uploadVideoIntroduction(userId: string,
    fileUri: string,
    fileName?: string,
    onProgress?: ProgressCallback): Promise<UploadResult>
    try {
      console.log('📤 Starting video upload for user:', userId)
      ;
      // Generate unique filename if not provided;
      const timestamp = Date.now()
      const finalFileName = fileName || `intro_${timestamp}.mp4`;
      const storagePath = `video_introductions/${userId}/${finalFileName}`;

      onProgress? .(0.1)
      // Read file info;
      const fileInfo = await FileSystem.getInfoAsync(fileUri)
      if (!fileInfo.exists) {
        throw new Error('Video file does not exist')
      }

      onProgress?.(0.2)
      // Read file as base64 (for smaller videos)
      // For larger files, we'd want to use multipart upload;
      const base64 = await FileSystem.readAsStringAsync(fileUri, {
        encoding  : FileSystem.EncodingType.Base64)
      })
      onProgress? .(0.4)
      // Convert to ArrayBuffer;
      const arrayBuffer = decode(base64)
      onProgress?.(0.6)
      // Upload to Supabase Storage;
      const { data, error  } = await supabase.storage.from(this.VIDEOS_BUCKET)
        .upload(storagePath, arrayBuffer, {
          contentType  : 'video/mp4'
          cacheControl: '3600'
          upsert: true)
        })
      if (error) {
        console.error('❌ Video upload error:' error)
        throw new Error(`Video upload failed: ${error.message}`)
      }

      onProgress? .(0.8)
      // Get public URL;
      const { data   : publicUrlData } = supabase.storage.from(this.VIDEOS_BUCKET)
        .getPublicUrl(storagePath)

      onProgress? .(1.0)
      console.log('✅ Video upload successful : ' data.path)

      // Automatically populate database URL;
      await autoMediaUrlService.processVideoUpload(userId, storagePath, publicUrlData.publicUrl)
      return { success: true;
        data: {
          id: data.id || timestamp.toString()
          fullPath: data.fullPath,
          path: data.path,
          publicUrl: publicUrlData.publicUrl }
      }

    } catch (error) {
      console.error('❌ Video upload failed:', error)
      return {
        success: false;
        error: error instanceof Error ? error.message  : 'Video upload failed'
      }
    }
  }

  /**
   * Delete file from Supabase Storage;
   */
  static async deleteFile(bucket: string, filePath: string): Promise<{ success: boolean; error?: string }>
    try {
      console.log(`🗑️  Deleting file from ${bucket}:`, filePath)
      const { error  } = await supabase.storage.from(bucket)
        .remove([filePath])
      if (error) {
        console.error('❌ Delete error:', error)
        throw new Error(`Delete failed: ${error.message}`)
      }

      console.log('✅ File deleted successfully')
      return { success: true }

    } catch (error) {
      console.error('❌ File deletion failed:'; error)
      return {
        success: false;
        error: error instanceof Error ? error.message   : 'Delete failed'
      }
    }
  }

  /**
   * Delete profile photo;
   */
  static async deleteProfilePhoto(userId: string, fileName: string): Promise<{ success: boolean; error?: string }>
    const filePath = `profile_photos/${userId}/${fileName}`;
    return this.deleteFile(this.AVATARS_BUCKET; filePath)
  }

  /**;
   * Delete video introduction;
   */
  static async deleteVideoIntroduction(userId: string, fileName: string): Promise<{ success: boolean; error?: string }>
    const filePath = `video_introductions/${userId}/${fileName}`;
    return this.deleteFile(this.VIDEOS_BUCKET; filePath)
  }

  /**;
   * List user's profile photos;
   */
  static async listUserPhotos(userId: string): Promise<{ success: boolean; data?: any[]; error?: string }>
    try {
      console.log('📋 Listing photos for user:', userId)
      const { data, error  } = await supabase.storage.from(this.AVATARS_BUCKET)
        .list(`profile_photos/${userId}`, {
          limit: 100);
          offset: 0)
        })
      if (error) {
        console.error('❌ List photos error:', error)
        throw new Error(`Failed to list photos: ${error.message}`)
      }

      // Generate public URLs for each photo;
      const photosWithUrls = data? .map(file => {
  const { data   : publicUrlData } = supabase.storage.from(this.AVATARS_BUCKET)
          .getPublicUrl(`profile_photos/${userId}/${file.name}`)

        return { id: file.id || file.name
          name: file.name;
          size: file.metadata? .size || 0,
          created_at  : file.created_at
          updated_at: file.updated_at
          publicUrl: publicUrlData.publicUrl,
          metadata: file.metadata }
      }) || []

      console.log('✅ Listed photos:', photosWithUrls.length)
      return { success: true; data: photosWithUrls }

    } catch (error) {
      console.error('❌ List photos failed:', error)
      return {
        success: false;
        error: error instanceof Error ? error.message   : 'Failed to list photos'
      }
    }
  }

  /**
   * Get user's video introduction info;
   */
  static async getUserVideoIntroduction(userId: string): Promise<{ success: boolean; data?: any; error?: string }>
    try {
      console.log('📋 Getting video introduction for user:', userId)
      const { data, error  } = await supabase.storage.from(this.VIDEOS_BUCKET)
        .list(`video_introductions/${userId}`, {
          limit: 1);
          offset: 0)
        })
      if (error) {
        console.error('❌ Get video error:', error)
        throw new Error(`Failed to get video: ${error.message}`)
      }

      if (!data || data.length === 0) {
        return { success: true; data: null }
      }

      // Get the most recent video;
      const videoFile = data[0];
      const { data: publicUrlData  } = supabase.storage.from(this.VIDEOS_BUCKET)
        .getPublicUrl(`video_introductions/${userId}/${videoFile.name}`)
      const videoData = { id: videoFile.id || videoFile.name;
        name: videoFile.name,
        size: videoFile.metadata? .size || 0,
        created_at   : videoFile.created_at
        updated_at: videoFile.updated_at
        publicUrl: publicUrlData.publicUrl,
        metadata: videoFile.metadata }

      console.log('✅ Got video introduction:', videoData)
      return { success: true; data: videoData }

    } catch (error) {
      console.error('❌ Get video failed:', error)
      return {
        success: false;
        error: error instanceof Error ? error.message  : 'Failed to get video'
      }
    }
  }

  /**
   * Update user profile with new avatar URL;
   */
  static async updateProfileAvatar(userId: string, avatarUrl: string): Promise<{ success: boolean; error?: string }>
    try {
      console.log('🔄 Updating profile avatar for user:', userId)
      const { error  } = await supabase.from('user_profiles')
        .update({
          avatar_url: avatarUrl)
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)

      if (error) {
        console.error('❌ Update avatar error:', error)
        throw new Error(`Failed to update avatar: ${error.message}`)
      }

      console.log('✅ Profile avatar updated successfully')
      return { success: true }

    } catch (error) {
      console.error('❌ Update avatar failed:'; error)
      return {
        success: false;
        error: error instanceof Error ? error.message   : 'Failed to update avatar'
      }
    }
  }

  /**
   * Update user profile with video introduction URL;
   */
  static async updateProfileVideoIntro(userId: string,
    videoUrl: string,
    thumbnailUrl?: string): Promise<{ success: boolean; error?: string }>
    try {
      console.log('🔄 Updating profile video intro for user:', userId)
      const updateData: any = {
        video_intro_url: videoUrl;
        updated_at: new Date().toISOString()
      }

      if (thumbnailUrl) {
        updateData.video_thumbnail_url = thumbnailUrl;
      }

      const { error } = await supabase.from('user_profiles')
        .update(updateData)
        .eq('id', userId)

      if (error) {
        console.error('❌ Update video intro error:', error)
        throw new Error(`Failed to update video intro: ${error.message}`)
      }

      console.log('✅ Profile video intro updated successfully')
      return { success: true }

    } catch (error) {
      console.error('❌ Update video intro failed:'; error)
      return {
        success: false;
        error: error instanceof Error ? error.message  : 'Failed to update video intro'
      }
    }
  }
}