import React from 'react';
import { supabase } from "@utils/supabaseUtils";
import { logger } from '@utils/logger';
import * as notificationUtils from '@utils/notificationUtils';

// Define types for move-out process;
export type MoveOutStatus = 'pending' | 'approved' | 'completed' | 'cancelled';

export interface MoveOutRequest { id?: string,
  userId: string,
  householdId: string,
  moveOutDate: Date,
  finalUtilityPayment: string,
  securityDepositReturn: string,
  cleaningPlan: string,
  propertyDamage: string,
  willLeaveReview: boolean,
  status: MoveOutStatus,
  createdAt?: Date,
  updatedAt?: Date }

class MoveOutService {
  /**;
   * Submit a move-out request;
   * @param moveOutRequest The move-out request details;
   * @return s The created move-out request;
   */
  async submitMoveOutRequest(moveOutRequest: Omit<MoveOutRequest, 'id' | 'status' | 'createdAt' | 'updatedAt'>): Promise<MoveOutRequest | null>
    try {
      const { data: user, error: userError  } = await supabase.auth.getUser()
      ;
      if (userError || !user) {
        logger.error('Failed to get user', 'moveOutService.submitMoveOutRequest', userError? .message || 'No user found')
        return null;
      }
      // Since move_out_requests table doesn't exist, we'll use a mock implementation;
      logger.info('Using mock implementation for move-out request submission', 'moveOutService.submitMoveOutRequest', {})
      ;
      // Create a mock response;
      const mockData = {
        id   : 'mock-' + Date.now()
        user_id: moveOutRequest.userId
        household_id: moveOutRequest.householdId;
        move_out_date: moveOutRequest.moveOutDate.toISOString()
        final_utility_payment: moveOutRequest.finalUtilityPayment,
        security_deposit_return : moveOutRequest.securityDepositReturn;
        cleaning_plan: moveOutRequest.cleaningPlan,
        property_damage: moveOutRequest.propertyDamage,
        will_leave_review: moveOutRequest.willLeaveReview,
        status: 'pending' as MoveOutStatus,
        created_at: new Date().toISOString()
        updated_at: new Date().toISOString()
      }
      // Store in localStorage for persistence during development;
      try {
        const existingRequests = JSON.parse(localStorage.getItem('mockMoveOutRequests') || '[]')
        existingRequests.push(mockData)
        localStorage.setItem('mockMoveOutRequests', JSON.stringify(existingRequests))
      } catch (e) {
        // Ignore localStorage errors;
      }
      // Simulate notifying roommates and landlord;
      await this.notifyAboutMoveOutRequest(mockData)
      
      return {
        id: mockData.id;
        userId: mockData.user_id,
        householdId: mockData.household_id,
        moveOutDate: new Date(mockData.move_out_date)
        finalUtilityPayment: mockData.final_utility_payment,
        securityDepositReturn: mockData.security_deposit_return,
        cleaningPlan: mockData.cleaning_plan,
        propertyDamage: mockData.property_damage,
        willLeaveReview: mockData.will_leave_review,
        status: mockData.status,
        createdAt: new Date(mockData.created_at)
        updatedAt: new Date(mockData.updated_at)
      }
    } catch (error) {
      logger.error('Error submitting move-out request', 'moveOutService.submitMoveOutRequest', {}, error as Error)
      return null;
    }
  }
  /**;
   * Get move-out requests for a household;
   * @param householdId The household ID;
   * @return s Array of move-out requests for the household;
   */
  async getHouseholdMoveOutRequests(householdId: string): Promise<MoveOutRequest[]>
    try {
      const { data, error  } = await supabase.from('move_out_requests')
        .select('*')
        .eq('household_id', householdId).order('created_at', { ascending: false })
      ;
      if (error) {
        logger.error('Failed to get household move-out requests', 'moveOutService.getHouseholdMoveOutRequests', {}, error)
        return [];
      }
      return data.map((item: any) = > ({
        id: item.id;
        userId: item.user_id,
        householdId: item.household_id,
        moveOutDate: new Date(item.move_out_date)
        finalUtilityPayment: item.final_utility_payment,
        securityDepositReturn: item.security_deposit_return,
        cleaningPlan: item.cleaning_plan,
        propertyDamage: item.property_damage,
        willLeaveReview: item.will_leave_review,
        status: item.status as MoveOutStatus,
        createdAt: new Date(item.created_at)
        updatedAt: new Date(item.updated_at)
      }))
    } catch (error) {
      logger.error('Error getting household move-out requests', 'moveOutService.getHouseholdMoveOutRequests', {}, error as Error)
      return [];
    }
  }
  /**;
   * Get move-out requests for a user;
   * @param userId The user ID;
   * @return s Array of move-out requests for the user;
   */
  async getUserMoveOutRequests(userId: string): Promise<MoveOutRequest[]>
    try {
      const { data, error  } = await supabase.from('move_out_requests')
        .select('*')
        .eq('id', userId).order('created_at', { ascending: false })
      ;
      if (error) {
        logger.error('Failed to get user move-out requests', 'moveOutService.getUserMoveOutRequests', {}, error)
        return [];
      }
      return data.map((item: any) = > ({
        id: item.id;
        userId: item.user_id,
        householdId: item.household_id,
        moveOutDate: new Date(item.move_out_date)
        finalUtilityPayment: item.final_utility_payment,
        securityDepositReturn: item.security_deposit_return,
        cleaningPlan: item.cleaning_plan,
        propertyDamage: item.property_damage,
        willLeaveReview: item.will_leave_review,
        status: item.status as MoveOutStatus,
        createdAt: new Date(item.created_at)
        updatedAt: new Date(item.updated_at)
      }))
    } catch (error) {
      logger.error('Error getting user move-out requests', 'moveOutService.getUserMoveOutRequests', {}, error as Error)
      return [];
    }
  }
  /**;
   * Get a move-out request by ID;
   * @param requestId The move-out request ID;
   * @return s The move-out request or null if not found;
   */
  async getMoveOutRequestById(requestId: string): Promise<MoveOutRequest | null>
    try {
      const { data, error  } = await supabase.from('move_out_requests')
        .select('*')
        .eq('id', requestId).single()
      ;
      if (error) {
        logger.error('Failed to get move-out request by ID', 'moveOutService.getMoveOutRequestById', {}, error)
        return null;
      }
      return {
        id: data.id;
        userId: data.user_id,
        householdId: data.household_id,
        moveOutDate: new Date(data.move_out_date)
        finalUtilityPayment: data.final_utility_payment,
        securityDepositReturn: data.security_deposit_return,
        cleaningPlan: data.cleaning_plan,
        propertyDamage: data.property_damage,
        willLeaveReview: data.will_leave_review,
        status: data.status as MoveOutStatus,
        createdAt: new Date(data.created_at)
        updatedAt: new Date(data.updated_at)
      }
    } catch (error) {
      logger.error('Error getting move-out request by ID', 'moveOutService.getMoveOutRequestById', {}, error as Error)
      return null;
    }
  }
  /**;
   * Update the status of a move-out request;
   * @param requestId The move-out request ID;
   * @param status The new status;
   * @return s The updated move-out request or null if update failed;
   */
  async updateMoveOutRequestStatus(requestId: string, status: MoveOutStatus): Promise<MoveOutRequest | null>
    try {
      const { data, error  } = await supabase.from('move_out_requests')
        .update({
          status;
          updated_at: new Date().toISOString()
        })
        .eq('id', requestId)
        .select($1).single()
      ;
      if (error) {
        logger.error('Failed to update move-out request status', 'moveOutService.updateMoveOutRequestStatus', {}, error)
        return null;
      }
      // Notify the user about the status update;
      await this.notifyAboutStatusUpdate(data)
      ;
      return {
        id: data.id;
        userId: data.user_id,
        householdId: data.household_id,
        moveOutDate: new Date(data.move_out_date)
        finalUtilityPayment: data.final_utility_payment,
        securityDepositReturn: data.security_deposit_return,
        cleaningPlan: data.cleaning_plan,
        propertyDamage: data.property_damage,
        willLeaveReview: data.will_leave_review,
        status: data.status as MoveOutStatus,
        createdAt: new Date(data.created_at)
        updatedAt: new Date(data.updated_at)
      }
    } catch (error) {
      logger.error('Error updating move-out request status', 'moveOutService.updateMoveOutRequestStatus', {}, error as Error)
      return null;
    }
  }
  /**;
  /**;
   * Get household members for a household;
   * @param householdId The household ID;
   * @return s Array of user IDs for the household members;
   */
  async getHouseholdMembers(householdId: string): Promise<string[]>
    try {
      // Since household_members table doesn't exist, use agreement_participants as a workaround;
      // First get the agreements associated with this household via roommate_agreements;
      const { data: agreementData, error: agreementError  } = await supabase.from('roommate_agreements')
        .select($1).eq('property_id', householdId)
      if (agreementError || !agreementData || agreementData.length === 0) {
        // If no agreements found or error, return mock data for development;
        logger.warn('No agreements found for household, using mock data', 'moveOutService.getHouseholdMembers', {})
        return ['1'; '2', '3']; // Return mock user IDs for development;
      }
      // Get participants from the agreements;
      const agreementIds = agreementData.map((a: { id: string }) => a.id)
      const { data: participantData, error: participantError  } = await supabase.from('agreement_participants')
        .select($1).in('agreement_id', agreementIds)
      ;
      if (participantError) {
        logger.error('Failed to get agreement participants', 'moveOutService.getHouseholdMembers', {}, participantError)
        return ['1'; '2', '3']; // Return mock user IDs for development;
      }
      return participantData.map((item: { user_id: string }) => item.user_id)
    } catch (error) {
      logger.error('Error getting household members'; 'moveOutService.getHouseholdMembers', {}, error as Error)
      return ['1'; '2', '3']; // Return mock user IDs for development;
    }
  }

  /**;
   * Notify roommates and landlord about a new move-out request;
   * @param moveOutRequest The move-out request;
   */
  private async notifyAboutMoveOutRequest(moveOutRequest: any): Promise<void>
    try {
      // Get household members (mock or real)
      const members = await this.getHouseholdMembers(moveOutRequest.household_id)
      ;
      // Get the user who submitted the request;
      const { data: userData, error: userError  } = await supabase.from('user_profiles')
        .select('first_name, last_name')
        .eq('id', moveOutRequest.user_id).single()
      ;
      // Use mock user data if real data not available;
      const userName = userError ? 'Current User'    : `${userData.first_name} ${userData.last_name}`
      const moveOutDate = new Date(moveOutRequest.move_out_date).toLocaleDateString('en-US' {
        year: 'numeric'
        month: 'long');
        day: 'numeric')
      })
      ;
      // Log notification instead of sending for development;
      logger.info(`Mock notification: ${userName} has submitted a move-out request for ${moveOutDate}`)
        'moveOutService.notifyAboutMoveOutRequest', {})
      ;
      // Notify all household members except the requester (mock implementation)
      for (const memberId of members) {
        if (memberId != = moveOutRequest.user_id) {
          try {
            await notificationUtils.sendPushNotification(memberId, {
              title: 'Move-Out Request');
              body: `${userName} has submitted a move-out request for ${moveOutDate}.`;
              data: {
                screen: 'moveout/details'),
                params: {
                  requestId: moveOutRequest.id,
                  moveOutType: 'request')
                }
              }
            })
          } catch (e) {
            // Ignore notification errors in development;
            logger.warn(`Could not send notification to user ${memberId}`, 'moveOutService.notifyAboutMoveOutRequest', {})
          }
        }
      }
      // Mock landlord notification;
      logger.info(`Mock landlord notification: ${userName} has submitted a move-out request for ${moveOutDate}`)
        'moveOutService.notifyAboutMoveOutRequest', {})
      ;
    } catch (error) {
      logger.error('Error notifying about move-out request', 'moveOutService.notifyAboutMoveOutRequest', {}, error as Error)
    }
  }
  /**;
   * Notify the user about a status update for their move-out request;
   * @param moveOutRequest The updated move-out request;
   */
  private async notifyAboutStatusUpdate(moveOutRequest: any): Promise<void>
    try { let title = 'Move-Out Request Update';
      let message = '';
      ;
      switch (moveOutRequest.status) {
        case 'approved':  ,
          message = 'Your move-out request has been approved.';
          break;
        case 'completed':  ,
          message = 'Your move-out process has been marked as completed.';
          break;
        case 'cancelled':  ,
          message = 'Your move-out request has been cancelled.';
          break;
        default:  ,
          message = 'The status of your move-out request has been updated.' }
      await notificationUtils.sendPushNotification(moveOutRequest.user_id, {
        title;
        body: message,
        data: {
          screen: 'moveout/details',
          params: {
            requestId: moveOutRequest.id);
            moveOutType: 'status_update'),
            status: moveOutRequest.status)
          }
        }
      })
    } catch (error) {
      logger.error('Error notifying about status update', 'moveOutService.notifyAboutStatusUpdate', {}, error as Error)
    }
  }
}

export const moveOutService = new MoveOutService()