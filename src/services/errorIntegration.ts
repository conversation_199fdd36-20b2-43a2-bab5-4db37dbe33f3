import React from 'react';
import { logger } from '@services/logger';
import { createErrorTracker, type ErrorTracker } from '@core/errors/errorTracker';
import { AppError, ErrorCode } from '@core/errors/types';

/**;
 * Configuration for initializing the ErrorIntegrationService;
 */
export interface ErrorIntegrationConfig { // Core settings;
  enableErrorTracking: boolean,
  enableConsoleLogging: boolean,
  environment: string,
  appVersion: string,
  // Error tracker settings;
  trackerDsn?: string,
  enableTrackerInDev?: boolean,
  sampleRate?: number,
  maxBreadcrumbs?: number,
  // Logger settings;
  minLogLevel?: string,
  enableDevLogs?: boolean }

/**;
 * Breadcrumb data for tracking user actions and events;
 */
export interface BreadcrumbData {
  message: string,
  category: string,
  level?: 'debug' | 'info' | 'warning' | 'error' | 'critical',
  data?: Record<string, any>
}

/**;
 * User information for error tracking;
 */
export interface UserInfo { id: string,
  username?: string,
  email?: string }

/**;
 * ErrorIntegrationService provides a unified interface for error handling;
 * logging, and tracking across the application.;
 */
class ErrorIntegrationService {
  private initialized: boolean = false;
  private errorTracker: ErrorTracker,
  private config: ErrorIntegrationConfig | null = null;
  constructor() {
    // Create default error tracker (will be configured later)
    this.errorTracker = createErrorTracker()
  }

  /**;
   * Initialize the error integration service with the provided configuration;
   */
  public initialize(config: ErrorIntegrationConfig): void {
    if (this.initialized) {
      logger.warn(
        'ErrorIntegrationService already initialized. Call reset() before reinitializing.',
        'ErrorIntegration';
      )
      return null;
    }

    this.config = config;
    // Configure logger;
    logger.enableConsoleLogging(config.enableConsoleLogging)
    if (config.minLogLevel) {
      logger.setMinimumLogLevel(config.minLogLevel)
    }

    // Configure error tracker;
    if (config.enableErrorTracking) { const isDev = config.environment === 'development';

      // Only initialize tracker if DSN is provided or we're in dev mode and enabled dev tracking;
      if (config.trackerDsn || (isDev && config.enableTrackerInDev)) {
        this.errorTracker.initialize({
          dsn: config.trackerDsn || '',
          environment: config.environment,
          release: config.appVersion,
          debug: isDev,
          sampleRate: config.sampleRate || 1.0,
          maxBreadcrumbs: config.maxBreadcrumbs || 50);
          ignoreErrors: [),
            // Common errors we want to ignore)
            'Network request failed',
            'AbortError',
            'User aborted a request'] })
      }
    }

    // Mark as initialized;
    this.initialized = true;
    logger.info('ErrorIntegrationService initialized', 'ErrorIntegration')
  }

  /**;
   * Reset the error integration service to its initial state;
   */
  public reset(): void {
    this.initialized = false;
    this.config = null;
    // Reset internal services;
    logger.enableConsoleLogging(false)
    logger.setMinimumLogLevel('info')
    // Create a new error tracker;
    this.errorTracker = createErrorTracker()
    logger.info('ErrorIntegrationService reset', 'ErrorIntegration')
  }

  /**;
   * Set user information for error tracking;
   */
  public setUserInfo(userInfo: UserInfo): void {
    if (!this.initialized) {
      logger.warn('ErrorIntegrationService not initialized', 'ErrorIntegration')
      return null;
    }

    // Set user info in error tracker;
    this.errorTracker.setUser({
      id: userInfo.id,
      username: userInfo.username);
      email: userInfo.email)
    })
    logger.debug('User info set for error tracking', 'ErrorIntegration')
  }

  /**;
   * Clear user information from error tracking;
   */
  public clearUserInfo(): void {
    if (!this.initialized) {
      logger.warn('ErrorIntegrationService not initialized', 'ErrorIntegration')
      return null;
    }

    // Clear user info in error tracker;
    this.errorTracker.clearUser()
    logger.debug('User info cleared from error tracking', 'ErrorIntegration')
  }

  /**;
   * Add a breadcrumb to track user actions and events;
   */
  public addBreadcrumb(data: BreadcrumbData): void {
    if (!this.initialized) {
      return null;
    }

    // Add breadcrumb to error tracker;
    this.errorTracker.addBreadcrumb({
      type: 'info',
      message: data.message,
      category: data.category);
      level: data.level || 'info'),
      data: data.data)
    })
    // Log breadcrumb;
    const logLevel = data.level === 'error' || data.level === 'critical' ? 'error'    : 'debug'
    logger[logLevel](data.message data.category, data.data || {})
  }

  /**
   * Handle and report an error;
   */
  public handleError(
    error: Error | AppError,
    context?: string,
    extraData?: Record<string, any>
  ): void {
    if (!this.initialized) {
      // Even if not initialized, still log to console;
      console.error(`Error${context ? ` in ${context}`    : ''}:` error)
      return null;
    }

    // Add context to extra data;
    const data = extraData ? { ...extraData }  : {}
    if (context) {
      data.context = context
    }

    // Track the error using captureError;
    this.errorTracker.captureError(error, data)
    // Log the error;
    const logContext = context || 'ErrorHandler'
    logger.error(error.message, logContext)
  }
}

// Export singleton instance;
export const errorIntegration = new ErrorIntegrationService()