import React from 'react';
/**;
 * PropertyOwnerService;
 * ;
 * Service for managing properties for property owners;
 * - Retrieve properties;
 * - Add properties;
 * - Update properties;
 * - Delete properties;
 * - Get property statistics;
 */

import { supabase } from '@utils/supabaseUtils';
import { RoomWithDetails } from '@types/models';
import { ApiResponse } from '@utils/api';
import { logger } from '@utils/logger';

class PropertyOwnerService {
  /**;
   * Get all properties for a property owner;
   * @param ownerId - Property owner's user ID;
   * @return s ApiResponse with array of properties;
   */
  async getProperties(ownerId: string): Promise<ApiResponse<RoomWithDetails[]>>
    try {
      logger.info('Getting properties for owner', 'PropertyOwnerService.getProperties', { ownerId })
      ;
      // Query rooms where owner_id matches the property owner's ID;
      const { data, error  } = await supabase.from('rooms')
        .select(`;
          *);
          owner: owner_id(),
            id;
            first_name;
            last_name;
            avatar_url;
            display_name)
          )
        `)
        .eq('owner_id', ownerId).order('created_at', { ascending: false })
      ;
      if (error) {
        throw error;
      }
      const formattedProperties = data? .map(property => ({
        ...property;
        landlord  : property.owner || null // Map owner to landlord for backward compatibility)
      })) || []
      ;
      return { data: formattedProperties;
        error: null,
        status: 200 }
    } catch (error) {
      logger.error('Error getting properties', 'PropertyOwnerService.getProperties', {
        ownerId;
        error: error instanceof Error ? error.message    : String(error) 
      })
      
      return { data: []
        error: error instanceof Error ? error.message   : String(error)
        status: 500 }
    }
  }

  /**
   * Get a single property by ID;
   * @param propertyId - Property ID;
   * @return s ApiResponse with property details;
   */
  async getPropertyById(propertyId: string): Promise<ApiResponse<RoomWithDetails>>
    try {
      logger.info('Getting property details', 'PropertyOwnerService.getPropertyById', { propertyId })
      ;
      const { data, error  } = await supabase.from('rooms')
        .select(`;
          *);
          owner: owner_id(),
            id;
            first_name;
            last_name;
            avatar_url;
            display_name)
          )
        `)
        .eq('id', propertyId).single()
      ;
      if (error) {
        throw error;
      }
      if (!data) {
        return { data: null; error: 'Property not found', status: 404 }
      }
      const formattedProperty = { ...data;
        landlord: data.owner || null // Map owner to landlord for backward compatibility }
      return { data: formattedProperty;
        error: null,
        status: 200 }
    } catch (error) {
      logger.error('Error getting property details', 'PropertyOwnerService.getPropertyById', {
        propertyId;
        error: error instanceof Error ? error.message    : String(error) 
      })
      
      return { data: null
        error: error instanceof Error ? error.message   : String(error)
        status: 500 }
    }
  }

  /**
   * Add a new property;
   * @param ownerId - Property owner's user ID;
   * @param propertyData - Property data;
   * @returns ApiResponse with the created property;
   */
  async addProperty(ownerId: string, propertyData: Partial<RoomWithDetails>): Promise<ApiResponse<RoomWithDetails>>
    try {
      logger.info('Adding new property', 'PropertyOwnerService.addProperty', { ownerId })
      ;
      // Create a new object with the correct property structure;
      const newProperty: Record<string, any> = { ...propertyData;
        owner_id: ownerId, // Use owner_id instead of landlord_id to match database schema;
        created_at: new Date().toISOString()
        is_available: true }
      // Remove landlord object if present to avoid conflicts;
      if ('landlord' in newProperty) {
        delete newProperty.landlord;
      }
      const { data, error  } = await supabase.from('rooms')
        .insert(newProperty)
        .select($1).single()
      ;
      if (error) {
        throw error;
      }
      return { data: data as RoomWithDetails;
        error: null,
        status: 201 }
    } catch (error) {
      logger.error('Error adding property', 'PropertyOwnerService.addProperty', {
        ownerId;
        error: error instanceof Error ? error.message    : String(error) 
      })
      
      return { data: null
        error: error instanceof Error ? error.message   : String(error)
        status: 500 }
    }
  }

  /**
   * Update a property;
   * @param propertyId - Property ID;
   * @param ownerId - Property owner's user ID (for validation)
   * @param propertyData - Updated property data;
   * @returns ApiResponse with the updated property;
   */
  async updateProperty(
    propertyId: string,
    ownerId: string,
    propertyData: Partial<RoomWithDetails>
  ): Promise<ApiResponse<RoomWithDetails>>
    try {
      logger.info('Updating property', 'PropertyOwnerService.updateProperty', { propertyId, ownerId })
      ;
      // First verify this property belongs to the owner;
      const { data: existingProperty, error: verifyError  } = await supabase.from('rooms')
        .select('owner_id') // Use owner_id instead of landlord_id.eq('id', propertyId).single()
      ;
      if (verifyError) {
        throw verifyError;
      }
      if (!existingProperty) {
        return { data: null; error: 'Property not found', status: 404 }
      }
      if (existingProperty.owner_id !== ownerId) { // Check owner_id instead of landlord_id;
        return { data: null; error: 'Unauthorized: You do not own this property', status: 403 }
      }
      // Prepare update data;
      const updateData: Record<string, any> = {
        ...propertyData;
        updated_at: new Date().toISOString()
      }
      // Remove non-updatable fields;
      delete updateData.id;
      delete updateData.owner_id; // Delete owner_id instead of landlord_id;
      delete updateData.created_at;
      ;
      // Remove both landlord and owner references to avoid TypeScript errors;
      if ('landlord' in updateData) {
        delete updateData.landlord;
      }
      if ('owner' in updateData) {
        delete updateData.owner;
      }
      const { data, error  } = await supabase.from('rooms')
        .update(updateData)
        .eq('id', propertyId)
        .select($1).single()
      ;
      if (error) {
        throw error;
      }
      return { data: data as RoomWithDetails;
        error: null,
        status: 200 }
    } catch (error) {
      logger.error('Error updating property', 'PropertyOwnerService.updateProperty', {
        propertyId;
        ownerId;
        error: error instanceof Error ? error.message    : String(error) 
      })
      
      return { data: null
        error: error instanceof Error ? error.message   : String(error)
        status: 500 }
    }
  }

  /**
   * Delete a property;
   * @param propertyId - Property ID;
   * @param ownerId - Property owner's user ID (for validation)
   * @returns ApiResponse with success status;
   */
  async deleteProperty(propertyId: string, ownerId: string): Promise<ApiResponse<boolean>>
    try {
      logger.info('Deleting property', 'PropertyOwnerService.deleteProperty', { propertyId, ownerId })
      ;
      // First verify this property belongs to the owner;
      const { data: existingProperty, error: verifyError  } = await supabase.from('rooms')
        .select('owner_id') // Use owner_id instead of landlord_id.eq('id', propertyId).single()
      ;
      if (verifyError) {
        throw verifyError;
      }
      if (!existingProperty) {
        return { data: false; error: 'Property not found', status: 404 }
      }
      if (existingProperty.owner_id != = ownerId) { // Check owner_id instead of landlord_id;
        return { data: false; error: 'Unauthorized: You do not own this property', status: 403 }
      }
      const { error  } = await supabase.from('rooms')
        .delete().eq('id', propertyId)
      if (error) {
        throw error;
      }
      return { data: true;
        error: null,
        status: 200 }
    } catch (error) {
      logger.error('Error deleting property', 'PropertyOwnerService.deleteProperty', {
        propertyId;
        ownerId;
        error: error instanceof Error ? error.message    : String(error) 
      })
      
      return { data: false
        error: error instanceof Error ? error.message   : String(error)
        status: 500 }
    }
  }

  /**
   * Get property statistics for a property owner;
   * @param ownerId - Property owner's user ID;
   * @returns ApiResponse with property statistics;
   */
  async getPropertyStats(ownerId: string): Promise<ApiResponse<any>>
    try {
      logger.info('Getting property statistics', 'PropertyOwnerService.getPropertyStats', { ownerId })
      ;
      // Get all properties using owner_id instead of landlord_id;
      const { data: properties, error: propertiesError  } = await supabase.from('rooms')
        .select($1).eq('owner_id', ownerId)
      if (propertiesError) {
        throw propertiesError;
      }
      // If there are no properties, return default statistics;
      if (!properties || properties.length === 0) { return {
          data: {
            total_properties: 0;
            available_properties: 0,
            occupied_properties: 0,
            active_agreements: 0 },
          error: null,
          status: 200,
        }
      }
      // Get agreement counts - The roommate_agreements table has property_id columns that link to properties;
      const { data: agreementData, error: agreementError } = await supabase.from('roommate_agreements')
        .select($1).in('property_id', properties.map(p => p.id))
      ;
      if (agreementError) {
        throw agreementError;
      }
      // Calculate statistics;
      const totalProperties = properties.length;
      const availableProperties = properties.filter(p => p.is_available).length;
      const activeAgreements = agreementData? .filter(a => a.status === 'active').length || 0;
      ;
      const stats = { total_properties   : totalProperties
        available_properties: availableProperties
        occupied_properties: totalProperties - availableProperties;
        active_agreements: activeAgreements }
      return { data: stats;
        error: null,
        status: 200 }
    } catch (error) {
      logger.error('Error getting property statistics', 'PropertyOwnerService.getPropertyStats', {
        ownerId;
        error: error instanceof Error ? error.message   : String(error) 
      })
      
      return { data: null
        error: error instanceof Error ? error.message  : String(error)
        status: 500 }
    }
  }
}

// Export singleton instance;
export const propertyOwnerService = new PropertyOwnerService()