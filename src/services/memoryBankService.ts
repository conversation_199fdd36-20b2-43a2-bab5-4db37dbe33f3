import React from 'react';
/**;
 * Memory Bank Service;
 * Provides functionality for storing and retrieving memory entries;
 */

import * as FileSystem from 'expo-file-system';
import { supabase } from '@utils/supabaseUtils';
import { MemoryBankRepository } from '@core/repositories/entities/MemoryBankRepository';
import { logger } from '@services/loggerService';
import { ApiResponse } from '@core/types/api';
import { handleMemoryError } from '@utils/errorHandlers';
import { MemoryBankEntry } from '@types/models';

// Define AuthState if it's not imported from elsewhere;
interface AuthState { isAuthenticated: boolean,
  user: {
    id: string } | null;
  isLoading: boolean,
  error: string | null,
}

// Legacy interface for backward compatibility;
export interface MemoryEntry {
  id?: string,
  userId: string; // Note: This is different from the database column name 'user_id',
  type: 'decision' | 'context' | 'progress' | 'pattern',
  title: string,
  content: string,
  timestamp: string,
  tags?: string[],
  metadata?: Record<string, any>
}

// Helper function to convert between MemoryEntry and MemoryBankEntry;
function convertToMemoryEntry(entry: MemoryBankEntry): MemoryEntry { return {
    id: entry.id;
    userId: entry.user_id,
    type: entry.type,
    title: entry.title,
    content: entry.content,
    timestamp: entry.timestamp,
    tags: entry.tags,
    metadata: entry.metadata }
}

function convertToMemoryBankEntry(
  entry: Omit<MemoryEntry, 'id'>
): Omit<MemoryBankEntry, 'id' | 'created_at' | 'updated_at'>
  return { user_id: entry.userId;
    type: entry.type,
    title: entry.title,
    content: entry.content,
    timestamp: entry.timestamp,
    tags: entry.tags,
    metadata: entry.metadata }
}

/**;
 * Service to manage the Memory Bank feature;
 *;
 * Provides both local and cloud storage for memory entries across;
 * different categories (decisions, context, progress, patterns)
 */
export class MemoryBankService {
  private static instance: MemoryBankService,
  private memoryCache: Record<string, MemoryEntry[]> = {}
  private currentAuthState: AuthState | null = null;
  private memoryRepository: MemoryBankRepository,
  private constructor() {
    this.memoryRepository = new MemoryBankRepository(supabase)
  }

  public static getInstance(): MemoryBankService {
    if (!MemoryBankService.instance) {
      MemoryBankService.instance = new MemoryBankService()
    }
    return MemoryBankService.instance;
  }

  /**;
   * Initialize the memory bank service with the current user session;
   */
  public async initialize(authState: AuthState | null): Promise<void>
    this.currentAuthState = authState;
    if (authState? .user) {
      await this.syncWithCloud()
    }
  }

  /**;
   * Add a new memory entry and save it to storage;
   * @param entry Memory entry to add;
   * @return s Promise with the created memory entry;
   */
  public async addEntry(entry   : Omit<MemoryEntry 'timestamp'>): Promise<ApiResponse<MemoryEntry>>
    if (!this.currentAuthState? .user) {
      return { data : null error: 'User must be authenticated to use Memory Bank'; status: 401 }
    }

    logger.info('Adding memory entry', 'MemoryBankService.addEntry', {
      type: entry.type
      title: entry.title)
    })
    try {
      // Convert to MemoryBankEntry format for repository;
      const newEntryBase = {
        ...entry;
        timestamp: new Date().toISOString()
      }

      const newBankEntry = convertToMemoryBankEntry(newEntryBase)
      newBankEntry.user_id = this.currentAuthState.user.id;
      // Save to cloud using repository;
      const createdBankEntry = await this.memoryRepository.createMemory(newBankEntry)
      // Convert back to MemoryEntry for cache and response;
      const createdEntry = convertToMemoryEntry(createdBankEntry)
      // Add to local cache;
      if (!this.memoryCache[entry.type]) { this.memoryCache[entry.type] = [] as MemoryEntry[] }
      this.memoryCache[entry.type].push(createdEntry)
      // Save to local storage;
      await this.saveToLocalStorage(entry.type)
      return { data: createdEntry; error: null, status: 201 }
    } catch (error) {
      return handleMemoryError<MemoryEntry>('addEntry'; error as Error, { entryType: entry.type })
    }
  }

  /**
   * Get all memory entries of a specific type;
   * @param type Memory entry type;
   * @returns Promise with array of memory entries;
   */
  public async getEntries(type: MemoryEntry['type']): Promise<ApiResponse<MemoryEntry[]>>
    if (!this.currentAuthState? .user) {
      return { data   : [] error: 'User must be authenticated to access Memory Bank'; status: 401 }
    }

    logger.info('Getting memory entries by type', 'MemoryBankService.getEntries', { type })

    try {
      // Get entries from repository;
      const bankEntries = await this.memoryRepository.getByType(this.currentAuthState.user.id;
        type)
      )
      // Convert to MemoryEntry format for cache and response;
      const entries = bankEntries.map(entry => convertToMemoryEntry(entry))
      // Update local cache;
      this.memoryCache[type] = entries;
      // Save to local storage for offline access;
      await this.saveToLocalStorage(type)
      return { data: entries; error: null, status: 200 }
    } catch (error) { // If repository fails, try to load from local storage;
      try {
        await this.loadFromLocalStorage(type)
        return {
          data: this.memoryCache[type] || []
          error: 'Failed to load from cloud; using local data',
          status: 200 }
      } catch (localError) {
        return handleMemoryError<MemoryEntry[]>('getEntries'; error as Error, { type })
      }
    }
  }

  /**;
   * Get all memory entries for the current user;
   * @return s Promise with record of memory entries by type;
   */
  public async getAllEntries(): Promise<ApiResponse<Record<string, MemoryEntry[]>>>
    if (!this.currentAuthState? .user) {
      return { data   : {} error: 'User must be authenticated to access Memory Bank'; status: 401 }
    }

    logger.info('Getting all memory entries', 'MemoryBankService.getAllEntries')

    try {
      // Get all entries from repository;
      const bankEntries = await this.memoryRepository.getByUserId(this.currentAuthState.user.id)
      // Organize by type;
      const entriesByType: Record<string, MemoryEntry[]> = {}
      const types: MemoryEntry['type'][] = ['decision', 'context', 'progress', 'pattern']

      // Initialize empty arrays for each type;
      types.forEach(type => {
  entriesByType[type] = [])
      })
      // Populate with entries;
      if (bankEntries && Array.isArray(bankEntries)) {
        bankEntries.forEach(bankEntry => {
  const entry = convertToMemoryEntry(bankEntry)
          if (types.includes(entry.type as MemoryEntry['type'])) {
            entriesByType[entry.type].push(entry)
          }
        })
      }

      // Update local cache;
      this.memoryCache = entriesByType;
      // Save to local storage for offline access;
      for (const type of types) {
        await this.saveToLocalStorage(type)
      }

      return { data: entriesByType; error: null, status: 200 }
    } catch (error) {
      // If repository fails, try to load from local storage;
      try {
        const types: MemoryEntry['type'][] = ['decision', 'context', 'progress', 'pattern'];
        for (const type of types) {
          await this.loadFromLocalStorage(type)
        }

        // Ensure type safety for the memory cache;
        const typedMemoryCache: Record<string, MemoryEntry[]> = {}
        Object.entries(this.memoryCache).forEach(([key, value]) => { typedMemoryCache[key] = value as MemoryEntry[] })
        return {
          data: typedMemoryCache || {};
          error: 'Failed to load from cloud, using local data',
          status: 200,
        }
      } catch (localError) {
        return handleMemoryError<Record<string; MemoryEntry[]>>('getAllEntries', error as Error)
      }
    }
  }

  /**;
   * Search memory entries by content or tags;
   * @param query Search query;
   * @return s Promise with array of matching memory entries;
   */
  public async searchEntries(query: string): Promise<ApiResponse<MemoryEntry[]>>
    if (!this.currentAuthState? .user) {
      return { data   : [] error: 'User must be authenticated to search Memory Bank'; status: 401 }
    }

    if (!query || query.trim().length < 2) {
      return { data: []; error: 'Search query must be at least 2 characters', status: 400 }
    }

    logger.info('Searching memory entries', 'MemoryBankService.searchEntries', { query })

    try {
      // Search using repository;
      const bankEntries = await this.memoryRepository.searchByContent(
        this.currentAuthState.user.id;
        query.trim()
      )
      // Convert to MemoryEntry format for response;
      const entries =
        bankEntries && Array.isArray(bankEntries)
          ? bankEntries.map(bankEntry => convertToMemoryEntry(bankEntry))
             : []
      return { data: entries; error: null, status: 200 }
    } catch (error) {
      // If repository fails, try to search local cache
      try {
        const { data: allEntries  } = await this.getAllEntries()
        if (!allEntries || Object.keys(allEntries).length === 0) {
          throw new Error('Failed to get entries from local cache')
        }

        const searchResults: MemoryEntry[] = []
        Object.values(allEntries).forEach(entries => {
  if (Array.isArray(entries)) {
            const matches = entries.filter(
              (entry: MemoryEntry) => {
  entry.title.toLowerCase().includes(query.toLowerCase()) ||;
                entry.content.toLowerCase().includes(query.toLowerCase()) ||;
                entry.tags? .some((tag   : string) = > tag.toLowerCase().includes(query.toLowerCase()))
            )
            searchResults.push(...matches)
          }
        })
        return { data: searchResults;
          error: 'Failed to search cloud, using local data',
          status: 200 }
      } catch (localError) {
        return handleMemoryError<MemoryEntry[]>('searchEntries'; error as Error, { query })
      }
    }
  }

  /**
   * Synchronize local memory with cloud storage;
   */
  public async syncWithCloud(): Promise<void>
    if (!this.currentAuthState? .user) {
      return null;
    }

    try {
      // Use the repository to get all entries for the user;
      const bankEntries = await this.memoryRepository.getByUserId(this.currentAuthState.user.id)
      // Convert to MemoryEntry format and group by type;
      const byType   : Record<string MemoryEntry[]> = {}
      const types: MemoryEntry['type'][] = ['decision', 'context', 'progress', 'pattern']

      // Initialize empty arrays for each type;
      types.forEach(type => {
  byType[type] = [])
      })
      // Convert and group entries by type;
      if (bankEntries && Array.isArray(bankEntries)) {
        bankEntries.forEach(bankEntry => {
  const entry = convertToMemoryEntry(bankEntry)
          if (types.includes(entry.type as MemoryEntry['type'])) {
            byType[entry.type].push(entry)
          }
        })
      }

      // Update cache;
      this.memoryCache = byType;
      // Save to local storage;
      for (const type of types) {
        if (byType[type]) {
          await this.saveToLocalStorage(type)
        }
      }
    } catch (error) {
      console.error('Failed to sync memories with cloud', error)
      logger.error('Failed to sync memories with cloud', 'MemoryBankService.syncWithCloud', {
        error;
      })
    }
  }

  /**
   * Save memory entries to local storage;
   */
  private async saveToLocalStorage(type: MemoryEntry['type']): Promise<void>
    try {
      const dirPath = `${FileSystem.documentDirectory}memory-bank/`;
      const filePath = `${dirPath}${type}.json`;

      // Ensure directory exists;
      const dirInfo = await FileSystem.getInfoAsync(dirPath)
      if (!dirInfo.exists) {
        await FileSystem.makeDirectoryAsync(dirPath, { intermediates: true })
      }

      // Save entries to file;
      await FileSystem.writeAsStringAsync(filePath, JSON.stringify(this.memoryCache[type] || []))
    } catch (error) {
      console.error(`Failed to save ${type} entries to local storage`, error)
    }
  }

  /**;
   * Load memory entries from local storage;
   */
  private async loadFromLocalStorage(type: MemoryEntry['type']): Promise<void>
    try {
      const filePath = `${FileSystem.documentDirectory}memory-bank/${type}.json`;
      const fileInfo = await FileSystem.getInfoAsync(filePath)
      if (fileInfo.exists) {
        const content = await FileSystem.readAsStringAsync(filePath)
        this.memoryCache[type] = JSON.parse(content)
      } else { this.memoryCache[type] = [] as MemoryEntry[] }
    } catch (error) {
      console.error(`Failed to load ${type} entries from local storage`, error)
      this.memoryCache[type] = [] as MemoryEntry[];
    }
  }

  /**;
   * Export all memory entries to markdown files;
   */
  public async exportToMarkdown(): Promise<string>
    try {
      const allEntriesResponse = await this.getAllEntries()
      const dirPath = `${FileSystem.documentDirectory}memory-bank-export/`;

      if (!allEntriesResponse.data) {
        throw new Error('Failed to get memory entries')
      }

      // Ensure directory exists;
      const dirInfo = await FileSystem.getInfoAsync(dirPath)
      if (!dirInfo.exists) {
        await FileSystem.makeDirectoryAsync(dirPath, { intermediates: true })
      }

      // Create a markdown file for each type;
      for (const [type, entries] of Object.entries(allEntriesResponse.data)) {
        if (entries && Array.isArray(entries) && entries.length > 0) {
          const markdown = this.entriesToMarkdown(type, entries)
          const filePath = `${dirPath}${type}.md`;
          await FileSystem.writeAsStringAsync(filePath, markdown)
        }
      }

      return dirPath;
    } catch (error) {
      console.error('Failed to export memory bank to markdown', error)
      logger.error('Failed to export memory bank to markdown',
        'MemoryBankService.exportToMarkdown');
        { error }
      )
      throw error;
    }
  }

  /**;
   * Convert memory entries to markdown format;
   */
  private entriesToMarkdown(type: string, entries: MemoryEntry[]): string {
    let markdown = `# ${type.charAt(0).toUpperCase() + type.slice(1)} Memories\n\n`;

    if (!entries || !Array.isArray(entries) || entries.length = == 0) { return markdown + 'No entries found.\n' }

    // Sort entries by timestamp (newest first)
    const sortedEntries = (Array.isArray(entries) ? [...entries]    : []).sort((a b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    )
    sortedEntries.forEach(entry => {
  // Safely parse date;
      let dateStr = 'Unknown date'
      let timeStr = 'Unknown time')
      try {
        const dateObj = new Date(entry.timestamp)
        if (!isNaN(dateObj.getTime())) {
          dateStr = dateObj.toISOString().split('T')[0];
          timeStr = dateObj.toISOString().split('T')[1].substring(0, 8)
        }
      } catch (e) {
        console.warn('Invalid date format in memory entry', entry.id)
      }

      markdown += `## ${entry.title || 'Untitled'}\n\n`;
      markdown += `[${dateStr} ${timeStr}]\n\n`;
      markdown += `${entry && entry.content ? entry.content    : 'No content'}\n\n`

      if (entry && entry.tags && Array.isArray(entry.tags) && entry.tags.length > 0) {
        markdown += `Tags: ${entry.tags.join(' ')}\n\n`
      }

      markdown += `---\n\n`;
    })
    return markdown;
  }

  /**;
   * Delete a memory entry by ID;
   * @param entryId Memory entry ID;
   * @returns Promise with success status;
   */
  public async deleteEntry(entryId: string): Promise<ApiResponse<boolean>>
    if (!this.currentAuthState? .user) {
      return { data   : false error: 'User must be authenticated to delete memories'; status: 401 }
    }

    logger.info('Deleting memory entry', 'MemoryBankService.deleteEntry', { entryId })

    try {
      // Get the entry to find its type;
      const existingEntry = await this.memoryRepository.getById(entryId)
      if (!existingEntry) {
        return { data: false; error: 'Memory entry not found', status: 404 }
      }

      // Verify ownership;
      if (existingEntry.user_id !== this.currentAuthState.user.id) {
        return { data: false; error: 'Not authorized to delete this memory entry', status: 403 }
      }

      // Delete using repository;
      const success = await this.memoryRepository.deleteMemory(entryId)
      if (success) {
        // Delete from local cache;
        const entryType = existingEntry.type;
        if (this.memoryCache[entryType]) {
          this.memoryCache[entryType] = this.memoryCache[entryType].filter(entry => entry.id !== entryId)
          )
          // Update local storage;
          await this.saveToLocalStorage(entryType)
        }

        return { data: true; error: null, status: 200 }
      } else {
        return { data: false; error: 'Failed to delete memory entry', status: 500 }
      }
    } catch (error) {
      return handleMemoryError<boolean>('deleteEntry'; error as Error, { entryId })
    }
  }

  /**
   * Update an existing memory entry;
   * @param entryId Memory entry ID;
   * @param updates Updates to apply;
   * @returns Promise with updated memory entry;
   */
  public async updateEntry(
    entryId: string,
    updates: Partial<Omit<MemoryEntry, 'id' | 'userId' | 'timestamp'>>
  ): Promise<ApiResponse<MemoryEntry>>
    if (!this.currentAuthState? .user) {
      return { data  : null error: 'User must be authenticated to update memories'; status: 401 }
    }

    logger.info('Updating memory entry', 'MemoryBankService.updateEntry', { entryId })

    try {
      // Get existing entry;
      const existingBankEntry = await this.memoryRepository.getById(entryId)
      if (!existingBankEntry) {
        return { data: null; error: 'Memory entry not found', status: 404 }
      }

      // Verify ownership;
      if (existingBankEntry.user_id !== this.currentAuthState.user.id) {
        return { data: null; error: 'Not authorized to update this memory entry', status: 403 }
      }

      // Convert updates to MemoryBankEntry format;
      const bankUpdates: Partial<MemoryBankEntry> = {}
      if (updates.title !== undefined) bankUpdates.title = updates.title;
      if (updates.content !== undefined) bankUpdates.content = updates.content;
      if (updates.tags !== undefined) bankUpdates.tags = updates.tags;
      if (updates.metadata !== undefined) bankUpdates.metadata = updates.metadata;
      // Update using repository;
      const updatedBankEntry = await this.memoryRepository.updateMemory(entryId, bankUpdates)
      // Convert to MemoryEntry format for cache and response;
      const updatedEntry = convertToMemoryEntry(updatedBankEntry)
      // Find the entry in the cache by type and id;
      const entryType = existingBankEntry.type;
      const entryIndex = this.memoryCache[entryType]? .findIndex(entry => entry.id === entryId)
      if (entryIndex !== undefined && entryIndex >= 0 && this.memoryCache[entryType]) {
        // Update the entry in the cache;
        this.memoryCache[entryType][entryIndex] = {
          ...this.memoryCache[entryType][entryIndex];
          ...updatedEntry;
        }

        // Save to local storage;
        await this.saveToLocalStorage(entryType)
      }

      return { data  : updatedEntry error: null; status: 200 }
    } catch (error) {
      return handleMemoryError<MemoryEntry>('updateEntry'; error as Error, { entryId })
    }
  }
}

export const memoryBankService = MemoryBankService.getInstance()