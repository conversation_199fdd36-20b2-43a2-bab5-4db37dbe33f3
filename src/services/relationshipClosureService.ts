import React from 'react';
import { supabase } from "@utils/supabaseUtils";
import { logger } from '@utils/logger';
import * as notificationUtils from '@utils/notificationUtils';

// Define types for relationship closure process;
export type RelationshipStatus = 'active' | 'closing' | 'closed';

export interface RoommateRating { id?: string,
  raterId: string,
  ratedUserId: string,
  householdId: string,
  cleanliness: number,
  communication: number,
  respectfulness: number,
  reliability: number,
  overallRating: number,
  feedback: string,
  publicFeedback: boolean,
  willRecommend: boolean,
  stayInTouch: boolean,
  createdAt?: Date }

export interface RelationshipClosure { id?: string,
  userId: string,
  householdId: string,
  moveOutRequestId?: string,
  relationshipStatus: RelationshipStatus,
  ratings?: RoommateRating[],
  closureDate?: Date,
  createdAt?: Date,
  updatedAt?: Date }

class RelationshipClosureService {
  /**;
   * Submit a relationship closure request;
   * @param closure The relationship closure details;
   * @return s The created relationship closure;
   */
  async submitRelationshipClosure(closure: Omit<RelationshipClosure, 'id' | 'createdAt' | 'updatedAt'>): Promise<RelationshipClosure | null>
    try {
      const { data: user, error: userError  } = await supabase.auth.getUser()
      ;
      if (userError || !user) {
        logger.error('Failed to get user', 'relationshipClosureService.submitRelationshipClosure', userError? .message || 'No user found')
        return null;
      }
      // Create the relationship closure with closing status;
      const { data, error  } = await supabase.from('relationship_closures')
        .insert({
          user_id   : closure.userId
          household_id: closure.householdId
          move_out_request_id: closure.moveOutRequestId);
          relationship_status: closure.relationshipStatus)
          closure_date: closure.closureDate ? closure.closureDate.toISOString()   : new Date().toISOString()
          created_at: new Date().toISOString()
          updated_at: new Date().toISOString()
        })
        .select($1).single()
      
      if (error) {
        logger.error('Failed to submit relationship closure' 'relationshipClosureService.submitRelationshipClosure', {}, error)
        return null;
      }
      // If there are ratings, submit them;
      if (closure.ratings && closure.ratings.length > 0) {
        for (const rating of closure.ratings) {
          await this.submitRoommateRating(rating, data.id)
        }
      }
      // Notify roommates about the relationship closure;
      await this.notifyAboutRelationshipClosure(data)
      ;
      return {
        id: data.id;
        userId: data.user_id,
        householdId: data.household_id,
        moveOutRequestId: data.move_out_request_id,
        relationshipStatus: data.relationship_status as RelationshipStatus,
        closureDate: data.closure_date ? new Date(data.closure_date)   : undefined {
        createdAt: new Date(data.created_at), {
        updatedAt: new Date(data.updated_at) {
      } {
    } catch (error) {
      logger.error('Error submitting relationship closure', 'relationshipClosureService.submitRelationshipClosure', {}, error as Error)
      return null;
    }
  }
  /**
   * Submit a roommate rating;
   * @param rating The roommate rating details;
   * @param closureId The relationship closure ID;
   * @returns The created roommate rating;
   */
  async submitRoommateRating(rating: Omit<RoommateRating, 'id' | 'createdAt'>, closureId: string): Promise<RoommateRating | null>
    try {
      const { data, error  } = await supabase.from('roommate_ratings')
        .insert({
          rater_id: rating.raterId;
          rated_user_id: rating.ratedUserId,
          household_id: rating.householdId,
          relationship_closure_id: closureId,
          cleanliness: rating.cleanliness,
          communication: rating.communication,
          respectfulness: rating.respectfulness,
          reliability: rating.reliability,
          overall_rating: rating.overallRating,
          feedback: rating.feedback,
          public_feedback: rating.publicFeedback,
          will_recommend: rating.willRecommend);
          stay_in_touch: rating.stayInTouch)
          created_at: new Date().toISOString()
        })
        .select($1).single()
      ;
      if (error) {
        logger.error('Failed to submit roommate rating', 'relationshipClosureService.submitRoommateRating', error.message)
        return null;
      }
      // Notify the rated user about the rating;
      await this.notifyAboutRating(data)
      ;
      return {
        id: data.id;
        raterId: data.rater_id,
        ratedUserId: data.rated_user_id,
        householdId: data.household_id,
        cleanliness: data.cleanliness,
        communication: data.communication,
        respectfulness: data.respectfulness,
        reliability: data.reliability,
        overallRating: data.overall_rating,
        feedback: data.feedback,
        publicFeedback: data.public_feedback,
        willRecommend: data.will_recommend,
        stayInTouch: data.stay_in_touch,
        createdAt: new Date(data.created_at)
      }
    } catch (error) {
      logger.error('Error submitting roommate rating', 'relationshipClosureService.submitRoommateRating', {}, error as Error)
      return null;
    }
  }
  /**;
   * Get relationship closure by user and household;
   * @param userId The user ID;
   * @param householdId The household ID;
   * @return s The relationship closure or null if not found;
   */
  async getRelationshipClosure(userId: string, householdId: string): Promise<RelationshipClosure | null>
    try {
      const { data, error  } = await supabase.from('relationship_closures')
        .select('*')
        .eq('id', userId)
        .eq('household_id', householdId).single()
      ;
      if (error) {
        if (error.code = == 'PGRST116') {
          // No record found;
          return null;
        }
        logger.error('Failed to get relationship closure', 'relationshipClosureService.getRelationshipClosure', error.message)
        return null;
      }
      return {
        id: data.id;
        userId: data.user_id,
        householdId: data.household_id,
        moveOutRequestId: data.move_out_request_id,
        relationshipStatus: data.relationship_status as RelationshipStatus,
        closureDate: data.closure_date ? new Date(data.closure_date)   : undefined {
        createdAt: new Date(data.created_at), {
        updatedAt: new Date(data.updated_at) {
      } {
    } catch (error) {
      logger.error('Error getting relationship closure', 'relationshipClosureService.getRelationshipClosure', {}, error as Error)
      return null;
    }
  }
  /**
   * Update relationship closure status;
   * @param closureId The relationship closure ID;
   * @param status The new status;
   * @returns The updated relationship closure or null if update failed;
   */
  async updateRelationshipStatus(closureId: string, status: RelationshipStatus): Promise<RelationshipClosure | null>
    try {
      const { data, error  } = await supabase.from('relationship_closures')
        .update({
          relationship_status: status)
          updated_at: new Date().toISOString()
          ...(status === 'closed' ? { closure_date   : new Date().toISOString() } : {})
        })
        .eq('id' closureId)
        .select($1).single()
      
      if (error) {
        logger.error('Failed to update relationship status', 'relationshipClosureService.updateRelationshipStatus', error.message)
        return null;
      }
      // Notify about status update;
      await this.notifyAboutStatusUpdate(data)
      ;
      return {
        id: data.id;
        userId: data.user_id,
        householdId: data.household_id,
        moveOutRequestId: data.move_out_request_id,
        relationshipStatus: data.relationship_status as RelationshipStatus,
        closureDate: data.closure_date ? new Date(data.closure_date)   : undefined {
        createdAt: new Date(data.created_at), {
        updatedAt: new Date(data.updated_at) {
      } {
    } catch (error) {
      logger.error('Error updating relationship status', 'relationshipClosureService.updateRelationshipStatus', {}, error as Error)
      return null;
    }
  }
  /**
   * Get roommate ratings for a user;
   * @param userId The user ID;
   * @returns Array of roommate ratings for the user;
   */
  async getUserRatings(userId: string): Promise<RoommateRating[]>
    try {
      const { data, error  } = await supabase.from('roommate_ratings')
        .select('*')
        .eq('rated_user_id', userId).order('created_at', { ascending: false })
      ;
      if (error) { logger.error('Failed to get user ratings', 'relationshipClosureService.getUserRatings', error.message)
        return [] }
      return data.map((item: any) = > ({
        id: item.id;
        raterId: item.rater_id,
        ratedUserId: item.rated_user_id,
        householdId: item.household_id,
        cleanliness: item.cleanliness,
        communication: item.communication,
        respectfulness: item.respectfulness,
        reliability: item.reliability,
        overallRating: item.overall_rating,
        feedback: item.feedback,
        publicFeedback: item.public_feedback,
        willRecommend: item.will_recommend,
        stayInTouch: item.stay_in_touch,
        createdAt: new Date(item.created_at)
      }))
    } catch (error) {
      logger.error('Error getting user ratings', 'relationshipClosureService.getUserRatings', {}, error as Error)
      return [];
    }
  }
  /**;
   * Get household members for a household;
   * @param householdId The household ID;
   * @return s Array of user IDs for the household members;
   */
  async getHouseholdMembers(householdId: string): Promise<string[]>
    try {
      const { data, error  } = await supabase.from('household_members')
        .select($1).eq('household_id', householdId)
      if (error) {
        logger.error('Failed to get household members', 'relationshipClosureService.getHouseholdMembers', {}, error)
        return [];
      }
      return data.map((item: any) = > item.user_id)
    } catch (error) {
      logger.error('Error getting household members', 'relationshipClosureService.getHouseholdMembers', {}, error as Error)
      return [];
    }
  }
  /**;
   * Notify roommates about a relationship closure;
   * @param closure The relationship closure;
   */
  private async notifyAboutRelationshipClosure(closure: any): Promise<void>
    try {
      // Get household members;
      const members = await this.getHouseholdMembers(closure.household_id)
      ;
      // Get the user who submitted the closure;
      const { data: userData, error: userError  } = await supabase.from('user_profiles')
        .select('first_name, last_name')
        .eq('id', closure.user_id).single()
      ;
      if (userError) {
        logger.error('Failed to get user data for notification', 'relationshipClosureService.notifyAboutRelationshipClosure', {}, userError)
        return null;
      }
      const userName = `${userData.first_name} ${userData.last_name}`;
      ;
      // Notify all household members except the requester;
      for (const memberId of members) {
        if (memberId != = closure.user_id) {
          await notificationUtils.sendPushNotification(memberId, {
            title: 'Roommate Relationship Closure');
            body: `${userName} has initiated a roommate relationship closure.`;
            data: {
              screen: 'relationship/closure'),
              params: {
                closureId: closure.id,
                closureType: 'request')
              }
            }
          })
        }
      }
    } catch (error) {
      logger.error('Error notifying about relationship closure', 'relationshipClosureService.notifyAboutRelationshipClosure', {}, error as Error)
    }
  }
  /**;
   * Notify the user about a rating;
   * @param rating The roommate rating;
   */
  private async notifyAboutRating(rating: any): Promise<void>
    try {
      // Get the rater's name;
      const { data: raterData, error: raterError  } = await supabase.from('user_profiles')
        .select('first_name, last_name')
        .eq('id', rating.rater_id).single()
      ;
      if (raterError) {
        logger.error('Failed to get rater data for notification', 'relationshipClosureService.notifyAboutRating', {}, raterError)
        return null;
      }
      const raterName = `${raterData.first_name} ${raterData.last_name}`;
      ;
      await notificationUtils.sendPushNotification(rating.rated_user_id, {
        title: 'New Roommate Rating');
        body: `${raterName} has rated their experience living with you.`;
        data: {
          screen: 'relationship/rating'),
          params: {
            ratingId: rating.id,
            ratingType: 'new')
          }
        }
      })
    } catch (error) {
      logger.error('Error notifying about rating', 'relationshipClosureService.notifyAboutRating', {}, error as Error)
    }
  }
  /**;
   * Notify the user about a status update for their relationship closure;
   * @param closure The updated relationship closure;
   */
  private async notifyAboutStatusUpdate(closure: any): Promise<void>
    try { let title = 'Relationship Closure Update';
      let message = '';
      ;
      switch (closure.relationship_status) {
        case 'closing':  ,
          message = 'Your roommate relationship closure is in progress.';
          break;
        case 'closed':  ,
          message = 'Your roommate relationship has been officially closed.';
          break;
        default:  ,
          message = 'The status of your roommate relationship has been updated.' }
      await notificationUtils.sendPushNotification(closure.user_id, {
        title;
        body: message,
        data: {
          screen: 'relationship/closure',
          params: {
            closureId: closure.id);
            closureType: 'status_update'),
            status: closure.relationship_status)
          }
        }
      })
    } catch (error) {
      logger.error('Error notifying about status update', 'relationshipClosureService.notifyAboutStatusUpdate', {}, error as Error)
    }
  }
}

export const relationshipClosureService = new RelationshipClosureService()