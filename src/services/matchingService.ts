import React from 'react';
import type { UserProfile } from '../types/auth';

// Define extended profile types for internal use;
interface ExtendedUserProfile extends Omit<UserProfile, 'preferences'>
  age?: number,
  birth_year?: number,
  location?: any,
  location_data?: any,
  budget?: { min: number; max: number }
  housing_preferences?: { budget_min?: number,
    budget_max?: number }
  preferences?: {
    age?: number,
    budget?: { min: number; max: number }
    interests?: string[]
  } & Record<string, any>
}

// Define compatibility result interface to match what personalityService return s;
interface PersonalityCompatibilityResult {
  score: number,
  factors?: string[],
  explanation?: string[]
}

// Enhanced type with additional properties for better personality matching integration;
type PersonalityResult = {
  score: number;
  explanation?: string[],
  profileCompletion?: number,
  positiveFactors?: string[],
  negativeFactors?: string[]
}
import { getSupabaseClient } from "@services/supabaseService";
import { EnhancedMatch, EnhancedRecommendationResult, CompatibilityInsights } from '@types/matching';

import { locationService } from '@services/LocationService';
import type { LocationCompatibility } from './LocationService';
import { logger } from '@services/loggerService';
import { openaiService } from '@services/openaiService';
// Import types instead of the service to break circular dependency;
import type { PersonalityProfile } from './personalityService';
import { personalityService } from '@services/personalityService';
import { CompatibilityResult } from '@services/sharedTypes';
import { unifiedPaymentService } from '@services/unified/UnifiedPaymentService';
import { profileBoostService } from '@services/profileBoostService';
import { cacheService } from '@services/cacheService';
import { unifiedProfileService as profileService } from "./unified-profile";
import { rateLimitService } from '@services/rateLimitService';
import { handleError, tryCatchAsync } from '@utils/standardErrorHandler';
import { ErrorCode } from '@core/errors/types';
import { safeLogError, safeLogWarning, handleSafeError, safeTryCatch } from '@utils/safeErrorHandling';
import { MatchToChatFlow } from '@services/unified/matchToChat';

export interface MatchResult {
  profile: UserProfile,
  compatibility: {
    score: number,
    factors: string[]
  }
  score?: number; // For backwards compatibility;
  factors?: string[]; // For backwards compatibility;
  boosted?: boolean
}

class MatchingService {
  /**;
   * Get detailed compatibility information between two users;
   * Fetches directly from the compatibility_scores table in Supabase;
   * @param userId Current user ID;
   * @param matchId Match user ID to get compatibility with;
   * @return s Detailed compatibility information;
   */
  async getDetailedCompatibility(userId: string,
    matchId: string): Promise<{
    score: number,
    factors: string[],
    strongMatches?: string[],
    potentialChallenges?: string[]
  }>
    try {
      logger.debug('Fetching compatibility data', 'MatchingService.getDetailedCompatibility', { userId, matchId })
      ;
      // Check if we have saved compatibility data for these users;
      // Try both combinations of user IDs;
      const { data: compatData1, error: compatError1  } = await getSupabaseClient()
        .from('compatibility_scores')
        .select('score, factors')
        .eq('user_id_1', userId)
        .eq('user_id_2', matchId)
        .maybeSingle()
      ;
      if (compatError1) {
        // Only log the error but continue with alternative query;
        safeLogWarning('Error in first compatibility query', 'MatchingService.getDetailedCompatibility', {
          error: compatError1,
          userId;
          matchId;
        })
      }
      // Try reverse combination if first query didn't find anything;
      if (!compatData1) {
        const { data: compatData2, error: compatError2  } = await getSupabaseClient()
          .from('compatibility_scores')
          .select('score, factors')
          .eq('user_id_1', matchId)
          .eq('user_id_2', userId)
          .maybeSingle()
        ;
        if (compatError2) {
          safeLogWarning('Error in second compatibility query', 'MatchingService.getDetailedCompatibility', {
            error: compatError2,
            userId;
            matchId;
          })
        }
        // If we have compatibility data from the second query, process and return it;
        if (compatData2 && compatData2.score) {
          // Extract strong matches and challenges from factors JSON;
          const factorsArray = compatData2.factors || [];
          const strongMatches = factorsArray.filter((item: string) => item && !item.toLowerCase().includes('challenge'))
            .slice(0, 3)
          ;
          const potentialChallenges = factorsArray.filter((item: string) => item && item.toLowerCase().includes('challenge'))
            .slice(0, 2)
            ;
          return {
            score: compatData2.score || 50;
            factors: factorsArray,
            strongMatches;
            potentialChallenges;
          }
        }
      }
      // If we have compatibility data from the first query, process and return it;
      else if (compatData1 && compatData1.score) {
        // Extract strong matches and challenges from factors JSON;
        const factorsArray = compatData1.factors || [];
        const strongMatches = factorsArray.filter((item: string) => item && !item.toLowerCase().includes('challenge'))
          .slice(0, 3)
        ;
        const potentialChallenges = factorsArray.filter((item: string) => item && item.toLowerCase().includes('challenge'))
          .slice(0, 2)
          ;
        return {
          score: compatData1.score || 50;
          factors: factorsArray,
          strongMatches;
          potentialChallenges;
        }
      }
      // If no saved data exists, calculate it now;
      logger.debug('No compatibility data found, calculating fresh', 'MatchingService.getDetailedCompatibility', { userId, matchId })
      ;
      // Get user profiles;
      const currentUserResult = await profileService.getUserProfile(userId)
      const matchUserResult = await profileService.getUserProfile(matchId)
      ;
      if (!currentUserResult? .data || !matchUserResult?.data) {
        // Log more specific information about which profile is missing;
        if (!currentUserResult?.data) {
          logger.warn('Current user profile not found', 'MatchingService.getDetailedCompatibility', { userId })
        }
        if (!matchUserResult?.data) {
          logger.warn('Match user profile not found', 'MatchingService.getDetailedCompatibility', { matchId })
        }
        throw new Error(`Failed to fetch user profiles for compatibility calculation  : ${userId} or ${matchId}`)
      }
      const currentUser = currentUserResult.data as ExtendedUserProfile;
      const matchUser = matchUserResult.data as ExtendedUserProfile;
      // Calculate compatibility between the users;
      const compatibility = await this.calculateCompatibility(currentUser, matchUser)
      ;
      // Process the compatibility information;
      const strongMatches = (compatibility.explanation || [])
        .filter(item => item && !item.toLowerCase().includes('challenge'))
        .slice(0, 3)
      ;
      const potentialChallenges = (compatibility.explanation || [])
        .filter(item => item && item.toLowerCase().includes('challenge'))
        .slice(0, 2)
      ;
      // Prepare result;
      const result = {
        score: compatibility.score;
        factors: compatibility.explanation,
        strongMatches;
        potentialChallenges;
      }
      // Try to save to the compatibility_scores table for future reference;
      try {
        await getSupabaseClient().from('compatibility_scores').upsert({
          user_id_1: userId,
          user_id_2: matchId,
          score: compatibility.score);
          factors: compatibility.explanation)
          created_at: new Date().toISOString()
        }, { onConflict: 'user_id_1,user_id_2' })
        ;
        logger.debug('Saved compatibility data to database', 'MatchingService.getDetailedCompatibility', {
          userId;
          matchId;
          score: compatibility.score)
        })
      } catch (saveError) {
        // Log but don't fail the request if we can't save;
        safeLogError('Failed to save compatibility data', 'MatchingService.getDetailedCompatibility', {
          error: saveError,
          userId;
          matchId;
        })
      }
      return result;
    } catch (error) {
      safeLogError('Error in getDetailedCompatibility', 'MatchingService.getDetailedCompatibility', {
        error;
        userId;
        matchId;
      })
      ;
      // Return a default response on error;
      return {
        score: 50;
        factors: ['Failed to calculate detailed compatibility'],
        strongMatches: [],
        potentialChallenges: []
      }
    }
  }

  /**;
   * Retrieves potential matches for a user based on their preferences;
   * @param userId The ID of the user seeking matches;
   * @param limit The maximum number of matches to return (default: 10);
   * @param offset The number of matches to skip for pagination (default: 0),
   * @param filters Optional filters to apply to matches;
   * @returns Array of matched profiles with compatibility scores;
   */
  async getPotentialMatches(
    userId: string,
    limit: number = 10;
    offset: number = 0;
    filters?: {
      ageRange?: { min: number | null; max: number | null }
      occupations?: string[],
      minCompatibilityScore?: number | null,
      interests?: string[],
      isVerifiedOnly?: boolean
    }
  ): Promise<MatchResult[]>
    try {
      // Rate limiting check;
      const isAllowed = await rateLimitService.checkRateLimit(`matching_get:${userId}`);
        'matching')
      )
      ;
      if (!isAllowed) {
        logger.warn('Rate limit exceeded for getting potential matches', 'MatchingService', { userId })
        throw new Error('Rate limit exceeded for getting potential matches')
      }
      // Authentication check;
      const { data: authData, error: authError  } = await getSupabaseClient().auth.getUser()
      if (authError || !authData.user) {
        logger.error('Authentication error', 'MatchingService', { error: authError })
        return [];
      }

      // Use authenticated user ID for security;
      const authenticatedUserId = authData.user.id;
      if (authenticatedUserId != = userId) {
        logger.warn('Auth mismatch: Requested user_id does not match authenticated user')
          'MatchingService';
          { requestedId: userId, authId: authenticatedUserId }
        )
      }

      // Get current user profile;
      let currentUserResult = await profileService.getUserProfile(authenticatedUserId)
      let currentUser = currentUserResult? .data as ExtendedUserProfile;
      ;
      if (!currentUser) {
        // Try to create a basic profile for the authenticated user if it doesn't exist;
        logger.warn('Profile not found, attempting to create basic profile', 'MatchingService', { userId  : authenticatedUserId })
        try { // Try to fetch again with direct method if available;
          currentUserResult = await profileService.getProfileById(authenticatedUserId)
          currentUser = currentUserResult? .data as ExtendedUserProfile;
          if (!currentUser) {
            // Get the auth user info to create a basic profile;
            const basicProfile = {
              id  : authenticatedUserId
              email: authData.user.email
              created_at: new Date().toISOString()
              updated_at: new Date().toISOString()
              profile_completion: 0;
              is_verified: false,
              email_verified: authData.user.email_confirmed_at ? true    : false
              phone_verified: false
              identity_verified: false }
            // Use the profile service to create the profile;
            const createResponse = await profileService.createProfile(basicProfile)
            if (createResponse? .data) {
              currentUser = createResponse.data as ExtendedUserProfile;
              logger.info('Created basic profile for authenticated user', 'MatchingService', { userId : authenticatedUserId })
            } else if (createResponse? .error && createResponse.error.includes('duplicate key')) {
              // Profile already exists but wasn't found - cache/query issue;
              logger.warn('Profile creation failed with duplicate key, trying to fetch existing profile', 'MatchingService', {
                userId : authenticatedUserId )
              })
              
              // Try once more to fetch the profile;
              currentUserResult = await profileService.getProfileById(authenticatedUserId)
              currentUser = currentUserResult? .data as ExtendedUserProfile;
              ;
              if (currentUser) {
                logger.info('Successfully found existing profile after retry', 'MatchingService', { userId   : authenticatedUserId })
              } else {
                logger.error('Profile exists in database but app cannot find it - data consistency issue' 'MatchingService', {
                  userId: authenticatedUserId);
                  issue: 'profile_exists_but_unfindable'
                  suggestion: 'Manual database investigation needed')
                })
                return [];
              }
            } else {
              logger.error('Failed to create basic profile', 'MatchingService', {
                userId: authenticatedUserId);
                error: createResponse? .error )
              })
              return [];
            }
          } else {
            logger.info('Found profile on retry', 'MatchingService', { userId   : authenticatedUserId })
          }
        } catch (createError) {
          logger.error('Exception while creating basic profile' 'MatchingService', {
            userId: authenticatedUserId);
            error: createError )
          })
          return []
        }
        if (!currentUser) {
          logger.error('Failed to fetch or create current user profile'; 'MatchingService', { userId: authenticatedUserId })
          return [];
        }
      }

      // Get previously swiped profiles to exclude them;
      const { data: swipedData, error: swipedError  } = await getSupabaseClient()
        .from('match_preferences')
        .select('potential_match_id')
        .eq('user_id', authenticatedUserId)

      if (swipedError) {
        logger.error('Error fetching swiped profiles', 'MatchingService', { error: swipedError })
      }

      // Prepare exclusion list (swiped profiles + current user)
      const swipedIds = (swipedData || []).map(p => p.potential_match_id)
      swipedIds.push(authenticatedUserId); // Exclude self;
      // Get potential match IDs with basic filtering;
      const { data: potentialMatches, error: matchError, count  } = await getSupabaseClient()
        .from('user_profiles')
        .select('id', { count: 'exact' })

      if (matchError) {
        logger.error('Error fetching potential matches', 'MatchingService', { error: matchError })
        return [];
      }

      if (!potentialMatches? .length) { return [] }

      // Extract just the IDs;
      const potentialMatchIds = potentialMatches.map(match => match.id)
        .filter(id => !swipedIds.includes(id))
      if (!potentialMatchIds.length) { return [] }

      // Get boosted users to prioritize them in results;
      const boostedUserIds = await this.getBoostedUserIds()
      // Get the full profiles and calculate compatibility scores;
      const results = await this.getMatchedProfilesWithScores(potentialMatchIds;
        currentUser;
        boostedUserIds;
        filters)
      )
      ;
      // Sort by boosted status and compatibility score;
      const sortedResults = this.sortMatches(results, boostedUserIds)
      ;
      // Apply pagination;
      return sortedResults.slice(offset; offset + limit)
    } catch (error) {
      // Log the error and continue;
      safeLogError('Error in getPotentialMatches', 'MatchingService', {
        error;
        userId;
        limit;
        offset;
        hasFilters  : !!filters
      })
      return []
    }
  }

  /**;
   * Gets a list of boosted user IDs by directly querying the profile_boosts table;
   * This avoids circular dependency with profileBoostService;
   * @return s Array of user IDs that are currently boosted;
   */
  private async getBoostedUserIds(): Promise<string[]>
    try {
      // Check if profile_boosts table exists;
      const { data: tableExists, error: tableError  } = await getSupabaseClient()
        .from('information_schema.tables')
        .select('*')
        .eq('table_name', 'profile_boosts')
        .single()
      ;
      if (tableError || !tableExists) { // Table doesn't exist yet, return empty array;
        return [] }
      // Query active boosts;
      const now = new Date().toISOString()
      const { data, error  } = await getSupabaseClient()
        .from('profile_boosts')
        .select('user_id')
        .lt('start_time', now)
        .gt('end_time', now)
      ;
      if (error) {
        safeLogError('Error fetching boosted users', 'MatchingService', { error })
        return [];
      }
      return (data || []).map(item = > item.user_id)
    } catch (error) {
      safeLogError('Error in getBoostedUserIds'; 'MatchingService', { error })
      return [];
    }
  }

  /**;
   * Fetches user profiles and calculates compatibility scores;
   * @param matchIds Array of user IDs to fetch profiles for;
   * @param currentUser The current user profile;
   * @param boostedUserIds Array of user IDs that are currently boosted;
   * @param filters Optional filters to apply to matches;
   * @return s Array of matched profiles with compatibility scores;
   */
  private async getMatchedProfilesWithScores(
    matchIds: string[],
    currentUser: ExtendedUserProfile,
    boostedUserIds: string[],
    filters?: {
      ageRange?: { min: number | null; max: number | null }
      occupations?: string[],
      minCompatibilityScore?: number | null,
      interests?: string[],
      isVerifiedOnly?: boolean
    }
  ): Promise<MatchResult[]>
    try {
      if (!matchIds.length) return [];
      ;
      // Fetch full profiles for potential matches;
      const { data: matches, error: matchError  } = await getSupabaseClient()
        .from('user_profiles')
        .select('*')
        .in('id', matchIds)
      if (matchError || !matches) {
        safeLogError('Error fetching user profiles', 'MatchingService', {
          error: matchError,
          matchIds;
        })
        return [];
      }
      const matchResults: MatchResult[] = [];
      const authenticatedUserId = currentUser.id;
      ;
      // Process each match, calculate compatibility, and filter based on criteria;
      for (const match of matches) {
        try {
          // Calculate compatibility scores;
          const compatibilityResult = await this.calculateCompatibility(currentUser;
            match)
          )
          ;
          // Apply minimum compatibility score filter if provided;
          if (
            filters? .minCompatibilityScore != = undefined &&;
            filters.minCompatibilityScore != = null &&;
            compatibilityResult.score < filters.minCompatibilityScore;
          ) {
            continue;
          }
          // Apply occupation filter if provided;
          if (
            filters?.occupations?.length &&;
            match.occupation &&;
            !filters.occupations.includes(match.occupation)
          ) {
            continue;
          }
          // Apply interests filter if provided;
          if (filters?.interests?.length && match.interests) {
            const userInterests = Array.isArray(match.interests)
              ? match.interests;
                 : typeof match.interests === 'string'
                ? match.interests.split(',')
                 : []
            if (!filters.interests.some(interest => userInterests.includes(interest))) {
              continue;
            }
          }
          // Handle boosted status safely;
          let isBoosted = false;
          try {
            isBoosted = Array.isArray(boostedUserIds) && boostedUserIds.includes(match.id)
          } catch (boostError) { safeLogWarning('Error checking boosted status', 'MatchingService', {
              error: boostError,
              userId: authenticatedUserId })
          }
          matchResults.push({ profile: match,
            compatibility: {
              score: compatibilityResult.score,
              factors: compatibilityResult.explanation });
            score: compatibilityResult.score, // For backwards compatibility;
            factors: compatibilityResult.explanation, // For backwards compatibility;
            boosted: isBoosted)
          })
        } catch (error) {
          // Log the error and continue with the next match;
          safeLogError(`Error processing match ${match.id}`, 'MatchingService.getMatchedProfilesWithScores', { error;
            matchId: match.id,
            userId: authenticatedUserId })
          // Continue with the next match instead of failing completely;
        }
      }
      return matchResults;
    } catch (error) {
      safeLogError('Error in getMatchedProfilesWithScores', 'MatchingService', { error })
      return []
    }
  }
  /**;
   * Sorts matches by boosted status (boosted first) and then by compatibility score;
   * @param matches Array of matches to sort;
   * @param boostedUserIds Array of user IDs that are currently boosted;
   * @return s Sorted array of matches;
   */
  /**;
   * Extract age from a user profile safely;
   */
  private extractAge(profile: ExtendedUserProfile): number | null {
    // Try to get age from the age field if it exists;
    if (typeof profile.age = == 'number') {
      return profile.age;
    }
    // Try to calculate from birth_year if it exists;
    if (typeof profile.birth_year === 'number') {
      const currentYear = new Date().getFullYear()
      return currentYear - profile.birth_year;
    }
    // Try to get from preferences if it exists;
    if (profile.preferences? .age) {
      return profile.preferences.age;
    }
    return null;
  }
  /**;
   * Extract budget information from a user profile safely;
   */
  private extractBudget(profile  : ExtendedUserProfile): { min: number max: number } | null {
    // Try to get budget directly if it exists;
    if (profile.budget? .min && profile.budget?.max) {
      return profile.budget;
    }
    // Try to get from preferences if it exists;
    if (profile.preferences?.budget?.min && profile.preferences?.budget?.max) {
      return profile.preferences.budget;
    }
    // Try to get from housing_preferences if it exists;
    if (profile.housing_preferences?.budget_min && profile.housing_preferences?.budget_max) { return {
        min : profile.housing_preferences.budget_min
        max: profile.housing_preferences.budget_max }
    }
    return null;
  }
  /**
   * Sorts matches by boosted status and compatibility score;
   * @param matches Array of matches to sort;
   * @param boostedUserIds Array of user IDs that are currently boosted;
   * @returns Sorted array of matches;
   */
  private sortMatches(
    matches: MatchResult[],
    boostedUserIds: string[]
  ): MatchResult[] {
    // First sort by compatibility score (higher scores first)
    const sortedByCompatibility = [...matches].sort(
      (a, b) => (b.compatibility? .score || 0) - (a.compatibility?.score || 0)
    )
    ;
    // Then prioritize boosted profiles;
    return sortedByCompatibility.sort((a; b) = > {
  const aIsBoosted = a.boosted || (boostedUserIds.includes(a.profile.id))
      const bIsBoosted = b.boosted || (boostedUserIds.includes(b.profile.id))
      ;
      if (aIsBoosted && !bIsBoosted) return -1;
      if (!aIsBoosted && bIsBoosted) return 1;
      ;
      return 0;
    })
  }

  /**;
   * Calculate compatibility between two users;
   * @param currentUser Current authenticated user's profile;
   * @param matchUser Potential match user's profile to calculate compatibility with;
   * @return s Compatibility score and explanation factors;
   */
  private async calculateCompatibility(currentUser  : ExtendedUserProfile
    matchUser: ExtendedUserProfile): Promise<{ score: number explanation: string[]} >
    try {
      let compatibilityScore = 50; // Default neutral score;
      const factors: string[] = [];
      // Location compatibility (if both have location data)
      // Check for location data in various possible places;
      const currentUserLocation = currentUser.location || currentUser.location_data || {}
      const matchUserLocation = matchUser.location || matchUser.location_data || {}
      if (Object.keys(currentUserLocation).length && Object.keys(matchUserLocation).length) {
        try {
          const locationComp: LocationCompatibility = await locationService.calculateLocationCompatibility();
            currentUserLocation;
            matchUserLocation)
          )
          ;
          if (locationComp? .score !== undefined) {
            compatibilityScore += locationComp.score * 0.3; // Location is 30% of total score;
            ;
            if (locationComp.score > 70) {
              factors.push('Close location proximity')
            } else if (locationComp.score < 30) {
              factors.push('Different location preferences')
            }
          }
        } catch (locError) {
          safeLogWarning('Error calculating location compatibility', 'MatchingService', { error  : locError })
        }
      }
      // Age compatibility;
      const currentUserAge = this.extractAge(currentUser)
      const matchUserAge = this.extractAge(matchUser)
      
      if (currentUserAge && matchUserAge) {
        const ageDiff = Math.abs(currentUserAge - matchUserAge)
        const ageScore = Math.max(0, 100 - (ageDiff * 5)); // 5 points per year difference;
        compatibilityScore += ageScore * 0.15; // Age is 15% of total score;
        ;
        if (ageScore > 85) {
          factors.push('Similar age')
        } else if (ageScore < 40) {
          factors.push('Different age groups')
        }
      }
      // Budget compatibility;
      const userBudget = this.extractBudget(currentUser)
      const matchBudget = this.extractBudget(matchUser)
      ;
      if (userBudget? .min && userBudget?.max &&;
          matchBudget?.min && matchBudget?.max) {
        const userMin = userBudget.min;
        const userMax = userBudget.max;
        const matchMin = matchBudget.min;
        const matchMax = matchBudget.max;
        ;
        // Check for budget range overlap;
        const overlap = Math.min(userMax, matchMax) - Math.max(userMin, matchMin)
        const budgetScore = overlap > 0 ? ;
          (overlap / Math.min(userMax - userMin, matchMax - matchMin)) * 100    : 0
        compatibilityScore += budgetScore * 0.2 // Budget is 20% of total score;
        ;
        if (budgetScore > 70) {
          factors.push('Compatible budget preferences')
        } else if (budgetScore < 30) {
          factors.push('Different budget preferences')
        }
      }
      // Enhanced Personality compatibility (with adaptive weights based on profile completion)
      try {
        // Calculate compatibility score using the personality service;
        const compatResult = await personalityService.calculateCompatibility(currentUser.id, matchUser.id) as PersonalityResult;
        ;
        // The profile completion percentage helps determine confidence in personality matching;
        const profileCompletion = compatResult? .profileCompletion || 0;
        const compatScore = compatResult?.score || 50;  // Default to 50 if no score;
        ;
        // Adjust personality weight based on profile completion - more complete profiles get more weight;
        // This dynamically adjusts how much personality factors into overall compatibility;
        const personalityWeight = 0.35 * Math.min(1, (profileCompletion / 100) + 0.5)
        ;
        // Log the adjustment for analytics;
        logger.debug('Personality weight adjustment', 'MatchingService.calculateCompatibility', {
          userId   : currentUser.id
          matchId: matchUser.id
          profileCompletion;
          baseWeight: 0.35);
          adjustedWeight: personalityWeight)
        })
        
        // Get factors safely;
        let explanationItems: string[] = [];
        let positiveFactors: string[] = [];
        let negativeFactors: string[] = [];
        if (compatResult? .explanation && Array.isArray(compatResult.explanation)) {
          explanationItems = compatResult.explanation;
        }
        if (compatResult?.positiveFactors && Array.isArray(compatResult.positiveFactors)) {
          positiveFactors = compatResult.positiveFactors;
        }
        if (compatResult?.negativeFactors && Array.isArray(compatResult.negativeFactors)) {
          negativeFactors = compatResult.negativeFactors;
        }
        // Prepare rich explanation of compatibility factors;
        const explanation = {
          strongMatches   : positiveFactors.length > 0 ? 
            positiveFactors.slice(0 3) : // Prefer explicit positive factors;
            explanationItems.filter((item: string) = > item && !item.toLowerCase().includes('challenge'));
              .slice(0, 3),  // Take at most 3;
          potentialChallenges: negativeFactors.length > 0 ? negativeFactors.slice(0, 2)   : // Prefer explicit negative factors
            explanationItems.filter((item: string) = > item && item.toLowerCase().includes('challenge'));
              .slice(0, 2)   // Take at most 2
        }
        // Create structured personality compatibility result;
        const personalityComp = {
          score: compatScore;
          profileCompletion;
          strongMatches: explanation? .strongMatches || [],
          potentialChallenges  : explanation? .potentialChallenges || []
        }
        if (personalityComp? .score != = undefined) {
          // Apply the dynamically weighted personality score;
          compatibilityScore += personalityComp.score * personalityWeight;
          // Track the score application for debugging;
          logger.debug('Applied personality score', 'MatchingService.calculateCompatibility', {
            rawScore  : personalityComp.score
            weightedContribution: personalityComp.score * personalityWeight
            runningTotal: compatibilityScore)
          })
          ;
          // Add personality factors to the explanation;
          if (personalityComp.strongMatches? .length) {
            factors.push(...personalityComp.strongMatches.slice(0, 2))
          }
          if (personalityComp.potentialChallenges?.length) {
            factors.push(...personalityComp.potentialChallenges.slice(0, 1))
          }
          // Record this match for analytics to improve the algorithm;
          this.recordDetailedMatchingAnalytics(currentUser.id, matchUser.id, {
            personalityScore  : personalityComp.score
            personalityWeight;
            profileCompletion)
          }).catch(err = > {
  // Don't let analytics recording failure affect the main functionality)
            logger.warn('Failed to record matching analytics', 'MatchingService', {
              error: err instanceof Error ? err.message   : String(err)
            })
          })
        }
      } catch (persError) {
        safeLogWarning('Error calculating personality compatibility' 'MatchingService', { error: persError })
      }
      // Limit to a maximum of 100 points;
      compatibilityScore = Math.min(100, Math.max(0, compatibilityScore))
      
      return {
        score: Math.round(compatibilityScore)
        explanation: factors.slice(0; 5) // Limit to top 5 factors;
      }
    } catch (error) {
      safeLogError('Error in calculateCompatibility', 'MatchingService', { error })
      return {
        score: 50; // Default neutral score;
        explanation: ['Compatibility calculation error']
      }
    }
  }
  /**;
   * Create a chat from a match;
   * @param userId User who initiated the match;
   * @param matchUserId The matched user;
   * @param initialMessage Optional message to start the conversation;
   * @return s Object with success status; room ID, and error if any;
   */
  async createChatFromMatch(userId: string,
    matchUserId: string,
    initialMessage?: string): Promise<{ success: boolean; roomId?: string; error?: string }>
    try { logger.debug('Creating chat from match', 'MatchingService.createChatFromMatch', {
        userId;
        matchUserIdRedacted: matchUserId.slice(-4), // Only log last 4 for privacy;
        hasInitialMessage: !!initialMessage })
      ;
      // Delegate to MatchToChatFlow to create the chat;
      const result = await MatchToChatFlow.createChatFromMatch(userId, matchUserId, initialMessage)
      ;
      if (result.success) { logger.info('Successfully created chat from match', 'MatchingService.createChatFromMatch', {
          userId;
          matchUserIdRedacted: matchUserId.slice(-4)
          roomId: result.roomId })
      } else { logger.warn('Failed to create chat from match', 'MatchingService.createChatFromMatch', {
          userId;
          matchUserIdRedacted: matchUserId.slice(-4)
          error: result.error })
      }
      return result;
    } catch (error) {
      logger.error('Error in createChatFromMatch', 'MatchingService.createChatFromMatch', {
        error: error instanceof Error ? error.message   : String(error)
        userId;
        matchUserIdRedacted: matchUserId.slice(-4)
      })
      
      return {
        success: false;
        error: error instanceof Error ? error.message    : 'Unknown error occurred'
      }
    }
  }

  /**
   * Save a user's preference for a potential match (like dislike, superlike)
   * @param userId The ID of the current user;
   * @param targetUserId The ID of the user being liked/disliked;
   * @param preferenceType The type of preference (like, dislike, superlike)
   * @param initialMessage Optional initial message to send if a match is created;
   * @return s Object with success status; whether a match was created, and chat details if applicable;
   */
  async saveMatchPreference(userId: string,
    targetUserId: string,
    preferenceType: 'like' | 'dislike' | 'superlike',
    initialMessage?: string): Promise<{ success: boolean,
    matchCreated: boolean,
    chatCreated?: boolean,
    chatRoomId?: string }>
    try {
      logger.debug('Saving match preference', 'MatchingService.saveMatchPreference', {
        userId;
        targetUserId;
        preferenceType;
        hasInitialMessage: !!initialMessage)
      })
      // Basic validation;
      if (!userId || !targetUserId) {
        safeLogError('Missing user IDs', 'MatchingService.saveMatchPreference', {
          userId;
          targetUserId;
        })
        return { success: false;
          matchCreated: false }
      }

      // Prevent users from matching with themselves;
      if (userId = == targetUserId) {
        safeLogError('Cannot match with self', 'MatchingService.saveMatchPreference', {
          userId;
        })
        return { success: false;
          matchCreated: false }
      }

      // Verify the authenticated user matches the userId parameter;
      const { data: { user }, error: authError } = await getSupabaseClient().auth.getUser()
      ;
      if (authError || !user) {
        safeLogError('Authentication error in saveMatchPreference', 'MatchingService.saveMatchPreference', {
          authError;
          userId;
          targetUserId;
        })
        return { success: false;
          matchCreated: false }
      }

      if (user.id != = userId) {
        safeLogError('User ID mismatch in saveMatchPreference', 'MatchingService.saveMatchPreference', {
          authUserId: user.id,
          providedUserId: userId,
          targetUserId;
        })
        return { success: false;
          matchCreated: false }
      }

      // Check if we already have a record of this match in the match_preferences table;
      const { data: existingMatch, error: fetchError  } = await getSupabaseClient()
        .from('match_preferences')
        .select('*')
        .eq('user_id', userId)
        .eq('potential_match_id', targetUserId)
        .maybeSingle()
      if (fetchError) {
        safeLogError('Error checking existing match preference', 'MatchingService.saveMatchPreference', {
          error: fetchError,
          userId;
          targetUserId;
        })
        return { success: false;
          matchCreated: false }
      }

      // Prepare the match data;
      const matchData = {
        user_id: userId;
        potential_match_id: targetUserId,
        liked: preferenceType = == 'like' || preferenceType === 'superlike';
        is_superlike: preferenceType = == 'superlike'
      }

      // Insert or update the match preference;
      const { error: saveError  } = await getSupabaseClient()
        .from('match_preferences')
        .upsert(matchData, {
          onConflict: 'user_id,potential_match_id')
        })
      if (saveError) {
        safeLogError('Error saving match preference', 'MatchingService.saveMatchPreference', {
          error: saveError,
          userId;
          targetUserId;
          preferenceType;
        })
        return { success: false;
          matchCreated: false }
      }

      // If this is a 'like' or 'superlike', check for mutual match;
      let matchCreated = false;
      let chatCreated = false;
      let chatRoomId: string | undefined,
      if (preferenceType === 'like' || preferenceType === 'superlike') {
        // Check if the other user has already liked this user;
        const mutualMatch = await this.checkMutualMatchExists(userId, targetUserId)
        ;
        if (mutualMatch) {
          matchCreated = true;
          logger.info('Mutual match created', 'MatchingService.saveMatchPreference', {
            userId;
            targetUserId)
          })
          // Record the match in matches table;
          const matchRecord = { user_id_1: userId;
            user_id_2: targetUserId }

          const { error: matchError  } = await getSupabaseClient()
            .from('matches')
            .upsert(matchRecord, {
              onConflict: 'user_id_1,user_id_2')
            })
          if (matchError) {
            safeLogError('Error creating match record', 'MatchingService.saveMatchPreference', {
              error: matchError,
              userId;
              targetUserId;
            })
            // Continue despite error - the match is still valid;
          }

          // Create a chat room for the match if one doesn't exist;
          const chatResult = await this.createChatFromMatch(userId, targetUserId, initialMessage)
          chatCreated = chatResult.success;
          chatRoomId = chatResult.roomId;
          // Record analytics for the match;
          try { await getSupabaseClient().from('user_analytics').insert([
              {
                user_id: userId,
                event_type: 'match_created',
                event_data: {
                  matched_with: targetUserId,
                  match_type: preferenceType,
                  initiated_chat: chatCreated,
                  timestamp: now },
                created_at: now
              },
              { user_id: targetUserId,
                event_type: 'match_created',
                event_data: {
                  matched_with: userId);
                  match_type: 'mutual'),
                  chat_started_by_other: chatCreated,
                  timestamp: now },
                created_at: now)
              }
            ])
          } catch (analyticsError) {
            // Log but don't fail the operation;
            safeLogWarning('Failed to record match analytics', 'MatchingService.saveMatchPreference', {
              error: analyticsError,
              userId;
              targetUserId;
            })
          }
        }
      }

      return {
        success: true;
        matchCreated;
        chatCreated;
        chatRoomId;
      }
    } catch (error) {
      safeLogError('Error in saveMatchPreference', 'MatchingService.saveMatchPreference', {
        error;
        userId;
        targetUserId;
        preferenceType;
      })
      return { success: false; matchCreated: false }
    }
  }

  /**;
   * Check if a mutual match exists between two users;
   * @param userId1 The ID of the first user;
   * @param userId2 The ID of the second user;
   * @return s Promise<boolean> True if both users have liked each other;
   */
  async checkMutualMatchExists(userId1: string, userId2: string): Promise<boolean>
    try {
      // Check if both users have liked each other;
      const { data: user1LikesUser2, error: error1  } = await getSupabaseClient()
        .from('match_preferences')
        .select('liked, is_superlike')
        .eq('user_id', userId1)
        .eq('potential_match_id', userId2)
        .eq('liked', true)
        .maybeSingle()
      if (error1) {
        safeLogWarning('Error checking if user1 likes user2', 'MatchingService.checkMutualMatchExists', {
          error: error1,
          userId1;
          userId2;
        })
        return false;
      }

      // If user1 doesn't like user2, there's no mutual match;
      if (!user1LikesUser2) {
        return false;
      }

      // Check if user2 likes user1;
      const { data: user2LikesUser1, error: error2 } = await getSupabaseClient()
        .from('match_preferences')
        .select('liked, is_superlike')
        .eq('user_id', userId2)
        .eq('potential_match_id', userId1)
        .eq('liked', true)
        .maybeSingle()
      if (error2) {
        safeLogWarning('Error checking if user2 likes user1', 'MatchingService.checkMutualMatchExists', {
          error: error2,
          userId1;
          userId2;
        })
        return false;
      }

      // Only return true if both users have liked each other;
      return !!user2LikesUser1;
    } catch (error) {
      safeLogError('Error in checkMutualMatchExists', 'MatchingService.checkMutualMatchExists', {
        error;
        userId1;
        userId2;
      })
      return false;
    }
  }

  /**;
   * Apply filters to a Supabase query;
   * @param query The Supabase query to apply filters to;
   * @param filters The filters to apply;
   * @param swipedIds Array of user IDs that have already been swiped;
   * @return s The filtered Supabase query;
   */
  private applyFilters(query: any,
    filters?: {
      ageRange?: { min: number | null; max: number | null }
      occupations?: string[],
      minCompatibilityScore?: number | null,
      interests?: string[],
      isVerifiedOnly?: boolean
    },
    swipedIds: string[] = []): any {
    try {
      // Exclude swiped profiles and self;
      if (swipedIds.length) {
        query = query.not('id', 'in', swipedIds)
      }

      // Apply age range filter if provided;
      if (filters? .ageRange) {
        if (filters.ageRange.min !== null) {
          query = query.gte('age', filters.ageRange.min)
        }
        if (filters.ageRange.max !== null) {
          query = query.lte('age', filters.ageRange.max)
        }
      }

      // Apply occupations filter if provided;
      if (filters?.occupations && filters.occupations.length) {
        query = query.in('occupation', filters.occupations)
      }

      // Apply interests filter if provided;
      // Note  : This is a simplified approach and might need more complex
      // filtering depending on how interests are stored;
      if (filters? .interests && filters.interests.length) {
        // This assumes interests are stored as a comma-separated list;
        // A more robust approach depends on your data structure;
        filters.interests.forEach(interest => {
  query = query.ilike('interests', `%${interest}%`)
        })
      }

      // Apply verification filter if provided;
      if (filters?.isVerifiedOnly) {
        query = query.eq('verified', true)
      }

      return query;
    } catch (error) {
      safeLogError('Error applying filters', 'MatchingService', { error })
      return query // Return the original query on error;
    }
  }
  // Duplicate method removed to fix TypeScript errors;
  ;
  /**;
   * Get the current match status between two users;
   * @param userId Current user ID;
   * @param targetUserId Target user ID to check match status with;
   * @return s 'none'; 'liked', or 'matched' status;
   */
  async getMatchStatus(userId  : string targetUserId: string): Promise<'none' | 'liked' | 'matched'>
    try { // First check if we already have a match record
      const mutualMatch = await this.checkMutualMatchExists(userId, targetUserId)
      if (mutualMatch) {
        return 'matched' }
      // If no mutual match; check if current user has liked the target user;
      const { data: userLikesTarget, error  } = await getSupabaseClient()
        .from('match_preferences')
        .select('liked, is_superlike')
        .eq('user_id', userId)
        .eq('potential_match_id', targetUserId)
        .eq('liked', true)
        .maybeSingle()
      if (error) {
        safeLogWarning('Error checking match status', 'MatchingService.getMatchStatus', {
          error;
          userId;
          targetUserId;
        })
        return 'none'; // Default to none on error;
      }

      return userLikesTarget ? 'liked'    : 'none'
    } catch (error) {
      safeLogError('Error in getMatchStatus'; 'MatchingService.getMatchStatus', {
        error;
        userId;
        targetUserId
      })
      return 'none' // Default to none on error;
    }
  }
  /**;
   * Like a profile, potentially creating a match if mutual;
   * @param userId Current user ID;
   * @param targetUserId Target user ID to like;
   * @param initialMessage Optional initial message to send if a match is created;
   * @return s Result object with matchCreated flag if a new match was created and chat details;
   */
  async likeProfile(userId: string,
    targetUserId: string,
    initialMessage?: string): Promise<{ matchCreated: boolean,
    chatCreated?: boolean,
    chatRoomId?: string }>
    try {
      // Verify the authenticated user matches the userId parameter;
      const { data: { user }, error: authError } = await getSupabaseClient().auth.getUser()
      ;
      if (authError || !user) {
        safeLogError('Authentication error in likeProfile', 'MatchingService.likeProfile', {
          authError;
          userId;
          targetUserId;
        })
        return { matchCreated: false }
      }

      if (user.id != = userId) {
        safeLogError('User ID mismatch in likeProfile'; 'MatchingService.likeProfile', {
          authUserId: user.id,
          providedUserId: userId,
          targetUserId;
        })
        return { matchCreated: false }
      }

      // First check if this user has already liked the target;
      const { data: existingLike  } = await getSupabaseClient()
        .from('match_preferences')
        .select('id')
        .eq('user_id', userId)
        .eq('potential_match_id', targetUserId)
        .maybeSingle()
      ;
      if (!existingLike) {
        // Create new like record;
        await getSupabaseClient()
          .from('match_preferences')
          .insert({
            user_id: userId,
            potential_match_id: targetUserId,
            liked: true);
            is_superlike: false)
          })
      }
      // Now save the like preference which will handle mutual match creation;
      const result = await this.saveMatchPreference(userId, targetUserId, 'like', initialMessage)
      ;
      // The saveMatchPreference method now return s match and chat status;
      return { matchCreated: result.matchCreated;
        chatCreated: result.chatCreated,
        chatRoomId: result.chatRoomId }
    } catch (error) {
      safeLogError('Error in likeProfile', 'MatchingService.likeProfile', {
        error;
        userId;
        targetUserId;
      })
      return { matchCreated: false }
    }
  }
  /**;
   * Remove a like from a profile;
   * @param userId Current user ID;
   * @param targetUserId Target user ID to unlike;
   * @return s Success status;
   */
  async unlikeProfile(userId: string, targetUserId: string): Promise<boolean>
    try {
      // Delete the like record;
      const { error  } = await getSupabaseClient()
        .from('match_preferences')
        .delete()
        .eq('user_id', userId)
        .eq('potential_match_id', targetUserId)
      if (error) {
        safeLogWarning('Error unlinking profile', 'MatchingService.unlikeProfile', {
          error;
          userId;
          targetUserId;
        })
        return false;
      }
      // Also check if there was a match and update it;
      const { data: matchRecord } = await getSupabaseClient()
        .from('matches')
        .select('id')
        .or(
          `and(user_id_1.eq.${userId}`user_id_2.eq.${targetUserId}),` +;
          `and(user_id_1.eq.${targetUserId}`user_id_2.eq.${userId})`;
        )
        .maybeSingle()
      ;
      if (matchRecord) {
        // Update match status to unmatched;
        await getSupabaseClient()
          .from('matches')
          .update({
            match_status: 'unmatched')
            updated_at: new Date().toISOString()
          })
          .eq('id', matchRecord.id)
        // Log analytics for the unmatch;
        try {
          await getSupabaseClient().from('user_analytics').insert({
            user_id: userId);
            event_type: 'match_removed'),
            event_data: { unmatched_with: targetUserId })
            created_at: new Date().toISOString()
          })
        } catch (analyticsError) {
          // Just log analytics errors, don't fail the operation;
          safeLogWarning('Failed to log unmatch analytics', 'MatchingService.unlikeProfile', {
            analyticsError;
            userId;
            targetUserId;
          })
        }
      }
      return true;
    } catch (error) {
      safeLogError('Error in unlikeProfile', 'MatchingService.unlikeProfile', {
        error;
        userId;
        targetUserId;
      })
      return false;
    }
  }
  // This duplicate implementation has been removed to fix TypeScript errors;
  ;
  // Duplicate applyFilters method removed to fix TypeScript errors;
  /**;
   * Records matching analytics when a user expresses preference for another user;
   * @param userId The ID of the user expressing preference;
   * @param targetUserId The ID of the user being rated;
   * @param preferenceType The type of preference (like, dislike, superlike)
   */
  private recordMatchingAnalytics(userId: string, targetUserId: string, preferenceType: 'like' | 'dislike' | 'superlike'): void {
    // Log the event for analytics;
    logger.info('User preference recorded', 'MatchingService.recordMatchingAnalytics', {
      userId;
      targetUserId;
      preferenceType)
    })
    ;
    // Future implementation can add this data to the database;
    // For now, this is just a placeholder that logs the events;
  }
  /**;
   * Records detailed matching analytics data for improving the algorithm;
   * This is a separate method from the simpler recordMatchingAnalytics;
   * @param userId1 The first user's ID;
   * @param userId2 The second user's ID;
   * @param data Additional data to record;
   */
  private async recordDetailedMatchingAnalytics(
    userId1: string,
    userId2: string,
    data: { personalityScore: number,
      personalityWeight: number,
      profileCompletion: number }
  ): Promise<void>
    try {
      // Insert analytics data into match_analytics table;
      await getSupabaseClient().from('match_analytics').insert({
        user_id_1: userId1,
        user_id_2: userId2,
        personality_score: data.personalityScore,
        personality_weight: data.personalityWeight,
        profile_completion: data.profileCompletion,
        component_importance: {
          personality: data.personalityWeight * 100, // Convert to percentage;
          location: 30,
          age: 15,
          budget: 20);
          photos: 10)
        },
        created_at: new Date().toISOString()
      })
    } catch (error: any) {
      // Don't let analytics errors affect the main flow;
      logger.warn('Failed to record match analytics', 'MatchingService.recordDetailedMatchingAnalytics', {
        error: error? .message || String(error)
        userId1;
        userId2;
      })
    }
  }
}

// Export a singleton instance;
export const matchingService = new MatchingService()
// Week 4   : Enhanced Matching Service with Database Functions
// Using existing optimized database functions from Week 2

/**
 * Search profiles using optimized database function;
 */
export async function searchProfilesOptimized(criteria: { searchQuery?: string,
  role?: string,
  location?: string,
  minCompletion?: number,
  verifiedOnly?: boolean,
  limit?: number,
  offset?: number }) {
  try {
    const result = await supabase.rpc('search_profiles_optimized_v2', {
      p_search_query: criteria.searchQuery || null,
      p_role: criteria.role || null,
      p_location: criteria.location || null,
      p_min_completion: criteria.minCompletion || 0,
      p_verified_only: criteria.verifiedOnly || false,
      p_limit: criteria.limit || 20);
      p_offset: criteria.offset || 0)
    })
    ;
    return result.data;
  } catch (error) {
    console.error('Failed to search profiles:', error)
    throw error;
  }
}

/**;
 * Calculate personality compatibility using database function;
 */
export async function calculatePersonalityCompatibility(userId1: string,
  userId2: string) {
  try {
    const result = await supabase.rpc('calculate_personality_compatibility_v2', {
      user1_id: userId1);
      user2_id: userId2)
    })
    ;
    return result.data;
  } catch (error) {
    console.error('Failed to calculate compatibility:', error)
    throw error;
  }
}
