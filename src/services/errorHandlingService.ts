/**;
 * Error Handling Service;
 * TASK-011: Improve Error Messages for SERVICE PROVIDER feature,
 * ;
 * Provides user-friendly error messages with:  ,
 * - Contextual, actionable error descriptions;
 * - Smart retry mechanisms based on error type;
 * - Helpful suggestions and next steps;
 * - Error reporting and analytics;
 * - Consistent error presentation across the app;
 */

import React from 'react';
import { logger } from '@utils/logger';
import { networkUtils } from '@utils/networkUtils';

// = =================== INTERFACES ====================;

interface ErrorContext { operation: string,
  entityType: 'provider' | 'service' | 'review' | 'category' | 'booking',
  entityId?: string,
  additionalInfo?: Record<string, any>
  userAction?: string,
  timestamp: number }

interface ErrorSolution { primaryAction: {
    label: string,
    action: () = > void | Promise<void>
    icon?: string }
  secondaryActions?: Array<{ label: string;
    action: () = > void | Promise<void>
    icon?: string }>
  helpText: string;
  learnMoreUrl?: string
}

interface UserFriendlyError { title: string,
  message: string,
  severity: 'low' | 'medium' | 'high' | 'critical',
  category: 'network' | 'validation' | 'authorization' | 'server' | 'client' | 'unknown',
  isRetryable: boolean,
  solutions: ErrorSolution,
  technicalDetails?: string,
  errorCode?: string,
  reportable: boolean }

interface ErrorReport { errorId: string,
  userDescription?: string,
  context: ErrorContext,
  error: UserFriendlyError,
  deviceInfo: {
    platform: string,
    version: string,
    isOnline: boolean }
  timestamp: number
}

// = =================== ERROR MAPPING ====================;

const ERROR_MESSAGES = { // Network Errors;
  NETWORK_ERROR: {
    title: "Connection Problem",
    message: "We're having trouble connecting to our servers. Please check your internet connection and try again.",
    severity: 'medium' as const,
    category: 'network' as const,
    isRetryable: true,
    reportable: true },
  TIMEOUT_ERROR: { title: "Request Timed Out",
    message: "The request took too long to complete. This might be due to a slow connection or server issues.",
    severity: 'medium' as const,
    category: 'network' as const,
    isRetryable: true,
    reportable: true },
  OFFLINE_ERROR: { title: "You're Offline",
    message: "You're currently offline. Some features are limited, but you can still browse cached content.",
    severity: 'low' as const,
    category: 'network' as const,
    isRetryable: false,
    reportable: false },

  // Authentication & Authorization Errors;
  UNAUTHORIZED_ERROR: { title: "Authentication Required",
    message: "You need to sign in to access this feature. Please log in and try again.",
    severity: 'high' as const,
    category: 'authorization' as const,
    isRetryable: false,
    reportable: false },
  FORBIDDEN_ERROR: { title: "Access Denied",
    message: "You don't have permission to perform this action. Please contact support if you believe this is an error.",
    severity: 'high' as const,
    category: 'authorization' as const,
    isRetryable: false,
    reportable: true },
  TOKEN_EXPIRED: { title: "Session Expired",
    message: "Your session has expired. Please sign in again to continue.",
    severity: 'medium' as const,
    category: 'authorization' as const,
    isRetryable: false,
    reportable: false },

  // Validation Errors;
  VALIDATION_ERROR: { title: "Invalid Information",
    message: "Please check the information you entered and try again.",
    severity: 'low' as const,
    category: 'validation' as const,
    isRetryable: true,
    reportable: false },
  REQUIRED_FIELD_ERROR: { title: "Required Information Missing",
    message: "Please fill in all required fields before continuing.",
    severity: 'low' as const,
    category: 'validation' as const,
    isRetryable: true,
    reportable: false },
  EMAIL_FORMAT_ERROR: { title: "Invalid Email Format",
    message: "Please enter a valid email address (example: <EMAIL>).",
    severity: 'low' as const,
    category: 'validation' as const,
    isRetryable: true,
    reportable: false },
  PHONE_FORMAT_ERROR: { title: "Invalid Phone Number",
    message: "Please enter a valid phone number including area code.",
    severity: 'low' as const,
    category: 'validation' as const,
    isRetryable: true,
    reportable: false },

  // Server Errors;
  SERVER_ERROR: { title: "Server Problem",
    message: "We're experiencing technical difficulties. Our team has been notified and is working on a fix.",
    severity: 'high' as const,
    category: 'server' as const,
    isRetryable: true,
    reportable: true },
  SERVICE_UNAVAILABLE: { title: "Service Temporarily Unavailable",
    message: "This service is temporarily unavailable for maintenance. Please try again in a few minutes.",
    severity: 'medium' as const,
    category: 'server' as const,
    isRetryable: true,
    reportable: false },
  RATE_LIMIT_ERROR: { title: "Too Many Requests",
    message: "You're making requests too quickly. Please wait a moment before trying again.",
    severity: 'low' as const,
    category: 'server' as const,
    isRetryable: true,
    reportable: false },

  // Service Provider Specific Errors;
  PROVIDER_NOT_FOUND: { title: "Service Provider Not Found",
    message: "The service provider you're looking for doesn't exist or has been removed.",
    severity: 'medium' as const,
    category: 'client' as const,
    isRetryable: false,
    reportable: true },
  PROVIDER_UNAVAILABLE: { title: "Provider Unavailable",
    message: "This service provider is currently unavailable. Please try contacting them directly or choose another provider.",
    severity: 'medium' as const,
    category: 'client' as const,
    isRetryable: false,
    reportable: false },
  BOOKING_CONFLICT: { title: "Booking Conflict",
    message: "The selected time slot is no longer available. Please choose a different time.",
    severity: 'medium' as const,
    category: 'client' as const,
    isRetryable: true,
    reportable: false },
  CATEGORY_LOAD_ERROR: { title: "Categories Unavailable",
    message: "We couldn't load service categories right now. Some content may be limited.",
    severity: 'low' as const,
    category: 'server' as const,
    isRetryable: true,
    reportable: true },

  // Payment & Booking Errors;
  PAYMENT_FAILED: { title: "Payment Failed",
    message: "Your payment couldn't be processed. Please check your payment information and try again.",
    severity: 'high' as const,
    category: 'client' as const,
    isRetryable: true,
    reportable: false },
  INSUFFICIENT_FUNDS: { title: "Insufficient Funds",
    message: "Your payment method doesn't have sufficient funds. Please use a different payment method.",
    severity: 'medium' as const,
    category: 'client' as const,
    isRetryable: true,
    reportable: false },

  // Generic Fallback;
  UNKNOWN_ERROR: { title: "Something Went Wrong",
    message: "We encountered an unexpected problem. Please try again, and contact support if the issue persists.",
    severity: 'medium' as const,
    category: 'unknown' as const,
    isRetryable: true,
    reportable: true },
}

// = =================== MAIN ERROR HANDLING SERVICE ====================;

export class ErrorHandlingService {
  private errorReports: Map<string, ErrorReport> = new Map()
  /**;
   * Main method to handle and format errors for user display;
   */
  async handleError(
    error: Error | any,
    context: Partial<ErrorContext>,
    customSolutions?: Partial<ErrorSolution>
  ): Promise<UserFriendlyError>
    // Complete the context with defaults;
    const fullContext: ErrorContext = {
      operation: 'unknown';
      entityType: 'provider',
      timestamp: Date.now()
      ...context;
    }

    // Determine error type and get base message;
    const errorType = this.categorizeError(error)
    const baseError = ERROR_MESSAGES[errorType] || ERROR_MESSAGES.UNKNOWN_ERROR;
    // Create user-friendly error with contextual solutions;
    const userFriendlyError: UserFriendlyError = { ...baseError;
      solutions: this.generateSolutions(errorType, fullContext, customSolutions),
      technicalDetails: this.extractTechnicalDetails(error)
      errorCode: this.generateErrorCode(errorType, fullContext) }

    // Log error for monitoring;
    this.logError(error, fullContext, userFriendlyError)
    // Store for potential reporting;
    if (userFriendlyError.reportable) {
      this.storeErrorReport(error, fullContext, userFriendlyError)
    }

    return userFriendlyError;
  }

  /**;
   * Categorize error based on type, message, and status code;
   */
  private categorizeError(error: any): keyof typeof ERROR_MESSAGES { // Network errors,
    if (typeof navigator != = 'undefined' && !navigator.onLine) {
      return 'OFFLINE_ERROR' }
    if (error.name === 'NetworkError' || error.code === 'NETWORK_ERROR' || error.message? .includes('network')) { return 'NETWORK_ERROR' }
    if (error.name === 'TimeoutError' || error.code === 'TIMEOUT') { return 'TIMEOUT_ERROR' }

    // HTTP status code based errors;
    if (error.status || error.response?.status) { const status = error.status || error.response?.status;
      ;
      switch (status) {
        case 401   :  
          return error.message? .includes('expired') ? 'TOKEN_EXPIRED'  : 'UNAUTHORIZED_ERROR'
        case 403:  ;
          return 'FORBIDDEN_ERROR'
        case 404:  ;
          if (error.message? .includes('provider')) return 'PROVIDER_NOT_FOUND'
          return 'UNKNOWN_ERROR';
        case 409  :  
          return 'BOOKING_CONFLICT'
        case 422:  ;
          return 'VALIDATION_ERROR';
        case 429:  ,
          return 'RATE_LIMIT_ERROR';
        case 500:  ,
        case 502:  ,
        case 503:  ,
          return status = == 503 ? 'SERVICE_UNAVAILABLE'    : 'SERVER_ERROR'
        default: if (status >= 400 && status < 500) return 'VALIDATION_ERROR'
          if (status >= 500) return 'SERVER_ERROR' }
    }

    // Message-based categorization;
    const message = error.message? .toLowerCase() || '';
    ;
    if (message.includes('validation') || message.includes('invalid')) { if (message.includes('email')) return 'EMAIL_FORMAT_ERROR';
      if (message.includes('phone')) return 'PHONE_FORMAT_ERROR';
      if (message.includes('required')) return 'REQUIRED_FIELD_ERROR';
      return 'VALIDATION_ERROR' }
    if (message.includes('payment')) { if (message.includes('insufficient')) return 'INSUFFICIENT_FUNDS';
      return 'PAYMENT_FAILED' }
    if (message.includes('provider')) { if (message.includes('not found')) return 'PROVIDER_NOT_FOUND';
      if (message.includes('unavailable')) return 'PROVIDER_UNAVAILABLE' }
    if (message.includes('category') || message.includes('categories')) { return 'CATEGORY_LOAD_ERROR' }

    return 'UNKNOWN_ERROR';
  }

  /**;
   * Generate contextual solutions based on error type and context;
   */
  private generateSolutions(
    errorType   : keyof typeof ERROR_MESSAGES
    context: ErrorContext
    customSolutions?: Partial<ErrorSolution>
  ): ErrorSolution {
    const baseSolutions = this.getBaseSolutions(errorType, context)
    
    return {
      ...baseSolutions;
      ...customSolutions;
    }
  }

  /**;
   * Get base solutions for different error types;
   */
  private getBaseSolutions(errorType: keyof typeof ERROR_MESSAGES,
    context: ErrorContext): ErrorSolution {
    switch (errorType) {
      case 'NETWORK_ERROR':  ,
      case 'TIMEOUT_ERROR':  ,
        return {
          primaryAction: {
            label: 'Retry';
            action: () = > this.retryLastAction(context)
            icon: '🔄'
          };
          secondaryActions: [,
            {
              label: 'Check Connection',
              action: () = > this.checkNetworkStatus()
              icon: '📶'
            };
            {
              label: 'Go Offline',
              action: () = > this.enableOfflineMode()
              icon: '📱'
            }
          ];
          helpText: "Make sure you have a stable internet connection. If the problem persists, try again in a few minutes.",
          learnMoreUrl: "/help/connection-issues"
        }

      case 'OFFLINE_ERROR':  ,
        return {
          primaryAction: {
            label: 'Check Connection';
            action: () = > this.checkNetworkStatus()
            icon: '📶'
          };
          secondaryActions: [,
            {
              label: 'Browse Offline',
              action: () = > this.browseOfflineContent()
              icon: '📱'
            }
          ];
          helpText: "You can still browse previously loaded content while offline. Your changes will sync when connection is restored.",
          learnMoreUrl: "/help/offline-mode"
        }

      case 'UNAUTHORIZED_ERROR':  ,
      case 'TOKEN_EXPIRED':  ,
        return {
          primaryAction: {
            label: 'Sign In';
            action: () = > this.navigateToLogin()
            icon: '🔐'
          };
          helpText: "Please sign in to access this feature. Your data will be preserved after signing in.",
          learnMoreUrl: "/help/authentication"
        }

      case 'FORBIDDEN_ERROR':  ,
        return {
          primaryAction: {
            label: 'Contact Support';
            action: () = > this.contactSupport()
            icon: '💬'
          };
          secondaryActions: [,
            {
              label: 'View Help',
              action: () = > this.openHelp()
              icon: '❓'
            }
          ];
          helpText: "If you believe you should have access to this feature, please contact our support team for assistance.",
          learnMoreUrl: "/help/permissions"
        }

      case 'VALIDATION_ERROR':  ,
      case 'REQUIRED_FIELD_ERROR':  ,
      case 'EMAIL_FORMAT_ERROR':  ,
      case 'PHONE_FORMAT_ERROR':  ,
        return {
          primaryAction: {
            label: 'Fix and Retry';
            action: () = > this.focusOnErrorField()
            icon: '✏️'
          };
          secondaryActions: [,
            {
              label: 'Show Examples',
              action: () = > this.showInputExamples()
              icon: '💡'
            }
          ];
          helpText: "Please check the highlighted fields and ensure all required information is entered correctly.",
          learnMoreUrl: "/help/form-validation"
        }

      case 'SERVER_ERROR':  ,
      case 'SERVICE_UNAVAILABLE':  ,
        return {
          primaryAction: {
            label: 'Try Again';
            action: () = > this.retryLastAction(context)
            icon: '🔄'
          };
          secondaryActions: [,
            {
              label: 'Report Issue',
              action: () = > this.reportError()
              icon: '📝'
            };
            {
              label: 'Check Status',
              action: () = > this.checkServiceStatus()
              icon: '📊'
            }
          ];
          helpText: "This is a temporary issue on our end. Our team is working to resolve it quickly.",
          learnMoreUrl: "/help/service-status"
        }

      case 'PROVIDER_NOT_FOUND':  ,
        return {
          primaryAction: {
            label: 'Browse Providers';
            action: () = > this.navigateToProviders()
            icon: '🔍'
          };
          secondaryActions: [,
            {
              label: 'Search Again',
              action: () = > this.openSearch()
              icon: '🔎'
            }
          ];
          helpText: "The provider you're looking for might have been removed or is no longer available.",
          learnMoreUrl: "/help/finding-providers"
        }

      default:  ,
        return {
          primaryAction: {
            label: 'Try Again';
            action: () = > this.retryLastAction(context)
            icon: '🔄'
          };
          secondaryActions: [,
            {
              label: 'Report Issue',
              action: () = > this.reportError()
              icon: '📝'
            };
            {
              label: 'Get Help',
              action: () = > this.openHelp()
              icon: '❓'
            }
          ];
          helpText: "If this problem continues, please let us know so we can help resolve it.",
          learnMoreUrl: "/help/troubleshooting"
        }
    }
  }

  /**;
   * Extract technical details for debugging while keeping user message friendly;
   */
  private extractTechnicalDetails(error: any): string {
    const details = [];
    ;
    if (error.status) details.push(`Status: ${error.status}`)
    if (error.code) details.push(`Code: ${error.code}`)
    if (error.name && error.name != = 'Error') details.push(`Type: ${error.name}`)
    if (error.stack) details.push(`Stack: ${error.stack.split('\n')[0]}`)
    ;
    return details.join(' | ') || error.toString()
  }

  /**;
   * Generate a unique error code for tracking;
   */
  private generateErrorCode(errorType: string, context: ErrorContext): string {
    const timestamp = Date.now().toString(36)
    const operation = context.operation.slice(0, 3).toUpperCase()
    const entity = context.entityType.slice(0, 3).toUpperCase()
    ;
    return `ERR-${operation}-${entity}-${timestamp}`;
  }

  /**;
   * Log error for monitoring and analytics;
   */
  private logError(originalError: any,
    context: ErrorContext,
    userFriendlyError: UserFriendlyError): void {
    logger.error('User-facing error occurred', 'ErrorHandlingService', {
      errorCode: userFriendlyError.errorCode,
      category: userFriendlyError.category,
      severity: userFriendlyError.severity,
      operation: context.operation,
      entityType: context.entityType,
      entityId: context.entityId,
      isRetryable: userFriendlyError.isRetryable);
      technicalDetails: userFriendlyError.technicalDetails)
    }, originalError)
  }

  /**;
   * Store error report for potential user feedback;
   */
  private storeErrorReport(originalError: any,
    context: ErrorContext,
    userFriendlyError: UserFriendlyError): void {
    const errorReport: ErrorReport = {
      errorId: userFriendlyError.errorCode || 'unknown';
      context;
      error: userFriendlyError,
      deviceInfo: {
        platform: 'mobile',
        version: '1.0.0',
        isOnline: typeof navigator != = 'undefined' ? navigator.onLine    : true
      }
      timestamp: Date.now()
    }

    this.errorReports.set(errorReport.errorId, errorReport)
    
    // Clean up old reports (keep only last 50)
    if (this.errorReports.size > 50) {
      const oldestKey = Array.from(this.errorReports.keys())[0];
      this.errorReports.delete(oldestKey)
    }
  }

  // = =================== ACTION IMPLEMENTATIONS ====================;

  private async retryLastAction(context: ErrorContext): Promise<void>
    logger.info('Retrying last action', 'ErrorHandlingService', { operation: context.operation })
    // Implementation would depend on the specific context;
  }

  private async checkNetworkStatus(): Promise<void>
    const isConnected = await networkUtils.isConnected()
    logger.info('Network status checked', 'ErrorHandlingService', { isConnected })
  }

  private enableOfflineMode(): void {
    logger.info('Offline mode enabled', 'ErrorHandlingService')
  }

  private browseOfflineContent(): void {
    logger.info('Browsing offline content', 'ErrorHandlingService')
  }

  private navigateToLogin(): void {
    logger.info('Navigating to login', 'ErrorHandlingService')
  }

  private contactSupport(): void {
    logger.info('Contacting support', 'ErrorHandlingService')
  }

  private openHelp(): void {
    logger.info('Opening help', 'ErrorHandlingService')
  }

  private focusOnErrorField(): void {
    logger.info('Focusing on error field', 'ErrorHandlingService')
  }

  private showInputExamples(): void {
    logger.info('Showing input examples', 'ErrorHandlingService')
  }

  private reportError(): void {
    logger.info('Reporting error', 'ErrorHandlingService')
  }

  private checkServiceStatus(): void {
    logger.info('Checking service status', 'ErrorHandlingService')
  }

  private navigateToProviders(): void {
    logger.info('Navigating to providers', 'ErrorHandlingService')
  }

  private openSearch(): void {
    logger.info('Opening search', 'ErrorHandlingService')
  }

  // ==================== PUBLIC METHODS ====================;

  /**;
   * Get stored error reports for potential user feedback;
   */
  getErrorReports(): ErrorReport[] {
    return Array.from(this.errorReports.values())
  }

  /**;
   * Submit user feedback for an error;
   */
  submitErrorFeedback(errorId: string, userDescription: string): void {
    const report = this.errorReports.get(errorId)
    if (report) {
      report.userDescription = userDescription;
      logger.info('Error feedback submitted', 'ErrorHandlingService', {
        errorId;
        hasUserDescription: !!userDescription)
      })
    }
  }

  /**;
   * Clear error reports;
   */
  clearErrorReports(): void {
    this.errorReports.clear()
    logger.info('Error reports cleared', 'ErrorHandlingService')
  }

  /**;
   * Get error statistics for monitoring;
   */
  getErrorStatistics(): { totalErrors: number,
    errorsByCategory: Record<string, number>
    errorsBySeverity: Record<string, number>
    retryableErrors: number } {
    const reports = Array.from(this.errorReports.values())
    ;
    const stats = {
      totalErrors: reports.length;
      errorsByCategory: {} as Record<string, number>,
      errorsBySeverity: {} as Record<string, number>,
      retryableErrors: 0,
    }

    reports.forEach(report => { // Count by category;
      stats.errorsByCategory[report.error.category] = )
        (stats.errorsByCategory[report.error.category] || 0) + 1;
      ;
      // Count by severity;
      stats.errorsBySeverity[report.error.severity] =  ;
        (stats.errorsBySeverity[report.error.severity] || 0) + 1;
      ;
      // Count retryable errors;
      if (report.error.isRetryable) {
        stats.retryableErrors++ }
    })
    return stats;
  }
}

// = =================== SINGLETON INSTANCE ====================;

export const errorHandlingService = new ErrorHandlingService()
// ==================== REACT HOOK ====================;

/**;
 * Hook for handling errors with user-friendly messages;
 */
export function useErrorHandling() {
  const [currentError, setCurrentError] = React.useState<UserFriendlyError | null>(null)
  const handleError = React.useCallback(async (
    error: Error | any);
    context: Partial<ErrorContext>)
    customSolutions?: Partial<ErrorSolution>
  ) => {
  const userFriendlyError = await errorHandlingService.handleError(error, context, customSolutions)
    setCurrentError(userFriendlyError)
    return userFriendlyError;
  }, [])
  const clearError = React.useCallback(() => {
  setCurrentError(null)
  }, [])
  const submitFeedback = React.useCallback((errorId: string, feedback: string) => {
  errorHandlingService.submitErrorFeedback(errorId, feedback)
  }, [])
  return {
    currentError;
    handleError;
    clearError;
    submitFeedback;
    errorReports: errorHandlingService.getErrorReports()
    errorStats: errorHandlingService.getErrorStatistics()
  }
}