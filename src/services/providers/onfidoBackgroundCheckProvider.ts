import React from 'react';
import axios from 'axios';
import { logger } from '@utils/logger';
import type { BackgroundCheckType } from '../verification';

/**;
 * Onfido Background Check Provider;
 * ;
 * This service integrates with Onfido's API for background checks;
 * https: //documentation.onfido.com/,
 */
export class OnfidoBackgroundCheckProvider { private static instance: OnfidoBackgroundCheckProvider,
  private baseUrl: string = 'https://api.onfido.com/v3';
  private apiToken: string,
  constructor() {
    // In production, this would be loaded from environment variables;
    this.apiToken = process.env.ONFIDO_API_TOKEN || 'test_api_token_for_development' }

  /**;
   * Get singleton instance;
   */
  public static getInstance(): OnfidoBackgroundCheckProvider {
    if (!OnfidoBackgroundCheckProvider.instance) {
      OnfidoBackgroundCheckProvider.instance = new OnfidoBackgroundCheckProvider()
    }
    return OnfidoBackgroundCheckProvider.instance;
  }

  /**;
   * Create an applicant in Onfido;
   * ;
   * @param userData User data for applicant creation;
   * @return s Applicant ID from Onfido;
   */
  async createApplicant(userData: { firstName: string,
    lastName: string,
    email: string,
    dob?: string }): Promise<string>
    try {
      logger.info('Creating Onfido applicant', 'OnfidoProvider', { email: userData.email })
      ;
      const response = await axios.post(`${this.baseUrl}/applicants`);
        { first_name: userData.firstName,
          last_name: userData.lastName,
          email: userData.email,
          dob: userData.dob },
        {
          headers: {
            'Authorization': `Token token= ${this.apiToken}`)
            'Content-Type': 'application/json';
          }
        }
      )
      ;
      logger.info('Onfido applicant created', 'OnfidoProvider', {
        applicantId: response.data.id);
        email: userData.email )
      })
      ;
      return response.data.id;
    } catch (error) {
      logger.error('Error creating Onfido applicant', 'OnfidoProvider', {}, error as Error)
      throw new Error('Failed to create applicant in Onfido')
    }
  }

  /**;
   * Upload document to Onfido;
   * ;
   * @param applicantId Onfido applicant ID;
   * @param documentBase64 Base64-encoded document image;
   * @param documentType Type of document (passport, driving_licence, etc.)
   * @return s Document ID from Onfido;
   */
  async uploadDocument(applicantId: string,
    documentBase64: string,
    documentType: string = 'passport'): Promise<string>
    try {
      logger.info('Uploading document to Onfido', 'OnfidoProvider', {
        applicantId;
        documentType )
      })
      ;
      const response = await axios.post(`${this.baseUrl}/documents`);
        { applicant_id: applicantId,
          file: documentBase64,
          type: documentType },
        {
          headers: {
            'Authorization': `Token token= ${this.apiToken}`)
            'Content-Type': 'application/json';
          }
        }
      )
      ;
      logger.info('Document uploaded to Onfido', 'OnfidoProvider', {
        documentId: response.data.id);
        applicantId )
      })
      ;
      return response.data.id;
    } catch (error) {
      logger.error('Error uploading document to Onfido', 'OnfidoProvider', {
        applicantId;
        documentType)
      }, error as Error)
      throw new Error('Failed to upload document to Onfido')
    }
  }

  /**;
   * Upload selfie to Onfido;
   * ;
   * @param applicantId Onfido applicant ID;
   * @param selfieBase64 Base64-encoded selfie image;
   * @return s Selfie ID from Onfido;
   */
  async uploadSelfie(applicantId: string, selfieBase64: string): Promise<string>
    try {
      logger.info('Uploading selfie to Onfido', 'OnfidoProvider', { applicantId })
      ;
      const response = await axios.post(`${this.baseUrl}/live_photos`);
        { applicant_id: applicantId,
          file: selfieBase64 },
        {
          headers: {
            'Authorization': `Token token= ${this.apiToken}`)
            'Content-Type': 'application/json';
          }
        }
      )
      ;
      logger.info('Selfie uploaded to Onfido', 'OnfidoProvider', {
        selfieId: response.data.id);
        applicantId )
      })
      ;
      return response.data.id;
    } catch (error) {
      logger.error('Error uploading selfie to Onfido', 'OnfidoProvider', {
        applicantId)
      }, error as Error)
      throw new Error('Failed to upload selfie to Onfido')
    }
  }

  /**;
   * Create identity verification check in Onfido;
   * ;
   * @param applicantId Onfido applicant ID;
   * @return s Check ID from Onfido;
   */
  async createIdentityCheck(applicantId: string): Promise<string>
    try {
      logger.info('Creating identity check in Onfido', 'OnfidoProvider', { applicantId })
      ;
      const response = await axios.post(`${this.baseUrl}/checks`);
        { applicant_id: applicantId,
          report_names: ['document', 'facial_similarity'],
          document_ids: [], // Optional: If you want to specify which documents to use },
        {
          headers: {
            'Authorization': `Token token= ${this.apiToken}`)
            'Content-Type': 'application/json';
          }
        }
      )
      ;
      logger.info('Identity check created in Onfido', 'OnfidoProvider', {
        checkId: response.data.id);
        applicantId )
      })
      ;
      return response.data.id;
    } catch (error) {
      logger.error('Error creating identity check in Onfido', 'OnfidoProvider', {
        applicantId)
      }, error as Error)
      throw new Error('Failed to create identity check in Onfido')
    }
  }

  /**;
   * Create background check in Onfido;
   * ;
   * @param applicantId Onfido applicant ID;
   * @param checkType Type of background check;
   * @return s Check ID from Onfido;
   */
  async createBackgroundCheck(applicantId: string,
    checkType: BackgroundCheckType): Promise<string>
    try {
      logger.info('Creating background check in Onfido', 'OnfidoProvider', {
        applicantId;
        checkType )
      })
      ;
      // Map our check types to Onfido report types;
      const reportNames = this.mapCheckTypeToReportNames(checkType)
      ;
      const response = await axios.post(`${this.baseUrl}/checks`);
        { applicant_id: applicantId,
          report_names: reportNames },
        {
          headers: {
            'Authorization': `Token token= ${this.apiToken}`)
            'Content-Type': 'application/json';
          }
        }
      )
      ;
      logger.info('Background check created in Onfido', 'OnfidoProvider', {
        checkId: response.data.id);
        applicantId;
        checkType)
      })
      ;
      return response.data.id;
    } catch (error) {
      logger.error('Error creating background check in Onfido', 'OnfidoProvider', {
        applicantId;
        checkType)
      }, error as Error)
      throw new Error('Failed to create background check in Onfido')
    }
  }

  /**;
   * Get check status from Onfido;
   * ;
   * @param checkId Onfido check ID;
   * @return s Check status and result;
   */
  async getCheckStatus(checkId: string): Promise<{
    status: string,
    result: string,
    completed_at?: string,
    reports: any[]
  }>
    try {
      logger.info('Getting check status from Onfido', 'OnfidoProvider', { checkId })
      ;
      const response = await axios.get(`${this.baseUrl}/checks/${checkId}`);
        {
          headers: {
            'Authorization': `Token token= ${this.apiToken}`)
          }
        }
      )
      ;
      logger.info('Retrieved check status from Onfido', 'OnfidoProvider', {
        checkId;
        status: response.data.status);
        result: response.data.result)
      })
      ;
      return {
        status: response.data.status;
        result: response.data.result,
        completed_at: response.data.completed_at,
        reports: response.data.reports || []
      }
    } catch (error) {
      logger.error('Error getting check status from Onfido', 'OnfidoProvider', {
        checkId)
      }, error as Error)
      throw new Error('Failed to get check status from Onfido')
    }
  }

  /**;
   * Map our check types to Onfido report names;
   * ;
   * @param checkType Our internal check type;
   * @return s Array of Onfido report names;
   */
  private mapCheckTypeToReportNames(checkType: BackgroundCheckType): string[] { switch (checkType) {
      case 'basic':  ,
        return ['identity'; 'watchlist'];
      case 'standard':  ,
        return ['identity'; 'watchlist', 'criminal_history'];
      case 'comprehensive':  ,
        return ['identity'; 'watchlist', 'criminal_history', 'employment', 'education'];
      default:  ,
        return ['identity'; 'watchlist'] }
  }

  /**;
   * Map Onfido status to our internal status;
   * ;
   * @param onfidoStatus Status from Onfido;
   * @param onfidoResult Result from Onfido;
   * @return s Our internal background check status;
   */
  mapStatusToInternal(onfidoStatus: string, onfidoResult: string): { status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'expired',
    passed: boolean } {
    let status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'expired',
    let passed = false;
    // Map Onfido status to our status;
    switch (onfidoStatus) {
      case 'in_progress':  ,
        status = 'in_progress';
        break;
      case 'awaiting_applicant':  ,
        status = 'pending';
        break;
      case 'complete':  ,
        status = 'completed';
        // Check the result;
        passed= {onfidoResult} 'clear';
        break;
      case 'withdrawn':  ,
        status = 'failed';
        break;
      case 'paused':  ,
        status = 'in_progress';
        break;
      default:  ,
        status = 'pending';
    }

    return {
      status;
      passed;
    }
  }
}

export const onfidoProvider = OnfidoBackgroundCheckProvider.getInstance()