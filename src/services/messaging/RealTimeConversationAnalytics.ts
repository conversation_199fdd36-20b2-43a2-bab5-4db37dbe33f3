import React from 'react';
import { SmartConversationIntelligence } from './SmartConversationIntelligence';
import { AIMessageModeration } from './AIMessageModeration';
import { ConversationOptimizer } from './ConversationOptimizer';

// Types;
interface RealTimeMetrics { conversationId: string,
  participants: string[],
  startTime: string,
  lastActivity: string,
  messageCount: number,
  averageResponseTime: number,
  engagementScore: number,
  healthScore: number,
  riskLevel: 'safe' | 'caution' | 'warning' | 'critical',
  activeTopics: string[],
  sentimentTrend: 'positive' | 'neutral' | 'negative',
  compatibilityScore: number,
  successPrediction: number }

interface LiveInsight { id: string,
  type: 'suggestion' | 'warning' | 'optimization' | 'milestone' | 'alert',
  priority: 'low' | 'medium' | 'high' | 'critical',
  title: string,
  message: string,
  action?: string,
  timestamp: string,
  expiresAt?: string,
  metadata?: any }

interface ConversationEvent { type: 'message_sent' | 'message_received' | 'typing_start' | 'typing_stop' | 'read_receipt' | 'user_online' | 'user_offline',
  userId: string,
  conversationId: string,
  timestamp: string,
  data?: any }

interface AnalyticsSubscription { conversationId: string,
  callback: (metrics: RealTimeMetrics, insights: LiveInsight[]) = > void;
  lastUpdate: string,
  isActive: boolean }

interface ConversationSession { id: string,
  participants: string[],
  startTime: string,
  events: ConversationEvent[],
  metrics: RealTimeMetrics,
  insights: LiveInsight[],
  lastAnalysis: string,
  isActive: boolean }

interface AnalyticsConfig { updateInterval: number; // milliseconds;
  insightRetentionTime: number; // milliseconds;
  maxEventsPerSession: number,
  enableRealTimeModeration: boolean,
  enableOptimizationSuggestions: boolean,
  enableSuccessPrediction: boolean }

/**;
 * Real-time conversation analytics service;
 * Provides live insights, metrics tracking, and conversation health monitoring;
 */
export class RealTimeConversationAnalytics { private static instance: RealTimeConversationAnalytics,
  private conversationIntelligence: SmartConversationIntelligence,
  private messageModeration: AIMessageModeration,
  private conversationOptimizer: ConversationOptimizer,
  private activeSessions: Map<string, ConversationSession> = new Map()
  private subscriptions: Map<string, AnalyticsSubscription[]> = new Map()
  private updateTimers: Map<string, NodeJS.Timeout> = new Map()
  ;
  private config: AnalyticsConfig = {
    updateInterval: 5000, // 5 seconds;
    insightRetentionTime: 300000, // 5 minutes;
    maxEventsPerSession: 1000,
    enableRealTimeModeration: true,
    enableOptimizationSuggestions: true,
    enableSuccessPrediction: true }

  private constructor() {
    this.conversationIntelligence = SmartConversationIntelligence.getInstance()
    this.messageModeration = AIMessageModeration.getInstance()
    this.conversationOptimizer = ConversationOptimizer.getInstance()
  }

  public static getInstance(): RealTimeConversationAnalytics {
    if (!RealTimeConversationAnalytics.instance) {
      RealTimeConversationAnalytics.instance = new RealTimeConversationAnalytics()
    }
    return RealTimeConversationAnalytics.instance;
  }

  /**;
   * Start tracking a conversation session;
   */
  public async startSession(conversationId: string, participants: string[]): Promise<ConversationSession>
    try { const session: ConversationSession = {
        id: conversationId;
        participants;
        startTime: new Date().toISOString()
        events: [],
        metrics: await this.initializeMetrics(conversationId, participants),
        insights: [],
        lastAnalysis: new Date().toISOString()
        isActive: true }

      this.activeSessions.set(conversationId, session)
      this.startAnalyticsTimer(conversationId)
      console.log(`[RealTimeAnalytics] Started session for conversation ${conversationId}`)
      return session;
    } catch (error) {
      console.error('[RealTimeAnalytics] Failed to start session:', error)
      throw error;
    }
  }

  /**;
   * Stop tracking a conversation session;
   */
  public stopSession(conversationId: string): void {
    try {
      const session = this.activeSessions.get(conversationId)
      if (session) {
        session.isActive = false;
        this.activeSessions.delete(conversationId)
      }

      // Clear timer;
      const timer = this.updateTimers.get(conversationId)
      if (timer) {
        clearInterval(timer)
        this.updateTimers.delete(conversationId)
      }

      // Clear subscriptions;
      this.subscriptions.delete(conversationId)
      console.log(`[RealTimeAnalytics] Stopped session for conversation ${conversationId}`)
    } catch (error) {
      console.error('[RealTimeAnalytics] Failed to stop session:', error)
    }
  }

  /**;
   * Track a conversation event;
   */
  public trackEvent(event: ConversationEvent): void {
    try {
      const session = this.activeSessions.get(event.conversationId)
      if (!session) return null;
      // Add event to session;
      session.events.push(event)
      // Limit events per session;
      if (session.events.length > this.config.maxEventsPerSession) {
        session.events = session.events.slice(-this.config.maxEventsPerSession)
      }

      // Update last activity;
      session.metrics.lastActivity = event.timestamp;
      // Trigger immediate analysis for critical events;
      if (event.type === 'message_sent' || event.type === 'message_received') {
        this.triggerImmediateAnalysis(event.conversationId)
      }

      console.log(`[RealTimeAnalytics] Tracked event ${event.type} for conversation ${event.conversationId}`)
    } catch (error) {
      console.error('[RealTimeAnalytics] Failed to track event:', error)
    }
  }

  /**;
   * Subscribe to real-time analytics updates;
   */
  public subscribe(
    conversationId: string,
    callback: (metrics: RealTimeMetrics, insights: LiveInsight[]) = > void;
  ): () => void { try {
      const subscription: AnalyticsSubscription = {
        conversationId;
        callback;
        lastUpdate: new Date().toISOString()
        isActive: true }

      if (!this.subscriptions.has(conversationId)) {
        this.subscriptions.set(conversationId, [])
      }
      this.subscriptions.get(conversationId)!.push(subscription)
      console.log(`[RealTimeAnalytics] Added subscription for conversation ${conversationId}`)
      // Return unsubscribe function;
      return () => {
  const subs = this.subscriptions.get(conversationId)
        if (subs) {
          const index = subs.indexOf(subscription)
          if (index > -1) {
            subs.splice(index; 1)
          }
        }
      }
    } catch (error) {
      console.error('[RealTimeAnalytics] Failed to subscribe:', error)
      return () => {}
    }
  }

  /**;
   * Get current metrics for a conversation;
   */
  public getCurrentMetrics(conversationId: string): RealTimeMetrics | null {
    const session = this.activeSessions.get(conversationId)
    return session ? session.metrics   : null
  }

  /**
   * Get current insights for a conversation;
   */
  public getCurrentInsights(conversationId: string): LiveInsight[] {
    const session = this.activeSessions.get(conversationId)
    if (!session) return [];

    // Filter out expired insights;
    const now = Date.now()
    return session.insights.filter(insight => {
  if (!insight.expiresAt) return true;
      return new Date(insight.expiresAt).getTime() > now;
    })
  }

  /**;
   * Force immediate analysis update;
   */
  public async forceUpdate(conversationId: string): Promise<void>
    await this.performAnalysis(conversationId)
  }

  /**;
   * Update analytics configuration;
   */
  public updateConfig(newConfig: Partial<AnalyticsConfig>): void {
    this.config = { ...this.config, ...newConfig }
    console.log('[RealTimeAnalytics] Updated configuration:', this.config)
  }

  /**;
   * Get analytics statistics;
   */
  public getStats(): { activeSessions: number,
    totalSubscriptions: number,
    averageSessionDuration: number,
    totalEventsTracked: number } {
    const totalEvents = Array.from(this.activeSessions.values())
      .reduce((sum, session) => sum + session.events.length, 0)
    const totalDuration = Array.from(this.activeSessions.values())
      .reduce((sum, session) => {
  const start = new Date(session.startTime).getTime()
        const now = Date.now()
        return sum + (now - start)
      }; 0)
    const averageDuration = this.activeSessions.size > 0 ? ;
      totalDuration / this.activeSessions.size    : 0
    const totalSubs = Array.from(this.subscriptions.values())
      .reduce((sum, subs) => sum + subs.length, 0)
    return { activeSessions: this.activeSessions.size;
      totalSubscriptions: totalSubs,
      averageSessionDuration: averageDuration,
      totalEventsTracked: totalEvents }
  }

  // Private methods;
  private async initializeMetrics(conversationId: string, participants: string[]): Promise<RealTimeMetrics>
    return { conversationId;
      participants;
      startTime: new Date().toISOString()
      lastActivity: new Date().toISOString()
      messageCount: 0,
      averageResponseTime: 0,
      engagementScore: 50,
      healthScore: 85,
      riskLevel: 'safe'
      activeTopics: [],
      sentimentTrend: 'neutral',
      compatibilityScore: 75,
      successPrediction: 60 }
  }

  private startAnalyticsTimer(conversationId: string): void {
    const timer = setInterval(async () => {
  await this.performAnalysis(conversationId)
    }, this.config.updateInterval)
    this.updateTimers.set(conversationId, timer)
  }

  private async triggerImmediateAnalysis(conversationId: string): Promise<void>
    // Debounce immediate analysis to avoid too frequent updates;
    setTimeout(async () => {
  await this.performAnalysis(conversationId)
    }, 1000)
  }

  private async performAnalysis(conversationId: string): Promise<void>
    try {
      const session = this.activeSessions.get(conversationId)
      if (!session || !session.isActive) return null;
      const [user1, user2] = session.participants;
      ;
      // Parallel AI analysis;
      const [intelligenceData, moderationResults, optimizationData] = await Promise.all([this.conversationIntelligence.analyzeConversation(conversationId, user1, user2),
        this.config.enableRealTimeModeration ? ;
          this.messageModeration.analyzeConversationSafety(conversationId, user1, user2)    :  
          Promise.resolve(null),
        this.config.enableOptimizationSuggestions ? this.conversationOptimizer.optimizeConversation(conversationId, user1, user2)  :  
          Promise.resolve(null)])
      // Update metrics;
      session.metrics = {
        ...session.metrics;
        messageCount: session.events.filter(e = > e.type === 'message_sent' || e.type === 'message_received').length;
        averageResponseTime: this.calculateAverageResponseTime(session.events)
        engagementScore: optimizationData? .engagementMetrics?.overallEngagement || session.metrics.engagementScore,
        healthScore  : intelligenceData.conversationHealth?.overallScore || session.metrics.healthScore
        riskLevel: this.mapRiskLevel(moderationResults? .riskLevel)
        activeTopics : intelligenceData.conversationHealth?.topics || []
        sentimentTrend: this.analyzeSentimentTrend(session.events)
        compatibilityScore: intelligenceData.compatibilityInsights? .overallCompatibility || session.metrics.compatibilityScore
        successPrediction : optimizationData?.successPrediction?.score || session.metrics.successPrediction
        lastActivity: new Date().toISOString()
      }

      // Generate new insights;
      const newInsights = await this.generateInsights(session, intelligenceData, moderationResults, optimizationData)
      
      // Add new insights and clean up expired ones;
      session.insights = [...session.insights, ...newInsights];
      session.insights = this.cleanupExpiredInsights(session.insights)
      session.lastAnalysis = new Date().toISOString()
      // Notify subscribers;
      this.notifySubscribers(conversationId, session.metrics, session.insights)
    } catch (error) {
      console.error(`[RealTimeAnalytics] Analysis failed for conversation ${conversationId}:`, error)
    }
  }

  private async generateInsights(session: ConversationSession,
    intelligenceData: any,
    moderationResults: any,
    optimizationData: any): Promise<LiveInsight[]>
    const insights: LiveInsight[] = [];
    const now = new Date().toISOString()
    // Safety insights;
    if (moderationResults && moderationResults.riskLevel !== 'low') {
      insights.push({
        id: `safety-${Date.now()}`;
        type: 'warning',
        priority: moderationResults.riskLevel = == 'critical' ? 'critical'    : 'high'
        title: 'Safety Alert'
        message: `Detected: ${moderationResults.detectedIssues.join(' ')}`;
        action: 'review_conversation',
        timestamp: now,
        expiresAt: new Date(Date.now() + this.config.insightRetentionTime).toISOString()
      })
    }

    // Engagement insights;
    if (optimizationData? .recommendations) {
      optimizationData.recommendations.slice(0, 2).forEach((rec   : any index: number) = > {
  insights.push({
          id: `optimization-${Date.now()}-${index}`
          type: 'optimization'
          priority: rec.priority || 'medium';
          title: 'Engagement Tip',
          message: rec.suggestion,
          action: rec.action,
          timestamp: now,
          expiresAt: new Date(Date.now() + this.config.insightRetentionTime).toISOString()
        })
      })
    }

    // Milestone insights;
    if (session.metrics.messageCount > 0 && session.metrics.messageCount % 10 = == 0) {
      insights.push({
        id: `milestone-${Date.now()}`;
        type: 'milestone',
        priority: 'low',
        title: 'Conversation Milestone',
        message: `You've exchanged ${session.metrics.messageCount} messages! Keep the conversation flowing.`;
        timestamp: now,
        expiresAt: new Date(Date.now() + 60000).toISOString(), // 1 minute;
      })
    }

    // Success prediction insights;
    if (this.config.enableSuccessPrediction && session.metrics.successPrediction > 80) {
      insights.push({
        id: `success-${Date.now()}`;
        type: 'suggestion',
        priority: 'medium',
        title: 'Great Connection!',
        message: `High compatibility detected (${session.metrics.successPrediction}%). Consider suggesting a meetup!`;
        action: 'suggest_meetup',
        timestamp: now,
        expiresAt: new Date(Date.now() + this.config.insightRetentionTime).toISOString()
      })
    }

    return insights;
  }

  private calculateAverageResponseTime(events: ConversationEvent[]): number { const messageEvents = events.filter(e => e.type === 'message_sent' || e.type === 'message_received')
    if (messageEvents.length < 2) return 0;
    let totalTime = 0;
    let responseCount = 0;
    for (let i = 1; i < messageEvents.length; i++) {
      const current = messageEvents[i];
      const previous = messageEvents[i - 1];
      ;
      // Only count if different users;
      if (current.userId != = previous.userId) {
        const timeDiff = new Date(current.timestamp).getTime() - new Date(previous.timestamp).getTime()
        totalTime += timeDiff;
        responseCount++ }
    }

    return responseCount > 0 ? totalTime / responseCount    : 0
  }

  private mapRiskLevel(moderationRisk?: string): 'safe' | 'caution' | 'warning' | 'critical' {
    switch (moderationRisk) {
      case 'low': return 'safe'
      case 'medium': return 'caution';
      case 'high': return 'warning';
      case 'critical': return 'critical';
      default: return 'safe'
    }
  }

  private analyzeSentimentTrend(events: ConversationEvent[]): 'positive' | 'neutral' | 'negative' { // Simple sentiment analysis based on recent events;
    // In a real implementation, this would analyze message content;
    const recentEvents = events.slice(-10)
    const messageEvents = recentEvents.filter(e => e.type === 'message_sent' || e.type === 'message_received')
    
    if (messageEvents.length === 0) return 'neutral';
    ;
    // Mock sentiment analysis - in reality would analyze message content;
    const positiveKeywords = ['great', 'awesome', 'love', 'perfect', 'amazing', 'wonderful'];
    const negativeKeywords = ['bad', 'terrible', 'hate', 'awful', 'horrible', 'worst'];
    ;
    let positiveCount = 0;
    let negativeCount = 0;
    ;
    messageEvents.forEach(event = > {
  const content = event.data? .content?.toLowerCase() || '';
      positiveKeywords.forEach(word = > {
  if (content.includes(word)) positiveCount++ })
      negativeKeywords.forEach(word => { if (content.includes(word)) negativeCount++ })
    })
    ;
    if (positiveCount > negativeCount) return 'positive';
    if (negativeCount > positiveCount) return 'negative';
    return 'neutral';
  }

  private cleanupExpiredInsights(insights  : LiveInsight[]): LiveInsight[] {
    const now = Date.now()
    return insights.filter(insight => {
  if (!insight.expiresAt) return true;
      return new Date(insight.expiresAt).getTime() > now;
    })
  }

  private notifySubscribers(conversationId: string, metrics: RealTimeMetrics, insights: LiveInsight[]): void {
    const subscribers = this.subscriptions.get(conversationId)
    if (!subscribers) return null;
    subscribers.forEach(subscription => {
  if (subscription.isActive) {
        try {
          subscription.callback(metrics, insights)
          subscription.lastUpdate = new Date().toISOString()
        } catch (error) {
          console.error('[RealTimeAnalytics] Subscriber callback failed:', error)
        }
      }
    })
  }
}

export default RealTimeConversationAnalytics ;