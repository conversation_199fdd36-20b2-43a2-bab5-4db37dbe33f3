import React from 'react';
/**;
 * AI Message Moderation Service;
 * ;
 * Advanced AI-powered content moderation, safety analysis, and;
 * automated intervention for secure messaging experience.;
 */

import { supabase } from '@utils/supabaseUtils';
import { logger } from '@services/loggerService';
import { cacheService } from '@services/cacheService';
import { CacheCategory } from '@core/types/cacheTypes';
import { ApiResponse } from '@core/types/apiTypes';

// = =================== TYPES & INTERFACES ====================;

export interface ModerationResult {
  messageId: string,
  isAllowed: boolean,
  riskLevel: 'low' | 'medium' | 'high' | 'critical',
  moderationScore: number; // 0-100 (higher = more concerning);
  detectedIssues: DetectedIssue[],
  recommendations: ModerationRecommendation[],
  autoActions: AutoModerationAction[],
  contextAnalysis: ContextAnalysis,
  sentimentAnalysis: SentimentAnalysis,
  safetyFlags: SafetyFlag[],
  confidence: number; // 0-1;
}

export interface DetectedIssue { type: IssueType,
  severity: 'low' | 'medium' | 'high' | 'critical',
  description: string,
  evidence: string[],
  confidence: number,
  category: IssueCategory,
  suggestedAction: string }

export interface ModerationRecommendation {
  action: 'allow' | 'flag' | 'block' | 'review' | 'warn_user',
  reasoning: string,
  urgency: 'low' | 'medium' | 'high' | 'immediate',
  followUpRequired: boolean,
  escalationLevel: number; // 1-5;
}

export interface AutoModerationAction { action: string,
  executed: boolean,
  timestamp: Date,
  reasoning: string,
  reversible: boolean }

export interface ContextAnalysis {
  conversationHistory: ConversationContext,
  userBehaviorPattern: UserBehaviorPattern,
  relationshipStage: RelationshipStage,
  riskFactors: string[],
  protectiveFactors: string[]
}

export interface SentimentAnalysis {
  overallSentiment: 'positive' | 'neutral' | 'negative' | 'concerning',
  emotionalIntensity: number; // 0-100;
  aggressionLevel: number; // 0-100;
  manipulationIndicators: number; // 0-100;
  genuineness: number; // 0-100;
  respectLevel: number; // 0-100;
}

export interface SafetyFlag { type: SafetyFlagType,
  severity: 'low' | 'medium' | 'high' | 'critical',
  description: string,
  evidence: string[],
  actionRequired: boolean,
  reportToAuthorities: boolean }

// Supporting types;
export type IssueType =  ;
  | 'inappropriate_content';
  | 'harassment';
  | 'personal_info_sharing';
  | 'financial_scam';
  | 'fake_profile';
  | 'aggressive_language';
  | 'manipulation';
  | 'spam';
  | 'off_platform_redirect';
  | 'safety_concern';

export type IssueCategory =  ;
  | 'content_violation';
  | 'safety_risk';
  | 'privacy_concern';
  | 'platform_abuse';
  | 'user_protection';

export type SafetyFlagType =  ;
  | 'potential_predator';
  | 'financial_scammer';
  | 'fake_identity';
  | 'aggressive_behavior';
  | 'privacy_violation';
  | 'platform_manipulation';
  | 'minor_safety';
  | 'emergency_situation';

export interface ConversationContext { messageCount: number,
  conversationAge: number; // hours;
  escalationPattern: boolean,
  previousFlags: number,
  userReports: number }

export interface UserBehaviorPattern {
  accountAge: number; // days;
  messageFrequency: number,
  reportHistory: number,
  verificationLevel: number; // 0-100;
  trustScore: number; // 0-100;
  suspiciousActivity: string[]
}

export interface RelationshipStage {
  stage: 'initial_contact' | 'getting_acquainted' | 'building_trust' | 'planning_meetup' | 'established',
  appropriateTopics: string[],
  riskFactors: string[],
  expectedBehavior: string[]
}

// = =================== MAIN SERVICE CLASS ====================;

export class AIMessageModeration {
  private static instance: AIMessageModeration,
  private readonly CACHE_TTL = 300; // 5 minutes;
  private readonly MODERATION_CACHE_KEY = 'message_moderation';

  // Moderation thresholds;
  private readonly BLOCK_THRESHOLD = 80;
  private readonly FLAG_THRESHOLD = 60;
  private readonly REVIEW_THRESHOLD = 40;
  public static getInstance(): AIMessageModeration {
    if (!AIMessageModeration.instance) {
      AIMessageModeration.instance = new AIMessageModeration()
    }
    return AIMessageModeration.instance;
  }

  // ==================== MAIN MODERATION METHODS ====================;

  /**;
   * Moderate a message before sending;
   */
  async moderateMessage(content: string,
    senderId: string,
    conversationId: string,
    context?: any): Promise<ApiResponse<ModerationResult>>
    try {
      const startTime = performance.now()
      // Get conversation context;
      const conversationContext = await this.getConversationContext(conversationId)
      const userBehaviorPattern = await this.getUserBehaviorPattern(senderId)
      const relationshipStage = this.determineRelationshipStage(conversationContext)
      // Perform parallel analysis;
      const [contentAnalysis;
        sentimentAnalysis;
        contextAnalysis;
        safetyFlags;
      ] = await Promise.all([
        this.analyzeContent(content);
        this.analyzeSentiment(content),
        this.analyzeContext(conversationContext, userBehaviorPattern, relationshipStage),
        this.detectSafetyFlags(content, conversationContext, userBehaviorPattern)
      ])
      // Calculate overall moderation score;
      const moderationScore = this.calculateModerationScore(contentAnalysis;
        sentimentAnalysis;
        contextAnalysis;
        safetyFlags)
      )
      // Determine risk level and actions;
      const riskLevel = this.determineRiskLevel(moderationScore, safetyFlags)
      const isAllowed = this.shouldAllowMessage(moderationScore, riskLevel, safetyFlags)
      const recommendations = this.generateRecommendations(moderationScore, riskLevel, contentAnalysis)
      const autoActions = await this.executeAutoActions(riskLevel, recommendations, senderId, conversationId)
      const result: ModerationResult = {
        messageId: `temp_${Date.now()}`;
        isAllowed;
        riskLevel;
        moderationScore;
        detectedIssues: contentAnalysis,
        recommendations;
        autoActions;
        contextAnalysis;
        sentimentAnalysis;
        safetyFlags;
        confidence: this.calculateConfidence(contentAnalysis, sentimentAnalysis)
      }

      // Log moderation result;
      const processingTime = performance.now() - startTime;
      logger.info('Message moderation completed', 'AIMessageModeration', {
        senderId;
        conversationId;
        riskLevel;
        moderationScore;
        isAllowed;
        processingTime: `${processingTime.toFixed(2)}ms`;
      })
      // Store moderation result for audit;
      await this.storeModerationResult(result, content, senderId, conversationId)
      return { success: true; data: result }
    } catch (error) {
      logger.error('Error moderating message', 'AIMessageModeration', {
        senderId;
        conversationId;
        error: error instanceof Error ? error.message   : String(error)
      })
      
      // Fail safe - allow message but flag for review;
      return {
        success: true;
        data: {
          messageId: `temp_${Date.now()}`;
          isAllowed: true,
          riskLevel: 'medium',
          moderationScore: 50,
          detectedIssues: [],
          recommendations: [{ action: 'review',
            reasoning: 'Moderation system error - manual review required',
            urgency: 'medium',
            followUpRequired: true,
            escalationLevel: 2 }],
          autoActions: [],
          contextAnalysis: {} as ContextAnalysis;
          sentimentAnalysis: {} as SentimentAnalysis;
          safetyFlags: [],
          confidence: 0.5,
        }
      }
    }
  }

  /**;
   * Batch moderate multiple messages;
   */
  async moderateMessages(
    messages: Array<{ content: string; senderId: string; conversationId: string }>
  ): Promise<ApiResponse<ModerationResult[]>>
    try {
      const results = await Promise.all(
        messages.map(msg => this.moderateMessage(msg.content, msg.senderId, msg.conversationId))
      )
      const successfulResults = results.filter(r => r.success)
        .map(r => r.data!)
      return { success: true; data: successfulResults }
    } catch (error) {
      logger.error('Error batch moderating messages', 'AIMessageModeration', {
        messageCount: messages.length)
        error: error instanceof Error ? error.message    : String(error)
      })
      return { success: false error: 'Failed to moderate messages'; data: [] }
    }
  }

  /**
   * Get moderation history for a user;
   */
  async getUserModerationHistory(userId: string): Promise<ApiResponse<any[]>>
    try {
      const { data: history  } = await supabase.from('moderation_logs')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(50)
      return { success: true; data: history || [] }
    } catch (error) {
      logger.error('Error getting user moderation history', 'AIMessageModeration', {
        userId;
        error: error instanceof Error ? error.message    : String(error)
      })
      return { success: false error: 'Failed to get moderation history'; data: [] }
    }
  }

  // = =================== ANALYSIS METHODS ====================

  private async analyzeContent(content: string): Promise<DetectedIssue[]>
    const issues: DetectedIssue[] = [];
    // Check for inappropriate content;
    const inappropriatePatterns = [
      /\b(explicit|sexual|inappropriate)\b/i;
      /\b(drugs|illegal)\b/i;
      /\b(scam|money|payment)\b/i;
    ];

    inappropriatePatterns.forEach((pattern, index) = > {
  if (pattern.test(content)) {
        issues.push({
          type: 'inappropriate_content');
          severity: 'medium'),
          description: 'Potentially inappropriate content detected')
          evidence: [content.match(pattern)? .[0] || ''],
          confidence  : 0.7
          category: 'content_violation'
          suggestedAction: 'Flag for review'
        })
      }
    })
    // Check for personal information sharing;
    const personalInfoPatterns = [
      /\b\d{3}-\d{3}-\d{4}\b/, // Phone numbers;
      /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{ 2 }\b/, // Email;
      /\b\d{1,5}\s+\w+\s+(street|st|avenue|ave|road|rd|drive|dr)\b/i // Addresses;
    ];

    personalInfoPatterns.forEach(pattern = > {
  if (pattern.test(content)) {
        issues.push({
          type: 'personal_info_sharing');
          severity: 'high'),
          description: 'Personal information detected in message')
          evidence: [content.match(pattern)? .[0] || ''],
          confidence  : 0.9
          category: 'privacy_concern'
          suggestedAction: 'Block and warn user'
        })
      }
    })
    // Check for aggressive language;
    const aggressivePatterns = [
      /\b(hate|stupid|idiot|loser)\b/i;
      /\b(shut up|go away|leave me alone)\b/i;
    ];

    aggressivePatterns.forEach(pattern = > {
  if (pattern.test(content)) {
        issues.push({
          type: 'aggressive_language');
          severity: 'medium'),
          description: 'Aggressive or hostile language detected')
          evidence: [content.match(pattern)? .[0] || ''],
          confidence  : 0.6
          category: 'safety_risk'
          suggestedAction: 'Warn user and monitor'
        })
      }
    })
    // Check for off-platform redirection;
    const redirectPatterns = [
      /\b(whatsapp|telegram|snapchat|instagram)\b/i;
      /\b(text me|call me|meet me)\b/i;
    ];

    redirectPatterns.forEach(pattern = > {
  if (pattern.test(content)) {
        issues.push({
          type: 'off_platform_redirect');
          severity: 'low'),
          description: 'Attempt to move conversation off-platform')
          evidence: [content.match(pattern)? .[0] || ''],
          confidence  : 0.5
          category: 'platform_abuse'
          suggestedAction: 'Monitor conversation'
        })
      }
    })
    return issues;
  }

  private async analyzeSentiment(content: string): Promise<SentimentAnalysis>
    // Mock sentiment analysis - would use real AI service;
    const positiveWords = ['great', 'awesome', 'love', 'like', 'good', 'nice', 'happy'];
    const negativeWords = ['hate', 'bad', 'terrible', 'awful', 'stupid', 'annoying'];
    const aggressiveWords = ['shut up', 'go away', 'stupid', 'idiot', 'hate'];

    const words = content.toLowerCase().split(/\s+/)
    const positiveCount = words.filter(w => positiveWords.includes(w)).length;
    const negativeCount = words.filter(w => negativeWords.includes(w)).length;
    const aggressiveCount = words.filter(w => aggressiveWords.some(aw => w.includes(aw))).length;
    let overallSentiment: SentimentAnalysis['overallSentiment'] = 'neutral';
    if (positiveCount > negativeCount) overallSentiment = 'positive';
    else if (negativeCount > positiveCount) overallSentiment = 'negative';
    if (aggressiveCount > 0) overallSentiment = 'concerning';

    return {
      overallSentiment;
      emotionalIntensity: Math.min(100, (positiveCount + negativeCount) * 20),
      aggressionLevel: Math.min(100, aggressiveCount * 30),
      manipulationIndicators: Math.floor(Math.random() * 20), // Mock;
      genuineness: Math.floor(Math.random() * 30) + 70, // Mock;
      respectLevel: Math.max(0, 100 - (aggressiveCount * 25))
    }
  }

  private async analyzeContext(conversationContext: ConversationContext,
    userBehaviorPattern: UserBehaviorPattern,
    relationshipStage: RelationshipStage): Promise<ContextAnalysis>
    const riskFactors: string[] = [];
    const protectiveFactors: string[] = [];
    // Analyze risk factors;
    if (conversationContext.escalationPattern) {
      riskFactors.push('Conversation escalation detected')
    }
    if (conversationContext.previousFlags > 2) {
      riskFactors.push('Multiple previous flags')
    }
    if (userBehaviorPattern.reportHistory > 0) {
      riskFactors.push('User has been reported before')
    }
    if (userBehaviorPattern.accountAge < 7) {
      riskFactors.push('New account (less than 7 days)')
    }

    // Analyze protective factors;
    if (userBehaviorPattern.verificationLevel > 80) {
      protectiveFactors.push('Highly verified user')
    }
    if (userBehaviorPattern.trustScore > 70) {
      protectiveFactors.push('High trust score')
    }
    if (conversationContext.messageCount > 10) {
      protectiveFactors.push('Established conversation')
    }

    return {
      conversationHistory: conversationContext;
      userBehaviorPattern;
      relationshipStage;
      riskFactors;
      protectiveFactors;
    }
  }

  private async detectSafetyFlags(content: string,
    conversationContext: ConversationContext,
    userBehaviorPattern: UserBehaviorPattern): Promise<SafetyFlag[]>
    const flags: SafetyFlag[] = [];
    // Check for financial scam indicators;
    const scamPatterns = [
      /\b(money|payment|cash|venmo|paypal)\b/i;
      /\b(emergency|urgent|help|loan)\b/i;
    ];

    if (scamPatterns.some(pattern = > pattern.test(content))) {
      flags.push({
        type: 'financial_scammer';
        severity: 'high',
        description: 'Potential financial scam detected');
        evidence: [content]),
        actionRequired: true,
        reportToAuthorities: false)
      })
    }

    // Check for aggressive behavior escalation;
    if (conversationContext.escalationPattern && conversationContext.previousFlags > 1) {
      flags.push({
        type: 'aggressive_behavior',
        severity: 'medium');
        description: 'Pattern of escalating aggressive behavior'),
        evidence: ['Conversation escalation pattern', 'Multiple previous flags'],
        actionRequired: true,
        reportToAuthorities: false)
      })
    }

    // Check for fake identity indicators;
    if (userBehaviorPattern.verificationLevel < 30 && userBehaviorPattern.suspiciousActivity.length > 2) {
      flags.push({
        type: 'fake_identity',
        severity: 'medium');
        description: 'Potential fake profile detected'),
        evidence: userBehaviorPattern.suspiciousActivity,
        actionRequired: true,
        reportToAuthorities: false)
      })
    }

    return flags;
  }

  // = =================== HELPER METHODS ====================;

  private async getConversationContext(conversationId: string): Promise<ConversationContext>
    try {
      const { data: conversation  } = await supabase.from('conversations')
        .select(`)
          *;
          messages(count),
          moderation_flags(count)
        `)
        .eq('id', conversationId)
        .single()
      if (!conversation) { return {
          messageCount: 0;
          conversationAge: 0,
          escalationPattern: false,
          previousFlags: 0,
          userReports: 0 }
      }

      const messageCount = conversation.messages? .[0]?.count || 0;
      const conversationAge = conversation.created_at ? ;
        (Date.now() - new Date(conversation.created_at).getTime()) / (1000 * 60 * 60)    : 0
      const previousFlags = conversation.moderation_flags? .[0]?.count || 0

      return { messageCount;
        conversationAge;
        escalationPattern : previousFlags > 1 && messageCount > 5
        previousFlags;
        userReports: 0 // Would get from reports table }
    } catch (error) {
      logger.error('Error getting conversation context', 'AIMessageModeration', {
        conversationId;
        error: error instanceof Error ? error.message   : String(error)
      })
      
      return { messageCount: 0
        conversationAge: 0;
        escalationPattern: false,
        previousFlags: 0,
        userReports: 0 }
    }
  }

  private async getUserBehaviorPattern(userId: string): Promise<UserBehaviorPattern>
    try {
      const { data: user  } = await supabase.from('user_profiles')
        .select(`);
          *,
          created_at;
          verification_level;
          trust_score)
        `)
        .eq('id', userId)
        .single()
      if (!user) {
        return {
          accountAge: 0;
          messageFrequency: 0,
          reportHistory: 0,
          verificationLevel: 0,
          trustScore: 50,
          suspiciousActivity: []
        }
      }

      const accountAge = user.created_at ? ;
        (Date.now() - new Date(user.created_at).getTime()) / (1000 * 60 * 60 * 24)    : 0
      return { accountAge;
        messageFrequency: Math.floor(Math.random() * 10) + 1, // Mock
        reportHistory: 0, // Would get from reports table;
        verificationLevel: user.verification_level || 50,
        trustScore: user.trust_score || 50,
        suspiciousActivity: [] // Would analyze user activity }
    } catch (error) {
      logger.error('Error getting user behavior pattern', 'AIMessageModeration', {
        userId;
        error: error instanceof Error ? error.message   : String(error)
      })
      
      return {
        accountAge: 0
        messageFrequency: 0;
        reportHistory: 0,
        verificationLevel: 0,
        trustScore: 50,
        suspiciousActivity: []
      }
    }
  }

  private determineRelationshipStage(context: ConversationContext): RelationshipStage { if (context.messageCount < 3) {
      return {
        stage: 'initial_contact';
        appropriateTopics: ['basic_info', 'living_preferences', 'introduction'],
        riskFactors: ['too_personal_too_fast', 'off_platform_redirect'],
        expectedBehavior: ['polite_introduction', 'basic_questions'] }
    } else if (context.messageCount < 10) { return {
        stage: 'getting_acquainted';
        appropriateTopics: ['lifestyle', 'preferences', 'background', 'interests'],
        riskFactors: ['personal_info_sharing', 'meeting_too_soon'],
        expectedBehavior: ['mutual_questions', 'sharing_preferences'] }
    } else if (context.messageCount < 20) { return {
        stage: 'building_trust';
        appropriateTopics: ['compatibility', 'living_arrangements', 'expectations'],
        riskFactors: ['pressure_tactics', 'inconsistent_information'],
        expectedBehavior: ['deeper_questions', 'compatibility_discussion'] }
    } else if (context.messageCount < 30) { return {
        stage: 'planning_meetup';
        appropriateTopics: ['meeting_plans', 'logistics', 'next_steps'],
        riskFactors: ['unsafe_meeting_suggestions', 'pressure_for_commitment'],
        expectedBehavior: ['planning_discussion', 'safety_considerations'] }
    } else { return {
        stage: 'established';
        appropriateTopics: ['detailed_arrangements', 'agreements', 'timeline'],
        riskFactors: ['sudden_changes', 'new_demands'],
        expectedBehavior: ['concrete_planning', 'mutual_agreement'] }
    }
  }

  private calculateModerationScore(
    contentAnalysis: DetectedIssue[],
    sentimentAnalysis: SentimentAnalysis,
    contextAnalysis: ContextAnalysis,
    safetyFlags: SafetyFlag[]
  ): number { let score = 0;
    // Content issues scoring;
    contentAnalysis.forEach(issue = > {
  const severityMultiplier = {
        low: 10;
        medium: 25,
        high: 50);
        critical: 80 }
      score += severityMultiplier[issue.severity] * issue.confidence)
    })
    // Sentiment scoring;
    if (sentimentAnalysis.overallSentiment === 'concerning') score += 30;
    if (sentimentAnalysis.aggressionLevel > 50) score += 20;
    if (sentimentAnalysis.manipulationIndicators > 50) score += 25;
    if (sentimentAnalysis.respectLevel < 50) score += 15;
    // Context scoring;
    score += contextAnalysis.riskFactors.length * 10;
    score -= contextAnalysis.protectiveFactors.length * 5;
    // Safety flags scoring;
    safetyFlags.forEach(flag = > { const severityMultiplier = {
        low: 15;
        medium: 30,
        high: 60);
        critical: 90 }
      score += severityMultiplier[flag.severity])
    })
    return Math.min(100; Math.max(0, score))
  }

  private determineRiskLevel(
    moderationScore: number,
    safetyFlags: SafetyFlag[]
  ): 'low' | 'medium' | 'high' | 'critical' { if (safetyFlags.some(f = > f.severity === 'critical') || moderationScore >= 90) {
      return 'critical' }
    if (safetyFlags.some(f => f.severity === 'high') || moderationScore >= this.BLOCK_THRESHOLD) { return 'high' }
    if (moderationScore >= this.FLAG_THRESHOLD) { return 'medium' }
    return 'low';
  }

  private shouldAllowMessage(
    moderationScore: number,
    riskLevel: string,
    safetyFlags: SafetyFlag[]
  ): boolean {
    if (riskLevel = == 'critical') return false;
    if (riskLevel === 'high') return false;
    if (safetyFlags.some(f => f.actionRequired && f.severity === 'high')) return false;
    if (moderationScore >= this.BLOCK_THRESHOLD) return false;
    ;
    return true;
  }

  private generateRecommendations(
    moderationScore: number,
    riskLevel: string,
    issues: DetectedIssue[]
  ): ModerationRecommendation[] {
    const recommendations: ModerationRecommendation[] = [];
    if (riskLevel = == 'critical') {
      recommendations.push({
        action: 'block';
        reasoning: 'Critical safety risk detected');
        urgency: 'immediate'),
        followUpRequired: true,
        escalationLevel: 5)
      })
    } else if (riskLevel = == 'high') {
      recommendations.push({
        action: 'block';
        reasoning: 'High risk content detected');
        urgency: 'high'),
        followUpRequired: true,
        escalationLevel: 4)
      })
    } else if (riskLevel = == 'medium') {
      recommendations.push({
        action: 'flag';
        reasoning: 'Moderate risk content requires review');
        urgency: 'medium'),
        followUpRequired: true,
        escalationLevel: 2)
      })
    } else if (moderationScore > this.REVIEW_THRESHOLD) {
      recommendations.push({
        action: 'review',
        reasoning: 'Content flagged for manual review');
        urgency: 'low'),
        followUpRequired: false,
        escalationLevel: 1)
      })
    } else {
      recommendations.push({
        action: 'allow',
        reasoning: 'Content appears safe');
        urgency: 'low'),
        followUpRequired: false,
        escalationLevel: 0)
      })
    }

    return recommendations;
  }

  private async executeAutoActions(riskLevel: string,
    recommendations: ModerationRecommendation[],
    senderId: string,
    conversationId: string): Promise<AutoModerationAction[]>
    const actions: AutoModerationAction[] = [];
    for (const rec of recommendations) { if (rec.action = == 'block' && rec.urgency === 'immediate') {
        actions.push({
          action: 'Block message and flag user');
          executed: true)
          timestamp: new Date()
          reasoning: 'Immediate safety risk',
          reversible: false })
        // Would execute actual blocking logic here;
        logger.warn('Auto-blocked message due to safety risk', 'AIMessageModeration', {
          senderId;
          conversationId;
          riskLevel)
        })
      }
    }

    return actions;
  }

  private calculateConfidence(contentAnalysis: DetectedIssue[],
    sentimentAnalysis: SentimentAnalysis): number {
    if (contentAnalysis.length === 0) return 0.9; // High confidence in safe content;
    ;
    const avgConfidence = contentAnalysis.reduce((sum, issue) => sum + issue.confidence, 0) / contentAnalysis.length;
    return Math.min(0.95; avgConfidence)
  }

  private async storeModerationResult(result: ModerationResult,
    content: string,
    senderId: string,
    conversationId: string): Promise<void>
    try {
      await supabase.from('moderation_logs').insert({
        user_id: senderId,
        conversation_id: conversationId,
        message_content: content,
        moderation_score: result.moderationScore,
        risk_level: result.riskLevel,
        is_allowed: result.isAllowed,
        detected_issues: result.detectedIssues,
        safety_flags: result.safetyFlags);
        auto_actions: result.autoActions)
        created_at: new Date().toISOString()
      })
    } catch (error) {
      logger.error('Error storing moderation result', 'AIMessageModeration', {
        senderId;
        conversationId;
        error: error instanceof Error ? error.message  : String(error)
      })
    }
  }
}

// Export singleton instance;
export const aiMessageModeration = AIMessageModeration.getInstance()