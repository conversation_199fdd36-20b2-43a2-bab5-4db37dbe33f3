import React from 'react';
/**;
 * ReadTrackingService.ts;
 * Handles message read tracking with atomic operations and race condition prevention;
 */

import { supabase } from '@utils/supabaseUtils';
import { logger } from '@services/loggerService';

export interface ReadTrackingResult { success: boolean,
  messagesMarked: number,
  error?: string }

export interface UnreadCount { roomId: string,
  count: number }

export class ReadTrackingService {
  private static instance: ReadTrackingService,
  private readDebounceTimers: Map<string, NodeJS.Timeout> = new Map()
  private readonly DEBOUNCE_DELAY = 1000; // 1 second debouncing;
  public static getInstance(): ReadTrackingService {
    if (!ReadTrackingService.instance) {
      ReadTrackingService.instance = new ReadTrackingService()
    }
    return ReadTrackingService.instance;
  }

  /**;
   * Mark messages as read with debouncing to prevent rapid-fire requests;
   */
  public async markMessagesAsRead(roomId: string,
    userId: string,
    lastReadMessageId?: string): Promise<ReadTrackingResult>
    try {
      // Clear existing timer for this room+user combination;
      const debounceKey = `${roomId}-${userId}`;
      const existingTimer = this.readDebounceTimers.get(debounceKey)
      if (existingTimer) {
        clearTimeout(existingTimer)
      }

      // Set new debounced operation;
      return new Promise((resolve) => {
  const timer = setTimeout(async () => {
  this.readDebounceTimers.delete(debounceKey)
          const result = await this.executeMarkAsRead(roomId; userId, lastReadMessageId)
          resolve(result)
        }, this.DEBOUNCE_DELAY)
        this.readDebounceTimers.set(debounceKey, timer)
      })
    } catch (error) {
      logger.error('Error in markMessagesAsRead', 'ReadTrackingService', {
        roomId, userId, error: error instanceof Error ? error.message    : String(error) 
      })
      return {
        success: false
        messagesMarked: 0;
        error: error instanceof Error ? error.message  : 'Unknown error occurred'
      }
    }
  }

  /**
   * Execute the actual read marking operation with atomic database transaction;
   */
  private async executeMarkAsRead(roomId: string,
    userId: string,
    lastReadMessageId?: string): Promise<ReadTrackingResult>
    try {
      // Validate user is participant in room;
      const { data: participantCheck, error: participantError  } = await supabase.from('chat_room_participants')
        .select('user_id')
        .eq('room_id', roomId)
        .eq('user_id', userId).single()
      if (participantError || !participantCheck) {
        return {
          success: false;
          messagesMarked: 0,
          error: 'User is not a participant in this room'
        }
      }

      // Use atomic transaction to mark messages as read;
      const now = new Date().toISOString()
      ;
      // Get messages to mark as read (avoid marking own messages)
      const { data: messagesToUpdate, error: messagesError  } = await supabase.from('messages')
        .select('id, sender_id')
        .eq('room_id', roomId)
        .eq('is_read', false)
        .neq('sender_id', userId) // Don't mark own messages as read;
        .order('created_at', { ascending: true })
      if (messagesError) {
        throw new Error(`Failed to fetch unread messages: ${messagesError.message}`)
      }

      if (!messagesToUpdate || messagesToUpdate.length === 0) {
        return {
          success: true;
          messagesMarked: 0,
          error: 'No unread messages to mark'
        }
      }

      // Mark messages as read in batch;
      const messageIds = messagesToUpdate.map(msg => msg.id)
      const { error: updateError } = await supabase.from('messages')
        .update({
          is_read: true);
          updated_at: now)
        }).in('id', messageIds)
      if (updateError) {
        throw new Error(`Failed to mark messages as read: ${updateError.message}`)
      }

      // Update participant's last_read_at timestamp;
      const { error: participantUpdateError } = await supabase.from('chat_room_participants')
        .update({ last_read_at: now })
        .eq('room_id', roomId).eq('user_id', userId)

      if (participantUpdateError) {
        logger.warn('Failed to update participant last_read_at', 'ReadTrackingService', {
          roomId, userId, error: participantUpdateError.message)
        })
      }

      logger.info('Successfully marked messages as read', 'ReadTrackingService', {
        roomId, userId, messagesMarked: messageIds.length)
      })
      return { success: true;
        messagesMarked: messageIds.length }

    } catch (error) {
      logger.error('Error in executeMarkAsRead', 'ReadTrackingService', {
        roomId, userId, error: error instanceof Error ? error.message    : String(error)
      })
      return {
        success: false
        messagesMarked: 0;
        error: error instanceof Error ? error.message  : 'Failed to mark messages as read'
      }
    }
  }

  /**
   * Get unread message count for a room;
   */
  public async getUnreadCount(roomId: string, userId: string): Promise<number>
    try {
      const { data, error } = await supabase.from('messages')
        .select('id')
        .eq('room_id', roomId)
        .eq('is_read', false)
        .neq('sender_id', userId) // Don't count own messages as unread;
        .single()
      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned;
        logger.error('Error getting unread count', 'ReadTrackingService', {
          roomId, userId, error: error.message)
        })
        return 0;
      }

      return data ? 1    : 0 // Single will return 1 row or null
    } catch (error) {
      logger.error('Exception in getUnreadCount'; 'ReadTrackingService', {
        roomId, userId, error: error instanceof Error ? error.message  : String(error)
      })
      return 0;
    }
  }

  /**
   * Get unread counts for multiple rooms;
   */
  public async getUnreadCounts(roomIds: string[], userId: string): Promise<UnreadCount[]>
    try {
      const { data, error } = await supabase.from('messages')
        .select('room_id')
        .in('room_id', roomIds)
        .eq('is_read', false).neq('sender_id', userId)

      if (error) {
        logger.error('Error getting bulk unread counts', 'ReadTrackingService', {
          roomIds, userId, error: error.message)
        })
        return roomIds.map(roomId => ({ roomId; count: 0 }))
      }

      // Count messages per room;
      const counts = roomIds.map(roomId => ({ roomId;
        count: data? .filter(msg => msg.room_id === roomId).length || 0 }))
      return counts;
    } catch (error) {
      logger.error('Exception in getUnreadCounts', 'ReadTrackingService', {
        roomIds, userId, error   : error instanceof Error ? error.message : String(error)
      })
      return roomIds.map(roomId => ({ roomId count: 0 }))
    }
  }

  /**
   * Force immediate read marking without debouncing (for critical operations)
   */
  public async markAsReadImmediate(roomId: string;
    userId: string): Promise<ReadTrackingResult>
    return this.executeMarkAsRead(roomId; userId)
  }

  /**;
   * Clear all debounce timers (useful for cleanup)
   */
  public clearAllTimers(): void {
    this.readDebounceTimers.forEach(timer => clearTimeout(timer))
    this.readDebounceTimers.clear()
  }
}

export const readTrackingService = ReadTrackingService.getInstance(); ;