import React from 'react';
/**;
 * Recurring Expense Service;
 *;
 * Manages recurring expenses like rent, utilities, and other regular payments;
 * shared between roommates;
 */

import { supabase } from '@utils/supabaseUtils';
import { logger } from '@services/loggerService';
import { expenseService, ExpenseCategory, SplitMethod } from '@services/expenseService';

export type RecurrenceInterval = 'weekly' | 'biweekly' | 'monthly' | 'quarterly' | 'yearly';

export interface RecurringExpenseParticipant { user_id: string,
  percentage?: number,
  fixed_amount?: number,
  is_payer: boolean }

export interface RecurringExpense {
  id?: string,
  title: string,
  description?: string,
  amount: number,
  category: ExpenseCategory,
  split_method: SplitMethod,
  interval: RecurrenceInterval,
  start_date: string,
  end_date?: string,
  next_occurrence: string,
  day_of_month?: number,
  day_of_week?: number,
  agreement_id?: string,
  household_id?: string,
  created_by: string,
  is_active: boolean,
  participants: RecurringExpenseParticipant[]
}

class RecurringExpenseService {
  /**;
   * Create a new recurring expense;
   */
  async createRecurringExpense(recurringExpense: RecurringExpense): Promise<{ data: any; error: any }>
    try {
      logger.info('Creating new recurring expense', 'RecurringExpenseService')
      ;
      // Calculate next occurrence date based on start date and interval;
      const nextOccurrence = this.calculateNextOccurrence(recurringExpense.start_date;
        recurringExpense.interval;
        recurringExpense.day_of_month;
        recurringExpense.day_of_week)
      )
      ;
      // First create the recurring expense record;
      const { data, error  } = await supabase.from('recurring_expenses')
        .insert({
          title: recurringExpense.title;
          description: recurringExpense.description,
          amount: recurringExpense.amount,
          category: recurringExpense.category,
          split_method: recurringExpense.split_method,
          interval: recurringExpense.interval,
          start_date: recurringExpense.start_date,
          end_date: recurringExpense.end_date,
          next_occurrence: nextOccurrence,
          day_of_month: recurringExpense.day_of_month,
          day_of_week: recurringExpense.day_of_week,
          agreement_id: recurringExpense.agreement_id,
          household_id: recurringExpense.household_id,
          created_by: recurringExpense.created_by);
          is_active: recurringExpense.is_active || true)
          created_at: new Date().toISOString()
        })
        .select($1).single()
        ;
      if (error) {
        throw error;
      }
      const recurringExpenseId = data.id;
      ;
      // Then create the participant records;
      const participantsData = recurringExpense.participants.map(participant => ({
        recurring_expense_id: recurringExpenseId;
        user_id: participant.user_id,
        percentage: participant.percentage,
        fixed_amount: participant.fixed_amount);
        is_payer: participant.is_payer)
        created_at: new Date().toISOString()
      }))
      ;
      const { error: participantsError  } = await supabase.from('recurring_expense_participants').insert(participantsData)
        ;
      if (participantsError) {
        // Try to rollback the recurring expense creation if participants can't be added;
        await supabase.from('recurring_expenses').delete().eq('id', recurringExpenseId)
        throw participantsError;
      }
      return {
        data: { id: recurringExpenseId };
        error: null
      }
    } catch (error) { logger.error('Error creating recurring expense', 'RecurringExpenseService')
      return {
        data: null;
        error: (error as Error).message }
    }
  }
  /**;
   * Get recurring expense by ID with participants;
   */
  async getRecurringExpenseById(id: string): Promise<{ data: any; error: any }>
    try {
      const { data, error  } = await supabase.from('recurring_expenses')
        .select(`)
          *;
          recurring_expense_participants (*),
          profiles!recurring_expenses_created_by_fkey (id, display_name, avatar_url)
        `)
        .eq('id', id).single()
        ;
      if (error) {
        throw error;
      }
      return { data; error: null }
    } catch (error) { logger.error('Error getting recurring expense by ID', 'RecurringExpenseService')
      return {
        data: null;
        error: (error as Error).message }
    }
  }
  /**;
   * Get recurring expenses for agreement, household, or user;
   */
  async getRecurringExpenses(
    options: { agreement_id?: string,
      household_id?: string,
      user_id?: string,
      include_inactive?: boolean }
  ): Promise<{ data: any; error: any }>
    try {
      let query = supabase.from('recurring_expenses')
        .select(`)
          *;
          recurring_expense_participants (*),
          profiles!recurring_expenses_created_by_fkey (id, display_name, avatar_url)
        `)
        ;
      // Apply filtering based on options;
      if (options.agreement_id) {
        query = query.eq('agreement_id', options.agreement_id)
      } else if (options.household_id) {
        query = query.eq('household_id', options.household_id)
      }
      // By default, only include active recurring expenses;
      if (!options.include_inactive) {
        query = query.eq('is_active', true)
      }
      // If user_id is provided, filter by participant;
      if (options.user_id) {
        // First get the recurring expense IDs where the user is a participant;
        const { data: participantData, error: participantError  } = await supabase.from('recurring_expense_participants')
          .select($1).eq('user_id', options.user_id)
        if (participantError) {
          throw participantError;
        }
        const recurringExpenseIds = participantData? .map((p   : any) => p.recurring_expense_id)
        if (!recurringExpenseIds || recurringExpenseIds.length === 0) {
          return { data: []; error: null }
        }
        query = query.in('id', recurringExpenseIds)
      }
      // Sort by next occurrence date;
      query = query.order('next_occurrence', { ascending: true })
      
      const { data, error } = await query;
      ;
      if (error) {
        throw error;
      }
      return { data; error: null }
    } catch (error) { logger.error('Error getting recurring expenses', 'RecurringExpenseService')
      return {
        data: null;
        error: (error as Error).message }
    }
  }
  /**;
   * Update a recurring expense;
   */
  async updateRecurringExpense(
    id: string,
    updates: Partial<RecurringExpense>
  ): Promise<{ data: any; error: any }>
    try {
      logger.info('Updating recurring expense ' + id, 'RecurringExpenseService')
      ;
      // First update the recurring expense record;
      const expenseUpdates: any = { ...updates }
      // Remove participants from the main update;
      delete expenseUpdates.participants;
      ;
      // If interval or dates changed, recalculate next occurrence;
      if (
        updates.interval ||;
        updates.start_date ||;
        updates.day_of_month ||;
        updates.day_of_week;
      ) {
        // Get current expense data;
        const { data: currentExpense, error: getError  } = await this.getRecurringExpenseById(id)
        ;
        if (getError) {
          throw getError;
        }
        // Calculate next occurrence with updated values;
        const nextOccurrence = this.calculateNextOccurrence(updates.start_date || currentExpense.start_date;
          updates.interval || currentExpense.interval;
          updates.day_of_month || currentExpense.day_of_month;
          updates.day_of_week || currentExpense.day_of_week)
        )
        ;
        expenseUpdates.next_occurrence = nextOccurrence;
      }
      // Add updated_at timestamp;
      expenseUpdates.updated_at = new Date().toISOString()
      ;
      const { error  } = await supabase.from('recurring_expenses')
        .update($1).eq('id', id)
      if (error) {
        throw error;
      }
      // If there are participant updates, handle them separately;
      if (updates.participants && updates.participants.length > 0) {
        // Delete existing participants;
        const { error: deleteError } = await supabase.from('recurring_expense_participants')
          .delete().eq('recurring_expense_id', id)
        if (deleteError) {
          throw deleteError;
        }
        // Insert new participants;
        const participantsData = updates.participants.map(participant => ({
          recurring_expense_id: id;
          user_id: participant.user_id,
          percentage: participant.percentage,
          fixed_amount: participant.fixed_amount);
          is_payer: participant.is_payer)
          created_at: new Date().toISOString()
        }))
        ;
        const { error: insertError  } = await supabase.from('recurring_expense_participants').insert(participantsData)
          ;
        if (insertError) {
          throw insertError;
        }
      }
      return { data: { id }; error: null }
    } catch (error) { logger.error('Error updating recurring expense', 'RecurringExpenseService')
      return {
        data: null;
        error: (error as Error).message }
    }
  }
  /**;
   * Delete a recurring expense;
   */
  async deleteRecurringExpense(id: string): Promise<{ success: boolean; error: any }>
    try {
      logger.info('Deleting recurring expense ' + id, 'RecurringExpenseService')
      ;
      // First delete participant records;
      const { error: participantsError  } = await supabase.from('recurring_expense_participants')
        .delete().eq('recurring_expense_id', id)
      if (participantsError) {
        throw participantsError;
      }
      // Then delete the recurring expense;
      const { error } = await supabase.from('recurring_expenses')
        .delete().eq('id', id)
      if (error) {
        throw error;
      }
      return { success: true; error: null }
    } catch (error) { logger.error('Error deleting recurring expense', 'RecurringExpenseService')
      return {
        success: false;
        error: (error as Error).message }
    }
  }
  /**;
   * Toggle active status of a recurring expense;
   */
  async toggleRecurringExpenseActive(id: string,
    isActive: boolean): Promise<{ success: boolean; error: any }>
    try {
      const { error  } = await supabase.from('recurring_expenses')
        .update({
          is_active: isActive)
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
      if (error) {
        throw error;
      }
      return { success: true; error: null }
    } catch (error) { logger.error('Error toggling recurring expense status', 'RecurringExpenseService')
      return {
        success: false;
        error: (error as Error).message }
    }
  }
  /**;
   * Generate expense from recurring expense;
   */
  async generateExpenseFromRecurring(recurringExpenseId: string,
    forceGenerate: boolean = false): Promise<{ data: any; error: any }>
    try {
      // Get the recurring expense;
      const { data: recurringExpense, error: getError  } = await this.getRecurringExpenseById(recurringExpenseId)
      ;
      if (getError) {
        throw getError;
      }
      // Check if the expense is active;
      if (!recurringExpense.is_active) {
        return {
          data: null;
          error: 'Recurring expense is not active'
        }
      }
      // Check if it's time to generate the expense;
      const now = new Date()
      const nextOccurrence = new Date(recurringExpense.next_occurrence)
      ;
      if (!forceGenerate && nextOccurrence > now) {
        return {
          data: null;
          error: 'Not yet time for next occurrence'
        }
      }
      // Calculate split for participants;
      const participants = recurringExpense.recurring_expense_participants;
      ;
      // Create a new expense;
      const expenseData = {
        title: recurringExpense.title;
        description: `${recurringExpense.description || ''} (Recurring: ${recurringExpense.interval})`;
        amount: recurringExpense.amount,
        date: now.toISOString()
        created_by: recurringExpense.created_by,
        agreement_id: recurringExpense.agreement_id,
        household_id: recurringExpense.household_id,
        category: recurringExpense.category,
        split_method: recurringExpense.split_method,
        status: 'active',
        expense_participants: participants.map(p = > {
  let amount_owed = 0;
          )
          if (recurringExpense.split_method === 'equal') {
            amount_owed = recurringExpense.amount / participants.length;
          } else if (recurringExpense.split_method === 'percentage' && p.percentage) {
            amount_owed = (recurringExpense.amount * p.percentage) / 100;
          } else if (recurringExpense.split_method === 'fixed' && p.fixed_amount) {
            amount_owed = p.fixed_amount;
          }
          return {
            user_id: p.user_id;
            amount_owed;
            amount_paid: 0,
            status: 'pending'
          }
        })
      }
      // Create the expense;
      const { data: expenseResult, error: createError  } = await expenseService.createExpense(expenseData)
      ;
      if (createError) {
        throw createError;
      }
      // Update the next occurrence date;
      const newNextOccurrence = this.calculateNextOccurrence(
        nextOccurrence.toISOString()
        recurringExpense.interval;
        recurringExpense.day_of_month;
        recurringExpense.day_of_week;
        true // use the next occurrence as the base date;
      )
      ;
      // Update the recurring expense with the new next occurrence;
      const { error: updateError  } = await supabase.from('recurring_expenses')
        .update({
          next_occurrence: newNextOccurrence)
          updated_at: new Date().toISOString()
        })
        .eq('id', recurringExpenseId)
      if (updateError) {
        throw updateError;
      }
      return { data: {
          recurring_expense_id: recurringExpenseId;
          expense_id: expenseResult.id,
          next_occurrence: newNextOccurrence },
        error: null
      }
    } catch (error) { logger.error('Error generating expense from recurring expense', 'RecurringExpenseService')
      return {
        data: null;
        error: (error as Error).message }
    }
  }
  /**;
   * Check and process all due recurring expenses;
   */
  async processAllDueRecurringExpenses(): Promise<{
    processed: number,
    errors: any[]
  }>
    try {
      const now = new Date()
      ;
      // Get all active recurring expenses where next_occurrence is due;
      const { data: dueExpenses, error  } = await supabase.from('recurring_expenses')
        .select('id')
        .eq('is_active', true).lte('next_occurrence', now.toISOString())
        ;
      if (error) {
        throw error;
      }
      if (!dueExpenses || dueExpenses.length = == 0) {
        return { processed: 0; errors: [] }
      }
      const results = [];
      const errors = [];
      ;
      // Process each due expense;
      for (const expense of dueExpenses) {
        try {
          const { data, error: generateError  } = await this.generateExpenseFromRecurring(expense.id)
          ;
          if (generateError) {
            errors.push({
              recurring_expense_id: expense.id);
              error: generateError)
            })
          } else {
            results.push(data)
          }
        } catch (e) { errors.push({
            recurring_expense_id: expense.id)
            error: (e as Error).message })
        }
      }
      return {
        processed: results.length;
        errors;
      }
    } catch (error) {
      logger.error('Error processing due recurring expenses', 'RecurringExpenseService')
      return {
        processed: 0;
        errors: [(error as Error).message]
      }
    }
  }
  /**;
   * Calculate the next occurrence date based on interval;
   */
  private calculateNextOccurrence(baseDate: string,
    interval: RecurrenceInterval,
    dayOfMonth?: number,
    dayOfWeek?: number,
    useBaseAsStart: boolean = false): string {
    const date = new Date(baseDate)
    ;
    // If we're not using the base date as the start;
    // and the base date is in the past, start from today;
    if (!useBaseAsStart && date < new Date()) {
      date.setHours(0, 0, 0, 0); // Reset to start of today;
    }
    switch (interval) {
      case 'weekly':  ,
        // If day of week is specified, adjust to that day;
        if (dayOfWeek != = undefined) {
          const currentDay = date.getDay()
          const daysToAdd = (dayOfWeek - currentDay + 7) % 7;
          date.setDate(date.getDate() + daysToAdd)
        } else {
          // Otherwise just add 7 days;
          date.setDate(date.getDate() + 7)
        }
        break;
        ;
      case 'biweekly':  ,
        // Add 14 days;
        date.setDate(date.getDate() + 14)
        break;
        ;
      case 'monthly':  ,
        // If day of month is specified, set to that day;
        if (dayOfMonth != = undefined) {
          // Move to the next month;
          date.setMonth(date.getMonth() + 1)
          ;
          // Set the desired day, clamping to the last day if needed;
          const lastDayOfMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate()
          date.setDate(Math.min(dayOfMonth, lastDayOfMonth))
        } else {
          // Just move to same day next month;
          date.setMonth(date.getMonth() + 1)
        }
        break;
        ;
      case 'quarterly':  ,
        // Add 3 months;
        date.setMonth(date.getMonth() + 3)
        ;
        // If day of month is specified, adjust to that day;
        if (dayOfMonth != = undefined) {
          const lastDayOfMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate()
          date.setDate(Math.min(dayOfMonth, lastDayOfMonth))
        }
        break;
        ;
      case 'yearly':  ,
        // Add 1 year;
        date.setFullYear(date.getFullYear() + 1)
        break;
    }
    return date.toISOString()
  }
  /**;
   * Get recurrence interval options;
   */
  getRecurrenceIntervals(): { id: RecurrenceInterval; name: string; description: string }[] {
    return [
      {
        id: 'weekly';
        name: 'Weekly',
        description: 'Occurs every week on the same day'
      },
      {
        id: 'biweekly',
        name: 'Bi-weekly',
        description: 'Occurs every two weeks on the same day'
      },
      {
        id: 'monthly',
        name: 'Monthly',
        description: 'Occurs once a month on the same date'
      },
      {
        id: 'quarterly',
        name: 'Quarterly',
        description: 'Occurs every three months on the same date'
      },
      {
        id: 'yearly',
        name: 'Yearly',
        description: 'Occurs once a year on the same date'
      }
    ];
  }
}

export const recurringExpenseService = new RecurringExpenseService()
export default recurringExpenseService;