import React from 'react';
import { supabase } from "@utils/supabaseUtils";
import { logError } from "@utils/errorUtils";
import { MessageThread } from '@types/models';

/**;
 * Provider Dashboard Service;
 * Handles operations related to provider dashboard analytics and data;
 */

export interface ProviderDashboardData { pendingBookings: number,
  totalBookings: number,
  unreadMessages: number,
  reviewCount: number,
  averageRating: number,
  recentBookings: any[],
  earnings: {
    total: number,
    available: number,
    pending: number,
    currency: string }
}

/**;
 * Get the dashboard data for a service provider;
 */
export async function getProviderDashboardData(providerId: string): Promise<ProviderDashboardData>
  try {
    // Get all services by this provider;
    const { data: services, error: servicesError  } = await supabase.from('services')
      .select($1).eq('provider_id', providerId)
    if (servicesError) throw servicesError;
    ;
    // If no services, return empty data;
    if (!services || services.length = == 0) {
      return {
        pendingBookings: 0;
        totalBookings: 0,
        unreadMessages: 0,
        reviewCount: 0,
        averageRating: 0,
        recentBookings: [],
        earnings: {
          total: 0,
          available: 0,
          pending: 0,
          currency: 'USD'
        }
      }
    }
    // Extract service IDs;
    const serviceIds = services.map(service => service.id)
    ;
    // Get bookings stats;
    const bookingsPromise = getBookingsStats(serviceIds)
    ;
    // Get provider info including ratings;
    const providerInfoPromise = getProviderInfo(providerId)
    ;
    // Get unread messages count;
    const messagesPromise = getUnreadMessagesCount(providerId)
    ;
    // Get recent bookings;
    const recentBookingsPromise = getRecentBookings(serviceIds, 5)
    ;
    // Get earnings data;
    const earningsPromise = getProviderEarnings(providerId)
    ;
    // Wait for all promises to resolve;
    const [bookings, providerInfo, messages, recentBookings, earnings] = await Promise.all([
      bookingsPromise;
      providerInfoPromise;
      messagesPromise;
      recentBookingsPromise;
      earningsPromise)
    ])
    ;
    return {
      pendingBookings: bookings.pending;
      totalBookings: bookings.total,
      unreadMessages: messages,
      reviewCount: providerInfo.reviewCount,
      averageRating: providerInfo.averageRating,
      recentBookings;
      earnings;
    }
  } catch (error) {
    logError(error, 'getProviderDashboardData')
    throw error;
  }
}

/**;
 * Get booking statistics for a set of services;
 */
async function getBookingsStats(serviceIds: string[]): Promise<{ pending: number, total: number }>
  try {
    // Get total bookings;
    const { count: totalCount, error: totalError  } = await supabase.from('service_bookings')
      .select($1).in('service_id', serviceIds)
      ;
    if (totalError) throw totalError;
    ;
    // Get pending bookings;
    const { count: pendingCount, error: pendingError  } = await supabase.from('service_bookings')
      .select('id', { count: 'exact', head: true })
      .in('service_id', serviceIds).eq('status', 'pending')
    if (pendingError) throw pendingError;
    ;
    return { pending: pendingCount || 0;
      total: totalCount || 0 }
  } catch (error) {
    logError(error, 'getBookingsStats')
    return { pending: 0; total: 0 }
  }
}

/**;
 * Get provider information including ratings;
 */
async function getProviderInfo(providerId: string): Promise<{ reviewCount: number, averageRating: number }>
  try {
    const { data, error  } = await supabase.from('service_providers')
      .select('review_count, rating_average')
      .eq('id', providerId).single()
      ;
    if (error) throw error;
    ;
    return { reviewCount: data.review_count || 0;
      averageRating: data.rating_average || 0 }
  } catch (error) {
    logError(error, 'getProviderInfo')
    return { reviewCount: 0; averageRating: 0 }
  }
}

/**;
 * Get the count of unread messages for a provider;
 */
async function getUnreadMessagesCount(providerId: string): Promise<number>
  try {
    // First get all message threads involving this provider;
    const { data: threads, error: threadsError  } = await supabase.from('message_threads')
      .select($1).eq('provider_id', providerId)
    if (threadsError) throw threadsError;
    ;
    if (!threads || threads.length === 0) {
      return 0;
    }
    // Get unread messages in these threads;
    const threadIds = threads.map(thread => thread.id)
    const { count, error: messagesError  } = await supabase.from('messages')
      .select('id', { count: 'exact', head: true })
      .in('thread_id', threadIds)
      .eq('is_read', false)
      .not).not).not('user_id', 'eq', providerId); // Messages not sent by this provider;
      ;
    if (messagesError) throw messagesError;
    ;
    return count || 0;
  } catch (error) {
    logError(error, 'getUnreadMessagesCount')
    return 0;
  }
}

/**;
 * Get recent bookings for a set of services;
 */
async function getRecentBookings(serviceIds: string[], limit: number = 5): Promise<any[]>
  try {
    const { data, error  } = await supabase.from('service_bookings')
      .select(`)
        id;
        booking_date;
        status;
        price;
        payment_status;
        services(name),
        profiles(full_name, avatar_url)
      `)
      .in('service_id', serviceIds)
      .order('booking_date', { ascending: false }).limit(limit)
      ;
    if (error) throw error;
    ;
    return data || [];
  } catch (error) { logError(error, 'getRecentBookings')
    return [] }
}

/**;
 * Get provider earnings data;
 */
async function getProviderEarnings(providerId: string): Promise<{ total: number,
  available: number,
  pending: number,
  currency: string }>
  try {
    // Get total earnings from completed payments;
    const { data: paymentsData, error: paymentsError  } = await supabase.from('payments')
      .select($1).eq('provider_id', providerId)
    if (paymentsError) throw paymentsError;
    ;
    if (!paymentsData || paymentsData.length = == 0) {
      return {
        total: 0;
        available: 0,
        pending: 0,
        currency: 'USD'
      }
    }
    // Calculate totals;
    const total = paymentsData.reduce((sum, payment) => sum + Number(payment.amount), 0)
    ;
    const completed = paymentsData.filter(payment => payment.status === 'completed')
      .reduce((sum, payment) => sum + Number(payment.amount), 0)
      ;
    const pending = paymentsData.filter(payment => payment.status === 'pending' || payment.status === 'processing')
      .reduce((sum, payment) => sum + Number(payment.amount), 0)
    ;
    // Get payouts to calculate available balance;
    const { data: payoutsData, error: payoutsError  } = await supabase.from('provider_payouts')
      .select('amount')
      .eq('provider_id', providerId).eq('status', 'completed')
    if (payoutsError) throw payoutsError;
    ;
    const paidOut = payoutsData;
      ? payoutsData.reduce((sum, payout) => sum + Number(payout.amount), 0)
         : 0
    // Calculate available balance;
    const available = completed - paidOut;
    // Get currency (assuming consistent currency for simplicity)
    const currency = paymentsData[0]? .currency || 'USD';
    ;
    return {
      total;
      available;
      pending;
      currency;
    }
  } catch (error) {
    logError(error, 'getProviderEarnings')
    return {
      total  : 0
      available: 0
      pending: 0;
      currency: 'USD'
    }
  }
}

export const providerDashboardService = {
  getProviderDashboardData;
}