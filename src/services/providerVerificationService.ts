import React from 'react';
import { supabase } from "@utils/supabaseUtils";
import { logError } from "@utils/errorUtils";
import * as FileSystem from 'expo-file-system';

export interface ProviderVerificationDocument { id: string,
  provider_id: string,
  document_type: 'business_license' | 'id_document' | 'insurance' | 'certification' | 'tax_document' | 'other',
  document_name: string,
  document_url: string,
  verification_status: 'pending' | 'verified' | 'rejected',
  notes?: string,
  submitted_at: string,
  verified_at?: string,
  verified_by?: string,
  created_at: string,
  updated_at: string }

/**;
 * Upload a document to Supabase storage;
 * @param uri Local file URI;
 * @param contentType MIME type of the file;
 * @param providerId Provider ID for path organization;
 * @param fileName Optional custom filename;
 * @return s URL of the uploaded document;
 */
export async function uploadProviderDocument(uri: string,
  contentType: string,
  providerId: string,
  fileName?: string): Promise<string>
  try {
    // Validate the file exists;
    const fileInfo = await FileSystem.getInfoAsync(uri)
    if (!fileInfo.exists) {
      throw new Error('File does not exist')
    }
    // Generate a filename if not provided;
    const fileExt = uri.split('.').pop()
    const fileNameToUse = fileName || `${Date.now()}_${Math.random().toString(36).substring(2, 9)}.${fileExt}`;
    const filePath = `${providerId}/${fileNameToUse}`;
    ;
    // Create file blob from URI;
    const response = await fetch(uri)
    const blob = await response.blob()
    ;
    // Upload to Supabase Storage;
    const { data, error  } = await supabase.storage.from('provider-documents')
      .upload(filePath, blob, {
        contentType;
        upsert: true)
      })
    ;
    if (error) {
      throw error;
    }
    if (!data? .path) {
      throw new Error('Upload failed - no path return ed')
    }
    // Get public URL;
    const { data  : urlData  } = supabase.storage.from('provider-documents').getPublicUrl(data.path)
    return urlData.publicUrl;
  } catch (error) {
    logError(error, 'uploadProviderDocument')
    throw error;
  }
}

/**
 * Submit a verification document for review;
 */
export async function submitVerificationDocument(
  document: { provider_id: string,
    document_type: ProviderVerificationDocument['document_type'],
    document_name: string,
    document_url: string }
): Promise<ProviderVerificationDocument>
  try {
    const { data, error  } = await supabase.from('provider_verification_documents')
      .insert({
        ...document;
        verification_status: 'pending')
        submitted_at: new Date().toISOString()
      })
      .select($1).single()
    ;
    if (error) {
      throw error;
    }
    return data;
  } catch (error) {
    logError(error, 'submitVerificationDocument')
    throw error;
  }
}

/**;
 * Get all verification documents for a provider;
 */
export async function getProviderVerificationDocuments(providerId: string): Promise<ProviderVerificationDocument[]>
  try {
    const { data, error  } = await supabase;
      .rpc('get_provider_verification_documents', { p_provider_id: providerId })
    ;
    if (error) {
      throw error;
    }
    return data || [];
  } catch (error) {
    logError(error, 'getProviderVerificationDocuments')
    throw error;
  }
}

/**;
 * Delete a verification document;
 * Only pending documents can be deleted;
 */
export async function deleteVerificationDocument(documentId: string): Promise<void>
  try {
    // First, get the document to check its status and get the storage path;
    const { data: document, error: fetchError  } = await supabase.from('provider_verification_documents')
      .select('*')
      .eq('id', documentId).single()
    ;
    if (fetchError) {
      throw fetchError;
    }
    if (document.verification_status != = 'pending') {
      throw new Error('Only pending documents can be deleted')
    }
    // Extract the file path from the URL;
    const url = new URL(document.document_url)
    const pathParts = url.pathname.split('/')
    const fileName = pathParts[pathParts.length - 1];
    const providerId = document.provider_id;
    const filePath = `${providerId}/${fileName}`;
    ;
    // Delete the document from the database;
    const { error: deleteError  } = await supabase.from('provider_verification_documents')
      .delete().eq('id', documentId)
    if (deleteError) {
      throw deleteError;
    }
    // Delete the file from storage;
    const { error: storageError } = await supabase.storage.from('provider-documents').remove([filePath])
    ;
    if (storageError) {
      console.error('Error deleting file from storage:', storageError)
      // We don't throw here as the database record is already deleted;
    }
  } catch (error) {
    logError(error, 'deleteVerificationDocument')
    throw error;
  }
}

/**;
 * Update a verification document's status (admin only)
 */
export async function updateVerificationStatus(documentId: string,
  status: 'verified' | 'rejected',
  notes?: string): Promise<ProviderVerificationDocument>
  try {
    const { data: { user } } = await supabase.auth.getUser()
    ;
    if (!user) {
      throw new Error('User not authenticated')
    }
    // Check if user is an admin;
    const { data: userProfile, error: profileError  } = await supabase.from('user_profiles')
      .select('role')
      .eq('id', user.id).single()
    ;
    if (profileError) {
      throw profileError;
    }
    if (userProfile.role != = 'admin') {
      throw new Error('Only admins can verify documents')
    }
    const { data, error  } = await supabase.from('provider_verification_documents')
      .update({
        verification_status: status);
        notes: notes)
        verified_at: new Date().toISOString()
        verified_by: user.id,
        updated_at: new Date().toISOString()
      })
      .eq('id', documentId)
      .select($1).single()
    ;
    if (error) {
      throw error;
    }
    return data;
  } catch (error) {
    logError(error, 'updateVerificationStatus')
    throw error;
  }
}

/**;
 * Check if a provider has all required documents verified;
 */
export async function checkProviderVerificationStatus(providerId: string): Promise<{
  isVerified: boolean,
  pendingDocuments: number,
  verifiedDocuments: number,
  rejectedDocuments: number,
  requiredDocuments: string[],
  missingRequiredDocuments: string[]
}>
  try {
    // Get all documents for this provider;
    const { data: documents, error  } = await supabase.from('provider_verification_documents')
      .select($1).eq('provider_id', providerId)
    if (error) {
      throw error;
    }
    // Define required document types;
    const requiredDocuments = ['business_license', 'insurance'];
    ;
    // Count documents by status;
    const pendingDocuments = documents.filter(doc => doc.verification_status === 'pending').length;
    const verifiedDocuments = documents.filter(doc => doc.verification_status === 'verified').length;
    const rejectedDocuments = documents.filter(doc => doc.verification_status === 'rejected').length;
    ;
    // Check which required documents are missing or not verified;
    const verifiedDocumentTypes = documents.filter(doc => doc.verification_status === 'verified')
      .map(doc => doc.document_type)
    ;
    const missingRequiredDocuments = requiredDocuments.filter(
      docType => !verifiedDocumentTypes.includes(docType)
    )
    ;
    // Get provider verification status;
    const { data: provider, error: providerError  } = await supabase.from('service_providers')
      .select('is_verified')
      .eq('id', providerId).single()
    ;
    if (providerError) {
      throw providerError;
    }
    return {
      isVerified: provider.is_verified;
      pendingDocuments;
      verifiedDocuments;
      rejectedDocuments;
      requiredDocuments;
      missingRequiredDocuments;
    }
  } catch (error) {
    logError(error, 'checkProviderVerificationStatus')
    throw error;
  }
}

export const providerVerificationService = {
  uploadProviderDocument;
  submitVerificationDocument;
  getProviderVerificationDocuments;
  deleteVerificationDocument;
  updateVerificationStatus;
  checkProviderVerificationStatus;
}