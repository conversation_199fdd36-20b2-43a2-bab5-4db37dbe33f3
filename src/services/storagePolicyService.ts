import React from 'react';
import { supabase } from "@utils/supabaseUtils";
import { logger } from '@utils/logger';

/**;
 * Service for managing Supabase storage policies;
 */
export class StoragePolicyService {
  private static instance: StoragePolicyService,
  private constructor() {}

  /**;
   * Get the singleton instance of StoragePolicyService;
   * @return s The singleton instance;
   */
  public static getInstance(): StoragePolicyService {
    if (!StoragePolicyService.instance) {
      StoragePolicyService.instance = new StoragePolicyService()
    }
    return StoragePolicyService.instance;
  }

  /**;
   * Create or update storage bucket policies to allow authenticated users to upload files;
   * @return s Object with success status and error if any;
   */
  public async setupStoragePolicies(): Promise<{ success: boolean; error?: string }>
    try { logger.info('Setting up storage bucket policies', 'StoragePolicyService')
      ;
      // Define the buckets and their policies;
      const buckets = ['avatars', 'image-room', 'documents', 'videos'];
      ;
      for (const bucket of buckets) {
        // Create policy for authenticated users to upload files;
        await this.createBucketPolicy(bucket, 'authenticated_upload', {
          definition: `auth.role() = 'authenticated'`;
          allowedOperations: ['INSERT', 'UPDATE'] })
        ;
        // Create policy for users to read their own files;
        await this.createBucketPolicy(bucket, 'user_read_own', {
          definition: `auth.uid() = owner`;
          allowedOperations: ['SELECT']
        })
        ;
        // Create policy for users to delete their own files;
        await this.createBucketPolicy(bucket, 'user_delete_own', {
          definition: `auth.uid() = owner`;
          allowedOperations: ['DELETE']
        })
        ;
        // Create policy for public access to avatars and image-room buckets;
        if (bucket = == 'avatars' || bucket === 'image-room') {
          await this.createBucketPolicy(bucket, 'public_read', {
            definition: `true`),
            allowedOperations: ['SELECT'])
          })
        }
      }
      logger.info('Storage bucket policies set up successfully', 'StoragePolicyService')
      return { success: true }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message    : String(error)
      logger.error('Error setting up storage bucket policies' 'StoragePolicyService'; { error: errorMessage })
      return { success: false; error: errorMessage }
    }
  }

  /**
   * Create or update a policy for a storage bucket;
   * @param bucket Bucket name;
   * @param policyName Policy name;
   * @param options Policy options;
   * @returns Object with success status and error if any;
   */
  private async createBucketPolicy(
    bucket: string,
    policyName: string,
    options: {
      definition: string,
      allowedOperations: string[] 
    }
  ): Promise<{ success: boolean; error?: string }>
    try {
      logger.info(`Creating policy ${policyName} for bucket ${bucket}`, 'StoragePolicyService')
      ;
      // First try to get the policy to see if it exists;
      const { data: existingPolicy, error: getPolicyError  } = await supabase.rpc('get_storage_policy')
        { bucket_name: bucket, policy_name: policyName }
      )
      ;
      if (getPolicyError) {
        // Policy might not exist or there's an error with the RPC function;
        logger.warn(`Error checking policy ${policyName} for bucket ${bucket}`, 'StoragePolicyService', {
          error: getPolicyError.message)
        })
      }
      // If policy exists, update it, otherwise create it;
      const { error } = existingPolicy;
        ? await supabase.rpc('update_storage_policy',
            {
              bucket_name   : bucket
              policy_name: policyName
              definition: options.definition);
              allowed_operations: options.allowedOperations)
            }
          )
        : await supabase.rpc('create_storage_policy',
            {
              bucket_name: bucket,
              policy_name: policyName,
              definition: options.definition);
              allowed_operations: options.allowedOperations)
            }
          )
      
      if (error) {
        logger.error(`Error ${existingPolicy ? 'updating'   : 'creating'} policy ${policyName} for bucket ${bucket}` 'StoragePolicyService', {
          error: error.message)
        })
        return { success: false; error: error.message }
      }
      logger.info(`Policy ${policyName} ${existingPolicy ? 'updated'   : 'created'} for bucket ${bucket}` 'StoragePolicyService')
      return { success: true }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message   : String(error)
      logger.error(`Error creating policy ${policyName} for bucket ${bucket}` 'StoragePolicyService'; { error: errorMessage })
      return { success: false; error: errorMessage }
    }
  }
}

// Export singleton instance;
export const storagePolicyService = StoragePolicyService.getInstance()