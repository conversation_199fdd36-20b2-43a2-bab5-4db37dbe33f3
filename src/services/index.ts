// Re-export all services;
// Standardized services - prefer these implementations;
export * from './standardized';

// Core services;
export * from './api';
// Original implementation - use standardized version instead;
// export * from './authService';
export * from './loggerService';
export * from './databaseService';

// User profile services;
// Original implementation - use standardized version instead;
// export * from "../unifiedProfileService";
export * from './profileCompletionService';
export * from './profileImageService';
export * from './profileMatchingService';
export * from './profileMemoryService';
// export * from './profileVerificationService'; // File does not exist - functionality moved to unified verification;
// Matching and communication services;
export * from './matchingService';
// Original implementation - use standardized version instead;
// export * from './chatService';
// The standardized ChatService is now exported from './standardized';
export * from './conversationStarterService';

// Location and property services;
export * from './LocationService'; // Note: Case-sensitive import,
export * from './propertyService';
// Legacy room services removed - use UnifiedRoomService instead;
// export * from './roomService';
// export * from './savedRoomService';

// = ===================;
// AGREEMENT SERVICES - PHASE 6 UNIFIED ARCHITECTURE ✅;
// = ===================;

// ✅ UNIFIED AGREEMENT SERVICE (Consolidates 4 services: agreement + profile + status + compliance)
export {
  unifiedAgreementService;
  UnifiedAgreementService;
  type AgreementTemplate;
  type AgreementData;
  type AgreementParticipant;
  type AgreementStatus;
  type StatusTransition;
  type ComplianceSummary;
  type ComplianceAlert;
  type AgreementDashboard;
} from './unified/UnifiedAgreementService';

// ✅ MIGRATION COMPATIBILITY LAYER (Ensures zero breaking changes)
export {
  createAgreementService;
  createAgreementProfileService;
  createAgreementStatusService;
  createComplianceAutomationService;
  agreementServiceMigration;
  agreementProfileServiceMigration;
  agreementStatusServiceMigration;
  complianceAutomationServiceMigration;
} from './unified/migrations/AgreementServiceMigration';

// ✅ LEGACY AGREEMENT SERVICE (For backward compatibility)
export {
  agreementService, // Now routes to UnifiedAgreementService;
  AgreementService;
  AgreementSection;
  AgreementVersion;
  AgreementReminder;
  ComplianceItem;
  ComplianceHistory;
  AgreementAmendment;
  AmendmentApproval;
} from './agreementService';

// ✅ LEGACY SERVICES (Now route to UnifiedAgreementService)
// export * from './AgreementStatusService'; // ← Consolidated into UnifiedAgreementService;
// export * from "../unifiedProfileService";
// export * from './complianceAutomationService'; // ← Consolidated into UnifiedAgreementService;
// Living together services;
export * from './SharedExpenseService';
// Original implementation - use standardized version instead;
// export * from './MoveInChecklistService';
export * from './MoveInScheduleService';
export * from './moveOutService';
export * from './conflictResolutionService';
export * from './relationshipClosureService';

// Service provider related;
export * from './serviceProviderService';
export { getServiceProviderByUserId } from './serviceProviderService';
export * from './bookingService';

// = ===================;
// AI & ANALYSIS SERVICES - PHASE 8 UNIFIED ARCHITECTURE ✅;
// = ===================;

// ✅ UNIFIED AI SERVICE (Consolidates 6 services: sentiment + topic + moderation + personality + openai + api)
export {
  unifiedAIService;
  UnifiedAIService;
  type SentimentAnalysis;
  type ConversationSentiment;
  type Topic;
  type TopicSuggestion;
  type ConversationTopicAnalysis;
  type ModerationResult;
  type PersonalityProfile;
  type PersonalityTrait;
  type PersonalityTraitCategory;
  type CompatibilityResult;
  type OpenAIRequest;
  type OpenAIResponse;
} from './unified/UnifiedAIService';

// ✅ MIGRATION COMPATIBILITY LAYER (Ensures zero breaking changes)
export {
  sentimentService;
  topicAnalysisService;
  moderationService;
  personalityService;
  openaiService;
  openaiApi;
  SentimentService;
  TopicAnalysisService;
  ModerationService;
  PersonalityService;
  OpenAIService;
  OpenAIApi;
  createSentimentService;
  createTopicAnalysisService;
  createModerationService;
  createPersonalityService;
  createOpenAIService;
  createOpenAIApi;
} from './unified/migrations/AIServiceMigration';

// ✅ LEGACY EXPORTS (For backward compatibility - now route to UnifiedAIService)
// export * from './sentimentService'; // ← Consolidated into UnifiedAIService;
// export * from './topicAnalysisService'; // ← Consolidated into UnifiedAIService;
// export * from './moderationService'; // ← Consolidated into UnifiedAIService;
// export * from './personalityService'; // ← Consolidated into UnifiedAIService;
// export * from './openaiService'; // ← Consolidated into UnifiedAIService;
// export * from './api/openaiApi'; // ← Consolidated into UnifiedAIService;
// Security services;
export * from './fraudDetectionService';

// Storage services;
export * from './storage';

// = ===================;
// VERIFICATION SERVICES - PHASE 7 UNIFIED ARCHITECTURE ✅;
// = ===================;

// ✅ UNIFIED VERIFICATION SERVICE (Consolidates 5 services: verification + profile + background + identity + reference)
export {
  unifiedVerificationService;
  UnifiedVerificationService;
  type VerificationSession;
  type VerificationStatus;
  type VerificationResult;
  type BackgroundCheck;
  type VerificationDashboard;
  type VerificationType;
  type BackgroundCheckType;
  type DocumentType;
  type VerificationPrice;
} from './unified/UnifiedVerificationService';

// ✅ MIGRATION COMPATIBILITY LAYER (Ensures zero breaking changes)
export {
  verificationService;
  profileVerificationService;
  backgroundCheckService;
  VerificationServiceMigration as VerificationService;
  ProfileVerificationServiceMigration as ProfileVerificationService;
  BackgroundCheckServiceMigration as BackgroundCheckService;
  createVerificationService;
  createProfileVerificationService;
  createBackgroundCheckService;
  // Legacy types for compatibility;
  type LegacyVerificationSession;
  type LegacyVerificationResult;
  type LegacyVerificationType;
  type LegacyVerificationStatusOld;
  type BackgroundCheckPrice;
} from './unified/migrations/VerificationServiceMigration';

// ✅ LEGACY EXPORTS (For existing code compatibility - now from unified service)
export {
  VerificationSession as LegacyVerificationSessionType;
  VerificationResult as IdentityVerificationResult;
} from './unified/UnifiedVerificationService';

// Legacy types now exported from unified verification service;
// Note: profileVerificationService types are now consolidated into UnifiedVerificationService,
// Note: notificationService has been replaced by notificationUtils from the utils directory,
// Migration Note: Services are being migrated to the standardized pattern.,
// See src/docs/service-migration-guide.md and src/docs/service-migration-progress.md;
// for more information on the migration process.;

// = ===================;
// PAYMENT SERVICES - CONSOLIDATED UNIFIED ARCHITECTURE ✅;
// = ===================;

// ✅ UNIFIED PAYMENT SERVICE (Consolidates 4 services: payment + fraud + recovery + listing)
export {
  unifiedPaymentService;
  paymentService, // Backward compatibility alias;
  initializePaymentBridge;
  type PaymentStatusType;
  type PaymentMethodType;
  type Payment;
  type Subscription;
  type SubscriptionPlan;
  type SplitPayment;
  type SplitPaymentShare;
  type CreatePaymentParams;
  type CreateSubscriptionParams;
  type CreateSplitPaymentParams;
  type FraudDetectionParams;
  type FraudDetectionResult;
  type RecoveryOptions;
  type ListingPaymentRequest;
  type ListingPaymentResponse;
} from './unified/UnifiedPaymentService';

// ✅ SPECIALIZED PAYMENT SERVICES (Preserved for specific functionality)
export { providerPaymentService } from './providerPaymentService';
export { internationalPaymentService } from './internationalPaymentService';

export * from './profileBoostService';

// Phase 9: Room Listings + Roommate Matching Fixes,
export { roomListingStatusFix, RoomListingStatusFix } from './fixes/RoomListingStatusFix';
export { matchQueueEnhancement, MatchQueueEnhancement } from './fixes/MatchQueueEnhancement';

// Phase 10: Messaging System Fixes,
export { messagingTableFix, MessagingTableFix } from './fixes/MessagingTableFix';

// = ===================;
// MESSAGING SERVICES - UNIFIED ARCHITECTURE ✅;
// = ===================;

// ✅ UNIFIED MESSAGING SERVICE;
export { unifiedMessagingService } from './enhanced/UnifiedMessagingService';

// ✅ MESSAGING MIGRATION COMPATIBILITY LAYER (Ensures zero breaking changes)
export {
  createAgreementChatService;
  createEnhancedAgreementChatService;
  matchChatService;
  enhancedAgreementChatService;
  chatService;
  AgreementChatServiceMigration;
  EnhancedAgreementChatServiceMigration;
  MatchChatServiceMigration;
  ChatServiceMigration;
  ChatServiceCompat;
} from './unified/migrations/MessagingServiceMigration';

// Legacy service compatibility (for migration)
export { roomListingStatusFix as roomStatusService } from './fixes/RoomListingStatusFix';
export { matchQueueEnhancement as enhancedMatchQueue } from './fixes/MatchQueueEnhancement';
export { messagingTableFix as messageTableCompatibility } from './fixes/MessagingTableFix';
