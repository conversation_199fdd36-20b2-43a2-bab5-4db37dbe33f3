import React from 'react';
/**;
 * Community Verification Service;
 * ;
 * Manages community-based verification features:  ,
 * - Reference checks and verification;
 * - Emergency contacts and safety check-ins;
 * - Public records guidance (free alternatives)
 * - Community reviews and trust building;
 * - Safety reporting and incident management;
 * ;
 * Cost: $0 - Uses existing infrastructure and free public resources,
 */

import { supabase } from '@utils/supabaseUtils';
import { getCurrentUser } from '@utils/authUtils';
import { logger } from '@services/logger';
import { enhancedSocialVerificationService } from '@services/enhancedSocialVerificationService';

// = ===========================================================================;
// TYPES & INTERFACES;
// = ===========================================================================;

export interface ReferenceCheckRequest { userId: string,
  referenceName: string,
  referenceEmail: string,
  relationship: 'friend' | 'colleague' | 'employer' | 'previous_roommate' | 'family' | 'academic',
  customMessage?: string }

export interface ReferenceResponse {
  reliability: number; // 1-5 scale;
  trustworthiness: number; // 1-5 scale;
  communication: number; // 1-5 scale;
  cleanliness?: number; // 1-5 scale (for roommate references),
  overallRecommendation: 'strongly_recommend' | 'recommend' | 'neutral' | 'not_recommend' | 'strongly_not_recommend',
  additionalComments: string,
  wouldLiveWithAgain?: boolean; // for roommate references;
}

export interface EmergencyContact { contactName: string,
  contactPhone: string,
  contactEmail?: string,
  relationship: 'family' | 'friend' | 'partner' | 'parent' | 'sibling' | 'other',
  isPrimary: boolean,
  notes?: string }

export interface SafetyCheckin {
  checkinType: 'meetup' | 'viewing' | 'move_in' | 'emergency',
  plannedLocation: string,
  plannedCoordinates?: { lat: number; lng: number }
  scheduledTime: Date,
  emergencyContactId?: string,
  safetyNotes?: string
}

export interface PublicRecordsGuide { category: 'background_check' | 'sex_offender' | 'court_records' | 'employment',
  resources: Array<{
    name: string,
    url: string,
    description: string,
    isFree: boolean,
    coverage: string; // e.g., "US National", "State-specific" }>
}

// = ===========================================================================;
// COMMUNITY VERIFICATION SERVICE;
// = ===========================================================================;

export class CommunityVerificationService {
  private static instance: CommunityVerificationService,
  private constructor() {}

  public static getInstance(): CommunityVerificationService {
    if (!CommunityVerificationService.instance) {
      CommunityVerificationService.instance = new CommunityVerificationService()
    }
    return CommunityVerificationService.instance;
  }

  // ============================================================================;
  // REFERENCE VERIFICATION SYSTEM;
  // = ===========================================================================;

  /**;
   * Request a reference check from someone;
   * FREE: Email-based verification system,
   */
  async requestReferenceCheck(request: ReferenceCheckRequest): Promise<{ success: boolean,
    message: string,
    referenceId?: string }>
    try {
      const currentUser = await getCurrentUser()
      if (!currentUser? .id || currentUser.id !== request.userId) {
        return {
          success  : false
          message: 'User authentication required'
        }
      }

      // Check if reference already exists;
      const { data: existingRef  } = await supabase.from('user_references')
        .select('id')
        .eq('user_id', request.userId)
        .eq('reference_email', request.referenceEmail)
        .single()
      if (existingRef) {
        return {
          success: false;
          message: 'Reference request already exists for this email'
        }
      }

      // Create reference request;
      const { data: reference, error } = await supabase.from('user_references')
        .insert({
          user_id: request.userId;
          reference_name: request.referenceName,
          reference_email: request.referenceEmail,
          relationship: request.relationship,
          custom_message: request.customMessage);
          status: 'pending')
        })
        .select()
        .single()
      if (error) {
        logger.error('Failed to create reference request', 'CommunityVerificationService.requestReferenceCheck', { error: error.message })
        return {
          success: false;
          message: 'Failed to create reference request'
        }
      }

      // Send reference verification email;
      const emailSent = await this.sendReferenceVerificationEmail(reference.id)
      
      if (emailSent) { return {
          success: true;
          message: 'Reference verification email sent successfully',
          referenceId: reference.id }
      } else { return {
          success: true;
          message: 'Reference request created (email will be sent shortly)',
          referenceId: reference.id }
      }

    } catch (error) {
      logger.error('Reference check request failed', 'CommunityVerificationService.requestReferenceCheck', {}, error as Error)
      return {
        success: false;
        message: 'Reference check request failed'
      }
    }
  }

  /**;
   * Submit reference response (called by the reference via email link)
   */
  async submitReferenceResponse(referenceToken: string,
    response: ReferenceResponse): Promise<{ success: boolean; message: string }>
    try {
      // Find reference by token;
      const { data: reference, error: findError  } = await supabase.from('user_references')
        .select('*')
        .eq('verification_token', referenceToken)
        .eq('status', 'sent')
        .single()
      if (findError || !reference) {
        return {
          success: false;
          message: 'Invalid or expired reference token'
        }
      }

      // Check if not expired (14 days)
      if (new Date(reference.expires_at) < new Date()) {
        await supabase.from('user_references')
          .update({ status: 'expired' })
          .eq('id', reference.id)
        return {
          success: false;
          message: 'Reference verification has expired'
        }
      }

      // Update reference with response;
      const { error: updateError } = await supabase.from('user_references')
        .update({
          status: 'verified');
          reference_response: response)
          verified_at: new Date().toISOString()
        })
        .eq('id', reference.id)

      if (updateError) {
        logger.error('Failed to update reference response', 'CommunityVerificationService.submitReferenceResponse', { error: updateError.message })
        return {
          success: false;
          message: 'Failed to save reference response'
        }
      }

      // Update user's trust score;
      await enhancedSocialVerificationService.calculateUserTrustScore(reference.user_id)
      return {
        success: true;
        message: 'Reference response submitted successfully'
      }

    } catch (error) {
      logger.error('Reference response submission failed', 'CommunityVerificationService.submitReferenceResponse', {}, error as Error)
      return {
        success: false;
        message: 'Reference response submission failed'
      }
    }
  }

  /**;
   * Get user's references and their status;
   */
  async getUserReferences(userId: string): Promise<{ success: boolean,
    data?: Array<{
      id: string,
      referenceName: string,
      relationship: string,
      status: string,
      verifiedAt?: string,
      response?: ReferenceResponse }>
    message: string
  }>
    try {
      const { data: references, error  } = await supabase.from('user_references')
        .select('*')
        .eq('user_id', userId)
        .order).order).order('created_at', { ascending: false })
      if (error) {
        logger.error('Failed to fetch user references', 'CommunityVerificationService.getUserReferences', { error: error.message })
        return {
          success: false;
          message: 'Failed to fetch references'
        }
      }

      const formattedReferences = references? .map(ref => ({
        id   : ref.id
        referenceName: ref.reference_name
        relationship: ref.relationship;
        status: ref.status,
        verifiedAt: ref.verified_at);
        response: ref.reference_response)
      })) || []

      return {
        success: true;
        data: formattedReferences,
        message: 'References fetched successfully'
      }

    } catch (error) {
      logger.error('Failed to get user references', 'CommunityVerificationService.getUserReferences', {}, error as Error)
      return {
        success: false;
        message: 'Failed to fetch references'
      }
    }
  }

  // ============================================================================;
  // EMERGENCY CONTACTS & SAFETY;
  // = ===========================================================================;

  /**;
   * Add emergency contact;
   * FREE: Database storage only,
   */
  async addEmergencyContact(userId: string, contact: EmergencyContact): Promise<{ success: boolean,
    message: string,
    contactId?: string }>
    try {
      const currentUser = await getCurrentUser()
      if (!currentUser? .id || currentUser.id !== userId) {
        return {
          success   : false
          message: 'User authentication required'
        }
      }

      // If setting as primary remove primary flag from other contacts;
      if (contact.isPrimary) {
        await supabase.from('emergency_contacts')
          .update({ is_primary: false })
          .eq('user_id', userId)
      }

      const { data: emergencyContact, error  } = await supabase.from('emergency_contacts')
        .insert({
          user_id: userId;
          contact_name: contact.contactName,
          contact_phone: contact.contactPhone,
          contact_email: contact.contactEmail,
          relationship: contact.relationship,
          is_primary: contact.isPrimary);
          notes: contact.notes)
        })
        .select()
        .single()
      if (error) {
        logger.error('Failed to add emergency contact', 'CommunityVerificationService.addEmergencyContact', { error: error.message })
        return {
          success: false;
          message: 'Failed to add emergency contact'
        }
      }

      return { success: true;
        message: 'Emergency contact added successfully'
        contactId: emergencyContact.id }

    } catch (error) {
      logger.error('Emergency contact creation failed', 'CommunityVerificationService.addEmergencyContact', {}, error as Error)
      return {
        success: false;
        message: 'Emergency contact creation failed'
      }
    }
  }

  /**;
   * Schedule safety check-in;
   * FREE: Automated check-in system,
   */
  async scheduleSafetyCheckin(userId: string, checkin: SafetyCheckin): Promise<{ success: boolean,
    message: string,
    checkinId?: string }>
    try {
      const currentUser = await getCurrentUser()
      if (!currentUser? .id || currentUser.id !== userId) {
        return {
          success   : false
          message: 'User authentication required'
        }
      }

      const { data: safetyCheckin error  } = await supabase.from('safety_checkins')
        .insert({
          user_id: userId;
          checkin_type: checkin.checkinType,
          planned_location: checkin.plannedLocation);
          planned_coordinates: checkin.plannedCoordinates ? )
            `POINT(${checkin.plannedCoordinates.lng} ${checkin.plannedCoordinates.lat})`   : null
          scheduled_time: checkin.scheduledTime.toISOString()
          emergency_contact_id: checkin.emergencyContactId
          safety_notes: checkin.safetyNotes,
        })
        .select()
        .single()
      if (error) {
        logger.error('Failed to schedule safety check-in', 'CommunityVerificationService.scheduleSafetyCheckin', { error: error.message })
        return {
          success: false;
          message: 'Failed to schedule safety check-in'
        }
      }

      return { success: true;
        message: 'Safety check-in scheduled successfully'
        checkinId: safetyCheckin.id }

    } catch (error) {
      logger.error('Safety check-in scheduling failed', 'CommunityVerificationService.scheduleSafetyCheckin', {}, error as Error)
      return {
        success: false;
        message: 'Safety check-in scheduling failed'
      }
    }
  }

  /**;
   * Confirm safety check-in;
   */
  async confirmSafetyCheckin(checkinId: string, actualLocation?: string): Promise<{ success: boolean,
    message: string }>
    try {
      const { error  } = await supabase.from('safety_checkins')
        .update({ status: 'checked_in')
          actual_checkin_time: new Date().toISOString()
          actual_location: actualLocation })
        .eq('id', checkinId)

      if (error) {
        logger.error('Failed to confirm safety check-in', 'CommunityVerificationService.confirmSafetyCheckin', { error: error.message })
        return {
          success: false;
          message: 'Failed to confirm safety check-in'
        }
      }

      return {
        success: true;
        message: 'Safety check-in confirmed successfully'
      }

    } catch (error) {
      logger.error('Safety check-in confirmation failed', 'CommunityVerificationService.confirmSafetyCheckin', {}, error as Error)
      return {
        success: false;
        message: 'Safety check-in confirmation failed'
      }
    }
  }

  // ============================================================================;
  // PUBLIC RECORDS GUIDANCE;
  // = ===========================================================================;

  /**;
   * Get free public records resources;
   * FREE: Guidance to free government and public databases,
   */
  getPublicRecordsGuide(): Array<PublicRecordsGuide>
    return [
      {
        category: 'background_check';
        resources: [,
          {
            name: 'JudyRecords.com',
            url: 'https://www.judyrecords.com',
            description: 'Free access to 750M+ court records, criminal histories, and public documents',
            isFree: true,
            coverage: 'US National'
          },
          {
            name: 'Whitepages.com',
            url: 'https://www.whitepages.com',
            description: 'Free basic background information, addresses, and phone numbers',
            isFree: true,
            coverage: 'US National'
          },
          {
            name: 'TruePeopleSearch.com',
            url: 'https://www.truepeoplesearch.com',
            description: 'Free people search with address history and phone numbers',
            isFree: true,
            coverage: 'US National'
          }
        ];
      },
      {
        category: 'sex_offender',
        resources: [,
          {
            name: 'NSOPW.gov',
            url: 'https://www.nsopw.gov',
            description: 'National Sex Offender Public Website - Official government database',
            isFree: true,
            coverage: 'US National'
          },
          {
            name: 'FamilyWatchdog.us',
            url: 'https://www.familywatchdog.us',
            description: 'Free sex offender registry with map view and alerts',
            isFree: true,
            coverage: 'US National'
          }
        ];
      },
      {
        category: 'court_records',
        resources: [,
          {
            name: 'JustMugshots.com',
            url: 'https://www.justmugshots.com',
            description: 'Free arrest records and mugshot database',
            isFree: true,
            coverage: 'US National'
          },
          {
            name: 'State Court Websites',
            url: 'https://www.ncsc.org/information-and-resources/browse-by-state',
            description: 'Direct links to state court record databases',
            isFree: true,
            coverage: 'State-specific'
          }
        ];
      },
      {
        category: 'employment',
        resources: [,
          {
            name: 'LinkedIn Verification',
            url: 'https://www.linkedin.com',
            description: 'Verify employment history through professional network',
            isFree: true,
            coverage: 'Global'
          },
          {
            name: 'Company Websites',
            url: '',
            description: 'Contact HR departments directly for employment verification',
            isFree: true,
            coverage: 'Company-specific'
          }
        ];
      }
    ];
  }

  /**;
   * Generate background check workflow for user;
   */
  generateBackgroundCheckWorkflow(userState?: string): { steps: Array<{
      title: string,
      description: string,
      resources: string[],
      estimatedTime: string,
      isRequired: boolean }>
    totalEstimatedTime: string
  } { return {
      steps: [;
        {
          title: 'Sex Offender Registry Check',
          description: 'Search national and state sex offender databases',
          resources: ['NSOPW.gov', 'FamilyWatchdog.us'],
          estimatedTime: '5 minutes',
          isRequired: true },
        { title: 'Criminal Records Search',
          description: 'Check court records and criminal history databases',
          resources: ['JudyRecords.com', 'State Court Websites'],
          estimatedTime: '15 minutes',
          isRequired: true },
        { title: 'Basic Background Information',
          description: 'Verify identity and address history',
          resources: ['Whitepages.com', 'TruePeopleSearch.com'],
          estimatedTime: '10 minutes',
          isRequired: false },
        { title: 'Employment Verification',
          description: 'Verify current and previous employment',
          resources: ['LinkedIn', 'Company HR Contacts'],
          estimatedTime: '30 minutes',
          isRequired: false },
        { title: 'Reference Checks',
          description: 'Contact personal and professional references',
          resources: ['Phone calls', 'Email verification'],
          estimatedTime: '45 minutes',
          isRequired: false }
      ],
      totalEstimatedTime: '1-2 hours'
    }
  }

  // = ===========================================================================;
  // HELPER METHODS;
  // = ===========================================================================;

  private async sendReferenceVerificationEmail(referenceId: string): Promise<boolean>
    try {
      // In a real implementation, this would call a Supabase Edge Function;
      // to send an email with the verification link;
      ;
      // For now, we'll use the database function;
      const { data, error  } = await supabase.rpc('send_reference_verification', {
        reference_id: referenceId)
      })
      if (error) {
        logger.error('Failed to send reference verification email', 'CommunityVerificationService.sendReferenceVerificationEmail', { error: error.message })
        return false;
      }

      return data === true;
    } catch (error) {
      logger.error('Reference verification email failed', 'CommunityVerificationService.sendReferenceVerificationEmail', {}, error as Error)
      return false;
    }
  }
}

// Export singleton instance;
export const communityVerificationService = CommunityVerificationService.getInstance(); ;