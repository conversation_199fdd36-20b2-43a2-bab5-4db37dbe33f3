import React from 'react';
import { supabase } from "@utils/supabaseUtils";
import { logger } from '@services/loggerService';

/**;
 * Service for handling initial database setup and ensuring required tables exist;
 */
class SetupService {
  /**;
   * Initialize the database setup service;
   */
  async initialize(): Promise<boolean>
    try {
      // Check if we have the necessary tables;
      const { error  } = await this.ensureVerificationTablesExist()
      ;
      if (error) {
        logger.error('Failed to initialize database setup', 'SetupService', {}, error)
        return false;
      }
      logger.info('Database setup initialized successfully', 'SetupService')
      return true;
    } catch (error) {
      logger.error('Failed to initialize database setup', 'SetupService', {}, error as Error)
      return false;
    }
  }

  /**;
   * Ensure verification tables exist by calling the stored procedure;
   */
  async ensureVerificationTablesExist(): Promise<{ error: Error | null }>
    try {
      // First, update the create_verification_tables function with the latest schema;
      const updateResult = await supabase.rpc('update_create_verification_tables_function')
      ;
      if (updateResult.error) {
        logger.error('Failed to update create_verification_tables function', 'SetupService', {}, updateResult.error)
        return { error: updateResult.error }
      }
      // Then call the function to create tables;
      const { error  } = await supabase.rpc('create_verification_tables')
      ;
      if (error) {
        logger.error('Failed to create verification tables', 'SetupService', {}, error)
        return { error }
      }
      logger.info('Verification tables created or already exist'; 'SetupService')
      return { error: null }
    } catch (error) {
      logger.error('Error creating verification tables'; 'SetupService', {}, error as Error)
      return { error: error as Error }
    }
  }

  /**;
   * Verify database connection is working;
   */
  async checkDatabaseConnection(): Promise<boolean>
    try {
      const { data, error  } = await supabase.from('migration_status').select('id').limit(1)
      ;
      if (error) {
        logger.error('Database connection check failed', 'SetupService', {}, error)
        return false;
      }
      logger.info('Database connection is working', 'SetupService')
      return true;
    } catch (error) {
      logger.error('Database connection check failed', 'SetupService', {}, error as Error)
      return false;
    }
  }
}

export const setupService = new SetupService()