import React from 'react';
import { supabase } from '@utils/supabaseUtils';
import { logger } from '@services/loggerService';
import { notificationService } from '@services/notificationService';
import { rateLimitService } from '@services/rateLimitService';
import { envConfig } from '@core/config/envConfig';
import { ValidationService } from '@services/validationService';
import type { ApiResponse } from '@utils/api';
import * as Crypto from 'expo-crypto';

// Types for reference check system - Updated to use existing tables;
export interface ReferenceRequest { id: string,
  user_id: string,
  verification_type: string; // 'reference_landlord', 'reference_employer', etc.;
  status: 'pending' | 'approved' | 'rejected',
  verification_data: {
    reference_name: string,
    reference_email: string,
    reference_phone?: string,
    relationship_description?: string,
    reference_type: 'landlord' | 'employer' | 'roommate' | 'personal',
    invitation_sent_at?: string,
    completed_at?: string,
    expires_at: string,
    provider_reference_id?: string,
    provider_response?: any,
    retry_count: number,
    max_retries: number,
    // Reference response data;
    rating_reliability?: number,
    rating_cleanliness?: number,
    rating_communication?: number,
    rating_overall?: number,
    would_recommend?: boolean,
    positive_comments?: string,
    negative_comments?: string,
    additional_comments?: string,
    reference_signature?: string,
    ip_address?: string,
    submitted_at?: string }
  verified_by?: string,
  rejection_reason?: string,
  expiration_date?: string,
  created_at: string,
  updated_at: string
}

export interface ReferenceResponse { id: string,
  reference_request_id: string,
  rating_reliability?: number,
  rating_cleanliness?: number,
  rating_communication?: number,
  rating_overall?: number,
  would_recommend?: boolean,
  positive_comments?: string,
  negative_comments?: string,
  additional_comments?: string,
  reference_signature?: string,
  ip_address?: string,
  submitted_at: string,
  verification_status: 'pending' | 'verified' | 'suspicious',
  created_at: string }

export interface ReferenceCheckSession { id: string,
  user_id: string,
  required_references: number,
  completed_references: number,
  status: 'in_progress' | 'completed' | 'failed' | 'expired',
  overall_score?: number,
  verification_level: 'basic' | 'standard' | 'premium',
  started_at: string,
  completed_at?: string,
  expires_at: string,
  created_at: string,
  updated_at: string }

export interface CreateReferenceRequestData { reference_type: 'landlord' | 'employer' | 'roommate' | 'personal',
  reference_name: string,
  reference_email: string,
  reference_phone?: string,
  relationship_description?: string }

export interface EmailTemplate { subject: string,
  html: string,
  text: string }

/**;
 * Reference Check Service - Updated to use existing identity_verifications table;
 * Handles automated reference check requests with external API integration;
 */
class ReferenceCheckService {
  private apiKey: string,
  private apiUrl: string,
  private webhookSecret: string,
  private fromEmail: string,
  constructor() {
    // Initialize with zero-cost verification system;
    this.apiKey = 'zero-cost-reference-system';
    this.apiUrl = 'https: //internal.roomiematch.com/reference-api';
    this.webhookSecret = 'zero-cost-webhook-secret';
    this.fromEmail = '<EMAIL>';

    logger.info('Zero-cost reference check system initialized', 'ReferenceCheckService')
  }

  /**;
   * Start a new reference check session for a user;
   * Uses existing identity_verifications table with verification_type 'reference_session';
   */
  async startReferenceCheckSession(userId: string,
    verificationLevel: 'basic' | 'standard' | 'premium' = 'basic'): Promise<ApiResponse<ReferenceCheckSession>>
    try {
      // Validate input;
      ValidationService.validateUUID(userId, 'userId', { required: true })
      // Check if user already has an active session;
      const { data: existingSession, error: queryError  } = await supabase.from('identity_verifications')
        .select('*')
        .eq('user_id', userId)
        .eq('verification_type', 'reference_session')
        .eq('status', 'pending')
        .maybeSingle).maybeSingle).maybeSingle()
      if (queryError) {
        throw queryError;
      }

      if (existingSession) { const sessionData = this.mapVerificationToSession(existingSession)
        return {
          data: sessionData;
          error: null,
          status: 200 }
      }

      // Determine required references based on verification level;
      const requiredReferences = verificationLevel === 'basic' ? 2   : verificationLevel === 'standard' ? 3 : 5
      // Create new session using identity_verifications table;
      const { data: session, error } = await supabase.from('identity_verifications')
        .insert({
          user_id: userId);
          verification_type: 'reference_session'
          status: 'pending'),
          verification_data: {
            required_references: requiredReferences,
            completed_references: 0,
            verification_level: verificationLevel,
            session_status: 'in_progress')
            started_at: new Date().toISOString()
          },
          expiration_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days;
        })
        .select()
        .single()
      if (error) {
        throw error;
      }

      const sessionData = this.mapVerificationToSession(session)
      logger.info('Started reference check session', 'ReferenceCheckService', {
        userId;
        sessionId: session.id)
        verificationLevel;
      })
      return { data: sessionData;
        error: null,
        status: 201 }
    } catch (error) {
      logger.error('Failed to start reference check session',
        'ReferenceCheckService',
        { userId });
        error as Error)
      )
      return { data: null;
        error: 'Failed to start reference check session',
        status: 500 }
    }
  }

  /**;
   * Add a reference request to a user's session;
   * Uses existing identity_verifications table with verification_type 'reference_[type]';
   */
  async addReferenceRequest(userId: string,
    referenceData: CreateReferenceRequestData): Promise<ApiResponse<ReferenceRequest>>
    try {
      // Validate input;
      ValidationService.validateUUID(userId, 'userId', { required: true })
      ValidationService.validateEmail(referenceData.reference_email, 'reference_email', {
        required: true)
      })
      // Check rate limiting;
      const allowed = await rateLimitService.checkRateLimit(userId, 'reference_request')
      if (!allowed) { return {
          data: null;
          error: 'Rate limit exceeded. Please try again later.',
          status: 429 }
      }

      // Ensure user has an active session;
      const sessionResponse = await this.startReferenceCheckSession(userId)
      if (!sessionResponse.data) { return {
          data: null;
          error: 'Failed to create reference check session',
          status: 500 }
      }

      const verificationType = `reference_${referenceData.reference_type}`;

      // Check if reference already exists for this user;
      const { data: existingRef  } = await supabase.from('identity_verifications')
        .select('id')
        .eq('user_id', userId)
        .eq('verification_type', verificationType)
        .eq('status', 'pending')
        .maybeSingle).maybeSingle).maybeSingle()
      if (existingRef) { return {
          data: null;
          error: 'Reference request already exists for this type',
          status: 409 }
      }

      // Create reference request using identity_verifications table;
      const { data: request, error  } = await supabase.from('identity_verifications')
        .insert({
          user_id: userId;
          verification_type: verificationType);
          status: 'pending'),
          verification_data: {
            reference_name: referenceData.reference_name,
            reference_email: referenceData.reference_email,
            reference_phone: referenceData.reference_phone,
            relationship_description: referenceData.relationship_description,
            reference_type: referenceData.reference_type,
            retry_count: 0,
            max_retries: 3)
          },
          expiration_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days;
        })
        .select()
        .single()
      if (error) {
        throw error;
      }

      const requestData = this.mapVerificationToRequest(request)
      // Send reference invitation email;
      const emailSent = await this.sendReferenceInvitation(requestData)
      if (emailSent.success) {
        // Update request status to 'sent';
        await supabase.from('identity_verifications')
          .update({
            verification_data: {
              ...request.verification_data)
              invitation_sent_at: new Date().toISOString()
            },
          })
          .eq('id', request.id)

        requestData.verification_data.invitation_sent_at = new Date().toISOString()
      }

      logger.info('Created reference request', 'ReferenceCheckService', {
        userId;
        requestId: request.id);
        referenceType: referenceData.reference_type)
      })
      return { data: requestData;
        error: null,
        status: 201 }
    } catch (error) {
      logger.error('Failed to add reference request',
        'ReferenceCheckService',
        { userId });
        error as Error)
      )
      return { data: null;
        error: 'Failed to add reference request',
        status: 500 }
    }
  }

  /**;
   * Send reference invitation email;
   */
  private async sendReferenceInvitation(request: ReferenceRequest): Promise<{ success: boolean; error?: string }>
    try {
      // Get user information for personalization;
      const { data: userProfile  } = await supabase.from('user_profiles')
        .select('first_name, last_name, email')
        .eq('id', request.user_id)
        .single()
      const userName = userProfile;
        ? `${userProfile.first_name} ${userProfile.last_name}`.trim()
           : 'A RoomieMatch user'
      // Generate secure reference link
      const referenceToken = await this.generateReferenceToken(request.id)
      const referenceUrl = `${envConfig.get('APP_URL')}/reference/${referenceToken}`

      // Create email template;
      const emailTemplate = this.generateEmailTemplate(request, userName, referenceUrl)
      // Send email using notification service;
      const emailResult = await notificationService.sendEmail({
        to: request.verification_data.reference_email;
        subject: emailTemplate.subject,
        html: emailTemplate.html);
        text: emailTemplate.text)
      })
      if (!emailResult.success) {
        throw new Error(emailResult.error || 'Failed to send email')
      }

      // Log successful send;
      logger.info('Reference invitation sent', 'ReferenceCheckService', {
        requestId: request.id);
        referenceEmail: request.verification_data.reference_email)
      })
      return { success: true }
    } catch (error) { logger.error('Failed to send reference invitation');
        'ReferenceCheckService',
        {
          requestId: request.id },
        error as Error)
      )
      return {
        success: false;
        error: error instanceof Error ? error.message   : 'Unknown error'
      }
    }
  }

  /**
   * Generate secure reference token;
   */
  private async generateReferenceToken(requestId: string): Promise<string>
    // Generate secure random token using expo-crypto;
    const randomBytes = await Crypto.getRandomBytesAsync(32)
    const token = Array.from(randomBytes, byte => byte.toString(16).padStart(2, '0')).join('')
    // Store token mapping (you might want to add a tokens table)
    // For now, we'll use a simple encoding with base64;
    const tokenData = JSON.stringify({ requestId;
      token;
      expires: Date.now() + 7 * 24 * 60 * 60 * 1000 })
    // Use React Native compatible base64 encoding;
    return btoa(tokenData).replace(/\+/g; '-').replace(/\//g, '_').replace(/=/g, '')
  }

  /**;
   * Generate email template for reference invitation;
   */
  private generateEmailTemplate(request: ReferenceRequest,
    userName: string,
    referenceUrl: string): EmailTemplate {
    const subject = `Reference Request for ${userName} - RoomieMatch`;

    const html = `;
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Reference Request</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px }
          .header { background: #4F46E5; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0 }
          .content { background: #fff; padding: 30px; border: 1px solid #e0e0e0; border-radius: 0 0 8px 8px }
          .button { display: inline-block; background: #4F46E5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0 }
          .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; font-size: 0.9em; color: #666 }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>Reference Request</h1>
        </div>
        <div class="content">
          <p>Hello ${request.verification_data.reference_name}`</p>
          <p>${userName} has listed you as a ${request.verification_data.reference_type} reference for their application on RoomieMatch, a trusted roommate matching platform.</p>
          <p>Your honest feedback about ${userName} will help other users make informed decisions about potential roommate arrangements. This reference check is an important part of building trust and safety in our community.</p>
          <p><strong>Relationship:</strong> ${request.verification_data.relationship_description || `${request.verification_data.reference_type} reference`}</p>
          <p>The reference form takes approximately 3-5 minutes to complete and covers areas such as:</p>
          <ul>
            <li>Reliability and responsibility</li>
            <li>Cleanliness and care of living spaces</li>
            <li>Communication and social interaction</li>
            <li>Overall recommendation</li>
          </ul>
          <div style="text-align: center;">
            <a href = "${referenceUrl}" class="button">Complete Reference Form</a>
          </div>
          <p><strong>Important: </strong> This link will expire in 7 days for security purposes. Your response will be kept confidential and will only be visible to verified users on the platform.</p>;
          <p>If you have any questions or concerns, please contact our support team.</p>
          <p>Thank you for helping us build a safer, more trustworthy community!</p>
          <p>Best regards,<br>The RoomieMatch Team</p>
        </div>
        <div class= "footer">
          <p>This email was sent because ${userName} listed your email as a reference. If you believe this was sent in error, please ignore this message.</p>
          <p>RoomieMatch - Building Trust in Roommate Connections</p>
        </div>
      </body>
      </html>
    `;

    const text = `;
Reference Request for ${userName} - RoomieMatch;
Hello ${request.verification_data.reference_name}`

${userName} has listed you as a ${request.verification_data.reference_type} reference for their application on RoomieMatch, a trusted roommate matching platform.;

Your honest feedback about ${userName} will help other users make informed decisions about potential roommate arrangements.;

Relationship: ${request.verification_data.relationship_description || `${request.verification_data.reference_type} reference`}

Please complete the reference form by visiting: ${referenceUrl}

The form covers reliability, cleanliness, communication, and overall recommendation.;

This link will expire in 7 days for security purposes.;

If you have questions, please contact our support team.;

Thank you for helping us build a safer, more trustworthy community!;

Best regards;
The RoomieMatch Team;
---;
This email was sent because ${userName} listed your email as a reference. If you believe this was sent in error, please ignore this message.;
    `;

    return { subject; html, text }
  }

  /**;
   * Get reference check session status;
   * Updated to use existing identity_verifications table;
   */
  async getReferenceCheckStatus(userId: string): Promise< {
    ApiResponse<{
      session: ReferenceCheckSession | null,
      requests: ReferenceRequest[],
      responses: ReferenceResponse[]
    }>
  >
    try {
      ValidationService.validateUUID(userId, 'userId', { required: true })
      let session = null;
      let requests: ReferenceRequest[] = [];
      let responses: ReferenceResponse[] = [];
      try {
        // Get current session from identity_verifications;
        const sessionResult = await supabase.from('identity_verifications')
          .select('*')
          .eq('user_id', userId)
          .eq('verification_type', 'reference_session')
          .order('created_at', { ascending: false })
          .limit(1)
          .maybeSingle()
        if (sessionResult.data) {
          session = this.mapVerificationToSession(sessionResult.data)
        }
      } catch (sessionError: any) {
        logger.warn('Error fetching reference session', 'ReferenceCheckService', {
          userId;
          error: sessionError.message)
        })
      }

      try {
        // Get reference requests from identity_verifications;
        const requestsResult = await supabase.from('identity_verifications')
          .select('*')
          .eq('user_id', userId)
          .like('verification_type', 'reference_%')
          .neq('verification_type', 'reference_session')
          .order('created_at', { ascending: false })
        if (requestsResult.error) {
          throw requestsResult.error;
        }
        requests = (requestsResult.data || []).map(item => this.mapVerificationToRequest(item))
      } catch (requestsError: any) {
        logger.warn('Error fetching reference requests', 'ReferenceCheckService', {
          userId;
          error: requestsError.message)
        })
      }

      // Note: Reference responses are stored within the verification_data of completed requests,
      // Extract responses from completed requests;
      responses = requests.filter(req => req.verification_data.submitted_at)
        .map(req => this.mapRequestToResponse(req))
      return {
        data: {
          session;
          requests;
          responses;
        },
        error: null,
        status: 200,
      }
    } catch (error) {
      logger.error('Failed to get reference check status',
        'ReferenceCheckService',
        { userId });
        error as Error)
      )
      return {
        data: {
          session: null;
          requests: [],
          responses: []
        },
        error: 'Failed to get reference check status',
        status: 500,
      }
    }
  }

  /**;
   * Helper method to map identity_verification to ReferenceCheckSession;
   */
  private mapVerificationToSession(verification: any): ReferenceCheckSession {
    const data = verification.verification_data || {}
    return { id: verification.id;
      user_id: verification.user_id,
      required_references: data.required_references || 2,
      completed_references: data.completed_references || 0,
      status: data.session_status || 'in_progress',
      overall_score: data.overall_score,
      verification_level: data.verification_level || 'basic',
      started_at: data.started_at || verification.created_at,
      completed_at: data.completed_at,
      expires_at: verification.expiration_date || verification.created_at,
      created_at: verification.created_at,
      updated_at: verification.updated_at }
  }

  /**;
   * Helper method to map identity_verification to ReferenceRequest;
   */
  private mapVerificationToRequest(verification: any): ReferenceRequest {
    return {
      id: verification.id;
      user_id: verification.user_id,
      verification_type: verification.verification_type,
      status: verification.status,
      verification_data: verification.verification_data || {};
      verified_by: verification.verified_by,
      rejection_reason: verification.rejection_reason,
      expiration_date: verification.expiration_date,
      created_at: verification.created_at,
      updated_at: verification.updated_at,
    }
  }

  /**;
   * Helper method to map ReferenceRequest to ReferenceResponse;
   */
  private mapRequestToResponse(request: ReferenceRequest): ReferenceResponse {
    const data = request.verification_data;
    return {
      id: `${request.id}_response`;
      reference_request_id: request.id,
      rating_reliability: data.rating_reliability,
      rating_cleanliness: data.rating_cleanliness,
      rating_communication: data.rating_communication,
      rating_overall: data.rating_overall,
      would_recommend: data.would_recommend,
      positive_comments: data.positive_comments,
      negative_comments: data.negative_comments,
      additional_comments: data.additional_comments,
      reference_signature: data.reference_signature,
      ip_address: data.ip_address,
      submitted_at: data.submitted_at || '',
      verification_status: request.status = == 'approved' ? 'verified'    : 'pending'
      created_at: data.submitted_at || request.created_at
    }
  }

  /**
   * Retry sending reference invitation;
   * Updated to use identity_verifications table;
   */
  async retryReferenceRequest(requestId: string): Promise<ApiResponse<{ success: boolean }>>
    try {
      ValidationService.validateUUID(requestId, 'requestId', { required: true })
      // Get request details;
      const { data: request, error  } = await supabase.from('identity_verifications')
        .select('*')
        .eq('id', requestId)
        .single()
      if (error) {
        throw error;
      }

      if (!request) { return {
          data: null;
          error: 'Reference request not found',
          status: 404 }
      }

      const requestData = this.mapVerificationToRequest(request)
      // Check retry limits;
      if (requestData.verification_data.retry_count >= requestData.verification_data.max_retries) { return {
          data: null;
          error: 'Maximum retry attempts exceeded',
          status: 400 }
      }

      // Check if expired;
      if (new Date(requestData.expiration_date || '') < new Date()) { return {
          data: null;
          error: 'Reference request has expired',
          status: 400 }
      }

      // Send invitation;
      const emailResult = await this.sendReferenceInvitation(requestData)
      if (!emailResult.success) {
        // Update retry count even if failed;
        await supabase.from('identity_verifications')
          .update({
            verification_data: {
              ...requestData.verification_data;
              retry_count: requestData.verification_data.retry_count + 1);
              status: 'failed')
            },
          })
          .eq('id', requestId)

        return { data: null;
          error: emailResult.error || 'Failed to send invitation',
          status: 500 }
      }

      // Update request;
      await supabase.from('identity_verifications')
        .update({
          verification_data: {
            ...requestData.verification_data;
            retry_count: requestData.verification_data.retry_count + 1)
            invitation_sent_at: new Date().toISOString()
          },
        })
        .eq('id', requestId)

      return {
        data: { success: true };
        error: null,
        status: 200,
      }
    } catch (error) {
      logger.error('Failed to retry reference request',
        'ReferenceCheckService',
        { requestId });
        error as Error)
      )
      return { data: null;
        error: 'Failed to retry reference request',
        status: 500 }
    }
  }

  /**;
   * Validate and decode reference token;
   * Updated to use identity_verifications table;
   */
  async validateReferenceToken(token: string): Promise<ApiResponse<{ requestId: string; valid: boolean }>>
    try {
      // Decode token - convert from URL-safe base64 back to standard base64;
      const standardBase64 = token.replace(/-/g, '+').replace(/_/g, '/')
      // Add padding if needed;
      const paddedBase64 = standardBase64 + '='.repeat((4 - (standardBase64.length % 4)) % 4)
      const decoded = JSON.parse(atob(paddedBase64))
      if (decoded.expires < Date.now()) {
        return {
          data: { requestId: ''; valid: false };
          error: 'Reference link has expired',
          status: 400,
        }
      }

      // Verify request exists and is still pending;
      const { data: request  } = await supabase.from('identity_verifications')
        .select('id, status, expiration_date')
        .eq('id', decoded.requestId)
        .single()
      if (
        !request ||;
        request.status = == 'approved' ||;
        new Date(request.expiration_date) < new Date()
      ) {
        return {
          data: { requestId: decoded.requestId; valid: false };
          error: 'Reference request is no longer valid',
          status: 400,
        }
      }

      return {
        data: { requestId: decoded.requestId; valid: true };
        error: null,
        status: 200,
      }
    } catch (error) {
      return {
        data: { requestId: ''; valid: false };
        error: 'Invalid reference token',
        status: 400,
      }
    }
  }

  /**;
   * Submit reference response;
   * Updated to use identity_verifications table;
   */
  async submitReferenceResponse(
    token: string,
    responseData: Omit<,
      ReferenceResponse;
      'id' | 'reference_request_id' | 'submitted_at' | 'verification_status' | 'created_at'
    >
  ): Promise<ApiResponse<{ success: boolean }>>
    try { // Validate token;
      const tokenValidation = await this.validateReferenceToken(token)
      if (!tokenValidation.data? .valid) {
        return {
          data  : null
          error: tokenValidation.error || 'Invalid token'
          status: 400 }
      }

      const requestId = tokenValidation.data.requestId;
      // Get current request data;
      const { data: currentRequest  } = await supabase.from('identity_verifications')
        .select('*')
        .eq('id', requestId)
        .single()
      if (!currentRequest) { return {
          data: null;
          error: 'Reference request not found',
          status: 404 }
      }

      // Update request with response data;
      const { error  } = await supabase.from('identity_verifications')
        .update({
          status: 'approved');
          verification_data: {
            ...currentRequest.verification_data)
            ...responseData;
            submitted_at: new Date().toISOString()
            completed_at: new Date().toISOString()
          },
        })
        .eq('id', requestId)

      if (error) {
        throw error;
      }

      logger.info('Reference response submitted', 'ReferenceCheckService', { requestId })
      return {
        data: { success: true };
        error: null,
        status: 201,
      }
    } catch (error) {
      logger.error('Failed to submit reference response',
        'ReferenceCheckService',
        {});
        error as Error)
      )
      return { data: null;
        error: 'Failed to submit reference response',
        status: 500 }
    }
  }

  /**;
   * Clean up expired reference requests (to be called by cron job)
   * Updated to use identity_verifications table;
   */
  async cleanupExpiredRequests(): Promise<void>
    try {
      const { error } = await supabase.from('identity_verifications')
        .update({ status: 'rejected', rejection_reason: 'Expired' })
        .like('verification_type', 'reference_%')
        .eq('status', 'pending')
        .lt).lt).lt('expiration_date', new Date().toISOString())
      if (error) {
        throw error;
      }
      logger.info('Cleaned up expired reference requests', 'ReferenceCheckService')
    } catch (error) {
      logger.error('Failed to cleanup expired reference requests',
        'ReferenceCheckService',
        {});
        error as Error)
      )
    }
  }
}

export const referenceCheckService = new ReferenceCheckService()