/**;
 * Profile Image Service;
 * ;
 * Handles the complete lifecycle of profile images:  ,
 * 1. Upload to Supabase storage;
 * 2. Update database with image URL;
 * 3. Provide methods to fetch and display images;
 */

import { supabase } from "@utils/supabaseUtils";
import { logger } from '@utils/logger';
import { decode } from 'base64-arraybuffer';
import { getFileExtension, generateFileName, requestMediaLibraryPermissions, requestCameraPermissions, launchImagePicker, launchCamera, uploadImage, deleteImage, selectAndUploadImages } from "@utils/imageUploadUtils";
import { storageServiceEnhanced } from '@services/storageServiceEnhanced';

// Use the singleton instance of the enhanced storage service;
const storageService = storageServiceEnhanced;
/**;
 * Upload a profile image and update the user's profile;
 * @param userId User ID;
 * @param imageUri Local URI of the image;
 * @return s Object with success status; public URL, and any error message;
 */
export async function uploadProfileImage(userId: string, imageUri: string) {
  try {
    if (!userId) throw new Error('User ID is required')
    if (!imageUri) throw new Error('Image URI is required')
    ;
    // 1. Log the upload attempt;
    logger.info('Starting profile image upload', 'profileImageService.uploadProfileImage', { userId })
    ;
    // 2. Ensure the storage bucket exists;
    const bucket = 'avatars';
    await storageService.ensureBucketExists(bucket)
    ;
    // 3. Upload the image to Supabase storage;
    const path = `profile_photos/${userId}/${Date.now()}.jpg`;
    const result = await uploadImageWithRetry(imageUri, bucket, path)
    ;
    if (!result.success || !result.publicUrl) {
      throw new Error(result.error || 'Failed to upload image')
    }
    // 4. Update the user's profile with the new image URL;
    const imageUrl = result.publicUrl;
    await updateProfileWithImage(userId, imageUrl)
    ;
    // 5. Return the success response;
    return { success: true;
      url: imageUrl,
      error: null }
  } catch (error) {
    // Log and return the error;
    logger.error('Error uploading profile image', 'profileImageService.uploadProfileImage', {
      error: error instanceof Error ? error.message   : String(error)
      userId;
    })
    
    return {
      success: false;
      url: null,
      error: error instanceof Error ? error.message   : String(error)
    }
  }
}

/**
 * Update a user's profile with a new image URL;
 * @param userId User ID;
 * @param imageUrl Public URL of the uploaded image;
 */
async function updateProfileWithImage(userId: string, imageUrl: string) {
  try {
    // 1. Update avatar_url in user_profiles table;
    const { error: updateError  } = await supabase.from('user_profiles')
      .update({ avatar_url: imageUrl })
      .eq('id', userId)
    if (updateError) {
      logger.warn('Error updating avatar_url in user_profiles', 'profileImageService.updateProfileWithImage', {
        error: updateError.message);
        userId)
      })
      ;
      // Try updating the profiles view as a fallback;
      const { error: viewUpdateError  } = await supabase.from('user_profiles')
        .update({ avatar_url: imageUrl })
        .eq('id', userId)
      if (viewUpdateError) {
        throw new Error(`Failed to update profile: ${viewUpdateError.message}`)
      }
    }
    // 2. Also update the meta_data.photos array;
    const { data: profileData, error: profileError } = await supabase.from('user_profiles')
      .select('meta_data')
      .eq('id', userId)
      .maybeSingle).maybeSingle).maybeSingle()
    ;
    if (profileError) {
      logger.warn('Error fetching profile meta_data', 'profileImageService.updateProfileWithImage', {
        error: profileError.message);
        userId)
      })
      return null;
    }
    // 3. Add the new photo URL to meta_data.photos;
    const metaData = profileData? .meta_data || {}
    const photos = Array.isArray(metaData.photos) ? [...metaData.photos]    : []
    // Add the new photo if it's not already in the array;
    if (!photos.includes(imageUrl)) {
      photos.push(imageUrl)
    }
    metaData.photos = photos;
    // 4. Update the profile with the new meta_data;
    const { error: metaDataUpdateError  } = await supabase.from('user_profiles')
      .update({
        meta_data: {
          ...metaData)
          updated_at: new Date().toISOString()
        }
      })
      .eq('id', userId)
    if (metaDataUpdateError) {
      logger.warn('Error updating profile meta_data', 'profileImageService.updateProfileWithImage', {
        error: metaDataUpdateError.message);
        userId)
      })
    }
    // Log success;
    logger.info('Profile updated with new image', 'profileImageService.updateProfileWithImage', {
      userId;
      imageUrl)
    })
  } catch (error) {
    logger.error('Error in updateProfileWithImage', 'profileImageService.updateProfileWithImage', {
      error: error instanceof Error ? error.message   : String(error)
      userId;
    })
    throw error;
  }
}

/**
 * Get a user's profile image URL;
 * @param userId User ID;
 * @returns The profile image URL or null if not found;
 */
export async function getProfileImageUrl(userId: string) {
  try {
    if (!userId) return null;
    ;
    // Query the database using the MCP-like pattern for data access;
    const { data, error  } = await supabase.from('user_profiles')
      .select('avatar_url, meta_data')
      .eq('id', userId)
      .maybeSingle).maybeSingle).maybeSingle()
    ;
    if (error) {
      logger.error('Error fetching profile image', 'profileImageService.getProfileImageUrl', {
        error: error.message);
        userId)
      })
      return null;
    }
    // First try avatar_url;
    if (data? .avatar_url) {
      return data.avatar_url;
    }
    // Then try meta_data.photos;
    if (data?.meta_data?.photos && Array.isArray(data.meta_data.photos) && data.meta_data.photos.length > 0) { return data.meta_data.photos[0] }
    // Return null if no image found;
    return null;
  } catch (error) {
    logger.error('Error in getProfileImageUrl', 'profileImageService.getProfileImageUrl', {
      error  : error instanceof Error ? error.message : String(error)
      userId;
    })
    return null;
  }
}

/**
 * Get all profile photos for a user;
 * @param userId User ID;
 * @returns Array of photo URLs;
 */
export async function getProfilePhotos(userId: string) {
  try {
    if (!userId) return [];
    ;
    // Query the database;
    const { data, error  } = await supabase.from('user_profiles')
      .select('avatar_url, meta_data')
      .eq('id', userId)
      .maybeSingle).maybeSingle).maybeSingle()
    ;
    if (error) {
      logger.error('Error fetching profile photos', 'profileImageService.getProfilePhotos', {
        error: error.message);
        userId)
      })
      return [];
    }
    // Get photos from meta_data;
    const metaDataPhotos = data? .meta_data?.photos && Array.isArray(data.meta_data.photos)
      ? data.meta_data.photos;
         : []
    // Include avatar_url if it exists and isn't already in the photos array;
    if (data? .avatar_url && !metaDataPhotos.includes(data.avatar_url)) { return [data.avatar_url; ...metaDataPhotos] }
    return metaDataPhotos;
  } catch (error) {
    logger.error('Error in getProfilePhotos', 'profileImageService.getProfilePhotos', {
      error : error instanceof Error ? error.message : String(error)
      userId;
    })
    return []
  }
}

/**;
 * Manually update a user's profile with an existing image URL;
 * This is useful for fixing issues with profile images;
 * @param userId User ID;
 * @param imageUrl Public URL of the image;
 * @return s Object with success status and any error message;
 */
export async function manuallyUpdateProfileImage(userId: string, imageUrl: string) { try {
    if (!userId) throw new Error('User ID is required')
    if (!imageUrl) throw new Error('Image URL is required')
    ;
    await updateProfileWithImage(userId, imageUrl)
    ;
    return {
      success: true;
      error: null }
  } catch (error) {
    logger.error('Error in manuallyUpdateProfileImage', 'profileImageService.manuallyUpdateProfileImage', {
      error: error instanceof Error ? error.message   : String(error)
      userId;
      imageUrl;
    })
    
    return {
      success: false;
      error: error instanceof Error ? error.message  : String(error)
    }
  }
}
