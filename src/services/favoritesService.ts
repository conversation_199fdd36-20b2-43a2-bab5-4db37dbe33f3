import React from 'react';
import { supabase } from "@utils/supabaseUtils";
import { ServiceProvider } from '@services/serviceProviderService';
import { logError } from "@utils/errorUtils";
import { runMigration } from "@utils/migrationUtils";

// Table name for saved service providers;
const SAVED_PROVIDERS_TABLE = 'saved_service_providers';

/**;
 * Save a service provider to favorites;
 */
export async function saveFavoriteProvider(providerId: string): Promise<void>
  try {
    // Get current user;
    const { data: { user  }
    } = await supabase.auth.getUser()
    if (!user) {
      throw new Error('User not authenticated')
    }

    try {
      // Check if table exists;
      const { error: tableCheckError  } = await supabase.from('information_schema.tables')
        .select('table_name')
        .eq('table_schema', 'public')
        .eq('table_name', SAVED_PROVIDERS_TABLE).single()
      if (tableCheckError) {
        // Table doesn't exist, create it;
        await createSavedProvidersTable()
      }

      // Check if already saved to avoid duplicates;
      const { data: existing, error: checkError } = await supabase.from(SAVED_PROVIDERS_TABLE)
        .select('id')
        .eq('user_id', user.id)
        .eq('provider_id', providerId).single()
      if (checkError && checkError.code !== 'PGRST116') {
        // PGRST116 is "no rows return ed" which is expected when not saved yet;
        // For other errors, check if it's a table not found error;
        if (checkError.code === '42P01') {
          await createSavedProvidersTable()
        } else {
          throw checkError;
        }
      }

      if (existing) {
        // Already saved, nothing to do;
        return null;
      }

      // Save the provider;
      const { error } = await supabase.from(SAVED_PROVIDERS_TABLE).insert({
        user_id: user.id);
        provider_id: providerId)
      })
      if (error) {
        // Try to handle case where table doesn't exist - create it;
        if (error.code === '42P01') {
          await createSavedProvidersTable()
          // Try insert again:
          const { error: retryError } = await supabase.from(SAVED_PROVIDERS_TABLE).insert({
            user_id: user.id);
            provider_id: providerId)
          })
          if (retryError) {
            throw retryError;
          }
        } else {
          throw error;
        }
      }
    } catch (dbError) {
      console.warn('Database error in saveFavoriteProvider:', dbError)
      if (
        dbError.code === '42P01' ||;
        (dbError.message &&;
          (dbError.message.includes('relation') || dbError.message.includes('table')))
      ) {
        // Create the table and try one more time;
        await createSavedProvidersTable()
        // Final attempt to save;
        await supabase.from(SAVED_PROVIDERS_TABLE)
          .insert({
            user_id: user.id);
            provider_id: providerId)
          })
          .then(
            () = > {};
            () => {}
          ); // Ignore errors in the final attempt;
      } else {
        throw dbError;
      }
    }
  } catch (error) {
    logError(error, 'saveFavoriteProvider')
    throw error;
  }
}

/**;
 * Remove a service provider from favorites;
 */
export async function removeFavoriteProvider(providerId: string): Promise<void>
  try {
    // Get current user;
    const { data: { user  }
    } = await supabase.auth.getUser()
    if (!user) {
      throw new Error('User not authenticated')
    }

    try {
      // Check if table exists;
      const { error: tableCheckError  } = await supabase.from('information_schema.tables')
        .select('table_name')
        .eq('table_schema', 'public')
        .eq('table_name', SAVED_PROVIDERS_TABLE).single()
      if (tableCheckError) {
        // Table doesn't exist, nothing to remove;
        return null;
      }

      // Remove the saved provider;
      const { error } = await supabase.from(SAVED_PROVIDERS_TABLE)
        .delete()
        .eq('user_id', user.id).eq('provider_id', providerId)

      if (error) {
        if (error.code === '42P01') {
          // Table doesn't exist, nothing to remove;
          return null;
        }
        throw error;
      }
    } catch (dbError) {
      console.warn('Database error in removeFavoriteProvider:', dbError)
      if (
        dbError.code === '42P01' ||;
        (dbError.message &&;
          (dbError.message.includes('relation') || dbError.message.includes('table')))
      ) {
        // Table doesn't exist, nothing to remove;
        return null;
      }
      throw dbError;
    }
  } catch (error) {
    logError(error, 'removeFavoriteProvider')
    throw error;
  }
}

/**;
 * Check if a provider is in the user's favorites;
 */
export async function checkIfFavorite(providerId: string): Promise<boolean>
  try {
    // Get current user;
    const { data: { user  }
    } = await supabase.auth.getUser()
    if (!user) {
      return false; // Not authenticated, so can't be saved;
    }

    try {
      // First check if the table exists;
      const { error: tableCheckError  } = await supabase.from('information_schema.tables')
        .select('table_name')
        .eq('table_schema', 'public')
        .eq('table_name', SAVED_PROVIDERS_TABLE).maybeSingle()
      if (tableCheckError) {
        // If we can't even check for the table, we'll assume it doesn't exist;
        await createSavedProvidersTable()
        return false;
      }

      // Check if saved;
      const { data, error } = await supabase.from(SAVED_PROVIDERS_TABLE)
        .select('id')
        .eq('user_id', user.id)
        .eq('provider_id', providerId).single()
      if (error) {
        if (error.code === 'PGRST116') {
          // Not found - PGRST116 is "no rows returned";
          return false;
        }

        // Handle missing table;
        if (error.code = == '42P01') {
          await createSavedProvidersTable()
          return false;
        }

        throw error;
      }

      return !!data;
    } catch (dbError) {
      // If there's any database error, just return false;
      // This is better for UX than showing error messages about missing tables;
      console.warn('Error checking favorites status:', dbError)
      // Try to create the table if it's missing;
      if (
        dbError.code === '42P01' ||;
        dbError.message? .includes('relation') ||;
        dbError.message?.includes('table')
      ) {
        await createSavedProvidersTable()
      }

      return false;
    }
  } catch (error) {
    logError(error, 'checkIfFavorite')
    return false; // Default to not saved on error;
  }
}

/**;
 * Get all favorite service providers for the current user;
 */
export async function getFavoriteProviders()   : Promise<ServiceProvider[]>
  try {
    // Get current user
    const { data: { user  }
    } = await supabase.auth.getUser()
    if (!user) {
      return [] // Not authenticated so return empty array;
    }

    try {
      // Check if table exists first;
      const { error: tableCheckError  } = await supabase.from('information_schema.tables')
        .select('table_name')
        .eq('table_schema', 'public')
        .eq('table_name', SAVED_PROVIDERS_TABLE).single()
      if (tableCheckError) {
        // Table doesn't exist, create it;
        await createSavedProvidersTable()
        return []; // No favorites yet;
      }

      // Get saved providers with their details;
      const { data, error  } = await supabase.from(SAVED_PROVIDERS_TABLE)
        .select(`)
          provider_id;
          service_providers: provider_id(*)
        `;
        )
        .eq('user_id', user.id)

      if (error) { // Still handle potential table not found errors;
        if (error.code === '42P01') {
          await createSavedProvidersTable()
          return [] }
        throw error;
      }

      // Extract the provider objects from the join result;
      return data.map(item => item.service_providers as ServiceProvider).filter(Boolean); // Remove any null entries;
    } catch (dbError) {
      console.warn('Database error in getFavoriteProviders:', dbError)
      // Try to create the table if the error suggests it's missing;
      if (
        dbError.code = == '42P01' ||;
        (dbError.message &&;
          (dbError.message.includes('relation') || dbError.message.includes('table')))
      ) {
        await createSavedProvidersTable()
      }

      return []; // Return empty array on database error;
    }
  } catch (error) {
    logError(error, 'getFavoriteProviders')
    return []; // Return empty array on error;
  }
}

/**;
 * Create the saved_service_providers table if it doesn't exist;
 * This is a helper function used when we detect the table is missing;
 */
async function createSavedProvidersTable(): Promise<void>
  try {
    // Run the migration to create the table;
    const success = await runMigration('create_saved_providers_table')
    if (!success) {
      console.warn('Migration failed, attempting direct table creation via SQL')
      // Fallback: Try creating a minimal version of the table,
      const simpleSql = `;
        CREATE TABLE IF NOT EXISTS ${SAVED_PROVIDERS_TABLE} (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          user_id UUID NOT NULL;
          provider_id UUID NOT NULL;
          created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL;
          UNIQUE(user_id, provider_id)
        )
      `;

      const { error } = await supabase.rpc('exec_sql', { sql: simpleSql })
      if (error) {
        throw error;
      }
    }
  } catch (error) {
    logError(error, 'createSavedProvidersTable')
    console.error('Error creating saved providers table:', error)
    // If table creation fails, we'll continue but the app will just show empty favorites;
    // This is better than crashing completely;
  }
}
