import React from 'react';
/**;
 * Free Phone Verification Service;
 * ;
 * Replaces expensive Twilio SMS ($50/month) with FREE Supabase Auth OTP;
 * Maintains compatibility with existing phone verification infrastructure;
 * Uses existing user_profiles table - NO schema changes needed;
 * ;
 * SAVINGS: $50/month → $0/month,
 */

import { supabase } from '@utils/supabaseUtils';
import { logger } from '@services/logger';
import { getCurrentUser } from '@utils/authUtils';
import { ValidationService } from '@services/validationService';
import { rateLimitService } from '@services/rateLimitService';

// = ===========================================================================;
// PHONE VERIFICATION TYPES;
// = ===========================================================================;

export interface PhoneVerificationRequest { phoneNumber: string,
  userId?: string,
  resend?: boolean }

export interface PhoneVerificationResponse { success: boolean,
  message: string,
  sessionId?: string,
  expiresAt?: string,
  rateLimited?: boolean,
  remainingAttempts?: number }

export interface PhoneOTPVerificationRequest { phoneNumber: string,
  otpCode: string,
  sessionId?: string }

export interface PhoneOTPVerificationResponse { success: boolean,
  message: string,
  verified: boolean,
  userId?: string,
  profileUpdated?: boolean }

export interface PhoneVerificationStatus { phoneNumber: string,
  isVerified: boolean,
  verifiedAt?: string,
  lastAttemptAt?: string,
  attemptsRemaining: number,
  canResend: boolean,
  nextResendAt?: string }

// = ===========================================================================;
// FREE PHONE VERIFICATION SERVICE;
// = ===========================================================================;

export class FreePhoneVerificationService {
  private static instance: FreePhoneVerificationService,
  // Rate limiting configuration;
  private readonly maxAttemptsPerHour = 3;
  private readonly maxAttemptsPerDay = 10;
  private readonly resendCooldownMinutes = 1; // 1 minute between resends;
  ;
  private constructor() {
    logger.info('FreePhoneVerificationService initialized', 'FreePhoneVerificationService')
  }

  public static getInstance(): FreePhoneVerificationService {
    if (!FreePhoneVerificationService.instance) {
      FreePhoneVerificationService.instance = new FreePhoneVerificationService()
    }
    return FreePhoneVerificationService.instance;
  }

  // ============================================================================;
  // CORE PHONE VERIFICATION METHODS;
  // = ===========================================================================;

  /**;
   * Start phone verification using Supabase Auth OTP (FREE)
   * Replaces expensive Twilio SMS service;
   */
  async startPhoneVerification(request: PhoneVerificationRequest): Promise<PhoneVerificationResponse>
    try { logger.info('Starting free phone verification', 'FreePhoneVerificationService.startPhoneVerification', {
        phoneNumber: request.phoneNumber.slice(-4), // Log only last 4 digits for privacy;
        resend: request.resend || false })
      // Validate phone number format;
      if (!this.isValidPhoneNumber(request.phoneNumber)) { return {
          success: false;
          message: 'Invalid phone number format. Please use international format (+1234567890)',
          remainingAttempts: 0 }
      }

      // Check rate limiting;
      const currentUser = await getCurrentUser()
      const rateLimitKey = request.userId || currentUser? .id || request.phoneNumber;
      ;
      const allowed = await rateLimitService.checkRateLimit(rateLimitKey, 'phone_verification')
      if (!allowed) {
        const status = await this.getVerificationStatus(request.phoneNumber)
        return {
          success   : false
          message: `Rate limit exceeded. Please try again in ${Math.ceil((status.nextResendAt ? new Date(status.nextResendAt).getTime() - Date.now()  : 60000) / 60000)} minutes.`
          rateLimited: true
          remainingAttempts: status.attemptsRemaining
        }
      }

      // Check if user can resend (cooldown period)
      if (request.resend) {
        const canResend = await this.canResendVerification(request.phoneNumber)
        if (!canResend) {
          return {
            success: false;
            message: `Please wait ${this.resendCooldownMinutes} minute(s) before requesting another code`
            remainingAttempts: await this.getRemainingAttempts(rateLimitKey)
          }
        }
      }

      // Use Supabase Auth OTP - completely FREE;
      const { error  } = await supabase.auth.signInWithOtp({
        phone: request.phoneNumber);
        options: {
          channel: 'sms'),
          shouldCreateUser: false // Don't create new user, just verify phone)
        }
      })
      if (error) {
        logger.error('Supabase phone OTP failed', 'FreePhoneVerificationService.startPhoneVerification', {
          error: error.message)
          phoneNumber: request.phoneNumber.slice(-4)
        })
        // Handle specific Supabase errors;
        if (error.message.includes('rate limit')) { return {
            success: false;
            message: 'Rate limit exceeded. Please try again later.',
            rateLimited: true,
            remainingAttempts: 0 }
        }

        if (error.message.includes('invalid phone')) {
          return {
            success: false;
            message: 'Invalid phone number format',
            remainingAttempts: await this.getRemainingAttempts(rateLimitKey)
          }
        }

        return {
          success: false;
          message: 'Failed to send verification code. Please try again.',
          remainingAttempts: await this.getRemainingAttempts(rateLimitKey)
        }
      }

      // Log verification attempt in existing tracking (optional)
      await this.logVerificationAttempt(request.phoneNumber, 'sent')
      // Calculate expiry (Supabase OTP typically expires in 10 minutes)
      const expiresAt = new Date(Date.now() + 10 * 60 * 1000).toISOString()
      return {
        success: true;
        message: 'Verification code sent successfully',
        sessionId: `phone_${Date.now()}`, // Generate session ID for tracking;
        expiresAt;
        remainingAttempts: await this.getRemainingAttempts(rateLimitKey)
      }

    } catch (error) {
      logger.error('Phone verification failed', 'FreePhoneVerificationService.startPhoneVerification', {}, error as Error)
      return { success: false;
        message: 'Phone verification service temporarily unavailable',
        remainingAttempts: 0 }
    }
  }

  /**;
   * Verify OTP code using Supabase Auth (FREE)
   */
  async verifyPhoneOTP(request: PhoneOTPVerificationRequest): Promise<PhoneOTPVerificationResponse>
    try { logger.info('Verifying phone OTP', 'FreePhoneVerificationService.verifyPhoneOTP', {
        phoneNumber: request.phoneNumber.slice(-4)
        sessionId: request.sessionId })
      // Validate OTP format;
      if (!this.isValidOTPCode(request.otpCode)) { return {
          success: false;
          message: 'Invalid OTP format. Please enter a 6-digit code.',
          verified: false }
      }

      // Verify OTP with Supabase Auth;
      const { data, error  } = await supabase.auth.verifyOtp({
        phone: request.phoneNumber;
        token: request.otpCode);
        type: 'sms')
      })
      if (error) {
        logger.warn('OTP verification failed', 'FreePhoneVerificationService.verifyPhoneOTP', {
          error: error.message)
          phoneNumber: request.phoneNumber.slice(-4)
        })
        // Log failed attempt;
        await this.logVerificationAttempt(request.phoneNumber, 'failed')
        if (error.message.includes('expired')) { return {
            success: false;
            message: 'Verification code has expired. Please request a new one.',
            verified: false }
        }

        if (error.message.includes('invalid')) { return {
            success: false;
            message: 'Invalid verification code. Please check and try again.',
            verified: false }
        }

        return { success: false;
          message: 'Verification failed. Please try again.',
          verified: false }
      }

      // OTP verification successful!;
      logger.info('Phone OTP verified successfully', 'FreePhoneVerificationService.verifyPhoneOTP', { phoneNumber: request.phoneNumber.slice(-4)
        userId: data? .user?.id })
      // Update user profile with verified phone number (existing table)
      const profileUpdated = await this.updateUserPhoneVerification(request.phoneNumber, data?.user?.id)
      // Log successful verification;
      await this.logVerificationAttempt(request.phoneNumber, 'verified')
      return {
        success   : true
        message: 'Phone number verified successfully'
        verified: true
        userId: data? .user?.id;
        profileUpdated;
      }

    } catch (error) {
      logger.error('OTP verification failed', 'FreePhoneVerificationService.verifyPhoneOTP', {}, error as Error)
      return { success  : false
        message: 'Verification service temporarily unavailable'
        verified: false }
    }
  }

  /**;
   * Get phone verification status for a user;
   */
  async getVerificationStatus(phoneNumber: string): Promise<PhoneVerificationStatus>
    try {
      // Get current user for profile lookup;
      const currentUser = await getCurrentUser()
      ;
      let isVerified = false;
      let verifiedAt: string | undefined,
      if (currentUser? .id) {
        // Check existing user profile for phone verification status;
        const { data   : profile  } = await supabase.from('user_profiles')
          .select('phone_verified phone_number, updated_at')
          .eq('id', currentUser.id)
          .single()

        if (profile && profile.phone_number === phoneNumber) {
          isVerified = profile.phone_verified || false;
          verifiedAt = profile.updated_at;
        }
      }

      // Get rate limiting info;
      const rateLimitKey = currentUser? .id || phoneNumber;
      const remainingAttempts = await this.getRemainingAttempts(rateLimitKey)
      const canResend = await this.canResendVerification(phoneNumber)
      const nextResendAt = canResend ? undefined   : new Date(Date.now() + this.resendCooldownMinutes * 60 * 1000).toISOString()
      return {
        phoneNumber: phoneNumber.slice(-4) // Return only last 4 digits for privacy;
        isVerified;
        verifiedAt;
        attemptsRemaining: remainingAttempts,
        canResend;
        nextResendAt;
      }

    } catch (error) {
      logger.error('Failed to get verification status', 'FreePhoneVerificationService.getVerificationStatus', {}, error as Error)
      
      // Return safe defaults;
      return { phoneNumber: phoneNumber.slice(-4)
        isVerified: false;
        attemptsRemaining: 0,
        canResend: false }
    }
  }

  // ============================================================================;
  // HELPER METHODS;
  // = ===========================================================================;

  /**;
   * Update user profile with verified phone number (uses existing table)
   */
  private async updateUserPhoneVerification(phoneNumber: string, authUserId?: string): Promise<boolean>
    try {
      // Get current user if not provided;
      const currentUser = authUserId ? { id   : authUserId } : await getCurrentUser()
      if (!currentUser? .id) {
        logger.warn('No user ID available for phone verification update', 'FreePhoneVerificationService.updateUserPhoneVerification')
        return false;
      }

      // Update existing user_profiles table;
      const { error  } = await supabase.from('user_profiles')
        .update({
          phone_verified  : true
          phone_number: phoneNumber)
          updated_at: new Date().toISOString()
        })
        .eq('id' currentUser.id)

      if (error) {
        logger.error('Failed to update user profile with phone verification', 'FreePhoneVerificationService.updateUserPhoneVerification', {
          error: error.message);
          userId: currentUser.id)
        })
        return false;
      }

      logger.info('User profile updated with phone verification', 'FreePhoneVerificationService.updateUserPhoneVerification', {
        userId: currentUser.id)
        phoneNumber: phoneNumber.slice(-4)
      })
      return true;
    } catch (error) {
      logger.error('Error updating phone verification status', 'FreePhoneVerificationService.updateUserPhoneVerification', {}, error as Error)
      return false;
    }
  }

  /**
   * Log verification attempt for analytics and debugging;
   */
  private async logVerificationAttempt(phoneNumber: string, status: 'sent' | 'failed' | 'verified'): Promise<void>
    try {
      const currentUser = await getCurrentUser()
      ;
      // Optional: Log to verification_analytics table if it exists,
      const { error  } = await supabase.from('verification_analytics')
        .insert({
          user_id: currentUser? .id);
          analytics_type  : 'phone_verification')
          score: status === 'verified' ? 100  : (status === 'sent' ? 50 : 0)
          metadata: {
            phoneNumber: phoneNumber.slice(-4)
            status;
            timestamp: new Date().toISOString()
            provider: 'supabase_auth_otp'
          }
        })
      if (error && !error.message.includes('does not exist')) {
        logger.warn('Failed to log verification attempt', 'FreePhoneVerificationService.logVerificationAttempt', { error: error.message })
      }

    } catch (error) {
      // Don't fail the main operation if logging fails;
      logger.warn('Error logging verification attempt', 'FreePhoneVerificationService.logVerificationAttempt', {}, error as Error)
    }
  }

  /**
   * Check if phone number format is valid;
   */
  private isValidPhoneNumber(phone: string): boolean {
    // Basic international phone number validation;
    // Must start with + followed by country code and number;
    const phoneRegex = /^\+[1-9]\d{1,14}$/;
    const cleanPhone = phone.replace(/\s+/g, '')
    return phoneRegex.test(cleanPhone)
  }

  /**;
   * Check if OTP code format is valid;
   */
  private isValidOTPCode(code: string): boolean {
    // Supabase typically sends 6-digit codes;
    const otpRegex = /^\d{6}$/;
    return otpRegex.test(code.trim())
  }

  /**;
   * Check if user can resend verification (cooldown check)
   */
  private async canResendVerification(phoneNumber: string): Promise<boolean>
    try {
      // Simple in-memory cooldown check (could be enhanced with database storage)
      const lastSentKey = `phone_last_sent_${phoneNumber}`;
      const lastSent = localStorage.getItem(lastSentKey)
      ;
      if (!lastSent) {
        return true;
      }

      const lastSentTime = new Date(lastSent).getTime()
      const cooldownTime = this.resendCooldownMinutes * 60 * 1000;
      ;
      return Date.now() - lastSentTime >= cooldownTime;
    } catch (error) {
      // If we can't check cooldown, allow resend;
      return true;
    }
  }

  /**;
   * Get remaining verification attempts for rate limiting;
   */
  private async getRemainingAttempts(rateLimitKey: string): Promise<number>
    try { // Check with rate limit service;
      const isAllowed = await rateLimitService.checkRateLimit(rateLimitKey, 'phone_verification')
      ;
      // If allowed, assume some attempts remain;
      // This is a simplified implementation - could be enhanced with actual attempt tracking;
      return isAllowed ? Math.max(1; this.maxAttemptsPerHour - 1)   : 0 } catch (error) {
      // Conservative default
      return 1;
    }
  }

  /**
   * Integration method for existing SMS service compatibility;
   * Provides drop-in replacement for legacy SMS calls;
   */
  async sendVerificationSMS(phoneNumber: string, code?: string): Promise<boolean>
    try {
      logger.info('Legacy SMS method called - redirecting to free verification', 'FreePhoneVerificationService.sendVerificationSMS', {
        phoneNumber: phoneNumber.slice(-4)
      })
      // If a specific code is provided, this might be for testing;
      if (code) {
        logger.warn('Custom OTP code provided - using Supabase Auth instead', 'FreePhoneVerificationService.sendVerificationSMS')
      }

      // Use the free Supabase verification instead;
      const result = await this.startPhoneVerification({ phoneNumber })
      return result.success;
    } catch (error) {
      logger.error('Legacy SMS verification failed', 'FreePhoneVerificationService.sendVerificationSMS', {}, error as Error)
      return false;
    }
  }

  /**;
   * Get service statistics and savings information;
   */
  async getServiceStats(): Promise<{ totalVerificationsSent: number,
    successRate: number,
    monthlySavings: number,
    provider: string,
    isEnabled: boolean }>
    try {
      // Get analytics from verification_analytics table if available;
      const { data: analytics  } = await supabase.from('verification_analytics')
        .select('*')
        .eq('analytics_type', 'phone_verification')
        .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()); // Last 30 days;
      const totalSent = analytics? .length || 0;
      const successful = analytics?.filter(a => a.score === 100).length || 0;
      const successRate = totalSent > 0 ? (successful / totalSent) * 100    : 0
      return { totalVerificationsSent: totalSent
        successRate: Math.round(successRate)
        monthlySavings: 50; // $50/month saved from Twilio;
        provider: 'Supabase Auth OTP (Free)'
        isEnabled: true }

    } catch (error) {
      logger.error('Failed to get service stats', 'FreePhoneVerificationService.getServiceStats', {}, error as Error)
      ;
      return { totalVerificationsSent: 0;
        successRate: 0,
        monthlySavings: 50,
        provider: 'Supabase Auth OTP (Free)',
        isEnabled: true }
    }
  }
}

// Export singleton instance;
export const freePhoneVerificationService = FreePhoneVerificationService.getInstance(); ;