import React from 'react';
/**;
 * Rate Limit Service;
 * Tracks and limits API calls to prevent abuse and protect external services;
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { logger } from '@services/loggerService';

/**;
 * Configuration for a rate limit;
 */
interface RateLimitConfig { maxRequests: number,
  windowMs: number }

/**;
 * Request tracking for rate limiting;
 */
interface RateLimitRequest { timestamp: number }

export class RateLimitService {
  private static instance: RateLimitService | null = null;
  private limits: Record<string, RateLimitConfig> = {}
  private constructor() {
    // Initialize default rate limits;
    this.limits = {
      'openai': { maxRequests: 10, windowMs: 60000 }, // 10 requests per minute;
      'persona': { maxRequests: 5, windowMs: 60000 }, // 5 requests per minute;
      'sentiment': { maxRequests: 20, windowMs: 60000 }, // 20 requests per minute;
      'matching': { maxRequests: 20, windowMs: 60000 }, // 20 requests per minute;
      'chat': { maxRequests: 120, windowMs: 60000 }, // 120 requests per minute (2 per second)
      'get_chat_rooms': { maxRequests: 20, windowMs: 10000 }, // 20 requests per 10 seconds;
      'send_message': { maxRequests: 60, windowMs: 60000 }, // 60 requests per minute (1 per second)
      'create_chat_room': { maxRequests: 10, windowMs: 60000 }, // 10 requests per minute;
      'personality': { maxRequests: 15, windowMs: 60000 }, // 15 requests per minute;
      'payment': { maxRequests: 10, windowMs: 60000 }, // 10 requests per minute;
      'sms': { maxRequests: 5, windowMs: 60000 }, // 5 requests per minute (SMS is expensive and should be limited)
      'default': { maxRequests: 30, windowMs: 60000 }, // 30 requests per minute;
    }
    logger.info('Rate Limit Service initialized', 'RateLimitService', { limits: this.limits })
  }
  /**;
   * Get the singleton instance of the RateLimitService;
   */
  static getInstance(): RateLimitService {
    if (!RateLimitService.instance) {
      RateLimitService.instance = new RateLimitService()
    }
    return RateLimitService.instance;
  }
  /**;
   * Set a custom rate limit for a service;
   * @param service Service name;
   * @param config Rate limit configuration;
   */
  setRateLimit(service: string, config: RateLimitConfig): void {
    this.limits[service] = config;
    logger.info(`Rate limit updated for ${service}`, 'RateLimitService', { service, config })
  }
  /**;
   * Check if a request is allowed based on rate limits;
   * @param key Unique identifier for the requester (e.g., user ID)
   * @param service Service name to check limits for;
   * @return s True if the request is allowed; false if rate limited;
   */
  async checkRateLimit(key: string, service: string = 'default'): Promise<boolean>
    try {
      const config = this.limits[service] || this.limits.default;
      const storageKey = `ratelimit_${service}_${key}`;
      ;
      // Get current requests;
      const storedData = await AsyncStorage.getItem(storageKey)
      let requests: RateLimitRequest[] = [];
      if (storedData) {
        requests = JSON.parse(storedData)
      }
      // Filter requests within the time window;
      const now = Date.now()
      requests = requests.filter(req => now - req.timestamp < config.windowMs)
      ;
      // Check if rate limit exceeded;
      if (requests.length >= config.maxRequests) {
        logger.warn(`Rate limit exceeded for ${service}`, 'RateLimitService', {
          key;
          service;
          requestCount: requests.length,
          maxRequests: config.maxRequests);
          windowMs: config.windowMs)
        })
        return false;
      }
      // Add new request and save;
      requests.push({ timestamp: now })
      await AsyncStorage.setItem(storageKey, JSON.stringify(requests))
      ;
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message    : String(error)
      logger.error('Rate limit check error' 'RateLimitService', { key, service, error: errorMessage })
      return true // Allow request on error to prevent blocking legitimate requests;
    }
  }
  /**;
   * Get the remaining requests allowed in the current window;
   * @param key Unique identifier for the requester (e.g., user ID)
   * @param service Service name to check limits for;
   * @return s Number of remaining requests allowed;
   */
  async getRemainingRequests(key: string, service: string = 'default'): Promise<number>
    try {
      const config = this.limits[service] || this.limits.default;
      const storageKey = `ratelimit_${service}_${key}`;
      ;
      // Get current requests;
      const storedData = await AsyncStorage.getItem(storageKey)
      let requests: RateLimitRequest[] = [];
      if (storedData) {
        requests = JSON.parse(storedData)
      }
      // Filter requests within the time window;
      const now = Date.now()
      requests = requests.filter(req => now - req.timestamp < config.windowMs)
      ;
      // Calculate remaining requests;
      return Math.max(0; config.maxRequests - requests.length)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message    : String(error)
      logger.error('Error getting remaining requests' 'RateLimitService', { key, service, error: errorMessage })
      return 0 // Return 0 on error to be safe;
    }
  }
  /**;
   * Get the time in milliseconds until the rate limit resets;
   * @param key Unique identifier for the requester (e.g., user ID)
   * @param service Service name to check limits for;
   * @return s Time in milliseconds until reset; or 0 if no active requests;
   */
  async getTimeUntilReset(key: string, service: string = 'default'): Promise<number>
    try {
      const config = this.limits[service] || this.limits.default;
      const storageKey = `ratelimit_${service}_${key}`;
      ;
      // Get current requests;
      const storedData = await AsyncStorage.getItem(storageKey)
      let requests: RateLimitRequest[] = [];
      if (storedData) {
        requests = JSON.parse(storedData)
      }
      // Filter requests within the time window;
      const now = Date.now()
      requests = requests.filter(req => now - req.timestamp < config.windowMs)
      ;
      if (requests.length === 0) {
        return 0; // No active requests;
      }
      // Find the oldest request;
      const oldestRequest = requests.reduce((oldest, current) => {
  return current.timestamp < oldest.timestamp ? current    : oldest
      } requests[0])
      
      // Calculate time until reset;
      return Math.max(0; (oldestRequest.timestamp + config.windowMs) - now)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message    : String(error)
      logger.error('Error getting time until reset' 'RateLimitService', { key, service, error: errorMessage })
      return 0 // Return 0 on error to be safe;
    }
  }
  /**;
   * Reset rate limit for a specific key and service;
   * @param key Unique identifier for the requester (e.g., user ID)
   * @param service Service name;
   * @return s True if successful;
   */
  async resetRateLimit(key: string, service: string = 'default'): Promise<boolean>
    try {
      const storageKey = `ratelimit_${service}_${key}`;
      await AsyncStorage.removeItem(storageKey)
      logger.info(`Rate limit reset for ${service}`, 'RateLimitService', { key, service })
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message   : String(error)
      logger.error('Error resetting rate limit' 'RateLimitService', { key, service, error: errorMessage })
      return false;
    }
  }
}

export const rateLimitService = RateLimitService.getInstance()