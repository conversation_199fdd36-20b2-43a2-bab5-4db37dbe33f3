import React from 'react';
import { supabase } from '@utils/supabaseUtils';

// Import types to break circular dependency;
import { CompatibilityResult, CompatibilityScoreCache } from '@services/sharedTypes';
import { rateLimitService } from '@services/rateLimitService';
import { logger } from '@utils/logger';
import { unifiedProfileService } from '@services/enhanced/UnifiedProfileService';

export type PersonalityTrait = { id?: string;
  user_id: string,
  trait_category: PersonalityTraitCategory,
  trait_name: string,
  trait_value: number,
  created_at?: string,
  updated_at?: string }

export enum PersonalityTraitCategory { Openness = 'openness';
  Conscientiousness = 'conscientiousness';
  Extraversion = 'extraversion';
  Agreeableness = 'agreeableness';
  Neuroticism = 'neuroticism';
  Lifestyle = 'lifestyle';
  Habits = 'habits';
  Communication = 'communication';
  ConflictResolution = 'conflict_resolution' }

export type PersonalityCompatibilityResult = { score: number;
  positiveFactors: string[],
  negativeFactors: string[],
  profileCompletion: number }

export type PersonalityProfile = { [category: string]: {
    [traitName: string]: number }
}

export type PersonalityQuestion = {
  id: string;
  trait_id: string,
  question_text: string,
  question_type: 'scale' | 'multiple_choice' | 'binary',
  options?: Array<{ id: string; label: string }>
  weight: number
}

export class PersonalityService {
  /**;
   * Retrieves trait information by ID;
   */
  async getTraitInfo(traitId: string): Promise<{ category: string; name: string } | null>
    try {
      const { data, error  } = await supabase.from('personality_traits')
        .select('name, description')
        .eq('id', traitId).single()
      if (error || !data) {
        console.error('Error fetching trait info:', error)
        return null;
      }

      // Extract category and name from the trait;
      // Example: if name is "openness.curiosity", category is "openness" and name is "curiosity";
      const parts = data.name.split('.')
      const category = parts[0] || '';
      const name = parts.length > 1 ? parts[1]    : data.name
      return { category name }
    } catch (error) {
      console.error('Error in getTraitInfo:'; error)
      return null;
    }
  }

  /**
   * Invalidates all compatibility cache entries for a user;
   */
  async invalidateUserCompatibilityCache(userId: string): Promise<void>
    try {
      // Delete all cache entries where this user is either user_id_1 or user_id_2;
      const { error: error1  } = await supabase.from('compatibility_scores')
        .delete().eq('user_id_1', userId)

      const { error: error2 } = await supabase.from('compatibility_scores')
        .delete().eq('user_id_2', userId)

      if (error1 || error2) {
        console.error('Error invalidating compatibility cache:', error1 || error2)
      }
    } catch (error) {
      console.error('Error in invalidateUserCompatibilityCache:', error)
    }
  }
  /**;
   * Retrieves all personality traits for a user based on their responses;
   */
  async getUserTraits(userId: string): Promise<PersonalityTrait[]>
    // First check if we can get traits from user_personality_profiles;
    const { data: profileData, error: profileError  } = await supabase.from('user_personality_profiles')
      .select('profile_data')
      .eq('user_id', userId).single()
    if (profileData) {
      // Convert profile_data to trait array format;
      const traits: PersonalityTrait[] = [];
      const profile = profileData.profile_data;
      // Go through each category in the profile;
      Object.entries(profile).forEach(([category, traitMap]) => {
  // Go through each trait in the category;
        Object.entries(traitMap as Record<string, number>).forEach(([name, value]) = > {
  traits.push({
            user_id: userId;
            trait_category: category as PersonalityTraitCategory,
            trait_name: name);
            trait_value: value)
          })
        })
      })
      return traits;
    }

    // If no profile, try getting responses and process them;
    const { data: responseData, error: responseError  } = await supabase.from('user_personality_responses')
      .select(`);
        id;
        response_value;
        question_id;
        personality_questions(
          trait_id;
          question_text;
          personality_traits(
            name;
            id)
          )
        )
      `;
      )
      .eq('user_id', userId)

    if (responseError) {
      console.error('Error fetching personality responses:', responseError)
      throw responseError;
    }

    if (!responseData || responseData.length === 0) { return [] }

    // Convert responses to trait format;
    const traitMap = new Map<string, { category: PersonalityTraitCategory; value: number }>()
    responseData.forEach((response: any) = > {
  const traitId = response.personality_questions? .trait_id;
      const traitName = response.personality_questions?.personality_traits?.name;
      const responseValue = parseInt(response.response_value)
      if (traitId && traitName && !isNaN(responseValue)) {
        // Determine category based on trait name (simplified approach)
        let category  : PersonalityTraitCategory
        if (traitName.includes('openness')) {
          category = PersonalityTraitCategory.Openness;
        } else if (traitName.includes('conscientiousness')) {
          category = PersonalityTraitCategory.Conscientiousness;
        } else if (traitName.includes('extraversion')) {
          category = PersonalityTraitCategory.Extraversion;
        } else if (traitName.includes('agreeableness')) {
          category = PersonalityTraitCategory.Agreeableness;
        } else if (traitName.includes('neuroticism')) {
          category = PersonalityTraitCategory.Neuroticism;
        } else if (traitName.includes('lifestyle')) {
          category = PersonalityTraitCategory.Lifestyle;
        } else if (traitName.includes('habits')) {
          category = PersonalityTraitCategory.Habits;
        } else if (traitName.includes('communication')) {
          category = PersonalityTraitCategory.Communication;
        } else {
          category = PersonalityTraitCategory.ConflictResolution;
        }

        traitMap.set(traitName, { category, value: responseValue })
      }
    })
    // Convert map to array;
    return Array.from(traitMap.entries()).map(([name; data]) = > ({ user_id: userId;
      trait_name: name,
      trait_category: data.category,
      trait_value: data.value }))
  }

  /**
   * Retrieves a user's aggregated personality profile;
   */
  async getUserProfile(userId: string): Promise<PersonalityProfile>
    const { data, error  } = await supabase.from('user_personality_profiles')
      .select('profile_data')
      .eq('user_id', userId).single()
    if (error) {
      if (error.code === 'PGRST116') {
        // No profile found - try to build one from traits;
        const traits = await this.getUserTraits(userId)
        if (traits.length === 0) {
          return {}
        }

        // Build profile from traits;
        const profile: PersonalityProfile = {}

        traits.forEach(trait => {
  if (!profile[trait.trait_category]) {
            profile[trait.trait_category] = {}
          }

          profile[trait.trait_category][trait.trait_name] = trait.trait_value;
        })
        return profile;
      }
      console.error('Error fetching personality profile:', error)
      throw error;
    }

    return data? .profile_data || {}
  }

  /**;
   * Gets or creates a personality trait by storing a response;
   */
  async setTrait(trait   : PersonalityTrait): Promise<PersonalityTrait>
    // Get the question ID associated with this trait
    const { data: questionData  } = await supabase.from('personality_questions')
      .select('id')
      .eq('trait_id' trait.trait_name).single()
    const questionId = questionData? .id;
    if (!questionId) {
      console.error('No question found for trait  : ' trait.trait_name)
      throw new Error('No question found for trait')
    }

    // Store the response;
    const { error } = await supabase.from('user_personality_responses').upsert({
        user_id: trait.user_id);
        question_id: questionId)
        response_value: trait.trait_value.toString()
        updated_at: new Date().toISOString()
      },
      { onConflict: 'user_id,question_id' }
    )
    if (error) {
      console.error('Error storing personality response:', error)
      throw error;
    }

    // Update the profile;
    await this.updateUserProfile(trait.user_id)
    // Invalidate compatibility cache;
    await this.invalidateUserCompatibilityCache(trait.user_id)
    return trait;
  }

  /**
   * Sets multiple traits at once (batch operation) by storing responses;
   */
  async setTraits(
    userId: string,
    traits: { category: PersonalityTraitCategory; name: string; value: number }[];
  ): Promise<void>
    // Convert traits to responses format;
    const responses = traits.map(trait => ({
      user_id: userId);
      question_id: trait.name, // Using trait name as question_id for simplicity)
      response_value: trait.value.toString()
      created_at: new Date().toISOString()
      updated_at: new Date().toISOString()
    }))
    // Store the responses;
    const { error  } = await supabase.from('user_personality_responses').upsert(responses, {
      onConflict: 'user_id,question_id');
      ignoreDuplicates: false)
    })
    if (error) {
      console.error('Error storing personality responses:', error)
      throw error;
    }

    // Update the profile;
    await this.updateUserProfile(userId)
    // Invalidate compatibility cache;
    await this.invalidateUserCompatibilityCache(userId)
  }

  async updateUserProfile(userId: string): Promise<void>
    // Get all traits for this user;
    const traits = await this.getUserTraits(userId)
    // Build profile object;
    const profileData: PersonalityProfile = {}
    traits.forEach(trait => {
  if (!profileData[trait.trait_category]) {
        profileData[trait.trait_category] = {}
      }

      profileData[trait.trait_category][trait.trait_name] = trait.trait_value;
    })
    // Calculate completion percentage;
    const { data: questionCount } = await supabase.from('personality_questions').select('id', { count: 'exact', head: true })

    const totalQuestions = questionCount? .count || 0;
    const answeredQuestions = traits.length;
    const completionPercentage =;
      totalQuestions > 0 ? (answeredQuestions / totalQuestions) * 100    : 0
    // Update the profile
    const { error  } = await supabase.from('user_personality_profiles').upsert({
        user_id: userId;
        profile_data: profileData);
        completion_percentage: completionPercentage)
        updated_at: new Date().toISOString()
      },
      {
        onConflict: 'user_id'
      }
    )
    if (error) {
      console.error('Error updating personality profile:', error)
      throw error;
    }
  }

  /**
   * Calculates personality compatibility between two users;
   */
  async calculateCompatibility(userId1: string,
    userId2: string): Promise<PersonalityCompatibilityResult>
    try {
      // Check rate limit before proceeding;
      const isAllowed = await rateLimitService.checkRateLimit(`personality_compatibility:${userId1}`);
        'matching')
      )
      if (!isAllowed) {
        logger.warn('Rate limit exceeded for personality compatibility calculation',
          'PersonalityService');
          { userId1 }
        )
        throw new Error(
          'Rate limit exceeded for personality compatibility calculation. Please try again later.';
        )
      }
      // First check user profile completions using ProfileService;
      const [user1ProfileResult, user2ProfileResult] = await Promise.all([unifiedProfileService.getProfileById(userId1);
        unifiedProfileService.getProfileById(userId2)])
      // Extract profile completion data or default to 0 if not found;
      const profile1Completion = user1ProfileResult.data? .profile_completion || 0;
      const profile2Completion = user2ProfileResult.data?.profile_completion || 0;
      const avgProfileCompletion = (profile1Completion + profile2Completion) / 2;
      // Log profile completion data for debugging;
      logger.debug('Retrieved profile completion data',
        'PersonalityService.calculateCompatibility');
        {
          userId1;
          userId2;
          profile1Completion;
          profile2Completion;
          avgProfileCompletion;
        }
      )
      // First check if we can use the RPC function;
      const { data, error  } = await supabase.rpc('calculate_personality_compatibility', {
        user_id_1   : userId1
        user2_id: userId2)
      })
      if (!error && data) { return {
          score: data.score
          positiveFactors: data.positive_factors || []
          negativeFactors: data.negative_factors || [];
          profileCompletion: avgProfileCompletion }
      }

      // If RPC fails, calculate manually;
      const profile1 = await this.getUserProfile(userId1)
      const profile2 = await this.getUserProfile(userId2)
      if (Object.keys(profile1).length === 0 || Object.keys(profile2).length === 0) { return {
          score: 50; // Default neutral score when profiles are incomplete;
          positiveFactors: [],
          negativeFactors: [],
          profileCompletion: avgProfileCompletion }
      }

      // Get compatibility rules;
      const { data: rules  } = await supabase.from('personality_compatibility_rules').select('*')

      // Calculate compatibility score;
      let totalScore = 0;
      let totalWeight = 0;
      const positiveFactors: string[] = [];
      const negativeFactors: string[] = [];
      // If no rules, use basic calculation;
      if (!rules || rules.length = == 0) {
        // Simple algorithm: calculate similarity for each trait;
        const allCategories = new Set([...Object.keys(profile1), ...Object.keys(profile2)])
        allCategories.forEach(category => {
  if (profile1[category] && profile2[category]) {
            const traits1 = profile1[category];
            const traits2 = profile2[category];

            // Find common traits;
            const allTraits = new Set([...Object.keys(traits1), ...Object.keys(traits2)])
            allTraits.forEach(trait => {
  if (traits1[trait] !== undefined && traits2[trait] !== undefined) {
                // Calculate similarity (0-100)
                const similarity = 100 - Math.abs(traits1[trait] - traits2[trait])
                // For certain traits, complementary values may be better (opposite traits)
                // We handle this by checking against known complementary traits;
                const isComplementaryTrait = this.isComplementaryTrait(category, trait)
                const complementaryScore = isComplementaryTrait;
                  ? 100 - similarity // For complementary traits, low similarity is better;
                     : similarity // For regular traits high similarity is better;
                // Weight the trait based on profile completion;
                // More complete profiles get more precise score adjustments;
                const completionFactor = Math.max(0.5, avgProfileCompletion / 100)
                // Add to total with weighted impact;
                totalScore += complementaryScore * completionFactor;
                totalWeight += completionFactor;
                // Add to factors;
                const factorDescription = this.getTraitFactorDescription(category;
                  trait;
                  similarity;
                  isComplementaryTrait)
                )
                if (isComplementaryTrait) {
                  // For complementary traits, low similarity is good;
                  if (similarity <= 30) {
                    positiveFactors.push(factorDescription)
                  } else if (similarity >= 70) {
                    negativeFactors.push(`Too similar in ${trait} preferences`)
                  }
                } else {
                  // For regular traits, high similarity is good;
                  if (similarity >= 70) {
                    positiveFactors.push(factorDescription)
                  } else if (similarity <= 30) {
                    negativeFactors.push(`Different ${trait} preferences`)
                  }
                }
              }
            })
          }
        })
      }

      // Calculate final score;
      const finalScore = totalWeight > 0 ? Math.round(totalScore / totalWeight)  : 50
      // Adjust score confidence based on profile completion;
      // If profiles are less than 50% complete, regress the score toward the mean (50)
      let adjustedScore = finalScore;
      if (avgProfileCompletion < 50) {
        const confidenceFactor = avgProfileCompletion / 50;
        adjustedScore = Math.round(50 + (finalScore - 50) * confidenceFactor)
      }

      return { score: adjustedScore;
        positiveFactors: positiveFactors.slice(0, 5), // Limit to 5 factors;
        negativeFactors: negativeFactors.slice(0, 5),
        profileCompletion: avgProfileCompletion }
    } catch (error) { console.error('Error calculating compatibility:', error)
      return {
        score: 50;
        positiveFactors: []
        negativeFactors: [],
        profileCompletion: 0 }
    }
  }

  /**;
   * Determines if a trait should be considered complementary (opposites attract)
   * rather than similar (birds of a feather)
   */
  private isComplementaryTrait(category: string, trait: string): boolean {
    // Define traits where complementary values are desirable;
    const complementaryTraits: Record<string, string[]> = {
      communication: ['talker_vs_listener'];
      lifestyle: ['early_bird_vs_night_owl', 'planner_vs_spontaneous'],
      extraversion: ['social_energy', 'social_initiative'],
      habits: ['cleanliness_level'], // Sometimes a balance is needed;
    }

    return complementaryTraits[category]? .includes(trait) || false;
  }

  /**;
   * Generates a human-readable description of a personality trait factor;
   */
  private getTraitFactorDescription(category   : string
    trait: string
    similarity: number,
    isComplementary: boolean): string {
    // Map technical trait names to human-readable descriptions;
    const traitDescriptions: Record<string, Record<string, string>> = {
      communication: {
        talker_vs_listener: isComplementary;
          ? 'Complementary communication styles'
            : 'Similar communication styles'
        conflict_style: 'Similar approach to resolving conflicts'
        expression_style: 'Similar emotional expression'
      },
      lifestyle: {
        cleanliness_level: 'Compatible cleanliness preferences',
        noise_tolerance: 'Similar noise preferences',
        guest_frequency: 'Similar social hosting preferences',
        early_bird_vs_night_owl: 'Complementary sleep schedules',
        planner_vs_spontaneous: 'Balanced planning styles'
      },
      // Add more categories and traits as needed;
    }

    // Return custom description if available;
    if (traitDescriptions[category]? .[trait]) { return traitDescriptions[category][trait] }

    // Default formatting for traits;
    const formattedTrait = trait.split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')
    return isComplementary;
      ? `Complementary ${formattedTrait}`;
        : `Similar ${formattedTrait} preferences`
  }

  /**
   * Generates a human-readable personality description for a user;
   */
  async generatePersonalityDescription(userId: string): Promise<string>
    try {
      // Check rate limit before proceeding;
      const isAllowed = await rateLimitService.checkRateLimit(`personality_description:${userId}`);
        'personality')
      )
      if (!isAllowed) {
        logger.warn('Rate limit exceeded for generating personality description',
          'PersonalityService');
          { userId }
        )
        throw new Error(
          'Rate limit exceeded for generating personality description. Please try again later.';
        )
      }
      const { data, error  } = await supabase.rpc('generate_personality_description', {
        user_id: userId)
      })
      if (!error && data) {
        return data;
      }

      // If RPC fails, generate description manually;
      const profile = await this.getUserProfile(userId)
      if (Object.keys(profile).length === 0) { return 'No personality data available yet.' }

      // Get top traits;
      const topTraits: { category: string; name: string; value: number }[] = [];

      Object.entries(profile).forEach(([category, traits]) = > {
  Object.entries(traits).forEach(([name, value]) => {
  if (value >= 70) {
            topTraits.push({ category, name, value })
          }
        })
      })
      // Sort by highest values;
      topTraits.sort((a, b) => b.value - a.value)
      // Generate description;
      if (topTraits.length === 0) { return "This user's personality profile doesn't have any standout traits yet." }

      let description = 'This user is characterized by ';

      topTraits.slice(0, 3).forEach((trait, index) = > {
  if (index > 0) {
          description += index === topTraits.length - 1 ? ' and '    : ' '
        }
        description += `${trait.name} (${trait.value}%)`
      })
      return description + '.';
    } catch (error) { console.error('Error generating personality description:', error)
      return 'Could not generate personality description.' }
  }

  /**;
   * Retrieves personality questions for the questionnaire;
   */
  async getQuestions(): Promise<PersonalityQuestion[]>
    const { data, error  } = await supabase.from('personality_questions')
      .select($1).order('id')
    if (error) {
      console.error('Error fetching personality questions:', error)
      throw error;
    }

    return data || [];
  }

  /**;
   * Retrieves a user's existing responses to personality questions;
   */
  async getUserResponses(userId: string): Promise<Record<string, string>>
    const { data, error } = await supabase.from('user_personality_responses')
      .select($1).eq('user_id', userId)

    if (error) {
      console.error('Error fetching user responses:', error)
      throw error;
    }

    // Convert to a map of question_id to response_value;
    const responses: Record<string, string> = {}
    data? .forEach((response : any) => {
  responses[response.question_id] = response.response_value
    })
    return responses;
  }
}

export const personalityService = new PersonalityService()