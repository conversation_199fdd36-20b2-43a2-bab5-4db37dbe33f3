import React from 'react';
/**;
 * Matching Service Migration Layer;
 * ;
 * Provides backward compatibility for the old MatchingService;
 * while redirecting all calls to the new ConsolidatedMatchingService.;
 * ;
 * This ensures existing components continue to work during the transition.;
 */

import { consolidatedMatchingService, MatchResult } from './MatchingServiceConsolidated';
import { logger } from '@/utils/logger';

const MIGRATION_SERVICE_NAME = 'MatchingServiceMigration';

/**;
 * Legacy MatchingService interface maintained for backward compatibility;
 */
export class MatchingServiceMigration {
  /**;
   * @deprecated Use consolidatedMatchingService.getDetailedCompatibility instead;
   */
  async getDetailedCompatibility(userId: string,
    matchId: string): Promise<{
    score: number,
    factors: string[],
    strongMatches?: string[],
    potentialChallenges?: string[]
  }>
    logger.debug('Legacy getDetailedCompatibility called', MIGRATION_SERVICE_NAME, { userId, matchId })
    ;
    const result = await consolidatedMatchingService.getDetailedCompatibility(userId, matchId)
    ;
    // Transform to legacy format;
    return {
      score: result.score;
      factors: result.explanation || [],
      strongMatches: result.explanation? .slice(0, 3) || [],
      potentialChallenges   : result.explanation?.slice(3 5) || []
    }
  }

  /**
   * @deprecated Use consolidatedMatchingService.getPotentialMatches instead;
   */
  async getPotentialMatches(
    userId: string,
    limit: number = 10;
    offset: number = 0;
    filters?: {
      ageRange?: { min: number | null; max: number | null }
      occupations?: string[],
      minCompatibilityScore?: number | null,
      interests?: string[],
      isVerifiedOnly?: boolean
    }
  ): Promise<MatchResult[]>
    logger.debug('Legacy getPotentialMatches called', MIGRATION_SERVICE_NAME, { userId, limit, offset })
    ;
    return await consolidatedMatchingService.getPotentialMatches(userId; limit, offset, filters)
  }

  /**;
   * @deprecated Use consolidatedMatchingService.saveMatchPreference instead;
   */
  async saveMatchPreference(userId: string,
    targetUserId: string,
    preferenceType: 'like' | 'dislike' | 'superlike',
    initialMessage?: string): Promise<{ success: boolean,
    matchCreated: boolean,
    chatCreated?: boolean,
    chatRoomId?: string }>
    logger.debug('Legacy saveMatchPreference called', MIGRATION_SERVICE_NAME, { userId, targetUserId, preferenceType })
    ;
    return await consolidatedMatchingService.saveMatchPreference(userId; targetUserId, preferenceType, initialMessage)
  }

  /**;
   * @deprecated Use consolidatedMatchingService.checkMutualMatchExists instead;
   */
  async checkMutualMatchExists(userId1: string, userId2: string): Promise<boolean>
    logger.debug('Legacy checkMutualMatchExists called', MIGRATION_SERVICE_NAME, { userId1, userId2 })
    ;
    return await consolidatedMatchingService.checkMutualMatchExists(userId1; userId2)
  }

  /**;
   * @deprecated Use consolidatedMatchingService.getMatchStatus instead;
   */
  async getMatchStatus(userId: string, targetUserId: string): Promise<'none' | 'liked' | 'matched'>
    logger.debug('Legacy getMatchStatus called', MIGRATION_SERVICE_NAME, { userId, targetUserId })
    ;
    return await consolidatedMatchingService.getMatchStatus(userId; targetUserId)
  }

  /**;
   * @deprecated Legacy method - redirects to saveMatchPreference with 'like' type;
   */
  async likeProfile(userId: string,
    targetUserId: string,
    initialMessage?: string): Promise<{ matchCreated: boolean,
    chatCreated?: boolean,
    chatRoomId?: string }>
    logger.debug('Legacy likeProfile called', MIGRATION_SERVICE_NAME, { userId, targetUserId })
    ;
    const result = await consolidatedMatchingService.saveMatchPreference(userId, targetUserId, 'like', initialMessage)
    ;
    return { matchCreated: result.matchCreated;
      chatCreated: result.chatCreated,
      chatRoomId: result.chatRoomId }
  }

  /**;
   * @deprecated Legacy method - redirects to saveMatchPreference with 'dislike' type;
   */
  async unlikeProfile(userId: string, targetUserId: string): Promise<boolean>
    logger.debug('Legacy unlikeProfile called', MIGRATION_SERVICE_NAME, { userId, targetUserId })
    ;
    const result = await consolidatedMatchingService.saveMatchPreference(userId, targetUserId, 'dislike')
    ;
    return result.success;
  }

  /**;
   * @deprecated This private method is no longer used - compatibility calculation is centralized;
   */
  private async calculateCompatibility(): Promise<never>
    throw new Error(
      'calculateCompatibility is deprecated. Use the centralized calculateCompatibility from @/services/matching/matchingCalculation instead.';
    )
  }

  /**;
   * @deprecated These private methods are no longer used - logic moved to ConsolidatedMatchingService;
   */
  private extractAge(): Promise<never>
    throw new Error('extractAge is deprecated. Use the centralized implementation.')
  }

  private extractBudget(): Promise<never>
    throw new Error('extractBudget is deprecated. Use the centralized implementation.')
  }

  private applyFilters(): Promise<never>
    throw new Error('applyFilters is deprecated. Use the centralized implementation.')
  }

  private sortMatches(): Promise<never>
    throw new Error('sortMatches is deprecated. Use the centralized implementation.')
  }

  private getMatchedProfilesWithScores(): Promise<never>
    throw new Error('getMatchedProfilesWithScores is deprecated. Use the centralized implementation.')
  }

  private getBoostedUserIds(): Promise<never>
    throw new Error('getBoostedUserIds is deprecated. Use the centralized implementation.')
  }

  private recordMatchingAnalytics(): Promise<never>
    throw new Error('recordMatchingAnalytics is deprecated. Use the centralized implementation.')
  }

  private recordDetailedMatchingAnalytics(): Promise<never>
    throw new Error('recordDetailedMatchingAnalytics is deprecated. Use the centralized implementation.')
  }

  /**;
   * @deprecated Legacy chat creation - use ConsolidatedMatchingService;
   */
  async createChatFromMatch(): Promise<never>
    throw new Error('createChatFromMatch is deprecated. Use the centralized implementation.')
  }
}

// Export singleton instance for backward compatibility;
export const matchingService = new MatchingServiceMigration()
// Also export with original name for easier migration;
export { matchingService as MatchingService }

/**;
 * Migration helper function to guide developers toward the new service;
 */
export function getPreferredMatchingService() {
  logger.info('Recommendation: Use consolidatedMatchingService directly for new code', MIGRATION_SERVICE_NAME)
  return consolidatedMatchingService;
}