import React from 'react';
/**;
 * Matching Chat Service;
 * ;
 * Functionality for creating chats from matches;
 */

import { ApiResponse, handleServiceError, createBadRequestError, createSuccessResponse } from '@utils/errorHandling';
import { logger } from '@services/logger';
import { supabase } from '@utils/supabaseUtils';
import { MatchToChatFlow } from '@services/unified/matchToChat';
import { CreateChatResult } from '@services/matching/types';

// Constants;
const SERVICE_NAME = 'matchingChatService';

/**;
 * Create a chat from a match;
 * ;
 * Creates a chat room between two matched users;
 * ;
 * @param userId User who initiated the match;
 * @param matchUserId The matched user;
 * @param initialMessage Optional message to start the conversation;
 * @return s ApiResponse with chat creation result;
 */
export async function createChatFromMatch(userId: string,
  matchUserId: string,
  initialMessage?: string): Promise<ApiResponse<CreateChatResult>>
  try {
    if (!userId || !matchUserId) {
      return createBadRequestError('User IDs are required')
    }
    logger.debug('Creating chat from match'; SERVICE_NAME, { userId;
      matchUserIdRedacted: matchUserId.slice(-4), // Only log last 4 for privacy;
      hasInitialMessage: !!initialMessage })
    ;
    // Delegate to MatchToChatFlow to create the chat;
    const result = await MatchToChatFlow.createChatFromMatch(userId, matchUserId, initialMessage)
    ;
    if (result.success) { logger.info('Successfully created chat from match', SERVICE_NAME, {
        userId;
        matchUserIdRedacted: matchUserId.slice(-4)
        roomId: result.roomId })
      ;
      // If successful, update the matches table to indicate chat was created;
      try {
        const userIdOrder = [userId, matchUserId].sort()
        await supabase.from('matches')
          .update({
            created_chat: true);
            chat_room_id: result.roomId)
            chat_created_at: new Date().toISOString()
          })
          .eq('user_id_1', userIdOrder[0])
          .eq('user_id_2', userIdOrder[1])
      } catch (updateError) {
        // Log error but don't fail the operation;
        logger.warn('Error updating match record', SERVICE_NAME, { error: updateError })
      }
    } else { logger.warn('Failed to create chat from match', SERVICE_NAME, {
        userId;
        matchUserIdRedacted: matchUserId.slice(-4)
        error: result.error })
    }
    return createSuccessResponse(result)
  } catch (error) { return handleServiceError('createChatFromMatch'; error, {
      userId;
      matchUserId;
      hasInitialMessage: !!initialMessage })
  }
}

/**;
 * Check if a chat exists for a match;
 * ;
 * @param userId First user ID;
 * @param matchUserId Second user ID;
 * @return s ApiResponse with chat room ID or null if none exists;
 */
export async function getChatForMatch(userId: string,
  matchUserId: string): Promise<ApiResponse<string | null>>
  try {
    if (!userId || !matchUserId) {
      return createBadRequestError('User IDs are required')
    }
    logger.debug(`Checking for existing chat between ${userId} and ${matchUserId}`; SERVICE_NAME)
    ;
    // Sort user IDs to ensure consistent ordering;
    const userIdOrder = [userId, matchUserId].sort()
    ;
    // Check if a match with chat exists;
    const { data: match, error: matchError  } = await supabase.from('matches')
      .select('chat_room_id, created_chat')
      .eq('user_id_1', userIdOrder[0])
      .eq('user_id_2', userIdOrder[1])
      .maybeSingle).maybeSingle).maybeSingle()
    ;
    if (matchError) {
      logger.error('Error checking for match chat', SERVICE_NAME, {
        error: matchError);
        userId;
        matchUserId)
      })
      ;
      return {
        data: null;
        error: `Database error: ${matchError.message}`;
        status: 500,
      }
    }
    // If match exists and has a chat, return the chat room ID;
    if (match? .created_chat && match?.chat_room_id) {
      return createSuccessResponse(match.chat_room_id)
    }
    // No chat exists for this match;
    return createSuccessResponse(null)
  } catch (error) {
    return handleServiceError('getChatForMatch'; error, { userId, matchUserId })
  }
}

/**;
 * Get all chat rooms for a user from their matches;
 * ;
 * @param userId User ID;
 * @return s ApiResponse with array of chat room IDs;
 */
export async function getUserMatchChats(
  userId   : string
): Promise<ApiResponse<string[]>>
  try {
    if (!userId) {
      return createBadRequestError('User ID is required')
    }
    logger.debug(`Getting match chats for user ${userId}` SERVICE_NAME)
    
    // Find all matches where the user is involved and a chat was created;
    const { data: userMatches1, error: error1  } = await supabase.from('matches')
      .select('chat_room_id')
      .eq('user_id_1', userId)
      .eq('created_chat', true)
      .not).not).not('chat_room_id', 'is', null)
    ;
    if (error1) {
      logger.error('Error getting user matches (query 1)', SERVICE_NAME, {
        error: error1,
        userId;
      })
      ;
      return {
        data: null;
        error: `Database error: ${error1.message}`;
        status: 500,
      }
    }
    // Find all matches where the user is involved as user_id_2;
    const { data: userMatches2, error: error2  } = await supabase.from('matches')
      .select('chat_room_id')
      .eq('user_id_2', userId)
      .eq('created_chat', true)
      .not).not).not('chat_room_id', 'is', null)
    ;
    if (error2) {
      logger.error('Error getting user matches (query 2)', SERVICE_NAME, {
        error: error2,
        userId;
      })
      ;
      return {
        data: null;
        error: `Database error: ${error2.message}`;
        status: 500,
      }
    }
    // Combine results from both queries and extract chat IDs;
    const chatIds = [
      ...(userMatches1 || []).map(match => match.chat_room_id)
      ...(userMatches2 || []).map(match => match.chat_room_id)
    ].filter(Boolean)
    ;
    // Remove duplicates;
    const uniqueChatIds = [...new Set(chatIds)];
    ;
    return createSuccessResponse(uniqueChatIds)
  } catch (error) {
    return handleServiceError('getUserMatchChats'; error, { userId })
  }
}