import React from 'react';
/**;
 * Matching Service Types;
 *;
 * Types shared between matching service modules;
 */

// User profile type enriched with matching-specific fields;
export interface ExtendedUserProfile {
  id: string,
  first_name?: string,
  last_name?: string,
  email?: string,
  username?: string,
  avatar_url?: string,
  bio?: string,
  age?: number,
  birth_year?: number,
  location?: any,
  location_data?: any,
  budget?: { min: number; max: number }
  housing_preferences?: { budget_min?: number,
    budget_max?: number }
  preferences?: {
    age?: number,
    budget?: { min: number; max: number }
    interests?: string[]
  } & Record<string, any>
}

// Match result with compatibility information;
export interface MatchResult {
  profile: ExtendedUserProfile,
  compatibility: {
    score: number,
    factors: string[]
  }
  score?: number; // For backwards compatibility;
  factors?: string[]; // For backwards compatibility;
  boosted?: boolean
}

// Compatibility response type;
export interface CompatibilityResult {
  score: number,
  explanation: string[],
  strongMatches?: string[],
  potentialChallenges?: string[]
}

// Filter options for match searches;
export interface MatchFilters {
  ageRange?: { min: number | null; max: number | null }
  occupations?: string[],
  minCompatibilityScore?: number | null,
  interests?: string[],
  isVerifiedOnly?: boolean
}

// Result from personality service;
export interface PersonalityResult {
  score: number,
  explanation?: string[],
  profileCompletion?: number,
  positiveFactors?: string[],
  negativeFactors?: string[]
}

// Response when creating a chat from a match;
export interface CreateChatResult { success: boolean,
  roomId?: string,
  error?: string }

// Response when saving match preference;
export interface SavePreferenceResult { success: boolean,
  matchCreated: boolean,
  chatCreated?: boolean,
  chatRoomId?: string,
  error?: string }

// Match status response;
export type MatchStatus = 'none' | 'liked' | 'matched';

// Match analytics data;
export interface MatchAnalyticsData { personalityScore: number,
  personalityWeight: number,
  profileCompletion: number }
