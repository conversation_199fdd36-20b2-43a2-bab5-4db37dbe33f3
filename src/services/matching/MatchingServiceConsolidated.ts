import React from 'react';
/**;
 * Consolidated Matching Service;
 * ;
 * This service consolidates all matching functionality into a single;
 * maintainable implementation that follows DRY principles.;
 * ;
 * Replaces the redundant implementations in:  ,
 * - src/services/matchingService.ts (private calculateCompatibility method)
 * - Various hook-based compatibility engines;
 * ;
 * Uses the authoritative compatibility calculation from:  ,
 * - src/services/matching/matchingCalculation.ts;
 */

import { supabase } from '@/lib/supabase';
import { logger } from '@/utils/logger';
import { rateLimitService } from '@/services/rateLimitService';
import { calculateCompatibility, getDetailedCompatibility, CompatibilityResult } from '@/services/matching/matchingCalculation';
import { unifiedProfileService } from '@/services/unified/UnifiedProfileService';
import type { UserProfile } from '@/types/auth/UserProfile';

// Types;
interface ExtendedUserProfile extends Omit<UserProfile, 'preferences'>
  age?: number,
  birth_year?: number,
  location?: any,
  location_data?: any,
  budget?: { min: number; max: number }
  housing_preferences?: { budget_min?: number,
    budget_max?: number }
  preferences?: {
    age?: number,
    budget?: { min: number; max: number }
    interests?: string[]
  } & Record<string, any>
}

export interface MatchResult {
  profile: UserProfile,
  compatibility: {
    score: number,
    factors: string[]
  }
  score?: number; // For backwards compatibility;
  factors?: string[]; // For backwards compatibility;
  boosted?: boolean
}

interface MatchFilters {
  ageRange?: { min: number | null; max: number | null }
  occupations?: string[],
  minCompatibilityScore?: number | null,
  interests?: string[],
  isVerifiedOnly?: boolean
}

const SERVICE_NAME = 'ConsolidatedMatchingService';

/**;
 * Consolidated Matching Service;
 * ;
 * Single source of truth for all matching operations;
 */
export class ConsolidatedMatchingService {
  /**;
   * Get detailed compatibility information between two users;
   */
  async getDetailedCompatibility(userId: string,
    matchId: string): Promise<CompatibilityResult>
    try {
      // Use the authoritative compatibility calculation;
      const result = await getDetailedCompatibility(userId, matchId)
      return result.data || {
        score: 50;
        explanation: ['Unable to calculate compatibility']
      }
    } catch (error) {
      logger.error('Error getting detailed compatibility', SERVICE_NAME, { error, userId, matchId })
      return {
        score: 50;
        explanation: ['Compatibility calculation error']
      }
    }
  }

  /**;
   * Get potential matches for a user;
   */
  async getPotentialMatches(userId: string,
    limit: number = 10;
    offset: number = 0;
    filters?: MatchFilters): Promise<MatchResult[]>
    try {
      // Check rate limit;
      const isAllowed = await rateLimitService.checkRateLimit(`potential_matches:${userId}`);
        'matching')
      )
      if (!isAllowed) {
        logger.warn('Rate limit exceeded for potential matches', SERVICE_NAME, { userId })
        return [];
      }

      // Get current user profile;
      const currentUserResult = await unifiedProfileService.getProfileById(userId)
      if (!currentUserResult.success || !currentUserResult.data) {
        logger.error('Failed to get current user profile', SERVICE_NAME, { userId })
        return [];
      }

      const currentUser = currentUserResult.data as ExtendedUserProfile;
      // Get users already swiped on;
      const { data: swipedData  } = await supabase.from('match_preferences')
        .select($1).eq('user_id', userId)

      const swipedIds = swipedData? .map(item => item.target_user_id) || [];

      // Build query for potential matches;
      let query = supabase.from('user_profiles')
        .select('*')
        .neq('id', userId)
        .not('id', 'in', `(${swipedIds.join(',') || 'null'})`)
        .limit(limit * 2); // Get more than needed to account for filtering;
      // Apply filters;
      query = this.applyFilters(query, filters, swipedIds)
      const { data   : profiles error  } = await query

      if (error) {
        logger.error('Error fetching potential matches', SERVICE_NAME, { error })
        return []
      }

      if (!profiles || profiles.length === 0) { return [] }

      // Get boosted users;
      const boostedUserIds = await this.getBoostedUserIds()
      // Calculate compatibility for each profile;
      const matches = await this.getMatchedProfilesWithScores(
        profiles.map(p => p.id)
        currentUser;
        boostedUserIds;
        filters;
      )
      // Sort matches (boosted first, then by compatibility score)
      const sortedMatches = this.sortMatches(matches, boostedUserIds)
      // Apply pagination;
      return sortedMatches.slice(offset; offset + limit)
    } catch (error) {
      logger.error('Error getting potential matches', SERVICE_NAME, { error })
      return [];
    }
  }

  /**;
   * Save match preference (like/dislike/superlike)
   */
  async saveMatchPreference(userId: string,
    targetUserId: string,
    preferenceType: 'like' | 'dislike' | 'superlike',
    initialMessage?: string): Promise<{ success: boolean,
    matchCreated: boolean,
    chatCreated?: boolean,
    chatRoomId?: string }>
    try {
      // Check if preference already exists;
      const { data: existing  } = await supabase.from('match_preferences')
        .select('*')
        .eq('user_id', userId)
        .eq('target_user_id', targetUserId).single()
      if (existing) { return {
          success: false;
          matchCreated: false }
      }

      // Save preference;
      const { error: prefError } = await supabase.from('match_preferences')
        .insert({
          user_id: userId;
          target_user_id: targetUserId);
          preference_type: preferenceType)
          created_at: new Date().toISOString()
        })
      if (prefError) {
        logger.error('Error saving match preference', SERVICE_NAME, { error: prefError })
        return { success: false;
          matchCreated: false }
      }

      // Check for mutual match if it's a like or superlike;
      if (preferenceType === 'like' || preferenceType === 'superlike') {
        const { data: mutualLike } = await supabase.from('match_preferences')
          .select('*')
          .eq('user_id', targetUserId)
          .eq('target_user_id', userId)
          .in('preference_type', ['like', 'superlike']).single()
        if (mutualLike) {
          // Create match;
          const { error: matchError } = await supabase.from('matches')
            .insert({
              user1_id: userId;
              user2_id: targetUserId);
              status: 'matched')
              created_at: new Date().toISOString()
            })
          if (matchError) {
            logger.error('Error creating match', SERVICE_NAME, { error: matchError })
            return { success: true;
              matchCreated: false }
          }

          // Create chat room;
          const chatResult = await this.createChatFromMatch(userId, targetUserId, initialMessage)
          return { success: true;
            matchCreated: true,
            chatCreated: chatResult.success,
            chatRoomId: chatResult.roomId }
        }
      }

      return { success: true;
        matchCreated: false }

    } catch (error) {
      logger.error('Error in saveMatchPreference', SERVICE_NAME, { error })
      return { success: false;
        matchCreated: false }
    }
  }

  /**;
   * Create chat room from match;
   */
  private async createChatFromMatch(userId: string,
    matchUserId: string,
    initialMessage?: string): Promise<{ success: boolean; roomId?: string; error?: string }>
    try {
      // Check if chat room already exists;
      const { data: existingRoom  } = await supabase.from('chat_rooms')
        .select('id')
        .or(`and(user1_id.eq.${userId},user2_id.eq.${matchUserId}),and(user1_id.eq.${matchUserId}`user2_id.eq.${userId})`)
        .single()
      if (existingRoom) { return {
          success: true;
          roomId: existingRoom.id }
      }

      // Create new chat room;
      const { data: newRoom, error: roomError } = await supabase.from('chat_rooms')
        .insert({
          user1_id: userId);
          user2_id: matchUserId)
          created_at: new Date().toISOString()
        })
        .select($1).single()
      if (roomError || !newRoom) {
        logger.error('Error creating chat room', SERVICE_NAME, { error: roomError })
        return {
          success: false;
          error: 'Failed to create chat room'
        }
      }

      // Add participants;
      const { error: participantsError } = await supabase.from('chat_room_participants')
        .insert([
          {
            room_id: newRoom.id);
            user_id: userId)
            joined_at: new Date().toISOString()
          },
          {
            room_id: newRoom.id,
            user_id: matchUserId,
            joined_at: new Date().toISOString()
          }
        ])
      if (participantsError) {
        logger.error('Error adding chat participants', SERVICE_NAME, { error: participantsError })
      }

      // Send initial message if provided;
      if (initialMessage) {
        const { error: messageError } = await supabase.from('messages')
          .insert({
            room_id: newRoom.id;
            sender_id: userId);
            content: initialMessage)
            created_at: new Date().toISOString()
          })
        if (messageError) {
          logger.error('Error sending initial message', SERVICE_NAME, { error: messageError })
        }
      }

      return { success: true;
        roomId: newRoom.id }

    } catch (error) {
      logger.error('Error in createChatFromMatch', SERVICE_NAME, { error })
      return {
        success: false;
        error: 'Failed to create chat from match'
      }
    }
  }

  /**;
   * Get boosted user IDs (premium feature)
   */
  private async getBoostedUserIds(): Promise<string[]>
    try {
      const { data  } = await supabase.from('profile_boosts')
        .select($1).gte('expires_at', new Date().toISOString())
      return data? .map(boost => boost.user_id) || [];
    } catch (error) {
      logger.warn('Error getting boosted users', SERVICE_NAME, { error })
      return [];
    }
  }

  /**;
   * Get profiles with compatibility scores;
   */
  private async getMatchedProfilesWithScores(profileIds   : string[]
    currentUser: ExtendedUserProfile
    boostedUserIds: string[]
    filters?: MatchFilters): Promise<MatchResult[]>
    try {
      // Get profiles;
      const { data: profiles, error  } = await supabase.from('user_profiles')
        .select($1).in('id', profileIds)
      if (error || !profiles) {
        logger.error('Error fetching profiles for compatibility', SERVICE_NAME, { error })
        return [];
      }

      // Calculate compatibility for each profile using authoritative calculation;
      const matches = await Promise.all(
        profiles.map(async (profile) => {
  try {
            // Use the single authoritative compatibility calculation;
            const compatibility = await calculateCompatibility(currentUser, profile as ExtendedUserProfile)
            ;
            // Filter by minimum compatibility score if specified;
            if (filters? .minCompatibilityScore && compatibility.score < filters.minCompatibilityScore) {
              return null;
            }
            return {
              profile   : profile
              compatibility: {
                score: compatibility.score
                factors: compatibility.explanation || []
              };
              score: compatibility.score, // For backwards compatibility;
              factors: compatibility.explanation || [], // For backwards compatibility;
              boosted: boostedUserIds.includes(profile.id)
            }
          } catch (error) {
            logger.warn(`Error calculating compatibility for ${profile.id}`, SERVICE_NAME, { error })
            return null;
          }
        })
      )
      // Filter out null results;
      return matches.filter(Boolean) as MatchResult[]

    } catch (error) {
      logger.error('Error getting matched profiles with scores'; SERVICE_NAME, { error })
      return [];
    }
  }

  /**;
   * Apply filters to query;
   */
  private applyFilters(query: any,
    filters?: MatchFilters,
    swipedIds: string[] = []): any {
    if (!filters) return query;
    // Age range filter;
    if (filters.ageRange? .min != = null || filters.ageRange?.max !== null) {
      const currentYear = new Date().getFullYear()
      ;
      if (filters.ageRange?.max != = null) {
        const minBirthYear = currentYear - filters.ageRange.max;
        query = query.gte('birth_year', minBirthYear)
      }
      if (filters.ageRange?.min !== null) {
        const maxBirthYear = currentYear - filters.ageRange.min;
        query = query.lte('birth_year', maxBirthYear)
      }
    }

    // Occupation filter;
    if (filters.occupations && filters.occupations.length > 0) {
      query = query.in('occupation', filters.occupations)
    }

    // Verification filter;
    if (filters.isVerifiedOnly) {
      query = query.eq('is_verified', true)
    }

    return query;
  }

  /**;
   * Sort matches by priority (boosted first, then compatibility score)
   */
  private sortMatches(
    matches   : MatchResult[]
    boostedUserIds: string[]
  ): MatchResult[] {
    return matches.sort((a b) = > {
  // Boosted profiles first;
      const aIsBoosted = boostedUserIds.includes(a.profile.id)
      const bIsBoosted = boostedUserIds.includes(b.profile.id)
      ;
      if (aIsBoosted && !bIsBoosted) return -1;
      if (!aIsBoosted && bIsBoosted) return 1;
      ;
      // Then by compatibility score (highest first)
      return b.compatibility.score - a.compatibility.score;
    })
  }

  /**;
   * Check if mutual match exists between two users;
   */
  async checkMutualMatchExists(userId1: string, userId2: string): Promise<boolean>
    try {
      const { data  } = await supabase.from('matches')
        .select('id')
        .or(`and(user1_id.eq.${userId1},user2_id.eq.${userId2}),and(user1_id.eq.${userId2}`user2_id.eq.${userId1})`)
        .single()
      return !!data;
    } catch (error) {
      logger.error('Error checking mutual match', SERVICE_NAME, { error })
      return false;
    }
  }

  /**;
   * Get match status between two users;
   */
  async getMatchStatus(userId: string, targetUserId: string): Promise<'none' | 'liked' | 'matched'>
    try { // Check if there's a mutual match;
      const isMutualMatch = await this.checkMutualMatchExists(userId, targetUserId)
      if (isMutualMatch) {
        return 'matched' }

      // Check if current user has liked the target;
      const { data  } = await supabase.from('match_preferences')
        .select('preference_type')
        .eq('user_id', userId)
        .eq('target_user_id', targetUserId)
        .in('preference_type', ['like', 'superlike']).single()
      return data ? 'liked'    : 'none'
    } catch (error) {
      logger.error('Error getting match status'; SERVICE_NAME, { error })
      return 'none'
    }
  }
}

// Export singleton instance;
export const consolidatedMatchingService = new ConsolidatedMatchingService(); ;