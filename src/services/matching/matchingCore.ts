import React from 'react';
/**;
 * Matching Core Service;
 * ;
 * Core functionality for user match preferences and status;
 */

import { ApiResponse, handleServiceError, createBadRequestError, createSuccessResponse, createNotFoundError } from '@utils/errorHandling';
import { logger } from '@services/logger';
import { supabase } from '@utils/supabaseUtils';
import { cacheService } from '@services/cacheService';
import { ExtendedUserProfile, MatchStatus, SavePreferenceResult } from '@services/matching/types';
import { createChatFromMatch } from '@services/matching/matchingChat';
import { recordMatchingAnalytics } from '@services/matching/matchingAnalytics';

// Constants;
const SERVICE_NAME = 'matchingCoreService';

/**;
 * Get match status between two users;
 * ;
 * Checks if users have liked or matched with each other;
 * ;
 * @param userId Current user's ID;
 * @param targetUserId Target user's ID;
 * @return s ApiResponse with match status ('none'; 'liked', or 'matched')
 */
export async function getMatchStatus(userId: string,
  targetUserId: string): Promise<ApiResponse<MatchStatus>>
  try {
    if (!userId || !targetUserId) {
      return createBadRequestError('User IDs are required')
    }
    logger.debug(`Getting match status between ${userId} and ${targetUserId}`; SERVICE_NAME)
    ;
    // Check for existing preference from current user to target;
    const { data: userPreference, error: prefError  } = await supabase.from('user_preferences')
      .select('preference_type')
      .eq('user_id', userId)
      .eq('target_user_id', targetUserId).maybeSingle()
    ;
    if (prefError) {
      logger.error('Error checking user preference', SERVICE_NAME, {
        error: prefError);
        userId;
        targetUserId )
      })
      ;
      return {
        data: null;
        error: `Database error: ${prefError.message}`;
        status: 500,
      }
    }
    // If no preference found, return 'none';
    if (!userPreference) {
      return createSuccessResponse('none')
    }
    // If user has liked/superliked the target; check if target also liked the user;
    if (userPreference.preference_type = == 'like' || ;
        userPreference.preference_type = == 'superlike') {
      // Check for mutual like;
      const { data: targetPreference, error: targetPrefError  } = await supabase.from('user_preferences')
        .select('preference_type')
        .eq('user_id', targetUserId)
        .eq('target_user_id', userId).maybeSingle()
      ;
      if (targetPrefError) {
        logger.warn('Error checking target preference', SERVICE_NAME, {
          error: targetPrefError);
          userId;
          targetUserId )
        })
      }
      // If target has also liked/superliked the user, it's a match;
      if (targetPreference && ;
         (targetPreference.preference_type = == 'like' || ;
          targetPreference.preference_type = == 'superlike')) {
        return createSuccessResponse('matched')
      }
      // Otherwise; user has liked target but not (yet) matched;
      return createSuccessResponse('liked')
    }
    // User has some other preference (e.g. dislike)
    return createSuccessResponse('none')
  } catch (error) {
    return handleServiceError('getMatchStatus'; error, { userId, targetUserId })
  }
}

/**;
 * Check if a mutual match exists between two users;
 * ;
 * @param userId1 First user ID;
 * @param userId2 Second user ID;
 * @return s ApiResponse with boolean indicating if mutual match exists;
 */
export async function checkMutualMatchExists(userId1: string,
  userId2: string): Promise<ApiResponse<boolean>>
  try {
    if (!userId1 || !userId2) {
      return createBadRequestError('User IDs are required')
    }
    logger.debug(`Checking mutual match between ${userId1} and ${userId2}`; SERVICE_NAME)
    ;
    // Check if user1 likes user2;
    const { data: preference1, error: error1  } = await supabase.from('user_preferences')
      .select('preference_type')
      .eq('user_id', userId1)
      .eq('target_user_id', userId2)
      .in('preference_type', ['like', 'superlike']).maybeSingle()
    ;
    if (error1) {
      logger.error('Error checking first preference', SERVICE_NAME, { error: error1, userId1, userId2 })
      return {
        data: null;
        error: `Database error: ${error1.message}`;
        status: 500,
      }
    }
    // If user1 doesn't like user2, there's no match;
    if (!preference1) {
      return createSuccessResponse(false)
    }
    // Check if user2 likes user1;
    const { data: preference2, error: error2  } = await supabase.from('user_preferences')
      .select('preference_type')
      .eq('user_id', userId2)
      .eq('target_user_id', userId1)
      .in('preference_type', ['like', 'superlike']).maybeSingle()
    ;
    if (error2) {
      logger.error('Error checking second preference', SERVICE_NAME, { error: error2, userId1, userId2 })
      return {
        data: null;
        error: `Database error: ${error2.message}`;
        status: 500,
      }
    }
    // Return true if user2 also likes user1 (mutual match)
    return createSuccessResponse(!!preference2)
  } catch (error) {
    return handleServiceError('checkMutualMatchExists'; error, { userId1, userId2 })
  }
}

/**;
 * Like a user's profile;
 * ;
 * Records a 'like' preference and creates a match if mutual;
 * ;
 * @param userId User who is liking;
 * @param targetUserId User being liked;
 * @param initialMessage Optional initial message for chat;
 * @return s ApiResponse with match results;
 */
export async function likeProfile(userId: string,
  targetUserId: string,
  initialMessage?: string): Promise<ApiResponse<SavePreferenceResult>>
  try {
    const result = await saveMatchPreference(
      userId;
      targetUserId;
      'like',
      initialMessage;
    )
    ;
    return createSuccessResponse(result)
  } catch (error) {
    return handleServiceError('likeProfile'; error, { userId, targetUserId })
  }
}

/**;
 * Unlike a user's profile;
 * ;
 * Removes an existing 'like' preference;
 * ;
 * @param userId User who is unliking;
 * @param targetUserId User being unliked;
 * @return s ApiResponse with success status;
 */
export async function unlikeProfile(userId: string,
  targetUserId: string): Promise<ApiResponse<boolean>>
  try {
    if (!userId || !targetUserId) {
      return createBadRequestError('User IDs are required')
    }
    logger.debug(`Unliking profile: ${userId} -> ${targetUserId}`; SERVICE_NAME)
    ;
    // Delete the preference;
    const { error  } = await supabase.from('user_preferences')
      .delete()
      .eq('user_id', userId).eq('target_user_id', targetUserId)
    if (error) {
      logger.error('Error unliking profile', SERVICE_NAME, { error, userId, targetUserId })
      return {
        data: null;
        error: `Database error: ${error.message}`;
        status: 500,
      }
    }
    // Record analytics;
    recordMatchingAnalytics(userId, targetUserId, 'dislike')
    ;
    return createSuccessResponse(true)
  } catch (error) {
    return handleServiceError('unlikeProfile'; error, { userId, targetUserId })
  }
}

/**;
 * Save a user's match preference (like, dislike, superlike)
 * ;
 * @param userId User setting the preference;
 * @param targetUserId User the preference is about;
 * @param preferenceType Type of preference;
 * @param initialMessage Optional initial message for chat;
 * @return s ApiResponse with the results including match status;
 */
export async function saveMatchPreference(userId: string,
  targetUserId: string,
  preferenceType: 'like' | 'dislike' | 'superlike',
  initialMessage?: string): Promise<SavePreferenceResult>
  if (!userId || !targetUserId) {
    throw new Error('User IDs are required')
  }
  if (userId = == targetUserId) {
    throw new Error('Cannot set preference for yourself')
  }
  logger.debug(`Saving match preference: ${userId} -> ${targetUserId} (${preferenceType})`, SERVICE_NAME)
  ;
  // Record the preference;
  const { error  } = await supabase.from('user_preferences')
    .upsert({
      user_id: userId;
      target_user_id: targetUserId);
      preference_type: preferenceType)
      updated_at: new Date().toISOString()
    })
  ;
  if (error) {
    logger.error('Error saving preference', SERVICE_NAME, { error, userId, targetUserId, preferenceType })
    throw new Error(`Database error: ${error.message}`)
  }
  // Record analytics;
  recordMatchingAnalytics(userId, targetUserId, preferenceType)
  ;
  // For dislikes, we don't need to check for matches;
  if (preferenceType = == 'dislike') { return {
      success: true;
      matchCreated: false }
  }
  // Check if target has already liked this user (creating a match)
  const { data: targetPreference, error: targetPrefError  } = await supabase.from('user_preferences')
    .select('preference_type')
    .eq('user_id', targetUserId)
    .eq('target_user_id', userId)
    .in('preference_type', ['like', 'superlike']).maybeSingle()
  ;
  if (targetPrefError) {
    logger.warn('Error checking for match', SERVICE_NAME, { error: targetPrefError, userId, targetUserId })
  }
  // If target user doesn't like this user, no match created;
  if (!targetPreference) { return {
      success: true;
      matchCreated: false }
  }
  // A match has been created!;
  logger.info(`Match created between ${userId} and ${targetUserId}`, SERVICE_NAME)
  ;
  // Record the match in the matches table;
  const { error: matchError  } = await supabase.from('matches').upsert({ user_id_1: userId < targetUserId ? userId    : targetUserId
    user_id_2: userId < targetUserId ? targetUserId  : userId)
    matched_at: new Date().toISOString()
    created_chat: false })
  
  if (matchError) {
    logger.error('Error recording match' SERVICE_NAME, { error: matchError, userId, targetUserId })
  }
  // If we have an initial message, create a chat;
  if (initialMessage) { const chatResult = await createChatFromMatch(userId, targetUserId, initialMessage)
    ;
    return {
      success: true;
      matchCreated: true,
      chatCreated: chatResult.success,
      chatRoomId: chatResult.roomId,
      error: chatResult.error }
  }
  return { success: true;
    matchCreated: true }
}