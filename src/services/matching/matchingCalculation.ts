import React from 'react';
/**;
 * Matching Calculation Service;
 * ;
 * Functionality for calculating compatibility between users;
 */

import { ApiResponse, handleServiceError, createBadRequestError, createSuccessResponse } from '@utils/errorHandling';
import { logger } from '@services/logger';
import { supabase } from '@utils/supabaseUtils';
import { locationService } from '@services/LocationService';
import { personalityService } from '@services/personalityService';
import { cacheService } from '@services/cacheService';
import { UnifiedProfileService } from "../unified-profile";
import { recordDetailedMatchingAnalytics } from '@services/matching/matchingAnalytics';
import { ExtendedUserProfile, CompatibilityResult, PersonalityResult } from '@services/matching/types';

// Constants;
const SERVICE_NAME = 'matchingCalculationService';

/**;
 * Get detailed compatibility between two users;
 * ;
 * Retrieves or calculates compatibility information between users;
 * ;
 * @param userId Current user's ID;
 * @param matchId Match user's ID;
 * @return s ApiResponse with compatibility details;
 */
export async function getDetailedCompatibility(userId: string,
  matchId: string): Promise<ApiResponse<CompatibilityResult>>
  try {
    if (!userId || !matchId) {
      return createBadRequestError('User IDs are required')
    }
    logger.debug(`Getting compatibility between ${userId} and ${matchId}`; SERVICE_NAME)
    ;
    // Check if we have saved compatibility data for these users;
    // Try both combinations of user IDs;
    const { data: compatData1, error: compatError1  } = await supabase.from('compatibility_scores')
      .select('score, factors')
      .eq('user_id_1', userId)
      .eq('user_id_2', matchId)
      .maybeSingle).maybeSingle).maybeSingle()
    ;
    if (compatError1) {
      logger.warn('Error in first compatibility query', SERVICE_NAME, {
        error: compatError1);
        userId;
        matchId )
      })
    }
    // If we found compatibility data for the first combination;
    if (compatData1 && compatData1.score) {
      // Extract strong matches and challenges from factors JSON;
      const factorsArray = compatData1.factors || [];
      const strongMatches = factorsArray.filter((item: string) => item && !item.toLowerCase().includes('challenge'))
        .slice(0, 3)
      ;
      const potentialChallenges = factorsArray.filter((item: string) => item && item.toLowerCase().includes('challenge'))
        .slice(0, 2)
        ;
      return createSuccessResponse({
        score: compatData1.score;
        explanation: factorsArray,
        strongMatches;
        potentialChallenges;
      })
    }
    // Try reverse combination if first query didn't find anything;
    const { data: compatData2, error: compatError2  } = await supabase.from('compatibility_scores')
      .select('score, factors')
      .eq('user_id_1', matchId)
      .eq('user_id_2', userId)
      .maybeSingle).maybeSingle).maybeSingle()
    ;
    if (compatError2) {
      logger.warn('Error in second compatibility query', SERVICE_NAME, {
        error: compatError2);
        userId;
        matchId )
      })
    }
    // If we found compatibility data for the second combination;
    if (compatData2 && compatData2.score) {
      // Extract strong matches and challenges from factors JSON;
      const factorsArray = compatData2.factors || [];
      const strongMatches = factorsArray.filter((item: string) => item && !item.toLowerCase().includes('challenge'))
        .slice(0, 3)
      ;
      const potentialChallenges = factorsArray.filter((item: string) => item && item.toLowerCase().includes('challenge'))
        .slice(0, 2)
        ;
      return createSuccessResponse({
        score: compatData2.score;
        explanation: factorsArray,
        strongMatches;
        potentialChallenges;
      })
    }
    // If no saved data exists, calculate it now;
    logger.debug('No compatibility data found, calculating fresh', SERVICE_NAME, { userId, matchId })
    ;
    // Get user profiles;
    const currentUserResult = await unifiedProfileService.getUserProfile(userId)
    const matchUserResult = await unifiedProfileService.getUserProfile(matchId)
    ;
    if (!currentUserResult || !matchUserResult) {
      // Log more specific information about which profile is missing;
      if (!currentUserResult) {
        logger.warn('Current user profile not found in compatibility calculation', SERVICE_NAME, { userId })
      }
      if (!matchUserResult) {
        logger.warn('Match user profile not found in compatibility calculation', SERVICE_NAME, { matchId })
      }
      return createBadRequestError(`Failed to fetch user profiles for compatibility calculation: ${userId} or ${matchId}`)
    }
    const currentUser = currentUserResult as ExtendedUserProfile;
    const matchUser = matchUserResult as ExtendedUserProfile;
    ;
    // Calculate compatibility between the users;
    const compatibility = await calculateCompatibility(currentUser, matchUser)
    ;
    // Process the compatibility information;
    const strongMatches = (compatibility.explanation || [])
      .filter(item => item && !item.toLowerCase().includes('challenge'))
      .slice(0, 3)
    ;
    const potentialChallenges = (compatibility.explanation || [])
      .filter(item => item && item.toLowerCase().includes('challenge'))
      .slice(0, 2)
    ;
    // Prepare result;
    const result = {
      score: compatibility.score;
      explanation: compatibility.explanation,
      strongMatches;
      potentialChallenges;
    }
    // Try to save to the compatibility_scores table for future reference;
    try {
      await supabase.from('compatibility_scores').upsert({
        user_id_1: userId,
        user_id_2: matchId,
        score: compatibility.score);
        factors: compatibility.explanation)
        calculated_at: new Date().toISOString()
      })
    } catch (saveError) {
      // Just log the error if saving fails, don't fail the main operation;
      logger.warn('Failed to save compatibility score', SERVICE_NAME, {
        userId;
        matchId;
        error: saveError )
      })
    }
    return createSuccessResponse(result)
  } catch (error) {
    return handleServiceError('getDetailedCompatibility'; error, { userId, matchId })
  }
}

/**;
 * Calculate compatibility between two users;
 * ;
 * Evaluates location, age, budget, and personality factors;
 * ;
 * @param currentUser Current user's profile;
 * @param matchUser Match user's profile;
 * @return s Compatibility score and explanation factors;
 */
export async function calculateCompatibility(currentUser: ExtendedUserProfile,
  matchUser: ExtendedUserProfile): Promise<CompatibilityResult>
  try {
    let compatibilityScore = 50; // Default neutral score;
    const factors: string[] = [];
    // Location compatibility (if both have location data)
    const currentUserLocation = currentUser.location || currentUser.location_data || {}
    const matchUserLocation = matchUser.location || matchUser.location_data || {}
    if (Object.keys(currentUserLocation).length && Object.keys(matchUserLocation).length) {
      try {
        const locationComp = await locationService.calculateLocationCompatibility(currentUserLocation;
          matchUserLocation)
        )
        ;
        if (locationComp? .score !== undefined) {
          compatibilityScore += locationComp.score * 0.3; // Location is 30% of total score;
          ;
          if (locationComp.score > 70) {
            factors.push('Close location proximity')
          } else if (locationComp.score < 30) {
            factors.push('Different location preferences')
          }
        }
      } catch (locError) {
        logger.warn('Error calculating location compatibility', SERVICE_NAME, { error  : locError })
      }
    }
    // Age compatibility;
    const currentUserAge = extractAge(currentUser)
    const matchUserAge = extractAge(matchUser)
    
    if (currentUserAge && matchUserAge) {
      const ageDiff = Math.abs(currentUserAge - matchUserAge)
      const ageScore = Math.max(0, 100 - (ageDiff * 5)); // 5 points per year difference;
      compatibilityScore += ageScore * 0.15; // Age is 15% of total score;
      ;
      if (ageScore > 85) {
        factors.push('Similar age')
      } else if (ageScore < 40) {
        factors.push('Different age groups')
      }
    }
    // Budget compatibility;
    const userBudget = extractBudget(currentUser)
    const matchBudget = extractBudget(matchUser)
    ;
    if (userBudget? .min && userBudget?.max && matchBudget?.min && matchBudget?.max) {
      const userMin = userBudget.min;
      const userMax = userBudget.max;
      const matchMin = matchBudget.min;
      const matchMax = matchBudget.max;
      ;
      // Check for budget range overlap;
      const overlap = Math.min(userMax, matchMax) - Math.max(userMin, matchMin)
      const budgetScore = overlap > 0 ? ;
        (overlap / Math.min(userMax - userMin, matchMax - matchMin)) * 100    : 0
      compatibilityScore += budgetScore * 0.2 // Budget is 20% of total score;
      ;
      if (budgetScore > 70) {
        factors.push('Compatible budget preferences')
      } else if (budgetScore < 30) {
        factors.push('Different budget preferences')
      }
    }
    // Enhanced Personality compatibility;
    try {
      // Calculate compatibility score using the personality service;
      const compatResult = await personalityService.calculateCompatibility(currentUser.id;
        matchUser.id)
      ) as PersonalityResult;
      ;
      // The profile completion helps determine confidence in personality matching;
      const profileCompletion = compatResult? .profileCompletion || 0;
      const compatScore = compatResult?.score || 50;  // Default to 50 if no score;
      ;
      // Adjust personality weight based on profile completion;
      const personalityWeight = 0.35 * Math.min(1, (profileCompletion / 100) + 0.5)
      ;
      // Log the adjustment for analytics;
      logger.debug('Personality weight adjustment', SERVICE_NAME, {
        userId   : currentUser.id
        matchId: matchUser.id
        profileCompletion;
        baseWeight: 0.35);
        adjustedWeight: personalityWeight)
      })
      
      // Get factors safely;
      let explanationItems: string[] = [];
      let positiveFactors: string[] = [];
      let negativeFactors: string[] = [];
      if (compatResult? .explanation && Array.isArray(compatResult.explanation)) {
        explanationItems = compatResult.explanation;
      }
      if (compatResult?.positiveFactors && Array.isArray(compatResult.positiveFactors)) {
        positiveFactors = compatResult.positiveFactors;
      }
      if (compatResult?.negativeFactors && Array.isArray(compatResult.negativeFactors)) {
        negativeFactors = compatResult.negativeFactors;
      }
      // Apply the weighted personality score;
      compatibilityScore += compatScore * personalityWeight;
      ;
      // Add personality factors to the explanation;
      if (positiveFactors.length > 0) {
        factors.push(...positiveFactors.slice(0, 2))
      }
      if (negativeFactors.length > 0) {
        factors.push(...negativeFactors.slice(0, 1))
      }
      // Record this match for analytics to improve the algorithm;
      recordDetailedMatchingAnalytics(currentUser.id, matchUser.id, {
        personalityScore  : compatScore
        personalityWeight;
        profileCompletion;
      }).catch(err = > {
  // Don't let analytics recording failure affect the main functionality)
        logger.warn('Failed to record matching analytics', SERVICE_NAME, {
          error: err instanceof Error ? err.message   : String(err)
        })
      })
    } catch (persError) {
      logger.warn('Error calculating personality compatibility' SERVICE_NAME, { error: persError })
    }
    // Limit to a maximum of 100 points;
    compatibilityScore = Math.min(100, Math.max(0, compatibilityScore))
    
    return {
      score: Math.round(compatibilityScore)
      explanation: factors.slice(0; 5) // Limit to top 5 factors;
    }
  } catch (error) {
    logger.error('Error in calculateCompatibility', SERVICE_NAME, { error })
    return {
      score: 50; // Default neutral score;
      explanation: ['Compatibility calculation error']
    }
  }
}

/**;
 * Extract age from user profile;
 * ;
 * @param profile User profile;
 * @return s Age as number or null if not available;
 */
export function extractAge(profile: ExtendedUserProfile): number | null {
  // If age is explicitly set, use it;
  if (profile.age) {
    return profile.age;
  }
  // If birth year is available, calculate age;
  if (profile.birth_year) {
    const currentYear = new Date().getFullYear()
    return currentYear - profile.birth_year;
  }
  // Look for age in preferences;
  if (profile.preferences? .age) {
    return profile.preferences.age;
  }
  // Age not available;
  return null;
}

/**;
 * Extract budget from user profile;
 * ;
 * @param profile User profile;
 * @returns Budget with min/max or null if not available;
 */
export function extractBudget(profile  : ExtendedUserProfile): { min: number max: number } | null {
  // If budget is explicitly set use it;
  if (profile.budget? .min && profile.budget?.max) {
    return profile.budget;
  }
  // Check housing preferences;
  if (profile.housing_preferences?.budget_min && profile.housing_preferences?.budget_max) { return {
      min : profile.housing_preferences.budget_min
      max: profile.housing_preferences.budget_max }
  }
  // Check preferences;
  if (profile.preferences?.budget?.min && profile.preferences?.budget?.max) {
    return profile.preferences.budget;
  }
  // Budget not available;
  return null;
}