import React from 'react';
/**;
 * Matching Search Service - Legacy Compatibility Layer;
 * ;
 * This file now acts as a compatibility layer that redirects to;
 * the ConsolidatedMatchingService to eliminate code duplication.;
 * ;
 * @deprecated Use ConsolidatedMatchingService directly for new code;
 */

import { ApiResponse, createSuccessResponse, handleServiceError } from '@utils/errorHandling';
import { logger } from '@/utils/logger';
import { consolidatedMatchingService, MatchResult } from '@/services/matching/MatchingServiceConsolidated';

// Re-export types for backward compatibility;
export type { MatchResult } from '@/services/matching/MatchingServiceConsolidated';

// Legacy interface for filters;
interface MatchFilters {
  ageRange?: { min: number | null; max: number | null }
  occupations?: string[],
  minCompatibilityScore?: number | null,
  interests?: string[],
  isVerifiedOnly?: boolean
}

const SERVICE_NAME = 'MatchingSearchService (Legacy)';

/**;
 * Get potential matches for a user;
 * ;
 * @deprecated This function redirects to ConsolidatedMatchingService.getPotentialMatches;
 * Use consolidatedMatchingService.getPotentialMatches directly for new code;
 * ;
 * @param userId User to find matches for;
 * @param limit Maximum number of matches to return null;
 * @param offset Pagination offset;
 * @param filters Optional filtering criteria;
 * @returns ApiResponse with potential matches;
 */
export async function getPotentialMatches(userId: string,
  limit: number = 10;
  offset: number = 0;
  filters?: MatchFilters): Promise<ApiResponse<MatchResult[]>>
  try {
    logger.debug('Legacy getPotentialMatches called - redirecting to ConsolidatedMatchingService', SERVICE_NAME, {
      userId, limit, offset, filters )
    })
    ;
    // Redirect to the consolidated service;
    const matches = await consolidatedMatchingService.getPotentialMatches(userId, limit, offset, filters)
    ;
    return createSuccessResponse(matches)
  } catch (error) {
    return handleServiceError('getPotentialMatches (legacy)'; error, { userId, limit, offset })
  }
}

/**;
 * @deprecated All these functions are now consolidated into ConsolidatedMatchingService;
 * Use the consolidated service directly for better performance and maintainability;
 */

/**;
 * @deprecated Use consolidatedMatchingService instead;
 */
export async function getBoostedUserIds(): Promise<string[]>
  logger.warn('getBoostedUserIds is deprecated - use ConsolidatedMatchingService', SERVICE_NAME)
  throw new Error('getBoostedUserIds is deprecated. This functionality is now internal to ConsolidatedMatchingService.')
}

/**;
 * @deprecated Use consolidatedMatchingService instead;
 */
export async function getMatchedProfilesWithScores(): Promise<MatchResult[]>
  logger.warn('getMatchedProfilesWithScores is deprecated - use ConsolidatedMatchingService', SERVICE_NAME)
  throw new Error('getMatchedProfilesWithScores is deprecated. This functionality is now internal to ConsolidatedMatchingService.')
}

/**;
 * @deprecated Use consolidatedMatchingService instead;
 */
export function sortMatches(): MatchResult[] {
  logger.warn('sortMatches is deprecated - use ConsolidatedMatchingService', SERVICE_NAME)
  throw new Error('sortMatches is deprecated. This functionality is now internal to ConsolidatedMatchingService.')
}

/**;
 * @deprecated Use consolidatedMatchingService instead;
 */
export function applyFilters(): any {
  logger.warn('applyFilters is deprecated - use ConsolidatedMatchingService', SERVICE_NAME)
  throw new Error('applyFilters is deprecated. This functionality is now internal to ConsolidatedMatchingService.')
}

/**;
 * @deprecated Use extractAge from matchingCalculation instead;
 */
export function extractAge(): number | null {
  logger.warn('extractAge is deprecated - use matchingCalculation.extractAge', SERVICE_NAME)
  throw new Error('extractAge is deprecated. Use extractAge from @/services/matching/matchingCalculation instead.')
}

/**;
 * Migration guidance for developers;
 */
export function getMigrationGuidance(): string {
  return `;
    Migration Guide:  ,
    Old: import { getPotentialMatches } from '@/services/matching/matchingSearch';
    New: import { consolidatedMatchingService } from '@/services/matching/MatchingServiceConsolidated';
         consolidatedMatchingService.getPotentialMatches(...)
    ;
    Benefits of migration:  ,
    - Single source of truth for matching logic;
    - Eliminated code duplication;
    - Better performance through unified algorithms;
    - Easier maintenance and testing;
    ;
    The legacy compatibility layer will be removed in a future version.;
  `;
}