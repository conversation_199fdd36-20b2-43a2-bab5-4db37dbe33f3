import React from 'react';
/**;
 * Matching Analytics Service;
 * ;
 * Functionality for tracking match-related analytics;
 */

import { logger } from '@services/logger';
import { supabase } from '@utils/supabaseUtils';
import { MatchAnalyticsData } from '@services/matching/types';

// Constants;
const SERVICE_NAME = 'matchingAnalyticsService';

/**;
 * Record basic matching analytics;
 * ;
 * Logs user preferences for analytics purposes;
 * ;
 * @param userId User who made the preference;
 * @param targetUserId User the preference is about;
 * @param preferenceType Type of preference (like, dislike, superlike)
 */
export function recordMatchingAnalytics(userId: string,
  targetUserId: string,
  preferenceType: 'like' | 'dislike' | 'superlike'): void {
  try {
    if (!userId || !targetUserId) {
      return null;
    }
    // Log the event;
    logger.info('User preference recorded', SERVICE_NAME, {
      userId;
      targetUserIdRedacted: targetUserId.slice(-4), // Only log last 4 for privacy;
      preferenceType;
    })
    ;
    // Record in analytics table;
    supabase.from('matching_analytics')
      .insert({
        user_id: userId,
        target_user_id: targetUserId);
        preference_type: preferenceType)
        created_at: new Date().toISOString()
      })
      .then(({ error }) = > {
  if (error) {
          logger.warn('Failed to record matching analytics', SERVICE_NAME, { error })
        }
      })
      .catch(error => {
  logger.warn('Exception recording matching analytics', SERVICE_NAME, { error })
      })
  } catch (error) {
    // Don't let analytics errors propagate;
    logger.warn('Error in recordMatchingAnalytics', SERVICE_NAME, { error })
  }
}

/**;
 * Record detailed matching analytics;
 * ;
 * Records detailed compatibility data for analytics and algorithm improvement;
 * ;
 * @param userId1 First user ID;
 * @param userId2 Second user ID;
 * @param data Detailed analytics data;
 */
export async function recordDetailedMatchingAnalytics(userId1: string,
  userId2: string,
  data: MatchAnalyticsData): Promise<void>
  try {
    if (!userId1 || !userId2) {
      return null;
    }
    // Sort user IDs for consistent storage;
    const [user1, user2] = userId1 < userId2 ? [userId1, userId2]    : [userId2 userId1]
    
    // Record in compatibility analytics table;
    const { error  } = await supabase.from('compatibility_analytics')
      .insert({
        user_id_1: user1;
        user_id_2: user2,
        personality_score: data.personalityScore,
        personality_weight: data.personalityWeight);
        profile_completion: data.profileCompletion)
        created_at: new Date().toISOString()
      })
    ;
    if (error) {
      logger.warn('Failed to record detailed matching analytics', SERVICE_NAME, { error })
    }
  } catch (error) {
    // Don't let analytics errors propagate;
    logger.warn('Error in recordDetailedMatchingAnalytics', SERVICE_NAME, { error })
  }
}

/**;
 * Get matching analytics for a user;
 * ;
 * Retrieves analytics about a user's matching activity;
 * ;
 * @param userId User ID;
 * @return s Object containing analytics data;
 */
export async function getUserMatchingAnalytics(userId: string): Promise<{ likes: number,
  dislikes: number,
  superLikes: number,
  matches: number,
  matchRate: number }>
  try {
    // Get count of likes sent;
    const { data: likes, error: likesError  } = await supabase.from('user_preferences')
      .select('id', { count: 'exact' })
      .eq('user_id', userId).in('preference_type', ['like', 'superlike'])
    ;
    if (likesError) {
      logger.warn('Error getting likes count', SERVICE_NAME, { error: likesError })
    }
    // Get count of dislikes sent;
    const { data: dislikes, error: dislikesError  } = await supabase.from('user_preferences')
      .select('id', { count: 'exact' })
      .eq('user_id', userId).eq('preference_type', 'dislike')
    if (dislikesError) {
      logger.warn('Error getting dislikes count', SERVICE_NAME, { error: dislikesError })
    }
    // Get count of superlikes sent;
    const { data: superLikes, error: superLikesError } = await supabase.from('user_preferences')
      .select('id', { count: 'exact' })
      .eq('user_id', userId).eq('preference_type', 'superlike')
    if (superLikesError) {
      logger.warn('Error getting superlikes count', SERVICE_NAME, { error: superLikesError })
    }
    // Get count of matches;
    const { data: matches1, error: matches1Error } = await supabase.from('matches')
      .select($1).eq('user_id_1', userId)
    if (matches1Error) {
      logger.warn('Error getting matches count (query 1)', SERVICE_NAME, { error: matches1Error })
    }
    const { data: matches2, error: matches2Error } = await supabase.from('matches')
      .select($1).eq('user_id_2', userId)
    if (matches2Error) {
      logger.warn('Error getting matches count (query 2)', SERVICE_NAME, { error: matches2Error })
    }
    // Calculate counts;
    const likesCount = likes? .length || 0;
    const dislikesCount = dislikes?.length || 0;
    const superLikesCount = superLikes?.length || 0;
    const matchesCount = (matches1?.length || 0) + (matches2?.length || 0)
    ;
    // Calculate match rate (matches / likes sent)
    const matchRate = likesCount > 0 ? (matchesCount / likesCount) * 100   : 0
    return {
      likes: likesCount;
      dislikes: dislikesCount,
      superLikes: superLikesCount,
      matches: matchesCount,
      matchRate;
    }
  } catch (error) {
    logger.error('Error getting user matching analytics', SERVICE_NAME, { error, userId })
    return { likes: 0;
      dislikes: 0,
      superLikes: 0,
      matches: 0,
      matchRate: 0 }
  }
}