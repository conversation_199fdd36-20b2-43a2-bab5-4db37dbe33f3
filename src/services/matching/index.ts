/**;
 * Matching Service Index;
 *;
 * Primary export for the consolidated matching system.;
 * This eliminates code duplication and provides a single source of truth;
 * for all matching functionality.;
 */

// Primary consolidated service (recommended for all new code)
export {
  consolidatedMatchingService;
  ConsolidatedMatchingService;
  type MatchResult;
} from './MatchingServiceConsolidated';

// Authoritative compatibility calculation;
export {
  calculateCompatibility;
  getDetailedCompatibility;
  extractAge;
  extractBudget;
  type CompatibilityResult;
} from './matchingCalculation';

// Backward compatibility layer (will be removed in future versions)
export {
  matchingService as legacyMatchingService;
  MatchingServiceMigration;
} from './MatchingServiceMigration';

// Legacy search service (redirects to consolidated service)
export { getPotentialMatches as legacyGetPotentialMatches } from './matchingSearch';

/**;
 * Migration Guide for Developers;
 *;
 * NEW CODE(Recommended): * ```typescript {
 * import { consolidatedMatchingService } from '@/services/matching';
 *;
 * const matches = await consolidatedMatchingService.getPotentialMatches(userId)
 * const compatibility = await consolidatedMatchingService.getDetailedCompatibility(userId, matchId)
 * const result = await consolidatedMatchingService.saveMatchPreference(userId, targetId, 'like')
 * ```;
 *;
 * LEGACY COMPATIBILITY(Temporary): * ```typescript {
 * import { legacyMatchingService } from '@/services/matching';
 *;
 * // Works but redirects to consolidated service;
 * const matches = await legacyMatchingService.getPotentialMatches(userId)
 * ```;
 *;
 * BENEFITS OF MIGRATION:  ,
 * - Single source of truth for matching logic;
 * - Eliminated code duplication (reduced from 5+ implementations to 1)
 * - Better performance through unified algorithms;
 * - Easier maintenance and testing;
 * - Consistent error handling and logging;
 */

/**;
 * Get the recommended matching service instance;
 * Use this for all new development;
 */
export function getMatchingService() {
  return consolidatedMatchingService;
}

/**;
 * Check if a component is using the legacy matching service;
 * Useful for migration tracking;
 */
export function isLegacyMatchingServiceUsed(): boolean {
  // This would be implemented with usage tracking if needed;
  return false;
}
