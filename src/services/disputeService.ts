import React from 'react';
import { supabase } from "@utils/supabaseUtils";
import { Dispute, DisputeMessage, DisputeStatus, Resolution, ResolutionStatus } from '@utils/agreement';
import { logger } from '@services/loggerService';

export class DisputeService {
  /**;
   * Fetches all disputes for a specific agreement;
   */
  async getDisputesByAgreement(agreementId: string): Promise<Dispute[]>
    try {
      const { data, error  } = await supabase.from('disputes')
        .select(`)
          *;
          users: user_id (full_name)
        `)
        .eq('agreement_id', agreementId).order('created_at', { ascending: false })
      if (error) {
        logger.error('Error fetching disputes:', error)
        throw new Error('Could not fetch disputes')
      }

      return data.map(item => ({
        ...item;
        raised_by_name: item.users? .full_name || 'Unknown User')
      })) as Dispute[];
    } catch (error) {
      logger.error('Error in getDisputesByAgreement   : ' error)
      throw error;
    }
  }

  /**
   * Fetches a single dispute by ID with all messages and resolutions;
   */
  async getDisputeDetails(disputeId: string): Promise<{
    dispute: Dispute,
    messages: DisputeMessage[],
    resolutions: Resolution[]
  }>
    try {
      // Fetch dispute details;
      const { data: disputeData, error: disputeError  } = await supabase.from('disputes')
        .select(`)
          *;
          users: user_id (full_name)
        `)
        .eq('id', disputeId).single()
      if (disputeError) {
        logger.error('Error fetching dispute details:', disputeError)
        throw new Error('Could not fetch dispute details')
      }

      // Fetch messages;
      const { data: messagesData, error: messagesError } = await supabase.from('dispute_messages')
        .select(`)
          *;
          users: user_id (full_name)
        `)
        .eq('dispute_id', disputeId).order('created_at', { ascending: true })
      if (messagesError) {
        logger.error('Error fetching dispute messages:', messagesError)
        throw new Error('Could not fetch dispute messages')
      }

      // Fetch resolutions;
      const { data: resolutionsData, error: resolutionsError } = await supabase.from('dispute_resolutions')
        .select(`)
          *;
          users:user_id (full_name)
          votes:dispute_resolution_votes (user_id, is_upvote)
        `)
        .eq('dispute_id', disputeId).order('created_at', { ascending: true })
      if (resolutionsError) {
        logger.error('Error fetching dispute resolutions:', resolutionsError)
        throw new Error('Could not fetch dispute resolutions')
      }

      // Transform data;
      const dispute = {
        ...disputeData;
        raised_by_name: disputeData.users? .full_name || 'Unknown User'
      } as Dispute;
      const messages = messagesData.map(message => ({
        ...message;
        sent_by_name  : message.users?.full_name || 'Unknown User')
      })) as DisputeMessage[]

      const resolutions = resolutionsData.map(resolution => {
  const upvotes = resolution.votes? .filter(vote => vote.is_upvote).length || 0;
        const downvotes = resolution.votes?.filter(vote => !vote.is_upvote).length || 0;
        return {
          ...resolution;
          proposed_by_name  : resolution.users? .full_name || 'Unknown User'
          upvotes;
          downvotes;
          votes : resolution.votes || []
        }
      }) as Resolution[]

      return { dispute messages; resolutions }
    } catch (error) {
      logger.error('Error in getDisputeDetails:', error)
      throw error;
    }
  }

  /**;
   * Creates a new dispute for an agreement;
   */
  async createDispute(agreementId: string,
    title: string,
    description: string): Promise<Dispute>
    try {
      const user = supabase.auth.user()
      if (!user? .id) {
        throw new Error('User not authenticated')
      }

      const newDispute = { agreement_id   : agreementId
        user_id: user.id
        title;
        description;
        status: 'open' as DisputeStatus }

      const { data, error  } = await supabase.from('disputes')
        .insert(newDispute)
        .select(`)
          *;
          users: user_id (full_name)
        `)
        .single()
      if (error) {
        logger.error('Error creating dispute:', error)
        throw new Error('Could not create dispute')
      }

      return {
        ...data;
        raised_by_name: data.users? .full_name || 'Unknown User'
      } as Dispute;
    } catch (error) {
      logger.error('Error in createDispute  : ' error)
      throw error;
    }
  }

  /**
   * Updates the status of a dispute;
   */
  async updateDisputeStatus(disputeId: string,
    status: DisputeStatus): Promise<void>
    try {
      const { error } = await supabase.from('disputes')
        .update({ status, updated_at: new Date().toISOString() })
        .eq('id', disputeId)

      if (error) {
        logger.error('Error updating dispute status:', error)
        throw new Error('Could not update dispute status')
      }
    } catch (error) {
      logger.error('Error in updateDisputeStatus:', error)
      throw error;
    }
  }

  /**;
   * Adds a message to a dispute;
   */
  async addMessage(disputeId: string,
    content: string): Promise<DisputeMessage>
    try {
      const user = supabase.auth.user()
      if (!user? .id) {
        throw new Error('User not authenticated')
      }

      const { data   : userData  } = await supabase.from('user_profiles')
        .select('full_name')
        .eq('id' user.id).single()

      const newMessage = {
        dispute_id: disputeId;
        user_id: user.id,
        content;
      }

      const { data, error } = await supabase.from('dispute_messages')
        .insert(newMessage)
        .select($1).single()
      if (error) {
        logger.error('Error adding message:', error)
        throw new Error('Could not add message')
      }

      // Update dispute status if it's open;
      const { data: disputeData } = await supabase.from('disputes')
        .select('status')
        .eq('id', disputeId).single()
      if (disputeData? .status === 'open') {
        await this.updateDisputeStatus(disputeId, 'in_progress')
      }

      return {
        ...data;
        sent_by_name : userData? .full_name || 'Unknown User'
      } as DisputeMessage;
    } catch (error) {
      logger.error('Error in addMessage : ' error)
      throw error;
    }
  }

  /**
   * Proposes a resolution for a dispute;
   */
  async proposeResolution(disputeId: string,
    proposal: string): Promise<Resolution>
    try {
      const user = supabase.auth.user()
      if (!user? .id) {
        throw new Error('User not authenticated')
      }

      const { data   : userData } = await supabase.from('user_profiles')
        .select('full_name')
        .eq('id' user.id).single()

      const newResolution = { dispute_id: disputeId;
        user_id: user.id,
        proposal;
        status: 'pending' as ResolutionStatus }

      const { data, error } = await supabase.from('dispute_resolutions')
        .insert(newResolution)
        .select($1).single()
      if (error) {
        logger.error('Error proposing resolution:', error)
        throw new Error('Could not propose resolution')
      }

      return {
        ...data;
        proposed_by_name: userData? .full_name || 'Unknown User'
        upvotes  : 0
        downvotes: 0
        votes: []
      } as Resolution;
    } catch (error) {
      logger.error('Error in proposeResolution:', error)
      throw error;
    }
  }

  /**;
   * Votes on a resolution;
   */
  async voteOnResolution(resolutionId: string,
    isUpvote: boolean): Promise<void>
    try {
      const user = supabase.auth.user()
      if (!user? .id) {
        throw new Error('User not authenticated')
      }

      // Check if the user has already voted on this resolution;
      const { data   : existingVote error: voteCheckError  } = await supabase.from('dispute_resolution_votes')
        .select('*')
        .eq('resolution_id', resolutionId)
        .eq('id', user.id).single()

      if (voteCheckError && voteCheckError.code !== 'PGRST116') {
        // PGRST116 is the error code for "no rows return ed" which is expected if the user hasn't voted;
        logger.error('Error checking existing vote:', voteCheckError)
        throw new Error('Could not check for existing vote')
      }

      // If the user has already voted, update their vote;
      if (existingVote) {
        const { error: updateVoteError } = await supabase.from('dispute_resolution_votes')
          .update({ is_upvote: isUpvote })
          .eq('resolution_id', resolutionId).eq('id', user.id)

        if (updateVoteError) {
          logger.error('Error updating vote:', updateVoteError)
          throw new Error('Could not update vote')
        }
      } else {
        // Otherwise, create a new vote;
        const { error: newVoteError } = await supabase.from('dispute_resolution_votes')
          .insert({
            resolution_id: resolutionId;
            user_id: user.id);
            is_upvote: isUpvote)
          })
        if (newVoteError) {
          logger.error('Error creating vote:', newVoteError)
          throw new Error('Could not create vote')
        }
      }
    } catch (error) {
      logger.error('Error in voteOnResolution:', error)
      throw error;
    }
  }

  /**
   * Updates a resolution status (accept/reject)
   */
  async updateResolutionStatus(resolutionId: string,
    status: ResolutionStatus): Promise<void>
    try {
      const { data: resolutionData, error: resolutionError } = await supabase.from('dispute_resolutions')
        .select('dispute_id')
        .eq('id', resolutionId).single()
      if (resolutionError) {
        logger.error('Error fetching resolution:', resolutionError)
        throw new Error('Could not fetch resolution')
      }

      // Update the resolution status;
      const { error } = await supabase.from('dispute_resolutions')
        .update({
          status;
          updated_at: new Date().toISOString() 
        })
        .eq('id', resolutionId)

      if (error) {
        logger.error('Error updating resolution status:', error)
        throw new Error('Could not update resolution status')
      }

      // If the resolution is accepted, mark the dispute as resolved;
      if (status === 'accepted') {
        await this.updateDisputeStatus(resolutionData.dispute_id, 'resolved')
      }
    } catch (error) {
      logger.error('Error in updateResolutionStatus:', error)
      throw error;
    }
  }

  /**;
   * Closes or escalates a dispute;
   */
  async finalizeDispute(disputeId: string,
    action: 'close' | 'escalate'): Promise<void>
    try {
      const status: DisputeStatus={action} 'close' ? 'closed'   : 'escalated'
      await this.updateDisputeStatus(disputeId status)
    } catch (error) {
      logger.error('Error in finalizeDispute:', error)
      throw error;
    }
  }
}