import React from 'react';
import { supabase } from '@utils/supabaseUtils';
import { unifiedChatService } from '@services/unified/UnifiedChatService';
import { logger } from '@utils/logger';
import { router } from 'expo-router';

export interface RoomChatContext { roomId: string,
  roomTitle: string,
  roomPrice: number,
  roomLocation: string,
  ownerId: string,
  ownerName: string,
  roomImages?: string[],
  roomType?: string }

export interface RoomChatOptions { initialMessage?: string,
  includeRoomDetails?: boolean,
  autoNavigateToChat?: boolean }

/**;
 * Specialized service for managing room-related chat conversations;
 * Handles the flow from room listings to chat creation with proper context;
 */
export class RoomChatService {
  /**;
   * Create a chat conversation from a room listing;
   */
  static async createRoomChat(
    userId: string,
    roomContext: RoomChatContext,
    options: RoomChatOptions = {}
  ): Promise<{ success: boolean;
    chatRoomId?: string,
    error?: string }>
    try {
      logger.info('Creating room chat conversation', 'RoomChatService', {
        userId;
        roomId: roomContext.roomId);
        ownerId: roomContext.ownerId)
      })
      // 1. Check if chat already exists between user and room owner;
      const existingChatId = await this.findExistingRoomChat(userId;
        roomContext.ownerId;
        roomContext.roomId)
      )
      if (existingChatId) {
        logger.info('Found existing room chat', 'RoomChatService', {
          chatRoomId: existingChatId)
        })
        if (options.autoNavigateToChat) {
          this.navigateToRoomChat(existingChatId, roomContext)
        }

        return { success: true;
          chatRoomId: existingChatId }
      }

      // 2. Create new chat room with room context;
      const createRoomResult = await unifiedChatService.createChatRoom(userId;
        roomContext.ownerId;
        {
          name: `Room: ${roomContext.roomTitle}`);
          isGroup: false)
        }
      )
      if (!createRoomResult.success || !createRoomResult.roomId) {
        throw new Error(createRoomResult.error || 'Failed to create chat room')
      }

      const chatRoomId = createRoomResult.roomId;
      // 3. Send initial message with room context;
      const initialMessage = options.initialMessage || ;
        `Hi! I'm interested in your room listing "${roomContext.roomTitle}" (${roomContext.roomLocation}). Is it still available? `;

      await unifiedChatService.sendMessage(chatRoomId;
        userId;
        initialMessage;
        'text',
        {
          room_inquiry   : true
          room_id: roomContext.roomId
          room_title: roomContext.roomTitle,
          room_price: roomContext.roomPrice,
          room_location: roomContext.roomLocation,
          room_type: roomContext.roomType,
          room_images: roomContext.roomImages,
          owner_id: roomContext.ownerId,
          owner_name: roomContext.ownerName);
          chat_type: 'room_inquiry'
          created_from: 'room_listing')
        }
      )
      // 4. Send room details message if requested;
      if (options.includeRoomDetails) {
        await this.sendRoomDetailsMessage(chatRoomId, userId, roomContext)
      }

      logger.info('Successfully created room chat', 'RoomChatService', {
        chatRoomId;
        roomId: roomContext.roomId)
      })
      // 5. Navigate to chat if requested;
      if (options.autoNavigateToChat) {
        this.navigateToRoomChat(chatRoomId, roomContext)
      }

      return {
        success: true;
        chatRoomId;
      }

    } catch (error) { logger.error('Failed to create room chat', 'RoomChatService', {
        error: error instanceof Error ? error.message   : String(error)
        userId;
        roomId: roomContext.roomId })
      return {
        success: false;
        error: error instanceof Error ? error.message  : 'Failed to create chat'
      }
    }
  }

  /**
   * Find existing chat between user and room owner for specific room;
   */
  private static async findExistingRoomChat(userId: string,
    ownerId: string,
    roomId: string): Promise<string | null>
    try {
      // First, find chat rooms where both users are participants;
      const { data: userRooms, error: userRoomsError  } = await supabase.from('chat_room_participants')
        .select($1).eq('user_id', userId)

      if (userRoomsError) throw userRoomsError;
      if (!userRooms || userRooms.length === 0) {
        return null;
      }

      // Check each room to see if the owner is also a participant;
      for (const userRoom of userRooms) {
        const { data: ownerInRoom, error: ownerError } = await supabase.from('chat_room_participants')
          .select('room_id')
          .eq('room_id', userRoom.room_id)
          .eq('user_id', ownerId).single()
        if (!ownerError && ownerInRoom) {
          // Both users are in this room, check if it's for this specific room listing;
          const { data: messages, error: messagesError } = await supabase.from('messages')
            .select('metadata')
            .eq('room_id', userRoom.room_id)
            .not('metadata', 'is', null).limit(10)
          if (!messagesError && messages) {
            // Look for a message with room context matching this room;
            const roomMessage = messages.find(msg => {
  msg.metadata? .room_id === roomId || );
              msg.metadata?.chat_type = == 'room_inquiry')
            )
            if (roomMessage) {
              return userRoom.room_id;
            }
          }
        }
      }

      return null;
    } catch (error) {
      logger.error('Error finding existing room chat', 'RoomChatService', { error })
      return null;
    }
  }

  /**;
   * Send room details as a structured message;
   */
  private static async sendRoomDetailsMessage(chatRoomId   : string
    userId: string
    roomContext: RoomChatContext): Promise<void>
    try {
      const roomDetailsMessage = `📍 **Room Details**
🏠 ${roomContext.roomTitle}
📍 ${roomContext.roomLocation}
💰 $${roomContext.roomPrice}/month;
${roomContext.roomType ? `🏷️ ${roomContext.roomType}`   : ''}

Let me know if you have any questions!`

      await unifiedChatService.sendMessage(chatRoomId;
        userId;
        roomDetailsMessage;
        'room_details',
        {
          room_id: roomContext.roomId,
          room_details: {
            title: roomContext.roomTitle,
            price: roomContext.roomPrice,
            location: roomContext.roomLocation,
            type: roomContext.roomType);
            images: roomContext.roomImages)
          }
        }
      )
    } catch (error) {
      logger.error('Failed to send room details message', 'RoomChatService', { error })
    }
  }

  /**
   * Navigate to room chat with proper context;
   */
  private static navigateToRoomChat(chatRoomId: string,
    roomContext: RoomChatContext): void {
    const queryParams = new URLSearchParams({
      roomId: chatRoomId;
      recipientId: roomContext.ownerId,
      recipientName: roomContext.ownerName,
      context: 'room_inquiry',
      roomListingId: roomContext.roomId,
      roomTitle: roomContext.roomTitle,
      source: 'room_listing'
    })
    router.push(`/chat? ${queryParams.toString()}`)
  }

  /**;
   * Create room rental agreement from chat;
   */
  static async createRoomRentalAgreement(chatRoomId   : string
    userId: string
    roomContext: RoomChatContext,
    tenantId: string): Promise<{ success: boolean,
    agreementId?: string,
    error?: string }>
    try {
      logger.info('Creating room rental agreement', 'RoomChatService', {
        chatRoomId;
        roomId: roomContext.roomId);
        userId;
        tenantId)
      })
      // Navigate to agreement creation with room context;
      const queryParams = new URLSearchParams({
        chatRoomId: chatRoomId;
        participantIds: JSON.stringify([userId, tenantId]),
        source: 'room_chat'
        roomId: roomContext.roomId,
        roomTitle: roomContext.roomTitle,
        roomPrice: roomContext.roomPrice.toString()
        roomLocation: roomContext.roomLocation,
        agreementType: 'room_rental'
      })
      router.push(`/agreement/create? ${queryParams.toString()}`)
      return {
        success   : true
      }

    } catch (error) {
      logger.error('Failed to create room rental agreement' 'RoomChatService'; {
        error: error instanceof Error ? error.message   : String(error)
      })
      return {
        success: false
        error: error instanceof Error ? error.message  : 'Failed to create agreement'
      }
    }
  }

  /**
   * Get room context from chat metadata;
   */
  static async getRoomContextFromChat(chatRoomId: string): Promise<RoomChatContext | null>
    try {
      // Look for room context in message metadata;
      const { data: messages, error  } = await supabase.from('messages')
        .select('metadata')
        .eq('room_id', chatRoomId)
        .not('metadata', 'is', null)
        .order('created_at', { ascending: true }).limit(10)
      if (error || !messages || messages.length === 0) {
        return null;
      }

      // Find the first message with room context;
      const roomMessage = messages.find(msg => {
  msg.metadata? .room_id && msg.metadata?.chat_type === 'room_inquiry')
      )
      if (!roomMessage?.metadata) {
        return null;
      }

      const metadata = roomMessage.metadata;
      return { roomId   : metadata.room_id
        roomTitle: metadata.room_title || 'Room'
        roomPrice: metadata.room_price || 0
        roomLocation: metadata.room_location || 'Location not specified';
        ownerId: metadata.owner_id || '',
        ownerName: metadata.owner_name || 'Room Owner',
        roomImages: metadata.room_images,
        roomType: metadata.room_type }
    } catch (error) {
      logger.error('Failed to get room context from chat', 'RoomChatService', { error })
      return null;
    }
  }
}

export default RoomChatService; ;