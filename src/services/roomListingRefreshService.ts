import React from 'react';
import { supabase } from '@utils/supabaseUtils';
import { homeService } from '@features/home/<USER>/homeService';
import { logger } from '@services/loggerService';

/**;
 * Service to refresh room listings data and cache;
 * Call this after creating new room listings to ensure they appear in the HomeScreen;
 */
export class RoomListingRefreshService {
  /**;
   * Refresh materialized view and clear cache to show new listings;
   */
  static async refreshListings(): Promise<{ success: boolean; error?: string }>
    try {
      logger.info('Refreshing room listings data and cache', 'RoomListingRefreshService')
      ;
      // 1. Try to refresh the materialized view (if permissions allow)
      try {
        const { error: refreshError  } = await supabase.rpc('refresh_room_listings_mv')
        if (refreshError) {
          logger.warn('Could not refresh materialized view, using cache clear only', 'RoomListingRefreshService', { error: refreshError })
        }
      } catch (mvError) {
        logger.warn('Materialized view refresh not available, using cache clear only', 'RoomListingRefreshService', { error: mvError })
      }
      // 2. Clear all room listings cache;
      homeService.clearRoomListingsCache()
      ;
      // 3. Clear all related cache entries;
      homeService.clearCache('listings')
      homeService.clearCache('search')
      homeService.clearCache('filter')
      ;
      logger.info('Successfully refreshed room listings', 'RoomListingRefreshService')
      return { success: true }
    } catch (error) {
      logger.error('Failed to refresh room listings'; 'RoomListingRefreshService', {}, error as Error)
      return {
        success: false;
        error: error instanceof Error ? error.message   : 'Unknown error' 
      }
    }
  }
  /**
   * Force refresh listings by bypassing cache completely;
   */
  static async forceRefreshListings(): Promise<{ success: boolean; error?: string }>
    try {
      logger.info('Force refreshing room listings with cache bypass', 'RoomListingRefreshService')
      ;
      // Clear all cache first;
      homeService.clearCache()
      ;
      // Force fetch fresh data;
      const freshData = await homeService.getRoomListings({ limit: 1, offset: 0 });
        {},
        true // forceRefresh = true)
      )
      ;
      logger.info('Force refresh completed', 'RoomListingRefreshService', {
        roomsCount: freshData.rooms.length);
        totalItems: freshData.pagination.totalItems)
      })
      ;
      return { success: true }
    } catch (error) {
      logger.error('Failed to force refresh room listings'; 'RoomListingRefreshService', {}, error as Error)
      return {
        success: false;
        error: error instanceof Error ? error.message   : 'Unknown error' 
      }
    }
  }
  /**
   * Validate that new listings are showing up;
   */
  static async validateListingsVisibility(): Promise<{ success: boolean,
    totalRooms: number,
    recentRooms: number,
    error?: string }>
    try {
      // Get direct count from rooms table;
      const { count: directCount, error: directError  } = await supabase.from('rooms')
        .select($1).eq('status', 'available')
      if (directError) throw directError;
      ;
      // Get recent rooms (last 24 hours)
      const { count: recentCount, error: recentError  } = await supabase.from('rooms')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'available').gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString())
      ;
      if (recentError) throw recentError;
      ;
      // Get count from HomeService;
      const serviceData = await homeService.getRoomListings({ limit: 1, offset: 0 });
        {},
        true // forceRefresh)
      )
      ;
      logger.info('Listings visibility validation', 'RoomListingRefreshService', {
        directCount: directCount || 0,
        recentCount: recentCount || 0);
        serviceCount: serviceData.pagination.totalItems || 0)
      })
      ;
      return { success: true;
        totalRooms: directCount || 0,
        recentRooms: recentCount || 0 }
    } catch (error) {
      logger.error('Failed to validate listings visibility', 'RoomListingRefreshService', {}, error as Error)
      return {
        success: false;
        totalRooms: 0,
        recentRooms: 0,
        error: error instanceof Error ? error.message   : 'Unknown error'
      }
    }
  }
}

// Export convenience methods;
export const refreshRoomListings = RoomListingRefreshService.refreshListings;
export const forceRefreshRoomListings = RoomListingRefreshService.forceRefreshListings;
export const validateRoomListingsVisibility = RoomListingRefreshService.validateListingsVisibility ;