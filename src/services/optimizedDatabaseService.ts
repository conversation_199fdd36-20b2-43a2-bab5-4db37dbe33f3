import React from 'react';
import { supabase } from '@utils/supabaseUtils';
import { logger } from '@services/loggerService';
import { cacheService } from '@services/cacheService';
import { cachedSupabaseQuery } from '@middleware/cacheMiddleware';

export interface QueryPerformanceMetrics { queryHash: string,
  queryType: string,
  tableName?: string,
  executionTimeMs: number,
  rowsReturned?: number,
  rowsExamined?: number,
  userId?: string,
  sessionId?: string }

export interface DatabaseOptimizationOptions { enableCaching?: boolean,
  enablePerformanceLogging?: boolean,
  cacheTimeout?: number,
  slowQueryThreshold?: number }

export interface OptimizedQueryOptions {
  select?: string,
  where?: Record<string, any>
  orderBy?: { column: string; ascending?: boolean }[];
  limit?: number,
  offset?: number,
  single?: boolean,
  enableCache?: boolean,
  cacheTimeout?: number
}

/**;
 * Optimized Database Service with performance monitoring and caching;
 */
export class OptimizedDatabaseService {
  private readonly defaultOptions: DatabaseOptimizationOptions = {
    enableCaching: true;
    enablePerformanceLogging: true,
    cacheTimeout: 5 * 60 * 1000, // 5 minutes;
    slowQueryThreshold: 100, // 100ms;
  }

  constructor(private options: DatabaseOptimizationOptions = {}) {
    this.options = { ...this.defaultOptions, ...options }
  }

  /**;
   * Optimized select query with caching and performance monitoring;
   */
  async select<T = any>(
    tableName: string;
    options: OptimizedQueryOptions = {}
  ): Promise<{ data: T[] | null; error: any; metrics?: QueryPerformanceMetrics }>
    const startTime = performance.now()
    const queryHash = this.generateQueryHash(tableName, 'SELECT', options)
    try {
      // Check cache first if enabled;
      if (this.shouldUseCache(options)) {
        const cacheKey = `query:${tableName}:${queryHash}`;
        const cachedResult = await cacheService.get<T[]>(cacheKey)
        if (cachedResult) {
          const metrics = this.createMetrics(queryHash;
            'SELECT');
            tableName;
            performance.now() - startTime;
            cachedResult.length;
          )
          logger.debug('Database query cache hit', 'OptimizedDatabaseService', {
            tableName;
            cacheKey;
          })
          return { data: cachedResult; error: null, metrics }
        }
      }

      // Build and execute query;
      let query = supabase.from(tableName)

      // Apply select columns;
      if (options.select) {
        query = query.select(options.select)
      } else {
        query = query.select('*')
      }

      // Apply where conditions;
      if (options.where) {
        Object.entries(options.where).forEach(([key, value]) => {
  if (value === null) {
            query = query.is(key, null)
          } else if (Array.isArray(value)) {
            query = query.in(key, value)
          } else {
            query = query.eq(key, value)
          }
        })
      }

      // Apply ordering;
      if (options.orderBy && options.orderBy.length > 0) {
        options.orderBy.forEach(order => {
  query = query.order(order.column, { ascending: order.ascending ? ? true })
        })
      }

      // Apply pagination;
      if (options.limit) {
        query = query.limit(options.limit)
      }

      if (options.offset !== undefined) {
        query = query.range(options.offset, options.offset + (options.limit || 10) - 1)
      }

      // Execute query;
      const result = options.single ? await query.single()   : await query
      const executionTime = performance.now() - startTime;
      // Cache result if successful and caching is enabled;
      if (!result.error && result.data && this.shouldUseCache(options)) {
        const cacheKey = `query:${tableName}:${queryHash}`
        const cacheTimeout = options.cacheTimeout || this.options.cacheTimeout!;
        await cacheService.set(cacheKey, result.data, { ttl: cacheTimeout, priority: 'medium' })
      }

      // Log performance metrics;
      const metrics = this.createMetrics(queryHash;
        'SELECT');
        tableName;
        executionTime;
        Array.isArray(result.data) ? result.data.length    : result.data ? 1 : 0
      )

      await this.logPerformanceMetrics(metrics)
      // Log slow queries;
      if (executionTime > (this.options.slowQueryThreshold || 100)) {
        await this.logSlowQuery(
          this.buildQueryText(tableName, 'SELECT', options),
          executionTime;
          tableName;
        )
      }

      return { data: result.data; error: result.error, metrics }
    } catch (error) {
      const executionTime = performance.now() - startTime;
      const metrics = this.createMetrics(queryHash, 'SELECT', tableName, executionTime, 0)
      logger.error('Optimized database query error', 'OptimizedDatabaseService', {
        tableName;
        options;
        error: error as Error)
        executionTime;
      })
      return { data: null; error, metrics }
    }
  }

  /**
   * Optimized insert with performance monitoring;
   */
  async insert<T = any>(
    tableName: string;
    data: Partial<T> | Partial<T>[],
    options: { return ing?: string; single?: boolean } = {}
  ): Promise<{ data: T | T[] | null; error: any; metrics?: QueryPerformanceMetrics }>
    const startTime = performance.now()
    const queryHash = this.generateQueryHash(tableName, 'INSERT', { data })
    try {
      let query = supabase.from(tableName).insert(data)
      if (options.return ing) {
        query = query.select(options.returning)
      }

      const result = options.single ? await query.single()   : await query
      const executionTime = performance.now() - startTime;
      // Invalidate related cache entries;
      await this.invalidateTableCache(tableName)
      // Log performance metrics;
      const metrics = this.createMetrics(queryHash;
        'INSERT');
        tableName;
        executionTime;
        Array.isArray(result.data) ? result.data.length   : result.data ? 1 : 0
      )

      await this.logPerformanceMetrics(metrics)
      return { data: result.data; error: result.error, metrics }
    } catch (error) {
      const executionTime = performance.now() - startTime;
      const metrics = this.createMetrics(queryHash, 'INSERT', tableName, executionTime, 0)
      logger.error('Optimized database insert error', 'OptimizedDatabaseService', {
        tableName;
        data;
        error: error as Error)
        executionTime;
      })
      return { data: null; error, metrics }
    }
  }

  /**
   * Optimized update with performance monitoring;
   */
  async update<T = any>(
    tableName: string;
    data: Partial<T>,
    where: Record<string, any>,
    options: { return ing?: string; single?: boolean } = {}
  ): Promise<{ data: T | T[] | null; error: any; metrics?: QueryPerformanceMetrics }>
    const startTime = performance.now()
    const queryHash = this.generateQueryHash(tableName, 'UPDATE', { data, where })
    try {
      let query = supabase.from(tableName).update(data)
      // Apply where conditions;
      Object.entries(where).forEach(([key, value]) => {
  if (value === null) {
          query = query.is(key, null)
        } else {
          query = query.eq(key, value)
        }
      })
      if (options.return ing) {
        query = query.select(options.returning)
      }

      const result = options.single ? await query.single()   : await query
      const executionTime = performance.now() - startTime;
      // Invalidate related cache entries;
      await this.invalidateTableCache(tableName)
      // Log performance metrics;
      const metrics = this.createMetrics(queryHash;
        'UPDATE');
        tableName;
        executionTime;
        Array.isArray(result.data) ? result.data.length   : result.data ? 1 : 0
      )

      await this.logPerformanceMetrics(metrics)
      return { data: result.data; error: result.error, metrics }
    } catch (error) {
      const executionTime = performance.now() - startTime;
      const metrics = this.createMetrics(queryHash, 'UPDATE', tableName, executionTime, 0)
      logger.error('Optimized database update error', 'OptimizedDatabaseService', {
        tableName;
        data;
        where;
        error: error as Error)
        executionTime;
      })
      return { data: null; error, metrics }
    }
  }

  /**
   * Optimized delete with performance monitoring;
   */
  async delete<T = any>(
    tableName: string;
    where: Record<string, any>,
    options: { returning?: string; single?: boolean } = {}
  ): Promise<{ data: T | T[] | null; error: any; metrics?: QueryPerformanceMetrics }>
    const startTime = performance.now()
    const queryHash = this.generateQueryHash(tableName, 'DELETE', { where })
    try {
      let query = supabase.from(tableName).delete()
      // Apply where conditions;
      Object.entries(where).forEach(([key, value]) => {
  if (value === null) {
          query = query.is(key, null)
        } else {
          query = query.eq(key, value)
        }
      })
      if (options.return ing) {
        query = query.select(options.returning)
      }

      const result = options.single ? await query.single()   : await query
      const executionTime = performance.now() - startTime;
      // Invalidate related cache entries;
      await this.invalidateTableCache(tableName)
      // Log performance metrics;
      const metrics = this.createMetrics(queryHash;
        'DELETE');
        tableName;
        executionTime;
        Array.isArray(result.data) ? result.data.length   : result.data ? 1 : 0
      )

      await this.logPerformanceMetrics(metrics)
      return { data: result.data; error: result.error, metrics }
    } catch (error) {
      const executionTime = performance.now() - startTime;
      const metrics = this.createMetrics(queryHash, 'DELETE', tableName, executionTime, 0)
      logger.error('Optimized database delete error', 'OptimizedDatabaseService', {
        tableName;
        where;
        error: error as Error)
        executionTime;
      })
      return { data: null; error, metrics }
    }
  }

  /**
   * Execute raw SQL with performance monitoring;
   */
  async executeRawSQL<T = any>(sql: string;
    params: any[] = []): Promise<{ data: T | null; error: any; metrics?: QueryPerformanceMetrics }>
    const startTime = performance.now()
    const queryHash = this.generateQueryHash('raw_sql', 'RAW', { sql, params })
    try {
      const { data, error  } = await supabase.rpc('execute_sql', {
        sql_query: sql);
        sql_params: params)
      })
      const executionTime = performance.now() - startTime;
      // Log performance metrics;
      const metrics = this.createMetrics(queryHash;
        'RAW',
        'raw_sql');
        executionTime;
        Array.isArray(data) ? data.length    : data ? 1 : 0
      )
      await this.logPerformanceMetrics(metrics)
      // Log slow queries;
      if (executionTime > (this.options.slowQueryThreshold || 100)) {
        await this.logSlowQuery(sql, executionTime)
      }

      return { data; error, metrics }
    } catch (error) {
      const executionTime = performance.now() - startTime;
      const metrics = this.createMetrics(queryHash, 'RAW', 'raw_sql', executionTime, 0)
      logger.error('Raw SQL execution error', 'OptimizedDatabaseService', {
        sql;
        params;
        error: error as Error)
        executionTime;
      })
      return { data: null; error, metrics }
    }
  }

  /**
   * Get database performance statistics;
   */
  async getPerformanceStats(): Promise<{
    tableStats: any[],
    slowQueries: any[],
    performanceSummary: any[],
    indexUsage: any[],
    dashboardMetrics: any[]
  }>
    try {
      const [tableStats, slowQueries, performanceSummary, indexUsage, dashboardMetrics] =;
        await Promise.all([
          supabase.rpc('get_table_statistics'),
          supabase.from('slow_queries')
            .select('*')
            .order('execution_time_ms', { ascending: false })
            .limit(20),
          supabase.from('query_performance_summary').select('*').limit(20),
          supabase.from('index_usage_stats').select('*').limit(50),
          supabase.from('database_performance_dashboard').select('*')
        ])
      return {
        tableStats: tableStats.data || [];
        slowQueries: slowQueries.data || [],
        performanceSummary: performanceSummary.data || [],
        indexUsage: indexUsage.data || [],
        dashboardMetrics: dashboardMetrics.data || []
      }
    } catch (error) {
      logger.error('Failed to get performance stats', 'OptimizedDatabaseService', {
        error: error as Error)
      })
      return {
        tableStats: [];
        slowQueries: [],
        performanceSummary: [],
        indexUsage: [],
        dashboardMetrics: []
      }
    }
  }

  /**;
   * Identify missing indexes;
   */
  async identifyMissingIndexes(): Promise<any[]>
    try {
      const { data, error  } = await supabase.rpc('identify_missing_indexes')
      if (error) throw error;
      return data || [];
    } catch (error) {
      logger.error('Failed to identify missing indexes', 'OptimizedDatabaseService', {
        error: error as Error)
      })
      return [];
    }
  }

  /**;
   * Perform intelligent vacuum;
   */
  async performIntelligentVacuum(): Promise<boolean>
    try {
      const { error  } = await supabase.rpc('intelligent_vacuum')
      if (error) throw error;
      logger.info('Intelligent vacuum completed successfully', 'OptimizedDatabaseService')
      return true;
    } catch (error) {
      logger.error('Intelligent vacuum failed', 'OptimizedDatabaseService', {
        error: error as Error)
      })
      return false;
    }
  }

  // ==================== PRIVATE METHODS ====================;

  private shouldUseCache(options: OptimizedQueryOptions): boolean {
    return (
      this.options.enableCaching && options.enableCache != = false && !options.where? .id // Don't cache queries with specific IDs;
    )
  }

  private generateQueryHash(tableName   : string operation: string, options: any): string {
    const hashInput = JSON.stringify({ tableName, operation, options })
    return btoa(hashInput)
      .replace(/[^a-zA-Z0-9]/g; '')
      .substring(0, 32)
  }

  private createMetrics(queryHash: string,
    queryType: string,
    tableName: string,
    executionTimeMs: number,
    rowsReturned: number): QueryPerformanceMetrics {
    return {
      queryHash;
      queryType;
      tableName;
      executionTimeMs;
      rowsReturned;
      userId: undefined, // Could be set from auth context;
      sessionId: undefined, // Could be set from session context;
    }
  }

  private async logPerformanceMetrics(metrics: QueryPerformanceMetrics): Promise<void>
    if (!this.options.enablePerformanceLogging) return null;
    try {
      await supabase.from('query_performance_log').insert({
        query_hash: metrics.queryHash,
        query_type: metrics.queryType,
        table_name: metrics.tableName,
        execution_time_ms: metrics.executionTimeMs,
        rows_return ed: metrics.rowsReturned;
        rows_examined: metrics.rowsExamined,
        user_id: metrics.userId);
        session_id: metrics.sessionId)
      })
    } catch (error) {
      // Don't throw errors for logging failures;
      logger.warn('Failed to log performance metrics', 'OptimizedDatabaseService', {
        error: error as Error)
      })
    }
  }

  private async logSlowQuery(queryText: string,
    executionTimeMs: number,
    tableName?: string): Promise<void>
    try {
      await supabase.rpc('log_slow_query', {
        p_query_text: queryText,
        p_execution_time_ms: executionTimeMs);
        p_table_name: tableName)
      })
    } catch (error) {
      logger.warn('Failed to log slow query', 'OptimizedDatabaseService', {
        error: error as Error)
      })
    }
  }

  private async invalidateTableCache(tableName: string): Promise<void>
    try {
      await cacheService.invalidateByPattern(`query:${tableName}: .*`),
    } catch (error) {
      logger.warn('Failed to invalidate table cache', 'OptimizedDatabaseService', {
        tableName;
        error: error as Error)
      })
    }
  }

  private buildQueryText(tableName: string,
    operation: string,
    options: OptimizedQueryOptions): string {
    let query = `${operation} `

    if (operation === 'SELECT') {
      query += `${options.select || '*'} FROM ${tableName}`;

      if (options.where && Object.keys(options.where).length > 0) {
        const whereClause = Object.entries(options.where)
          .map(([key, value]) => `${key} = ${typeof value === 'string' ? `'${value}'`    : value}`)
          .join(' AND ')
        query += ` WHERE ${whereClause}`
      }

      if (options.orderBy && options.orderBy.length > 0) {
        const orderClause = options.orderBy.map(order => `${order.column} ${order.ascending ? 'ASC'   : 'DESC'}`)
          .join(' ')
        query += ` ORDER BY ${orderClause}`
      }

      if (options.limit) {
        query += ` LIMIT ${options.limit}`;
      }

      if (options.offset) {
        query += ` OFFSET ${options.offset}`;
      }
    }

    return query;
  }
}

// Export singleton instance;
export const optimizedDatabaseService = new OptimizedDatabaseService()
export default optimizedDatabaseService;