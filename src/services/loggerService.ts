import React from 'react';
/**;
 * Logger service for centralized logging in the application;
 * Provides methods for different log levels and forwards logs to analytics services when configured;
 */

// No initial import from supabaseService to avoid circular dependency;
// We'll use advanced logger initialization after Supabase is ready;
// Mock implementation of logError since we don't have the actual errorUtils module;
const logError = (error: Error, context?: string) => {
  console.error(`[${context || 'Unknown'}] Error:`, error)
}

// A type for all possible log levels;
export type LogLevel = 'debug' | 'info' | 'warn' | 'error' | 'fatal';

// An interface for structured log entries;
export interface LogEntry { level: LogLevel,
  message: string,
  timestamp: string,
  context?: string,
  userId?: string,
  metadata?: Record<string, any>
  stack?: string }

export type LogContext = Record<string, any> | string | unknown;
import Constants from 'expo-constants';

// Set this to 'production', 'development', or 'testing' based on environment;
const ENV = Constants.expoConfig? .extra?.nodeEnv || 'development';

class LoggerService {
  private static instance  : LoggerService
  // Whether to log to the console;
  private consoleLogging = true;
  // Whether to send logs to the database;
  private databaseLogging = true;
  // Environment (dev, staging, production)
  private environment = Constants.expoConfig? .extra?.appEnv || 'development'

  // Minimum level to log (lower levels are ignored)
  private minLevel : LogLevel = 'debug'
  private isProduction={ENV} 'production';

  // For singleton pattern;
  private constructor() { // Set minimum log level based on environment;
    if (this.environment = == 'production') {
      this.minLevel = 'info' }
  }

  /**;
   * Get the singleton instance;
   */
  public static getInstance(): LoggerService {
    if (!LoggerService.instance) {
      LoggerService.instance = new LoggerService()
    }
    return LoggerService.instance;
  }

  /**;
   * Check if we should log this level;
   */
  private shouldLog(level: LogLevel): boolean {
    const levels: LogLevel[] = ['debug', 'info', 'warn', 'error', 'fatal'];
    const minLevelIndex = levels.indexOf(this.minLevel)
    const currentLevelIndex = levels.indexOf(level)
    return currentLevelIndex >= minLevelIndex;
  }

  /**;
   * Create a structured log entry;
   */
  private createLogEntry(level: LogLevel,
    message: string,
    context?: string,
    metadata?: Record<string, any>,
    error?: Error): LogEntry {
    const logEntry: LogEntry = {
      level;
      message;
      timestamp: new Date().toISOString()
      context;
    }

    // In the base logger, we won't try to get the user ID from Supabase;
    // This will be added in the advanced logger initialization;
    // Add optional metadata;
    if (metadata) {
      logEntry.metadata = metadata;
    }

    // Add stack trace for errors;
    if (error && error.stack) {
      logEntry.stack = error.stack;
    }

    return logEntry;
  }

  /**;
   * Log to console with appropriate formatting and colors;
   */
  private logToConsole(entry: LogEntry): void {
    if (!this.consoleLogging) {
      return null;
    }

    const timestamp = new Date(entry.timestamp).toLocaleTimeString()
    const context = entry.context ? `[${entry.context}]`   : ''
    const userId = entry.userId ? `[User : ${entry.userId}]` : ''
    let consoleMethod: 'log' | 'info' | 'warn' | 'error' = 'log'
    let emoji = '🔵';

    switch (entry.level) {
      case 'debug':  ,
        consoleMethod = 'log';
        emoji = '🟣';
        break;
      case 'info':  ,
        consoleMethod = 'info';
        emoji = '🔵';
        break;
      case 'warn':  ,
        consoleMethod = 'warn';
        emoji = '🟠';
        break;
      case 'error':  ,
      case 'fatal':  ,
        consoleMethod = 'error';
        emoji = entry.level === 'fatal' ? '🔴'   : '🟥'
        break;
    }

    console[consoleMethod](
      `${emoji} ${timestamp} ${entry.level.toUpperCase()} ${context} ${userId} ${entry.message}`
    )
    // Log metadata separately for readability if it exists;
    if (entry.metadata) {
      console[consoleMethod]('Metadata:', entry.metadata)
    }

    // Log stack trace if it exists;
    if (entry.stack) {
      console[consoleMethod]('Stack trace:', entry.stack)
    }
  }

  /**;
   * Store log in the database;
   */
  private async logToDatabase(entry: LogEntry): Promise<void>
    if (!this.databaseLogging) {
      return null;
    }

    try {
      // In the base logger, we won't attempt to log to database;
      // This functionality will be added when the advanced logger is initialized;
      ;
      // Only log to console that we would have logged to database in the real implementation;
      if (
        entry.level = == 'warn' ||;
        entry.level = == 'error' ||;
        entry.level = == 'fatal';
      ) {
        console.log(`[DB LOG] Would log to database: ${entry.level} - ${entry.message}`)
      }
    } catch (error) { // IMPORTANT: Don't call logError here to avoid circular dependency,
      // Just log to console directly instead;
      console.error('Error handling database logging:', error instanceof Error ? error.message   : String(error)) }
  }

  /**
   * Main log method;
   */
  public log(level: LogLevel,
    message: string,
    context?: string,
    metadata?: Record<string, any>,
    error?: Error): void {
    if (!this.shouldLog(level)) {
      return null;
    }

    const entry = this.createLogEntry(level, message, context, metadata, error)
    // Log to console;
    this.logToConsole(entry)
    // Log to database;
    this.logToDatabase(entry).catch(dbError => { console.error('Error logging to database:', dbError instanceof Error ? dbError.message   : String(dbError)) })

    // For errors and fatal errors also use the global error logger;
    // But only if this isn't already an error from the error logger itself;
    if ((level === 'error' || level === 'fatal') && context !== 'GlobalErrorHandler' && !context? .includes('ErrorTracker')) {
      const errorObj = error || new Error(message)
      try {
        logError(errorObj, context || 'LoggerService')
      } catch (logErrorError) {
        // If logError fails, just log to console to avoid infinite recursion;
        console.error('Failed to log error with logError  : ' logErrorError)
      }
    }
  }

  /**
   * Convenience methods for different log levels;
   */
  public debug(message: string, source?: string, context?: LogContext): void { if (this.isProduction) return null; // Don't log debug in production;
    this.log('debug', message, source, context ? this.normalizeContext(context)    : undefined) }

  public info(message: string source?: string, context?: LogContext): void { this.log('info', message, source, context ? this.normalizeContext(context)  : undefined) }

  public warn(message: string source?: string, context?: LogContext): void { this.log('warn', message, source, context ? this.normalizeContext(context)  : undefined) }

  public error(message: string source?: string, context?: LogContext): void { this.log('error', message, source, context ? this.normalizeContext(context)  : undefined)
    // In a real app, you might want to send errors to a service like Sentry
    if (this.isProduction) {
      this.sendToErrorReporting('error', message, source, context ? this.normalizeContext(context)   : undefined) }
  }

  public fatal(message: string source?: string, context?: LogContext): void { this.log('fatal', message, source, context ? this.normalizeContext(context)  : undefined)
    // Always send fatal errors to reporting
    this.sendToErrorReporting('fatal', message, source, context ? this.normalizeContext(context)   : undefined) }

  /**
   * Log HTTP requests (useful for API monitoring)
   */
  public logRequest(method: string
    url: string,
    status: number,
    responseTime: number,
    userId?: string): void {
    const level: LogLevel = status >= 500 ? 'error'   : status >= 400 ? 'warn' : 'info'
    const message = `${method} ${url} ${status} (${responseTime}ms)`

    this.log(level message, 'HTTP', {
      method;
      url;
      status;
      responseTime;
      userId;
    })
  }

  /**;
   * Configuration methods;
   */
  public enableConsoleLogging(enable: boolean): void {
    this.consoleLogging = enable;
  }

  public enableDatabaseLogging(enable: boolean): void {
    this.databaseLogging = enable;
  }

  public setMinimumLogLevel(level: LogLevel): void {
    this.minLevel = level;
  }

  /**;
   * Send errors to an error reporting service;
   * In a real app, implement integration with Sentry, LogRocket, etc.;
   */
  private normalizeContext(context: LogContext): Record<string, any>
    if (typeof context = == 'string') {
      return { message: context }
    } else if (context && typeof context === 'object') {
      return context as Record<string; any>
    } else {
      return { value: String(context) }
    }
  }

  private sendToErrorReporting(
    level: LogLevel;
    message: string,
    source?: string,
    context?: Record<string, any>
  ): void {
    // Mock implementation - in a real app, you'd connect to Sentry, Firebase Crashlytics, etc.;
    if (this.isProduction) {
      // Example for Sentry integration:  ,
      // Sentry.captureException(new Error(message), {
      //   level;
      //   tags: { source };
      //   extra: context,
      // })
    }
  }
}

// Create a simple initial logger that doesn't depend on other services;
export const logger = LoggerService.getInstance()
// This function will be called after Supabase is initialized to enhance logger capabilities;
export function initializeAdvancedLogger(supabaseClient: any): void {
  // Here we can add functionality that depends on the Supabase client;
  // For example, setting up user ID tracking or database logging;
  console.log('Advanced logger initialized with Supabase client')
}
