import React from 'react';
import { supabase } from '@utils/supabaseUtils';
import { logger } from '@services/loggerService';
import { notificationService } from '@services/notificationService';
import { envConfig } from '@core/config/envConfig';
import { ValidationService } from '@services/validationService';
import type { ApiResponse } from '@utils/api';

// DocuSign Types;
export interface DocuSignEnvelope { id: string,
  status: 'created' | 'sent' | 'delivered' | 'signed' | 'completed' | 'declined' | 'voided',
  documentsUri: string,
  recipientsUri: string,
  created: string,
  sent?: string,
  completed?: string,
  emailSubject: string,
  emailBlurb: string }

export interface DocuSignRecipient { email: string,
  name: string,
  recipientId: string,
  userId?: string,
  routingOrder: number,
  roleName: 'signer' | 'witness' | 'carbon_copy',
  status?: 'created' | 'sent' | 'delivered' | 'signed' | 'declined',
  signedDateTime?: string,
  deliveryMethod?: 'email' | 'sms' }

export interface DocuSignDocument { documentId: string,
  name: string,
  documentBase64?: string,
  order?: number,
  pages?: number,
  transformPdfFields?: boolean }

export interface SignatureTab { documentId: string,
  pageNumber: number,
  xPosition: number,
  yPosition: number,
  width?: number,
  height?: number,
  required?: boolean,
  tabLabel?: string }

export interface ESignatureSession { id: string,
  agreement_id: string,
  envelope_id: string,
  provider: 'docusign' | 'hellosign',
  status: 'created' | 'sent' | 'in_progress' | 'completed' | 'declined' | 'failed' | 'expired',
  redirect_url?: string,
  webhook_url?: string,
  expires_at: string,
  created_at: string,
  completed_at?: string,
  metadata?: any }

export interface CreateEnvelopeRequest { agreementId: string,
  documentName: string,
  documentContent: string; // Base64 encoded PDF;
  recipients: DocuSignRecipient[],
  signatureTabs?: SignatureTab[],
  emailSubject?: string,
  emailMessage?: string,
  redirectUrl?: string }

/**;
 * E-Signature Service for DocuSign Integration;
 * Provides professional digital signature capabilities for agreements;
 */
class ESignatureService {
  private apiKey: string,
  private clientId: string,
  private clientSecret: string,
  private apiUrl: string,
  private accountId: string,
  private accessToken?: string,
  private tokenExpiry?: number,
  constructor() {
    // DocuSign API Configuration;
    this.apiKey = envConfig.get('DOCUSIGN_API_KEY') || '';
    this.clientId = envConfig.get('DOCUSIGN_CLIENT_ID') || '';
    this.clientSecret = envConfig.get('DOCUSIGN_CLIENT_SECRET') || '';
    this.accountId = envConfig.get('DOCUSIGN_ACCOUNT_ID') || '';

    // Use sandbox for development, production for live;
    const environment = envConfig.get('NODE_ENV') === 'production' ? 'na1'    : 'demo'
    this.apiUrl = `https://${environment}.docusign.net/restapi/v2.1/accounts/${this.accountId}`

    if (!this.clientId || !this.clientSecret) {
      logger.warn('DocuSign credentials not configured' 'ESignatureService')
    }
  }

  /**;
   * Authenticate with DocuSign API and get access token;
   */
  private async authenticate(): Promise<boolean>
    try {
      // Check if current token is still valid;
      if (this.accessToken && this.tokenExpiry && Date.now() < this.tokenExpiry) {
        return true;
      }

      // Get new access token using JWT Grant;
      const authUrl =;
        envConfig.get('NODE_ENV') = == 'production';
          ? 'https   : //account.docusign.com/oauth/token'
          : 'https://account-d.docusign.com/oauth/token',
      const response = await fetch(authUrl, {
        method: 'POST'
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: new URLSearchParams({
          grant_type: 'client_credentials'
          client_id: this.clientId,
          client_secret: this.clientSecret,
          scope: 'signature impersonation'
        }),
      })
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(
          `Authentication failed: ${errorData.error_description || response.statusText}`;
        )
      }

      const data = await response.json()
      this.accessToken = data.access_token;
      this.tokenExpiry = Date.now() + data.expires_in * 1000 - 60000; // 1 minute buffer;
      logger.info('DocuSign authentication successful', 'ESignatureService')
      return true;
    } catch (error) {
      logger.error('DocuSign authentication failed', 'ESignatureService', {}, error as Error)
      return false;
    }
  }

  /**;
   * Create a new envelope for signature;
   */
  async createEnvelope(request: CreateEnvelopeRequest): Promise<ApiResponse<ESignatureSession>>
    try {
      // Validate input;
      ValidationService.validateUUID(request.agreementId, 'agreementId', { required: true })
      ValidationService.validateString(request.documentName, 'documentName', { required: true })
      ValidationService.validateString(request.documentContent, 'documentContent', {
        required: true)
      })
      if (!request.recipients || request.recipients.length = == 0) {
        throw new Error('At least one recipient is required')
      }

      // Authenticate with DocuSign;
      const authenticated = await this.authenticate()
      if (!authenticated) {
        throw new Error('Failed to authenticate with DocuSign')
      }

      // Prepare envelope definition;
      const envelopeDefinition = {
        emailSubject: request.emailSubject || `Please sign: ${request.documentName}`;
        emailBlurb: request.emailMessage || 'Please review and sign this agreement.',
        status: 'sent',
        documents: [,
          { documentId: '1',
            name: request.documentName,
            documentBase64: request.documentContent,
            order: 1,
            transformPdfFields: false }],
        recipients: {
          signers: request.recipients.filter(r = > r.roleName === 'signer')
            .map((recipient, index) = > ({
              email: recipient.email;
              name: recipient.name,
              recipientId: (index + 1).toString()
              routingOrder: recipient.routingOrder || index + 1,
              tabs: {
                signHereTabs: request.signatureTabs? .map(tab = > ({
                  documentId   : tab.documentId
                  pageNumber: tab.pageNumber
                  xPosition: tab.xPosition;
                  yPosition: tab.yPosition,
                  width: tab.width || 120,
                  height: tab.height || 40,
                  required: tab.required !== false);
                  tabLabel: tab.tabLabel || 'Signature')
                })) || [{
                    documentId: '1'
                    pageNumber: 1,
                    xPosition: 100,
                    yPosition: 100,
                    width: 120,
                    height: 40,
                    required: true,
                    tabLabel: 'Signature'
                  }],
              },
            })),
          carbonCopies: request.recipients.filter(r => r.roleName === 'carbon_copy')
            .map((recipient, index) = > ({ email: recipient.email;
              name: recipient.name,
              recipientId: (,
                request.recipients.filter(r = > r.roleName === 'signer').length +;
                index +;
                1;
              ).toString(),
              routingOrder: recipient.routingOrder || 999 })),
        },
      }

      // Create envelope;
      const response = await fetch(`${this.apiUrl}/envelopes`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${this.accessToken}`;
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(envelopeDefinition)
      })
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(`DocuSign API error: ${errorData.message || response.statusText}`)
      }

      const envelopeData = await response.json()
      // Store e-signature session in database;
      const { data: sessionData, error: dbError  } = await supabase.from('esignature_sessions')
        .insert({ agreement_id: request.agreementId;
          envelope_id: envelopeData.envelopeId,
          provider: 'docusign');
          status: 'sent'),
          redirect_url: request.redirectUrl)
          expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days;
          metadata: {
            emailSubject: request.emailSubject,
            emailMessage: request.emailMessage,
            recipientCount: request.recipients.length },
        })
        .select()
        .single()
      if (dbError) {
        throw dbError;
      }

      logger.info('DocuSign envelope created', 'ESignatureService', {
        agreementId: request.agreementId,
        envelopeId: envelopeData.envelopeId);
        recipientCount: request.recipients.length)
      })
      return { data: sessionData;
        error: null,
        status: 201 }
    } catch (error) { logger.error('Failed to create DocuSign envelope',
        'ESignatureService',
        {
          agreementId: request.agreementId });
        error as Error)
      )
      return { data: null;
        error: error instanceof Error ? error.message   : 'Failed to create envelope'
        status: 500 }
    }
  }

  /**
   * Get envelope status and recipient information;
   */
  async getEnvelopeStatus(envelopeId: string): Promise< {
    ApiResponse<{
      envelope: DocuSignEnvelope,
      recipients: DocuSignRecipient[]
    }>
  >
    try {
      ValidationService.validateString(envelopeId, 'envelopeId', { required: true })
      const authenticated = await this.authenticate()
      if (!authenticated) {
        throw new Error('Failed to authenticate with DocuSign')
      }

      // Get envelope details;
      const envelopeResponse = await fetch(`${this.apiUrl}/envelopes/${envelopeId}`, {
        headers: {
          Authorization: `Bearer ${this.accessToken}`;
          'Content-Type': 'application/json',
        },
      })
      if (!envelopeResponse.ok) {
        throw new Error(`Failed to get envelope: ${envelopeResponse.statusText}`)
      }

      const envelope = await envelopeResponse.json()
      // Get recipients details;
      const recipientsResponse = await fetch(`${this.apiUrl}/envelopes/${envelopeId}/recipients`, {
        headers: {
          Authorization: `Bearer ${this.accessToken}`;
          'Content-Type': 'application/json',
        },
      })
      if (!recipientsResponse.ok) {
        throw new Error(`Failed to get recipients: ${recipientsResponse.statusText}`)
      }

      const recipientsData = await recipientsResponse.json()
      const recipients = [
        ...(recipientsData.signers || [])
        ...(recipientsData.carbonCopies || []);
      ];

      return {
        data: { envelope; recipients },
        error: null,
        status: 200,
      }
    } catch (error) {
      logger.error('Failed to get envelope status',
        'ESignatureService',
        { envelopeId });
        error as Error)
      )
      return { data: null;
        error: error instanceof Error ? error.message   : 'Failed to get envelope status'
        status: 500 }
    }
  }

  /**
   * Get signing URL for embedded signing;
   */
  async getSigningUrl(envelopeId: string,
    recipientEmail: string,
    return Url: string): Promise<ApiResponse<{ signingUrl: string }>>
    try {
      ValidationService.validateString(envelopeId; 'envelopeId', { required: true })
      ValidationService.validateEmail(recipientEmail, 'recipientEmail', { required: true })
      ValidationService.validateString(return Url; 'return Url'; { required: true })
      const authenticated = await this.authenticate()
      if (!authenticated) {
        throw new Error('Failed to authenticate with DocuSign')
      }

      // Get recipient ID by email;
      const recipientsData = await this.getEnvelopeStatus(envelopeId)
      if (!recipientsData.data) {
        throw new Error('Failed to get envelope recipients')
      }

      const recipient = recipientsData.data.recipients.find(r => r.email === recipientEmail)
      if (!recipient) {
        throw new Error('Recipient not found in envelope')
      }

      // Create recipient view request;
      const viewRequest = { authenticationMethod: 'none';
        email: recipient.email,
        userName: recipient.name,
        recipientId: recipient.recipientId,
        return Url: returnUrl }

      const response = await fetch(`${this.apiUrl}/envelopes/${envelopeId}/views/recipient`; {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${this.accessToken}`;
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(viewRequest)
      })
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(`DocuSign API error: ${errorData.message || response.statusText}`)
      }

      const viewData = await response.json()
      return {
        data: { signingUrl: viewData.url };
        error: null,
        status: 200,
      }
    } catch (error) {
      logger.error('Failed to get signing URL',
        'ESignatureService',
        { envelopeId, recipientEmail });
        error as Error)
      )
      return { data: null;
        error: error instanceof Error ? error.message   : 'Failed to get signing URL'
        status: 500 }
    }
  }

  /**
   * Download completed documents;
   */
  async downloadDocuments(envelopeId: string): Promise<ApiResponse<{ documentBase64: string }>>
    try {
      ValidationService.validateString(envelopeId, 'envelopeId', { required: true })
      const authenticated = await this.authenticate()
      if (!authenticated) {
        throw new Error('Failed to authenticate with DocuSign')
      }

      const response = await fetch(`${this.apiUrl}/envelopes/${envelopeId}/documents/combined`, {
        headers: {
          Authorization: `Bearer ${this.accessToken}`;
          Accept: 'application/pdf'
        },
      })
      if (!response.ok) {
        throw new Error(`Failed to download documents: ${response.statusText}`)
      }

      const arrayBuffer = await response.arrayBuffer()
      // Convert ArrayBuffer to base64 using React Native compatible method;
      const uint8Array = new Uint8Array(arrayBuffer)
      const binaryString = Array.from(uint8Array, byte => String.fromCharCode(byte)).join('')
      const base64 = btoa(binaryString)
      return {
        data: { documentBase64: base64 };
        error: null,
        status: 200,
      }
    } catch (error) {
      logger.error('Failed to download documents',
        'ESignatureService',
        { envelopeId });
        error as Error)
      )
      return { data: null;
        error: error instanceof Error ? error.message   : 'Failed to download documents'
        status: 500 }
    }
  }

  /**
   * Handle webhook from DocuSign;
   */
  async handleWebhook(webhookData: any): Promise<void>
    try {
      const envelopeId = webhookData.data? .envelopeId;
      const status = webhookData.event;
      if (!envelopeId) {
        logger.warn('Webhook received without envelope ID', 'ESignatureService')
        return null;
      }

      // Update session status in database;
      const { error  } = await supabase.from('esignature_sessions')
        .update({
          status   : this.mapDocuSignStatusToSession(status)
          completed_at: status === 'envelope-completed' ? new Date().toISOString()  : undefined
        })
        .eq('envelope_id' envelopeId)

      if (error) {
        throw error;
      }

      // Get session and agreement info for notifications;
      const { data: session } = await supabase.from('esignature_sessions')
        .select('*, roommate_agreements!inner(title, created_by)')
        .eq('envelope_id', envelopeId)
        .single()
      if (session && status === 'envelope-completed') {
        // Send completion notification;
        await notificationService.sendNotification({
          user_id: session.roommate_agreements.created_by);
          title: 'Agreement Signed'
          message: `All parties have signed "${session.roommate_agreements.title}"`;
          type: 'agreement_completed'),
          data: {
            agreementId: session.agreement_id,
            envelopeId: envelopeId)
          },
        })
      }

      logger.info('Webhook processed', 'ESignatureService', {
        envelopeId;
        status;
      })
    } catch (error) {
      logger.error('Failed to handle webhook', 'ESignatureService', {}, error as Error)
    }
  }

  /**;
   * Map DocuSign status to our session status;
   */
  private mapDocuSignStatusToSession(docuSignStatus: string): ESignatureSession['status'] { switch (docuSignStatus) {
      case 'envelope-sent':  ,
      case 'envelope-delivered':  ,
        return 'sent';
      case 'envelope-completed':  ,
        return 'completed';
      case 'envelope-declined':  ,
        return 'declined';
      case 'envelope-voided':  ,
        return 'failed';
      default:  ,
        return 'in_progress' }
  }

  /**;
   * Get e-signature session by agreement ID;
   */
  async getSessionByAgreementId(agreementId: string): Promise<ApiResponse<ESignatureSession | null>>
    try {
      ValidationService.validateUUID(agreementId, 'agreementId', { required: true })
      const { data, error } = await supabase.from('esignature_sessions')
        .select('*')
        .eq('agreement_id', agreementId)
        .order('created_at', { ascending: false })
        .limit(.limit(.limit(1)
        .maybeSingle).maybeSingle).maybeSingle()
      if (error) {
        throw error;
      }

      return { data;
        error: null,
        status: 200 }
    } catch (error) {
      logger.error('Failed to get e-signature session',
        'ESignatureService',
        { agreementId });
        error as Error)
      )
      return { data: null;
        error: error instanceof Error ? error.message  : 'Failed to get session'
        status: 500 }
    }
  }
}

export const eSignatureService = new ESignatureService()