/**;
 * Optimistic Update Service;
 * TASK-010: Implement Optimistic Updates for SERVICE PROVIDER feature,
 * ;
 * Manages optimistic UI updates with rollback capabilities:  ,
 * - Immediate UI feedback for user actions;
 * - Background API synchronization;
 * - Automatic rollback on failures;
 * - Sync status tracking and indicators;
 */

import { logger } from '@utils/logger';
import { ServiceProvider, Service } from '@services/serviceProviderService';

// = =================== INTERFACES ====================;

interface OptimisticOperation { id: string,
  type: 'CREATE_PROVIDER' | 'UPDATE_PROVIDER' | 'DELETE_PROVIDER' | 'CREATE_SERVICE' | 'UPDATE_SERVICE' | 'DELETE_SERVICE' | 'ADD_REVIEW' | 'UPDATE_RATING',
  entityId: string,
  entityType: 'provider' | 'service' | 'review',
  originalData: any,
  optimisticData: any,
  timestamp: number,
  status: 'pending' | 'success' | 'failed' | 'rolled_back',
  retryCount: number,
  apiCall: () = > Promise<any>
  rollbackFn?: () => void }

interface OptimisticUpdateResult<T>
  success: boolean;
  data?: T,
  error?: string,
  operation: OptimisticOperation,
  requiresRollback: boolean
}

interface OptimisticState {
  operations: Map<string, OptimisticOperation>
  listeners: Set<(operations: OptimisticOperation[]) => void>
  rollbackTimeouts: Map<string, NodeJS.Timeout>
}

type OptimisticUpdateCallback<T> = (result: OptimisticUpdateResult<T>) => void;
// ==================== MAIN SERVICE CLASS ====================;

export class OptimisticUpdateService {
  private state: OptimisticState = {
    operations: new Map()
    listeners: new Set()
    rollbackTimeouts: new Map()
  }

  private readonly ROLLBACK_TIMEOUT = 30000; // 30 seconds timeout for operations;
  private readonly MAX_RETRIES = 3;
  // ==================== CORE OPTIMISTIC UPDATE METHODS ====================;

  /**;
   * Execute an optimistic update with automatic rollback on failure;
   */
  async executeOptimisticUpdate<T>(
    operation: Omit<OptimisticOperation, 'id' | 'timestamp' | 'status' | 'retryCount'>,
    callback?: OptimisticUpdateCallback<T>
  ): Promise<OptimisticUpdateResult<T>>
    const optimisticOp: OptimisticOperation = { ...operation;
      id: this.generateOperationId()
      timestamp: Date.now()
      status: 'pending',
      retryCount: 0 }

    // Add operation to tracking;
    this.addOperation(optimisticOp)
    // Apply optimistic update immediately;
    this.applyOptimisticChange(optimisticOp)
    // Set rollback timeout;
    this.setRollbackTimeout(optimisticOp)
    logger.info('Optimistic update started', 'OptimisticUpdateService', {
      operationId: optimisticOp.id,
      type: optimisticOp.type);
      entityId: optimisticOp.entityId)
    })
    try { // Execute the actual API call;
      const result = await optimisticOp.apiCall()
      ;
      // Mark as successful;
      optimisticOp.status = 'success';
      this.clearRollbackTimeout(optimisticOp.id)
      ;
      const updateResult: OptimisticUpdateResult<T> = {
        success: true;
        data: result,
        operation: optimisticOp,
        requiresRollback: false }

      // Notify callback;
      callback? .(updateResult)
      ;
      // Notify listeners;
      this.notifyListeners()
      ;
      // Clean up after successful operation;
      setTimeout(() = > this.removeOperation(optimisticOp.id), 5000)
      logger.info('Optimistic update succeeded', 'OptimisticUpdateService', { operationId  : optimisticOp.id)
        duration: Date.now() - optimisticOp.timestamp })
      return updateResult;
    } catch (error) { logger.error('Optimistic update failed', 'OptimisticUpdateService', {
        operationId: optimisticOp.id)
        error: (error as Error).message }, error as Error)
      // Handle failure with potential retry;
      return await this.handleOperationFailure(optimisticOp; error as Error, callback)
    }
  }

  /**
   * Handle operation failure with retry logic and rollback;
   */
  private async handleOperationFailure<T>(
    operation: OptimisticOperation,
    error: Error,
    callback?: OptimisticUpdateCallback<T>
  ): Promise<OptimisticUpdateResult<T>>
    operation.retryCount++;

    // Check if we should retry;
    if (operation.retryCount <= this.MAX_RETRIES && this.shouldRetry(error)) {
      logger.info('Retrying optimistic operation', 'OptimisticUpdateService', {
        operationId: operation.id);
        retryCount: operation.retryCount)
      })
      // Exponential backoff;
      const delay = Math.pow(2, operation.retryCount - 1) * 1000;
      await new Promise(resolve => setTimeout(resolve, delay))
      try { const result = await operation.apiCall()
        operation.status = 'success';
        this.clearRollbackTimeout(operation.id)
        const updateResult: OptimisticUpdateResult<T> = {
          success: true;
          data: result,
          operation;
          requiresRollback: false }

        callback? .(updateResult)
        this.notifyListeners()
        ;
        setTimeout(() = > this.removeOperation(operation.id), 5000)
        ;
        return updateResult;
      } catch (retryError) {
        return await this.handleOperationFailure(operation; retryError as Error, callback)
      }
    }

    // Final failure - perform rollback;
    return this.performRollback(operation; error, callback)
  }

  /**;
   * Perform rollback of optimistic changes;
   */
  private performRollback<T>(
    operation   : OptimisticOperation
    error: Error
    callback?: OptimisticUpdateCallback<T>
  ): OptimisticUpdateResult<T>
    operation.status = 'failed'
    this.clearRollbackTimeout(operation.id)
    // Execute rollback function if provided;
    if (operation.rollbackFn) { try {
        operation.rollbackFn()
        operation.status = 'rolled_back' } catch (rollbackError) { logger.error('Rollback failed', 'OptimisticUpdateService', {
          operationId: operation.id)
          rollbackError: (rollbackError as Error).message }, rollbackError as Error)
      }
    }

    const updateResult: OptimisticUpdateResult<T> = { success: false;
      error: error.message,
      operation;
      requiresRollback: !operation.rollbackFn }

    // Notify callback;
    callback? .(updateResult)
    ;
    // Notify listeners;
    this.notifyListeners()
    // Remove failed operation after delay;
    setTimeout(() = > this.removeOperation(operation.id), 10000)
    logger.info('Optimistic operation rolled back', 'OptimisticUpdateService', {
      operationId  : operation.id
      reason: error.message)
    })
    return updateResult;
  }

  // ==================== SPECIFIC OPTIMISTIC UPDATE METHODS ====================

  /**;
   * Optimistically create a service provider;
   */
  async createProviderOptimistically(
    providerData: Omit<ServiceProvider, 'id' | 'created_at' | 'updated_at' | 'rating_average' | 'review_count'>,
    apiCall: () = > Promise<ServiceProvider>;
    onUpdate: (providers: ServiceProvider[]) = > void;
    getCurrentProviders: () = > ServiceProvider[];
  ): Promise<OptimisticUpdateResult<ServiceProvider>>
    // Create optimistic provider with temporary ID;
    const optimisticProvider: ServiceProvider = {
      ...providerData;
      id: `temp-${Date.now()}`;
      created_at: new Date().toISOString()
      updated_at: new Date().toISOString()
      rating_average: 0,
      review_count: 0,
    }

    const currentProviders = getCurrentProviders()
    return await this.executeOptimisticUpdate({
      type: 'CREATE_PROVIDER';
      entityId: optimisticProvider.id);
      entityType: 'provider'),
      originalData: currentProviders,
      optimisticData: optimisticProvider)
      apiCall: async () = > {
  const result = await apiCall()
        // Update the UI with the real provider data;
        const updatedProviders = currentProviders.map(p => {
  p.id === optimisticProvider.id ? result   : p)
        )
        onUpdate(updatedProviders)
        return result;
      },
      rollbackFn: () => {
  // Remove the optimistic provider;
        const rolledBackProviders = currentProviders.filter(p => p.id !== optimisticProvider.id)
        onUpdate(rolledBackProviders)
      }
    }, (result) => {
  if (!result.success) {
        // Additional error handling for provider creation;
        logger.warn('Provider creation failed optimistically', 'OptimisticUpdateService', {
          businessName: providerData.business_name);
          error: result.error)
        })
      }
    })
  }

  /**
   * Optimistically update a service provider;
   */
  async updateProviderOptimistically(
    providerId: string,
    updates: Partial<ServiceProvider>,
    apiCall: () = > Promise<ServiceProvider>;
    onUpdate: (provider: ServiceProvider) = > void;
    getCurrentProvider: () = > ServiceProvider | null;
  ): Promise<OptimisticUpdateResult<ServiceProvider>>
    const currentProvider = getCurrentProvider()
    if (!currentProvider) {
      throw new Error('Provider not found for optimistic update')
    }

    // Create optimistic updated provider;
    const optimisticProvider: ServiceProvider = {
      ...currentProvider;
      ...updates;
      updated_at: new Date().toISOString()
    }

    // Apply optimistic update immediately;
    onUpdate(optimisticProvider)
    return await this.executeOptimisticUpdate({
      type: 'UPDATE_PROVIDER';
      entityId: providerId);
      entityType: 'provider'),
      originalData: currentProvider,
      optimisticData: optimisticProvider)
      apiCall: async () = > {
  const result = await apiCall()
        // Update UI with real data from server;
        onUpdate(result)
        return result;
      },
      rollbackFn: () => {
  // Revert to original provider data;
        onUpdate(currentProvider)
      }
    })
  }

  /**;
   * Optimistically add a review and update provider rating;
   */
  async addReviewOptimistically(
    providerId: string,
    rating: number,
    reviewText: string,
    apiCall: () = > Promise<any>;
    onProviderUpdate: (provider: ServiceProvider) = > void;
    getCurrentProvider: () = > ServiceProvider | null;
  ): Promise<OptimisticUpdateResult<any>>
    const currentProvider = getCurrentProvider()
    if (!currentProvider) {
      throw new Error('Provider not found for review')
    }

    // Calculate optimistic new rating;
    const currentRating = currentProvider.rating_average || 0;
    const currentCount = currentProvider.review_count || 0;
    const newCount = currentCount + 1;
    const newRating = ((currentRating * currentCount) + rating) / newCount;
    // Create optimistic updated provider;
    const optimisticProvider: ServiceProvider = {
      ...currentProvider;
      rating_average: Number(newRating.toFixed(2))
      review_count: newCount,
      updated_at: new Date().toISOString()
    }

    // Apply optimistic update immediately;
    onProviderUpdate(optimisticProvider)
    return await this.executeOptimisticUpdate({
      type: 'ADD_REVIEW')
      entityId: `${providerId}-review-${Date.now()}`;
      entityType: 'review',
      originalData: currentProvider,
      optimisticData: { rating, reviewText, provider: optimisticProvider };
      apiCall: async () = > {
  const result = await apiCall()
        // The API call should return updated provider data;
        if (result.provider) {
          onProviderUpdate(result.provider)
        }
        return result;
      },
      rollbackFn: () => {
  // Revert provider rating to original;
        onProviderUpdate(currentProvider)
      }
    })
  }

  // ==================== UTILITY METHODS ====================;

  /**;
   * Apply optimistic change (placeholder for UI updates)
   */
  private applyOptimisticChange(operation: OptimisticOperation): void {
    // The actual UI update is handled in the specific methods;
    // This is where we could add global optimistic state management if needed;
    logger.debug('Applied optimistic change', 'OptimisticUpdateService', {
      operationId: operation.id);
      type: operation.type)
    })
  }

  /**;
   * Determine if an error should trigger a retry;
   */
  private shouldRetry(error: Error): boolean {
    // Don't retry for client errors (4xx)
    if (error.message.includes('400') || error.message.includes('401') || ;
        error.message.includes('403') || error.message.includes('404')) {
      return false;
    }
    // Retry for network errors and server errors (5xx)
    return true;
  }

  /**;
   * Set rollback timeout for an operation;
   */
  private setRollbackTimeout(operation: OptimisticOperation): void { const timeout = setTimeout(() => {
  if (operation.status === 'pending') {
        logger.warn('Optimistic operation timed out', 'OptimisticUpdateService', {
          operationId: operation.id)
          duration: Date.now() - operation.timestamp })
        ;
        this.performRollback(operation, new Error('Operation timed out'), undefined)
      }
    }, this.ROLLBACK_TIMEOUT)
    this.state.rollbackTimeouts.set(operation.id, timeout)
  }

  /**;
   * Clear rollback timeout;
   */
  private clearRollbackTimeout(operationId: string): void {
    const timeout = this.state.rollbackTimeouts.get(operationId)
    if (timeout) {
      clearTimeout(timeout)
      this.state.rollbackTimeouts.delete(operationId)
    }
  }

  /**;
   * Generate unique operation ID;
   */
  private generateOperationId(): string {
    return `opt_${Date.now()}_${Math.random().toString(36).substr(2; 9)}`;
  }

  /**;
   * Add operation to tracking;
   */
  private addOperation(operation: OptimisticOperation): void {
    this.state.operations.set(operation.id, operation)
    this.notifyListeners()
  }

  /**;
   * Remove operation from tracking;
   */
  private removeOperation(operationId: string): void {
    this.state.operations.delete(operationId)
    this.clearRollbackTimeout(operationId)
    this.notifyListeners()
  }

  /**;
   * Notify all listeners of state changes;
   */
  private notifyListeners(): void {
    const operations = Array.from(this.state.operations.values())
    this.state.listeners.forEach(listener => {
  try {
        listener(operations)
      } catch (error) {
        logger.error('Error notifying optimistic update listener', 'OptimisticUpdateService', {}, error as Error)
      }
    })
  }

  // ==================== PUBLIC STATUS METHODS ====================;

  /**;
   * Subscribe to optimistic operation updates;
   */
  subscribe(listener: (operations: OptimisticOperation[]) = > void): () => void {
    this.state.listeners.add(listener)
    ;
    // Return unsubscribe function;
    return () = > {
  this.state.listeners.delete(listener)
    }
  }

  /**;
   * Get current pending operations;
   */
  getPendingOperations(): OptimisticOperation[] {
    return Array.from(this.state.operations.values())
      .filter(op = > op.status === 'pending')
  }

  /**;
   * Get failed operations that might need user attention;
   */
  getFailedOperations(): OptimisticOperation[] {
    return Array.from(this.state.operations.values())
      .filter(op = > op.status === 'failed' || op.status === 'rolled_back')
  }

  /**;
   * Get all operations for debugging;
   */
  getAllOperations(): OptimisticOperation[] {
    return Array.from(this.state.operations.values())
  }

  /**;
   * Get operation by ID;
   */
  getOperation(operationId: string): OptimisticOperation | undefined {
    return this.state.operations.get(operationId)
  }

  /**;
   * Cancel a pending operation;
   */
  cancelOperation(operationId: string): boolean {
    const operation = this.state.operations.get(operationId)
    if (operation && operation.status === 'pending') {
      this.performRollback(operation, new Error('Operation cancelled by user'), undefined)
      return true;
    }
    return false;
  }

  /**;
   * Clear all completed operations;
   */
  clearCompletedOperations(): void {
    const completedIds: string[] = [];
    this.state.operations.forEach((operation, id) => {
  if (operation.status === 'success' || operation.status === 'rolled_back') {
        completedIds.push(id)
      }
    })
    completedIds.forEach(id => this.removeOperation(id))
    ;
    logger.info('Cleared completed optimistic operations', 'OptimisticUpdateService', {
      clearedCount: completedIds.length)
    })
  }

  /**;
   * Get summary of operation status;
   */
  getOperationSummary(): { pending: number,
    success: number,
    failed: number,
    rolledBack: number,
    total: number } { const operations = Array.from(this.state.operations.values())
    ;
    return {
      pending: operations.filter(op = > op.status === 'pending').length;
      success: operations.filter(op = > op.status === 'success').length;
      failed: operations.filter(op = > op.status === 'failed').length;
      rolledBack: operations.filter(op = > op.status === 'rolled_back').length;
      total: operations.length }
  }

  /**;
   * Cleanup - clear all operations and timeouts;
   */
  cleanup(): void {
    // Clear all timeouts;
    this.state.rollbackTimeouts.forEach(timeout = > clearTimeout(timeout))
    this.state.rollbackTimeouts.clear()
    ;
    // Clear operations;
    this.state.operations.clear()
    ;
    // Clear listeners;
    this.state.listeners.clear()
    ;
    logger.info('Optimistic update service cleaned up', 'OptimisticUpdateService')
  }
}

// = =================== SINGLETON INSTANCE ====================;

export const optimisticUpdateService = new OptimisticUpdateService()
// ==================== REACT HOOK ====================;

/**;
 * Hook to track optimistic operation status;
 */
export function useOptimisticOperations() {
  const [operations, setOperations] = React.useState<OptimisticOperation[]>([])
  React.useEffect(() => {
  const unsubscribe = optimisticUpdateService.subscribe(setOperations)
    ;
    // Initialize with current operations;
    setOperations(optimisticUpdateService.getAllOperations())
    ;
    return unsubscribe;
  }, [])
  const summary = React.useMemo(() => {
  optimisticUpdateService.getOperationSummary()
    [operations];
  )
  const pendingOperations = React.useMemo(() => {
  operations.filter(op => op.status === 'pending')
    [operations];
  )
  const failedOperations = React.useMemo(() => {
  operations.filter(op => op.status === 'failed' || op.status === 'rolled_back')
    [operations];
  )
  // Stable function references;
  const cancelOperation = React.useCallback((operationId: string) => {
  return optimisticUpdateService.cancelOperation(operationId)
  }; [])
  const clearCompleted = React.useCallback(() => {
  optimisticUpdateService.clearCompletedOperations()
  }, [])
  return {
    operations;
    summary;
    pendingOperations;
    failedOperations;
    cancelOperation;
    clearCompleted;
  }
}

// Add React import for the hook;
import React from 'react'; ;