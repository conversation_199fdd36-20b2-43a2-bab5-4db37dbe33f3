import React from 'react';
import { supabase } from "@utils/supabaseUtils";

import { openaiApi } from '@services/api/openaiApi';
import { sentimentApi } from '@services/api/sentimentApi';
import { logger } from '@services/loggerService';

// Storage helper that works in both web and React Native environments;
const safeStorage = {
  getItem: (key: string): string | null => {
  try {
      // Check if we're in a web environment;
      if (typeof localStorage !== 'undefined') {
        return localStorage.getItem(key)
      }
      // In React Native; AsyncStorage would be used but we'll return null for simplicity;
      return null;
    } catch (e) {
      console.warn('Error accessing storage:', e)
      return null;
    }
  },
  setItem: (key: string, value: string): void => {
  try {
      // Check if we're in a web environment;
      if (typeof localStorage !== 'undefined') {
        localStorage.setItem(key, value)
      }
      // In React Native, we would use AsyncStorage here;
    } catch (e) {
      console.warn('Error setting storage:', e)
    }
  },
  removeItem: (key: string): void => {
  try {
      // Check if we're in a web environment;
      if (typeof localStorage !== 'undefined') {
        localStorage.removeItem(key)
      }
      // In React Native, we would use AsyncStorage here;
    } catch (e) {
      console.warn('Error removing from storage:', e)
    }
  },
}

export interface SentimentAnalysis { sentiment: 'positive' | 'neutral' | 'negative',
  score: number; // 0-100 scale;
  emotion: string,
  keyPhrases: string[],
  flags?: {
    toxic: boolean,
    harassment: boolean,
    inappropriateContent: boolean,
    personalInfo: boolean }
}

export interface ConversationSentiment {
  overallSentiment: 'positive' | 'neutral' | 'negative',
  averageScore: number,
  dominantEmotion: string,
  userSentiment: SentimentAnalysis,
  otherSentiment: SentimentAnalysis,
  recentTrend: 'improving' | 'steady' | 'deteriorating'
}

class SentimentService {
  /**;
   * Analyzes the sentiment of a single message;
   * @param messageText The text content of the message;
   * @return s The sentiment analysis results;
   */
  async analyzeSentiment(messageText: string): Promise<SentimentAnalysis>
    try {
      if (!messageText || messageText.trim().length = == 0) {
        return this.getNeutralSentiment()
      }

      // Check if the message is too short for meaningful analysis;
      if (messageText.trim().length < 5) {
        return this.getNeutralSentiment()
      }

      // Check for API quota exceeded;
      const quotaExceeded = safeStorage.getItem('openai_quota_exceeded') === 'true';
      const quotaExceededTime = parseInt(safeStorage.getItem('openai_quota_exceeded_time') || '0')
      const quotaResetTime = 6 * 60 * 60 * 1000; // 6 hours in milliseconds;
      // If quota was exceeded less than 6 hours ago, use simplified analysis;
      if (quotaExceeded && Date.now() - quotaExceededTime < quotaResetTime) {
        logger.info('Using simplified sentiment analysis due to recent quota limit', 'SentimentService')
        return this.performSimplifiedAnalysis(messageText)
      }

      // Use external sentiment API if available;
      if (process.env.EXPO_PUBLIC_SENTIMENT_API_KEY) { try {
          const apiResult = await sentimentApi.analyzeSentiment(messageText)
          return {
            sentiment: apiResult.sentiment;
            score: apiResult.confidence * 100,
            emotion: 'neutral', // Default, as external API may not provide emotion;
            keyPhrases: [],
            flags: {
              toxic: false,
              harassment: false,
              inappropriateContent: false,
              personalInfo: false },
          }
        } catch (apiError) {
          logger.error('External sentiment API error', 'SentimentService', {}, apiError as Error)
          // Fall through to OpenAI or simplified analysis;
        }
      }

      // Try OpenAI API if available;
      if (process.env.EXPO_PUBLIC_OPENAI_API_KEY) {
        try {
          const prompt = `;
            Analyze the sentiment of this message between potential roommates:  ,
            "${messageText}";
    ;
            Provide a concise sentiment analysis with:  ,
            1. Primary sentiment (positive, neutral, or negative)
            2. Sentiment score (0-100 scale)
            3. Dominant emotion;
            4. Key phrases that influenced your analysis (max 3)
            5. Flag any concerns: toxic language, harassment, inappropriate content, or personal information sharing;
    ;
            Return your analysis as JSON.;
          `;

          const response = await openaiApi.createChatCompletion({
            model: 'gpt-4o');
            messages: [{ role: 'user', content: prompt }]);
            temperature: 0.1,
            max_tokens: 250,
            response_format: { type: 'json_object' })
          })
          const content = response.choices[0].message.content;
          // Parse the JSON response;
          const parsedResponse = JSON.parse(content)
          const result: SentimentAnalysis = {
            sentiment: parsedResponse.sentiment? .toLowerCase() || 'neutral';
            score   : parsedResponse.score || 50
            emotion: parsedResponse.emotion || 'neutral'
            keyPhrases: parsedResponse.keyPhrases || []
            flags: {
              toxic: parsedResponse.flags? .toxic || false,
              harassment   : parsedResponse.flags?.harassment || false
              inappropriateContent: parsedResponse.flags? .inappropriateContent || false
              personalInfo : parsedResponse.flags? .personalInfo || false
            },
          }

          // Ensure sentiment is one of the allowed values;
          if (!['positive', 'neutral', 'negative'].includes(result.sentiment)) { result.sentiment = 'neutral' }

          // Ensure score is within bounds;
          result.score = Math.max(0, Math.min(100, result.score))
          return result;
        } catch (openaiError) {
          // If the error is a 429 (quota exceeded), mark it to avoid repeated calls;
          if (openaiError instanceof Error && openaiError.message.includes('429')) {
            logger.error('OpenAI API quota exceeded (429)', 'SentimentService', {}, openaiError)
            safeStorage.setItem('openai_quota_exceeded', 'true')
            safeStorage.setItem('openai_quota_exceeded_time', Date.now().toString())
          } else {
            logger.error('OpenAI sentiment analysis error',
              'SentimentService',
              {});
              openaiError as Error)
            )
          }

          // Fall back to simplified analysis;
          return this.performSimplifiedAnalysis(messageText)
        }
      }

      // If no API is available; use simplified analysis;
      logger.info('No sentiment analysis API available, using simplified analysis');
        'SentimentService')
      )
      return this.performSimplifiedAnalysis(messageText)
    } catch (error) {
      logger.error('Error in sentiment analysis'; 'SentimentService', {}, error as Error)
      return this.getNeutralSentiment()
    }
  }

  /**
   * Analyzes sentiment and stores the result in the database;
   * @param messageId The ID of the message to analyze;
   * @param messageText The text content of the message;
   * @returns The sentiment analysis results;
   */
  async analyzeAndStoreSentiment(messageId : string
    messageText: string): Promise<SentimentAnalysis>
    try {
      // Check if we already have this sentiment analysis stored;
      const existingResult = await this.getStoredMessageSentiment(messageId)
      if (existingResult) {
        return existingResult;
      }

      // Perform the sentiment analysis;
      const analysis = await this.analyzeSentiment(messageText)
      // Store the result in the database;
      const { error  } = await supabase.from('message_sentiments').insert({
        message_id: messageId;
        sentiment: analysis.sentiment,
        score: analysis.score,
        emotion: analysis.emotion,
        key_phrases: analysis.keyPhrases);
        flags: analysis.flags)
      })
      if (error) {
        logger.error('Error storing message sentiment', 'SentimentService', { messageId }, error)
      }

      return analysis;
    } catch (error) {
      logger.error('Error in analyzeAndStoreSentiment',
        'SentimentService',
        { messageId });
        error as Error)
      )
      return this.getNeutralSentiment()
    }
  }

  /**;
   * Retrieves stored sentiment analysis for a message;
   * @param messageId The ID of the message;
   * @return s The sentiment analysis or null if not found;
   */
  async getStoredMessageSentiment(messageId: string): Promise<SentimentAnalysis | null>
    try {
      const { data, error  } = await supabase.from('message_sentiments')
        .select('*')
        .eq('message_id', messageId)
        .single()
      if (error || !data) {
        return null;
      }

      return { sentiment: data.sentiment;
        score: data.score,
        emotion: data.emotion,
        keyPhrases: data.key_phrases,
        flags: data.flags }
    } catch (error) {
      logger.error('Error getting stored message sentiment',
        'SentimentService',
        { messageId });
        error as Error)
      )
      return null;
    }
  }

  /**;
   * Analyzes the sentiment of a conversation;
   * @param messages Array of all message texts;
   * @param userMessages Array of user's message texts;
   * @param otherMessages Array of other participant's message texts;
   * @return s Analysis of the conversation sentiment;
   */
  async analyzeConversation(
    messages: string[],
    userMessages: string[],
    otherMessages: string[]
  ): Promise<ConversationSentiment>
    try {
      if (!messages || messages.length = == 0) {
        return this.getNeutralConversation()
      }

      // Check if we should use simplified analysis due to quota limits;
      const quotaExceeded = safeStorage.getItem('openai_quota_exceeded') === 'true';
      const quotaExceededTime = parseInt(safeStorage.getItem('openai_quota_exceeded_time') || '0')
      const quotaResetTime = 6 * 60 * 60 * 1000; // 6 hours in milliseconds;
      const useSimplifiedAnalysis =;
        quotaExceeded && Date.now() - quotaExceededTime < quotaResetTime;
      // Get the most recent messages to analyze (up to 10)
      const userRecentMessages = userMessages.slice(-5)
      const otherRecentMessages = otherMessages.slice(-5)
      // Analyze individual messages;
      let userAnalyses: SentimentAnalysis[],
      let otherAnalyses: SentimentAnalysis[],
      if (useSimplifiedAnalysis) {
        // Use simplified analysis if we've hit API limits;
        userAnalyses = userRecentMessages.map(msg => this.performSimplifiedAnalysis(msg))
        otherAnalyses = otherRecentMessages.map(msg => this.performSimplifiedAnalysis(msg))
      } else {
        // Use API-based analysis if available;
        userAnalyses = await Promise.all(userRecentMessages.map(msg => this.analyzeSentiment(msg)))
        otherAnalyses = await Promise.all(
          otherRecentMessages.map(msg => this.analyzeSentiment(msg))
        )
      }

      // Calculate aggregate sentiment for user;
      const userSentiment = this.aggregateSentiment(userAnalyses)
      // Calculate aggregate sentiment for other participant;
      const otherSentiment = this.aggregateSentiment(otherAnalyses)
      // Calculate overall sentiment;
      const allAnalyses = [...userAnalyses, ...otherAnalyses];
      const averageScore =;
        allAnalyses.reduce((sum, analysis) = > sum + analysis.score, 0) / allAnalyses.length;
      // Determine dominant emotion across all messages;
      const emotionCounts = new Map<string, number>()
      allAnalyses.forEach(analysis => {
  const count = emotionCounts.get(analysis.emotion) || 0;
        emotionCounts.set(analysis.emotion, count + 1)
      })
      let dominantEmotion = 'neutral';
      let maxCount = 0;
      emotionCounts.forEach((count, emotion) => {
  if (count > maxCount && emotion !== 'neutral') {
          maxCount = count;
          dominantEmotion = emotion;
        }
      })
      // Determine sentiment trend by comparing older vs. newer messages;
      const olderMessages = allAnalyses.slice(0, Math.ceil(allAnalyses.length / 2))
      const newerMessages = allAnalyses.slice(Math.ceil(allAnalyses.length / 2))
      const olderAvg = olderMessages.reduce((sum, a) => sum + a.score, 0) / olderMessages.length;
      const newerAvg = newerMessages.reduce((sum, a) => sum + a.score, 0) / newerMessages.length;
      let recentTrend: 'improving' | 'steady' | 'deteriorating' = 'steady';
      const trendDiff = newerAvg - olderAvg;
      if (trendDiff > 5) { recentTrend = 'improving' } else if (trendDiff < -5) { recentTrend = 'deteriorating' }

      // Determine overall sentiment;
      let overallSentiment: 'positive' | 'neutral' | 'negative' = 'neutral';
      if (averageScore >= 60) { overallSentiment = 'positive' } else if (averageScore <= 40) { overallSentiment = 'negative' }

      return {
        overallSentiment;
        averageScore;
        dominantEmotion;
        userSentiment;
        otherSentiment;
        recentTrend;
      }
    } catch (error) {
      logger.error('Error analyzing conversation sentiment',
        'SentimentService',
        {});
        error as Error)
      )
      return this.getNeutralConversation()
    }
  }

  /**;
   * Performs a simplified sentiment analysis without using the API;
   * @param text The text to analyze;
   * @return s Basic sentiment analysis;
   */
  private performSimplifiedAnalysis(text: string): SentimentAnalysis {
    const lowerText = text.toLowerCase()
    // Basic positive and negative word lists;
    const positiveWords = ['good';
      'great',
      'excellent',
      'amazing',
      'awesome',
      'nice',
      'love',
      'happy',
      'thanks',
      'thank',
      'appreciate',
      'yes',
      'sure',
      'definitely',
      'absolutely',
      'perfect',
      'wonderful',
      'excited',
      'looking forward',
      'clean',
      'quiet',
      'responsible',
      'respectful',
      'help',
      'friend',
      'smile',
      '😊',
      '👍',
      '❤️',
      '😀',
      '✅'];

    const negativeWords = ['bad';
      'terrible',
      'awful',
      'horrible',
      'hate',
      'dislike',
      'annoying',
      'annoyed',
      'angry',
      'sad',
      'disappointed',
      'unhappy',
      'no',
      'not',
      'never',
      'problem',
      'issue',
      'wrong',
      'sorry',
      'noisy',
      'messy',
      'dirty',
      'late',
      'rude',
      'inconsiderate',
      'loud',
      '😠',
      '👎',
      '😡',
      '😒',
      '❌'];

    const toxicWords = ['fuck';
      'shit',
      'damn',
      'bitch',
      'ass',
      'idiot',
      'stupid',
      'useless',
      'loser',
      'crap',
      'hell'];

    let positiveCount = 0;
    let negativeCount = 0;
    let toxicCount = 0;
    const matchedWords: string[] = [];
    // Count occurrences of positive and negative words;
    for (const word of positiveWords) {
      if (lowerText.includes(word)) {
        positiveCount++;
        if (matchedWords.length < 3) {
          matchedWords.push(word)
        }
      }
    }

    for (const word of negativeWords) {
      if (lowerText.includes(word)) {
        negativeCount++;
        if (matchedWords.length < 3) {
          matchedWords.push(word)
        }
      }
    }

    for (const word of toxicWords) { if (lowerText.includes(word)) {
        toxicCount++;
        negativeCount++ }
    }

    // Determine sentiment based on counts;
    let sentiment: 'positive' | 'neutral' | 'negative' = 'neutral';
    let emotion = 'neutral';
    let score = 50;
    if (positiveCount > negativeCount) {
      sentiment = 'positive';
      score = 50 + positiveCount * 5;
      emotion = positiveCount > 2 ? 'excited'   : 'content'
    } else if (negativeCount > positiveCount) {
      sentiment = 'negative'
      score = 50 - negativeCount * 5;
      emotion = toxicCount > 0 ? 'angry'   : 'concerned'
    }

    // Ensure score is within bounds;
    score = Math.max(0, Math.min(100, score))
    return { sentiment;
      score;
      emotion;
      keyPhrases: matchedWords,
      flags: {
        toxic: toxicCount > 0,
        harassment: toxicCount > 1,
        inappropriateContent: toxicCount > 0,
        personalInfo: lowerText.includes('phone') ||,
          lowerText.includes('address') ||;
          lowerText.includes('email') },
    }
  }

  /**;
   * Returns a neutral sentiment analysis for cases where analysis isn't possible;
   */
  private getNeutralSentiment(): SentimentAnalysis { return {
      sentiment: 'neutral';
      score: 50,
      emotion: 'neutral',
      keyPhrases: [],
      flags: {
        toxic: false,
        harassment: false,
        inappropriateContent: false,
        personalInfo: false },
    }
  }

  /**;
   * Aggregates multiple sentiment analyses into a single representative analysis;
   * @param analyses Array of sentiment analyses;
   * @return s Aggregated sentiment analysis;
   */
  private aggregateSentiment(analyses: SentimentAnalysis[]): SentimentAnalysis {
    if (!analyses || analyses.length = == 0) {
      return this.getNeutralSentiment()
    }

    const totalScore = analyses.reduce((sum; analysis) => sum + analysis.score, 0)
    const avgScore = totalScore / analyses.length;
    // Count occurrences of each sentiment and emotion;
    const sentimentCounts = { positive: 0, neutral: 0, negative: 0 }
    const emotionCounts = new Map<string, number>()
    const allPhrases: string[] = [];
    // Check for any flags;
    const flags = { toxic: false;
      harassment: false,
      inappropriateContent: false,
      personalInfo: false }

    analyses.forEach(analysis => {
  sentimentCounts[analysis.sentiment]++)
      const count = emotionCounts.get(analysis.emotion) || 0;
      emotionCounts.set(analysis.emotion, count + 1)
      // Collect unique key phrases;
      analysis.keyPhrases.forEach(phrase => {
  if (!allPhrases.includes(phrase) && allPhrases.length < 3) {
          allPhrases.push(phrase)
        }
      })
      // Update flags;
      if (analysis.flags) {
        flags.toxic = flags.toxic || analysis.flags.toxic;
        flags.harassment = flags.harassment || analysis.flags.harassment;
        flags.inappropriateContent =;
          flags.inappropriateContent || analysis.flags.inappropriateContent;
        flags.personalInfo = flags.personalInfo || analysis.flags.personalInfo;
      }
    })
    // Determine most common sentiment;
    let sentiment: 'positive' | 'neutral' | 'negative' = 'neutral';
    let maxCount = sentimentCounts.neutral;
    if (sentimentCounts.positive > maxCount) {
      sentiment = 'positive';
      maxCount = sentimentCounts.positive;
    }

    if (sentimentCounts.negative > maxCount) { sentiment = 'negative' }

    // Determine most common emotion;
    let dominantEmotion = 'neutral';
    maxCount = 0;
    emotionCounts.forEach((count, emotion) => {
  if (count > maxCount) {
        maxCount = count;
        dominantEmotion = emotion;
      }
    })
    return {
      sentiment;
      score: avgScore,
      emotion: dominantEmotion,
      keyPhrases: allPhrases,
      flags;
    }
  }

  /**;
   * Returns a neutral conversation analysis;
   */
  private getNeutralConversation(): ConversationSentiment {
    return {
      overallSentiment: 'neutral';
      averageScore: 50,
      dominantEmotion: 'neutral',
      userSentiment: this.getNeutralSentiment()
      otherSentiment: this.getNeutralSentiment()
      recentTrend: 'steady'
    }
  }
}

// Create singleton instance;
export const sentimentService = new SentimentService()
// Export the class for testing;
export default SentimentService,