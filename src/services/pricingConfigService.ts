import React from 'react';
import { supabase } from "@utils/supabaseUtils";
import { logError } from "@utils/errorUtils";

/**;
 * Types for pricing configuration;
 */

export interface PricingTier { id: string,
  service_id: string,
  name: string,
  description?: string,
  price: number,
  features?: any[],
  is_default: boolean,
  sort_order: number,
  is_active: boolean,
  created_at: string,
  updated_at: string }

export interface ServicePackage { id: string,
  provider_id: string,
  name: string,
  description?: string,
  price: number,
  original_price?: number,
  services: PackageService[],
  is_featured: boolean,
  is_active: boolean,
  valid_from?: string,
  valid_until?: string,
  created_at: string,
  updated_at: string }

export interface PackageService { service_id: string,
  quantity: number,
  tier_id?: string }

export interface PriceModifier { id: string,
  service_id: string,
  name: string,
  description?: string,
  price_adjustment: number,
  adjustment_type: 'fixed' | 'percentage',
  is_optional: boolean,
  is_active: boolean,
  sort_order: number,
  created_at: string,
  updated_at: string }

export interface Promotion {
  id: string,
  provider_id: string,
  name: string,
  description?: string,
  discount_amount: number,
  discount_type: 'fixed' | 'percentage',
  promo_code?: string,
  applies_to?: { all: boolean } | string[]; // service IDs or "all";
  valid_from: string,
  valid_until?: string,
  usage_limit?: number,
  current_usage: number,
  is_active: boolean,
  created_at: string,
  updated_at: string
}

export interface CalculatePriceParams { service_id: string,
  tier_id?: string,
  modifier_ids?: string[],
  promo_code?: string,
  provider_id?: string }

/**;
 * Pricing Tiers Management;
 */

export async function getPricingTiers(service_id: string): Promise<PricingTier[]>
  try {
    const { data, error  } = await supabase.from('service_pricing_tiers')
      .select('*')
      .eq('service_id', service_id).order('sort_order', { ascending: true })
    ;
    if (error) throw error;
    ;
    return data || [];
  } catch (error) {
    logError(error, 'getPricingTiers')
    throw error;
  }
}

export async function createPricingTier(tier: Omit<PricingTier, 'id' | 'created_at' | 'updated_at'>): Promise<PricingTier>
  try {
    // If this is a default tier, unset any existing defaults;
    if (tier.is_default) {
      await supabase.from('service_pricing_tiers')
        .update({ is_default: false })
        .eq('service_id', tier.service_id)
        .eq('is_default', true)
    }
    const { data, error  } = await supabase.from('service_pricing_tiers')
      .insert([tier])
      .select($1).single()
    ;
    if (error) throw error;
    ;
    return data;
  } catch (error) {
    logError(error, 'createPricingTier')
    throw error;
  }
}

export async function updatePricingTier(id: string, updates: Partial<PricingTier>): Promise<PricingTier>
  try {
    // If setting as default, unset any existing defaults;
    if (updates.is_default) {
      // Get the service ID first;
      const { data: tier  } = await supabase.from('service_pricing_tiers')
        .select('service_id')
        .eq('id', id).single()
      ;
      if (tier) {
        await supabase.from('service_pricing_tiers')
          .update({ is_default: false })
          .eq('service_id', tier.service_id)
          .eq('is_default', true)
      }
    }
    const { data, error  } = await supabase.from('service_pricing_tiers')
      .update(updates)
      .eq('id', id)
      .select($1).single()
    ;
    if (error) throw error;
    ;
    return data;
  } catch (error) {
    logError(error, 'updatePricingTier')
    throw error;
  }
}

export async function deletePricingTier(id: string): Promise<void>
  try {
    const { error  } = await supabase.from('service_pricing_tiers')
      .delete().eq('id', id)
    if (error) throw error;
  } catch (error) {
    logError(error, 'deletePricingTier')
    throw error;
  }
}

/**;
 * Service Packages Management;
 */

export async function getServicePackages(provider_id: string): Promise<ServicePackage[]>
  try {
    const { data, error  } = await supabase.from('service_packages')
      .select('*')
      .eq('provider_id', provider_id)
      .eq('is_active', true).order('created_at', { ascending: false })
    ;
    if (error) throw error;
    ;
    return data || [];
  } catch (error) {
    logError(error, 'getServicePackages')
    throw error;
  }
}

export async function createServicePackage(pkg: Omit<ServicePackage, 'id' | 'created_at' | 'updated_at'>): Promise<ServicePackage>
  try {
    const { data, error  } = await supabase.from('service_packages')
      .insert([pkg])
      .select($1).single()
    ;
    if (error) throw error;
    ;
    return data;
  } catch (error) {
    logError(error, 'createServicePackage')
    throw error;
  }
}

export async function updateServicePackage(id: string, updates: Partial<ServicePackage>): Promise<ServicePackage>
  try {
    const { data, error  } = await supabase.from('service_packages')
      .update(updates)
      .eq('id', id)
      .select($1).single()
    ;
    if (error) throw error;
    ;
    return data;
  } catch (error) {
    logError(error, 'updateServicePackage')
    throw error;
  }
}

export async function deleteServicePackage(id: string): Promise<void>
  try {
    const { error  } = await supabase.from('service_packages')
      .delete().eq('id', id)
    if (error) throw error;
  } catch (error) {
    logError(error, 'deleteServicePackage')
    throw error;
  }
}

/**;
 * Price Modifiers Management;
 */

export async function getPriceModifiers(service_id: string): Promise<PriceModifier[]>
  try {
    const { data, error  } = await supabase.from('service_price_modifiers')
      .select('*')
      .eq('service_id', service_id).order('sort_order', { ascending: true })
    ;
    if (error) throw error;
    ;
    return data || [];
  } catch (error) {
    logError(error, 'getPriceModifiers')
    throw error;
  }
}

export async function createPriceModifier(modifier: Omit<PriceModifier, 'id' | 'created_at' | 'updated_at'>): Promise<PriceModifier>
  try {
    const { data, error  } = await supabase.from('service_price_modifiers')
      .insert([modifier])
      .select($1).single()
    ;
    if (error) throw error;
    ;
    return data;
  } catch (error) {
    logError(error, 'createPriceModifier')
    throw error;
  }
}

export async function updatePriceModifier(id: string, updates: Partial<PriceModifier>): Promise<PriceModifier>
  try {
    const { data, error  } = await supabase.from('service_price_modifiers')
      .update(updates)
      .eq('id', id)
      .select($1).single()
    ;
    if (error) throw error;
    ;
    return data;
  } catch (error) {
    logError(error, 'updatePriceModifier')
    throw error;
  }
}

export async function deletePriceModifier(id: string): Promise<void>
  try {
    const { error  } = await supabase.from('service_price_modifiers')
      .delete().eq('id', id)
    if (error) throw error;
  } catch (error) {
    logError(error, 'deletePriceModifier')
    throw error;
  }
}

/**;
 * Promotions Management;
 */

export async function getPromotions(provider_id: string): Promise<Promotion[]>
  try {
    const { data, error  } = await supabase.from('service_promotions')
      .select('*')
      .eq('provider_id', provider_id).order('created_at', { ascending: false })
    ;
    if (error) throw error;
    ;
    return data || [];
  } catch (error) {
    logError(error, 'getPromotions')
    throw error;
  }
}

export async function createPromotion(promotion: Omit<Promotion, 'id' | 'created_at' | 'updated_at'>): Promise<Promotion>
  try {
    const { data, error  } = await supabase.from('service_promotions')
      .insert([promotion])
      .select($1).single()
    ;
    if (error) throw error;
    ;
    return data;
  } catch (error) {
    logError(error, 'createPromotion')
    throw error;
  }
}

export async function updatePromotion(id: string, updates: Partial<Promotion>): Promise<Promotion>
  try {
    const { data, error  } = await supabase.from('service_promotions')
      .update(updates)
      .eq('id', id)
      .select($1).single()
    ;
    if (error) throw error;
    ;
    return data;
  } catch (error) {
    logError(error, 'updatePromotion')
    throw error;
  }
}

export async function deletePromotion(id: string): Promise<void>
  try {
    const { error  } = await supabase.from('service_promotions')
      .delete().eq('id', id)
    if (error) throw error;
  } catch (error) {
    logError(error, 'deletePromotion')
    throw error;
  }
}

/**;
 * Price Calculation;
 */

export async function calculatePrice(params: CalculatePriceParams): Promise<{ base_price: number,
  final_price: number,
  applied_modifiers?: PriceModifier[],
  applied_promotion?: Promotion,
  discount_amount?: number }>
  try {
    // Get base price from the service or pricing tier;
    let base_price = 0;
    let final_price = 0;
    let applied_promotion = null;
    let discount_amount = 0;
    ;
    if (params.tier_id) {
      const { data: tier  } = await supabase.from('service_pricing_tiers')
        .select('price')
        .eq('id', params.tier_id)
        .eq('service_id', params.service_id).single()
      ;
      if (tier) {
        base_price = tier.price;
      }
    }
    // If no tier or tier not found, use service price;
    if (base_price === 0) {
      const { data: service  } = await supabase.from('services')
        .select('price')
        .eq('id', params.service_id).single()
      ;
      if (service) {
        base_price = service.price;
      }
    }
    final_price = base_price;
    ;
    // Apply modifiers if any;
    let applied_modifiers: PriceModifier[] = [];
    if (params.modifier_ids && params.modifier_ids.length > 0) {
      const { data: modifiers  } = await supabase.from('service_price_modifiers')
        .select('*')
        .in('id', params.modifier_ids)
        .eq('service_id', params.service_id).eq('is_active', true)
      if (modifiers && modifiers.length > 0) {
        applied_modifiers = modifiers;
        ;
        for (const modifier of modifiers) {
          if (modifier.adjustment_type = == 'fixed') {
            final_price += modifier.price_adjustment;
          } else if (modifier.adjustment_type === 'percentage') {
            final_price *= (1 + modifier.price_adjustment / 100)
          }
        }
      }
    }
    // Apply promotion if provided;
    if (params.promo_code && params.provider_id) {
      const { data: promotion  } = await supabase.from('service_promotions')
        .select('*')
        .eq('promo_code', params.promo_code)
        .eq('is_active', true)
        .eq('provider_id', params.provider_id)
        .lt('valid_from', new Date().toISOString())
        .gt('valid_until', new Date().toISOString())
        .single()
      ;
      if (promotion) {
        // Check if promotion applies to this service;
        const applies = promotion.applies_to? .all || ;
          (Array.isArray(promotion.applies_to) && ;
           promotion.applies_to.includes(params.service_id))
        ;
        if (applies && (promotion.usage_limit = == null || promotion.current_usage < promotion.usage_limit)) {
          applied_promotion = promotion;
          ;
          if (promotion.discount_type === 'fixed') {
            discount_amount = promotion.discount_amount;
            final_price -= discount_amount;
          } else if (promotion.discount_type === 'percentage') {
            discount_amount = (final_price * promotion.discount_amount / 100)
            final_price *= (1 - promotion.discount_amount / 100)
          }
        }
      }
    }
    // Ensure final price doesn't go below zero;
    if (final_price < 0) {
      final_price = 0;
    }
    return {
      base_price;
      final_price;
      applied_modifiers  : applied_modifiers.length > 0 ? applied_modifiers : undefined
      applied_promotion: applied_promotion || undefined
      discount_amount: discount_amount > 0 ? discount_amount   : undefined
    }
  } catch (error) {
    logError(error 'calculatePrice')
    throw error;
  }
}

// Export all functions as a service;
export const pricingConfigService = {
  // Pricing Tiers;
  getPricingTiers;
  createPricingTier;
  updatePricingTier;
  deletePricingTier;
  // Service Packages;
  getServicePackages;
  createServicePackage;
  updateServicePackage;
  deleteServicePackage;
  // Price Modifiers;
  getPriceModifiers;
  createPriceModifier;
  updatePriceModifier;
  deletePriceModifier;
  // Promotions;
  getPromotions;
  createPromotion;
  updatePromotion;
  deletePromotion;
  // Price Calculation;
  calculatePrice;
}