import React from 'react';
/**;
 * Database Initialization Service;
 * Handles database setup, migrations, and schema management;
 */

import { SupabaseClient } from '@supabase/supabase-js';
import { logger } from '@services/loggerService';
import { DatabaseService } from '@services/databaseService';
import { ValidationService } from '@services/validationService';

// Database schema version;
export const CURRENT_SCHEMA_VERSION = '1.0.0';

// Table definitions for initial setup;
export interface TableDefinition {
  name: string,
  columns: ColumnDefinition[],
  indexes?: IndexDefinition[],
  constraints?: ConstraintDefinition[],
  policies?: PolicyDefinition[]
}

export interface ColumnDefinition { name: string,
  type: string,
  nullable?: boolean,
  defaultValue?: string,
  primaryKey?: boolean,
  unique?: boolean,
  references?: {
    table: string,
    column: string,
    onDelete?: 'CASCADE' | 'RESTRICT' | 'SET NULL' | 'SET DEFAULT' | 'NO ACTION' }
}

export interface IndexDefinition { name: string,
  columns: string[],
  unique?: boolean }

export interface ConstraintDefinition { name: string,
  type: 'CHECK' | 'UNIQUE' | 'FOREIGN KEY' | 'PRIMARY KEY',
  definition: string }

export interface PolicyDefinition {
  name: string,
  operation: 'ALL' | 'SELECT' | 'INSERT' | 'UPDATE' | 'DELETE',
  expression: string,
  roles?: string[]
}

// Migration definition;
export interface Migration {
  version: string,
  description: string,
  up: string[],
  down: string[]
}

export class DatabaseInitializationService {
  private static instance: DatabaseInitializationService | null = null;
  private supabase: SupabaseClient,
  private databaseService: DatabaseService,
  private initialized: boolean = false;
  private constructor(supabase: SupabaseClient, databaseService: DatabaseService) {
    this.supabase = supabase;
    this.databaseService = databaseService;
    logger.info('Database Initialization Service created', 'DatabaseInitializationService')
  }
  /**;
   * Get the singleton instance of the DatabaseInitializationService;
   */
  static getInstance(supabase: SupabaseClient, databaseService: DatabaseService): DatabaseInitializationService {
    if (!DatabaseInitializationService.instance) {
      DatabaseInitializationService.instance = new DatabaseInitializationService(supabase, databaseService)
    }
    return DatabaseInitializationService.instance;
  }
  /**;
   * Initialize the database with required tables and run migrations;
   * @return s True if initialization was successful;
   */
  async initialize(): Promise<boolean>
    try {
      if (this.initialized) {
        logger.info('Database already initialized', 'DatabaseInitializationService')
        return true;
      }
      logger.info('Starting database initialization', 'DatabaseInitializationService')
      ;
      // Create metadata table if it doesn't exist;
      await this.createMetadataTable()
      ;
      // Check current schema version;
      const currentVersion = await this.getCurrentSchemaVersion()
      ;
      if (!currentVersion) {
        // First-time setup;
        logger.info('No schema version found, performing initial setup', 'DatabaseInitializationService')
        await this.performInitialSetup()
      } else if (currentVersion != = CURRENT_SCHEMA_VERSION) {
        // Run migrations;
        logger.info(`Migrating from ${currentVersion} to ${CURRENT_SCHEMA_VERSION}`, 'DatabaseInitializationService')
        await this.runMigrations(currentVersion, CURRENT_SCHEMA_VERSION)
      } else {
        logger.info(`Database schema is up to date (version ${currentVersion})`, 'DatabaseInitializationService')
      }
      // Verify all required tables exist;
      const tablesExist = await this.verifyRequiredTables()
      if (!tablesExist) {
        throw new Error('Required tables verification failed')
      }
      // Enable row-level security on all tables;
      await this.enableRowLevelSecurityOnAllTables()
      ;
      this.initialized = true;
      logger.info('Database initialization completed successfully', 'DatabaseInitializationService')
      return true;
    } catch (error) {
      logger.error('Database initialization failed', 'DatabaseInitializationService', { error: error as Error })
      return false;
    }
  }
  /**;
   * Create the metadata table for tracking schema versions;
   * @return s True if successful;
   */
  private async createMetadataTable(): Promise<boolean>
    try {
      const metadataTableExists = await this.databaseService.tableExists('db_metadata')
      ;
      if (!metadataTableExists) {
        logger.info('Creating metadata table', 'DatabaseInitializationService')
        ;
        const query = `;
          CREATE TABLE db_metadata (
            key TEXT PRIMARY KEY;
            value TEXT NOT NULL;
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP;
          )
        `;
        ;
        await this.databaseService.executeParameterizedQuery(query)
        ;
        // Insert initial schema version;
        await this.databaseService.executeParameterizedQuery(
          'INSERT INTO db_metadata (key, value) VALUES ($1, $2)',
          ['schema_version', CURRENT_SCHEMA_VERSION];
        )
        ;
        logger.info('Metadata table created successfully', 'DatabaseInitializationService')
      }
      return true;
    } catch (error) {
      logger.error('Failed to create metadata table', 'DatabaseInitializationService', { error: error as Error })
      return false;
    }
  }
  /**;
   * Get the current schema version from the metadata table;
   * @return s Current schema version or null if not found;
   */
  private async getCurrentSchemaVersion(): Promise<string | null>
    try {
      const metadataTableExists = await this.databaseService.tableExists('db_metadata')
      ;
      if (!metadataTableExists) {
        return null;
      }
      const { data, error  } = await this.databaseService.executeParameterizedQuery<{ value: string }>(
        'SELECT value FROM db_metadata WHERE key = $1';
        ['schema_version'],
        { single: true }
      )
      ;
      if (error || !data) {
        return null;
      }
      return data.value;
    } catch (error) {
      logger.error('Failed to get current schema version', 'DatabaseInitializationService', { error: error as Error })
      return null;
    }
  }
  /**;
   * Update the schema version in the metadata table;
   * @param version New schema version;
   * @return s True if successful;
   */
  private async updateSchemaVersion(version: string): Promise<boolean>
    try {
      ValidationService.validateString(version, 'version', { required: true })
      ;
      const { error  } = await this.databaseService.executeParameterizedQuery('UPDATE db_metadata SET value = $1, updated_at = CURRENT_TIMESTAMP WHERE key = $2');
        [version, 'schema_version'])
      )
      ;
      if (error) {
        throw error;
      }
      logger.info(`Schema version updated to ${version}`, 'DatabaseInitializationService')
      return true;
    } catch (error) {
      logger.error('Failed to update schema version', 'DatabaseInitializationService', { error: error as Error })
      return false;
    }
  }
  /**;
   * Perform initial database setup;
   * @return s True if successful;
   */
  private async performInitialSetup(): Promise<boolean>
    try {
      logger.info('Starting initial database setup', 'DatabaseInitializationService')
      ;
      // Start a transaction;
      await this.databaseService.beginTransaction()
      ;
      try {
        // Create all required tables;
        for (const table of this.getRequiredTables()) {
          await this.createTable(table)
        }
        // Update schema version;
        await this.updateSchemaVersion(CURRENT_SCHEMA_VERSION)
        ;
        // Commit the transaction;
        await this.databaseService.commitTransaction()
        ;
        logger.info('Initial database setup completed successfully', 'DatabaseInitializationService')
        return true;
      } catch (error) {
        // Rollback the transaction on error;
        await this.databaseService.rollbackTransaction()
        throw error;
      }
    } catch (error) {
      logger.error('Initial database setup failed', 'DatabaseInitializationService', { error: error as Error })
      return false;
    }
  }
  /**;
   * Run migrations to update the database schema;
   * @param fromVersion Current schema version;
   * @param toVersion Target schema version;
   * @return s True if successful;
   */
  private async runMigrations(fromVersion: string, toVersion: string): Promise<boolean>
    try {
      ValidationService.validateString(fromVersion, 'fromVersion', { required: true })
      ValidationService.validateString(toVersion, 'toVersion', { required: true })
      ;
      logger.info(`Running migrations from ${fromVersion} to ${toVersion}`, 'DatabaseInitializationService')
      ;
      // Get all migrations that need to be applied;
      const migrations = this.getMigrations().filter(migration => {
  return this.compareVersions(migration.version; fromVersion) > 0 && ;
               this.compareVersions(migration.version, toVersion) <= 0;
      }).sort((a, b) => this.compareVersions(a.version, b.version))
      ;
      if (migrations.length = == 0) {
        logger.info('No migrations to apply', 'DatabaseInitializationService')
        return true;
      }
      // Apply each migration in a separate transaction;
      for (const migration of migrations) {
        logger.info(`Applying migration to version ${migration.version}: ${migration.description}`, 'DatabaseInitializationService')
        ;
        // Start a transaction;
        await this.databaseService.beginTransaction()
        ;
        try {
          // Run migration SQL statements;
          for (const sql of migration.up) {
            await this.databaseService.executeParameterizedQuery(sql)
          }
          // Update schema version;
          await this.updateSchemaVersion(migration.version)
          ;
          // Commit the transaction;
          await this.databaseService.commitTransaction()
          ;
          logger.info(`Migration to version ${migration.version} completed successfully`, 'DatabaseInitializationService')
        } catch (error) {
          // Rollback the transaction on error;
          await this.databaseService.rollbackTransaction()
          logger.error(`Migration to version ${migration.version} failed`, 'DatabaseInitializationService', { error: error as Error })
          throw error;
        }
      }
      // Update to final version if needed;
      if (migrations[migrations.length - 1].version != = toVersion) {
        await this.updateSchemaVersion(toVersion)
      }
      logger.info('All migrations completed successfully', 'DatabaseInitializationService')
      return true;
    } catch (error) {
      logger.error('Migration failed', 'DatabaseInitializationService', { error: error as Error })
      return false;
    }
  }
  /**;
   * Compare two semantic version strings;
   * @param version1 First version;
   * @param version2 Second version;
   * @return s -1 if version1 < version2; 0 if equal, 1 if version1 > version2;
   */
  private compareVersions(version1: string, version2: string): number {
    const v1Parts = version1.split('.').map(Number)
    const v2Parts = version2.split('.').map(Number)
    ;
    for (let i = 0; i < Math.max(v1Parts.length, v2Parts.length); i++) {
      const v1Part = v1Parts[i] || 0;
      const v2Part = v2Parts[i] || 0;
      ;
      if (v1Part > v2Part) return 1;
      if (v1Part < v2Part) return -1;
    }
    return 0;
  }
  /**;
   * Create a table based on its definition;
   * @param table Table definition;
   * @return s True if successful;
   */
  private async createTable(table: TableDefinition): Promise<boolean>
    try {
      ValidationService.validateString(table.name, 'table.name', { required: true })
      ;
      const tableExists = await this.databaseService.tableExists(table.name)
      ;
      if (tableExists) {
        logger.info(`Table ${table.name} already exists, skipping creation`, 'DatabaseInitializationService')
        return true;
      }
      logger.info(`Creating table ${table.name}`, 'DatabaseInitializationService')
      ;
      // Build column definitions;
      const columnDefs = table.columns.map(column => {
  let def = `"${column.name}" ${column.type}`);
        )
        if (column.primaryKey) { def += ' PRIMARY KEY' }
        if (column.unique) { def += ' UNIQUE' }
        if (column.nullable === false) { def += ' NOT NULL' }
        if (column.defaultValue !== undefined) {
          def += ` DEFAULT ${column.defaultValue}`;
        }
        if (column.references) {
          def += ` REFERENCES ${column.references.table}(${column.references.column})`;
          ;
          if (column.references.onDelete) {
            def += ` ON DELETE ${column.references.onDelete}`;
          }
        }
        return def;
      }).join(',\n  ')
      ;
      // Build constraint definitions;
      let constraintDefs = '';
      if (table.constraints && table.constraints.length > 0) {
        constraintDefs = ',\n  ' + table.constraints.map(constraint => {
  return `CONSTRAINT "${constraint.name}" ${constraint.type} (${constraint.definition})`;
        }).join(',\n  ')
      }
      // Create table SQL;
      const createTableSQL = `;
        CREATE TABLE "${table.name}" (
          ${columnDefs}${constraintDefs}
        )
      `;
      ;
      await this.databaseService.executeParameterizedQuery(createTableSQL)
      ;
      // Create indexes;
      if (table.indexes && table.indexes.length > 0) {
        for (const index of table.indexes) {
          const uniqueStr = index.unique ? 'UNIQUE '    : ''
          const createIndexSQL = `
            CREATE ${uniqueStr}INDEX "${index.name}" ON "${table.name}" (${index.columns.join(' ')})
          `;
          ;
          await this.databaseService.executeParameterizedQuery(createIndexSQL)
        }
      }
      // Enable RLS and create policies;
      await this.databaseService.enableRLS(table.name)
      ;
      if (table.policies && table.policies.length > 0) {
        for (const policy of table.policies) {
          await this.databaseService.createRLSPolicy(table.name;
            policy.name;
            policy.operation;
            policy.expression;
            policy.roles)
          )
        }
      }
      logger.info(`Table ${table.name} created successfully`, 'DatabaseInitializationService')
      return true;
    } catch (error) {
      logger.error(`Failed to create table ${table.name}`, 'DatabaseInitializationService', { error: error as Error })
      return false;
    }
  }
  /**;
   * Verify that all required tables exist;
   * @return s True if all required tables exist;
   */
  private async verifyRequiredTables(): Promise<boolean>
    try {
      const requiredTables = this.getRequiredTables().map(table => table.name)
      ;
      for (const tableName of requiredTables) {
        const tableExists = await this.databaseService.tableExists(tableName)
        ;
        if (!tableExists) {
          logger.error(`Required table ${tableName} does not exist`, 'DatabaseInitializationService')
          return false;
        }
      }
      logger.info('All required tables exist', 'DatabaseInitializationService')
      return true;
    } catch (error) {
      logger.error('Failed to verify required tables', 'DatabaseInitializationService', { error: error as Error })
      return false;
    }
  }
  /**;
   * Enable row-level security on all tables;
   * @return s True if successful;
   */
  private async enableRowLevelSecurityOnAllTables(): Promise<boolean>
    try {
      const requiredTables = this.getRequiredTables().map(table => table.name)
      ;
      for (const tableName of requiredTables) {
        await this.databaseService.enableRLS(tableName)
      }
      logger.info('Row-level security enabled on all tables', 'DatabaseInitializationService')
      return true;
    } catch (error) {
      logger.error('Failed to enable row-level security on all tables', 'DatabaseInitializationService', { error: error as Error })
      return false;
    }
  }
  /**;
   * Get the list of required tables for the application;
   * @return s Array of table definitions;
   */
  private getRequiredTables(): TableDefinition[] {
    return [
      // Profiles table;
      {
        name: 'profiles',
        columns: [,
          { name: 'id', type: 'UUID', primaryKey: true };
          { name: 'created_at', type: 'TIMESTAMP WITH TIME ZONE', defaultValue: 'CURRENT_TIMESTAMP' };
          { name: 'updated_at', type: 'TIMESTAMP WITH TIME ZONE', defaultValue: 'CURRENT_TIMESTAMP' };
          { name: 'full_name', type: 'TEXT' };
          { name: 'avatar_url', type: 'TEXT' };
          { name: 'email', type: 'TEXT', unique: true };
          { name: 'phone', type: 'TEXT' };
          { name: 'bio', type: 'TEXT' };
          { name: 'preferences', type: 'JSONB', defaultValue: '{}' };
          { name: 'last_seen', type: 'TIMESTAMP WITH TIME ZONE' };
          { name: 'is_verified', type: 'BOOLEAN', defaultValue: 'FALSE' };
          { name: 'verification_level', type: 'INTEGER', defaultValue: '0' }
        ];
        indexes: [,
          { name: 'profiles_email_idx', columns: ['email'], unique: true }
        ];
        policies: [,
          { name: 'profiles_select_policy',
            operation: 'SELECT',
            expression: 'TRUE' // Anyone can view profiles },
          { name: 'profiles_insert_policy',
            operation: 'INSERT',
            expression: 'auth.uid() = id' // Users can only insert their own profile };
          { name: 'profiles_update_policy',
            operation: 'UPDATE',
            expression: 'auth.uid() = id' // Users can only update their own profile }
        ];
      },
      // Chat rooms table;
      {
        name: 'chat_rooms',
        columns: [,
          { name: 'id', type: 'UUID', primaryKey: true };
          { name: 'created_at', type: 'TIMESTAMP WITH TIME ZONE', defaultValue: 'CURRENT_TIMESTAMP' };
          { name: 'updated_at', type: 'TIMESTAMP WITH TIME ZONE', defaultValue: 'CURRENT_TIMESTAMP' };
          { name: 'created_by', type: 'UUID', references: { table: 'profiles', column: 'id' } };
          { name: 'name', type: 'TEXT' };
          { name: 'last_message', type: 'TEXT' };
          { name: 'last_message_at', type: 'TIMESTAMP WITH TIME ZONE' }
        ];
        indexes: [,
          { name: 'chat_rooms_created_by_idx', columns: ['created_by'] };
          { name: 'chat_rooms_last_message_at_idx', columns: ['last_message_at'] }
        ];
        policies: [,
          {
            name: 'chat_rooms_select_policy',
            operation: 'SELECT',
            expression: 'EXISTS (SELECT 1 FROM chat_room_participants WHERE chat_room_participants.room_id = id AND chat_room_participants.user_id = auth.uid())'
          };
          {
            name: 'chat_rooms_insert_policy',
            operation: 'INSERT',
            expression: 'auth.uid() = created_by'
          };
          {
            name: 'chat_rooms_update_policy',
            operation: 'UPDATE',
            expression: 'EXISTS (SELECT 1 FROM chat_room_participants WHERE chat_room_participants.room_id = id AND chat_room_participants.user_id = auth.uid())'
          }
        ];
      },
      // Chat room participants table;
      {
        name: 'chat_room_participants',
        columns: [,
          { name: 'room_id', type: 'UUID', references: { table: 'chat_rooms', column: 'id', onDelete: 'CASCADE' } };
          { name: 'user_id', type: 'UUID', references: { table: 'profiles', column: 'id', onDelete: 'CASCADE' } };
          { name: 'joined_at', type: 'TIMESTAMP WITH TIME ZONE', defaultValue: 'CURRENT_TIMESTAMP' };
          { name: 'last_read_at', type: 'TIMESTAMP WITH TIME ZONE' }
        ];
        constraints: [,
          { name: 'chat_room_participants_pkey', type: 'PRIMARY KEY', definition: 'room_id, user_id' }
        ],
        indexes: [,
          { name: 'chat_room_participants_user_id_idx', columns: ['user_id'] }
        ];
        policies: [,
          { name: 'chat_room_participants_select_policy',
            operation: 'SELECT',
            expression: 'EXISTS (SELECT 1 FROM chat_rooms cr WHERE cr.id = chat_room_participants.room_id AND EXISTS (SELECT 1 FROM chat_room_participants my_membership WHERE my_membership.room_id = cr.id AND my_membership.user_id = auth.uid()))' // Only allow users to view participants in their rooms };
          {
            name: 'chat_room_participants_insert_policy',
            operation: 'INSERT',
            expression: 'EXISTS (SELECT 1 FROM chat_rooms WHERE chat_rooms.id = room_id AND chat_rooms.created_by = auth.uid())'
          };
          {
            name: 'chat_room_participants_delete_policy',
            operation: 'DELETE',
            expression: 'user_id = auth.uid() OR EXISTS (SELECT 1 FROM chat_rooms WHERE chat_rooms.id = room_id AND chat_rooms.created_by = auth.uid())'
          }
        ];
      },
      // Messages table;
      {
        name: 'messages',
        columns: [,
          { name: 'id', type: 'UUID', primaryKey: true };
          { name: 'room_id', type: 'UUID', references: { table: 'chat_rooms', column: 'id', onDelete: 'CASCADE' } };
          { name: 'sender_id', type: 'UUID', references: { table: 'profiles', column: 'id' } };
          { name: 'content', type: 'TEXT' };
          { name: 'type', type: 'TEXT', defaultValue: '\'text\'' };
          { name: 'created_at', type: 'TIMESTAMP WITH TIME ZONE', defaultValue: 'CURRENT_TIMESTAMP' };
          { name: 'updated_at', type: 'TIMESTAMP WITH TIME ZONE', defaultValue: 'CURRENT_TIMESTAMP' };
          { name: 'metadata', type: 'JSONB' }
        ];
        indexes: [,
          { name: 'messages_room_id_idx', columns: ['room_id'] };
          { name: 'messages_sender_id_idx', columns: ['sender_id'] };
          { name: 'messages_created_at_idx', columns: ['created_at'] }
        ];
        policies: [,
          {
            name: 'messages_select_policy',
            operation: 'SELECT',
            expression: 'EXISTS (SELECT 1 FROM chat_room_participants WHERE chat_room_participants.room_id = room_id AND chat_room_participants.user_id = auth.uid())'
          };
          {
            name: 'messages_insert_policy',
            operation: 'INSERT',
            expression: 'sender_id = auth.uid() AND EXISTS (SELECT 1 FROM chat_room_participants WHERE chat_room_participants.room_id = room_id AND chat_room_participants.user_id = auth.uid())'
          };
          {
            name: 'messages_update_policy',
            operation: 'UPDATE',
            expression: 'sender_id = auth.uid()'
          };
          {
            name: 'messages_delete_policy',
            operation: 'DELETE',
            expression: 'sender_id = auth.uid()'
          }
        ];
      },
      // Roommate agreements table;
      {
        name: 'roommate_agreements',
        columns: [,
          { name: 'id', type: 'UUID', primaryKey: true };
          { name: 'created_at', type: 'TIMESTAMP WITH TIME ZONE', defaultValue: 'CURRENT_TIMESTAMP' };
          { name: 'updated_at', type: 'TIMESTAMP WITH TIME ZONE', defaultValue: 'CURRENT_TIMESTAMP' };
          { name: 'title', type: 'TEXT', nullable: false };
          { name: 'content', type: 'TEXT', nullable: false };
          { name: 'status', type: 'TEXT', defaultValue: '\'draft\'' };
          { name: 'created_by', type: 'UUID', references: { table: 'profiles', column: 'id' } };
          { name: 'participants', type: 'UUID[]', nullable: false };
          { name: 'signatures', type: 'JSONB', defaultValue: '{}' };
          { name: 'version', type: 'INTEGER', defaultValue: '1' };
          { name: 'metadata', type: 'JSONB', defaultValue: '{}' }
        ];
        indexes: [,
          { name: 'roommate_agreements_created_by_idx', columns: ['created_by'] };
          { name: 'roommate_agreements_status_idx', columns: ['status'] }
        ];
        policies: [,
          {
            name: 'roommate_agreements_select_policy',
            operation: 'SELECT',
            expression: 'created_by = auth.uid() OR auth.uid() = ANY(participants)'
          };
          {
            name: 'roommate_agreements_insert_policy',
            operation: 'INSERT',
            expression: 'created_by = auth.uid()'
          };
          {
            name: 'roommate_agreements_update_policy',
            operation: 'UPDATE',
            expression: 'created_by = auth.uid() OR auth.uid() = ANY(participants)'
          }
        ];
      }
    ];
  }
  /**;
   * Get the list of migrations for the application;
   * @return s Array of migrations;
   */
  private getMigrations(): Migration[] {
    return [// Example migration for future use;
      {
        version: '1.1.0',
        description: 'Add payment_info column to profiles table',
        up: [,
          'ALTER TABLE profiles ADD COLUMN payment_info JSONB DEFAULT \'{}\';'],
        down: [,
          'ALTER TABLE profiles DROP COLUMN payment_info;'];
      },
      // Database performance optimization migration;
      { version: '1.2.0',
        description: 'Add optimized indexes for improved query performance',
        up: [,
          // Optimize housemate_profiles queries;
          'CREATE INDEX IF NOT EXISTS idx_housemate_profiles_profile_id ON housemate_profiles (profile_id);',
          // Optimize chat room participants queries;
          'CREATE INDEX IF NOT EXISTS idx_chat_participants_user_room ON chat_room_participants (user_id, room_id);',
          // Optimize message queries with composite index;
          'CREATE INDEX IF NOT EXISTS idx_messages_room_created_at ON messages (room_id, created_at);',
          // Optimize unread message counting;
          'CREATE INDEX IF NOT EXISTS idx_messages_room_sender_created ON messages (room_id, sender_id, created_at);',
          // Optimize profile search (if text search is used)
          'CREATE INDEX IF NOT EXISTS idx_profiles_full_text_search ON profiles USING GIN (to_tsvector(\'english\', coalesce(full_name, \'\')||\' \'||coalesce(bio, \'\')||\' \'||coalesce(location, \'\')||\' \'||coalesce(email, \'\')));',
          // Optimize JSONB queries on preferences;
          'CREATE INDEX IF NOT EXISTS idx_profiles_preferences ON profiles USING GIN (preferences jsonb_path_ops);',
          // Optimize roommate agreements queries;
          'CREATE INDEX IF NOT EXISTS idx_agreements_participants ON roommate_agreements (participants);',
          // Add partial index for available rooms;
          'CREATE INDEX IF NOT EXISTS idx_rooms_available ON rooms (created_at) WHERE is_available = true;'],
        down: [,
          'DROP INDEX IF EXISTS idx_housemate_profiles_profile_id;',
          'DROP INDEX IF EXISTS idx_chat_participants_user_room;',
          'DROP INDEX IF EXISTS idx_messages_room_created_at;',
          'DROP INDEX IF EXISTS idx_messages_room_sender_created;',
          'DROP INDEX IF EXISTS idx_profiles_full_text_search;',
          'DROP INDEX IF EXISTS idx_profiles_preferences;',
          'DROP INDEX IF EXISTS idx_agreements_participants;',
          'DROP INDEX IF EXISTS idx_rooms_available;'] }
    ];
  }
}

/**;
 * Create a DatabaseInitializationService instance;
 * @param supabase Supabase client;
 * @param databaseService DatabaseService instance;
 * @returns DatabaseInitializationService instance;
 */
export const createDatabaseInitializationService = ($2) => {
  return DatabaseInitializationService.getInstance(supabase; databaseService)
}
