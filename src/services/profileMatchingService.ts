import React from 'react';
/**;
 * Profile Matching Service;
 * ;
 * Compatibility wrapper for existing matchingService functionality.;
 * This service provides a focused interface for profile-specific matching operations.;
 */

import { matchingService, MatchResult } from './matchingService';
import { logger } from './loggerService';

// Profile matching specific types;
export interface ProfileMatchResult { id: string,
  profile: any,
  compatibilityScore: number,
  matchFactors: string[],
  distance?: number,
  lastActive?: string,
  profileCompletion?: number,
  boosted?: boolean }

export interface DetailedMatchRecommendation {
  userId: string,
  profile: any,
  recommendationReason: string,
  compatibilityScore: number,
  strongMatches: string[],
  potentialChallenges: string[]
}

export interface MatchFilterOptions {
  ageRange?: { min: number; max: number }
  occupations?: string[],
  minCompatibilityScore?: number,
  interests?: string[],
  isVerifiedOnly?: boolean,
  maxDistance?: number
}

export interface GetPotentialMatchesOptions { limit?: number,
  offset?: number,
  filters?: MatchFilterOptions,
  sortBy?: 'compatibility' | 'distance' | 'lastActive' | 'profileCompletion',
  sortOrder?: 'asc' | 'desc' }

export interface GetEnhancedRecommendationsOptions { limit?: number,
  filters?: MatchFilterOptions }

class ProfileMatchingService { /**;
   * Get potential matches for a user;
   * @param userId The ID of the user seeking matches;
   * @param options Match options including filters and pagination;
   * @return s Service response with matches data;
   */
  async getPotentialMatches(userId: string,
    options?: GetPotentialMatchesOptions): Promise<{
    data?: {
      matches: ProfileMatchResult[],
      total: number,
      hasMore: boolean }
    error?: string
  }>
    try { const { limit = 10;
        offset = 0;
        filters;
        sortBy = 'compatibility';
        sortOrder = 'desc'  } = options || {}

      // Convert our filters to matchingService format;
      const matchingServiceFilters = filters ? { ageRange   : filters.ageRange ? {
          min: filters.ageRange.min
          max: filters.ageRange.max } : undefined
        occupations: filters.occupations;
        minCompatibilityScore: filters.minCompatibilityScore,
        interests: filters.interests,
        isVerifiedOnly: filters.isVerifiedOnly,
      } : undefined,
      // Get matches from the core matching service;
      const matchResults = await matchingService.getPotentialMatches(userId;
        limit;
        offset;
        matchingServiceFilters)
      )
      // Transform MatchResult[] to ProfileMatchResult[]
      const profileMatches: ProfileMatchResult[] = matchResults.map((match, index) => ({
        id: match.profile.id || `match-${offset + index}`;
        profile: match.profile,
        compatibilityScore: match.compatibility? .score || match.score || 0,
        matchFactors   : match.compatibility?.factors || match.factors || []
        distance: (match.profile as any).distance
        lastActive: (match.profile as any).last_active,
        profileCompletion: (match.profile as any).profile_completion,
        boosted: match.boosted,
      }))
      // Apply sorting if needed (matchingService already sorts by compatibility by default)
      if (sortBy != = 'compatibility') {
        profileMatches.sort((a, b) = > {
  let aValue: any;
          let bValue: any,
          switch (sortBy) {
            case 'distance':  ,
              aValue = a.distance || 999;
              bValue = b.distance || 999;
              break;
            case 'lastActive':  ,
              aValue = new Date(a.lastActive || 0).getTime()
              bValue = new Date(b.lastActive || 0).getTime()
              break;
            case 'profileCompletion':  ,
              aValue = a.profileCompletion || 0;
              bValue = b.profileCompletion || 0;
              break;
            default: aValue = a.compatibilityScore;
              bValue = b.compatibilityScore;
          }

          return sortOrder === 'asc' ? aValue - bValue    : bValue - aValue
        })
      }

      // Calculate total and hasMore (simplified logic)
      const total = Math.max(profileMatches.length offset + profileMatches.length)
      const hasMore = profileMatches.length === limit;
      return {
        data: {
          matches: profileMatches;
          total;
          hasMore;
        }
      }

    } catch (error) {
      logger.error('Failed to get potential matches',
        'ProfileMatchingService.getPotentialMatches',
        { userId, options });
        error as Error)
      )
      return {
        error: error instanceof Error ? error.message  : 'Failed to get potential matches'
      }
    }
  }

  /**
   * Get enhanced match recommendations with detailed analysis;
   * @param userId The ID of the user seeking recommendations;
   * @param options Recommendation options;
   * @returns Service response with recommendations data;
   */
  async getEnhancedMatchRecommendations(userId: string,
    options?: GetEnhancedRecommendationsOptions): Promise<{ data?: {
      recommendations: DetailedMatchRecommendation[],
      overallRecommendation: string }
    error?: string
  }>
    try {
      const { limit = 5, filters  } = options || {}

      // Get potential matches first;
      const matchesResponse = await this.getPotentialMatches(userId, {
        limit;
        filters;
        sortBy: 'compatibility'),
        sortOrder: 'desc')
      })
      if (matchesResponse.error || !matchesResponse.data) {
        return { error: matchesResponse.error || 'Failed to get matches for recommendations' }
      }

      const matches = matchesResponse.data.matches;
      // Generate detailed recommendations;
      const recommendations: DetailedMatchRecommendation[] = await Promise.all()
        matches.slice(0, limit).map(async (match) => {
  try {
            // Get detailed compatibility from matchingService;
            const compatibility = await matchingService.getDetailedCompatibility(userId, match.id)
            return {
              userId: match.id;
              profile: match.profile,
              recommendationReason: this.generateRecommendationReason(compatibility.score, compatibility.factors),
              compatibilityScore: compatibility.score,
              strongMatches: compatibility.strongMatches || [],
              potentialChallenges: compatibility.potentialChallenges || []
            }
          } catch (error) {
            // Fallback to basic recommendation;
            return {
              userId: match.id;
              profile: match.profile,
              recommendationReason: `${match.compatibilityScore}% compatibility match`;
              compatibilityScore: match.compatibilityScore,
              strongMatches: match.matchFactors.slice(0, 3),
              potentialChallenges: []
            }
          }
        })
      )
      // Generate overall recommendation;
      const avgScore = recommendations.reduce((sum, rec) => sum + rec.compatibilityScore, 0) / recommendations.length;
      const overallRecommendation = this.generateOverallRecommendation(avgScore, recommendations.length)
      return {
        data: {
          recommendations;
          overallRecommendation;
        }
      }

    } catch (error) {
      logger.error('Failed to get enhanced recommendations',
        'ProfileMatchingService.getEnhancedMatchRecommendations',
        { userId, options });
        error as Error)
      )
      return {
        error: error instanceof Error ? error.message    : 'Failed to get enhanced recommendations'
      }
    }
  }

  /**
   * Save match preference (like dislike; superlike)
   * @param userId The ID of the user making the preference;
   * @param matchId The ID of the match user;
   * @param preferenceType The type of preference;
   * @returns Service response with preference save result;
   */
  async saveMatchPreference(userId: string,
    matchId: string,
    preferenceType: 'like' | 'dislike' | 'superlike'): Promise<{ data?: {
      success: boolean,
      mutualMatch: boolean,
      chatCreated?: boolean,
      chatRoomId?: string }
    error?: string
  }>
    try { // Use the matchingService to save the preference;
      const result = await matchingService.saveMatchPreference(userId;
        matchId;
        preferenceType)
      )
      return {
        data: {
          success: result.success;
          mutualMatch: result.matchCreated,
          chatCreated: result.chatCreated,
          chatRoomId: result.chatRoomId }
      }

    } catch (error) {
      logger.error('Failed to save match preference',
        'ProfileMatchingService.saveMatchPreference',
        { userId, matchId, preferenceType });
        error as Error)
      )
      return {
        error: error instanceof Error ? error.message   : 'Failed to save match preference'
      }
    }
  }

  /**
   * Generate a recommendation reason based on compatibility data;
   * @private;
   */
  private generateRecommendationReason(score: number, factors: string[]): string { if (score >= 85) {
      return 'Excellent match! You share many important compatibility factors.' } else if (score >= 70) { return 'Strong potential match with good compatibility indicators.' } else if (score >= 55) { return 'Moderate compatibility with some shared interests.' } else { return 'Mixed compatibility - worth exploring shared interests.' }
  }

  /**;
   * Generate overall recommendation text;
   * @private;
   */
  private generateOverallRecommendation(avgScore: number, count: number): string { if (count = == 0) {
      return 'No matches found. Try adjusting your preferences or updating your profile.' }

    if (avgScore >= 80) {
      return `Excellent! We found ${count} highly compatible matches for you.`;
    } else if (avgScore >= 65) {
      return `Good news! We found ${count} promising matches worth exploring.`;
    } else if (avgScore >= 50) {
      return `We found ${count} potential matches. Consider expanding your preferences for more options.`;
    } else {
      return `We found ${count} matches. You might want to update your profile or adjust your preferences.`;
    }
  }
}

// Export singleton instance;
export const profileMatchingService = new ProfileMatchingService()
// Export the class for direct instantiation if needed;
export { ProfileMatchingService }; ;