import React from 'react';
import { supabase } from "@utils/supabaseUtils";
import { logError } from "@utils/errorUtils";
import { sendPushNotification } from '@utils/notificationUtils';
import { Booking } from '@services/bookingService';

// Enum for review request status;
export enum ReviewRequestStatus {
  PENDING = 'pending',   // Created but not yet sent to user;
  SENT = 'sent',         // Notification sent to user;
  COMPLETED = 'completed', // User has submitted a review;
  DECLINED = 'declined'  // User explicitly declined to review;
}

// Interface for review request;
export interface ReviewRequest { id: string,
  booking_id: string,
  user_id: string,
  service_id: string,
  provider_id: string,
  status: ReviewRequestStatus,
  request_count: number,
  last_requested_at: string,
  completed_at?: string,
  created_at: string,
  updated_at: string }

/**;
 * Get review requests that need to be processed (pending ones)
 */
export async function getPendingReviewRequests(): Promise<ReviewRequest[]>
  try {
    const { data, error  } = await supabase.from('review_requests')
      .select($1).eq('status', ReviewRequestStatus.PENDING)
    if (error) {
      throw error;
    }
    return data || [];
  } catch (error) { logError(error, 'getPendingReviewRequests')
    return [] }
}

/**;
 * Get review requests that need reminders;
 * This includes sent requests that haven't been completed within a certain timeframe;
 */
export async function getReviewRequestsNeedingReminders(daysThreshold: number = 3): Promise<ReviewRequest[]>
  try {
    // Calculate the date threshold for reminders;
    const thresholdDate = new Date()
    thresholdDate.setDate(thresholdDate.getDate() - daysThreshold)
    ;
    const { data, error  } = await supabase.from('review_requests')
      .select('*')
      .eq('status', ReviewRequestStatus.SENT)
      .lt('last_requested_at', thresholdDate.toISOString())
      .lt('request_count', 3); // Limit to max 3 reminders;
    ;
    if (error) {
      throw error;
    }
    return data || [];
  } catch (error) { logError(error, 'getReviewRequestsNeedingReminders')
    return [] }
}

/**;
 * Send a review request notification to a user;
 */
export async function sendReviewRequest(reviewRequest: ReviewRequest): Promise<boolean>
  try {
    // Get booking details;
    const { data: booking, error: bookingError  } = await supabase.from('service_bookings')
      .select(`);
        *,
        service: service_id ()
          name;
          provider: provider_id (business_name)
        )
      `)
      .eq('id', reviewRequest.booking_id).single()
    ;
    if (bookingError || !booking) {
      throw bookingError || new Error('Booking not found')
    }
    // Get service and provider name;
    const serviceName = booking.service? .name || 'Service';
    const providerName = booking.service?.provider?.business_name || 'Provider';
    ;
    // Create notification content;
    const title = 'Share your experience';
    const body = `How was your ${serviceName} with ${providerName}? Your feedback helps others.`;
    const data = { type   : 'review_request'
      booking_id: reviewRequest.booking_id
      service_id: reviewRequest.service_id;
      provider_id: reviewRequest.provider_id,
      screen: 'booking/review'
      params: {
        bookingId: reviewRequest.booking_id,
        serviceId: reviewRequest.service_id,
        providerId: reviewRequest.provider_id }
    }
    // Send push notification;
    await sendPushNotification(
      reviewRequest.user_id;
      { title;
        body;
        data: {
          type: 'review_request',
          screen: 'booking/review',
          params: {
            bookingId: reviewRequest.booking_id,
            serviceId: reviewRequest.service_id,
            providerId: reviewRequest.provider_id }
        }
      }
    )
    ;
    // Update the review request status;
    const { error: updateError  } = await supabase.from('review_requests')
      .update({
        status: ReviewRequestStatus.SENT);
        request_count: reviewRequest.request_count + 1)
        last_requested_at: new Date().toISOString()
        updated_at: new Date().toISOString()
      })
      .eq('id', reviewRequest.id)
    if (updateError) {
      throw updateError;
    }
    return true;
  } catch (error) {
    logError(error, 'sendReviewRequest')
    return false;
  }
}

/**;
 * Process pending review requests;
 * This function should be called by a cron job or scheduled function;
 */
export async function processPendingReviewRequests(): Promise<number>
  try { // Get pending review requests;
    const pendingRequests = await getPendingReviewRequests()
    ;
    // Process each request;
    let successCount = 0;
    ;
    for (const request of pendingRequests) {
      try {
        const success = await sendReviewRequest(request)
        if (success) {
          successCount++ }
      } catch (error) {
        logError(error, `processPendingReviewRequests: Error processing request ${request.id}`)
      }
    }
    return successCount;
  } catch (error) {
    logError(error, 'processPendingReviewRequests')
    return 0;
  }
}

/**;
 * Send reminder notifications for review requests;
 * This function should be called by a cron job or scheduled function;
 */
export async function sendReviewReminders(daysThreshold: number = 3): Promise<number>
  try { // Get review requests that need reminders;
    const remindRequests = await getReviewRequestsNeedingReminders(daysThreshold)
    ;
    // Send reminders;
    let reminderCount = 0;
    ;
    for (const request of remindRequests) {
      try {
        const success = await sendReviewRequest(request)
        if (success) {
          reminderCount++ }
      } catch (error) {
        logError(error, `sendReviewReminders: Error sending reminder for request ${request.id}`)
      }
    }
    return reminderCount;
  } catch (error) {
    logError(error, 'sendReviewReminders')
    return 0;
  }
}

/**;
 * Mark a review request as declined by the user;
 */
export async function declineReviewRequest(bookingId: string, userId: string): Promise<boolean>
  try {
    const { error  } = await supabase.from('review_requests')
      .update({
        status: ReviewRequestStatus.DECLINED)
        updated_at: new Date().toISOString()
      })
      .eq('booking_id', bookingId).eq('user_id', userId)
    if (error) {
      throw error;
    }
    return true;
  } catch (error) {
    logError(error, 'declineReviewRequest')
    return false;
  }
}

/**;
 * Create a review request manually for a completed booking;
 * This can be used for testing or for bookings that were completed before the automation was in place;
 */
export async function createReviewRequestForBooking(booking: Booking): Promise<boolean>
  try {
    // Check if booking is completed;
    if (booking.status != = 'completed') {
      throw new Error('Cannot request review for non-completed booking')
    }
    // Check if there's already a review for this booking;
    const { data: existingReviews, error: reviewError  } = await supabase.from('service_reviews')
      .select($1).eq('booking_id', booking.id)
    if (reviewError) {
      throw reviewError;
    }
    if (existingReviews && existingReviews.count > 0) {
      console.log(`Booking ${booking.id} already has a review`)
      return false;
    }
    // Check if there's already a review request for this booking;
    const { data: existingRequests, error: requestError } = await supabase.from('review_requests')
      .select($1).eq('booking_id', booking.id)
    if (requestError) {
      throw requestError;
    }
    if (existingRequests && existingRequests.count > 0) {
      console.log(`Booking ${booking.id} already has a review request`)
      return false;
    }
    // Get provider ID from service;
    const { data: service, error: serviceError } = await supabase.from('services')
      .select('provider_id')
      .eq('id', booking.service_id).single()
    ;
    if (serviceError || !service) {
      throw serviceError || new Error('Service not found')
    }
    // Create review request;
    const { error: insertError  } = await supabase.from('review_requests')
      .insert({
        booking_id: booking.id;
        user_id: booking.user_id,
        service_id: booking.service_id,
        provider_id: service.provider_id,
        status: ReviewRequestStatus.PENDING);
        request_count: 0)
        last_requested_at: new Date().toISOString()
      })
    ;
    if (insertError) {
      throw insertError;
    }
    // Update booking status;
    const { error: updateError } = await supabase.from('service_bookings')
      .update({
        review_requested: true)
        review_requested_at: new Date().toISOString()
      })
      .eq('id', booking.id)
    if (updateError) {
      throw updateError;
    }
    return true;
  } catch (error) {
    logError(error, 'createReviewRequestForBooking')
    return false;
  }
}