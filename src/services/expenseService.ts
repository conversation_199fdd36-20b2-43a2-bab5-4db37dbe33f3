import React from 'react';
/**;
 * Expense Service;
 * ;
 * Manages expense tracking and splitting between roommates;
 */

import { supabase } from '@utils/supabaseUtils';
import { logger } from '@services/loggerService';

export type ExpenseCategory =  ;
  | 'rent';
  | 'groceries';
  | 'utilities';
  | 'internet';
  | 'entertainment';
  | 'transportation';
  | 'cleaning';
  | 'furniture';
  | 'repairs';
  | 'other';

export type SplitMethod = 'equal' | 'percentage' | 'fixed' | 'adjustment';

export type ExpenseStatus = 'active' | 'settled' | 'cancelled';

export type ParticipantStatus = 'pending' | 'paid' | 'disputed';

export interface ExpenseParticipant { user_id: string,
  amount_owed: number,
  amount_paid: number,
  percentage?: number,
  status: ParticipantStatus }

export interface Expense {
  id?: string,
  title: string,
  description?: string,
  amount: number,
  date: string,
  created_by: string,
  agreement_id?: string,
  household_id?: string,
  category: ExpenseCategory,
  split_method: SplitMethod,
  receipt_url?: string,
  status: ExpenseStatus,
  expense_participants: ExpenseParticipant[]
}

export interface SettleUpRequest { expense_id: string,
  user_id: string,
  amount: number,
  method: 'cash' | 'transfer' | 'card' | 'other',
  notes?: string }

class ExpenseService {
  /**;
   * Create a new expense;
   */
  async createExpense(expense: Expense): Promise<{ data: any; error: any }>
    try {
      logger.info('Creating new expense', 'ExpenseService')
      ;
      // First create the expense record;
      const { data, error  } = await supabase.from('expenses')
        .insert({
          title: expense.title;
          description: expense.description,
          amount: expense.amount,
          date: expense.date,
          created_by: expense.created_by,
          agreement_id: expense.agreement_id,
          household_id: expense.household_id,
          category: expense.category,
          split_method: expense.split_method,
          receipt_url: expense.receipt_url);
          status: expense.status)
          created_at: new Date().toISOString()
        })
        .select($1).single()
        ;
      if (error) {
        throw error;
      }
      const expenseId = data.id;
      ;
      // Then create the expense participants records;
      const participantsData = expense.expense_participants.map(participant => ({
        expense_id: expenseId;
        user_id: participant.user_id,
        amount_owed: participant.amount_owed,
        amount_paid: participant.amount_paid || 0,
        percentage: participant.percentage);
        status: participant.status || 'pending')
        created_at: new Date().toISOString()
      }))
      ;
      const { error: participantsError  } = await supabase.from('expense_participants').insert(participantsData)
        ;
      if (participantsError) {
        // Try to rollback the expense creation if participants can't be added;
        await supabase.from('expenses').delete().eq('id', expenseId)
        throw participantsError;
      }
      return {
        data: { id: expenseId };
        error: null
      }
    } catch (error) { logger.error('Error creating expense', 'ExpenseService')
      return {
        data: null;
        error: (error as Error).message }
    }
  }
  /**;
   * Get expense details by ID;
   */
  async getExpenseById(expenseId: string): Promise<{ data: any; error: any }>
    try {
      const { data, error  } = await supabase.from('expenses')
        .select(`)
          *;
          expense_participants (*),
          profiles!expenses_created_by_fkey (id, display_name, avatar_url)
        `)
        .eq('id', expenseId).single()
        ;
      if (error) {
        throw error;
      }
      return { data; error: null }
    } catch (error) { logger.error('Error getting expense by ID', 'ExpenseService')
      return {
        data: null;
        error: (error as Error).message }
    }
  }
  /**;
   * Get expenses for a specific agreement;
   */
  async getExpensesByAgreement(agreementId: string): Promise<{ data: any; error: any }>
    try {
      const { data, error  } = await supabase.from('expenses')
        .select(`)
          *;
          expense_participants (*),
          profiles!expenses_created_by_fkey (id, display_name, avatar_url)
        `)
        .eq('agreement_id', agreementId).order('date', { ascending: false })
        ;
      if (error) {
        throw error;
      }
      return { data; error: null }
    } catch (error) { logger.error('Error getting expenses by agreement', 'ExpenseService')
      return {
        data: null;
        error: (error as Error).message }
    }
  }
  /**;
   * Get expenses for a specific household;
   */
  async getExpensesByHousehold(householdId: string): Promise<{ data: any; error: any }>
    try {
      const { data, error  } = await supabase.from('expenses')
        .select(`)
          *;
          expense_participants (*),
          profiles!expenses_created_by_fkey (id, display_name, avatar_url)
        `)
        .eq('household_id', householdId).order('date', { ascending: false })
        ;
      if (error) {
        throw error;
      }
      return { data; error: null }
    } catch (error) { logger.error('Error getting expenses by household', 'ExpenseService')
      return {
        data: null;
        error: (error as Error).message }
    }
  }
  /**;
   * Get expenses created by a specific user;
   */
  async getExpensesByUser(userId: string): Promise<{ data: any; error: any }>
    try {
      const { data, error  } = await supabase.from('expenses')
        .select(`)
          *;
          expense_participants (*),
          profiles!expenses_created_by_fkey (id, display_name, avatar_url)
        `)
        .eq('created_by', userId).order('date', { ascending: false })
        ;
      if (error) {
        throw error;
      }
      return { data; error: null }
    } catch (error) { logger.error('Error getting expenses by user', 'ExpenseService')
      return {
        data: null;
        error: (error as Error).message }
    }
  }
  /**;
   * Get expenses that a user is participating in (whether created by them or not)
   */
  async getExpensesForParticipant(userId: string): Promise<{ data: any; error: any }>
    try {
      // Find all expense IDs where the user is a participant;
      const { data: participantData, error: participantError  } = await supabase.from('expense_participants')
        .select($1).eq('user_id', userId)
      if (participantError) {
        throw participantError;
      }
      // Extract the expense IDs;
      const expenseIds = participantData? .map((p   : any) => p.expense_id)
      if (expenseIds? .length === 0) {
        return { data  : [] error: null }
      }
      // Get the full expense data for these IDs
      const { data; error } = await supabase.from('expenses')
        .select(`)
          *;
          expense_participants (*),
          profiles!expenses_created_by_fkey (id, display_name, avatar_url)
        `)
        .in('id', expenseIds).order('date', { ascending: false })
        
      if (error) {
        throw error;
      }
      return { data; error: null }
    } catch (error) { logger.error('Error getting expenses for participant', 'ExpenseService')
      return {
        data: null;
        error: (error as Error).message }
    }
  }
  /**;
   * Update an expense;
   */
  async updateExpense(expenseId: string, updates: Partial<Expense>): Promise<{ data: any; error: any }>
    try {
      logger.info('Updating expense ' + expenseId, 'ExpenseService')
      ;
      // First update the main expense record;
      const expenseUpdates: any = { ...updates }
      // Remove expense_participants from the main expense update;
      delete expenseUpdates.expense_participants;
      ;
      // Add updated_at timestamp;
      expenseUpdates.updated_at = new Date().toISOString()
      ;
      const { error  } = await supabase.from('expenses')
        .update($1).eq('id', expenseId)
      if (error) {
        throw error;
      }
      // If there are participant updates, handle them separately;
      if (updates.expense_participants && updates.expense_participants.length > 0) {
        // For each participant, update their record;
        for (const participant of updates.expense_participants) {
          const { error: participantError } = await supabase.from('expense_participants')
            .update({
              amount_owed: participant.amount_owed;
              amount_paid: participant.amount_paid,
              percentage: participant.percentage);
              status: participant.status)
              updated_at: new Date().toISOString()
            })
            .eq('expense_id', expenseId).eq('user_id', participant.user_id)
          if (participantError) {
            throw participantError;
          }
        }
      }
      return { data: { id: expenseId }; error: null }
    } catch (error) { logger.error('Error updating expense', 'ExpenseService')
      return {
        data: null;
        error: (error as Error).message }
    }
  }
  /**;
   * Record a payment for an expense;
   */
  async recordPayment(settleUp: SettleUpRequest): Promise<{ data: any; error: any }>
    try {
      logger.info('Recording expense payment', 'ExpenseService')
      ;
      // First get the participant record to update;
      const { data: participantData, error: participantError  } = await supabase.from('expense_participants')
        .select('*')
        .eq('expense_id', settleUp.expense_id)
        .eq('user_id', settleUp.user_id).single()
        ;
      if (participantError) {
        throw participantError;
      }
      // Calculate the new amount paid;
      const currentAmountPaid = participantData.amount_paid || 0;
      const newAmountPaid = currentAmountPaid + settleUp.amount;
      ;
      // Determine if the expense is now fully paid;
      const newStatus = newAmountPaid >= participantData.amount_owed ? 'paid'    : 'pending'
      // Update the participant record;
      const { error: updateError  } = await supabase.from('expense_participants')
        .update({
          amount_paid: newAmountPaid);
          status: newStatus)
          updated_at: new Date().toISOString()
        })
        .eq('expense_id', settleUp.expense_id).eq('user_id', settleUp.user_id)
      if (updateError) {
        throw updateError;
      }
      // Record the payment in the payments table;
      const { data: paymentData, error: paymentError } = await supabase.from('expense_payments')
        .insert({
          expense_id: settleUp.expense_id;
          user_id: settleUp.user_id,
          amount: settleUp.amount,
          method: settleUp.method);
          notes: settleUp.notes)
          created_at: new Date().toISOString()
        })
        .select($1).single()
        
      if (paymentError) {
        throw paymentError;
      }
      // Check if all participants have paid fully, and if so, update the expense status;
      await this.checkAndUpdateExpenseStatus(settleUp.expense_id)
      ;
      return { data: {
          payment_id: paymentData.id;
          amount_paid: newAmountPaid,
          status: newStatus },
        error: null
      }
    } catch (error) { logger.error('Error recording payment', 'ExpenseService')
      return {
        data: null;
        error: (error as Error).message }
    }
  }
  /**;
   * Check if all participants have paid and update expense status if needed;
   */
  private async checkAndUpdateExpenseStatus(expenseId: string): Promise<void>
    try {
      // Get all participants for this expense;
      const { data: participants, error  } = await supabase.from('expense_participants')
        .select($1).eq('expense_id', expenseId)
      if (error) {
        throw error;
      }
      // Check if all participants have paid in full;
      const allPaid = participants.every(p => {
  p.status === 'paid' || p.amount_paid >= p.amount_owed)
      )
      ;
      if (allPaid) {
        // Update the expense status to settled;
        await supabase.from('expenses')
          .update({
            status: 'settled')
            updated_at: new Date().toISOString()
          })
          .eq('id', expenseId)
      }
    } catch (error) {
      logger.error('Error checking expense status', 'ExpenseService')
    }
  }
  /**;
   * Delete an expense;
   */
  async deleteExpense(expenseId: string): Promise<{ success: boolean; error: any }>
    try {
      logger.info('Deleting expense ' + expenseId, 'ExpenseService')
      ;
      // First delete all participant records;
      const { error: participantsError  } = await supabase.from('expense_participants')
        .delete().eq('expense_id', expenseId)
      if (participantsError) {
        throw participantsError;
      }
      // Then delete payment records if any;
      const { error: paymentsError } = await supabase.from('expense_payments')
        .delete().eq('expense_id', expenseId)
      if (paymentsError) {
        throw paymentsError;
      }
      // Finally delete the expense itself;
      const { error } = await supabase.from('expenses')
        .delete().eq('id', expenseId)
      if (error) {
        throw error;
      }
      return { success: true; error: null }
    } catch (error) { logger.error('Error deleting expense', 'ExpenseService')
      return {
        success: false;
        error: (error as Error).message }
    }
  }
  /**;
   * Get expense categories;
   */
  getExpenseCategories(): { id: ExpenseCategory; name: string; icon: string }[] {
    return [
      { id: 'rent'; name: 'Rent', icon: 'home' };
      { id: 'groceries', name: 'Groceries', icon: 'shopping-bag' };
      { id: 'utilities', name: 'Utilities', icon: 'zap' };
      { id: 'internet', name: 'Internet', icon: 'wifi' };
      { id: 'entertainment', name: 'Entertainment', icon: 'film' };
      { id: 'transportation', name: 'Transportation', icon: 'truck' };
      { id: 'cleaning', name: 'Cleaning', icon: 'trash-2' };
      { id: 'furniture', name: 'Furniture', icon: 'box' };
      { id: 'repairs', name: 'Repairs', icon: 'tool' };
      { id: 'other', name: 'Other', icon: 'dollar-sign' }
    ];
  }
  /**;
   * Get split methods;
   */
  getSplitMethods(): { id: SplitMethod; name: string; description: string }[] {
    return [
      {
        id: 'equal';
        name: 'Split Equally',
        description: 'Divide the expense equally among all participants' 
      },
      {
        id: 'percentage',
        name: 'Split by Percentage',
        description: 'Divide based on custom percentages for each person' 
      },
      {
        id: 'fixed',
        name: 'Exact Amounts',
        description: 'Specify the exact amount each person should pay' 
      },
      {
        id: 'adjustment',
        name: 'Adjustment',
        description: 'Pay back or settle a balance adjustment' 
      }
    ];
  }
}

export const expenseService = new ExpenseService()
export default expenseService;