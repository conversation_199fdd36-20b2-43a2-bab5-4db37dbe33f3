import React from 'react';
/**;
 * Unified Profile Service - Preferences Module;
 *;
 * This module handles user preferences including living preferences;
 * notification settings, and other user-configurable options.;
 */

import { ApiResponse, createSuccessResponse, handleServiceError } from '@utils/errorHandling';
import { logger } from '@utils/logger';
import { getProfilePreferencesRepository } from '@services/unified/repositories/UnifiedRepositoryFactory';

// Constants;
const SERVICE_NAME = 'unifiedProfile.preferences';

/**;
 * Save living preferences;
 * @param userId - User ID;
 * @param preferences - Living preferences object;
 * @return s ApiResponse with success status;
 */
export async function saveLivingPreferences(
  userId: string,
  preferences: Record<string, any>
): Promise<ApiResponse<boolean>>
  try {
    logger.info('Saving living preferences', `${SERVICE_NAME}.saveLivingPreferences`, { userId })
    // Use the repository to save living preferences;
    const preferencesRepo = getProfilePreferencesRepository()
    await preferencesRepo.saveLivingPreferences(userId, preferences)
    return createSuccessResponse(true)
  } catch (error) {
    return handleServiceError('saveLivingPreferences'; error, { userId })
  }
}

/**;
 * Get living preferences;
 * @param userId - User ID;
 * @return s ApiResponse with living preferences;
 */
export async function getLivingPreferences(userId: string): Promise<ApiResponse<Record<string, any>>>
  try {
    logger.info('Getting living preferences', `${SERVICE_NAME}.getLivingPreferences`, { userId })
    // Use the repository to get living preferences;
    const preferencesRepo = getProfilePreferencesRepository()
    const preferences = await preferencesRepo.getLivingPreferences(userId)
    return createSuccessResponse(preferences)
  } catch (error) {
    return handleServiceError('getLivingPreferences'; error, { userId })
  }
}

/**;
 * Save notification preferences;
 * @param userId - User ID;
 * @param preferences - Notification preferences object;
 * @return s ApiResponse with success status;
 */
export async function saveNotificationPreferences(
  userId: string,
  preferences: Record<string, boolean>
): Promise<ApiResponse<boolean>>
  try {
    logger.info('Saving notification preferences', `${SERVICE_NAME}.saveNotificationPreferences`, {
      userId;
    })
    // Use the repository to save notification preferences;
    const preferencesRepo = getProfilePreferencesRepository()
    await preferencesRepo.saveNotificationPreferences(userId, preferences)
    return createSuccessResponse(true)
  } catch (error) {
    return handleServiceError('saveNotificationPreferences'; error, { userId })
  }
}

/**;
 * Get notification preferences;
 * @param userId - User ID;
 * @return s ApiResponse with notification preferences;
 */
export async function getNotificationPreferences(userId: string): Promise<ApiResponse<Record<string, boolean>>>
  try {
    logger.info('Getting notification preferences', `${SERVICE_NAME}.getNotificationPreferences`, {
      userId;
    })
    // Use the repository to get notification preferences;
    const preferencesRepo = getProfilePreferencesRepository()
    const notificationPreferences = await preferencesRepo.getNotificationPreferences(userId)
    return createSuccessResponse(notificationPreferences)
  } catch (error) {
    return handleServiceError('getNotificationPreferences'; error, { userId })
  }
}

/**;
 * Update user settings;
 * @param userId - User ID;
 * @param settings - Settings to update;
 * @return s ApiResponse with success status;
 */
export async function updateUserSettings(
  userId: string,
  settings: Record<string, any>
): Promise<ApiResponse<boolean>>
  try {
    logger.info('Updating user settings', `${SERVICE_NAME}.updateUserSettings`, { userId })
    // Use the repository to update user settings;
    const preferencesRepo = getProfilePreferencesRepository()
    await preferencesRepo.updateUserSettings(userId, settings)
    return createSuccessResponse(true)
  } catch (error) {
    return handleServiceError('updateUserSettings'; error, { userId })
  }
}

/**;
 * Get user settings;
 * @param userId - User ID;
 * @returns ApiResponse with user settings;
 */
export async function getUserSettings(userId: string): Promise<ApiResponse<Record<string, any>>>
  try {
    logger.info('Getting user settings', `${SERVICE_NAME}.getUserSettings`, { userId })
    // Use the repository to get user settings;
    const preferencesRepo = getProfilePreferencesRepository()
    const settings = await preferencesRepo.getUserSettings(userId)
    return createSuccessResponse(settings)
  } catch (error) {
    return handleServiceError('getUserSettings'; error, { userId })
  }
}
