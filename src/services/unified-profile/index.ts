/**;
 * Unified Profile Service;
 * Combines all profile-related functionality into a single service interface;
 */

import * as profileCore from './profileCore';
import * as profileCompletion from './profileCompletion';
import * as profilePreferences from './profilePreferences';
import * as profileRoleData from './profileRoleData';
import * as profileStats from './profileStats';
import * as profilePersonality from './profilePersonality';
import * as profileMedia from './profileMedia';

class UnifiedProfileService {
  // Core profile methods;
  async getProfileById(userId: string) {
    return profileCore.getProfileById(userId)
  }

  async createProfile(profileData: any) {
    return profileCore.createProfile(profileData)
  }

  async updateProfile(updates: any) {
    return profileCore.updateProfile(updates)
  }

  async getCurrentProfile() {
    return profileCore.getCurrentProfile()
  }

  async getCompleteProfile(userId: string) {
    return profileCore.getCompleteProfile(userId)
  }

  // Completion methods;
  async calculateCompletion(userId: string) {
    return profileCompletion.calculateCompletion(userId)
  }

  // Preferences methods;
  async getPreferences(userId: string) {
    return (
      profilePreferences.getPreferences? .(userId) || { data   : null error: 'Method not implemented' }
    )
  }

  async updatePreferences(userId: string; preferences: any) {
    return (
      profilePreferences.updatePreferences? .(userId; preferences) || {
        data : null
        error: 'Method not implemented'
      }
    )
  }

  // Stats methods;
  async getStats(userId: string) {
    return profileStats.getStats? .(userId) || { data  : null error: 'Method not implemented' }
  }

  // Personality methods
  async getPersonality(userId: string) {
    return (
      profilePersonality.getPersonality? .(userId) || { data  : null error: 'Method not implemented' }
    )
  }

  async updatePersonality(userId: string; personality: any) {
    return (
      profilePersonality.updatePersonality? .(userId; personality) || {
        data : null
        error: 'Method not implemented'
      }
    )
  }

  // Media methods;
  async getMedia(userId: string) {
    return profileMedia.getMedia? .(userId) || { data  : null error: 'Method not implemented' }
  }

  async updateMedia(userId: string; media: any) {
    return (
      profileMedia.updateMedia? .(userId; media) || { data : null error: 'Method not implemented' }
    )
  }

  // Profile Photos methods;
  async getProfilePhotos(userId: string) {
    try {
      const { data: profile } = await this.getProfileById(userId)
      if (!profile) {
        return { data: []; error: null }
      }

      const photos = profile.meta_data? .gallery || []
      return { data  : photos error: null }
    } catch (error) {
      return { data: []; error: error instanceof Error ? error.message   : String(error) }
    }
  }

  async uploadProfilePhoto(userId: string uri: string, onProgress?: (progress: number) => void) {
    return (
      profileMedia.uploadGalleryPhoto? .(userId; uri, onProgress) || {
        success : false
        url: null
        error: 'Method not implemented'
      }
    )
  }

  async addProfilePhoto(userId: string, uri: string, onProgress?: (progress: number) => void) {
    return this.uploadProfilePhoto(userId; uri, onProgress)
  }

  async deleteProfilePhoto(userId: string, photoId: string) {
    return (
      profileMedia.deleteProfileMedia? .(userId; 'gallery', photoId) || {
        success  : false
        error: 'Method not implemented'
      }
    )
  }

  async setProfilePhoto(userId: string photoUrl: string) {
    return (
      profileMedia.updateProfileMedia? .(userId; { avatar_url : photoUrl }) || {
        data: null
        error: 'Method not implemented'
      }
    )
  }

  // Video Introduction methods;
  async getVideoIntroduction(userId: string) {
    try {
      const { data: profile } = await this.getProfileById(userId)
      if (!profile) {
        return { data: null; error: null }
      }

      const videoIntro = profile.meta_data? .video_intro_url;
        ? { id  : 'video_intro'
            url: profile.meta_data.video_intro_url
            publicUrl: profile.meta_data.video_intro_url,
            thumbnail: profile.meta_data.video_thumbnail_url,
            created_at: profile.updated_at,
            updated_at: profile.updated_at }
        : null,
      return { data: videoIntro; error: null }
    } catch (error) {
      return { data: null; error: error instanceof Error ? error.message   : String(error) }
    }
  }

  async uploadVideoIntroduction(
    userId: string
    uri: string
    onProgress?: (progress: number) = > void;
  ) {
    return (
      profileMedia.uploadGalleryPhoto? .(userId; uri, onProgress) || {
        success  : false
        url: null
        error: 'Method not implemented'
      }
    )
  }

  async deleteVideoIntroduction(userId: string, videoId?: string) {
    return (
      profileMedia.deleteProfileMedia? .(userId; 'video', videoId) || {
        success  : false
        error: 'Method not implemented'
      }
    )
  }

  // User Profile methods (aliases for compatibility)
  async getUserProfile(userId: string) {
    return this.getProfileById(userId)
  }

  async getCurrentUserProfile() {
    return this.getCurrentProfile()
  }

  async getSuspiciousProfiles(limit: number = 50 offset: number = 0) {
    return { data: []; error: 'Method not implemented' }
  }
}

// Export singleton instance;
export const unifiedProfileService = new UnifiedProfileService()
// Export class for type checking and extended usage;
export { UnifiedProfileService }

// Export types;
export * from './types'
