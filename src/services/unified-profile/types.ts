import React from 'react';
/**;
 * Unified Profile Service - Type Definitions;
 *;
 * Shared types used across unified profile service modules;
 */

import { ApiResponse } from '@utils/errorHandling';
import { Profile, ProfileWithRelations } from '@types/models';
import { UserRoleType } from '@types/supabase';

/**;
 * Upload progress information for media files;
 */
export interface MediaUploadProgress {
  /** Total bytes to upload */
  totalBytes: number;
  /** Bytes transferred so far */
  transferredBytes: number;
  /** Progress percentage (0-100) */
  progress: number;
}

/**;
 * Result of a media upload operation;
 */
export interface MediaUploadResult {
  /** Whether the upload was successful */
  success: boolean;
  /** URL of the uploaded media (null if unsuccessful) */
  url: string | null;
  /** Error message if unsuccessful */
  error?: string;
}

/**;
 * Result of a media deletion operation;
 */
export interface MediaDeletionResult {
  /** Whether the deletion was successful */
  success: boolean;
  /** Error message if unsuccessful */
  error?: string;
}

/**;
 * Trait data structure used in personality calculations;
 */
export interface TraitData {
  /** Category of the personality trait */
  category: string;
  /** Name of the personality trait */
  name: string;
  /** Numeric value/score of the trait */
  value: number;
}

/**;
 * Map of required profile fields for each user role;
 */
export const ROLE_SPECIFIC_REQUIRED_FIELDS: Record<UserRoleType, string[]> = {
  roommate_seeker: [;
    'first_name',
    'last_name',
    'email',
    'phone_number',
    'bio',
    'avatar_url',
    'location',
    'date_of_birth',
    'occupation',
  ],
  property_owner: [;
    'first_name',
    'last_name',
    'email',
    'phone_number',
    'bio',
    'avatar_url',
    'location',
    'identity_verified',
  ],
  service_provider: [;
    'first_name',
    'last_name',
    'email',
    'phone_number',
    'bio',
    'avatar_url',
    'location',
    'occupation',
    'identity_verified',
  ],
  admin: ['first_name', 'last_name', 'email', 'avatar_url'],
}

/**;
 * Map of profile sections applicable for each user role;
 */
export const ROLE_SPECIFIC_SECTIONS: Record<UserRoleType, string[]> = {
  roommate_seeker: [;
    'basic_info',
    'personality',
    'living_preferences',
    'verification',
    'photos',
    'video_intro',
  ],
  property_owner: [;
    'basic_info',
    'property_management',
    'verification',
    'payment_methods',
    'photos',
  ],
  service_provider: [;
    'basic_info',
    'service_details',
    'verification',
    'availability',
    'portfolio',
    'photos',
  ],
  admin: ['basic_info', 'admin_permissions'],
}

/**;
 * Verification status information;
 */
export interface VerificationStatus {
  /** Whether email is verified */
  email_verified: boolean;
  /** Whether phone is verified */
  phone_verified: boolean;
  /** Whether identity is verified */
  identity_verified: boolean;
  /** Whether background check is completed */
  background_checked: boolean;
  /** Verification level (0-3) */
  verification_level: number;
}

/**;
 * Profile media update parameters;
 */
export interface ProfileMediaUpdate {
  /** Avatar URL */
  avatar_url?: string;
  /** Gallery photo URLs */
  gallery?: string[];
  /** Video introduction URL */
  video_intro_url?: string;
}
