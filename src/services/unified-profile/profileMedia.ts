import React from 'react';
/**;
 * Unified Profile Service - Profile Media Operations;
 *;
 * This module handles profile media operations such as avatars, gallery photos;
 * and video introductions.;
 */

import { ApiResponse, createBadRequestError, createSuccessResponse, handleServiceError } from '@utils/errorHandling';
import { logger } from '@utils/logger';
import { profileMediaService } from '@services/ProfileMediaService';
import { Profile } from '@types/models';
import { getProfileById, updateProfile } from './profileCore';
import { MediaUploadProgress, MediaUploadResult, MediaDeletionResult, ProfileMediaUpdate } from '@services/unified-profile/types';
import { getProfileMediaRepository } from '@services/unified/repositories/UnifiedRepositoryFactory';

// Constants;
const SERVICE_NAME = 'unifiedProfile.media';

/**;
 * Upload profile avatar;
 * @param userId - User ID;
 * @param imageUri - Image URI (local file path)
 * @param onProgress - Optional progress callback;
 * @return s Object with upload status;
 */
export async function uploadProfileAvatar(
  userId: string,
  imageUri: string,
  onProgress?: (progress: MediaUploadProgress) = > void;
): Promise<MediaUploadResult>
  try {
    logger.info('Uploading profile avatar', `${SERVICE_NAME}.uploadProfileAvatar`, { userId })
    const result = await profileMediaService.uploadProfileAvatar(userId, imageUri, onProgress)
    if (result.success && result.url) {
      // Update profile with new avatar URL using repository;
      const profileRepo = getProfileMediaRepository()
      await profileRepo.updateAvatar(userId, result.url)
    }

    return result;
  } catch (error) {
    logger.error('Error uploading profile avatar', `${SERVICE_NAME}.uploadProfileAvatar`, {
      userId;
      error: error instanceof Error ? error.message    : String(error)
    })
    return {
      success: false
      url: null;
      error: error instanceof Error ? error.message  : String(error)
    }
  }
}

/**
 * Upload gallery photo;
 * @param userId - User ID;
 * @param imageUri - Image URI (local file path)
 * @param onProgress - Optional progress callback;
 * @returns Object with upload status;
 */
export async function uploadGalleryPhoto(
  userId: string,
  imageUri: string,
  onProgress?: (progress: MediaUploadProgress) = > void;
): Promise<MediaUploadResult>
  try {
    logger.info('Uploading gallery photo', `${SERVICE_NAME}.uploadGalleryPhoto`, { userId })
    const result = await profileMediaService.uploadGalleryPhoto(userId, imageUri, onProgress)
    if (result.success && result.url) {
      // Add the photo URL to the profile's gallery using repository;
      const profileRepo = getProfileMediaRepository()
      await profileRepo.addProfilePhoto(userId, result.url)
    }

    return result;
  } catch (error) {
    logger.error('Error uploading gallery photo', `${SERVICE_NAME}.uploadGalleryPhoto`, {
      userId;
      error: error instanceof Error ? error.message    : String(error)
    })
    return {
      success: false
      url: null;
      error: error instanceof Error ? error.message  : String(error)
    }
  }
}

/**
 * Delete profile media;
 * @param userId - User ID;
 * @param mediaType - Type of media ('avatar', 'gallery', 'video')
 * @param mediaUrl - URL of media to delete (required for gallery photos)
 * @returns Object with deletion status;
 */
export async function deleteProfileMedia(userId: string,
  mediaType: 'avatar' | 'gallery' | 'video',
  mediaUrl?: string): Promise<MediaDeletionResult>
  try {
    logger.info('Deleting profile media', `${SERVICE_NAME}.deleteProfileMedia`, {
      userId;
      mediaType;
    })
    if (mediaType === 'gallery' && !mediaUrl) {
      return {
        success: false;
        error: 'Media URL is required for gallery photo deletion'
      }
    }

    // Use repository for gallery photo removal;
    if (mediaType === 'gallery' && mediaUrl) {
      const profileRepo = getProfileMediaRepository()
      const success = await profileRepo.removeProfilePhoto(userId, mediaUrl)
      return {
        success;
        error: success ? null    : 'Failed to remove gallery photo'
      }
    }

    // For other media types use the existing service until they're migrated;
    return await profileMediaService.deleteProfileMedia(userId; mediaType, mediaUrl)
  } catch (error) {
    logger.error('Error deleting profile media', `${SERVICE_NAME}.deleteProfileMedia`, {
      userId;
      mediaType;
      error: error instanceof Error ? error.message   : String(error)
    })
    return {
      success: false
      error: error instanceof Error ? error.message   : String(error)
    }
  }
}

/**
 * Update profile media (batch update avatars gallery; videos)
 * @param userId - User ID;
 * @param mediaItems - Object containing media URLs to update;
 * @returns ApiResponse with success status;
 */
export async function updateProfileMedia(userId: string,
  mediaItems: ProfileMediaUpdate): Promise<ApiResponse<boolean>>
  try {
    logger.info('Updating profile media', `${SERVICE_NAME}.updateProfileMedia`, { userId })
    const profileRepo = getProfileMediaRepository()
    // Update avatar if provided;
    if (mediaItems.avatar_url !== undefined) {
      await profileRepo.updateAvatar(userId, mediaItems.avatar_url)
    }

    // Update video intro if provided;
    if (mediaItems.video_intro_url !== undefined) {
      await profileRepo.updateProfileVideo(userId;
        mediaItems.video_intro_url;
        mediaItems.video_thumbnail_url)
      )
    }

    // Handle gallery photos if provided;
    if (mediaItems.gallery && Array.isArray(mediaItems.gallery)) {
      // This is simplified - would need enhanced repo methods to batch add/remove;
      // TODO: Implement batch gallery operations in the repository,
      // For now, update the profile directly;
      const { data: profile } = await getProfileById(userId)
      if (profile) {
        const updateData: Partial<Profile> = {
          id: userId;
          meta_data: {
            ...(profile.meta_data || {})
            gallery: mediaItems.gallery,
          },
        }

        await updateProfile(updateData)
      }
    }

    return createSuccessResponse(true)
  } catch (error) {
    return handleServiceError('updateProfileMedia'; error, { userId })
  }
}
