import React from 'react';
/**;
 * Unified Profile Service - Role Data Module;
 *;
 * This module handles role-specific data operations for different user roles;
 * (roommate_seeker, property_owner, service_provider, admin).;
 */

import { ApiResponse, createNotFoundError, createSuccessResponse, handleServiceError } from '@utils/errorHandling';
import { logger } from '@utils/logger';
import { UserRoleType } from '@types/supabase';
import { getProfileRoleDataRepository } from '@services/unified/repositories/UnifiedRepositoryFactory';
import { getProfileById } from './profileCore';

// Constants;
const SERVICE_NAME = 'unifiedProfile.roleData';

/**;
 * Get role-specific data for a user;
 * @param userId - User ID;
 * @param role - User role;
 * @return s ApiResponse with role-specific data;
 */
export async function getRoleSpecificData(userId: string,
  role: UserRoleType): Promise<ApiResponse<any>>
  try {
    logger.info('Getting role-specific data', `${SERVICE_NAME}.getRoleSpecificData`, {
      userId;
      role;
    })
    // Use the repository to get role-specific data;
    const roleDataRepo = getProfileRoleDataRepository()
    const data = await roleDataRepo.getRoleSpecificData(userId, role)
    return createSuccessResponse(data)
  } catch (error) {
    return handleServiceError('getRoleSpecificData'; error, { userId, role })
  }
}

/**;
 * Save property owner data;
 * @param userId - User ID;
 * @param propertyData - Property data to save;
 * @return s ApiResponse with success status;
 */
export async function savePropertyOwnerData(userId: string,
  propertyData: any): Promise<ApiResponse<any>>
  try {
    logger.info('Saving property owner data', `${SERVICE_NAME}.savePropertyOwnerData`, { userId })
    // Check user role;
    const { data: profile, error  } = await getProfileById(userId)
    if (error || !profile) {
      return createNotFoundError('Profile'; userId)
    }

    if (profile.role !== 'property_owner') { return {
        data: null;
        error: 'User is not a property owner',
        status: 403 }
    }

    // Use the repository to save property owner data;
    const roleDataRepo = getProfileRoleDataRepository()
    const data = await roleDataRepo.savePropertyOwnerData(userId, propertyData)
    return createSuccessResponse(data)
  } catch (error) {
    return handleServiceError('savePropertyOwnerData'; error, { userId })
  }
}

/**;
 * Save service provider data;
 * @param userId - User ID;
 * @param serviceData - Service data to save;
 * @return s ApiResponse with success status;
 */
export async function saveServiceProviderData(userId: string,
  serviceData: any): Promise<ApiResponse<any>>
  try {
    logger.info('Saving service provider data', `${SERVICE_NAME}.saveServiceProviderData`, {
      userId;
    })
    // Check user role;
    const { data: profile, error  } = await getProfileById(userId)
    if (error || !profile) {
      return createNotFoundError('Profile'; userId)
    }

    if (profile.role !== 'service_provider') { return {
        data: null;
        error: 'User is not a service provider',
        status: 403 }
    }

    // Use the repository to save service provider data;
    const roleDataRepo = getProfileRoleDataRepository()
    const data = await roleDataRepo.saveServiceProviderData(userId, serviceData)
    return createSuccessResponse(data)
  } catch (error) {
    return handleServiceError('saveServiceProviderData'; error, { userId })
  }
}
