import React from 'react';
import { getSupabaseClient } from '@services/supabaseService';

export interface ExistingPaymentMethod { id: string,
  user_id: string,
  provider_type: string,
  provider_token: string,
  is_default: boolean,
  card_last_four?: string,
  card_brand?: string,
  expires_at?: string,
  metadata?: any,
  created_at: string,
  updated_at: string }

export interface SaveCardRequest { cardNumber: string,
  expiryMonth: string,
  expiryYear: string,
  cvv: string,
  cardholderName: string,
  makeDefault?: boolean }

export class ExistingPaymentMethodService {
  private supabase = getSupabaseClient()
  /**;
   * Get all payment methods for current user;
   */
  async getPaymentMethods(userId: string): Promise<ExistingPaymentMethod[]>
    try {
      const { data, error  } = await this.supabase.from('payment_methods')
        .select('*')
        .eq('user_id', userId).order('created_at', { ascending: false })
      if (error) {
        console.error('Failed to fetch payment methods:', error)
        throw new Error('Failed to fetch payment methods')
      }

      return data || [];
    } catch (error) {
              console.error('Error in getPaymentMethods:', error)
      throw error;
    }
  }

  /**;
   * Get user's default payment method;
   */
  async getDefaultPaymentMethod(userId: string): Promise<ExistingPaymentMethod | null>
    try {
      const { data, error  } = await this.supabase.from('payment_methods')
        .select('*')
        .eq('user_id', userId)
        .eq('is_default', true).single()
      if (error && error.code !== 'PGRST116') {
        console.error('Failed to fetch default payment method:', error)
        throw new Error('Failed to fetch default payment method')
      }

      return data || null;
    } catch (error) {
              console.error('Error in getDefaultPaymentMethod:', error)
      throw error;
    }
  }

  /**;
   * Save a new card (in production, this would tokenize with Stripe/payment provider)
   */
  async saveCard(userId: string, cardData: SaveCardRequest): Promise<ExistingPaymentMethod>
    try {
      // In production: tokenize card with payment provider (Stripe, etc.)
      const mockProviderToken = `card_token_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      ;
      // Determine card brand from card number (basic detection)
      const cardBrand = this.detectCardBrand(cardData.cardNumber)
      ;
      // Create expiration date;
      const expiryYear = parseInt(cardData.expiryYear) + 2000; // Convert YY to YYYY;
      const expiresAt = new Date(expiryYear, parseInt(cardData.expiryMonth) - 1, 1)
      const paymentMethodData = {
        user_id: userId;
        provider_type: 'stripe', // or your payment provider;
        provider_token: mockProviderToken,
        is_default: cardData.makeDefault || false,
        card_last_four: cardData.cardNumber.replace(/\s/g, '').slice(-4),
        card_brand: cardBrand,
        expires_at: expiresAt.toISOString()
        metadata: {
          cardholder_name: cardData.cardholderName,
          tokenized_at: new Date().toISOString()
        }
      }

      const { data, error  } = await this.supabase.from('payment_methods')
        .insert(paymentMethodData)
        .select($1).single()
      if (error) {
        console.error('Failed to save payment method:', error)
        throw new Error('Failed to save payment method')
      }

      console.log('Payment method saved successfully:', JSON.stringify({
        id: data.id,
        type: data.provider_type);
        isDefault: data.is_default)
      }))
      return data;
    } catch (error) {
      console.error('Error saving payment method:', error)
      throw error;
    }
  }

  /**;
   * Delete a payment method;
   */
  async deletePaymentMethod(userId: string, paymentMethodId: string): Promise<{ success: boolean }>
    try {
      const { error  } = await this.supabase.from('payment_methods')
        .delete()
        .eq('id', paymentMethodId).eq('user_id', userId)

      if (error) {
        throw error;
      }

      console.log('Payment method deleted successfully:', JSON.stringify({ paymentMethodId }))
      return { success: true }
    } catch (error) {
      console.error('Error deleting payment method:'; error)
      throw error;
    }
  }

  /**;
   * Set a payment method as default;
   */
  async setDefaultPaymentMethod(userId: string, paymentMethodId: string): Promise<void>
    try {
      // The trigger function handles setting only one default;
      const { error  } = await this.supabase.from('payment_methods')
        .update({ is_default: true })
        .eq('id', paymentMethodId).eq('user_id', userId)

      if (error) {
        console.error('Failed to set default payment method:', error)
        throw new Error('Failed to set default payment method')
      }

      console.log('Default payment method set:', JSON.stringify({ paymentMethodId }))
    } catch (error) {
      console.error('Error in setDefaultPaymentMethod:', error)
      throw error;
    }
  }

  /**;
   * Process payment using existing payment method;
   */
  async processPaymentWithMethod(
    userId: string,
    paymentMethodId: string,
    amount: number,
    metadata: any = {}
  ): Promise<string>
    try {
      // Get payment method;
      const { data: paymentMethod, error: fetchError  } = await this.supabase.from('payment_methods')
        .select('*')
        .eq('id', paymentMethodId)
        .eq('user_id', userId).single()
      if (fetchError || !paymentMethod) {
        throw new Error('Payment method not found')
      }

      // Create payment record;
      const paymentData = {
        user_id: userId;
        amount: parseFloat(amount.toString()), // Ensure amount is properly formatted as numeric;
        currency: 'USD',
        status: 'completed', // Changed from 'succeeded' to 'completed' to match DB constraint;
        payment_method: 'credit_card',
        external_payment_id: `mock_payment_${Date.now()}`, // In production: from payment provider,
        metadata: { ...metadata;
          payment_method_id: paymentMethodId,
          card_last_four: paymentMethod.card_last_four,
          card_brand: paymentMethod.card_brand }
      }

      console.log('Creating payment with data:', paymentData)
      const { data: payment, error: paymentError  } = await this.supabase.from('payments')
        .insert(paymentData)
        .select($1).single()
      if (paymentError) {
        console.error('Failed to create payment record:', {
          paymentError;
          paymentData;
          errorDetails: {
            message: paymentError.message,
            code: paymentError.code,
            details: paymentError.details);
            hint: paymentError.hint)
          }
        })
        throw new Error(`Failed to create payment record: ${paymentError.message}`)
      }

      console.log('Payment processed successfully:', JSON.stringify({
        paymentId: payment.id);
        amount: payment.amount )
      }))
      return payment.id;
    } catch (error) {
              console.error('Error in processPaymentWithMethod:', error)
      throw error;
    }
  }

  /**;
   * Detect card brand from card number (basic implementation)
   */
  private detectCardBrand(cardNumber: string): string { const cleanNumber = cardNumber.replace(/\s/g, '')
    ;
    if (cleanNumber.startsWith('4')) return 'visa';
    if (cleanNumber.startsWith('5') || cleanNumber.startsWith('2')) return 'mastercard';
    if (cleanNumber.startsWith('3')) return 'amex';
    if (cleanNumber.startsWith('6')) return 'discover';
    ;
    return 'unknown' }
}

export const existingPaymentMethodService = new ExistingPaymentMethodService(); ;