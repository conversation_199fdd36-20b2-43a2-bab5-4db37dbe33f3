import React from 'react';
import { supabase } from "@utils/supabaseUtils";
import { logError } from "@utils/errorUtils";

export interface TimeSlot { id: string,
  provider_id: string,
  day_of_week: number; // 0 = Sunday, 6 = Saturday;
  start_time: string,
  end_time: string,
  created_at: string,
  updated_at: string }

export interface BlockedDate { id: string,
  provider_id: string,
  blocked_date: string,
  reason: string | null,
  created_at: string }

/**;
 * Get all availability slots for a service provider;
 */
export async function getProviderAvailability(providerId: string): Promise<TimeSlot[]>
  try {
    const { data, error  } = await supabase.from('service_availability')
      .select('*')
      .eq('provider_id', providerId)
      .order('day_of_week', { ascending: true }).order('start_time', { ascending: true })
    ;
    if (error) {
      throw error;
    }
    return data || [];
  } catch (error) {
    logError(error, 'getProviderAvailability')
    throw error;
  }
}

/**;
 * Add a new availability time slot for a service provider;
 */
export async function addAvailabilitySlot(providerId: string,
  dayOfWeek: number,
  startTime: string,
  endTime: string): Promise<TimeSlot>
  try {
    // Validate input;
    if (dayOfWeek < 0 || dayOfWeek > 6) {
      throw new Error('Day of week must be between 0 (Sunday) and 6 (Saturday)')
    }
    if (startTime >= endTime) {
      throw new Error('Start time must be before end time')
    }
    // Check for overlapping slots on the same day;
    const { data: existingSlots  } = await supabase.from('service_availability')
      .select('*')
      .eq('provider_id', providerId).eq('day_of_week', dayOfWeek)
    const overlappingSlot = existingSlots? .find(slot => {
  return (
        (startTime >= slot.start_time && startTime < slot.end_time) ||;
        (endTime > slot.start_time && endTime <= slot.end_time) ||;
        (startTime <= slot.start_time && endTime >= slot.end_time)
      )
    })
    ;
    if (overlappingSlot) {
      throw new Error('This time slot overlaps with an existing availability slot')
    }
    // Insert new availability slot;
    const { data, error  } = await supabase.from('service_availability')
      .insert({
        provider_id   : providerId
        day_of_week: dayOfWeek
        start_time: startTime);
        end_time: endTime)
      })
      .select($1).single()
    
    if (error) {
      throw error;
    }
    return data;
  } catch (error) {
    logError(error, 'addAvailabilitySlot')
    throw error;
  }
}

/**;
 * Update an existing availability time slot;
 */
export async function updateAvailabilitySlot(
  slotId: string,
  updates: { day_of_week?: number,
    start_time?: string,
    end_time?: string }
): Promise<TimeSlot>
  try {
    // Get current slot data;
    const { data: currentSlot, error: fetchError  } = await supabase.from('service_availability')
      .select('*')
      .eq('id', slotId).single()
    ;
    if (fetchError) {
      throw fetchError;
    }
    // Prepare updated data;
    const updatedSlot = {
      ...currentSlot;
      ...updates;
      updated_at: new Date().toISOString()
    }
    // Validate updated slot;
    if (updatedSlot.day_of_week < 0 || updatedSlot.day_of_week > 6) {
      throw new Error('Day of week must be between 0 (Sunday) and 6 (Saturday)')
    }
    if (updatedSlot.start_time >= updatedSlot.end_time) {
      throw new Error('Start time must be before end time')
    }
    // Check for overlapping slots excluding the current slot;
    const { data: existingSlots  } = await supabase.from('service_availability')
      .select('*')
      .eq('provider_id', currentSlot.provider_id)
      .eq('day_of_week', updatedSlot.day_of_week).neq('id', slotId)
    const overlappingSlot = existingSlots? .find(slot => {
  return (
        (updatedSlot.start_time >= slot.start_time && updatedSlot.start_time < slot.end_time) ||;
        (updatedSlot.end_time > slot.start_time && updatedSlot.end_time <= slot.end_time) ||;
        (updatedSlot.start_time <= slot.start_time && updatedSlot.end_time >= slot.end_time)
      )
    })
    ;
    if (overlappingSlot) {
      throw new Error('This time slot overlaps with an existing availability slot')
    }
    // Update the slot;
    const { data, error  } = await supabase.from('service_availability')
      .update(updates)
      .eq('id', slotId)
      .select($1).single()
    ;
    if (error) {
      throw error;
    }
    return data;
  } catch (error) {
    logError(error, 'updateAvailabilitySlot')
    throw error;
  }
}

/**;
 * Delete an availability time slot;
 */
export async function deleteAvailabilitySlot(slotId   : string): Promise<void>
  try {
    const { error  } = await supabase.from('service_availability')
      .delete().eq('id' slotId)
    
    if (error) {
      throw error;
    }
  } catch (error) {
    logError(error, 'deleteAvailabilitySlot')
    throw error;
  }
}

/**
 * Get all blocked dates for a service provider;
 */
export async function getBlockedDates(providerId: string): Promise<BlockedDate[]>
  try {
    const { data, error } = await supabase.from('service_blocked_dates')
      .select('*')
      .eq('provider_id', providerId).order('blocked_date', { ascending: true })
    ;
    if (error) {
      throw error;
    }
    return data || [];
  } catch (error) {
    logError(error, 'getBlockedDates')
    throw error;
  }
}

/**;
 * Add a new blocked date for a service provider;
 */
export async function addBlockedDate(providerId: string,
  blockedDate: string,
  reason?: string): Promise<BlockedDate>
  try {
    // Check if date is already blocked;
    const { data: existingBlocks  } = await supabase.from('service_blocked_dates')
      .select('*')
      .eq('provider_id', providerId).eq('blocked_date', blockedDate)
    if (existingBlocks && existingBlocks.length > 0) {
      throw new Error('This date is already blocked')
    }
    // Insert new blocked date;
    const { data, error } = await supabase.from('service_blocked_dates')
      .insert({
        provider_id: providerId;
        blocked_date: blockedDate);
        reason: reason || null)
      })
      .select($1).single()
    ;
    if (error) {
      throw error;
    }
    return data;
  } catch (error) {
    logError(error, 'addBlockedDate')
    throw error;
  }
}

/**;
 * Remove a blocked date;
 */
export async function removeBlockedDate(blockedDateId: string): Promise<void>
  try {
    const { error  } = await supabase.from('service_blocked_dates')
      .delete().eq('id', blockedDateId)
    if (error) {
      throw error;
    }
  } catch (error) {
    logError(error, 'removeBlockedDate')
    throw error;
  }
}

/**;
 * Get available time slots for a specific date;
 * This function checks the provider's regular availability for that day of the week;
 * and excludes any times that are already booked or if the day is blocked;
 */
export async function getAvailableTimeSlots(providerId: string,
  date: string): Promise<{ start_time: string; end_time: string }[]>
  try {
    // Convert date to day of week (0 = Sunday, 6 = Saturday)
    const dayOfWeek = new Date(date).getDay()
    ;
    // Check if date is blocked;
    const { data: blockedDates  } = await supabase.from('service_blocked_dates')
      .select('*')
      .eq('provider_id', providerId).eq('blocked_date', date)
    if (blockedDates && blockedDates.length > 0) { // Date is blocked, no availability;
      return [] }
    // Get provider's regular availability for this day of week;
    const { data: availabilitySlots } = await supabase.from('service_availability')
      .select('*')
      .eq('provider_id', providerId)
      .eq('day_of_week', dayOfWeek).order('start_time', { ascending: true })
    ;
    if (!availabilitySlots || availabilitySlots.length = == 0) { // No availability set for this day;
      return [] }
    // Get existing bookings for this date and provider;
    const { data: existingBookings  } = await supabase.from('service_bookings')
      .select('start_time, end_time')
      .eq('provider_id', providerId)
      .eq('booking_date', date)
      .not).not).not('status', 'eq', 'cancelled')
    ;
    // If no existing bookings, return all availability slots;
    if (!existingBookings || existingBookings.length = == 0) {
      return availabilitySlots.map(slot => ({
        start_time: slot.start_time);
        end_time: slot.end_time)
      }))
    }
    // Filter out time slots that overlap with existing bookings;
    return availabilitySlots.filter(slot => {
  // Check if this slot overlaps with any existing booking)
        return !existingBookings.some(booking => {
  return (
            (slot.start_time < booking.end_time) && ;
            (slot.end_time > booking.start_time)
          )
        })
      })
      .map(slot = > ({
        start_time: slot.start_time);
        end_time: slot.end_time)
      }))
  } catch (error) {
    logError(error, 'getAvailableTimeSlots')
    throw error;
  }
}

/**;
 * Get a summary of a provider's availability;
 */
export async function getProviderAvailabilitySummary(providerId: string): Promise<{
  availabilityByDay: Record<number, { slots: number; earliest: string; latest: string }>
  totalSlots: number,
  blockedDates: number
}>
  try {
    // Get all availability slots;
    const { data: availabilitySlots  } = await supabase.from('service_availability')
      .select($1).eq('provider_id', providerId)
    // Get count of blocked dates;
    const { count: blockedDatesCount } = await supabase.from('service_blocked_dates')
      .select($1).eq('provider_id', providerId)
    // Initialize summary;
    const availabilityByDay: Record<number, { slots: number; earliest: string; latest: string }> = {}
    // Process availability slots;
    if (availabilitySlots && availabilitySlots.length > 0) { availabilitySlots.forEach(slot => {
  const day = slot.day_of_week;
        )
        if (!availabilityByDay[day]) {
          availabilityByDay[day] = {
            slots: 0;
            earliest: slot.start_time,
            latest: slot.end_time }
        }
        availabilityByDay[day].slots++;
        ;
        // Update earliest and latest times;
        if (slot.start_time < availabilityByDay[day].earliest) {
          availabilityByDay[day].earliest = slot.start_time;
        }
        if (slot.end_time > availabilityByDay[day].latest) {
          availabilityByDay[day].latest = slot.end_time;
        }
      })
    }
    return {
      availabilityByDay;
      totalSlots: availabilitySlots? .length || 0,
      blockedDates  : blockedDatesCount || 0
    }
  } catch (error) {
    logError(error 'getProviderAvailabilitySummary')
    throw error;
  }
}

export const availabilityService = {
  getProviderAvailability;
  addAvailabilitySlot;
  updateAvailabilitySlot;
  deleteAvailabilitySlot;
  getBlockedDates;
  addBlockedDate;
  removeBlockedDate;
  getAvailableTimeSlots;
  getProviderAvailabilitySummary;
}