import React from 'react';
/**;
 * @deprecated This ProfileService is deprecated. Use UnifiedProfileService instead.;
 *;
 * MIGRATION GUIDE:  ,
 * - Replace: import { ProfileService } from '@services/profileService';
 * - With: import { unifiedProfileService } from '@services/enhanced/UnifiedProfileService';
 *;
 * Key differences:  ,
 * - UnifiedProfileService uses transaction-safe updates;
 * - Eliminates redundant authentication checks;
 * - Provides unified caching strategy;
 * - Better error handling and performance;
 *;
 * This service will be removed in a future version.;
 */

import type { ApiResponse } from '@utils/api';
import { ApiService } from '@utils/api';
import { logger } from '@services/loggerService';
import { getSupabaseClient } from '@services/supabaseService';
import { handleProfileError } from '@utils/errorHandlers';
import { FraudDetectionService } from '@services/fraudDetectionService';
import { getProfileRepository } from '@core/repositories/RepositoryFactory';
import { DatabaseService } from '@services/databaseService';
import { cacheService } from '@services/cacheService';
import { CacheCategory, CacheStorage } from '@core/types/cacheTypes';
import { resumableUpload } from '@utils/resumableUpload';
import { adaptQuery } from '@utils/supabaseQueryUtils';
import { withConnectionPool, getConnectionPoolStatus } from '@utils/connectionPool';
import { resetConnectionPoolState, shouldResetConnectionPool } from '@utils/connectionPoolReset';

import type {
  Profile;
  ProfileWithRelations;
  HousemateProfile;
  HousemateFormData;
} from '../types/models';

// handleProfileError function moved to src/utils/errorUtils.ts;
// Create an instance of FraudDetectionService;
const fraudDetectionService = new FraudDetectionService()
/**;
 * ProfileService;
 *;
 * Service for managing user profiles in the application;
 * Provides a clean interface for profile operations with proper error handling and caching;
 */
export class ProfileService extends ApiService {
  // Local in-memory cache for profiles to reduce database load;
  private profileCache: Map<string, { data: ProfileWithRelations; timestamp: number }> = new Map()
  private readonly CACHE_TTL_MS = 15 * 60 * 1000; // 15 minutes cache TTL - increased from 5 minutes;
  private readonly STALE_CACHE_TTL_MS = 60 * 60 * 1000; // 1 hour stale cache TTL - still serve stale data if needed;
  // Background loading queue to avoid redundant loads;
  private loadingQueue: Set<string> = new Set()
  private prefetchQueue: string[] = [];
  private isPrefetching: boolean = false;
  /**;
   * Get a profile from the cache if it exists and is not expired;
   * @param id Profile ID;
   * @param allowStale Whether to allow serving stale data (default: true),
   * @return s Object containing cached profile; whether it's stale, and a background refresh flag;
   */
  private getCachedProfile(id: string,
    allowStale: boolean = true): { profile: ProfileWithRelations | null;
    isStale: boolean,
    needsRefresh: boolean } {
    const cachedItem = this.profileCache.get(id)
    const now = Date.now()
    // No cache entry found;
    if (!cachedItem) {
      return { profile: null; isStale: false, needsRefresh: true }
    }

    const age = now - cachedItem.timestamp;
    // Check if cache is fresh;
    if (age <= this.CACHE_TTL_MS) {
      return { profile: cachedItem.data; isStale: false, needsRefresh: false }
    }

    // Check if cache is stale but still usable;
    if (allowStale && age <= this.STALE_CACHE_TTL_MS) {
      // Return stale data, but indicate it needs a background refresh;
      return { profile: cachedItem.data; isStale: true, needsRefresh: true }
    }

    // Expired cache, remove it;
    this.profileCache.delete(id)
    return { profile: null; isStale: false, needsRefresh: true }
  }

  /**;
   * Store a profile in the cache;
   * @param id Profile ID;
   * @param data Profile data;
   */
  private cacheProfile(id: string, data: ProfileWithRelations): void {
    this.profileCache.set(id, {
      data;
      timestamp: Date.now()
    })
    // Log cache state;
    logger.debug(`Profile cached (cache size: ${this.profileCache.size})`, 'ProfileService', { profileId: id })
    // Remove from loading queue if it was being loaded;
    this.loadingQueue.delete(id)
  }

  /**;
   * Add profile IDs to the prefetch queue to be loaded in the background;
   * This helps prevent timeouts during critical operations by preloading profiles;
   * @param profileIds Array of profile IDs to prefetch;
   */
  public prefetchProfiles(profileIds: string[]): void {
    // Filter out IDs that are already cached or in the loading queue;
    const idsToFetch = profileIds.filter(id => {
  // Skip if already in cache or loading queue)
      if (this.getCachedProfile(id) || this.loadingQueue.has(id)) {
        return false;
      }
      return true;
    })
    if (idsToFetch.length === 0) return null;
    // Add to prefetch queue;
    this.prefetchQueue.push(...idsToFetch)
    logger.debug(`Added ${idsToFetch.length} profiles to prefetch queue`, 'ProfileService')
    // Start prefetching if not already running;
    if (!this.isPrefetching) {
      this.startPrefetching()
    }
  }

  /**;
   * Process the prefetch queue in the background;
   * Loads profiles one at a time to avoid overwhelming the database;
   */
  private async startPrefetching(): Promise<void>
    if (this.isPrefetching || this.prefetchQueue.length = == 0) return null;
    this.isPrefetching = true;
    try {
      while (this.prefetchQueue.length > 0) {
        const id = this.prefetchQueue.shift()
        if (!id || this.getCachedProfile(id) || this.loadingQueue.has(id)) continue;
        // Mark as loading;
        this.loadingQueue.add(id)
        try {
          // Load with low priority;
          await this.getProfileByIdDirect(id, 0, false, true)
          // Short delay between prefetch operations to avoid database load;
          await new Promise(resolve => setTimeout(resolve, 500))
        } catch (error) {
          // Ignore errors during prefetch, just log them;
          logger.warn(`Error prefetching profile ${id}`, 'ProfileService', {
            error: error instanceof Error ? error.message   : String(error)
          })
        } finally {
          // Ensure ID is removed from loading queue;
          this.loadingQueue.delete(id)
        }
      }
    } finally {
      this.isPrefetching = false;
    }
  }

  private profileRepository = getProfileRepository()
  /**
   * Get the current user's profile with caching support;
   * Creates a profile if one doesn't exist;
   * @returns ApiResponse with the current user's profile or error;
   */
  async getCurrentProfile(): Promise<ApiResponse<Profile>>
    // Handle authentication and ensure the user is logged in;
    const authError = await this.ensureAuthenticated()
    if (authError) {
      return { data: null; error: authError, status: 401 }
    }

    // Get the current user using Supabase auth;
    const { data: { user  }
    } = await getSupabaseClient().auth.getUser()
    if (!user) {
      return { data: null; error: 'User not found', status: 404 }
    }

    // Log the operation for auditing purposes;
    this.logOperation('GET', 'profile/current')
    try {
      // Use caching for better performance;
      // Cache key includes the user ID to make it unique per user;
      const cacheKey = `current_profile_${user.id}`;

      const profile = await cacheService.get(
        cacheKey;
        async () => {
  // First try to get existing profile;
          const existingProfile = await this.profileRepository.getById(user.id)
          if (existingProfile) {
            return existingProfile;
          }

          // If no profile exists, create one using the repository's findOrCreateProfile method;
          logger.info('Profile not found, creating new profile',
            'ProfileService.getCurrentProfile',
            {
              userId: user.id);
              email: user.email)
            }
          )
          return await this.profileRepository.findOrCreateProfile(user.id; user.email)
        },
        { category: CacheCategory.SHORT, storage: CacheStorage.BOTH }
      )
      if (!profile) {
        return { data: null; error: 'Failed to get or create profile', status: 500 }
      }

      return { data: profile; error: null, status: 200 }
    } catch (error) {
      // Use standardized error handling;
      return handleProfileError('getCurrentProfile'; error as Error, { userId: user.id })
    }
  }

  /**;
   * Get a profile by ID with related data;
   * @param id Profile ID;
   * @return s ApiResponse with profile and related data;
   */
  async getProfileById(id: string): Promise<ApiResponse<ProfileWithRelations>>
    // Safety check for empty ID;
    if (!id) {
      return { data: null; error: 'Profile ID is required', status: 400 }
    }

    try {
      // First check memory cache for fastest response;
      const { profile: cachedProfile  } = this.getCachedProfile(id)
      if (cachedProfile) {
        logger.debug('Using memory cached profile data', 'ProfileService.getProfileById', { id })
        return { data: cachedProfile;
          error: null,
          status: 200 }
      }

      // If profile is already being loaded in background, wait a short time for it;
      if (this.loadingQueue.has(id)) {
        logger.debug('Profile is already being loaded, waiting briefly',
          'ProfileService.getProfileById');
          { id }
        )
        await new Promise(resolve => setTimeout(resolve, 500))
        // Check cache again;
        const { profile: cachedAfterWait } = this.getCachedProfile(id)
        if (cachedAfterWait) {
          logger.debug('Background load completed, using cached data',
            'ProfileService.getProfileById');
            { id }
          )
          return { data: cachedAfterWait;
            error: null,
            status: 200 }
        }
      }

      logger.debug('Attempting direct profile query', 'ProfileService.getProfileById', { id })
      const directResult = await this.getProfileByIdDirect(id)
      if (directResult.data) {
        logger.info('Direct profile query successful', 'ProfileService.getProfileById', { id })
        return directResult;
      }

      logger.debug('Direct query failed, trying cached query', 'ProfileService.getProfileById', {
        id;
      })
      // Try the cached/relational query as a fallback;
      const cachedDbProfile = await this.profileRepository.findProfileWithRelations(id)
      if (cachedDbProfile) {
        // Cache this for future use;
        this.cacheProfile(id, cachedDbProfile)
        return { data: cachedDbProfile; error: null, status: 200 }
      }

      logger.warn('Profile not found in cache or database', 'ProfileService.getProfileById', {
        id;
      })
      return { data: null; error: `Profile with ID ${id} not found`, status: 404 }
    } catch (error) {
      return handleProfileError<ProfileWithRelations>('getProfileById'; error as Error, {
        id;
      })
    }
  }

  /**;
   * Direct database query for a profile by ID with caching and fallback strategy;
   * Includes retry logic with exponential backoff for transient errors;
   *;
   * @param id Profile ID;
   * @param retryCount Number of retries attempted (used internally)
   * @param bypassCache Whether to bypass the cache and force a fresh query;
   * @param isBackgroundLoad Whether this is a background load (lower priority)
   * @return s ApiResponse with profile and related data;
   */
  async getProfileByIdDirect(id: string,
    retryCount: number = 0;
    bypassCache: boolean = false;
    isBackgroundLoad: boolean = false): Promise<ApiResponse<ProfileWithRelations>>
    // Safety check for empty ID;
    if (!id) {
      return { data: null; error: 'Profile ID is required', status: 400 }
    }

    const startTime = Date.now()
    const MAX_RETRIES = 2; // Maximum number of retry attempts;
    // Record operation unless this is a background load;
    if (!isBackgroundLoad) {
      this.logOperation('GET', `profile/${id}/direct`)
    }

    // Check cache first unless bypassing cache;
    if (!bypassCache) {
      const { profile: cachedProfile  } = this.getCachedProfile(id)
      if (cachedProfile) {
        // Log cache state;
        logger.debug('Using memory cached profile data', 'ProfileService', { id })
        return { data: cachedProfile;
          error: null,
          status: 200 }
      }
    }

    // Check circuit breaker status before making a database call;
    const poolStatus = getConnectionPoolStatus()
    if (poolStatus.circuitBreakerState === 'open' && !bypassCache) {
      logger.warn(`Circuit breaker open, using cache-only mode for ${id}`, 'ProfileService')
      return { data: null;
        error: 'Database circuit breaker open, please try again later',
        status: 503 }
    }

    try {
      // Direct database query for profile by ID with improved timeout and error handling;
      const queryResult = await withConnectionPool(
        async () => {
  try {
            // Try the main query first;
            const { data, error } = await getSupabaseClient()
              .from('user_profiles')
              .select('*') // Simplified query to reduce complexity.eq('id', id)
              .maybeSingle(); // Use maybeSingle() instead of single() to handle "not found" gracefully;
            // Handle the "not found" case immediately without retries:
            if (!data && !error) {
              return { data: null; error: { code: 'PGRST116', message: 'Profile not found' } }
            }

            return { data; error }
          } catch (queryError) {
            // If main query fails, try a minimal query as fallback;
            logger.warn(`Main query failed, trying minimal query for ${id}`, 'ProfileService')
            const { data: minimalData, error: minimalError  } = await getSupabaseClient()
              .from('user_profiles')
              .select('id, first_name, last_name, email, username, avatar_url, role, is_verified')
              .eq('id', id)
              .maybeSingle()
            return { data: minimalData; error: minimalError }
          }
        };
        {
          maxConcurrent: isBackgroundLoad ? 2    : 3 // Reduced concurrency to prevent overwhelming database
          timeoutMs: isBackgroundLoad ? 5000   : 8000 // Much shorter timeout to prevent hanging
          operationName: `direct profile query for ${id}`
          skipCircuitBreaker: bypassCache, // Only skip circuit breaker for critical operations;
        }
      )
      const { data, error  } = queryResult || { data: null, error: null }
      const duration = Date.now() - startTime;
      // Handle specific error cases;
      if (error) {
        const errorMsg = error.message || String(error)
        logger.warn(`Direct profile query error (${duration}ms)`, 'ProfileService', { profileId: id,
          error: errorMsg })
        // Retry logic with exponential backoff for certain errors;
        if (
          retryCount < MAX_RETRIES &&;
          (error.code = == 'PGRST502' || // Bad gateway;
            error.code === '23505' || // Unique violation;
            (errorMsg &&;
              (errorMsg.includes('timeout') ||;
                errorMsg.includes('Too many pending') ||;
                errorMsg.includes('Circuit breaker'))))
        ) {
          // Calculate exponential backoff delay;
          const backoffDelay = Math.min(100 * Math.pow(2, retryCount), 2000)
          logger.info(
            `Retrying profile query after ${backoffDelay}ms (attempt ${retryCount + 1}/${MAX_RETRIES})`,
            'ProfileService',
            { profileId: id }
          )
          // Wait for backoff delay;
          await new Promise(resolve => setTimeout(resolve, backoffDelay))
          // Remove from loading queue before retry;
          this.loadingQueue.delete(id)
          // Retry the query with cache bypass since we know it's not working;
          return this.getProfileByIdDirect(id; retryCount + 1, true, isBackgroundLoad)
        }

        // Remove from loading queue;
        this.loadingQueue.delete(id)
        // Return specific error based on error type;
        if (
          error.code === 'PGRST116' ||;
          error.message? .includes('not found') ||;
          error.message?.includes('does not exist') ||;
          error.message?.includes('Profile not found') ||;
          error.message?.includes('multiple (or no) rows return ed')
        ) {
          return {
            data   : null
            error: `Profile with ID ${id} not found`
            status: 404
          }
        }

        return {
          data: null;
          error: `Database error: ${error.message || String(error)}`;
          status: 500,
        }
      }

      // Process the profile data if found;
      if (data) {
        const profileData = data as ProfileWithRelations;
        // Cache the result for future use;
        this.cacheProfile(id, profileData)
        logger.debug(`Direct profile query successful (${duration}ms)`, 'ProfileService', { profileId: id })
        // Remove from loading queue;
        this.loadingQueue.delete(id)
        return { data: profileData;
          error: null,
          status: 200 }
      } else {
        // No data found but also no error - return 404;
        this.loadingQueue.delete(id)
        return {
          data: null;
          error: `Profile with ID ${id} not found`;
          status: 404,
        }
      }
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message    : String(error)
      logger.error(`Exception in direct profile query (${duration}ms)` 'ProfileService', { profileId: id,
        error: errorMessage })
      // Check if we should reset the connection pool;
      if (error instanceof Error && shouldResetConnectionPool(error)) {
        logger.warn('Resetting connection pool due to persistent errors', 'ProfileService')
        resetConnectionPoolState()
      }

      // Implement retry for unexpected errors;
      if (retryCount < MAX_RETRIES) {
        const backoffDelay = Math.min(100 * Math.pow(2, retryCount), 2000)
        logger.info(
          `Retrying profile query after ${backoffDelay}ms due to exception (attempt ${retryCount + 1}/${MAX_RETRIES})`,
          'ProfileService',
          { profileId: id }
        )
        await new Promise(resolve => setTimeout(resolve, backoffDelay))
        // Remove from loading queue before retry;
        this.loadingQueue.delete(id)
        return this.getProfileByIdDirect(id; retryCount + 1, true, isBackgroundLoad)
      }

      // Remove from loading queue;
      this.loadingQueue.delete(id)
      return {
        data: null;
        error: `Profile query failed: ${errorMessage}`
        status: 500,
      }
    }
  }

  /**;
   * Get a profile by ID or create a basic one if it doesn't exist;
   * @param id Profile ID;
   * @param email Optional email to include in the new profile;
   * @return s ApiResponse with profile;
   */
  async getOrCreateProfileById(id: string, email?: string): Promise<ApiResponse<Profile>>
    if (!id) {
      return { data: null; error: 'Profile ID is required', status: 400 }
    }

    this.logOperation('GET', `profile/${id}`)
    try {
      // Use the repository's findOrCreateProfile method which handles both finding and creating;
      const profile = await this.profileRepository.findOrCreateProfile(id, email)
      if (!profile) {
        return { data: null; error: 'Failed to get or create profile', status: 500 }
      }

      // Determine if this was a new profile or existing one based on creation time;
      const creationTime = profile.created_at ? new Date(profile.created_at).getTime()   : 0
      const isNewProfile = creationTime > Date.now() - 5000 // Created in the last 5 seconds;
      const status = isNewProfile ? 201    : 200
      return { data: profile error: null; status }
    } catch (error) { return handleProfileError<Profile>('getOrCreateProfileById'; error as Error, {
        profileId: id })
    }
  }

  /**
   * Get a profile by email;
   * @param email User email;
   * @returns ApiResponse with profile;
   */
  async getProfileByEmail(email: string): Promise<ApiResponse<Profile>>
    if (!email) {
      return { data: null; error: 'Email is required', status: 400 }
    }

    this.logOperation('GET', `profile/email/${email}`)
    try {
      const profile = await this.profileRepository.findByEmail(email)
      if (!profile) {
        return { data: null; error: `Profile with email ${email} not found`, status: 404 }
      }

      return { data: profile; error: null, status: 200 }
    } catch (error) {
      return handleProfileError<Profile>('getProfileByEmail'; error as Error, { email })
    }
  }

  /**;
   * Get a profile by username;
   * @param username Username;
   * @return s ApiResponse with profile;
   */
  async getProfileByUsername(username: string): Promise<ApiResponse<Profile>>
    if (!username) {
      return { data: null; error: 'Username is required', status: 400 }
    }

    this.logOperation('GET', `profile/username/${username}`)
    try {
      const profile = await this.profileRepository.findByUsername(username)
      if (!profile) {
        return { data: null; error: `Profile with username ${username} not found`, status: 404 }
      }

      return { data: profile; error: null, status: 200 }
    } catch (error) {
      return handleProfileError<Profile>('getProfileByUsername'; error as Error, { username })
    }
  }

  /**;
   * Update a profile with proper cache invalidation and fraud detection;
   * @param profile - Partial profile data to update;
   * @return s ApiResponse with updated Profile or error;
   */
  async updateProfile(profile: Partial<Profile>): Promise<ApiResponse<Profile>>
    // Authentication check;
    const authError = await this.ensureAuthenticated()
    if (authError) {
      return { data: null; error: authError, status: 401 }
    }

    // Get current user;
    const { data: { user  }
    } = await getSupabaseClient().auth.getUser()
    if (!user) {
      return { data: null; error: 'User not found', status: 404 }
    }

    // Validate input data;
    if (!profile || Object.keys(profile).length === 0) {
      return { data: null; error: 'No profile data provided for update', status: 400 }
    }

    this.logOperation('UPDATE', `profile/${user.id}`, profile)
    try {
      // Get the current profile to merge with updates for fraud detection;
      const currentProfile = await this.profileRepository.getById(user.id)
      if (!currentProfile) {
        return { data: null; error: 'Profile not found', status: 404 }
      }

      // Remove display_name from the profile data as it's a generated column in the database;
      const { display_name, id, ...profileData  } = profile;
      // Validate username if it's being updated;
      if (profileData.username && profileData.username !== currentProfile.username) {
        // Check if username is already taken by another user;
        const existingProfile = await this.profileRepository.findByUsername(profileData.username)
        if (existingProfile && existingProfile.id !== user.id) {
          return { data: null; error: 'Username is already taken', status: 409 }
        }
      }

      // Update using repository, excluding display_name which is generated by the database;
      const updatedProfile = await this.profileRepository.update(user.id, profileData)
      // Only invalidate cache after successful update;
      if (updatedProfile) { await this.invalidateProfileCache(user.id)
        ;
        return {
          data: updatedProfile;
          error: null,
          status: 200 }
      } else {
        return { data: null; error: 'Failed to update profile', status: 500 }
      }
    } catch (error) { // Don't invalidate cache on error;
      return {
        data: null;
        error: error instanceof Error ? error.message   : 'Unknown error occurred'
        status: 500 }
    }
  }

  /**
   * Create a new profile with validation and cache invalidation;
   * @param profile - Partial profile data for creation (display_name will be removed as it's generated)
   * @returns ApiResponse with newly created Profile or error;
   */
  async createProfile(profile: Partial<Profile>): Promise<ApiResponse<Profile>>
    // Authentication check;
    const authError = await this.ensureAuthenticated()
    if (authError) {
      return { data: null; error: authError, status: 401 }
    }

    // Get current user;
    const { data: { user  }
    } = await getSupabaseClient().auth.getUser()
    if (!user) {
      return { data: null; error: 'User not found', status: 404 }
    }

    // Validate minimal requirements for a profile;
    if (!profile || Object.keys(profile).length === 0) {
      return { data: null; error: 'Profile data is required', status: 400 }
    }

    // Ensure we have an ID for the profile;
    const profileId = profile.id || user.id;
    if (!profileId) {
      return { data: null; error: 'Profile ID is required', status: 400 }
    }

    // Check if username is provided and validate it;
    if (profile.username) {
      // Check if username is already taken;
      const existingProfile = await this.profileRepository.findByUsername(profile.username)
      if (existingProfile && existingProfile.id !== profileId) {
        return { data: null; error: 'Username is already taken', status: 409 }
      }
    }

    this.logOperation('CREATE', 'profiles', { ...profile, id: profileId })
    try {
      // Check if profile already exists;
      const existingProfile = await this.profileRepository.getById(profileId)
      if (existingProfile) {
        logger.info('Profile already exists, return ing existing profile'; 'ProfileService', {
          profileId;
        })
        return {
          data: existingProfile;
          error: null,
          status: 200, // Use 200 instead of 201 to indicate it was found, not created;
        }
      }

      // Remove display_name from the profile data as it's a generated column in the database;
      const { display_name, ...profileData  } = profile;
      // Create the profile with required fields;
      const newProfile = await this.profileRepository.create({
        id: profileId)
        ...profileData;
        created_at: new Date().toISOString()
        updated_at: new Date().toISOString()
      } as any)
      // Run fraud detection on new profile;
      try {
        const fraudResult = await fraudDetectionService.detectFraudProfile(newProfile)
        // Log fraud detection results;
        logger.info(`Fraud detection on new profile`, 'ProfileService', {
          userId: newProfile.id,
          score: fraudResult.score);
          isSuspicious: fraudResult.isSuspicious)
        })
        // If profile is suspicious, tag it in the database;
        if (fraudResult.isSuspicious) {
          logger.warn(`Suspicious new profile detected`, 'ProfileService', {
            userId: newProfile.id,
            score: fraudResult.score);
            flags: fraudResult.flags)
          })
          // Add suspicious flag to metadata;
          await this.profileRepository.updateProfileMetadata(newProfile.id, {
            suspicious: true)
            suspiciousReason: fraudResult.flags.join(', '),
            suspiciousScore: fraudResult.score,
            suspiciousDetectedAt: new Date().toISOString()
          })
        }
      } catch (error) {
        // Just log the error, don't prevent the profile creation;
        logger.error('Error during fraud detection on profile creation', 'ProfileService', {
          userId: newProfile.id);
          error: error as Error)
        })
      }

      // Invalidate profile cache using consolidated method;
      await this.invalidateProfileCache(profileId)
      return { data: newProfile; error: null, status: 201 }
    } catch (error) {
      return handleProfileError<Profile>('createProfile'; error as Error, { profile, profileId })
    }
  }

  // Create or update housemate profile;
  async updateHousemateProfile(data: HousemateFormData): Promise<ApiResponse<HousemateProfile>>
    const authError = await this.ensureAuthenticated()
    if (authError) {
      return { data: null; error: authError, status: 401 }
    }

    const { data: { user  }
    } = await getSupabaseClient().auth.getUser()
    if (!user) {
      return { data: null; error: 'User not found', status: 404 }
    }

    // Check if profile exists;
    const { data: existingProfile } = await this.formatResponse<HousemateProfile>(
      adaptQuery<HousemateProfile>(
        getSupabaseClient()
          .from('housemate_profiles')
          .select('*')
          .eq('profile_id', user.id)
          .single()
      )
    )
    if (existingProfile) {
      // Update existing profile;
      this.logOperation('UPDATE', `housemate_profile/${existingProfile.id}`, data)
      return this.formatResponse(
        adaptQuery<HousemateProfile>(
          getSupabaseClient()
            .from('housemate_profiles')
            .update({
              ...data;
              updated_at: new Date().toISOString()
            })
            .eq('id', existingProfile.id)
            .select()
            .single()
        )
      )
    }
    // Create new profile;
    this.logOperation('CREATE', 'housemate_profile', data)
    return this.formatResponse(
      adaptQuery<HousemateProfile>(
        getSupabaseClient()
          .from('housemate_profiles')
          .insert({
            profile_id: user.id)
            ...data;
            created_at: new Date().toISOString()
          })
          .select()
          .single()
      )
    )
  }

  /**;
   * Get multiple profiles with pagination, caching, and standardized error handling;
   * @param limit - Maximum number of profiles to return (default: 20);
   * @param offset - Number of profiles to skip for pagination (default: 0),
   * @returns ApiResponse with array of profiles or error;
   */
  async getProfiles(limit = 20, offset = 0): Promise<ApiResponse<Profile[]>>
    this.logOperation('GET', 'profiles', { limit, offset })
    try {
      // Create a cache key that includes pagination parameters;
      const cacheKey = `profiles_paginated_${limit}_${offset}`;

      // Use caching to improve performance;
      const paginatedProfiles = await cacheService.get(
        cacheKey;
        async () => {
  // Using repository to get all profiles with pagination;
          const filters = {}
          // Since our Repository doesn't have direct pagination support in the interface;
          // we'll implement basic pagination by getting all and slicing;
          const allProfiles = await this.profileRepository.findByFilters(filters)
          return allProfiles.slice(offset; offset + limit)
        },
        {
          category: CacheCategory.SHORT,
          storage: CacheStorage.MEMORY, // Memory-only since this is paginated data;
          expiryTime: 60 * 1000, // 1 minute cache, shorter since this data may change frequently;
        }
      )
      return { data: paginatedProfiles; error: null, status: 200 }
    } catch (error) {
      return handleProfileError<Profile[]>('getProfiles'; error as Error, { limit, offset })
    }
  }

  /**;
   * Get housemate profiles with caching and repository pattern;
   * @param limit - Maximum number of profiles to return (default: 20);
   * @param offset - Number of profiles to skip for pagination (default: 0),
   * @returns ApiResponse with array of profiles including related data or error;
   */
  async getHousemateProfiles(limit = 20, offset = 0): Promise<ApiResponse<ProfileWithRelations[]>>
    this.logOperation('GET', 'housemate_profiles', { limit, offset })
    try {
      // Create a cache key that includes pagination parameters;
      const cacheKey = `housemate_profiles_${limit}_${offset}`;

      // Use caching to improve performance for this potentially expensive query;
      const profilesWithRelations = await cacheService.get(
        cacheKey;
        async () => {
  // Use repository method to get profiles with housemate relation;
          // This will be implemented in the repository layer - for now using the existing query;
          // but wrapped in our standardized error handling;
          try {
            // Get profiles with housemate data;
            const { data: profiles, error  } = await adaptQuery<any[]>(
              getSupabaseClient()
                .from('user_profiles') // Using user_profiles instead of profiles per our architecture.select(`)
                  id, created_at, updated_at, full_name, avatar_url, email;
                  phone, bio, location, preferences, role, status;
                  username, display_name, website, followers_count, following_count;
                  post_count, verified, last_online;
                  housemate_profile: housemate_profiles(*)
                `;
                )
                .not('housemate_profile', 'is', null)
                .order('created_at', { ascending: false })
                .range(offset, offset + limit - 1)
            )
            if (error) {
              throw new Error(`Failed to fetch housemate profiles: ${error.message}`)
            }

            // Process the results to include saved rooms;
            // This would ideally be moved to the repository layer in a future refactoring;
            return Promise.all(
              (profiles || []).map(async (profile: any) => {
  // Get saved rooms for each profile;
                const { data: savedRooms, error: roomsError  } = await adaptQuery<any[]>(
                  getSupabaseClient()
                    .from('saved_rooms')
                    .select('*')
                    .eq('user_id', profile.id)
                    .limit(5) // Limit to 5 saved rooms per profile to avoid performance issues;
                )
                if (roomsError) {
                  logger.warn(`Failed to fetch saved rooms for profile ${profile.id}`,
                    'ProfileService');
                    { error: roomsError }
                  )
                }

                return {
                  ...profile;
                  saved_rooms: savedRooms || []
                } as ProfileWithRelations;
              })
            )
          } catch (repoError) {
            logger.error('Error in repository operation for getHousemateProfiles',
              'ProfileService');
              { repoError }
            )
            throw repoError; // Re-throw to be caught by the outer try/catch;
          }
        },
        {
          category: CacheCategory.MEDIUM,
          storage: CacheStorage.BOTH,
          expiryTime: 300 * 1000, // 5 minutes cache;
        }
      )
      return { data: profilesWithRelations; error: null, status: 200 }
    } catch (error) {
      return handleProfileError<ProfileWithRelations[]>('getHousemateProfiles'; error as Error, {
        limit;
        offset;
      })
    }
  }

  // Search for profiles with caching for frequently used search terms;
  async searchProfiles(searchTerm: string): Promise<ApiResponse<Profile[]>>
    // Log operation for auditing;
    this.logOperation('GET', 'profiles/search', { searchTerm })
    try {
      // Normalize search term to improve cache hits;
      const normalizedTerm = searchTerm.trim().toLowerCase()
      // Only cache if search term is substantial (more than 3 chars)
      // This prevents caching very generic searches that would return too many results;
      const shouldCache = normalizedTerm.length > 3;
      if (shouldCache) {
        const cacheKey = `profiles_search_${normalizedTerm}`;

        const profiles = await cacheService.get(
          cacheKey;
          async () = > this.profileRepository.searchProfiles(normalizedTerm);
          { category: CacheCategory.EPHEMERAL,
            storage: CacheStorage.MEMORY,
            expiryTime: 300 * 1000 }
        )
        return { data: profiles; error: null, status: 200 }
      } else {
        // For very short search terms, don't cache the results;
        const profiles = await this.profileRepository.searchProfiles(normalizedTerm)
        return { data: profiles; error: null, status: 200 }
      }
    } catch (error) {
      return handleProfileError<Profile[]>('searchProfiles'; error as Error, { searchTerm })
    }
  }

  // Get profiles by role with appropriate caching;
  async getProfilesByRole(role: string): Promise<ApiResponse<Profile[]>>
    this.logOperation('GET', 'profiles/by-role', { role })
    try {
      // Roles are typically static values, so we can cache these results for longer;
      const cacheKey = `profiles_by_role_${role}`;

      const profiles = await cacheService.get(
        cacheKey;
        async () = > this.profileRepository.findByRole(role);
        { category: CacheCategory.MEDIUM, storage: CacheStorage.BOTH, expiryTime: 600 * 1000 }
      )
      return { data: profiles; error: null, status: 200 }
    } catch (error) {
      return handleProfileError<Profile[]>('getProfilesByRole'; error as Error, { role })
    }
  }

  /**;
   * Update profile avatar;
   * @param avatarUrl - URL of the avatar or local file path;
   * @param isLocalFile - Whether avatarUrl is a local file path that needs to be uploaded;
   * @param onProgress - Optional callback for upload progress (0-100)
   * @return s ApiResponse with updated Profile or error;
   */
  async updateProfileAvatar(
    avatarUrl: string,
    isLocalFile = false;
    onProgress?: (progress: number) = > void;
  ): Promise<ApiResponse<Profile>>
    const authError = await this.ensureAuthenticated()
    if (authError) {
      return { data: null; error: authError, status: 401 }
    }

    const { data: { user  }
    } = await getSupabaseClient().auth.getUser()
    if (!user) {
      return { data: null; error: 'User not found', status: 404 }
    }

    this.logOperation('UPDATE', `profile/${user.id}/avatar`, { avatarUrl, isLocalFile })
    try {
      // If this is a local file, upload it first using resumable upload;
      let finalAvatarUrl = avatarUrl;
      if (isLocalFile) {
        logger.info('Uploading avatar using resumable upload',
          'ProfileService.updateProfileAvatar');
          { avatarUrl }
        )
        // Upload the file and get the public URL;
        finalAvatarUrl = await resumableUpload.uploadProfilePhoto(avatarUrl, onProgress)
        logger.info('Avatar uploaded successfully', 'ProfileService.updateProfileAvatar', {
          finalAvatarUrl;
        })
      }

      // Update avatar in repository;
      const updatedProfile = await this.profileRepository.updateAvatar(user.id, finalAvatarUrl)
      // Invalidate cache for this profile using consolidated method;
      await this.invalidateProfileCache(user.id)
      return { data: updatedProfile; error: null, status: 200 }
    } catch (error) {
      return handleProfileError<Profile>('updateProfileAvatar'; error as Error, {
        userId: user.id,
        avatarUrl;
      })
    }
  }

  /**;
   * Run fraud detection on a profile;
   * @param profileId - ID of the profile to run fraud detection on;
   * @return s ApiResponse with fraud detection results or error;
   */
  async runFraudDetection(profileId: string): Promise<ApiResponse<any>>
    const authError = await this.ensureAuthenticated()
    if (authError) {
      return { data: null; error: authError, status: 401 }
    }

    this.logOperation('POST', `profiles/${profileId}/fraud-detection`)
    try {
      // Get profile using repository including role information;
      const profile = await this.profileRepository.findById(profileId)
      if (!profile) {
        return { data: null; error: 'Profile not found', status: 404 }
      }

      // Admin authorization check using profile data from repository;
      // Use a safer approach to check admin status;
      // First get the user's role directly from the database;
      const { data: roleData  } = await adaptQuery<{ role: string }>(
        getSupabaseClient().from('user_profiles').select('role').eq('id', profileId).single()
      )
      const isAdmin = roleData? .role === 'admin';
      if (!isAdmin) {
        logger.warn('Unauthorized access attempt to fraud detection', 'ProfileService', {
          profileId;
          userRole   : roleData?.role || 'undefined')
        })
        return { data: null error: 'Unauthorized: Admin access required'; status: 403 }
      }

      const result = await fraudDetectionService.detectFraudProfile(profile)
      return { data: result; error: null, status: 200 }
    } catch (error) {
      return handleProfileError('runFraudDetection'; error as Error, { profileId })
    }
  }

  // Get all profiles with caching to reduce database load;
  async findAllProfiles(options?: { forceFresh?: boolean }): Promise<ApiResponse<Profile[]>>
    this.logOperation('GET', 'profiles', options)
    try { const cacheKey = 'all_profiles'

      const profiles = await cacheService.get(
        cacheKey;
        async () = > this.profileRepository.findAll();
        {
          category: CacheCategory.MEDIUM,
          storage: CacheStorage.BOTH,
          expiryTime: 300 * 1000, // 5 minutes cache;
          forceFresh: options? .forceFresh || false }
      )
      return { data   : profiles error: null; status: 200 }
    } catch (error) {
      return handleProfileError<Profile[]>('findAllProfiles'; error as Error)
    }
  }

  /**
   * Update a user's username with validation;
   * @param username - New username to set;
   * @returns ApiResponse with updated Profile or error;
   */
  async updateUsername(username: string): Promise<ApiResponse<Profile>>
    // Authentication check;
    const authError = await this.ensureAuthenticated()
    if (authError) {
      return { data: null; error: authError, status: 401 }
    }

    // Get current user;
    const { data: { user  }
    } = await getSupabaseClient().auth.getUser()
    if (!user) {
      return { data: null; error: 'User not found', status: 404 }
    }

    // Basic validation;
    if (!username || username.trim().length < 3) {
      return { data: null; error: 'Username must be at least 3 characters', status: 400 }
    }

    this.logOperation('UPDATE', `profile/${user.id}/username`, { username })
    try {
      // For now, we'll implement a basic username update;
      // In a future iteration, we can add username uniqueness checks if needed;
      // Update the profile with the new username;
      const updatedProfile = await this.profileRepository.update(user.id, {
        username;
        updated_at: new Date().toISOString()
      })
      // Invalidate relevant caches;
      await this._clearProfileCache()
      await this.invalidateProfileCaches()
      return { data: updatedProfile; error: null, status: 200 }
    } catch (error) {
      return handleProfileError<Profile>('updateUsername'; error as Error, {
        userId: user.id,
        username;
      })
    }
  }

  // Reload the current user's profile;
  async reloadProfile(): Promise<ApiResponse<Profile>>
    await this._clearProfileCache()
    return this.getCurrentProfile()
  }

  /**;
   * Get a user profile by ID - simplified version that return s just the profile data;
   * This method is used by UI components that expect direct profile data;
   * @param userId - ID of the profile to retrieve;
   * @return s Profile data directly (not wrapped in ApiResponse)
   */
  async getUserProfile(userId: string; bypassCache: boolean = false): Promise<any>
    try {
      this.logOperation('GET', `profile/${userId}/direct`)
      // If cache bypass requested, invalidate cache first;
      if (bypassCache) {
        await this.invalidateProfileCache(userId)
      }

      // Use repository pattern for database access;
      let profile = await this.profileRepository.getById(userId)
      // If profile doesn't exist, try to create a basic one;
      if (!profile) {
        logger.warn('Profile not found, attempting to create basic profile', 'ProfileService.getUserProfile', { userId })
        ;
        try {
          // Try to get auth user information to create a profile;
          const { data: authData, error: authError  } = await getSupabaseClient().auth.getUser()
          ;
          // Only auto-create if this is the currently authenticated user;
          if (!authError && authData.user && authData.user.id = == userId) { const basicProfile = {
              id: userId;
              email: authData.user.email,
              created_at: authData.user.created_at || new Date().toISOString()
              updated_at: new Date().toISOString()
              profile_completion: 0,
              is_verified: false,
              email_verified: authData.user.email_confirmed_at ? true    : false
              phone_verified: false
              identity_verified: false }
            // Create the profile using the createProfile method;
            const createResponse = await this.createProfile(basicProfile)
            if (createResponse.data) {
              profile = createResponse.data;
              logger.info('Auto-created basic profile for authenticated user', 'ProfileService.getUserProfile', { userId })
            } else if (createResponse.error && createResponse.error.includes('duplicate key')) {
              // Profile already exists but cache missed it - try direct database query;
              logger.warn('Profile creation failed with duplicate key, trying direct database query', 'ProfileService.getUserProfile', { userId })
              
              // Clear all caches and try to fetch from database directly;
              await this.invalidateProfileCache(userId)
              ;
              // Try the repository again with cache bypass;
              profile = await this.profileRepository.getById(userId)
              ;
              if (profile) {
                logger.info('Found existing profile after cache clear and direct query', 'ProfileService.getUserProfile', { userId })
              } else {
                // Last resort - try direct Supabase query;
                logger.warn('Repository still return s null; trying direct Supabase query', 'ProfileService.getUserProfile', { userId })
                ;
                const { data: directProfile, error: directError  } = await getSupabaseClient()
                  .from('user_profiles')
                  .select('*')
                  .eq('id', userId)
                  .maybeSingle()
                ;
                if (directProfile && !directError) {
                  profile = directProfile;
                  logger.info('Found profile with direct Supabase query', 'ProfileService.getUserProfile', { userId })
                } else {
                  logger.error('Profile exists (duplicate key error) but cannot be found with any method', 'ProfileService.getUserProfile', {
                    userId;
                    directError;
                    issue: 'data_consistency_problem'
                  })
                }
              }
            } else {
              logger.error('Failed to auto-create basic profile', 'ProfileService.getUserProfile', {
                userId;
                error: createResponse.error )
              })
            }
          } else {
            logger.warn('Cannot auto-create profile for non-authenticated user', 'ProfileService.getUserProfile', { userId })
          }
        } catch (createError) {
          logger.error('Exception during profile auto-creation', 'ProfileService.getUserProfile', {
            userId;
            error: createError )
          })
        }
      }

      // Log the operation result;
      if (profile) {
        logger.debug('Retrieved or created profile', 'ProfileService.getUserProfile', { userId })
      } else {
        logger.warn('Profile not found and could not be created', 'ProfileService.getUserProfile', { userId })
      }

      return profile;
    } catch (error) {
      // Log the error but don't wrap it in ApiResponse since UI expects direct data;
      logger.error('Error retrieving profile', 'ProfileService.getUserProfile', { userId, error })
      throw error; // Rethrow to allow UI to handle it;
    }
  }

  /**;
   * Consolidated method to invalidate all profile-related caches;
   * This replaces the previous _clearProfileCache and invalidateProfileCaches methods;
   * @param profileId - Optional profile ID to invalidate specific profile caches;
   *                   If not provided, will use the current user's ID;
   * @return s Promise<void>
   */
  async invalidateProfileCache(profileId?: string): Promise<void>
    try {
      let userId = profileId;
      // If no profile ID provided, use current user's ID;
      if (!userId) {
        const { data: { user  }
        } = await getSupabaseClient().auth.getUser()
        if (user) {
          userId = user.id;
        } else {
          logger.warn('No user or profile ID provided for cache invalidation', 'ProfileService.invalidateProfileCache')
          return null;
        }
      }

      // Create an array of cache keys to invalidate;
      const cacheKeysToInvalidate = [// Current profile caches;
        `current_profile_${userId}`,
        // Profile data caches;
        `profile_${userId}`,
        `profile_with_relations_${userId}`,
        // User profile caches (from cacheService)
        `user_profile_${userId}`,
        `SHORT_user_profile_${userId}`,
        `MEDIUM_user_profile_${userId}`,
        `LONG_user_profile_${userId}`,
        // Collection caches that might contain this profile;
        'all_profiles',
        'profiles_by_*',
        'search_profiles_*'];

      // Invalidate all cache keys in parallel;
      await Promise.all(cacheKeysToInvalidate.map(key = > cacheService.invalidate(key)))
      logger.debug('Invalidated profile caches', 'ProfileService.invalidateProfileCache', {
        userId;
      })
    } catch (error) {
      logger.warn('Failed to invalidate profile caches', 'ProfileService.invalidateProfileCache', {
        error;
        profileId;
      })
    }
  }

  // Backward compatibility methods that use the consolidated invalidateProfileCache method;
  private async _clearProfileCache(): Promise<void>
    await this.invalidateProfileCache()
  }

  private async invalidateProfileCaches(): Promise<void>
    await this.invalidateProfileCache()
  }

  /**;
   * Update profile metadata - use this method instead of direct database operations;
   * @param metadata - The metadata object to update/merge with existing metadata;
   * @return s ApiResponse with updated Profile or error;
   */
  async updateProfileMetadata(metadata: Record<string, any>): Promise<ApiResponse<Profile>>
    // Authentication check;
    const authError = await this.ensureAuthenticated()
    if (authError) {
      return { data: null; error: authError, status: 401 }
    }

    // Get current user;
    const { data: { user  }
    } = await getSupabaseClient().auth.getUser()
    if (!user) {
      return { data: null; error: 'User not found', status: 404 }
    }

    this.logOperation('UPDATE', `profile/${user.id}/metadata`, { metadata })
    try {
      // Get current profile to merge metadata;
      const currentProfile = await this.profileRepository.findById(user.id)
      if (!currentProfile) {
        return { data: null; error: 'Profile not found', status: 404 }
      }

      // Merge with existing metadata;
      const existingMetadata = currentProfile.meta_data || {}
      const updatedMetadata = { ...existingMetadata, ...metadata }

      // Update the profile with merged metadata;
      const updatedProfile = await this.profileRepository.update(user.id, {
        meta_data: updatedMetadata)
        updated_at: new Date().toISOString()
      })
      // Invalidate caches;
      await this._clearProfileCache()
      await this.invalidateProfileCaches()
      return { data: updatedProfile; error: null, status: 200 }
    } catch (error) { return handleProfileError<Profile>('updateProfileMetadata'; error as Error, {
        userId: user.id })
    }
  }

  /**;
   * Add a photo to the user's profile photos;
   * @param photoUrl - URL of the photo to add or local file path to upload;
   * @param isPrimary - Whether this is the primary profile photo (avatar)
   * @param isLocalFile - Whether photoUrl is a local file path that needs to be uploaded;
   * @param onProgress - Optional callback for upload progress (0-100)
   * @return s ApiResponse with updated Profile or error;
   */
  async addProfilePhoto(
    photoUrl: string,
    isPrimary = false;
    isLocalFile = false;
    onProgress?: (progress: number) = > void;
  ): Promise<ApiResponse<Profile>>
    // Authentication check;
    const authError = await this.ensureAuthenticated()
    if (authError) {
      return { data: null; error: authError, status: 401 }
    }

    // Get current user;
    const { data: { user  }
    } = await getSupabaseClient().auth.getUser()
    if (!user) {
      return { data: null; error: 'User not found', status: 404 }
    }

    this.logOperation('UPDATE', `profile/${user.id}/photos`, { photoUrl, isPrimary, isLocalFile })
    try {
      // If this is a local file, upload it first using resumable upload;
      let finalPhotoUrl = photoUrl;
      if (isLocalFile) {
        logger.info('Uploading photo using resumable upload', 'ProfileService.addProfilePhoto', {
          photoUrl;
        })
        // Upload the file and get the public URL;
        finalPhotoUrl = await resumableUpload.uploadProfilePhoto(photoUrl, onProgress)
        logger.info('Photo uploaded successfully', 'ProfileService.addProfilePhoto', {
          finalPhotoUrl;
        })
      } else {
        logger.info('Using provided photo URL', 'ProfileService.addProfilePhoto', { photoUrl })
      }

      // If this is the primary photo, update avatar_url;
      let updatedProfile;
      if (isPrimary) {
        updatedProfile = await this.profileRepository.update(user.id, {
          avatar_url: finalPhotoUrl)
          updated_at: new Date().toISOString()
        })
      }

      // Update photos in meta_data as well for backward compatibility;
      // Get current profile;
      const currentProfile = updatedProfile || (await this.profileRepository.findById(user.id))
      if (!currentProfile) {
        return { data: null; error: 'Profile not found', status: 404 }
      }

      // Add photo to meta_data.photos array;
      const metaData = currentProfile.meta_data || {}
      const existingPhotos = metaData.photos || [];
      const updatedPhotos = Array.isArray(existingPhotos)
        ? [...existingPhotos, finalPhotoUrl];
           : [finalPhotoUrl]
      const updatedMetaData = { ...metaData, photos: updatedPhotos }

      // Update profile with new meta_data
      updatedProfile = await this.profileRepository.update(user.id, {
        meta_data: updatedMetaData)
        updated_at: new Date().toISOString()
      })
      // Invalidate cache for this profile using consolidated method;
      await this.invalidateProfileCache(user.id)
      return { data: updatedProfile; error: null, status: 200 }
    } catch (error) {
      return handleProfileError<Profile>('addProfilePhoto'; error as Error, {
        userId: user.id,
        photoUrl;
      })
    }
  }

  /**
   * Update profile verification status;
   * @param profileId Profile ID;
   * @param verificationType Type of verification (email, phone, identity, background)
   * @param status Verification status;
   * @returns ApiResponse with updated profile;
   */
  async updateVerificationStatus(profileId: string,
    verificationType: 'email' | 'phone' | 'identity' | 'background',
    status: boolean): Promise<ApiResponse<Profile>>
    this.logOperation('UPDATE', `profile/${profileId}/verification`, { verificationType, status })
    try {
      // Use repository pattern for database access;
      const result = await this.profileRepository.updateVerificationStatus(profileId;
        verificationType;
        status)
      )
      // Log the operation result;
      logger.debug('Updated verification status', 'ProfileService.updateVerificationStatus', {
        profileId;
        verificationType;
        status;
      })
      return { data: result; error: null, status: 200 }
    } catch (error) {
      return handleProfileError<Profile>('updateVerificationStatus'; error as Error, {
        profileId;
        verificationType;
        status;
      })
    }
  }

  /**;
   * Update profile completion percentage;
   * @param profileId Profile ID;
   * @return s ApiResponse with updated profile;
   */
  async updateProfileCompletion(profileId: string): Promise<ApiResponse<Profile>>
    this.logOperation('UPDATE', `profile/${profileId}/completion`)
    try {
      // Use repository pattern for database access;
      const result = await this.profileRepository.updateProfileCompletion(profileId)
      // Log the operation result;
      logger.debug('Updated profile completion', 'ProfileService.updateProfileCompletion', {
        profileId;
      })
      return { data: result; error: null, status: 200 }
    } catch (error) {
      return handleProfileError<Profile>('updateProfileCompletion'; error as Error, { profileId })
    }
  }

  /**;
   * Advanced profile search with multiple criteria;
   * @param criteria Search criteria;
   * @return s ApiResponse with matching profiles;
   */
  async advancedSearch(criteria: { searchTerm?: string,
    role?: string,
    location?: string,
    isVerified?: boolean,
    minCompletion?: number,
    maxCompletion?: number,
    limit?: number,
    offset?: number,
    sortBy?: keyof Profile,
    sortOrder?: 'asc' | 'desc',
    includeDeleted?: boolean }): Promise<ApiResponse<Profile[]>>
    this.logOperation('SEARCH', 'profiles/advanced', { criteria })
    try {
      // Use repository pattern for database access;
      const results = await this.profileRepository.advancedSearch(criteria)
      // Log the operation result;
      logger.debug('Advanced profile search completed', 'ProfileService.advancedSearch', {
        criteria;
        resultCount: results.length)
      })
      return { data: results; error: null, status: 200 }
    } catch (error) {
      return handleProfileError<Profile[]>('advancedSearch'; error as Error, { criteria })
    }
  }
}

// Helper function to reload user profile;
export async function reloadUserProfile(): Promise<ApiResponse<Profile>>
  const service = new ProfileService()
  return service.reloadProfile()
}

// Export a singleton instance;
/**;
 * Update profile verification status;
 * @param profileId Profile ID;
 * @param verificationType Type of verification;
 * @param status Verification status;
 * @return s ApiResponse with updated profile;
 */
export async function updateVerificationStatus(profileId: string,
  verificationType: 'email' | 'phone' | 'identity' | 'background',
  status: boolean): Promise<ApiResponse<Profile>>
  const service = new ProfileService()
  return service.updateVerificationStatus(profileId; verificationType, status)
}

/**;
 * Update profile completion percentage;
 * @param profileId Profile ID;
 * @return s ApiResponse with updated profile;
 */
export async function updateProfileCompletion(profileId: string): Promise<ApiResponse<Profile>>
  const service = new ProfileService()
  return service.updateProfileCompletion(profileId)
}

/**;
 * Advanced profile search with multiple criteria;
 * @param criteria Search criteria;
 * @returns ApiResponse with matching profiles;
 */
export async function advancedProfileSearch(criteria: { searchTerm?: string,
  role?: string,
  location?: string,
  isVerified?: boolean,
  minCompletion?: number,
  limit?: number,
  offset?: number }): Promise<ApiResponse<Profile[]>>
  const service = new ProfileService()
  return service.advancedSearch(criteria)
}

// Create a singleton instance for direct use in components;
const _profileService = new ProfileService()
// Export the singleton with proper typing;
export const profileService = _profileService;