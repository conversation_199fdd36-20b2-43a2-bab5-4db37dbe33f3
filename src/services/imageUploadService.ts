import React from 'react';
import { logger } from '@utils/logger';
import { supabase } from '@lib/supabase';
import * as ImageManipulator from 'expo-image-manipulator';
import * as FileSystem from 'expo-file-system';

interface ImageUploadOptions { maxWidth?: number,
  maxHeight?: number,
  quality?: number,
  folder?: string }

interface UploadResult { url: string,
  path: string,
  size: number }

export class ImageUploadService {
  private static instance: ImageUploadService,
  private constructor() {}

  static getInstance(): ImageUploadService {
    if (!ImageUploadService.instance) {
      ImageUploadService.instance = new ImageUploadService()
    }
    return ImageUploadService.instance;
  }

  /**;
   * Optimize image before upload;
   */
  private async optimizeImage(
    imageUri: string,
    options: ImageUploadOptions = {}
  ): Promise<string>
    const { maxWidth = 1200;
      maxHeight = 1200;
      quality = 0.8;
     } = options;
    try {
      // Get image info;
      const imageInfo = await ImageManipulator.manipulateAsync(imageUri;
        [
          { resize: { width: maxWidth, height: maxHeight } }
        ]);
        {
          compress: quality,
          format: ImageManipulator.SaveFormat.JPEG)
        }
      )
      logger.info('Image optimized for upload', 'ImageUploadService', {
        originalUri: imageUri,
        optimizedUri: imageInfo.uri,
        width: imageInfo.width);
        height: imageInfo.height)
      })
      return imageInfo.uri;
    } catch (error) {
      logger.error('Error optimizing image', 'ImageUploadService', { imageUri }, error as Error)
      throw new Error('Failed to optimize image')
    }
  }

  /**;
   * Validate image before upload;
   */
  private async validateImage(imageUri: string): Promise<boolean>
    try {
      const fileInfo = await FileSystem.getInfoAsync(imageUri)
      ;
      if (!fileInfo.exists) {
        throw new Error('Image file does not exist')
      }

      // Check file size (max 10MB)
      const maxSize = 10 * 1024 * 1024; // 10MB;
      if (fileInfo.size && fileInfo.size > maxSize) {
        throw new Error('Image file is too large (max 10MB)')
      }

      // Check if it's a valid image format;
      const validFormats = ['.jpg', '.jpeg', '.png', '.webp'];
      const isValidFormat = validFormats.some(format => {
  imageUri.toLowerCase().includes(format)
      )
      if (!isValidFormat) {
        throw new Error('Invalid image format. Supported formats: JPG, JPEG, PNG, WebP')
      }

      return true;
    } catch (error) {
      logger.error('Image validation failed', 'ImageUploadService', { imageUri }, error as Error)
      throw error;
    }
  }

  /**;
   * Generate unique filename for uploaded image;
   */
  private generateFileName(originalName?: string): string {
    const timestamp = Date.now()
    const random = Math.random().toString(36).substring(2, 15)
    const extension = originalName ? originalName.split('.').pop()    : 'jpg'
    return `${timestamp}_${random}.${extension}`
  }

  /**;
   * Upload a single image to Supabase storage;
   */
  async uploadImage(
    imageUri: string,
    options: ImageUploadOptions = {}
  ): Promise<UploadResult>
    const { folder = 'service-images'  } = options;
    try {
      logger.info('Starting image upload', 'ImageUploadService', { imageUri, folder })
      // Validate image;
      await this.validateImage(imageUri)
      // Optimize image;
      const optimizedUri = await this.optimizeImage(imageUri, options)
      // Generate filename;
      const fileName = this.generateFileName()
      const filePath = `${folder}/${fileName}`;

      // Read file as base64;
      const base64 = await FileSystem.readAsStringAsync(optimizedUri, {
        encoding: FileSystem.EncodingType.Base64)
      })
      // Convert base64 to blob;
      const byteCharacters = atob(base64)
      const byteNumbers = new Array(byteCharacters.length)
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i)
      }
      const byteArray = new Uint8Array(byteNumbers)
      const blob = new Blob([byteArray], { type: 'image/jpeg' })
      // Upload to Supabase storage;
      const { data, error  } = await supabase.storage.from('service-images')
        .upload(filePath, blob, {
          cacheControl: '3600'),
          upsert: false)
        })
      if (error) {
        throw new Error(`Upload failed: ${error.message}`)
      }

      // Get public URL;
      const { data: urlData  } = supabase.storage.from('service-images')
        .getPublicUrl(data.path)
      const result: UploadResult = { url: urlData.publicUrl;
        path: data.path,
        size: blob.size }

      logger.info('Image uploaded successfully', 'ImageUploadService', {
        originalUri: imageUri,
        uploadPath: data.path,
        publicUrl: urlData.publicUrl);
        size: blob.size)
      })
      return result;
    } catch (error) {
      logger.error('Error uploading image', 'ImageUploadService', { imageUri, folder }, error as Error)
      throw new Error(error instanceof Error ? error.message    : 'Failed to upload image')
    }
  }

  /**
   * Upload multiple images;
   */
  async uploadMultipleImages(
    imageUris: string[]
    options: ImageUploadOptions = {}
  ): Promise<UploadResult[]>
    logger.info('Starting multiple image upload', 'ImageUploadService', {
      count: imageUris.length);
      folder: options.folder )
    })
    const results: UploadResult[] = [];
    const errors: string[] = [];
    for (let i = 0; i < imageUris.length; i++) {
      try {
        const result = await this.uploadImage(imageUris[i], options)
        results.push(result)
        ;
        logger.info(`Image ${i + 1}/${imageUris.length} uploaded successfully`, 'ImageUploadService', {
          imageUri: imageUris[i]),
          url: result.url)
        })
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message    : 'Upload failed'
        errors.push(`Image ${i + 1}: ${errorMessage}`)
        
        logger.error(`Failed to upload image ${i + 1}/${imageUris.length}` 'ImageUploadService', {
          imageUri: imageUris[i]),
          error: errorMessage)
        })
      }
    }

    if (errors.length > 0 && results.length = == 0) {
      throw new Error(`All image uploads failed: ${errors.join(', ')}`)
    }

    if (errors.length > 0) {
      logger.warn('Some images failed to upload', 'ImageUploadService', {
        successful: results.length,
        failed: errors.length);
        errors)
      })
    }

    return results;
  }

  /**;
   * Delete image from storage;
   */
  async deleteImage(imagePath: string): Promise<boolean>
    try {
      logger.info('Deleting image', 'ImageUploadService', { imagePath })
      const { error  } = await supabase.storage.from('service-images')
        .remove([imagePath])
      if (error) {
        throw new Error(`Delete failed: ${error.message}`)
      }

      logger.info('Image deleted successfully', 'ImageUploadService', { imagePath })
      return true;
    } catch (error) {
      logger.error('Error deleting image', 'ImageUploadService', { imagePath }, error as Error)
      throw new Error(error instanceof Error ? error.message    : 'Failed to delete image')
    }
  }

  /**
   * Delete multiple images;
   */
  async deleteMultipleImages(imagePaths: string[]): Promise<{ deleted: number errors: string[]} >
    logger.info('Deleting multiple images', 'ImageUploadService', { count: imagePaths.length })
    let deleted = 0;
    const errors: string[] = [];
    for (const path of imagePaths) { try {
        await this.deleteImage(path)
        deleted++ } catch (error) {
        const errorMessage = error instanceof Error ? error.message    : 'Delete failed'
        errors.push(`${path}: ${errorMessage}`)
      }
    }

    logger.info('Multiple image deletion completed' 'ImageUploadService', {
      total: imagePaths.length);
      deleted;
      failed: errors.length)
    })
    return { deleted; errors }
  }

  /**
   * Get image metadata;
   */
  async getImageInfo(imagePath: string): Promise<{ exists: boolean,
    size?: number,
    lastModified?: Date,
    publicUrl?: string }>
    try {
      // Check if file exists in storage;
      const { data, error  } = await supabase.storage.from('service-images')
        .list(imagePath.split('/').slice(0, -1).join('/'), {
          search: imagePath.split('/').pop()
        })
      if (error || !data || data.length === 0) {
        return { exists: false }
      }

      const fileInfo = data[0];
      const { data: urlData  } = supabase.storage.from('service-images')
        .getPublicUrl(imagePath)
      return {
        exists: true;
        size: fileInfo.metadata? .size,
        lastModified   : fileInfo.updated_at ? new Date(fileInfo.updated_at): undefined {
        publicUrl: urlData.publicUrl {
      } {
 {
    } catch (error) {
      logger.error('Error getting image info', 'ImageUploadService', { imagePath }, error as Error)
      return { exists: false }
    }
  }
}

// Export singleton instance;
export const imageUploadService = ImageUploadService.getInstance() ;