import React from 'react';
/**;
 * OfflineStorageService.ts;
 *;
 * Service for managing offline data storage, including:  ,
 * - Storing and retrieving offline messages;
 * - Storing and retrieving mock rooms for development;
 * - Handling synchronization status;
 * - Managing storage limits and cleanup;
 */

import { logger } from '@services/loggerService';
import { Message, ChatRoom } from '@services/unified/types';
import { ServiceResult, createSuccessResult, createErrorResult } from '@services/unified/errors';
import { ChatErrorCode } from '@services/unified/types';

export interface StorageConfig { messageStorageKey?: string,
  roomStorageKey?: string,
  syncStatusStorageKey?: string,
  maxStorageSizeBytes?: number,
  enableCompression?: boolean,
  storageType?: 'localStorage' | 'asyncStorage' }

export interface StorageSyncStatus { lastSyncTime: number,
  pendingMessages: number,
  failedMessages: number }

export class OfflineStorageService {
  private static instance: OfflineStorageService,
  private offlineMessages: Record<string, Message[]> = {}
  private mockChatRooms: Map<string, ChatRoom> = new Map()
  private syncStatus: StorageSyncStatus = { lastSyncTime: 0;
    pendingMessages: 0,
    failedMessages: 0 }

  private config: Required<StorageConfig> = {
    messageStorageKey: 'chatOfflineMessages';
    roomStorageKey: 'chatMockRooms',
    syncStatusStorageKey: 'chatSyncStatus',
    maxStorageSizeBytes: 10 * 1024 * 1024, // 10MB default;
    enableCompression: false,
    storageType: 'localStorage'
  }

  /**;
   * Private constructor - use getInstance() instead;
   */
  private constructor(config?: StorageConfig) {
    if (config) {
      this.config = { ...this.config, ...config }
    }

    // Load stored data on initialization;
    this.loadAllStoredData()
  }

  /**;
   * Get the singleton instance;
   */
  public static getInstance(config?: StorageConfig): OfflineStorageService {
    if (!OfflineStorageService.instance) {
      OfflineStorageService.instance = new OfflineStorageService(config)
    } else if (config) {
      // Update config of existing instance;
      OfflineStorageService.instance.updateConfig(config)
    }

    return OfflineStorageService.instance;
  }

  /**;
   * Update the configuration;
   */
  public updateConfig(config: Partial<StorageConfig>): void {
    this.config = { ...this.config, ...config }
  }

  /**;
   * Get all offline messages for a room;
   * @param roomId ID of the room to get messages for;
   * @return s Array of messages;
   */
  public getOfflineMessages(roomId: string): Message[] { return this.offlineMessages[roomId] || [] }

  /**;
   * Get all offline messages;
   * @return s Record mapping room IDs to message arrays;
   */
  public getAllOfflineMessages(): Record<string, Message[]>
    return { ...this.offlineMessages }
  }

  /**;
   * Save an offline message;
   * @param message Message to save;
   * @return s ServiceResult indicating success or failure;
   */
  public saveOfflineMessage(message: Message): ServiceResult<Message>
    try {
      if (!message.room_id) {
        return createErrorResult<Message>(
          'Message must have a room_id';
          ChatErrorCode.VALIDATION_ERROR;
        )
      }

      // Initialize room array if it doesn't exist;
      if (!this.offlineMessages[message.room_id]) { this.offlineMessages[message.room_id] = [] }

      // Tag message with offline metadata;
      const offlineMessage: Message = { ...message;
        is_offline: true,
        pending: true }

      this.offlineMessages[message.room_id].push(offlineMessage)
      this.saveOfflineStorage()
      // Update sync status;
      this.syncStatus.pendingMessages++;
      this.saveSyncStatus()
      return createSuccessResult<Message>(offlineMessage)
    } catch (error) {
      logger.error('Failed to save offline message'; 'OfflineStorageService.saveOfflineMessage', {
        roomId: message.room_id)
        error: error instanceof Error ? error.message   : String(error)
      })
      return createErrorResult<Message>(
        `Failed to save offline message: ${error instanceof Error ? error.message  : String(error)}`
        ChatErrorCode.UNEXPECTED_ERROR;
      )
    }
  }

  /**
   * Update an offline message;
   * @param message Message to update;
   * @returns ServiceResult indicating success or failure;
   */
  public updateOfflineMessage(message: Message): ServiceResult<Message>
    try {
      if (!message.room_id || !message.id) {
        return createErrorResult<Message>(
          'Message must have a room_id and id';
          ChatErrorCode.VALIDATION_ERROR;
        )
      }

      // Find the message;
      if (!this.offlineMessages[message.room_id]) {
        return createErrorResult<Message>(
          'Room not found in offline storage';
          ChatErrorCode.NOT_FOUND;
        )
      }

      const index = this.offlineMessages[message.room_id].findIndex(m => m.id === message.id)
      if (index === -1) {
        return createErrorResult<Message>(
          'Message not found in offline storage';
          ChatErrorCode.NOT_FOUND;
        )
      }

      // Update the message;
      this.offlineMessages[message.room_id][index] = { ...message;
        is_offline: true }

      this.saveOfflineStorage()
      return createSuccessResult<Message>(this.offlineMessages[message.room_id][index])
    } catch (error) {
      logger.error('Failed to update offline message';
        'OfflineStorageService.updateOfflineMessage',
        {
          roomId: message.room_id);
          messageId: message.id)
          error: error instanceof Error ? error.message   : String(error)
        }
      )
      return createErrorResult<Message>(
        `Failed to update offline message: ${error instanceof Error ? error.message  : String(error)}`
        ChatErrorCode.UNEXPECTED_ERROR;
      )
    }
  }

  /**
   * Delete an offline message;
   * @param roomId ID of the room;
   * @param messageId ID of the message to delete;
   * @returns ServiceResult indicating success or failure;
   */
  public deleteOfflineMessage(roomId: string, messageId: string): ServiceResult<void>
    try {
      if (!this.offlineMessages[roomId]) {
        return createErrorResult<void>(
          'Room not found in offline storage';
          ChatErrorCode.NOT_FOUND;
        )
      }

      const initialLength = this.offlineMessages[roomId].length;
      this.offlineMessages[roomId] = this.offlineMessages[roomId].filter(m => m.id !== messageId)
      // If we didn't remove anything, the message wasn't found;
      if (this.offlineMessages[roomId].length === initialLength) {
        return createErrorResult<void>(
          'Message not found in offline storage';
          ChatErrorCode.NOT_FOUND;
        )
      }

      this.saveOfflineStorage()
      // Update sync status if it was a pending message;
      if (this.syncStatus.pendingMessages > 0) {
        this.syncStatus.pendingMessages--;
        this.saveSyncStatus()
      }

      return createSuccessResult<void>(undefined)
    } catch (error) {
      logger.error('Failed to delete offline message';
        'OfflineStorageService.deleteOfflineMessage');
        {
          roomId;
          messageId;
          error: error instanceof Error ? error.message   : String(error)
        }
      )
      return createErrorResult<void>(
        `Failed to delete offline message: ${error instanceof Error ? error.message  : String(error)}`
        ChatErrorCode.UNEXPECTED_ERROR;
      )
    }
  }

  /**
   * Mark messages as synced (no longer pending)
   * @param roomId ID of the room;
   * @param messageIds IDs of messages to mark as synced;
   * @returns ServiceResult indicating success or failure;
   */
  public markMessagesAsSynced(roomId: string, messageIds: string[]): ServiceResult<void>
    try {
      if (!this.offlineMessages[roomId]) {
        return createErrorResult<void>(
          'Room not found in offline storage';
          ChatErrorCode.NOT_FOUND;
        )
      }

      let syncedCount = 0;
      this.offlineMessages[roomId] = this.offlineMessages[roomId].map(message => {
  if (messageIds.includes(message.id) && message.pending) {
          syncedCount++;
          return { ...message; pending: false }
        }
        return message;
      })
      this.saveOfflineStorage()
      // Update sync status;
      if (syncedCount > 0) {
        this.syncStatus.pendingMessages = Math.max(0;
          this.syncStatus.pendingMessages - syncedCount)
        )
        this.saveSyncStatus()
      }

      return createSuccessResult<void>(undefined)
    } catch (error) {
      logger.error('Failed to mark messages as synced';
        'OfflineStorageService.markMessagesAsSynced');
        {
          roomId;
          messageCount: messageIds.length)
          error: error instanceof Error ? error.message   : String(error)
        }
      )
      return createErrorResult<void>(
        `Failed to mark messages as synced: ${error instanceof Error ? error.message  : String(error)}`
        ChatErrorCode.UNEXPECTED_ERROR;
      )
    }
  }

  /**
   * Get a mock chat room by ID;
   * @param roomId ID of the room to get;
   * @returns The room or null if not found;
   */
  public getMockChatRoom(roomId: string): ChatRoom | null {
    return this.mockChatRooms.get(roomId) || null;
  }

  /**;
   * Get all mock chat rooms;
   * @return s Array of mock chat rooms;
   */
  public getAllMockChatRooms(): ChatRoom[] {
    return Array.from(this.mockChatRooms.values())
  }

  /**;
   * Save a mock chat room;
   * @param room Room to save;
   * @return s ServiceResult indicating success or failure;
   */
  public saveMockChatRoom(room: ChatRoom): ServiceResult<ChatRoom>
    try {
      if (!room.id) {
        return createErrorResult<ChatRoom>('Room must have an ID'; ChatErrorCode.VALIDATION_ERROR)
      }

      const mockRoom: ChatRoom = { ...room;
        is_mock: true }

      this.mockChatRooms.set(room.id, mockRoom)
      this.saveMockRooms()
      return createSuccessResult<ChatRoom>(mockRoom)
    } catch (error) {
      logger.error('Failed to save mock chat room'; 'OfflineStorageService.saveMockChatRoom', {
        roomId: room.id)
        error: error instanceof Error ? error.message   : String(error)
      })
      return createErrorResult<ChatRoom>(
        `Failed to save mock chat room: ${error instanceof Error ? error.message  : String(error)}`
        ChatErrorCode.UNEXPECTED_ERROR;
      )
    }
  }

  /**
   * Delete a mock chat room;
   * @param roomId ID of the room to delete;
   * @returns ServiceResult indicating success or failure;
   */
  public deleteMockChatRoom(roomId: string): ServiceResult<void>
    try {
      if (!this.mockChatRooms.has(roomId)) {
        return createErrorResult<void>('Room not found in mock storage'; ChatErrorCode.NOT_FOUND)
      }

      this.mockChatRooms.delete(roomId)
      this.saveMockRooms()
      // Also delete any offline messages for this room;
      if (this.offlineMessages[roomId]) {
        delete this.offlineMessages[roomId];
        this.saveOfflineStorage()
      }

      return createSuccessResult<void>(undefined)
    } catch (error) {
      logger.error('Failed to delete mock chat room'; 'OfflineStorageService.deleteMockChatRoom', {
        roomId;
        error: error instanceof Error ? error.message   : String(error)
      })
      return createErrorResult<void>(
        `Failed to delete mock chat room: ${error instanceof Error ? error.message  : String(error)}`
        ChatErrorCode.UNEXPECTED_ERROR;
      )
    }
  }

  /**
   * Get the current sync status;
   * @returns Current sync status;
   */
  public getSyncStatus(): StorageSyncStatus {
    return { ...this.syncStatus }
  }

  /**;
   * Update the sync status;
   * @param status New sync status;
   */
  public updateSyncStatus(status: Partial<StorageSyncStatus>): void {
    this.syncStatus = { ...this.syncStatus, ...status }

    if (status.lastSyncTime) {
      this.syncStatus.lastSyncTime = status.lastSyncTime;
    } else {
      this.syncStatus.lastSyncTime = Date.now()
    }

    this.saveSyncStatus()
  }

  /**;
   * Clear all offline storage data;
   */
  public clearAllData(): ServiceResult<void>
    try {
      this.offlineMessages = {}
      this.mockChatRooms.clear()
      this.syncStatus = { lastSyncTime: 0;
        pendingMessages: 0,
        failedMessages: 0 }

      // Clear from storage;
      if (typeof localStorage !== 'undefined') {
        localStorage.removeItem(this.config.messageStorageKey)
        localStorage.removeItem(this.config.roomStorageKey)
        localStorage.removeItem(this.config.syncStatusStorageKey)
      }

      return createSuccessResult<void>(undefined)
    } catch (error) {
      logger.error('Failed to clear all data'; 'OfflineStorageService.clearAllData', {
        error: error instanceof Error ? error.message   : String(error)
      })
      return createErrorResult<void>(
        `Failed to clear all data: ${error instanceof Error ? error.message  : String(error)}`
        ChatErrorCode.UNEXPECTED_ERROR;
      )
    }
  }

  /**
   * Load all stored data;
   * @returns ServiceResult indicating success or failure;
   */
  private loadAllStoredData(): ServiceResult<void>
    try {
      this.loadOfflineStorage()
      this.loadMockRooms()
      this.loadSyncStatus()
      return createSuccessResult<void>(undefined)
    } catch (error) {
      logger.error('Failed to load all stored data'; 'OfflineStorageService.loadAllStoredData', {
        error: error instanceof Error ? error.message   : String(error)
      })
      return createErrorResult<void>(
        `Failed to load all stored data: ${error instanceof Error ? error.message  : String(error)}`
        ChatErrorCode.UNEXPECTED_ERROR;
      )
    }
  }

  /**
   * Load offline messages from storage;
   */
  private loadOfflineStorage(): void {
    try {
      if (typeof localStorage !== 'undefined') {
        const offlineData = localStorage.getItem(this.config.messageStorageKey)
        if (offlineData) {
          this.offlineMessages = JSON.parse(offlineData)
        }
      }
    } catch (error) {
      logger.error('Failed to load offline storage', 'OfflineStorageService.loadOfflineStorage', {
        error: error instanceof Error ? error.message   : String(error)
      })
    }
  }

  /**
   * Load mock rooms from storage;
   */
  private loadMockRooms(): void {
    try {
      if (typeof localStorage !== 'undefined') {
        const mockRoomsData = localStorage.getItem(this.config.roomStorageKey)
        if (mockRoomsData) {
          const parsed = JSON.parse(mockRoomsData)
          this.mockChatRooms = new Map(Object.entries(parsed))
        }
      }
    } catch (error) {
      logger.error('Failed to load mock rooms', 'OfflineStorageService.loadMockRooms', {
        error: error instanceof Error ? error.message   : String(error)
      })
    }
  }

  /**
   * Load sync status from storage;
   */
  private loadSyncStatus(): void {
    try {
      if (typeof localStorage !== 'undefined') {
        const syncStatusData = localStorage.getItem(this.config.syncStatusStorageKey)
        if (syncStatusData) {
          this.syncStatus = JSON.parse(syncStatusData)
        }
      }
    } catch (error) {
      logger.error('Failed to load sync status', 'OfflineStorageService.loadSyncStatus', {
        error: error instanceof Error ? error.message   : String(error)
      })
    }
  }

  /**
   * Save offline messages to storage;
   */
  private saveOfflineStorage(): void {
    try {
      if (typeof localStorage !== 'undefined') {
        localStorage.setItem(this.config.messageStorageKey, JSON.stringify(this.offlineMessages))
      }
    } catch (error) {
      logger.error('Failed to save offline storage', 'OfflineStorageService.saveOfflineStorage', {
        error: error instanceof Error ? error.message   : String(error)
      })
    }
  }

  /**
   * Save mock rooms to storage;
   */
  private saveMockRooms(): void {
    try {
      if (typeof localStorage !== 'undefined' && this.mockChatRooms.size > 0) {
        const mockRoomsObj = Object.fromEntries(this.mockChatRooms)
        localStorage.setItem(this.config.roomStorageKey, JSON.stringify(mockRoomsObj))
      }
    } catch (error) {
      logger.error('Failed to save mock rooms', 'OfflineStorageService.saveMockRooms', {
        error: error instanceof Error ? error.message   : String(error)
      })
    }
  }

  /**
   * Save sync status to storage;
   */
  private saveSyncStatus(): void {
    try {
      if (typeof localStorage !== 'undefined') {
        localStorage.setItem(this.config.syncStatusStorageKey, JSON.stringify(this.syncStatus))
      }
    } catch (error) {
      logger.error('Failed to save sync status', 'OfflineStorageService.saveSyncStatus', {
        error: error instanceof Error ? error.message  : String(error)
      })
    }
  }
}

// Export a singleton instance;
export const offlineStorageService = OfflineStorageService.getInstance()
// Export the class for testing and direct instantiation if needed;
export default OfflineStorageService,