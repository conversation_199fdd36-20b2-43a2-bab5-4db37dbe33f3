import React from 'react';
/**;
 * Unified Chat Service Types;
 * Contains all type definitions used by the unified chat service;
 */

// Base message type;
export type MessageType = 'text' | 'image' | 'location' | 'agreement' | 'system';

// Message interface;
export interface Message {
  id: string,
  room_id: string,
  sender_id: string,
  content: string,
  timestamp: string,
  read: boolean,
  type: MessageType,
  metadata?: any,
  is_offline?: boolean,
  is_system_message?: boolean,
  pending?: boolean; // For messages that are waiting to be sent;
}

// Chat room interface;
export interface ChatRoom { id: string,
  participants: ChatParticipant[],
  created_at: string,
  updated_at: string,
  created_by?: string,
  last_message?: string,
  last_message_at?: string | null,
  is_group?: boolean,
  name?: string,
  avatar_url?: string,
  is_mock?: boolean,
  unread_count: number }

// Chat participant interface;
export interface ChatParticipant { user_id: string,
  room_id: string,
  joined_at?: string,
  last_read_at?: string,
  profile?: UserProfile }

// User profile interface (simplified)
export interface UserProfile { id: string,
  full_name?: string,
  avatar_url?: string,
  email?: string }

// Error types;
export enum ChatErrorCode { NETWORK_ERROR = 'network_error';
  PERMISSION_ERROR = 'permission_error';
  NOT_FOUND = 'not_found';
  VALIDATION_ERROR = 'validation_error';
  DATABASE_ERROR = 'database_error';
  UNEXPECTED_ERROR = 'unexpected_error';
  OFFLINE_ERROR = 'offline_error';
  CONFLICT_ERROR = 'conflict_error';
  TIMEOUT_ERROR = 'timeout_error';
  AUTH_ERROR = 'auth_error';
  GENERAL_ERROR = 'general_error' }

export interface ChatError { code: ChatErrorCode;
  message: string,
  isRetryable: boolean,
  metadata?: Record<string, any>
  timestamp: string }

// Service configuration options;
export interface ChatServiceConfig { enableMockRooms?: boolean,
  offlineMode?: boolean,
  maxRetries?: number,
  retryDelay?: number,
  warningThrottleMs?: number }

// Chat service interface;
export interface IChatService { // Room management;
  getChatRooms(userId: string): Promise<ChatRoom[]>
  getChatRoom(roomId: string): Promise<ChatRoom | null>; {
  createChatRoom( {
    creatorId: string, {
    participantIds: string[], {
    options?: {
      name?: string,
      isGroup?: boolean,
      avatarUrl?: string }
  ): Promise<{ success: boolean; roomId?: string; error?: string }>
  // Participants;
  getChatRoomParticipants(roomId: string): Promise<ChatParticipant[]>
  addParticipantToChatRoom( {
    roomId: string, {
    userId: string, {
    addedByUserId: string {
  ): Promise<{ success: boolean; error?: string }>
  removeParticipantFromChatRoom(roomId: string,
    userId: string,
    removedByUserId: string): Promise<{ success: boolean; error?: string }>
  // Messages;
  getMessages(roomId: string): Promise<Message[]>
  sendMessage(roomId: string,
    userId: string,
    content: string,
    type?: MessageType,
    metadata?: any): Promise<Message | null>
  markMessagesAsRead(roomId: string, userId: string): Promise<boolean>
  getUnreadMessageCount(roomId: string, userId: string): Promise<number>
  // Subscriptions {
  subscribeToMessages( {
    roomId: string, {
    callback: (message: Message) = > void {
  ): { unsubscribe: () => void }
  subscribeToChatRoomUpdates(roomId: string;
    callback: (,
      eventType: 'room_updated' | 'participant_joined' | 'participant_left' | 'read_status_changed',
      data: any) => void;
  ): { unsubscribe: () => void }

  // Match to chat flow;
  createChatFromMatch(userId: string,
    matchUserId: string,
    initialMessage?: string): Promise<{ success: boolean; roomId?: string; error?: string }>
  // Chat to agreement flow;
  initiateAgreementFromChat(chatRoomId: string,
    userId: string,
    otherUserId: string,
    templateId?: string): Promise<{ success: boolean; agreementId?: string; error?: string }>
  // Network and error handling;
  checkNetworkConnection(): Promise<boolean>
  setOfflineMode(offline: boolean): void; { {
  // Storage management {
  syncOfflineData( {
    progressCallback?: (progress: {
      total: number,
      processed: number,
      succeeded: number,
      failed: number,
      roomId?: string }) => void;
  ): Promise<{
    totalMessages: number,
    succeededMessages: number,
    failedMessages: number,
    roomsWithFailures: string[]
  }>
}
