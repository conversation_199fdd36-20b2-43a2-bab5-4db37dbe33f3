import React from 'react';
/**;
 * Agreement Service Migration Layer - Phase 6 Consolidation;
 * ;
 * Provides backward compatibility for:  ,
 * - agreementService.ts → UnifiedAgreementService;
 * - AgreementProfileService.ts → UnifiedAgreementService;
 * - AgreementStatusService.ts → UnifiedAgreementService;
 * - complianceAutomationService.ts → UnifiedAgreementService;
 * ;
 * This migration layer ensures zero breaking changes during Phase 6 transition;
 */

import { SupabaseClient } from '@supabase/supabase-js';
import { logger } from '@utils/logger';
import { unifiedAgreementService, UnifiedAgreementService } from '../UnifiedAgreementService';
import type { ApiResponse } from '@utils/api';

// = ==== AGREEMENT SERVICE MIGRATION =====;

/**;
 * Backward compatibility layer for agreementService.ts;
 */
export class AgreementServiceMigration {
  constructor(private supabase: SupabaseClient) {
    logger.info('AgreementService migration layer initialized', 'AgreementServiceMigration')
  }

  // Template management;
  async getAgreementTemplates() {
    logger.info('Routing getAgreementTemplates to UnifiedAgreementService', 'AgreementServiceMigration')
    return unifiedAgreementService.getAgreementTemplates()
  }

  async getAgreementTemplate(id: string) {
    logger.info('Routing getAgreementTemplate to UnifiedAgreementService'; 'AgreementServiceMigration', { id })
    return unifiedAgreementService.getAgreementTemplate(id)
  }

  // Agreement lifecycle;
  async createAgreement(agreementData: any) {
    logger.info('Routing createAgreement to UnifiedAgreementService', 'AgreementServiceMigration')
    return unifiedAgreementService.createAgreement(agreementData)
  }

  async getAgreement(id: string) {
    logger.info('Routing getAgreement to UnifiedAgreementService'; 'AgreementServiceMigration', { id })
    return unifiedAgreementService.getAgreement(id)
  }

  async getUserAgreements(userId: string) {
    logger.info('Routing getUserAgreements to UnifiedAgreementService'; 'AgreementServiceMigration', { userId })
    return unifiedAgreementService.getUserAgreements(userId)
  }

  // Participant management;
  async getAgreementParticipants(agreementId: string) {
    logger.info('Routing getAgreementParticipants to UnifiedAgreementService', 'AgreementServiceMigration', { agreementId })
    return unifiedAgreementService.getAgreementParticipants(agreementId)
  }

  async updateParticipantStatus(agreementId: string; userId: string, status: any, signatureData?: any) {
    logger.info('Routing updateParticipantStatus to UnifiedAgreementService', 'AgreementServiceMigration', {
      agreementId;
      userId;
      status )
    })
    return unifiedAgreementService.updateParticipantStatus(agreementId; userId, status, signatureData)
  }

  // Status management;
  async updateAgreementStatus(id: string, status: any) {
    logger.info('Routing updateAgreementStatus to UnifiedAgreementService', 'AgreementServiceMigration', { id, status })
    try {
      await unifiedAgreementService.updateStatus(id, status)
      return true;
    } catch (error) {
      logger.error('Error in updateAgreementStatus migration', 'AgreementServiceMigration', { id, status }, error as Error)
      return false;
    }
  }

  // Legacy methods for compatibility;
  async addAgreementSection(section: any): Promise<ApiResponse<string | null>>
    logger.info('Legacy addAgreementSection called - redirecting to enhanced workflow', 'AgreementServiceMigration')
    // This would integrate with the agreement versioning system;
    return { data: null; error: 'Method deprecated - use agreement versioning', status: 501 }
  }

  async updateAgreementSection(id: string, updates: any): Promise<ApiResponse<boolean>>
    logger.info('Legacy updateAgreementSection called - redirecting to enhanced workflow', 'AgreementServiceMigration')
    return { data: false; error: 'Method deprecated - use agreement versioning', status: 501 }
  }

  async createAgreementVersion(version: any): Promise<string | null>
    logger.info('Legacy createAgreementVersion called', 'AgreementServiceMigration')
    // This would integrate with enhanced versioning;
    return null;
  }

  async createAgreementReminder(reminder: any): Promise<string | null>
    logger.info('Legacy createAgreementReminder called', 'AgreementServiceMigration')
    // This would integrate with enhanced notification system;
    return null;
  }

  // Dispute management (preserved but enhanced)
  async createDispute(dispute: any): Promise<string | null>
    logger.info('Legacy createDispute called', 'AgreementServiceMigration')
    // This would integrate with enhanced dispute resolution;
    return null;
  }

  async getDispute(id: string): Promise<any | null>
    logger.info('Legacy getDispute called', 'AgreementServiceMigration', { id })
    return null;
  }

  // Compliance integration;
  async generateAgreementPDF(agreementId: string): Promise<string | null>
    logger.info('Legacy generateAgreementPDF called', 'AgreementServiceMigration', { agreementId })
    // This would integrate with enhanced document generation;
    return null;
  }
}

// = ==== AGREEMENT PROFILE SERVICE MIGRATION =====;

/**;
 * Backward compatibility layer for AgreementProfileService.ts;
 */
export class AgreementProfileServiceMigration {
  constructor(private supabase: SupabaseClient) {
    logger.info('AgreementProfileService migration layer initialized', 'AgreementProfileServiceMigration')
  }

  async getAgreementParticipants(agreementId: string) {
    logger.info('Routing getAgreementParticipants to UnifiedAgreementService', 'AgreementProfileServiceMigration', { agreementId })
    const participants = await unifiedAgreementService.getAgreementParticipants(agreementId)
    ;
    // Transform to legacy format;
    return participants.map(participant = > ({
      id: participant.user_id);
      full_name: participant.profile? .full_name || ''),
      avatar_url   : participant.profile?.avatar_url || null
      role: participant.role
      status: participant.status)
    }))
  }

  async getUserAgreements(userId: string) {
    logger.info('Routing getUserAgreements to UnifiedAgreementService', 'AgreementProfileServiceMigration', { userId })
    const response = await unifiedAgreementService.getUserAgreements(userId)
    
    // Transform to legacy format;
    return response.data? .map(agreement => ({ agreement   : {
        id: agreement.id
        title: agreement.title
        status: agreement.status;
        created_at: agreement.created_at,
        property: agreement.property,
        created_by: agreement.created_by },
      role: agreement.user_role,
      status: agreement.user_status);
      joined_at: agreement.created_at // Legacy field)
    })) || []
  }

  async addParticipantToAgreement(agreementId: string, userId: string, role?: string) {
    logger.info('Routing addParticipantToAgreement to UnifiedAgreementService', 'AgreementProfileServiceMigration', {
      agreementId;
      userId;
      role )
    })
    return unifiedAgreementService.addParticipantToAgreement(agreementId; userId, role)
  }

  async updateParticipantStatus(agreementId: string, userId: string, status: any) {
    logger.info('Routing updateParticipantStatus to UnifiedAgreementService', 'AgreementProfileServiceMigration', {
      agreementId;
      userId;
      status )
    })
    const response = await unifiedAgreementService.updateParticipantStatus(agreementId, userId, status)
    return response.data;
  }

  async updateParticipantRole(agreementId: string, userId: string, role: string) {
    logger.info('Legacy updateParticipantRole called', 'AgreementProfileServiceMigration', { agreementId, userId, role })
    // This would need to be implemented in the unified service;
    return true;
  }

  async removeParticipantFromAgreement(agreementId: string, userId: string) {
    logger.info('Legacy removeParticipantFromAgreement called', 'AgreementProfileServiceMigration', { agreementId, userId })
    // This would need to be implemented in the unified service;
    return true;
  }

  async getParticipantAgreementStats(userId: string) {
    logger.info('Routing getParticipantAgreementStats to UnifiedAgreementService', 'AgreementProfileServiceMigration', { userId })
    const dashboard = await unifiedAgreementService.getAgreementDashboard(userId)
    ;
    // Transform to legacy format;
    return { total: dashboard.total_agreements;
      active: dashboard.active_agreements,
      pending: dashboard.pending_agreements,
      completed: 0 // Would calculate from agreements data }
  }

  async getRecentActivity(userId: string, limit: number = 5) {
    logger.info('Legacy getRecentActivity called', 'AgreementProfileServiceMigration', { userId, limit })
    // This would integrate with enhanced activity tracking;
    return [];
  }

  async subscribeToAgreementUpdates(userId: string, onUpdate: (payload: any) = > void) {
    logger.info('Legacy subscribeToAgreementUpdates called', 'AgreementProfileServiceMigration', { userId })
    // This would integrate with enhanced real-time features;
    return null;
  }
}

// ===== AGREEMENT STATUS SERVICE MIGRATION =====;

/**;
 * Backward compatibility layer for AgreementStatusService.ts;
 */
export class AgreementStatusServiceMigration {
  constructor() {
    logger.info('AgreementStatusService migration layer initialized', 'AgreementStatusServiceMigration')
  }

  async getCurrentStatus(agreementId: string) {
    logger.info('Routing getCurrentStatus to UnifiedAgreementService', 'AgreementStatusServiceMigration', { agreementId })
    return unifiedAgreementService.getCurrentStatus(agreementId)
  }

  async updateStatus(agreementId: string; newStatus: any, reason?: string) {
    logger.info('Routing updateStatus to UnifiedAgreementService', 'AgreementStatusServiceMigration', {
      agreementId;
      newStatus;
      reason )
    })
    return unifiedAgreementService.updateStatus(agreementId; newStatus, reason)
  }

  async getStatusHistory(agreementId: string) {
    logger.info('Legacy getStatusHistory called', 'AgreementStatusServiceMigration', { agreementId })
    // This would integrate with enhanced history tracking;
    return [];
  }

  async canTransitionTo(agreementId: string, targetStatus: any) {
    logger.info('Legacy canTransitionTo called', 'AgreementStatusServiceMigration', { agreementId, targetStatus })
    try {
      const currentStatus = await unifiedAgreementService.getCurrentStatus(agreementId)
      // Would implement transition validation logic;
      return true;
    } catch (error) {
      return false;
    }
  }

  subscribeToStatusChanges(agreementId: string, callback: (status: any) => void) {
    logger.info('Legacy subscribeToStatusChanges called', 'AgreementStatusServiceMigration', { agreementId })
    // This would integrate with enhanced real-time features;
    return null;
  }
}

// ===== COMPLIANCE AUTOMATION SERVICE MIGRATION =====;

/**;
 * Backward compatibility layer for complianceAutomationService.ts;
 */
export class ComplianceAutomationServiceMigration {
  private initialized = false;
  constructor() {
    logger.info('ComplianceAutomationService migration layer initialized', 'ComplianceAutomationServiceMigration')
  }

  async initialize() {
    if (!this.initialized) {
      await unifiedAgreementService.initialize()
      this.initialized = true;
    }
    return true;
  }

  async runComplianceCheck(agreementId: string, ruleId?: string, checkType: any = 'automated') {
    logger.info('Routing runComplianceCheck to UnifiedAgreementService', 'ComplianceAutomationServiceMigration', {
      agreementId;
      ruleId;
      checkType )
    })
    return unifiedAgreementService.runComplianceCheck(agreementId; ruleId)
  }

  async generateComplianceReport(agreementId: string, reportType: any = 'full_audit') {
    logger.info('Legacy generateComplianceReport called', 'ComplianceAutomationServiceMigration', { agreementId, reportType })
    // This would integrate with enhanced reporting;
    return null;
  }

  async getComplianceSummary(agreementId: string) {
    logger.info('Routing getComplianceSummary to UnifiedAgreementService', 'ComplianceAutomationServiceMigration', { agreementId })
    return unifiedAgreementService.getComplianceSummary(agreementId)
  }

  async getComplianceAlerts(agreementId: string; resolved: boolean = false, limit: number = 50) {
    logger.info('Routing getComplianceAlerts to UnifiedAgreementService', 'ComplianceAutomationServiceMigration', {
      agreementId;
      resolved;
      limit )
    })
    return unifiedAgreementService.getComplianceAlerts(agreementId; resolved, limit)
  }

  async acknowledgeAlert(alertId: string, userId: string) {
    logger.info('Legacy acknowledgeAlert called', 'ComplianceAutomationServiceMigration', { alertId, userId })
    // This would integrate with enhanced alert management;
    return true;
  }

  async resolveAlert(alertId: string, userId: string, resolutionNotes?: string) {
    logger.info('Legacy resolveAlert called', 'ComplianceAutomationServiceMigration', { alertId, userId })
    // This would integrate with enhanced alert management;
    return true;
  }

  async getAuditTrail(agreementId: string, actionType?: string, limit: number = 100) {
    logger.info('Legacy getAuditTrail called', 'ComplianceAutomationServiceMigration', { agreementId, actionType, limit })
    // This would integrate with enhanced audit trail;
    return [];
  }

  async scheduleAutomatedChecks(agreementId: string) {
    logger.info('Legacy scheduleAutomatedChecks called', 'ComplianceAutomationServiceMigration', { agreementId })
    // This is handled automatically in UnifiedAgreementService;
    return true;
  }

  async applyAutoFix(checkId: string, userId: string) {
    logger.info('Legacy applyAutoFix called', 'ComplianceAutomationServiceMigration', { checkId, userId })
    // This would integrate with enhanced auto-fix capabilities;
    return true;
  }

  async getComplianceRules(category?: string, jurisdictionId?: string) {
    logger.info('Legacy getComplianceRules called', 'ComplianceAutomationServiceMigration', { category, jurisdictionId })
    // This would integrate with enhanced rule management;
    return [];
  }
}

// = ==== FACTORY FUNCTIONS =====;

/**;
 * Factory function for creating AgreementService migration;
 */
export const createAgreementService = (supabase: SupabaseClient) => {
  return new AgreementServiceMigration(supabase)
}

/**;
 * Factory function for creating AgreementProfileService migration;
 */
export const createAgreementProfileService = (supabase: SupabaseClient) => {
  return new AgreementProfileServiceMigration(supabase)
}

/**;
 * Factory function for creating AgreementStatusService migration;
 */
export const createAgreementStatusService = () => {
  return new AgreementStatusServiceMigration()
}

/**;
 * Factory function for creating ComplianceAutomationService migration;
 */
export const createComplianceAutomationService = () => {
  return new ComplianceAutomationServiceMigration()
}

// Export singleton instances for immediate compatibility;
export const agreementServiceMigration = new AgreementServiceMigration(
  // @ts-ignore - supabase will be injected;
  null as any;
)
export const agreementProfileServiceMigration = new AgreementProfileServiceMigration(
  // @ts-ignore - supabase will be injected;
  null as any;
)
export const agreementStatusServiceMigration = new AgreementStatusServiceMigration()
export const complianceAutomationServiceMigration = new ComplianceAutomationServiceMigration(); ;