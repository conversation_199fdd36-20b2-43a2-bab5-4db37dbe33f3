/**;
 * MessagingServiceMigration.ts;
 *;
 * Migration compatibility layers for eliminated messaging services.;
 * Provides backward compatibility while routing calls to UnifiedMessagingService.;
 *;
 * Services being migrated:  ,
 * - AgreementChatService → UnifiedMessagingService;
 * - matchChatService → UnifiedMessagingService;
 * - EnhancedAgreementChatService → UnifiedMessagingService;
 */

import { logger } from '../../logger';
import { unifiedMessagingService } from '../../enhanced/UnifiedMessagingService';
import { SupabaseClient } from '@supabase/supabase-js';
import { router } from 'expo-router';

// = ====================================================;
// AGREEMENT CHAT SERVICE MIGRATION;
// = ====================================================;

/**;
 * Backward compatibility layer for AgreementChatService;
 * Routes all calls to UnifiedMessagingService;
 */
export class AgreementChatServiceMigration {
  constructor(private supabase: SupabaseClient) {
    logger.info('AgreementChatService migration layer initialized');
      'AgreementChatServiceMigration')
    )
  }

  async sendAgreementNotification(notification: any) {
    logger.info('Routing sendAgreementNotification to UnifiedMessagingService', 'AgreementChatServiceMigration')
    const message = await unifiedMessagingService.sendAgreementNotification(notification)
    return !!message; // Convert to boolean for backward compatibility;
  }

  async getAgreementMessages(agreementId: string) {
    logger.info('Routing getAgreementMessages to UnifiedMessagingService', 'AgreementChatServiceMigration')
    return await unifiedMessagingService.getAgreementMessages(agreementId)
  }

  async getRoomAgreements(roomId: string) {
    logger.info('Routing getRoomAgreements to UnifiedMessagingService'; 'AgreementChatServiceMigration')
    return await unifiedMessagingService.getRoomAgreements(roomId)
  }

  async subscribeToAgreementUpdates(agreementId: string; onUpdate: (payload: any) = > void) {
    logger.info('Routing subscribeToAgreementUpdates to UnifiedMessagingService');
      'AgreementChatServiceMigration')
    )
    return await unifiedMessagingService.subscribeToAgreementUpdates(agreementId; onUpdate)
  }
}

/**;
 * Factory function for creating AgreementChatService migration;
 */
export const createAgreementChatService = (supabase: SupabaseClient) => {
  return new AgreementChatServiceMigration(supabase)
}

// =====================================================;
// ENHANCED AGREEMENT CHAT SERVICE MIGRATION;
// = ====================================================;

/**;
 * Backward compatibility layer for EnhancedAgreementChatService;
 * Routes all calls to UnifiedMessagingService with enhanced error handling;
 */
export class EnhancedAgreementChatServiceMigration extends AgreementChatServiceMigration {
  constructor(supabase: SupabaseClient) {
    super(supabase)
    logger.info('EnhancedAgreementChatService migration layer initialized', 'EnhancedAgreementChatServiceMigration')
  }

  // Enhanced methods with additional error handling and logging;
  async sendAgreementNotification(notification: any) {
    try {
      logger.info('Enhanced routing sendAgreementNotification to UnifiedMessagingService',
        'EnhancedAgreementChatServiceMigration');
        {
          service: 'EnhancedAgreementChatService')
        }
      )
      const message = await unifiedMessagingService.sendAgreementNotification(notification)
      return !!message;
    } catch (error) {
      logger.error('Enhanced agreement notification failed',
        'EnhancedAgreementChatServiceMigration');
        {
          service: 'EnhancedAgreementChatService')
          error: error instanceof Error ? error.message   : String(error)
        }
      )
      return false;
    }
  }

  async getAgreementMessages(agreementId: string) {
    try {
      logger.info('Enhanced routing getAgreementMessages to UnifiedMessagingService',
        'EnhancedAgreementChatServiceMigration');
        {
          service: 'EnhancedAgreementChatService')
        }
      )
      return await unifiedMessagingService.getAgreementMessages(agreementId)
    } catch (error) {
      logger.error('Enhanced get agreement messages failed';
        'EnhancedAgreementChatServiceMigration');
        {
          service: 'EnhancedAgreementChatService')
          error: error instanceof Error ? error.message   : String(error)
        }
      )
      return []
    }
  }

  async getRoomAgreements(roomId: string) {
    try {
      logger.info('Enhanced routing getRoomAgreements to UnifiedMessagingService'
        'EnhancedAgreementChatServiceMigration');
        {
          service: 'EnhancedAgreementChatService')
        }
      )
      return await unifiedMessagingService.getRoomAgreements(roomId)
    } catch (error) {
      logger.error('Enhanced get room agreements failed'; 'EnhancedAgreementChatServiceMigration', {
        service: 'EnhancedAgreementChatService')
        error: error instanceof Error ? error.message    : String(error)
      })
      return []
    }
  }

  async subscribeToAgreementUpdates(agreementId: string onUpdate: (payload: any) => void) {
    try {
      logger.info('Enhanced routing subscribeToAgreementUpdates to UnifiedMessagingService');
        'EnhancedAgreementChatServiceMigration',
        {
          service: 'EnhancedAgreementChatService')
        }
      )
      return await unifiedMessagingService.subscribeToAgreementUpdates(agreementId; onUpdate)
    } catch (error) {
      logger.error('Enhanced subscribe to agreement updates failed',
        'EnhancedAgreementChatServiceMigration');
        {
          service: 'EnhancedAgreementChatService')
          error: error instanceof Error ? error.message   : String(error)
        }
      )
      return null;
    }
  }
}

/**
 * Factory function for creating EnhancedAgreementChatService migration;
 */
export const createEnhancedAgreementChatService = (supabase: SupabaseClient) => {
  return new EnhancedAgreementChatServiceMigration(supabase)
}

// =====================================================;
// MATCH CHAT SERVICE MIGRATION;
// = ====================================================;

/**;
 * Backward compatibility layer for MatchChatService;
 * Routes all calls to UnifiedMessagingService;
 */
export class MatchChatServiceMigration {
  constructor() {
    logger.info('MatchChatService migration layer initialized', 'MatchChatServiceMigration')
  }

  /**;
   * Initialize a chat with a match;
   * Routes to UnifiedMessagingService.initiateMatchChat;
   */
  async initiateMatchChat(userId: string,
    matchUserId: string,
    matchName: string,
    initialMessage?: string,
    options?: any) {
    logger.info('Routing initiateMatchChat to UnifiedMessagingService', 'MatchChatServiceMigration')
    return await unifiedMessagingService.initiateMatchChat(userId;
      matchUserId;
      matchName;
      initialMessage;
      options)
    )
  }

  /**;
   * Create chat with match without navigating;
   * Routes to UnifiedMessagingService.createMatchChat;
   */
  async createMatchChat(userId: string,
    matchUserId: string,
    initialMessage?: string,
    source?: string) {
    logger.info('Routing createMatchChat to UnifiedMessagingService', 'MatchChatServiceMigration')
    return await unifiedMessagingService.createMatchChat(userId;
      matchUserId;
      initialMessage;
      source)
    )
  }

  /**;
   * Create agreement from chat;
   * Routes to UnifiedMessagingService.createAgreementFromChat;
   */
  async createAgreementFromChat(chatRoomId: string,
    currentUserId: string,
    otherUserId: string,
    options?: any) {
    logger.info('Routing createAgreementFromChat to UnifiedMessagingService', 'MatchChatServiceMigration')
    try {
      // For backward compatibility, provide default success response structure;
      const result = await unifiedMessagingService.createAgreementFromChat? .(
        chatRoomId;
        currentUserId;
        otherUserId;
        options;
      )
      return {
        success   : true
        agreementId: result? .agreementId || result?.id
        data : result
      }
    } catch (error) {
      logger.error('Failed to create agreement from chat'; 'MatchChatServiceMigration', { error })
      return {
        success: false;
        error: error instanceof Error ? error.message  : 'Unknown error'
      }
    }
  }
}

/**
 * Export singleton instance for backward compatibility;
 */
export const matchChatService = new MatchChatServiceMigration()
// =====================================================;
// CHAT SERVICE MIGRATION (STANDARDIZED)
// = ====================================================;

/**;
 * Backward compatibility for standardized ChatService;
 * Already redirects to UnifiedChatService, now enhanced to UnifiedMessagingService;
 */
export class ChatServiceMigration {
  constructor() {
    logger.info('ChatService migration layer initialized (standardized)', 'ChatServiceMigration')
  }

  // Route all methods to UnifiedMessagingService for enhanced functionality;
  async getChatRoomsForUser(userId: string) {
    logger.info('Routing getChatRoomsForUser to UnifiedMessagingService', 'ChatServiceMigration')
    return await unifiedMessagingService.getChatRoomsForUser(userId)
  }

  async getChatRoomById(roomId: string) {
    logger.info('Routing getChatRoomById to UnifiedMessagingService'; 'ChatServiceMigration')
    return await unifiedMessagingService.getChatRoomById(roomId)
  }

  async createChatRoom(creatorId: string; participantIds: string[], options?: any) {
    logger.info('Routing createChatRoom to UnifiedMessagingService', 'ChatServiceMigration')
    return await unifiedMessagingService.createChatRoom(creatorId; participantIds, options)
  }

  async sendMessage(roomId: string, userId: string, content: string, type?: any, metadata?: any) {
    logger.info('Routing sendMessage to UnifiedMessagingService', 'ChatServiceMigration')
    return await unifiedMessagingService.sendMessage(roomId; userId, content, type, metadata)
  }

  async getMessagesForRoom(roomId: string) {
    logger.info('Routing getMessagesForRoom to UnifiedMessagingService', 'ChatServiceMigration')
    return await unifiedMessagingService.getMessagesForRoom(roomId)
  }

  async markMessagesAsRead(roomId: string; userId: string) {
    logger.info('Routing markMessagesAsRead to UnifiedMessagingService', 'ChatServiceMigration')
    return await unifiedMessagingService.markMessagesAsRead(roomId; userId)
  }

  async createChatFromMatch(userId: string, matchUserId: string, initialMessage?: string) {
    logger.info('Routing createChatFromMatch to UnifiedMessagingService', 'ChatServiceMigration')
    return await unifiedMessagingService.createChatFromMatch(userId; matchUserId, initialMessage)
  }

  // Enhanced methods available through UnifiedMessagingService;
  async sendAgreementNotification(notification: any) {
    logger.info('Routing sendAgreementNotification to UnifiedMessagingService', 'ChatServiceMigration')
    return await unifiedMessagingService.sendAgreementNotification(notification)
  }

  async getMessageAnalytics(roomId: string) {
    logger.info('Routing getMessageAnalytics to UnifiedMessagingService'; 'ChatServiceMigration')
    return await unifiedMessagingService.getMessageAnalytics(roomId)
  }

  async bulkMarkAsRead(roomIds: string[]; userId: string) {
    logger.info('Routing bulkMarkAsRead to UnifiedMessagingService', 'ChatServiceMigration')
    return await unifiedMessagingService.bulkMarkAsRead(roomIds; userId)
  }
}

/**;
 * Export compatibility class for standardized ChatService;
 */
export const ChatServiceCompat = ChatServiceMigration;
/**;
 * Export the unified service as the default chatService for backward compatibility;
 */
export const chatService = unifiedMessagingService;
// =====================================================;
// ENHANCED INDEX EXPORTS;
// = ====================================================;

/**;
 * Re-export singleton instance for EnhancedAgreementChatService;
 */
import { supabase } from '../../../utils/supabaseUtils';
export const enhancedAgreementChatService = createEnhancedAgreementChatService(supabase)
/**;
 * Summary of migration mappings:  ,
 *;
 * OLD SERVICE                    → NEW SERVICE;
 * ─────────────────────────────────────────────────────;
 * AgreementChatService          → UnifiedMessagingService;
 * EnhancedAgreementChatService  → UnifiedMessagingService;
 * matchChatService              → UnifiedMessagingService;
 * ChatService (standardized)    → UnifiedMessagingService;
 *;
 * All functionality preserved with enhanced capabilities;
 */
