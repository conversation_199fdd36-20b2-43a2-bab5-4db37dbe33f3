import React from 'react';
/**;
 * Room Service Migration Layer;
 * ;
 * Provides backward compatibility during transition to UnifiedRoomService;
 * Maintains existing API contracts while routing to unified implementation;
 * ;
 * Phase 3: Room Listings Migration,
 * Gradually replaces: RoomService, SavedRoomService, BrowseService room logic;
 */

import { unifiedRoomService, RoomFormData, RoomFilters } from '@services/enhanced/UnifiedRoomService';
import { logger } from '@services/loggerService';

// Legacy type mappings for backward compatibility;
export interface LegacyRoomFormData { title: string,
  description: string,
  price: number,
  location: string,
  room_type: string,
  bedrooms?: number,
  bathrooms?: number,
  furnished?: boolean,
  pets_allowed?: boolean,
  images?: string[],
  amenities?: string[],
  preferences?: string[],
  move_in_date: string,
  status?: string,
  is_available?: boolean,
  landlord_id?: string,
  location_id?: string }

export interface LegacyApiResponse<T>
  data: T | null,
  error: string | null,
  status: number
}

/**;
 * Migration wrapper for original RoomService;
 * Maintains API compatibility while using UnifiedRoomService;
 */
export class RoomServiceMigration { private unified = unifiedRoomService;
  /**;
   * Legacy getRooms method with filter mapping;
   */
  async getRooms(
    limit = 20;
    offset = 0;
    filters?: {
      minPrice?: number,
      maxPrice?: number,
      location?: string,
      roomType?: string,
      bedrooms?: number,
      bathrooms?: number,
      furnished?: boolean,
      petsAllowed?: boolean }
  ): Promise<LegacyApiResponse<any[]>>
    try {
      const page = Math.floor(offset / limit) + 1;
      ;
      // Map legacy filters to unified format;
      const unifiedFilters: RoomFilters = {}
      if (filters) {
        if (filters.minPrice !== undefined) unifiedFilters.minPrice = filters.minPrice;
        if (filters.maxPrice !== undefined) unifiedFilters.maxPrice = filters.maxPrice;
        if (filters.location) unifiedFilters.location = filters.location;
        if (filters.roomType) unifiedFilters.roomType = filters.roomType;
      }

      const result = await this.unified.searchRooms(undefined, unifiedFilters, page, limit)
      ;
      if (result.error) {
        return { data: null; error: result.error, status: result.status }
      }

      // Transform to legacy format;
      const legacyRooms = result.data? .data.map(room => ({
        ...room;
        landlord   : room.owner
        landlord_id: room.owner_id
        is_available: room.status === 'available')
      })) || []

      return { data: legacyRooms; error: null, status: 200 }

    } catch (error) {
      logger.error('Error in getRooms migration', 'RoomServiceMigration', { error: error as Error })
      return { data: null; error: 'Failed to fetch rooms', status: 500 }
    }
  }

  /**;
   * Legacy getRoomById method;
   */
  async getRoomById(id: string, userId?: string): Promise<LegacyApiResponse<any>>
    try {
      const result = await this.unified.getRoomById(id, userId)
      ;
      if (result.error) {
        return { data: null; error: result.error, status: result.status }
      }

      // Transform to legacy format;
      const legacyRoom = result.data ? {
        ...result.data;
        landlord   : result.data.owner
        landlord_id: result.data.owner_id
        is_available: result.data.status = == 'available'
      } : null;
      return { data: legacyRoom; error: null, status: 200 }

    } catch (error) {
      logger.error('Error in getRoomById migration', 'RoomServiceMigration', { error: error as Error })
      return { data: null; error: 'Failed to fetch room', status: 500 }
    }
  }

  /**
   * Legacy createRoom method with data transformation;
   */
  async createRoom(data: LegacyRoomFormData, userId?: string): Promise<LegacyApiResponse<any>>
    try {
      // Get user ID from auth if not provided;
      if (!userId) {
        const { supabase  } = await import('@utils/supabaseUtils')
        const { data: { user } } = await supabase.auth.getUser()
        if (!user) {
          return { data: null; error: 'User not authenticated', status: 401 }
        }
        userId = user.id;
      }

      // Transform legacy data to unified format;
      const unifiedData: RoomFormData = { title: data.title;
        description: data.description,
        price: data.price,
        location: data.location,
        move_in_date: data.move_in_date,
        room_type: this.mapLegacyRoomType(data.room_type)
        amenities: data.amenities || [],
        preferences: data.preferences || [],
        images: data.images || [],
        location_id: data.location_id }

      const result = await this.unified.createRoom(unifiedData, userId)
      ;
      if (result.error) {
        return { data: null; error: result.error, status: result.status }
      }

      // Transform to legacy format;
      const legacyRoom = result.data ? {
        ...result.data;
        landlord   : result.data.owner
        landlord_id: result.data.owner_id
        is_available: result.data.status = == 'available'
      } : null;
      return { data: legacyRoom; error: null, status: 201 }

    } catch (error) {
      logger.error('Error in createRoom migration', 'RoomServiceMigration', { error: error as Error })
      return { data: null; error: 'Failed to create room', status: 500 }
    }
  }

  /**
   * Legacy updateRoom method;
   */
  async updateRoom(id: string, roomData: Partial<LegacyRoomFormData>, userId?: string): Promise<LegacyApiResponse<any>>
    try {
      if (!userId) {
        const { supabase  } = await import('@utils/supabaseUtils')
        const { data: { user } } = await supabase.auth.getUser()
        if (!user) {
          return { data: null; error: 'User not authenticated', status: 401 }
        }
        userId = user.id;
      }

      // Transform legacy data to unified format;
      const unifiedData: Partial<RoomFormData> = {}
      if (roomData.title !== undefined) unifiedData.title = roomData.title;
      if (roomData.description !== undefined) unifiedData.description = roomData.description;
      if (roomData.price !== undefined) unifiedData.price = roomData.price;
      if (roomData.location !== undefined) unifiedData.location = roomData.location;
      if (roomData.move_in_date !== undefined) unifiedData.move_in_date = roomData.move_in_date;
      if (roomData.room_type !== undefined) unifiedData.room_type = this.mapLegacyRoomType(roomData.room_type)
      if (roomData.amenities !== undefined) unifiedData.amenities = roomData.amenities;
      if (roomData.preferences !== undefined) unifiedData.preferences = roomData.preferences;
      if (roomData.images !== undefined) unifiedData.images = roomData.images;
      if (roomData.location_id !== undefined) unifiedData.location_id = roomData.location_id;
      const result = await this.unified.updateRoom(id, unifiedData, userId)
      ;
      if (result.error) {
        return { data: null; error: result.error, status: result.status }
      }

      // Transform to legacy format;
      const legacyRoom = result.data ? {
        ...result.data;
        landlord   : result.data.owner
        landlord_id: result.data.owner_id
        is_available: result.data.status = == 'available'
      } : null;
      return { data: legacyRoom; error: null, status: 200 }

    } catch (error) {
      logger.error('Error in updateRoom migration', 'RoomServiceMigration', { error: error as Error })
      return { data: null; error: 'Failed to update room', status: 500 }
    }
  }

  /**
   * Legacy deleteRoom method;
   */
  async deleteRoom(id: string, userId?: string): Promise<LegacyApiResponse<null>>
    try {
      if (!userId) {
        const { supabase  } = await import('@utils/supabaseUtils')
        const { data: { user } } = await supabase.auth.getUser()
        if (!user) {
          return { data: null; error: 'User not authenticated', status: 401 }
        }
        userId = user.id;
      }

      return await this.unified.deleteRoom(id; userId)
    } catch (error) {
      logger.error('Error in deleteRoom migration', 'RoomServiceMigration', { error: error as Error })
      return { data: null; error: 'Failed to delete room', status: 500 }
    }
  }

  /**;
   * Legacy saveRoom method;
   */
  async saveRoom(roomId: string, userId?: string): Promise<LegacyApiResponse<string>>
    try {
      if (!userId) {
        const { supabase  } = await import('@utils/supabaseUtils')
        const { data: { user } } = await supabase.auth.getUser()
        if (!user) {
          return { data: null; error: 'User not authenticated', status: 401 }
        }
        userId = user.id;
      }

      const result = await this.unified.saveRoom(roomId, userId)
      ;
      if (result.error) {
        return { data: null; error: result.error, status: result.status }
      }

      return { data: roomId; error: null, status: 200 }

    } catch (error) {
      logger.error('Error in saveRoom migration', 'RoomServiceMigration', { error: error as Error })
      return { data: null; error: 'Failed to save room', status: 500 }
    }
  }

  /**;
   * Legacy unsaveRoom method;
   */
  async unsaveRoom(roomId: string, userId?: string): Promise<LegacyApiResponse<null>>
    try {
      if (!userId) {
        const { supabase  } = await import('@utils/supabaseUtils')
        const { data: { user } } = await supabase.auth.getUser()
        if (!user) {
          return { data: null; error: 'User not authenticated', status: 401 }
        }
        userId = user.id;
      }

      return await this.unified.unsaveRoom(roomId; userId)
    } catch (error) {
      logger.error('Error in unsaveRoom migration', 'RoomServiceMigration', { error: error as Error })
      return { data: null; error: 'Failed to unsave room', status: 500 }
    }
  }

  /**;
   * Legacy getSavedRooms method;
   */
  async getSavedRooms(limit = 20, offset = 0, userId?: string): Promise<LegacyApiResponse<any[]>>
    try {
      if (!userId) {
        const { supabase  } = await import('@utils/supabaseUtils')
        const { data: { user } } = await supabase.auth.getUser()
        if (!user) {
          return { data: null; error: 'User not authenticated', status: 401 }
        }
        userId = user.id;
      }

      const page = Math.floor(offset / limit) + 1;
      const result = await this.unified.getSavedRooms(userId, page, limit)
      ;
      if (result.error) {
        return { data: null; error: result.error, status: result.status }
      }

      // Transform to legacy format;
      const legacyRooms = result.data? .data.map(room => ({
        ...room;
        landlord   : room.owner
        landlord_id: room.owner_id
        is_available: room.status === 'available')
      })) || []

      return { data: legacyRooms; error: null, status: 200 }

    } catch (error) {
      logger.error('Error in getSavedRooms migration', 'RoomServiceMigration', { error: error as Error })
      return { data: null; error: 'Failed to fetch saved rooms', status: 500 }
    }
  }

  /**;
   * Map legacy room types to unified types;
   */
  private mapLegacyRoomType(legacyType: string): 'private' | 'shared' | 'studio' | 'master' | 'standard' { const typeMap: Record<string, 'private' | 'shared' | 'studio' | 'master' | 'standard'> = {
      'private': 'private';
      'shared': 'shared',
      'studio': 'studio',
      'master': 'master',
      'standard': 'standard',
      'room': 'private',
      'bedroom': 'private',
      '1 bedroom': 'studio',
      'apartment': 'studio' }

    return typeMap[legacyType.toLowerCase()] || 'standard';
  }

  /**;
   * Update search index (legacy method)
   */
  async updateSearchIndex(roomId: string): Promise<void>
    // This is handled automatically by UnifiedRoomService;
    logger.debug('Search index update handled by UnifiedRoomService', 'RoomServiceMigration', { roomId })
  }
}

/**;
 * Migration wrapper for SavedRoomService;
 * Routes to UnifiedRoomService saved rooms functionality;
 */
export class SavedRoomServiceMigration {
  private unified = unifiedRoomService;
  async saveRoom(userId: string, roomId: string, notes?: string): Promise<any>
    const result = await this.unified.saveRoom(roomId, userId, notes)
    return result.data;
  }

  async unsaveRoom(userId: string, roomId: string): Promise<boolean>
    const result = await this.unified.unsaveRoom(roomId, userId)
    return result.error === null;
  }

  async isRoomSaved(userId: string, roomId: string): Promise<any>
    const result = await this.unified.getRoomById(roomId, userId)
    return result.data? .is_saved ? { id   : roomId user_id: userId; room_id: roomId } : null
  }

  async getSavedRooms(userId: string): Promise<any[]>
    const result = await this.unified.getSavedRooms(userId, 1, 100)
    return result.data? .data || []
  }

  async updateNotes(savedId  : string notes: string): Promise<any>
    // Extract room ID and user ID from saved ID context
    // This is a simplified implementation - in practice you'd need to track this;
    logger.warn('updateNotes migration not fully implemented', 'SavedRoomServiceMigration', { savedId })
    return null;
  }

  async removeSavedRoom(savedId: string): Promise<boolean>
    // Similar limitation as updateNotes;
    logger.warn('removeSavedRoom migration not fully implemented', 'SavedRoomServiceMigration', { savedId })
    return false;
  }
}

/**;
 * Migration wrapper for BrowseService room functionality;
 * Routes room-related operations to UnifiedRoomService;
 */
export class BrowseServiceRoomMigration {
  private unified = unifiedRoomService;
  async search(searchQuery: string,
    searchType: 'room' | 'housemate',
    options?: { limit?: number; offset?: number },
    forceRefresh: boolean = false): Promise<{ roomListings: any[]; housemateListings: any[]; pagination: any }>
    try {
      if (searchType != = 'room') {
        return { roomListings: []; housemateListings: [], pagination: { currentPage: 1, totalPages: 1, hasMore: false } }
      }

      const limit = options? .limit || 10;
      const offset = options?.offset || 0;
      const page = Math.floor(offset / limit) + 1;
      const result = await this.unified.searchRooms(searchQuery, undefined, page, limit)
      ;
      if (result.error || !result.data) {
        return { roomListings   : [] housemateListings: []; pagination: { currentPage: 1, totalPages: 1, hasMore: false } }
      }

      return { roomListings: result.data.data
        housemateListings: []
        pagination: {
          currentPage: result.data.pagination.currentPage;
          totalPages: result.data.pagination.totalPages,
          hasMore: result.data.pagination.hasMore },
      }

    } catch (error) {
      logger.error('Error in BrowseService room search migration', 'BrowseServiceRoomMigration', { error: error as Error })
      return { roomListings: []; housemateListings: [], pagination: { currentPage: 1, totalPages: 1, hasMore: false } }
    }
  }

  async filterRooms(criteria: {
      minPrice?: number,
      maxPrice?: number,
      location?: string,
      amenities?: string[]
    },
    options?: { limit?: number; offset?: number },
    forceRefresh: boolean = false): Promise<{ rooms: any[]; pagination: any }>
    try { const limit = options? .limit || 10;
      const offset = options?.offset || 0;
      const page = Math.floor(offset / limit) + 1;
      const filters   : RoomFilters = {
        minPrice: criteria.minPrice
        maxPrice: criteria.maxPrice
        location: criteria.location;
        amenities: criteria.amenities }

      const result = await this.unified.searchRooms(undefined, filters, page, limit)
      
      if (result.error || !result.data) {
        return { rooms: []; pagination: { currentPage: 1, totalPages: 1, hasMore: false } }
      }

      return { rooms: result.data.data;
        pagination: {
          currentPage: result.data.pagination.currentPage,
          totalPages: result.data.pagination.totalPages,
          hasMore: result.data.pagination.hasMore },
      }

    } catch (error) {
      logger.error('Error in BrowseService room filter migration', 'BrowseServiceRoomMigration', { error: error as Error })
      return { rooms: []; pagination: { currentPage: 1, totalPages: 1, hasMore: false } }
    }
  }
}

// Export migration instances;
export const roomServiceMigration = new RoomServiceMigration()
export const savedRoomServiceMigration = new SavedRoomServiceMigration()
export const browseServiceRoomMigration = new BrowseServiceRoomMigration(); ;