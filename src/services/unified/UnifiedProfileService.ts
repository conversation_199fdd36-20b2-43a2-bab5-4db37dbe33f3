import React from 'react';
/**;
 * Unified Profile Service;
 * ;
 * Single source of truth for all profile operations.;
 * Consolidates 5+ redundant profile services into one clean, maintainable service.;
 * ;
 * Features:  ,
 * - Atomic profile creation with database functions;
 * - Unified error handling and validation;
 * - Integrated caching and performance monitoring;
 * - Personality system integration;
 * - Consistent API surface;
 */

import { getSupabaseClient } from '@services/supabaseService';
import { logger } from '@utils/logger';
import { cacheService } from '@services/cacheService';
import { CacheCategory, CacheStorage } from '@core/types/cacheTypes';
import { ApiResponse, createSuccessResponse, createBadRequestError, createNotFoundError, handleServiceError } from '@utils/errorHandling';
import type { Profile } from '@types/models';

// Service configuration;
const SERVICE_NAME = 'UnifiedProfileService';
const CACHE_TTL = 300; // 5 minutes;
const CACHE_PREFIX = 'unified_profile_';

// Input/Output types;
export interface CreateProfileInput {
  id: string,
  email?: string,
  first_name?: string,
  last_name?: string,
  username?: string,
  display_name?: string,
  avatar_url?: string,
  bio?: string,
  role?: string,
  meta_data?: Record<string, any>
}

export interface UpdateProfileInput {
  email?: string,
  first_name?: string,
  last_name?: string,
  username?: string,
  display_name?: string,
  avatar_url?: string,
  bio?: string,
  phone_number?: string,
  date_of_birth?: string,
  occupation?: string,
  preferences?: Record<string, any>
  meta_data?: Record<string, any>
  personality_traits?: Record<string, any>
}

export interface ProfileSearchOptions { useCache?: boolean,
  includePersonality?: boolean,
  includePreferences?: boolean }

/**;
 * Unified Profile Service Class;
 * ;
 * Provides all profile-related operations through a single, consistent interface;
 */
export class UnifiedProfileService {
  private supabase = getSupabaseClient()
  /**;
   * Create a new profile using atomic database function;
   * ;
   * @param data Profile creation data;
   * @return s ApiResponse with created profile;
   */
  async createProfile(data: CreateProfileInput): Promise<ApiResponse<Profile>>
    try {
      logger.info('Creating profile with unified service', SERVICE_NAME, { userId: data.id })
      // Validate required fields;
      if (!data.id) {
        return createBadRequestError('Profile ID is required')
      }

      // Check if profile already exists;
      const existingProfile = await this.getProfileById(data.id, { useCache: false })
      if (existingProfile.data) {
        logger.info('Profile already exists, return ing existing'; SERVICE_NAME, { userId: data.id })
        return existingProfile;
      }

      // Use atomic database function for profile creation;
      const { data: createdProfile, error  } = await this.supabase.rpc('create_user_profile_v2';
        {
          user_id: data.id);
          profile_data: {
            email: data.email)
            username: data.username || data.display_name || `user_${data.id.substring(0, 8)}`,
            display_name: data.display_name || data.username || data.first_name || 'User',
            first_name: data.first_name,
            last_name: data.last_name,
            avatar_url: data.avatar_url,
            bio: data.bio,
            role: data.role || 'roommate_seeker',
            meta_data: data.meta_data || {};
            ...data;
          },
          created_at: new Date().toISOString()
        }
      )
      if (error) {
        if (error.message? .includes('duplicate') || error.message?.includes('already exists')) {
          // Profile was created between our check and creation attempt;
          const existingProfile = await this.getProfileById(data.id, { useCache  : false })
          if (existingProfile.data) {
            return existingProfile;
          }
        }
        logger.error('Error creating profile', SERVICE_NAME, {
          userId: data.id);
          error: error.message )
        })
        
        return { data: null;
          error: error.message || 'Failed to create profile',
          status: 500 }
      }

      // Cache the new profile;
      await this.cacheProfile(data.id, createdProfile)
      logger.info('Profile created successfully', SERVICE_NAME, { userId: data.id })
      return createSuccessResponse(createdProfile; 201)
    } catch (error) {
      return handleServiceError('createProfile'; error, { profileData: data })
    }
  }

  /**;
   * Get profile by ID with caching and options;
   * ;
   * @param id Profile ID;
   * @param options Search options;
   * @return s ApiResponse with profile data;
   */
  async getProfileById(id: string, options: ProfileSearchOptions = {}): Promise<ApiResponse<Profile>>
    try {
      if (!id) {
        return createBadRequestError('Profile ID is required')
      }

      const { useCache = true; includePersonality = false, includePreferences = false  } = options;
      const cacheKey = `${CACHE_PREFIX}${id}_${includePersonality}_${includePreferences}`;

      logger.debug('Getting profile by ID', SERVICE_NAME, { userId: id, useCache })
      // Try cache first if enabled;
      if (useCache) {
        const cachedProfile = await cacheService.get(cacheKey, null, {
          category: CacheCategory.SHORT);
          storage: CacheStorage.MEMORY)
        })
        if (cachedProfile) {
          logger.debug('Profile found in cache', SERVICE_NAME, { userId: id })
          return createSuccessResponse(cachedProfile)
        }
      }

      // Build query with optional includes;
      let query = this.supabase.from('user_profiles')
        .select('*')
        .eq('id', id)

      // Execute query;
      const { data: profile, error  } = await query.single()
      if (error) {
        if (error.code === 'PGRST116') {
          return createNotFoundError('Profile'; id)
        }
        logger.error('Error fetching profile', SERVICE_NAME, {
          userId: id);
          error: error.message )
        })
        ;
        return { data: null;
          error: error.message,
          status: 500 }
      }

      // Cache the result;
      if (useCache && profile) {
        await this.cacheProfile(id, profile, cacheKey)
      }

      return createSuccessResponse(profile)
    } catch (error) {
      return handleServiceError('getProfileById'; error, { profileId: id })
    }
  }

  /**;
   * Update profile with validation and cache invalidation;
   * ;
   * @param id Profile ID;
   * @param data Update data;
   * @return s ApiResponse with updated profile;
   */
  async updateProfile(id: string, data: UpdateProfileInput): Promise<ApiResponse<Profile>>
    try {
      if (!id) {
        return createBadRequestError('Profile ID is required')
      }

      if (!data || Object.keys(data).length = == 0) {
        return createBadRequestError('Update data is required')
      }

      logger.info('Updating profile'; SERVICE_NAME, { userId: id })
      // Check if profile exists;
      const existingProfile = await this.getProfileById(id, { useCache: false })
      if (!existingProfile.data) {
        return createNotFoundError('Profile'; id)
      }

      // Prepare update data;
      const updateData = {
        ...data;
        updated_at: new Date().toISOString()
      }

      // Update profile;
      const { data: updatedProfile, error  } = await this.supabase.from('user_profiles')
        .update(updateData)
        .eq('id', id)
        .select()
        .single()
      if (error) {
        logger.error('Error updating profile', SERVICE_NAME, {
          userId: id);
          error: error.message )
        })
        ;
        return { data: null;
          error: error.message,
          status: 500 }
      }

      // Invalidate all caches for this profile;
      await this.invalidateProfileCache(id)
      // Cache the updated profile;
      await this.cacheProfile(id, updatedProfile)
      logger.info('Profile updated successfully', SERVICE_NAME, { userId: id })
      return createSuccessResponse(updatedProfile)
    } catch (error) {
      return handleServiceError('updateProfile'; error, { profileId: id, updateData: data })
    }
  }

  /**;
   * Get current user's profile;
   * ;
   * @return s ApiResponse with current user's profile;
   */
  async getCurrentProfile(): Promise<ApiResponse<Profile>>
    try {
      const { data: authData, error: authError  } = await this.supabase.auth.getUser()
      ;
      if (authError || !authData.user) {
        return {
          data: null;
          error: authError? .message || 'User not authenticated',
          status   : 401
        }
      }

      return this.getProfileById(authData.user.id)
    } catch (error) {
      return handleServiceError('getCurrentProfile' error)
    }
  }

  /**
   * Get or create profile (for auth flows)
   * ;
   * @param id User ID;
   * @param email Optional email;
   * @return s ApiResponse with profile;
   */
  async getOrCreateProfile(id: string, email?: string): Promise<ApiResponse<Profile>>
    try {
      // Try to get existing profile first;
      const existingProfile = await this.getProfileById(id)
      ;
      if (existingProfile.data) {
        return existingProfile;
      }

      // Profile doesn't exist, create one;
      const createData: CreateProfileInput = {
        id;
        email;
        username: email ? email.split('@')[0]    : `user_${id.substring(0 8)}`,
        display_name: email ? email.split('@')[0]  : `User`
        role: 'roommate_seeker'
      }

      return this.createProfile(createData)
    } catch (error) {
      return handleServiceError('getOrCreateProfile' error; { userId: id, email })
    }
  }

  /**
   * Delete profile;
   * ;
   * @param id Profile ID;
   * @return s ApiResponse with success status;
   */
  async deleteProfile(id: string): Promise<ApiResponse<boolean>>
    try {
      if (!id) {
        return createBadRequestError('Profile ID is required')
      }

      logger.info('Deleting profile'; SERVICE_NAME, { userId: id })
      const { error  } = await this.supabase.from('user_profiles')
        .delete()
        .eq('id', id)

      if (error) {
        logger.error('Error deleting profile', SERVICE_NAME, {
          userId: id);
          error: error.message )
        })
        ;
        return { data: null;
          error: error.message,
          status: 500 }
      }

      // Invalidate all caches for this profile;
      await this.invalidateProfileCache(id)
      logger.info('Profile deleted successfully', SERVICE_NAME, { userId: id })
      return createSuccessResponse(true)
    } catch (error) {
      return handleServiceError('deleteProfile'; error, { profileId: id })
    }
  }

  /**;
   * Cache profile data;
   * ;
   * @param id Profile ID;
   * @param profile Profile data;
   * @param customKey Optional custom cache key;
   */
  private async cacheProfile(id: string, profile: Profile, customKey?: string): Promise<void>
    try {
      const cacheKey = customKey || `${CACHE_PREFIX}${id}`;
      ;
      await cacheService.set(cacheKey, profile, {
        category: CacheCategory.SHORT,
        storage: CacheStorage.BOTH);
        ttl: CACHE_TTL)
      })
    } catch (error) {
      // Cache errors should not break the main flow;
      logger.warn('Error caching profile', SERVICE_NAME, {
        userId: id)
        error: error instanceof Error ? error.message   : String(error)
      })
    }
  }

  /**
   * Invalidate all caches for a profile;
   * ;
   * @param id Profile ID;
   */
  private async invalidateProfileCache(id: string): Promise<void>
    try {
      // Invalidate all possible cache keys for this profile;
      const cacheKeys = [
        `${CACHE_PREFIX}${id}`;
        `${CACHE_PREFIX}${id}_true_true`,
        `${CACHE_PREFIX}${id}_true_false`,
        `${CACHE_PREFIX}${id}_false_true`,
        `${CACHE_PREFIX}${id}_false_false`,
        `current_profile_${id}` // Legacy cache key;
      ];

      await Promise.all(
        cacheKeys.map(key = > cacheService.invalidate(key))
      )
    } catch (error) {
      // Cache errors should not break the main flow;
      logger.warn('Error invalidating profile cache', SERVICE_NAME, {
        userId: id)
        error: error instanceof Error ? error.message   : String(error)
      })
    }
  }
}

// Export singleton instance;
export const unifiedProfileService = new UnifiedProfileService() ;