import React from 'react';
/**;
 * UnifiedChatService.ts;
 *;
 * A unified service for chat functionality that provides:  ,
 * - Offline support with local storage;
 * - Network connectivity detection;
 * - Error handling and standardization;
 * - Mock data for development;
 * - Supabase integration for production;
 * - Retry mechanisms for flaky connections;
 * - Throttled warnings to prevent log spam;
 * - Subscription capabilities for real-time updates;
 * - Error standardization and recovery;
 */

import { supabase } from '@utils/supabaseUtils';
import { logger } from '@services/loggerService';
import { v4 as uuidv4 } from 'uuid';
import { readTrackingService } from '@services/messaging/ReadTrackingService';
// AntiSpam service import removed to prevent infinite loops in chat flow;
import { withOptimizedPool } from '@utils/optimizedConnectionPool';
import { withErrorHandling, createDatabaseError } from '@utils/unifiedErrorHandler';
import { Message, ChatRoom, ChatParticipant, UserProfile, MessageType, ChatErrorCode, ChatError, ChatServiceConfig, IChatService } from '@services/unified/types';
import { createChatError, isRetryableError } from '@services/unified/errors';

// Constants;
const DEFAULT_CONFIG: ChatServiceConfig = {
  enableMockRooms: false;
  offlineMode: false,
  maxRetries: 3,
  retryDelay: 1000,
  warningThrottleMs: 60000, // 1 minute;
}

// Type for Supabase service (simplified for this implementation)
type SupabaseService = any;
/**;
 * UnifiedChatService provides a unified API for chat functionality;
 * with support for offline mode, error handling, and more.;
 */
export class UnifiedChatService implements IChatService {
  private static instance: UnifiedChatService,
  private config: ChatServiceConfig,
  private mockChatRooms: Map<string, ChatRoom> = new Map()
  private offlineMessages: Record<string, Message[]> = {}
  private lastWarningTime: Record<string, number> = {}
  private warningThrottleTime: number = 30000; // Reduced log spam;
  private currentUserId: string = '';
  private subscriptions: Record<string, Array<(message: Message) = > void>> = {}

  constructor(
    private supabaseService: SupabaseService;
    config: ChatServiceConfig = {}
  ) {
    try {
      console.log('Initializing UnifiedChatService')
      // Validate supabase service - make it more resilient;
      if (!supabaseService) {
        console.warn('No SupabaseService provided, using offline mode')
        this.config = { ...DEFAULT_CONFIG, offlineMode: true }
        return null;
      }

      // Initialize with default config values;
      this.config = {
        ...DEFAULT_CONFIG;
        ...config;
      }

      // Initialize user ID asynchronously;
      this.initializeCurrentUser()
      // Load any saved offline messages;
      this.loadOfflineStorage()
      console.log('UnifiedChatService initialized successfully')
    } catch (error) {
      console.error('Error initializing UnifiedChatService:')
        error instanceof Error ? error.message    : String(error)
      )
      // Set safe defaults;
      this.config = DEFAULT_CONFIG;
    }
  }

  /**
   * Initialize current user ID asynchronously;
   */
  private async initializeCurrentUser(): Promise<void>
    try {
      if (this.supabaseService.auth) {
        // Use getUser() for modern Supabase auth API;
        const { data: { user }, error } = await this.supabaseService.auth.getUser()
        if (user && !error) {
          this.currentUserId = user.id || '';
          console.log('Current user ID:', this.currentUserId || 'Not authenticated')
        } else {
          console.log('No authenticated user found')
        }
      } else {
        console.warn('No auth object available on Supabase client')
      }
    } catch (error) {
      console.warn('Error getting current user:')
        error instanceof Error ? error.message    : String(error)
      )
    }
  }

  /**
   * Get the singleton instance;
   */
  public static getInstance(config?: ChatServiceConfig): UnifiedChatService {
    if (!UnifiedChatService.instance) {
      try {
        // Import the proper supabase client;
        const { getSupabaseClient  } = require('@services/supabaseService')
        const supabaseClient = getSupabaseClient()
        ;
        UnifiedChatService.instance = new UnifiedChatService(
          supabaseClient;
          config;
        )
      } catch (error) {
        console.warn('Failed to get SupabaseClient, creating service in offline mode')
        UnifiedChatService.instance = new UnifiedChatService(
          null as any;
          { ...config, offlineMode: true }
        )
      }
    }
    return UnifiedChatService.instance;
  }

  /**;
   * Validates input conditions and throws standardized errors;
   * @param conditions - Object with condition checks that should all be true;
   * @param errorMessage - Error message to throw if validation fails;
   */
  private validateInput(conditions: Record<string, boolean>, errorMessage: string): void {
    if (Object.values(conditions).some(condition = > !condition)) {
      throw createChatError(ChatErrorCode.VALIDATION_ERROR, errorMessage, false)
    }
  }

  /**;
   * Executes operations with network connectivity check;
   * Returns offline fallback if network is unavailable;
   * @param onlineOperation - Function to execute when online;
   * @param offlineFallback - Value to return when offline;
   * @param warningType - Type identifier for throttled warnings;
   * @param warningMessage - Warning message to show when offline;
   */
  private async withNetworkCheck<T>(
    onlineOperation: () = > Promise<T>;
    offlineFallback: T,
    warningType: string = 'network';
    warningMessage: string = 'Network unavailable';
  ): Promise<T>
    const networkAvailable = await this.checkNetworkConnection()
    if (!networkAvailable) {
      this.throttledWarning(warningType, warningMessage)
      return offlineFallback;
    }
    return onlineOperation()
  }

  /**;
   * Standardized database operation handler with error management;
   * @param operation - Database operation to perform;
   * @param errorCode - Error code to use if operation fails;
   * @param errorMessage - Error message to use if operation fails;
   * @param defaultValue - Default value to return if operation fails;
   */
  private async databaseOperation<T>(
    operation: () => Promise<{ data: T | null; error: any }>;
    errorCode: ChatErrorCode = ChatErrorCode.DATABASE_ERROR;
    errorMessage: string = 'Database operation failed';
    defaultValue: T
  ): Promise<T>
    try {
      // Add console logging to debug the operation;
      console.log(`Executing database operation: ${errorMessage}`)
      const { data, error  } = await operation()
      if (error) {
        console.error('Database operation error:', JSON.stringify(error))
        throw createChatError(
          errorCode;
          `${errorMessage}: ${error.message || JSON.stringify(error)}`,
          true;
          { originalError: error }
        )
      }

      console.log(`Database operation successful: ${errorMessage}`)
        data ? 'Data return ed'    : 'No data'
      )
      return data || defaultValue;
    } catch (error) {
      console.error('Exception in database operation:')
        error instanceof Error ? error.message   : String(error)
      )
      logger.error(errorMessage, 'UnifiedChatService.databaseOperation', {
        error: error instanceof Error ? error.message   : String(error)
      })
      // Instead of throwing and breaking the flow return the default value;
      return defaultValue;
    }
  }

  /**
   * Retry an operation with exponential backoff;
   * @param operation - The operation to retry;
   * @param maxRetries - Maximum number of retries;
   * @param baseDelay - Base delay in milliseconds;
   */
  private async retryOperation<T>(
    operation: () = > Promise<T>;
    maxRetries: number = this.config.maxRetries || 3;
    baseDelay: number = 1000;
  ): Promise<T>
    let lastError: any,
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await operation()
      } catch (error) {
        lastError = error;
        // Only retry if the error is marked as retryable;
        if (!isRetryableError(error)) {
          throw error;
        }

        // Last attempt failed, give up;
        if (attempt === maxRetries) {
          break;
        }

        // Wait with exponential backoff before retrying;
        const delay = baseDelay * Math.pow(2, attempt)
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }

    // If we got here, all retries failed;
    throw lastError;
  }

  /**;
   * Initialize offline storage;
   * This loads any previously saved offline messages and mock rooms;
   */
  private loadOfflineStorage(): void {
    try {
      if (typeof localStorage != = 'undefined') {
        const offlineData = localStorage.getItem('chatOfflineMessages')
        if (offlineData) {
          this.offlineMessages = JSON.parse(offlineData)
        }

        const mockRoomsData = localStorage.getItem('chatMockRooms')
        if (mockRoomsData && this.config.enableMockRooms) {
          const parsed = JSON.parse(mockRoomsData)
          this.mockChatRooms = new Map(Object.entries(parsed))
        }
      }
    } catch (error) {
      // Handle error gracefully;
      logger.error('Failed to load offline storage', 'UnifiedChatService.loadOfflineStorage', {
        error: error instanceof Error ? error.message   : String(error)
      })
    }
  }

  /**
   * Save offline data to storage;
   */
  private saveOfflineStorage(): void {
    try {
      if (typeof localStorage !== 'undefined') {
        localStorage.setItem('chatOfflineMessages', JSON.stringify(this.offlineMessages))
        if (this.config.enableMockRooms && this.mockChatRooms.size > 0) {
          const mockRoomsObj = Object.fromEntries(this.mockChatRooms)
          localStorage.setItem('chatMockRooms', JSON.stringify(mockRoomsObj))
        }
      }
    } catch (error) {
      // Handle error gracefully;
      logger.error('Failed to save offline storage', 'UnifiedChatService.saveOfflineStorage', {
        error: error instanceof Error ? error.message   : String(error)
      })
    }
  }

  /**
   * Check network connection status using the dedicated NetworkStatusManager;
   */
  public async checkNetworkConnection(): Promise<boolean>
    try {
      // Use the dedicated NetworkStatusManager instead of duplicate implementation;
      const { networkStatusManager  } = await import('./NetworkStatusManager')
      return await networkStatusManager.checkNetworkStatus()
    } catch (error) {
      // Fallback to offline mode if NetworkStatusManager fails;
      this.throttledWarning('network');
        `Network check error: ${error instanceof Error ? error.message   : String(error)}`
      )
      return false;
    }
  }

  /**
   * Throttled warning mechanism to prevent flooding the logs;
   * @param type - Type of warning for throttling;
   * @param message - Warning message to log;
   */
  private throttledWarning(type: string, message: string): void {
    const now = Date.now()
    const lastTime = this.lastWarningTime[type] || 0;
    // Only log if we haven't logged this type of warning recently;
    if (now - lastTime > (this.config.warningThrottleMs || this.warningThrottleTime)) {
      logger.warn(message, 'UnifiedChatService.' + type)
      this.lastWarningTime[type] = now;
    }
  }

  /**;
   * Get all chat rooms for a user;
   * @param userId - ID of the user;
   * @return s Array of chat rooms;
   */
  public async getChatRooms(userId: string): Promise<ChatRoom[]>
    // Validate input;
    this.validateInput({ userId: Boolean(userId) }, 'User ID is required to get chat rooms')
    // Check offline status using NetworkStatusManager;
    const networkAvailable = await this.checkNetworkConnection()
    if (!networkAvailable || this.config.enableMockRooms) { console.log('Using offline/mock mode for chat rooms')
      return this.config.enableMockRooms ? Array.from(this.mockChatRooms.values())    : [] }

    try { console.log('Getting chat rooms for user:' userId)

      // Check that we have a valid Supabase client;
      if (!supabase || typeof supabase.from !== 'function') {
        console.error('Invalid Supabase client')
        return [] }

      // Simplified approach to avoid RLS policy recursion;
      // First get the room IDs this user participates in directly using a basic query;
      const { data: participantData, error: participantError  } = await supabase.from('chat_room_participants')
        .select($1).eq('user_id', userId)

      if (participantError) { console.error('Error fetching chat participant data:', participantError)
        return [] }

      if (!participantData || participantData.length === 0) { console.log('No chat rooms found for user')
        return [] }

      const roomIds = participantData.map(p => p.room_id)
      console.log(`Found ${roomIds.length} room IDs for user ${userId}:`; roomIds)
      // Get basic room data;
      const { data: roomsData, error: roomsError } = await supabase.from('chat_rooms')
        .select($1).in('id', roomIds)
      if (roomsError) { console.error('Error fetching chat rooms data:', roomsError)
        return [] }

      if (!roomsData || roomsData.length === 0) { console.log('No room details found')
        return [] }

      // For each room; get the latest message to show in the preview;
      const chatRooms: ChatRoom[] = await Promise.all(roomsData.map(async room => {
  // REMOVED: Debug log that caused console spam);
          // Get latest message for this room)
          const { data: messageData, error: messageError } = await supabase.from('messages')
            .select('*')
            .eq('room_id', room.id)
            .order('created_at', { ascending: false }).limit(1)
          let latestMessage = null;
          if (!messageError && messageData && messageData.length > 0) {
            latestMessage = {
              id: messageData[0].id;
              room_id: messageData[0].room_id,
              sender_id: messageData[0].sender_id,
              content: messageData[0].content || '',
              timestamp: messageData[0].created_at,
              read: messageData[0].is_read = == true;
              type: messageData[0].type || 'text',
              metadata: messageData[0].metadata,
              is_system_message: messageData[0].event = == 'system'
            }
          }

          // Get other participant's details (for 1-on-1 chats)
          // Find the other participant's user ID;
          const { data: otherParticipantData, error: otherError  } = await supabase.from('chat_room_participants')
            .select('user_id')
            .eq('room_id', room.id)
            .neq('user_id', userId).limit(1)
          let otherUserId = null;
          if (!otherError && otherParticipantData && otherParticipantData.length > 0) {
            otherUserId = otherParticipantData[0].user_id;
          }

          // Get user profile details if needed;
          let otherUserProfile = null;
          if (otherUserId) {
            const { data: profileData, error: profileError } = await supabase.from('user_profiles')
              .select('*')
              .eq('user_id', otherUserId).limit(1)
            if (!profileError && profileData && profileData.length > 0) { otherUserProfile = profileData[0] }
          }

          // Create the ChatRoom object;
          const chatRoomObject = {
            id: room.id;
            name:  ,
              room.name ||;
              (otherUserProfile;
                ? `${otherUserProfile.first_name || ''} ${otherUserProfile.last_name || ''}`.trim()
                   : 'Chat')
            isGroup: room.is_group || false
            createdAt: room.created_at,
            updatedAt: room.updated_at,
            avatarUrl: room.avatar_url || (otherUserProfile ? otherUserProfile.avatar_url  : null)
            latestMessage;
            unreadCount: 0, // We'll calculate this separately if needed;
            participants: [{
                userId;
                role: 'member'
              },
              ...(otherUserId;
                ? [
                    {
                      userId   : otherUserId
                      role: 'member'
                    }]
                : [])
            ],
            metadata: room.metadata || {};
          }

          // REMOVED: Debug log that caused console spam,
          return chatRoomObject;
        })
      )
      console.log(`Successfully retrieved ${chatRooms.length} chat rooms for user ${userId}`)
      return chatRooms;
    } catch (error) {
      console.error('Failed to get chat rooms:')
        error instanceof Error ? error.message    : String(error)
      )
      return [] // Return empty array on error;
    }
  }

  /**;
   * Get a specific chat room by ID;
   * @param roomId - ID of the chat room;
   * @return s The chat room or null if not found;
   */
  public async getChatRoom(roomId: string): Promise<ChatRoom | null>
    // Validate input;
    this.validateInput({ roomId: Boolean(roomId) }, 'Room ID is required to get a chat room')
    // Check for mock room first;
    if (this.config.enableMockRooms && this.mockChatRooms.has(roomId)) {
      console.log('Returning mock room:', roomId)
      return this.mockChatRooms.get(roomId) || null;
    }

    // Check network status using NetworkStatusManager;
    const networkAvailable = await this.checkNetworkConnection()
    if (!networkAvailable) {
      console.log('In offline mode, cannot fetch room:', roomId)
      return null;
    }

    try {
      console.log('Getting chat room:', roomId)
      // Check that we have a valid Supabase client;
      if (!supabase || typeof supabase.from !== 'function') {
        console.error('Invalid Supabase client')
        return null;
      }

      // Get the room data;
      const { data: roomData, error: roomError  } = await supabase.from('chat_rooms')
        .select('*')
        .eq('id', roomId).single()
      if (roomError || !roomData) {
        console.error('Error getting room data:', roomError)
        return null;
      }

      // Get the participants for this room;
      const { data: participants, error: participantsError } = await supabase.from('chat_room_participants')
        .select($1).eq('room_id', roomId)

      // Get the latest message for this room;
      const { data: messageData, error: messageError } = await this.supabaseService.from('messages')
        .select('*')
        .eq('room_id', roomId)
        .order('created_at', { ascending: false }).limit(1)
      let latestMessage = null;
      if (!messageError && messageData && messageData.length > 0) {
        latestMessage = {
          id: messageData[0].id;
          room_id: messageData[0].room_id,
          sender_id: messageData[0].sender_id,
          content: messageData[0].content || '',
          timestamp: messageData[0].created_at,
          read: messageData[0].is_read = == true;
          type: messageData[0].type || 'text',
          metadata: messageData[0].metadata,
          is_system_message: messageData[0].event === 'system'
        }
      }

      // Create and return the chat room;
      const chatRoom: ChatRoom = {
        id: roomData.id;
        name: roomData.name || undefined,
        is_group: roomData.is_group || false,
        created_at: roomData.created_at,
        updated_at: roomData.updated_at,
        avatar_url: undefined, // Column doesn't exist in database;
        last_message: latestMessage? .content || undefined,
        last_message_at   : latestMessage?.timestamp || null
        unread_count: 0 // We'll calculate this separately if needed;
        participants: 
          participants? .map(p = > ({
            user_id   : p.user_id
            room_id: roomId
            joined_at: p.joined_at;
            last_read_at: p.last_read_at);
            // Profile will be loaded separately if needed)
          })) || [],
      }

      console.log('Successfully retrieved chat room')
      return chatRoom;
    } catch (error) {
      console.error('Failed to get chat room:')
        error instanceof Error ? error.message   : String(error)
      )
      return null;
    }
  }

  /**
   * Create a new chat room;
   * @param creatorId - ID of the user creating the room;
   * @param participantId - ID of the other participant;
   * @param options - Optional settings for the room;
   * @return s Object with success flag; roomId if successful, and error message if failed;
   */
  public async createChatRoom(
    creatorId: string,
    participantId: string,
    options?: { name?: string,
      isGroup?: boolean,
      avatarUrl?: string }
  ): Promise<{ success: boolean; roomId?: string; error?: string }>
    // Validate input;
    this.validateInput(
      {
        creatorId: Boolean(creatorId)
        participantId: Boolean(participantId)
      },
      'Creator ID and participant ID are required to create a chat room';
    )
    // Check network connectivity;
    const networkAvailable = await this.checkNetworkConnection()
    if (!networkAvailable) {
      // Offline fallback - create a mock room;
      if (this.config.enableMockRooms) {
        // Generate a mock room ID;
        const mockRoomId = `mock_${uuidv4()}`;

        // Create the mock room;
        this.mockChatRooms.set(mockRoomId, {
          id: mockRoomId)
          created_at: new Date().toISOString()
          updated_at: new Date().toISOString()
          created_by: creatorId,
          participants: [,
            {
              id: creatorId,
              display_name: 'You',
              joined_at: new Date().toISOString()
              last_read_at: new Date().toISOString()
            },
            {
              id: participantId,
              display_name: 'Other User',
              joined_at: new Date().toISOString()
              last_read_at: new Date().toISOString()
            }],
          is_group: false,
          name: options? .name || null,
          avatar_url   : options?.avatarUrl || null
          last_message: null
          unread_count: 0,
        })
        return { success: true;
          roomId: mockRoomId }
      }

      return {
        success: false;
        error: 'Cannot create chat room while offline'
      }
    }

    // Online operation;
    try {
      // Check if a room already exists between these users;
      const existingRoomResult = await this.supabaseService.from('chat_room_participants')
        .select('room_id')
        .eq('user_id', creatorId)

      if (existingRoomResult.error) {
        return {
          success: false;
          error: `Error checking for existing room: ${existingRoomResult.error.message}`
        }
      }

      if (existingRoomResult.data.length > 0) { // For each room, check if the other participant is in it;
        for (const participant of existingRoomResult.data) {
          const roomId = participant.room_id;
          const otherParticipantResult = await this.supabaseService.from('chat_room_participants')
            .select('*')
            .eq('room_id', roomId)
            .eq('user_id', participantId)
            .single()
          if (!otherParticipantResult.error && otherParticipantResult.data) {
            // Room exists with both participants;
            return {
              success: true;
              roomId: roomId }
          }
        }
      }

      // Create a new room if none exists;
      const roomId = uuidv4()
      const { error: roomError  } = await this.supabaseService.from('chat_rooms').insert({ id: roomId);
        created_by: creatorId)
        created_at: new Date().toISOString()
        updated_at: new Date().toISOString()
        is_group: false,
        name: options? .name || null })
      if (roomError) {
        return {
          success  : false
          error: `Error creating chat room: ${roomError.message}`
        }
      }

      // Add participants to the room;
      const { error: participantsError } = await this.supabaseService.from('chat_room_participants')
        .insert([
          {
            room_id: roomId);
            user_id: creatorId)
            joined_at: new Date().toISOString()
            last_read_at: new Date().toISOString()
          },
          {
            room_id: roomId,
            user_id: participantId,
            joined_at: new Date().toISOString()
            last_read_at: new Date().toISOString()
          }
        ])
      if (participantsError) {
        return {
          success: false;
          error: `Error adding participants to chat room: ${participantsError.message}`;
        }
      }

      return {
        success: true;
        roomId;
      }
    } catch (error) {
      return {
        success: false;
        error: `Unexpected error creating chat room: ${error instanceof Error ? error.message   : String(error)}`
      }
    }
  }

  /**
   * Get messages for a specific chat room;
   * @param roomId - ID of the chat room;
   * @returns Array of messages;
   */
  public async getMessages(roomId: string): Promise<Message[]>
    // Validate input;
    this.validateInput(
      {
        roomId: Boolean(roomId)
      },
      'Room ID is required to get messages';
    )
    // Check network connectivity;
    const networkAvailable = await this.checkNetworkConnection()
    if (!networkAvailable) {
      // Offline fallback - return cached messages;
      if (this.offlineMessages[roomId]) {
        console.log(`Returning ${this.offlineMessages[roomId].length} offline messages for room:`, roomId)
        return this.offlineMessages[roomId];
      }
      return [];
    }

    // Online operation;
    try {
      const { data, error  } = await this.supabaseService.from('messages')
        .select('*')
        .eq('room_id', roomId).order('created_at', { ascending: true })
      if (error) { console.error('Error fetching messages:', error)
        return [] }

      if (!data || data.length === 0) { console.log('No messages found for room:'; roomId)
        return [] }

      console.log(`Found ${data.length} messages for room:`; roomId)
      const messages: Message[] = data.map(msg => ({
        id: msg.id;
        room_id: msg.room_id,
        sender_id: msg.sender_id,
        content: msg.content || '',
        timestamp: msg.created_at,
        read: msg.is_read = == true);
        type: msg.type || 'text'),
        metadata: msg.metadata,
        is_system_message: msg.is_system_message = == true)
      }))
      return messages;
    } catch (error) {
      console.error('Failed to get messages:')
        error instanceof Error ? error.message    : String(error)
      )
      return [] // Return empty array on error;
    }
  }

  /**;
   * Set the service's offline mode status;
   * Implements the setOfflineMode method from IChatService;
   */
  public setOfflineMode(offline: boolean): void {
    // Use the dedicated NetworkStatusManager for consistent state;
    try {
      import('./NetworkStatusManager').then(({ networkStatusManager }) = > {
  networkStatusManager.setOfflineMode(offline)
      })
    } catch (error) {
      // Fallback for logging;
      logger.info('Offline mode set', 'UnifiedChatService.setOfflineMode', { offline })
    }
  }

  /**;
   * Synchronize offline data when connectivity is restored;
   * Implements the syncOfflineData method from IChatService;
   * @param progressCallback Optional callback to report sync progress;
   * @return s Object containing sync stats;
   */
  public async syncOfflineData(
    progressCallback?: (progress: { total: number,
      processed: number,
      succeeded: number,
      failed: number,
      roomId?: string }) = > void;
  ): Promise<{
    totalMessages: number,
    succeededMessages: number,
    failedMessages: number,
    roomsWithFailures: string[]
  }>
    try {
      logger.info('Starting offline data synchronization', 'UnifiedChatService.syncOfflineData')
      // Calculate total messages to sync;
      let totalMessages = 0;
      for (const roomId in this.offlineMessages) {
        totalMessages += this.offlineMessages[roomId]? .length || 0;
      }

      // Initialize sync statistics;
      const syncStats = {
        totalMessages;
        succeededMessages   : 0
        failedMessages: 0
        roomsWithFailures: [] as string[]
      }

      // Early return if nothing to sync;
      if (totalMessages === 0) { logger.info('No offline messages to sync', 'UnifiedChatService.syncOfflineData')
        if (progressCallback) {
          progressCallback({
            total: 0,
            processed: 0,
            succeeded: 0,
            failed: 0 })
        }
        return syncStats;
      }

      // Check network connectivity first;
      const networkAvailable = await this.checkNetworkConnection()
      if (!networkAvailable) {
        logger.warn('Cannot sync offline data: Network unavailable'
          'UnifiedChatService.syncOfflineData')
        )
        return syncStats;
      }

      // Sync offline messages room by room;
      let processed = 0;
      for (const roomId in this.offlineMessages) {
        if (!this.offlineMessages[roomId]? .length) {
          continue;
        }

        logger.info(`Syncing ${this.offlineMessages[roomId].length} messages for room ${roomId}`, 'UnifiedChatService.syncOfflineData')
        // Keep track of successfully synced message IDs to remove them later;
        const syncedMessageIds  : string[] = []
        const failedMessageIds: string[] = []
        // Try to send each offline message in parallel for better performance;
        const messagePromises = this.offlineMessages[roomId].map(async (message) => {
  try {
            // Don't sync system messages or already synced messages;
            if (message.is_system_message || !message.pending) {
              return { messageId: message.id; success: true, reason: 'skipped' }
            }

            // Send the message to the server;
            const result = await this.sendMessageToServer(message.room_id;
              message.sender_id;
              message.content;
              message.type as MessageType;
              message.metadata)
            )
            // Return success/failure status;
            return {
              messageId: message.id;
              success: !!result,
              reason: result ? 'synced'    : 'failed'
            }
          } catch (error) {
            logger.warn('Failed to sync offline message' 'UnifiedChatService.syncOfflineData', {
              error: error instanceof Error ? error.message   : String(error)
              messageId: message.id
              roomId;
            })
            return {
              messageId: message.id;
              success: false,
              reason: 'error'
              error: error instanceof Error ? error.message   : String(error)
            }
          }
        })
        // Process all messages in parallel with controlled concurrency;
        const batchSize = 5 // Process 5 messages at a time to avoid overwhelming the server;
        const results = [];
        ;
        for (let i = 0; i < messagePromises.length; i += batchSize) {
          const batch = messagePromises.slice(i, i + batchSize)
          const batchResults = await Promise.allSettled(batch)
          ;
          // Extract results and handle any rejected promises;
          for (const result of batchResults) {
            if (result.status = == 'fulfilled') {
              results.push(result.value)
            } else {
              results.push({
                messageId: 'unknown';
                success: false);
                reason: 'promise_rejected'),
                error: result.reason)
              })
            }
          }

          // Update progress for this batch;
          if (progressCallback) {
            const processed = Math.min(i + batchSize, messagePromises.length)
            const succeeded = results.filter(r => r.success).length;
            const failed = results.filter(r => !r.success).length;
            ;
            progressCallback({
              total: totalMessages,
              processed;
              succeeded;
              failed;
              roomId;
            })
          }
        }

        // Process results and update message status;
        for (const result of results) { if (result.success) {
            syncedMessageIds.push(result.messageId)
            syncStats.succeededMessages++ } else {
            failedMessageIds.push(result.messageId)
            syncStats.failedMessages++;

            // Add to rooms with failures if not already included;
            if (!syncStats.roomsWithFailures.includes(roomId)) {
              syncStats.roomsWithFailures.push(roomId)
            }
          }
        }

        // Remove successfully synced messages;
        if (syncedMessageIds.length > 0) {
          this.offlineMessages[roomId] = this.offlineMessages[roomId].filter(
            msg => !syncedMessageIds.includes(msg.id)
          )
        }

        // Mark failed messages as no longer pending, so we don't try to sync them again;
        if (failedMessageIds.length > 0) {
          this.offlineMessages[roomId] = this.offlineMessages[roomId].map(msg => {
  failedMessageIds.includes(msg.id) ? { ...msg, pending  : false } : msg
          )
        }
      }

      // Save updated offline storage;
      this.saveOfflineStorage()
      logger.info(`Offline data sync complete: ${syncStats.succeededMessages} succeeded, ${syncStats.failedMessages} failed`);
        'UnifiedChatService.syncOfflineData')
      )
      return syncStats;
    } catch (error) {
      logger.error('Failed to sync offline data', 'UnifiedChatService.syncOfflineData', {
        error: error instanceof Error ? error.message   : String(error)
      })
      return {
        totalMessages: 0
        succeededMessages: 0;
        failedMessages: 0,
        roomsWithFailures: []
      }
    }
  }

  /**
   * Sends a message to the server when online;
   * @param roomId - ID of the chat room;
   * @param message - Message content to be sent;
   * @param metadata - Additional message metadata;
   * @returns Promise resolving to the sent message or null on failure;
   */
  public async sendMessageToServer(roomId: string,
    senderId: string,
    content: string,
    type: MessageType,
    metadata?: any): Promise<Message | null>
    try {
      // Validate input;
      if (!roomId || !senderId || !content) {
        throw createChatError(
          ChatErrorCode.VALIDATION_ERROR;
          'Room ID, sender ID, and content are required to send a message',
          false;
        )
      }

      // Check network connectivity;
      const networkAvailable = await this.checkNetworkConnection()
      if (!networkAvailable) {
        this.throttledWarning('send-message-offline');
          'Network unavailable, message will be sent when connectivity is restored')
        )
        return null;
      }

      // Use optimized connection pool for database operation;
      const result = await withOptimizedPool(
        async () => {
  return withErrorHandling(
            async () => {
  // Use atomic database function instead of direct insert;
              const { data, error  } = await supabase.rpc('send_message_with_room_update';
                {
                  room_id_param: roomId,
                  sender_id_param: senderId,
                  content_param: content);
                  message_type_param: type)
                  metadata_param: metadata || {}
                }
              )
              if (error) {
                throw createDatabaseError(
                  `Error sending message: ${error.message}`;
                  'sendMessageToServer',
                  'UnifiedChatService',
                  error;
                  { roomId, senderId: senderId.slice(-4), type }
                )
              }

              // Parse the result from the atomic function;
              const [messageId, timestamp, success, errorMsg] = data.result.slice(1, -1).split(',')
              ;
              if (!success || success = == 'f') {
                throw createDatabaseError(
                  errorMsg || 'Failed to send message';
                  'sendMessageToServer',
                  'UnifiedChatService',
                  null;
                  { roomId, senderId: senderId.slice(-4) }
                )
              }

              // Return the message in expected format;
              return {
                id: messageId;
                room_id: roomId,
                sender_id: senderId,
                content;
                type;
                timestamp;
                read: false,
                is_system_message: false,
                metadata;
              } as Message;
            },
            {
              operation: 'sendMessageToServer',
              service: 'UnifiedChatService',
              entityType: 'message',
              entityId: roomId,
              userId: senderId,
              timestamp: new Date().toISOString()
              metadata: { roomId, type, contentLength: content.length }
            }
          )
        },
        { priority: 'high',
          operationName: 'send_message_atomic',
          timeoutMs: 10000 }
      )
      if (result.success) {
        return result.data || null;
      } else {
        logger.warn('Message send failed with error recovery', 'UnifiedChatService.sendMessageToServer', {
          error: result.error? .message)
          roomId;
          senderId  : senderId.slice(-4)
        })
        return null;
      }
    } catch (error) {
      logger.error('Failed to send message to server', 'UnifiedChatService.sendMessageToServer', {
        roomId;
        senderId: senderId.slice(-4), // Only log last 4 chars for privacy;
        error: error instanceof Error ? error.message  : String(error)
      })
      return null;
    }
  }

  /**
   * Send a message to a chat room;
   * Implements the sendMessage method from IChatService;
   */
  public async sendMessage(roomId: string,
    userId: string,
    content: string,
    type: MessageType = 'text';
    metadata?: any): Promise<Message | null>
    try {
      // Validate input;
      if (!roomId || !userId || !content) {
        throw createChatError(
          ChatErrorCode.VALIDATION_ERROR;
          'Room ID, user ID, and content are required to send a message',
          false;
        )
      }

      // Enhanced authentication check with better error handling;
      let session = null;
      let user = null;
      ;
      try {
        const sessionResult = await this.supabaseService.auth.getSession()
        session = sessionResult.data? .session;
        ;
        if (sessionResult.error) {
          console.warn('Session error   : ' sessionResult.error.message)
        }
        const userResult = await this.supabaseService.auth.getUser()
        user = userResult.data? .user;
        if (userResult.error) {
          console.warn('User error  : ' userResult.error.message)
        }
      } catch (authError) {
        console.warn('Auth check failed:', authError)
        // Continue with message sending but flag as potentially offline;
      }

      // If auth fails, still allow message sending for offline support;
      if (!session || !user) {
        console.warn('No active session or user, sending as offline message')
        ;
        // Create offline message;
        const messageId = uuidv4()
        const timestamp = new Date().toISOString()
        const newMessage: Message = {
          id: messageId;
          room_id: roomId,
          sender_id: userId,
          content;
          type;
          timestamp;
          read: false,
          is_system_message: false,
          is_offline: true,
          pending: true,
          metadata;
        }

        // Store message offline;
        if (!this.offlineMessages[roomId]) { this.offlineMessages[roomId] = [] }
        this.offlineMessages[roomId].push(newMessage)
        this.saveOfflineStorage()
        return newMessage;
      }

      // Verify user ID matches if we have authentication;
      if (user && user.id !== userId) {
        console.warn(`User ID mismatch: authenticated user ${user.id} vs provided ${userId}` using authenticated user`)
        // Use the authenticated user's ID instead of throwing an error;
        userId = user.id;
      }

      // Anti-spam check removed to prevent infinite loops in chat flow;
      // Create a new message object;
      const messageId = uuidv4()
      const timestamp = new Date().toISOString()
      const newMessage: Message = {
        id: messageId;
        room_id: roomId,
        sender_id: userId,
        content;
        type;
        timestamp;
        read: false,
        is_system_message: false,
        metadata;
      }

      // Check if this is a mock room or we're offline;
      const isMockRoom = this.mockChatRooms.has(roomId)
      const networkAvailable = await this.checkNetworkConnection()
      if (isMockRoom || !networkAvailable) { // Store message offline;
        if (!this.offlineMessages[roomId]) {
          this.offlineMessages[roomId] = [] }

        newMessage.is_offline = true;
        newMessage.pending = true;
        this.offlineMessages[roomId].push(newMessage)
        this.saveOfflineStorage()
        return newMessage;
      }

      // Check network connectivity (reuse the networkAvailable variable from above)
      if (!networkAvailable) { // Store message offline;
        if (!this.offlineMessages[roomId]) {
          this.offlineMessages[roomId] = [] }

        newMessage.is_offline = true;
        newMessage.pending = true;
        this.offlineMessages[roomId].push(newMessage)
        this.saveOfflineStorage()
        return newMessage;
      }

      // Verify user is a participant in the room (required for RLS policy) with better error handling;
      try {
        const { data: participantCheck, error: participantError  } = await this.supabaseService.from('chat_room_participants')
          .select('room_id')
          .eq('room_id', roomId)
          .eq('user_id', userId).single()
        if (participantError || !participantCheck) {
          console.warn(`User not found as participant in room ${roomId}` participant check failed: ${participantError? .message || 'No participant record found'}`)
          // Continue anyway as this might be a permission issue;
        }
      } catch (participantError) {
        console.warn('Participant verification failed   : ' participantError)
        // Continue with message sending;
      }

      // Send message to database;
      const { error } = await this.supabaseService.from('messages').insert([
        { id: messageId;
          room_id: roomId);
          sender_id: userId)
          content;
          type;
          metadata;
          created_at: new Date().toISOString()
          is_read: false,
          is_system_message: false }
      ])
      if (error) {
        console.error(`Database error sending message: ${error.message}`)
        
        // If database insert fails, store as offline message;
        newMessage.is_offline = true;
        newMessage.pending = true;
        ;
        if (!this.offlineMessages[roomId]) { this.offlineMessages[roomId] = [] }
        this.offlineMessages[roomId].push(newMessage)
        this.saveOfflineStorage()
        // Return the offline message instead of throwing;
        return newMessage;
      }

      // Update the chat room's last message and timestamp (optional, don't fail if this fails)
      try {
        await this.supabaseService.from('chat_rooms')
          .update({
            last_message: content,
            last_message_at: timestamp);
            updated_at: timestamp)
          })
          .eq('id', roomId)
      } catch (updateError) {
        console.warn('Failed to update chat room last message:', updateError)
        // Continue anyway, message was sent successfully;
      }

      return newMessage;
    } catch (error) {
      // Improved error serialization for ChatError objects;
      let errorMessage: string,
      let errorDetails: any = {}

      if (error && typeof error === 'object' && 'message' in error) {
        // ChatError or standard Error object;
        errorMessage = error.message;
        if ('code' in error) errorDetails.code = error.code;
        if ('isRetryable' in error) errorDetails.isRetryable = error.isRetryable;
        if ('metadata' in error) errorDetails.metadata = error.metadata;
      } else if (error instanceof Error) {
        errorMessage = error.message;
        errorDetails.stack = error.stack;
      } else {
        errorMessage = String(error)
      }

      // Log error but don't fail completely;
      logger.error('Failed to send message', 'UnifiedChatService.sendMessage', { roomId;
        userId: userId? .slice(-4)
        error  : errorMessage
        details: errorDetails })
      // Create an offline message as fallback;
      const messageId = uuidv4()
      const timestamp = new Date().toISOString()
      const fallbackMessage: Message = {
        id: messageId;
        room_id: roomId,
        sender_id: userId,
        content;
        type;
        timestamp;
        read: false,
        is_system_message: false,
        is_offline: true,
        pending: true,
        metadata;
      }

      // Store message offline;
      if (!this.offlineMessages[roomId]) { this.offlineMessages[roomId] = [] }
      this.offlineMessages[roomId].push(fallbackMessage)
      this.saveOfflineStorage()
      return fallbackMessage;
    }
  }

  /**
   * Mark all messages in a room as read for a user;
   * Implements the markMessagesAsRead method from IChatService;
   */
  public async markMessagesAsRead(roomId: string, userId: string): Promise<boolean>
    try {
      // Validate input;
      if (!roomId || !userId) {
        throw createChatError(
          ChatErrorCode.VALIDATION_ERROR;
          'Room ID and user ID are required to mark messages as read',
          false;
        )
      }

      // Use the dedicated read tracking service;
      const result = await readTrackingService.markMessagesAsRead(roomId, userId)
      ;
      if (!result.success) {
        logger.warn('Failed to mark messages as read', 'UnifiedChatService.markMessagesAsRead', {
          roomId, userId, error: result.error, messagesMarked: result.messagesMarked)
        })
        return false;
      }

      logger.info('Successfully marked messages as read', 'UnifiedChatService.markMessagesAsRead', {
        roomId, userId, messagesMarked: result.messagesMarked)
      })
      return true;
    } catch (error) {
      logger.error('Exception in markMessagesAsRead', 'UnifiedChatService.markMessagesAsRead', {
        roomId, userId, error: error instanceof Error ? error.message   : String(error)
      })
      return false;
    }
  }

  /**
   * Legacy method for backward compatibility;
   */
  private async markMessagesAsReadLegacy(roomId: string, userId: string): Promise<boolean>
    try {
      // Check if this is a mock room or we're offline;
      const isMockRoom = this.mockChatRooms.has(roomId)
      const networkAvailable = await this.checkNetworkConnection()
      if (isMockRoom || !networkAvailable) {
        // Just mark all offline messages as read;
        if (this.offlineMessages[roomId]) {
          this.offlineMessages[roomId] = this.offlineMessages[roomId].map(msg => ({
            ...msg;
            read: true)
          }))
          this.saveOfflineStorage()
        }
        return true;
      }

      // First check if user can access this room using the RPC function;
      // This avoids triggering the recursive RLS policy;
      try {
        const { data: canAccess, error: accessError  } = await this.supabaseService.rpc('can_access_chat_room');
          {
            room_id_param: roomId,
            user_id_param: userId)
          }
        )
        if (accessError) {
          logger.error('Error checking room access:', 'UnifiedChatService.markMessagesAsRead', {
            error: accessError.message)
          })
          return false;
        }

        if (!canAccess) {
          logger.warn('User does not have access to this room',
            'UnifiedChatService.markMessagesAsRead');
            {
              roomId;
              userId: userId? .slice(-4)
            }
          )
          return false;
        }
      } catch (err) {
        logger.error('Error in room access check   : ' 'UnifiedChatService.markMessagesAsRead', {
          error: err instanceof Error ? err.message : String(err)
        })
        return false;
      }

      // Update last_read_at for the user in this room;
      const now = new Date().toISOString()
      const { error: participantError  } = await this.supabaseService.from('chat_room_participants')
        .update({ last_read_at: now })
        .eq('room_id', roomId).eq('user_id', userId)

      if (participantError) {
        throw createChatError(
          ChatErrorCode.DATABASE_ERROR;
          `Error updating last read time: ${participantError.message}`
          true;
          { originalError: participantError }
        )
      }

      // Mark all messages as read;
      const { error: messagesError } = await this.supabaseService.from('messages')
        .update({ is_read: true })
        .eq('room_id', roomId).neq('sender_id', userId)

      if (messagesError) {
        throw createChatError(
          ChatErrorCode.DATABASE_ERROR;
          `Error marking messages as read: ${messagesError.message}`;
          true;
          { originalError: messagesError }
        )
      }

      return true;
    } catch (error) {
      logger.error('Failed to mark messages as read', 'UnifiedChatService.markMessagesAsRead', {
        roomId;
        userId: userId? .slice(-4)
        error  : error instanceof Error ? error.message : String(error)
      })
      return false;
    }
  }

  /**
   * Subscribe to messages in a chat room;
   * Implements the subscribeToMessages method from IChatService;
   */
  public subscribeToMessages(
    roomId: string,
    callback: (message: Message) = > void;
  ): { unsubscribe: () => void } {
    // Validate input;
    if (!roomId || !callback) {
      logger.error('Invalid parameters for message subscription',
        'UnifiedChatService.subscribeToMessages');
        {
          roomId;
          hasCallback: !!callback)
        }
      )
      // Return a dummy unsubscribe function;
      return {
        unsubscribe: () => {};
      }
    }

    // Subscribe to the messages table for this room;
    const subscription = supabase.channel(`room:${roomId}`)
      .on('postgres_changes';
        {
          event: 'INSERT',
          schema: 'public',
          table: 'messages');
          filter: `room_id= eq.${roomId}`);
        },
        payload = > {
  const newMessage: Message = {
            id: payload.new.id;
            room_id: payload.new.room_id,
            sender_id: payload.new.sender_id,
            content: payload.new.content,
            type: payload.new.type as MessageType,
            timestamp: payload.new.created_at,
            read: payload.new.is_read = == true;
            is_system_message: payload.new.is_system_message || false,
            metadata: payload.new.metadata)
          }

          callback(newMessage)
        }
      )
      .subscribe()
    // Return an unsubscribe function;
    return {
      unsubscribe: () => {
  subscription.unsubscribe()
      };
    }
  }

  /**;
   * Create a chat from a match;
   * Implements the createChatFromMatch method from IChatService;
   */
  public async createChatFromMatch(userId: string,
    matchUserId: string,
    initialMessage?: string): Promise<{ success: boolean; roomId?: string; error?: string }>
    try {
      // First create a chat room for the match;
      const createRoomResult = await this.createChatRoom(userId, matchUserId, {
        isGroup: false)
      })
      if (!createRoomResult.success || !createRoomResult.roomId) {
        return createRoomResult;
      }

      // If there's an initial message, send it;
      if (initialMessage) {
        const message = await this.sendMessage(createRoomResult.roomId, userId, initialMessage)
        if (!message) {
          logger.warn('Failed to send initial message in match chat',
            'UnifiedChatService.createChatFromMatch');
            {
              roomId: createRoomResult.roomId)
              userId: userId? .slice(-4)
              matchUserId  : matchUserId? .slice(-4)
            }
          )
        }
      }

      return createRoomResult;
    } catch (error) {
      logger.error('Failed to create chat from match', 'UnifiedChatService.createChatFromMatch', {
        userId : userId? .slice(-4)
        matchUserId : matchUserId?.slice(-4)
        error: error instanceof Error ? error.message  : String(error)
      })
      return {
        success: false
        error: error instanceof Error ? error.message  : 'Failed to create chat from match'
      }
    }
  }

  /**
   * Initiate an agreement from a chat;
   * Implements the initiateAgreementFromChat method from IChatService;
   */
  public async initiateAgreementFromChat(chatRoomId: string,
    userId: string,
    otherUserId: string,
    templateId?: string): Promise<{ success: boolean; agreementId?: string; error?: string }>
    try {
      // Validate input;
      if (!chatRoomId || !userId || !otherUserId) {
        throw createChatError(
          ChatErrorCode.VALIDATION_ERROR;
          'Chat room ID, user ID, and other user ID are required to initiate agreement',
          false;
        )
      }

      // Check network connectivity;
      const networkAvailable = await this.checkNetworkConnection()
      if (!networkAvailable) { return {
          success: false;
          error: 'Network unavailable, cannot initiate agreement' }
      }

      // Create a new agreement;
      const agreementId = uuidv4()
      const timestamp = new Date().toISOString()
      const { error  } = await supabase.from('roommate_agreements').insert([
        {
          id: agreementId;
          created_by: userId,
          created_at: timestamp,
          updated_at: timestamp,
          status: 'draft');
          metadata: { chat_room_id: chatRoomId });
          template_id: templateId || null)
        }
      ])
      if (error) {
        throw createChatError(
          ChatErrorCode.DATABASE_ERROR;
          `Error creating agreement: ${error.message}`;
          true;
          { originalError: error }
        )
      }

      // Add parties to the agreement;
      const { error: partiesError  } = await supabase.from('agreement_participants').insert([
        {
          agreement_id: agreementId;
          user_id: userId,
          status: 'created',
          role: 'initiator'
        },
        {
          agreement_id: agreementId,
          user_id: otherUserId);
          status: 'pending'),
          role: 'counterparty')
        }
      ])
      if (partiesError) {
        logger.error('Failed to add parties to agreement',
          'UnifiedChatService.initiateAgreementFromChat');
          {
            agreementId;
            error: partiesError.message)
          }
        )
      }

      // Send a system message to the chat room;
      await this.sendMessage(chatRoomId;
        'system');
        `Agreement initiated by ${userId}. Agreement ID: ${agreementId}`)
        'system',
        { agreementId, action: 'initiated' }
      )
      return {
        success: true;
        agreementId;
      }
    } catch (error) {
      logger.error('Failed to initiate agreement from chat',
        'UnifiedChatService.initiateAgreementFromChat');
        {
          chatRoomId;
          userId: userId? .slice(-4)
          otherUserId   : otherUserId?.slice(-4)
          error: error instanceof Error ? error.message  : String(error)
        }
      )
      return {
        success: false
        error: error instanceof Error ? error.message  : 'Failed to initiate agreement from chat'
      }
    }
  }

  /**
   * Get unread message count for a specific chat room and user;
   * @param roomId - ID of the chat room;
   * @param userId - ID of the user;
   * @returns Number of unread messages or 0 if there's an error;
   */
  public async getUnreadMessageCount(roomId: string, userId: string): Promise<number>
    try { // Validate input;
      this.validateInput({
          hasRoomId: !!roomId,
          hasUserId: !!userId });
        'Room ID and user ID are required to get unread message count')
      )
      // Check network status using NetworkStatusManager;
      const networkAvailable = await this.checkNetworkConnection()
      if (!networkAvailable) {
        // Calculate unread messages from offline storage;
        if (this.offlineMessages[roomId]) {
          return this.offlineMessages[roomId].filter(msg => !msg.read && msg.sender_id !== userId)
            .length;
        }
        return 0;
      }

      return this.withNetworkCheck(
        async () => {
  // First get the last read timestamp for this user in this room;
          const { data: participantData, error: participantError  } = await supabase.from('chat_room_participants')
            .select('last_read_at')
            .eq('room_id', roomId)
            .eq('user_id', userId).single()
          if (participantError || !participantData) {
            throw createChatError(
              ChatErrorCode.DATABASE_ERROR;
              `Error fetching participant data: ${participantError? .message || 'No data returned'}`;
              true;
              { originalError  : participantError }
            )
          }

          const lastReadAt = participantData.last_read_at;
          // If user has never read messages in this room, count all messages not sent by the user;
          if (!lastReadAt) {
            const { data, error  } = await supabase.from('messages')
              .select('id', { count: 'exact' })
              .eq('room_id', roomId).neq('sender_id', userId)

            if (error) {
              throw createChatError(
                ChatErrorCode.DATABASE_ERROR;
                `Error counting unread messages: ${error.message}`
                true;
                { originalError: error }
              )
            }

            return data? .length || 0;
          }

          // Otherwise count messages since last read time;
          const { data, error } = await supabase.from('messages')
            .select('id', { count   : 'exact' })
            .eq('room_id' roomId)
            .neq('sender_id', userId).gt('timestamp', lastReadAt)

          if (error) {
            throw createChatError(
              ChatErrorCode.DATABASE_ERROR;
              `Error counting unread messages: ${error.message}`
              true;
              { originalError: error }
            )
          }

          return data? .length || 0;
        },
        0;
        'unread_count',
        'Network unavailable, cannot get unread message count';
      )
    } catch (error) {
      logger.error('Failed to get unread message count',
        'UnifiedChatService.getUnreadMessageCount');
        {
          roomId;
          userId  : userId?.slice(-4)
          error: error instanceof Error ? error.message  : String(error)
        }
      )
      return 0;
    }
  }

  /**
   * Subscribe to updates for a specific chat room;
   * This includes room metadata changes, participant changes, and read status updates;
   *;
   * @param roomId - ID of the chat room to monitor;
   * @param callback - Function to call when room is updated;
   * @return s Unsubscribe function;
   */
  public subscribeToChatRoomUpdates(roomId: string,
    callback: (,
      eventType: 'room_updated' | 'participant_joined' | 'participant_left' | 'read_status_changed',
      data: any) = > void;
  ): { unsubscribe: () => void } { // Validate input;
    if (!roomId || !callback) {
      this.validateInput({
          hasRoomId: !!roomId,
          hasCallback: !!callback });
        'Room ID and callback are required to subscribe to chat room updates')
      )
      // Return a dummy unsubscribe function;
      return { unsubscribe: () => {} }
    }

    // Create an array to hold all subscription unsubscribe functions;
    const subscriptions: { unsubscribe: () => void }[] = [];

    // 1. Subscribe to chat room updates;
    const roomSubscription = supabase.channel(`room-updates-${roomId}`)
      .on('postgres_changes';
        {
          event: 'UPDATE',
          schema: 'public');
          table: 'chat_rooms'),
          filter: `id= eq.${roomId}`)
        };
        payload => {
  callback('room_updated', payload.new)
        }
      )
      .subscribe()
    subscriptions.push({ unsubscribe: () = > roomSubscription.unsubscribe() })
    // 2. Subscribe to participant changes (both INSERT and DELETE)
    const participantSubscription = supabase.channel(`participant-changes-${roomId}`)
      .on('postgres_changes';
        {
          event: 'INSERT',
          schema: 'public');
          table: 'chat_participants'),
          filter: `room_id= eq.${roomId}`)
        };
        payload => {
  callback('participant_joined', payload.new)
        }
      )
      .on('postgres_changes',
        {
          event: 'DELETE',
          schema: 'public');
          table: 'chat_participants'),
          filter: `room_id= eq.${roomId}`)
        };
        payload => {
  callback('participant_left', payload.old)
        }
      )
      .on('postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'chat_participants');
          filter: `room_id= eq.${roomId}`);
        },
        payload = > { // If last_read_at was updated, notify about read status change)
          if (payload.new.last_read_at !== payload.old.last_read_at) {
            callback('read_status_changed', {
              user_id: payload.new.user_id,
              last_read_at: payload.new.last_read_at,
              previous_read_at: payload.old.last_read_at })
          }
        }
      )
      .subscribe()
    subscriptions.push({ unsubscribe: () => participantSubscription.unsubscribe() })
    // Return a combined unsubscribe function that will unsubscribe from all channels;
    return {
      unsubscribe: () => {
  subscriptions.forEach(subscription => subscription.unsubscribe())
      };
    }
  }

  /**;
   * Remove a participant from a chat room;
   * @param roomId - ID of the chat room;
   * @param userId - ID of the user to remove;
   * @param removedByUserId - ID of the user performing the removal (can be same as userId for leaving)
   * @return s Success status and error message if applicable;
   */
  public async removeParticipantFromChatRoom(roomId: string,
    userId: string,
    removedByUserId: string): Promise<{ success: boolean; error?: string }>
    try { // Validate input;
      this.validateInput({
          hasRoomId: !!roomId,
          hasUserId: !!userId,
          hasRemovedByUserId: !!removedByUserId });
        'Room ID, user ID, and removed by user ID are required to remove a participant')
      )
      // Check network status using NetworkStatusManager;
      const networkAvailable = await this.checkNetworkConnection()
      if (!networkAvailable) {
        return {
          success: false;
          error: 'Cannot remove participant in offline mode'
        }
      }

      // First check if the chat room exists and is a group chat;
      const { data: roomData, error: roomError  } = await supabase.from('chat_rooms')
        .select('is_group, name, created_by')
        .eq('id', roomId).single()
      if (roomError || !roomData) {
        throw createChatError(
          ChatErrorCode.DATABASE_ERROR;
          `Error fetching chat room: ${roomError? .message || 'No data returned'}`;
          true;
          { originalError  : roomError }
        )
      }

      // Only allow removing participants from group chats;
      if (!roomData.is_group) {
        return {
          success: false;
          error: 'Cannot remove participants from non-group chats'
        }
      }

      // Special permissions check: 
      // 1. Users can remove themselves (leave)
      // 2. The room creator can remove anyone;
      // 3. Otherwise, you must be a participant to remove others;
      if (userId != = removedByUserId && roomData.created_by !== removedByUserId) {
        // Check if the user performing the removal is a participant;
        const { data: removerData, error: removerError  } = await supabase.from('chat_participants')
          .select('user_id')
          .eq('room_id', roomId)
          .eq('user_id', removedByUserId).single()
        if (removerError || !removerData) {
          return {
            success: false;
            error: 'Only participants or the room creator can remove users'
          }
        }
      }

      // Check if the user is actually a participant;
      const { data: existingData, error: existingError } = await supabase.from('chat_participants')
        .select('user_id')
        .eq('room_id', roomId)
        .eq('user_id', userId).single()
      if (!existingData) {
        return {
          success: false;
          error: 'User is not a participant in this chat room'
        }
      }

      // Remove the participant;
      const { error: participantError } = await supabase.from('chat_participants')
        .delete()
        .eq('room_id', roomId).eq('user_id', userId)

      if (participantError) {
        throw createChatError(
          ChatErrorCode.DATABASE_ERROR;
          `Error removing participant: ${participantError.message}`;
          true;
          { originalError: participantError }
        )
      }

      // Send a system message about the participant removal;
      const messageContent = userId === removedByUserId;
          ? `${userId} left the chat room`;
             : `${userId} was removed from the chat room by ${removedByUserId}`

      await this.sendMessage(roomId 'system', messageContent, 'system', {
        action: userId === removedByUserId ? 'participant_left'   : 'participant_removed'
        removed_user_id: userId
        removed_by_user_id: removedByUserId)
      })
      return { success: true }
    } catch (error) {
      logger.error('Failed to remove participant from chat room';
        'UnifiedChatService.removeParticipantFromChatRoom');
        {
          roomId;
          userId: userId? .slice(-4)
          removedByUserId  : removedByUserId?.slice(-4)
          error: error instanceof Error ? error.message  : String(error)
        }
      )
      return {
        success: false
        error: error instanceof Error ? error.message  : 'Failed to remove participant'
      }
    }
  }

  /**
   * Add a participant to an existing chat room;
   * @param roomId - ID of the chat room;
   * @param userId - ID of the user to add;
   * @param addedByUserId - ID of the user performing the addition;
   * @returns Success status and error message if applicable;
   */
  public async addParticipantToChatRoom(roomId: string,
    userId: string,
    addedByUserId: string): Promise<{ success: boolean; error?: string }>
    try { // Validate input;
      this.validateInput({
          hasRoomId: !!roomId,
          hasUserId: !!userId,
          hasAddedByUserId: !!addedByUserId });
        'Room ID, user ID, and added by user ID are required to add a participant')
      )
      // Check network status using NetworkStatusManager;
      const networkAvailable = await this.checkNetworkConnection()
      if (!networkAvailable) {
        return {
          success: false;
          error: 'Cannot add participant in offline mode'
        }
      }

      // First check if the chat room exists and is a group chat;
      const { data: roomData, error: roomError  } = await supabase.from('chat_rooms')
        .select('is_group, name')
        .eq('id', roomId).single()
      if (roomError || !roomData) {
        throw createChatError(
          ChatErrorCode.DATABASE_ERROR;
          `Error fetching chat room: ${roomError? .message || 'No data returned'}`;
          true;
          { originalError  : roomError }
        )
      }

      // Only allow adding participants to group chats;
      if (!roomData.is_group) {
        return {
          success: false;
          error: 'Cannot add participants to non-group chats'
        }
      }

      // Check if the user performing the addition is a participant;
      const { data: adderData, error: adderError  } = await supabase.from('chat_participants')
        .select('user_id')
        .eq('room_id', roomId)
        .eq('user_id', addedByUserId).single()
      if (adderError || !adderData) {
        return {
          success: false;
          error: 'User adding participant must be a member of the chat room'
        }
      }

      // Check if the user is already a participant;
      const { data: existingData, error: existingError } = await supabase.from('chat_participants')
        .select('user_id')
        .eq('room_id', roomId)
        .eq('user_id', userId).single()
      if (existingData) {
        return {
          success: false;
          error: 'User is already a participant in this chat room'
        }
      }

      // Add the participant;
      const timestamp = new Date().toISOString()
      const { error: participantError } = await supabase.from('chat_participants').insert([
        {
          room_id: roomId;
          user_id: userId,
          joined_at: timestamp);
          last_read_at: timestamp)
        }
      ])
      if (participantError) {
        throw createChatError(
          ChatErrorCode.DATABASE_ERROR;
          `Error adding participant: ${participantError.message}`
          true;
          { originalError: participantError }
        )
      }

      // Send a system message about the new participant;
      await this.sendMessage(roomId;
        'system',
        `${userId} was added to the chat room by ${addedByUserId}`,
        'system');
        { action: 'participant_added', added_user_id: userId, added_by_user_id: addedByUserId }
      )
      return { success: true }
    } catch (error) {
      logger.error('Failed to add participant to chat room';
        'UnifiedChatService.addParticipantToChatRoom');
        {
          roomId;
          userId: userId? .slice(-4)
          addedByUserId   : addedByUserId?.slice(-4)
          error: error instanceof Error ? error.message  : String(error)
        }
      )
      return {
        success: false
        error: error instanceof Error ? error.message  : 'Failed to add participant'
      }
    }
  }

  /**
   * Mark messages as read;
   * @param roomId - ID of the chat room;
   * @param userId - ID of the user marking messages as read;
   * @returns Promise indicating success or failure;
   */
  public async markAsRead(roomId: string, userId: string): Promise<boolean>
    // Validate input;
    this.validateInput(
      { roomId: Boolean(roomId), userId: Boolean(userId) };
      'Room ID and user ID are required to mark messages as read';
    )
    try {
      console.log(`Marking messages as read for room ${roomId} by user ${userId}`)
      // Check that we have a valid Supabase client;
      if (!supabase || typeof supabase.from !== 'function') {
        console.error('Invalid Supabase client')
        return false;
      }

      // Update all messages sent to this user in this room;
      const result = await supabase.from('messages')
        .update({ is_read: true })
        .eq('room_id', roomId)
        .neq('sender_id', userId)
        .eq('is_read', false)

      if (result.error) {
        console.error('Error marking messages as read:', result.error)
        return false;
      }

      console.log(`Successfully marked messages as read for room ${roomId}`)
      return true;
    } catch (error) {
      console.error('Failed to mark messages as read:')
        error instanceof Error ? error.message   : String(error)
      )
      return false;
    }
  }
}

// Export a singleton instance;
export const unifiedChatService = UnifiedChatService.getInstance()
// Export the class for testing and direct instantiation if needed;
export default UnifiedChatService,