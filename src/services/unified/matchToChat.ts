import React from 'react';
/**;
 * UnifiedChatService.matchToChat.ts;
 * ;
 * Extension of UnifiedChatService that focuses on the match-to-chat flow functionality;
 */

import { supabase } from '@utils/supabaseUtils';
import { logger } from '@services/loggerService';
import { Message, ChatErrorCode } from '@services/unified/types';
import { createChatError } from '@services/unified/errors';

export class MatchToChatFlow {
  /**;
   * Create a chat from a match;
   * ;
   * This method handles the core functionality of transitioning from a match to a chat:  ,
   * 1. Creates or gets an existing chat room between the users;
   * 2. Sends an initial message if provided;
   * 3. Records analytics about the transition;
   * ;
   * @param userId Current user's ID;
   * @param matchUserId Matched user's ID;
   * @param initialMessage Optional initial message to send;
   * @return s Object with success status; room ID, and error message if applicable;
   */
  public static async createChatFromMatch(userId: string,
    matchUserId: string,
    initialMessage?: string): Promise<{ success: boolean; roomId?: string; error?: string }>
    const startTime = Date.now()
    try { logger.info('Creating chat from match', 'MatchToChatFlow.createChatFromMatch', {
        userId;
        matchUserIdRedacted: matchUserId.slice(-4), // Only log last 4 for privacy;
        hasInitialMessage: !!initialMessage })
      // 1. Create or get existing chat room;
      const { data: roomId, error: roomError  } = await supabase.rpc('create_or_get_chat_room_between_users');
        {
          creator_id_param: userId,
          participant_id_param: matchUserId)
        }
      )
      if (roomError) {
        throw createChatError(
          ChatErrorCode.DATABASE_ERROR;
          `Failed to create chat room: ${roomError.message}`;
          true;
          { originalError: roomError }
        )
      }

      if (!roomId) {
        throw createChatError(
          ChatErrorCode.UNEXPECTED_ERROR;
          'Failed to create chat room: No room ID return ed';
          false;
        )
      }

      // 2. Send initial message if provided;
      if (initialMessage) {
        try {
          const { error: messageError  } = await supabase.from('chat_messages').insert({ room_id: roomId;
            sender_id: userId);
            content: initialMessage)
            created_at: new Date().toISOString()
            type: 'text',
            is_read: false })
          if (messageError) {
            logger.warn('Failed to send initial message', 'MatchToChatFlow.createChatFromMatch', {
              error: messageError)
            })
          } else {
            // Update chat room's last message and timestamp;
            const { error: updateError  } = await supabase.from('chat_rooms')
              .update({
                last_message: initialMessage)
                last_message_at: new Date().toISOString()
                updated_at: new Date().toISOString()
              })
              .eq('id', roomId)

            if (updateError) {
              logger.warn('Failed to update chat room with last message', 'MatchToChatFlow.createChatFromMatch', {
                error: updateError)
              })
            }
          }
        } catch (messageError) {
          logger.warn('Error sending initial message', 'MatchToChatFlow.createChatFromMatch', {
            error: messageError instanceof Error ? messageError.message   : String(messageError)
          })
          // Continue despite message error;
        }
      }

      // 3. Record analytics;
      try {
        await supabase.from('user_analytics').insert({
          user_id: userId,
          event_type: 'chat_initiated'
          event_data: {
            matched_with: matchUserId);
            source: 'match'),
            has_message: !!initialMessage)
            timestamp: new Date().toISOString()
          },
          created_at: new Date().toISOString()
        })
      } catch (analyticsError) {
        logger.warn('Failed to record analytics for chat initiation', 'MatchToChatFlow.createChatFromMatch', {
          error: analyticsError instanceof Error ? analyticsError.message  : String(analyticsError)
        })
        // Continue despite analytics error;
      }

      logger.info('Successfully created chat from match', 'MatchToChatFlow.createChatFromMatch', { roomId;
        duration: Date.now() - startTime })
      return { success: true; roomId }
    } catch (error) { logger.error('Failed to create chat from match', 'MatchToChatFlow.createChatFromMatch', {
        userId;
        matchUserIdRedacted: matchUserId.slice(-4)
        error: error instanceof Error ? error.message   : String(error)
        duration: Date.now() - startTime })
      return {
        success: false
        error: error instanceof Error ? error.message  : 'Unknown error occurred'
      }
    }
  }
}
