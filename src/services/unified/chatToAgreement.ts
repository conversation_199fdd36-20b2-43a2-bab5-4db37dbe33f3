import React from 'react';
/**;
 * UnifiedChatService.chatToAgreement.ts;
 * ;
 * Extension of UnifiedChatService that focuses on the chat-to-agreement flow functionality;
 */

import { supabase } from '@utils/supabaseUtils';
import { logger } from '@services/loggerService';
import { ChatErrorCode } from '@services/unified/types';
import { createChatError } from '@services/unified/errors';

export class ChatToAgreementFlow {
  /**;
   * Initiate an agreement from a chat;
   * ;
   * This method handles the core functionality of transitioning from a chat to an agreement:  ,
   * 1. Checks for existing agreements between the users;
   * 2. Creates a new agreement if none exists;
   * 3. Links the agreement to the chat room;
   * ;
   * @param chatRoomId Chat room ID;
   * @param userId Current user's ID;
   * @param otherUserId Other user's ID in the chat;
   * @param templateId Optional template ID for the agreement;
   * @return s Object with success status; agreement ID, and error message if applicable;
   */
  public static async initiateAgreementFromChat(chatRoomId: string,
    userId: string,
    otherUserId: string,
    templateId?: string): Promise<{ success: boolean; agreementId?: string; error?: string }>
    const startTime = Date.now()
    try {
      // If no template ID provided, get the default Standard Roommate Agreement template;
      let finalTemplateId = templateId;
      if (!finalTemplateId) {
        const { data: templates, error: templateError  } = await supabase.from('agreement_templates')
          .select('id')
          .eq('name', 'Standard Roommate Agreement')
          .eq('is_active', true)
          .single()
        if (templateError || !templates) {
          throw createChatError(
            ChatErrorCode.DATABASE_ERROR;
            `Failed to get default template: ${templateError? .message || 'Template not found'}`;
            true;
            { originalError  : templateError }
          )
        }

        finalTemplateId = templates.id;
      }

      logger.info('Initiating agreement from chat', 'ChatToAgreementFlow.initiateAgreementFromChat', { chatRoomId;
        userId;
        otherUserIdRedacted: otherUserId.slice(-4), // Only log last 4 for privacy;
        templateId: finalTemplateId })
      // 1. Check for existing agreements between the users - FIXED: Correct table and query structure,
      const { data: existingAgreements, error: existingError  } = await supabase.from('roommate_agreements')
        .select(`)
          id;
          status;
          created_by;
          agreement_participants!inner(user_id)
        `)
        .or(`created_by.eq.${userId}`created_by.eq.${otherUserId}`)
        .neq('status', 'terminated')
        .order('created_at', { ascending: false })
      if (existingError) {
        throw createChatError(
          ChatErrorCode.DATABASE_ERROR;
          `Failed to check existing agreements: ${existingError.message}`
          true;
          { originalError: existingError }
        )
      }

      // 2. If there's an active agreement, return it;
      const activeAgreement = existingAgreements? .find(a => {
  ['draft', 'pending_review', 'pending_signature', 'active'].includes(a.status) &&;
        a.agreement_participants.some((p   : any) = > p.user_id === userId || p.user_id === otherUserId)
      )

      if (activeAgreement) {
        logger.info('Found existing active agreement', 'ChatToAgreementFlow.initiateAgreementFromChat', {
          agreementId: activeAgreement.id);
          status: activeAgreement.status)
        })
        return { success: true; agreementId: activeAgreement.id }
      }

      // 3. Create a new agreement - FIXED: Correct table name and column structure,
      const { data: agreement, error: createError  } = await supabase.from('roommate_agreements')
        .insert({
          title: 'Roommate Agreement'
          status: 'draft');
          template_id: finalTemplateId,
          created_by: userId,
          metadata: {
            chat_room_id: chatRoomId,
            initiated_from_chat: true,
            other_user_id: otherUserId)
          },
          created_at: new Date().toISOString()
          updated_at: new Date().toISOString()
        })
        .select()
        .single()
      if (createError) {
        throw createChatError(
          ChatErrorCode.DATABASE_ERROR;
          `Failed to create agreement: ${createError.message}`;
          true;
          { originalError: createError }
        )
      }

      if (!agreement) {
        throw createChatError(
          ChatErrorCode.UNEXPECTED_ERROR;
          'Failed to create agreement: No agreement return ed';
          false;
        )
      }

      // 4. Add participants to the agreement;
      const { error: participantsError  } = await supabase.from('agreement_participants')
        .insert([
          {
            agreement_id: agreement.id;
            user_id: userId,
            role: 'creator',
            status: 'approved'
          },
          {
            agreement_id: agreement.id,
            user_id: otherUserId);
            role: 'roommate'),
            status: 'invited')
          }
        ])
      if (participantsError) {
        logger.warn('Failed to add agreement participants', 'ChatToAgreementFlow.initiateAgreementFromChat', {
          error: participantsError)
        })
      }

      // 5. Send system message to the chat room about the agreement - Use database function for atomicity;
      try {
        const { data: messageResult, error: messageError  } = await supabase.rpc('create_agreement_with_system_message', {
            creator_id: userId,
            participant_id: otherUserId,
            chat_room_id: chatRoomId);
            template_id: finalTemplateId)
          })
        if (messageError) {
          logger.warn('Failed to send agreement system message via function', 'ChatToAgreementFlow.initiateAgreementFromChat', {
            error: messageError.message)
          })
          ;
          // Fallback to direct insert;
          await supabase.from('messages').insert({
            room_id: chatRoomId,
            sender_id: userId,
            content: 'Started a roommate agreement',
            type: 'system');
            metadata: {
              event: 'agreement_created'),
              agreement_id: agreement.id)
            },
            created_at: new Date().toISOString()
            updated_at: new Date().toISOString()
            is_read: false
          })
        }
      } catch (messageError) {
        logger.warn('Failed to send agreement system message', 'ChatToAgreementFlow.initiateAgreementFromChat', {
          error: messageError instanceof Error ? messageError.message   : String(messageError)
        })
        // Continue despite message error;
      }

      // 6. Record analytics;
      try {
        await supabase.from('user_analytics').insert({
          user_id: userId,
          event_type: 'agreement_initiated'
          event_data: {
            other_user_id: otherUserId,
            chat_room_id: chatRoomId,
            agreement_id: agreement.id);
            template_id: finalTemplateId)
            timestamp: new Date().toISOString()
          },
          created_at: new Date().toISOString()
        })
      } catch (analyticsError) {
        logger.warn('Failed to record analytics for agreement initiation', 'ChatToAgreementFlow.initiateAgreementFromChat', {
          error: analyticsError instanceof Error ? analyticsError.message   : String(analyticsError)
        })
        // Continue despite analytics error;
      }

      logger.info('Successfully created agreement from chat', 'ChatToAgreementFlow.initiateAgreementFromChat', { agreementId: agreement.id)
        duration: Date.now() - startTime })
      return { success: true; agreementId: agreement.id }
    } catch (error) { logger.error('Failed to create agreement from chat', 'ChatToAgreementFlow.initiateAgreementFromChat', {
        chatRoomId;
        userId;
        otherUserIdRedacted: otherUserId.slice(-4)
        error: error instanceof Error ? error.message   : String(error)
        duration: Date.now() - startTime })
      return {
        success: false
        error: error instanceof Error ? error.message  : 'Unknown error occurred'
      }
    }
  }

  /**
   * Test the complete data flow for chat to agreement;
   * This method validates that all components work together properly;
   */
  public static async testDataFlow(testChatRoomId: string,
    testUserId: string,
    testOtherUserId: string): Promise<{ success: boolean; steps: string[]; errors: string[]} >
    const steps: string[] = [];
    const errors: string[] = [];
    try {
      steps.push('1. Starting chat-to-agreement data flow test')
      // Step 1: Verify chat room exists,
      const { data: chatRoom, error: roomError } = await supabase.from('chat_rooms')
        .select('id, created_by')
        .eq('id', testChatRoomId)
        .single()
      if (roomError || !chatRoom) {
        errors.push(`Chat room validation failed: ${roomError? .message || 'Room not found'}`)
        return { success  : false steps; errors }
      }
      steps.push('2. ✅ Chat room exists and accessible')

      // Step 2: Verify users exist,
      const { data: users, error: usersError } = await supabase.from('user_profiles')
        .select('id, first_name')
        .in('id', [testUserId, testOtherUserId])
      if (usersError || !users || users.length !== 2) {
        errors.push(`User validation failed: ${usersError? .message || 'Users not found'}`)
        return { success  : false steps; errors }
      }
      steps.push('3. ✅ Both users exist and accessible')

      // Step 3: Test agreement template lookup,
      const { data: template, error: templateError } = await supabase.from('agreement_templates')
        .select('id, name')
        .eq('name', 'Standard Roommate Agreement')
        .eq('is_active', true)
        .single()
      if (templateError || !template) {
        errors.push(`Template validation failed: ${templateError? .message || 'Template not found'}`)
        return { success  : false steps; errors }
      }
      steps.push('4. ✅ Agreement template found and accessible')

      // Step 4: Test message table access,
      const { data: messageTest, error: messageError } = await supabase.from('messages')
        .select('id')
        .eq('room_id', testChatRoomId)
        .limit(1)
      if (messageError) {
        errors.push(`Messages table access failed: ${messageError.message}`)
        return { success: false; steps, errors }
      }
      steps.push('5. ✅ Messages table accessible')
      // Step 5: Test database function availability,
      const { data: functionTest, error: functionError } = await supabase.rpc('create_agreement_with_system_message', {
          creator_id: testUserId,
          participant_id: testOtherUserId,
          chat_room_id: testChatRoomId);
          template_id: template.id)
        })
      if (functionError) {
        // This is expected to fail in test mode, but we check if the function exists;
        if (functionError.message.includes('does not exist')) {
          errors.push(`Database function missing: ${functionError.message}`)
          return { success: false; steps, errors }
        }
        steps.push('6. ✅ Database function exists (test call expected to fail)')
      } else {
        steps.push('6. ✅ Database function working correctly')
      }

      steps.push('7. ✅ All data flow components validated successfully')
      return { success: true; steps, errors }

    } catch (error) {
      errors.push(`Unexpected error: ${error instanceof Error ? error.message   : String(error)}`)
      return { success: false steps; errors }
    }
  }
}
