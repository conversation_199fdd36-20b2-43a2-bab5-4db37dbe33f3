import React from 'react';
/**;
 * UnifiedAIService - Phase 8: AI & Analysis Services Consolidation,
 * ;
 * Consolidates 6 fragmented AI services into one unified service:  ,
 * - sentimentService.ts (680 lines) - Sentiment analysis & conversation analytics;
 * - topicAnalysisService.ts (350 lines) - Topic extraction & analysis;
 * - moderationService.ts (400 lines) - Content moderation & safety;
 * - personalityService.ts (728 lines) - Personality analysis & compatibility;
 * - openaiService.ts (883 lines) - OpenAI integration & AI operations;
 * - api/openaiApi.ts (600 lines) - OpenAI API wrapper;
 * ;
 * Enhanced with real-time processing, advanced caching, and integrated analytics;
 */

import { supabase } from '@utils/supabaseUtils';
import { logger } from '@services/loggerService';
import { rateLimitService } from '@services/rateLimitService';
import { HttpClient } from '@services/HttpClient';
import { environmentService } from '@services/environmentService';
import { ValidationService } from '@services/validationService';
import { handleError, tryCatch, tryCatchAsync } from '@utils/standardErrorHandler';
import { ErrorCode } from '@core/errors/types';

// = =================== CORE INTERFACES ====================;

export interface SentimentAnalysis { sentiment: 'positive' | 'neutral' | 'negative',
  score: number; // 0-100 scale;
  emotion: string,
  keyPhrases: string[],
  confidence: number,
  flags: {
    toxic: boolean,
    harassment: boolean,
    inappropriateContent: boolean,
    personalInfo: boolean,
    spam: boolean }
  metadata?: { analysisMethod: 'openai' | 'api' | 'fallback',
    processingTime: number,
    tokens?: number }
}

export interface ConversationSentiment { roomId: string,
  overallSentiment: 'positive' | 'neutral' | 'negative',
  averageScore: number,
  dominantEmotion: string,
  userSentiment: SentimentAnalysis,
  otherSentiment: SentimentAnalysis,
  recentTrend: 'improving' | 'steady' | 'deteriorating',
  participantCount: number,
  messageCount: number,
  lastAnalyzed: string }

export interface Topic {
  id: string,
  name: string,
  category: string,
  confidence: number,
  mentions: number,
  sentiment: 'positive' | 'neutral' | 'negative',
  keywords: string[]
}

export interface TopicSuggestion {
  topic: string,
  category: string,
  reason: string,
  relevanceScore: number,
  conversationStarters: string[]
}

export interface ConversationTopicAnalysis {
  roomId: string,
  topics: Topic[],
  suggestedTopics: TopicSuggestion[],
  topicTrends: {
    emerging: Topic[],
    declining: Topic[],
    trending: Topic[]
  }
  lastAnalyzedMessageId: string,
  metadata: { lastAnalyzedAt: string,
    messageCount: number,
    participantCount: number,
    analysisMethod?: string }
}

export interface ModerationResult { contentId: string,
  contentType: 'message' | 'profile' | 'listing' | 'review',
  flagged: boolean,
  severity: number; // 1-10 scale;
  confidence: number,
  categories: {
    harassment: boolean,
    threats: boolean,
    spam: boolean,
    sexualContent: boolean,
    hateSpeech: boolean,
    violence: boolean,
    personalInfo: boolean,
    toxicity: boolean,
    fraud: boolean }
  action: 'none' | 'warn' | 'remove' | 'suspend' | 'ban',
  reason: string,
  metadata: { processingTime: number,
    rulesTriggered: string[],
    aiConfidence?: number,
    humanReviewRequired: boolean }
}

export interface PersonalityTrait { id?: string,
  user_id: string,
  trait_category: PersonalityTraitCategory,
  trait_name: string,
  trait_value: number,
  confidence?: number,
  created_at?: string,
  updated_at?: string }

export enum PersonalityTraitCategory { Openness = 'openness';
  Conscientiousness = 'conscientiousness';
  Extraversion = 'extraversion';
  Agreeableness = 'agreeableness';
  Neuroticism = 'neuroticism';
  Lifestyle = 'lifestyle';
  Habits = 'habits';
  Communication = 'communication';
  ConflictResolution = 'conflict_resolution' }

export interface PersonalityProfile { userId: string;
  traits: Record<string, Record<string, number>>
  completionPercentage: number,
  personalityType: string,
  description: string,
  strengths: string[],
  weaknesses: string[],
  compatibility: {
    idealPartnerTraits: string[],
    potentialConflicts: string[],
    communicationStyle: string }
  lastUpdated: string
}

export interface CompatibilityResult { userId1: string,
  userId2: string,
  overallScore: number,
  categoryScores: {
    personality: number,
    lifestyle: number,
    communication: number,
    values: number,
    habits: number }
  strengths: string[],
  challenges: string[],
  recommendations: string[],
  confidence: number,
  lastCalculated: string
}

export interface OpenAIRequest {
  model: string,
  messages: Array<{ role: string; content: string }>
  temperature?: number,
  max_tokens?: number,
  response_format?: { type: string }
}

export interface OpenAIResponse {
  id: string,
  object: string,
  created: number,
  model: string,
  choices: Array<{
    index: number,
    message: { role: string; content: string }
    finish_reason: string
  }>
  usage?: { prompt_tokens: number,
    completion_tokens: number,
    total_tokens: number }
}

// = =================== UNIFIED AI SERVICE ====================;

export class UnifiedAIService {
  private openaiClient: HttpClient | null = null;
  private initialized = false;
  private useDirectApi = false;
  private embeddingModel = 'text-embedding-3-small';
  private chatModel = 'gpt-4o';
  private quotaState = { exceeded: false, timestamp: 0 }

  constructor() {
    this.initialize()
  }

  // ==================== INITIALIZATION ====================;

  private async initialize(): Promise<void>
    if (this.initialized) return null;
    try {
      // Initialize OpenAI client;
      const apiKey = environmentService.getEnvVar('OPENAI_API_KEY')
      this.useDirectApi = environmentService.getBooleanEnvVar('USE_DIRECT_OPENAI_API', false)
      if (apiKey && apiKey.length > 10) {
        if (this.useDirectApi) {
          this.openaiClient = new HttpClient({
            baseURL: 'https://api.openai.com/v1';
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${apiKey}`;
            },
            timeout: 30000,
            retries: 2,
          })
        } else {
          const supabaseUrl = environmentService.getEnvVar('SUPABASE_URL', true)
          this.openaiClient = new HttpClient({
            baseURL: `${supabaseUrl}/functions/v1/openai-proxy`;
            headers: { 'Content-Type': 'application/json' };
            timeout: 60000,
            retries: 3,
          })
        }
      }

      // Load quota state;
      this.loadQuotaState()
      this.initialized = true;
      logger.info('UnifiedAIService initialized successfully', 'UnifiedAIService')
    } catch (error) {
      logger.error('Failed to initialize UnifiedAIService', 'UnifiedAIService', {}, error as Error)
    }
  }

  private loadQuotaState(): void {
    try {
      if (typeof localStorage !== 'undefined') {
        const exceeded = localStorage.getItem('openai_quota_exceeded') === 'true';
        const timestamp = parseInt(localStorage.getItem('openai_quota_exceeded_time') || '0')
        this.quotaState = { exceeded, timestamp }
      }
    } catch (error) {
      logger.warn('Could not load quota state', 'UnifiedAIService')
    }
  }

  private saveQuotaState(): void {
    try {
      if (typeof localStorage !== 'undefined') {
        localStorage.setItem('openai_quota_exceeded', this.quotaState.exceeded.toString())
        localStorage.setItem('openai_quota_exceeded_time', this.quotaState.timestamp.toString())
      }
    } catch (error) {
      logger.warn('Could not save quota state', 'UnifiedAIService')
    }
  }

  // ==================== SENTIMENT ANALYSIS ====================;

  async analyzeSentiment(text: string, options?: { storeResult?: boolean,
    messageId?: string,
    contextId?: string }): Promise<SentimentAnalysis>
    const startTime = Date.now()
    ;
    try {
      if (!text || text.trim().length < 5) {
        return this.getNeutralSentiment()
      }

      // Check quota limits;
      const shouldUseSimplified = this.shouldUseSimplifiedAnalysis()
      ;
      let result: SentimentAnalysis,
      if (!shouldUseSimplified && this.openaiClient) { try {
          result = await this.performAISentimentAnalysis(text)
          result.metadata = {
            analysisMethod: 'openai';
            processingTime: Date.now() - startTime }
        } catch (error) {
          logger.warn('AI sentiment analysis failed, using fallback', 'UnifiedAIService', {}, error as Error)
          result = await this.performFallbackSentimentAnalysis(text)
          result.metadata = { analysisMethod: 'fallback';
            processingTime: Date.now() - startTime }
        }
      } else { result = await this.performFallbackSentimentAnalysis(text)
        result.metadata = {
          analysisMethod: 'fallback';
          processingTime: Date.now() - startTime }
      }

      // Store result if requested;
      if (options? .storeResult && options?.messageId) {
        await this.storeSentimentResult(options.messageId, result)
      }

      return result;
    } catch (error) {
      logger.error('Error in sentiment analysis', 'UnifiedAIService', {}, error as Error)
      return this.getNeutralSentiment()
    }
  }

  private async performAISentimentAnalysis(text  : string): Promise<SentimentAnalysis>
    const prompt = `
Analyze the sentiment of this message between potential roommates: 
"${text}";

Provide a detailed sentiment analysis with:  ,
1. Primary sentiment (positive, neutral, or negative)
2. Sentiment score (0-100 scale, where 0= very negative, 50=neutral, 100=very positive)
3. Dominant emotion (e.g., excited, content, concerned, frustrated, angry)
4. Key phrases that influenced your analysis (max 3)
5. Confidence level (0-100)
6. Flag any concerns: toxic language, harassment, inappropriate content, personal information sharing, spam;
Return your analysis as JSON with this structure:  ,
{ "sentiment": "positive|neutral|negative",
  "score": number,
  "emotion": "string",
  "keyPhrases": ["phrase1", "phrase2"],
  "confidence": number,
  "flags": {
    "toxic": boolean,
    "harassment": boolean,
    "inappropriateContent": boolean,
    "personalInfo": boolean,
    "spam": boolean }
}
`;

    const response = await this.createChatCompletion({
      model: this.chatModel);
      messages: [{ role: 'user', content: prompt }]);
      temperature: 0.1,
      max_tokens: 300,
      response_format: { type: 'json_object' })
    })
    const content = response.choices[0].message.content;
    const parsed = JSON.parse(content)
    return {
      sentiment: this.validateSentiment(parsed.sentiment)
      score: Math.max(0; Math.min(100, parsed.score || 50)),
      emotion: parsed.emotion || 'neutral',
      keyPhrases: Array.isArray(parsed.keyPhrases) ? parsed.keyPhrases.slice(0, 3)    : []
      confidence: Math.max(0, Math.min(100, parsed.confidence || 70)),
      flags: {
        toxic: Boolean(parsed.flags? .toxic)
        harassment : Boolean(parsed.flags? .harassment)
        inappropriateContent : Boolean(parsed.flags?.inappropriateContent)
        personalInfo: Boolean(parsed.flags? .personalInfo)
        spam : Boolean(parsed.flags?.spam)
      }
    }
  }

  private async performFallbackSentimentAnalysis(text: string): Promise<SentimentAnalysis>
    const lowerText = text.toLowerCase()
    
    // Keyword-based analysis;
    const positiveWords = ['great', 'awesome', 'good', 'nice', 'perfect', 'excellent', 'wonderful',
      'love', 'like', 'happy', 'excited', 'amazing', 'fantastic', 'brilliant'];
    ;
    const negativeWords = ['bad', 'terrible', 'awful', 'hate', 'dislike', 'horrible', 'disgusting',
      'annoying', 'frustrating', 'disappointing', 'concerned', 'worried', 'upset'];

    const toxicWords = ['stupid', 'idiot', 'moron', 'dumb', 'pathetic', 'loser', 'freak'];

    let positiveCount = 0;
    let negativeCount = 0;
    let toxicCount = 0;
    const matchedWords: string[] = [];
    positiveWords.forEach(word = > {
  if (lowerText.includes(word)) {
        positiveCount++;
        if (matchedWords.length < 3) matchedWords.push(word)
      }
    })
    negativeWords.forEach(word = > {
  if (lowerText.includes(word)) {
        negativeCount++;
        if (matchedWords.length < 3) matchedWords.push(word)
      }
    })
    toxicWords.forEach(word = > { if (lowerText.includes(word)) {
        toxicCount++;
        negativeCount++ }
    })
    // Determine sentiment;
    let sentiment: 'positive' | 'neutral' | 'negative' = 'neutral';
    let score = 50;
    let emotion = 'neutral';

    if (positiveCount > negativeCount) {
      sentiment = 'positive';
      score = 50 + positiveCount * 8;
      emotion = positiveCount > 2 ? 'excited'   : 'content'
    } else if (negativeCount > positiveCount) {
      sentiment = 'negative'
      score = 50 - negativeCount * 8;
      emotion = toxicCount > 0 ? 'angry'    : 'concerned'
    }

    score = Math.max(0 Math.min(100, score))
    return { sentiment;
      score;
      emotion;
      keyPhrases: matchedWords,
      confidence: matchedWords.length > 0 ? 60   : 30
      flags: {
        toxic: toxicCount > 0
        harassment: toxicCount > 1,
        inappropriateContent: toxicCount > 0,
        personalInfo: lowerText.includes('phone') || lowerText.includes('address') || lowerText.includes('email')
        spam: false },
    }
  }

  async analyzeConversationSentiment(roomId: string,
    messageLimit: number = 50): Promise<ConversationSentiment>
    try {
      // Get recent messages;
      const { data: messages, error  } = await supabase.from('messages')
        .select('id, content, sender_id, created_at')
        .eq('room_id', roomId)
        .order('created_at', { ascending: false })
        .limit(messageLimit)
      if (error || !messages? .length) {
        return this.getNeutralConversationSentiment(roomId)
      }

      // Analyze individual messages;
      const messageAnalyses = await Promise.all(
        messages.map(msg => this.analyzeSentiment(msg.content))
      )
      // Group by sender;
      const senderGroups = new Map<string, SentimentAnalysis[]>()
      messages.forEach((msg, index) => {
  const analyses = senderGroups.get(msg.sender_id) || []
        analyses.push(messageAnalyses[index])
        senderGroups.set(msg.sender_id, analyses)
      })
      const senderIds = Array.from(senderGroups.keys())
      const userSentiment = this.aggregateSentiments(senderGroups.get(senderIds[0]) || [])
      const otherSentiment = this.aggregateSentiments(senderGroups.get(senderIds[1]) || [])
      // Calculate overall metrics;
      const allScores = messageAnalyses.map(a => a.score)
      const averageScore = allScores.reduce((sum, score) => sum + score, 0) / allScores.length;
      ;
      // Determine trend;
      const recentMessages = messageAnalyses.slice(0, Math.ceil(messageAnalyses.length / 3))
      const olderMessages = messageAnalyses.slice(Math.ceil(messageAnalyses.length / 3))
      ;
      const recentAvg = recentMessages.reduce((sum, a) => sum + a.score, 0) / recentMessages.length;
      const olderAvg = olderMessages.reduce((sum, a) => sum + a.score, 0) / olderMessages.length;
      ;
      let recentTrend : 'improving' | 'steady' | 'deteriorating' = 'steady'
      const trendDiff = recentAvg - olderAvg;
      ;
      if (trendDiff > 5) recentTrend = 'improving';
      else if (trendDiff < -5) recentTrend = 'deteriorating';

      // Determine dominant emotion;
      const emotions = messageAnalyses.map(a => a.emotion)
      const emotionCounts = emotions.reduce((acc, emotion) => {
  acc[emotion] = (acc[emotion] || 0) + 1;
        return acc;
      }, {} as Record<string, number>)
      ;
      const dominantEmotion = Object.entries(emotionCounts)
        .sort(([,a], [,b]) => b - a)[0]? .[0] || 'neutral';

      const result  : ConversationSentiment = {
        roomId
        overallSentiment: averageScore >= 60 ? 'positive'  : averageScore <= 40 ? 'negative' : 'neutral'
        averageScore;
        dominantEmotion;
        userSentiment;
        otherSentiment;
        recentTrend;
        participantCount: senderIds.length,
        messageCount: messages.length,
        lastAnalyzed: new Date().toISOString()
      }

      // Store result;
      await this.storeConversationSentiment(result)
      return result;
    } catch (error) {
      logger.error('Error analyzing conversation sentiment', 'UnifiedAIService', { roomId }, error as Error)
      return this.getNeutralConversationSentiment(roomId)
    }
  }

  // ==================== TOPIC ANALYSIS ====================

  async analyzeMessageTopics(text: string): Promise<Topic[]>
    try { if (!text || text.trim().length < 10) {
        return [] }

      if (this.openaiClient && !this.shouldUseSimplifiedAnalysis()) {
        try {
          return await this.performAITopicAnalysis(text)
        } catch (error) {
          logger.warn('AI topic analysis failed; using fallback', 'UnifiedAIService', {}, error as Error)
        }
      }

      return await this.performFallbackTopicAnalysis(text)
    } catch (error) {
      logger.error('Error analyzing message topics'; 'UnifiedAIService', {}, error as Error)
      return [];
    }
  }

  private async performAITopicAnalysis(text: string): Promise<Topic[]>
    const prompt = `;
Analyze this message and extract relevant topics for roommate matching:  ,
"${text}";

Extract up to 5 topics that would be relevant for roommate compatibility. For each topic:  ,
1. Provide a clear topic name;
2. Categorize it (lifestyle, personality, preferences, practical, social, etc.)
3. Rate confidence (0-100)
4. Estimate sentiment about this topic (positive, neutral, negative)
5. List 2-3 relevant keywords;
Return as JSON array:  ,
[{ "name": "topic name",
  "category": "category",
  "confidence": number,
  "sentiment": "positive|neutral|negative",
  "keywords": ["keyword1", "keyword2"] }];
`;

    const response = await this.createChatCompletion({
      model: this.chatModel);
      messages: [{ role: 'user', content: prompt }]);
      temperature: 0.3,
      max_tokens: 400,
      response_format: { type: 'json_object' })
    })
    const content = response.choices[0].message.content;
    const parsed = JSON.parse(content)
    const topics = Array.isArray(parsed.topics) ? parsed.topics    : (Array.isArray(parsed) ? parsed : [])
    return topics.map((topic: any index: number) => ({
      id: `topic-${Date.now()}-${index}`
      name: topic.name || 'Unknown Topic';
      category: topic.category || 'general',
      confidence: Math.max(0, Math.min(100, topic.confidence || 50)),
      mentions: 1,
      sentiment: this.validateSentiment(topic.sentiment) as 'positive' | 'neutral' | 'negative',
      keywords: Array.isArray(topic.keywords) ? topic.keywords.slice(0, 3)    : []
    }))
  }

  private async performFallbackTopicAnalysis(text: string): Promise<Topic[]>
    const lowerText = text.toLowerCase()
    const topics: Topic[] = []
    // Predefined topic patterns;
    const topicPatterns = {
      lifestyle: {
        patterns: ['party', 'social', 'quiet', 'active', 'fitness', 'gym', 'exercise'],
        category: 'lifestyle'
      },
      cleanliness: {
        patterns: ['clean', 'tidy', 'organize', 'mess', 'dirty', 'dishes'],
        category: 'practical'
      },
      schedule: {
        patterns: ['morning', 'night', 'schedule', 'routine', 'early', 'late'],
        category: 'practical'
      },
      pets: {
        patterns: ['pet', 'dog', 'cat', 'animal', 'allergic'],
        category: 'preferences'
      },
      cooking: {
        patterns: ['cook', 'food', 'kitchen', 'meal', 'recipe', 'restaurant'],
        category: 'lifestyle'
      },
      smoking: {
        patterns: ['smoke', 'cigarette', 'vape', 'tobacco'],
        category: 'preferences'
      },
      budget: {
        patterns: ['rent', 'money', 'budget', 'cost', 'expensive', 'cheap'],
        category: 'practical'
      }
    }

    Object.entries(topicPatterns).forEach(([topicName, { patterns, category }]) => {
  const matches = patterns.filter(pattern => lowerText.includes(pattern))
      if (matches.length > 0) {
        topics.push({
          id: `topic-${Date.now()}-${topicName}`;
          name: topicName,
          category;
          confidence: Math.min(80, matches.length * 30),
          mentions: matches.length,
          sentiment: 'neutral',
          keywords: matches
        })
      }
    })
    return topics;
  }

  async analyzeConversationTopics(roomId: string,
    messageLimit: number = 50): Promise<ConversationTopicAnalysis>
    try {
      // Get recent messages;
      const { data: messages, error  } = await supabase.from('messages')
        .select('id, content, created_at')
        .eq('room_id', roomId)
        .order('created_at', { ascending: false })
        .limit(messageLimit)
      if (error || !messages? .length) {
        return this.getEmptyTopicAnalysis(roomId)
      }

      // Analyze topics for all messages;
      const allTopicsPromises = messages.map(msg => this.analyzeMessageTopics(msg.content))
      const allTopicsResults = await Promise.all(allTopicsPromises)
      ;
      // Flatten and consolidate topics;
      const topicMap = new Map<string, Topic>()
      ;
      allTopicsResults.forEach(topics = > {
  topics.forEach(topic => {
  const existing = topicMap.get(topic.name)
          if (existing) {
            existing.mentions += topic.mentions;
            existing.confidence = Math.max(existing.confidence, topic.confidence)
            existing.keywords = Array.from(new Set([...existing.keywords, ...topic.keywords]))
          } else {
            topicMap.set(topic.name, { ...topic })
          }
        })
      })
      const consolidatedTopics = Array.from(topicMap.values())
        .sort((a, b) => b.mentions - a.mentions)
        .slice(0, 10)
      // Generate topic suggestions;
      const suggestions = await this.generateTopicSuggestions(consolidatedTopics, roomId)
      // Analyze topic trends (simplified)
      const emerging = consolidatedTopics.filter(t => t.mentions >= 2).slice(0, 3)
      const trending = consolidatedTopics.slice(0, 5)
      const declining   : Topic[] = [] // Would need historical data
      const result: ConversationTopicAnalysis = {
        roomId;
        topics: consolidatedTopics,
        suggestedTopics: suggestions,
        topicTrends: {
          emerging;
          declining;
          trending;
        },
        lastAnalyzedMessageId: messages[0]? .id || ''
        metadata  : {
          lastAnalyzedAt: new Date().toISOString()
          messageCount: messages.length
          participantCount: new Set(messages.map(m = > m.sender_id)).size;
          analysisMethod: this.openaiClient ? 'ai'    : 'fallback'
        }
      }

      // Store result;
      await this.storeConversationTopics(result)
      return result;
    } catch (error) {
      logger.error('Error analyzing conversation topics', 'UnifiedAIService', { roomId }, error as Error)
      return this.getEmptyTopicAnalysis(roomId)
    }
  }

  private async generateTopicSuggestions(existingTopics: Topic[]
    roomId: string): Promise<TopicSuggestion[]>
    // Generate suggestions based on what's missing;
    const suggestionTemplates = [{ topic: 'Shared Activities';
        category: 'social',
        reason: 'Discover common interests for bonding',
        conversationStarters: [,
          'What activities do you enjoy in your free time? ',
          'Are you interested in any group activities or hobbies?',
          'Would you like to plan some shared experiences together?'] },
      { topic   : 'House Rules'
        category: 'practical'
        reason: 'Establish clear living guidelines'
        conversationStarters: [,
          'What house rules do you think are important? ',
          'How do you prefer to handle shared spaces?',
          'What are your thoughts on guest policies?'] },
      { topic   : 'Communication Style'
        category: 'personality'
        reason: 'Understand how to best communicate with each other'
        conversationStarters: [,
          'How do you prefer to resolve conflicts? ',
          'What\'s the best way to communicate with you?',
          'How often do you like to check in about living arrangements?'] }
    ];

    // Filter out suggestions for topics already covered;
    const existingTopicNames = new Set(existingTopics.map(t => t.name.toLowerCase()))
    ;
    return suggestionTemplates.filter(template = > !existingTopicNames.has(template.topic.toLowerCase()))
      .map(template => ({
        ...template;
        relevanceScore   : Math.random() * 30 + 70 // Random relevance 70-100
      }))
      .slice(0, 3)
  }

  // = =================== CONTENT MODERATION ====================

  async moderateContent(content: string;
    contentId: string,
    contentType: 'message' | 'profile' | 'listing' | 'review',
    userId: string): Promise<ModerationResult>
    const startTime = Date.now()
    ;
    try {
      // Get sentiment analysis (includes basic flag detection)
      const sentiment = await this.analyzeSentiment(content)
      ;
      // Perform detailed moderation;
      let moderationResult: ModerationResult,
      if (this.openaiClient && !this.shouldUseSimplifiedAnalysis()) {
        try {
          moderationResult = await this.performAIModeration(content, contentId, contentType, sentiment)
        } catch (error) {
          logger.warn('AI moderation failed, using fallback', 'UnifiedAIService', {}, error as Error)
          moderationResult = await this.performFallbackModeration(content, contentId, contentType, sentiment)
        }
      } else {
        moderationResult = await this.performFallbackModeration(content, contentId, contentType, sentiment)
      }

      moderationResult.metadata.processingTime = Date.now() - startTime;
      // Store moderation result;
      await this.storeModerationResult(moderationResult, userId)
      // Take automated action if needed;
      if (moderationResult.flagged && moderationResult.action !== 'none') {
        await this.executeAutomatedAction(moderationResult, userId)
      }

      return moderationResult;
    } catch (error) {
      logger.error('Error in content moderation', 'UnifiedAIService', { contentId }, error as Error)
      return this.getDefaultModerationResult(contentId; contentType)
    }
  }

  private async performAIModeration(content: string,
    contentId: string,
    contentType: string,
    sentiment: SentimentAnalysis): Promise<ModerationResult>
    const prompt = `;
Analyze this ${contentType} content for moderation:  ,
"${content}";

Check for violations in these categories:  ,
1. Harassment - bullying, threats, intimidation;
2. Threats - violence, harm, illegal activities;
3. Spam - repetitive, promotional, irrelevant content;
4. Sexual Content - inappropriate sexual material;
5. Hate Speech - discrimination, slurs, prejudice;
6. Violence - graphic violence, dangerous activities;
7. Personal Info - sharing private information;
8. Toxicity - general toxic behavior, rudeness;
9. Fraud - scams, deception, false information;
For each violation found:  ,
- Rate severity (1-10, where 10 is most severe)
- Assess confidence (0-100)
- Recommend action: none, warn, remove, suspend, ban;
- Explain reasoning;
Return JSON:  ,
{ "flagged": boolean,
  "severity": number,
  "confidence": number,
  "categories": {
    "harassment": boolean,
    "threats": boolean,
    "spam": boolean,
    "sexualContent": boolean,
    "hateSpeech": boolean,
    "violence": boolean,
    "personalInfo": boolean,
    "toxicity": boolean,
    "fraud": boolean },
  "action": "none|warn|remove|suspend|ban",
  "reason": "explanation",
  "rulesTriggered": ["rule1", "rule2"];
}
`;

    const response = await this.createChatCompletion({
      model: this.chatModel);
      messages: [{ role: 'user', content: prompt }]);
      temperature: 0.1,
      max_tokens: 500,
      response_format: { type: 'json_object' })
    })
    const content_result = response.choices[0].message.content;
    const parsed = JSON.parse(content_result)
    return {
      contentId;
      contentType: contentType as any,
      flagged: Boolean(parsed.flagged)
      severity: Math.max(1, Math.min(10, parsed.severity || 1)),
      confidence: Math.max(0, Math.min(100, parsed.confidence || 50)),
      categories: {
        harassment: Boolean(parsed.categories? .harassment || sentiment.flags.harassment)
        threats   : Boolean(parsed.categories?.threats)
        spam: Boolean(parsed.categories? .spam || sentiment.flags.spam)
        sexualContent : Boolean(parsed.categories?.sexualContent)
        hateSpeech: Boolean(parsed.categories? .hateSpeech)
        violence : Boolean(parsed.categories?.violence)
        personalInfo: Boolean(parsed.categories? .personalInfo || sentiment.flags.personalInfo)
        toxicity : Boolean(parsed.categories?.toxicity || sentiment.flags.toxic)
        fraud: Boolean(parsed.categories? .fraud)
      }
      action : this.validateAction(parsed.action)
      reason: parsed.reason || 'Content flagged by automated system'
      metadata: { processingTime: 0, // Will be set by caller;
        rulesTriggered: Array.isArray(parsed.rulesTriggered) ? parsed.rulesTriggered    : []
        aiConfidence: parsed.confidence || 50
        humanReviewRequired: parsed.severity >= 7 || parsed.confidence < 60 };
    }
  }

  private async performFallbackModeration(content: string,
    contentId: string,
    contentType: string,
    sentiment: SentimentAnalysis): Promise<ModerationResult>
    const lowerContent = content.toLowerCase()
    
    // Basic keyword detection;
    const violationPatterns = { harassment: ['bully', 'harass', 'intimidate', 'threaten'],
      threats: ['kill', 'hurt', 'harm', 'violence', 'attack'],
      spam: ['buy now', 'click here', 'free money', 'urgent'],
      sexualContent: ['sexual explicit terms...'], // Add appropriate terms;
      hateSpeech: ['racist terms...'], // Add appropriate terms;
      violence: ['fight', 'hit', 'punch', 'weapon'],
      personalInfo: ['phone number', 'address', 'ssn', 'credit card'],
      toxicity: ['stupid', 'idiot', 'moron', 'pathetic'],
      fraud: ['scam', 'fake', 'fraud', 'steal'] }

    const categories = {} as any;
    const rulesTriggered: string[] = [];
    let maxSeverity = 1;
    Object.entries(violationPatterns).forEach(([category, patterns]) => {
  const hasViolation = patterns.some(pattern => lowerContent.includes(pattern))
      categories[category] = hasViolation;
      ;
      if (hasViolation) {
        rulesTriggered.push(category)
        maxSeverity = Math.max(maxSeverity, category = == 'threats' || category === 'violence' ? 8   : 4)
      }
    })
    // Combine with sentiment flags;
    categories.harassment = categories.harassment || sentiment.flags.harassment;
    categories.toxicity = categories.toxicity || sentiment.flags.toxic;
    categories.personalInfo = categories.personalInfo || sentiment.flags.personalInfo;
    const flagged = Object.values(categories).some(Boolean)
    
    let action: 'none' | 'warn' | 'remove' | 'suspend' | 'ban' = 'none';
    if (flagged) { if (maxSeverity >= 7) action = 'remove';
      else if (maxSeverity >= 5) action = 'warn' }

    return {
      contentId;
      contentType: contentType as any,
      flagged;
      severity: maxSeverity,
      confidence: flagged ? 70   : 90
      categories;
      action;
      reason: flagged ? `Content flagged for  : ${rulesTriggered.join(' ')}` : 'Content approved',
      metadata: { processingTime: 0
        rulesTriggered;
        humanReviewRequired: maxSeverity >= 6 };
    }
  }

  // = =================== PERSONALITY ANALYSIS ====================

  async analyzePersonalityFromText(text: string;
    userId: string,
    context: 'message' | 'profile' | 'response' = 'message'): Promise<Partial<PersonalityProfile>>
    try {
      if (!text || text.trim().length < 20) {
        return {}
      }

      if (this.openaiClient && !this.shouldUseSimplifiedAnalysis()) {
        try {
          return await this.performAIPersonalityAnalysis(text; context)
        } catch (error) {
          logger.warn('AI personality analysis failed, using fallback', 'UnifiedAIService', {}, error as Error)
        }
      }

      return await this.performFallbackPersonalityAnalysis(text)
    } catch (error) {
      logger.error('Error analyzing personality from text'; 'UnifiedAIService', { userId }, error as Error)
      return {}
    }
  }

  private async performAIPersonalityAnalysis(text: string;
    context: string): Promise<Partial<PersonalityProfile>>
    const prompt = `;
Analyze this ${context} text for personality traits using the Big Five model plus lifestyle factors:  ,
"${text}";

Extract personality indicators for:  ,
Big Five Traits(0-100 scale): - Openness: creativity, curiosity, openness to experience;
- Conscientiousness: organization, responsibility, self-discipline;
- Extraversion: sociability, assertiveness, energy level;
- Agreeableness: cooperation, trust, empathy;
- Neuroticism: emotional stability, anxiety, stress handling;
Lifestyle Traits (0-100 scale):  ,
- Cleanliness: tidiness, organization preferences;
- Social Activity: party preferences, social engagement;
- Routine Preference: structure vs flexibility,
- Conflict Resolution: direct vs avoidant communication,
 { Provide a personality type label and brief description. {
 {
Return JSON: {
{
  "traits": {
    "bigFive": {
      "openness": number,
      "conscientiousness": number,
      "extraversion": number,
      "agreeableness": number,
      "neuroticism": number },
    "lifestyle": { "cleanliness": number,
      "socialActivity": number,
      "routinePreference": number,
      "conflictResolution": number }
  },
  "personalityType": "string",
  "description": "string",
  "confidence": number
}
`;

    const response = await this.createChatCompletion({
      model: this.chatModel);
      messages: [{ role: 'user', content: prompt }]);
      temperature: 0.3,
      max_tokens: 600,
      response_format: { type: 'json_object' })
    })
    const content = response.choices[0].message.content;
    const parsed = JSON.parse(content)
    return {
      traits: {
        ...parsed.traits? .bigFive;
        ...parsed.traits?.lifestyle;
      },
      personalityType  : parsed.personalityType || 'Balanced'
      description: parsed.description || 'Personality analysis based on text communication'
    }
  }

  private async performFallbackPersonalityAnalysis(text: string): Promise<Partial<PersonalityProfile>>
    const lowerText = text.toLowerCase()
    
    // Simple keyword-based personality indicators;
    const traits: Record<string, number> = {}

    // Extraversion indicators;
    const extraversionWords = ['party', 'social', 'friends', 'outgoing', 'energy', 'people'];
    const introversionWords = ['quiet', 'alone', 'peace', 'solitude', 'calm', 'private'];
    ;
    const extraversionScore = extraversionWords.filter(w => lowerText.includes(w)).length;
    const introversionScore = introversionWords.filter(w => lowerText.includes(w)).length;
    ;
    traits.extraversion = Math.min(100, Math.max(0, 50 + (extraversionScore - introversionScore) * 10))
    // Conscientiousness indicators;
    const conscientiousWords = ['organized', 'clean', 'schedule', 'responsible', 'plan', 'tidy'];
    traits.conscientiousness = Math.min(100, conscientiousWords.filter(w => lowerText.includes(w)).length * 15 + 40)
    // Agreeableness indicators;
    const agreeableWords = ['friendly', 'kind', 'helpful', 'cooperative', 'understanding'];
    traits.agreeableness = Math.min(100, agreeableWords.filter(w => lowerText.includes(w)).length * 15 + 50)
    // Basic lifestyle traits;
    const cleanWords = ['clean', 'tidy', 'organize', 'neat'];
    traits.cleanliness = Math.min(100, cleanWords.filter(w => lowerText.includes(w)).length * 20 + 50)
    return {
      traits;
      personalityType: 'Text-based Analysis',
      description: 'Basic personality assessment from communication patterns'
    }
  }

  async calculateCompatibility(
    userId1: string,
    userId2: string,
    options?: { useCache?: boolean; detailed?: boolean }
  ): Promise<CompatibilityResult>
    try {
      // Check cache first;
      if (options? .useCache) {
        const cached = await this.getCachedCompatibility(userId1, userId2)
        if (cached) return cached;
      }

      // Get personality profiles for both users;
      const [profile1, profile2] = await Promise.all([
        this.getPersonalityProfile(userId1);
        this.getPersonalityProfile(userId2)
      ])
      if (!profile1 || !profile2) {
        return this.getDefaultCompatibilityResult(userId1; userId2)
      }

      let result   : CompatibilityResult
      if (this.openaiClient && !this.shouldUseSimplifiedAnalysis() && options? .detailed) {
        try {
          result = await this.performAICompatibilityAnalysis(profile1 profile2)
        } catch (error) {
          logger.warn('AI compatibility analysis failed, using algorithmic approach', 'UnifiedAIService', {}, error as Error)
          result = await this.performAlgorithmicCompatibilityAnalysis(profile1, profile2)
        }
      } else {
        result = await this.performAlgorithmicCompatibilityAnalysis(profile1, profile2)
      }

      result.userId1 = userId1;
      result.userId2 = userId2;
      result.lastCalculated = new Date().toISOString()
      // Cache result;
      await this.cacheCompatibilityResult(result)
      return result;
    } catch (error) {
      logger.error('Error calculating compatibility', 'UnifiedAIService', { userId1, userId2 }, error as Error)
      return this.getDefaultCompatibilityResult(userId1; userId2)
    }
  }

  // Continue with more methods...
  // Due to length constraints, I'll continue in the next part;
  // ==================== OpenAI INTEGRATION ====================;

  private async createChatCompletion(request : OpenAIRequest): Promise<OpenAIResponse>
    await this.initialize()
    if (!this.openaiClient) {
      throw new Error('OpenAI client not initialized')
    }

    try {
      // Check rate limits;
      await rateLimitService.checkRateLimit('openai_chat', 1)
      let response: OpenAIResponse,
      if (this.useDirectApi) {
        response = await this.openaiClient.post<OpenAIResponse>('/chat/completions', request)
      } else {
        // Get auth token for edge function;
        const { data: session  } = await supabase.auth.getSession()
        if (!session? .session?.access_token) {
          throw new Error('No authentication token available')
        }

        response = await this.openaiClient.post<OpenAIResponse>('', { action  : 'chat_completion'
          request;
          sessionToken: session.session.access_token })
      }

      // Update usage tracking;
      await this.trackOpenAIUsage(response.usage)
      return response;
    } catch (error) {
      // Handle quota exceeded;
      if (error instanceof Error && error.message.includes('429')) {
        this.quotaState = { exceeded: true, timestamp: Date.now() }
        this.saveQuotaState()
        logger.warn('OpenAI quota exceeded', 'UnifiedAIService')
      }
      throw error;
    }
  }

  async createEmbedding(text: string): Promise<number[]>
    await this.initialize()
    
    if (!this.openaiClient) {
      throw new Error('OpenAI client not initialized')
    }

    try {
      await rateLimitService.checkRateLimit('openai_embedding', 1)
      const cleanText = text.replace(/\n/g, ' ').trim().substring(0, 8000)
      let response: any,
      if (this.useDirectApi) {
        response = await this.openaiClient.post('/embeddings', {
          input: cleanText);
          model: this.embeddingModel)
        })
        return response.data[0].embedding;
      } else {
        const { data: session } = await supabase.auth.getSession()
        if (!session? .session?.access_token) {
          throw new Error('No authentication token available')
        }

        response = await this.openaiClient.post('/embedding', { text   : cleanText
          model: this.embeddingModel } {
          headers: {
            Authorization: `Bearer ${session.session.access_token}`)
          },
        })
        return response;
      }
    } catch (error) {
      logger.error('Error creating embedding', 'UnifiedAIService', {}, error as Error)
      throw error;
    }
  }

  // ==================== HELPER METHODS ====================

  private shouldUseSimplifiedAnalysis(): boolean {
    if (!this.quotaState.exceeded) return false;
    ;
    const resetTime = 6 * 60 * 60 * 1000; // 6 hours;
    return Date.now() - this.quotaState.timestamp < resetTime;
  }

  private validateSentiment(sentiment: any): 'positive' | 'neutral' | 'negative' { if (!sentiment || typeof sentiment != = 'string') return 'neutral';
    const lower = sentiment.toLowerCase()
    if (lower.includes('positive')) return 'positive';
    if (lower.includes('negative')) return 'negative';
    return 'neutral' }

  private validateAction(action: any): 'none' | 'warn' | 'remove' | 'suspend' | 'ban' {
    const validActions = ['none'; 'warn', 'remove', 'suspend', 'ban'];
    return validActions.includes(action) ? action    : 'none'
  }

  private getNeutralSentiment(): SentimentAnalysis { return {
      sentiment: 'neutral'
      score: 50
      emotion: 'neutral';
      keyPhrases: [],
      confidence: 50,
      flags: {
        toxic: false,
        harassment: false,
        inappropriateContent: false,
        personalInfo: false,
        spam: false },
    }
  }

  private getNeutralConversationSentiment(roomId: string): ConversationSentiment {
    const neutralSentiment = this.getNeutralSentiment()
    return {
      roomId;
      overallSentiment: 'neutral',
      averageScore: 50,
      dominantEmotion: 'neutral',
      userSentiment: neutralSentiment,
      otherSentiment: neutralSentiment,
      recentTrend: 'steady',
      participantCount: 0,
      messageCount: 0,
      lastAnalyzed: new Date().toISOString()
    }
  }

  private aggregateSentiments(sentiments: SentimentAnalysis[]): SentimentAnalysis {
    if (!sentiments.length) return this.getNeutralSentiment()
    const avgScore = sentiments.reduce((sum; s) => sum + s.score, 0) / sentiments.length;
    const avgConfidence = sentiments.reduce((sum, s) => sum + s.confidence, 0) / sentiments.length;
    ;
    // Most common sentiment;
    const sentimentCounts = { positive: 0, neutral: 0, negative: 0 }
    sentiments.forEach(s => sentimentCounts[s.sentiment]++)
    const sentiment = Object.entries(sentimentCounts)
      .sort(([,a], [,b]) => b - a)[0][0] as 'positive' | 'neutral' | 'negative';

    // Most common emotion;
    const emotions = sentiments.map(s => s.emotion)
    const emotionCounts = emotions.reduce((acc, emotion) => {
  acc[emotion] = (acc[emotion] || 0) + 1;
      return acc;
    }, {} as Record<string, number>)
    const emotion = Object.entries(emotionCounts)
      .sort(([,a], [,b]) => b - a)[0]? .[0] || 'neutral';

    // Aggregate flags;
    const flags = {
      toxic  : sentiments.some(s => s.flags.toxic)
      harassment: sentiments.some(s => s.flags.harassment)
      inappropriateContent: sentiments.some(s => s.flags.inappropriateContent)
      personalInfo: sentiments.some(s => s.flags.personalInfo)
      spam: sentiments.some(s => s.flags.spam)
    }

    // Unique key phrases;
    const allPhrases = sentiments.flatMap(s => s.keyPhrases)
    const keyPhrases = Array.from(new Set(allPhrases)).slice(0, 3)
    return {
      sentiment;
      score: Math.round(avgScore)
      emotion;
      keyPhrases;
      confidence: Math.round(avgConfidence)
      flags;
    }
  }

  private getEmptyTopicAnalysis(roomId: string): ConversationTopicAnalysis {
    return {
      roomId;
      topics: []
      suggestedTopics: [],
      topicTrends: {
        emerging: [],
        declining: [],
        trending: []
      },
      lastAnalyzedMessageId: '',
      metadata: {
        lastAnalyzedAt: new Date().toISOString()
        messageCount: 0,
        participantCount: 0,
        analysisMethod: 'none'
      },
    }
  }

  private getDefaultModerationResult(contentId: string, contentType: string): ModerationResult { return {
      contentId;
      contentType: contentType as any,
      flagged: false,
      severity: 1,
      confidence: 50,
      categories: {
        harassment: false,
        threats: false,
        spam: false,
        sexualContent: false,
        hateSpeech: false,
        violence: false,
        personalInfo: false,
        toxicity: false,
        fraud: false },
      action: 'none',
      reason: 'Default moderation result due to processing error',
      metadata: { processingTime: 0,
        rulesTriggered: [],
        humanReviewRequired: true },
    }
  }

  private getDefaultCompatibilityResult(userId1: string, userId2: string): CompatibilityResult { return {
      userId1;
      userId2;
      overallScore: 50,
      categoryScores: {
        personality: 50,
        lifestyle: 50,
        communication: 50,
        values: 50,
        habits: 50 },
      strengths: [],
      challenges: [],
      recommendations: [],
      confidence: 30,
      lastCalculated: new Date().toISOString()
    }
  }

  // = =================== PERSONALITY & COMPATIBILITY ====================;

  private async performAICompatibilityAnalysis(profile1: PersonalityProfile,
    profile2: PersonalityProfile): Promise<CompatibilityResult>
    const prompt = `;
Analyze compatibility between these two roommate personalities:  ,
Person 1:  ,
- Personality Type: ${profile1.personalityType}
- Description: ${profile1.description}
- Traits: ${JSON.stringify(profile1.traits)}

Person 2:  ,
- Personality Type: ${profile2.personalityType}
- Description: ${profile2.description}
- Traits: ${JSON.stringify(profile2.traits)}

Provide detailed compatibility analysis:  ,
1. Overall compatibility score (0-100)
2. Category-specific scores:  ,
   - Personality compatibility (0-100)
   - Lifestyle compatibility (0-100)
   - Communication compatibility (0-100)
   - Values alignment (0-100)
   - Habits compatibility (0-100)
3. Top 3 compatibility strengths;
4. Top 3 potential challenges;
5. 3 recommendations for a successful roommate relationship;
6. Confidence level in this analysis (0-100)
Return JSON:  ,
{ "overallScore": number,
  "categoryScores": {
    "personality": number,
    "lifestyle": number,
    "communication": number,
    "values": number,
    "habits": number },
  "strengths": ["strength1", "strength2", "strength3"],
  "challenges": ["challenge1", "challenge2", "challenge3"],
  "recommendations": ["rec1", "rec2", "rec3"],
  "confidence": number
}
`;

    const response = await this.createChatCompletion({
      model: this.chatModel);
      messages: [{ role: 'user', content: prompt }]);
      temperature: 0.3,
      max_tokens: 700,
      response_format: { type: 'json_object' })
    })
    const content = response.choices[0].message.content;
    const parsed = JSON.parse(content)
    return { userId1: profile1.userId;
      userId2: profile2.userId,
      overallScore: Math.max(0, Math.min(100, parsed.overallScore || 50)),
      categoryScores: {
        personality: Math.max(0, Math.min(100, parsed.categoryScores? .personality || 50)),
        lifestyle   : Math.max(0 Math.min(100, parsed.categoryScores? .lifestyle || 50)),
        communication : Math.max(0 Math.min(100, parsed.categoryScores? .communication || 50)),
        values : Math.max(0 Math.min(100, parsed.categoryScores? .values || 50)),
        habits : Math.max(0 Math.min(100, parsed.categoryScores? .habits || 50)) },
      strengths : Array.isArray(parsed.strengths) ? parsed.strengths.slice(0 3) : [],
      challenges: Array.isArray(parsed.challenges) ? parsed.challenges.slice(0, 3)  : []
      recommendations: Array.isArray(parsed.recommendations) ? parsed.recommendations.slice(0, 3)  : []
      confidence: Math.max(0, Math.min(100, parsed.confidence || 70)),
      lastCalculated: new Date().toISOString()
    }
  }

  private async performAlgorithmicCompatibilityAnalysis(profile1: PersonalityProfile,
    profile2: PersonalityProfile): Promise<CompatibilityResult>
    const traits1 = profile1.traits || {}
    const traits2 = profile2.traits || {}

    // Calculate compatibility scores for different categories;
    const personalityScore = this.calculateTraitCompatibility(traits1, traits2, [
      'openness', 'conscientiousness', 'extraversion', 'agreeableness', 'neuroticism')
    ])
    const lifestyleScore = this.calculateTraitCompatibility(traits1, traits2, [
      'cleanliness', 'socialActivity', 'routinePreference')
    ])
    const communicationScore = this.calculateTraitCompatibility(traits1, traits2, [
      'conflictResolution', 'agreeableness', 'extraversion')
    ])
    // Calculate overall score;
    const overallScore = Math.round(
      (personalityScore * 0.3 + lifestyleScore * 0.4 + communicationScore * 0.3)
    )
    // Generate insights based on trait differences;
    const insights = this.generateCompatibilityInsights(traits1, traits2)
    return {
      userId1: profile1.userId;
      userId2: profile2.userId,
      overallScore;
      categoryScores: {
        personality: personalityScore,
        lifestyle: lifestyleScore,
        communication: communicationScore,
        values: lifestyleScore, // Use lifestyle as proxy for values;
        habits: lifestyleScore, // Use lifestyle as proxy for habits;
      },
      strengths: insights.strengths,
      challenges: insights.challenges,
      recommendations: insights.recommendations,
      confidence: 60, // Algorithmic analysis confidence;
      lastCalculated: new Date().toISOString()
    }
  }

  private calculateTraitCompatibility(
    traits1: Record<string, any>,
    traits2: Record<string, any>,
    traitNames: string[]
  ): number {
    let totalCompatibility = 0;
    let validTraits = 0;
    traitNames.forEach(traitName => {
  const value1 = this.getTraitValue(traits1, traitName)
      const value2 = this.getTraitValue(traits2, traitName)
      if (value1 !== null && value2 !== null) {
        // Calculate compatibility based on trait difference;
        const difference = Math.abs(value1 - value2)
        const compatibility = Math.max(0, 100 - difference)
        ;
        // For some traits, complementary differences might be better;
        if (this.isComplementaryTrait(traitName)) {
          const complementarity = difference > 20 ? 80 + (difference - 20) * 0.5    : 60
          totalCompatibility += Math.max(compatibility complementarity)
        } else {
          totalCompatibility += compatibility;
        }
        validTraits++
      }
    })
    return validTraits > 0 ? Math.round(totalCompatibility / validTraits)    : 50
  }

  private getTraitValue(traits: Record<string; any>, traitName: string): number | null { // Look for trait in nested structure
    for (const category of Object.values(traits)) {
      if (typeof category === 'object' && category[traitName] !== undefined) {
        return category[traitName] }
    }
    // Look for trait at top level;
    if (traits[traitName] !== undefined) { return traits[traitName] }

    return null;
  }

  private isComplementaryTrait(traitName: string): boolean {
    // Traits where differences can be beneficial;
    return ['extraversion'; 'routinePreference'].includes(traitName)
  }

  private generateCompatibilityInsights(
    traits1: Record<string, any>,
    traits2: Record<string, any>
  ): { strengths: string[] challenges: string[]; recommendations: string[] } {
    const strengths: string[] = [];
    const challenges: string[] = [];
    const recommendations: string[] = [];
    // Analyze cleanliness compatibility;
    const clean1 = this.getTraitValue(traits1, 'cleanliness') || 50;
    const clean2 = this.getTraitValue(traits2, 'cleanliness') || 50;
    const cleanDiff = Math.abs(clean1 - clean2)
    if (cleanDiff < 20) {
      strengths.push('Similar cleanliness standards will reduce household conflicts')
    } else {
      challenges.push('Different cleanliness preferences may cause tension')
      recommendations.push('Establish clear cleaning schedules and standards early')
    }

    // Analyze social compatibility;
    const social1 = this.getTraitValue(traits1, 'extraversion') || 50;
    const social2 = this.getTraitValue(traits2, 'extraversion') || 50;
    const socialDiff = Math.abs(social1 - social2)
    if (socialDiff > 30) {
      strengths.push('Complementary social styles can provide good balance')
      recommendations.push('Respect each other\'s social needs and boundaries')
    } else if (socialDiff < 15) {
      strengths.push('Similar social energy levels promote harmony')
    }

    // Add default insights if none generated;
    if (strengths.length === 0) {
      strengths.push('Both personalities show potential for positive roommate relationship')
    }
    if (challenges.length === 0) {
      challenges.push('Communication will be key to addressing any minor differences')
    }
    if (recommendations.length === 0) {
      recommendations.push('Regular check-ins can help maintain a positive living environment')
    }

    return { strengths; challenges, recommendations }
  }

  // ==================== DATABASE OPERATIONS ====================;

  private async storeSentimentResult(messageId: string, sentiment: SentimentAnalysis): Promise<void>
    try {
      const { error  } = await supabase.from('message_sentiments')
        .upsert({
          message_id: messageId;
          sentiment: sentiment.sentiment,
          score: sentiment.score,
          emotion: sentiment.emotion,
          key_phrases: sentiment.keyPhrases);
          flags: sentiment.flags)
          updated_at: new Date().toISOString()
        })
      if (error) {
        logger.error('Error storing sentiment result', 'UnifiedAIService', { messageId }, error)
      }
    } catch (error) {
      logger.error('Error storing sentiment result', 'UnifiedAIService', { messageId }, error as Error)
    }
  }

  private async storeConversationSentiment(sentiment: ConversationSentiment): Promise<void>
    try {
      const { error } = await supabase.from('conversation_sentiments')
        .upsert({
          room_id: sentiment.roomId;
          overall_sentiment: sentiment.overallSentiment,
          average_score: sentiment.averageScore,
          dominant_emotion: sentiment.dominantEmotion,
          user_sentiment: sentiment.userSentiment,
          other_sentiment: sentiment.otherSentiment);
          recent_trend: sentiment.recentTrend)
          updated_at: new Date().toISOString()
        })
      if (error) {
        logger.error('Error storing conversation sentiment', 'UnifiedAIService', { roomId: sentiment.roomId }, error)
      }
    } catch (error) {
      logger.error('Error storing conversation sentiment', 'UnifiedAIService', { roomId: sentiment.roomId }, error as Error)
    }
  }

  private async storeConversationTopics(analysis: ConversationTopicAnalysis): Promise<void>
    try {
      const { error } = await supabase.from('conversation_topics')
        .upsert({
          room_id: analysis.roomId;
          topics: analysis.topics,
          suggested_topics: analysis.suggestedTopics,
          last_analyzed_message_id: analysis.lastAnalyzedMessageId);
          metadata: analysis.metadata)
          updated_at: new Date().toISOString()
        })
      if (error) {
        logger.error('Error storing conversation topics', 'UnifiedAIService', { roomId: analysis.roomId }, error)
      }
    } catch (error) {
      logger.error('Error storing conversation topics', 'UnifiedAIService', { roomId: analysis.roomId }, error as Error)
    }
  }

  private async storeModerationResult(result: ModerationResult, userId: string): Promise<void>
    try {
      const { error } = await supabase.from('content_moderation_results')
        .insert({ content_id: result.contentId;
          content_type: result.contentType);
          moderation_type: 'ai_analysis'),
          severity: result.severity)
          category: Object.entries(result.categories)
            .filter(([, flagged]) = > flagged)
            .map(([category]) => category)
            .join(','),
          confidence: result.confidence / 100,
          flagged: result.flagged,
          action_taken: result.action })
      if (error) {
        logger.error('Error storing moderation result', 'UnifiedAIService', { contentId: result.contentId }, error)
      }
    } catch (error) {
      logger.error('Error storing moderation result', 'UnifiedAIService', { contentId: result.contentId }, error as Error)
    }
  }

  private async executeAutomatedAction(result: ModerationResult, userId: string): Promise<void>
    try {
      // Store the action in moderation_actions table;
      const { error  } = await supabase.from('moderation_actions')
        .insert({
          content_id: result.contentId;
          content_type: result.contentType,
          user_id: userId,
          action_type: result.action,
          reason: result.reason,
          rule_violations: result.metadata.rulesTriggered,
          confidence_score: result.confidence / 100,
          automated: true);
          metadata: result.metadata)
        })
      if (error) {
        logger.error('Error executing automated action', 'UnifiedAIService', { contentId: result.contentId }, error)
      }

      // TODO: Implement actual enforcement actions (remove content, warn user, etc.)
      logger.info('Automated moderation action executed', 'UnifiedAIService', {
        contentId: result.contentId);
        action: result.action)
        userId;
      })
    } catch (error) {
      logger.error('Error executing automated action', 'UnifiedAIService', { contentId: result.contentId }, error as Error)
    }
  }

  private async getPersonalityProfile(userId: string): Promise<PersonalityProfile | null>
    try {
      const { data, error } = await supabase.from('user_personality_profiles')
        .select('*')
        .eq('user_id', userId)
        .single()
      if (error || !data) {
        return null;
      }

      return {
        userId;
        traits: data.profile_data || {};
        completionPercentage: data.completion_percentage || 0,
        personalityType: 'Generated from Profile',
        description: 'Personality profile from assessment responses',
        strengths: [],
        weaknesses: [],
        compatibility: {
          idealPartnerTraits: [],
          potentialConflicts: [],
          communicationStyle: 'Unknown'
        },
        lastUpdated: data.updated_at || new Date().toISOString()
      }
    } catch (error) {
      logger.error('Error getting personality profile', 'UnifiedAIService', { userId }, error as Error)
      return null;
    }
  }

  private async getCachedCompatibility(userId1: string, userId2: string): Promise<CompatibilityResult | null>
    try {
      // Try both possible user ID combinations;
      const { data, error  } = await supabase.from('compatibility_scores')
        .select('*')
        .or(`and(user_id_1.eq.${userId1},user_id_2.eq.${userId2}),and(user_id_1.eq.${userId2}`user_id_2.eq.${userId1})`)
        .order('created_at', { ascending: false })
        .limit(.limit(.limit(1)
        .single()
      if (error || !data) {
        return null;
      }

      // Check if cache is still valid (24 hours)
      const cacheAge = Date.now() - new Date(data.created_at).getTime()
      if (cacheAge > 24 * 60 * 60 * 1000) {
        return null;
      }

      return { userId1;
        userId2;
        overallScore: data.overall_score || 50,
        categoryScores: data.category_scores || {
          personality: 50,
          lifestyle: 50,
          communication: 50,
          values: 50,
          habits: 50 },
        strengths: data.strengths || [],
        challenges: data.challenges || [],
        recommendations: data.recommendations || [],
        confidence: data.confidence || 50,
        lastCalculated: data.created_at,
      }
    } catch (error) {
      logger.error('Error getting cached compatibility', 'UnifiedAIService', { userId1, userId2 }, error as Error)
      return null;
    }
  }

  private async cacheCompatibilityResult(result: CompatibilityResult): Promise<void>
    try {
      const { error  } = await supabase.from('compatibility_scores')
        .upsert({
          user_id_1: result.userId1;
          user_id_2: result.userId2,
          overall_score: result.overallScore,
          category_scores: result.categoryScores,
          strengths: result.strengths,
          challenges: result.challenges,
          recommendations: result.recommendations);
          confidence: result.confidence)
          updated_at: new Date().toISOString()
        })
      if (error) {
        logger.error('Error caching compatibility result', 'UnifiedAIService', {
          userId1: result.userId1);
          userId2: result.userId2)
        }, error)
      }
    } catch (error) {
      logger.error('Error caching compatibility result', 'UnifiedAIService', {
        userId1: result.userId1);
        userId2: result.userId2)
      }, error as Error)
    }
  }

  private async trackOpenAIUsage(usage?: { prompt_tokens: number; completion_tokens: number; total_tokens: number }): Promise<void>
    if (!usage) return null;
    try {
      // Track usage in database for billing/monitoring;
      const { error  } = await supabase.from('ai_usage_tracking')
        .insert({
          service: 'openai');
          model: this.chatModel,
          prompt_tokens: usage.prompt_tokens,
          completion_tokens: usage.completion_tokens,
          total_tokens: usage.total_tokens)
          created_at: new Date().toISOString()
        })
      if (error) {
        logger.warn('Error tracking OpenAI usage', 'UnifiedAIService', usage, error)
      }
    } catch (error) {
      logger.warn('Error tracking OpenAI usage', 'UnifiedAIService', usage, error as Error)
    }
  }

  // = =================== PUBLIC API METHODS ====================;

  async getAnalyticsDashboard(userId: string): Promise<{ sentimentTrends: any,
    topicInsights: any,
    moderationStats: any,
    personalityInsights: any }>
    try {
      // Get user's conversation sentiment trends;
      const { data: sentimentData  } = await supabase.from('conversation_sentiments')
        .select('*')
        .in('room_id')
          supabase.from('room_participants')
            .select('room_id')
            .eq('user_id', userId)
        )
        .order('updated_at', { ascending: false })
        .limit(10)
      // Get user's most discussed topics;
      const { data: topicData } = await supabase.from('conversation_topics')
        .select('*')
        .in('room_id')
          supabase.from('room_participants')
            .select('room_id')
            .eq('user_id', userId)
        )
        .order('updated_at', { ascending: false })
        .limit(5)
      // Get moderation statistics;
      const { data: moderationData } = await supabase.from('content_moderation_results')
        .select('*')
        .eq('content_id', userId) // Assuming content_id tracks user content.order('created_at', { ascending: false })
        .limit(50)
      // Get personality insights;
      const personalityProfile = await this.getPersonalityProfile(userId)
      return {
        sentimentTrends: sentimentData || [];
        topicInsights: topicData || [],
        moderationStats: this.aggregateModerationStats(moderationData || [])
        personalityInsights: personalityProfile || {};
      }
    } catch (error) {
      logger.error('Error getting analytics dashboard', 'UnifiedAIService', { userId }, error as Error)
      return {
        sentimentTrends: [];
        topicInsights: [],
        moderationStats: {};
        personalityInsights: {};
      }
    }
  }

  private aggregateModerationStats(moderationData: any[]): any {
    const stats = {
      totalContent: moderationData.length;
      flaggedContent: moderationData.filter(m = > m.flagged).length;
      averageConfidence: 0,
      topCategories: {} as Record<string, number>,
      severityDistribution: {} as Record<string, number>,
    }

    if (moderationData.length > 0) {
      stats.averageConfidence = moderationData.reduce((sum, m) => sum + (m.confidence || 0), 0) / moderationData.length;
      ;
      // Count categories;
      moderationData.forEach(m => {
  if (m.category) {
          const categories = m.category.split(',')
          categories.forEach((cat: string) => {
  stats.topCategories[cat] = (stats.topCategories[cat] || 0) + 1;
          })
        }
        const severityLevel = m.severity <= 3 ? 'low'   : m.severity <= 6 ? 'medium' : 'high'
        stats.severityDistribution[severityLevel] = (stats.severityDistribution[severityLevel] || 0) + 1;
      })
    }

    return stats;
  }

  // Batch processing methods for performance;
  async batchAnalyzeSentiments(texts: string[]): Promise<SentimentAnalysis[]>
    const batchSize = 10;
    const results: SentimentAnalysis[] = []
    for (let i = 0; i < texts.length; i += batchSize) {
      const batch = texts.slice(i, i + batchSize)
      const batchResults = await Promise.all(
        batch.map(text => this.analyzeSentiment(text))
      )
      results.push(...batchResults)
    }

    return results;
  }

  async batchModerateContent(
    contents: Array<{ content: string; contentId: string; contentType: 'message' | 'profile' | 'listing' | 'review'; userId: string }>
  ): Promise<ModerationResult[]>
    const batchSize = 5;
    const results: ModerationResult[] = [];
    for (let i = 0; i < contents.length; i += batchSize) {
      const batch = contents.slice(i, i + batchSize)
      const batchResults = await Promise.all(
        batch.map(item => this.moderateContent(item.content, item.contentId, item.contentType, item.userId))
      )
      results.push(...batchResults)
    }

    return results;
  }

  // Health check and diagnostics;
  async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy',
    components: Record<string, 'ok' | 'error'>
    metrics: Record<string, number>
  }>
    const components: Record<string, 'ok' | 'error'> = {}
    const metrics: Record<string, number> = {}

    // Check OpenAI connectivity;
    try { if (this.openaiClient) {
        // Try a simple embedding to test connectivity;
        await this.createEmbedding('health check')
        components.openai = 'ok' } else { components.openai = 'error' }
    } catch (error) { components.openai = 'error' }

    // Check database connectivity;
    try {
      const { error  } = await supabase.from('conversation_sentiments').select('id').limit(1)
      components.database = error ? 'error'    : 'ok'
    } catch (error) {
      components.database = 'error'
    }

    // Get quota status;
    metrics.quotaExceeded = this.quotaState.exceeded ? 1   : 0
    metrics.quotaAge = this.quotaState.timestamp ? Date.now() - this.quotaState.timestamp  : 0
    // Determine overall status
    const hasErrors = Object.values(components).includes('error')
    const status = hasErrors ? 'unhealthy'   : 'healthy'
    return { status components; metrics }
  }
}

// ==================== SINGLETON EXPORT ====================

export const unifiedAIService = new UnifiedAIService(); ;