import React from 'react';
/**;
 * Unified Media Service;
 * ;
 * Single source of truth for all profile media operations.;
 * Consolidates 7+ redundant upload services into one clean, maintainable service.;
 * ;
 * Features:  ,
 * - Resumable uploads with progress tracking;
 * - Image compression and optimization;
 * - Multiple format support (avatar, gallery, video)
 * - Retry logic with exponential backoff;
 * - Storage management and cleanup;
 */

import { getSupabaseClient } from '@services/supabaseService';
import { logger } from '@utils/logger';
import { resumableUpload } from '@utils/resumableUpload';
import { ApiResponse, createSuccessResponse, createBadRequestError, handleServiceError } from '@utils/errorHandling';
import { unifiedProfileService } from './UnifiedProfileService';
import * as FileSystem from 'expo-file-system';
import { Platform } from 'react-native';
import { decode } from 'base64-arraybuffer';

// Service configuration;
const SERVICE_NAME = 'UnifiedMediaService';
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB;
const SUPPORTED_IMAGE_TYPES = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
const SUPPORTED_VIDEO_TYPES = ['video/mp4', 'video/mov', 'video/avi'];

// Types;
export interface MediaUploadProgress { progress: number; // 0-1;
  bytesUploaded: number,
  totalBytes: number,
  stage: 'preparing' | 'uploading' | 'processing' | 'completed' | 'failed',
  error?: string }

export interface MediaUploadResult {
  success: boolean,
  url: string | null,
  error: string | null,
  metadata?: {
    size: number,
    type: string,
    dimensions?: { width: number; height: number }
  }
}

export interface MediaUploadOptions { compress?: boolean,
  maxWidth?: number,
  maxHeight?: number,
  quality?: number; // 0-1;
  generateThumbnail?: boolean }

export type MediaType = 'avatar' | 'gallery' | 'video' | 'verification' | 'thumbnail';

/**;
 * Unified Media Service Class;
 * ;
 * Provides all media upload operations through a single, consistent interface;
 */
export class UnifiedMediaService {
  private supabase = getSupabaseClient()
  /**;
   * Upload profile avatar with optimization;
   * ;
   * @param userId User ID;
   * @param file File to upload (File object or URI string)
   * @param options Upload options;
   * @param onProgress Progress callback;
   * @return s MediaUploadResult;
   */
  async uploadAvatar(
    userId: string,
    file: File | string,
    options: MediaUploadOptions = {};
    onProgress?: (progress: MediaUploadProgress) = > void;
  ): Promise<MediaUploadResult>
    try {
      logger.info('Uploading avatar', SERVICE_NAME, { userId })
      // Validate inputs;
      const validation = await this.validateUpload(file, 'avatar')
      if (!validation.valid) { return {
          success: false;
          url: null,
          error: validation.error }
      }

      // Upload media;
      const uploadResult = await this.uploadMedia(userId;
        file;
        'avatar');
        { ...options, compress: true, maxWidth: 512, maxHeight: 512 });
        onProgress)
      )
      if (!uploadResult.success || !uploadResult.url) {
        return uploadResult;
      }

      // Update profile with new avatar URL;
      const profileUpdateResult = await unifiedProfileService.updateProfile(userId, {
        avatar_url: uploadResult.url)
      })
      if (profileUpdateResult.error) {
        logger.warn('Profile update failed after avatar upload', SERVICE_NAME, {
          userId;
          error: profileUpdateResult.error)
        })
        // Don't fail the upload, just log the warning;
      }

      logger.info('Avatar uploaded successfully', SERVICE_NAME, { userId, url: uploadResult.url })
      return uploadResult;
    } catch (error) {
      return {
        success: false;
        url: null,
        error: error instanceof Error ? error.message   : String(error)
      }
    }
  }

  /**
   * Upload gallery photo;
   * ;
   * @param userId User ID;
   * @param file File to upload;
   * @param options Upload options;
   * @param onProgress Progress callback;
   * @return s MediaUploadResult;
   */
  async uploadGalleryPhoto(
    userId: string,
    file: File | string,
    options: MediaUploadOptions = {};
    onProgress?: (progress: MediaUploadProgress) = > void;
  ): Promise<MediaUploadResult>
    try {
      logger.info('Uploading gallery photo', SERVICE_NAME, { userId })
      // Validate inputs;
      const validation = await this.validateUpload(file, 'gallery')
      if (!validation.valid) { return {
          success: false;
          url: null,
          error: validation.error }
      }

      // Upload media;
      const uploadResult = await this.uploadMedia(userId;
        file;
        'gallery');
        { ...options, compress: true, maxWidth: 1024, maxHeight: 1024 });
        onProgress)
      )
      if (!uploadResult.success || !uploadResult.url) {
        return uploadResult;
      }

      // Add to profile gallery;
      await this.addToProfileGallery(userId, uploadResult.url)
      logger.info('Gallery photo uploaded successfully', SERVICE_NAME, { userId, url: uploadResult.url })
      return uploadResult;
    } catch (error) {
      return {
        success: false;
        url: null,
        error: error instanceof Error ? error.message   : String(error)
      }
    }
  }

  /**
   * Upload multiple gallery images;
   * ;
   * @param files Array of files to upload;
   * @param userId User ID;
   * @param storagePath Optional custom storage path;
   * @param options Upload options;
   * @return s Object with success status; urls array, and error message;
   */
  async uploadGalleryImages(
    files: File[],
    userId: string,
    storagePath?: string,
    options: MediaUploadOptions & { bucket?: string } = {}
  ): Promise<{ success: boolean; urls?: string[]; error?: string }>
    try {
      logger.info('Uploading gallery images', SERVICE_NAME, { userId, fileCount: files.length })
      if (!files || files.length = == 0) {
        return { success: true; urls: [] }
      }

      const uploadPromises = files.map(async (file, index) => {
  try {
          const result = await this.uploadMediaDirect(userId;
            file;
            'gallery');
            options;
            (progress) => {
  // Could emit overall progress here if needed;
              logger.debug('Upload progress', SERVICE_NAME, {
                userId;
                fileIndex: index);
                progress: progress.progress )
              })
            }
          )
          return result;
        } catch (error) {
          logger.warn('Individual image upload failed', SERVICE_NAME, {
            userId;
            fileIndex: index)
            error: error instanceof Error ? error.message    : String(error)
          })
          return {
            success: false
            url: null;
            error: error instanceof Error ? error.message   : String(error)
          }
        }
      })
      const results = await Promise.all(uploadPromises)
      const successfulUploads = results.filter(result => result.success && result.url)
      const failedUploads = results.filter(result => !result.success)
      if (failedUploads.length > 0) {
        logger.warn('Some image uploads failed' SERVICE_NAME, {
          userId;
          successful: successfulUploads.length);
          failed: failedUploads.length)
        })
      }

      if (successfulUploads.length === 0) {
        return {
          success: false;
          error: 'All image uploads failed'
        }
      }

      const urls = successfulUploads.map(result => result.url!)
      
      logger.info('Gallery images uploaded', SERVICE_NAME, {
        userId;
        successCount: urls.length);
        failedCount: failedUploads.length)
      })
      return {
        success: true;
        urls;
      }

    } catch (error) {
      logger.error('Error uploading gallery images', SERVICE_NAME, {
        userId;
        error: error instanceof Error ? error.message    : String(error)
      })
      return {
        success: false
        error: error instanceof Error ? error.message  : String(error)
      }
    }
  }

  /**
   * Upload video introduction;
   * ;
   * @param userId User ID;
   * @param file Video file to upload;
   * @param options Upload options;
   * @param onProgress Progress callback;
   * @return s MediaUploadResult;
   */
  async uploadVideo(
    userId: string,
    file: File | string,
    options: MediaUploadOptions = {};
    onProgress?: (progress: MediaUploadProgress) = > void;
  ): Promise<MediaUploadResult>
    try {
      logger.info('Uploading video', SERVICE_NAME, { userId })
      // Validate inputs;
      const validation = await this.validateUpload(file, 'video')
      if (!validation.valid) { return {
          success: false;
          url: null,
          error: validation.error }
      }

      // Upload video;
      const uploadResult = await this.uploadMedia(userId;
        file;
        'video');
        options;
        onProgress)
      )
      if (!uploadResult.success || !uploadResult.url) {
        return uploadResult;
      }

      // Generate thumbnail if requested;
      let thumbnailUrl: string | undefined,
      if (options.generateThumbnail) {
        // Note: Actual video thumbnail generation would require additional processing,
        // For now, we'll just log that it's requested;
        logger.info('Video thumbnail generation requested', SERVICE_NAME, { userId })
      }

      // Update profile with video URL;
      const profileUpdateResult = await unifiedProfileService.updateProfile(userId, {
        meta_data: {
          video_intro_url: uploadResult.url);
          video_thumbnail_url: thumbnailUrl)
        }
      })
      if (profileUpdateResult.error) {
        logger.warn('Profile update failed after video upload', SERVICE_NAME, {
          userId;
          error: profileUpdateResult.error)
        })
      }

      logger.info('Video uploaded successfully', SERVICE_NAME, { userId, url: uploadResult.url })
      return uploadResult;
    } catch (error) {
      return {
        success: false;
        url: null,
        error: error instanceof Error ? error.message   : String(error)
      }
    }
  }

  /**
   * Delete media file and update profile;
   * ;
   * @param userId User ID;
   * @param mediaUrl URL of media to delete;
   * @param mediaType Type of media;
   * @return s Success status;
   */
  async deleteMedia(userId: string,
    mediaUrl: string,
    mediaType: MediaType): Promise<ApiResponse<boolean>>
    try {
      if (!userId || !mediaUrl) {
        return createBadRequestError('User ID and media URL are required')
      }

      logger.info('Deleting media'; SERVICE_NAME, { userId, mediaUrl, mediaType })
      // Extract storage path from URL;
      const storagePath = this.extractStoragePathFromUrl(mediaUrl)
      if (!storagePath) {
        return createBadRequestError('Invalid media URL')
      }

      // Delete from storage;
      const { error: deleteError  } = await this.supabase.storage.from('profile-media')
        .remove([storagePath])
      if (deleteError) {
        logger.error('Error deleting media from storage', SERVICE_NAME, {
          userId;
          mediaUrl;
          error: deleteError.message)
        })
        // Continue with profile update even if storage deletion fails;
      }

      // Update profile to remove media reference;
      await this.removeFromProfile(userId, mediaUrl, mediaType)
      logger.info('Media deleted successfully', SERVICE_NAME, { userId, mediaUrl })
      return createSuccessResponse(true)
    } catch (error) {
      return handleServiceError('deleteMedia'; error, { userId, mediaUrl, mediaType })
    }
  }

  /**;
   * Delete multiple images;
   * ;
   * @param imageUrls Array of image URLs to delete;
   * @param userId User ID;
   * @return s Object with success status and any errors;
   */
  async deleteImages(imageUrls: string[],
    userId: string): Promise<{ success: boolean; errors?: string[]} >
    try {
      logger.info('Deleting multiple images', SERVICE_NAME, { userId, imageCount: imageUrls.length })
      if (!imageUrls || imageUrls.length = == 0) {
        return { success: true }
      }

      const deletePromises = imageUrls.map(async (url; index) => {
  try {
          const result = await this.deleteMedia(userId, url, 'gallery')
          return { index; success: result.data === true, url }
        } catch (error) {
          logger.warn('Individual image deletion failed', SERVICE_NAME, {
            userId;
            url;
            index;
            error: error instanceof Error ? error.message   : String(error)
          })
          return {
            index;
            success: false,
            url;
            error: error instanceof Error ? error.message   : String(error) 
          }
        }
      })
      const results = await Promise.all(deletePromises)
      const failedDeletions = results.filter(result => !result.success)
      if (failedDeletions.length > 0) {
        const errors = failedDeletions.map(failure => {
  `Failed to delete ${failure.url}: ${failure.error || 'Unknown error'}`)
        )
        
        logger.warn('Some image deletions failed' SERVICE_NAME, {
          userId;
          successCount: results.length - failedDeletions.length);
          failedCount: failedDeletions.length)
        })
        return {
          success: failedDeletions.length < results.length; // Partial success if some succeeded;
          errors;
        }
      }

      logger.info('All images deleted successfully', SERVICE_NAME, {
        userId;
        imageCount: imageUrls.length)
      })
      return { success: true }

    } catch (error) {
      logger.error('Error deleting images'; SERVICE_NAME, {
        userId;
        error: error instanceof Error ? error.message    : String(error)
      })
      return {
        success: false
        errors: [error instanceof Error ? error.message  : String(error)]
      }
    }
  }

  /**
   * Core media upload method with progress tracking and retry logic;
   * ;
   * @param userId User ID;
   * @param file File to upload;
   * @param mediaType Type of media;
   * @param options Upload options;
   * @param onProgress Progress callback;
   * @return s MediaUploadResult;
   */
  private async uploadMedia(
    userId: string,
    file: File | string,
    mediaType: MediaType,
    options: MediaUploadOptions = {};
    onProgress?: (progress: MediaUploadProgress) = > void;
  ): Promise<MediaUploadResult>
    try {
      // Initialize progress;
      onProgress? .({
        progress   : 0.05
        bytesUploaded: 0
        totalBytes: 1,
        stage: 'preparing'
      })
      // Ensure bucket exists;
      await this.ensureBucketExists()
      // Generate storage path;
      const storagePath = this.generateStoragePath(userId, mediaType)
      // Get file data and metadata;
      const { data: fileData, metadata  } = await this.processFile(file, options)
      // Update progress;
      onProgress? .({
        progress  : 0.1
        bytesUploaded: 0
        totalBytes: metadata.size,
        stage: 'uploading'
      })
      // Upload using resumable upload utility;
      let uploadUrl: string,
      if (typeof file === 'string') {
        // File URI - use resumable upload;
        uploadUrl = await resumableUpload.uploadFile(
          'profile-media'
          file;
          storagePath;
          metadata.type;
          (progress) = > {
  onProgress? .({
              progress   : 0.1 + (progress * 0.8 / 100) // Upload is 80% of total progress
              bytesUploaded: Math.floor(metadata.size * progress / 100)
              totalBytes: metadata.size;
              stage: 'uploading'
            })
          }
        )
      } else {
        // File object - direct upload;
        const { data, error } = await this.supabase.storage.from('profile-media')
          .upload(storagePath, fileData, {
            contentType: metadata.type);
            upsert: true)
          })
        if (error) {
          throw error;
        }

        // Get public URL;
        const { data: urlData } = this.supabase.storage.from('profile-media')
          .getPublicUrl(storagePath)
        uploadUrl = urlData.publicUrl;
      }

      // Final progress update;
      onProgress? .({
        progress  : 1
        bytesUploaded: metadata.size
        totalBytes: metadata.size,
        stage: 'completed'
      })
      return {
        success: true;
        url: uploadUrl,
        error: null,
        metadata;
      }

    } catch (error) { const errorMessage = error instanceof Error ? error.message   : String(error)
      onProgress? .({
        progress  : 0
        bytesUploaded: 0
        totalBytes: 1;
        stage: 'failed'
        error: errorMessage })
      return { success: false;
        url: null,
        error: errorMessage }
    }
  }

  /**;
   * Upload media with custom bucket support (public method for listing images)
   * ;
   * @param userId User ID;
   * @param file File to upload;
   * @param mediaType Type of media;
   * @param options Upload options including custom bucket;
   * @param onProgress Progress callback;
   * @return s MediaUploadResult;
   */
  async uploadMediaDirect(
    userId: string,
    file: File | string,
    mediaType: MediaType,
    options: MediaUploadOptions & { bucket?: string } = {};
    onProgress?: (progress: MediaUploadProgress) = > void;
  ): Promise<MediaUploadResult> {
    const bucket = options.bucket || 'profile-media';
    ;
    try {
      // Initialize progress;
      onProgress? .({
        progress   : 0.05
        bytesUploaded: 0
        totalBytes: 1,
        stage: 'preparing'
      })
      // Validate inputs;
      const validation = await this.validateUpload(file, mediaType)
      if (!validation.valid) { return {
          success: false;
          url: null,
          error: validation.error }
      }

      // Ensure bucket exists;
      await this.ensureBucketExists(bucket)
      // Generate storage path;
      const storagePath = this.generateStoragePath(userId, mediaType)
      // Get file data and metadata;
      const { data: fileData, metadata  } = await this.processFile(file, options)
      // Update progress;
      onProgress? .({
        progress  : 0.1
        bytesUploaded: 0
        totalBytes: metadata.size,
        stage: 'uploading'
      })
      // Upload using resumable upload utility or direct upload;
      let uploadUrl: string,
      if (typeof file === 'string') {
        // File URI - use resumable upload;
        uploadUrl = await resumableUpload.uploadFile(
          bucket;
          file;
          storagePath;
          metadata.type;
          (progress) = > {
  onProgress? .({
              progress  : 0.1 + (progress * 0.8 / 100) // Upload is 80% of total progress
              bytesUploaded: Math.floor(metadata.size * progress / 100)
              totalBytes: metadata.size;
              stage: 'uploading'
            })
          }
        )
      } else {
        // File object - direct upload;
        const { data, error } = await this.supabase.storage.from(bucket)
          .upload(storagePath, fileData, {
            contentType: metadata.type);
            upsert: true)
          })
        if (error) {
          throw error;
        }

        // Get public URL;
        const { data: urlData } = this.supabase.storage.from(bucket)
          .getPublicUrl(storagePath)
        uploadUrl = urlData.publicUrl;
      }

      // Final progress update;
      onProgress? .({
        progress  : 1
        bytesUploaded: metadata.size
        totalBytes: metadata.size,
        stage: 'completed'
      })
      return {
        success: true;
        url: uploadUrl,
        error: null,
        metadata;
      }

    } catch (error) { const errorMessage = error instanceof Error ? error.message   : String(error)
      onProgress? .({
        progress  : 0
        bytesUploaded: 0
        totalBytes: 1;
        stage: 'failed'
        error: errorMessage })
      return { success: false;
        url: null,
        error: errorMessage }
    }
  }

  /**;
   * Validate file upload;
   * ;
   * @param file File to validate;
   * @param mediaType Type of media;
   * @return s Validation result;
   */
  private async validateUpload(file: File | string,
    mediaType: MediaType): Promise<{ valid: boolean; error?: string }>
    if (!file) {
      return { valid: false; error: 'File is required' }
    }

    // Get file info;
    let fileInfo: { size?: number; type?: string } = {}
    if (typeof file === 'string') {
      // File URI;
      const info = await FileSystem.getInfoAsync(file)
      if (!info.exists) {
        return { valid: false; error: 'File does not exist' }
      }
      fileInfo.size = info.size;
      // Type would need to be determined from file extension or metadata;
    } else {
      // File object;
      fileInfo.size = file.size;
      fileInfo.type = file.type;
    }

    // Validate file size;
    if (fileInfo.size && fileInfo.size > MAX_FILE_SIZE) {
      return {
        valid: false;
        error: `File too large. Maximum size: ${MAX_FILE_SIZE / 1024 / 1024}MB` ;
      }
    }

    // Validate file type;
    if (fileInfo.type) {
      const isValidImage = SUPPORTED_IMAGE_TYPES.includes(fileInfo.type)
      const isValidVideo = SUPPORTED_VIDEO_TYPES.includes(fileInfo.type)
      ;
      if (mediaType = == 'video' && !isValidVideo) {
        return {
          valid: false;
          error: `Unsupported video type. Supported: ${SUPPORTED_VIDEO_TYPES.join(', ')}` ;
        }
      }
      if (mediaType != = 'video' && !isValidImage) {
        return {
          valid: false;
          error: `Unsupported image type. Supported: ${SUPPORTED_IMAGE_TYPES.join(', ')}` ;
        }
      }
    }

    return { valid: true }
  }

  /**;
   * Process file for upload (compression, etc.)
   * ;
   * @param file File to process;
   * @param options Processing options;
   * @return s Processed file data and metadata;
   */
  private async processFile(file: File | string,
    options: MediaUploadOptions): Promise<{ data: ArrayBuffer; metadata: { size: number; type: string } }>
    if (typeof file = == 'string') {
      // File URI - read as base64 and convert;
      const base64 = await FileSystem.readAsStringAsync(file, {
        encoding: FileSystem.EncodingType.Base64)
      })
      ;
      const data = decode(base64)
      ;
      return { data;
        metadata: {
          size: data.byteLength,
          type: 'image/jpeg' // Default type }
      }
    } else { // File object;
      const arrayBuffer = await file.arrayBuffer()
      ;
      return {
        data: arrayBuffer;
        metadata: {
          size: file.size,
          type: file.type }
      }
    }
  }

  /**;
   * Generate storage path for file;
   * ;
   * @param userId User ID;
   * @param mediaType Type of media;
   * @return s Storage path;
   */
  private generateStoragePath(userId: string, mediaType: MediaType): string {
    const timestamp = Date.now()
    const random = Math.random().toString(36).substring(2, 8)
    const extension = mediaType === 'video' ? 'mp4'    : 'jpg'
    return `${mediaType}/${userId}/${timestamp}_${random}.${extension}`
  }

  /**;
   * Ensure storage bucket exists;
   */
  private async ensureBucketExists(bucketName: string = 'profile-media'): Promise<void>
    try {
      // Check if bucket exists;
      const { data: buckets  } = await this.supabase.storage.listBuckets()
      const bucketExists = buckets? .some(bucket => bucket.name === bucketName)
      ;
      if (!bucketExists) {
        // Create bucket;
        const { error  } = await this.supabase.storage.createBucket(bucketName, {
          public   : true
          allowedMimeTypes: [...SUPPORTED_IMAGE_TYPES ...SUPPORTED_VIDEO_TYPES]);
          fileSizeLimit: MAX_FILE_SIZE)
        })
        
        if (error) {
          logger.warn(`Could not create bucket ${bucketName}`, SERVICE_NAME, { error: error.message })
        }
      }
    } catch (error) {
      logger.warn(`Error ensuring bucket ${bucketName} exists`, SERVICE_NAME, {
        error: error instanceof Error ? error.message   : String(error)
      })
    }
  }

  /**
   * Add photo to profile gallery;
   * ;
   * @param userId User ID;
   * @param photoUrl Photo URL;
   */
  private async addToProfileGallery(userId: string, photoUrl: string): Promise<void>
    try {
      const profileResult = await unifiedProfileService.getProfileById(userId)
      if (!profileResult.data) return null;
      const profile = profileResult.data;
      const metaData = profile.meta_data || {}
      const photos = Array.isArray(metaData.photos) ? [...metaData.photos]    : []
      if (!photos.includes(photoUrl)) {
        photos.push(photoUrl)
        
        await unifiedProfileService.updateProfile(userId, {
          meta_data: {
            ...metaData;
            photos)
          }
        })
      }
    } catch (error) {
      logger.warn('Error adding photo to gallery', SERVICE_NAME, {
        userId;
        photoUrl;
        error: error instanceof Error ? error.message   : String(error)
      })
    }
  }

  /**
   * Remove media reference from profile;
   * ;
   * @param userId User ID;
   * @param mediaUrl Media URL to remove;
   * @param mediaType Type of media;
   */
  private async removeFromProfile(userId: string,
    mediaUrl: string,
    mediaType: MediaType): Promise<void>
    try {
      const profileResult = await unifiedProfileService.getProfileById(userId)
      if (!profileResult.data) return null;
      const profile = profileResult.data;
      const updateData: any = {}

      if (mediaType === 'avatar') {
        updateData.avatar_url = null;
      } else if (mediaType === 'gallery') {
        const metaData = profile.meta_data || {}
        const photos = Array.isArray(metaData.photos) ? metaData.photos   : []
        const updatedPhotos = photos.filter(url => url !== mediaUrl)
        
        updateData.meta_data = { ...metaData;
          photos: updatedPhotos }
      } else if (mediaType === 'video') {
        const metaData = profile.meta_data || {}
        updateData.meta_data = { ...metaData;
          video_intro_url: null,
          video_thumbnail_url: null }
      }

      if (Object.keys(updateData).length > 0) {
        await unifiedProfileService.updateProfile(userId, updateData)
      }
    } catch (error) {
      logger.warn('Error removing media from profile', SERVICE_NAME, {
        userId;
        mediaUrl;
        mediaType;
        error: error instanceof Error ? error.message   : String(error)
      })
    }
  }

  /**
   * Extract storage path from public URL;
   * ;
   * @param url Public URL;
   * @return s Storage path;
   */
  private extractStoragePathFromUrl(url: string): string | null {
    try {
      // Extract path from Supabase storage URL;
      const match = url.match(/\/storage\/v1\/object\/public\/profile-media\/(.+)/)
      return match ? match[1]   : null
    } catch {
      return null;
    }
  }
}

// Export singleton instance;
export const unifiedMediaService = new UnifiedMediaService() ;