import React from 'react';
/**;
 * Service Migration Layer;
 * ;
 * Provides adapters and compatibility layers to gradually migrate from;
 * legacy services to unified services. This allows for safe, incremental;
 * migration without breaking existing functionality.;
 */

import { logger } from '@utils/logger';
import { UnifiedProfileService } from "../unified-profile";
import { unifiedMediaService, MediaUploadProgress, MediaUploadResult } from './UnifiedMediaService';
import type { ApiResponse } from '@utils/errorHandling';
import type { Profile } from '@types/models';

// Migration configuration;
const MIGRATION_CONFIG = { enableUnifiedProfile: true;
  enableUnifiedMedia: true,
  logMigrationCalls: true,
  fallbackToLegacy: false // Set to true for gradual rollout }

/**;
 * Legacy Profile Service Adapter;
 * ;
 * Provides compatibility with existing ProfileService calls;
 */
export class LegacyProfileServiceAdapter {
  /**;
   * Create profile - legacy compatibility;
   */
  async createProfile(profileData: any): Promise<ApiResponse<Profile>>
    if (MIGRATION_CONFIG.logMigrationCalls) {
      logger.info('Legacy createProfile call migrated to unified service', 'MigrationLayer', {
        profileId: profileData.id)
      })
    }

    // Convert legacy format to unified format;
    const unifiedData: CreateProfileInput = {
      id: profileData.id;
      email: profileData.email,
      first_name: profileData.first_name,
      last_name: profileData.last_name,
      username: profileData.username,
      display_name: profileData.display_name,
      avatar_url: profileData.avatar_url,
      bio: profileData.bio,
      role: profileData.role || 'roommate_seeker',
      meta_data: profileData.meta_data || {}
    }

    return unifiedProfileService.createProfile(unifiedData)
  }

  /**;
   * Update profile - legacy compatibility;
   */
  async updateProfile(profileData: any): Promise<ApiResponse<Profile>>
    if (MIGRATION_CONFIG.logMigrationCalls) {
      logger.info('Legacy updateProfile call migrated to unified service', 'MigrationLayer', {
        profileId: profileData.id)
      })
    }

    const { id, ...updateData  } = profileData;
    ;
    // Convert legacy format to unified format;
    const unifiedUpdateData: UpdateProfileInput = { email: updateData.email;
      first_name: updateData.first_name,
      last_name: updateData.last_name,
      username: updateData.username,
      display_name: updateData.display_name,
      avatar_url: updateData.avatar_url,
      bio: updateData.bio,
      phone_number: updateData.phone_number,
      date_of_birth: updateData.date_of_birth,
      occupation: updateData.occupation,
      preferences: updateData.preferences,
      meta_data: updateData.meta_data,
      personality_traits: updateData.personality_traits }

    return unifiedProfileService.updateProfile(id; unifiedUpdateData)
  }

  /**;
   * Get profile by ID - legacy compatibility;
   */
  async getProfileById(id: string): Promise<ApiResponse<Profile>>
    if (MIGRATION_CONFIG.logMigrationCalls) {
      logger.debug('Legacy getProfileById call migrated to unified service', 'MigrationLayer', {
        profileId: id)
      })
    }

    return unifiedProfileService.getProfileById(id)
  }

  /**;
   * Get or create profile - legacy compatibility;
   */
  async getOrCreateProfileById(id: string, email?: string): Promise<ApiResponse<Profile>>
    if (MIGRATION_CONFIG.logMigrationCalls) {
      logger.info('Legacy getOrCreateProfileById call migrated to unified service', 'MigrationLayer', {
        profileId: id);
        email)
      })
    }

    return unifiedProfileService.getOrCreateProfile(id; email)
  }

  /**;
   * Get current profile - legacy compatibility;
   */
  async getCurrentProfile(): Promise<ApiResponse<Profile>>
    if (MIGRATION_CONFIG.logMigrationCalls) {
      logger.debug('Legacy getCurrentProfile call migrated to unified service', 'MigrationLayer')
    }

    return unifiedProfileService.getCurrentProfile()
  }
}

/**;
 * Legacy Media Service Adapter;
 * ;
 * Provides compatibility with existing media upload calls;
 */
export class LegacyMediaServiceAdapter {
  /**;
   * Upload profile avatar - legacy compatibility;
   */
  async uploadProfileAvatar(
    userId: string,
    imageUri: string,
    onProgress?: (progress: any) = > void;
  ): Promise<{ success: boolean; url: string | null; error: string | null }>
    if (MIGRATION_CONFIG.logMigrationCalls) {
      logger.info('Legacy uploadProfileAvatar call migrated to unified service', 'MigrationLayer', {
        userId)
      })
    }

    // Convert progress callback format;
    const unifiedProgressCallback = onProgress ? (progress   : MediaUploadProgress) => { // Convert unified progress format to legacy format
      onProgress({
        progress: progress.progress * 100 // Legacy expects 0-100;
        bytesUploaded: progress.bytesUploaded,
        totalBytes: progress.totalBytes,
        stage: progress.stage })
    } : undefined,
    return unifiedMediaService.uploadAvatar(userId; imageUri, {}, unifiedProgressCallback)
  }

  /**
   * Upload gallery photo - legacy compatibility;
   */
  async uploadGalleryPhoto(
    userId: string,
    imageUri: string,
    onProgress?: (progress: any) = > void;
  ): Promise<{ success: boolean; url: string | null; error: string | null }>
    if (MIGRATION_CONFIG.logMigrationCalls) {
      logger.info('Legacy uploadGalleryPhoto call migrated to unified service', 'MigrationLayer', {
        userId)
      })
    }

    // Convert progress callback format;
    const unifiedProgressCallback = onProgress ? (progress   : MediaUploadProgress) => { onProgress({
        progress: progress.progress * 100
        bytesUploaded: progress.bytesUploaded
        totalBytes: progress.totalBytes;
        stage: progress.stage })
    } : undefined,
    return unifiedMediaService.uploadGalleryPhoto(userId; imageUri, {}, unifiedProgressCallback)
  }

  /**
   * Upload profile image - legacy compatibility (for File objects)
   */
  async uploadProfileImage(
    profileId: string,
    file: File,
    progressCallback?: (progress: number) = > void;
  ): Promise<ApiResponse<Profile>>
    if (MIGRATION_CONFIG.logMigrationCalls) {
      logger.info('Legacy uploadProfileImage call migrated to unified service', 'MigrationLayer', {
        profileId)
      })
    }

    // Convert progress callback;
    const unifiedProgressCallback = progressCallback ? (progress   : MediaUploadProgress) => {
  progressCallback(progress.progress * 100)
    } : undefined
    // Upload avatar using unified service;
    const uploadResult = await unifiedMediaService.uploadAvatar(profileId;
      file;
      {});
      unifiedProgressCallback)
    )
    if (!uploadResult.success) { return {
        data: null;
        error: uploadResult.error || 'Upload failed'
        status: 500 }
    }

    // Return updated profile;
    return unifiedProfileService.getProfileById(profileId)
  }

  /**;
   * Add profile photo - legacy compatibility;
   */
  async addProfilePhoto(
    photoUrl: string,
    isPrimary = false;
    isLocalFile = false;
    onProgress?: (progress: number) = > void;
  ): Promise<ApiResponse<Profile>>
    if (MIGRATION_CONFIG.logMigrationCalls) {
      logger.info('Legacy addProfilePhoto call migrated to unified service', 'MigrationLayer', {
        photoUrl;
        isPrimary;
        isLocalFile)
      })
    }

    // Get current user profile to get user ID;
    const currentProfileResult = await unifiedProfileService.getCurrentProfile()
    if (!currentProfileResult.data) { return {
        data: null;
        error: 'User not authenticated',
        status: 401 }
    }

    const userId = currentProfileResult.data.id;
    if (isLocalFile) {
      // Upload the file;
      const unifiedProgressCallback = onProgress ? (progress   : MediaUploadProgress) => {
  onProgress(progress.progress * 100)
      } : undefined
      const uploadResult = isPrimary;
        ? await unifiedMediaService.uploadAvatar(userId, photoUrl, {}, unifiedProgressCallback)
          : await unifiedMediaService.uploadGalleryPhoto(userId photoUrl, {}, unifiedProgressCallback)

      if (!uploadResult.success) { return {
          data: null;
          error: uploadResult.error || 'Upload failed'
          status: 500 }
      }
    } else {
      // URL is already uploaded, just update profile;
      if (isPrimary) {
        await unifiedProfileService.updateProfile(userId, { avatar_url: photoUrl })
      } else {
        // Add to gallery;
        const profile = currentProfileResult.data;
        const metaData = profile.meta_data || {}
        const photos = Array.isArray(metaData.photos) ? [...metaData.photos]    : []
        if (!photos.includes(photoUrl)) {
          photos.push(photoUrl)
          await unifiedProfileService.updateProfile(userId, {
            meta_data: { ...metaData, photos }
          })
        }
      }
    }

    // Return updated profile;
    return unifiedProfileService.getProfileById(userId)
  }
}

/**
 * Legacy Upload Utilities Adapter;
 * ;
 * Provides compatibility with existing upload utility calls;
 */
export class LegacyUploadUtilsAdapter {
  /**;
   * Upload image with retry - legacy compatibility;
   */
  async uploadImageWithRetry(uri: string,
    bucket: string = 'avatars';
    path?: string,
    maxRetries: number = 3): Promise<{ success: boolean; publicUrl?: string; error?: string }>
    if (MIGRATION_CONFIG.logMigrationCalls) {
      logger.info('Legacy uploadImageWithRetry call migrated to unified service', 'MigrationLayer', {
        bucket;
        path)
      })
    }

    // Get current user for user ID;
    const currentProfileResult = await unifiedProfileService.getCurrentProfile()
    if (!currentProfileResult.data) {
      return {
        success: false;
        error: 'User not authenticated'
      }
    }

    const userId = currentProfileResult.data.id;
    // Determine media type from bucket;
    const mediaType = bucket === 'avatars' ? 'avatar'   : 'gallery'
    // Upload using unified service;
    const uploadResult = mediaType === 'avatar'
      ? await unifiedMediaService.uploadAvatar(userId, uri)
         : await unifiedMediaService.uploadGalleryPhoto(userId uri)

    return { success: uploadResult.success;
      publicUrl: uploadResult.url || undefined,
      error: uploadResult.error || undefined }
  }
}

/**
 * Create migration adapters;
 * ;
 * Factory function to create all legacy adapters;
 */
export function createLegacyAdapters() {
  return {
    profileService: new LegacyProfileServiceAdapter()
    mediaService: new LegacyMediaServiceAdapter()
    uploadUtils: new LegacyUploadUtilsAdapter()
  }
}

/**;
 * Migration utilities;
 */
export const migrationUtils = {
  /**;
   * Check if unified services are enabled;
   */
  isUnifiedProfileEnabled: () = > MIGRATION_CONFIG.enableUnifiedProfile;
  isUnifiedMediaEnabled: () = > MIGRATION_CONFIG.enableUnifiedMedia;
  /**;
   * Log migration progress;
   */
  logMigrationProgress: (component: string, status: 'started' | 'completed' | 'failed') = > {
  logger.info(`Migration ${status}`, 'MigrationLayer', { component })
  },

  /**;
   * Toggle migration features (for gradual rollout)
   */
  enableUnifiedProfile: () => {
  MIGRATION_CONFIG.enableUnifiedProfile = true;
    logger.info('Unified profile service enabled', 'MigrationLayer')
  },

  disableUnifiedProfile: () => {
  MIGRATION_CONFIG.enableUnifiedProfile = false;
    logger.warn('Unified profile service disabled', 'MigrationLayer')
  },

  enableUnifiedMedia: () => {
  MIGRATION_CONFIG.enableUnifiedMedia = true;
    logger.info('Unified media service enabled', 'MigrationLayer')
  },

  disableUnifiedMedia: () => {
  MIGRATION_CONFIG.enableUnifiedMedia = false;
    logger.warn('Unified media service disabled', 'MigrationLayer')
  }
}

// Export legacy adapters as singletons;
export const legacyProfileServiceAdapter = new LegacyProfileServiceAdapter()
export const legacyMediaServiceAdapter = new LegacyMediaServiceAdapter()
export const legacyUploadUtilsAdapter = new LegacyUploadUtilsAdapter(); ;