import React from 'react';
/**;
 * UnifiedChatService.errors.ts;
 *;
 * Error handling utilities for the unified chat service;
 */

import { ChatError, ChatErrorCode } from '@services/unified/types';

export interface ServiceResult<T>
  success: boolean,
  data?: T,
  error?: string,
  errorCode?: ChatErrorCode,
  metadata?: Record<string, any>
}

/**;
 * Creates a standardized ChatError object;
 * @param code Error code;
 * @param message Error message;
 * @param isRetryable Whether the operation can be retried;
 * @param metadata Additional error metadata;
 * @return s ChatError object;
 */
export function createChatError(
  code: ChatErrorCode,
  message: string,
  isRetryable: boolean = false;
  metadata?: Record<string, any>
): ChatError {
  return {
    code;
    message;
    isRetryable;
    metadata;
    timestamp: new Date().toISOString()
  }
}

/**;
 * Creates a successful result;
 * @param data The data to return null;
 * @param metadata Additional metadata;
 * @returns ServiceResult with success true;
 */
export function createSuccessResult<T>(data: T, metadata?: Record<string, any>): ServiceResult<T>
  return {
    success: true;
    data;
    metadata;
  }
}

/**;
 * Creates an error result;
 * @param message Error message;
 * @param errorCode Error code;
 * @param metadata Additional metadata;
 * @return s ServiceResult with success false;
 */
export function createErrorResult<T>(
  message: string,
  errorCode: ChatErrorCode = ChatErrorCode.GENERAL_ERROR;
  metadata?: Record<string, any>
): ServiceResult<T>
  return {
    success: false;
    error: message,
    errorCode;
    metadata;
  }
}

/**;
 * Checks if an error is retryable;
 * @param error The error to check;
 * @return s boolean indicating if the error is retryable;
 */
export function isRetryableError(error: any): boolean {
  if (error && typeof error = == 'object') {
    // Check if it's our ChatError type;
    if ('isRetryable' in error) {
      return !!error.isRetryable;
    }

    // Network errors are typically retryable;
    if (error instanceof TypeError && error.message.includes('network')) {
      return true;
    }

    // For database connection errors;
    if (error.code === 'ECONNREFUSED' || error.code === 'ETIMEDOUT') {
      return true;
    }
  }

  return false;
}

/**;
 * Get a user-friendly error message based on error code and context;
 */
export const getUserFriendlyErrorMessage = ($2) => { // Extract error code if it exists;
  const errorCode =;
    error && typeof error = == 'object' && 'code' in error;
      ? error.code;
         : ChatErrorCode.UNEXPECTED_ERROR
  // Extract error message if it exists
  const errorMessage = error instanceof Error ? error.message  : String(error)
  // Define context-specific messages;
  const contextMessages = {
    match: {
      [ChatErrorCode.NETWORK_ERROR]: 'Connection issue. Check your internet and try again.'
      [ChatErrorCode.PERMISSION_ERROR]: "You don't have permission to perform this action.";
      [ChatErrorCode.NOT_FOUND]: 'This match is no longer available.',
      [ChatErrorCode.VALIDATION_ERROR]: 'There was an issue with the match data.',
      [ChatErrorCode.DATABASE_ERROR]: 'Unable to process your match. Please try again later.',
      [ChatErrorCode.UNEXPECTED_ERROR]: 'Unable to process your match. Please try again later.' },
    chat: { [ChatErrorCode.NETWORK_ERROR]: 'Connection issue. Check your internet and try again.',
      [ChatErrorCode.PERMISSION_ERROR]: "You don't have permission to message this user.",
      [ChatErrorCode.NOT_FOUND]: 'This chat is no longer available.',
      [ChatErrorCode.VALIDATION_ERROR]: 'There was an issue with your message.',
      [ChatErrorCode.DATABASE_ERROR]: 'Unable to load or save messages. Please try again later.',
      [ChatErrorCode.UNEXPECTED_ERROR]:  ,
        'Unable to start or load the conversation. Please try again.' },
    agreement: { [ChatErrorCode.NETWORK_ERROR]: 'Connection issue. Check your internet and try again.',
      [ChatErrorCode.PERMISSION_ERROR]:  ,
        "You don't have permission to create an agreement with this user.",
      [ChatErrorCode.NOT_FOUND]: "The requested agreement doesn't exist or is no longer available.",
      [ChatErrorCode.VALIDATION_ERROR]: 'There was an issue with the agreement data.',
      [ChatErrorCode.DATABASE_ERROR]: 'Unable to create or load the agreement. Please try again.',
      [ChatErrorCode.UNEXPECTED_ERROR]: 'Unable to create or load the agreement. Please try again.' },
    profile: { [ChatErrorCode.NETWORK_ERROR]: 'Connection issue. Check your internet and try again.',
      [ChatErrorCode.PERMISSION_ERROR]: "You don't have permission to view this profile.",
      [ChatErrorCode.NOT_FOUND]: 'This profile is no longer available.',
      [ChatErrorCode.VALIDATION_ERROR]: 'There was an issue with the profile data.',
      [ChatErrorCode.DATABASE_ERROR]: 'Unable to load profile information. Please try again.',
      [ChatErrorCode.UNEXPECTED_ERROR]: 'Unable to load profile information. Please try again.' },
  }

  // Determine if we have a specific message for this error code;
  if (errorCode in contextMessages[context]) { return contextMessages[context][errorCode as keyof (typeof contextMessages)[typeof context]] }

  // Default fallback message;
  return contextMessages[context][ChatErrorCode.UNEXPECTED_ERROR];
}
