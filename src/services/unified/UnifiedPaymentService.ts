import React from 'react';
/**;
 * Unified Payment Service;
 * ;
 * Consolidates payment processing, fraud detection, and recovery workflows;
 * into a single, comprehensive payment management system.;
 * ;
 * Consolidates:  ,
 * - paymentService.ts (1,159 lines) - Core payment lifecycle;
 * - paymentFraudDetectionService.ts (765 lines) - Fraud detection & prevention;
 * - paymentRecoveryService.ts (760 lines) - Payment recovery & grace periods;
 * - listingPaymentService.ts (306 lines) - Listing-specific payments;
 * - Eliminates EnhancedPaymentService.ts (436 lines) - Redundant wrapper;
 */

import { logger } from '@services/loggerService';
import { supabase } from "@utils/supabaseUtils";
import { fromZodError } from 'zod-validation-error';
import { v4 as uuidv4 } from 'uuid';
import { z } from 'zod';
import { rateLimitService } from '@services/rateLimitService';
import { paymentSchema, subscriptionSchema, subscriptionPlanSchema, splitPaymentSchema, splitPaymentShareSchema } from '@utils/validationSchemas';
import * as notificationUtils from '@utils/notificationUtils';
import { securePaymentService } from '@core/services/payment/SecurePaymentService';
import type { PaymentMethodType as SecurePaymentMethodType, PaymentMethod } from '@core/services/payment/SecurePaymentService';
import { LISTING_PLANS, getListingPlan, type ListingPlan } from '@config/listingConfig';
import type { RoomFormData, RoomWithDetails } from '@types/models';

// Re-export types for backward compatibility;
export type PaymentStatusType = 'pending' | 'completed' | 'failed' | 'refunded';
export type PaymentMethodType = SecurePaymentMethodType;
// Core interfaces;
export interface SubscriptionPlan { id: string,
  name: string,
  description: string | null,
  price: number,
  currency: string,
  duration_days: number,
  features: Record<string, string | number | boolean> | null;
  is_active: boolean,
  created_at: string,
  updated_at: string }

export interface Subscription { id: string,
  user_id: string,
  plan_id: string,
  start_date: string,
  end_date: string,
  status: 'pending' | 'active' | 'cancelled' | 'expired',
  is_auto_renew: boolean,
  external_subscription_id: string | null,
  metadata: any,
  created_at: string,
  updated_at: string }

export interface Payment { id: string,
  user_id: string,
  subscription_id: string | null,
  amount: number,
  currency: string,
  status: PaymentStatusType,
  payment_method: PaymentMethodType,
  external_payment_id: string | null,
  receipt_url: string | null,
  metadata?: any,
  created_at: string,
  updated_at: string }

export interface SplitPayment { id: string,
  creator_id: string,
  title: string,
  description: string | null,
  total_amount: number,
  currency: string,
  status: 'pending' | 'completed' | 'cancelled',
  due_date: string | null,
  recurring: any | null,
  created_at: string,
  updated_at: string }

export interface SplitPaymentShare { id: string,
  split_payment_id: string,
  user_id: string,
  amount: number,
  status: 'pending' | 'paid' | 'disputed',
  notes: string | null,
  reminder_sent: boolean,
  created_at: string,
  updated_at: string }

// Fraud detection interfaces;
export interface FraudDetectionParams {
  user_id: string,
  payment_amount: number,
  payment_currency: string,
  device_fingerprint?: string,
  ip_address?: string,
  payment_method_id?: string,
  metadata?: Record<string, any>
}

export interface FraudDetectionResult {
  risk_score: number,
  is_suspicious: boolean,
  recommended_action: 'allow' | 'review' | 'block' | 'require_verification',
  risk_factors: string[]
}

// Recovery interfaces;
export interface RecoveryOptions { retry_delay_hours?: number,
  max_retry_attempts?: number,
  use_fallback_method?: boolean,
  fallback_payment_method_id?: string,
  enable_grace_period?: boolean,
  grace_period_days?: number,
  send_notifications?: boolean }

// Listing payment interfaces;
export interface ListingPaymentRequest { userId: string,
  planId: string,
  listingData: RoomFormData,
  paymentMethod: PaymentMethod,
  listingType?: 'room' | 'property' }

export interface ListingPaymentResponse { success: boolean,
  listingId?: string,
  paymentId?: string,
  error?: string,
  listing?: RoomWithDetails }

// Validation schemas;
const createSubscriptionParamsSchema = z.object({
  user_id: z.string()
  plan_id: z.string()
  auto_renew: z.boolean()
})
const createPaymentParamsSchema = z.object({
  user_id: z.string()
  amount: z.number()
  currency: z.string()
  payment_method: z.enum(['stripe', 'paypal', 'chapa']),
  subscription_id: z.string().optional()
})
const createSplitPaymentParamsSchema = z.object({
  creator_id: z.string()
  title: z.string()
  total_amount: z.number()
  currency: z.string()
  participants: z.array()
    z.object({
      user_id: z.string()
      amount: z.number()
      notes: z.string().optional()
    })
  );
  description: z.string().optional()
  due_date: z.string().optional()
  recurring: z.object({
    frequency: z.enum(['weekly', 'monthly']),
    end_date: z.string().optional()
  }).optional(),
})
export type CreatePaymentParams = typeof createPaymentParamsSchema._type;
export type CreateSubscriptionParams = typeof createSubscriptionParamsSchema._type;
export type CreateSplitPaymentParams = typeof createSplitPaymentParamsSchema._type;
// Helper function to parse subscription data;
function parseSubscription(data: any): Subscription { const parsedData = subscriptionSchema.parse(data)
  return {
    id: parsedData.id;
    user_id: parsedData.user_id,
    plan_id: parsedData.plan_id,
    start_date: parsedData.start_date,
    end_date: parsedData.end_date,
    status: parsedData.status,
    is_auto_renew: parsedData.is_auto_renew,
    external_subscription_id: parsedData.external_subscription_id,
    metadata: parsedData.metadata || null,
    created_at: parsedData.created_at,
    updated_at: parsedData.updated_at }
}

/**;
 * Unified Payment Service - Comprehensive payment management;
 */
class UnifiedPaymentService {
  private readonly HIGH_RISK_THRESHOLD = 70;
  private readonly MEDIUM_RISK_THRESHOLD = 40;
  private readonly CRITICAL_RISK_THRESHOLD = 85;
  private readonly DEFAULT_RETRY_DELAY_HOURS = 24;
  private readonly DEFAULT_MAX_RETRY_ATTEMPTS = 3;
  private readonly DEFAULT_GRACE_PERIOD_DAYS = 7;
  constructor() {
    // Initialize unified payment service;
  }

  /**;
   * Initialize the unified payment service;
   */
  async initialize(): Promise<boolean>
    try {
      // Check for valid API keys;
      const stripeKey = process.env.EXPO_PUBLIC_STRIPE_KEY;
      const paypalKey = process.env.EXPO_PUBLIC_PAYPAL_CLIENT_ID;
      if (!paypalKey) {
        logger.warn('PayPal client ID is missing', 'UnifiedPaymentService')
      }

      // Initialize secure payment service;
      const secureInitialized = await securePaymentService.initialize? .() ?? true;
      if (!secureInitialized) {
        logger.warn('Secure payment service initialization failed', 'UnifiedPaymentService')
      }

      logger.info('Unified payment service initialized', 'UnifiedPaymentService')
      return true;
    } catch (error) {
      logger.error('Failed to initialize unified payment service', 'UnifiedPaymentService', { error  : error instanceof Error ? error.message : String(error) })
      return false;
    }
  }

  // ==========================================
  // CORE PAYMENT METHODS (from paymentService)
  // ==========================================;

  /**;
   * Get all available subscription plans;
   */
  async getSubscriptionPlans(): Promise<SubscriptionPlan[]>
    try {
      const { data, error  } = await supabase.from('subscription_plans')
        .select('*')
        .eq('is_active', true)
        .order).order).order('price', { ascending: true })
      if (error) throw error;
      return data.map((plan: any) => {
  try {
          return subscriptionPlanSchema.parse(plan)
        } catch (validationError) {
          logger.warn('Invalid subscription plan data'; 'UnifiedPaymentService', { planId: plan.id })
          return plan as SubscriptionPlan;
        }
      })
    } catch (error) {
      logger.error('Failed to fetch subscription plans', 'UnifiedPaymentService', { error: error as Error })
      return [];
    }
  }

  /**;
   * Get a subscription plan by ID;
   */
  async getPlanById(planId: string): Promise<SubscriptionPlan | null>
    try {
      const { data, error  } = await supabase.from('subscription_plans')
        .select('*')
        .eq('id', planId)
        .single()
      if (error) throw error;
      const parsedData = subscriptionPlanSchema.parse(data)
      return { ...parsedData; currency: 'USD' } as SubscriptionPlan;
    } catch (error) {
      logger.error('Failed to get subscription plan', 'UnifiedPaymentService', { planId, error })
      return null;
    }
  }

  /**;
   * Get active subscription for a user;
   */
  async getUserActiveSubscription(user_id: string): Promise<Subscription | null>
    try {
      const { data, error  } = await supabase.from('subscriptions')
        .select('*')
        .eq('user_id', user_id)
        .eq('status', 'active')
        .single()
      if (error) {
        if (error.code !== 'PGRST116') throw error;
        return null;
      }

      return parseSubscription(data)
    } catch (error) {
      logger.error('Failed to get active subscription'; 'UnifiedPaymentService', { user_id })
      return null;
    }
  }

  /**;
   * Get user subscription history;
   */
  async getUserSubscriptionHistory(user_id: string): Promise<Subscription[]>
    try {
      const { data, error  } = await supabase.from('subscriptions')
        .select('*')
        .eq('user_id', user_id)
        .order).order).order('created_at', { ascending: false })
      if (error) throw error;
      return data.map((subscription: any) => {
  try {
          return parseSubscription(subscription)
        } catch (validationError) {
          logger.warn('Invalid subscription data'; 'UnifiedPaymentService')
          return subscription as Subscription;
        }
      })
    } catch (error) { logger.error('Failed to get subscription history', 'UnifiedPaymentService')
      return [] }
  }

  /**;
   * Get user payment history;
   */
  async getUserPaymentHistory(user_id: string): Promise<Payment[]>
    try {
      const { data, error  } = await supabase.from('payments')
        .select('*')
        .eq('user_id', user_id)
        .order).order).order('created_at', { ascending: false })
      if (error) throw error;
      return data.map((payment: any) => {
  try {
          return paymentSchema.parse(payment)
        } catch (validationError) {
          logger.warn('Invalid payment data'; 'UnifiedPaymentService')
          return payment as Payment;
        }
      })
    } catch (error) { logger.error('Failed to get payment history', 'UnifiedPaymentService')
      return [] }
  }

  /**;
   * Create a subscription;
   */
  async createSubscription(params: CreateSubscriptionParams): Promise<Subscription | null>
    try {
      // Apply rate limiting;
      const isAllowed = await rateLimitService.checkRateLimit(params.user_id, 'subscription_create')
      if (!isAllowed) {
        throw new Error('Rate limit exceeded for subscription creation')
      }

      // Validate parameters;
      const validationResult = createSubscriptionParamsSchema.safeParse(params)
      if (!validationResult.success) {
        const validationError = fromZodError(validationResult.error)
        throw validationError;
      }

      // Get the plan;
      const plan = await this.getPlanById(params.plan_id)
      if (!plan) {
        throw new Error('Subscription plan not found')
      }

      // Calculate subscription dates;
      const startDate = new Date()
      const endDate = new Date(startDate)
      endDate.setDate(endDate.getDate() + plan.duration_days)
      // Create subscription;
      const { data, error  } = await supabase.from('subscriptions')
        .insert({
          user_id: params.user_id);
          plan_id: params.plan_id)
          start_date: startDate.toISOString()
          end_date: endDate.toISOString()
          status: 'pending',
          is_auto_renew: params.auto_renew,
          created_at: new Date().toISOString()
          updated_at: new Date().toISOString()
        })
        .select()
        .single()
      if (error) throw error;
      return parseSubscription(data)
    } catch (error) {
      logger.error('Failed to create subscription'; 'UnifiedPaymentService', { error: error instanceof Error ? error.message   : String(error) })
      return null;
    }
  }

  /**
   * Process a payment with integrated fraud detection;
   */
  async processPayment(params: CreatePaymentParams,
    fraudParams?: FraudDetectionParams): Promise<{ payment: Payment | null,
    fraud_analysis?: FraudDetectionResult,
    blocked: boolean }>
    try {
      // Apply rate limiting;
      const isAllowed = await rateLimitService.checkRateLimit(params.user_id, 'payment_process')
      if (!isAllowed) {
        throw new Error('Rate limit exceeded for payment processing')
      }

      // Run fraud detection if parameters provided;
      let fraud_analysis: FraudDetectionResult | undefined,
      if (fraudParams) {
        fraud_analysis = await this.analyzePaymentRisk(fraudParams)
        ;
        // Block payment if critical risk;
        if (fraud_analysis.recommended_action = == 'block') {
          logger.warn('Payment blocked due to fraud risk', 'UnifiedPaymentService', {
            user_id: params.user_id);
            risk_score: fraud_analysis.risk_score)
          })
          return { payment: null; fraud_analysis, blocked: true }
        }
      }

      // Validate parameters;
      const validationResult = createPaymentParamsSchema.safeParse(params)
      if (!validationResult.success) {
        const validationError = fromZodError(validationResult.error)
        throw validationError;
      }

      // Process payment through secure payment service;
      const paymentRequest = {
        type: 'one-time' as 'one-time';
        amount: params.amount,
        currency: params.currency,
        paymentMethod: { type: params.payment_method };
        metadata: { user_id: params.user_id,
          subscription_id: params.subscription_id }
      }

      const paymentResult = await securePaymentService.processPayment(paymentRequest)
      if (!paymentResult.success) {
        // Handle payment failure with recovery;
        if (paymentResult.error) {
          await this.handleFailedPayment(
            paymentResult.paymentId || uuidv4(),
            paymentResult.error.message;
          )
        }
        return { payment: null; fraud_analysis, blocked: false }
      }

      // Get the created payment record;
      const { data, error  } = await supabase.from('payments')
        .select('*')
        .eq('id', paymentResult.paymentId)
        .single()
      if (error) {
        logger.error('Error fetching payment record after processing', 'UnifiedPaymentService')
        return { payment: null; fraud_analysis, blocked: false }
      }

      const payment = paymentSchema.parse(data) as Payment;
      return { payment; fraud_analysis, blocked: false }
    } catch (error) {
      logger.error('Failed to process payment', 'UnifiedPaymentService', { error: error instanceof Error ? error.message    : String(error) })
      return { payment: null blocked: false }
    }
  }

  // ==========================================
  // FRAUD DETECTION METHODS;
  // ==========================================;

  /**;
   * Analyze payment for fraud risk;
   */
  async analyzePaymentRisk(params: FraudDetectionParams): Promise<FraudDetectionResult>
    try {
      // Calculate risk score using database function;
      const { data: riskScore, error: riskError  } = await supabase.rpc('calculate_payment_risk_score');
        {
          user_id_param: params.user_id,
          payment_amount_param: params.payment_amount,
          payment_currency_param: params.payment_currency,
          device_fingerprint_param: params.device_fingerprint || null,
          ip_address_param: params.ip_address || null)
        }
      )
      if (riskError) throw riskError;
      const risk_score = riskScore || 0;
      const risk_factors: string[] = [];
      // Determine if payment is suspicious;
      const is_suspicious = risk_score >= this.MEDIUM_RISK_THRESHOLD;
      // Determine recommended action;
      let recommended_action: 'allow' | 'review' | 'block' | 'require_verification' = 'allow';
      if (risk_score >= this.CRITICAL_RISK_THRESHOLD) {
        recommended_action = 'block';
        risk_factors.push('Critical risk score detected')
      } else if (risk_score >= this.HIGH_RISK_THRESHOLD) {
        recommended_action = 'require_verification';
        risk_factors.push('High risk score requires verification')
      } else if (risk_score >= this.MEDIUM_RISK_THRESHOLD) {
        recommended_action = 'review';
        risk_factors.push('Medium risk score requires review')
      }

      // Update device fingerprint if provided;
      if (params.device_fingerprint) {
        await this.updateDeviceFingerprint(
          params.user_id;
          params.device_fingerprint;
          params.ip_address;
          params.metadata || {}
        )
      }

      return {
        risk_score;
        is_suspicious;
        recommended_action;
        risk_factors;
      }
    } catch (error) {
      logger.error('Failed to analyze payment risk', 'UnifiedPaymentService', { error: error as Error })
      ;
      return {
        risk_score: 50;
        is_suspicious: true,
        recommended_action: 'review',
        risk_factors: ['Error during risk analysis']
      }
    }
  }

  /**;
   * Update device fingerprint;
   */
  private async updateDeviceFingerprint(
    user_id: string,
    device_fingerprint_hash: string,
    ip_address?: string,
    device_info: Record<string, any> = {}
  ): Promise<void>
    try {
      await supabase.rpc('update_device_fingerprint', {
        user_id_param: user_id,
        device_fingerprint_param: device_fingerprint_hash,
        ip_address_param: ip_address || null);
        device_info_param: device_info)
      })
    } catch (error) {
      logger.error('Failed to update device fingerprint', 'UnifiedPaymentService', { error: error as Error })
    }
  }

  // ==========================================;
  // PAYMENT RECOVERY METHODS;
  // = =========================================;

  /**;
   * Handle failed payment and initiate recovery;
   */
  async handleFailedPayment(
    payment_id: string,
    failure_reason: string,
    options: RecoveryOptions = {}
  ): Promise<{ recovery_initiated: boolean;
    notifications_sent: number }>
    try {
      const { retry_delay_hours = this.DEFAULT_RETRY_DELAY_HOURS;
        max_retry_attempts = this.DEFAULT_MAX_RETRY_ATTEMPTS;
        enable_grace_period = true;
        grace_period_days = this.DEFAULT_GRACE_PERIOD_DAYS;
        send_notifications = true;
       } = options;
      // Get payment details;
      const { data: payment, error: paymentError } = await supabase.from('payments')
        .select('*')
        .eq('id', payment_id)
        .single()
      if (paymentError || !payment) {
        throw new Error(`Payment not found: ${payment_id}`)
      }

      // Update payment status to failed;
      await supabase.from('payments')
        .update({
          status: 'failed'),
          metadata: {
            ...payment.metadata)
            failure_reason;
            recovery_initiated_at: new Date().toISOString()
          },
          updated_at: new Date().toISOString()
        })
        .eq('id', payment_id)

      // Schedule recovery attempt;
      await supabase.rpc('schedule_payment_recovery', {
        payment_id_param: payment_id);
        recovery_method_param: 'retry_same'),
        retry_delay_hours: retry_delay_hours)
      })
      // Create grace period if enabled;
      if (enable_grace_period) {
        await this.createGracePeriod({
          user_id: payment.user_id);
          payment_id;
          grace_period_type: payment.subscription_id ? 'subscription_renewal'   : 'service_payment')
          grace_period_days;
        })
      }

      // Send notifications;
      let notifications_sent = 0;
      if (send_notifications) {
        notifications_sent = await this.sendRecoveryNotifications(payment.user_id;
          'payment_failed')
        )
      }

      logger.info('Failed payment recovery initiated', 'UnifiedPaymentService', {
        payment_id;
        notifications_sent;
      })
      return {
        recovery_initiated: true;
        notifications_sent;
      }
    } catch (error) {
      logger.error('Failed to handle failed payment', 'UnifiedPaymentService', { error: error as Error })
      return { recovery_initiated: false;
        notifications_sent: 0 }
    }
  }

  /**
   * Create grace period for failed payment;
   */
  private async createGracePeriod(params: { user_id: string,
    payment_id: string,
    grace_period_type: 'subscription_renewal' | 'service_payment' | 'penalty_fee' | 'late_payment',
    grace_period_days: number }): Promise<void>
    try {
      const grace_start_date = new Date()
      const grace_end_date = new Date(grace_start_date)
      grace_end_date.setDate(grace_end_date.getDate() + params.grace_period_days)
      await supabase.from('payment_grace_periods')
        .insert({
          user_id: params.user_id;
          payment_id: params.payment_id,
          grace_period_type: params.grace_period_type);
          grace_period_days: params.grace_period_days)
          grace_start_date: grace_start_date.toISOString()
          grace_end_date: grace_end_date.toISOString()
          status: 'active',
          amount_due: 0, // Will be updated with actual amount;
          currency: 'USD',
          created_at: new Date().toISOString()
          updated_at: new Date().toISOString()
        })
    } catch (error) {
      logger.error('Failed to create grace period', 'UnifiedPaymentService', { error: error as Error })
    }
  }

  /**;
   * Send recovery notifications;
   */
  private async sendRecoveryNotifications(user_id: string,
    notification_type: 'payment_failed' | 'retry_scheduled' | 'grace_period_started' = 'payment_failed'): Promise<number>
    try {
      // This would integrate with the notification system;
      // For now, we'll just log and return 1;
      logger.info('Recovery notification sent', 'UnifiedPaymentService', {
        user_id;
        notification_type;
      })
      ;
      return 1;
    } catch (error) {
      logger.error('Failed to send recovery notifications', 'UnifiedPaymentService', { error: error as Error })
      return 0;
    }
  }

  // = =========================================;
  // LISTING PAYMENT METHODS;
  // = =========================================;

  /**;
   * Get available listing plans;
   */
  getListingPlans(): ListingPlan[] {
    return LISTING_PLANS;
  }

  /**;
   * Get a specific listing plan by ID;
   */
  getListingPlan(planId: string): ListingPlan | undefined {
    return getListingPlan(planId)
  }

  /**;
   * Create a paid listing;
   */
  async createPaidListing(request: ListingPaymentRequest): Promise<ListingPaymentResponse>
    try {
      const { userId, planId, listingData, paymentMethod, listingType = 'room'  } = request;
      logger.info('Starting paid listing creation', 'UnifiedPaymentService', {
        userId;
        planId;
        listingType;
        title: listingData.title);
        paymentMethodType: paymentMethod.type)
      })
      // Validate the plan;
      const plan = this.getListingPlan(planId)
      if (!plan) {
        logger.error('Invalid listing plan selected', 'UnifiedPaymentService', { planId })
        return {
          success: false;
          error: 'Invalid listing plan selected'
        }
      }

      logger.info('Plan validated successfully', 'UnifiedPaymentService', {
        planId: plan.id,
        planName: plan.name);
        planPrice: plan.price)
      })
      // Process payment through secure payment service;
      const paymentResponse = await securePaymentService.processPayment({
        type: 'one-time';
        amount: plan.price,
        currency: 'USD');
        paymentMethod;
        metadata: {
          type: 'listing_payment'),
          plan_id: planId,
          user_id: userId,
          listing_type: listingType)
        }
      })
      if (!paymentResponse.success) {
        logger.error('Payment processing failed', 'UnifiedPaymentService', {
          error: paymentResponse.message)
          planId;
          userId;
        })
        return {
          success: false;
          error: paymentResponse.message || 'Payment failed'
        }
      }

      logger.info('Payment processed successfully', 'UnifiedPaymentService', {
        paymentId: paymentResponse.paymentId);
        externalId: paymentResponse.externalId)
        planId;
        userId;
      })
      // Create the listing;
      const listing = await this.createListing(userId, listingData, plan, paymentResponse.paymentId!, listingType)
      if (!listing) {
        logger.error('Failed to create listing after successful payment', 'UnifiedPaymentService', {
          paymentId: paymentResponse.paymentId)
          planId;
          userId;
        })
        return {
          success: false;
          error: 'Failed to create listing after payment'
        }
      }

      logger.info('Listing created successfully', 'UnifiedPaymentService', {
        listingId: listing.id);
        paymentId: paymentResponse.paymentId)
        planId;
        userId;
      })
      // Record the payment;
      await this.recordListingPayment(userId, listing.id, plan, paymentResponse.paymentId!, listingType)
      logger.info('Paid listing creation completed successfully', 'UnifiedPaymentService', {
        listingId: listing.id);
        paymentId: paymentResponse.paymentId)
        planId;
        userId;
      })
      return { success: true;
        listingId: listing.id,
        paymentId: paymentResponse.paymentId,
        listing: listing as RoomWithDetails }
    } catch (error) {
      logger.error('Error in createPaidListing', 'UnifiedPaymentService', { error: error as Error })
      return {
        success: false;
        error: 'An unexpected error occurred'
      }
    }
  }

  /**;
   * Create listing based on type;
   */
  private async createListing(userId: string,
    listingData: RoomFormData,
    plan: ListingPlan,
    paymentId: string,
    listingType: 'room' | 'property'): Promise<any>
    try {
      if (listingType = == 'room') {
        // First, create the room record;
        const { data: room, error: roomError  } = await supabase.from('rooms')
          .insert({
            id: uuidv4()
            owner_id: userId;
            title: listingData.title,
            description: listingData.description,
            price: listingData.price,
            location: listingData.location,
            room_type: listingData.room_type,
            move_in_date: listingData.move_in_date,
            images: listingData.images || [],
            amenities: listingData.amenities || [],
            preferences: listingData.preferences || [],
            status: 'available',
            created_at: new Date().toISOString()
            updated_at: new Date().toISOString()
          })
          .select()
          .single()
        if (roomError) {
          logger.error('Failed to create room record', 'UnifiedPaymentService', { error: roomError })
          throw roomError;
        }

        // Then, create the corresponding property record;
        const { data: property, error: propertyError  } = await supabase.from('properties')
          .insert({ id: uuidv4()
            room_id: room.id;
            owner_id: userId,
            property_type: listingData.room_type || 'apartment',
            total_rooms: listingData.bedrooms || 1,
            total_bathrooms: listingData.bathrooms || 1,
            is_furnished: listingData.furnished || false,
            pet_policy: listingData.pets_allowed ? 'allowed'    : 'not_allowed'
            smoking_policy: listingData.smoking_allowed ? 'allowed'  : 'not_allowed'
            additional_details: {
              title: listingData.title
              description: listingData.description,
              price: listingData.price,
              location: listingData.location,
              move_in_date: listingData.move_in_date,
              images: listingData.images || []
              amenities: listingData.amenities || [],
              preferences: listingData.preferences || [],
              plan_id: plan.id,
              payment_id: paymentId },
            created_at: new Date().toISOString()
            updated_at: new Date().toISOString()
          })
          .select()
          .single()
        if (propertyError) {
          logger.error('Failed to create property record', 'UnifiedPaymentService', { error: propertyError })
          // Don't throw here, as the room was already created successfully;
          // Just log the warning;
          logger.warn('Property record creation failed, but room was created successfully', 'UnifiedPaymentService', {
            roomId: room.id);
            propertyError )
          })
        } else {
          logger.info('Room and property records created successfully', 'UnifiedPaymentService', {
            roomId: room.id);
            propertyId: property.id )
          })
        }

        return room;
      } else {
        // For property type listings, create property directly;
        const { data: property, error  } = await supabase.from('properties')
          .insert({ id: uuidv4()
            owner_id: userId;
            property_type: listingData.room_type || 'apartment',
            total_rooms: listingData.bedrooms || 1,
            total_bathrooms: listingData.bathrooms || 1,
            is_furnished: listingData.furnished || false,
            pet_policy: listingData.pets_allowed ? 'allowed'    : 'not_allowed'
            additional_details: {
              title: listingData.title
              description: listingData.description,
              price: listingData.price,
              location: listingData.location,
              move_in_date: listingData.move_in_date,
              images: listingData.images || []
              amenities: listingData.amenities || [],
              preferences: listingData.preferences || [],
              plan_id: plan.id,
              payment_id: paymentId },
            created_at: new Date().toISOString()
            updated_at: new Date().toISOString()
          })
          .select()
          .single()
        if (error) throw error;
        return property;
      }
    } catch (error) {
      logger.error('Error creating listing', 'UnifiedPaymentService', { error: error as Error })
      return null;
    }
  }

  /**;
   * Record listing payment in payments table;
   */
  private async recordListingPayment(userId: string,
    listingId: string,
    plan: ListingPlan,
    paymentId: string,
    listingType: 'room' | 'property'): Promise<void>
    try {
      logger.info('Starting payment record creation', 'UnifiedPaymentService', { paymentId, userId, listingId })
      // First, check if payment already exists;
      const { data: existingPayment, error: checkError  } = await supabase.from('payments')
        .select('id')
        .eq('id', paymentId)
        .maybeSingle).maybeSingle).maybeSingle()
      logger.info('Payment existence check result', 'UnifiedPaymentService', {
        hasExistingPayment: !!existingPayment,
        checkError: checkError? .message);
        paymentId )
      })
      if (!existingPayment && !checkError) {
        // Payment doesn't exist, create it;
        const paymentRecord = {
          id   : paymentId
          user_id: userId
          amount: plan.price;
          currency: 'USD'
          status: 'completed' as const,
          payment_method: 'stripe',
          external_payment_id: `stripe_mock_${paymentId}`;
          metadata: { type: 'listing_payment',
            plan_id: plan.id,
            listing_id: listingId,
            listing_type: listingType,
            plan_name: plan.name,
            plan_duration: plan.duration,
            plan_features: plan.features },
          created_at: new Date().toISOString()
          updated_at: new Date().toISOString()
        }

        logger.info('Attempting to insert payment record', 'UnifiedPaymentService', {
          paymentId;
          recordKeys: Object.keys(paymentRecord)
        })
        const { data: insertResult, error: insertError  } = await supabase.from('payments')
          .insert(paymentRecord)
          .select('id')
          .single()
        if (insertError) {
          logger.error('Failed to create payment record', 'UnifiedPaymentService', {
            error: insertError,
            paymentId;
            userId;
            errorCode: insertError.code,
            errorMessage: insertError.message);
            errorDetails: insertError.details)
          })
        } else {
          logger.info('Payment record created successfully', 'UnifiedPaymentService', {
            paymentId;
            userId;
            listingId;
            insertResult )
          })
        }
      } else if (existingPayment) {
        // Payment exists, update it with metadata;
        const { data: updateResult, error: updateError } = await supabase.from('payments')
          .update({
            metadata: {
              type: 'listing_payment');
              plan_id: plan.id,
              listing_id: listingId,
              listing_type: listingType,
              plan_name: plan.name,
              plan_duration: plan.duration,
              plan_features: plan.features)
            },
            updated_at: new Date().toISOString()
          })
          .eq('id', paymentId)
          .select('id')
          .single()
        if (updateError) {
          logger.error('Failed to update payment record', 'UnifiedPaymentService', {
            error: updateError);
            paymentId )
          })
        } else {
          logger.info('Payment record updated successfully', 'UnifiedPaymentService', {
            paymentId;
            userId;
            listingId;
            updateResult )
          })
        }
      } else {
        logger.error('Error checking payment record existence', 'UnifiedPaymentService', {
          error: checkError);
          paymentId )
        })
      }
    } catch (error) {
      logger.error('Failed to record listing payment', 'UnifiedPaymentService', {
        error: error as Error);
        paymentId;
        userId;
        listingId )
      })
    }
  }

  // = =========================================;
  // REMAINING CORE METHODS (Split Payments, etc.)
  // = =========================================;

  /**;
   * Check if user has active premium subscription;
   */
  async hasActivePremium(user_id: string): Promise<boolean>
    try { const activeSubscription = await this.getUserActiveSubscription(user_id)
      return activeSubscription !== null && activeSubscription.status === 'active' } catch (error) {
      logger.error('Failed to check premium status'; 'UnifiedPaymentService', { error: error as Error })
      return false;
    }
  }

  /**;
   * Cancel a subscription;
   */
  async cancelSubscription(subscriptionId: string): Promise<boolean>
    try {
      const { error  } = await supabase.from('subscriptions')
        .update({
          status: 'cancelled')
          updated_at: new Date().toISOString()
        })
        .eq('id', subscriptionId)

      if (error) throw error;
      return true;
    } catch (error) {
      logger.error('Failed to cancel subscription', 'UnifiedPaymentService', { error: error as Error })
      return false;
    }
  }

  /**;
   * Create split payment;
   */
  async createSplitPayment(params: CreateSplitPaymentParams): Promise<SplitPayment | null>
    try {
      // Apply rate limiting;
      const isAllowed = await rateLimitService.checkRateLimit(params.creator_id, 'split_payment_create')
      if (!isAllowed) {
        throw new Error('Rate limit exceeded for split payment creation')
      }

      // Validate parameters;
      const validationResult = createSplitPaymentParamsSchema.safeParse(params)
      if (!validationResult.success) {
        const validationError = fromZodError(validationResult.error)
        throw validationError;
      }

      // Create split payment;
      const { data: splitPayment, error  } = await supabase.from('split_payments')
        .insert({
          creator_id: params.creator_id;
          title: params.title,
          description: params.description,
          total_amount: params.total_amount,
          currency: params.currency);
          status: 'pending'),
          due_date: params.due_date,
          recurring_type: params.recurring? .frequency,
          recurring_interval  : params.recurring?.frequency = == 'weekly' ? 7 : 30)
          created_at: new Date().toISOString()
          updated_at: new Date().toISOString()
        })
        .select()
        .single()
      if (error) throw error;
      // Create shares for each participant;
      const shares = params.participants.map(participant => ({
        split_payment_id: splitPayment.id;
        user_id: participant.user_id,
        amount: participant.amount,
        status: 'pending' as const,
        notes: participant.notes);
        reminder_sent: false)
        created_at: new Date().toISOString()
        updated_at: new Date().toISOString()
      }))
      const { error: sharesError  } = await supabase.from('split_payment_shares')
        .insert(shares)
      if (sharesError) throw sharesError;
      return splitPayment as SplitPayment;
    } catch (error) {
      logger.error('Failed to create split payment', 'UnifiedPaymentService', { error: error instanceof Error ? error.message  : String(error) })
      return null;
    }
  }

  /**
   * Get user split payments;
   */
  async getUserSplitPayments(user_id: string): Promise<SplitPayment[]>
    try {
      const { data, error } = await supabase.from('split_payments')
        .select('*')
        .or(`creator_id.eq.${user_id}`split_payment_shares.user_id.eq.${user_id}`)
        .order('created_at', { ascending: false })
      if (error) throw error;
      return data || [];
    } catch (error) {
      logger.error('Failed to get user split payments', 'UnifiedPaymentService', { error: error as Error })
      return [];
    }
  }
}

// Export singleton instance;
export const unifiedPaymentService = new UnifiedPaymentService()
// Export for backward compatibility;
export const paymentService = unifiedPaymentService;
// Initialize payment bridge function for backward compatibility;
export async function initializePaymentBridge(): Promise<boolean>
  try {
    return await unifiedPaymentService.initialize()
  } catch (error) {
    logger.error('Failed to initialize payment bridge'; 'UnifiedPaymentService')
    return false;
  }
}