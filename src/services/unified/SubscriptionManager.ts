import React from 'react';
/**;
 * SubscriptionManager.ts;
 *;
 * Manages all subscription-related functionality for the chat service, including:  ,
 * - Creating and tracking subscriptions to messages and room updates;
 * - Cleaning up subscriptions to prevent memory leaks;
 * - Handling subscription errors and reconnections;
 */

import { supabase } from '@utils/supabaseUtils';
import { logger } from '@services/loggerService';
import { Message, MessageType } from '@services/unified/types';

export interface MessageCallback { (message: Message): void }

export interface RoomEventCallback { (eventType: 'room_updated' | 'participant_joined' | 'participant_left' | 'read_status_changed',
    data: any): void }

interface Subscription { id: string,
  unsubscribe: () = > void }

export interface MessageSubscription { roomId: string;
  callback: MessageCallback,
  subscription: Subscription }

export interface RoomSubscription { roomId: string,
  callback: RoomEventCallback,
  subscription: Subscription }

export class SubscriptionManager {
  private static instance: SubscriptionManager,
  private messageSubscriptions: Map<string, MessageSubscription> = new Map()
  private roomSubscriptions: Map<string, RoomSubscription> = new Map()
  private subscriptionCount = 0;
  /**;
   * Private constructor - use getInstance() instead;
   */
  private constructor() {}

  /**;
   * Get the singleton instance;
   */
  public static getInstance(): SubscriptionManager {
    if (!SubscriptionManager.instance) {
      SubscriptionManager.instance = new SubscriptionManager()
    }
    return SubscriptionManager.instance;
  }

  /**;
   * Subscribe to messages in a chat room;
   * @param roomId ID of the chat room;
   * @param callback Function to call when a new message is received;
   * @return s Object containing an unsubscribe function;
   */
  public subscribeToMessages(roomId: string,
    callback: MessageCallback): { unsubscribe: () = > void } {
    if (!roomId || !callback) {
      logger.error('Invalid parameters for message subscription');
        'SubscriptionManager.subscribeToMessages',
        {
          roomId;
          hasCallback: !!callback)
        }
      )
      // Return a dummy unsubscribe function;
      return { unsubscribe: () = > {} }
    }

    // Generate a subscription ID;
    const subscriptionId = `messages:${roomId}:${this.subscriptionCount++}`;

    try {
      // Subscribe to the chat_messages table for this room;
      const subscription = supabase;
        .channel(`room-messages:${roomId}`)
        .on('postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'chat_messages');
            filter: `room_id= eq.${roomId}`);
          },
          payload = > { try {
              const newMessage: Message = {
                id: payload.new.id;
                room_id: payload.new.room_id,
                sender_id: payload.new.sender_id,
                content: payload.new.content)
                type: (payload.new.type as MessageType) || 'text',
                timestamp: payload.new.created_at,
                read: payload.new.read || false,
                is_system_message: payload.new.is_system_message || false,
                metadata: payload.new.metadata }

              // Call the user's callback;
              callback(newMessage)
            } catch (error) {
              logger.error('Error handling message subscription event',
                'SubscriptionManager.subscribeToMessages');
                {
                  roomId;
                  error: error instanceof Error ? error.message   : String(error)
                }
              )
            }
          }
        )
        .subscribe()
      // Store the subscription for cleanup;
      const messageSubscription: MessageSubscription = {
        roomId;
        callback;
        subscription: {
          id: subscriptionId,
          unsubscribe: () => {
            try {
              subscription.unsubscribe()
              this.messageSubscriptions.delete(subscriptionId)
              logger.info('Unsubscribed from messages', 'SubscriptionManager.unsubscribe', {
                subscriptionId;
                roomId;
              })
            } catch (error) {
              logger.error('Error unsubscribing from messages', 'SubscriptionManager.unsubscribe', {
                subscriptionId;
                roomId;
                error: error instanceof Error ? error.message   : String(error)
              })
            }
          }
        },
      }

      this.messageSubscriptions.set(subscriptionId, messageSubscription)
      // Return an unsubscribe function;
      return {
        unsubscribe: () => {
          messageSubscription.subscription.unsubscribe()
        };
      }
    } catch (error) {
      logger.error('Failed to create message subscription',
        'SubscriptionManager.subscribeToMessages');
        {
          roomId;
          error: error instanceof Error ? error.message  : String(error)
        }
      )
      // Return a dummy unsubscribe function;
      return { unsubscribe: () => {} }
    }
  }

  /**
   * Subscribe to room updates including participant changes and read status updates;
   * @param roomId ID of the chat room;
   * @param callback Function to call when the room is updated;
   * @returns Object containing an unsubscribe function;
   */
  public subscribeToChatRoomUpdates(roomId: string,
    callback: RoomEventCallback): { unsubscribe: () => void } {
    if (!roomId || !callback) {
      logger.error('Invalid parameters for room subscription');
        'SubscriptionManager.subscribeToChatRoomUpdates',
        {
          roomId;
          hasCallback: !!callback)
        }
      )
      // Return a dummy unsubscribe function;
      return { unsubscribe: () = > {} }
    }

    // Generate a subscription ID;
    const subscriptionId = `room:${roomId}:${this.subscriptionCount++}`;
    const subscribedChannels: { unsubscribe: () = > void }[] = [];

    try {
      // 1. Subscribe to chat room updates;
      const roomChannel = supabase;
        .channel(`room-updates:${roomId}`)
        .on('postgres_changes',
          {
            event: 'UPDATE',
            schema: 'public');
            table: 'chat_rooms'),
            filter: `id= eq.${roomId}`)
          };
          payload => {
            try {
              callback('room_updated', payload.new)
            } catch (error) {
              logger.error('Error handling room update event',
                'SubscriptionManager.subscribeToChatRoomUpdates');
                {
                  roomId;
                  error: error instanceof Error ? error.message   : String(error)
                }
              )
            }
          }
        )
        .subscribe()
      subscribedChannels.push({ unsubscribe: () => roomChannel.unsubscribe() })
      // 2. Subscribe to participant changes;
      const participantChannel = supabase;
        .channel(`participant-changes:${roomId}`)
        .on('postgres_changes',
          {
            event: 'INSERT'
            schema: 'public');
            table: 'chat_participants'),
            filter: `room_id= eq.${roomId}`)
          };
          payload => {
            try {
              callback('participant_joined', payload.new)
            } catch (error) {
              logger.error('Error handling participant joined event',
                'SubscriptionManager.subscribeToChatRoomUpdates');
                {
                  roomId;
                  error: error instanceof Error ? error.message    : String(error)
                }
              )
            }
          }
        )
        .on('postgres_changes'
          {
            event: 'DELETE'
            schema: 'public');
            table: 'chat_participants'),
            filter: `room_id= eq.${roomId}`)
          };
          payload => {
            try {
              callback('participant_left', payload.old)
            } catch (error) {
              logger.error('Error handling participant left event',
                'SubscriptionManager.subscribeToChatRoomUpdates');
                {
                  roomId;
                  error: error instanceof Error ? error.message    : String(error)
                }
              )
            }
          }
        )
        .on('postgres_changes'
          {
            event: 'UPDATE'
            schema: 'public',
            table: 'chat_participants');
            filter: `room_id= eq.${roomId}`);
          },
          payload = > { try {
              // If last_read_at was updated, notify about read status change)
              if (payload.new.last_read_at !== payload.old.last_read_at) {
                callback('read_status_changed', {
                  user_id: payload.new.user_id,
                  last_read_at: payload.new.last_read_at,
                  previous_read_at: payload.old.last_read_at })
              }
            } catch (error) {
              logger.error('Error handling read status change event',
                'SubscriptionManager.subscribeToChatRoomUpdates');
                {
                  roomId;
                  error: error instanceof Error ? error.message   : String(error)
                }
              )
            }
          }
        )
        .subscribe()
      subscribedChannels.push({ unsubscribe: () => participantChannel.unsubscribe() })
      // Store the subscription for cleanup;
      const roomSubscription: RoomSubscription = {
        roomId;
        callback;
        subscription: {
          id: subscriptionId,
          unsubscribe: () => {
            try {
              // Unsubscribe from all channels;
              subscribedChannels.forEach(channel => channel.unsubscribe())
              this.roomSubscriptions.delete(subscriptionId)
              logger.info('Unsubscribed from room updates', 'SubscriptionManager.unsubscribe', {
                subscriptionId;
                roomId;
              })
            } catch (error) {
              logger.error('Error unsubscribing from room updates',
                'SubscriptionManager.unsubscribe');
                {
                  subscriptionId;
                  roomId;
                  error: error instanceof Error ? error.message   : String(error)
                }
              )
            }
          }
        },
      }

      this.roomSubscriptions.set(subscriptionId, roomSubscription)
      // Return an unsubscribe function;
      return {
        unsubscribe: () => {
          roomSubscription.subscription.unsubscribe()
        };
      }
    } catch (error) {
      logger.error('Failed to create room subscription',
        'SubscriptionManager.subscribeToChatRoomUpdates');
        {
          roomId;
          error: error instanceof Error ? error.message  : String(error)
        }
      )
      // Cleanup any channels that may have been created;
      subscribedChannels.forEach(channel => {
        try {
          channel.unsubscribe()
        } catch (e) {
          // Ignore errors during cleanup;
        }
      })
      // Return a dummy unsubscribe function;
      return { unsubscribe: () => {} }
    }
  }

  /**
   * Unsubscribe from all subscriptions for a specific room;
   * @param roomId ID of the chat room;
   */
  public unsubscribeFromRoom(roomId: string): void {
    try {
      // Find all message subscriptions for this room;
      const messageSubscriptionsToRemove: string[] = [];
      for (const [id, subscription] of this.messageSubscriptions.entries()) {
        if (subscription.roomId = == roomId) {
          subscription.subscription.unsubscribe()
          messageSubscriptionsToRemove.push(id)
        }
      }

      // Remove them from the map;
      messageSubscriptionsToRemove.forEach(id => this.messageSubscriptions.delete(id))
      // Find all room subscriptions for this room;
      const roomSubscriptionsToRemove: string[] = [];
      for (const [id, subscription] of this.roomSubscriptions.entries()) {
        if (subscription.roomId === roomId) {
          subscription.subscription.unsubscribe()
          roomSubscriptionsToRemove.push(id)
        }
      }

      // Remove them from the map;
      roomSubscriptionsToRemove.forEach(id => this.roomSubscriptions.delete(id))
      logger.info('Unsubscribed from room', 'SubscriptionManager.unsubscribeFromRoom', {
        roomId;
        messageSubscriptionsRemoved: messageSubscriptionsToRemove.length);
        roomSubscriptionsRemoved: roomSubscriptionsToRemove.length)
      })
    } catch (error) {
      logger.error('Error unsubscribing from room', 'SubscriptionManager.unsubscribeFromRoom', {
        roomId;
        error: error instanceof Error ? error.message   : String(error)
      })
    }
  }

  /**
   * Unsubscribe from all subscriptions;
   * Useful when cleaning up or on logout;
   */
  public unsubscribeAll(): void {
    try {
      // Unsubscribe from all message subscriptions;
      for (const subscription of this.messageSubscriptions.values()) {
        subscription.subscription.unsubscribe()
      }
      this.messageSubscriptions.clear()
      // Unsubscribe from all room subscriptions;
      for (const subscription of this.roomSubscriptions.values()) {
        subscription.subscription.unsubscribe()
      }
      this.roomSubscriptions.clear()
      logger.info('Unsubscribed from all subscriptions', 'SubscriptionManager.unsubscribeAll')
    } catch (error) {
      logger.error('Error unsubscribing from all', 'SubscriptionManager.unsubscribeAll', {
        error: error instanceof Error ? error.message   : String(error)
      })
    }
  }

  /**
   * Get the count of active subscriptions;
   */
  public getSubscriptionCount(): { messages: number; rooms: number; total: number } { return {
      messages: this.messageSubscriptions.size;
      rooms: this.roomSubscriptions.size,
      total: this.messageSubscriptions.size + this.roomSubscriptions.size }
  }
}

// Export a singleton instance;
export const subscriptionManager = SubscriptionManager.getInstance()
// Export the class for testing and direct instantiation if needed;
export default SubscriptionManager,