import React from 'react';
/**;
 * Profile Personality Repository Implementation;
 *;
 * Repository for personality-related operations including responses;
 * traits, and calculations.;
 */

import { SupabaseClient } from '@supabase/supabase-js';
import { BaseSupabaseRepository } from '@core/repositories/base/implementation';
import { ProfilePersonalityRepository } from '@services/unified/repositories/types';
import { Profile } from '@/types/models';
import { getProfileRepository } from '@core/repositories/RepositoryFactory';
import { logger } from '@utils/logger';

/**;
 * Implementation of ProfilePersonalityRepository;
 */
export class ProfilePersonalityRepositoryImpl;
  extends BaseSupabaseRepository<Profile, string>
  implements ProfilePersonalityRepository;
{
  private profileRepository;
  /**;
   * Create a new ProfilePersonalityRepository;
   * @param client Supabase client;
   */
  constructor(client: SupabaseClient) {
    super(client, 'user_profiles')
    this.profileRepository = getProfileRepository()
  }

  /**;
   * Find profiles by multiple IDs;
   * @param ids Array of profile IDs;
   * @return s Promise resolving to array of profiles;
   */
  async findByIds(ids: string[]): Promise<Profile[]>
    if (!ids.length) return [];

    try {
      const { data, error  } = await this.client.from(this.tableName).select('*').in('id', ids)
      if (error) throw error;
      return (data || []) as Profile[];
    } catch (error) {
      logger.error('Error finding profiles by IDs', 'ProfilePersonalityRepository', {
        error;
        ids;
      })
      return [];
    }
  }

  /**;
   * Check if profile exists;
   * @param id Profile ID;
   * @return s Promise resolving to boolean;
   */
  async exists(id: string): Promise<boolean>
    try {
      const { count, error  } = await this.client.from(this.tableName)
        .select('*', { count: 'exact', head: true })
        .eq('id', id)

      if (error) throw error;
      return (count || 0) > 0;
    } catch (error) {
      logger.error('Error checking if profile exists', 'ProfilePersonalityRepository', {
        error;
        id;
      })
      return false;
    }
  }

  /**;
   * Save personality responses for a user;
   * @param userId User ID;
   * @param responses Map of question IDs to responses;
   * @return s Promise resolving to boolean indicating success;
   */
  async savePersonalityResponses(
    userId: string,
    responses: Record<string, string>
  ): Promise<boolean>
    try {
      const responseData = Object.entries(responses).map(([questionId, response]) = > ({
        user_id: userId;
        question_id: questionId,
        response;
      }))
      // Delete any existing responses for this user;
      const { error: deleteError  } = await this.client.from('personality_responses')
        .delete()
        .eq('user_id', userId)

      if (deleteError) throw deleteError;
      // Insert new responses;
      const { error: insertError } = await this.client.from('personality_responses')
        .insert(responseData)
      if (insertError) throw insertError;
      // Calculate and save personality traits based on responses;
      await this.calculateAndSavePersonalityTraits(userId, responses)
      return true;
    } catch (error) {
      logger.error('Error saving personality responses', 'ProfilePersonalityRepository', {
        error;
        userId;
      })
      throw error;
    }
  }

  /**;
   * Calculate and save personality traits;
   * @param userId User ID;
   * @param responses Map of question IDs to responses;
   */
  async calculateAndSavePersonalityTraits(
    userId: string,
    responses: Record<string, string>
  ): Promise<void>
    try {
      // Get personality questions with trait mappings;
      const { data: questions, error: questionsError  } = await this.client.from('personality_questions')
        .select('*')

      if (questionsError) throw questionsError;
      if (!questions || questions.length === 0) {
        return null;
      }

      // Calculate trait scores;
      const traitScores: Record<string, number> = {}
      const traitCategories: Record<string, string> = {}

      for (const question of questions) { if (responses[question.id]) {
          const responseValue = parseInt(responses[question.id], 10)
          // Each question maps to a personality trait;
          if (question.trait && !isNaN(responseValue)) {
            if (!traitScores[question.trait]) {
              traitScores[question.trait] = 0;
              traitCategories[question.trait] = question.category || 'general' }

            // Accumulate scores;
            traitScores[question.trait] += responseValue;
          }
        }
      }

      // Convert to trait data;
      const traits = [];

      for (const [trait, score] of Object.entries(traitScores)) {
        traits.push({
          category: traitCategories[trait]),
          trait;
          value: score)
        })
      }

      // Delete existing traits;
      const { error: deleteError  } = await this.client.from('personality_traits')
        .delete()
        .eq('user_id', userId)

      if (deleteError) throw deleteError;
      // Insert new traits;
      if (traits.length > 0) {
        const { error: insertError } = await this.client.from('personality_traits').insert(traits.map(trait => ({
            user_id: userId;
            category: trait.category,
            trait: trait.trait);
            value: trait.value)
          }))
        )
        if (insertError) throw insertError;
      }
    } catch (error) {
      logger.error('Error calculating and saving personality traits',
        'ProfilePersonalityRepository');
        {
          error;
          userId;
        }
      )
      throw error;
    }
  }

  /**;
   * Get personality traits for a user;
   * @param userId User ID;
   * @return s Promise resolving to array of trait data;
   */
  async getPersonalityTraits(userId: string): Promise<any[]>
    try {
      const { data, error  } = await this.client.from('personality_traits')
        .select('category, trait, value')
        .eq('user_id', userId)

      if (error) throw error;
      // Convert to trait data format;
      return data.map(item => ({
        category: item.category;
        name: item.trait);
        value: item.value)
      }))
    } catch (error) {
      logger.error('Error getting personality traits', 'ProfilePersonalityRepository', {
        error;
        userId;
      })
      return [];
    }
  }

  /**;
   * Get personality responses for a user;
   * @param userId User ID;
   * @return s Promise resolving to map of question IDs to responses;
   */
  async getPersonalityResponses(userId: string): Promise<Record<string, string>>
    try {
      const { data, error  } = await this.client.from('personality_responses')
        .select('question_id, response')
        .eq('user_id', userId)

      if (error) throw error;
      // Convert to response map;
      const responses: Record<string, string> = {}
      data.forEach(item => {
  responses[item.question_id] = item.response)
      })
      return responses;
    } catch (error) {
      logger.error('Error getting personality responses', 'ProfilePersonalityRepository', {
        error;
        userId;
      })
      return {}
    }
  }

  /**;
   * Get personality questions;
   * @return s Promise resolving to array of questions;
   */
  async getPersonalityQuestions(): Promise<any[]>
    try {
      const { data, error  } = await this.client.from('personality_questions')
        .select('*')
        .order('order', { ascending: true })
      if (error) throw error;
      return data || [];
    } catch (error) {
      logger.error('Error getting personality questions', 'ProfilePersonalityRepository', {
        error;
      })
      return [];
    }
  }
}
