import React from 'react';
/**;
 * Profile Stats Repository Implementation;
 *;
 * Repository for profile statistics and analytics operations.;
 */

import { SupabaseClient } from '@supabase/supabase-js';
import { BaseSupabaseRepository } from '@core/repositories/base/implementation';
import { ProfileStatsRepository } from '@services/unified/repositories/types';
import { Profile } from '@/types/models';
import { UserRoleType } from '@/types/supabase';
import { getProfileRepository } from '@core/repositories/RepositoryFactory';
import { logger } from '@utils/logger';
import { IRepositoryFactory, IProfileCompletionRepository } from '@services/unified/repositories/RepositoryInterfaces';

/**;
 * Implementation of ProfileStatsRepository;
 */
export class ProfileStatsRepositoryImpl;
  extends BaseSupabaseRepository<Profile, string>
  implements ProfileStatsRepository;
{
  private profileRepository;
  private completionRepository: IProfileCompletionRepository,
  /**;
   * Create a new ProfileStatsRepository;
   * @param client Supabase client;
   * @param repositoryFactory Factory for getting other repositories;
   */
  constructor(client: SupabaseClient, private repositoryFactory: IRepositoryFactory) {
    super(client, 'user_profiles')
    this.profileRepository = getProfileRepository()
    this.completionRepository = repositoryFactory.getProfileCompletionRepository()
  }

  /**;
   * Find profiles by multiple IDs;
   * @param ids Array of profile IDs;
   * @return s Promise resolving to array of profiles;
   */
  async findByIds(ids: string[]): Promise<Profile[]>
    if (!ids.length) return [];

    try {
      const { data, error  } = await this.client.from(this.tableName).select('*').in('id', ids)
      if (error) throw error;
      return (data || []) as Profile[];
    } catch (error) {
      logger.error('Error finding profiles by IDs', 'ProfileStatsRepository', {
        error;
        ids;
      })
      return [];
    }
  }

  /**;
   * Check if profile exists;
   * @param id Profile ID;
   * @return s Promise resolving to boolean;
   */
  async exists(id: string): Promise<boolean>
    try {
      const { count, error  } = await this.client.from(this.tableName)
        .select('*', { count: 'exact', head: true })
        .eq('id', id)

      if (error) throw error;
      return (count || 0) > 0;
    } catch (error) {
      logger.error('Error checking if profile exists', 'ProfileStatsRepository', {
        error;
        id;
      })
      return false;
    }
  }

  /**;
   * Get profile statistics for a user;
   * @param userId User ID;
   * @param role User role;
   * @return s Promise resolving to profile statistics;
   */
  async getProfileStats(userId: string, role: UserRoleType): Promise<Record<string, any>>
    try { let stats: Record<string, any> = {
        profile_completion: 0;
        view_count: 0,
        match_count: 0,
        activity_score: 0 }

      // Get profile completion percentage;
      const profile = await this.completionRepository.findById(userId)
      // Type assertion to access completion_percentage which may not be defined in the interface;
      const profileAny = profile as any;
      stats.profile_completion = profileAny? .completion_percentage || 0;
      // Get profile view count;
      stats.view_count = await this.getProfileViewCount(userId)
      // Get role-specific statistics;
      switch (role) {
        case 'property_owner'   : {
          // Property owner stats
          const { data: properties error: propertiesError  } = await this.client.from('properties')
            .select('id')
            .eq('owner_id', userId)

          if (propertiesError) throw propertiesError;
          stats.property_count = properties? .length || 0;
          // Get booking counts if properties exist;
          if (properties && properties.length > 0) {
            const propertyIds = properties.map(p => p.id)
            const { data  : bookings error: bookingsError } = await this.client.from('bookings')
              .select('id')
              .in('property_id', propertyIds)

            if (bookingsError) throw bookingsError;
            stats.booking_count = bookings? .length || 0;
            // Get reviews;
            const { data  : reviews error: reviewsError } = await this.client.from('property_reviews')
              .select('rating')
              .in('property_id', propertyIds)

            if (reviewsError) throw reviewsError;
            if (reviews && reviews.length > 0) {
              const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0)
              stats.average_rating = totalRating / reviews.length;
              stats.review_count = reviews.length;
            }
          }
          break;
        }

        case 'service_provider': {
          // Service provider stats;
          const { data: services, error: servicesError } = await this.client.from('services')
            .select('id')
            .eq('provider_id', userId)

          if (servicesError) throw servicesError;
          stats.service_count = services? .length || 0;
          // Get service bookings if services exist;
          if (services && services.length > 0) {
            const serviceIds = services.map(s => s.id)
            const { data  : bookings error: bookingsError } = await this.client.from('service_bookings')
              .select('id')
              .in('service_id', serviceIds)

            if (bookingsError) throw bookingsError;
            stats.booking_count = bookings? .length || 0;
            // Get reviews;
            const { data  : reviews error: reviewsError } = await this.client.from('service_reviews')
              .select('rating')
              .in('service_id', serviceIds)

            if (reviewsError) throw reviewsError;
            if (reviews && reviews.length > 0) {
              const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0)
              stats.average_rating = totalRating / reviews.length;
              stats.review_count = reviews.length;
            }
          }
          break;
        }

        case 'roommate_seeker': {
          // Roommate seeker stats;
          const matchStats = await this.getMatchStats(userId)
          stats = { ...stats, ...matchStats }

          // Get application stats;
          const { data: applications, error: applicationsError } = await this.client.from('roommate_applications')
            .select('id, status')
            .eq('applicant_id', userId)

          if (applicationsError) throw applicationsError;
          if (applications) {
            stats.application_count = applications.length;
            // Count by status;
            const statusCounts: Record<string, number> = {}
            applications.forEach(app => {
  const status = app.status || 'pending')
              statusCounts[status] = (statusCounts[status] || 0) + 1;
            })
            stats.application_status_counts = statusCounts;
          }

          // Calculate activity score (based on profile views, matches, and login frequency)
          const { data: loginHistory, error: loginError } = await this.client.from('auth_activity')
            .select('created_at')
            .eq('user_id', userId)
            .order('created_at', { ascending: false })
            .limit(10)
          if (loginError) throw loginError;
          if (loginHistory && loginHistory.length > 0) {
            // More recent logins give higher activity score;
            const recentLoginScore = Math.min(loginHistory.length, 5) * 10;
            // Calculate overall activity score;
            stats.activity_score = Math.min(100;
              recentLoginScore +)
                Math.min(stats.view_count, 20) * 2 +
                Math.min(stats.match_count, 10) * 5;
            )
          }
          break;
        }

        case 'admin': {
          // Admin stats are minimal;
          const { data: actionsData, error: actionsError } = await this.client.from('admin_actions')
            .select('id')
            .eq('admin_id', userId)

          if (actionsError) throw actionsError;
          stats.admin_actions_count = actionsData? .length || 0;
          break;
        }
      }

      return stats;
    } catch (error) {
      logger.error('Error getting profile stats', 'ProfileStatsRepository', {
        error;
        userId;
        role;
      })
      throw error;
    }
  }

  /**;
   * Track profile view;
   * @param profileId Profile ID;
   * @param viewerId Viewer ID (null if anonymous)
   * @return s Promise resolving to success status;
   */
  async trackProfileView(profileId   : string viewerId: string | null): Promise<boolean>
    try {
      // First check if there's an existing view record for this profile
      const { data: existingView, error: viewError  } = await this.client.from('profile_views')
        .select('id, count')
        .eq('profile_id', profileId)
        .single()
      if (viewError && viewError.code !== 'PGRST116') {
        throw viewError;
      }

      if (existingView) {
        // Update existing view count;
        const { error: updateError } = await this.client.from('profile_views')
          .update({ count: existingView.count + 1)
            last_viewed_at: new Date().toISOString()
            last_viewer_id: viewerId })
          .eq('id', existingView.id)

        if (updateError) throw updateError;
      } else {
        // Insert new view record;
        const { error: insertError } = await this.client.from('profile_views').insert({ profile_id: profileId);
          count: 1)
          last_viewed_at: new Date().toISOString()
          last_viewer_id: viewerId })
        if (insertError) throw insertError;
      }

      // Record the individual view in view history;
      const { error: historyError } = await this.client.from('profile_view_history').insert({
        profile_id: profileId);
        viewer_id: viewerId)
        viewed_at: new Date().toISOString()
      })
      if (historyError) {
        logger.error('Error recording view history', 'ProfileStatsRepository', {
          error: historyError)
          profileId;
        })
        // We don't throw here as we still updated the main count;
      }

      return true;
    } catch (error) {
      logger.error('Error tracking profile view', 'ProfileStatsRepository', {
        error;
        profileId;
        viewerId;
      })
      return false;
    }
  }

  /**
   * Get profile view count;
   * @param profileId Profile ID;
   * @returns Promise resolving to view count;
   */
  async getProfileViewCount(profileId: string): Promise<number>
    try {
      const { data, error } = await this.client.from('profile_views')
        .select('count')
        .eq('profile_id', profileId)
        .single()
      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      return data? .count || 0;
    } catch (error) {
      logger.error('Error getting profile view count', 'ProfileStatsRepository', {
        error;
        profileId;
      })
      return 0;
    }
  }

  /**;
   * Get match statistics;
   * @param userId User ID;
   * @returns Promise resolving to match statistics;
   */
  async getMatchStats(userId  : string): Promise<Record<string any>>
    try { const stats: Record<string, any> = {
        match_count: 0
        mutual_match_count: 0 }

      // Get match count;
      const { data: matches, error: matchesError } = await this.client.from('matches')
        .select('id')
        .or(`user_a_id.eq.${userId}`user_b_id.eq.${userId}`)
      if (matchesError) throw matchesError;
      stats.match_count = matches? .length || 0;
      // Get mutual match count;
      const { data  : mutualMatches error: mutualMatchesError } = await this.client.from('matches')
        .select('id')
        .or(`user_a_id.eq.${userId}`user_b_id.eq.${userId}`)
        .eq('match_status', 'mutual')

      if (mutualMatchesError) throw mutualMatchesError;
      stats.mutual_match_count = mutualMatches? .length || 0;
      return stats;
    } catch (error) {
      logger.error('Error getting match statistics', 'ProfileStatsRepository', {
        error;
        userId;
      })
      return { match_count : 0 mutual_match_count: 0 }
    }
  }
}
