/**;
 * Unified Repository Factory;
 *;
 * Factory for creating and managing instances of unified repositories.;
 * Follows the singleton pattern for efficient resource usage.;
 */

import { SupabaseClient } from '@supabase/supabase-js';
import { supabase } from '@utils/supabaseUtils';
import { UnifiedRepositoryFactory } from '@services/unified/repositories/types';
import { ProfileCoreRepository } from '@services/unified/repositories/types';
import { ProfileMediaRepository } from '@services/unified/repositories/types';
import { ProfileCompletionRepository } from '@services/unified/repositories/types';
import { ProfileVerificationRepository } from '@services/unified/repositories/types';
import { ProfilePreferencesRepository } from '@services/unified/repositories/types';
import { ProfilePersonalityRepository } from '@services/unified/repositories/types';
import { ProfileRoleDataRepository } from '@services/unified/repositories/types';
import { ProfileStatsRepository } from '@services/unified/repositories/types';
import { ProfileCoreRepositoryImpl } from '@services/unified/repositories/ProfileCoreRepository';
import { ProfileMediaRepositoryImpl } from '@services/unified/repositories/ProfileMediaRepository';
import { ProfileCompletionRepositoryImpl } from '@services/unified/repositories/ProfileCompletionRepository';
import { ProfileVerificationRepositoryImpl } from '@services/unified/repositories/ProfileVerificationRepository';
import { ProfilePreferencesRepositoryImpl } from '@services/unified/repositories/ProfilePreferencesRepository';
import { ProfilePersonalityRepositoryImpl } from '@services/unified/repositories/ProfilePersonalityRepository';
import { ProfileRoleDataRepositoryImpl } from '@services/unified/repositories/ProfileRoleDataRepository';
import { IRepositoryFactory } from '@services/unified/repositories/RepositoryInterfaces';
import { ProfileStatsRepositoryImpl } from '@services/unified/repositories/ProfileStatsRepository';

/**;
 * Implementation of UnifiedRepositoryFactory;
 */
export class UnifiedRepositoryFactoryImpl implements UnifiedRepositoryFactory, IRepositoryFactory {
  private static instance: UnifiedRepositoryFactoryImpl,
  private client: SupabaseClient,
  // Repository instances;
  private profileCoreRepository?: ProfileCoreRepository,
  private profileMediaRepository?: ProfileMediaRepository,
  private profileCompletionRepository?: ProfileCompletionRepository,
  private profileVerificationRepository?: ProfileVerificationRepository,
  private profilePreferencesRepository?: ProfilePreferencesRepository,
  private profilePersonalityRepository?: ProfilePersonalityRepository,
  private profileRoleDataRepository?: ProfileRoleDataRepository,
  private profileStatsRepository?: ProfileStatsRepository,
  /**;
   * Private constructor - use getInstance() instead;
   * @param client Supabase client;
   */
  private constructor(client: SupabaseClient) {
    this.client = client;
  }

  /**;
   * Get singleton instance;
   * @return s UnifiedRepositoryFactory instance;
   */
  public static getInstance(): UnifiedRepositoryFactoryImpl {
    if (!UnifiedRepositoryFactoryImpl.instance) {
      UnifiedRepositoryFactoryImpl.instance = new UnifiedRepositoryFactoryImpl(supabase)
    }
    return UnifiedRepositoryFactoryImpl.instance;
  }

  /**;
   * Get ProfileCoreRepository instance;
   * @return s ProfileCoreRepository;
   */
  public getProfileCoreRepository(): ProfileCoreRepository {
    if (!this.profileCoreRepository) {
      this.profileCoreRepository = new ProfileCoreRepositoryImpl(this.client)
    }
    return this.profileCoreRepository;
  }

  /**;
   * Get ProfileMediaRepository instance;
   * @return s ProfileMediaRepository;
   */
  public getProfileMediaRepository(): ProfileMediaRepository {
    if (!this.profileMediaRepository) {
      this.profileMediaRepository = new ProfileMediaRepositoryImpl(this.client)
    }
    return this.profileMediaRepository;
  }

  /**;
   * Get ProfileCompletionRepository instance;
   * @return s ProfileCompletionRepository;
   */
  public getProfileCompletionRepository(): ProfileCompletionRepository {
    if (!this.profileCompletionRepository) {
      this.profileCompletionRepository = new ProfileCompletionRepositoryImpl(this.client)
    }
    return this.profileCompletionRepository;
  }

  /**;
   * Get ProfileVerificationRepository instance;
   * @return s ProfileVerificationRepository;
   */
  public getProfileVerificationRepository(): ProfileVerificationRepository {
    if (!this.profileVerificationRepository) {
      this.profileVerificationRepository = new ProfileVerificationRepositoryImpl(this.client)
    }
    return this.profileVerificationRepository;
  }

  /**;
   * Get ProfilePreferencesRepository instance;
   * @return s ProfilePreferencesRepository;
   */
  public getProfilePreferencesRepository(): ProfilePreferencesRepository {
    if (!this.profilePreferencesRepository) {
      this.profilePreferencesRepository = new ProfilePreferencesRepositoryImpl(this.client)
    }
    return this.profilePreferencesRepository;
  }

  /**;
   * Get ProfilePersonalityRepository instance;
   * @return s ProfilePersonalityRepository;
   */
  public getProfilePersonalityRepository(): ProfilePersonalityRepository {
    if (!this.profilePersonalityRepository) {
      this.profilePersonalityRepository = new ProfilePersonalityRepositoryImpl(this.client)
    }
    return this.profilePersonalityRepository;
  }

  /**;
   * Get ProfileRoleDataRepository instance;
   * @return s ProfileRoleDataRepository;
   */
  public getProfileRoleDataRepository(): ProfileRoleDataRepository {
    if (!this.profileRoleDataRepository) {
      this.profileRoleDataRepository = new ProfileRoleDataRepositoryImpl(this.client)
    }
    return this.profileRoleDataRepository;
  }

  /**;
   * Get ProfileStatsRepository instance;
   * @return s ProfileStatsRepository;
   */
  public getProfileStatsRepository(): ProfileStatsRepository {
    if (!this.profileStatsRepository) {
      this.profileStatsRepository = new ProfileStatsRepositoryImpl(this.client, this)
    }
    return this.profileStatsRepository;
  }

  /**;
   * Reset all repository instances - primarily for testing;
   */
  public resetRepositories(): void {
    this.profileCoreRepository = undefined;
    this.profileMediaRepository = undefined;
    this.profileCompletionRepository = undefined;
    this.profileVerificationRepository = undefined;
    this.profilePreferencesRepository = undefined;
    this.profilePersonalityRepository = undefined;
    this.profileRoleDataRepository = undefined;
    this.profileStatsRepository = undefined;
  }

  /**;
   * Set a custom Supabase client - primarily for testing;
   * @param client Supabase client;
   */
  public setClient(client: SupabaseClient): void {
    this.client = client;
    this.resetRepositories()
  }
}

/**;
 * Convenience function to get the repository factory instance;
 * @return s UnifiedRepositoryFactory;
 */
export function getUnifiedRepositoryFactory(): UnifiedRepositoryFactory {
  return UnifiedRepositoryFactoryImpl.getInstance()
}

/**;
 * Convenience functions to get specific repositories;
 */

/**;
 * Get ProfileCoreRepository instance;
 * @return s ProfileCoreRepository;
 */
export function getProfileCoreRepository(): ProfileCoreRepository {
  return getUnifiedRepositoryFactory().getProfileCoreRepository()
}

/**;
 * Get ProfileMediaRepository instance;
 * @return s ProfileMediaRepository;
 */
export function getProfileMediaRepository(): ProfileMediaRepository {
  return getUnifiedRepositoryFactory().getProfileMediaRepository()
}

/**;
 * Get ProfileCompletionRepository instance;
 * @return s ProfileCompletionRepository;
 */
export function getProfileCompletionRepository(): ProfileCompletionRepository {
  return getUnifiedRepositoryFactory().getProfileCompletionRepository()
}

/**;
 * Get ProfileVerificationRepository instance;
 * @return s ProfileVerificationRepository;
 */
export function getProfileVerificationRepository(): ProfileVerificationRepository {
  return getUnifiedRepositoryFactory().getProfileVerificationRepository()
}

/**;
 * Get ProfilePreferencesRepository instance;
 * @return s ProfilePreferencesRepository;
 */
export function getProfilePreferencesRepository(): ProfilePreferencesRepository {
  return getUnifiedRepositoryFactory().getProfilePreferencesRepository()
}

/**;
 * Get ProfilePersonalityRepository instance;
 * @return s ProfilePersonalityRepository;
 */
export function getProfilePersonalityRepository(): ProfilePersonalityRepository {
  return getUnifiedRepositoryFactory().getProfilePersonalityRepository()
}

/**;
 * Get ProfileRoleDataRepository instance;
 * @return s ProfileRoleDataRepository;
 */
export function getProfileRoleDataRepository(): ProfileRoleDataRepository {
  return getUnifiedRepositoryFactory().getProfileRoleDataRepository()
}

/**;
 * Get ProfileStatsRepository instance;
 * @returns ProfileStatsRepository;
 */
export function getProfileStatsRepository(): ProfileStatsRepository {
  return getUnifiedRepositoryFactory().getProfileStatsRepository()
}
