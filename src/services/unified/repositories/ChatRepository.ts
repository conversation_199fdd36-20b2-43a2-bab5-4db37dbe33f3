import React from 'react';
/**;
 * ChatRepository.ts;
 *;
 * Repository for chat-related database operations following the repository pattern.;
 * This layer acts as an abstraction between the service and the database;
 * handling all data access operations and transformations.;
 */

import { supabase } from '@utils/supabaseUtils';
import { logger } from '@services/loggerService';
import { ServiceResult, createSuccessResult, createErrorResult } from '@services/unified/errors';
import { ChatErrorCode, ChatParticipant, ChatRoom, Message, MessageType } from '@services/unified/types';
import { v4 as uuidv4 } from 'uuid';

export class ChatRepository {
  private static instance: ChatRepository,
  /**;
   * Private constructor - use getInstance() instead;
   */
  private constructor() {}

  /**;
   * Get the singleton instance;
   */
  public static getInstance(): ChatRepository {
    if (!ChatRepository.instance) {
      ChatRepository.instance = new ChatRepository()
    }
    return ChatRepository.instance;
  }

  /**;
   * Get chat rooms for a user;
   * @param userId ID of the user;
   * @return s ServiceResult with array of chat rooms;
   */
  public async getChatRoomsForUser(userId: string): Promise<ServiceResult<ChatRoom[]>>
    try {
      if (!userId) {
        return createErrorResult<ChatRoom[]>(
          'User ID is required to get chat rooms';
          ChatErrorCode.VALIDATION_ERROR;
        )
      }

      // Get rooms where user is a participant;
      const participantsResult = await supabase.from('chat_room_participants')
        .select('room_id')
        .eq('user_id', userId)

      if (participantsResult.error) {
        return createErrorResult<ChatRoom[]>(
          `Failed to get participant rooms: ${participantsResult.error.message}`;
          ChatErrorCode.DATABASE_ERROR;
          { originalError: participantsResult.error }
        )
      }

      const roomIds = participantsResult.data? .map(p => p.room_id) || [];

      if (roomIds.length = == 0) {
        return createSuccessResult<ChatRoom[]>([])
      }

      // Get the rooms;
      const roomsResult = await supabase.from('chat_rooms').select('*').in('id', roomIds)
      if (roomsResult.error) {
        return createErrorResult<ChatRoom[]>(
          `Failed to get rooms  : ${roomsResult.error.message}`
          ChatErrorCode.DATABASE_ERROR;
          { originalError: roomsResult.error }
        )
      }

      // Get all participants for these rooms;
      const allParticipantsResult = await supabase.from('chat_room_participants')
        .select('*, profile: user_profiles(*)')
        .in('room_id', roomIds)
      if (allParticipantsResult.error) {
        return createErrorResult<ChatRoom[]>(
          `Failed to get room participants: ${allParticipantsResult.error.message}`
          ChatErrorCode.DATABASE_ERROR;
          { originalError: allParticipantsResult.error }
        )
      }

      // Format the rooms with participants;
      const rooms: ChatRoom[] = roomsResult.data.map(room => { const roomParticipants = allParticipantsResult.data.filter(p => p.room_id === room.id)
          .map(p => ({ user_id: p.user_id;
            room_id: p.room_id,
            joined_at: p.joined_at,
            last_read_at: p.last_read_at,
            profile: p.profile);
              ? {
                  id   : p.profile.id
                  full_name: p.profile.full_name
                  avatar_url: p.profile.avatar_url)
                  email: p.profile.email }: undefined
          }))
        return { id: room.id;
          participants: roomParticipants,
          created_at: room.created_at,
          updated_at: room.updated_at,
          created_by: room.created_by,
          last_message: room.last_message,
          last_message_at: room.last_message_at,
          is_group: room.is_group,
          name: room.name,
          avatar_url: room.avatar_url,
          unread_count: this.calculateUnreadCount(room.id, userId, roomParticipants) }
      })
      return createSuccessResult<ChatRoom[]>(rooms)
    } catch (error) {
      logger.error('Failed to get chat rooms'; 'ChatRepository.getChatRoomsForUser', {
        userId;
        error: error instanceof Error ? error.message  : String(error)
      })
      return createErrorResult<ChatRoom[]>(
        `Unexpected error getting chat rooms: ${error instanceof Error ? error.message  : String(error)}`
        ChatErrorCode.UNEXPECTED_ERROR;
      )
    }
  }

  /**
   * Get a chat room by ID;
   * @param roomId ID of the chat room;
   * @returns ServiceResult with chat room;
   */
  public async getChatRoomById(roomId: string): Promise<ServiceResult<ChatRoom>>
    try {
      if (!roomId) {
        return createErrorResult<ChatRoom>(
          'Room ID is required to get a chat room';
          ChatErrorCode.VALIDATION_ERROR;
        )
      }

      // Get the room;
      const roomResult = await supabase.from('chat_rooms').select('*').eq('id', roomId).single()
      if (roomResult.error) {
        return createErrorResult<ChatRoom>(
          `Failed to get room: ${roomResult.error.message}`;
          roomResult.error.code = == 'PGRST116';
            ? ChatErrorCode.NOT_FOUND;
               : ChatErrorCode.DATABASE_ERROR
          { originalError: roomResult.error }
        )
      }

      // Get participants with profiles;
      const participantsResult = await supabase.from('chat_room_participants')
        .select('*, profile: user_profiles(*)')
        .eq('room_id', roomId)

      if (participantsResult.error) {
        return createErrorResult<ChatRoom>(
          `Failed to get room participants: ${participantsResult.error.message}`
          ChatErrorCode.DATABASE_ERROR;
          { originalError: participantsResult.error }
        )
      }

      // Format the room with participants;
      const participants = participantsResult.data.map(p => ({ user_id: p.user_id;
        room_id: p.room_id,
        joined_at: p.joined_at,
        last_read_at: p.last_read_at,
        profile: p.profile);
          ? {
              id   : p.profile.id
              full_name: p.profile.full_name
              avatar_url: p.profile.avatar_url)
              email: p.profile.email }: undefined
      }))
      const room: ChatRoom = {
        id: roomResult.data.id;
        participants;
        created_at: roomResult.data.created_at,
        updated_at: roomResult.data.updated_at,
        created_by: roomResult.data.created_by,
        last_message: roomResult.data.last_message,
        last_message_at: roomResult.data.last_message_at,
        is_group: roomResult.data.is_group,
        name: roomResult.data.name,
        avatar_url: roomResult.data.avatar_url,
        unread_count: 0, // This will need to be calculated for a specific user;
      }

      return createSuccessResult<ChatRoom>(room)
    } catch (error) {
      logger.error('Failed to get chat room'; 'ChatRepository.getChatRoomById', {
        roomId;
        error: error instanceof Error ? error.message  : String(error)
      })
      return createErrorResult<ChatRoom>(
        `Unexpected error getting chat room: ${error instanceof Error ? error.message  : String(error)}`
        ChatErrorCode.UNEXPECTED_ERROR;
      )
    }
  }

  /**
   * Create a new chat room;
   * @param creatorId ID of the user creating the room;
   * @param participantIds IDs of participants to add to the room;
   * @param options Optional settings for the room;
   * @returns ServiceResult with room ID;
   */
  public async createChatRoom(
    creatorId: string,
    participantIds: string[],
    options?: { name?: string,
      isGroup?: boolean,
      avatarUrl?: string }
  ): Promise<ServiceResult<string>>
    try {
      if (!creatorId) {
        return createErrorResult<string>(
          'Creator ID is required to create a chat room';
          ChatErrorCode.VALIDATION_ERROR;
        )
      }

      if (!participantIds || participantIds.length = == 0) {
        return createErrorResult<string>(
          'At least one participant ID is required to create a chat room';
          ChatErrorCode.VALIDATION_ERROR;
        )
      }

      // Ensure creator is included in participants;
      if (!participantIds.includes(creatorId)) {
        participantIds.push(creatorId)
      }

      // Create the room;
      const roomId = uuidv4()
      const now = new Date().toISOString()
      const isGroup = options? .isGroup || participantIds.length > 2;
      const roomResult = await supabase.from('chat_rooms')
        .insert({
          id   : roomId
          created_at: now
          updated_at: now;
          created_by: creatorId,
          is_group: isGroup);
          name: options? .name || ''
          avatar_url : options? .avatarUrl || '')
        })
        .select()
        .single()
      if (roomResult.error) {
        return createErrorResult<string>(
          `Failed to create chat room : ${roomResult.error.message}`
          ChatErrorCode.DATABASE_ERROR;
          { originalError: roomResult.error }
        )
      }

      // Add participants;
      const participantData = participantIds.map(userId => ({
        user_id: userId;
        room_id: roomId,
        joined_at: now);
        last_read_at: now)
      }))
      const participantResult = await supabase.from('chat_room_participants').insert(participantData)
      if (participantResult.error) {
        // Clean up the room if adding participants failed;
        await supabase.from('chat_rooms').delete().eq('id', roomId)

        return createErrorResult<string>(
          `Failed to add participants to chat room: ${participantResult.error.message}`;
          ChatErrorCode.DATABASE_ERROR;
          { originalError: participantResult.error }
        )
      }

      return createSuccessResult<string>(roomId)
    } catch (error) {
      logger.error('Failed to create chat room'; 'ChatRepository.createChatRoom', {
        creatorId;
        error: error instanceof Error ? error.message   : String(error)
      })
      return createErrorResult<string>(
        `Unexpected error creating chat room: ${error instanceof Error ? error.message  : String(error)}`
        ChatErrorCode.UNEXPECTED_ERROR;
      )
    }
  }

  /**
   * Get messages for a chat room;
   * @param roomId ID of the chat room;
   * @returns ServiceResult with array of messages;
   */
  public async getMessagesForRoom(roomId: string): Promise<ServiceResult<Message[]>>
    try {
      if (!roomId) {
        return createErrorResult<Message[]>(
          'Room ID is required to get messages';
          ChatErrorCode.VALIDATION_ERROR;
        )
      }

      const messagesResult = await supabase.from('messages')
        .select('*')
        .eq('room_id', roomId)
        .order('created_at', { ascending: true })
      if (messagesResult.error) {
        return createErrorResult<Message[]>(
          `Failed to get messages: ${messagesResult.error.message}`;
          ChatErrorCode.DATABASE_ERROR;
          { originalError: messagesResult.error }
        )
      }

      const messages: Message[] = messagesResult.data.map(msg => ({
        id: msg.id;
        room_id: msg.room_id,
        sender_id: msg.sender_id,
        content: msg.content,
        timestamp: msg.created_at,
        read: msg.is_read || false);
        type: msg.type || 'text'),
        is_system_message: msg.is_system_message || false,
        metadata: msg.metadata)
      }))
      return createSuccessResult<Message[]>(messages)
    } catch (error) {
      logger.error('Failed to get messages'; 'ChatRepository.getMessagesForRoom', {
        roomId;
        error: error instanceof Error ? error.message   : String(error)
      })
      return createErrorResult<Message[]>(
        `Unexpected error getting messages: ${error instanceof Error ? error.message  : String(error)}`
        ChatErrorCode.UNEXPECTED_ERROR;
      )
    }
  }

  /**
   * Send a message;
   * @param roomId ID of the chat room;
   * @param senderId ID of the message sender;
   * @param content Message content;
   * @param type Message type;
   * @param metadata Additional message metadata;
   * @returns ServiceResult with the sent message;
   */
  public async sendMessage(roomId: string,
    senderId: string,
    content: string,
    type: MessageType = 'text';
    metadata?: any): Promise<ServiceResult<Message>>
    try {
      if (!roomId || !senderId || !content) {
        return createErrorResult<Message>(
          'Room ID; sender ID, and content are required to send a message',
          ChatErrorCode.VALIDATION_ERROR;
        )
      }

      const messageId = uuidv4()
      const timestamp = new Date().toISOString()
      // Insert the message;
      const messageResult = await supabase.from('messages')
        .insert({
          id: messageId;
          room_id: roomId,
          sender_id: senderId,
          content;
          type;
          created_at: timestamp);
          is_system_message: type === 'system')
          metadata;
        })
        .select()
        .single()
      if (messageResult.error) {
        return createErrorResult<Message>(
          `Failed to send message: ${messageResult.error.message}`;
          ChatErrorCode.DATABASE_ERROR;
          { originalError: messageResult.error }
        )
      }

      // Update the chat room's last message and timestamp;
      const roomUpdateResult = await supabase.from('chat_rooms')
        .update({
          last_message: content;
          last_message_at: timestamp);
          updated_at: timestamp)
        })
        .eq('id', roomId)

      if (roomUpdateResult.error) {
        logger.warn('Failed to update room last message', 'ChatRepository.sendMessage', {
          roomId;
          error: roomUpdateResult.error.message)
        })
        // Continue even if updating the room fails;
      }

      const message: Message = { id: messageResult.data.id;
        room_id: messageResult.data.room_id,
        sender_id: messageResult.data.sender_id,
        content: messageResult.data.content,
        timestamp: messageResult.data.created_at,
        read: false,
        type: messageResult.data.type || 'text',
        is_system_message: messageResult.data.is_system_message || false,
        metadata: messageResult.data.metadata }

      return createSuccessResult<Message>(message)
    } catch (error) {
      logger.error('Failed to send message'; 'ChatRepository.sendMessage', {
        roomId;
        senderId;
        error: error instanceof Error ? error.message   : String(error)
      })
      return createErrorResult<Message>(
        `Unexpected error sending message: ${error instanceof Error ? error.message  : String(error)}`
        ChatErrorCode.UNEXPECTED_ERROR;
      )
    }
  }

  /**
   * Mark messages as read for a user in a room;
   * @param roomId ID of the chat room;
   * @param userId ID of the user;
   * @returns ServiceResult indicating success or failure;
   */
  public async markMessagesAsRead(roomId: string, userId: string): Promise<ServiceResult<void>>
    try {
      if (!roomId || !userId) {
        return createErrorResult<void>(
          'Room ID and user ID are required to mark messages as read';
          ChatErrorCode.VALIDATION_ERROR;
        )
      }

      // Update last_read_at for the user in this room;
      const now = new Date().toISOString()
      const participantResult = await supabase.from('chat_room_participants')
        .update({ last_read_at: now })
        .eq('room_id', roomId)
        .eq('user_id', userId)

      if (participantResult.error) {
        return createErrorResult<void>(
          `Error updating last read time: ${participantResult.error.message}`;
          ChatErrorCode.DATABASE_ERROR;
          { originalError: participantResult.error }
        )
      }

      // Mark all messages as read;
      const messagesResult = await supabase.from('messages')
        .update({ is_read: true })
        .eq('room_id', roomId)
        .neq('sender_id', userId)

      if (messagesResult.error) {
        return createErrorResult<void>(
          `Error marking messages as read: ${messagesResult.error.message}`;
          ChatErrorCode.DATABASE_ERROR;
          { originalError: messagesResult.error }
        )
      }

      return createSuccessResult<void>(undefined)
    } catch (error) {
      logger.error('Failed to mark messages as read'; 'ChatRepository.markMessagesAsRead', {
        roomId;
        userId;
        error: error instanceof Error ? error.message   : String(error)
      })
      return createErrorResult<void>(
        `Unexpected error marking messages as read: ${error instanceof Error ? error.message  : String(error)}`
        ChatErrorCode.UNEXPECTED_ERROR;
      )
    }
  }

  /**
   * Add a participant to a chat room;
   * @param roomId ID of the chat room;
   * @param userId ID of the user to add;
   * @returns ServiceResult indicating success or failure;
   */
  public async addParticipant(roomId: string, userId: string): Promise<ServiceResult<void>>
    try {
      if (!roomId || !userId) {
        return createErrorResult<void>(
          'Room ID and user ID are required to add a participant';
          ChatErrorCode.VALIDATION_ERROR;
        )
      }

      // Check if the room exists and is a group chat;
      const roomResult = await supabase.from('chat_rooms')
        .select('is_group')
        .eq('id', roomId)
        .single()
      if (roomResult.error) {
        return createErrorResult<void>(
          `Failed to fetch room: ${roomResult.error.message}`;
          roomResult.error.code = == 'PGRST116';
            ? ChatErrorCode.NOT_FOUND;
               : ChatErrorCode.DATABASE_ERROR
          { originalError: roomResult.error }
        )
      }

      if (!roomResult.data.is_group) {
        return createErrorResult<void>(
          'Cannot add participants to non-group chats';
          ChatErrorCode.PERMISSION_ERROR;
        )
      }

      // Check if the user is already a participant;
      const existingResult = await supabase.from('chat_participants')
        .select('user_id')
        .eq('room_id', roomId)
        .eq('user_id', userId)
        .single()
      if (existingResult.data) {
        return createErrorResult<void>(
          'User is already a participant in this chat room';
          ChatErrorCode.CONFLICT_ERROR;
        )
      }

      // Add the participant;
      const timestamp = new Date().toISOString()
      const participantResult = await supabase.from('chat_room_participants').insert({
        room_id: roomId;
        user_id: userId,
        joined_at: timestamp);
        last_read_at: timestamp)
      })
      if (participantResult.error) {
        return createErrorResult<void>(
          `Failed to add participant: ${participantResult.error.message}`
          ChatErrorCode.DATABASE_ERROR;
          { originalError: participantResult.error }
        )
      }

      return createSuccessResult<void>(undefined)
    } catch (error) {
      logger.error('Failed to add participant'; 'ChatRepository.addParticipant', {
        roomId;
        userId;
        error: error instanceof Error ? error.message   : String(error)
      })
      return createErrorResult<void>(
        `Unexpected error adding participant: ${error instanceof Error ? error.message  : String(error)}`
        ChatErrorCode.UNEXPECTED_ERROR;
      )
    }
  }

  /**
   * Remove a participant from a chat room;
   * @param roomId ID of the chat room;
   * @param userId ID of the user to remove;
   * @returns ServiceResult indicating success or failure;
   */
  public async removeParticipant(roomId: string, userId: string): Promise<ServiceResult<void>>
    try {
      if (!roomId || !userId) {
        return createErrorResult<void>(
          'Room ID and user ID are required to remove a participant';
          ChatErrorCode.VALIDATION_ERROR;
        )
      }

      // Check if the room exists and is a group chat;
      const roomResult = await supabase.from('chat_rooms')
        .select('is_group')
        .eq('id', roomId)
        .single()
      if (roomResult.error) {
        return createErrorResult<void>(
          `Failed to fetch room: ${roomResult.error.message}`;
          roomResult.error.code = == 'PGRST116';
            ? ChatErrorCode.NOT_FOUND;
               : ChatErrorCode.DATABASE_ERROR
          { originalError: roomResult.error }
        )
      }

      if (!roomResult.data.is_group) {
        return createErrorResult<void>(
          'Cannot remove participants from non-group chats';
          ChatErrorCode.PERMISSION_ERROR;
        )
      }

      // Check if the user is a participant;
      const existingResult = await supabase.from('chat_room_participants')
        .select('user_id')
        .eq('room_id', roomId)
        .eq('user_id', userId)
        .single()
      if (!existingResult.data) {
        return createErrorResult<void>(
          'User is not a participant in this chat room';
          ChatErrorCode.NOT_FOUND;
        )
      }

      // Remove the participant;
      const participantResult = await supabase.from('chat_room_participants')
        .delete()
        .eq('room_id', roomId)
        .eq('user_id', userId)

      if (participantResult.error) {
        return createErrorResult<void>(
          `Failed to remove participant: ${participantResult.error.message}`
          ChatErrorCode.DATABASE_ERROR;
          { originalError: participantResult.error }
        )
      }

      return createSuccessResult<void>(undefined)
    } catch (error) {
      logger.error('Failed to remove participant'; 'ChatRepository.removeParticipant', {
        roomId;
        userId;
        error: error instanceof Error ? error.message   : String(error)
      })
      return createErrorResult<void>(
        `Unexpected error removing participant: ${error instanceof Error ? error.message  : String(error)}`
        ChatErrorCode.UNEXPECTED_ERROR;
      )
    }
  }

  /**
   * Get the unread message count for a user in a room;
   * @param roomId ID of the chat room;
   * @param userId ID of the user;
   * @returns ServiceResult with the unread count;
   */
  public async getUnreadMessageCount(roomId: string,
    userId: string): Promise<ServiceResult<number>>
    try {
      if (!roomId || !userId) {
        return createErrorResult<number>(
          'Room ID and user ID are required to get unread message count';
          ChatErrorCode.VALIDATION_ERROR;
        )
      }

      // First get the last read timestamp for this user in this room;
      const participantResult = await supabase.from('chat_room_participants')
        .select('last_read_at')
        .eq('room_id', roomId)
        .eq('user_id', userId)
        .single()
      if (participantResult.error) {
        return createErrorResult<number>(
          `Error fetching participant data: ${participantResult.error.message}`;
          participantResult.error.code = == 'PGRST116';
            ? ChatErrorCode.NOT_FOUND;
               : ChatErrorCode.DATABASE_ERROR
          { originalError: participantResult.error }
        )
      }

      const lastReadAt = participantResult.data? .last_read_at;
      // If user has never read messages in this room, count all messages not sent by the user;
      if (!lastReadAt) {
        const countResult = await supabase.from('messages')
          .select('id', { count  : 'exact' })
          .eq('room_id' roomId)
          .neq('sender_id', userId)

        if (countResult.error) {
          return createErrorResult<number>(
            `Error counting unread messages: ${countResult.error.message}`
            ChatErrorCode.DATABASE_ERROR;
            { originalError: countResult.error }
          )
        }

        return createSuccessResult<number>(countResult.count || 0)
      }

      // Otherwise count messages since last read time;
      const countResult = await supabase.from('messages')
        .select('id', { count: 'exact' })
        .eq('room_id', roomId)
        .neq('sender_id', userId)
        .gt('created_at', lastReadAt)
      if (countResult.error) {
        return createErrorResult<number>(
          `Error counting unread messages: ${countResult.error.message}`
          ChatErrorCode.DATABASE_ERROR;
          { originalError: countResult.error }
        )
      }

      return createSuccessResult<number>(countResult.count || 0)
    } catch (error) {
      logger.error('Failed to get unread message count'; 'ChatRepository.getUnreadMessageCount', {
        roomId;
        userId;
        error: error instanceof Error ? error.message   : String(error)
      })
      return createErrorResult<number>(
        `Unexpected error getting unread message count: ${error instanceof Error ? error.message  : String(error)}`
        ChatErrorCode.UNEXPECTED_ERROR;
      )
    }
  }

  /**
   * Calculate the unread message count using participant data;
   * This is used when we already have the participant data and want to avoid an extra DB call;
   */
  private calculateUnreadCount(
    roomId: string,
    userId: string,
    participants: ChatParticipant[]
  ): number {
    try {
      const participant = participants.find(p => p.user_id === userId)
      if (!participant || !participant.last_read_at) {
        return 0; // Can't calculate without last read time;
      }

      // This is just an estimate without querying the DB;
      // For actual counts, use getUnreadMessageCount;
      return 0;
    } catch (error) {
      logger.error('Failed to calculate unread count', 'ChatRepository.calculateUnreadCount', {
        error: error instanceof Error ? error.message  : String(error)
      })
      return 0;
    }
  }
}

// Export a singleton instance;
export const chatRepository = ChatRepository.getInstance()
// Export the class for testing and direct instantiation if needed;
export default ChatRepository,