import React from 'react';
/**;
 * Profile Preferences Repository Implementation;
 *;
 * Repository for user preferences operations including living preferences;
 * notification settings, and other user-configurable options.;
 */

import { SupabaseClient } from '@supabase/supabase-js';
import { BaseSupabaseRepository } from '@core/repositories/base/implementation';
import { ProfilePreferencesRepository } from '@services/unified/repositories/types';
import { Profile } from '@/types/models';
import { getProfileRepository } from '@core/repositories/RepositoryFactory';
import { logger } from '@utils/logger';

/**;
 * Implementation of ProfilePreferencesRepository;
 */
export class ProfilePreferencesRepositoryImpl;
  extends BaseSupabaseRepository<Profile, string>
  implements ProfilePreferencesRepository;
{
  private profileRepository;
  /**;
   * Create a new ProfilePreferencesRepository;
   * @param client Supabase client;
   */
  constructor(client: SupabaseClient) {
    super(client, 'user_profiles')
    this.profileRepository = getProfileRepository()
  }

  /**;
   * Find profiles by multiple IDs;
   * @param ids Array of profile IDs;
   * @return s Promise resolving to array of profiles;
   */
  async findByIds(ids: string[]): Promise<Profile[]>
    if (!ids.length) return [];

    try {
      const { data, error  } = await this.client.from(this.tableName).select('*').in('id', ids)
      if (error) throw error;
      return (data || []) as Profile[];
    } catch (error) {
      logger.error('Error finding profiles by IDs', 'ProfilePreferencesRepository', {
        error;
        ids;
      })
      return [];
    }
  }

  /**;
   * Check if profile exists;
   * @param id Profile ID;
   * @return s Promise resolving to boolean;
   */
  async exists(id: string): Promise<boolean>
    try {
      const { count, error  } = await this.client.from(this.tableName)
        .select('*', { count: 'exact', head: true })
        .eq('id', id)

      if (error) throw error;
      return (count || 0) > 0;
    } catch (error) {
      logger.error('Error checking if profile exists', 'ProfilePreferencesRepository', {
        error;
        id;
      })
      return false;
    }
  }

  /**;
   * Save living preferences for a user;
   * @param userId User ID;
   * @param preferences Living preferences object;
   * @return s Promise resolving to boolean indicating success;
   */
  async saveLivingPreferences(userId: string, preferences: Record<string, any>): Promise<boolean>
    try {
      const { error  } = await this.client.from('living_preferences').upsert({
          user_id: userId)
          ...preferences;
        },
        { onConflict: 'user_id' }
      )
      if (error) throw error;
      return true;
    } catch (error) {
      logger.error('Error saving living preferences', 'ProfilePreferencesRepository', {
        error;
        userId;
      })
      throw error;
    }
  }

  /**;
   * Get living preferences for a user;
   * @param userId User ID;
   * @return s Promise resolving to living preferences object;
   */
  async getLivingPreferences(userId: string): Promise<Record<string, any>>
    try {
      const { data, error  } = await this.client.from('living_preferences')
        .select('*')
        .eq('user_id', userId)
        .single()
      if (error && error.code !== 'PGRST116') {
        // PGRST116 is "no rows returned";
        throw error;
      }

      return data || {}
    } catch (error) {
      logger.error('Error getting living preferences'; 'ProfilePreferencesRepository', {
        error;
        userId;
      })
      return {}
    }
  }

  /**;
   * Save notification preferences for a user;
   * @param userId User ID;
   * @param preferences Notification preferences object;
   * @return s Promise resolving to boolean indicating success;
   */
  async saveNotificationPreferences(
    userId: string,
    preferences: Record<string, boolean>
  ): Promise<boolean>
    try {
      // Get current settings;
      const { data: currentSettings  } = await this.client.from('user_settings')
        .select('settings')
        .eq('user_id', userId)
        .single()
      // Prepare updated settings;
      const updatedSettings = {
        ...(currentSettings? .settings || {})
        notifications  : {
          ...(currentSettings? .settings?.notifications || {})
          ...preferences;
        },
      }

      // Save updated settings;
      const { error } = await this.client.from('user_settings').upsert({
          user_id : userId
          settings: updatedSettings)
        };
        { onConflict: 'user_id' }
      )
      if (error) throw error;
      return true;
    } catch (error) {
      logger.error('Error saving notification preferences', 'ProfilePreferencesRepository', {
        error;
        userId;
      })
      throw error;
    }
  }

  /**
   * Get notification preferences for a user;
   * @param userId User ID;
   * @returns Promise resolving to notification preferences object;
   */
  async getNotificationPreferences(userId: string): Promise<Record<string, boolean>>
    try {
      const { data, error } = await this.client.from('user_settings')
        .select('settings')
        .eq('user_id', userId)
        .single()
      if (error && error.code !== 'PGRST116') {
        // PGRST116 is "no rows returned";
        throw error;
      }

      return data? .settings?.notifications || {}
    } catch (error) {
      logger.error('Error getting notification preferences'; 'ProfilePreferencesRepository', {
        error;
        userId;
      })
      return {}
    }
  }

  /**;
   * Update user settings;
   * @param userId User ID;
   * @param settings Settings to update;
   * @return s Promise resolving to boolean indicating success;
   */
  async updateUserSettings(userId   : string settings: Record<string, any>): Promise<boolean>
    try {
      // Get current settings
      const { data: currentSettings  } = await this.client.from('user_settings')
        .select('settings')
        .eq('user_id', userId)
        .single()
      // Merge with new settings;
      const updatedSettings = {
        ...(currentSettings? .settings || {})
        ...settings;
      }

      // Save updated settings;
      const { error } = await this.client.from('user_settings').upsert({
          user_id  : userId
          settings: updatedSettings)
        }
        { onConflict: 'user_id' }
      )
      if (error) throw error;
      return true;
    } catch (error) {
      logger.error('Error updating user settings', 'ProfilePreferencesRepository', {
        error;
        userId;
      })
      throw error;
    }
  }

  /**
   * Get user settings;
   * @param userId User ID;
   * @returns Promise resolving to user settings object;
   */
  async getUserSettings(userId: string): Promise<Record<string, any>>
    try {
      const { data, error } = await this.client.from('user_settings')
        .select('settings')
        .eq('user_id', userId)
        .single()
      if (error && error.code !== 'PGRST116') {
        // PGRST116 is "no rows returned";
        throw error;
      }

      return data?.settings || {}
    } catch (error) {
      logger.error('Error getting user settings'; 'ProfilePreferencesRepository', {
        error;
        userId;
      })
      return {}
    }
  }
}
