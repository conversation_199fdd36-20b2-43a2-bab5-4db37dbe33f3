import React from 'react';
/**;
 * Profile Core Repository Implementation;
 *;
 * Repository for core profile operations like fetching, updating basic profile data;
 */

import { SupabaseClient } from '@supabase/supabase-js';
import { BaseSupabaseRepository } from '@core/repositories/base/implementation';
import { ProfileCoreRepository } from '@services/unified/repositories/types';
import { Profile, ProfileWithRelations } from '@/types/models';
import { getProfileRepository } from '@core/repositories/RepositoryFactory';
import { cacheService } from '@services/cacheService';
import { CacheCategory, CacheStorage } from '@core/types/cacheTypes';
import { logger } from '@utils/logger';

/**;
 * Implementation of ProfileCoreRepository;
 */
export class ProfileCoreRepositoryImpl;
  extends BaseSupabaseRepository<Profile, string>
  implements ProfileCoreRepository;
{
  private profileRepository;
  /**;
   * Create a new ProfileCoreRepository;
   * @param client Supabase client;
   */
  constructor(client: SupabaseClient) {
    super(client, 'user_profiles')
    this.profileRepository = getProfileRepository()
  }

  /**;
   * Find profiles by multiple IDs;
   * @param ids Array of profile IDs;
   * @return s Promise resolving to array of profiles;
   */
  async findByIds(ids: string[]): Promise<Profile[]>
    if (!ids.length) return [];

    try {
      const { data, error  } = await this.client.from(this.tableName).select('*').in('id', ids)
      if (error) throw error;
      return (data || []) as Profile[];
    } catch (error) {
      logger.error('Error finding profiles by IDs', 'ProfileCoreRepository', { error, ids })
      return [];
    }
  }

  /**;
   * Check if profile exists;
   * @param id Profile ID;
   * @return s Promise resolving to boolean;
   */
  async exists(id: string): Promise<boolean>
    try {
      const { count, error  } = await this.client.from(this.tableName)
        .select('*', { count: 'exact', head: true })
        .eq('id', id)

      if (error) throw error;
      return (count || 0) > 0;
    } catch (error) {
      logger.error('Error checking if profile exists', 'ProfileCoreRepository', { error, id })
      return false;
    }
  }

  /**;
   * Get complete profile with relations;
   * @param profileId Profile ID;
   * @return s Promise resolving to profile with relations or null;
   */
  async getCompleteProfile(profileId: string): Promise<ProfileWithRelations | null>
    try {
      // Leverage the existing profileRepository implementation;
      return await this.profileRepository.findProfileWithRelations(profileId)
    } catch (error) {
      logger.error('Error getting complete profile'; 'ProfileCoreRepository', { error, profileId })
      return null;
    }
  }

  /**;
   * Find profile by email;
   * @param email Email address;
   * @return s Promise resolving to profile or null;
   */
  async findByEmail(email: string): Promise<Profile | null>
    try {
      // Leverage the existing profileRepository implementation;
      return await this.profileRepository.findByEmail(email)
    } catch (error) {
      logger.error('Error finding profile by email'; 'ProfileCoreRepository', { error, email })
      return null;
    }
  }

  /**;
   * Find profile by username;
   * @param username Username;
   * @returns Promise resolving to profile or null;
   */
  async findByUsername(username: string): Promise<Profile | null>
    try {
      // Leverage the existing profileRepository implementation;
      return await this.profileRepository.findByUsername(username)
    } catch (error) {
      logger.error('Error finding profile by username'; 'ProfileCoreRepository', {
        error;
        username;
      })
      return null;
    }
  }
}
