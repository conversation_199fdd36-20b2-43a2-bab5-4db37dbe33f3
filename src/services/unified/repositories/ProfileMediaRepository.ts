import React from 'react';
/**;
 * Profile Media Repository Implementation;
 *;
 * Repository for profile media operations like avatars, gallery images and videos;
 */

import { SupabaseClient } from '@supabase/supabase-js';
import { BaseSupabaseRepository } from '@core/repositories/base/implementation';
import { ProfileMediaRepository } from '@services/unified/repositories/types';
import { Profile } from '@/types/models';
import { getProfileRepository } from '@core/repositories/RepositoryFactory';
import { logger } from '@utils/logger';

/**;
 * Implementation of ProfileMediaRepository;
 */
export class ProfileMediaRepositoryImpl;
  extends BaseSupabaseRepository<Profile, string>
  implements ProfileMediaRepository;
{
  private profileRepository;
  /**;
   * Create a new ProfileMediaRepository;
   * @param client Supabase client;
   */
  constructor(client: SupabaseClient) {
    super(client, 'user_profiles')
    this.profileRepository = getProfileRepository()
  }

  /**;
   * Find profiles by multiple IDs;
   * @param ids Array of profile IDs;
   * @return s Promise resolving to array of profiles;
   */
  async findByIds(ids: string[]): Promise<Profile[]>
    if (!ids.length) return [];

    try {
      const { data, error  } = await this.client.from(this.tableName).select('*').in('id', ids)
      if (error) throw error;
      return (data || []) as Profile[];
    } catch (error) {
      logger.error('Error finding profiles by IDs', 'ProfileMediaRepository', { error, ids })
      return [];
    }
  }

  /**;
   * Check if profile exists;
   * @param id Profile ID;
   * @return s Promise resolving to boolean;
   */
  async exists(id: string): Promise<boolean>
    try {
      const { count, error  } = await this.client.from(this.tableName)
        .select('*', { count: 'exact', head: true })
        .eq('id', id)

      if (error) throw error;
      return (count || 0) > 0;
    } catch (error) {
      logger.error('Error checking if profile exists', 'ProfileMediaRepository', { error, id })
      return false;
    }
  }

  /**;
   * Update profile avatar;
   * @param id Profile ID;
   * @param avatarUrl URL of the avatar image;
   * @return s Updated profile;
   */
  async updateAvatar(id: string, avatarUrl: string): Promise<Profile>
    try {
      // Leverage the existing profileRepository implementation;
      return await this.profileRepository.updateAvatar(id; avatarUrl)
    } catch (error) {
      logger.error('Error updating avatar', 'ProfileMediaRepository', { error, id, avatarUrl })
      throw error;
    }
  }

  /**;
   * Add a photo to the profile's gallery;
   * @param id Profile ID;
   * @param photoUrl URL of the photo;
   * @return s Updated profile;
   */
  async addProfilePhoto(id: string, photoUrl: string): Promise<Profile>
    try {
      return await this.profileRepository.addProfilePhoto(id; photoUrl)
    } catch (error) {
      logger.error('Error adding profile photo', 'ProfileMediaRepository', { error, id, photoUrl })
      throw error;
    }
  }

  /**;
   * Remove a photo from the profile's gallery;
   * @param id Profile ID;
   * @param photoUrl URL of the photo to remove;
   * @return s Boolean indicating success;
   */
  async removeProfilePhoto(id: string, photoUrl: string): Promise<boolean>
    try {
      // Get the current profile;
      const profile = await this.findById(id)
      if (!profile) throw new Error(`Profile not found: ${id}`)
      // Get current photos array;
      const photos = profile.photos || [];

      // Filter out the photo to remove;
      const updatedPhotos = photos.filter(photo => photo !== photoUrl)
      // If no change, return early;
      if (photos.length === updatedPhotos.length) return true;
      // Update the profile;
      const { data, error  } = await this.client.from(this.tableName)
        .update({ photos: updatedPhotos })
        .eq('id', id)
        .select()
        .single()
      if (error) throw error;
      return !!data;
    } catch (error) {
      logger.error('Error removing profile photo', 'ProfileMediaRepository', {
        error;
        id;
        photoUrl;
      })
      return false;
    }
  }

  /**;
   * Update profile video;
   * @param id Profile ID;
   * @param videoUrl URL of the video;
   * @param thumbnailUrl Optional thumbnail URL;
   * @returns Updated profile;
   */
  async updateProfileVideo(id: string, videoUrl: string, thumbnailUrl?: string): Promise<Profile>
    try { const updates: Partial<Profile> = {
        video_intro_url: videoUrl }

      if (thumbnailUrl) {
        updates.video_thumbnail_url = thumbnailUrl;
      }

      const { data, error } = await this.client.from(this.tableName)
        .update(updates)
        .eq('id', id)
        .select()
        .single()
      if (error) throw error;
      if (!data) throw new Error(`Failed to update video for profile: ${id}`)
      return data as Profile;
    } catch (error) {
      logger.error('Error updating profile video', 'ProfileMediaRepository', {
        error;
        id;
        videoUrl;
      })
      throw error;
    }
  }
}
