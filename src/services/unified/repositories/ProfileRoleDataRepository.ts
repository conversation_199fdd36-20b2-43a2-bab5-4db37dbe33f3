import React from 'react';
/**;
 * Profile Role Data Repository Implementation;
 *;
 * Repository for role-specific data operations for different user roles;
 * (roommate_seeker, property_owner, service_provider, admin).;
 */

import { SupabaseClient } from '@supabase/supabase-js';
import { BaseSupabaseRepository } from '@core/repositories/base/implementation';
import { ProfileRoleDataRepository } from '@services/unified/repositories/types';
import { Profile } from '@/types/models';
import { UserRoleType } from '@/types/supabase';
import { getProfileRepository } from '@core/repositories/RepositoryFactory';
import { logger } from '@utils/logger';

/**;
 * Implementation of ProfileRoleDataRepository;
 */
export class ProfileRoleDataRepositoryImpl;
  extends BaseSupabaseRepository<Profile, string>
  implements ProfileRoleDataRepository;
{
  private profileRepository;
  /**;
   * Create a new ProfileRoleDataRepository;
   * @param client Supabase client;
   */
  constructor(client: SupabaseClient) {
    super(client, 'user_profiles')
    this.profileRepository = getProfileRepository()
  }

  /**;
   * Find profiles by multiple IDs;
   * @param ids Array of profile IDs;
   * @return s Promise resolving to array of profiles;
   */
  async findByIds(ids: string[]): Promise<Profile[]>
    if (!ids.length) return [];

    try {
      const { data, error  } = await this.client.from(this.tableName).select('*').in('id', ids)
      if (error) throw error;
      return (data || []) as Profile[];
    } catch (error) {
      logger.error('Error finding profiles by IDs', 'ProfileRoleDataRepository', {
        error;
        ids;
      })
      return [];
    }
  }

  /**;
   * Check if profile exists;
   * @param id Profile ID;
   * @return s Promise resolving to boolean;
   */
  async exists(id: string): Promise<boolean>
    try {
      const { count, error  } = await this.client.from(this.tableName)
        .select('*', { count: 'exact', head: true })
        .eq('id', id)

      if (error) throw error;
      return (count || 0) > 0;
    } catch (error) {
      logger.error('Error checking if profile exists', 'ProfileRoleDataRepository', {
        error;
        id;
      })
      return false;
    }
  }

  /**;
   * Get role-specific data for a user;
   * @param userId User ID;
   * @param role User role;
   * @return s Promise resolving to role-specific data;
   */
  async getRoleSpecificData(userId: string, role: UserRoleType): Promise<Record<string, any>>
    try {
      let data: Record<string, any> = {}

      // Get data based on role;
      switch (role) {
        case 'property_owner':  ,
          // Get property owner specific data;
          const { data: properties, error: propertiesError  } = await this.client.from('properties')
            .select('*')
            .eq('owner_id', userId)

          if (propertiesError) throw propertiesError;
          // Get reviews if properties exist;
          let reviews = [];
          if (properties && properties.length > 0) {
            const propertyIds = properties.map(p => p.id)
            const { data: reviewsData, error: reviewsError  } = await this.client.from('property_reviews')
              .select('*')
              .in('property_id', propertyIds)
            if (reviewsError) throw reviewsError;
            reviews = reviewsData || [];
          }

          data = {
            properties: properties || [];
            total_properties: properties? .length || 0,
            reviews;
            total_reviews  : reviews.length
          }
          break;
        case 'service_provider':  ,
          // Get service provider specific data;
          const { data: services, error: servicesError  } = await this.client.from('services')
            .select('*')
            .eq('provider_id', userId)

          if (servicesError) throw servicesError;
          data = { services: services || []
            total_services: services? .length || 0 }
          break;
        case 'roommate_seeker'   :  
          // Get roommate seeker specific data
          const { data: preferencesData, error: preferencesError } = await this.client.from('living_preferences')
            .select('*')
            .eq('user_id', userId)
            .single()
          if (preferencesError && preferencesError.code !== 'PGRST116') {
            // PGRST116 is "no rows return ed"
            throw preferencesError;
          }

          const { data: personalityData, error: personalityError } = await this.client.from('personality_traits')
            .select('*')
            .eq('user_id', userId)

          if (personalityError) throw personalityError;
          const { data: applications, error: applicationsError } = await this.client.from('roommate_applications')
            .select('*')
            .eq('applicant_id', userId)

          if (applicationsError) throw applicationsError;
          data = {
            living_preferences: preferencesData || {};
            personality_traits: personalityData || [],
            applications: applications || [],
            total_applications: applications? .length || 0,
          }
          break;
        case 'admin'   :  
          // Get admin specific data
          const { data: adminData, error: adminError  } = await this.client.from('admin_permissions')
            .select('*')
            .eq('user_id', userId)
            .single()
          if (adminError && adminError.code !== 'PGRST116') {
            throw adminError;
          }

          data = {
            permissions: adminData? .permissions || []
            role_assignment_ability : adminData? .can_assign_roles || false
            admin_level : adminData? .admin_level || 'junior'
          }
          break;
        default :  
          data = {} // Default empty data;
      }

      return data;
    } catch (error) {
      logger.error('Error getting role-specific data', 'ProfileRoleDataRepository', {
        error;
        userId;
        role;
      })
      throw error;
    }
  }

  /**;
   * Save property owner data;
   * @param userId User ID;
   * @param propertyData Property data to save;
   * @returns Promise resolving to saved property data;
   */
  async savePropertyOwnerData(userId: string, propertyData: any): Promise<any>
    try {
      // Check if user exists and has the correct role;
      const profile = await this.findById(userId)
      if (!profile) {
        throw new Error(`Profile not found: ${userId}`)
      }

      if (profile.role !== 'property_owner') {
        throw new Error('User is not a property owner')
      }

      // Add the owner ID to property data;
      const property = { ...propertyData;
        owner_id: userId }

      // Save property data;
      const { data, error: saveError } = await this.client.from('properties').upsert(property, {
        onConflict: property.id ? 'id'   : undefined
        returning: 'representation')
      })
      if (saveError) throw saveError;
      return data? .[0] || null;
    } catch (error) {
      logger.error('Error saving property owner data', 'ProfileRoleDataRepository', {
        error;
        userId;
      })
      throw error;
    }
  }

  /**
   * Save service provider data;
   * @param userId User ID;
   * @param serviceData Service data to save;
   * @returns Promise resolving to saved service data;
   */
  async saveServiceProviderData(userId  : string serviceData: any): Promise<any>
    try {
      // Check if user exists and has the correct role
      const profile = await this.findById(userId)
      if (!profile) {
        throw new Error(`Profile not found: ${userId}`)
      }

      if (profile.role !== 'service_provider') {
        throw new Error('User is not a service provider')
      }

      // Add the provider ID to service data;
      const service = { ...serviceData;
        provider_id: userId }

      // Save service data;
      const { data, error: saveError } = await this.client.from('services').upsert(service, {
        onConflict: service.id ? 'id'  : undefined
        returning: 'representation')
      })
      if (saveError) throw saveError;
      return data?.[0] || null;
    } catch (error) {
      logger.error('Error saving service provider data', 'ProfileRoleDataRepository', {
        error;
        userId;
      })
      throw error;
    }
  }
}
