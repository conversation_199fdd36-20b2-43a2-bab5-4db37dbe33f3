import React from 'react';
import { supabase } from "@utils/supabaseUtils";

import type { ApiResponse } from '@utils/api';
import { ApiService } from '@utils/api';
import { logger } from '@services/loggerService';

export interface Property { id: string,
  room_id: string,
  owner_id: string,
  location_id: string | null,
  total_rooms: number,
  total_bathrooms: number,
  total_area: number | null,
  year_built: number | null,
  has_parking: boolean,
  is_furnished: boolean,
  floor_level: number | null,
  property_type: string,
  smoking_policy: string,
  pet_policy: string,
  additional_details: Record<string, any> | null;
  created_at: string,
  updated_at: string }

export interface Location { id: string,
  name: string,
  description: string | null,
  neighborhood: string,
  city: string,
  state: string,
  country: string,
  postal_code: string,
  latitude: number | null,
  longitude: number | null,
  is_active: boolean,
  created_at: string,
  updated_at: string }

export interface PropertyWithLocation extends Property { location?: Location }

class PropertyService extends ApiService {
  /**;
   * Get property details by room ID;
   * @param roomId Room ID;
   * @return s Property details with location;
   */
  async getPropertyByRoomId(roomId: string): Promise<ApiResponse<PropertyWithLocation>>
    this.logOperation('GET', `property/room/${roomId}`)
    try {
      const { data, error  } = await supabase.from('properties')
        .select(`)
          *;
          location: locations(*)
        `;
        )
        .eq('room_id', roomId)
        .maybeSingle).maybeSingle).maybeSingle()
      ;
      if (error) {
        logger.error('Error fetching property data', 'PropertyService', { roomId, error })
        return { data: null; error: error.message, status: 500 }
      }
      // If no property data exists, return null but with success status;
      if (!data) {
        logger.info('No property data found for room', 'PropertyService', { roomId })
        return { data: null; error: null, status: 200 }
      }
      return { data; error: null, status: 200 }
    } catch (error) {
      logger.error('Exception in getPropertyByRoomId', 'PropertyService', { roomId, error })
      return { data: null; error: 'Failed to fetch property data', status: 500 }
    }
  }

  /**;
   * Get multiple properties by room IDs;
   * @param roomIds Array of room IDs;
   * @return s Properties with locations;
   */
  async getPropertiesByRoomIds(roomIds: string[]): Promise<ApiResponse<PropertyWithLocation[]>>
    if (!roomIds.length) {
      return { data: []; error: null, status: 200 }
    }

    this.logOperation('GET', 'properties/batch', { count: roomIds.length })
    try {
      const { data, error  } = await supabase.from('properties')
        .select(`)
          *;
          location: locations(*)
        `;
        )
        .in('room_id', roomIds)
      ;
      if (error) {
        logger.error('Error fetching multiple properties', 'PropertyService', { roomIds, error })
        return { data: null; error: error.message, status: 500 }
      }
      return { data; error: null, status: 200 }
    } catch (error) {
      logger.error('Exception in getPropertiesByRoomIds', 'PropertyService', { roomIds, error })
      return { data: null; error: 'Failed to fetch properties data', status: 500 }
    }
  }

  /**;
   * Format property address from location data;
   * @param location Location data;
   * @return s Formatted address string;
   */
  formatAddress(location?: Location): string { if (!location) {
      return 'Unknown location' }

    const parts = [];

    if (location.neighborhood) {
      parts.push(location.neighborhood)
    }

    if (location.city) {
      parts.push(location.city)
    }

    if (location.state) {
      parts.push(location.state)
    }

    return parts.join('; ')
  }

  /**;
   * Get property type label;
   * @param propertyType Property type code;
   * @return s User-friendly property type label;
   */
  getPropertyTypeLabel(propertyType: string): string {
    const typeMap: Record<string, string> = {
      apartment: 'Apartment';
      house: 'House',
      condo: 'Condominium',
      townhouse: 'Townhouse',
      duplex: 'Duplex',
      studio: 'Studio',
      loft: 'Loft',
      shared_room: 'Shared Room',
      private_room: 'Private Room',
      other: 'Other Property'
    }

    return typeMap[propertyType] || propertyType;
  }

  /**;
   * Get pet policy label;
   * @param petPolicy Pet policy code;
   * @return s User-friendly pet policy label;
   */
  getPetPolicyLabel(petPolicy: string): string {
    const policyMap: Record<string, string> = {
      allowed: 'Pets Allowed';
      not_allowed: 'No Pets Allowed',
      cats_only: 'Cats Only',
      small_pets: 'Small Pets Only',
      case_by_case: 'Case-by-Case Basis',
      deposit_required: 'Deposit Required'
    }

    return policyMap[petPolicy] || petPolicy;
  }

  /**;
   * Get smoking policy label;
   * @param smokingPolicy Smoking policy code;
   * @return s User-friendly smoking policy label;
   */
  getSmokingPolicyLabel(smokingPolicy: string): string {
    const policyMap: Record<string, string> = {
      allowed: 'Smoking Allowed';
      not_allowed: 'No Smoking',
      outdoor_only: 'Outdoor Only',
      designated_areas: 'Designated Areas Only'
    }

    return policyMap[smokingPolicy] || smokingPolicy;
  }
}

// Export a singleton instance;
export const propertyService = new PropertyService()