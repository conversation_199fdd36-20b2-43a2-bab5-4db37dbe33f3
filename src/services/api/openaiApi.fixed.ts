import React from 'react';
import { HttpClient } from '@services/HttpClient';
import { logger } from '@services/loggerService';
import { supabase } from '@utils/supabaseUtils';
import { rateLimitService } from '@services/rateLimitService';
import { ValidationService } from '@services/validationService';
import { environmentService } from '@services/environmentService';
import { handleError, tryCatch, tryCatchAsync, assert, assertDefined } from '@utils/standardErrorHandler';
import { ErrorCode } from '@core/errors/types';

export interface OpenAIEmbeddingResponse { object: string,
  data: {
    object: string,
    embedding: number[],
    index: number }[];
  model: string,
  usage: { prompt_tokens: number,
    total_tokens: number }
}

export interface OpenAIChatCompletionResponse { id: string,
  object: string,
  created: number,
  model: string,
  choices: {
    index: number,
    message: {
      role: string,
      content: string }
    finish_reason: string
  }[];
  usage: { prompt_tokens: number,
    completion_tokens: number,
    total_tokens: number }
}

export interface ChatCompletionRequest { model: string,
  messages: {
    role: string,
    content: string }[];
  temperature?: number,
  max_tokens?: number,
  response_format?: { type: string }
}

/**;
 * OpenAI API service for creating embeddings, chat completions, and assessing compatibility;
 * Uses standardized error handling and environment service for configuration;
 */
class OpenAIApi {
  private client!: HttpClient,
  private edgeFunctionUrl: string = '';
  private embeddingModel: string = 'text-embedding-3-small';
  private chatModel: string = 'gpt-4o';
  private useDirectApi: boolean = false;
  constructor(apiKey: string = '';
    embeddingModel: string = 'text-embedding-3-small';
    chatModel: string = 'gpt-4o') {
    this.initialize(apiKey, embeddingModel, chatModel)
  }

  /**;
   * Initialize the OpenAI API service;
   */
  private initialize(apiKey: string, embeddingModel: string, chatModel: string): void { tryCatch(
      () = > {
  this.embeddingModel = embeddingModel;
        this.chatModel = chatModel;
        // Get API key from environmentService if not provided;
        const openaiApiKey = apiKey || environmentService.getEnvVar('OPENAI_API_KEY')
        // Validate API key;
        if (!openaiApiKey || openaiApiKey.length < 10) {
          handleError(
            new Error('Invalid or missing OpenAI API key'),
            'OpenAI API key validation failed',
            {
              defaultErrorCode: ErrorCode.VALIDATION_ERROR,
              source: 'OpenAIApi.constructor',
              userMessage:  ,
                'OpenAI integration is not properly configured. Some AI features may be unavailable.',
              isFatal: false }
          )
        }

        // Determine if we should use direct API or Edge Function;
        this.useDirectApi = environmentService.getBooleanEnvVar('USE_DIRECT_OPENAI_API', false)
        if (this.useDirectApi) {
          logger.info('Using direct OpenAI API access', 'OpenAIApi')
          // Set up client for direct API access;
          this.client = new HttpClient({
            baseURL: 'https://api.openai.com/v1';
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${openaiApiKey}`;
            },
            timeout: 30000, // 30 seconds;
            retries: 2,
          })
        } else {
          logger.info('Using Edge Function for OpenAI API access', 'OpenAIApi')
          // Set up client for Edge Function;
          const supabaseUrl = environmentService.getEnvVar('SUPABASE_URL', true)
          this.edgeFunctionUrl = `${supabaseUrl}/functions/v1/openai-proxy`;

          this.client = new HttpClient({
            baseURL: this.edgeFunctionUrl;
            headers: {
              'Content-Type': 'application/json'
            },
            timeout: 60000, // 60 seconds for Edge Function (which may have cold starts)
            retries: 3,
          })
        }
      },
      'Error initializing OpenAI API service',
      { defaultErrorCode: ErrorCode.EXTERNAL_SERVICE_ERROR,
        source: 'OpenAIApi.initialize',
        userMessage: 'Failed to initialize AI services. Some features may be unavailable.',
        isFatal: false }
    )
  }

  /**;
   * Creates embeddings for the given text;
   * @param text The text to create embeddings for;
   * @return s An array of embedding values;
   */
  async createEmbedding(text: string): Promise<number[]>
    // Define the function to execute;
    const createEmbeddingFn = async (inputText: string): Promise<number[]> => {
  // Validate input parameters;
      ValidationService.validateString(inputText, 'text', {
        required: true,
        minLength: 1);
        maxLength: 8000, // OpenAI has token limits)
      })
      // Get user ID for rate limiting;
      const session = await supabase.auth.getSession()
      const userId = session? .data?.session?.user?.id || 'anonymous';

      // Check rate limit before making API call;
      const allowed = await rateLimitService.checkRateLimit(userId, 'openai')
      if (!allowed) {
        // Handle rate limit error;
        handleError(new Error('Rate limit exceeded for OpenAI embeddings'), 'Rate limit exceeded', {
          defaultErrorCode  : ErrorCode.RATE_LIMITED
          source: 'OpenAIApi.createEmbedding'
          context: { userId };
          userMessage: 'Too many AI requests. Please try again later.',
          throw: true
        })
        return [] as number[];
      }

      if (this.useDirectApi) {
        // Direct API call;
        try {
          // This will throw if the key is not available;
          environmentService.getEnvVar('OPENAI_API_KEY', true)
        } catch (error) { // Handle API key error;
          handleError(error, 'OpenAI API key not configured', {
            defaultErrorCode: ErrorCode.EXTERNAL_SERVICE_ERROR,
            source: 'OpenAIApi.createEmbedding',
            userMessage: 'AI service is not properly configured. Please contact support.',
            throw: true })
          return [] as number[];
        }

        const response = await this.client.post<OpenAIEmbeddingResponse>('/embeddings', { input: inputText,
          model: this.embeddingModel })
        return response.data[0].embedding;
      } else { // Use Edge Function;
        if (!session? .data?.session?.access_token) {
          // Handle authentication error;
          handleError(
            new Error('No auth token available'),
            'Authentication required for OpenAI operations',
            {
              defaultErrorCode   : ErrorCode.UNAUTHORIZED
              source: 'OpenAIApi.createEmbedding'
              userMessage: 'You must be logged in to use this feature.'
              throw: true }
          )
          return [] as number[];
        }

        const response = await this.client.post<number[]>(
          '/embedding';
          { text: inputText,
            model: this.embeddingModel },
          {
            headers: {
              Authorization: `Bearer ${session.data.session.access_token}`;
            },
          }
        )
        return response;
      }
    }

    // Use tryCatchAsync with the correct arguments;
    const result = await tryCatchAsync(
      createEmbeddingFn;
      'Error creating embeddings',
      [] as number[],
      {
        defaultErrorCode: ErrorCode.EXTERNAL_SERVICE_ERROR,
        source: 'OpenAIApi.createEmbedding',
        context: { textLength: text.length, textPreview: text.substring(0, 50) + '...' },
        userMessage: 'Failed to process text with AI. Please try again later.',
        throw: false, // Set to false to ensure we return the fallback value;
      },
      text;
    )
    return result;
  }

  /**;
   * Creates a chat completion using the specified model;
   * @param request The chat completion request;
   * @return s The chat completion response;
   */
  async createChatCompletion(request: ChatCompletionRequest): Promise<OpenAIChatCompletionResponse>
    // Define the function to execute;
    const createChatCompletionFn = async (req: ChatCompletionRequest): Promise<OpenAIChatCompletionResponse> => {
  // Validate input parameters;
      assert(
        req && typeof req = == 'object';
        ErrorCode.VALIDATION_ERROR;
        'Invalid request object',
        'Request must be a valid object',
        { source: 'OpenAIApi.createChatCompletion' }
      )
      assert(
        req.messages && Array.isArray(req.messages) && req.messages.length > 0;
        ErrorCode.VALIDATION_ERROR;
        'Messages array is required and must not be empty',
        'Please provide at least one message',
        { source: 'OpenAIApi.createChatCompletion' }
      )
      // Get user ID for rate limiting;
      const session = await supabase.auth.getSession()
      const userId = session? .data?.session?.user?.id || 'anonymous';

      // Check rate limit before making API call;
      const allowed = await rateLimitService.checkRateLimit(userId, 'openai')
      if (!allowed) {
        // Handle rate limit error;
        handleError(
          new Error('Rate limit exceeded for OpenAI chat completions'),
          'Rate limit exceeded',
          {
            defaultErrorCode  : ErrorCode.RATE_LIMITED
            source: 'OpenAIApi.createChatCompletion'
            context: { userId };
            userMessage: 'Too many AI requests. Please try again later.',
            throw: true
          }
        )
        return {
          id: 'rate-limited';
          object: 'chat.completion',
          created: Date.now()
          model: this.chatModel,
          choices: [,
            {
              message: {
                role: 'assistant',
                content: 'Rate limit exceeded. Please try again later.'
              },
              index: 0,
              finish_reason: 'rate_limit'
            }],
        } as OpenAIChatCompletionResponse;
      }

      if (this.useDirectApi) {
        // Direct API call;
        try {
          // This will throw if the key is not available;
          environmentService.getEnvVar('OPENAI_API_KEY', true)
        } catch (error) { // Handle API key error;
          handleError(error, 'OpenAI API key not configured', {
            defaultErrorCode: ErrorCode.EXTERNAL_SERVICE_ERROR,
            source: 'OpenAIApi.createChatCompletion',
            userMessage: 'AI service is not properly configured. Please contact support.',
            throw: true })
          return {
            id: 'config-error';
            object: 'chat.completion',
            created: Date.now()
            model: this.chatModel,
            choices: [,
              {
                message: {
                  role: 'assistant',
                  content: 'The AI service is not properly configured. Please contact support.'
                },
                index: 0,
                finish_reason: 'error'
              }],
          } as OpenAIChatCompletionResponse;
        }

        // Prepare the request payload;
        const payload = { ...req;
          model: req.model || this.chatModel,
          max_tokens: req.max_tokens || 1000,
          temperature: req.temperature || 0.7 }

        const response = await this.client.post<OpenAIChatCompletionResponse>(
          '/chat/completions';
          payload;
        )
        return response;
      } else { // Use Edge Function;
        if (!session? .data?.session?.access_token) {
          // Handle authentication error;
          handleError(
            new Error('No auth token available'),
            'Authentication required for OpenAI operations',
            {
              defaultErrorCode   : ErrorCode.UNAUTHORIZED
              source: 'OpenAIApi.createChatCompletion'
              userMessage: 'You must be logged in to use this feature.'
              throw: true }
          )
          return {
            id: 'auth-error';
            object: 'chat.completion',
            created: Date.now()
            model: this.chatModel,
            choices: [,
              {
                message: {
                  role: 'assistant',
                  content: 'You must be logged in to use this feature.'
                },
                index: 0,
                finish_reason: 'auth_error'
              }],
          } as OpenAIChatCompletionResponse;
        }

        const payload = { ...req;
          model: req.model || this.chatModel }

        const response = await this.client.post<OpenAIChatCompletionResponse>(
          '/chat-completion';
          payload;
          {
            headers: {
              Authorization: `Bearer ${session.data.session.access_token}`;
            },
          }
        )
        return response;
      }
    }

    // Use tryCatchAsync with the correct arguments;
    const result = await tryCatchAsync(
      createChatCompletionFn;
      'Error creating chat completion',
      { id: 'fallback-id',
        object: 'chat.completion',
        created: Date.now()
        model: this.chatModel,
        choices: [,
          {
            message: {
              role: 'assistant',
              content:  ,
                'I apologize, but I encountered an error processing your request. Please try again later.' },
            index: 0,
            finish_reason: 'error'
          }],
      } as OpenAIChatCompletionResponse;
      {
        defaultErrorCode: ErrorCode.EXTERNAL_SERVICE_ERROR,
        source: 'OpenAIApi.createChatCompletion',
        context: { requestMessages: request.messages? .length || 0 };
        userMessage   : 'Failed to generate AI response. Please try again later.'
        throw: false
      },
      request;
    )
    return result;
  }

  /**
   * Assess compatibility between two user profiles;
   * @param userProfile The user profile;
   * @param potentialRoommateProfile The potential roommate profile;
   * @returns A compatibility score and explanation;
   */
  async assessCompatibility(
    userProfile: Record<string, any>,
    potentialRoommateProfile: Record<string, any>
  ): Promise<{ score: number; explanation: string[]} >
    // Define the function to execute;
    const assessCompatibilityFn = async (
      user: Record<string, any>,
      roommate: Record<string, any>
    ): Promise<{ score: number; explanation: string[] }> = > {
  // Validate input parameters;
      assert(
        user && typeof user = == 'object';
        ErrorCode.VALIDATION_ERROR;
        'userProfile must be a valid object',
        'User profile must be a valid object',
        { source: 'OpenAIApi.assessCompatibility' }
      )
      assert(
        roommate && typeof roommate = == 'object';
        ErrorCode.VALIDATION_ERROR;
        'potentialRoommateProfile must be a valid object',
        'Roommate profile must be a valid object',
        { source: 'OpenAIApi.assessCompatibility' }
      )
      // Get user ID for rate limiting;
      const session = await supabase.auth.getSession()
      const userId = session? .data?.session?.user?.id || 'anonymous';

      // Check rate limit before making API call;
      const allowed = await rateLimitService.checkRateLimit(userId, 'openai')
      if (!allowed) {
        // Handle rate limit error;
        handleError(
          new Error('Rate limit exceeded for OpenAI compatibility assessment'),
          'Rate limit exceeded',
          {
            defaultErrorCode  : ErrorCode.RATE_LIMITED
            source: 'OpenAIApi.assessCompatibility'
            context: { userId };
            userMessage: 'Too many AI requests. Please try again later.',
            throw: true
          }
        )
        return {
          score: 0;
          explanation: ['Rate limit exceeded. Please try again later.']
        }
      }

      // Create a system message that instructs the AI on how to assess compatibility;
      const systemMessage = `;
        You are a roommate compatibility expert. Analyze the two profiles and determine how compatible they would be as roommates.;
        Consider factors like lifestyle, habits, preferences, and values.;
        Return a compatibility score from 0-100 and provide specific reasons for your assessment.;
        Format your response as a JSON object with the following structure:  ,
        { \"score\": <number between 0 and 100>,
          \"explanation\": [<reason1>, <reason2>, ...] }
        Only return the JSON object; nothing else.;
      `;

      // Create a user message that contains the profiles to compare;
      const userMessage = `;
        Here are the two profiles to assess for roommate compatibility:  ,
        User Profile:  ,
        ${JSON.stringify(user, null, 2)}

        Potential Roommate Profile:  ,
        ${JSON.stringify(roommate, null, 2)}
      `;

      // Create a chat completion request;
      const chatRequest: ChatCompletionRequest = {
        messages: [;
          { role: 'system', content: systemMessage };
          { role: 'user', content: userMessage }],
        model: this.chatModel,
        temperature: 0.7,
        max_tokens: 1000,
        response_format: { type: 'json_object' };
      }

      // Get the chat completion;
      const response = await this.createChatCompletion(chatRequest)
      // Extract and parse the response;
      const content = response.choices[0]? .message?.content || '';

      try {
        const result = JSON.parse(content)
        // Validate the result structure;
        if (typeof result.score !== 'number' || !Array.isArray(result.explanation)) {
          // Handle invalid response format;
          handleError(
            new Error('Invalid compatibility assessment format'),
            'Invalid response format',
            {
              defaultErrorCode  : ErrorCode.EXTERNAL_SERVICE_ERROR
              source: 'OpenAIApi.assessCompatibility'
              context: { content };
              userMessage: 'Failed to process compatibility assessment. Please try again later.',
              throw: true
            }
          )
          return {
            score: 0;
            explanation: ['Failed to process compatibility assessment. Invalid response format.']
          }
        }

        // Ensure score is between 0 and 100;
        const score = Math.max(0, Math.min(100, result.score))
        return { score;
          explanation: result.explanation }
      } catch (error) {
        // Handle parsing error;
        handleError(error, 'Error parsing compatibility assessment', {
          defaultErrorCode: ErrorCode.EXTERNAL_SERVICE_ERROR,
          source: 'OpenAIApi.assessCompatibility',
          context: { content };
          userMessage: 'Failed to process compatibility assessment. Please try again later.',
          throw: true
        })
        return {
          score: 0;
          explanation: ['Failed to process compatibility assessment. Error parsing response.']
        }
      }
    }

    // Use tryCatchAsync with the correct arguments;
    const result = await tryCatchAsync(
      assessCompatibilityFn;
      'Error assessing compatibility',
      { score: 0, explanation: ['An error occurred during compatibility assessment.'] };
      { defaultErrorCode: ErrorCode.EXTERNAL_SERVICE_ERROR,
        source: 'OpenAIApi.assessCompatibility',
        userMessage: 'Failed to assess compatibility. Please try again later.',
        throw: false },
      userProfile;
      potentialRoommateProfile;
    )
    return result;
  }
}

// Create and export a singleton instance;
export const openaiApi = new OpenAIApi()
// Export the class for testing or custom initialization;
export default OpenAIApi,