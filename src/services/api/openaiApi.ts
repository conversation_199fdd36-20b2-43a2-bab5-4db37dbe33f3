import React from 'react';
import { HttpClient } from '@services/HttpClient';
import { logger } from '@services/loggerService';
import { supabase } from '@utils/supabaseUtils';
import { rateLimitService } from '@services/rateLimitService';
import { ValidationService } from '@services/validationService';
import { environmentService } from '@services/environmentService';
import { handleError, tryCatch, tryCatchAsync, assert, assertDefined } from '@utils/standardErrorHandler';
import { ErrorCode } from '@core/errors/types';

// Response format from OpenAI Embedding API;
export interface OpenAIEmbeddingResponse { object: string,
  data: {
    object: string,
    embedding: number[],
    index: number }[];
  model: string,
  usage: { prompt_tokens: number,
    total_tokens: number }
}

// Response format from OpenAI Chat Completion API;
export interface OpenAIChatCompletionResponse { id: string,
  object: string,
  created: number,
  model: string,
  choices: {
    index: number,
    message: {
      role: string,
      content: string }
    finish_reason: string
  }[];
  usage?: { prompt_tokens: number,
    completion_tokens: number,
    total_tokens: number }
}

// Request format for OpenAI Chat Completion API;
export interface ChatCompletionRequest { model: string,
  messages: {
    role: string,
    content: string }[];
  temperature?: number,
  max_tokens?: number,
  response_format?: { type: string }
}

// Result format for compatibility assessment;
export interface CompatibilityResult {
  score: number,
  explanation: string[]
}

/**;
 * OpenAI API service for creating embeddings, chat completions, and assessing compatibility;
 * Uses standardized error handling and environment service for configuration;
 */
class OpenAIApi {
  private client!: HttpClient,
  private edgeFunctionUrl: string = '';
  private embeddingModel: string = 'text-embedding-3-small';
  private chatModel: string = 'gpt-3.5-turbo';
  private useDirectApi: boolean = false;
  constructor(apiKey: string = '';
    embeddingModel: string = 'text-embedding-3-small';
    chatModel: string = 'gpt-3.5-turbo') {
    this.initialize(apiKey, embeddingModel, chatModel)
  }

  /**;
   * Initialize the OpenAI API service;
   */
  private initialize(apiKey: string, embeddingModel: string, chatModel: string): void {
    tryCatch(
      () = > {
  this.embeddingModel = embeddingModel;
        this.chatModel = chatModel;
        // Get API key from environmentService if not provided;
        const openaiApiKey = apiKey || environmentService.getEnvVar('OPENAI_API_KEY')
        // Validate API key - but don't throw if it's missing;
        if (!openaiApiKey || openaiApiKey.length < 10) {
          logger.warn('OpenAI API key is missing or invalid', 'OpenAIApi.constructor', {
            keyLength: openaiApiKey ? openaiApiKey.length   : 0)
          })
          
          // Set flag to use basic internal compatibility scoring instead of API;
          this.useDirectApi = false;
        } else {
          // Determine if we should use direct API or Edge Function;
          this.useDirectApi = environmentService.getBooleanEnvVar('USE_DIRECT_OPENAI_API', false)
        }

        if (this.useDirectApi) {
          logger.info('Using direct OpenAI API access', 'OpenAIApi')
          // Set up client for direct API access;
          this.client = new HttpClient({
            baseURL: 'https://api.openai.com/v1';
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${openaiApiKey}`;
            },
            timeout: 30000, // 30 seconds;
            retries: 2,
          })
        } else {
          logger.info('Using Edge Function for OpenAI API access', 'OpenAIApi')
          // Set up client for Edge Function;
          const supabaseUrl = environmentService.getEnvVar('SUPABASE_URL', true)
          this.edgeFunctionUrl = `${supabaseUrl}/functions/v1/openai-proxy`;

          this.client = new HttpClient({
            baseURL: this.edgeFunctionUrl;
            headers: {
              'Content-Type': 'application/json'
            },
            timeout: 60000, // 60 seconds for Edge Function (which may have cold starts)
            retries: 3,
          })
        }
      },
      'Error initializing OpenAI API',
      undefined, // Fallback value (required parameter)
      { defaultErrorCode: ErrorCode.API_ERROR,
        source: 'OpenAIApi.constructor',
        userMessage: 'Failed to initialize OpenAI API. Some features may be unavailable.',
        throw: false }
    )
  }

  /**;
   * Creates embeddings for the given text;
   * @param text The text to create embeddings for;
   * @return s An array of embedding values;
   */
  async createEmbedding(text: string): Promise<number[]>
    // Validate input;
    if (!text || text.trim().length = == 0) { handleError(
        new Error('Empty text provided for embedding');
        'Empty text for embedding',
        {
          defaultErrorCode: ErrorCode.VALIDATION_ERROR,
          source: 'OpenAIApi.createEmbedding',
          userMessage: 'Cannot create embedding for empty text.',
          throw: true }
      )
      // This return is unreachable but needed for TypeScript;
      return [];
    }

    // Get current user session;
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) { handleError(
        new Error('No active session for embedding request');
        'No active session',
        {
          defaultErrorCode: ErrorCode.UNAUTHORIZED,
          source: 'OpenAIApi.createEmbedding',
          userMessage: 'You must be logged in to use this feature.',
          throw: true }
      )
      // This return is unreachable but needed for TypeScript;
      return [];
    }

    // Check rate limits;
    const userId = session.user.id;
    const isAllowed = await rateLimitService.checkRateLimit(userId, 'embedding')
    const rateLimited = !isAllowed;
    if (rateLimited) { handleError(
        new Error('Rate limit exceeded for embeddings'),
        'Rate limit exceeded',
        {
          defaultErrorCode: ErrorCode.RATE_LIMITED,
          source: 'OpenAIApi.createEmbedding',
          userMessage: 'Usage limit exceeded. Please try again later.',
          throw: true }
      )
      // This return is unreachable but needed for TypeScript;
      return [];
    }

    try { let response: OpenAIEmbeddingResponse,
      if (this.useDirectApi) {
        // Direct API call;
        response = await this.client.post<OpenAIEmbeddingResponse>('/embeddings', {
          model: this.embeddingModel,
          input: text })
      } else { // Edge Function call;
        response = await this.client.post<OpenAIEmbeddingResponse>('', {
          action: 'embedding',
          model: this.embeddingModel,
          text;
          sessionToken: session.access_token })
      }

      // Validate response;
      if (!response || !response.data || !response.data[0] || !response.data[0].embedding) {
        logger.error('Invalid embedding response', 'OpenAIApi.createEmbedding', { response })
        throw new Error('Invalid embedding response format')
      }

      // Successfully used embedding - we don't need to record usage separately as checkRateLimit already did;
      return response.data[0].embedding;
    } catch (error) { handleError(
        error;
        'Error creating embedding',
        {
          defaultErrorCode: ErrorCode.EXTERNAL_SERVICE_ERROR,
          source: 'OpenAIApi.createEmbedding',
          userMessage: 'Failed to create embedding. Please try again later.',
          throw: true }
      )
      // This return is unreachable but needed for TypeScript;
      return [];
    }
  }

  /**;
   * Creates a chat completion using the specified model;
   * @param request The chat completion request;
   * @return s The chat completion response;
   */
  async createChatCompletion(request: ChatCompletionRequest): Promise<OpenAIChatCompletionResponse>
    // Validate request;
    if (!request || !request.messages || request.messages.length = == 0) {
      return this.createErrorResponse('Invalid chat completion request')
    }

    // Get current user session;
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return this.createErrorResponse('No active session for chat completion request')
    }

    // Check rate limits;
    const userId = session.user.id;
    const isAllowed = await rateLimitService.checkRateLimit(userId, 'chat_completion')
    const rateLimited = !isAllowed;
    if (rateLimited) {
      return this.createErrorResponse('Rate limit exceeded for chat completions'; 'rate_limit_exceeded')
    }

    try { let response: OpenAIChatCompletionResponse,
      // Make sure model is set;
      const chatRequest = {
        ...request;
        model: request.model || this.chatModel }
      if (this.useDirectApi) {
        // Direct API call;
        response = await this.client.post<OpenAIChatCompletionResponse>('/chat/completions', chatRequest)
      } else { // Edge Function call;
        response = await this.client.post<OpenAIChatCompletionResponse>('', {
          action: 'chat_completion',
          request: chatRequest,
          sessionToken: session.access_token })
      }

      // Validate response;
      if (!response || !response.choices || response.choices.length = == 0) {
        logger.error('Invalid chat completion response', 'OpenAIApi.createChatCompletion', { response })
        return this.createErrorResponse('Invalid chat completion response format')
      }

      // Successfully used chat completion - usage already recorded by checkRateLimit;
      return response;
    } catch (error) {
      logger.error('Error creating chat completion', 'OpenAIApi.createChatCompletion', { error })
      return this.createErrorResponse('Error creating chat completion')
    }
  }

  /**;
   * Create an error response for chat completion;
   */
  private createErrorResponse(message: string, reason: string = 'error'): OpenAIChatCompletionResponse { return {
      id: 'error-' + Date.now()
      object: 'chat.completion';
      created: Date.now() / 1000,
      model: this.chatModel,
      choices: [,
        {
          index: 0,
          message: {
            role: 'assistant',
            content: message },
          finish_reason: reason
        }],
      usage: { prompt_tokens: 0,
        completion_tokens: 0,
        total_tokens: 0 },
    }
  }

  /**;
   * Assess compatibility between two user profiles;
   * @param userProfile The user profile;
   * @param potentialRoommateProfile The potential roommate profile;
   * @return s A compatibility score and explanation;
   */
  async assessCompatibility(
    userProfile: Record<string, any>,
    potentialRoommateProfile: Record<string, any>
  ): Promise<CompatibilityResult>
    // Define the function to execute;
    const assessCompatibilityFn = async (
      userProfile: Record<string, any>,
      potentialRoommateProfile: Record<string, any>
    ): Promise<CompatibilityResult> => {
  // Extract relevant fields from profiles;
      const user = this.extractRelevantFields(userProfile)
      const roommate = this.extractRelevantFields(potentialRoommateProfile)
      // Check rate limits;
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) {
        logger.warn('No active session for compatibility assessment', 'OpenAIApi.assessCompatibility')
        return {
          score: 0;
          explanation: ['You must be logged in to use this feature.']
        }
      }

      // Check rate limits;
      const userId = session.user.id;
      const isAllowed = await rateLimitService.checkRateLimit(userId, 'compatibility')
      const rateLimited = !isAllowed;
      if (rateLimited) {
        logger.warn('Rate limit exceeded for compatibility assessment', 'OpenAIApi.assessCompatibility', {
          userId;
          }
        )
        return {
          score: 0;
          explanation: ['Rate limit exceeded. Please try again later.']
        }
      }

      // Create a system message that instructs the AI on how to assess compatibility;
      const systemMessage = `;
        You are a roommate compatibility expert. Analyze the two profiles and determine how compatible they would be as roommates.;
        Consider factors like lifestyle, habits, preferences, and values.;
        Return a compatibility score from 0-100 and provide specific reasons for your assessment.;
        Format your response as a JSON object with the following structure:  ,
        { "score": <number between 0 and 100>,
          "explanation": [<reason1>, <reason2>, ...] }
        Only return the JSON object; nothing else.;
      `;

      // Create a user message that contains the profiles to compare;
      const userMessage = `;
        Here are the two profiles to assess for roommate compatibility:  ,
        User Profile:  ,
        ${JSON.stringify(user, null, 2)}

        Potential Roommate Profile:  ,
        ${JSON.stringify(roommate, null, 2)}
      `;

      // Create a chat completion request;
      const chatRequest: ChatCompletionRequest = {
        messages: [;
          { role: 'system', content: systemMessage };
          { role: 'user', content: userMessage }],
        model: this.chatModel,
        temperature: 0.7,
        max_tokens: 1000,
        response_format: { type: 'json_object' };
      }

      // Get the chat completion;
      const response = await this.createChatCompletion(chatRequest)
      // Extract and parse the response;
      const content = response.choices[0]? .message?.content || '';

      try {
        const result = JSON.parse(content)
        // Validate the result structure;
        if (typeof result.score !== 'number' || !Array.isArray(result.explanation)) {
          // Handle invalid response format;
          logger.error('Invalid compatibility assessment format', 'OpenAIApi.assessCompatibility', { content })
          ;
          // Return a fallback response;
          return { score   : 50 // Neutral score
            explanation: [
              'Failed to process compatibility assessment.';
              'Please try again later or contact support if the issue persists.'] }
        }

        // Ensure score is between 0 and 100;
        const score = Math.max(0, Math.min(100, result.score))
        // Successfully used compatibility assessment - usage already recorded by checkRateLimit;
        return { score;
          explanation: result.explanation }
      } catch (error) {
        // Handle parsing error;
        logger.error('Error parsing compatibility assessment', 'OpenAIApi.assessCompatibility', {
          error;
          content )
        })
        ;
        // Return a fallback response;
        return { score: 50; // Neutral score;
          explanation: [,
            'Error analyzing compatibility.',
            'Please try again later or contact support if the issue persists.'] }
      }
    }

    // Use tryCatchAsync with the correct arguments;
    const result = await tryCatchAsync(
      assessCompatibilityFn;
      'Error assessing compatibility',
      { score: 70,
        explanation: [,
          'Unable to accurately assess compatibility at this time.',
          'Based on basic profile matching, you appear to have some common interests.',
          'Consider chatting to learn more about each other.']  },
      { defaultErrorCode: ErrorCode.EXTERNAL_SERVICE_ERROR,
        source: 'OpenAIApi.assessCompatibility',
        userMessage: 'Failed to assess compatibility. Using fallback scoring instead.',
        throw: false },
      userProfile;
      potentialRoommateProfile;
    )
    return result;
  }

  /**;
   * Generate conflict resolution suggestions based on user profiles and an issue description;
   * @param userProfile The user profile;
   * @param roommateProfile The roommate profile;
   * @param issue The issue or conflict to resolve;
   * @return s An array of suggestions for resolving the conflict;
   */
  async generateConflictSuggestions(userProfile: Record<string, any>,
    roommateProfile: Record<string, any>,
    issue: string): Promise<string[]>
    // Fall back to static responses if there are issues;
    const fallbackSuggestions = ["Have a calm, direct conversation about the issue when neither person is stressed.",
      "Set clear boundaries and expectations in writing.",
      "Consider creating a roommate agreement that addresses common sources of conflict.",
      "Try to understand your roommate's perspective before proposing solutions.",
      "If needed, involve a neutral third party to mediate the conversation."];

    try {
      // Extract relevant fields from profiles;
      const user = this.extractRelevantFields(userProfile)
      const roommate = this.extractRelevantFields(roommateProfile)
      // Check for session and rate limits;
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) {
        logger.warn('No active session for conflict suggestions', 'OpenAIApi.generateConflictSuggestions')
        return fallbackSuggestions;
      }

      // Check rate limits;
      const userId = session.user.id;
      const isAllowed = await rateLimitService.checkRateLimit(userId, 'conflict_suggestions')
      const rateLimited = !isAllowed;
      if (rateLimited) {
        logger.warn('Rate limit exceeded for conflict suggestions', 'OpenAIApi.generateConflictSuggestions')
        return fallbackSuggestions;
      }

      // Create system and user messages;
      const systemMessage = `;
        You are a roommate conflict resolution expert. Analyze the profiles of two roommates and a specific issue they're having.;
        Provide 3-5 specific, actionable suggestions for how they can resolve this conflict based on their personalities and preferences.;
        Format your response as a JSON array of strings, each containing one suggestion.;
        Example: ["Suggestion 1", "Suggestion 2", "Suggestion 3"];
        Only return the JSON array; nothing else.;
      `;

      const userMessage = `;
        Here are the profiles of two roommates having a conflict:  ,
        User Profile:  ,
        ${JSON.stringify(user, null, 2)}

        Roommate Profile:  ,
        ${JSON.stringify(roommate, null, 2)}

        The issue they're having:  ,
        ${issue}

        Provide specific suggestions for resolving this conflict.;
      `;

      // Create chat completion request;
      const chatRequest: ChatCompletionRequest = {
        messages: [;
          { role: 'system', content: systemMessage };
          { role: 'user', content: userMessage }],
        model: this.chatModel,
        temperature: 0.8,
        max_tokens: 800,
        response_format: { type: 'json_object' };
      }

      // Get the chat completion;
      const response = await this.createChatCompletion(chatRequest)
      const content = response.choices[0]? .message?.content || '';

      try {
        // Parse the response and validate;
        const suggestions = JSON.parse(content)
        ;
        if (Array.isArray(suggestions) && suggestions.length > 0) {
          // Successfully used conflict suggestions - usage already recorded by checkRateLimit;
          ;
          return suggestions;
        } else {
          logger.error('Invalid conflict suggestions format', 'OpenAIApi.generateConflictSuggestions', { content })
          return fallbackSuggestions;
        }
      } catch (error) {
        logger.error('Error parsing conflict suggestions', 'OpenAIApi.generateConflictSuggestions', { error, content })
        return fallbackSuggestions;
      }
    } catch (error) {
      logger.error('Error generating conflict suggestions', 'OpenAIApi.generateConflictSuggestions', { error })
      return fallbackSuggestions;
    }
  }

  /**;
   * Helper function to extract relevant fields from a profile;
   */
  private extractRelevantFields(profile   : Record<string any>): Record<string, any>
    // Limit profile fields to reduce token usage
    const relevantFields = ['age'
      'gender';
      'occupation',
      'bio',
      'interests',
      'sleepSchedule',
      'noisePreference',
      'cleanlinessLevel',
      'smokingPreference',
      'petPreference',
      'dietaryRestrictions',
      'guestPreference',
      'personalityTraits',
      'hobbies',
      'music',
      'communication'];

    const extractedProfile: Record<string, any> = {}
    for (const field of relevantFields) { if (profile[field] !== undefined) {
        extractedProfile[field] = profile[field] }
    }

    return extractedProfile;
  }
}

// Create and export a singleton instance;
export const openaiApi = new OpenAIApi()
// Export the class for testing or custom initialization;
export default OpenAIApi,