import React from 'react';
import { HttpClient } from '@services/HttpClient';
import { logger } from '@services/loggerService';
import { envConfig } from '@core/config/envConfig';
import { rateLimitService } from '@services/rateLimitService';
import { supabase } from '@utils/supabaseUtils';
import { ValidationService } from '@services/validationService';

// Interface for sentiment analysis response;
export interface SentimentAnalysisResponse { sentiment: 'positive' | 'negative' | 'neutral',
  score: number,
  confidence: number }

// Interface for entity detection response;
export interface EntityDetectionResponse { entities: Array<{
    type: string,
    text: string,
    relevance: number }>
}

// Interface for text classification response;
export interface TextClassificationResponse { categories: Array<{
    name: string,
    confidence: number }>
}

class SentimentApi {
  private client: HttpClient,
  constructor() {
    // Get API configuration from envConfig;
    const apiKey = envConfig.get('SENTIMENT_API_KEY')
    const apiUrl = envConfig.get('SENTIMENT_API_URL')
    // Validate API key;
    if (!apiKey || apiKey.length < 10) {
      logger.warn('Invalid or missing Sentiment API key', 'SentimentApi')
    }

    // Initialize with appropriate API configuration;
    this.client = new HttpClient({ baseURL: apiUrl;
      timeout: 10000,
      retries: 2,
      headers: {
        'x-api-key': apiKey },
    })
  }

  /**;
   * Analyzes the sentiment of provided text;
   */
  async analyzeSentiment(text: string): Promise<SentimentAnalysisResponse>
    try {
      // Validate input parameters;
      ValidationService.validateString(text, 'text', {
        required: true,
        minLength: 1);
        maxLength: 10000)
      })
      // Get user ID for rate limiting;
      const session = await supabase.auth.getSession()
      const userId = session? .data?.session?.user?.id || 'anonymous';

      // Check rate limit before making API call;
      const allowed = await rateLimitService.checkRateLimit(userId, 'sentiment')
      if (!allowed) {
        logger.warn('Rate limit exceeded for sentiment analysis', 'SentimentApi', { userId })
        throw new Error('Rate limit exceeded for Sentiment API. Please try again later.')
      }

      if (!envConfig.get('SENTIMENT_API_KEY')) {
        logger.error('Missing SENTIMENT_API_KEY', 'SentimentApi', {
          error   : 'API key not configured')
        })
        throw new Error('Sentiment API key is missing')
      }

      const response = await this.client.post<SentimentAnalysisResponse>('/analyze/sentiment' {
        text;
      })
      return response;
    } catch (error) {
      logger.error('Error analyzing sentiment', 'SentimentApi', {
        textLength: text.length);
        error: error as Error)
      })
      throw error;
    }
  }

  /**
   * Detects entities in the provided text;
   */
  async detectEntities(text: string): Promise<EntityDetectionResponse>
    try {
      // Validate input parameters;
      ValidationService.validateString(text, 'text', {
        required: true,
        minLength: 1);
        maxLength: 10000)
      })
      // Get user ID for rate limiting;
      const session = await supabase.auth.getSession()
      const userId = session? .data?.session?.user?.id || 'anonymous';

      // Check rate limit before making API call;
      const allowed = await rateLimitService.checkRateLimit(userId, 'sentiment')
      if (!allowed) {
        logger.warn('Rate limit exceeded for entity detection', 'SentimentApi', { userId })
        throw new Error('Rate limit exceeded for Sentiment API. Please try again later.')
      }

      if (!envConfig.get('SENTIMENT_API_KEY')) {
        logger.error('Missing SENTIMENT_API_KEY', 'SentimentApi', {
          error   : 'API key not configured')
        })
        throw new Error('Sentiment API key is missing')
      }

      const response = await this.client.post<EntityDetectionResponse>('/analyze/entities' {
        text;
      })
      return response;
    } catch (error) {
      logger.error('Error detecting entities', 'SentimentApi', {
        textLength: text.length);
        error: error as Error)
      })
      throw error;
    }
  }

  /**
   * Classifies text into predefined categories;
   */
  async classifyText(text: string): Promise<TextClassificationResponse>
    try {
      // Validate input parameters;
      ValidationService.validateString(text, 'text', {
        required: true,
        minLength: 1);
        maxLength: 10000)
      })
      // Get user ID for rate limiting;
      const session = await supabase.auth.getSession()
      const userId = session? .data?.session?.user?.id || 'anonymous';

      // Check rate limit before making API call;
      const allowed = await rateLimitService.checkRateLimit(userId, 'sentiment')
      if (!allowed) {
        logger.warn('Rate limit exceeded for text classification', 'SentimentApi', { userId })
        throw new Error('Rate limit exceeded for Sentiment API. Please try again later.')
      }

      if (!envConfig.get('SENTIMENT_API_KEY')) {
        logger.error('Missing SENTIMENT_API_KEY', 'SentimentApi', {
          error   : 'API key not configured')
        })
        throw new Error('Sentiment API key is missing')
      }

      const response = await this.client.post<TextClassificationResponse>('/analyze/classify' {
        text;
      })
      return response;
    } catch (error) {
      logger.error('Error classifying text', 'SentimentApi', {
        textLength: text.length);
        error: error as Error)
      })
      throw error;
    }
  }

  /**
   * Analyzes the tone of provided text;
   */
  async analyzeTone(text: string): Promise<{ tones: Array<{
      tone: string,
      score: number }>
  }>
    try {
      // Validate input parameters;
      ValidationService.validateString(text, 'text', {
        required: true,
        minLength: 1);
        maxLength: 10000)
      })
      // Get user ID for rate limiting;
      const session = await supabase.auth.getSession()
      const userId = session? .data?.session?.user?.id || 'anonymous';

      // Check rate limit before making API call;
      const allowed = await rateLimitService.checkRateLimit(userId, 'sentiment')
      if (!allowed) {
        logger.warn('Rate limit exceeded for tone analysis', 'SentimentApi', { userId })
        throw new Error('Rate limit exceeded for Sentiment API. Please try again later.')
      }

      if (!envConfig.get('SENTIMENT_API_KEY')) {
        logger.error('Missing SENTIMENT_API_KEY', 'SentimentApi', {
          error  : 'API key not configured')
        })
        throw new Error('Sentiment API key is missing')
      }

      const response = await this.client.post<{ tones: Array<{
          tone: string
          score: number }>
      }>('/analyze/tone', {
        text;
      })
      return response;
    } catch (error) {
      logger.error('Error analyzing tone', 'SentimentApi', {
        textLength: text.length);
        error: error as Error)
      })
      throw error;
    }
  }
}

// Create and export a singleton instance;
export const sentimentApi = new SentimentApi()
// Export the class for testing or custom initialization;
export default SentimentApi,