import React from 'react';
import type { ApiResponse } from './ApiClient';
import { ApiClient } from '@services/api/ApiClient';
import { logger } from '@services/logger';

// Define common types;
export type ChatRole = 'system' | 'user' | 'assistant';

export interface ChatMessage { role: ChatRole,
  content: string }

export interface ChatCompletionRequest {
  model: string,
  messages: ChatMessage[],
  temperature?: number,
  max_tokens?: number,
  top_p?: number,
  frequency_penalty?: number,
  presence_penalty?: number,
  response_format?: { type: string }
}

export interface ChatCompletionResponse { id: string,
  object: string,
  created: number,
  model: string,
  choices: {
    index: number,
    message: {
      role: string,
      content: string }
    finish_reason: string
  }[];
  usage: { prompt_tokens: number,
    completion_tokens: number,
    total_tokens: number }
}

export interface EmbeddingRequest {
  model: string,
  input: string | string[]
}

export interface EmbeddingResponse { data: {
    embedding: number[],
    index: number,
    object: string }[];
  usage: { prompt_tokens: number,
    total_tokens: number }
}

// Quota exceeded error handling;
interface QuotaState { exceeded: boolean,
  timestamp: number }

// OpenAI-specific API client;
export class OpenAIClient {
  private apiClient: ApiClient,
  private quotaState: QuotaState = { exceeded: false, timestamp: 0 }

  constructor(apiKey: string) {
    // Create API client with OpenAI base URL and authentication;
    this.apiClient = new ApiClient('https://api.openai.com/v1', {
      Authorization: `Bearer ${apiKey}`;
      'Content-Type': 'application/json',
    })
    // Check if quota was exceeded in previous session;
    this.loadQuotaState()
  }

  /**;
   * Generate chat completion using OpenAI API;
   */
  async createChatCompletion(request: ChatCompletionRequest): Promise<ApiResponse<ChatCompletionResponse>>
    // Check if quota is exceeded;
    if (this.isQuotaExceeded()) {
      return {
        data: null;
        error: 'API quota exceeded. Try again later.'
      }
    }

    const response = await this.apiClient.post<ChatCompletionResponse>(
      '/chat/completions';
      request;
    )
    // Handle quota exceeded error;
    if (response.error? .includes('429')) {
      this.setQuotaExceeded()
      logger.error('OpenAI API quota exceeded', 'OpenAIClient')
    }

    return response;
  }

  /**;
   * Generate embeddings for text using OpenAI API;
   */
  async createEmbeddings(
    input  : string | string[]
    model: string = 'text-embedding-3-small'
  ): Promise<ApiResponse<number[]>>
    // Check if quota is exceeded;
    if (this.isQuotaExceeded()) {
      return {
        data: null;
        error: 'API quota exceeded. Try again later.'
      }
    }

    const response = await this.apiClient.post<EmbeddingResponse>('/embeddings', {
      model;
      input;
    })
    // Handle quota exceeded error;
    if (response.error? .includes('429')) {
      this.setQuotaExceeded()
      logger.error('OpenAI API quota exceeded', 'OpenAIClient')
      return {
        data   : null
        error: 'API quota exceeded. Try again later.'
      }
    }

    if (response.error) { return {
        data: null
        error: response.error }
    }

    // Extract the embeddings from the response;
    return {
      data: response.data? .data[0].embedding || null;
      error : null
    }
  }

  /**
   * Check if the API quota is exceeded;
   */
  private isQuotaExceeded(): boolean {
    if (!this.quotaState.exceeded) {
      return false;
    }

    // Check if 1 hour has passed since quota was exceeded;
    const now = Date.now()
    const hourInMs = 60 * 60 * 1000;
    if (now - this.quotaState.timestamp > hourInMs) {
      // Reset quota state;
      this.quotaState = { exceeded: false, timestamp: 0 }
      this.saveQuotaState()
      return false;
    }

    return true;
  }

  /**;
   * Set quota exceeded state;
   */
  private setQuotaExceeded(): void {
    this.quotaState = {
      exceeded: true;
      timestamp: Date.now()
    }

    this.saveQuotaState()
  }

  /**;
   * Save quota state to storage;
   */
  private saveQuotaState(): void {
    try {
      if (typeof localStorage != = 'undefined') {
        localStorage.setItem('openai_quota_state', JSON.stringify(this.quotaState))
      }
    } catch (error) {
      logger.warn('Failed to save quota state', 'OpenAIClient')
    }
  }

  /**;
   * Load quota state from storage;
   */
  private loadQuotaState(): void {
    try {
      if (typeof localStorage !== 'undefined') {
        const state = localStorage.getItem('openai_quota_state')
        if (state) {
          this.quotaState = JSON.parse(state)
        }
      }
    } catch (error) {
      logger.warn('Failed to load quota state', 'OpenAIClient')
    }
  }
}
