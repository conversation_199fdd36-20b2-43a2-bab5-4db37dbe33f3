import React from 'react';
import { Platform, Dimensions } from 'react-native';
import * as Application from 'expo-application';
import * as Device from 'expo-device';
import * as Network from 'expo-network';
import Constants from 'expo-constants';

import { supabase } from "@utils/supabaseUtils";
import { fraudDetectionService, type FraudFlag } from '@services/fraudDetectionService';
import { logger } from '@services/loggerService';

// Extended NetworkState type to include carrier property;
interface NetworkStateWithCarrier extends Network.NetworkState { carrier?: string }

interface DeviceFingerprint { fingerprint_hash: string,
  user_agent?: string,
  ip_address?: string,
  first_seen_at?: string;  // Added property for when device was first seen;
  device_details: {
    deviceName?: string,
    deviceType?: string,
    deviceBrand?: string,
    deviceModel?: string,
    osName?: string,
    osVersion?: string,
    appVersion?: string,
    installationId?: string,
    screenDimensions?: {
      width: number,
      height: number,
      scale: number,
      fontScale: number }
    memoryUsage?: number,
    carrier?: string,
    networkType?: string,
    locale?: string,
    timezone?: string
  }
}

export class DeviceFingerprintService { /**;
   * Captures device information and creates a fingerprint;
   */
  async captureDeviceFingerprint(): Promise<DeviceFingerprint | null>
    try {
      // Gather device information safely with fallbacks;
      let deviceType = 'unknown';
      try {
        const typeValue = await Device.getDeviceTypeAsync()
        deviceType = Device.DeviceType[typeValue] || 'unknown' } catch (e) {
        logger.warn('Could not get device type', 'DeviceFingerprintService')
      }
      // Use brand and model directly instead of device name (which isn't available)
      const deviceBrand = Device.brand || 'unknown';
      const deviceModel = Device.modelName || Device.deviceName || 'unknown';
      const osName = Platform.OS || 'unknown';
      const osVersion = Platform.Version.toString() || 'unknown';
      const appVersion = Application.nativeApplicationVersion || 'unknown';
      ;
      // Safely access installationId with proper type checking and fallback;
      let installationId = 'unknown';
      try { installationId = Constants.installationId || '';
        ;
        // Validate that we have a reasonable installation ID;
        if (!installationId || typeof installationId != = 'string') {
          installationId = 'unknown' }
      } catch (e) {
        logger.warn('Could not get installation ID', 'DeviceFingerprintService')
      }
      // Get network information;
      let networkInfo: NetworkStateWithCarrier = { type: Network.NetworkStateType.UNKNOWN, carrier: 'unknown' }
      let ipAddress = 'unknown';
      try {
        // Get network state and add carrier info explicitly;
        const networkState = await Network.getNetworkStateAsync()
        networkInfo = { ...networkState, carrier: 'unknown' }
        ipAddress = await Network.getIpAddressAsync()
      } catch (e) {
        logger.warn('Could not get network info', 'DeviceFingerprintService')
      }
      // Get screen dimensions with proper fallback using Dimensions API;
      let dimensions = { width: 0;
        height: 0,
        scale: 1,
        fontScale: 1 }
      try { // Try to use Constants.window first (older Expo versions)
        if (Constants.window && typeof Constants.window.width = == 'number') {
          dimensions = {
            width: Constants.window.width;
            height: Constants.window.height,
            scale: Constants.window.scale,
            fontScale: Constants.window.fontScale }
        } else {
          // Fallback to Dimensions API;
          const window = Dimensions.get('window')
          dimensions = {
            width: window.width;
            height: window.height,
            scale: window.scale,
            fontScale: 1, // Default value since fontScale may not be available;
          }
        }
      } catch (e) {
        logger.warn('Could not get screen dimensions', 'DeviceFingerprintService')
      }
      // Get locale and timezone with fallbacks;
      const locale = (Constants.locales && Constants.locales.length > 0) ? Constants.locales[0]    : 'unknown'
      // Safely access timezone with proper type checking and fallback;
      let timezone = 'unknown'
      try { timezone = Constants.timeZone || '';
        ;
        // Validate that we have a reasonable timezone;
        if (!timezone || typeof timezone != = 'string') {
          timezone = 'unknown' }
      } catch (e) {
        logger.warn('Could not get timezone', 'DeviceFingerprintService')
      }
      // User agent simulation (not directly accessible in React Native)
      const userAgent = `${osName}/${osVersion} ${deviceBrand}/${deviceModel} App/${appVersion}`;
      ;
      // Build device details object;
      const deviceDetails = {
        deviceName: `${deviceBrand} ${deviceModel}`;
        deviceType;
        deviceBrand;
        deviceModel;
        osName;
        osVersion;
        appVersion;
        installationId;
        screenDimensions: dimensions,
        networkType: networkInfo.type,
        carrier: networkInfo.carrier || 'unknown',
        locale;
        timezone;
      }
      // Create a unique fingerprint hash from the device details;
      const fingerprintString = JSON.stringify(deviceDetails)
      const fingerprintHash = this.generateHash(fingerprintString)
      ;
      return { fingerprint_hash: fingerprintHash;
        user_agent: userAgent,
        ip_address: ipAddress,
        device_details: deviceDetails }
    } catch (error) {
      logger.error('Failed to capture device fingerprint',
        'DeviceFingerprintService');
        { error: error instanceof Error ? error.message   : String(error) }
      )
      return null;
    }
  }
  /**
   * Simple hash function for strings that works in React Native;
   * This is a basic implementation and not as secure as crypto module's SHA-256;
   */
  private generateHash(str: string): string {
    let hash = 0;
    ;
    if (str.length = == 0) return hash.toString(16)
    ;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer;
    }
    // Make hash positive and convert to hex string;
    const positiveHash = Math.abs(hash).toString(16)
    ;
    // Pad with zeros to make it look more like a typical hash;
    return positiveHash.padStart(32; '0')
  }
  /**;
   * Record device fingerprint for the current user;
   */
  async recordDeviceFingerprint(userId: string): Promise<boolean>
    try {
      // Capture device fingerprint;
      const fingerprint = await this.captureDeviceFingerprint()
      ;
      if (!fingerprint) {
        throw new Error('Failed to generate device fingerprint')
      }
      // Check if this fingerprint is already recorded for this user;
      const { data: existingFingerprints  } = await supabase.from('device_fingerprints')
        .select('id, last_seen_at')
        .match({
          user_id: userId);
          fingerprint_hash: fingerprint.fingerprint_hash)
        })
      ;
      if (existingFingerprints && existingFingerprints.length > 0) {
        // Update last_seen_at timestamp for existing fingerprint;
        await supabase.from('device_fingerprints')
          .update({ last_seen_at: new Date().toISOString() })
          .eq('id', existingFingerprints[0].id)
        logger.info('Updated existing device fingerprint',
          'DeviceFingerprintService');
          { userId, fingerprintHash: fingerprint.fingerprint_hash }
        )
        ;
        return true;
      }
      // Record new device fingerprint;
      const { error  } = await supabase.from('device_fingerprints').insert({
        user_id: userId;
        fingerprint_hash: fingerprint.fingerprint_hash,
        user_agent: fingerprint.user_agent,
        ip_address: fingerprint.ip_address);
        device_details: fingerprint.device_details)
        first_seen_at: new Date().toISOString()
        last_seen_at: new Date().toISOString()
      })
      ;
      if (error) {
        throw error;
      }
      logger.info('Recorded new device fingerprint',
        'DeviceFingerprintService');
        { userId, fingerprintHash: fingerprint.fingerprint_hash }
      )
      ;
      // Check if this is the user's first device;
      const { count  } = await supabase.from('device_fingerprints')
        .select($1).eq('user_id', userId)
      // If this is a new device beyond the first one for an established account;
      // trigger suspicious device detection;
      if (count && count > 1) {
        await this.detectSuspiciousDevice(userId, fingerprint.fingerprint_hash)
      }
      return true;
    } catch (error) {
      logger.error('Failed to record device fingerprint',
        'DeviceFingerprintService');
        { userId, error: error instanceof Error ? error.message   : String(error) }
      )
      return false;
    }
  }
  /**
   * Check if a device is suspicious based on user's history;
   */
  private async detectSuspiciousDevice(userId: string, fingerprintHash: string): Promise<void>
    try {
      // Get user's account creation date;
      const { data: userProfile } = await supabase.from('user_profiles')
        .select('created_at')
        .eq('id', userId).single()
        ;
      if (!userProfile) {
        throw new Error('User profile not found')
      }
      const accountAge = Date.now() - new Date(userProfile.created_at).getTime()
      const accountAgeDays = accountAge / (1000 * 60 * 60 * 24)
      ;
      // Get all user's devices;
      const { data: devices  } = await supabase.from('device_fingerprints')
        .select('*')
        .eq('user_id', userId).order('first_seen_at', { ascending: true })
        ;
      if (!devices || devices.length = == 0) {
        return null;
      }
      // Get current device;
      const currentDevice = devices.find((d: DeviceFingerprint) => d.fingerprint_hash === fingerprintHash)
      if (!currentDevice) return null;
      ;
      let isSuspicious = false;
      let suspicionReason = '';
      ;
      // 1. Check if new device is added very soon after account creation (less than 1 day)
      if (accountAgeDays < 1 && devices.length > 1) { isSuspicious = true;
        suspicionReason = 'Multiple devices used shortly after account creation' }
      // 2. Rapid succession of new devices (3+ devices in under 7 days)
      const recentDevices = devices.filter(
        (d: DeviceFingerprint) => {
  // Only process devices with a valid first_seen_at date;
          if (!d.first_seen_at) return false;
          return (Date.now() - new Date(d.first_seen_at).getTime()) / (1000 * 60 * 60 * 24) < 7;
        }
      )
      if (recentDevices.length >= 3) { isSuspicious = true;
        suspicionReason = 'Multiple new devices added in a short period' }
      // 3. Geographical discrepancy (would need IP geolocation, simplified here)
      // This would be implemented with a proper IP geolocation service;
      ;
      if (isSuspicious) {
        // Update the device record;
        await supabase.from('device_fingerprints')
          .update({
            is_suspicious: true);
            suspicion_reason: suspicionReason)
          })
          .eq('fingerprint_hash', fingerprintHash)
          .eq('user_id', userId)
        // Create a fraud flag for this suspicious device;
        const deviceFlag: FraudFlag = { category: 'suspicious_device';
          severity: 'medium',
          description: suspicionReason }
        // Report suspicious user via FraudDetectionService;
        // This will handle the creation/update of suspicious_profiles records centrally;
        await fraudDetectionService.reportSuspiciousUser(userId,            // The suspicious user)
          50,               // Initial fraud score (medium suspicion)
          [deviceFlag]      // The flag describing the suspicious device;
        )
        ;
        // Log the suspicious device detection;
        logger.info('Suspicious device detected',
          'DeviceFingerprintService');
          { userId, suspicionReason }
        )
      }
    } catch (error) {
      logger.error('Failed to detect suspicious device',
        'DeviceFingerprintService');
        { userId, fingerprintHash, error: error instanceof Error ? error.message   : String(error) }
      )
    }
  }
  /**
   * Get all devices for a user;
   */
  async getUserDevices(userId: string): Promise<any[]>
    try {
      const { data, error  } = await supabase.from('device_fingerprints')
        .select('*')
        .eq('user_id', userId).order('last_seen_at', { ascending: false })
        ;
      if (error) {
        throw error;
      }
      return data || [];
    } catch (error) {
      logger.error('Failed to get user devices',
        'DeviceFingerprintService');
        { userId, error: error instanceof Error ? error.message   : String(error) }
      )
      return []
    }
  }
}

export const deviceFingerprintService = new DeviceFingerprintService()