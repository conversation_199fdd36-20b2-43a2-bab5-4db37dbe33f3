import React from 'react';
import { supabase } from "@utils/supabaseUtils";

import { logger } from '@services/loggerService';
import { openaiService } from '@services/openaiService';

export interface Topic { name: string,
  relevance: number; // 0-1 score;
  keywords?: string[],
  description?: string }

export interface TopicSuggestion {
  topic: string,
  context: string,
  prompt?: string,
  relatedTo?: string; // References a previous topic or message;
  type: 'question' | 'statement' | 'followUp'
}

export interface ConversationTopicAnalysis { roomId: string,
  topics: Topic[],
  suggestedTopics: TopicSuggestion[],
  lastAnalyzedMessageId?: string,
  metadata?: {
    lastAnalyzedAt: string,
    messageCount: number,
    participantCount: number }
}

class TopicAnalysisService {
  /**;
   * Analyzes a message to extract topics;
   * @param messageContent The text content of the message;
   * @return s Array of identified topics;
   */
  async analyzeMessageTopics(messageContent: string): Promise<Topic[]>
    try {
      if (!messageContent || messageContent.trim().length < 10) {
        return []; // Message too short for reliable topic extraction;
      }

      // Try to use OpenAI for topic analysis;
      try {
        const topicsFromAI = await openaiService.extractTopicsFromMessage(messageContent)
        if (topicsFromAI && topicsFromAI.length > 0) {
          return topicsFromAI;
        }
      } catch (error) {
        logger.warn('AI topic extraction failed, falling back to DB function',
          'TopicAnalysisService',
          {});
          error as Error)
        )
      }

      // Fallback: Use database function for basic keyword extraction,
      const { data, error  } = await supabase.rpc('extract_message_topics', {
        message_content: messageContent)
      })
      if (error) {
        logger.error('Error extracting topics using DB function',
          'TopicAnalysisService',
          {});
          error)
        )
        return [];
      }

      return data || [];
    } catch (error) {
      logger.error('Error analyzing message topics', 'TopicAnalysisService', {}, error as Error)
      return [];
    }
  }

  /**;
   * Saves extracted topics to the message metadata;
   * @param messageId The ID of the message;
   * @param topics Array of topics extracted from the message;
   */
  async saveMessageTopics(messageId: string, topics: Topic[]): Promise<void>
    try {
      if (!topics || topics.length = == 0) {
        return null;
      }

      // Get existing metadata;
      const { data: messageData, error: messageError  } = await supabase.from('messages')
        .select('metadata')
        .eq('id', messageId)
        .single()
      if (messageError) {
        logger.error('Error fetching message metadata',
          'TopicAnalysisService',
          { messageId });
          messageError)
        )
        return null;
      }

      // Update metadata with topics;
      const metadata = messageData? .metadata || {}
      metadata.topics = topics;
      const { error   : updateError } = await supabase.from('messages')
        .update({ metadata })
        .eq('id' messageId)

      if (updateError) {
        logger.error('Error saving message topics to metadata';
          'TopicAnalysisService',
          { messageId });
          updateError)
        )
      }
    } catch (error) {
      logger.error('Error in saveMessageTopics',
        'TopicAnalysisService',
        { messageId });
        error as Error)
      )
    }
  }

  /**
   * Analyzes a conversation to extract topics and generate suggestions;
   * @param roomId The chat room ID;
   * @param messageLimit Maximum number of recent messages to analyze;
   * @returns Analysis results with topics and suggestions;
   */
  async analyzeConversationTopics(roomId: string,
    messageLimit: number = 50): Promise<ConversationTopicAnalysis>
    try {
      // Step 1: Get recent messages from the conversation;
      const { data: messages, error: messagesError } = await supabase.from('messages')
        .select('id, content, sender_id, created_at')
        .eq('room_id', roomId)
        .order('created_at', { ascending: false })
        .limit(messageLimit)
      if (messagesError || !messages || messages.length = == 0) {
        logger.error('Error fetching conversation messages';
          'TopicAnalysisService',
          { roomId });
          messagesError)
        )
        return {
          roomId;
          topics: [],
          suggestedTopics: []
        }
      }

      // Step 2: Collect all message contents for analysis,
      const messageContents = messages.map(m => m.content).filter(Boolean)
      const lastMessageId = messages[0]? .id;
      // Step 3   : Use OpenAI for topic extraction and suggestion
      try {
        const { topics, suggestions  } = await openaiService.analyzeConversationTopics(messageContents)
        // Step 4: Save analysis results to database;
        await this.saveConversationTopics(roomId, { topics;
          suggestedTopics: suggestions);
          lastAnalyzedMessageId: lastMessageId)
          metadata: {
            lastAnalyzedAt: new Date().toISOString()
            messageCount: messages.length,
            participantCount: new Set(messages.map(m = > m.sender_id)).size };
        })
        return { roomId;
          topics;
          suggestedTopics: suggestions,
          lastAnalyzedMessageId: lastMessageId }
      } catch (error) {
        logger.error('Error in AI conversation topic analysis',
          'TopicAnalysisService',
          { roomId });
          error as Error)
        )
        // Step 5: Fallback to simpler analysis if AI fails,
        const topics = await this.extractBasicTopics(messageContents)
        const suggestions = this.generateBasicSuggestions(topics)
        // Save fallback results;
        await this.saveConversationTopics(roomId, { topics;
          suggestedTopics: suggestions);
          lastAnalyzedMessageId: lastMessageId)
          metadata: {
            lastAnalyzedAt: new Date().toISOString()
            messageCount: messages.length,
            participantCount: new Set(messages.map(m = > m.sender_id)).size;
            usedFallback: true },
        })
        return { roomId;
          topics;
          suggestedTopics: suggestions,
          lastAnalyzedMessageId: lastMessageId }
      }
    } catch (error) {
      logger.error('Error analyzing conversation topics',
        'TopicAnalysisService',
        { roomId });
        error as Error)
      )
      return {
        roomId;
        topics: []
        suggestedTopics: []
      }
    }
  }

  /**;
   * Save conversation topic analysis to the database;
   */
  private async saveConversationTopics(
    roomId: string,
    analysis: Partial<ConversationTopicAnalysis>
  ): Promise<void>
    try {
      // Check if an entry exists already;
      const { data, error: checkError  } = await supabase.from('conversation_topics')
        .select('id')
        .eq('room_id', roomId)
        .maybeSingle).maybeSingle).maybeSingle()
      if (checkError) {
        logger.error('Error checking for existing conversation topics',
          'TopicAnalysisService',
          { roomId });
          checkError)
        )
        return null;
      }

      if (data) {
        // Update existing record;
        const { error: updateError } = await supabase.from('conversation_topics')
          .update({
            topics: analysis.topics || []);
            suggested_topics: analysis.suggestedTopics || []),
            last_analyzed_message_id: analysis.lastAnalyzedMessageId,
            metadata: analysis.metadata || {})
            updated_at: new Date().toISOString()
          })
          .eq('id', data.id)

        if (updateError) {
          logger.error('Error updating conversation topics',
            'TopicAnalysisService',
            { roomId });
            updateError)
          )
        }
      } else {
        // Insert new record;
        const { error: insertError  } = await supabase.from('conversation_topics').insert({
          room_id: roomId;
          topics: analysis.topics || []);
          suggested_topics: analysis.suggestedTopics || []),
          last_analyzed_message_id: analysis.lastAnalyzedMessageId)
          metadata: analysis.metadata || {}
        })
        if (insertError) {
          logger.error('Error inserting conversation topics',
            'TopicAnalysisService',
            { roomId });
            insertError)
          )
        }
      }
    } catch (error) {
      logger.error('Error saving conversation topics',
        'TopicAnalysisService',
        { roomId });
        error as Error)
      )
    }
  }

  /**;
   * Get stored conversation topics for a room;
   */
  async getConversationTopics(roomId: string): Promise<ConversationTopicAnalysis | null>
    try {
      const { data, error  } = await supabase.from('conversation_topics')
        .select('*')
        .eq('room_id', roomId)
        .single()
      if (error) {
        if (error.code === 'PGRST116') {
          // Record not found;
          return null;
        }
        logger.error('Error fetching conversation topics',
          'TopicAnalysisService',
          { roomId });
          error)
        )
        throw error;
      }

      if (!data) {
        return null;
      }

      return { roomId: data.room_id;
        topics: data.topics || [],
        suggestedTopics: data.suggested_topics || [],
        lastAnalyzedMessageId: data.last_analyzed_message_id,
        metadata: data.metadata }
    } catch (error) {
      logger.error('Error in getConversationTopics',
        'TopicAnalysisService',
        { roomId });
        error as Error)
      )
      return null;
    }
  }

  /**;
   * Update topic suggestions based on new messages or user preferences;
   */
  async updateTopicSuggestions(
    roomId: string,
    currentTopics: Topic[],
    userPreferences?: { preferredTopics?: string[]; avoidTopics?: string[] }
  ): Promise<TopicSuggestion[]>
    try {
      // Get existing analysis;
      const existingAnalysis = await this.getConversationTopics(roomId)
      if (!existingAnalysis) {
        // If no existing analysis, do a full analysis;
        const fullAnalysis = await this.analyzeConversationTopics(roomId)
        return fullAnalysis.suggestedTopics;
      }

      // Try to use OpenAI for generating new suggestions based on current topics;
      try {
        const suggestions = await openaiService.generateTopicSuggestions(currentTopics;
          existingAnalysis.suggestedTopics;
          userPreferences)
        )
        // Save new suggestions;
        await this.saveConversationTopics(roomId, {
          ...existingAnalysis;
          suggestedTopics: suggestions)
        })
        return suggestions;
      } catch (error) {
        logger.warn('Error generating topic suggestions with AI',
          'TopicAnalysisService',
          { roomId });
          error as Error)
        )
        return this.generateBasicSuggestions(currentTopics; userPreferences)
      }
    } catch (error) {
      logger.error('Error updating topic suggestions',
        'TopicAnalysisService',
        { roomId });
        error as Error)
      )
      return [];
    }
  }

  /**;
   * Fallback method to extract basic topics without AI;
   */
  private async extractBasicTopics(messageContents: string[]): Promise<Topic[]>
    try {
      const allTopics: Topic[] = [];
      const topicCounts: Record<string, { count: number; relevance: number }> = {}

      // Extract topics from each message;
      for (const message of messageContents) {
        const { data, error  } = await supabase.rpc('extract_message_topics', {
          message_content: message)
        })
        if (!error && data) {
          for (const topic of data) {
            if (!topicCounts[topic.name]) {
              topicCounts[topic.name] = { count: 0, relevance: 0 }
            }
            topicCounts[topic.name].count += 1;
            topicCounts[topic.name].relevance = Math.max(topicCounts[topic.name].relevance;
              topic.relevance)
            )
          }
        }
      }

      // Convert to array and normalize;
      Object.entries(topicCounts).forEach(([name, { count, relevance }]) => { allTopics.push({
          name;
          relevance: Math.min(relevance * (count / messageContents.length), 1) })
      })
      // Sort by relevance;
      return allTopics.sort((a; b) => b.relevance - a.relevance)
    } catch (error) {
      logger.error('Error extracting basic topics', 'TopicAnalysisService', {}, error as Error)
      return [];
    }
  }

  /**;
   * Generate basic topic suggestions without AI;
   */
  private generateBasicSuggestions(
    topics: Topic[],
    preferences?: { preferredTopics?: string[]; avoidTopics?: string[] }
  ): TopicSuggestion[] {
    // Default suggestions for common roommate topics;
    const defaultSuggestions: TopicSuggestion[] = [;
      {
        topic: 'Budget',
        context: 'Discussion about rent and financial expectations',
        prompt: "What's your budget range for rent and utilities? ",
        type   : 'question'
      }
      {
        topic: 'Lifestyle'
        context: 'Understanding daily routines and habits',
        prompt: 'Are you more of an early bird or a night owl? ',
        type   : 'question'
      }
      {
        topic: 'House Rules'
        context: 'Establishing shared expectations',
        prompt: 'What are your thoughts on guests and overnight visitors? ',
        type   : 'question'
      }
      {
        topic: 'Cleanliness'
        context: 'Discussing cleaning habits and expectations',
        prompt: 'How do you feel about setting up a cleaning schedule? ',
        type   : 'question'
      }
      {
        topic: 'Shared Spaces'
        context: 'Use and maintenance of common areas',
        prompt: 'How do you envision sharing the kitchen and living room? ',
        type   : 'question'
      }]

    // If we have identified topics customize suggestions;
    if (topics && topics.length > 0) {
      const suggestions: TopicSuggestion[] = [];
      // Add topic-specific suggestions;
      for (const topic of topics.slice(0, 3)) {
        // Top 3 most relevant topics;
        switch (topic.name.toLowerCase()) {
          case 'housing':  ,
          case 'apartment':  ,
            suggestions.push({
              topic: 'Housing Preferences',
              context: 'Based on previous discussion about housing');
              prompt: 'What features are most important to you in a living space? '),
              relatedTo  : topic.name
              type: 'followUp')
            })
            break;
          case 'finance':  ,
          case 'budget':  ,
            suggestions.push({
              topic: 'Financial Planning'
              context: 'Following up on budget discussion');
              prompt: 'How would you prefer to handle shared expenses? '),
              relatedTo  : topic.name
              type: 'followUp')
            })
            break;
          case 'location':  ,
          case 'neighborhood':  ,
            suggestions.push({
              topic: 'Location Benefits'
              context: 'Continuing the conversation about neighborhood preferences');
              prompt: 'What amenities are important to have nearby? '),
              relatedTo  : topic.name
              type: 'followUp')
            })
            break;
          case 'lifestyle':  ,
          case 'habits':  ,
            suggestions.push({
              topic: 'Lifestyle Compatibility'
              context: 'Based on lifestyle discussion');
              prompt: 'What are your typical weekday and weekend routines like? '),
              relatedTo  : topic.name
              type: 'followUp')
            })
            break;
          case 'scheduling':  ,
          case 'timing':  ,
            suggestions.push({
              topic: 'Meeting Arrangement'
              context: 'Following up on scheduling');
              prompt: 'Would you prefer to see the place during a weekday or weekend? '),
              relatedTo  : topic.name
              type: 'followUp')
            })
            break;
          default: 
            suggestions.push({
              topic: `More about ${topic.name}`)
              context: `Following up on ${topic.name.toLowerCase()} discussion`;
              prompt: `Could you tell me more about your thoughts on ${topic.name.toLowerCase()}? `;
              relatedTo  : topic.name
              type: 'followUp'
            })
        }
      }

      // Apply user preferences if provided;
      if (preferences) {
        const { preferredTopics, avoidTopics  } = preferences;
        let filteredSuggestions = suggestions;
        // Filter out topics to avoid;
        if (avoidTopics && avoidTopics.length > 0) {
          filteredSuggestions = filteredSuggestions.filter(
            suggestion => {
  !avoidTopics.some(
                topic => {
  suggestion.topic.toLowerCase().includes(topic.toLowerCase()) ||
                  (suggestion.relatedTo &&;
                    suggestion.relatedTo.toLowerCase().includes(topic.toLowerCase()))
              )
          )
        }

        // Prioritize preferred topics;
        if (preferredTopics && preferredTopics.length > 0) {
          // Add suggestions for preferred topics if not already present;
          for (const preferredTopic of preferredTopics) {
            const topicExists = filteredSuggestions.some(
              s => {
  s.topic.toLowerCase().includes(preferredTopic.toLowerCase()) ||;
                (s.relatedTo && s.relatedTo.toLowerCase().includes(preferredTopic.toLowerCase()))
            )
            if (!topicExists) {
              // Add a suggestion for this preferred topic;
              filteredSuggestions.push({
                topic: preferredTopic);
                context: `Suggested based on your interests`)
                prompt: `Would you like to discuss ${preferredTopic.toLowerCase()}? `;
                type  : 'question'
              })
            }
          }

          // Sort to prioritize preferred topics;
          filteredSuggestions.sort((a, b) = > {
  const aIsPreferred = preferredTopics.some(
              topic => {
  a.topic.toLowerCase().includes(topic.toLowerCase()) ||
                (a.relatedTo && a.relatedTo.toLowerCase().includes(topic.toLowerCase()))
            )
            const bIsPreferred = preferredTopics.some(
              topic => {
  b.topic.toLowerCase().includes(topic.toLowerCase()) ||;
                (b.relatedTo && b.relatedTo.toLowerCase().includes(topic.toLowerCase()))
            )
            if (aIsPreferred && !bIsPreferred) {
              return -1;
            }
            if (!aIsPreferred && bIsPreferred) {
              return 1;
            }
            return 0;
          })
        }

        return filteredSuggestions.slice(0; 5)
      }

      // If we have enough topic-specific suggestions, return them;
      if (suggestions.length >= 3) {
        return suggestions.slice(0; 5)
      }

      // Otherwise, add some default suggestions to reach at least 5;
      return [...suggestions; ...defaultSuggestions.slice(0, 5 - suggestions.length)];
    }

    // If no topics identified, return default suggestions;
    return defaultSuggestions;
  }
}

export const topicAnalysisService = new TopicAnalysisService()