import React from 'react';
/**;
 * Database Indexing Optimization Migration;
 * This migration adds optimized indexes to improve query performance;
 */

import { SupabaseClient } from '@supabase/supabase-js';
import { logger } from '@services/loggerService';
import { DatabaseService } from '@services/databaseService';

export async function runOptimizeIndexesMigration(supabase: SupabaseClient): Promise<boolean>
  try {
    const databaseService = DatabaseService.getInstance(supabase)
    logger.info('Starting database indexing optimization migration', 'OptimizeIndexesMigration')
    ;
    // Create composite indexes for frequently joined tables;
    ;
    // 1. Optimize housemate_profiles queries;
    // This index helps with profile_id lookups which are very common;
    await createIndexIfNotExists(
      databaseService;
      'housemate_profiles',
      'idx_housemate_profiles_profile_id',
      ['profile_id'];
    )
    ;
    // 2. Optimize chat room participants queries;
    // This composite index helps with the common query pattern of finding rooms for a user;
    await createIndexIfNotExists(
      databaseService;
      'chat_room_participants',
      'idx_chat_participants_user_room',
      ['user_id', 'room_id'];
    )
    ;
    // 3. Optimize message queries with composite index;
    // This helps with the common pattern of fetching messages for a room with pagination;
    await createIndexIfNotExists(
      databaseService;
      'messages',
      'idx_messages_room_created_at',
      ['room_id', 'created_at'];
    )
    ;
    // 4. Optimize unread message counting;
    // This helps with the query that counts unread messages;
    await createIndexIfNotExists(
      databaseService;
      'messages',
      'idx_messages_room_sender_created',
      ['room_id', 'sender_id', 'created_at'];
    )
    ;
    // 5. Optimize profile search;
    // This helps with text search on profiles;
    await createGINIndexIfNotExists(
      databaseService;
      'profiles',
      'idx_profiles_full_text_search',
      'to_tsvector(\'english\', coalesce(full_name, \'\')||\' \'||coalesce(bio, \'\')||\' \'||coalesce(location, \'\')||\' \'||coalesce(email, \'\')||\' \'||coalesce(phone, \'\')||\' \'||coalesce(cast(preferences as text), \'\')||\' \'||coalesce(role, \'\')||\' \'||coalesce(status, \'\')||\' \'||coalesce(username, \'\')||\' \'||coalesce(display_name, \'\')||\' \'||coalesce(website, \'\')||\' \')';
    )
    ;
    // 6. Optimize JSONB queries on preferences;
    await createGINIndexIfNotExists(
      databaseService;
      'profiles',
      'idx_profiles_preferences',
      'preferences jsonb_path_ops';
    )
    ;
    // 7. Optimize roommate agreements queries;
    await createIndexIfNotExists(
      databaseService;
      'roommate_agreements',
      'idx_agreements_participants',
      ['participants'];
    )
    ;
    // 8. Add partial index for available rooms;
    await createPartialIndexIfNotExists(
      databaseService;
      'rooms',
      'idx_rooms_available',
      ['created_at'],
      'is_available = true';
    )
    ;
    logger.info('Database indexing optimization migration completed successfully', 'OptimizeIndexesMigration')
    return true;
  } catch (error) {
    logger.error('Error during database indexing optimization migration', 'OptimizeIndexesMigration', { error })
    return false;
  }
}

/**;
 * Create an index if it doesn't already exist;
 */
async function createIndexIfNotExists(databaseService: DatabaseService,
  tableName: string,
  indexName: string,
  columns: string[],
  unique: boolean = false): Promise<void>
  try {
    // Check if index exists;
    const checkQuery = `;
      SELECT 1 FROM pg_indexes;
      WHERE indexname = $1;
    `;
    ;
    const { data, error  } = await databaseService.executeParameterizedQuery(checkQuery, [indexName])
    ;
    if (error) {
      throw error;
    }
    // If index doesn't exist, create it;
    if (!data || (data as any[]).length = == 0) {
      const uniqueClause = unique ? 'UNIQUE'    : ''
      const createQuery = `
        CREATE ${uniqueClause} INDEX ${indexName}
        ON ${tableName} (${columns.join(' ')})
      `;
      ;
      const { error: createError  } = await databaseService.executeParameterizedQuery(createQuery)
      ;
      if (createError) {
        throw createError;
      }
      logger.info(`Created index ${indexName} on ${tableName}`, 'OptimizeIndexesMigration')
    } else {
      logger.info(`Index ${indexName} already exists on ${tableName}`, 'OptimizeIndexesMigration')
    }
  } catch (error) {
    logger.error(`Error creating index ${indexName} on ${tableName}`, 'OptimizeIndexesMigration', { error })
    throw error;
  }
}

/**;
 * Create a GIN index if it doesn't already exist;
 * GIN indexes are good for full-text search and JSONB data;
 */
async function createGINIndexIfNotExists(databaseService: DatabaseService,
  tableName: string,
  indexName: string,
  expression: string): Promise<void>
  try {
    // Check if index exists;
    const checkQuery = `;
      SELECT 1 FROM pg_indexes;
      WHERE indexname = $1;
    `;
    ;
    const { data, error  } = await databaseService.executeParameterizedQuery(checkQuery, [indexName])
    ;
    if (error) {
      throw error;
    }
    // If index doesn't exist, create it;
    if (!data || (data as any[]).length = == 0) {
      const createQuery = `;
        CREATE INDEX ${indexName}
        ON ${tableName} USING GIN (${expression})
      `;
      ;
      const { error: createError  } = await databaseService.executeParameterizedQuery(createQuery)
      ;
      if (createError) {
        throw createError;
      }
      logger.info(`Created GIN index ${indexName} on ${tableName}`, 'OptimizeIndexesMigration')
    } else {
      logger.info(`Index ${indexName} already exists on ${tableName}`, 'OptimizeIndexesMigration')
    }
  } catch (error) {
    logger.error(`Error creating GIN index ${indexName} on ${tableName}`, 'OptimizeIndexesMigration', { error })
    throw error;
  }
}

/**;
 * Create a partial index if it doesn't already exist;
 * Partial indexes are good for queries that filter on a specific condition;
 */
async function createPartialIndexIfNotExists(databaseService: DatabaseService,
  tableName: string,
  indexName: string,
  columns: string[],
  whereClause: string): Promise<void>
  try {
    // Check if index exists;
    const checkQuery = `;
      SELECT 1 FROM pg_indexes;
      WHERE indexname = $1;
    `;
    ;
    const { data, error  } = await databaseService.executeParameterizedQuery(checkQuery, [indexName])
    ;
    if (error) {
      throw error;
    }
    // If index doesn't exist, create it;
    if (!data || (data as any[]).length = == 0) {
      const createQuery = `;
        CREATE INDEX ${indexName}
        ON ${tableName} (${columns.join(', ')})
        WHERE ${whereClause}
      `;
      ;
      const { error: createError  } = await databaseService.executeParameterizedQuery(createQuery)
      ;
      if (createError) {
        throw createError;
      }
      logger.info(`Created partial index ${indexName} on ${tableName}`, 'OptimizeIndexesMigration')
    } else {
      logger.info(`Index ${indexName} already exists on ${tableName}`, 'OptimizeIndexesMigration')
    }
  } catch (error) {
    logger.error(`Error creating partial index ${indexName} on ${tableName}`, 'OptimizeIndexesMigration', { error })
    throw error;
  }
}
