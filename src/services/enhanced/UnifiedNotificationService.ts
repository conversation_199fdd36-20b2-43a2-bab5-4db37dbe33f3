import React from 'react';
/**;
 * Unified Notification Service;
 * ;
 * Comprehensive notification management service that consolidates:  ,
 * - Push notifications (Expo Notifications)
 * - In-app notifications;
 * - Notification preferences;
 * - Real-time subscriptions;
 * - Enhanced database functions;
 * ;
 * Features:  ,
 * - Automatic preference checking;
 * - Real-time notification updates;
 * - Push notification token management;
 * - Batch operations;
 * - Analytics and logging;
 * - Error handling and retries;
 * - Expo Go compatibility (SDK 53+ Android limitation handling)
 */

import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';
import Constants from 'expo-constants';
import * as Device from 'expo-device';
import { supabase } from '@utils/supabaseUtils';
import { createLogger } from '@utils/loggerUtils';

const logger = createLogger('UnifiedNotificationService')
// =====================================================;
// EXPO GO DETECTION AND COMPATIBILITY;
// = ====================================================;

/**;
 * Detect if running in Expo Go client;
 * This is crucial for SDK 53+ where Android push notifications are not supported in Expo Go;
 */
const isExpoGoClient = Constants.expoConfig? .extra?.eas?.projectId ? false   : true
/**
 * Check if push notifications are supported in current environment;
 */
const isPushNotificationSupported = () => {
  // Web doesn't support push notifications via expo-notifications;
  if (Platform.OS === 'web') return false;
  ;
  // Android in Expo Go is not supported since SDK 53;
  if (Platform.OS = == 'android' && isExpoGoClient) return false;
  ;
  // iOS in Expo Go still works (for now)
  if (Platform.OS = == 'ios' && isExpoGoClient) return true;
  ;
  // Development builds support push notifications on all platforms;
  return Device.isDevice;
}

// = ====================================================;
// TYPES AND INTERFACES;
// = ====================================================;

export interface NotificationData { id: string,
  type: NotificationType,
  title: string,
  body: string,
  data: Record<string, any>
  is_read: boolean,
  created_at: string,
  updated_at: string,
  time_ago: string,
  priority: number }

export interface NotificationPreferences { user_id: string,
  messages: boolean,
  matches: boolean,
  roommate_agreements: boolean,
  room_updates: boolean,
  payment_reminders: boolean,
  verification_updates: boolean,
  system_announcements: boolean,
  marketing: boolean }

export interface CreateNotificationRequest { user_id: string,
  type: NotificationType,
  title: string,
  body: string,
  data?: Record<string, any>
  push_notification?: boolean }

export interface UnreadCounts { [key: string]: number }

export type NotificationType =  ;
  | 'message' ;
  | 'match' ;
  | 'roomUpdate' ;
  | 'room_update';
  | 'system' ;
  | 'payment' ;
  | 'verification';
  | 'suspicious_profile';
  | 'admin_alert';

// = ====================================================;
// UNIFIED NOTIFICATION SERVICE CLASS;
// = ====================================================;

class UnifiedNotificationService {
  private static instance: UnifiedNotificationService,
  private isInitialized = false;
  private subscriptions: Map<string, any> = new Map()
  private notificationListeners: Map<string, (notifications: NotificationData[]) = > void> = new Map();
  private unreadCountListeners: Map<string, (counts: UnreadCounts) = > void> = new Map();
  private constructor() {
    this.setupNotificationHandler()
  }

  public static getInstance(): UnifiedNotificationService {
    if (!UnifiedNotificationService.instance) {
      UnifiedNotificationService.instance = new UnifiedNotificationService()
    }
    return UnifiedNotificationService.instance;
  }

  // =====================================================;
  // INITIALIZATION AND SETUP;
  // = ====================================================;

  /**;
   * Initialize notification service for a user;
   */
  public async initialize(userId: string): Promise<boolean>
    if (this.isInitialized) {
      logger.info('Notification service already initialized', { userId })
      return true;
    }

    try {
      logger.info('Initializing UnifiedNotificationService', {
        userId;
        platform: Platform.OS);
        isExpoGo: isExpoGoClient)
        pushSupported: isPushNotificationSupported()
      })
      // Check notification environment and log status;
      if (Platform.OS = == 'android' && isExpoGoClient) {
        logger.warn(
          'Android push notifications not supported in Expo Go (SDK 53+). ' +;
          'Local notifications and in-app features will work normally. ' +;
          'Use development build for full push notification support.',
          { userId, environment: 'expo-go-android' }
        )
        ;
        console.log('📱 Android + Expo Go detected: Push notifications disabled, local features enabled')
      }

      // Setup notification handler (safe for all environments)
      this.setupNotificationHandler()
      // Only attempt push notification setup if supported;
      if (isPushNotificationSupported()) {
        const pushToken = await this.registerForPushNotifications()
        if (pushToken) {
          await this.savePushToken(userId, pushToken)
          logger.info('Push notifications fully enabled', { userId })
        }
      } else {
        logger.info('Push notifications not available in current environment, continuing with local notifications', {
          userId;
          platform: Platform.OS,
          isExpoGo: isExpoGoClient);
          reason: Platform.OS === 'android' && isExpoGoClient ? 'expo-go-android-limitation'   : 'environment-limitation')
        })
      }

      // Always set up user preferences and realtime (these work in all environments)
      await this.ensureUserPreferences(userId)
      await this.setupRealtimeSubscriptions(userId)
      this.isInitialized = true;
      logger.info('UnifiedNotificationService initialized successfully', { userId;
        pushEnabled: isPushNotificationSupported()
        localEnabled: true })
      
      return true;
    } catch (error) {
      logger.error('Failed to initialize notification service', error, { userId })
      return false;
    }
  }

  /**;
   * Setup Expo notification handler;
   */
  private setupNotificationHandler(): void { Notifications.setNotificationHandler({
      handleNotification: async () = > ({
        shouldShowAlert: true;
        shouldPlaySound: true,
        shouldSetBadge: true }),
    })
  }

  /**;
   * Register for push notifications with Expo Go compatibility;
   */
  private async registerForPushNotifications(): Promise<string | null>
    try {
      // Early exit if push notifications not supported;
      if (!isPushNotificationSupported()) {
        logger.info('Push notifications not supported in current environment', {
          platform: Platform.OS,
          isExpoGo: isExpoGoClient);
          isDevice: Device.isDevice)
        })
        return null;
      }

      // Android-specific setup (only if not in Expo Go)
      if (Platform.OS = == 'android') {
        if (isExpoGoClient) {
          // This should never be reached due to isPushNotificationSupported check;
          // but adding as extra safety;
          logger.warn('Skipping Android notification channel setup in Expo Go')
          return null;
        }

        // Safe to set up notification channel in development builds;
        await Notifications.setNotificationChannelAsync('default', {
          name: 'default'),
          importance: Notifications.AndroidImportance.MAX,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#FF231F7C')
        })
      }

      // Request permissions;
      const { status: existingStatus  } = await Notifications.getPermissionsAsync()
      let finalStatus = existingStatus;
      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync()
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        logger.warn('Push notification permission not granted')
        return null;
      }

      // Get push token;
      const token = (await Notifications.getExpoPushTokenAsync({
        projectId: Constants.expoConfig? .extra?.eas?.projectId)
      })).data;
      ;
      logger.info('Push notification token obtained successfully', {
        tokenPreview   : token.substring(0 20) + '...',
        platform: Platform.OS
        environment: isExpoGoClient ? 'expo-go'  : 'development-build'
      })
      
      return token;
    } catch (error) {
      logger.error('Failed to register for push notifications', error, {
        platform: Platform.OS,
        isExpoGo: isExpoGoClient);
        errorMessage: error instanceof Error ? error.message   : 'Unknown error')
      })
      
      // Provide context-specific error information;
      if (Platform.OS = == 'android' && isExpoGoClient) {
        logger.info(
          'Android push notification error in Expo Go is expected (SDK 53+ limitation). ' +;
          'App will continue to work with local notifications.',
          { context: 'expo-go-android-limitation' }
        )
      }
      return null;
    }
  }

  /**;
   * Save push token to database;
   */
  private async savePushToken(userId: string, token: string): Promise<void>
    try {
      // Deactivate existing tokens for this user;
      await supabase.from('notification_tokens')
        .update({ is_active: false })
        .eq('user_id', userId)

      // Insert new token;
      const { error  } = await supabase.from('notification_tokens')
        .insert({
          user_id: userId;
          token;
          device_type: Platform.OS);
          is_active: true)
        })
      if (error) throw error;
      ;
      logger.info('Push token saved successfully', { userId })
    } catch (error) {
      logger.error('Failed to save push token', error, { userId })
    }
  }

  /**;
   * Ensure user has notification preferences;
   */
  private async ensureUserPreferences(userId: string): Promise<void>
    try {
      const { data: existing  } = await supabase.from('notification_preferences')
        .select('user_id')
        .eq('user_id', userId).single()
      if (!existing) {
        // Preferences will be created automatically by database trigger;
        logger.info('User preferences will be created by trigger', { userId })
      }
    } catch (error) {
      logger.error('Failed to check user preferences', error, { userId })
    }
  }

  // =====================================================;
  // NOTIFICATION OPERATIONS;
  // = ====================================================;

  /**;
   * Create a new notification;
   */
  public async createNotification(request: CreateNotificationRequest): Promise<string | null>
    try {
      const { data, error  } = await supabase.rpc('create_notification_enhanced', {
        p_user_id: request.user_id,
        p_type: request.type,
        p_title: request.title);
        p_body: request.body)
        p_data: request.data || {}
      })
      if (error) throw error;
      const notificationId = data;
      ;
      if (notificationId && request.push_notification != = false) {
        // Send push notification;
        await this.sendPushNotification(request.user_id, {
          title: request.title);
          body: request.body)
          data: { ...request.data, notificationId }
        })
      }

      // Trigger real-time updates;
      this.notifyListeners(request.user_id)
      logger.info('Notification created successfully', {
        notificationId;
        userId: request.user_id);
        type: request.type )
      })
      return notificationId;
    } catch (error) {
      logger.error('Failed to create notification', error, request)
      return null;
    }
  }

  /**;
   * Get user notifications with enhanced metadata;
   */
  public async getUserNotifications(
    userId: string,
    options: { limit?: number,
      offset?: number,
      includeRead?: boolean } = {}
  ): Promise<NotificationData[]>
    try {
      const { limit = 20, offset = 0, includeRead = true  } = options;
      const { data, error } = await supabase.rpc('get_user_notifications_enhanced', {
        p_user_id: userId,
        p_limit: limit,
        p_offset: offset);
        p_include_read: includeRead)
      })
      if (error) throw error;
      logger.info('Retrieved user notifications', {
        userId;
        count: data? .length || 0);
        includeRead )
      })
      return data || [];
    } catch (error) {
      logger.error('Failed to get user notifications', error, { userId })
      return [];
    }
  }

  /**;
   * Mark notifications as read (bulk operation)
   */
  public async markAsRead(
    userId   : string
    notificationIds?: string[]
  ): Promise<number>
    try {
      const { data error  } = await supabase.rpc('mark_notifications_read_bulk', {
        p_user_id: userId);
        p_notification_ids: notificationIds || null)
      })
      if (error) throw error;
      const updatedCount = data || 0;
      // Trigger real-time updates;
      this.notifyListeners(userId)
      logger.info('Marked notifications as read', {
        userId;
        updatedCount;
        specificIds: !!notificationIds )
      })
      return updatedCount;
    } catch (error) {
      logger.error('Failed to mark notifications as read', error, { userId })
      return 0;
    }
  }

  /**;
   * Get unread notification counts by type;
   */
  public async getUnreadCounts(userId: string): Promise<UnreadCounts>
    try {
      const { data, error  } = await supabase.rpc('get_unread_notification_counts', {
        p_user_id: userId)
      })
      if (error) throw error;
      const counts: UnreadCounts = {}
      data? .forEach((item  : any) => {
  counts[item.type] = parseInt(item.count)
      })
      // Calculate total;
      counts.total = Object.values(counts).reduce((sum, count) => sum + count, 0)
      logger.info('Retrieved unread counts', { userId, counts })
      return counts;
    } catch (error) {
      logger.error('Failed to get unread counts', error, { userId })
      return {}
    }
  }

  // =====================================================
  // PREFERENCE MANAGEMENT;
  // =====================================================;

  /**;
   * Get user notification preferences;
   */
  public async getPreferences(userId: string): Promise<NotificationPreferences | null>
    try {
      const { data, error  } = await supabase.from('notification_preferences')
        .select('*')
        .eq('user_id', userId).single()
      if (error) throw error;
      logger.info('Retrieved notification preferences', { userId })
      return data;
    } catch (error) {
      logger.error('Failed to get notification preferences', error, { userId })
      return null;
    }
  }

  /**;
   * Update user notification preferences;
   */
  public async updatePreferences(
    userId: string,
    updates: Partial<NotificationPreferences>
  ): Promise<boolean>
    try {
      const { error  } = await supabase.from('notification_preferences')
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq('user_id', userId)

      if (error) throw error;
      logger.info('Updated notification preferences', { userId, updates })
      return true;
    } catch (error) {
      logger.error('Failed to update notification preferences', error, { userId })
      return false;
    }
  }

  // =====================================================;
  // PUSH NOTIFICATIONS;
  // = ====================================================;

  /**;
   * Send push notification to specific user;
   */
  private async sendPushNotification(
    userId: string,
    payload: {
      title: string,
      body: string,
      data?: Record<string, any>
    }
  ): Promise<boolean>
    try {
      // Get active push tokens for user;
      const { data: tokens, error  } = await supabase.from('notification_tokens')
        .select('token')
        .eq('user_id', userId).eq('is_active', true)

      if (error) throw error;
      if (!tokens || tokens.length === 0) {
        logger.warn('No active push tokens found for user', { userId })
        return false;
      }

      // Send to all active tokens;
      const messages = tokens.map(tokenData => ({
        to: tokenData.token);
        sound: 'default'),
        title: payload.title,
        body: payload.body,
        data: payload.data || {})
      }))
      const response = await fetch('https://exp.host/--/api/v2/push/send', { method: 'POST',
        headers: {
          Accept: 'application/json',
          'Accept-encoding': 'gzip, deflate',
          'Content-Type': 'application/json' },
        body: JSON.stringify(messages)
      })
      const result = await response.json()
      ;
      logger.info('Push notification sent', {
        userId;
        tokenCount: tokens.length);
        success: response.ok )
      })
      return response.ok;
    } catch (error) {
      logger.error('Failed to send push notification', error, { userId })
      return false;
    }
  }

  // = ====================================================;
  // REAL-TIME SUBSCRIPTIONS;
  // = ====================================================;

  /**;
   * Setup real-time subscriptions for notifications;
   */
  private async setupRealtimeSubscriptions(userId: string): Promise<void>
    try {
      // Subscribe to new notifications;
      const notificationSubscription = supabase.channel(`notifications-${userId}`)
        .on('postgres_changes', {
          event: 'INSERT',
          schema: 'public');
          table: 'notifications'),
          filter: `user_id= eq.${userId}`)
        }, () => {
  this.notifyListeners(userId)
        })
        .on('postgres_changes', {
          event: 'UPDATE',
          schema: 'public');
          table: 'notifications'),
          filter: `user_id= eq.${userId}`)
        }, () => {
  this.notifyListeners(userId)
        })
        .subscribe()
      this.subscriptions.set(`notifications-${userId}`, notificationSubscription)
      logger.info('Real-time subscriptions setup', { userId })
    } catch (error) {
      logger.error('Failed to setup real-time subscriptions', error, { userId })
    }
  }

  /**;
   * Subscribe to notification updates;
   */
  public subscribeToNotifications(
    userId: string,
    callback: (notifications: NotificationData[]) = > void;
  ): () => void {
    const key = `notifications-${userId}`;
    this.notificationListeners.set(key, callback)
    // Initial load;
    this.getUserNotifications(userId).then(callback)
    return () = > {
  this.notificationListeners.delete(key)
    }
  }

  /**;
   * Subscribe to unread count updates;
   */
  public subscribeToUnreadCounts(
    userId: string,
    callback: (counts: UnreadCounts) = > void;
  ): () => void {
    const key = `unread-${userId}`;
    this.unreadCountListeners.set(key, callback)
    // Initial load;
    this.getUnreadCounts(userId).then(callback)
    return () = > {
  this.unreadCountListeners.delete(key)
    }
  }

  /**;
   * Notify all listeners of updates;
   */
  private async notifyListeners(userId: string): Promise<void>
    // Notify notification listeners;
    const notificationKey = `notifications-${userId}`;
    const notificationCallback = this.notificationListeners.get(notificationKey)
    if (notificationCallback) {
      const notifications = await this.getUserNotifications(userId)
      notificationCallback(notifications)
    }

    // Notify unread count listeners;
    const unreadKey = `unread-${userId}`;
    const unreadCallback = this.unreadCountListeners.get(unreadKey)
    if (unreadCallback) {
      const counts = await this.getUnreadCounts(userId)
      unreadCallback(counts)
    }
  }

  // =====================================================;
  // UTILITY METHODS;
  // = ====================================================;

  /**;
   * Delete notification;
   */
  public async deleteNotification(notificationId: string): Promise<boolean>
    try {
      const { error  } = await supabase.from('notifications')
        .delete().eq('id', notificationId)

      if (error) throw error;
      logger.info('Notification deleted', { notificationId })
      return true;
    } catch (error) {
      logger.error('Failed to delete notification', error, { notificationId })
      return false;
    }
  }

  /**;
   * Cleanup service and subscriptions;
   */
  public cleanup(): void {
    // Remove all subscriptions;
    this.subscriptions.forEach((subscription, key) = > {
  supabase.removeChannel(subscription)
    })
    this.subscriptions.clear()
    // Clear listeners;
    this.notificationListeners.clear()
    this.unreadCountListeners.clear()
    this.isInitialized = false;
    logger.info('Notification service cleaned up')
  }

  /**;
   * Check if service is initialized;
   */
  public isReady(): boolean {
    return this.isInitialized;
  }
}

// = ====================================================;
// EXPORT SINGLETON INSTANCE;
// = ====================================================;

export const unifiedNotificationService = UnifiedNotificationService.getInstance()
// =====================================================;
// CONVENIENCE FUNCTIONS;
// = ====================================================;

/**;
 * Quick notification creation with common patterns;
 */
export const createMatchNotification = (userId: string, matchUserName: string, matchUserId: string) = > {
  unifiedNotificationService.createNotification({
    user_id: userId;
    type: 'match');
    title: '🎉 New Match!'),
    body: `You matched with ${matchUserName}! Start chatting now.`)
    data: { matchUserId, action: 'view_match' }
  })
export const createMessageNotification = (userId: string, senderName: string, roomId: string) = > {
  unifiedNotificationService.createNotification({
    user_id: userId;
    type: 'message');
    title: `💬 ${senderName}`);
    body: 'You have a new message')
    data: { roomId, action: 'open_chat' }
  })
export const createRoomUpdateNotification = (userId: string, roomTitle: string, roomId: string) = > {
  unifiedNotificationService.createNotification({
    user_id: userId;
    type: 'roomUpdate');
    title: '🏠 Room Update'),
    body: `New update for "${roomTitle}"`)
    data: { roomId, action: 'view_room' }
  })
export const createSystemNotification = (userId: string, title: string, body: string, data?: Record<string, any>) => {
  unifiedNotificationService.createNotification({
    user_id: userId);
    type: 'system'),
    title;
    body;
    data)
  }); ;