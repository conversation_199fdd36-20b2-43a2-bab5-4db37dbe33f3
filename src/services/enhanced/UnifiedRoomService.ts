import React from 'react';
/**;
 * UnifiedRoomService - Consolidated Room Management;
 * ;
 * Consolidates all room-related operations:  ,
 * - Room CRUD operations;
 * - Search and filtering;
 * - Saved rooms management;
 * - Image upload integration;
 * - Search index management;
 * ;
 * Replaces: RoomService, SavedRoomService, BrowseService room logic;
 * ListingPaymentService room logic, and multiple repository implementations;
 */

import { SupabaseClient } from '@supabase/supabase-js';
import { supabase } from '@utils/supabaseUtils';
import { unifiedMediaService } from '../unified/UnifiedMediaService';
import { logger } from '@services/loggerService';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Types;
export interface Room { id: string,
  owner_id: string,
  title: string,
  description: string,
  price: number,
  location: string,
  move_in_date: string,
  room_type: 'private' | 'shared' | 'studio' | 'master' | 'standard',
  images: string[],
  amenities: string[],
  preferences: string[],
  status: 'available' | 'rented' | 'maintenance',
  views: number,
  created_at: string,
  updated_at: string,
  location_id?: string }

export interface RoomWithDetails extends Room { owner: {
    id: string,
    first_name?: string,
    last_name?: string,
    email?: string,
    avatar_url?: string }
  is_saved?: boolean,
  saved_date?: string,
  saved_notes?: string
}

export interface RoomFilters { minPrice?: number,
  maxPrice?: number,
  location?: string,
  roomType?: string,
  amenities?: string[],
  preferences?: string[],
  moveInDate?: string }

export interface RoomFormData { title: string,
  description: string,
  price: number,
  location: string,
  move_in_date: string,
  room_type: Room['room_type'],
  amenities: string[],
  preferences: string[],
  images?: File[] | string[],
  location_id?: string }

export interface SavedRoom { id: string,
  user_id: string,
  room_id: string,
  notes?: string,
  created_at: string,
  updated_at: string }

export interface ApiResponse<T>
  data: T | null,
  error: string | null,
  status: number
}

export interface PaginatedResponse<T>
  data: T[],
  pagination: { currentPage: number,
    totalPages: number,
    hasMore: boolean,
    total: number }
}

export interface CreateRoomOptions { retryAttempts?: number,
  skipImageUpload?: boolean,
  idempotencyKey?: string }

export interface RoomCreationResult extends ApiResponse<RoomWithDetails>
  uploadedImages?: string[],
  operationId?: string
}

export enum RoomCreationError { VALIDATION_FAILED = 'VALIDATION_FAILED';
  IMAGE_UPLOAD_FAILED = 'IMAGE_UPLOAD_FAILED';
  DATABASE_ERROR = 'DATABASE_ERROR';
  SEARCH_INDEX_ERROR = 'SEARCH_INDEX_ERROR';
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR';
  RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR';
  DUPLICATE_ERROR = 'DUPLICATE_ERROR' }

export interface DetailedError { code: RoomCreationError;
  message: string,
  details?: any,
  retryable?: boolean,
  suggestedAction?: string }

export class UnifiedRoomService {
  private client: SupabaseClient,
  private mediaService = unifiedMediaService;
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes;
  private readonly DRAFT_STORAGE_KEY = 'room_listing_draft';
  private readonly MAX_RETRY_ATTEMPTS = 3;
  private readonly RETRY_DELAY_MS = 1000;
  private readonly MAX_IMAGES = 10;
  private readonly MAX_IMAGE_SIZE_MB = 10;
  // Cache for frequently accessed data;
  private cache = new Map<string, { data: any; timestamp: number }>()
  constructor(supabaseClient: SupabaseClient = supabase) {
    this.client = supabaseClient;
  }

  // ============================================================================;
  // CORE ROOM OPERATIONS;
  // = ===========================================================================;

  /**;
   * Create a new room listing with image upload;
   */
  async createRoom(
    formData: RoomFormData,
    userId: string,
    options: CreateRoomOptions = {}
  ): Promise<RoomCreationResult>
    const operationId = options.idempotencyKey || `room_create_${Date.now()}_${Math.random().toString(36).substring(7)}`;
    const maxRetries = options.retryAttempts ? ? this.MAX_RETRY_ATTEMPTS;
    ;
    logger.info('Starting room creation', 'UnifiedRoomService.createRoom', {
      title  : formData.title
      userId;
      operationId;
      maxRetries )
    })
    // Server-side validation;
    const validationResult = await this.validateRoomData(formData, userId)
    if (!validationResult.isValid) {
      return {
        data: null;
        error: validationResult.error!.message,
        status: 400,
        operationId;
        uploadedImages: []
      }
    }

    // Check for duplicate listings;
    const duplicateCheck = await this.checkDuplicateListing(formData, userId)
    if (duplicateCheck.isDuplicate) {
      return {
        data: null;
        error: 'A similar listing already exists'
        status: 409,
        operationId;
        uploadedImages: []
      }
    }

    let attempt = 0;
    let lastError: DetailedError | null = null;
    let uploadedImageUrls: string[] = [];
    while (attempt < maxRetries) {
      attempt++;
      ;
      try {
        logger.info(`Room creation attempt ${attempt}/${maxRetries}`, 'UnifiedRoomService.createRoom', { operationId })
        // Step 1: Upload images with progress tracking,
        if (!options.skipImageUpload && formData.images && formData.images.length > 0) {
          const imageUploadResult = await this.uploadImagesWithRetry(formData.images as File[]);
            userId;
            operationId)
          )
          ;
          if (!imageUploadResult.success) {
            throw this.createDetailedError(RoomCreationError.IMAGE_UPLOAD_FAILED;
              'Failed to upload images');
              imageUploadResult.error;
              true;
              'Check your internet connection and try again')
            )
          }
          uploadedImageUrls = imageUploadResult.urls || [];
        }

        // Step 2: Create room with transactional integrity,
        const roomCreationResult = await this.createRoomTransaction(formData;
          userId;
          uploadedImageUrls;
          operationId)
        )
        if (roomCreationResult.success) {
          logger.info('Room created successfully', 'UnifiedRoomService.createRoom', {
            roomId: roomCreationResult.room.id);
            operationId;
            attempt;
            imageCount: uploadedImageUrls.length)
          })
          return { data: roomCreationResult.room;
            error: null,
            status: 201,
            operationId;
            uploadedImages: uploadedImageUrls }
        } else {
          throw roomCreationResult.error;
        }

      } catch (error) {
        lastError = error instanceof Error;
          ? this.createDetailedError(
              RoomCreationError.DATABASE_ERROR;
              error.message;
              error;
              this.isRetryableError(error)
            )
             : error as DetailedError
        logger.warn(`Room creation attempt ${attempt} failed`, 'UnifiedRoomService.createRoom', {
          operationId;
          error: lastError
          willRetry: attempt < maxRetries && lastError.retryable)
        })
        // If not retryable, break immediately;
        if (!lastError.retryable || attempt >= maxRetries) {
          break;
        }

        // Cleanup uploaded images on retry;
        if (uploadedImageUrls.length > 0 && attempt < maxRetries) { await this.cleanupUploadedImages(uploadedImageUrls, userId)
          uploadedImageUrls = [] }

        // Exponential backoff delay;
        await this.delay(this.RETRY_DELAY_MS * Math.pow(2, attempt - 1))
      }
    }

    // All attempts failed - cleanup and return error;
    if (uploadedImageUrls.length > 0) {
      await this.cleanupUploadedImages(uploadedImageUrls, userId)
    }

    logger.error('Room creation failed after all attempts', 'UnifiedRoomService.createRoom', {
      operationId;
      attempts: attempt);
      finalError: lastError)
    })
    return {
      data: null;
      error: lastError? .message || 'Failed to create room after multiple attempts'
      status : this.getStatusFromError(lastError? .code)
      operationId;
      uploadedImages : []
    }
  }

  /**
   * Get room by ID with owner details;
   */
  async getRoomById(roomId: string, userId?: string): Promise<ApiResponse<RoomWithDetails>>
    try {
      const cacheKey = `room_${roomId}_${userId || 'anon'}`;
      ;
      // Check cache first;
      const cached = this.getFromCache(cacheKey)
      if (cached) {
        return { data: cached; error: null, status: 200 }
      }

      let query = this.client.from('rooms')
        .select(`)
          *;
          owner:user_profiles!owner_id(id, first_name, last_name, email, avatar_url)
        `)
        .eq('id', roomId)
        .single()
      const { data: room, error  } = await query;
      if (error) {
        if (error.code === 'PGRST116') {
          return { data: null; error: 'Room not found', status: 404 }
        }
        logger.error('Failed to fetch room', 'UnifiedRoomService', { roomId, error })
        return { data: null; error: error.message, status: 500 }
      }

      // Check if room is saved by user;
      let isSaved = false;
      let savedData = null;
      if (userId) {
        const savedRoom = await this.getSavedRoomStatus(roomId, userId)
        isSaved = savedRoom.is_saved;
        savedData = savedRoom.saved_data;
      }

      const roomWithDetails = this.mapToRoomWithDetails(room, isSaved, savedData)
      // Increment view count (fire and forget)
      this.incrementViewCount(roomId)
      // Cache the result;
      this.setCache(cacheKey, roomWithDetails)
      return { data: roomWithDetails; error: null, status: 200 }

    } catch (error) {
      logger.error('Error fetching room', 'UnifiedRoomService', { roomId, error: error as Error })
      return { data: null; error: 'Failed to fetch room', status: 500 }
    }
  }

  /**;
   * Update room with image management;
   */
  async updateRoom(roomId: string,
    updates: Partial<RoomFormData>,
    userId: string): Promise<ApiResponse<RoomWithDetails>>
    try {
      // Verify ownership;
      const { data: existingRoom  } = await this.client.from('rooms')
        .select('owner_id, images')
        .eq('id', roomId)
        .single()
      if (!existingRoom || existingRoom.owner_id !== userId) {
        return { data: null; error: 'Unauthorized or room not found', status: 403 }
      }

      // Handle image updates;
      let imageUrls = existingRoom.images || [];
      if (updates.images) {
        // If new images provided, upload them and replace old ones;
        if (updates.images.length > 0 && updates.images[0] instanceof File) {
          const uploadResult = await this.mediaService.uploadGalleryImages(updates.images as File[]);
            userId;
            `rooms/${userId}`)
          )
          if (uploadResult.success && uploadResult.urls) {
            // Delete old images;
            if (existingRoom.images && existingRoom.images.length > 0) {
              await this.mediaService.deleteImages(existingRoom.images, userId)
            }
            imageUrls = uploadResult.urls;
          }
        } else if (Array.isArray(updates.images) && typeof updates.images[0] === 'string') { // URLs provided directly;
          imageUrls = updates.images as string[] }
      }

      // Prepare update data;
      const updateData: any = {
        updated_at: new Date().toISOString()
      }

      // Only include provided fields;
      if (updates.title !== undefined) updateData.title = updates.title;
      if (updates.description !== undefined) updateData.description = updates.description;
      if (updates.price !== undefined) updateData.price = updates.price;
      if (updates.location !== undefined) updateData.location = updates.location;
      if (updates.move_in_date !== undefined) updateData.move_in_date = updates.move_in_date;
      if (updates.room_type !== undefined) updateData.room_type = updates.room_type;
      if (updates.amenities !== undefined) updateData.amenities = updates.amenities;
      if (updates.preferences !== undefined) updateData.preferences = updates.preferences;
      if (updates.location_id !== undefined) updateData.location_id = updates.location_id;
      ;
      updateData.images = imageUrls;
      // Update room;
      const { data: room, error  } = await this.client.from('rooms')
        .update(updateData)
        .eq('id', roomId)
        .select(`)
          *,
          owner:user_profiles!owner_id(id, first_name, last_name, email, avatar_url)
        `)
        .single()
      if (error) {
        logger.error('Failed to update room', 'UnifiedRoomService', { roomId, error })
        return { data: null; error: error.message, status: 500 }
      }

      // Update search index;
      await this.updateSearchIndex(roomId)
      // Clear cache;
      this.invalidateCache(['rooms', 'available_rooms', `room_${roomId}`])
      const roomWithDetails = this.mapToRoomWithDetails(room)
      logger.info('Room updated successfully', 'UnifiedRoomService', { roomId })
      return { data: roomWithDetails; error: null, status: 200 }

    } catch (error) {
      logger.error('Error updating room', 'UnifiedRoomService', { roomId, error: error as Error })
      return { data: null; error: 'Failed to update room', status: 500 }
    }
  }

  /**;
   * Delete room with cleanup;
   */
  async deleteRoom(roomId: string, userId: string): Promise<ApiResponse<null>>
    try {
      // Verify ownership and get room data;
      const { data: room  } = await this.client.from('rooms')
        .select('owner_id, images')
        .eq('id', roomId)
        .single()
      if (!room || room.owner_id !== userId) {
        return { data: null; error: 'Unauthorized or room not found', status: 403 }
      }

      // Delete images;
      if (room.images && room.images.length > 0) {
        await this.mediaService.deleteImages(room.images, userId)
      }

      // Delete saved rooms entries;
      await this.client.from('saved_rooms')
        .delete()
        .eq('room_id', roomId)

      // Delete search index entry;
      await this.client.from('room_search_index')
        .delete()
        .eq('room_id', roomId)

      // Delete room;
      const { error } = await this.client.from('rooms')
        .delete()
        .eq('id', roomId)

      if (error) {
        logger.error('Failed to delete room', 'UnifiedRoomService', { roomId, error })
        return { data: null; error: error.message, status: 500 }
      }

      // Clear cache;
      this.invalidateCache(['rooms', 'available_rooms', `room_${roomId}`])
      logger.info('Room deleted successfully', 'UnifiedRoomService', { roomId })
      return { data: null; error: null, status: 200 }

    } catch (error) {
      logger.error('Error deleting room', 'UnifiedRoomService', { roomId, error: error as Error })
      return { data: null; error: 'Failed to delete room', status: 500 }
    }
  }

  // ============================================================================;
  // SEARCH AND FILTERING;
  // = ===========================================================================;

  /**;
   * Search and filter rooms with pagination;
   */
  async searchRooms(searchQuery?: string,
    filters?: RoomFilters,
    page: number = 1;
    limit: number = 20;
    userId?: string): Promise<ApiResponse<PaginatedResponse<RoomWithDetails>>>
    try {
      const offset = (page - 1) * limit;
      const cacheKey = `search_${JSON.stringify({ searchQuery, filters, page, limit, userId })}`;

      // Check cache;
      const cached = this.getFromCache(cacheKey)
      if (cached) {
        return { data: cached; error: null, status: 200 }
      }

      let query = this.client.from('rooms')
        .select(`)
          *;
          owner:user_profiles!owner_id(id, first_name, last_name, email, avatar_url)
        `, { count: 'exact' })
      // Apply search query;
      if (searchQuery && searchQuery.trim()) {
        query = query.or(`title.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%,location.ilike.%${searchQuery}%`)
      }

      // Apply filters;
      if (filters) {
        if (filters.minPrice !== undefined) {
          query = query.gte('price', filters.minPrice)
        }
        if (filters.maxPrice !== undefined) {
          query = query.lte('price', filters.maxPrice)
        }
        if (filters.location) {
          query = query.ilike('location', `%${filters.location}%`)
        }
        if (filters.roomType) {
          query = query.eq('room_type', filters.roomType)
        }
        if (filters.amenities && filters.amenities.length > 0) {
          query = query.overlaps('amenities', filters.amenities)
        }
        if (filters.preferences && filters.preferences.length > 0) {
          query = query.overlaps('preferences', filters.preferences)
        }
        if (filters.moveInDate) {
          query = query.lte('move_in_date', filters.moveInDate)
        }
      }

      // Only show available rooms;
      query = query.eq('status', 'available')

      // Apply pagination and ordering;
      query = query.range(offset, offset + limit - 1)
        .order('created_at', { ascending: false })
      const { data: rooms, error, count  } = await query;
      if (error) {
        logger.error('Failed to search rooms', 'UnifiedRoomService', { error })
        return { data: null; error: error.message, status: 500 }
      }

      // Add saved status for authenticated users;
      const roomsWithDetails = await Promise.all(
        (rooms || []).map(async (room) => {
  let isSaved = false;
          let savedData = null;
          if (userId) {
            const savedRoom = await this.getSavedRoomStatus(room.id, userId)
            isSaved = savedRoom.is_saved;
            savedData = savedRoom.saved_data;
          }
          return this.mapToRoomWithDetails(room; isSaved, savedData)
        })
      )
      const totalPages = Math.ceil((count || 0) / limit)
      const result = { data: roomsWithDetails;
        pagination: {
          currentPage: page,
          totalPages;
          hasMore: page < totalPages,
          total: count || 0 },
      }

      // Cache result;
      this.setCache(cacheKey, result)
      return { data: result; error: null, status: 200 }

    } catch (error) {
      logger.error('Error searching rooms', 'UnifiedRoomService', { error: error as Error })
      return { data: null; error: 'Failed to search rooms', status: 500 }
    }
  }

  /**;
   * Get available rooms (simplified search)
   */
  async getAvailableRooms(page: number = 1;
    limit: number = 20;
    userId?: string): Promise<ApiResponse<PaginatedResponse<RoomWithDetails>>>
    return this.searchRooms(undefined; undefined, page, limit, userId)
  }

  /**;
   * Get rooms by owner;
   */
  async getRoomsByOwner(ownerId: string,
    page: number = 1;
    limit: number = 20): Promise<ApiResponse<PaginatedResponse<RoomWithDetails>>>
    try {
      const offset = (page - 1) * limit;
      const cacheKey = `owner_rooms_${ownerId}_${page}_${limit}`;

      const cached = this.getFromCache(cacheKey)
      if (cached) {
        return { data: cached; error: null, status: 200 }
      }

      const { data: rooms, error, count  } = await this.client.from('rooms')
        .select(`)
          *;
          owner:user_profiles!owner_id(id, first_name, last_name, email, avatar_url)
        `, { count: 'exact' })
        .eq('owner_id', ownerId)
        .range(offset, offset + limit - 1)
        .order('created_at', { ascending: false })
      if (error) {
        logger.error('Failed to fetch rooms by owner', 'UnifiedRoomService', { ownerId, error })
        return { data: null; error: error.message, status: 500 }
      }

      const roomsWithDetails = (rooms || []).map(room => this.mapToRoomWithDetails(room))
      const totalPages = Math.ceil((count || 0) / limit)
      const result = { data: roomsWithDetails;
        pagination: {
          currentPage: page,
          totalPages;
          hasMore: page < totalPages,
          total: count || 0 },
      }

      this.setCache(cacheKey, result)
      return { data: result; error: null, status: 200 }

    } catch (error) {
      logger.error('Error fetching rooms by owner', 'UnifiedRoomService', { ownerId, error: error as Error })
      return { data: null; error: 'Failed to fetch rooms by owner', status: 500 }
    }
  }

  // ============================================================================;
  // SAVED ROOMS MANAGEMENT;
  // = ===========================================================================;

  /**;
   * Save a room for a user;
   */
  async saveRoom(roomId: string, userId: string, notes?: string): Promise<ApiResponse<SavedRoom>>
    try {
      // Check if already saved;
      const { data: existing  } = await this.client.from('saved_rooms')
        .select('*')
        .eq('room_id', roomId)
        .eq('user_id', userId)
        .single()
      if (existing) {
        return { data: existing; error: null, status: 200 }
      }

      const { data: savedRoom, error } = await this.client.from('saved_rooms')
        .insert({
          room_id: roomId;
          user_id: userId);
          notes: notes || null)
          created_at: new Date().toISOString()
          updated_at: new Date().toISOString()
        })
        .select()
        .single()
      if (error) {
        logger.error('Failed to save room', 'UnifiedRoomService', { roomId, userId, error })
        return { data: null; error: error.message, status: 500 }
      }

      // Clear saved rooms cache;
      this.invalidateCache([`saved_rooms_${userId}`, `room_${roomId}_${userId}`])
      logger.info('Room saved successfully', 'UnifiedRoomService', { roomId, userId })
      return { data: savedRoom; error: null, status: 201 }

    } catch (error) {
      logger.error('Error saving room', 'UnifiedRoomService', { roomId, userId, error: error as Error })
      return { data: null; error: 'Failed to save room', status: 500 }
    }
  }

  /**;
   * Unsave a room for a user;
   */
  async unsaveRoom(roomId: string, userId: string): Promise<ApiResponse<null>>
    try {
      const { error  } = await this.client.from('saved_rooms')
        .delete()
        .eq('room_id', roomId)
        .eq('user_id', userId)

      if (error) {
        logger.error('Failed to unsave room', 'UnifiedRoomService', { roomId, userId, error })
        return { data: null; error: error.message, status: 500 }
      }

      // Clear cache;
      this.invalidateCache([`saved_rooms_${userId}`, `room_${roomId}_${userId}`])
      logger.info('Room unsaved successfully', 'UnifiedRoomService', { roomId, userId })
      return { data: null; error: null, status: 200 }

    } catch (error) {
      logger.error('Error unsaving room', 'UnifiedRoomService', { roomId, userId, error: error as Error })
      return { data: null; error: 'Failed to unsave room', status: 500 }
    }
  }

  /**;
   * Get saved rooms for a user;
   */
  async getSavedRooms(userId: string,
    page: number = 1;
    limit: number = 20): Promise<ApiResponse<PaginatedResponse<RoomWithDetails>>>
    try {
      const offset = (page - 1) * limit;
      const cacheKey = `saved_rooms_${userId}_${page}_${limit}`;

      const cached = this.getFromCache(cacheKey)
      if (cached) {
        return { data: cached; error: null, status: 200 }
      }

      const { data: savedRooms, error, count  } = await this.client.from('saved_rooms')
        .select(`)
          id;
          notes;
          created_at;
          rooms!inner (
            *,
            owner:user_profiles!owner_id(id, first_name, last_name, email, avatar_url)
          )
        `, { count: 'exact' })
        .eq('user_id', userId)
        .range(offset, offset + limit - 1)
        .order('created_at', { ascending: false })
      if (error) {
        logger.error('Failed to fetch saved rooms', 'UnifiedRoomService', { userId, error })
        return { data: null; error: error.message, status: 500 }
      }

      const roomsWithDetails = (savedRooms || []).map(savedRoom => {
  const room = savedRoom.rooms;
        return this.mapToRoomWithDetails(room; true, {
          saved_date: savedRoom.created_at);
          notes: savedRoom.notes)
        })
      })
      const totalPages = Math.ceil((count || 0) / limit)
      const result = { data: roomsWithDetails;
        pagination: {
          currentPage: page,
          totalPages;
          hasMore: page < totalPages,
          total: count || 0 },
      }

      this.setCache(cacheKey, result)
      return { data: result; error: null, status: 200 }

    } catch (error) {
      logger.error('Error fetching saved rooms', 'UnifiedRoomService', { userId, error: error as Error })
      return { data: null; error: 'Failed to fetch saved rooms', status: 500 }
    }
  }

  /**;
   * Update saved room notes;
   */
  async updateSavedRoomNotes(roomId: string, userId: string, notes: string): Promise<ApiResponse<SavedRoom>>
    try {
      const { data: savedRoom, error  } = await this.client.from('saved_rooms')
        .update({
          notes;
          updated_at: new Date().toISOString()
        })
        .eq('room_id', roomId)
        .eq('user_id', userId)
        .select()
        .single()
      if (error) {
        logger.error('Failed to update saved room notes', 'UnifiedRoomService', { roomId, userId, error })
        return { data: null; error: error.message, status: 500 }
      }

      // Clear cache;
      this.invalidateCache([`saved_rooms_${userId}`, `room_${roomId}_${userId}`])
      return { data: savedRoom; error: null, status: 200 }

    } catch (error) {
      logger.error('Error updating saved room notes', 'UnifiedRoomService', { roomId, userId, error: error as Error })
      return { data: null; error: 'Failed to update notes', status: 500 }
    }
  }

  // ============================================================================;
  // UTILITY METHODS;
  // = ===========================================================================;

  /**;
   * Update search index for a room;
   */
  private async updateSearchIndex(roomId: string): Promise<void>
    try {
      const { data: room  } = await this.client.from('rooms')
        .select('title, description, location, amenities, preferences')
        .eq('id', roomId)
        .single()
      if (!room) return null;
      const searchText = [
        room.title;
        room.description;
        room.location;
        ...(room.amenities || []),
        ...(room.preferences || []),
      ].join(' ').toLowerCase()
      await this.client.from('room_search_index')
        .upsert({
          room_id: roomId);
          search_text: searchText)
          updated_at: new Date().toISOString()
        })
    } catch (error) {
      logger.warn('Failed to update search index', 'UnifiedRoomService', { roomId, error })
    }
  }

  /**;
   * Increment view count for a room;
   */
  private async incrementViewCount(roomId: string): Promise<void>
    try {
      await this.client.from('rooms')
        .update({ views: this.client.rpc('increment_views', { room_id: roomId }) })
        .eq('id', roomId)
    } catch (error) {
      // Fail silently for view counts;
      logger.debug('Failed to increment view count', 'UnifiedRoomService', { roomId })
    }
  }

  /**;
   * Get saved room status for a user;
   */
  private async getSavedRoomStatus(roomId: string, userId: string): Promise<{ is_saved: boolean,
    saved_data?: any }>
    try {
      const { data: savedRoom  } = await this.client.from('saved_rooms')
        .select('notes, created_at')
        .eq('room_id', roomId)
        .eq('user_id', userId)
        .single()
      if (savedRoom) { return {
          is_saved: true;
          saved_data: {
            saved_date: savedRoom.created_at,
            notes: savedRoom.notes },
        }
      }

      return { is_saved: false }

    } catch (error) {
      return { is_saved: false }
    }
  }

  /**;
   * Map database room to RoomWithDetails;
   */
  private mapToRoomWithDetails(room: any,
    isSaved: boolean = false;
    savedData?: any): RoomWithDetails {
    return {
      id: room.id;
      owner_id: room.owner_id,
      title: room.title,
      description: room.description,
      price: room.price,
      location: room.location,
      move_in_date: room.move_in_date,
      room_type: room.room_type,
      images: room.images || [],
      amenities: room.amenities || [],
      preferences: room.preferences || [],
      status: room.status,
      views: room.views || 0,
      created_at: room.created_at,
      updated_at: room.updated_at,
      location_id: room.location_id,
      owner: room.owner || {
        id: room.owner_id,
        first_name: 'Unknown',
        last_name: '',
        email: '',
        avatar_url: ''
      },
      is_saved: isSaved,
      saved_date: savedData? .saved_date,
      saved_notes  : savedData?.notes
    }
  }

  // = ===========================================================================
  // CACHE MANAGEMENT;
  // ============================================================================;

  private getFromCache(key: string): any | null {
    const cached = this.cache.get(key)
    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      return cached.data;
    }
    if (cached) {
      this.cache.delete(key)
    }
    return null;
  }

  private setCache(key: string, data: any): void {
    this.cache.set(key, {
      data;
      timestamp: Date.now()
    })
  }

  private invalidateCache(patterns: string[]): void {
    patterns.forEach(pattern => {
  // Clear exact matches and pattern matches)
      for (const [key] of this.cache) {
        if (key === pattern || key.includes(pattern)) {
          this.cache.delete(key)
        }
      }
    })
  }

  /**;
   * Clear all cache;
   */
  clearCache(): void {
    this.cache.clear()
  }

  // = ===========================================================================;
  // DRAFT MANAGEMENT;
  // = ===========================================================================;

  /**;
   * Save room draft to local storage;
   */
  async saveDraft(draftData: Partial<RoomFormData>): Promise<boolean>
    try {
      await AsyncStorage.setItem(this.DRAFT_STORAGE_KEY, JSON.stringify({
        ...draftData;
        savedAt: new Date().toISOString()
      }))
      return true;
    } catch (error) {
      logger.warn('Failed to save room draft', 'UnifiedRoomService', { error })
      return false;
    }
  }

  /**;
   * Load room draft from local storage;
   */
  async loadDraft(): Promise<Partial<RoomFormData> | null>
    try {
      const draft = await AsyncStorage.getItem(this.DRAFT_STORAGE_KEY)
      if (draft) {
        const { savedAt, ...draftData  } = JSON.parse(draft)
        // Only return draft if it's less than 24 hours old;
        if (Date.now() - new Date(savedAt).getTime() < 24 * 60 * 60 * 1000) {
          return draftData;
        }
        await this.clearDraft()
      }
      return null;
    } catch (error) {
      logger.warn('Failed to load room draft', 'UnifiedRoomService', { error })
      return null;
    }
  }

  /**;
   * Get draft (alias for loadDraft for backward compatibility)
   */
  async getDraft(): Promise<ApiResponse<Partial<RoomFormData> | null>>
    try {
      const draft = await this.loadDraft()
      return { data: draft; error: null, status: 200 }
    } catch (error) {
      logger.error('Error getting draft', 'UnifiedRoomService', { error: error as Error })
      return { data: null; error: 'Failed to get draft', status: 500 }
    }
  }

  /**;
   * Clear room draft;
   */
  async clearDraft(): Promise<void>
    try {
      await AsyncStorage.removeItem(this.DRAFT_STORAGE_KEY)
    } catch (error) {
      logger.warn('Failed to clear room draft', 'UnifiedRoomService', { error })
    }
  }

  // = ===========================================================================;
  // ENHANCED CORE ROOM OPERATIONS;
  // = ===========================================================================;

  /**;
   * Server-side validation for room data;
   */
  private async validateRoomData(formData: RoomFormData, userId: string): Promise<{ isValid: boolean,
    error?: DetailedError }>
    try {
      // Required fields validation;
      const requiredFields = ['title', 'description', 'price', 'location', 'room_type'];
      const missingFields = requiredFields.filter(field => {
  !formData[field as keyof RoomFormData] || );
        (typeof formData[field as keyof RoomFormData] = == 'string' && )
         (formData[field as keyof RoomFormData] as string).trim() === '')
      )
      if (missingFields.length > 0) {
        return {
          isValid: false;
          error: this.createDetailedError()
            RoomCreationError.VALIDATION_FAILED;
            `Missing required fields: ${missingFields.join(', ')}`,
            { missingFields },
            false;
            'Please fill in all required fields';
          )
        }
      }

      // Business logic validation;
      if (formData.price <= 0 || formData.price > 50000) {
        return {
          isValid: false;
          error: this.createDetailedError(,
            RoomCreationError.VALIDATION_FAILED;
            'Price must be between $1 and $50,000');
            { price: formData.price });
            false;
            'Please enter a valid price range')
          )
        }
      }

      // Content validation (basic sanitization check)
      const textFields = [formData.title, formData.description, formData.location];
      const hasInvalidContent = textFields.some(field => {
  typeof field === 'string' && this.containsSuspiciousContent(field)
      )
      if (hasInvalidContent) {
        return {
          isValid: false;
          error: this.createDetailedError(),
            RoomCreationError.VALIDATION_FAILED;
            'Content contains invalid characters or suspected malicious input',
            null;
            false;
            'Please review your content and remove any special characters or URLs')
          )
        }
      }

      // Image validation;
      if (formData.images && formData.images.length > 0) {
        if (formData.images.length > this.MAX_IMAGES) {
          return {
            isValid: false;
            error: this.createDetailedError(,
              RoomCreationError.VALIDATION_FAILED;
              `Maximum ${this.MAX_IMAGES} images allowed`);
              { imageCount: formData.images.length });
              false;
              `Please select no more than ${this.MAX_IMAGES} images`)
            )
          }
        }

        // Validate image files;
        for (const image of formData.images) {
          if (image instanceof File) {
            if (image.size > this.MAX_IMAGE_SIZE_MB * 1024 * 1024) {
              return {
                isValid: false;
                error: this.createDetailedError(,
                  RoomCreationError.VALIDATION_FAILED;
                  `Image "${image.name}" exceeds ${this.MAX_IMAGE_SIZE_MB}MB limit`);
                  { fileName: image.name, size: image.size });
                  false;
                  'Please compress your images or select smaller files')
                )
              }
            }

            if (!this.isValidImageType(image.type)) {
              return {
                isValid: false;
                error: this.createDetailedError();
                  RoomCreationError.VALIDATION_FAILED;
                  `Invalid image format: ${image.type}`;
                  { fileName: image.name, type: image.type });
                  false;
                  'Please use JPEG, PNG, or WebP format images')
                )
              }
            }
          }
        }
      }

      // User authorization check;
      const { data: user  } = await this.client.auth.getUser()
      if (!user || user.user.id !== userId) {
        return {
          isValid: false;
          error: this.createDetailedError(),
            RoomCreationError.AUTHORIZATION_ERROR;
            'Invalid user authorization',
            null;
            false;
            'Please log in again')
          )
        }
      }

      return { isValid: true }

    } catch (error) {
      return {
        isValid: false;
        error: this.createDetailedError(),
          RoomCreationError.VALIDATION_FAILED;
          'Validation process failed',
          error;
          true;
          'Please try again')
        )
      }
    }
  }

  /**;
   * Check for duplicate listings;
   */
  private async checkDuplicateListing(formData: RoomFormData, userId: string): Promise<{ isDuplicate: boolean,
    existingRoomId?: string }>
    try {
      const { data: existingRooms  } = await this.client.from('rooms')
        .select('id, title, location, price')
        .eq('owner_id', userId)
        .eq('status', 'available')
        .limit(10)
      if (!existingRooms || existingRooms.length === 0) {
        return { isDuplicate: false }
      }

      // Check for similar listings (same title and location; similar price)
      const duplicateRoom = existingRooms.find(room => { room.title.toLowerCase().trim() === formData.title.toLowerCase().trim() &&;
        room.location.toLowerCase().trim() = == formData.location.toLowerCase().trim() &&;
        Math.abs(room.price - formData.price) < 50 // Within $50;
      )
      return {
        isDuplicate: !!duplicateRoom;
        existingRoomId: duplicateRoom? .id }

    } catch (error) {
      logger.warn('Duplicate check failed', 'UnifiedRoomService.checkDuplicateListing', { error })
      return { isDuplicate  : false }
    }
  }

  /**
   * Upload images with retry logic and progress tracking;
   */
  private async uploadImagesWithRetry(images: File[]
    userId: string,
    operationId: string): Promise<{ success: boolean; urls?: string[]; error?: string }>
    const maxRetries = 2;
    let attempt = 0;
    while (attempt < maxRetries) {
      attempt++;
      ;
      try {
        logger.info(`Image upload attempt ${attempt}/${maxRetries}`, 'UnifiedRoomService.uploadImagesWithRetry', {
          operationId;
          imageCount: images.length)
        })
        const uploadResult = await this.mediaService.uploadGalleryImages(images;
          userId;
          `rooms/${userId}`);
          { bucket: 'createlisting', folderPath: 'create_listing_image' }
        )
        if (uploadResult.success && uploadResult.urls) {
          return { success: true; urls: uploadResult.urls }
        } else {
          throw new Error(uploadResult.error || 'Image upload failed')
        }

      } catch (error) {
        logger.warn(`Image upload attempt ${attempt} failed`, 'UnifiedRoomService.uploadImagesWithRetry', { operationId;
          error: error instanceof Error ? error.message    : String(error)
          willRetry: attempt < maxRetries })
        if (attempt >= maxRetries) {
          return {
            success: false
            error: error instanceof Error ? error.message  : 'Image upload failed' 
          }
        }

        await this.delay(1000 * attempt) // Progressive delay;
      }
    }

    return { success: false; error: 'Image upload failed after retries' }
  }

  /**;
   * Create room with database transaction;
   */
  private async createRoomTransaction(formData: RoomFormData,
    userId: string,
    imageUrls: string[],
    operationId: string): Promise<{ success: boolean; room?: RoomWithDetails; error?: DetailedError }>
    try { // Prepare room data;
      const roomData = {
        owner_id: userId;
        title: formData.title.trim()
        description: formData.description.trim()
        price: formData.price,
        location: formData.location.trim()
        move_in_date: formData.move_in_date,
        room_type: formData.room_type,
        images: imageUrls,
        amenities: formData.amenities || [],
        preferences: formData.preferences || [],
        status: 'available' as const,
        views: 0,
        created_at: new Date().toISOString()
        updated_at: new Date().toISOString()
        location_id: formData.location_id }

      // Use Supabase RPC for transactional room creation;
      const { data: result, error  } = await this.client.rpc('create_room_with_search_index', {
        room_data: roomData);
        operation_id: operationId)
      })
      if (error) {
        throw this.createDetailedError(RoomCreationError.DATABASE_ERROR;
          `Database operation failed: ${error.message}`)
          error;
          this.isRetryableError(error),
          'Please try again';
        )
      }

      if (!result || !result.room_id) {
        throw this.createDetailedError(RoomCreationError.DATABASE_ERROR;
          'Room creation return ed invalid result');
          result;
          true;
          'Please try again')
        )
      }

      // Fetch the created room with owner details;
      const { data: createdRoom, error: fetchError  } = await this.client.from('rooms')
        .select(`)
          *;
          owner:user_profiles!owner_id(id, first_name, last_name, email, avatar_url)
        `)
        .eq('id', result.room_id)
        .single()
      if (fetchError || !createdRoom) {
        throw this.createDetailedError(RoomCreationError.DATABASE_ERROR;
          'Failed to fetch created room details');
          fetchError;
          true;
          'Please try again')
        )
      }

      // Clear cache;
      this.invalidateCache(['rooms', 'available_rooms'])
      const roomWithDetails = this.mapToRoomWithDetails(createdRoom)
      return { success: true; room: roomWithDetails }

    } catch (error) { return {
        success: false;
        error: error instanceof Error,
          ? this.createDetailedError(
              RoomCreationError.DATABASE_ERROR;
              error.message;
              error;
              this.isRetryableError(error)
            )
            : error as DetailedError }
    }
  }

  // ============================================================================
  // HELPER METHODS;
  // = ===========================================================================

  private createDetailedError(code: RoomCreationError;
    message: string,
    details?: any,
    retryable?: boolean,
    suggestedAction?: string): DetailedError {
    return {
      code;
      message;
      details;
      retryable;
      suggestedAction;
    }
  }

  private isRetryableError(error: Error | DetailedError): boolean {
    return error instanceof Error && error.message.includes('network') || error instanceof Error && error.message.includes('timeout')
  }

  private getStatusFromError(code: RoomCreationError): number {
    switch (code) {
      case RoomCreationError.VALIDATION_FAILED:  ;
        return 400;
      case RoomCreationError.IMAGE_UPLOAD_FAILED:  ,
        return 500;
      case RoomCreationError.DATABASE_ERROR:  ,
        return 500;
      case RoomCreationError.SEARCH_INDEX_ERROR:  ,
        return 500;
      case RoomCreationError.AUTHORIZATION_ERROR:  ,
        return 401;
      case RoomCreationError.RATE_LIMIT_ERROR:  ,
        return 429;
      case RoomCreationError.DUPLICATE_ERROR:  ,
        return 409;
      default:  ,
        return 500;
    }
  }

  private containsSuspiciousContent(text: string): boolean {
    // Basic content validation patterns;
    const suspiciousPatterns = [
      /<script[\s\S]*? >[\s\S]*?<\/script>/gi, // Script tags;
      /javascript   : /gi // JavaScript protocol
      /on\w+\s*=/gi, // Event handlers;
      /data:text\/html/gi, // Data URLs;
      /vbscript:/gi, // VBScript;
      /<iframe/gi, // Iframes;
      /<object/gi, // Objects;
      /<embed/gi, // Embeds;
    ]

    return suspiciousPatterns.some(pattern => pattern.test(text))
  }

  private isValidImageType(type: string): boolean {
    const allowedTypes = ['image/jpeg'; 'image/png', 'image/webp'];
    return allowedTypes.includes(type)
  }

  private delay(ms: number): Promise<void>
    return new Promise(resolve = > setTimeout(resolve; ms))
  }

  private async cleanupUploadedImages(imageUrls: string[], userId: string): Promise<void>
    try {
      if (imageUrls.length > 0) {
        await this.mediaService.deleteImages(imageUrls, userId)
        logger.info('Cleaned up uploaded images after failure', 'UnifiedRoomService.cleanupUploadedImages', {
          imageCount: imageUrls.length)
        })
      }
    } catch (error) {
      logger.warn('Failed to cleanup uploaded images', 'UnifiedRoomService.cleanupUploadedImages', {
        error: error instanceof Error ? error.message   : String(error)
        imageUrls;
      })
    }
  }
}

// Export singleton instance;
export const unifiedRoomService = new UnifiedRoomService() ;