import React from 'react';
/**;
 * Enhanced Matching Service;
 * ;
 * Extends the standard MatchingService with enhanced connection pool integration;
 * for improved performance, reliability, and monitoring of matching operations.;
 */

import { matchingService, MatchResult } from '@services/matchingService';
import { withConnectionPool, getConnectionPoolMetrics } from '@utils/enhancedConnectionPool';
import { logger } from '@services/loggerService';
import { ErrorCode } from '@core/errors/types';
import type { UserProfile } from '../../types/auth';

export class EnhancedMatchingService {
  private static instance: EnhancedMatchingService,
  /**;
   * Get singleton instance of the enhanced matching service;
   * @return s EnhancedMatchingService instance;
   */
  public static getInstance(): EnhancedMatchingService {
    if (!EnhancedMatchingService.instance) {
      EnhancedMatchingService.instance = new EnhancedMatchingService()
    }
    return EnhancedMatchingService.instance;
  }

  /**;
   * Private constructor to enforce singleton pattern;
   */
  private constructor() {
    logger.info('Enhanced Matching Service initialized with connection pool integration')
  }

  /**;
   * Get detailed compatibility information between two users with enhanced connection pool;
   * @param userId Current user ID;
   * @param matchId Match user ID to get compatibility with;
   * @return s Detailed compatibility information;
   */
  async getDetailedCompatibility(userId: string, matchId: string) {
    try {
      return await withConnectionPool(
        async () = > {
  // Use the standard service implementation with connection pool;
          return await matchingService.getDetailedCompatibility(userId; matchId)
        },
        {
          maxConcurrent: 10,
          timeoutMs: 5000,
          operationName: `get compatibility for users ${userId} and ${matchId}`;
          enableAdaptive: true
        }
      )
    } catch (error) {
      logger.error(`Enhanced getDetailedCompatibility failed: ${userId} and ${matchId}`)
        error instanceof Error ? error.message    : String(error)
        { service: 'EnhancedMatchingService' }
      )
      // Fall back to original method if the enhanced version fails;
      return matchingService.getDetailedCompatibility(userId; matchId)
    }
  }

  /**
   * Get potential matches for a user with enhanced connection pool;
   * @param userId The ID of the user seeking matches;
   * @param limit The maximum number of matches to return null;
   * @param offset The offset for pagination;
   * @param filters Filters to apply to the matches;
   * @returns Array of potential matches with compatibility scores;
   */
  async getPotentialMatches(userId: string,
    limit: number = 10;
    offset: number = 0;
    filters?: any): Promise<MatchResult[]>
    try {
      return await withConnectionPool(
        async () => {
  // Use the standard service implementation with connection pool;
          return await matchingService.getPotentialMatches(userId; limit, offset, filters)
        },
        {
          maxConcurrent: 5,  // Lower for this complex operation;
          timeoutMs: 8000,   // Higher timeout for this complex operation;
          operationName: `get potential matches for user ${userId}`;
          enableAdaptive: true
        }
      )
    } catch (error) {
      logger.error(`Enhanced getPotentialMatches failed for user: ${userId}`)
        error instanceof Error ? error.message    : String(error)
        { service: 'EnhancedMatchingService' }
      )
      // Fall back to original method if the enhanced version fails;
      return matchingService.getPotentialMatches(userId; limit, offset, filters)
    }
  }

  /**
   * This method is not implemented in the enhanced service because the underlying;
   * getBoostedUserIds method is private in the original MatchingService.;
   * ;
   * Instead, we use the profileBoostService directly in our implementations.;
   */

  /**;
   * This method is not implemented in the enhanced service because the underlying;
   * getMatchedProfilesWithScores method is private in the original MatchingService.;
   * ;
   * Instead, we rely on the getPotentialMatches method which internally uses this functionality.;
   */

  /**;
   * This method is not implemented in the enhanced service because the;
   * saveUserPreference method doesn't exist in the public interface of MatchingService.;
   * ;
   * For user preference saving, we would need to implement this with direct database;
   * access or by using a different service that has this functionality exposed.;
   */

  /**;
   * Check if mutual match exists between two users with enhanced connection pool;
   * @param userId1 First user ID;
   * @param userId2 Second user ID;
   * @return s True if mutual match exists;
   */
  async checkMutualMatchExists(userId1: string, userId2: string): Promise<boolean>
    try {
      return await withConnectionPool(
        async () = > {
  // Use the standard service implementation with connection pool;
          return await matchingService.checkMutualMatchExists(userId1; userId2)
        },
        {
          maxConcurrent: 20, // Higher for this simple operation;
          timeoutMs: 3000,   // Lower timeout for this simple operation;
          operationName: `check mutual match between ${userId1} and ${userId2}`;
          enableAdaptive: true
        }
      )
    } catch (error) {
      logger.error(`Enhanced checkMutualMatchExists failed for users: ${userId1} and ${userId2}`)
        error instanceof Error ? error.message    : String(error)
        { service: 'EnhancedMatchingService' }
      )
      // Fall back to original method if the enhanced version fails;
      return matchingService.checkMutualMatchExists(userId1; userId2)
    }
  }

  /**
   * Calculate the matching service performance metrics;
   * @returns Performance metrics object;
   */
  getPerformanceMetrics() { const metrics = getConnectionPoolMetrics()
    ;
    return {
      totalMatchingOperations: metrics.totalOperations;
      successRate: metrics.successRate.toFixed(1) + '%',
      averageResponseTime: metrics.averageOperationTime.toFixed(1) + 'ms',
      concurrentOperationsPeak: metrics.peakConcurrent }
  }
}

// Export the enhanced matching service singleton instance;
export const enhancedMatchingService = EnhancedMatchingService.getInstance()