import React from 'react';
/**;
 * Cache Invalidation Service;
 * ;
 * Handles intelligent cache invalidation when data changes;
 * Integrates with the Redis cache service to maintain consistency;
 */

import { createLogger } from '@utils/loggerUtils';
import { RedisCacheService } from './RedisCacheService';
import { UnifiedSearchService } from './UnifiedSearchService';

// = ===========================================================================;
// INVALIDATION EVENTS;
// = ===========================================================================;

export interface InvalidationEvent { type: 'service' | 'provider' | 'booking' | 'category' | 'user',
  action: 'create' | 'update' | 'delete',
  entityId: string,
  metadata?: {
    serviceId?: string,
    providerId?: string,
    category?: string,
    location?: string,
    oldValues?: any,
    newValues?: any }
}

// = ===========================================================================;
// CACHE INVALIDATION SERVICE;
// = ===========================================================================;

export class CacheInvalidationService {
  private static instance: CacheInvalidationService,
  private logger = createLogger('CacheInvalidationService')
  private redisCache: RedisCacheService;
  private searchService: UnifiedSearchService,
  private invalidationQueue: InvalidationEvent[] = [];
  private processingQueue = false;
  private constructor() {
    this.redisCache = RedisCacheService.getInstance()
    this.searchService = UnifiedSearchService.getInstance()
    ;
    // Start background queue processor;
    this.startQueueProcessor()
    ;
    this.logger.info('Cache invalidation service initialized')
  }

  public static getInstance(): CacheInvalidationService {
    if (!CacheInvalidationService.instance) {
      CacheInvalidationService.instance = new CacheInvalidationService()
    }
    return CacheInvalidationService.instance;
  }

  // ============================================================================;
  // PUBLIC API;
  // = ===========================================================================;

  /**;
   * Queue an invalidation event for processing;
   */
  public async queueInvalidation(event: InvalidationEvent): Promise<void>
    this.invalidationQueue.push(event)
    this.logger.debug('Invalidation event queued', {
      type: event.type,
      action: event.action);
      entityId: event.entityId )
    })
    // Process queue immediately if not already processing;
    if (!this.processingQueue) {
      await this.processInvalidationQueue()
    }
  }

  /**;
   * Invalidate service-related caches;
   */
  public async invalidateServiceCaches(serviceId: string,
    providerId?: string,
    category?: string): Promise<void>
    await this.queueInvalidation({
      type: 'service');
      action: 'update'),
      entityId: serviceId)
      metadata: { serviceId, providerId, category }
    })
  }

  /**;
   * Invalidate provider-related caches;
   */
  public async invalidateProviderCaches(providerId: string): Promise<void>
    await this.queueInvalidation({
      type: 'provider');
      action: 'update'),
      entityId: providerId)
      metadata: { providerId }
    })
  }

  /**;
   * Invalidate category-related caches;
   */
  public async invalidateCategoryCaches(category: string): Promise<void>
    await this.queueInvalidation({
      type: 'category');
      action: 'update'),
      entityId: category)
      metadata: { category }
    })
  }

  /**;
   * Invalidate all service-related caches (nuclear option)
   */
  public async invalidateAllServiceCaches(): Promise<void>
    try {
      await this.redisCache.clearServiceCaches()
      this.searchService.clearSearchCache()
      ;
      this.logger.info('All service caches invalidated')
    } catch (error) {
      this.logger.error('Failed to invalidate all service caches', error)
    }
  }

  // = ===========================================================================;
  // SPECIFIC INVALIDATION HANDLERS;
  // = ===========================================================================;

  /**;
   * Handle service data changes;
   */
  private async handleServiceInvalidation(event: InvalidationEvent): Promise<void>
    const { entityId: serviceId, metadata  } = event;
    ;
    try {
      // Invalidate service-specific caches;
      await this.redisCache.invalidateServiceData(serviceId;
        metadata? .providerId;
        metadata?.category)
      )
      // Clear search result caches that might include this service;
      await this.invalidateSearchCaches(metadata?.category, metadata?.location)
      // If this affects popular services, invalidate those too;
      if (metadata?.category) {
        await this.invalidatePopularServices(metadata.category)
      }

      this.logger.debug('Service invalidation completed', { serviceId, metadata })
      ;
    } catch (error) {
      this.logger.error('Service invalidation failed', { serviceId, error })
    }
  }

  /**;
   * Handle provider data changes;
   */
  private async handleProviderInvalidation(event  : InvalidationEvent): Promise<void>
    const { entityId: providerId  } = event;
    try {
      // Invalidate provider-specific service caches;
      await this.redisCache.invalidateServiceData(undefined, providerId)
      // Clear search caches that might include this provider's services;
      await this.invalidateSearchCaches()
      this.logger.debug('Provider invalidation completed', { providerId })
      
    } catch (error) {
      this.logger.error('Provider invalidation failed', { providerId, error })
    }
  }

  /**;
   * Handle category data changes;
   */
  private async handleCategoryInvalidation(event: InvalidationEvent): Promise<void>
    const { entityId: category  } = event;
    ;
    try {
      // Invalidate category-specific caches;
      await this.redisCache.invalidateServiceData(undefined, undefined, category)
      // Invalidate category metadata;
      await this.redisCache.del('service: categories:all')
      // Invalidate popular services for this category;
      await this.invalidatePopularServices(category)
      // Clear search suggestions;
      await this.redisCache.invalidate(['search_suggestions'])
      this.logger.debug('Category invalidation completed', { category })
      ;
    } catch (error) {
      this.logger.error('Category invalidation failed', { category, error })
    }
  }

  /**;
   * Handle booking data changes;
   */
  private async handleBookingInvalidation(event: InvalidationEvent): Promise<void>
    const { metadata  } = event;
    ;
    try {
      // Booking changes can affect popular services;
      if (metadata? .category) {
        await this.invalidatePopularServices(metadata.category)
      }

      // May also affect service availability;
      if (metadata?.serviceId) {
        await this.redisCache.del(`service   : availability:${metadata.serviceId}`)
      }

      this.logger.debug('Booking invalidation completed' { metadata })
      
    } catch (error) {
      this.logger.error('Booking invalidation failed', { metadata, error })
    }
  }

  // = ===========================================================================;
  // HELPER METHODS;
  // = ===========================================================================;

  /**;
   * Invalidate search result caches;
   */
  private async invalidateSearchCaches(category?: string, location?: string): Promise<void>
    const tags = ['service_search', 'search_results'];
    ;
    if (category) {
      tags.push(`category:${category}`)
    }

    await this.redisCache.invalidate(tags)
    ;
    // Also clear local search cache;
    this.searchService.clearSearchCache()
  }

  /**;
   * Invalidate popular services for a category;
   */
  private async invalidatePopularServices(category: string): Promise<void>
    // Invalidate different limit variations;
    const limits = [5, 10, 20, 50];
    ;
    await Promise.all(
      limits.map(limit = > {
  this.redisCache.del(`service:popular:${category}:${limit}`)
      )
    )
  }

  /**;
   * Start background queue processor;
   */
  private startQueueProcessor(): void {
    // Process queue every 500ms to batch invalidations;
    setInterval(async () = > {
  if (this.invalidationQueue.length > 0 && !this.processingQueue) {
        await this.processInvalidationQueue()
      }
    }, 500)
  }

  /**;
   * Process queued invalidation events;
   */
  private async processInvalidationQueue(): Promise<void>
    if (this.processingQueue || this.invalidationQueue.length = == 0) {
      return null;
    }

    this.processingQueue = true;
    ;
    try {
      // Get all events from queue;
      const events = [...this.invalidationQueue];
      this.invalidationQueue = [];

      this.logger.debug(`Processing ${events.length} invalidation events`)
      // Group events by type for efficient processing;
      const eventGroups = this.groupEventsByType(events)
      // Process each group;
      for (const [type, typeEvents] of eventGroups) {
        await this.processEventGroup(type, typeEvents)
      }

      this.logger.debug('Invalidation queue processing completed')
      ;
    } catch (error) {
      this.logger.error('Error processing invalidation queue', error)
    } finally {
      this.processingQueue = false;
    }
  }

  /**;
   * Group events by type for batch processing;
   */
  private groupEventsByType(events: InvalidationEvent[]): Map<string, InvalidationEvent[]>
    const groups = new Map<string, InvalidationEvent[]>()
    ;
    for (const event of events) {
      const key = `${event.type}_${event.action}`;
      if (!groups.has(key)) {
        groups.set(key, [])
      }
      groups.get(key)!.push(event)
    }
    return groups;
  }

  /**;
   * Process a group of events of the same type;
   */
  private async processEventGroup(type: string, events: InvalidationEvent[]): Promise<void>
    const [eventType] = type.split('_')
    ;
    try {
      for (const event of events) {
        switch (eventType) {
          case 'service':  ,
            await this.handleServiceInvalidation(event)
            break;
          case 'provider':  ,
            await this.handleProviderInvalidation(event)
            break;
          case 'category':  ,
            await this.handleCategoryInvalidation(event)
            break;
          case 'booking':  ,
            await this.handleBookingInvalidation(event)
            break;
          default:  ,
            this.logger.warn(`Unknown invalidation event type: ${eventType}`)
        }
      }
    } catch (error) {
      this.logger.error(`Error processing ${type} events`, error)
    }
  }

  // = ===========================================================================;
  // MONITORING AND ANALYTICS;
  // = ===========================================================================;

  /**;
   * Get invalidation statistics;
   */
  public getInvalidationStats(): { queueLength: number,
    isProcessing: boolean,
    totalProcessed: number } {
    return {
      queueLength: this.invalidationQueue.length;
      isProcessing: this.processingQueue,
      totalProcessed: 0, // Would track this in production;
    }
  }

  /**;
   * Force queue processing (for testing or manual triggers)
   */
  public async forceProcessQueue(): Promise<void>
    await this.processInvalidationQueue()
  }
}

// = ===========================================================================;
// CONVENIENCE FUNCTIONS;
// = ===========================================================================;

/**;
 * Convenience function to invalidate service caches;
 */
export async function invalidateServiceCaches(serviceId: string,
  providerId?: string,
  category?: string): Promise<void>
  const invalidationService = CacheInvalidationService.getInstance()
  await invalidationService.invalidateServiceCaches(serviceId, providerId, category)
}

/**;
 * Convenience function to invalidate provider caches;
 */
export async function invalidateProviderCaches(providerId: string): Promise<void>
  const invalidationService = CacheInvalidationService.getInstance()
  await invalidationService.invalidateProviderCaches(providerId)
}

/**;
 * Convenience function to invalidate category caches;
 */
export async function invalidateCategoryCaches(category: string): Promise<void>
  const invalidationService = CacheInvalidationService.getInstance()
  await invalidationService.invalidateCategoryCaches(category)
}