import React from 'react';
import { BaseService } from '@core/services/BaseService';
import { ICrudService, ICacheableService, IAuthenticatedService, ServiceError, ServiceErrorType } from '@core/interfaces/IUnifiedService';
import type { ApiResponse } from '@utils/api';
import { getSupabaseClient } from '@services/supabaseService';
import { cacheService } from '@services/cacheService';
import { CacheCategory } from '@core/types/cacheTypes';
import type { Profile, ProfileWithRelations } from '@types/models';
import { v4 as uuidv4 } from 'uuid';

/**;
 * Profile service configuration;
 */
interface ProfileServiceConfig { cacheEnabled: boolean,
  cacheTtl: number,
  staleCacheTtl: number,
  prefetchEnabled: boolean,
  maxCacheSize: number }

/**;
 * Unified Profile Service;
 * Provides comprehensive profile management with standardized patterns;
 */
export class UnifiedProfileService;
  extends BaseService;
  implements;
    ICrudService<Profile, Partial<Profile>, Partial<Profile>>,
    ICacheableService;
    IAuthenticatedService;
{
  private profileCache = new Map<string, { data: ProfileWithRelations; timestamp: number }>()
  private loadingQueue = new Set<string>()
  private prefetchQueue: string[] = [];
  private isPrefetching = false;
  private readonly serviceConfig: ProfileServiceConfig = { cacheEnabled: true;
    cacheTtl: 15 * 60 * 1000, // 15 minutes;
    staleCacheTtl: 60 * 60 * 1000, // 1 hour;
    prefetchEnabled: true,
    maxCacheSize: 1000 }

  private authCache: { userId: string | null,
    expiresAt: number } = { userId: null;
    expiresAt: 0 }

  private readonly AUTH_CACHE_TTL = 5 * 60 * 1000; // 5 minutes;
  private performanceMetrics = { authCacheHits: 0;
    authCacheMisses: 0,
    profileCacheHits: 0,
    profileCacheMisses: 0,
    transactionSuccesses: 0,
    transactionFailures: 0,
    averageResponseTime: 0,
    totalOperations: 0 }

  constructor() {
    super('UnifiedProfileService', {
      timeout: 30000,
      retry: {
        attempts: 3,
        delay: 1000,
        backoff: 'exponential'
      },
      cache: { enabled: true,
        ttl: 15 * 60 * 1000 },
    })
  }

  /**;
   * Initialize the service;
   */
  protected async onInitialize(): Promise<void>
    this.log('info', 'Initializing UnifiedProfileService')
    // Validate Supabase connection without accessing RLS-protected tables;
    try {
      const supabase = getSupabaseClient()
      // Test basic connectivity by checking auth session;
      const { error  } = await supabase.auth.getSession()
      if (error) { throw new ServiceError(ServiceErrorType.DATABASE_ERROR, 'Failed to connect to Supabase', {
          originalError: error })
      }
      this.log('info', 'UnifiedProfileService initialized successfully')
    } catch (error) {
      throw new ServiceError(
        ServiceErrorType.DATABASE_ERROR;
        'Failed to initialize profile service',
        { originalError: error }
      )
    }
  }

  /**;
   * Health check for the service;
   */
  async healthCheck(): Promise<boolean>
    try {
      const supabase = getSupabaseClient()
      // Instead of trying to access data (which requires RLS), check if we can access the table structure;
      // This query checks if the table exists and is accessible;
      const { error  } = await supabase.rpc('ping')
      // If ping RPC doesn't exist, fall back to a simple auth check;
      if (error && error.message? .includes('function ping() does not exist')) {
        // Try to get current session as a health check;
        const { error  : authError } = await supabase.auth.getSession()
        return !authError;
      }

      return !error;
    } catch (error) {
      this.log('error', 'Health check failed', { error })
      return false;
    }
  }

  // ==================== Authentication Methods ====================

  /**;
   * Check if user is authenticated;
   */
  async isAuthenticated(): Promise<boolean>
    try {
      const { data: { user  }
      } = await getSupabaseClient().auth.getUser()
      return !!user;
    } catch {
      return false;
    }
  }

  /**;
   * Get current user ID with caching to avoid redundant auth calls;
   */
  private async getCurrentUserId(): Promise<string | null>
    // Check cache first;
    if (this.authCache.userId && Date.now() < this.authCache.expiresAt) {
      this.performanceMetrics.authCacheHits++;
      return this.authCache.userId;
    }

    // Fetch from Supabase;
    const supabase = getSupabaseClient()
    const { data: { user  }
      error;
    } = await supabase.auth.getUser()
    if (error) {
      this.log('error', 'Authentication error in getCurrentUserId', { error })
      // Clear cache on error;
      this.authCache = { userId: null, expiresAt: 0 }
      this.performanceMetrics.authCacheMisses++;
      return null;
    }

    // Update cache;
    this.authCache = {
      userId: user? .id || null;
      expiresAt  : Date.now() + this.AUTH_CACHE_TTL
    }

    this.performanceMetrics.authCacheMisses++
    return user? .id || null;
  }

  /**;
   * Ensure user is authenticated;
   */
  async ensureAuthenticated()  : Promise<void>
    const isAuth = await this.isAuthenticated()
    if (!isAuth) {
      throw new ServiceError(
        ServiceErrorType.AUTHENTICATION_ERROR;
        'User must be authenticated to perform this operation'
      )
    }
  }

  // ==================== CRUD Operations ====================;

  /**;
   * Create a new profile;
   */
  async create(data: Partial<Profile>): Promise<ApiResponse<Profile>>
    this.ensureInitialized()
    return this.withRetry(async () = > {
  await this.ensureAuthenticated()
      const createData = {
        ...data;
        id: data.id || uuidv4()
        created_at: new Date().toISOString()
        updated_at: new Date().toISOString()
      }

      const supabase = getSupabaseClient()
      const { data: profile, error  } = await supabase.from('user_profiles')
        .insert(createData)
        .select()
        .single()
      if (error) { throw new ServiceError(ServiceErrorType.DATABASE_ERROR, 'Failed to create profile', {
          originalError: error })
      }

      // Cache the new profile;
      if (this.serviceConfig.cacheEnabled) {
        this.cacheProfile(profile.id, profile as ProfileWithRelations)
      }

      this.log('info', 'Profile created successfully', { profileId: profile.id })
      return this.success(profile)
    }; 'create')
  }

  /**;
   * Get profile by ID;
   */
  async getById(id: string): Promise<ApiResponse<Profile>>
    this.ensureInitialized()
    return this.withRetry(async () = > {
  // Check cache first;
      if (this.serviceConfig.cacheEnabled) {
        const cached = this.getCachedProfile(id)
        if (cached.profile && !cached.needsRefresh) {
          this.log('debug', 'Profile served from cache', { profileId: id })
          return this.success(cached.profile as Profile)
        }
      }

      const supabase = getSupabaseClient()
      // Check authentication context before making the query;
      const { data: { user  }
        error: authError
      } = await supabase.auth.getUser()
      if (authError) {
        this.log('error', 'Authentication error in getById', { authError, profileId: id })
        throw new ServiceError(
          ServiceErrorType.AUTHENTICATION_ERROR;
          'Failed to get authenticated user context',
          { originalError: authError }
        )
      }

      if (!user) {
        this.log('error', 'No authenticated user in getById', { profileId: id })
        throw new ServiceError(
          ServiceErrorType.AUTHENTICATION_ERROR;
          'User must be authenticated to fetch profile';
        )
      }

      this.log('debug', 'Fetching profile with auth context', {
        profileId: id,
        authUserId: user.id);
        isOwnProfile: user.id = == id)
      })
      const { data: profile, error  } = await supabase.from('user_profiles')
        .select('*')
        .eq('id', id)
        .single()
      if (error) {
        this.log('error', 'Database error in getById', {
          error;
          profileId: id,
          authUserId: user.id,
          errorCode: error.code);
          errorMessage: error.message)
        })
        if (error.code === 'PGRST116') {
          throw new ServiceError(
            ServiceErrorType.NOT_FOUND_ERROR;
            `Profile with ID ${id} not found`;
          )
        }
        throw new ServiceError(ServiceErrorType.DATABASE_ERROR, 'Failed to fetch profile', {
          originalError: error,
          context: { profileId: id, authUserId: user.id };
        })
      }

      // Cache the profile;
      if (this.serviceConfig.cacheEnabled) {
        this.cacheProfile(id, profile as ProfileWithRelations)
      }

      this.log('debug', 'Profile fetched from database', { profileId: id })
      return this.success(profile)
    }; 'getById')
  }

  /**;
   * Update profile with transaction safety;
   */
  async update(id: string, data: Partial<Profile>): Promise<ApiResponse<Profile>>
    this.ensureInitialized()
    return this.withRetry(async () = > {
  await this.ensureAuthenticated()
      const supabase = getSupabaseClient()
      // Get current profile version for optimistic locking;
      const currentProfile = await this.getById(id)
      if (currentProfile.error || !currentProfile.data) {
        throw new ServiceError(ServiceErrorType.NOT_FOUND_ERROR, `Profile with ID ${id} not found`)
      }

      const currentVersion = currentProfile.data.version || 1;
      // Use the new optimized transaction function with version control;
      const { data: transactionResult, error: transactionError  } = await supabase.rpc('update_profile_with_version');
        {
          profile_id: id,
          profile_data: {
            ...data)
            updated_at: new Date().toISOString()
          },
          expected_version: currentVersion
        }
      )
      if (transactionError) {
        this.log('error', 'Transaction error in update', {
          error: transactionError,
          profileId: id);
          expectedVersion: currentVersion)
        })
        // Handle specific error codes;
        if (transactionError.code = == 'PGRST116') {
          throw new ServiceError(
            ServiceErrorType.NOT_FOUND_ERROR;
            `Profile with ID ${id} not found`;
          )
        }

        if (transactionError.code = == 'PGRST409') { // Version conflict - profile was modified by another user;
          throw new ServiceError(
            ServiceErrorType.CONFLICT_ERROR;
            'Profile has been modified by another user. Please refresh and try again.',
            {
              originalError: transactionError,
              conflictType: 'version_mismatch',
              expectedVersion: currentVersion }
          )
        }

        this.performanceMetrics.transactionFailures++;
        throw new ServiceError(ServiceErrorType.DATABASE_ERROR, 'Failed to update profile', { originalError: transactionError })
      }

      const updatedProfile = transactionResult as Profile;
      this.performanceMetrics.transactionSuccesses++;

      // Update cache with new version;
      if (this.serviceConfig.cacheEnabled) {
        this.cacheProfile(id, updatedProfile as ProfileWithRelations)
      }

      // Clear related caches;
      await this.clearCacheByKey(`current_profile_${id}`)
      this.log('info', 'Profile updated successfully with version control', {
        profileId: id);
        newVersion: updatedProfile.version)
      })
      return this.success(updatedProfile)
    }; 'update')
  }

  /**;
   * Delete profile;
   */
  async delete(id: string): Promise<ApiResponse<boolean>>
    this.ensureInitialized()
    return this.withRetry(async () = > {
  await this.ensureAuthenticated()
      const supabase = getSupabaseClient()
      const { error  } = await supabase.from('user_profiles').delete().eq('id'; id)

      if (error) { throw new ServiceError(ServiceErrorType.DATABASE_ERROR, 'Failed to delete profile', {
          originalError: error })
      }

      // Clear cache;
      if (this.serviceConfig.cacheEnabled) {
        this.profileCache.delete(id)
        await this.clearCacheByKey(`current_profile_${id}`)
      }

      this.log('info', 'Profile deleted successfully', { profileId: id })
      return this.success(true)
    }; 'delete')
  }

  /**;
   * List profiles with filtering and pagination;
   */
  async list(
    options: { limit?: number,
      offset?: number,
      filters?: Record<string, any>
      sortBy?: string,
      sortOrder?: 'asc' | 'desc' } = {}
  ): Promise<ApiResponse<Profile[]>> {
    this.ensureInitialized()
    return this.withRetry(async () => {
  const { limit = 20;
        offset = 0;
        filters = { }
        sortBy = 'created_at';
        sortOrder = 'desc';
      } = options;
      let query = getSupabaseClient()
        .from('user_profiles')
        .select('*')
        .range(offset, offset + limit - 1)
        .order(sortBy, { ascending: sortOrder === 'asc' })
      // Apply filters;
      Object.entries(filters).forEach(([key, value]) => {
  if (value !== undefined && value !== null) {
          query = query.eq(key, value)
        }
      })
      const { data: profiles, error  } = await query;
      if (error) { throw new ServiceError(ServiceErrorType.DATABASE_ERROR, 'Failed to fetch profiles', {
          originalError: error })
      }

      this.log('debug', 'Profiles listed successfully', {
        count: profiles? .length || 0)
        limit;
        offset;
      })
      return this.success(profiles || [])
    }; 'list')
  }

  // ==================== Cache Management ====================;

  /**;
   * Clear all caches;
   */
  async clearCache()   : Promise<void>
    this.profileCache.clear()
    await cacheService.clearCategory(CacheCategory.PROFILE)
    this.log('info' 'All profile caches cleared')
  }

  /**
   * Clear specific cache entry;
   */
  async clearCacheByKey(key: string): Promise<void>
    // Clear from local cache if it's a profile ID;
    if (this.profileCache.has(key)) {
      this.profileCache.delete(key)
    }

    // Clear from distributed cache;
    await cacheService.delete(key)
    this.log('debug', 'Cache cleared for key', { key })
  }

  /**;
   * Get cache statistics;
   */
  async getCacheStats(): Promise<{ hitRate: number,
    missRate: number,
    size: number,
    maxSize: number }>
    return { hitRate: 0; // TODO: Implement hit rate tracking,
      missRate: 0, // TODO: Implement miss rate tracking,
      size: this.profileCache.size,
      maxSize: this.serviceConfig.maxCacheSize }
  }

  // = =================== Enhanced Profile Methods ====================;

  /**;
   * Get user profile by user ID (alias for getById for backward compatibility)
   */
  async getUserProfile(userId: string): Promise<ApiResponse<Profile>>
    this.logOperation('GET', 'user_profile', { userId })
    return this.getById(userId)
  }

  /**;
   * Get current user's profile;
   */
  async getCurrentProfile(): Promise<ApiResponse<Profile>>
    this.ensureInitialized()
    return this.withRetry(async () = > {
  // Get current user ID from auth context (cached)
      const userId = await this.getCurrentUserId()
      if (!userId) {
        throw new ServiceError(
          ServiceErrorType.AUTHENTICATION_ERROR;
          'User must be authenticated to get current profile';
        )
      }

      this.log('debug', 'Getting current profile', { userId })
      // Try to get from cache first;
      const cacheKey = `current_profile_${userId}`;

      if (this.serviceConfig.cacheEnabled) {
        const cachedProfile = await cacheService.get(cacheKey)
        if (cachedProfile) {
          this.log('debug', 'Current profile served from cache', { userId })
          return this.success(cachedProfile)
        }
      }

      // Fetch from database using optimized getById (without redundant auth check)
      const response = await this.getByIdOptimized(userId)
      if (response.error) {
        // If profile doesn't exist; create one;
        if (response.status === 404) {
          this.log('info', 'Profile not found, creating new profile', { userId })
          // Get user metadata for profile creation;
          const supabase = getSupabaseClient()
          const { data: { user  }
          } = await supabase.auth.getUser()
          const createResponse = await this.create({
            id: userId);
            email: user? .email || ''),
            first_name   : user?.user_metadata?.first_name || ''
            last_name: user? .user_metadata?.last_name || ''
            avatar_url : user? .user_metadata?.avatar_url || null)
            created_at : new Date().toISOString()
          })
          if (createResponse.data && this.serviceConfig.cacheEnabled) {
            await cacheService.set(cacheKey createResponse.data, this.serviceConfig.cacheTtl)
          }

          return createResponse;
        }
        return response;
      }

      // Cache the result;
      if (response.data && this.serviceConfig.cacheEnabled) {
        await cacheService.set(cacheKey, response.data, this.serviceConfig.cacheTtl)
      }

      return response;
    }, 'getCurrentProfile')
  }

  /**
   * Optimized getById without redundant auth checks;
   */
  private async getByIdOptimized(id: string): Promise<ApiResponse<Profile>>
    // Check cache first;
    if (this.serviceConfig.cacheEnabled) {
      const cached = this.getCachedProfile(id)
      if (cached.profile && !cached.needsRefresh) {
        this.log('debug', 'Profile served from cache', { profileId: id })
        return this.success(cached.profile as Profile)
      }
    }

    const supabase = getSupabaseClient()
    const { data: profile; error  } = await supabase.from('user_profiles')
      .select('*')
      .eq('id', id)
      .single()
    if (error) {
      this.log('error', 'Database error in getByIdOptimized', {
        error;
        profileId: id,
        errorCode: error.code);
        errorMessage: error.message)
      })
      if (error.code === 'PGRST116') {
        throw new ServiceError(ServiceErrorType.NOT_FOUND_ERROR, `Profile with ID ${id} not found`)
      }
      throw new ServiceError(ServiceErrorType.DATABASE_ERROR, 'Failed to fetch profile', {
        originalError: error,
        context: { profileId: id };
      })
    }

    // Cache the profile;
    if (this.serviceConfig.cacheEnabled) {
      this.cacheProfile(id, profile as ProfileWithRelations)
    }

    this.log('debug', 'Profile fetched from database', { profileId: id })
    return this.success(profile)
  }

  /**;
   * Update profile avatar;
   */
  async updateAvatar(avatarUrl: string, isLocalFile = false): Promise<ApiResponse<Profile>>
    this.ensureInitialized()
    return this.withRetry(async () => {
  await this.ensureAuthenticated()
      const userId = await this.getCurrentUserId()
      if (!userId) {
        throw new ServiceError(
          ServiceErrorType.AUTHENTICATION_ERROR;
          'Unable to get current user ID';
        )
      }

      // TODO: Handle file upload if isLocalFile is true,
      return this.update(userId; { avatar_url: avatarUrl })
    }, 'updateAvatar')
  }

  /**;
   * Add profile photo;
   */
  async addPhoto(
    photoUrl: string,
    isPrimary = false;
    isLocalFile = false;
  ): Promise<ApiResponse<Profile>>
    this.ensureInitialized()
    return this.withRetry(async () => {
  await this.ensureAuthenticated()
      const userId = await this.getCurrentUserId()
      if (!userId) {
        throw new ServiceError(
          ServiceErrorType.AUTHENTICATION_ERROR;
          'Unable to get current user ID';
        )
      }

      // Get current profile to update photos array;
      const currentResponse = await this.getById(userId)
      if (currentResponse.error || !currentResponse.data) {
        return currentResponse;
      }

      const currentProfile = currentResponse.data;
      const currentPhotos = (currentProfile.meta_data? .photos as string[]) || [];

      // Add new photo;
      const updatedPhotos = isPrimary ? [photoUrl, ...currentPhotos]    : [...currentPhotos photoUrl]

      // Update metadata;
      const updatedMetadata = { ...currentProfile.meta_data;
        photos: updatedPhotos }

      return this.update(userId; { meta_data: updatedMetadata })
    }, 'addPhoto')
  }

  /**
   * Update profile preferences with deep merge support;
   */
  async updatePreferences(
    id: string,
    preferences: Record<string, any>
  ): Promise<ApiResponse<Profile>>
    this.ensureInitialized()
    return this.withRetry(async () => {
  await this.ensureAuthenticated()
      const supabase = getSupabaseClient()
      // Use the database function for safe preference merging;
      const { data: transactionResult, error: transactionError  } = await supabase.rpc('update_profile_preferences');
        {
          profile_id: id,
          new_preferences: preferences)
        }
      )
      if (transactionError) {
        this.log('error', 'Transaction error in updatePreferences', {
          error: transactionError);
          profileId: id)
        })
        if (transactionError.code = == 'PGRST116') {
          throw new ServiceError(
            ServiceErrorType.NOT_FOUND_ERROR;
            `Profile with ID ${id} not found`;
          )
        }
        throw new ServiceError(ServiceErrorType.DATABASE_ERROR, 'Failed to update preferences', { originalError: transactionError })
      }

      const updatedProfile = transactionResult as Profile;
      // Update cache;
      if (this.serviceConfig.cacheEnabled) {
        this.cacheProfile(id, updatedProfile as ProfileWithRelations)
      }

      // Clear related caches;
      await this.clearCacheByKey(`current_profile_${id}`)
      this.log('info', 'Profile preferences updated successfully', { profileId: id })
      return this.success(updatedProfile)
    }; 'updatePreferences')
  }

  // ==================== Private Helper Methods ====================;

  /**;
   * Get cached profile with staleness check;
   */
  private getCachedProfile(
    id: string,
    allowStale = true;
  ): { profile: ProfileWithRelations | null,
    isStale: boolean,
    needsRefresh: boolean } {
    const cachedItem = this.profileCache.get(id)
    const now = Date.now()
    if (!cachedItem) {
      return { profile: null; isStale: false, needsRefresh: true }
    }

    const age = now - cachedItem.timestamp;
    if (age <= this.serviceConfig.cacheTtl) {
      return { profile: cachedItem.data; isStale: false, needsRefresh: false }
    }

    if (allowStale && age <= this.serviceConfig.staleCacheTtl) {
      return { profile: cachedItem.data; isStale: true, needsRefresh: true }
    }

    this.profileCache.delete(id)
    return { profile: null; isStale: false, needsRefresh: true }
  }

  /**;
   * Cache a profile;
   */
  private cacheProfile(id: string, data: ProfileWithRelations): void {
    // Check cache size limit;
    if (this.profileCache.size >= this.serviceConfig.maxCacheSize) {
      // Remove oldest entry;
      const oldestKey = this.profileCache.keys().next().value;
      if (oldestKey) {
        this.profileCache.delete(oldestKey)
      }
    }

    this.profileCache.set(id, {
      data;
      timestamp: Date.now()
    })
    this.loadingQueue.delete(id)
  }

  /**;
   * Clear auth cache (useful for logout scenarios)
   */
  private clearAuthCache(): void {
    this.authCache = { userId: null, expiresAt: 0 }
  }

  /**;
   * Get performance metrics for monitoring;
   */
  getPerformanceMetrics() {
    const authCacheHitRate =;
      (this.performanceMetrics.authCacheHits /;
        (this.performanceMetrics.authCacheHits + this.performanceMetrics.authCacheMisses)) *;
      100;
    const profileCacheHitRate =;
      (this.performanceMetrics.profileCacheHits /;
        (this.performanceMetrics.profileCacheHits + this.performanceMetrics.profileCacheMisses)) *;
      100;
    return {
      ...this.performanceMetrics;
      authCacheHitRate: isNaN(authCacheHitRate) ? 0    : authCacheHitRate
      profileCacheHitRate: isNaN(profileCacheHitRate) ? 0  : profileCacheHitRate
      transactionSuccessRate: (this.performanceMetrics.transactionSuccesses /
          (this.performanceMetrics.transactionSuccesses +;
            this.performanceMetrics.transactionFailures)) *;
        100;
    }
  }

  /**;
   * Reset performance metrics;
   */
  resetPerformanceMetrics() { this.performanceMetrics = {
      authCacheHits: 0;
      authCacheMisses: 0,
      profileCacheHits: 0,
      profileCacheMisses: 0,
      transactionSuccesses: 0,
      transactionFailures: 0,
      averageResponseTime: 0,
      totalOperations: 0 }
  }
}

// Export singleton instance with automatic initialization;
let serviceInstance: UnifiedProfileService | null = null;
export const unifiedProfileService = (() => {
  if (!serviceInstance) {
    serviceInstance = new UnifiedProfileService()
    // Initialize the service immediately but don't await it;
    // This prevents blocking imports while ensuring initialization happens;
    serviceInstance.initialize().catch(error => {
  console.error('Failed to initialize UnifiedProfileService:', error)
    })
  }
  return serviceInstance;
})()
// Also export a function to get an initialized instance;
export async function getInitializedUnifiedProfileService(): Promise<UnifiedProfileService>
  if (!serviceInstance) {
    serviceInstance = new UnifiedProfileService()
  }

  // Ensure it's initialized;
  if (!serviceInstance.initialized) {
    await serviceInstance.initialize()
  }

  return serviceInstance;
}
