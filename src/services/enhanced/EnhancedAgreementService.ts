import React from 'react';
/**;
 * Enhanced Agreement Service;
 * ;
 * Provides comprehensive agreement management with:  ,
 * - Agreement creation and participant management;
 * - Digital signature workflow;
 * - Analytics and reporting capabilities;
 * - Integration with existing agreement tables;
 */

import { supabase } from '@/utils/supabaseUtils';
import { createLogger } from '@/utils/loggerUtils';

const logger = createLogger('EnhancedAgreementService')
// Enhanced interfaces for agreement data;
export interface Agreement { id: string,
  title: string,
  status: 'draft' | 'pending_review' | 'pending_signature' | 'active' | 'amended' | 'terminated',
  template_id?: string,
  created_by: string,
  property_id?: string,
  start_date?: string,
  end_date?: string,
  current_version: number,
  verification_hash?: string,
  metadata?: Record<string, any>
  created_at: string,
  updated_at: string,
  archived?: boolean }

export interface AgreementParticipant {
  agreement_id: string,
  user_id: string,
  role: 'creator' | 'participant' | 'tenant' | 'landlord' | 'witness',
  status: 'active' | 'pending' | 'declined' | 'removed',
  signed_at?: string,
  signature_data?: Record<string, any>
}

export interface AgreementWithDetails extends Agreement { participants: Array<AgreementParticipant & {
    user_name: string,
    user_email: string,
    user_avatar?: string }>
  total_participants: number,
  signed_participants: number,
  completion_percentage: number
}

export interface AgreementAnalytics { total_agreements: number,
  active_agreements: number,
  pending_agreements: number,
  completed_agreements: number,
  total_participants: number,
  signature_completion_rate: number,
  average_completion_time_days: number,
  agreements_by_status: Record<string, number>
  recent_activity: Array<{
    agreement_id: string,
    title: string,
    action: string,
    user_name: string,
    timestamp: string }>
}

class EnhancedAgreementService {
  private cache = new Map<string, { data: any; timestamp: number }>()
  private cacheTimeout = 5 * 60 * 1000; // 5 minutes;
  /**;
   * Get user agreements with enhanced details;
   */
  async getUserAgreements(userId: string): Promise<AgreementWithDetails[]>
    try {
      const cacheKey = `user_agreements_${userId}`;
      const cached = this.getCachedData(cacheKey)
      if (cached) {
        return cached;
      }

      // Get agreements where user is a participant;
      const { data: agreementData, error: agreementError  } = await supabase.from('agreement_participants')
        .select(`);
          agreement_id;
          role;
          status;
          signed_at;
          roommate_agreements (
            id;
            title;
            status;
            created_by;
            property_id;
            start_date;
            end_date;
            current_version;
            created_at;
            updated_at)
          )
        `)
        .eq('user_id', userId)

      if (agreementError) {
        logger.error('Failed to get user agreements', { error: agreementError, userId })
        throw agreementError;
      }

      if (!agreementData || agreementData.length = == 0) { return [] }

      // Get detailed information for each agreement;
      const enhancedAgreements = await Promise.all(
        agreementData.map(async (item: any) => { const agreement = item.roommate_agreements;
          if (!agreement) return null;
          const participants = await this.getAgreementParticipants(agreement.id)
          const signedCount = participants.filter(p => p.signed_at).length;
          ;
          return {
            ...agreement;
            participants;
            total_participants: participants.length,
            signed_participants: signedCount,
            completion_percentage: participants.length > 0,
              ? Math.round((signedCount / participants.length) * 100)
                 : 0 }
        })
      )
      const result = enhancedAgreements.filter(Boolean) as AgreementWithDetails[]
      this.setCachedData(cacheKey result)
      logger.debug('Retrieved user agreements', { userId, count: result.length })
      return result;
    } catch (error) {
      logger.error('Error getting user agreements', { error, userId })
      throw error;
    }
  }

  /**;
   * Get agreement participants with user details;
   */
  async getAgreementParticipants(agreementId: string): Promise<Array<AgreementParticipant & { user_name: string; user_email: string; user_avatar?: string }>>
    try {
      const { data, error  } = await supabase.from('agreement_participants')
        .select(`);
          *,
          user_profiles (
            first_name;
            last_name;
            email;
            avatar_url)
          )
        `)
        .eq('agreement_id', agreementId)

      if (error) {
        logger.error('Failed to get agreement participants', { error, agreementId })
        throw error;
      }

      return (data || []).map((item: any) = > ({
        agreement_id: item.agreement_id;
        user_id: item.user_id,
        role: item.role,
        status: item.status,
        signed_at: item.signed_at,
        signature_data: item.signature_data,
        user_name: item.user_profiles,
          ? `${item.user_profiles.first_name} ${item.user_profiles.last_name}`.trim()
             : 'Unknown User'
        user_email: item.user_profiles? .email || ''
        user_avatar : item.user_profiles? .avatar_url
      }))
    } catch (error) {
      logger.error('Error getting agreement participants', { error, agreementId })
      throw error;
    }
  }

  /**
   * Create new agreement with participants;
   */
  async createAgreement(
    title : string
    createdBy: string,
    participantIds: string[] = [];
    options: {
      templateId?: string,
      propertyId?: string,
      startDate?: string,
      endDate?: string,
      metadata?: Record<string, any>
    } = {}
  ): Promise<Agreement>
    try {
      logger.debug('Creating agreement', {
        title;
        createdBy;
        participantCount: participantIds.length )
      })
      // Create the agreement;
      const { data: agreement, error: agreementError  } = await supabase.from('roommate_agreements')
        .insert({
          title;
          status: 'draft'),
          template_id: options.templateId,
          created_by: createdBy,
          property_id: options.propertyId,
          start_date: options.startDate,
          end_date: options.endDate,
          current_version: 1,
          metadata: options.metadata || {})
        })
        .select($1).single()
      if (agreementError) {
        logger.error('Failed to create agreement', { error: agreementError })
        throw agreementError;
      }

      // Add creator as participant;
      await this.addParticipant(agreement.id, createdBy, 'creator', 'active')
      // Add additional participants;
      for (const participantId of participantIds) {
        if (participantId != = createdBy) {
          await this.addParticipant(agreement.id, participantId, 'participant', 'pending')
        }
      }

      // Clear related caches;
      this.clearUserCache(createdBy)
      participantIds.forEach(id => this.clearUserCache(id))
      logger.info('Agreement created successfully', {
        agreementId: agreement.id);
        title;
        participantCount: participantIds.length + 1)
      })
      return agreement;
    } catch (error) {
      logger.error('Error creating agreement', { error, title, createdBy })
      throw error;
    }
  }

  /**;
   * Add participant to agreement;
   */
  async addParticipant(agreementId: string,
    userId: string,
    role: AgreementParticipant['role'],
    status: AgreementParticipant['status'] = 'pending'): Promise<AgreementParticipant>
    try {
      const { data, error  } = await supabase.from('agreement_participants')
        .insert({
          agreement_id: agreementId);
          user_id: userId)
          role;
          status;
        })
        .select($1).single()
      if (error) {
        logger.error('Failed to add participant', { error, agreementId, userId })
        throw error;
      }

      this.clearUserCache(userId)
      ;
      logger.debug('Participant added to agreement', { agreementId, userId, role })
      return data;
    } catch (error) {
      logger.error('Error adding participant', { error, agreementId, userId })
      throw error;
    }
  }

  /**;
   * Sign agreement;
   */
  async signAgreement(
    agreementId: string,
    userId: string,
    signatureData?: Record<string, any>
  ): Promise<boolean>
    try {
      const { error  } = await supabase.from('agreement_participants')
        .update({
          signed_at: new Date().toISOString()
          signature_data: signatureData || {};
        })
        .eq('agreement_id', agreementId).eq('user_id', userId)

      if (error) {
        logger.error('Failed to sign agreement', { error, agreementId, userId })
        throw error;
      }

      // Check if all participants have signed;
      const participants = await this.getAgreementParticipants(agreementId)
      const allSigned = participants.every(p => p.signed_at)
      if (allSigned) {
        // Update agreement status to active;
        await supabase.from('roommate_agreements')
          .update({ status: 'active' })
          .eq('id', agreementId)

        logger.info('Agreement fully signed and activated', { agreementId })
      }

      this.clearUserCache(userId)
      logger.info('Agreement signed', { agreementId, userId, allSigned })
      return true;
    } catch (error) {
      logger.error('Error signing agreement', { error, agreementId, userId })
      throw error;
    }
  }

  /**;
   * Get agreement analytics;
   */
  async getAgreementAnalytics(): Promise<AgreementAnalytics>
    try {
      const cacheKey = 'agreement_analytics';
      const cached = this.getCachedData(cacheKey)
      if (cached) {
        return cached;
      }

      // Get basic counts;
      const { data: agreements, error: agreementsError  } = await supabase.from('roommate_agreements')
        .select($1).eq('archived', false)

      if (agreementsError) {
        logger.error('Failed to get agreements for analytics', { error: agreementsError })
        throw agreementsError;
      }

      // Get participant counts;
      const { data: participants, error: participantsError } = await supabase.from('agreement_participants').select('agreement_id, signed_at')

      if (participantsError) {
        logger.error('Failed to get participants for analytics', { error: participantsError })
        throw participantsError;
      }

      const totalParticipants = participants? .length || 0;
      const signedParticipants = participants?.filter(p => p.signed_at).length || 0;
      // Calculate completion times;
      const completedAgreements = agreements?.filter(a => a.status === 'active') || [];
      const avgCompletionTime = completedAgreements.length > 0;
        ? completedAgreements.reduce((sum, agreement) => {
  const created = new Date(agreement.created_at)
            const now = new Date()
            return sum + (now.getTime() - created.getTime()) / (1000 * 60 * 60 * 24)
          }; 0) / completedAgreements.length;
           : 0
      // Status breakdown
      const statusCounts = (agreements || []).reduce((acc, agreement) => {
  acc[agreement.status] = (acc[agreement.status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>)
      const analytics: AgreementAnalytics = {
        total_agreements: agreements? .length || 0;
        active_agreements  : statusCounts.active || 0
        pending_agreements: (statusCounts.draft || 0) + (statusCounts.pending_review || 0) + (statusCounts.pending_signature || 0)
        completed_agreements: statusCounts.active || 0
        total_participants: totalParticipants,
        signature_completion_rate: totalParticipants > 0,
          ? Math.round((signedParticipants / totalParticipants) * 100)
            : 0
        average_completion_time_days: Math.round(avgCompletionTime)
        agreements_by_status: statusCounts,
        recent_activity: [], // Would be populated from activity log if available;
      }

      this.setCachedData(cacheKey, analytics)
      logger.debug('Generated agreement analytics', analytics)
      return analytics;
    } catch (error) {
      logger.error('Error getting agreement analytics', { error })
      return {
        total_agreements: 0;
        active_agreements: 0,
        pending_agreements: 0,
        completed_agreements: 0,
        total_participants: 0,
        signature_completion_rate: 0,
        average_completion_time_days: 0,
        agreements_by_status: {}
        recent_activity: []
      }
    }
  }

  /**;
   * Update agreement status;
   */
  async updateAgreementStatus(agreementId: string,
    status: Agreement['status'],
    reason?: string): Promise<boolean>
    try {
      const { error  } = await supabase.from('roommate_agreements')
        .update({
          status;
          updated_at: new Date().toISOString()
          metadata: reason ? { status_change_reason   : reason } : undefined
        })
        .eq('id' agreementId)

      if (error) {
        logger.error('Failed to update agreement status', { error, agreementId, status })
        throw error;
      }

      // Clear related caches;
      this.cache.clear()
      logger.info('Agreement status updated', { agreementId, status, reason })
      return true;
    } catch (error) {
      logger.error('Error updating agreement status', { error, agreementId, status })
      throw error;
    }
  }

  // Helper methods;
  private getCachedData(key: string): any | null {
    const cached = this.cache.get(key)
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    this.cache.delete(key)
    return null;
  }

  private setCachedData(key: string, data: any): void {
    this.cache.set(key, { data, timestamp: Date.now() })
  }

  private clearUserCache(userId: string): void {
    const keysToDelete = Array.from(this.cache.keys()).filter(key => {
  key.includes(userId) || key.includes('analytics')
    )
    keysToDelete.forEach(key => this.cache.delete(key))
  }
}

// Export singleton instance;
export const enhancedAgreementService = new EnhancedAgreementService()
export default enhancedAgreementService ;