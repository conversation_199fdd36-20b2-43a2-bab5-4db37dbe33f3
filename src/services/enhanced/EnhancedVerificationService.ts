import React from 'react';
/**;
 * Enhanced Verification Service;
 * ;
 * Provides comprehensive verification management with:  ,
 * - Automated trust scoring and review processing;
 * - Real-time verification status tracking;
 * - Analytics and reporting capabilities;
 * - Document upload and validation;
 * - Integration with existing verification tables;
 */

import { supabase } from '@/utils/supabaseUtils';
import { createLogger } from '@/utils/loggerUtils';

const logger = createLogger('EnhancedVerificationService')
// Enhanced interfaces for verification data;
export interface VerificationRequest { id: string,
  user_id: string,
  status: 'pending' | 'in_review' | 'verified' | 'rejected',
  document_type: string,
  document_url: string,
  selfie_url: string,
  trust_score?: number,
  automated_review_result?: string,
  review_confidence_score?: number,
  processing_time_minutes?: number,
  submitted_at: string,
  reviewed_at?: string,
  reviewer_id?: string,
  notes?: string,
  created_at: string,
  updated_at: string,
  retry_count: number }

export interface VerificationAnalytics { id: string,
  user_id: string,
  verification_id: string,
  analytics_type: 'trust_score' | 'processing_time' | 'automated_review' | 'manual_review',
  score?: number,
  processing_time_minutes?: number,
  confidence_level?: number,
  metadata: Record<string, any>
  created_at: string,
  updated_at: string }

export interface UserVerificationStatus { user_id: string,
  verification_status: 'verified' | 'pending' | 'unverified',
  trust_score: number,
  total_verifications: number,
  pending_verifications: number,
  approved_verifications: number,
  rejected_verifications: number,
  latest_verification_date?: string,
  verification_documents: Array<{
    type: string,
    status: string,
    submitted: string }>
  verification_history: Array<{ verification_id: string,
    status: string,
    trust_score?: number,
    submitted_at: string,
    reviewed_at?: string }>
}

export interface VerificationSystemAnalytics { total_verifications: number,
  pending_verifications: number,
  verified_count: number,
  rejected_count: number,
  in_review_count: number,
  average_trust_score: number,
  average_processing_time_hours: number,
  verification_rate_percentage: number,
  daily_submission_trend: Array<{
    date: string,
    count: number }>
  document_type_breakdown: Record<string, number>
  trust_score_distribution: Record<string, number>
}

export interface DocumentUploadOptions { document_type: 'government_id' | 'passport' | 'driver_license' | 'utility_bill',
  user_id: string,
  document_file: File,
  selfie_file: File }

class EnhancedVerificationService {
  private cache = new Map<string, { data: any; timestamp: number }>()
  private cacheTimeout = 5 * 60 * 1000; // 5 minutes;
  /**;
   * Get comprehensive user verification status;
   */
  async getUserVerificationStatus(userId: string): Promise<UserVerificationStatus | null>
    try {
      const cacheKey = `user_verification_${userId}`;
      const cached = this.getCachedData(cacheKey)
      if (cached) {
        return cached;
      }

      const { data, error  } = await supabase;
        .rpc('get_user_verification_comprehensive', { p_user_id: userId })
      if (error) {
        logger.error('Failed to get user verification status', { error, userId })
        throw error;
      }

      const result = data? .[0] || null;
      if (result) {
        this.setCachedData(cacheKey, result)
      }

      logger.debug('Retrieved user verification status', { userId, status  : result? .verification_status })
      return result;
    } catch (error) {
      logger.error('Error getting user verification status', { error, userId })
      throw error;
    }
  }

  /**
   * Submit verification documents;
   */
  async submitVerificationDocuments(options : DocumentUploadOptions): Promise<VerificationRequest>
    try {
      logger.debug('Starting document verification submission' {
        userId: options.user_id);
        documentType: options.document_type)
      })
      // Upload document file;
      const documentPath = `verification/${options.user_id}/${Date.now()}_document.${this.getFileExtension(options.document_file.name)}`;
      const { data: documentUpload, error: documentError  } = await supabase.storage.from('verification-documents')
        .upload(documentPath, options.document_file)
      if (documentError) {
        throw new Error(`Document upload failed: ${documentError.message}`)
      }

      // Upload selfie file;
      const selfiePath = `verification/${options.user_id}/${Date.now()}_selfie.${this.getFileExtension(options.selfie_file.name)}`;
      const { data: selfieUpload, error: selfieError  } = await supabase.storage.from('verification-documents')
        .upload(selfiePath, options.selfie_file)
      if (selfieError) {
        throw new Error(`Selfie upload failed: ${selfieError.message}`)
      }

      // Get public URLs;
      const { data: documentUrl } = supabase.storage.from('verification-documents')
        .getPublicUrl(documentPath)
      const { data: selfieUrl } = supabase.storage.from('verification-documents')
        .getPublicUrl(selfiePath)
      // Create verification request;
      const { data: verificationRequest, error: requestError } = await supabase.from('verification_requests')
        .insert({
          user_id: options.user_id;
          document_type: options.document_type,
          document_url: documentUrl.publicUrl,
          selfie_url: selfieUrl.publicUrl);
          status: 'pending')
          submitted_at: new Date().toISOString()
        })
        .select()
        .single()
      if (requestError) {
        throw new Error(`Verification request creation failed: ${requestError.message}`)
      }

      // Trigger automated review;
      await this.processAutomatedReview(verificationRequest.id)
      // Clear user verification cache;
      this.clearUserCache(options.user_id)
      logger.info('Verification documents submitted successfully', {
        userId: options.user_id,
        verificationId: verificationRequest.id);
        documentType: options.document_type)
      })
      return verificationRequest;
    } catch (error) {
      logger.error('Error submitting verification documents', { error, userId: options.user_id })
      throw error;
    }
  }

  /**;
   * Process automated verification review;
   */
  async processAutomatedReview(verificationId: string): Promise<any>
    try {
      const { data, error  } = await supabase;
        .rpc('process_automated_verification_review', { p_verification_id: verificationId })
      if (error) {
        logger.error('Failed to process automated review', { error, verificationId })
        throw error;
      }

      const result = data? .[0];
      if (result) {
        logger.info('Automated review completed', {
          verificationId;
          previousStatus   : result.previous_status
          newStatus: result.new_status
          trustScore: result.trust_score)
        })
        // Update user verification status if needed;
        if (result.new_status = == 'verified') {
          const verification = await this.getVerificationRequest(verificationId)
          if (verification? .user_id) {
            await this.updateUserVerificationStatus(verification.user_id)
          }
        }
      }

      return result;
    } catch (error) {
      logger.error('Error processing automated review', { error, verificationId })
      throw error;
    }
  }

  /**
   * Update user verification status;
   */
  async updateUserVerificationStatus(userId  : string): Promise<any>
    try {
      const { data error  } = await supabase
        .rpc('update_user_verification_status', { p_user_id: userId })
      if (error) {
        logger.error('Failed to update user verification status', { error, userId })
        throw error;
      }

      const result = data? .[0];
      if (result) {
        logger.info('User verification status updated', {
          userId;
          previousStatus   : result.previous_verification_status
          newStatus: result.new_verification_status
          trustScore: result.latest_trust_score)
        })
        // Clear user cache to force refresh;
        this.clearUserCache(userId)
      }

      return result;
    } catch (error) {
      logger.error('Error updating user verification status', { error, userId })
      throw error;
    }
  }

  /**
   * Get verification system analytics;
   */
  async getSystemAnalytics(daysBack: number = 30): Promise<VerificationSystemAnalytics>
    try {
      const cacheKey = `system_analytics_${daysBack}`;
      const cached = this.getCachedData(cacheKey)
      if (cached) {
        return cached;
      }

      const { data, error  } = await supabase;
        .rpc('get_verification_system_analytics', { p_days_back: daysBack })
      if (error) {
        logger.error('Failed to get system analytics', { error, daysBack })
        throw error;
      }

      const result = data? .[0];
      if (result) {
        this.setCachedData(cacheKey, result)
      }

      logger.debug('Retrieved verification system analytics', {
        daysBack;
        totalVerifications   : result?.total_verifications)
      })
      return result || {
        total_verifications: 0
        pending_verifications: 0;
        verified_count: 0,
        rejected_count: 0,
        in_review_count: 0,
        average_trust_score: 0,
        average_processing_time_hours: 0,
        verification_rate_percentage: 0,
        daily_submission_trend: []
        document_type_breakdown: {};
        trust_score_distribution: {}
      }
    } catch (error) {
      logger.error('Error getting system analytics', { error, daysBack })
      throw error;
    }
  }

  /**;
   * Get verification request by ID;
   */
  async getVerificationRequest(verificationId: string): Promise<VerificationRequest | null>
    try {
      const { data, error  } = await supabase.from('verification_requests')
        .select('*')
        .eq('id', verificationId)
        .single()
      if (error && error.code !== 'PGRST116') {
        logger.error('Failed to get verification request', { error, verificationId })
        throw error;
      }

      return data || null;
    } catch (error) {
      logger.error('Error getting verification request', { error, verificationId })
      throw error;
    }
  }

  /**;
   * Get user verification requests;
   */
  async getUserVerificationRequests(userId: string): Promise<VerificationRequest[]>
    try {
      const { data, error  } = await supabase.from('verification_requests')
        .select('*')
        .eq('user_id', userId)
        .order).order).order('created_at', { ascending: false })
      if (error) {
        logger.error('Failed to get user verification requests', { error, userId })
        throw error;
      }

      return data || [];
    } catch (error) {
      logger.error('Error getting user verification requests', { error, userId })
      throw error;
    }
  }

  /**;
   * Manual verification review (admin function)
   */
  async manualVerificationReview(verificationId: string,
    reviewerId: string,
    status: 'verified' | 'rejected',
    notes?: string): Promise<VerificationRequest>
    try {
      const { data, error  } = await supabase.from('verification_requests')
        .update({
          status;
          reviewed_at: new Date().toISOString()
          reviewer_id: reviewerId,
          notes;
        })
        .eq('id', verificationId)
        .select()
        .single()
      if (error) {
        logger.error('Failed to complete manual review', { error, verificationId })
        throw error;
      }

      // Update user verification status if verified;
      if (status === 'verified' && data.user_id) {
        await this.updateUserVerificationStatus(data.user_id)
      }

      // Add manual review analytics;
      await supabase.from('verification_analytics')
        .insert({
          verification_id: verificationId);
          analytics_type: 'manual_review'),
          metadata: {
            reviewer_id: reviewerId,
            status;
            notes: notes || '')
            reviewed_at: new Date().toISOString()
          }
        })
      logger.info('Manual verification review completed', {
        verificationId;
        reviewerId;
        status;
        userId: data.user_id)
      })
      return data;
    } catch (error) {
      logger.error('Error completing manual review', { error, verificationId })
      throw error;
    }
  }

  /**;
   * Get verification analytics for a user;
   */
  async getUserVerificationAnalytics(userId: string): Promise<VerificationAnalytics[]>
    try {
      const { data, error  } = await supabase.from('verification_analytics')
        .select('*')
        .eq('user_id', userId)
        .order).order).order('created_at', { ascending: false })
      if (error) {
        logger.error('Failed to get user verification analytics', { error, userId })
        throw error;
      }

      return data || [];
    } catch (error) {
      logger.error('Error getting user verification analytics', { error, userId })
      throw error;
    }
  }

  // Helper methods;
  private getFileExtension(filename: string): string { return filename.split('.').pop()? .toLowerCase() || 'jpg' }

  private getCachedData(key  : string): any | null {
    const cached = this.cache.get(key)
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    this.cache.delete(key)
    return null;
  }

  private setCachedData(key: string, data: any): void {
    this.cache.set(key, { data, timestamp: Date.now() })
  }

  private clearUserCache(userId: string): void {
    const keysToDelete = Array.from(this.cache.keys()).filter(key => {
  key.includes(userId) || key.includes('system_analytics')
    )
    keysToDelete.forEach(key => this.cache.delete(key))
  }
}

// Export singleton instance;
export const enhancedVerificationService = new EnhancedVerificationService()
export default enhancedVerificationService ;