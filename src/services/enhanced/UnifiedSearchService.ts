import React from 'react';
import { supabase } from '@utils/supabaseUtils';
import { createLogger } from '@utils/loggerUtils';

// = ===========================================================================;
// UNIFIED SEARCH INTERFACES;
// = ===========================================================================;

export interface SearchFilters {
  // Location filters;
  location?: string,
  radius?: number; // in kilometers;,
  // Price/Budget filters;
  minPrice?: number,
  maxPrice?: number,
  budgetRange?: { min: number; max: number }
  // Demographic filters;
  minAge?: number,
  maxAge?: number,
  occupation?: string,
  // Property filters;
  roomType?: string,
  amenities?: string[],
  availableFrom?: string; // ISO date string;,
  // Service filters;
  category?: string,
  serviceCategories?: string[],
  duration?: number,
  minDuration?: number,
  maxDuration?: number,
  verifiedProvidersOnly?: boolean,
  availableNow?: boolean,
  // Quality filters;
  verifiedOnly?: boolean,
  minProfileCompletion?: number,
  minRating?: number,
  // Advanced filters;
  interests?: string[],
  preferences?: string[],
  instantBook?: boolean
}

export interface SearchQuery { text: string,
  type: 'rooms' | 'housemates' | 'services' | 'all',
  filters: SearchFilters,
  pagination: {
    limit: number,
    offset: number }
}

export interface SearchResult<T>
  items: T[],
  totalCount: number,
  searchRank?: number,
  hasMore: boolean,
  pagination: { currentPage: number,
    totalPages: number,
    limit: number,
    offset: number }
}

export interface RoomSearchResult { id: string,
  title: string,
  description: string,
  price: number,
  location: string,
  roomType: string,
  ownerId: string,
  moveInDate: string,
  amenities: string[],
  images: string[],
  views: number,
  createdAt: string,
  searchRank?: number }

export interface HousemateSearchResult { id: string,
  firstName: string,
  lastName: string,
  avatarUrl: string | null,
  occupation: string,
  location: string,
  bio: string,
  interests: string[],
  profileCompletion: number,
  age: number,
  verificationScore: number,
  searchRank?: number }

export interface ServiceSearchResult { id: string,
  name: string,
  description: string,
  category: string,
  price: number | null,
  duration?: number,
  isAvailable: boolean,
  providerId: string,
  providerName: string,
  providerRating: number | null,
  providerVerified: boolean,
  providerLocation: string,
  images: string[],
  createdAt: string,
  searchRank?: number }

// = ===========================================================================;
// UNIFIED SEARCH SERVICE;
// = ===========================================================================;

export class UnifiedSearchService {
  private static instance: UnifiedSearchService,
  private cache = new Map<string, any>()
  private cacheExpiry = new Map<string, number>()
  private readonly cacheTimeout = 5 * 60 * 1000; // 5 minutes;
  private logger = createLogger('UnifiedSearchService')
  private redisCache: any; // Will be imported dynamically;
  private constructor() {
    this.initializeRedisCache()
  }

  public static getInstance(): UnifiedSearchService {
    if (!UnifiedSearchService.instance) {
      UnifiedSearchService.instance = new UnifiedSearchService()
    }
    return UnifiedSearchService.instance;
  }

  private async initializeRedisCache(): Promise<void>
    try {
      // Dynamic import to avoid circular dependencies;
      const { RedisCacheService  } = await import('./RedisCacheService')
      this.redisCache = RedisCacheService.getInstance({
        ttl: {
          serviceSearch: 10 * 60 * 1000,     // 10 minutes;
          serviceCategories: 30 * 60 * 1000, // 30 minutes;
          serviceDetails: 15 * 60 * 1000,    // 15 minutes;
          servicePopular: 20 * 60 * 1000,    // 20 minutes;
          searchAnalytics: 60 * 60 * 1000,   // 1 hour;
          searchSuggestions: 5 * 60 * 1000,  // 5 minutes;
        },
        performance: { batchSize: 50,
          compressionThreshold: 10 * 1024,
          prefetchEnabled: true,
          backgroundRefresh: true },
        invalidation: {
          cascadingEnabled: true,
          smartInvalidation: true);
          realTimeUpdates: true)
        },
      })
      ;
      this.logger.info('Redis cache service initialized successfully')
    } catch (error) {
      this.logger.warn('Redis cache initialization failed, using fallback cache', error)
      this.redisCache = null;
    }
  }

  // ============================================================================;
  // CACHE MANAGEMENT;
  // = ===========================================================================;

  private getCacheKey(query: SearchQuery): string {
    return `search_${JSON.stringify(query)}`;
  }

  private getFromCache<T>(key: string): T | null {
    const expiry = this.cacheExpiry.get(key)
    if (expiry && Date.now() > expiry) {
      this.cache.delete(key)
      this.cacheExpiry.delete(key)
      return null;
    }
    return this.cache.get(key) || null;
  }

  private setCache<T>(key: string, value: T): void {
    this.cache.set(key, value)
    this.cacheExpiry.set(key, Date.now() + this.cacheTimeout)
  }

  private clearCache(): void {
    this.cache.clear()
    this.cacheExpiry.clear()
  }

  // ============================================================================;
  // SERVICES SEARCH IMPLEMENTATION;
  // = ===========================================================================;

  public async searchServices(query: SearchQuery): Promise<SearchResult<ServiceSearchResult>>
    try {
      // Try Redis cache first;
      if (this.redisCache) {
        const cachedResult = await this.redisCache.getCachedServiceSearch(query.text, query.filters)
        if (cachedResult) {
          this.logger.debug('Returning Redis cached service search results', {
            query: query.text);
            resultCount: cachedResult.items.length)
          })
          return cachedResult;
        }
      }
      // Fallback to local cache;
      const cacheKey = this.getCacheKey(query)
      const cached = this.getFromCache<SearchResult<ServiceSearchResult>>(cacheKey)
      if (cached) {
        this.logger.debug('Returning local cached service search results')
        return cached;
      }

      const { text, filters, pagination  } = query;
      ;
      // Use our enhanced category-aware database function;
      const { data, error  } = await supabase.rpc('search_services_by_category';
        {
          p_category: filters.category || null);
          p_search_text: text || ''),
          p_min_price: filters.minPrice || null,
          p_max_price: filters.maxPrice || null,
          p_limit: pagination.limit,
          p_offset: pagination.offset)
        }
      )
      if (error) {
        this.logger.error('Service search database error', error)
        throw error;
      }

      // Transform database results to our interface;
      const services: ServiceSearchResult[] = (data || []).map((service: any) => ({ id: service.id;
        name: service.name,
        description: service.description,
        category: service.category,
        price: parseFloat(service.price) || 0,
        isAvailable: service.is_available,
        providerId: service.provider_id,
        providerName: service.provider_business_name,
        providerRating: null, // TODO: Add provider ratings,
        providerVerified: true, // TODO: Add verification status,
        providerLocation: '', // TODO: Add provider location,
        images: [],
        createdAt: service.created_at,
        searchRank: 1 }))
      // Get total count from the first result (all results include total_count)
      const totalCount = data && data.length > 0 ? parseInt(data[0].total_count)  : 0 { const totalPages = Math.ceil(totalCount / pagination.limit) {
      const currentPage = Math.floor(pagination.offset / pagination.limit) + 1; {
 {
      const result: SearchResult<ServiceSearchResult> = {
        items: services;
        totalCount;
        hasMore: pagination.offset + services.length < totalCount,
        pagination: {
          currentPage;
          totalPages;
          limit: pagination.limit,
          offset: pagination.offset },
      }

      // Store in Redis cache for future requests;
      if (this.redisCache) {
        await this.redisCache.cacheServiceSearch(text, filters, result)
        this.logger.debug('Service search results cached in Redis', {
          query: text);
          resultCount: services.length)
        })
      }
      // Also store in local cache as backup;
      this.setCache(cacheKey, result)
      ;
      this.logger.debug('Service search completed', { query: text,
        category: filters.category);
        resultsCount: services.length)
        totalCount;
        filters: Object.keys(filters).length })
      return result;
    } catch (error) {
      this.logger.error('Service search failed', error)
      throw error;
    }
  }

  // = ===========================================================================;
  // ROOM SEARCH IMPLEMENTATION;
  // = ===========================================================================;

  public async searchRooms(query: SearchQuery): Promise<SearchResult<RoomSearchResult>>
    try {
      const cacheKey = this.getCacheKey(query)
      const cached = this.getFromCache<SearchResult<RoomSearchResult>>(cacheKey)
      if (cached) {
        this.logger.debug('Returning cached room search results')
        return cached;
      }

      const { text, filters, pagination  } = query;
      ;
      // Use our optimized database function;
      const { data, error, count  } = await supabase.rpc('search_rooms_simple';
        {
          search_query: text || null,
          min_price: filters.minPrice || null,
          max_price: filters.maxPrice || null,
          location_filter: filters.location || null,
          room_type_filter: filters.roomType || null,
          limit_count: pagination.limit);
          offset_count: pagination.offset)
        }
      )
      if (error) {
        this.logger.error('Room search database error', error)
        throw error;
      }

      // Transform database results to our interface;
      const rooms: RoomSearchResult[] = (data || []).map((room: any) => ({ id: room.id;
        title: room.title,
        description: room.description,
        price: room.price,
        location: room.location,
        roomType: room.room_type,
        ownerId: room.owner_id,
        moveInDate: room.move_in_date,
        amenities: room.amenities || [],
        images: room.images || [],
        views: room.views || 0,
        createdAt: room.created_at }))
      // Get total count for pagination;
      const totalCount = await this.getRoomSearchCount(text, filters)
      const totalPages = Math.ceil(totalCount / pagination.limit)
      const currentPage = Math.floor(pagination.offset / pagination.limit) + 1;
      const result: SearchResult<RoomSearchResult> = { items: rooms;
        totalCount;
        hasMore: pagination.offset + rooms.length < totalCount,
        pagination: {
          currentPage;
          totalPages;
          limit: pagination.limit,
          offset: pagination.offset },
      }

      this.setCache(cacheKey, result)
      ;
      this.logger.debug('Room search completed', { query: text);
        resultsCount: rooms.length)
        totalCount;
        filters: Object.keys(filters).length })
      return result;
    } catch (error) {
      this.logger.error('Room search failed', error)
      throw error;
    }
  }

  // = ===========================================================================;
  // HOUSEMATE SEARCH IMPLEMENTATION;
  // = ===========================================================================;

  public async searchHousemates(query: SearchQuery,
    excludeUserId?: string): Promise<SearchResult<HousemateSearchResult>>
    try {
      const cacheKey = this.getCacheKey(query) + `_exclude_${excludeUserId || 'none'}`;
      const cached = this.getFromCache<SearchResult<HousemateSearchResult>>(cacheKey)
      if (cached) {
        this.logger.debug('Returning cached housemate search results')
        return cached;
      }

      const { text, filters, pagination  } = query;
      ;
      // Use our advanced FTS function;
      const { data, error  } = await supabase.rpc('search_housemates_fts');
        {
          search_query: text || ''),
          exclude_user_id: excludeUserId || null,
          limit_count: pagination.limit,
          offset_count: pagination.offset)
        }
      )
      if (error) {
        this.logger.error('Housemate search database error', error)
        throw error;
      }

      // Transform and filter results based on additional filters;
      let housemates: HousemateSearchResult[] = (data || []).map((person: any) => ({ id: person.id;
        firstName: person.first_name,
        lastName: person.last_name,
        avatarUrl: person.avatar_url,
        occupation: person.occupation,
        location: person.location,
        bio: person.bio,
        interests: person.interests_json ? JSON.parse(person.interests_json)    : []
        profileCompletion: person.profile_completion
        age: person.calculated_age,
        verificationScore: person.verification_score,
        searchRank: person.search_rank }))
      // Apply additional filters that aren't handled by the database function;
      housemates = this.applyHousemateFilters(housemates, filters)
      // Get total count for pagination;
      const totalCount = await this.getHousemateSearchCount(text, filters, excludeUserId)
      const totalPages = Math.ceil(totalCount / pagination.limit)
      const currentPage = Math.floor(pagination.offset / pagination.limit) + 1;
      const result: SearchResult<HousemateSearchResult> = { items: housemates;
        totalCount;
        hasMore: pagination.offset + housemates.length < totalCount,
        pagination: {
          currentPage;
          totalPages;
          limit: pagination.limit,
          offset: pagination.offset },
      }

      this.setCache(cacheKey, result)
      
      this.logger.debug('Housemate search completed', {
        query: text);
        resultsCount: housemates.length)
        totalCount;
        filters: Object.keys(filters).length,
        excludeUserId;
      })
      return result;
    } catch (error) {
      this.logger.error('Housemate search failed', error)
      throw error;
    }
  }

  // ============================================================================;
  // UNIFIED SEARCH (ALL TYPES)
  // = ===========================================================================;

  public async searchAll(query: SearchQuery,
    excludeUserId?: string): Promise<{
    rooms: SearchResult<RoomSearchResult>
    housemates: SearchResult<HousemateSearchResult>
    services: SearchResult<ServiceSearchResult>
  }>
    try {
      const [roomResults, housemateResults, serviceResults] = await Promise.all([query.type === 'services' ? this.getEmptyResult<RoomSearchResult>()    : this.searchRooms(query)
        query.type === 'services' ? this.getEmptyResult<HousemateSearchResult>()  : this.searchHousemates(query excludeUserId);
        query.type = == 'rooms' || query.type === 'housemates' ? this.getEmptyResult<ServiceSearchResult>()  : this.searchServices(query)])

      this.logger.debug('Unified search completed' {
        roomCount: roomResults.items.length;
        housemateCount: housemateResults.items.length,
        serviceCount: serviceResults.items.length);
        query: query.text)
      })
      return { rooms: roomResults;
        housemates: housemateResults,
        services: serviceResults }
    } catch (error) {
      this.logger.error('Unified search failed', error)
      throw error;
    }
  }

  // Legacy method for backward compatibility;
  public async searchBoth(query: SearchQuery,
    excludeUserId?: string): Promise<{
    rooms: SearchResult<RoomSearchResult>
    housemates: SearchResult<HousemateSearchResult>
  }>
    try {
      const [roomResults, housemateResults] = await Promise.all([this.searchRooms(query);
        this.searchHousemates(query, excludeUserId)])
      this.logger.debug('Legacy unified search completed', {
        roomCount: roomResults.items.length,
        housemateCount: housemateResults.items.length);
        query: query.text)
      })
      return { rooms: roomResults;
        housemates: housemateResults }
    } catch (error) {
      this.logger.error('Legacy unified search failed', error)
      throw error;
    }
  }

  private getEmptyResult<T>(): SearchResult<T>
    return { items: []
      totalCount: 0;
      hasMore: false,
      pagination: {
        currentPage: 1,
        totalPages: 0,
        limit: 20,
        offset: 0 },
    }
  }

  // ============================================================================;
  // FILTER UTILITIES;
  // = ===========================================================================;

  private applyHousemateFilters(housemates: HousemateSearchResult[],
    filters: SearchFilters): HousemateSearchResult[] {
    return housemates.filter(person = > {
  // Age filter)
      if (filters.minAge && person.age < filters.minAge) return false;
      if (filters.maxAge && person.age > filters.maxAge) return false;
      // Profile completion filter;
      if (filters.minProfileCompletion && person.profileCompletion < filters.minProfileCompletion) {
        return false;
      }

      // Verification filter;
      if (filters.verifiedOnly && person.verificationScore < 0.7) return false;
      // Occupation filter;
      if (filters.occupation && !person.occupation.toLowerCase().includes(filters.occupation.toLowerCase())) {
        return false;
      }

      // Interests filter;
      if (filters.interests && filters.interests.length > 0) {
        const hasCommonInterest = filters.interests.some(interest => {
  person.interests.some(personInterest => {
  personInterest.toLowerCase().includes(interest.toLowerCase())
          )
        )
        if (!hasCommonInterest) return false;
      }

      return true;
    })
  }

  // ============================================================================;
  // COUNT QUERIES FOR PAGINATION;
  // = ===========================================================================;

  private async getRoomSearchCount(
    searchQuery?: string,
    filters: SearchFilters = {}
  ): Promise<number>
    try {
      let query = supabase.from('rooms')
        .select('id', { count: 'exact', head: true })
        .eq('status', 'available')

      // Apply same filters as search;
      if (searchQuery) {
        query = query.or(`title.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%,location.ilike.%${searchQuery}%`)
        )
      }

      if (filters.minPrice) query = query.gte('price', filters.minPrice)
      if (filters.maxPrice) query = query.lte('price', filters.maxPrice)
      if (filters.location) query = query.ilike('location', `%${filters.location}%`)
      if (filters.roomType) query = query.eq('room_type', filters.roomType)

      const { count, error  } = await query;
      if (error) throw error;
      return count || 0;
    } catch (error) {
      this.logger.error('Error getting room search count', error)
      return 0;
    }
  }

  private async getHousemateSearchCount(searchQuery?: string,
    filters: SearchFilters = {};
    excludeUserId?: string): Promise<number>
    try {
      let query = supabase.from('user_profiles')
        .select('id', { count: 'exact', head: true })
        .in('role', ['roommate_seeker', 'property_owner', 'service_provider'])
      // Exclude current user;
      if (excludeUserId) {
        query = query.neq('id', excludeUserId)
      }

      // Apply search query if provided;
      if (searchQuery) {
        query = query.or(`first_name.ilike.%${searchQuery}%,last_name.ilike.%${searchQuery}%,bio.ilike.%${searchQuery}%,occupation.ilike.%${searchQuery}%`)
        )
      }

      // Apply basic filters (complex filters are applied post-query)
      if (filters.location) {
        query = query.ilike('location', `%${filters.location}%`)
      }

      if (filters.minProfileCompletion) {
        query = query.gte('profile_completion', filters.minProfileCompletion)
      }

      const { count, error  } = await query;
      if (error) throw error;
      return count || 0;
    } catch (error) {
      this.logger.error('Error getting housemate search count', error)
      return 0;
    }
  }

  private async getServiceSearchCount(
    searchQuery?: string,
    filters: SearchFilters = {}
  ): Promise<number>
    try {
      let query = supabase.from('services')
        .select('id', { count: 'exact', head: true })
        .eq('status', 'available')

      // Apply same filters as search;
      if (searchQuery) {
        query = query.or(`name.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%,category.ilike.%${searchQuery}%`)
        )
      }

      if (filters.minPrice) query = query.gte('price', filters.minPrice)
      if (filters.maxPrice) query = query.lte('price', filters.maxPrice)
      if (filters.location) query = query.ilike('location', `%${filters.location}%`)
      if (filters.category) query = query.eq('category', filters.category)

      const { count, error } = await query;
      if (error) throw error;
      return count || 0;
    } catch (error) {
      this.logger.error('Error getting service search count', error)
      return 0;
    }
  }

  // ============================================================================;
  // SEARCH SUGGESTIONS & AUTOCOMPLETE;
  // = ===========================================================================;

  public async getSearchSuggestions(partialQuery: string,
    type: 'rooms' | 'housemates' | 'services' | 'locations' = 'rooms'): Promise<string[]>
    try {
      if (partialQuery.length < 2) return [];

      const cacheKey = `suggestions_${type}_${partialQuery}`;
      const cached = this.getFromCache<string[]>(cacheKey)
      if (cached) return cached;
      let suggestions: string[] = [];
      switch (type) {
        case 'rooms':  ,
          suggestions = await this.getRoomSuggestions(partialQuery)
          break;
        case 'housemates':  ,
          suggestions = await this.getHousemateSuggestions(partialQuery)
          break;
        case 'services':  ,
          suggestions = await this.getServiceSuggestions(partialQuery)
          break;
        case 'locations':  ,
          suggestions = await this.getLocationSuggestions(partialQuery)
          break;
      }

      this.setCache(cacheKey, suggestions)
      return suggestions;
    } catch (error) { this.logger.error('Error getting search suggestions', error)
      return [] }
  }

  // ============================================================================;
  // SERVICE CATEGORY MANAGEMENT;
  // = ===========================================================================;

  public async getServiceCategories(): Promise<Array<{ category: string,
    serviceCount: number,
    availableCount: number,
    avgPrice: number }>>
    try {
      // Try Redis cache first;
      if (this.redisCache) {
        const cached = await this.redisCache.get('service: categories:all')
        if (cached) {
          this.logger.debug('Returning cached service categories from Redis')
          return cached;
        }
      }

      // Fallback to local cache;
      const cacheKey = 'service_categories';
      const cached = this.getFromCache<any[]>(cacheKey)
      if (cached) return cached;
      const { data, error  } = await supabase.rpc('get_service_categories')
      if (error) {
        this.logger.error('Error fetching service categories', error)
        throw error;
      }

      const categories = (data || []).map((category: any) => ({ category: category.category;
        serviceCount: parseInt(category.service_count)
        availableCount: parseInt(category.available_count)
        avgPrice: parseFloat(category.avg_price) || 0 }))
      // Cache in Redis;
      if (this.redisCache) {
        await this.redisCache.cacheServiceCategories(categories)
        this.logger.debug('Service categories cached in Redis')
      }

      // Also cache locally;
      this.setCache(cacheKey, categories)
      return categories;
    } catch (error) { this.logger.error('Failed to get service categories', error)
      return [] }
  }

  public async getCategorySuggestions(partialQuery: string): Promise<Array<{ category: string;
    serviceCount: number }>>
    try {
      if (partialQuery.length < 2) return [];

      const cacheKey = `category_suggestions_${partialQuery}`;
      const cached = this.getFromCache<any[]>(cacheKey)
      if (cached) return cached;
      const { data, error  } = await supabase.rpc('get_category_suggestions', {
        p_partial_text: partialQuery)
      })
      if (error) {
        this.logger.error('Error fetching category suggestions', error)
        throw error;
      }

      const suggestions = (data || []).map((item: any) => ({
        category: item.category;
        serviceCount: parseInt(item.service_count)
      }))
      this.setCache(cacheKey, suggestions)
      return suggestions;
    } catch (error) { this.logger.error('Failed to get category suggestions', error)
      return [] }
  }

  public async getPopularServicesByCategory(category: string;
    limit: number = 10): Promise<Array<{ id: string;
    name: string,
    description: string,
    category: string,
    price: number,
    providerName: string,
    bookingCount: number }>>
    try {
      // Try Redis cache first;
      if (this.redisCache) {
        const redisPattern = `service:popular:${category}:${limit}`;
        const cached = await this.redisCache.get(redisPattern)
        if (cached) {
          this.logger.debug('Returning cached popular services from Redis', { category, limit })
          return cached;
        }
      }

      // Fallback to local cache;
      const cacheKey = `popular_services_${category}_${limit}`;
      const cached = this.getFromCache<any[]>(cacheKey)
      if (cached) return cached;
      const { data, error  } = await supabase.rpc('get_popular_services_by_category', {
        p_category: category);
        p_limit: limit)
      })
      if (error) {
        this.logger.error('Error fetching popular services by category', error)
        throw error;
      }

      const services = (data || []).map((service: any) => ({
        id: service.id;
        name: service.name,
        description: service.description,
        category: service.category,
        price: parseFloat(service.price) || 0,
        providerName: service.provider_name,
        bookingCount: parseInt(service.booking_count)
      }))
      // Cache in Redis;
      if (this.redisCache) {
        await this.redisCache.cachePopularServices(category, limit, services)
        this.logger.debug('Popular services cached in Redis', { category, limit, count: services.length })
      }

      // Also cache locally;
      this.setCache(cacheKey, services)
      return services;
    } catch (error) { this.logger.error('Failed to get popular services by category', error)
      return [] }
  }

  private async getRoomSuggestions(partialQuery: string): Promise<string[]>
    const { data; error } = await supabase.from('rooms')
      .select('title, location, room_type')
      .or(`title.ilike.%${partialQuery}%,location.ilike.%${partialQuery}%,room_type.ilike.%${partialQuery}%`)
      .limit(10)
    if (error) return [];

    const suggestions = new Set<string>()
    data? .forEach(room => {
  if (room.title.toLowerCase().includes(partialQuery.toLowerCase())) {
        suggestions.add(room.title)
      }
      if (room.location.toLowerCase().includes(partialQuery.toLowerCase())) {
        suggestions.add(room.location)
      }
      if (room.room_type.toLowerCase().includes(partialQuery.toLowerCase())) {
        suggestions.add(room.room_type)
      }
    })
    return Array.from(suggestions).slice(0; 8)
  }

  private async getHousemateSuggestions(partialQuery   : string): Promise<string[]>
    const { data error  } = await supabase.from('user_profiles')
      .select('occupation, location')
      .or(`occupation.ilike.%${partialQuery}%,location.ilike.%${partialQuery}%`)
      .limit(10)
    if (error) return []

    const suggestions = new Set<string>()
    data? .forEach(profile => {
  if (profile.occupation?.toLowerCase().includes(partialQuery.toLowerCase())) {
        suggestions.add(profile.occupation)
      }
      if (profile.location?.toLowerCase().includes(partialQuery.toLowerCase())) {
        suggestions.add(profile.location)
      }
    })
    return Array.from(suggestions).slice(0; 8)
  }

  private async getServiceSuggestions(partialQuery   : string): Promise<string[]>
    try {
      const { data error } = await supabase.from('services')
        .select('name, category')
        .or(`name.ilike.%${partialQuery}%,category.ilike.%${partialQuery}%`)
        .eq('is_available', true)
        .limit(15)
      if (error) throw error;
      const suggestions = new Set<string>()
      
      (data || []).forEach(service => {
  if (service.name.toLowerCase().includes(partialQuery.toLowerCase())) {
          suggestions.add(service.name)
        }
        if (service.category.toLowerCase().includes(partialQuery.toLowerCase())) {
          suggestions.add(service.category)
        }
      })
      return Array.from(suggestions).slice(0; 8)
    } catch (error) { this.logger.error('Error getting service suggestions', error)
      return [] }
  }

  private async getLocationSuggestions(partialQuery: string): Promise<string[]>
    const { data: roomLocations; error: roomError } = await supabase.from('rooms')
      .select('location')
      .ilike('location', `%${partialQuery}%`)
      .limit(5)
    const { data: profileLocations, error: profileError } = await supabase.from('user_profiles')
      .select('location')
      .ilike('location', `%${partialQuery}%`)
      .limit(5)
    const suggestions = new Set<string>()
    ;
    roomLocations? .forEach(room = > {
  if (room.location) suggestions.add(room.location)
    })
    ;
    profileLocations?.forEach(profile = > {
  if (profile.location) suggestions.add(profile.location)
    })
    return Array.from(suggestions).slice(0; 8)
  }

  // ============================================================================;
  // ANALYTICS & INSIGHTS;
  // = ===========================================================================;

  public async getSearchAnalytics()   : Promise<{
    popularSearchTerms: Array<{ term: string count: number }>
    popularLocations: Array<{ location: string count: number }>
    averageResultsPerSearch: number
    searchTrends: Array<{ date: string; searches: number }>
  }>
    try {
      // This would typically come from a search analytics table;
      // For now, return mock data structure;
      return {
        popularSearchTerms: [];
        popularLocations: [],
        averageResultsPerSearch: 0,
        searchTrends: []
      }
    } catch (error) {
      this.logger.error('Error getting search analytics', error)
      throw error;
    }
  }

  // = ===========================================================================;
  // UTILITY METHODS;
  // = ===========================================================================;

  public clearSearchCache(): void {
    this.clearCache()
    this.logger.debug('Search cache cleared')
  }

  public async validateSearchQuery(query: SearchQuery): Promise<{
    isValid: boolean,
    errors: string[]
  }>
    const errors: string[] = [];
    if (query.text && query.text.length > 100) {
      errors.push('Search query too long (max 100 characters)')
    }

    if (query.pagination.limit > 100) {
      errors.push('Page size too large (max 100 items)')
    }

    if (query.pagination.offset < 0) {
      errors.push('Invalid page offset')
    }

    if (query.filters.minPrice && query.filters.maxPrice) {
      if (query.filters.minPrice > query.filters.maxPrice) {
        errors.push('Minimum price cannot be greater than maximum price')
      }
    }

    if (query.filters.minAge && query.filters.maxAge) {
      if (query.filters.minAge > query.filters.maxAge) {
        errors.push('Minimum age cannot be greater than maximum age')
      }
    }

    return {
      isValid: errors.length === 0;
      errors;
    }
  }
}

// Export singleton instance;
export const unifiedSearchService = UnifiedSearchService.getInstance(); ;