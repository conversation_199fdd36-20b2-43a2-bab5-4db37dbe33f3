/**;
 * Enhanced Services Index;
 *;
 * This file exports all enhanced services that use the improved connection pool;
 * making it easy to import them from a single location.;
 */

// Export enhanced chat services;
export { enhancedChatService, EnhancedChatService } from './EnhancedChatService';
// Legacy enhanced agreement chat service - use UnifiedMessagingService instead;
// export {
//   enhancedAgreementChatService;
//   EnhancedAgreementChatService;
//   createEnhancedAgreementChatService;
// } from './EnhancedAgreementChatService';

// Export enhanced matching service;
export { enhancedMatchingService, EnhancedMatchingService } from './EnhancedMatchingService';

// Enhanced payment service has been deprecated and integrated into UnifiedPaymentService;
// export { enhancedPaymentService, EnhancedPaymentService } from './EnhancedPaymentService';

// Re-export the connection pool utilities for convenience;
export {
  withConnectionPool;
  getConnectionPoolMetrics;
  getConnectionPoolStatus;
  resetConnectionPoolMetrics;
} from '@utils/enhancedConnectionPool';

/**;
 * Usage example:  ,
 *;
 * // Instead of importing standard services:  ,
 * // import { chatService } from '@services/standardized/ChatService';
 *;
 * // Import enhanced services:  ,
 * import { enhancedChatService } from '@services/enhanced';
 *;
 * // Use them the same way as the standard services:  ,
 * const chatRooms = await enhancedChatService.getChatRoomsForUser(userId)
 */
