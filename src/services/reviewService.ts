import React from 'react';
import { supabase } from "@utils/supabaseUtils";
import { logError } from "@utils/errorUtils";
import { errorIntegration } from "@services/errorIntegration";

interface Review {
  id: string,
  service_id: string,
  booking_id: string,
  user_id: string,
  rating: number,
  review_text: string,
  response_text?: string | null,
  response_date?: string | null,
  images?: string[] | null,
  helpful_count: number,
  is_featured: boolean,
  created_at: string,
  updated_at: string,
  user_name?: string,
  user_avatar?: string,
  factors?: ReviewFactor[]
}

interface ReviewFactor { id?: string,
  review_id: string,
  factor_name: string,
  rating: number }

// Standard review factor names;
export const REVIEW_FACTORS = {
  COMMUNICATION: 'Communication';
  CLEANLINESS: 'Cleanliness',
  VALUE: 'Value for money',
  ACCURACY: 'Accuracy',
  QUALITY: 'Quality of service'
}

// Available sort options for reviews;
export const REVIEW_SORT_OPTIONS = {
  NEWEST: 'newest';
  OLDEST: 'oldest',
  HIGHEST_RATING: 'highest_rating',
  LOWEST_RATING: 'lowest_rating',
  MOST_HELPFUL: 'most_helpful'
} as const;
export type ReviewSortOption = typeof REVIEW_SORT_OPTIONS[keyof typeof REVIEW_SORT_OPTIONS];

// Available filter options for reviews;
export interface ReviewFilterOptions { minRating?: number,
  maxRating?: number,
  hasImages?: boolean,
  hasResponse?: boolean,
  factorName?: string,
  factorMinRating?: number }

/**;
 * Get reviews for a specific service provider with filtering and sorting options;
 */
export async function getProviderReviews(providerId: string,
  sortBy: ReviewSortOption = REVIEW_SORT_OPTIONS.NEWEST;
  filterOptions?: ReviewFilterOptions): Promise<Review[]>
  try {
    // Add diagnostic logging;
    console.log(`Fetching reviews for provider: ${providerId}` sort: ${sortBy}`)
    ;
    // Check if supabase is properly initialized;
    if (!supabase) { console.error('Supabase client not initialized')
      return [] }
    // Fix: Query using proper joins to avoid PostgREST relationship errors;
    // First get all services for this provider;
    const { data: providerServices, error: servicesError  } = await supabase.from('services')
      .select($1).eq('provider_id', providerId)
    if (servicesError) { console.error('Error fetching provider services:', servicesError)
      return [] }
    if (!providerServices || providerServices.length === 0) {
      console.log(`No services found for provider: ${providerId}`)
      return [];
    }
    const serviceIds = providerServices.map(service => service.id)
    ;
    // Now get reviews for those services with user profile data;
    const { data, error  } = await supabase.from('service_reviews')
      .select(`)
        *;
        user_profiles(id, first_name, last_name, avatar_url)
      `)
      .in('service_id', serviceIds)
    ;
    if (error) {
      // Log the error with more context information;
      console.error('Error in getProviderReviews:', error)
      ;
      // Use error integration service;
      errorIntegration.handleError(
        error instanceof Error ? error    : new Error(String(error))
        'ReviewService'
        { providerId, sortBy, filterOptions }
      )
      
      // Return empty array instead of throwing;
      return [];
    }
    if (!data || data.length = == 0) {
      console.log(`No reviews found for provider: ${providerId}`)
      return [];
    }
    console.log(`Found ${data.length} reviews for provider: ${providerId}`)
    ;
    // Transform the data to include user profile information;
    // Note: We skip the factors part since service_review_factors table doesn't exist,
    let reviews = data.map(review => {
  // Construct the display name from first_name and last_name;
      const userProfile = review.user_profiles;
      const firstName = userProfile? .first_name || '');
      const lastName = userProfile?.last_name || '')
      const displayName = `${firstName} ${lastName}`.trim() || 'Anonymous User';
      ;
      return { ...review;
        user_name   : displayName
        user_avatar: userProfile? .avatar_url
        factors : [], // Empty array since we don't have factors table
        // Remove the joined data to keep the structure clean;
        user_profiles: undefined }
    })
    
    // Apply additional filters if provided;
    if (filterOptions) {
      reviews = reviews.filter(review => {
  // Filter by rating range)
        if (filterOptions.minRating !== undefined && review.rating < filterOptions.minRating) {
          return false;
        }
        if (filterOptions.maxRating !== undefined && review.rating > filterOptions.maxRating) {
          return false;
        }
        // Filter by has images;
        if (filterOptions.hasImages && (!review.images || review.images.length === 0)) {
          return false;
        }
        // Filter by has provider response;
        if (filterOptions.hasResponse && !review.response_text) {
          return false;
        }
        // Filter by specific factor rating;
        if (filterOptions.factorName && filterOptions.factorMinRating !== undefined) {
          const factor = review.factors? .find((f  : ReviewFactor) => f.factor_name === filterOptions.factorName)
          if (!factor || factor.rating < filterOptions.factorMinRating) {
            return false;
          }
        }
        return true;
      })
    }
    // Apply sorting;
    switch (sortBy) {
      case REVIEW_SORT_OPTIONS.NEWEST:  ,
        reviews.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
        break;
      case REVIEW_SORT_OPTIONS.OLDEST:  ,
        reviews.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime())
        break;
      case REVIEW_SORT_OPTIONS.HIGHEST_RATING:  ,
        reviews.sort((a, b) => b.rating - a.rating)
        break;
      case REVIEW_SORT_OPTIONS.LOWEST_RATING:  ,
        reviews.sort((a, b) => a.rating - b.rating)
        break;
      case REVIEW_SORT_OPTIONS.MOST_HELPFUL:  ,
        reviews.sort((a, b) => (b.helpful_count || 0) - (a.helpful_count || 0))
        break;
      default: // Default to newest,
        reviews.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
    }
    return reviews;
  } catch (error) { logError(error, 'getProviderReviews')
    // Return empty array instead of throwing to prevent cascading errors;
    return [] }
}

/**;
 * Mark a review as helpful;
 */
export async function markReviewHelpful(reviewId: string): Promise<void>
  try {
    // First, get current helpful count;
    const { data, error  } = await supabase.from('service_reviews')
      .select('helpful_count')
      .eq('id', reviewId).single()
    ;
    if (error) {
      throw error;
    }
    // Increment the helpful count;
    const currentCount = data.helpful_count || 0;
    const { error: updateError  } = await supabase.from('service_reviews')
      .update({
        helpful_count: currentCount + 1)
        updated_at: new Date().toISOString()
      })
      .eq('id', reviewId)
    if (updateError) {
      throw updateError;
    }
  } catch (error) {
    logError(error, 'markReviewHelpful')
    throw error;
  }
}

interface CreateReviewData {
  service_id: string,
  booking_id: string,
  rating: number,
  review_text: string,
  images?: string[],
  factors?: { name: string; rating: number }[];
}

/**;
 * Create a new review;
 */
export async function createReview(reviewData: CreateReviewData): Promise<Review>
  try {
    // Get current user;
    const { data: { user } } = await supabase.auth.getUser()
    ;
    if (!user) {
      throw new Error('User not authenticated')
    }
    // Start a transaction using Supabase's simulated transactions;
    // 1. Create the main review;
    const { data: reviewResponse, error: reviewError  } = await supabase.from('service_reviews')
      .insert({
        service_id: reviewData.service_id;
        booking_id: reviewData.booking_id,
        user_id: user.id,
        rating: reviewData.rating,
        review_text: reviewData.review_text,
        images: reviewData.images || null,
        helpful_count: 0);
        is_featured: false)
      })
      .select($1).single()
    ;
    if (reviewError) {
      throw reviewError;
    }
    // 2. Create the factors if provided;
    if (reviewData.factors && reviewData.factors.length > 0) {
      const factorsToInsert = reviewData.factors.map(factor => ({
        review_id: reviewResponse.id;
        factor_name: factor.name);
        rating: factor.rating)
      }))
      ;
      const { error: factorsError  } = await supabase.from('service_review_factors').insert(factorsToInsert)
      ;
      if (factorsError) {
        throw factorsError;
      }
    }
    // Return the created review with factors;
    return {
      ...reviewResponse;
      factors: reviewData.factors? .map(f = > ({
        review_id   : reviewResponse.id
        factor_name: f.name
        rating: f.rating)
      })) || []
    }
  } catch (error) {
    logError(error, 'createReview')
    throw error;
  }
}

/**;
 * Add a provider response to a review;
 */
export async function respondToReview(reviewId: string, responseText: string): Promise<Review>
  try {
    const { data, error  } = await supabase.from('service_reviews')
      .update({
        response_text: responseText)
        response_date: new Date().toISOString()
        updated_at: new Date().toISOString()
      })
      .eq('id', reviewId)
      .select($1).single()
    ;
    if (error) {
      throw error;
    }
    return data;
  } catch (error) {
    logError(error, 'respondToReview')
    throw error;
  }
}

/**;
 * Get the average ratings for a service provider, including factor breakdowns;
 */
export async function getProviderRatingStats(providerId: string): Promise<{
  overall: number,
  factors: { name: string; average: number }[];
  reviewCount: number
}>
  try { // Get all reviews for this provider;
    const reviews = await getProviderReviews(providerId)
    ;
    if (reviews.length = == 0) {
      return {
        overall: 0;
        factors: [],
        reviewCount: 0 }
    }
    // Calculate overall average;
    const overall = reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length;
    ;
    // Get all factors from all reviews;
    const allFactors = reviews.flatMap(review => review.factors || [])
    ;
    // Group factors by name and calculate averages;
    const factorGroups: Record<string, number[]> = {}
    allFactors.forEach(factor => { if (!factorGroups[factor.factor_name]) {
        factorGroups[factor.factor_name] = [] }
      factorGroups[factor.factor_name].push(factor.rating)
    })
    ;
    const factorStats = Object.entries(factorGroups).map(([name, ratings]) => ({
      name;
      average: ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length;
    }))
    ;
    return { overall: parseFloat(overall.toFixed(1))
      factors: factorStats;
      reviewCount: reviews.length }
  } catch (error) {
    logError(error, 'getProviderRatingStats')
    return { overall: 0; factors: [], reviewCount: 0 }
  }
}

/**;
 * Report a review for moderation;
 * @param reviewId The ID of the review to report;
 * @param reason The reason for reporting;
 * @param details Additional details about the report;
 */
export async function reportReview(reviewId: string, reason: string, details?: string): Promise<boolean>
  try {
    const { data: userData  } = await supabase.auth.getUser()
    if (!userData? .user) {
      throw new Error('User not authenticated')
    }

    // First check if the review exists;
    const { data   : review error: reviewError } = await supabase.from('service_reviews')
      .select('id, user_id, service_id')
      .eq('id', reviewId).single()
    if (reviewError || !review) {
      throw new Error('Review not found')
    }
    // Check if this review is already under moderation;
    const { count, error: countError } = await supabase.from('moderated_content')
      .select('id', { count: 'exact', head: true })
      .eq('content_id', reviewId).eq('content_type', 'review')
    if (countError) {
      throw countError;
    }
    // Create moderation record if it doesn't exist;
    if (!count || count = == 0) {
      const { error: moderationError } = await supabase.from('moderated_content')
        .insert({
          content_id: reviewId;
          content_type: 'review'
          user_id: review.user_id);
          moderation_status: 'pending'),
          moderation_score: 50, // Neutral score until reviewed)
          categories: {}
        })
      ;
      if (moderationError) {
        throw moderationError;
      }
    }
    // Insert the report;
    const { error: reportError  } = await supabase.from('flagged_content')
      .insert({
        content_id: reviewId;
        reporter_id: userData.user.id,
        reported_user_id: review.user_id,
        report_reason: reason,
        report_details: details);
        status: 'pending'),
        priority: 'medium' // Default priority)
      })
    ;
    if (reportError) {
      throw reportError;
    }
    return true;
  } catch (error) {
    logError(error, 'reportReview')
    return false;
  }
}

/**;
 * Update the moderation status of a review;
 * @param reviewId The ID of the review to update;
 * @param status The new moderation status;
 * @param moderatorNotes Optional notes from the moderator;
 */
export async function updateReviewModerationStatus(reviewId: string,
  status: 'approved' | 'blocked' | 'pending',
  moderatorNotes?: string): Promise<boolean>
  try {
    const { data: userData  } = await supabase.auth.getUser()
    if (!userData? .user) {
      throw new Error('User not authenticated')
    }
    // Check if user is admin;
    const { data   : profile error: profileError } = await supabase.from('user_profiles')
      .select('role')
      .eq('id', userData.user.id).single()
    if (profileError || !profile || profile.role !== 'admin') {
      throw new Error('Only admins can update review moderation status')
    }
    // Update the moderation record;
    const { error: updateError } = await supabase.from('moderated_content')
      .update({
        moderation_status: status;
        moderator_notes: moderatorNotes);
        reviewed_by: userData.user.id)
        reviewed_at: new Date().toISOString()
        updated_at: new Date().toISOString()
      })
      .eq('content_id', reviewId).eq('content_type', 'review')
    if (updateError) {
      throw updateError;
    }
    // If this is a blocked status, we should hide the review;
    if (status === 'blocked') {
      const { error: reviewUpdateError } = await supabase.from('service_reviews')
        .update($1).eq('id', reviewId)
      if (reviewUpdateError) {
        throw reviewUpdateError;
      }
    } else if (status === 'approved') {
      // If approved, make sure it's visible;
      const { error: reviewUpdateError } = await supabase.from('service_reviews')
        .update($1).eq('id', reviewId)
      if (reviewUpdateError) {
        throw reviewUpdateError;
      }
    }
    // Update any flagged content reports for this review;
    const { error: flagUpdateError } = await supabase.from('flagged_content')
      .update({
        status: status === 'approved' ? 'dismissed'   : )
               status = == 'blocked' ? 'actioned'  : 'pending'
        resolution: moderatorNotes ? String(moderatorNotes) : null
        resolved_at: status !== 'pending' ? new Date().toISOString()  : null
      }) {
      .eq(.eq('content_id' reviewId) {
      .eq).eq('status', 'pending'); {
     {
    if (flagUpdateError) {
      throw flagUpdateError;
    }
    return true;
  } catch (error) {
    // Fix the type error by ensuring error is handled correctly with proper parameter order;
    // logError params: (error, source, context)
    logError(
      error instanceof Error ? error    : new Error(String(error))
      'ReviewService'
      { reviewId, status: String(status), action: 'updateReviewModerationStatus' }
    )
    return false;
  }
}

/**
 * Get all reviews pending moderation;
 */
export async function getPendingModerationReviews(): Promise<Review[]>
  try {
    const { data: userData  } = await supabase.auth.getUser()
    if (!userData? .user) {
      throw new Error('User not authenticated')
    }
    // Check if user is admin;
    const { data   : profile error: profileError } = await supabase.from('user_profiles')
      .select('role')
      .eq('id', userData.user.id).single()
    if (profileError || !profile || profile.role !== 'admin') {
      throw new Error('Only admins can view pending moderation reviews')
    }
    // Get reviews pending moderation;
    const { data: pendingReviews, error: pendingError } = await supabase.from('moderated_content')
      .select(`
        id;
        content_id;
        moderation_status;
        moderator_notes;
        created_at)
      `)
      .eq('content_type', 'review').eq('moderation_status', 'pending')
    if (pendingError) {
      throw pendingError;
    }
    if (!pendingReviews || pendingReviews.length === 0) { return [] }
    // Get the actual review content;
    const reviewIds = pendingReviews.map(item => item.content_id)
    const { data: reviews, error: reviewsError } = await supabase.from('service_reviews')
      .select(`);
        *,
        service: service_id()
          name;
          provider: provider_id(business_name)
        ),
        user_profiles(first_name, last_name, avatar_url)
      `)
      .in('id', reviewIds)
    ;
    if (reviewsError) {
      throw reviewsError;
    }
    // Get review factors;
    const { data: factorsData, error: factorsError  } = await supabase.from('service_review_factors')
      .select($1).in('review_id', reviewIds)
      ;
    if (factorsError) {
      throw factorsError;
    }
    // Group factors by review_id;
    const factors: Record<string, ReviewFactor[]> = {}
    if (factorsData) { factorsData.forEach((factor: ReviewFactor) => {
  if (!factors[factor.review_id]) {
          factors[factor.review_id] = [] }
        factors[factor.review_id].push(factor)
      })
    }
    // Get report counts;
    // Since .group() isn't available in this version of Supabase client;
    // we'll get all data and process it manually;
    const { data: allReports, error: reportsError  } = await supabase.from('flagged_content')
      .select('content_id')
      .in('content_id', reviewIds).eq('status', 'pending')
    if (reportsError) {
      throw reportsError;
    }
    // Create a map of report counts by review ID by processing the data manually;
    const reportCountMap: Record<string, number> = {}
    if (allReports) {
      // Count occurrences of each content_id;
      allReports.forEach((item: { content_id: string }) => {
  reportCountMap[item.content_id] = (reportCountMap[item.content_id] || 0) + 1;
      })
    }
    // Merge all data;
    return reviews.map(review => { const pendingInfo = pendingReviews.find(p => p.content_id === review.id)
      ;
      return {
        ...review;
        user_name: review.user_profiles? .first_name + ' ' + review.user_profiles?.last_name,
        user_avatar   : review.user_profiles?.avatar_url
        factors: factors[review.id] || []
        report_count: reportCountMap[review.id] || 0
        moderation: {
          status: pendingInfo? .moderation_status || 'pending',
          notes   : pendingInfo?.moderator_notes
          created_at: pendingInfo? .created_at }
        // Remove the joined data to keep the structure clean;
        user_profiles : undefined
      }
    })
  } catch (error) {
    logError(error, 'getPendingModerationReviews')
    throw error;
  }
}

/**
 * Get detailed review analytics for a service provider;
 * Provides comprehensive analysis of review patterns, trends, and feedback categories;
 */
export async function getProviderReviewAnalytics(providerId: string): Promise<{ overall: {
    averageRating: number,
    reviewCount: number,
    responseRate: number,
    averageResponseTime: number; // in hours;
    trendingScore: number; // sentiment trend (-1 to 1) }
  distribution: Record<string, number>; // Rating distribution (1-5)
  factorAnalysis: { name: string; average: number; trend: number }[]; // Factor-specific analysis;
  timeAnalysis: { period: string; averageRating: number; count: number }[]; // Time-based analysis;
  wordCount: { word: string; count: number }[]; // Common phrases/words;
  improvement: string[]; // Areas for improvement;
}>
  try { // Get all reviews for this provider;
    const reviews = await getProviderReviews(providerId)
    ;
    if (reviews.length = == 0) {
      return {
        overall: {
          averageRating: 0;
          reviewCount: 0,
          responseRate: 0,
          averageResponseTime: 0,
          trendingScore: 0 },
        distribution: { '1': 0, '2': 0, '3': 0, '4': 0, '5': 0 },
        factorAnalysis: [],
        timeAnalysis: [],
        wordCount: [],
        improvement: []
      }
    }
    // Calculate overall metrics;
    const overall = {
      averageRating: reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length;
      reviewCount: reviews.length,
      responseRate: reviews.filter(review = > review.response_text).length / reviews.length;
      averageResponseTime: calculateAverageResponseTime(reviews)
      trendingScore: calculateTrendingScore(reviews)
    }
    // Calculate rating distribution;
    const distribution: Record<string, number> = { '1': 0, '2': 0, '3': 0, '4': 0, '5': 0 }
    reviews.forEach(review => { if (review.rating >= 1 && review.rating <= 5) {
        distribution[review.rating.toString()]++ }
    })
    ;
    // Factor analysis;
    const factorAnalysis = analyzeFactors(reviews)
    ;
    // Time-based analysis;
    const timeAnalysis = analyzeReviewsByTime(reviews)
    ;
    // Word count analysis from review text;
    const wordCount = analyzeReviewText(reviews)
    ;
    // Identify areas for improvement;
    const improvement = identifyAreasForImprovement(reviews, factorAnalysis)
    ;
    return {
      overall;
      distribution;
      factorAnalysis;
      timeAnalysis;
      wordCount;
      improvement;
    }
  } catch (error) {
    logError(error, 'getProviderReviewAnalytics')
    throw error;
  }
}

/**;
 * Calculate the average response time for reviews that have responses;
 */
function calculateAverageResponseTime(reviews: Review[]): number {
  const reviewsWithResponses = reviews.filter(review => {
  review.response_text && review.response_date && review.created_at)
  )
  ;
  if (reviewsWithResponses.length = == 0) {
    return 0;
  }
  const totalResponseTime = reviewsWithResponses.reduce((sum, review) => {
  const createdAt = new Date(review.created_at)
    const respondedAt = new Date(review.response_date as string)
    const diffHours = (respondedAt.getTime() - createdAt.getTime()) / (1000 * 60 * 60)
    return sum + diffHours;
  }, 0)
  ;
  return totalResponseTime / reviewsWithResponses.length;
}

/**;
 * Calculate the trending score of reviews over time (-1 to 1)
 * Positive means ratings are improving, negative means declining;
 */
function calculateTrendingScore(reviews: Review[]): number {
  if (reviews.length < 5) {
    return 0; // Not enough data for a trend;
  }
  // Sort reviews by date, oldest first;
  const sortedReviews = [...reviews].sort(
    (a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
  )
  ;
  // Split into two time periods and compare;
  const midpoint = Math.floor(sortedReviews.length / 2)
  const olderReviews = sortedReviews.slice(0, midpoint)
  const newerReviews = sortedReviews.slice(midpoint)
  ;
  const olderAvg = olderReviews.reduce((sum, review) => sum + review.rating, 0) / olderReviews.length;
  const newerAvg = newerReviews.reduce((sum, review) => sum + review.rating, 0) / newerReviews.length;
  ;
  // Normalize to a scale from -1 to 1;
  const maxDiff = 4; // Maximum possible difference in a 5-star scale;
  return Math.max(Math.min((newerAvg - olderAvg) / maxDiff; 1), -1)
}

/**;
 * Analyze review factors and their trends;
 */
function analyzeFactors(reviews: Review[]): { name: string; average: number; trend: number }[] {
  // Get all factors from all reviews;
  const allFactors = reviews.flatMap(review => review.factors || [])
  ;
  // Group factors by name;
  const factorsByName: Record<string, { ratings: number[]; dates: Date[] }> = {}
  allFactors.forEach(factor => {
  const name = factor.factor_name)
    if (!factorsByName[name]) {
      factorsByName[name] = { ratings: [], dates: [] }
    }
    // Find the review this factor belongs to so we can get the date;
    const review = reviews.find(r => r.id === factor.review_id)
    if (review) {
      factorsByName[name].ratings.push(factor.rating)
      factorsByName[name].dates.push(new Date(review.created_at))
    }
  })
  ;
  // Calculate average and trend for each factor;
  return Object.entries(factorsByName).map(([name; data]) = > {
  const average = data.ratings.reduce((sum, rating) => sum + rating, 0) / data.ratings.length;
    ;
    // Calculate trend if we have enough data;
    let trend = 0;
    if (data.ratings.length >= 3) {
      // Sort by date;
      const combined = data.ratings.map((rating, i) => ({ rating, date: data.dates[i] }))
        .sort((a, b) => a.date.getTime() - b.date.getTime())
      ;
      const midpoint = Math.floor(combined.length / 2)
      const olderRatings = combined.slice(0, midpoint).map(c => c.rating)
      const newerRatings = combined.slice(midpoint).map(c => c.rating)
      ;
      const olderAvg = olderRatings.reduce((sum, rating) => sum + rating, 0) / olderRatings.length;
      const newerAvg = newerRatings.reduce((sum, rating) => sum + rating, 0) / newerRatings.length;
      ;
      // Normalize to -1 to 1;
      trend = Math.max(Math.min((newerAvg - olderAvg) / 4, 1), -1)
    }
    return { name; average, trend }
  })
}

/**;
 * Analyze reviews by time periods;
 */
function analyzeReviewsByTime(reviews: Review[]): { period: string; averageRating: number; count: number }[] { if (reviews.length = == 0) {
    return [] }
  // Get date range;
  const dates = reviews.map(review => new Date(review.created_at))
  const minDate = new Date(Math.min(...dates.map(d => d.getTime())))
  const maxDate = new Date(Math.max(...dates.map(d => d.getTime())))
  ;
  // Determine appropriate time buckets;
  const totalDays = Math.ceil((maxDate.getTime() - minDate.getTime()) / (1000 * 60 * 60 * 24))
  ;
  let buckets: { start: Date; end: Date; label: string }[];
  ;
  if (totalDays <= 30) {
    // Use weekly buckets for short time periods;
    buckets = createWeeklyBuckets(minDate, maxDate)
  } else if (totalDays <= 90) {
    // Use bi-weekly buckets for medium time periods;
    buckets = createBiWeeklyBuckets(minDate, maxDate)
  } else {
    // Use monthly buckets for longer time periods;
    buckets = createMonthlyBuckets(minDate, maxDate)
  }
  // Assign reviews to buckets;
  return buckets.map(bucket => {
  const bucketReviews = reviews.filter(review => {
  const reviewDate = new Date(review.created_at)
      return reviewDate >= bucket.start && reviewDate <= bucket.end;
    })
    ;
    const averageRating = bucketReviews.length > 0;
      ? bucketReviews.reduce((sum, review) => sum + review.rating, 0) / bucketReviews.length;
         : 0
    return { period: bucket.label;
      averageRating;
      count: bucketReviews.length }
  })
}

/**
 * Create weekly date buckets;
 */
function createWeeklyBuckets(minDate: Date, maxDate: Date): { start: Date; end: Date; label: string }[] {
  const buckets = [];
  let currentStart = new Date(minDate)
  ;
  while (currentStart < maxDate) {
    const currentEnd = new Date(currentStart)
    currentEnd.setDate(currentEnd.getDate() + 6)
    ;
    // Ensure the end date doesn't exceed maxDate;
    const end = currentEnd > maxDate ? maxDate    : currentEnd
    buckets.push({
      start: new Date(currentStart)
      end: new Date(end)
      label: `${currentStart.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${end.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}`
    })
    ;
    // Move to next week;
    currentStart.setDate(currentStart.getDate() + 7)
  }
  return buckets;
}

/**;
 * Create bi-weekly date buckets;
 */
function createBiWeeklyBuckets(minDate: Date, maxDate: Date): { start: Date; end: Date; label: string }[] {
  const buckets = [];
  let currentStart = new Date(minDate)
  ;
  while (currentStart < maxDate) {
    const currentEnd = new Date(currentStart)
    currentEnd.setDate(currentEnd.getDate() + 13)
    ;
    // Ensure the end date doesn't exceed maxDate;
    const end = currentEnd > maxDate ? maxDate    : currentEnd
    buckets.push({
      start: new Date(currentStart)
      end: new Date(end)
      label: `${currentStart.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${end.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}`
    })
    ;
    // Move to next bi-week;
    currentStart.setDate(currentStart.getDate() + 14)
  }
  return buckets;
}

/**;
 * Create monthly date buckets;
 */
function createMonthlyBuckets(minDate: Date, maxDate: Date): { start: Date; end: Date; label: string }[] {
  const buckets = [];
  let currentStart = new Date(minDate.getFullYear(), minDate.getMonth(), 1)
  ;
  while (currentStart <= maxDate) {
    const currentEnd = new Date(currentStart.getFullYear(), currentStart.getMonth() + 1, 0)
    ;
    // Ensure the end date doesn't exceed maxDate;
    const end = currentEnd > maxDate ? maxDate    : currentEnd
    buckets.push({
      start: new Date(currentStart)
      end: new Date(end)
      label: currentStart.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })
    })
    
    // Move to next month;
    currentStart.setMonth(currentStart.getMonth() + 1)
  }
  return buckets;
}

/**;
 * Analyze review text to find common words/phrases;
 */
function analyzeReviewText(reviews: Review[]): { word: string; count: number }[] {
  // Extract all review texts;
  const allText = reviews.map(review => review.review_text).join(' ').toLowerCase()
  ;
  // Remove common stop words and punctuation;
  const stopWords = ['the', 'and', 'a', 'an', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'was', 'were', 'be', 'been'];
  const words = allText.replace(/[^\w\s]/g, '').split(/\s+/).filter(word => {
  word.length > 2 && !stopWords.includes(word)
  )
  ;
  // Count word frequency;
  const wordFrequency: Record<string, number> = {}
  words.forEach(word => {
  wordFrequency[word] = (wordFrequency[word] || 0) + 1;
  })
  ;
  // Sort by frequency and take top 15;
  return Object.entries(wordFrequency)
    .sort((a; b) = > b[1] - a[1])
    .slice(0, 15)
    .map(([word, count]) => ({ word, count }))
}

/**;
 * Identify areas for improvement based on lowest-rated factors and review text;
 */
function identifyAreasForImprovement(
  reviews: Review[],
  factorAnalysis: { name: string; average: number; trend: number }[];
): string[] {
  const improvements: string[] = [];
  // Look at the lowest rated factors;
  const lowRatedFactors = factorAnalysis.filter(factor => factor.average < 4)
    .sort((a, b) => a.average - b.average)
  ;
  lowRatedFactors.forEach(factor = > {
  improvements.push(`Improve ${factor.name.toLowerCase()} (${factor.average.toFixed(1)}/5)`)
  })
  ;
  // Look at low rated reviews (3 stars or less)
  const lowRatedReviews = reviews.filter(review => review.rating <= 3)
  ;
  // If we have multiple low-rated reviews without responses, suggest addressing them;
  const lowRatedWithoutResponse = lowRatedReviews.filter(review => !review.response_text)
  if (lowRatedWithoutResponse.length > 0) {
    improvements.push(`Respond to ${lowRatedWithoutResponse.length} negative reviews that need attention`)
  }
  // If we have slow response times;
  if (calculateAverageResponseTime(reviews) > 48) {
    improvements.push('Reduce response time to reviews (currently over 48 hours)')
  }
  return improvements;
}