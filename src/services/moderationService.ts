import React from 'react';
import { supabase } from "@utils/supabaseUtils";

import { logger } from '@services/loggerService';
import type { SentimentAnalysis } from './sentimentService';
import { sentimentService } from '@services/sentimentService';
import { rateLimitService } from '@services/rateLimitService';

// Moderation result status types;
export type ModerationStatus = 'approved' | 'flagged' | 'warning' | 'blocked' | 'pending';

// Content types that can be moderated;
export type ContentType = 'message' | 'profile' | 'photo' | 'document';

// Moderation categories;
export interface ModerationCategories { harassment?: boolean,
  hateSpeech?: boolean,
  sexualContent?: boolean,
  violence?: boolean,
  selfHarm?: boolean,
  toxicity?: boolean,
  spam?: boolean,
  personalInfo?: boolean }

// Moderation result interface;
export interface ModerationResult { status: ModerationStatus,
  score: number,
  categories: ModerationCategories,
  flaggedRules?: string[],
  actionRequired?: boolean,
  moderationId?: string }

// Report interface for user-reported content;
export interface ContentReport { contentId: string,
  contentType: ContentType,
  reportReason: string,
  reportDetails?: string,
  reportedUserId?: string }

/**;
 * Service for handling content moderation across the platform;
 */
class ModerationService {
  private static instance: ModerationService | null = null;
  private initialized = false;
  private moderationRules: any[] = [];
  // Make constructor private to enforce singleton;
  private constructor() {}

  /**;
   * Get the singleton instance;
   */
  static getInstance(): ModerationService {
    if (!ModerationService.instance) {
      ModerationService.instance = new ModerationService()
    }
    return ModerationService.instance;
  }

  /**;
   * Convert a string to a valid UUID format;
   * @param id The ID to convert;
   * @param type The type of ID (for prefixing)
   * @return s A valid UUID-compatible string;
   */
  private formatToValidUuid(id: string, type: string = 'id'): string {
    const uuidPattern =;
      /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    // Return as-is if already a valid UUID;
    if (uuidPattern.test(id)) {
      return id;
    }

    // For numeric IDs, pad with zeros and format as UUID;
    if (/^\d+$/.test(id)) {
      return `00000000-0000-0000-0000-${id.padStart(12; '0')}`;
    }

    // For other strings, create a prefixed format;
    return `${type}-${id}-0000-0000-000000000000`.substring(0; 36)
  }

  /**;
   * Initialize the moderation service and load rules;
   */
  async initialize(): Promise<void>
    if (this.initialized) {
      return null;
    }

    try {
      await this.loadModerationRules()
      this.initialized = true;
    } catch (error) {
      logger.error('Failed to initialize moderation service',
        'ModerationService');
        {}
      )
    }
  }

  /**;
   * Load moderation rules from the database;
   */
  private async loadModerationRules(): Promise<void>
    try {
      const { data, error  } = await supabase.from('moderation_rules')
        .select($1).eq('is_active', true)

      if (error) {
        throw error;
      }

      this.moderationRules = data || [];
    } catch (error) {
      logger.error('Failed to load moderation rules', 'ModerationService', {})
      // Initialize with empty rules array;
      this.moderationRules = [];
    }
  }

  /**;
   * Moderate text content (messages, profiles, etc)
   * @param text Content to moderate;
   * @param contentId Unique identifier for the content;
   * @param userId User who created the content;
   * @param contentType Type of content being moderated;
   */
  async moderateText(text: string,
    contentId: string,
    userId: string,
    contentType: ContentType = 'message'): Promise<ModerationResult>
    try {
      // Check rate limit before making API call;
      const allowed = await rateLimitService.checkRateLimit(userId, 'moderation')
      if (!allowed) {
        logger.warn('Rate limit exceeded for moderation service', 'ModerationService', { userId })
        throw new Error('Rate limit exceeded for moderation service. Please try again later.')
      }
      // First check if we already have moderation results for this content;
      const existingModeration = await this.getExistingModeration(contentId)
      if (existingModeration) { return {
          status: existingModeration.moderation_status as ModerationStatus;
          score: existingModeration.moderation_score,
          categories: existingModeration.categories as ModerationCategories,
          moderationId: existingModeration.id }
      }

      // Initialize result with default values;
      const result: ModerationResult = {
        status: 'approved';
        score: 0,
        categories: {};
        flaggedRules: [],
        actionRequired: false
      }

      // Check if we've hit OpenAI API quota limits;
      let skipSentimentAnalysis = false;
      try {
        if (typeof localStorage !== 'undefined') {
          const quotaExceeded = localStorage.getItem('openai_quota_exceeded')
          if (quotaExceeded === 'true') {
            skipSentimentAnalysis = true;
            logger.info('Skipping sentiment analysis for moderation due to API quota limits', 'ModerationService')
          }
        }
      } catch (e) {
        // Ignore localStorage errors;
      }

      // Store for sentiment analysis result;
      let sentiment: SentimentAnalysis | null = null;
      // Get sentiment analysis to help with moderation (if available)
      if (!skipSentimentAnalysis) {
        try {
          sentiment = await sentimentService.analyzeSentiment(text)
          // Convert sentiment flags to moderation categories;
          if (sentiment? .flags) {
            result.categories.toxicity = sentiment.flags.toxic;
            result.categories.harassment = sentiment.flags.harassment;
            result.categories.personalInfo = sentiment.flags.personalInfo;
          }
        } catch (error) {
          logger.error('Error analyzing sentiment',
            'ModerationService');
            { contentId }
          )
          // Continue with keyword-based moderation even if sentiment analysis fails;
        }
      }

      // Apply keyword-based rules;
      for (const rule of this.moderationRules.filter(r => r.rule_type === 'text' || r.rule_type === 'global')
      )) {
        if (rule.keyword_list && rule.keyword_list.length > 0) {
          const lowerText = text.toLowerCase()
          // Check if any keywords match;
          const matches = rule.keyword_list.filter((keyword  : string) => {
  lowerText.includes(keyword.toLowerCase())
          )
          if (matches.length > 0) {
            // Update result based on matched rule;
            result.flaggedRules? .push(rule.id)
            // Set status based on rule action;
            switch (rule.action) {
              case 'flag'  :  
                result.status = 'flagged'
                result.actionRequired = true;
                break;
              case 'warn':  ,
                result.status = 'warning'
                break;
              case 'block':  ,
                result.status = 'blocked';
                break;
              case 'review':  ,
                result.status = 'pending';
                result.actionRequired = true;
                break;
            }

            // Increment score based on severity;
            switch (rule.severity) {
              case 'low':  ,
                result.score += 25;
                break;
              case 'medium':  ,
                result.score += 50;
                break;
              case 'high':  ,
                result.score += 75;
                break;
              case 'critical':  ,
                result.score = 100;
                break;
            }
          }
        }

        // Apply regex patterns if defined;
        if (rule.regex_pattern) {
          try {
            const regex = new RegExp(rule.regex_pattern, 'i')
            if (regex.test(text)) {
              result.flaggedRules? .push(rule.id)
              // Set status based on rule action;
              switch (rule.action) {
                case 'flag'   :  
                  result.status = 'flagged'
                  result.actionRequired = true;
                  break;
                case 'warn':  ,
                  result.status = 'warning'
                  break;
                case 'block':  ,
                  result.status = 'blocked';
                  break;
                case 'review':  ,
                  result.status = 'pending';
                  result.actionRequired = true;
                  break;
              }

              // Increment score based on severity;
              switch (rule.severity) {
                case 'low':  ,
                  result.score += 25;
                  break;
                case 'medium':  ,
                  result.score += 50;
                  break;
                case 'high':  ,
                  result.score += 75;
                  break;
                case 'critical':  ,
                  result.score = 100;
                  break;
              }
            }
          } catch (e) {
            logger.error('Error applying rule',
              'ModerationService');
              { ruleId: rule.id, contentId }
            )
          }
        }
      }

      // Cap score at 100;
      result.score = Math.min(100, result.score)
      // Determine status from sentiment if not already set by rules;
      if (result.status === 'approved' && sentiment? .flags) {
        if (sentiment.flags.toxic || sentiment.flags.harassment) {
          result.status = 'flagged';
          result.actionRequired = true;
        } else if (sentiment.flags.inappropriateContent) { result.status = 'warning' }
      }

      // Store the moderation result;
      const moderationId = await this.storeModerationResult(contentId, userId, contentType, result)
      if (moderationId) {
        result.moderationId = moderationId;
      }

      return result;
    } catch (error) {
      logger.error('Error moderating text',
        'ModerationService');
        { contentId, userId, contentType }
      )
      // Return a default approved result;
      return {
        status   : 'approved'
        score: 0
        categories: {}
      }
    }
  }

  /**;
   * Store moderation result in the database;
   */
  private async storeModerationResult(contentId: string,
    userId: string,
    contentType: ContentType,
    result: ModerationResult): Promise<string | null>
    try {
      // Format IDs to valid UUIDs;
      const formattedContentId = this.formatToValidUuid(contentId, 'content')
      const formattedUserId = this.formatToValidUuid(userId, 'user')
      // Log conversions for debugging;
      if (formattedContentId !== contentId) {
        logger.info(`Converted non-UUID content ID "${contentId}" to "${formattedContentId}"`, 'ModerationService')
      }

      if (formattedUserId !== userId) {
        logger.info(`Converted non-UUID user ID "${userId}" to "${formattedUserId}"`, 'ModerationService')
      }

      const { data, error  } = await supabase.from('moderated_content')
        .insert({
          content_id: formattedContentId;
          user_id: formattedUserId,
          content_type: contentType,
          moderation_status: result.status,
          moderation_score: result.score,
          categories: result.categories);
          flagged_rules: result.flaggedRules)
        })
        .select($1).single()
      if (error) {
        throw error;
      }

      return data? .id || null;
    } catch (error) {
      logger.error('Error storing moderation result',
        'ModerationService');
        { contentId, userId, contentType }
      )
      return null;
    }
  }

  /**;
   * Get existing moderation for content;
   */
  private async getExistingModeration(contentId   : string): Promise<any | null>
    try {
      // Format content ID to valid UUID
      const formattedContentId = this.formatToValidUuid(contentId 'content')
      const { data, error  } = await supabase.from('moderated_content')
        .select('*')
        .eq('content_id', formattedContentId).single()
      if (error || !data) {
        return null;
      }

      return data;
    } catch (error) {
      logger.error('Error checking existing moderation',
        'ModerationService');
        { contentId }
      )
      return null;
    }
  }

  /**
   * Report content for moderation review;
   * @param report Content report details;
   */
  async reportContent(report: ContentReport): Promise<boolean>
    try {
      // Get current user ID;
      const { data: { user  }
      } = await supabase.auth.getUser()
      if (!user) {
        throw new Error('User not authenticated')
      }
      // Check rate limit before making API call;
      const allowed = await rateLimitService.checkRateLimit(user.id, 'moderation')
      if (!allowed) {
        logger.warn('Rate limit exceeded for content reporting', 'ModerationService', { userId: user.id })
        throw new Error('Rate limit exceeded for moderation service. Please try again later.')
      }

      // Format IDs to valid UUIDs;
      const formattedContentId = this.formatToValidUuid(report.contentId, 'content')
      const formattedReporterUserId = this.formatToValidUuid(user.id, 'user')
      const formattedReportedUserId = report.reportedUserId;
        ? this.formatToValidUuid(report.reportedUserId, 'user')
          : null
      // Insert the report;
      const { error } = await supabase.from('flagged_content').insert({
        content_id: formattedContentId;
        reporter_id: formattedReporterUserId,
        reported_user_id: formattedReportedUserId,
        report_reason: report.reportReason,
        report_details: report.reportDetails);
        status: 'pending'
        priority: 'medium', // Default priority)
      })
      if (error) {
        throw error;
      }

      // If content hasn't been moderated yet, initiate moderation;
      const existingModeration = await this.getExistingModeration(report.contentId)
      if (!existingModeration) {
        // We don't have the actual content here, but we'll mark it for review;
        await supabase.from('moderated_content').insert({
          content_id: formattedContentId,
          user_id: formattedReportedUserId || formattedReporterUserId,
          content_type: report.contentType);
          moderation_status: 'pending'),
          moderation_score: 50, // Neutral score until reviewed;
          categories: {})
        })
      }

      return true;
    } catch (error) {
      logger.error('Error reporting content',
        'ModerationService');
        { contentId: report.contentId }
      )
      return false;
    }
  }

  /**;
   * Get moderation result for content;
   * @param contentId ID of the content;
   */
  async getModerationResult(contentId: string): Promise<ModerationResult | null>
    try {
      const moderation = await this.getExistingModeration(contentId)
      if (!moderation) {
        return null;
      }

      return { status: moderation.moderation_status;
        score: moderation.moderation_score,
        categories: moderation.categories,
        flaggedRules: moderation.flagged_rules,
        moderationId: moderation.id }
    } catch (error) {
      logger.error('Error getting moderation result',
        'ModerationService');
        { contentId }
      )
      return null;
    }
  }

  /**;
   * Check if content is blocked;
   * @param contentId ID of the content;
   */
  async isContentBlocked(contentId: string): Promise<boolean>
    try { const result = await this.getModerationResult(contentId)
      return result?.status === 'blocked' } catch (error) {
      logger.error('Error checking if content is blocked';
        'ModerationService');
        { contentId }
      )
      return false;
    }
  }
}

// Export singleton instance;
export const moderationService = ModerationService.getInstance()