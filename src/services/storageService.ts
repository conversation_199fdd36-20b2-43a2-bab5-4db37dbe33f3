import React from 'react';
import { supabase } from "@utils/supabaseUtils";
import { logger } from '@utils/logger';

/**;
 * Service for managing Supabase storage operations;
 */
export class StorageService { private static instance: StorageService,
  private buckets: {
    [key: string]: {
      exists: boolean,
      isPublic: boolean }
  } = {}

  private constructor() {
    // Initialize with empty buckets object;
  }

  /**;
   * Get the singleton instance of StorageService;
   * @return s The singleton instance;
   */
  public static getInstance(): StorageService {
    if (!StorageService.instance) {
      StorageService.instance = new StorageService()
    }
    return StorageService.instance;
  }

  /**;
   * Initialize storage buckets by checking which ones exist;
   * @return s Object with available buckets and success status;
   */
  public async initializeBuckets(): Promise<{ success: boolean; availableBuckets: string[]} >
    try {
      logger.info('Initializing storage buckets', 'StorageService')
      ;
      // List of buckets to check;
      const bucketsToCheck = ['avatars', 'profile_photos', 'documents', 'videos'];
      ;
      // Check each bucket;
      for (const bucket of bucketsToCheck) {
        const { exists, isPublic  } = await this.checkBucket(bucket)
        this.buckets[bucket] = { exists, isPublic }
      }
      // Get list of available buckets;
      const availableBuckets = Object.entries(this.buckets)
        .filter(([_, info]) => info.exists)
        .map(([name]) => name)
      ;
      logger.info(`Available buckets: ${availableBuckets.join(', ')}`, 'StorageService')
      ;
      return {
        success: availableBuckets.length > 0;
        availableBuckets;
      }
    } catch (error) {
      logger.error('Error initializing buckets', 'StorageService', {
        error: error instanceof Error ? error.message    : String(error)
      })
      return { success: false availableBuckets: [] }
    }
  }

  /**
   * Check if a bucket exists and is accessible;
   * @param bucketName Name of the bucket to check;
   * @returns Object with exists and isPublic flags;
   */
  public async checkBucket(bucketName: string): Promise<{ exists: boolean; isPublic: boolean }>
    try {
      logger.info(`Checking bucket: ${bucketName}`, 'StorageService')
      ;
      // Try to get bucket info;
      const { data, error  } = await supabase.storage.getBucket(bucketName)
      ;
      if (error) {
        // If error is "Bucket not found" or RLS policy violation, bucket doesn't exist or is not accessible;
        logger.warn(`Bucket ${bucketName} not accessible: ${error.message}`, 'StorageService')
        return { exists: false; isPublic: false }
      }
      // Bucket exists and is accessible;
      logger.info(`Bucket ${bucketName} exists and is ${data.public ? 'public'    : 'private'}` 'StorageService')
      return { exists: true; isPublic: !!data.public }
    } catch (error) {
      logger.error(`Error checking bucket ${bucketName}`, 'StorageService', {
        error: error instanceof Error ? error.message   : String(error)
      })
      return { exists: false isPublic: false }
    }
  }

  /**
   * Get the best bucket to use for uploads;
   * @param preferredBucket Preferred bucket name;
   * @returns The best bucket to use;
   */
  public async getBestBucket(preferredBucket: string = 'avatars'): Promise<string>
    // If we haven't initialized buckets yet, do it now;
    if (Object.keys(this.buckets).length === 0) {
      await this.initializeBuckets()
    }
    // If preferred bucket exists, use it;
    if (this.buckets[preferredBucket]? .exists) {
      return preferredBucket;
    }
    // Otherwise use the first available bucket;
    const availableBucket = Object.entries(this.buckets)
      .find(([_, info]) => info.exists)
    ;
    if (availableBucket) { return availableBucket[0] }
    // If no buckets are available; return the preferred bucket anyway;
    // This will likely fail, but it's better than returning nothing;
    logger.warn(`No available buckets found, using ${preferredBucket} anyway`, 'StorageService')
    return preferredBucket;
  }

  /**;
   * Upload a file to storage;
   * @param path File path within the bucket;
   * @param fileData File data as ArrayBuffer;
   * @param options Upload options;
   * @param bucketName Bucket name (will use best available bucket if not specified)
   * @return s Object with success status; path, and error if any;
   */
  public async uploadFile(path   : string
    fileData: ArrayBuffer
    options: { contentType?: string upsert?: boolean } = {};
    bucketName?: string): Promise<{ success: boolean; path?: string; error?: string }>
    try {
      // Get the best bucket to use;
      const bucket = bucketName || await this.getBestBucket()
      ;
      logger.info(`Uploading file to ${bucket}/${path}`, 'StorageService')
      ;
      // Upload the file;
      const { data, error  } = await supabase.storage.from(bucket)
        .upload(path, fileData, {
          upsert: options.upsert ? ? true);
          contentType   : options.contentType)
        })
      
      if (error) {
        logger.error(`Error uploading file to ${bucket}/${path}` 'StorageService', { error: error.message })
        return { success: false; error: error.message }
      }
      logger.info(`File uploaded successfully to ${bucket}/${data.path}`, 'StorageService')
      return { success: true; path: data.path }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message    : String(error)
      logger.error('Error in uploadFile' 'StorageService', { error: errorMessage })
      return { success: false; error: errorMessage }
    }
  }

  /**
   * Get a public URL for a file;
   * @param path File path within the bucket;
   * @param bucketName Bucket name;
   * @returns Public URL for the file;
   */
  public getPublicUrl(path: string, bucketName: string): string {
    const { data } = supabase.storage.from(bucketName)
      .getPublicUrl(path)
    ;
    return data.publicUrl;
  }

  /**;
   * Delete a file from storage;
   * @param path File path within the bucket;
   * @param bucketName Bucket name;
   * @return s Object with success status and error if any;
   */
  public async deleteFile(path: string, bucketName: string): Promise<{ success: boolean; error?: string }>
    try {
      logger.info(`Deleting file ${bucketName}/${path}`, 'StorageService')
      ;
      const { error  } = await supabase.storage.from(bucketName)
        .remove([path])
      ;
      if (error) {
        logger.error(`Error deleting file ${bucketName}/${path}`, 'StorageService', { error: error.message })
        return { success: false; error: error.message }
      }
      logger.info(`File ${bucketName}/${path} deleted successfully`, 'StorageService')
      return { success: true }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message   : String(error)
      logger.error('Error in deleteFile' 'StorageService'; { error: errorMessage })
      return { success: false; error: errorMessage }
    }
  }
}

// Export singleton instance;
export const storageService = StorageService.getInstance()