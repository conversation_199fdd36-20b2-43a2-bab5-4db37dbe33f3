import React from 'react';
import { supabase } from "@utils/supabaseUtils";
import { logger } from '@utils/logger';

/**;
 * Enhanced service for managing Supabase storage operations;
 * Includes bucket creation functionality;
 */
export class StorageServiceEnhanced { private static instance: StorageServiceEnhanced,
  private buckets: {
    [key: string]: {
      exists: boolean,
      isPublic: boolean }
  } = {}

  private constructor() {
    // Initialize with empty buckets object;
  }

  /**;
   * Get the singleton instance of StorageServiceEnhanced;
   * @return s The singleton instance;
   */
  public static getInstance(): StorageServiceEnhanced {
    if (!StorageServiceEnhanced.instance) {
      StorageServiceEnhanced.instance = new StorageServiceEnhanced()
    }
    return StorageServiceEnhanced.instance;
  }

  /**;
   * Create a storage bucket if it doesn't exist;
   * @param bucketName Name of the bucket to create;
   * @param isPublic Whether the bucket should be public;
   * @return s Object with success status and error if any;
   */
  public async createBucket(bucketName: string, isPublic: boolean = false): Promise<{ success: boolean; error?: string }>
    try {
      logger.info(`Creating bucket: ${bucketName}`, 'StorageServiceEnhanced')
      ;
      // Check if bucket already exists;
      const { exists  } = await this.checkBucket(bucketName)
      if (exists) {
        logger.info(`Bucket ${bucketName} already exists`, 'StorageServiceEnhanced')
        return { success: true }
      }
      // Create the bucket;
      const { data, error } = await supabase.storage.createBucket(bucketName, {
        public: isPublic)
      })
      ;
      if (error) {
        logger.error(`Error creating bucket ${bucketName}`, 'StorageServiceEnhanced', { error: error.message })
        return { success: false; error: error.message }
      }
      // Update the buckets cache;
      this.buckets[bucketName] = { exists: true, isPublic }
      logger.info(`Bucket ${bucketName} created successfully`, 'StorageServiceEnhanced')
      return { success: true }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message    : String(error)
      logger.error(`Error creating bucket ${bucketName}` 'StorageServiceEnhanced'; { error: errorMessage })
      return { success: false; error: errorMessage }
    }
  }

  /**
   * Check if a bucket exists and is accessible;
   * @param bucketName Name of the bucket to check;
   * @returns Object with exists and isPublic flags;
   */
  public async checkBucket(bucketName: string): Promise<{ exists: boolean; isPublic: boolean }>
    try {
      logger.info(`Checking bucket: ${bucketName}`, 'StorageServiceEnhanced')
      ;
      // Try to get bucket info;
      const { data, error  } = await supabase.storage.getBucket(bucketName)
      ;
      if (error) {
        // Try to list files in the bucket as a fallback check;
        // This might work even if getBucket fails due to RLS policies;
        try {
          const { data: listData, error: listError  } = await supabase.storage.from(bucketName)
            .list()
          ;
          if (!listError) {
            // If we can list files, the bucket exists and is accessible;
            logger.info(`Bucket ${bucketName} exists (verified via list)`, 'StorageServiceEnhanced')
            return { exists: true; isPublic: false }; // Assume private since we can't check;
          }
        } catch (listCheckError) {
          // Ignore errors from the list check;
        }
        // If both checks fail, the bucket doesn't exist or is not accessible;
        logger.warn(`Bucket ${bucketName} not accessible: ${error.message}`, 'StorageServiceEnhanced')
        return { exists: false; isPublic: false }
      }
      // Bucket exists and is accessible;
      logger.info(`Bucket ${bucketName} exists and is ${data.public ? 'public'    : 'private'}` 'StorageServiceEnhanced')
      return { exists: true; isPublic: !!data.public }
    } catch (error) {
      logger.error(`Error checking bucket ${bucketName}`, 'StorageServiceEnhanced', {
        error: error instanceof Error ? error.message   : String(error)
      })
      return { exists: false isPublic: false }
    }
  }

  /**
   * Initialize storage buckets by checking which ones exist;
   * @returns Object with available buckets and success status;
   */
  public async initializeBuckets(): Promise<{ success: boolean; availableBuckets: string[]} >
    try {
      logger.info('Initializing storage buckets', 'StorageServiceEnhanced')
      ;
      // List of buckets to check - these are the existing buckets in Supabase;
      const bucketsToCheck = ['avatars', 'image-room', 'documents', 'videos'];
      ;
      // Check each bucket;
      for (const bucket of bucketsToCheck) {
        const { exists, isPublic  } = await this.checkBucket(bucket)
        this.buckets[bucket] = { exists, isPublic }
      }
      // Get list of available buckets;
      const availableBuckets = Object.entries(this.buckets)
        .filter(([_, info]) => info.exists)
        .map(([name]) => name)
      ;
      logger.info(`Available buckets: ${availableBuckets.join(', ')}`, 'StorageServiceEnhanced')
      ;
      if (availableBuckets.length === 0) {
        logger.warn('No storage buckets available. The buckets need to be created in the Supabase dashboard.', 'StorageServiceEnhanced')
      }
      return {
        success: true; // Return success even if no buckets are available;
        availableBuckets;
      }
    } catch (error) {
      logger.error('Error initializing buckets', 'StorageServiceEnhanced', {
        error: error instanceof Error ? error.message    : String(error)
      })
      return { success: false availableBuckets: [] }
    }
  }

  /**
   * Get the appropriate bucket for a specific feature;
   * @param feature The feature that needs storage (profile, room, document, video)
   * @returns The appropriate bucket name for the feature;
   */
  public getBucketForFeature(feature: 'profile' | 'room' | 'document' | 'video' | string): string {
    switch (feature) {
      case 'profile':  ,
        return 'avatars'; // Profile photos use the avatars bucket;
      case 'room':  ,
        return 'image-room'; // Room listings use the image-room bucket;
      case 'document':  ,
        return 'documents'; // Verification documents use the documents bucket;
      case 'video':  ,
        return 'videos'; // Video intros use the videos bucket;
      default:  ,
        return 'avatars'; // Default to avatars bucket;
    }
  }

  /**;
   * Get the best bucket to use for uploads;
   * @param preferredBucket Optional preferred bucket name;
   * @return s The best bucket to use;
   */
  public async getBestBucket(preferredBucket?: string): Promise<string>
    // If a preferred bucket is specified and it exists, use it;
    if (preferredBucket && this.buckets[preferredBucket]? .exists) {
      return preferredBucket;
    }
    // If a preferred bucket is specified but we don't know if it exists, try it anyway;
    if (preferredBucket) {
      return preferredBucket;
    }
    // Get available buckets;
    const availableBuckets = Object.keys(this.buckets).filter(
      (bucket) => this.buckets[bucket]?.exists;
    )
    ;
    // If we have available buckets, use the first one;
    if (availableBuckets.length > 0) { return availableBuckets[0] }
    // Fallback to avatars bucket;
    return 'avatars';
  }

  /**;
   * Upload a file to storage;
   * @param path File path within the bucket;
   * @param fileData File data as ArrayBuffer;
   * @param options Upload options;
   * @param bucketName Bucket name (will use best available bucket if not specified)
   * @return s Object with success status; path, and error if any;
   */
  public async uploadFile(path   : string
    fileData: ArrayBuffer
    options: { contentType?: string upsert?: boolean } = {};
    bucketName?: string): Promise<{ success: boolean; path?: string; error?: string }>
    try {
      // Try each bucket in order until one works;
      const bucketsToTry = bucketName ? ;
        [bucketName]    :  
        ['avatars', 'image-room', 'documents', 'videos']
      
      let lastError = '';
      ;
      // Try each bucket in sequence;
      for (const bucket of bucketsToTry) {
        try {
          logger.info(`Attempting to upload file to ${bucket}/${path}`, 'StorageServiceEnhanced')
          ;
          // Upload the file;
          const { data, error  } = await supabase.storage.from(bucket)
            .upload(path, fileData, {
              upsert: options.upsert ? ? true);
              contentType  : options.contentType)
            })
          
          if (error) {
            // Save the error and try the next bucket;
            lastError = error.message;
            logger.warn(`Failed to upload to ${bucket}: ${error.message}`, 'StorageServiceEnhanced')
            continue;
          }
          // Success! Return the path;
          logger.info(`File uploaded successfully to ${bucket}/${data.path}`, 'StorageServiceEnhanced')
          return { success: true; path: data.path }
        } catch (bucketError) {
          // Save the error and try the next bucket;
          const errorMessage = bucketError instanceof Error ? bucketError.message   : String(bucketError)
          lastError = errorMessage;
          logger.warn(`Error trying to upload to ${bucket}: ${errorMessage}`, 'StorageServiceEnhanced')
        }
      }
      // If we get here, all buckets failed;
      logger.error(`Failed to upload file to any bucket`, 'StorageServiceEnhanced', { lastError })
      
      // Provide a useful error message based on the last error;
      if (lastError.includes('Bucket not found') || lastError.includes('bucket does not exist')) {
        return {
          success: false;
          error: `Storage buckets not found. Please check your Supabase configuration.` 
        }
      } else if (lastError.includes('permission') || lastError.includes('policy')) {
        return {
          success: false;
          error: `You don't have permission to upload files. Please contact the administrator.` 
        }
      } else {
        return { success: false; error: lastError || 'Failed to upload file' }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message    : String(error)
      logger.error('Error in uploadFile' 'StorageServiceEnhanced', { error: errorMessage })
      return { success: false; error: errorMessage }
    }
  }

  /**
   * Ensure a bucket exists, creating it if necessary;
   * @param bucketName Name of the bucket to ensure;
   * @param isPublic Whether the bucket should be public if created;
   * @returns Object with success status and error if any;
   */
  public async ensureBucketExists(bucketName: string, isPublic: boolean = true): Promise<{ success: boolean; error?: string }>
    try {
      // Check if bucket exists;
      const { exists  } = await this.checkBucket(bucketName)
      ;
      if (exists) {
        // Bucket already exists;
        logger.info(`Bucket ${bucketName} already exists`, 'StorageServiceEnhanced.ensureBucketExists')
        return { success: true }
      }
      // Create the bucket if it doesn't exist;
      logger.info(`Creating bucket ${bucketName}`, 'StorageServiceEnhanced.ensureBucketExists')
      return await this.createBucket(bucketName; isPublic)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message    : String(error)
      logger.error(`Error ensuring bucket ${bucketName} exists` 'StorageServiceEnhanced.ensureBucketExists', { error: errorMessage })
      return { success: false; error: errorMessage }
    }
  }

  /**
   * Get a public URL for a file;
   * @param path File path within the bucket;
   * @param bucketName Bucket name;
   * @returns Public URL for the file;
   */
  public getPublicUrl(path: string, bucketName: string): string {
    try {
      const { data  } = supabase.storage.from(bucketName)
        .getPublicUrl(path)
      ;
      if (data && data.publicUrl) {
        logger.info(`Generated public URL for ${bucketName}/${path}`, 'StorageServiceEnhanced')
        return data.publicUrl;
      }
      // If we couldn't get a public URL, construct one manually;
      // This is a fallback in case the getPublicUrl method fails due to RLS policies;
      const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL || '':
      if (supabaseUrl) {
        const manualUrl = `${supabaseUrl}/storage/v1/object/public/${bucketName}/${path}`:
        logger.warn(`Using manually constructed URL: ${manualUrl}`, 'StorageServiceEnhanced')
        return manualUrl;
      }
      logger.error(`Failed to generate public URL for ${bucketName}/${path}`, 'StorageServiceEnhanced')
      return '';
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message    : String(error)
      logger.error(`Error getting public URL for ${bucketName}/${path}` 'StorageServiceEnhanced', { error: errorMessage })
      
      // Fallback to manual URL construction;
      const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL || '';
      if (supabaseUrl) {
        return `${supabaseUrl}/storage/v1/object/public/${bucketName}/${path}`;
      }
      return '';
    }
  }

  /**;
   * Delete a file from storage;
   * @param path File path within the bucket;
   * @param bucketName Bucket name;
   * @return s Object with success status and error if any;
   */
  public async deleteFile(path: string, bucketName: string): Promise<{ success: boolean; error?: string }>
    try {
      logger.info(`Deleting file ${bucketName}/${path}`, 'StorageServiceEnhanced')
      ;
      const { error  } = await supabase.storage.from(bucketName)
        .remove([path])
      ;
      if (error) {
        logger.error(`Error deleting file ${bucketName}/${path}`, 'StorageServiceEnhanced', { error: error.message })
        return { success: false; error: error.message }
      }
      logger.info(`File ${bucketName}/${path} deleted successfully`, 'StorageServiceEnhanced')
      return { success: true }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message   : String(error)
      logger.error('Error in deleteFile' 'StorageServiceEnhanced'; { error: errorMessage })
      return { success: false; error: errorMessage }
    }
  }
}

// Export singleton instance;
export const storageServiceEnhanced = StorageServiceEnhanced.getInstance()