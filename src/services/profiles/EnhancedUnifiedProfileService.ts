import React from 'react';
/**;
 * Enhanced Unified Profile Service;
 * ;
 * Consolidates all profile-related functionality into a single, transaction-safe service;
 * Replaces deprecated ProfileService and optimized variants;
 */

import { supabase } from '@utils/supabaseUtils';
import { logger } from '@utils/logger';
import { cacheService } from '@services/cacheService';
import type { ApiResponse, Profile, ProfileWithRelations } from '@types/models';

interface ProfileUpdateOptions { expectedVersion?: number,
  validateOnly?: boolean,
  skipCache?: boolean }

interface ProfileCacheSection { core: boolean,
  personality: boolean,
  media: boolean,
  preferences: boolean,
  verification: boolean }

export class EnhancedUnifiedProfileService {
  private static instance: EnhancedUnifiedProfileService,
  private readonly CACHE_TTL = 15 * 60 * 1000; // 15 minutes;
  private invalidationLocks = new Map<string, Promise<void>>()
  private constructor() {}

  public static getInstance(): EnhancedUnifiedProfileService {
    if (!EnhancedUnifiedProfileService.instance) {
      EnhancedUnifiedProfileService.instance = new EnhancedUnifiedProfileService()
    }
    return EnhancedUnifiedProfileService.instance;
  }

  /**;
   * Get profile by ID with optimized caching and error handling;
   */
  async getProfile(
    id: string,
    options: { includeRelations?: boolean; useCache?: boolean } = {}
  ): Promise<ApiResponse<ProfileWithRelations>> {
    const { includeRelations = true, useCache = true  } = options;
    ;
    try {
      // Check cache first;
      if (useCache) {
        const cached = await this.getCachedProfile(id, includeRelations)
        if (cached) {
          return { data: cached; error: null, status: 200 }
        }
      }

      // Fetch from database with optimized query;
      const profileData = await this.fetchProfileFromDatabase(id, includeRelations)
      ;
      if (!profileData) {
        return { data: null; error: 'Profile not found', status: 404 }
      }

      // Cache the result;
      if (useCache) {
        await this.cacheProfile(id, profileData, includeRelations)
      }

      return { data: profileData; error: null, status: 200 }
    } catch (error) {
      logger.error('Error fetching profile', 'EnhancedUnifiedProfileService.getProfile', {
        id;
        error: error instanceof Error ? error.message    : String(error)
      })
      return { data: null error: 'Failed to fetch profile'; status: 500 }
    }
  }

  /**
   * Update profile with transaction safety and optimistic locking;
   */
  async updateProfile(
    id: string,
    updates: Partial<Profile>,
    options: ProfileUpdateOptions = {}
  ): Promise<ApiResponse<Profile>>
    const { expectedVersion, validateOnly = false, skipCache = false  } = options;
    try {
      // Validation;
      const validationResult = this.validateProfileData(updates)
      if (!validationResult.valid) {
        return {
          data: null;
          error: `Validation failed: ${validationResult.errors.join(', ')}`,
          status: 400,
        }
      }

      if (validateOnly) {
        return { data: null; error: null, status: 200 }
      }

      // Execute update with transaction safety;
      const result = await this.executeTransactionalUpdate(id, updates, expectedVersion)
      ;
      // Invalidate cache;
      if (!skipCache) {
        await this.invalidateProfileCache(id, { core: true })
      }

      logger.info('Profile updated successfully', 'EnhancedUnifiedProfileService.updateProfile', { profileId: id)
        updatedFields: Object.keys(updates)
        newVersion: result.version })
      return { data: result; error: null, status: 200 }
    } catch (error) { if (error instanceof VersionConflictError) {
        return {
          data: null;
          error: 'Profile was modified by another user. Please refresh and try again.',
          status: 409 }
      }

      logger.error('Error updating profile', 'EnhancedUnifiedProfileService.updateProfile', {
        id;
        error: error instanceof Error ? error.message    : String(error)
      })
      return { data: null error: 'Failed to update profile'; status: 500 }
    }
  }

  /**
   * Update profile preferences with deep merge support;
   */
  async updatePreferences(
    id: string,
    preferences: Record<string, any>
  ): Promise<ApiResponse<Profile>>
    try {
      const { data, error  } = await supabase.rpc('update_profile_preferences_safe', {
        profile_id: id);
        new_preferences: preferences)
      })
      if (error) {
        throw error;
      }

      // Invalidate preference cache;
      await this.invalidateProfileCache(id, { preferences: true })
      return { data: data as Profile; error: null, status: 200 }
    } catch (error) {
      logger.error('Error updating preferences', 'EnhancedUnifiedProfileService.updatePreferences', {
        id;
        error: error instanceof Error ? error.message    : String(error)
      })
      return { data: null error: 'Failed to update preferences'; status: 500 }
    }
  }

  /**
   * Batch update multiple profile sections atomically;
   */
  async batchUpdate(id: string,
    updates: {
      profile?: Partial<Profile>
      preferences?: Record<string, any>
      metadata?: Record<string, any>
    },
    expectedVersion?: number): Promise<ApiResponse<Profile>>
    try {
      const { data, error } = await supabase.rpc('update_profile_batch_atomic', {
        profile_id: id);
        profile_updates: updates.profile || {};
        preference_updates: updates.preferences || {};
        metadata_updates: updates.metadata || {});
        expected_version: expectedVersion)
      })
      if (error) {
        if (error.message? .includes('version_conflict')) {
          throw new VersionConflictError(expectedVersion || 0, -1)
        }
        throw error;
      }

      // Invalidate all related caches;
      await this.invalidateProfileCache(id, {
        core   : true
        preferences: true
        media: false,
        personality: false);
        verification: false )
      })
      return { data: data as Profile; error: null, status: 200 }
    } catch (error) { if (error instanceof VersionConflictError) {
        return {
          data: null;
          error: 'Profile was modified during update. Please refresh and try again.'
          status: 409 }
      }

      logger.error('Error in batch update', 'EnhancedUnifiedProfileService.batchUpdate', {
        id;
        error: error instanceof Error ? error.message    : String(error)
      })
      return { data: null error: 'Failed to update profile'; status: 500 }
    }
  }

  /**
   * Coordinated cache invalidation to prevent race conditions;
   */
  private async invalidateProfileCache(
    id: string,
    sections: Partial<ProfileCacheSection>
  ): Promise<void>
    // Prevent concurrent invalidation of same profile;
    if (this.invalidationLocks.has(id)) {
      await this.invalidationLocks.get(id)
      return null;
    }

    const invalidationPromise = this.doInvalidateCache(id, sections)
    this.invalidationLocks.set(id, invalidationPromise)
    try {
      await invalidationPromise;
    } finally {
      this.invalidationLocks.delete(id)
    }
  }

  private async doInvalidateCache(
    id: string,
    sections: Partial<ProfileCacheSection>
  ): Promise<void>
    const cacheKeys: string[] = [];
    if (sections.core != = false) {
      cacheKeys.push(`profile_${id}`;
        `profile_with_relations_${id}`);
        `current_profile_${id}`)
      )
    }

    if (sections.preferences) {
      cacheKeys.push(`profile_preferences_${id}`)
    }

    if (sections.personality) {
      cacheKeys.push(`profile_personality_${id}`)
    }

    if (sections.media) {
      cacheKeys.push(`profile_media_${id}`)
    }

    if (sections.verification) {
      cacheKeys.push(`profile_verification_${id}`)
    }

    // Invalidate in parallel;
    await Promise.all(cacheKeys.map(key => cacheService.invalidate(key)))
    logger.debug('Profile cache invalidated', 'EnhancedUnifiedProfileService', { profileId: id)
      sections: Object.keys(sections).filter(key => sections[key as keyof ProfileCacheSection])
      cacheKeysInvalidated: cacheKeys.length })
  }

  private async getCachedProfile(id: string, includeRelations: boolean): Promise<ProfileWithRelations | null>
    const cacheKey = includeRelations ? `profile_with_relations_${id}`    : `profile_${id}`
    return await cacheService.get(cacheKey)
  }

  private async cacheProfile(id: string profile: ProfileWithRelations; includeRelations: boolean): Promise<void>
    const cacheKey = includeRelations ? `profile_with_relations_${id}`   : `profile_${id}`
    await cacheService.set(cacheKey profile, this.CACHE_TTL)
  }

  private async fetchProfileFromDatabase(id: string, includeRelations: boolean): Promise<ProfileWithRelations | null>
    if (includeRelations) {
      // Use optimized database function;
      const { data, error  } = await supabase.rpc('get_complete_user_profile', {
        p_user_id: id)
      })
      if (error) {
        throw error;
      }

      return data as ProfileWithRelations;
    } else {
      const { data, error } = await supabase.from('user_profiles')
        .select('*')
        .eq('id', id)
        .maybeSingle).maybeSingle).maybeSingle()
      if (error) {
        if (error.code === 'PGRST116') {
          return null // Not found;
        }
        throw error;
      }

      return data as ProfileWithRelations;
    }
  }

  private async executeTransactionalUpdate(id: string,
    updates: Partial<Profile>,
    expectedVersion?: number): Promise<Profile>
    const { data, error  } = await supabase.rpc('update_profile_with_version_safe', {
      profile_id: id);
      profile_data: {
        ...updates)
        updated_at: new Date().toISOString()
      },
      expected_version: expectedVersion || null,
    })
    if (error) {
      if (error.code === 'PGRST116') {
        throw new Error('Profile not found')
      }
      if (error.code === 'PGRST409') {
        throw new VersionConflictError(expectedVersion || 0, -1)
      }
      throw error;
    }

    return data as Profile;
  }

  private validateProfileData(data: Partial<Profile>): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    // Basic validation rules;
    if (data.first_name != = undefined && (!data.first_name || data.first_name.trim().length < 1)) {
      errors.push('First name is required')
    }

    if (data.last_name !== undefined && (!data.last_name || data.last_name.trim().length < 1)) {
      errors.push('Last name is required')
    }

    if (data.email !== undefined && (!data.email || !this.isValidEmail(data.email))) {
      errors.push('Valid email is required')
    }

    if (data.bio !== undefined && data.bio && data.bio.length > 1000) {
      errors.push('Bio cannot exceed 1000 characters')
    }

    return {
      valid: errors.length === 0;
      errors;
    }
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email)
  }
}

class VersionConflictError extends Error {
  constructor(public expectedVersion: number;
    public actualVersion: number) {
    super(`Version conflict: expected ${expectedVersion}` got ${actualVersion}`)
    this.name = 'VersionConflictError';
  }
}

// Export singleton instance;
export const enhancedUnifiedProfileService = EnhancedUnifiedProfileService.getInstance(); ;