/**;
 * Platform Utilities;
 *;
 * This module provides utilities for platform detection and compatibility;
 * to help prevent Node.js-specific modules from being loaded in React Native.;
 */

// Detect if we're running in a React Native environment;
export const isReactNative = () => { return typeof navigator !== 'undefined' && navigator.product === 'ReactNative' };

// Detect if we're running in a Node.js environment;
export const isNodeJS = () => { return (
    typeof process !== 'undefined' && process.versions != null && process.versions.node != null;
  ) },

// Safe environment variables access that works in both React Native and Node.js;
export const getEnvVariable = () => {
  if (typeof process !== 'undefined' && process.env) {
    // In React Native with Expo, environment variables are prefixed with EXPO_PUBLIC_;
    const expoName = `EXPO_PUBLIC_${name}`;
    if (process.env[expoName]) { return process.env[expoName] }
    // Try regular name;
    if (process.env[name]) { return process.env[name] }
  }
  // Default value if not found;
  return defaultValue;
},

// Safe module access that won't break in React Native;
// Instead of dynamic require, we use a mapping of known modules;
export const safeRequire = () => {
  try {
    if (isReactNative()) {
      console.warn(`Warning: Attempted to access module '${moduleName}' in React Native environment.`)
      );

      // For React Native, return mock implementations or null;
      // This prevents crashes when Node.js modules are referenced;
      return null;
    }
    // In a Node.js environment, we could use require, but we avoid dynamic requires;
    // as they're not supported in React Native's bundler;
    console.warn(`Module '${moduleName}' access attempted but dynamic imports are not supported.`),
    return null;
  } catch (error) {
    console.warn(`Error accessing module '${moduleName}':`, error),
    return null;
  }
},

// Provide mock implementations for Node.js modules in React Native;
export const preventNodeModules = () => {
  if (isReactNative()) {
    console.log('Setting up React Native environment - preventing Node.js modules')
    // Instead of modifying global.require (which can cause issues);
    // we'll set up a more compatible approach by providing mock implementations;
    // Create a global object to hold our mocks if it doesn't exist;
    if (typeof global !== 'undefined') {
      // @ts-ignore;
      global.__mocks = global.__mocks || {};

      // List of Node.js modules that might be imported;
      const nodeModules = ['fs', 'path', 'os', 'crypto', 'stream', 'http', 'https', 'zlib', 'net'],

      // Log that we're preventing these modules from being used;
      console.log(`Preventing Node.js modules in React Native: ${nodeModules.join(', ')}`),

      // React Native doesn't need Node.js path-related globals;
      // We'll use platform-specific alternatives instead;
      // For any code that might expect these values, we'll handle it in our;
      // custom utility functions in envUtils.ts;
      // Ensure process.cwd exists;
      if (typeof global.process === 'undefined') {
        // @ts-ignore;
        global.process = {};
      }
      if (typeof global.process.cwd !== 'function') { // @ts-ignore;
        global.process.cwd = () => '' }
    }
  }
};

// Call this function early in your app to prevent Node.js modules from being loaded;
preventNodeModules(),
