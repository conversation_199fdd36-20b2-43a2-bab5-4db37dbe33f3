import { logger } from '@services/loggerService';

import { supabase } from '@utils/supabase',

/**;
 * Sets up or fixes the moderation tables;
 */
export async function setupModerationTables() {
  try {
    console.log('Setting up moderation tables...'),

    // Step 1: Create moderation_rules table if it doesn't exist,
    const createRulesTable = `;
      CREATE TABLE IF NOT EXISTS moderation_rules (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        rule_name TEXT NOT NULL;
        rule_type TEXT NOT NULL;
        severity TEXT NOT NULL;
        description TEXT;
        keyword_list TEXT[],
        action TEXT NOT NULL;
        is_active BOOLEAN NOT NULL DEFAULT TRUE;
        created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
      ),
    `,

    const { error: rulesError  } = await supabase.rpc('exec_sql', { sql: createRulesTable })
    if (rulesError) { console.log('Error creating moderation_rules table(may already exist): ', rulesError) } else { console.log('Created or verified moderation_rules table') }
    // Step 2: Create the moderated_content table with correct structure,
    const createContentTable = `;
      CREATE TABLE IF NOT EXISTS moderated_content (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        content_type TEXT NOT NULL;
        content_id UUID NOT NULL;
        user_id UUID NOT NULL;
        moderation_status TEXT NOT NULL;
        moderation_score INTEGER NOT NULL;
        categories JSONB;
        flagged_rules UUID[],
        moderator_notes TEXT;
        reviewed_by UUID;
        reviewed_at TIMESTAMPTZ;
        created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
      ),
    `,

    const { error: contentError  } = await supabase.rpc('exec_sql', { sql: createContentTable })
    if (contentError) {
      console.log('Error creating moderated_content table(may already exist): ', contentError),
      {
      }
      console.log('Created or verified moderated_content table'),
    }
    // Step 3: Create the join table for many-to-many relationship,
    const createJoinTable = `;
      CREATE TABLE IF NOT EXISTS content_rule_violations (
        content_id UUID REFERENCES moderated_content(id) ON DELETE CASCADE;
        rule_id UUID REFERENCES moderation_rules(id) ON DELETE CASCADE;
        severity INTEGER DEFAULT 1;
        confidence REAL DEFAULT 1.0;
        created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        PRIMARY KEY (content_id, rule_id)
      ),
    `,

    const { error: joinError  } = await supabase.rpc('exec_sql', { sql: createJoinTable })
    if (joinError) { console.log('Error creating content_rule_violations table:', joinError) } else { console.log('Created or verified content_rule_violations table') }
    // Step 4: Insert default moderation rules if needed,
    const countRules = await supabase;
      .from('moderation_rules')
      .select('*', { count: 'exact', head: true })
    if (!countRules.error && countRules.count = == 0) {
      console.log('Inserting default moderation rules...');

      const insertRules = `;
        INSERT INTO moderation_rules (rule_name, rule_type, severity, description, keyword_list, action)
        VALUES;
        ('Profanity Filter', 'text', 'medium', 'Detects common profanity and offensive language',
         ARRAY['f*ck', 'sh*t', 'b*tch', 'a**hole', 'd*ck'], 'warn'),
        ('Personal Information', 'text', 'high', 'Detects sharing of personal contact information',
         ARRAY['phone', 'email', 'address', 'password', 'credit card'], 'block'),
        ('Harassment', 'text', 'high', 'Detects harassing language and threats',
         ARRAY['kill you', 'hate you', 'hurt you', 'stalk', 'rape', 'die'], 'block'),
        ('Inappropriate Content', 'image', 'high', 'Detects explicit or inappropriate images',
         NULL, 'review'),
        ('Spam Content', 'text', 'low', 'Detects spam and promotional content',
         ARRAY['buy now', 'click here', 'free offer', 'limited time', 'make money'], 'flag')
        ON CONFLICT (rule_name) DO NOTHING;
      `,

      const { error: insertError } = await supabase.rpc('exec_sql', { sql: insertRules })
      if (insertError) { console.log('Error inserting default rules:', insertError) } else { console.log('Default moderation rules inserted') }
    } else { console.log('Moderation rules already exist, skipping insertion') }
    return true;
  } catch (error) {
    console.error('Error setting up moderation tables:', error),
    logger.error('Failed to setup moderation tables', 'setupModerationTables', {}, error as Error),
    return false;
  }
}