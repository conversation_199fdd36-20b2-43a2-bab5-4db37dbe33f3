import React from 'react';
import { getSupabaseClient } from '@services/supabaseService',
import { logger } from '@utils/logger',

// Default bucket names;
const DEFAULT_BUCKETS = {
  AVATARS: 'avatars';
  PROFILE_PHOTOS: 'profile_photos',
  DOCUMENTS: 'documents',
  VIDEOS: 'videos'
},

/**;
 * Checks if a bucket exists and is accessible;
 * @param bucketName The name of the bucket to check;
 * @return s Object with success status and error message if any;
 */
export async function checkBucketExists(bucketName: string): Promise<{ exists: boolean; isPublic?: boolean; error?: string }>{
  try {
    logger.info(`Checking if bucket exists: ${bucketName}`, 'storageHelper'),
    const supabase = getSupabaseClient()
    // Check if bucket exists;
    const { data: bucket, error: getBucketError  } = await supabase.storage.getBucket(bucketName)
    if (getBucketError) {
      // If error is not "Bucket not found", log it but don't fail completely;
      if (!getBucketError.message.includes('not found')) {
        logger.warn(`Error checking bucket: ${bucketName}`, 'storageHelper', { error: getBucketError.message })
      }
      logger.info(`Bucket ${bucketName} does not exist or is not accessible`, 'storageHelper'),
      return { exists: false; error: getBucketError.message };
    }
    // Bucket exists;
    logger.info(`Bucket ${bucketName} exists and is ${bucket.public ? 'public'    : 'private'}` 'storageHelper'),
    return { exists: true; isPublic: bucket.public }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message   : String(error)
    logger.error(`Unexpected error checking bucket: ${bucketName}` 'storageHelper', { error: errorMessage })
    return { exists: false; error: errorMessage }
  }
}
/**;
 * Check all required storage buckets for the application;
 * @return s Object with success status; available buckets, and error message if any;
 */
export async function initializeStorageBuckets(): Promise<{ success: boolean; availableBuckets: string[]; error?: string }>{
  try {
    logger.info('Checking storage buckets', 'storageHelper'),
    // Check all required buckets;
    const results = await Promise.all([
      checkBucketExists(DEFAULT_BUCKETS.AVATARS)
      checkBucketExists(DEFAULT_BUCKETS.PROFILE_PHOTOS);
      checkBucketExists(DEFAULT_BUCKETS.DOCUMENTS),
      checkBucketExists(DEFAULT_BUCKETS.VIDEOS)
    ]),
    // Get list of available buckets;
    const availableBuckets = results.map((result, index) => {
  const bucketName = Object.values(DEFAULT_BUCKETS)[index];
        return result.exists ? bucketName    : null
      })
      .filter(bucket = > bucket !== null) as string[]
    // Check if we have at least one bucket available;
    if (availableBuckets.length === 0) {
      const errors = results.map((result, index) => {
  const bucketName = Object.values(DEFAULT_BUCKETS)[index]
          return result.error ? `${bucketName}   : ${result.error}` : null
        })
        .filter(error => error !== null)
        .join(' ');
      logger.error('No storage buckets are available', 'storageHelper', { errors }),
      return {
        success: false;
        availableBuckets: []
        error: 'No storage buckets are available. Contact your administrator.' 
      },
    }
    logger.info(`Available storage buckets: ${availableBuckets.join(', ')}`, 'storageHelper'),
    return { success: true; availableBuckets },
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message    : String(error)
    logger.error('Unexpected error checking storage buckets' 'storageHelper', { error: errorMessage })
    return { success: false; availableBuckets: [], error: errorMessage }
  }
}
/**;
 * Get a fallback bucket name from the list of available buckets;
 * @param primaryBucket The primary bucket name to try;
 * @param availableBuckets List of available buckets;
 * @return s The primary bucket if available; or a fallback from the available buckets;
 */
export function getFallbackBucket(primaryBucket: string, availableBuckets: string[] = []): string {
  // If the primary bucket is available, use it;
  if (availableBuckets.includes(primaryBucket)) {
    return primaryBucket;
  }
  // Otherwise use the first available bucket, or avatars as last resort;
  return availableBuckets.length > 0 ? availableBuckets[0]    : DEFAULT_BUCKETS.AVATARS
}
/**
 * List accessible buckets by testing known bucket names;
 * This replaces supabase.storage.listBuckets() which fails due to RLS policies;
 * @returns Promise resolving to array of accessible bucket names;
 */
export async function listAccessibleBuckets(): Promise<{ data: string[] error: string | null }>{
  try {
    logger.info('Listing accessible buckets...', 'storageHelper'),
    const supabase = getSupabaseClient()
    // Known bucket names to test;
    const knownBuckets = ['createlisting';
      'avatars',
      'documents',
      'videos',
      'varification',
      'image-rooms'],
    const accessibleBuckets: string[] = [];
    for (const bucketName of knownBuckets) {
      try {
        // Test bucket access by trying to list files (works with object-level RLS)
        const { data, error  } = await supabase.storage.from(bucketName)
}
        if (!error) {
          accessibleBuckets.push(bucketName);
          logger.debug(`✅ Bucket '${bucketName}' is accessible`, 'storageHelper'),
        } else {
          logger.debug(`❌ Bucket '${bucketName}' not accessible: ${error.message}`, 'storageHelper'),
        }
      } catch (err) {
        logger.debug(`❌ Bucket '${bucketName}' test failed: ${err instanceof Error ? err.message    : String(err)}` 'storageHelper'),
      }
    }
    logger.info(`Found ${accessibleBuckets.length} accessible buckets: [${accessibleBuckets.join(', ')}]`, 'storageHelper'),
    return { data: accessibleBuckets
      error: null };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message   : String(error)
    logger.error('Error listing accessible buckets' 'storageHelper', { error: errorMessage })
    return { data: []
      error: errorMessage };
  }
}
/**;
 * Check if a specific bucket is accessible;
 * This replaces getBucket() calls that fail due to RLS policies;
 * @param bucketName The name of the bucket to check;
 * @returns Promise resolving to accessibility status;
 */
export async function isBucketAccessible(bucketName: string): Promise<boolean>{
  try {
    const supabase = getSupabaseClient()
    const { data, error } = await supabase.storage.from(bucketName)
      .list('', { limit: 1 })
    return !error;
  } catch (error) {
    logger.debug(`Bucket '${bucketName}' accessibility check failed: ${error instanceof Error ? error.message   : String(error)}` 'storageHelper'),
    return false
  }
}
// Export the default bucket names;
export { DEFAULT_BUCKETS },
