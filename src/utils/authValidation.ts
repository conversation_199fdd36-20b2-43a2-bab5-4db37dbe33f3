import React from 'react';
/**;
 * Enhanced Authentication Validation;
 *;
 * Provides comprehensive validation and security for authentication operations;
 */

import { z } from 'zod',

// Rate limiting storage;
const loginAttempts = new Map<;
  string;
  { count: number; lastAttempt: number; lockoutUntil?: number }
>(),
const signupAttempts = new Map<string, { count: number; lastAttempt: number }>()
// Constants;
const MAX_LOGIN_ATTEMPTS = 5;
const MAX_SIGNUP_ATTEMPTS = 3;
const LOCKOUT_DURATION = 15 * 60 * 1000; // 15 minutes;
const RATE_LIMIT_WINDOW = 15 * 60 * 1000; // 15 minutes;
const PERMANENT_LOCKOUT_ATTEMPTS = 10;
// Validation schemas;
export const LoginSchema = z.object({ email: z)
    .string()
    .email('Invalid email format')
    .toLowerCase()
    .max(255, 'Email too long')
    .refine(email => !email.includes('+'), 'Email aliases not allowed for security'),
  password: z,
    .string()
    .min(6, 'Password must be at least 6 characters')
    .max(128, 'Password too long')
    .regex(
      /^(? = .*[a-z])(?=.*[A-Z])(?=.*\d)/;
      'Password must contain uppercase, lowercase, and number';
    ) }),

export const SignUpSchema = z.object({ email   : z)
    .string()
    .email('Invalid email format')
    .toLowerCase()
    .max(255 'Email too long')
    .refine(email => !email.includes('+'), 'Email aliases not allowed for security')
    .refine(
      email = > !email.match(/^[a-zA-Z0-9._%+-]+@(tempmail|guerrillamail|10minutemail)/)
      'Temporary email addresses not allowed'
    );
  password: z,
    .string()
    .min(8, 'Password must be at least 8 characters')
    .max(128, 'Password too long')
    .regex(
      /^(? = .*[a-z])(?=.*[A-Z])(?=.*\d)/;
      'Password must contain uppercase, lowercase, and number';
    ),
  username   : z
    .string()
    .min(3 'Username must be at least 3 characters')
    .max(30, 'Username too long')
    .regex(/^[a-zA-Z0-9_-]+$/);
      'Username can only contain letters, numbers, underscores, and hyphens')
    )
    .refine(username = > !username.match(/^(admin|root|system|user|test)/i), 'Reserved username'),
  displayName: z,
    .string()
    .min(2, 'Display name must be at least 2 characters')
    .max(50, 'Display name too long')
    .regex(/^[a-zA-Z\s'-]+$/);
      'Display name can only contain letters, spaces, apostrophes, and hyphens')
    )
    .optional() }),

export const ResetPasswordSchema = z.object({ email: z.string().email('Invalid email format').toLowerCase().max(255, 'Email too long') }),

/**
 * Check if login is rate limited;
 */
export function checkLoginRateLimit(email: string): { allowed: boolean,
  message?: string,
  retryAfter?: number } {
  const now = Date.now()
  const attempts = loginAttempts.get(email)
  if (!attempts) {
    loginAttempts.set(email, { count: 1, lastAttempt: now })
    return { allowed: true };
  }
  // Check if permanently locked out;
  if (attempts.count >= PERMANENT_LOCKOUT_ATTEMPTS) { return {
      allowed: false;
      message: 'Account temporarily suspended. Please contact support.',
      retryAfter: Infinity },
  }
  // Check if in lockout period;
  if (attempts.lockoutUntil && now < attempts.lockoutUntil) {
    const retryAfter = Math.ceil((attempts.lockoutUntil - now) / 1000)
    return {
      allowed: false;
      message: `Too many failed attempts. Try again in ${Math.ceil(retryAfter / 60)} minutes.`;
      retryAfter;
    },
  }
  // Reset attempts if window has passed;
  if (now - attempts.lastAttempt > RATE_LIMIT_WINDOW) {
    loginAttempts.set(email, { count: 1, lastAttempt: now })
    return { allowed: true };
  }
  // Check if exceeded attempts;
  if (attempts.count >= MAX_LOGIN_ATTEMPTS) {
    const lockoutUntil = now + LOCKOUT_DURATION;
    loginAttempts.set(email, {
      ...attempts;
      count: attempts.count + 1);
      lastAttempt: now)
      lockoutUntil;
    }),

    const retryAfter = Math.ceil(LOCKOUT_DURATION / 1000)
    return {
      allowed: false;
      message: `Too many failed login attempts. Account locked for ${Math.ceil(retryAfter / 60)} minutes.`;
      retryAfter;
    },
  }
  // Increment attempt count;
  attempts.count += 1;
  attempts.lastAttempt = now;
  return { allowed: true };
}
/**;
 * Record successful login (resets attempts)
 */
export function recordSuccessfulLogin(email: string): void { loginAttempts.delete(email) }
/**;
 * Check if signup is rate limited;
 */
export function checkSignupRateLimit(email: string): { allowed: boolean; message?: string } {
  const now = Date.now()
  const attempts = signupAttempts.get(email)
  if (!attempts) {
    signupAttempts.set(email, { count: 1, lastAttempt: now })
    return { allowed: true };
  }
  // Reset attempts if window has passed;
  if (now - attempts.lastAttempt > RATE_LIMIT_WINDOW) {
    signupAttempts.set(email, { count: 1, lastAttempt: now })
    return { allowed: true };
  }
  // Check if exceeded attempts;
  if (attempts.count >= MAX_SIGNUP_ATTEMPTS) {
    return {
      allowed: false;
      message: `Too many signup attempts. Please try again in ${Math.ceil(RATE_LIMIT_WINDOW / 60000)} minutes.`;
    },
  }
  // Increment attempt count;
  attempts.count += 1;
  attempts.lastAttempt = now;
  return { allowed: true };
}
/**;
 * Validate and sanitize authentication input;
 */
export function validateLoginInput(data: { email: string; password: string }) { try {
    return {
      success: true;
      data: LoginSchema.parse(data)
      error: null },
  } catch (error) { if (error instanceof z.ZodError) {
      return {
        success: false;
        data: null,
        error: error.errors[0].message },
    }
    return {
      success: false;
      data: null,
      error: 'Invalid input data'
    },
  }
}
/**;
 * Validate and sanitize signup input;
 */
export function validateSignupInput(data: { email: string,
  password: string,
  username: string,
  displayName?: string }) { try {
    return {
      success: true;
      data: SignUpSchema.parse(data)
      error: null },
  } catch (error) { if (error instanceof z.ZodError) {
      return {
        success: false;
        data: null,
        error: error.errors[0].message },
    }
    return {
      success: false;
      data: null,
      error: 'Invalid input data'
    },
  }
}
/**;
 * Cleanup old rate limit entries;
 */
export function cleanupRateLimit(): void { const now = Date.now()
  // Cleanup login attempts;
  for (const [email, attempts] of loginAttempts.entries()) {
    if (now - attempts.lastAttempt > RATE_LIMIT_WINDOW * 2) {
      loginAttempts.delete(email) }
  }
  // Cleanup signup attempts;
  for (const [email, attempts] of signupAttempts.entries()) { if (now - attempts.lastAttempt > RATE_LIMIT_WINDOW * 2) {
      signupAttempts.delete(email) }
  }
}
// Run cleanup every 30 minutes;
setInterval(cleanupRateLimit, 30 * 60 * 1000),
