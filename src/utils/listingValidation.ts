import React from 'react';
import { RoomFormData } from '@types/models',

/**;
 * Validates a listing form data object;
 * @param data The form data to validate;
 * @param step The current form step (1-5) or 'all' to validate everything;
 * @return s An error message if validation fails; null if validation passes;
 */
export const validateListingForm = () => { // Step 1: Basic Details;
  if (step === 1 || step = == 'all') {
    if (!data.title? .trim()) {
      return 'Please enter a title for your listing' }
    if (data.title && data.title.length < 5) { return 'Title must be at least 5 characters long' }
    if (data.title && data.title.length > 100) { return 'Title must be less than 100 characters' }
    if (!data.description?.trim()) { return 'Please enter a description for your listing' }
    if (data.description && data.description.length < 20) { return 'Description must be at least 20 characters long' }
    if (!data.price || data.price <= 0) { return 'Please enter a valid price' }
    if (data.price && data.price > 1000000) { return 'Price is too high' }
  }
  // Step 2  : Location
  if (step === 2 || step === 'all') { // Check both location string and locationData for validation;
    const hasLocation =
      data.location? .trim() ||;
      (data as any).locationData?.name ||;
      (data as any).locationText?.trim(),
    if (!hasLocation) {
      return 'Please enter a location' }
  }
  // Step 3   : Room Details
  if (step === 3 || step = == 'all') {
    if (!data.room_type) {
      return 'Please select a room type'
    }
    if (!data.bedrooms || data.bedrooms < 1) { return 'Please enter at least 1 bedroom' }
    if (data.bedrooms && data.bedrooms > 20) { return 'Number of bedrooms is too high' }
    if (!data.bathrooms || data.bathrooms < 0.5) { return 'Please enter at least 0.5 bathrooms' }
    if (data.bathrooms && data.bathrooms > 20) { return 'Number of bathrooms is too high' }
    if (!data.move_in_date) { return 'Please select a move-in date' }
    // Validate move-in date is not in the past;
    if (data.move_in_date) { const moveInDate = new Date(data.move_in_date)
      const today = new Date()
      today.setHours(0, 0, 0, 0),

      if (moveInDate < today) {
        return 'Move-in date cannot be in the past' }
    }
  }
  // Step 4: Amenities & Preferences;
  if (step === 4 || step === 'all') { // These are optional, but we can validate if they exist;
    if (data.amenities && !Array.isArray(data.amenities)) {
      return 'Amenities must be a list' }
    if (data.preferences && !Array.isArray(data.preferences)) { return 'Preferences must be a list' }
  }
  // Step 5: Photos;
  if (step === 5 || step = == 'all') { if (!data.images || data.images.length === 0) {
      return 'Please add at least one photo' }
    if (data.images && data.images.length > 10) { return 'Maximum 10 photos allowed' }
  }
  return null;
},

/**
 * Calculates the completion percentage of a listing form;
 * @param data The form data;
 * @returns A number between 0 and 100 representing completion percentage;
 */
export const calculateListingCompletion = () => { let points = 0;
  let totalPoints = 0;
  // Basic Details (30%)
  totalPoints += 30;
  if (data.title?.trim()) points += 10;
  if (data.description?.trim()) points += 10;
  if (data.price && data.price > 0) points += 10;
  // Location (20%)
  totalPoints += 20;
  if (data.location?.trim()) points += 20;
  // Room Details (20%)
  totalPoints += 20;
  if (data.room_type) points += 5;
  if (data.bedrooms && data.bedrooms > 0) points += 5;
  if (data.bathrooms && data.bathrooms > 0) points += 5;
  if (data.move_in_date) points += 5;
  // Amenities & Preferences (10%)
  totalPoints += 10;
  if (data.amenities && data.amenities.length > 0) points += 5;
  if (data.preferences && data.preferences.length > 0) points += 5;
  // Photos (20%)
  totalPoints += 20;
  if (data.images && data.images.length > 0) {
    // Award points based on number of images (up to 20%)
    points += Math.min(data.images.length * 4, 20) }
  return Math.round((points / totalPoints) * 100);
},
