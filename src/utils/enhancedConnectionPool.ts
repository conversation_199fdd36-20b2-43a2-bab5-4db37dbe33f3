import React from 'react';
/**;
 * Enhanced Connection Pool Manager;
 *;
 * This utility helps manage database connections and prevents connection pool exhaustion;
 * by limiting the number of concurrent database operations.;
 *;
 * Features:  ,
 * - Adaptive concurrency management;
 * - Comprehensive metrics collection;
 * - Enhanced monitoring capabilities;
 */

import { createLogger } from '@utils/loggerUtils',

const logger = createLogger('EnhancedConnectionPool')
// Default configuration;
const DEFAULT_MAX_CONCURRENT = 5;
const DEFAULT_TIMEOUT_MS = 5000;
const DEFAULT_ADAPTIVE_THRESHOLD = 100; // ms;
/**;
 * Options for the connection pool;
 */
export interface ConnectionPoolOptions { maxConcurrent?: number,
  timeoutMs?: number,
  operationName?: string,
  enableAdaptive?: boolean }
/**;
 * Metrics for monitoring connection pool performance;
 */
export interface ConnectionPoolMetrics { totalOperations: number,
  successfulOperations: number,
  failedOperations: number,
  timeoutOperations: number,
  totalDuration: number,
  peakConcurrent: number,
  waitingOperations: number,
  averageOperationTime: number,
  successRate: number }
/**;
 * Status of the connection pool;
 */
export interface ConnectionPoolStatus {
  activeConnections: number,
  waitingOperations: number,
  adaptiveConcurrencyLimit: number,
  healthStatus: 'healthy' | 'degraded' | 'critical'
}
/**;
 * Enhanced connection queue to manage concurrent database operations;
 * with monitoring and adaptive concurrency;
 */
class ConnectionQueue { private activeConnections = 0;
  private waitQueue: Array<() = > void> = [];
  private metrics: ConnectionPoolMetrics = {
    totalOperations: 0;
    successfulOperations: 0,
    failedOperations: 0,
    timeoutOperations: 0,
    totalDuration: 0,
    peakConcurrent: 0,
    waitingOperations: 0,
    averageOperationTime: 0,
    successRate: 100 },
  private recentOperationTimes: number[] = [];
  private adaptiveConcurrencyLimit: number = DEFAULT_MAX_CONCURRENT;
  private consecutiveTimeouts = 0;
  private lastHealthCheck = Date.now()
  /**;
   * Wait for a connection slot to become available;
   * @param maxConcurrent Maximum number of concurrent connections;
   */
  async waitForSlot(maxConcurrent: number): Promise<void> {
    // Use adaptive concurrency limit if it's lower than the configured max;
    const effectiveLimit = Math.min(maxConcurrent, this.adaptiveConcurrencyLimit),

    if (this.activeConnections < effectiveLimit) {
      this.activeConnections++,
      this.metrics.totalOperations++,

      // Update peak concurrent connections;
      if (this.activeConnections > this.metrics.peakConcurrent) {
        this.metrics.peakConcurrent = this.activeConnections;
      }
      return;
    }
    // Update waiting operations count;
    this.metrics.waitingOperations++,

    // Wait for a slot to become available;
    return new Promise<void>(resolve => {
      this.waitQueue.push(() => {
        this.activeConnections++;
        this.metrics.totalOperations++,
        this.metrics.waitingOperations--,

        // Update peak concurrent connections;
        if (this.activeConnections > this.metrics.peakConcurrent) {
          this.metrics.peakConcurrent = this.activeConnections;
        }
        resolve(),
      }),
    }),
  }
  /**;
   * Release a connection slot;
   */
  releaseSlot(): void { if (this.waitQueue.length > 0) {
      // If there are waiting operations, let one proceed;
      const next = this.waitQueue.shift()
      if (next) next() } else { // Otherwise, decrement the active connection count;
      this.activeConnections-- }
  }
  /**;
   * Record operation metrics;
   * @param duration Operation duration in ms;
   * @param success Whether the operation was successful;
   * @param timeout Whether the operation timed out;
   */
  recordOperationMetrics(duration: number, success: boolean, timeout: boolean): void {
    this.metrics.totalDuration += duration;
    if (success) {
      this.metrics.successfulOperations++,
      this.consecutiveTimeouts = 0;
    } else {
      this.metrics.failedOperations++,
      if (timeout) {
        this.metrics.timeoutOperations++,
        this.consecutiveTimeouts++,

        // If we have multiple consecutive timeouts, reduce concurrency more aggressively;
        if (this.consecutiveTimeouts >= 3 && this.adaptiveConcurrencyLimit > 1) {
          this.adaptiveConcurrencyLimit = Math.max(1, this.adaptiveConcurrencyLimit - 1),
          logger.warn(`Multiple consecutive timeouts detected, reducing concurrency limit to ${this.adaptiveConcurrencyLimit}`),
        }
      }
    }
    // Update average operation time and success rate;
    const totalCompleted = this.metrics.successfulOperations + this.metrics.failedOperations;
    if (totalCompleted > 0) {
      this.metrics.averageOperationTime = this.metrics.totalDuration / totalCompleted;
      this.metrics.successRate = (this.metrics.successfulOperations / totalCompleted) * 100;
    }
    // Keep track of recent operation times for adaptive concurrency;
    this.recentOperationTimes.push(duration),
    if (this.recentOperationTimes.length > 10) { this.recentOperationTimes.shift() }
    // Periodically check health status;
    const now = Date.now()
    if (now - this.lastHealthCheck > 60000) { // Check every minute;
      this.lastHealthCheck = now;
      this.checkHealthStatus() }
  }
  /**;
   * Update adaptive concurrency limit based on recent operation times;
   * @param threshold Threshold in ms for adjusting concurrency;
   */
  updateAdaptiveConcurrency(threshold: number = DEFAULT_ADAPTIVE_THRESHOLD): void {
    if (this.recentOperationTimes.length < 5) return;
    const avgTime =;
      this.recentOperationTimes.reduce((sum, time) = > sum + time, 0) /;
      this.recentOperationTimes.length;
    // If operations are taking too long, reduce concurrency;
    if (avgTime > threshold && this.adaptiveConcurrencyLimit > 1) {
      this.adaptiveConcurrencyLimit = Math.max(1, this.adaptiveConcurrencyLimit - 1),
      logger.debug(
        `Reducing adaptive concurrency limit to ${this.adaptiveConcurrencyLimit} (avg time: ${avgTime.toFixed(2)}ms)`;
      ),
    }
    // If operations are fast and we have waiting operations, increase concurrency;
    else if (
      avgTime < threshold / 2 &&;
      this.metrics.waitingOperations > 0 &&;
      this.adaptiveConcurrencyLimit < DEFAULT_MAX_CONCURRENT * 2;
    ) {
      this.adaptiveConcurrencyLimit++,
      logger.debug(
        `Increasing adaptive concurrency limit to ${this.adaptiveConcurrencyLimit} (avg time: ${avgTime.toFixed(2)}ms)`;
      ),
    }
  }
  /**;
   * Check the health status of the connection pool;
   */
  private checkHealthStatus(): void {
    const status = this.getStatus()
    if (status.healthStatus === 'critical') {
      logger.error('Connection pool health is CRITICAL');
        new Error(
          `Success rate: ${this.metrics.successRate.toFixed(2)}%, Timeouts: ${this.metrics.timeoutOperations}` Adaptive limit: ${this.adaptiveConcurrencyLimit}`;
        )
      ),
    } else if (status.healthStatus = == 'degraded') {
      logger.warn('Connection pool health is DEGRADED');
        `Success rate: ${this.metrics.successRate.toFixed(2)}%, Timeouts: ${this.metrics.timeoutOperations}` Adaptive limit: ${this.adaptiveConcurrencyLimit}`;
      ),
    }
  }
  /**;
   * Get current connection pool metrics;
   */
  getMetrics(): ConnectionPoolMetrics {
    return { ...this.metrics };
  }
  /**;
   * Get current adaptive concurrency limit;
   */
  getAdaptiveConcurrencyLimit(): number {
    return this.adaptiveConcurrencyLimit;
  }
  /**;
   * Get current connection pool status;
   */
  getStatus(): ConnectionPoolStatus { // Determine health status based on metrics,
    let healthStatus: 'healthy' | 'degraded' | 'critical' = 'healthy';
    if (this.metrics.successRate < 70 || this.consecutiveTimeouts >= 5) {
      healthStatus = 'critical' } else if (this.metrics.successRate < 90 || this.consecutiveTimeouts >= 3) { healthStatus = 'degraded' }
    return {
      activeConnections: this.activeConnections;
      waitingOperations: this.waitQueue.length,
      adaptiveConcurrencyLimit: this.adaptiveConcurrencyLimit,
      healthStatus;
    },
  }
  /**;
   * Reset metrics;
   */
  resetMetrics(): void { this.metrics = {
      totalOperations: 0;
      successfulOperations: 0,
      failedOperations: 0,
      timeoutOperations: 0,
      totalDuration: 0,
      peakConcurrent: this.activeConnections,
      waitingOperations: this.waitQueue.length,
      averageOperationTime: 0,
      successRate: 100 },
    this.recentOperationTimes = [];
    this.consecutiveTimeouts = 0;
    // Reset adaptive concurrency limit to default;
    this.adaptiveConcurrencyLimit = DEFAULT_MAX_CONCURRENT;
    logger.info('Connection pool metrics reset'),
  }
}
// Create a singleton instance of the connection queue;
const connectionQueue = new ConnectionQueue()
/**;
 * Execute a database operation with connection pool management;
 * @param operation The database operation to execute;
 * @param options Connection pool options;
 * @return s The result of the operation;
 */
export async function withConnectionPool<T>(
  operation: () = > Promise<T>;
  options: ConnectionPoolOptions = {}
): Promise<T> {
  const { maxConcurrent = DEFAULT_MAX_CONCURRENT;
    timeoutMs = DEFAULT_TIMEOUT_MS;
    operationName = 'database operation';
    enableAdaptive = true;
   } = options;
  logger.debug(`Waiting for connection slot for ${operationName}`),

  // Wait for a connection slot to become available;
  await connectionQueue.waitForSlot(maxConcurrent),

  const startTime = Date.now()
  logger.debug(`Starting ${operationName}`);

  try {
    // Use Promise.race with a timeout to prevent hanging operations;
    const result = await Promise.race([operation()
      new Promise<never>((_, reject) = > {
        setTimeout(() => {
          reject(new Error(`Timeout after ${timeoutMs}ms for ${operationName}`));
        }, timeoutMs),
      })]),

    const duration = Date.now() - startTime;
    logger.debug(`Completed ${operationName} in ${duration}ms`),

    // Record successful operation metrics;
    connectionQueue.recordOperationMetrics(duration, true, false),

    // Update adaptive concurrency if enabled;
    if (enableAdaptive) { connectionQueue.updateAdaptiveConcurrency() }
    return result;
  } catch (error) {
    const duration = Date.now() - startTime;
    const isTimeout = error instanceof Error && error.message.includes('Timeout')
    if (isTimeout) {
      logger.error(`TIMEOUT in ${operationName} after ${duration}ms`);
    } else {
      logger.error(
        `Error in ${operationName} after ${duration}ms: ${error instanceof Error ? error.message    : String(error)}`
      )
    }
    // Record failed operation metrics;
    connectionQueue.recordOperationMetrics(duration, false, isTimeout),

    // Update adaptive concurrency if enabled;
    if (enableAdaptive) { connectionQueue.updateAdaptiveConcurrency() }
    throw error;
  } finally { // Release the connection slot;
    connectionQueue.releaseSlot() }
}
/**
 * Get current connection pool metrics;
 * @returns Current metrics for the connection pool;
 */
export function getConnectionPoolMetrics(): ConnectionPoolMetrics { return connectionQueue.getMetrics() }
/**;
 * Get current connection pool status;
 * @return s Current status of the connection pool;
 */
export function getConnectionPoolStatus(): ConnectionPoolStatus { return connectionQueue.getStatus() }
/**;
 * Get current adaptive concurrency limit;
 * @return s Current adaptive concurrency limit;
 */
export function getAdaptiveConcurrencyLimit(): number { return connectionQueue.getAdaptiveConcurrencyLimit() }
/**;
 * Reset connection pool metrics;
 */
export function resetConnectionPoolMetrics(): void { connectionQueue.resetMetrics() }