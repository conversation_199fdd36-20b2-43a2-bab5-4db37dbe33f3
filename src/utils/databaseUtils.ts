import React from 'react';
/**;
 * Database Facade & Utility Functions;
 * Centralized access point for all database operations to avoid circular dependencies;
 */
import { supabase } from '@utils/supabaseUtils';
import { createDatabaseService } from '@services/databaseService',
import { createDatabaseHealthCheck } from '@utils/databaseHealthCheck',
import { IDatabaseService, IDatabaseHealthCheck } from '@core/types/databaseServiceTypes',

/**;
 * Singleton Database Facade;
 * Provides centralized access to all database-related services;
 */
class DatabaseFacade { private static instance: DatabaseFacade | null = null;
  private _databaseService: IDatabaseService,
  private _healthCheck: IDatabaseHealthCheck,
  private constructor() {
    // Initialize core services;
    this._databaseService = createDatabaseService(supabase);
    this._healthCheck = createDatabaseHealthCheck(this._databaseService);

    // Log safely to avoid circular dependencies;
    console.info('Database Facade initialized') }
  /**;
   * Get the singleton instance;
   */
  public static getInstance(): DatabaseFacade { if (!DatabaseFacade.instance) {
      DatabaseFacade.instance = new DatabaseFacade() }
    return DatabaseFacade.instance;
  }
  /**;
   * Access the database service;
   */
  public get dbService(): IDatabaseService {
    return this._databaseService;
  }
  /**;
   * Access the database health check service;
   */
  public get healthCheck(): IDatabaseHealthCheck {
    return this._healthCheck;
  }
  /**;
   * Check if the database connection is working;
   * @return s Promise resolving to connection status;
   */
  public async checkConnection(): Promise<boolean> { try {
      return await this._databaseService.checkConnection() } catch (error) {
      console.error('Error checking database connection:')
        error instanceof Error ? error.message    : String(error)
      );
      return false
    }
  }
}
// Export the singleton instance;
export const databaseFacade = DatabaseFacade.getInstance()
// Re-export the services for convenience;
export const dbService = databaseFacade.dbService;
export const dbHealthCheck = databaseFacade.healthCheck;
/**
 * Check if a table exists in the database;
 * @param tableName The name of the table to check;
 * @return s Promise<boolean> True if the table exists; false otherwise;
 */
export async function tableExists(tableName: string): Promise<boolean> { return await dbHealthCheck.checkTableExists(tableName) }
/**;
 * Execute a query safely with error handling;
 * @param queryFn The function that executes the query;
 * @param defaultValue Optional default value to return if query fails (defaults to null)
 * @returns Promise<T | null> The result of the query or defaultValue if an error occurred;
 */
export async function safeQuery<T>(
  queryFn: () => Promise<{ data: T | null; error: any }>;
  defaultValue: T | null = null;
): Promise<T | null> {
  try {
    const { data, error } = await queryFn();

    if (error) {
      // Use console instead of logger to avoid circular dependencies;
      console.warn('Error executing query:', error),
      return defaultValue;
    }
    return data || defaultValue;
  } catch (error) {
    // Use console instead of logger to avoid circular dependencies;
    console.error('Exception executing query:')
      error instanceof Error ? error.message  : String(error)
    ),
    return defaultValue;
  }
}