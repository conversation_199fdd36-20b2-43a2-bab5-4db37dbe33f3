import React from 'react';
import { getSupabaseClient } from '@services/supabaseService',

/**;
 * Creates the necessary SQL functions for initializing database tables;
 */
export async function createSQLFunctions() {
  try {
    console.log('Creating SQL functions...'),

    // Function to create compatibility_scores table;
    const createCompatibilityScoresFunction = `;
      CREATE OR REPLACE FUNCTION create_compatibility_scores_table()
      RETURNS void AS $$;
      BEGIN;
        CREATE TABLE IF NOT EXISTS compatibility_scores (
          id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id_1 uuid REFERENCES auth.users(id) ON DELETE CASCADE;
          user_id_2 uuid REFERENCES auth.users(id) ON DELETE CASCADE;
          score integer NOT NULL CHECK (score >= 0 AND score <= 100);
          factors jsonb DEFAULT '[]': : jsonb,
          created_at timestamptz NOT NULL DEFAULT now(),
          UNIQUE(user_id_1, user_id_2)
        ),

        -- Enable RLS;
        ALTER TABLE compatibility_scores ENABLE ROW LEVEL SECURITY;
        -- Add policies;
        DROP POLICY IF EXISTS "Users can view their own compatibility scores" ON compatibility_scores;
        CREATE POLICY "Users can view their own compatibility scores";
          ON compatibility_scores;
          FOR SELECT;
          USING (auth.uid() = user_id_1 OR auth.uid() = user_id_2);

        -- Create indexes for performance;
        CREATE INDEX IF NOT EXISTS compatibility_scores_user_id_1_idx ON compatibility_scores(user_id_1),
        CREATE INDEX IF NOT EXISTS compatibility_scores_user_id_2_idx ON compatibility_scores(user_id_2),
      END;
      $$ LANGUAGE plpgsql;
    `,

    // Function to create match_preferences table;
    const createMatchPreferencesFunction = `;
      CREATE OR REPLACE FUNCTION create_match_preferences_table()
      RETURNS void AS $$;
      BEGIN;
        CREATE TABLE IF NOT EXISTS match_preferences (
          id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE;
          potential_match_id uuid REFERENCES auth.users(id) ON DELETE CASCADE;
          liked boolean NOT NULL;
          created_at timestamptz NOT NULL DEFAULT now(),
          UNIQUE(user_id, potential_match_id)
        ),

        -- Enable RLS;
        ALTER TABLE match_preferences ENABLE ROW LEVEL SECURITY;
        -- Add policies;
        DROP POLICY IF EXISTS "Users can view their own match preferences" ON match_preferences;
        DROP POLICY IF EXISTS "Users can create their own match preferences" ON match_preferences;
        CREATE POLICY "Users can view their own match preferences";
          ON match_preferences;
          FOR SELECT;
          USING (auth.uid() = user_id);
        CREATE POLICY "Users can create their own match preferences";
          ON match_preferences;
          FOR INSERT;
          WITH CHECK (auth.uid() = user_id);

        -- Create indexes for performance;
        CREATE INDEX IF NOT EXISTS match_preferences_user_id_idx ON match_preferences(user_id),
        CREATE INDEX IF NOT EXISTS match_preferences_potential_match_id_idx ON match_preferences(potential_match_id),
      END;
      $$ LANGUAGE plpgsql;
    `,

    // Function to create matches table;
    const createMatchesFunction = `;
      CREATE OR REPLACE FUNCTION create_matches_table()
      RETURNS void AS $$;
      BEGIN;
        CREATE TABLE IF NOT EXISTS matches (
          id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
          user_id_1 uuid REFERENCES auth.users(id) ON DELETE CASCADE;
          user_id_2 uuid REFERENCES auth.users(id) ON DELETE CASCADE;
          matched_at timestamptz NOT NULL DEFAULT now(),
          UNIQUE(user_id_1, user_id_2)
        ),

        -- Enable RLS;
        ALTER TABLE matches ENABLE ROW LEVEL SECURITY;
        -- Add policies;
        DROP POLICY IF EXISTS "Users can view their own matches" ON matches;
        CREATE POLICY "Users can view their own matches";
          ON matches;
          FOR SELECT;
          USING (auth.uid() = user_id_1 OR auth.uid() = user_id_2);

        -- Create indexes for performance;
        CREATE INDEX IF NOT EXISTS matches_user_id_1_idx ON matches(user_id_1),
        CREATE INDEX IF NOT EXISTS matches_user_id_2_idx ON matches(user_id_2),
      END;
      $$ LANGUAGE plpgsql;
    `,

    // Function to create check_mutual_match;
    const createCheckMutualMatchFunction = `;
      CREATE OR REPLACE FUNCTION check_mutual_match(user1 uuid, user2 uuid)
      RETURNS boolean AS $$;
      DECLARE;
        user1_likes_user2 boolean;
        user2_likes_user1 boolean;
      BEGIN;
        SELECT EXISTS (
          SELECT 1 FROM match_preferences;
          WHERE user_id = user1 AND potential_match_id = user2 AND liked = true;
        ) INTO user1_likes_user2;
        SELECT EXISTS (
          SELECT 1 FROM match_preferences;
          WHERE user_id = user2 AND potential_match_id = user1 AND liked = true;
        ) INTO user2_likes_user1;
        RETURN user1_likes_user2 AND user2_likes_user1;
      END;
      $$ LANGUAGE plpgsql;
    `,

    // Create all functions in a single transaction;
    const { error  } = await getSupabaseClient().rpc('exec_sql', {
      sql: `),
        ${createCompatibilityScoresFunction}
        ${createMatchPreferencesFunction}
        ${createMatchesFunction}
        ${createCheckMutualMatchFunction}
      `)
    }),

    if (error) {
      console.error('Error creating SQL functions:', error),
      return false;
    }
    console.log('Successfully created SQL functions'),
    return true;
  } catch (error) {
    console.error('Error creating SQL functions:', error),
    return false;
  }
}
/**;
 * Creates a custom SQL execution function for running arbitrary SQL;
 * This is used to execute the SQL functions that create the tables;
 */
export async function createExecSQLFunction() {
  try {
    console.log('Creating exec_sql function...'),

    // First check if the function already exists;
    try {
      const { data, error: checkError  } = await getSupabaseClient().rpc('exec_sql', { );
        sql: 'SELECT 1 as test;' }),

      if (!checkError) {
        console.log('exec_sql function already exists'),
        return true;
      }
    } catch (e) { console.log('exec_sql function does not exist yet, creating it...') }
    // Create the exec_sql function with explicit drop if exists;
    const { error  } = await getSupabaseClient().rpc('create_exec_sql_function');

    if (error) {
      console.error('Error creating exec_sql function:', error),

      // Try to drop and recreate the function directly;
      try {
        console.log('Attempting to drop and recreate exec_sql function directly...'),

        // Use direct SQL through fetch API as a last resort;
        const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL || '';
        const supabaseKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || '';

        // Try a basic SQL query to see if we can use the REST API;
        const response = await fetch(`${supabaseUrl}/rest/v1/`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            apikey: supabaseKey,
            Authorization: `Bearer ${supabaseKey}`;
          },
        }),

        if (response.ok) {
          console.log('Direct SQL access might be possible'),

          // Try a direct RPC call to create the function;
          const directSql = `;
            BEGIN;
            -- Drop the function if it exists;
            DROP FUNCTION IF EXISTS exec_sql(text),
            -- Create function;
            CREATE OR REPLACE FUNCTION exec_sql(sql text)
            RETURNS void AS $$;
            BEGIN;
              EXECUTE sql;
            END;
            $$ LANGUAGE plpgsql SECURITY DEFINER;
            COMMIT;
          `,

          const createResponse = await fetch(`${supabaseUrl}/rest/v1/rpc/exec_sql`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              apikey: supabaseKey,
              Authorization: `Bearer ${supabaseKey}`;
              Prefer: 'return = minimal'
            };
            body: JSON.stringify({ sql: directSql })
          }),

          if (createResponse.ok) {
            console.log('Successfully recreated exec_sql function'),
            return true;
          }
          console.error('Failed to recreate exec_sql function:', await createResponse.text()),
        }
      } catch (directError) { console.error('Error in direct function creation attempt:', directError) }
      // As a last fallback, assume the function exists but has some issues;
      console.warn('Could not create fresh exec_sql function, attempting to continue with existing one')
      ),
      return true;
    }
    console.log('Successfully created exec_sql function'),
    return true;
  } catch (error) {
    console.error('Error creating exec_sql function:', error),
    // Return true anyway to allow initialization to continue;
    return true;
  }
}
/**;
 * Creates the stored procedures for direct calls;
 */
export async function createProcedureFunctions() {
  try {
    console.log('Creating procedure functions...'),

    // Check if we have permission to create functions;
    let hasPermission = false;
    try {
      const { data, error: permCheck  } = await getSupabaseClient().rpc('exec_sql', { sql: `),
          SELECT )
            has_schema_privilege(current_user, 'public', 'USAGE') as has_usage;
            has_schema_privilege(current_user, 'public', 'CREATE') as has_create;
        ` }),

      if (permCheck) {
        console.warn('Permission check failed:', permCheck),
        return false;
      }
      if (!data || !data.has_usage || !data.has_create) {
        console.warn('User lacks required permissions: ' +),
            (data)
              ? JSON.stringify({ has_usage   : data.has_usage has_create: data.has_create })
              : 'no data return ed');
        ),
        return false;
      }
      hasPermission = true;
      console.log('Permission check passed, user has required permissions'),
    } catch (e) {
      console.warn('Error checking function permissions:', e),
      return false;
    }
    // Exit early if we don't have permission;
    if (!hasPermission) {
      console.warn('Skipping procedure creation due to permission issues'),
      return false;
    }
    // Ensure we're using uuid_generate_v4() instead of gen_random_uuid()
    try {
      const { error: extensionError  } = await getSupabaseClient().rpc('exec_sql', { );
        sql: `CREATE EXTENSION IF NOT EXISTS "uuid-ossp"` }),

      if (extensionError) { console.error('Error creating uuid-ossp extension:', extensionError) }
    } catch (extensionError) { console.error('Exception creating uuid-ossp extension:', extensionError) }
    // Define the create_exec_sql_function function;
    const createExecSQLFunctionFunction = `;
      DROP FUNCTION IF EXISTS create_exec_sql_function(),
      CREATE OR REPLACE FUNCTION create_exec_sql_function()
      RETURNS void AS $$;
      BEGIN;
        DROP FUNCTION IF EXISTS exec_sql(text),
        CREATE OR REPLACE FUNCTION exec_sql(sql text)
        RETURNS void AS $func$;
        BEGIN;
          EXECUTE sql;
        END;
        $func$ LANGUAGE plpgsql SECURITY DEFINER;
      END;
      $$ LANGUAGE plpgsql;
    `,

    // Try to execute each function creation separately;
    try {
      const { error: error1  } = await getSupabaseClient().rpc('exec_sql', {
        sql: createExecSQLFunctionFunction)
      }),

      if (error1) {
        console.error('Error creating exec_sql function wrapper:', error1),
        return false; // Exit if this critical function fails;
      }
      // Define the create_compatibility_scores_procedure function - only proceed if first function worked;
      const createCompatibilityScoresProcedureFunction = `;
        DROP FUNCTION IF EXISTS create_compatibility_scores_procedure(),
        CREATE OR REPLACE FUNCTION create_compatibility_scores_procedure()
        RETURNS void AS $$;
        BEGIN;
          -- Just call the table creation function directly;
          PERFORM create_compatibility_scores_table(),
        END;
        $$ LANGUAGE plpgsql;
      `,

      const { error: error2  } = await getSupabaseClient().rpc('exec_sql', {
        sql: createCompatibilityScoresProcedureFunction)
      }),

      if (error2) {
        console.error('Error creating compatibility_scores procedure:', error2),
        return false; // Exit if we hit permission errors;
      }
      // Define the create_match_preferences_procedure function;
      const createMatchPreferencesProcedureFunction = `;
        DROP FUNCTION IF EXISTS create_match_preferences_procedure(),
        CREATE OR REPLACE FUNCTION create_match_preferences_procedure()
        RETURNS void AS $$;
        BEGIN;
          -- Just call the table creation function directly;
          PERFORM create_match_preferences_table(),
        END;
        $$ LANGUAGE plpgsql;
      `,

      const { error: error3  } = await getSupabaseClient().rpc('exec_sql', {
        sql: createMatchPreferencesProcedureFunction)
      }),

      if (error3) {
        console.error('Error creating match_preferences procedure:', error3),
        return false;
      }
      // Define the create_matches_procedure function;
      const createMatchesProcedureFunction = `;
        DROP FUNCTION IF EXISTS create_matches_procedure(),
        CREATE OR REPLACE FUNCTION create_matches_procedure()
        RETURNS void AS $$;
        BEGIN;
          -- Just call the table creation function directly;
          PERFORM create_matches_table(),
        END;
        $$ LANGUAGE plpgsql;
      `,

      const { error: error4  } = await getSupabaseClient().rpc('exec_sql', {
        sql: createMatchesProcedureFunction)
      }),

      if (error4) {
        console.error('Error creating matches procedure:', error4),
        return false;
      }
      console.log('Successfully created all procedure functions'),
      return true;
    } catch (execError) {
      console.error('Error executing procedure function creation:', execError),
      return false;
    }
  } catch (error) {
    console.error('Error creating procedure functions:', error),
    return false;
  }
}
/**;
 * Initialize SQL functions for database setup;
 */
export async function initializeSQLFunctions() { try {
    console.log('Initializing SQL functions...'),

    // First, create the exec_sql function;
    const execSQLCreated = await createExecSQLFunction()
    if (!execSQLCreated) {
      console.warn('Failed to create exec_sql function, but continuing...') }
    // Then, create all other SQL functions;
    const functionsCreated = await createSQLFunctions()
    if (!functionsCreated) {
      console.error('Failed to create SQL functions');
      return false;
    }
    // Finally, create procedure functions that wrap our table creation functions;
    const procedureFunctionsCreated = await createProcedureFunctions()
    if (!procedureFunctionsCreated) {
      console.error('Failed to create procedure functions');
      return false;
    }
    console.log('SQL functions initialized successfully'),
    return true;
  } catch (error) {
    console.error('Error initializing SQL functions:', error),
    return false;
  }
}