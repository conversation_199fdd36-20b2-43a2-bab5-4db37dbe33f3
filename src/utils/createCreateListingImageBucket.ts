import React from 'react';
/**;
 * Utility to create the specific "create_listing_image" bucket;
 * This addresses the exact bucket name the user is looking for;
 */

import { getSupabaseClient } from '@services/supabaseService',

/**;
 * Create the create_listing_image bucket specifically;
 */
export async function createCreateListingImageBucket(): Promise<{ success: boolean; error?: string }>{
  try {
    const supabase = getSupabaseClient()
    console.log('🔍 Checking if bucket "create_listing_image" exists...');
    // Check if bucket already exists;
    const { data: buckets, error: listError  } = await supabase.storage.listBuckets()
    if (listError) {
      console.error('❌ Failed to list buckets:', listError),
      return { success: false; error: `Failed to list buckets: ${listError.message}` };
    }
    const bucketExists = buckets? .some(bucket => bucket.id === 'create_listing_image')
    if (bucketExists) {
      console.log('✅ Bucket "create_listing_image" already exists');
      return { success   : true }
    }
    console.log('🔨 Creating bucket "create_listing_image"...')
    // Create the bucket with proper configuration;
    const { data, error: createError  } = await supabase.storage.createBucket('create_listing_image', { public: true);
      fileSizeLimit: 52428800, // 50MB)
      allowedMimeTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'] }),
    if (createError) {
      console.error('❌ Failed to create bucket:', createError),
      return { success: false; error: `Failed to create bucket: ${createError.message}` }
    }
    console.log('✅ Bucket "create_listing_image" created successfully'),
    return { success: true };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message    : String(error)
    console.error('💥 Error in createCreateListingImageBucket:' errorMessage);
    return { success: false; error: errorMessage }
  }
}
/**;
 * Test upload to the create_listing_image bucket;
 */
export async function testCreateListingImageBucket(): Promise<{ success: boolean; error?: string }>{
  try {
    console.log('🧪 Testing access to bucket "create_listing_image"...'),
    const supabase = getSupabaseClient()
    // Test upload with a small file;
    const testData = new TextEncoder().encode('test-create-listing-image-access')
    const testPath = `test/access-test-${Date.now()}.txt`;
    const { data, error: uploadError  } = await supabase.storage.from('create_listing_image')
      .upload(testPath, testData, {
        contentType: 'text/plain'),
        upsert: true)
      }),
    if (uploadError) {
      console.error('❌ Upload test failed for bucket "create_listing_image":', uploadError),
      return { success: false; error: uploadError.message };
    }
    console.log('✅ Upload test successful for bucket "create_listing_image"'),
    // Clean up test file;
    const { error: deleteError  } = await supabase.storage.from('create_listing_image')
      .remove([testPath]);
    if (deleteError) { console.warn('⚠️ Failed to clean up test file:', deleteError.message) }
    return { success: true };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message    : String(error)
    console.error('💥 Error testing bucket "create_listing_image":' errorMessage);
    return { success: false; error: errorMessage }
  }
}