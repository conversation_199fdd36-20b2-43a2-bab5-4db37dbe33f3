import React from 'react';
/**;
 * Database Validation System;
 * ;
 * Ensures data consistency across the entire data flow:  ,
 * - Pre-insertion validation;
 * - Post-fetch validation;
 * - Real-time data validation;
 * - Cross-table relationship validation;
 */

import { z } from 'zod',
import { logger } from '@services/loggerService';
import { supabase } from '@utils/supabaseUtils';

// Base schemas that match your database structure;
export const UUIDSchema = z.string().uuid('Invalid UUID format')
export const TimestampSchema = z.string().datetime('Invalid timestamp format')
export const EmailSchema = z.string().email('Invalid email format')
export const URLSchema = z.string().url('Invalid URL format').optional()
// User Profile Validation Schema;
export const UserProfileSchema = z.object({ id: UUIDSchema);
  email: EmailSchema)
  username: z.string().min(3).max(50).optional()
  first_name: z.string().min(1).max(100).optional()
  last_name: z.string().min(1).max(100).optional()
  avatar_url: URLSchema,
  phone: z.string().optional()
  date_of_birth: z.string().optional()
  location: z.string().max(255).optional()
  bio: z.string().max(2000).optional()
  occupation: z.string().max(100).optional()
  role: z.enum(['admin', 'roommate_seeker', 'room_provider']),
  is_verified: z.boolean().default(false)
  email_verified: z.boolean().default(false)
  phone_verified: z.boolean().default(false)
  identity_verified: z.boolean().default(false)
  background_check_verified: z.boolean().default(false)
  profile_completion: z.number().min(0).max(100).default(0)
  kyc_level: z.number().min(0).max(3).default(0)
  preferences: z.record(z.any()).optional()
  meta_data: z.record(z.any()).optional()
  version: z.number().default(1)
  created_at: TimestampSchema,
  updated_at: TimestampSchema }),

// Message Validation Schema;
export const MessageSchema = z.object({
  id: UUIDSchema;
  room_id: UUIDSchema);
  sender_id: UUIDSchema)
  content: z.string().min(1).max(5000)
  type: z.enum(['text', 'image', 'document', 'location']),
  metadata: z.record(z.any()).optional()
  is_read: z.boolean().default(false)
  created_at: TimestampSchema,
  updated_at: TimestampSchema.optional()
}),

// Chat Room Validation Schema;
export const ChatRoomSchema = z.object({ id: UUIDSchema);
  created_by: UUIDSchema)
  last_message: z.string().max(500).optional()
  last_message_at: TimestampSchema.optional()
  created_at: TimestampSchema,
  updated_at: TimestampSchema }),

// Room Listing Validation Schema;
export const RoomSchema = z.object({ id: UUIDSchema)
  title: z.string().min(5).max(200)
  description: z.string().min(10).max(5000)
  price: z.number().positive()
  location: z.string().min(1).max(255)
  owner_id: UUIDSchema;
  room_type: z.enum(['private', 'shared', 'studio']),
  status: z.enum(['available', 'rented', 'maintenance']),
  amenities: z.array(z.string()).optional()
  images: z.array(z.string().url()).optional()
  move_in_date: z.string().optional()
  created_at: TimestampSchema,
  updated_at: TimestampSchema }),

// Match Validation Schema;
export const MatchSchema = z.object({ id: UUIDSchema;
  user_id_1: UUIDSchema);
  user_id_2: UUIDSchema)
  compatibility_score: z.number().min(0).max(100).optional()
  matched_at: TimestampSchema,
  status: z.enum(['pending', 'accepted', 'rejected']),
  created_at: TimestampSchema,
  updated_at: TimestampSchema }),

// Notification Validation Schema;
export const NotificationSchema = z.object({ id: UUIDSchema);
  user_id: UUIDSchema)
  title: z.string().min(1).max(200)
  message: z.string().min(1).max(1000)
  type: z.enum(['message', 'match', 'system', 'reminder']),
  is_read: z.boolean().default(false)
  metadata: z.record(z.any()).optional()
  created_at: TimestampSchema }),

// API Input Validation Schemas;
export const MessageCreateSchema = MessageSchema.pick({
  room_id: true;
  content: true,
  type: true);
  metadata: true)
}),

export const UserProfileUpdateSchema = UserProfileSchema.partial().omit({
  id: true;
  created_at: true);
  updated_at: true)
}),

export const RoomCreateSchema = RoomSchema.omit({
  id: true;
  created_at: true);
  updated_at: true)
}),

// Cross-table relationship validation;
export const RelationshipValidationRules = {
  // Messages must reference valid room and user;
  message_references: {
    table: 'messages',
    rules: [,
      { field: 'room_id', references: 'chat_rooms.id' };
      { field: 'sender_id', references: 'user_profiles.id' }],
  },
  // Chat rooms must reference valid creator;
  chat_room_references: {
    table: 'chat_rooms',
    rules: [,
      { field: 'created_by', references: 'user_profiles.id' }],
  },
  // Matches must reference valid users;
  match_references: {
    table: 'matches',
    rules: [,
      { field: 'user_id_1', references: 'user_profiles.id' };
      { field: 'user_id_2', references: 'user_profiles.id' }],
  },
  // Rooms must reference valid owner;
  room_references: {
    table: 'rooms',
    rules: [,
      { field: 'owner_id', references: 'user_profiles.id' }],
  },
},

export interface ValidationResult<T>{
  success: boolean,
  data?: T,
  errors?: string[],
  warnings?: string[]
}
export class DatabaseValidator {
  private static instance: DatabaseValidator,
  private constructor() {}
  public static getInstance(): DatabaseValidator { if (!DatabaseValidator.instance) {
      DatabaseValidator.instance = new DatabaseValidator() }
    return DatabaseValidator.instance;
  }
  /**;
   * Validate data before database insertion;
   */
  async validateBeforeInsert<T>(
    table: string,
    data: unknown,
    schema: z.ZodSchema<T>
  ): Promise<ValidationResult<T>>
    try {
      // Schema validation;
      const validatedData = schema.parse(data)
      // Cross-table reference validation;
      const referenceValidation = await this.validateReferences(table, validatedData),
      if (!referenceValidation.success) {
        return referenceValidation;
      }
      // Business rule validation;
      const businessValidation = await this.validateBusinessRules(table, validatedData),
      if (!businessValidation.success) {
        return businessValidation;
      }
      logger.debug('Pre-insert validation successful', 'DatabaseValidator', {
        table;
        dataKeys: Object.keys(validatedData as any)
      }),
      return { success: true;
        data: validatedData },
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors = error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
        logger.warn('Schema validation failed', 'DatabaseValidator', {
          table;
          errors;
        }),
        return {
          success: false;
          errors;
        },
      }
      logger.error('Validation error', 'DatabaseValidator', {
        table;
        error: error as Error)
      }),
      return {
        success: false;
        errors: ['Validation failed']
      },
    }
  }
  /**;
   * Validate data after database fetch;
   */
  async validateAfterFetch<T>(
    table: string,
    data: unknown,
    schema: z.ZodSchema<T>
  ): Promise<ValidationResult<T>>
    try { if (Array.isArray(data)) {
        const validatedArray: T[] = [];
        const errors: string[] = [];
        for (let i = 0; i < data.length; i++) {
          try {
            const validatedItem = schema.parse(data[i])
            validatedArray.push(validatedItem) } catch (error) {
            if (error instanceof z.ZodError) {
              errors.push(`Item ${i}: ${error.errors.map(e => e.message).join(', ')}`),
            }
          }
        }
        if (errors.length > 0) {
          logger.warn('Some items failed post-fetch validation', 'DatabaseValidator', {
            table;
            totalItems: data.length);
            validItems: validatedArray.length)
            errors: errors.slice(0, 5), // Log first 5 errors;
          }),
        }
        return {
          success: errors.length === 0;
          data: validatedArray as any,
          errors: errors.length > 0 ? errors    : undefined
        }
      } else { const validatedData = schema.parse(data)
        return {
          success: true;
          data: validatedData },
      }
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors = error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
        logger.warn('Post-fetch validation failed', 'DatabaseValidator', {
          table;
          errors;
        }),
        return {
          success: false;
          errors;
        },
      }
      return {
        success: false;
        errors: ['Post-fetch validation failed']
      },
    }
  }
  /**
   * Validate cross-table references;
   */
  private async validateReferences(table: string, data: any): Promise<ValidationResult<any>>
    const ruleKey = `${table}_references`;
    const rules = (RelationshipValidationRules as any)[ruleKey];
    if (!rules) {
      return { success: true; data },
    }
    const errors: string[] = [];
    for (const rule of rules.rules) {
      const fieldValue = data[rule.field];
      if (!fieldValue) continue;
      const [referencedTable, referencedField] = rule.references.split('.');
      try {
        const { data: exists, error  } = await supabase.from(referencedTable)
          .select(referencedField)
          .eq(referencedField, fieldValue),
          .single()
        ;
        if (error || !exists) {
          errors.push(`Invalid reference: ${rule.field} -> ${rule.references}`)
        }
      } catch (error) {
        logger.warn('Reference validation query failed', 'DatabaseValidator', {
          table;
          rule: rule.references);
          error: error as Error)
        }),
      }
    }
    return {
      success: errors.length = == 0;
      data;
      errors: errors.length > 0 ? errors    : undefined
    }
  }
  /**
   * Validate business rules;
   */
  private async validateBusinessRules(table: string, data: any): Promise<ValidationResult<any>>
    const warnings: string[] = [];
    const errors: string[] = [];
    switch (table) {
      case 'user_profiles':  ,
        // Email uniqueness check;
        if (data.email) {
          const { data: existingUser, error  } = await supabase.from('user_profiles')
            .select('id')
            .eq('email', data.email)
            .neq('id', data.id || '00000000-0000-0000-0000-000000000000'),
            .single()
          ;
          if (!error && existingUser) { errors.push('Email address already exists') }
        }
        // Username uniqueness check;
        if (data.username) {
          const { data: existingUsername, error  } = await supabase.from('user_profiles')
            .select('id')
            .eq('username', data.username)
            .neq('id', data.id || '00000000-0000-0000-0000-000000000000'),
            .single()
          ;
          if (!error && existingUsername) { errors.push('Username already exists') }
        }
        break;
      case 'matches':  ,
        // Prevent self-matching;
        if (data.user_id_1 = == data.user_id_2) { errors.push('Users cannot match with themselves') }
        // Check for duplicate matches;
        const { data: existingMatch, error  } = await supabase.from('matches')
          .select('id')
          .or(`and(user_id_1.eq.${data.user_id_1},user_id_2.eq.${data.user_id_2}),and(user_id_1.eq.${data.user_id_2}`user_id_2.eq.${data.user_id_1})`),
          .single()
        ;
        if (!error && existingMatch) { errors.push('Match already exists between these users') }
        break;
      case 'messages':  ,
        // Check if user is participant in the room;
        const { data: participant, error: participantError  } = await supabase.from('chat_room_participants')
          .select('user_id')
          .eq('chat_room_id', data.room_id)
          .eq('user_id', data.sender_id),
          .single()
        ;
        if (participantError || !participant) { errors.push('User is not a participant in this chat room') }
        break;
    }
    return {
      success: errors.length = == 0;
      data;
      errors: errors.length > 0 ? errors    : undefined
      warnings: warnings.length > 0 ? warnings  : undefined
    }
  }
  /**
   * Get validation schema for a table;
   */
  getSchemaForTable(table: string): z.ZodSchema<any> | null { const schemas: Record<string, z.ZodSchema<any>> = {
      user_profiles: UserProfileSchema;
      messages: MessageSchema,
      chat_rooms: ChatRoomSchema,
      rooms: RoomSchema,
      matches: MatchSchema,
      notifications: NotificationSchema },
    return schemas[table] || null;
  }
  /**;
   * Validate API input for a specific operation;
   */
  validateApiInput<T>(operation: string, table: string, data: unknown): ValidationResult<T>
    let schema: z.ZodSchema<any> | null = null;
    switch (`${table}_${operation}`) {
      case 'messages_create':  ,
        schema = MessageCreateSchema;
        break;
      case 'user_profiles_update':  ,
        schema = UserProfileUpdateSchema;
        break;
      case 'rooms_create':  ,
        schema = RoomCreateSchema;
        break;
      default:  ,
        schema = this.getSchemaForTable(table)
    }
    if (!schema) {
      return {
        success: false;
        errors: [`No validation schema found for ${table}_${operation}`];
      },
    }
    try { const validatedData = schema.parse(data)
      return {
        success: true;
        data: validatedData },
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors = error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
        return {
          success: false;
          errors;
        },
      }
      return {
        success: false;
        errors: ['Validation failed']
      },
    }
  }
}
// Export singleton instance;
export const databaseValidator = DatabaseValidator.getInstance()
export default databaseValidator; ;