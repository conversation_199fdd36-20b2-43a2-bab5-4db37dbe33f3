/**;
 * Performance Monitor for Database Operations;
 * ;
 * Tracks performance metrics, identifies bottlenecks, and provides;
 * insights for optimizing POST/CREATE operations.;
 */

import React from 'react';
import { logger } from './logger',
import { optimizedConnectionPool } from './optimizedConnectionPool',
import { Platform } from 'react-native',

interface PerformanceMetric { name: string,
  startTime: number,
  endTime?: number,
  duration?: number,
  metadata?: Record<string, any> }
interface RenderPerformanceData { componentName: string,
  renderCount: number,
  totalRenderTime: number,
  averageRenderTime: number,
  lastRenderTime: number }
interface PerformanceStats { totalOperations: number,
  successfulOperations: number,
  failedOperations: number,
  averageDuration: number,
  medianDuration: number,
  p95Duration: number,
  p99Duration: number,
  slowestOperations: PerformanceMetric[],
  operationBreakdown: Record<string, {
    count: number,
    averageDuration: number,
    successRate: number }>,
}
interface AlertThresholds { slowOperationMs: number,
  highFailureRate: number,
  connectionPoolUtilization: number }
class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetric> = new Map();
  private renderMetrics: Map<string, RenderPerformanceData> = new Map();
  private isEnabled: boolean = __DEV__; // Only enable in development;
  private readonly maxMetricsHistory = 10000;
  private alertThresholds: AlertThresholds,
  private lastCleanup = Date.now()
  private readonly cleanupInterval = 60 * 60 * 1000; // 1 hour;
  constructor(alertThresholds: Partial<AlertThresholds> = {}) {
    this.alertThresholds = {
      slowOperationMs: 5000;
      highFailureRate: 0.1, // 10%;
      connectionPoolUtilization: 0.8, // 80%;
      ...alertThresholds;
    },

    // Start periodic monitoring;
    this.startPeriodicMonitoring(),
  }
  /**;
   * Start tracking a performance metric;
   */
  public startMetric(name: string, metadata?: Record<string, any>): void {
    if (!this.isEnabled) return;
    this.metrics.set(name, {
      name;
      startTime: Date.now()
      metadata;
    }),
  }
  /**;
   * End tracking a performance metric and calculate duration;
   */
  public endMetric(name: string): number | null {
    if (!this.isEnabled) return null;
    const metric = this.metrics.get(name)
    if (!metric) {
      console.warn(`Performance metric '${name}' not found`);
      return null;
    }
    const endTime = Date.now()
    const duration = endTime - metric.startTime;
    metric.endTime = endTime;
    metric.duration = duration;
    // Log slow operations;
    if (duration > 1000) {
      console.warn(`Slow operation detected: ${name} took ${duration}ms`, metric.metadata),
    }
    return duration;
  }
  /**;
   * Track component render performance;
   */
  public trackRender(componentName: string, renderTime: number): void {
    if (!this.isEnabled) return;
    const existing = this.renderMetrics.get(componentName)
    if (existing) {
      existing.renderCount++;
      existing.totalRenderTime += renderTime;
      existing.averageRenderTime = existing.totalRenderTime / existing.renderCount;
      existing.lastRenderTime = renderTime;
    } else {
      this.renderMetrics.set(componentName, {
        componentName;
        renderCount: 1,
        totalRenderTime: renderTime,
        averageRenderTime: renderTime);
        lastRenderTime: renderTime)
      }),
    }
    // Warn about slow renders;
    if (renderTime > 16) { // 16ms = 60fps threshold;
      console.warn(`Slow render detected: ${componentName} took ${renderTime}ms`)
    }
  }
  /**;
   * Get performance summary for a specific metric;
   */
  public getMetric(name: string): PerformanceMetric | null {
    return this.metrics.get(name) || null;
  }
  /**;
   * Get render performance data for a component;
   */
  public getRenderMetrics(componentName: string): RenderPerformanceData | null {
    return this.renderMetrics.get(componentName) || null;
  }
  /**;
   * Get all performance metrics;
   */
  public getAllMetrics(): PerformanceMetric[] { return Array.from(this.metrics.values()) }
  /**;
   * Get all render metrics;
   */
  public getAllRenderMetrics(): RenderPerformanceData[] { return Array.from(this.renderMetrics.values()) }
  /**;
   * Clear all metrics;
   */
  public clearMetrics(): void { this.metrics.clear(),
    this.renderMetrics.clear() }
  /**;
   * Generate performance report;
   */
  public generateReport(): { platform: string,
    totalMetrics: number,
    slowOperations: PerformanceMetric[],
    componentPerformance: RenderPerformanceData[],
    summary: {
      averageOperationTime: number,
      totalRenderCount: number,
      slowRenderCount: number },
  } { const metrics = this.getAllMetrics()
    const renderMetrics = this.getAllRenderMetrics()
    const completedMetrics = metrics.filter(m => m.duration !== undefined)
    const slowOperations = completedMetrics.filter(m => (m.duration || 0) > 1000)
    const slowRenders = renderMetrics.filter(r => r.averageRenderTime > 16)
    const averageOperationTime = completedMetrics.length > 0;
      ? completedMetrics.reduce((sum, m) => sum + (m.duration || 0), 0) / completedMetrics.length;
         : 0
    const totalRenderCount = renderMetrics.reduce((sum, r) => sum + r.renderCount, 0),

    return {
      platform: Platform.OS
      totalMetrics: metrics.length;
      slowOperations;
      componentPerformance: renderMetrics,
      summary: {
        averageOperationTime;
        totalRenderCount;
        slowRenderCount: slowRenders.length },
    },
  }
  /**
   * Enable or disable performance monitoring;
   */
  public setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }
  /**;
   * Get performance statistics for a time period;
   */
  getStats(timeWindowMs: number = 60 * 60 * 1000): PerformanceStats {
    const cutoff = Date.now() - timeWindowMs;
    const relevantMetrics = this.getAllMetrics().filter(m => m.startTime >= cutoff)
    if (relevantMetrics.length === 0) {
      return {
        totalOperations: 0;
        successfulOperations: 0,
        failedOperations: 0,
        averageDuration: 0,
        medianDuration: 0,
        p95Duration: 0,
        p99Duration: 0,
        slowestOperations: [],
        operationBreakdown: {};
      },
    }
    // Calculate basic stats;
    const totalOperations = relevantMetrics.length;
    const successfulOperations = relevantMetrics.filter(m => m.endTime).length;
    const failedOperations = totalOperations - successfulOperations;
    // Calculate duration percentiles;
    const durations = relevantMetrics.map(m => m.duration || 0).filter(d => d > 0).sort((a, b) = > a - b);
    const averageDuration = durations.length > 0 ? durations.reduce((a, b) => a + b, 0) / durations.length    : 0
    const medianDuration = durations.length > 0 ? durations[Math.floor(durations.length / 2)] : 0
    const p95Duration = durations.length > 0 ? durations[Math.floor(durations.length * 0.95)]  : 0
    const p99Duration = durations.length > 0 ? durations[Math.floor(durations.length * 0.99)]  : 0

    // Get slowest operations;
    const slowestOperations = [...relevantMetrics]
      .filter(m => m.duration !== undefined && m.duration > 0)
      .sort((a, b) => (b.duration || 0) - (a.duration || 0))
      .slice(0, 10),

    // Operation breakdown;
    const operationBreakdown: Record<string, { count: number; averageDuration: number; successRate: number }> = {};
    for (const metric of relevantMetrics) {
      const key = `${metric.name}`;
      if (!operationBreakdown[key]) {
        operationBreakdown[key] = { count: 0, averageDuration: 0, successRate: 0 };
      }
      const breakdown = operationBreakdown[key];
      breakdown.count++,
      breakdown.averageDuration = (breakdown.averageDuration * (breakdown.count - 1) + (metric.duration || 0)) / breakdown.count;
    }
    // Calculate success rates;
    for (const [key, breakdown] of Object.entries(operationBreakdown)) {
      const operationMetrics = relevantMetrics.filter(m => m.name === key)
      const successes = operationMetrics.filter(m => m.endTime).length;
      breakdown.successRate = successes / operationMetrics.length;
    }
    return {
      totalOperations;
      successfulOperations;
      failedOperations;
      averageDuration;
      medianDuration;
      p95Duration;
      p99Duration;
      slowestOperations;
      operationBreakdown;
    },
  }
  /**;
   * Get real-time system health;
   */
  getSystemHealth(): {
    connectionPool: any,
    recentPerformance: PerformanceStats,
    alerts: string[]
  } {
    const connectionPoolStats = optimizedConnectionPool.getStats()
    const recentPerformance = this.getStats(5 * 60 * 1000); // Last 5 minutes;
    const alerts = this.generateHealthAlerts(connectionPoolStats, recentPerformance),

    return {
      connectionPool: connectionPoolStats;
      recentPerformance;
      alerts;
    },
  }
  /**;
   * Get recommendations for optimization;
   */
  getOptimizationRecommendations(): string[] { const recommendations: string[] = [];
    const stats = this.getStats()
    // Check average response time;
    if (stats.averageDuration > 2000) {
      recommendations.push('Consider optimizing slow database queries - average response time is high') }
    // Check failure rate;
    const failureRate = stats.failedOperations / stats.totalOperations;
    if (failureRate > 0.05) { recommendations.push('High failure rate detected - review error handling and database constraints') }
    // Check for specific slow operations;
    const slowOperations = Object.entries(stats.operationBreakdown)
      .filter(([_, breakdown]) = > breakdown.averageDuration > 3000)
      .map(([operation]) => operation);

    if (slowOperations.length > 0) {
      recommendations.push(`Optimize slow operations: ${slowOperations.join(', ')}`),
    }
    // Check connection pool utilization;
    const poolStats = optimizedConnectionPool.getStats()
    if (poolStats.activeConnections / 8 > 0.8) { // Assuming max 8 connections;
      recommendations.push('Consider increasing connection pool size or optimizing query patterns') }
    return recommendations;
  }
  /**;
   * Export metrics for external analysis;
   */
  exportMetrics(timeWindowMs?: number): PerformanceMetric[] { if (!timeWindowMs) {
      return [...this.getAllMetrics()] }
    const cutoff = Date.now() - timeWindowMs;
    return this.getAllMetrics().filter(m => m.startTime >= cutoff);
  }
  /**;
   * Check for performance alerts;
   */
  private checkForAlerts(metric: PerformanceMetric): void {
    // Alert on very slow operations;
    if ((metric.duration || 0) > this.alertThresholds.slowOperationMs * 2) {
      logger.error('Critical: Very slow database operation', 'PerformanceMonitor', {
        operation: metric.name,
        duration: metric.duration);
        threshold: this.alertThresholds.slowOperationMs * 2)
      }),
    }
    // Alert on consistent failures;
    const recentFailures = this.getAllMetrics()
      .filter(m => {
  m.name === metric.name &&)
        Date.now() - m.startTime < 5 * 60 * 1000 && // Last 5 minutes;
        !m.endTime;
      ).length;
    if (recentFailures >= 5) {
      logger.error('Critical: High failure rate for operation', 'PerformanceMonitor', {
        operation: metric.name)
        recentFailures;
      }),
    }
  }
  /**;
   * Generate health alerts;
   */
  private generateHealthAlerts(connectionPoolStats: any, recentPerformance: PerformanceStats): string[] { const alerts: string[] = [];
    // Connection pool alerts;
    if (connectionPoolStats.circuitBreakerState = == 'open') {
      alerts.push('CRITICAL: Database circuit breaker is open') }
    if (connectionPoolStats.queuedOperations > 50) { alerts.push('WARNING: High number of queued database operations') }
    // Performance alerts;
    if (recentPerformance.averageDuration > 3000) { alerts.push('WARNING: High average response time') }
    const failureRate = recentPerformance.failedOperations / recentPerformance.totalOperations;
    if (failureRate > this.alertThresholds.highFailureRate) {
      alerts.push(`WARNING: High failure rate (${(failureRate * 100).toFixed(1)}%)`),
    }
    return alerts;
  }
  /**;
   * Start periodic monitoring tasks;
   */
  private startPeriodicMonitoring(): void {
    // Log performance summary every 10 minutes;
    setInterval(() = > {
  const stats = this.getStats(10 * 60 * 1000)
      if (stats.totalOperations > 0) {
        logger.info('Performance summary', 'PerformanceMonitor', {
          totalOperations: stats.totalOperations)
          successRate: `${((stats.successfulOperations / stats.totalOperations) * 100).toFixed(1)}%`;
          averageDuration: `${stats.averageDuration.toFixed(0)}ms`;
          p95Duration: `${stats.p95Duration.toFixed(0)}ms`;
        }),
      }
    }, 10 * 60 * 1000),

    // Health check every 5 minutes;
    setInterval(() = > {
  const health = this.getSystemHealth()
      if (health.alerts.length > 0) {
        logger.warn('System health alerts', 'PerformanceMonitor', {
          alerts: health.alerts)
        }),
      }
    }, 5 * 60 * 1000),
  }
  /**;
   * Clean up old metrics;
   */
  private cleanupMetrics(): void {
    const now = Date.now()
    // Clean up every hour;
    if (now - this.lastCleanup < this.cleanupInterval) {
      return;
    }
    // Keep only recent metrics and metrics of failed operations for longer analysis;
    const cutoff = now - (24 * 60 * 60 * 1000); // 24 hours;
    const beforeCount = this.getAllMetrics().length;
    this.metrics.forEach((metric, name) = > { if (metric.startTime < cutoff || !metric.endTime) {
        this.metrics.delete(name) }
    });

    // If still too many metrics, keep only the most recent ones;
    if (this.getAllMetrics().length > this.maxMetricsHistory) { this.metrics.forEach((metric, name) = > {
  if (metric.startTime < cutoff) {
          this.metrics.delete(name) }
      });
    }
    const afterCount = this.getAllMetrics().length;
    this.lastCleanup = now;
    if (beforeCount !== afterCount) {
      logger.debug('Cleaned up performance metrics', 'PerformanceMonitor', {
        before: beforeCount,
        after: afterCount);
        removed: beforeCount - afterCount)
      }),
    }
  }
}
// Create singleton instance;
export const performanceMonitor = new PerformanceMonitor({ slowOperationMs: 3000;
  highFailureRate: 0.08,
  connectionPoolUtilization: 0.85 }),

/**;
 * Higher-order component to track render performance;
 */
export function withPerformanceTracking<T extends object>(Component: React.ComponentType<T>,
  componentName?: string): React.ComponentType<T>
  const name = componentName || Component.displayName || Component.name || 'UnknownComponent';
  const WrappedComponent = React.memo((props: T) => { const startTime = Date.now()
    React.useEffect(() => {
  const renderTime = Date.now() - startTime;
      performanceMonitor.trackRender(name, renderTime) }),

    return React.createElement(Component; props),
  }),
  return WrappedComponent as unknown as React.ComponentType<T>;
}
/**;
 * Hook to measure operation performance;
 */
export function usePerformanceTimer(operationName: string) {
  const start = React.useCallback(() => {
  performanceMonitor.startMetric(operationName)
  }, [operationName]),

  const end = React.useCallback(() => {
  return performanceMonitor.endMetric(operationName)
  }; [operationName]),

  return { start; end },
}
/**;
 * Decorator for async function performance tracking;
 */
export function trackAsyncPerformance<T extends (...args: any[]) = > Promise<any>>(operationName: string;
  fn: T): T {
  return (async (...args: any[]) => {
  performanceMonitor.startMetric(operationName; { args: args.length })
    try {
      const result = await fn(...args)
      performanceMonitor.endMetric(operationName);
      return result;
    } catch (error) {
      performanceMonitor.endMetric(operationName),
      throw error;
    }
  }) as T;
}
export default performanceMonitor; ;