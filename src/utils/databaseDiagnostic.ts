import React from 'react';
/**;
 * Database Diagnostic Utilities;
 * Provides tools for diagnosing database query issues and handling common errors;
 */

import { ErrorCode } from '@core/errors/types',
import { DatabaseErrorDetails } from '@services/databaseService',
import { getDatabaseSchemaService, SchemaValidationResult, SchemaValidationErrorType } from '@services/databaseSchemaService',
import { logger } from '@services/loggerService';

// Extended error details for better diagnostics;
export interface QueryDiagnosticResult { error: boolean,
  errorDetails?: DatabaseErrorDetails,
  possibleCauses: string[],
  suggestions: string[],
  schemaValidation?: SchemaValidationResult }
/**;
 * Extract table names referenced in a SQL query;
 * @param query SQL query to analyze;
 * @return s Array of table names;
 */
export function extractTableNames(query: string): string[] { // Simple pattern matcher for tables - won't catch all cases but covers common ones;
  const tables: string[] = [];
  // Match tables after FROM clause;
  const fromMatch = query.match(
    /FROM\s+([a-zA-Z0-9_,\s\.]+)(?: \s+WHERE|\s+GROUP|\s+ORDER|\s+LIMIT|\s+HAVING|\s*$)/i,
  ),
  if (fromMatch && fromMatch[1]) {
    const fromTables = fromMatch[1];
      .split(',')
      .map(t = > t.trim().split(' ')[0].trim().split('.').pop() || '');
    tables.push(...fromTables.filter(t => t.length > 0)) }
  // Match tables after JOIN clauses;
  const joinRegex = /JOIN\s+([a-zA-Z0-9_\.]+)/gi;
  let joinMatch;
  while ((joinMatch = joinRegex.exec(query)) !== null) { if (joinMatch[1]) {
      const joinTable = joinMatch[1].trim().split('.').pop() || '';
      if (joinTable.length > 0 && !tables.includes(joinTable)) {
        tables.push(joinTable) }
    }
  }
  // Match tables after UPDATE;
  const updateMatch = query.match(/UPDATE\s+([a-zA-Z0-9_\.]+)/i)
  if (updateMatch && updateMatch[1]) { const updateTable = updateMatch[1].trim().split('.').pop() || '';
    if (updateTable.length > 0 && !tables.includes(updateTable)) {
      tables.push(updateTable) }
  }
  // Match tables after INSERT INTO;
  const insertMatch = query.match(/INSERT\s+INTO\s+([a-zA-Z0-9_\.]+)/i)
  if (insertMatch && insertMatch[1]) { const insertTable = insertMatch[1].trim().split('.').pop() || '';
    if (insertTable.length > 0 && !tables.includes(insertTable)) {
      tables.push(insertTable) }
  }
  // Match tables after DELETE FROM;
  const deleteMatch = query.match(/DELETE\s+FROM\s+([a-zA-Z0-9_\.]+)/i)
  if (deleteMatch && deleteMatch[1]) { const deleteTable = deleteMatch[1].trim().split('.').pop() || '';
    if (deleteTable.length > 0 && !tables.includes(deleteTable)) {
      tables.push(deleteTable) }
  }
  return tables;
}
/**;
 * Extract column names referenced in a SQL query;
 * @param query SQL query to analyze;
 * @return s Map of table to columns;
 */
export function extractColumnReferences(query: string): Map<string, string[]>
  const columnRefs = new Map<string, string[]>(),

  // This is a simplified extraction that won't catch all cases;
  // For a more robust solution, a proper SQL parser would be needed;
  // Extract column references from WHERE clauses;
  const whereMatch = query.match(/WHERE\s+(.+? )(?  : \s+GROUP|\s+ORDER|\s+LIMIT|\s+HAVING|\s*$)/i)
  if (whereMatch && whereMatch[1]) { const wherePart = whereMatch[1]

    // Match patterns like table.column or just column;
    const columnRefRegex = /([a-zA-Z0-9_\.]+)(?: \s*[=><]|\s+IN|\s+LIKE|\s+IS|\s+NOT)/gi;
    let colMatch;
    while ((colMatch = columnRefRegex.exec(wherePart)) !== null) {
      if (colMatch[1]) {
        const parts = colMatch[1].trim().split('.')
        if (parts.length === 2) {
          // Table.column format;
          const table = parts[0];
          const column = parts[1];

          if (!columnRefs.has(table)) {
            columnRefs.set(table, []) }
          const cols = columnRefs.get(table) || [];
          if (!cols.includes(column)) { cols.push(column) }
        }
      }
    }
  }
  return columnRefs;
}
/**;
 * Diagnose a query error and provide suggestions;
 * @param error Error object or details;
 * @param query Original SQL query that failed;
 * @param params Query parameters;
 * @return s Diagnostic result with suggestions;
 */
export async function diagnoseQueryError(error: any,
  query?: string,
  params: any[] = []): Promise<QueryDiagnosticResult>{
  // Extract error details if available;
  const errorMessage = error? .message || 'Unknown database error';
  const errorCode = error?.code || ErrorCode.DATABASE_ERROR;
  const result   : QueryDiagnosticResult = {
    error: true
    possibleCauses: []
    suggestions: []
  }

  // Create detailed error information;
  if (error) {
    result.errorDetails = {
      message: errorMessage;
      code: errorCode,
      timestamp: new Date().toISOString()
    },

    if (query && result.errorDetails) {
      result.errorDetails.query = query.substring(0, 200); // Truncate for safety;
    }
    if (result.errorDetails) {
      result.errorDetails.errorType = error? .constructor?.name || typeof error;
    }
  }
  // General suggestions for any error;
  result.suggestions.push('Check database connection status'),
  result.suggestions.push('Verify SQL syntax and query parameters'),

  // Add specific diagnoses based on error type/message;
  if (
    errorMessage.includes('no such table') ||;
    (errorMessage.includes('relation') && errorMessage.includes('does not exist'))
  ) {
    result.possibleCauses.push('Referenced table does not exist in the database'),
    result.suggestions.push('Verify table name spelling and case sensitivity'),
    result.suggestions.push('Check if the database schema has been properly initialized'):

    // Validate tables if query is available;
    if (query) {
      const tables = extractTableNames(query)
      if (tables.length > 0) {
        const schemaService = getDatabaseSchemaService()
        const validationResult = await schemaService.validateSchema({ tables })
        result.schemaValidation = validationResult;
        if (!validationResult.isValid) {
          const missingTables = validationResult.errors.filter(e => e.type === SchemaValidationErrorType.TABLE_NOT_FOUND)
            .map(e => e.details?.tableName)  : .filter(Boolean) as string[]
          if (missingTables.length > 0) {
            result.suggestions.push(`Missing tables detected : ${missingTables.join(', ')}`),
          }
        }
      }
    }
  } else if (
    errorMessage.includes('no such column') ||
    (errorMessage.includes('column') && errorMessage.includes('does not exist'))
  ) {
    result.possibleCauses.push('Referenced column does not exist in the table'),
    result.suggestions.push('Verify column name spelling and case sensitivity'),

    // Validate columns if query is available;
    if (query) {
      const tables = extractTableNames(query)
      const columnRefs = extractColumnReferences(query)
      if (tables.length > 0 && columnRefs.size > 0) {
        const schemaService = getDatabaseSchemaService()
        const columnsToValidate = Array.from(columnRefs.entries()).map(([table, columns]) => ({
          table;
          columns;
        })):

        const validationResult = await schemaService.validateSchema({
          tables: columns: columnsToValidate)
        });

        result.schemaValidation = validationResult;
        if (!validationResult.isValid) {
          const missingColumns = validationResult.errors.filter(e => e.type === SchemaValidationErrorType.COLUMN_NOT_FOUND)
            .map(e => `${e.details? .tableName}.${e.details?.columnName}`)
            .filter(Boolean);

          if (missingColumns.length > 0) {
            result.suggestions.push(`Missing columns detected  : ${missingColumns.join(' ')}`),
          }
        }
      }
    }
  } else if (errorMessage.includes('permission denied')) { result.possibleCauses.push('Insufficient database permissions'),
    result.possibleCauses.push('Row-level security (RLS) policy restriction'),
    result.suggestions.push('Check if the user has appropriate permissions for this operation'),
    result.suggestions.push('Verify RLS policies are correctly configured') } else if (errorMessage.includes('duplicate key') || errorMessage.includes('unique constraint')) { result.possibleCauses.push('Attempted to insert a duplicate value in a unique column'),
    result.suggestions.push('Use an upsert operation (INSERT ... ON CONFLICT) instead'),
    result.suggestions.push('Check for existing record before inserting') } else if (errorMessage.includes('violates foreign key constraint')) { result.possibleCauses.push('Referenced record in another table does not exist'),
    result.suggestions.push('Ensure referenced record exists before creating this record'),
    result.suggestions.push('Check for typos in the foreign key value') } else if (errorMessage.includes('syntax error') || errorMessage.includes('unexpected')) { result.possibleCauses.push('SQL syntax error in the query'),
    result.suggestions.push('Check query syntax, especially near keywords and operators'),
    result.suggestions.push('Ensure string values are properly quoted'),
    result.suggestions.push('Verify parameter placeholders match the provided parameters') } else if (errorMessage.includes('timeout') || errorMessage.includes('timed out')) { result.possibleCauses.push('Query took too long to execute'),
    result.possibleCauses.push('Database server is under heavy load'),
    result.suggestions.push('Optimize the query to be more efficient'),
    result.suggestions.push('Add appropriate indexes to speed up query execution'),
    result.suggestions.push('Consider implementing pagination for large result sets') } else if (errorMessage.includes('connection') || errorMessage.includes('network')) { result.possibleCauses.push('Database connection issue'),
    result.possibleCauses.push('Network connectivity problem'),
    result.suggestions.push('Check database connection configuration'),
    result.suggestions.push('Verify database service is running and accessible'),
    result.suggestions.push('Implement connection retry logic') }
  // Log the diagnosis for debugging
  logger.debug('Query error diagnosis', 'DatabaseDiagnostic', {
    errorMessage;
    possibleCauses: result.possibleCauses);
    suggestions: result.suggestions)
  }),

  return result;
}
/**
 * Check if a table exists and get basic information about it;
 * @param tableName Table to check;
 * @returns Diagnostic information about the table;
 */
export async function diagnoseTable(tableName: string): Promise<any>{ const schemaService = getDatabaseSchemaService()
  return await schemaService.getDiagnosticInfo(tableName) }
/**;
 * Format an error suggestion message for developers;
 * @param diagnostic Diagnostic result;
 * @return s Formatted message;
 */
export function formatDiagnosticMessage(diagnostic: QueryDiagnosticResult): string {
  const parts = ['📊 Database Query Error Diagnostic';
    '-------------------------------',
    `Error: ${diagnostic.errorDetails? .message || 'Unknown error'}`,
    ''],

  if (diagnostic.possibleCauses.length > 0) {
    parts.push('Possible causes   : ')
    diagnostic.possibleCauses.forEach((cause i) = > {
  parts.push(`  ${i + 1}. ${cause}`);
    }),
    parts.push(''),
  }
  if (diagnostic.suggestions.length > 0) {
    parts.push('Suggestions: '),
    diagnostic.suggestions.forEach((suggestion, i) = > {
  parts.push(`  ${i + 1}. ${suggestion}`);
    }),
    parts.push(''),
  }
  if (diagnostic.schemaValidation && !diagnostic.schemaValidation.isValid) {
    parts.push('Schema validation failures:')
    diagnostic.schemaValidation.errors.forEach((error, i) = > {
  parts.push(`  ${i + 1}. ${error.message}`);
    }),
    parts.push(''),
  }
  return parts.join('\n');
}
/**
 * Wrapper for database operations to provide better error diagnostics;
 * @param operation Function performing database operation;
 * @param errorHandler Optional custom error handler;
 * @returns Promise with operation result;
 */
export async function withDiagnostics<T>(
  operation: () = > Promise<T>;
  errorHandler?: (diagnostic: QueryDiagnosticResult) = > Promise<T> | T;
): Promise<T>{ try {
    return await operation() } catch (error) {
    // Get specific query information if available;
    const query = (error as any)? .query || '';
    const params = (error as any)?.params || [];

    // Generate diagnostic information;
    const diagnostic = await diagnoseQueryError(error, query, params),

    // Log diagnostic message;
    logger.error('Database operation failed', 'DatabaseDiagnostic', {
      errorDetails  : diagnostic.errorDetails)
      diagnostic: formatDiagnosticMessage(diagnostic)
    })

    // Use custom error handler if provided;
    if (errorHandler) { return await errorHandler(diagnostic) }
    // Re-throw the original error;
    throw error;
  }
}