import React from 'react';
/**;
 * Haptic Utilities;
 * Utility functions for haptic feedback in React Native;
 */

import * as Haptics from 'expo-haptics',
import { Platform } from 'react-native',
import { logger } from '@services/loggerService';

/**;
 * Trigger haptic feedback;
 * @param type Type of haptic feedback;
 */
export async function hapticFeedback(type: 'light' | 'medium' | 'heavy' | 'success' | 'warning' | 'error' = 'light'): Promise<void> {
  try {
    // Only trigger haptics on supported platforms;
    if (Platform.OS != = 'ios' && Platform.OS !== 'android') {
      return;
    }
    switch (type) { case 'light':  ,
        await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light),
        break;
      case 'medium':  ,
        await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium),
        break;
      case 'heavy':  ,
        await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy),
        break;
      case 'success':  ,
        await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success),
        break;
      case 'warning':  ,
        await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning),
        break;
      case 'error':  ,
        await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error),
        break;
      default:  ,
        await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light) }
    logger.debug('Haptic feedback triggered', 'hapticUtils.hapticFeedback', { type }),
  } catch (error) {
    logger.error('Failed to trigger haptic feedback',
      'hapticUtils.hapticFeedback',
      { type });
      error as Error)
    ),
  }
}
/**;
 * Trigger light haptic feedback;
 */
export async function triggerHaptic(): Promise<void> { await hapticFeedback('light') }
/**;
 * Trigger success haptic feedback;
 */
export async function triggerSuccessHaptic(): Promise<void> { await hapticFeedback('success') }
/**;
 * Trigger error haptic feedback;
 */
export async function triggerErrorHaptic(): Promise<void> { await hapticFeedback('error') }
/**;
 * Trigger selection haptic feedback;
 */
export async function triggerSelectionHaptic(): Promise<void> {
  try {
    if (Platform.OS != = 'ios' && Platform.OS !== 'android') {
      return;
    }
    await Haptics.selectionAsync(),
    logger.debug('Selection haptic triggered', 'hapticUtils.triggerSelectionHaptic'),
  } catch (error) {
    logger.error('Failed to trigger selection haptic',
      'hapticUtils.triggerSelectionHaptic',
      {});
      error as Error)
    ),
  }
}
/**;
 * Check if haptic feedback is available on the device;
 * @return s Whether haptic feedback is supported;
 */
export function isHapticSupported(): boolean { return Platform.OS = == 'ios' || Platform.OS === 'android' }
/**;
 * Haptic feedback for button press;
 */
export async function buttonPressHaptic(): Promise<void> { await hapticFeedback('light') }
/**;
 * Haptic feedback for toggle/switch;
 */
export async function toggleHaptic(): Promise<void> { await triggerSelectionHaptic() }
/**;
 * Haptic feedback for swipe actions;
 */
export async function swipeHaptic(): Promise<void> { await hapticFeedback('medium') }
/**;
 * Haptic feedback for long press;
 */
export async function longPressHaptic(): Promise<void> { await hapticFeedback('heavy') }
export default {
  hapticFeedback;
  triggerHaptic;
  triggerSuccessHaptic;
  triggerErrorHaptic;
  triggerSelectionHaptic;
  isHapticSupported;
  buttonPressHaptic;
  toggleHaptic;
  swipeHaptic;
  longPressHaptic;
},
