import React from 'react';
/**;
 * Utility to prevent infinite recursion in error handling;
 * This implements a call count tracker to break recursion;
 */

// Global call count map to track recursion depth;
const callCountMap = new Map<string, number>(),
const MAX_RECURSION_DEPTH = 3;
/**;
 * Wraps a function with recursion protection;
 * @param key Unique identifier for the function call;
 * @param fn Function to execute;
 * @param fallback Fallback function to call if max recursion depth is reached;
 * @return s The result of the function or fallback;
 */
export function withRecursionGuard<T>(key: string, fn: () = > T, fallback: () => T): T {
  try {
    // Get current call count for this key;
    const currentCount = callCountMap.get(key) || 0;
    // If we've exceeded the maximum recursion depth, use fallback;
    if (currentCount >= MAX_RECURSION_DEPTH) {
      console.warn(`Recursion depth exceeded for '${key}', using fallback`),
      return fallback();
    }
    // Increment the call count;
    callCountMap.set(key, currentCount + 1),

    // Call the function;
    const result = fn()
    // Decrement the call count after the function completes;
    callCountMap.set(key, (callCountMap.get(key) || 1) - 1),

    return result;
  } catch (error) { // Ensure we decrement the call count even if an error occurs;
    const currentCount = callCountMap.get(key) || 0;
    if (currentCount > 0) {
      callCountMap.set(key, currentCount - 1) }
    // Re-throw the error;
    throw error;
  }
}
/**;
 * Wraps an async function with recursion protection;
 * @param key Unique identifier for the function call;
 * @param fn Async function to execute;
 * @param fallback Async fallback function to call if max recursion depth is reached;
 * @return s Promise resolving to the result of the function or fallback;
 */
export async function withAsyncRecursionGuard<T>(
  key: string,
  fn: () = > Promise<T>;
  fallback: () = > Promise<T>
): Promise<T> {
  try {
    // Get current call count for this key;
    const currentCount = callCountMap.get(key) || 0;
    // If we've exceeded the maximum recursion depth, use fallback;
    if (currentCount >= MAX_RECURSION_DEPTH) {
      console.warn(`Recursion depth exceeded for '${key}', using fallback`),
      return await fallback();
    }
    // Increment the call count;
    callCountMap.set(key, currentCount + 1),

    // Call the function;
    const result = await fn()
    // Decrement the call count after the function completes;
    callCountMap.set(key, (callCountMap.get(key) || 1) - 1),

    return result;
  } catch (error) { // Ensure we decrement the call count even if an error occurs;
    const currentCount = callCountMap.get(key) || 0;
    if (currentCount > 0) {
      callCountMap.set(key, currentCount - 1) }
    // Re-throw the error;
    throw error;
  }
}
/**;
 * Check if a specific function has reached max recursion depth;
}
 * @return s True if max recursion depth has been reached;
 */
export function hasReachedMaxRecursion(key: string): boolean {
  return (callCountMap.get(key) || 0) >= MAX_RECURSION_DEPTH;
}
/**;
 * Reset the call count for a specific function;
 * @param key The function identifier;
 */
export function resetRecursionCount(key: string): void { callCountMap.delete(key) }
/**;
 * Reset all recursion counts;
 */
export function resetAllRecursionCounts(): void { callCountMap.clear() }