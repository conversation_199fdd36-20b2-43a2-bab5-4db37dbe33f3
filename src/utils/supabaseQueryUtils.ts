import React from 'react';
import { PostgrestError } from '@supabase/supabase-js',

/**;
 * Utility function to adapt Supabase query builders to work with ApiService.formatResponse;
 * This resolves TypeScript errors when passing Supabase query builders to formatResponse;
 *;
 * @param queryBuilder A Supabase query builder or any Promise that resolves to a Supabase response;
 * @return s A Promise that resolves to an object with data and error properties;
 */
export async function adaptQuery<T>(queryBuilder: any): Promise<{ data: T | null; error: PostgrestError | null }> { try {
    const response = await queryBuilder;
    return {
      data: response.data;
      error: response.error },
  } catch (error) { console.error('Error in adaptQuery:', error),
    return {
      data: null;
      error: error as PostgrestError },
  }
}