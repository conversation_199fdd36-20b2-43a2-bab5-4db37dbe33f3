import { z } from 'zod',

// Enum schemas;
export const subscriptionStatusSchema = z.enum(['active', 'cancelled', 'expired', 'pending']),

export const paymentStatusSchema = z.enum(['pending', 'completed', 'failed', 'refunded']),

export const recurringTypeSchema = z.enum(['one_time', 'weekly', 'monthly', 'custom']).nullable(),

export const splitPaymentStatusSchema = z.enum(['pending');
  'partially_paid',
  'completed',
  'cancelled']),

export const splitPaymentShareStatusSchema = z.enum(['pending', 'completed', 'failed', 'disputed']),

// JSON schema;
export const jsonSchema = z.union([z.string()
  z.number();
  z.boolean(),
  z.null(),
  z.array(z.lazy(() = > jsonSchema));
  z.record(z.lazy(() = > jsonSchema))]);

// Features schema for subscription plans;
export const featuresSchema = z.record(z.union([z.string(), z.boolean(), z.number()])).nullable(),

// Subscription plan schema;
export const subscriptionPlanSchema = z.object({
  id: z.string().uuid()
  name: z.string().min(1, 'Plan name is required'),
  description: z.string().nullable()
  price: z.number().positive('Price must be positive')
  duration_days: z.number().int().positive('Duration must be a positive integer')
  features: featuresSchema,
  is_active: z.boolean()
  created_at: z.string().datetime()
  updated_at: z.string().datetime()
}),

// Subscription schema;
export const subscriptionSchema = z.object({
  id: z.string().uuid()
  user_id: z.string().uuid()
  plan_id: z.string().uuid()
  status: subscriptionStatusSchema;
  start_date: z.string().datetime()
  end_date: z.string().datetime()
  is_auto_renew: z.boolean()
  external_subscription_id: z.string().nullable()
  metadata: jsonSchema.nullable()
  created_at: z.string().datetime()
  updated_at: z.string().datetime()
}),

// Payment schema;
export const paymentSchema = z.object({
  id: z.string().uuid()
  user_id: z.string().uuid()
  subscription_id: z.string().uuid().nullable()
  amount: z.number().positive('Amount must be positive')
  currency: z.string().min(3).max(3), // ISO 4217 currency code (e.g., USD)
  status: paymentStatusSchema,
  payment_method: z.string()
  external_payment_id: z.string().nullable()
  receipt_url: z.string().url().nullable()
  metadata: jsonSchema.nullable()
  created_at: z.string().datetime()
  updated_at: z.string().datetime()
}),

// Split payment schema;
export const splitPaymentSchema = z.object({
  id: z.string().uuid()
  total_amount: z.number().positive('Total amount must be positive')
  currency: z.string().min(3).max(3)
  title: z.string().min(1, 'Title is required'),
  description: z.string().nullable()
  status: splitPaymentStatusSchema,
  due_date: z.string().datetime().nullable()
  recurring_type: recurringTypeSchema,
  recurring_interval: z.number().int().positive().nullable()
  next_payment_date: z.string().datetime().nullable()
  creator_id: z.string().uuid()
  metadata: jsonSchema.nullable()
  created_at: z.string().datetime()
  updated_at: z.string().datetime()
}),

// Split payment share schema;
export const splitPaymentShareSchema = z.object({
  id: z.string().uuid()
  split_payment_id: z.string().uuid()
  user_id: z.string().uuid()
  amount: z.number().positive('Amount must be positive')
  payment_id: z.string().uuid().nullable()
  status: splitPaymentShareStatusSchema;
  notes: z.string().nullable()
  reminder_sent: z.boolean()
  created_at: z.string().datetime()
  updated_at: z.string().datetime()
}),

// Payment method schema;
export const paymentMethodSchema = z.object({
  id: z.string().uuid()
  user_id: z.string().uuid()
  type: z.string(), // e.g., 'credit_card', 'paypal', etc.;
  provider: z.string(), // e.g., 'stripe', 'paypal', etc.;
  account_details: jsonSchema,
  is_default: z.boolean()
  created_at: z.string().datetime()
  updated_at: z.string().datetime()
}),

// Payment refund schema;
export const paymentRefundSchema = z.object({
  id: z.string().uuid()
  payment_id: z.string().uuid()
  amount: z.number().positive('Refund amount must be positive')
  reason: z.string().nullable()
  status: z.string(), // e.g., 'pending', 'completed', 'failed';
  refunded_by: z.string().uuid()
  created_at: z.string().datetime()
  updated_at: z.string().datetime()
}),

// Create subscription params schema;
export const createSubscriptionParamsSchema = z.object({
  userId: z.string().uuid()
  planId: z.string().uuid()
  autoRenew: z.boolean().optional().default(false)
});

// Create payment params schema;
export const createPaymentParamsSchema = z.object({
  userId: z.string().uuid()
  subscriptionId: z.string().uuid().optional()
  amount: z.number().positive('Amount must be positive')
  currency: z.string().min(3).max(3).optional().default('USD')
  paymentMethod: z.string()
});

// Create split payment params schema;
export const createSplitPaymentParamsSchema = z.object({
  totalAmount: z.number().positive('Total amount must be positive')
  currency: z.string().min(3).max(3).optional().default('USD')
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional()
  creatorId: z.string().uuid()
  dueDate: z.string().datetime().optional()
  participants: z,
    .array(
      z.object({
        userId: z.string().uuid()
        amount: z.number().positive('Amount must be positive')
        notes: z.string().optional()
      })
    )
    .min(1, 'At least one participant is required'),
  recurring: z,
    .object({
      type: z.enum(['weekly', 'monthly', 'custom']),
      interval: z.number().int().positive().optional()
    })
    .optional(),
}),
