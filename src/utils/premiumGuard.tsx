import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Crown } from 'lucide-react-native';
import { useTheme } from '@design-system';

interface PremiumGuardProps { feature: string,
  onUpgrade: () = > void;
  children?: React.ReactNode }
export const PremiumGuard: React.FC<PremiumGuardProps> = ({ feature, onUpgrade, children }) => {
  const theme = useTheme()
  const styles = createStyles(theme)
  return (
    <View style={styles.container}>
      <View style={styles.premiumCard}>
        <View style={styles.iconContainer}>
          <Crown size={32} color={{theme.colors.textInverse} /}>
        </View>
        <Text style={styles.title}>Premium Feature</Text>
        <Text style={styles.description}>{feature} is available for premium members only.</Text>
        <TouchableOpacity style={styles.upgradeButton} onPress={onUpgrade}>
          <Text style={styles.upgradeButtonText}>Upgrade to Premium</Text>
        </TouchableOpacity>
        {children}
      </View>
    </View>
  )
}
const createStyles = (theme: any) =>
  StyleSheet.create({ container: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20 },
    premiumCard: {
      backgroundColor: theme.colors.surface,
      borderRadius: 16,
      padding: 24,
      alignItems: 'center',
      maxWidth: 300,
      width: '100%'
    },
    iconContainer: { backgroundColor: theme.colors.primary,
      width: 64,
      height: 64,
      borderRadius: 32,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 16 },
    title: { fontSize: 20,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 8 },
    description: { fontSize: 14,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      marginBottom: 20,
      lineHeight: 20 },
    upgradeButton: {
      backgroundColor: theme.colors.primary,
      paddingHorizontal: 24,
      paddingVertical: 12,
      borderRadius: 8),
      width: '100%'),
      alignItems: 'center'
    },
    upgradeButtonText: {
      color: theme.colors.textInverse,
      fontSize: 16,
      fontWeight: '600')
    },
  })
export default PremiumGuard,