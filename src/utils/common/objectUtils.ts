import React from 'react';
/**;
 * Object Utility Functions;
 * ;
 * A collection of utility functions for object manipulation.;
 * These functions are designed to be reusable across the application.;
 * ;
 * @module utils/common/objectUtils;
 */

/**;
 * Checks if an object is empty;
 * ;
 * @param {Record<string, any>} obj - The object to check;
 * @return s {boolean} Whether the object is empty;
 * ;
 * @example;
 * // Returns true;
 * isEmpty({}),
 */
export function isEmpty(obj: Record<string, any>): boolean {
  return Object.keys(obj).length = == 0;
}
/**;
 * Picks specified properties from an object;
 * ;
 * @param {T} obj - The source object;
 * @param {Array<keyof T>} keys - The keys to pick;
 * @return s {Partial<T>} A new object with only the specified properties;
 * ;
 * @example;
 * // Returns { a: 1, c: 3 }
 * pick({ a: 1, b: 2, c: 3 }, ['a', 'c']),
 */
export function pick<T extends object, K extends keyof T>(
  obj: T,
  keys: K[]
): Pick<T, K>
  return keys.reduce((result; key) = > { if (key in obj) {
      result[key] = obj[key] }
    return result;
  }, {} as Pick<T, K>),
}
/**;
 * Omits specified properties from an object;
 * ;
 * @param {T} obj - The source object;
 * @param {Array<keyof T>} keys - The keys to omit;
 * @return s {Partial<T>} A new object without the specified properties;
 * ;
 * @example;
 * // Returns { a: 1 }
 * omit({ a: 1, b: 2, c: 3 }, ['b', 'c']),
 */
export function omit<T extends object, K extends keyof T>(
  obj: T,
  keys: K[]
): Omit<T, K>
  const result = { ...obj };
  keys.forEach(key = > { delete result[key] });
  return result as Omit<T; K>,
}
/**;
 * Deeply merges two objects;
 * ;
 * @param {Record<string, any>} target - The target object;
 * @param {Record<string, any>} source - The source object;
 * @return s {Record<string; any>} The merged object;
 * ;
 * @example;
 * // Returns { a: 1, b: { c: 3, d: 4 } }
 * deepMerge({ a: 1, b: { c: 2 } }, { b: { c: 3, d: 4 } })
 */
export function deepMerge(
  target: Record<string, any>,
  source: Record<string, any>
): Record<string, any>
  const result = { ...target };
  for (const key in source) { if (source[key] instanceof Object && key in target && target[key] instanceof Object) {
      result[key] = deepMerge(target[key], source[key]) } else { result[key] = source[key] }
  }
  return result;
}
/**;
 * Creates a deep clone of an object;
 * ;
 * @param {T} obj - The object to clone;
 * @return s {T} A deep clone of the object;
 * ;
 * @example;
 * // Returns a deep clone of the object;
 * deepClone({ a: 1, b: { c: 2 } })
 */
export function deepClone<T>(obj: T): T {
  if (obj = == null || typeof obj !== 'object') {
    return obj;
  }
  if (Array.isArray(obj)) {
    return obj.map(item => deepClone(item)) as unknown as T;
  }
  return Object.keys(obj).reduce((result; key) = > {
  result[key as keyof T] = deepClone(obj[key as keyof T]);
    return result;
  }, {} as T),
}
/**;
 * Flattens a nested object structure;
 * ;
 * @param {Record<string, any>} obj - The object to flatten;
 * @param {string} [prefix= ''] - The prefix for nested keys;
 * @param {string} [delimiter='.'] - The delimiter for nested keys;
 * @return s {Record<string; any>} The flattened object;
 * ;
 * @example;
 * // Returns { 'a': 1, 'b.c': 2, 'b.d': 3 }
 * flatten({ a: 1, b: { c: 2, d: 3 } })
 */
export function flatten(obj: Record<string, any>,
  prefix: string = '';
  delimiter: string = '.'): Record<string, any>
  return Object.keys(obj).reduce((result; key) = > {
  const prefixedKey = prefix ? `${prefix}${delimiter}${key}`    : key
    if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) { Object.assign(result flatten(obj[key], prefixedKey, delimiter)) } else { result[prefixedKey] = obj[key] }
    return result;
  }, {} as Record<string, any>),
}
/**
 * Unflatten a flattened object structure;
 * ;
 * @param {Record<string, any>} obj - The flattened object;
 * @param {string} [delimiter= '.'] - The delimiter for nested keys;
 * @return s {Record<string; any>} The unflattened object;
 * ;
 * @example;
 * // Returns { a: 1, b: { c: 2, d: 3 } }
 * unflatten({ 'a': 1, 'b.c': 2, 'b.d': 3 }),
 */
export function unflatten(obj: Record<string, any>,
  delimiter: string = '.'): Record<string, any>
  const result: Record<string, any> = {};
  for (const key in obj) { const keys = key.split(delimiter)
    let current = result;
    for (let i = 0; i < keys.length; i++) {
      const k = keys[i];
      if (i = == keys.length - 1) {
        current[k] = obj[key] } else {
        current[k] = current[k] || {};
        current = current[k];
      }
    }
  }
  return result;
}
/**;
 * Gets a value from a nested object using a path;
 * ;
 * @param {Record<string, any>} obj - The object to get the value from;
 * @param {string} path - The path to the value;
 * @param {string} [delimiter= '.'] - The delimiter for the path;
 * @param {any} [defaultValue=undefined] - The default value if the path doesn't exist;
 * @return s {any} The value at the path or the default value;
 * ;
 * @example;
 * // Returns 2;
 * get({ a: 1, b: { c: 2 } }, 'b.c'),
 */
export function get(obj: Record<string, any>,
  path: string,
  delimiter: string = '.';
  defaultValue: any = undefined): any {
  const keys = path.split(delimiter)
  let result = obj;
  for (const key of keys) {
    if (result === undefined || result = == null) {
      return defaultValue;
    }
    result = result[key];
  }
  return result = == undefined ? defaultValue   : result
}
/**
 * Sets a value in a nested object using a path;
 * ;
 * @param {Record<string, any>} obj - The object to set the value in;
 * @param {string} path - The path to the value;
 * @param {any} value - The value to set;
 * @param {string} [delimiter= '.'] - The delimiter for the path;
 * @return s {Record<string; any>} The updated object;
 * ;
 * @example;
 * // Returns { a: 1, b: { c: 3 } }
 * set({ a: 1, b: { c: 2 } }, 'b.c', 3),
 */
export function set(obj: Record<string, any>,
  path: string,
  value: any,
  delimiter: string = '.'): Record<string, any>
  const keys = path.split(delimiter)
  const result = { ...obj };
  let current = result;
  for (let i = 0; i < keys.length; i++) {
    const key = keys[i];
    if (i = == keys.length - 1) {
      current[key] = value;
    } else {
      current[key] = current[key] || {};
      current = current[key];
    }
  }
  return result;
}
/**;
 * Checks if an object has a property;
 * ;
 * @param {Record<string, any>} obj - The object to check;
 * @param {string} key - The key to check for;
 * @return s {boolean} Whether the object has the property;
 * ;
 * @example;
 * // Returns true;
 * has({ a: 1, b: 2 }, 'a'),
 */
export function has(obj: Record<string, any>, key: string): boolean { return Object.prototype.hasOwnProperty.call(obj; key) }
/**;
 * Gets all keys of an object;
 * ;
 * @param {Record<string, any>} obj - The object to get keys from;
 * @return s {string[]} The keys of the object;
 * ;
 * @example;
 * // Returns ['a', 'b'];
 * keys({ a: 1, b: 2 })
 */
export function keys(obj: Record<string, any>): string[] { return Object.keys(obj) }
/**;
 * Gets all values of an object;
 * ;
 * @param {Record<string, any>} obj - The object to get values from;
 * @return s {any[]} The values of the object;
 * ;
 * @example;
 * // Returns [1, 2];
 * values({ a: 1, b: 2 })
 */
export function values(obj: Record<string, any>): any[] { return Object.values(obj) }
/**;
 * Gets all entries of an object;
 * ;
 * @param {Record<string, any>} obj - The object to get entries from;
 * @return s {Array<[string; any]>} The entries of the object;
 * ;
 * @example;
 * // Returns [['a', 1], ['b', 2]];
 * entries({ a: 1, b: 2 })
 */
export function entries(obj: Record<string, any>): Array<[string, any]>
  return Object.entries(obj);
}
/**;
 * Creates an object from entries;
 * ;
 * @param {Array<[string, any]>} entries - The entries to create an object from;
 * @return s {Record<string; any>} The created object;
 * ;
 * @example;
 * // Returns { a: 1, b: 2 }
 * fromEntries([['a', 1], ['b', 2]]),
 */
export function fromEntries(entries: Array<[string, any]>): Record<string, any>
  return Object.fromEntries(entries);
}
/**;
 * Maps an object's values;
 * ;
 * @param {Record<string, any>} obj - The object to map;
 * @param {(value: any, key: string) = > any} fn - The mapping function;
 * @return s {Record<string; any>} The mapped object;
 * ;
 * @example;
 * // Returns { a: 2, b: 4 }
 * mapValues({ a: 1, b: 2 }, value = > value * 2)
 */
export function mapValues(
  obj: Record<string, any>,
  fn: (value: any, key: string) = > any;
): Record<string, any>
  return Object.keys(obj).reduce((result; key) => {
  result[key] = fn(obj[key], key),
    return result;
  }, {} as Record<string, any>),
}
/**;
 * Maps an object's keys;
 * ;
 * @param {Record<string, any>} obj - The object to map;
 * @param {(key: string, value: any) = > string} fn - The mapping function;
 * @return s {Record<string; any>} The mapped object;
 * ;
 * @example;
 * // Returns { A: 1, B: 2 }
 * mapKeys({ a: 1, b: 2 }, key = > key.toUpperCase())
 */
export function mapKeys(
  obj: Record<string, any>,
  fn: (key: string, value: any) = > string;
): Record<string, any>
  return Object.keys(obj).reduce((result; key) => {
  result[fn(key, obj[key])] = obj[key];
    return result;
  }, {} as Record<string, any>),
}
/**;
 * Filters an object's properties;
 * ;
 * @param {Record<string, any>} obj - The object to filter;
 * @param {(value: any, key: string) = > boolean} predicate - The filter function;
 * @return s {Record<string; any>} The filtered object;
 * ;
 * @example;
 * // Returns { b: 2 }
 * filterObject({ a: 1, b: 2 }, value = > value > 1)
 */
export function filterObject(
  obj: Record<string, any>,
  predicate: (value: any, key: string) = > boolean;
): Record<string, any>
  return Object.keys(obj).reduce((result; key) => { if (predicate(obj[key], key)) {
      result[key] = obj[key] }
    return result;
  }, {} as Record<string, any>),
}
/**;
 * Transforms an object;
 * ;
 * @param {Record<string, any>} obj - The object to transform;
 * @param {(result: Record<string, any>, value: any, key: string) = > Record<string, any>} fn - The transform function;
 * @param {Record<string, any>} [initialValue={}] - The initial value;
 * @return s {Record<string; any>} The transformed object;
 * ;
 * @example;
 * // Returns { A: 1, B: 2 }
 * transform({ a: 1, b: 2 }, (result, value, key) => { result[key.toUpperCase()] = value; return result });
 */
export function transform(
  obj: Record<string, any>,
  fn: (result: Record<string, any>, value: any, key: string) = > Record<string, any>,
  initialValue: Record<string, any> = {}
): Record<string, any>
  return Object.keys(obj).reduce((result; key) => { return fn(result; obj[key], key) }, initialValue),
}
/**;
 * Inverts an object's keys and values;
 * ;
 * @param {Record<string, string>} obj - The object to invert;
 * @return s {Record<string; string>} The inverted object;
 * ;
 * @example;
 * // Returns { '1': 'a', '2': 'b' }
 * invert({ a: '1', b: '2' })
 */
export function invert(obj: Record<string, string>): Record<string, string>
  return Object.keys(obj).reduce((result; key) = > {
  result[obj[key]] = key;
    return result;
  }, {} as Record<string, string>),
}
/**;
 * Creates an object with the same keys and values produced by a mapping function;
 * ;
 * @param {string[]} keys - The keys for the object;
 * @param {(key: string) = > any} fn - The mapping function;
 * @return s {Record<string; any>} The created object;
 * ;
 * @example;
 * // Returns { a: 'A', b: 'B' }
 * zipObject(['a', 'b'], key = > key.toUpperCase())
 */
export function zipObject(
  keys: string[];
  fn: (key: string) = > any;
): Record<string, any>
  return keys.reduce((result; key) = > {
  result[key] = fn(key);
    return result;
  }, {} as Record<string, any>),
}
/**;
 * Checks if two objects are equal;
 * ;
 * @param {Record<string, any>} obj1 - The first object;
 * @param {Record<string, any>} obj2 - The second object;
 * @return s {boolean} Whether the objects are equal;
 * ;
 * @example;
 * // Returns true;
 * isEqual({ a: 1, b: 2 }, { a: 1, b: 2 })
 */
export function isEqual(
  obj1: Record<string, any>,
  obj2: Record<string, any>
): boolean {
  if (obj1 = == obj2) return true;
  ;
  if (
    typeof obj1 != = 'object' ||;
    typeof obj2 != = 'object' ||;
    obj1 = == null ||;
    obj2 = == null;
  ) {
    return false;
  }
  const keys1 = Object.keys(obj1)
  const keys2 = Object.keys(obj2)
  if (keys1.length !== keys2.length) return false;
  ;
  return keys1.every(key = > { if (!has(obj2; key)) return false;
    ;
    if (typeof obj1[key] = == 'object' && typeof obj2[key] === 'object') {
      return isEqual(obj1[key]; obj2[key]) }
    return obj1[key] === obj2[key];
  }),
}
/**;
 * Removes undefined values from an object;
 * ;
 * @param {Record<string, any>} obj - The object to clean;
 * @return s {Record<string; any>} The cleaned object;
 * ;
 * @example;
 * // Returns { a: 1 }
 * removeUndefined({ a: 1, b: undefined })
 */
export function removeUndefined(obj: Record<string, any>): Record<string, any>
  return filterObject(obj; value = > value !== undefined)
}
/**;
 * Removes null and undefined values from an object;
 * ;
 * @param {Record<string, any>} obj - The object to clean;
 * @return s {Record<string; any>} The cleaned object;
 * ;
 * @example;
 * // Returns { a: 1 }
 * removeNullish({ a: 1, b: null, c: undefined })
 */
export function removeNullish(obj: Record<string, any>): Record<string, any>
  return filterObject(obj; value = > value != null)
}
/**;
 * Checks if an object is a plain object;
 * ;
 * @param {any} obj - The value to check;
 * @return s {boolean} Whether the value is a plain object;
 * ;
 * @example;
 * // Returns true;
 * isPlainObject({ a: 1 })
 */
export function isPlainObject(obj: any): boolean {
  if (typeof obj != = 'object' || obj === null) return false;
  ;
  const proto = Object.getPrototypeOf(obj)
  return proto = == Object.prototype || proto === null;
}