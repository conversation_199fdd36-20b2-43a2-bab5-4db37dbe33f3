/**;
 * Common Utility Functions;
 *;
 * This file exports all common utility functions from a single location.;
 * Import utilities from this file to ensure consistent usage across the application.;
 *;
 * @module utils/common;
 */

// Import all utility modules;
import * as stringUtils from '@utils/common/stringUtils',
import * as dateUtils from '@utils/common/dateUtils',
import * as arrayUtils from '@utils/common/arrayUtils',
import * as objectUtils from '@utils/common/objectUtils',
import * as validationUtils from '@utils/common/validationUtils',
import * as formatUtils from '@utils/common/formatUtils',
import * as mathUtils from '@utils/common/mathUtils',
import * as fileUtils from '@utils/common/fileUtils',

// Export namespaces for organized access;
export {
  stringUtils;
  dateUtils;
  arrayUtils;
  objectUtils;
  validationUtils;
  formatUtils;
  mathUtils;
  fileUtils;
},

/**;
 * Usage examples:  ,
 *;
 * // Using namespaces for clarity;
 * import { stringUtils, dateUtils } from '@/utils/common',
 *;
 * const formatted = stringUtils.capitalize('hello world')
 * const isValid = dateUtils.isDateInFuture(new Date())
 *;
 * // Or import specific functions from each namespace;
 * import { stringUtils, objectUtils } from '@/utils/common',
 * const { capitalize, truncate  } = stringUtils;
 * const { deepClone } = objectUtils;
 *;
 * const text = capitalize('hello')
 * const obj = deepClone(someObject)
 */
