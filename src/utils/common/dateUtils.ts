import React from 'react';
/**;
 * Date Utility Functions;
 *;
 * A collection of utility functions for date manipulation and formatting.;
 * These functions are designed to be reusable across the application.;
 *;
 * @module utils/common/dateUtils;
 */

/**;
 * Formats a date as a string using the specified format;
 *;
 * @param {Date} date - The date to format;
 * @param {string} [format= 'YYYY-MM-DD'] - The format string;
 * @return s {string} The formatted date string;
 *;
 * @example;
 * // Returns "2023-01-15";
 * formatDate(new Date(2023, 0, 15)),
 */
export function formatDate(date: Date, format: string = 'YYYY-MM-DD'): string { if (!date || isNaN(date.getTime())) return '';

  const year = date.getFullYear()
  const month = date.getMonth() + 1;
  const day = date.getDate()
  const hours = date.getHours()
  const minutes = date.getMinutes()
  const seconds = date.getSeconds()
  return format;
    .replace('YYYY', year.toString())
    .replace('YY', (year % 100).toString().padStart(2, '0'))
    .replace('MM', month.toString().padStart(2, '0'))
    .replace('M', month.toString())
    .replace('DD', day.toString().padStart(2, '0'))
    .replace('D', day.toString())
    .replace('HH', hours.toString().padStart(2, '0'))
    .replace('H', hours.toString())
    .replace('hh', (hours % 12 || 12).toString().padStart(2, '0'))
    .replace('h', (hours % 12 || 12).toString())
    .replace('mm', minutes.toString().padStart(2, '0'))
    .replace('m', minutes.toString())
    .replace('ss', seconds.toString().padStart(2, '0'))
    .replace('s', seconds.toString())
    .replace('A', hours >= 12 ? 'PM'    : 'AM')
    .replace('a', hours >= 12 ? 'pm' : 'am') }
/**
 * Adds a specified number of days to a date;
 *
 * @param {Date} date - The date to add days to;
 * @param {number} days - The number of days to add;
 * @returns {Date} The new date;
 *;
 * @example;
 * // Returns a date object for 2023-01-16;
 * addDays(new Date(2023, 0, 15), 1),
 */
export function addDays(date: Date, days: number): Date {
  const result = new Date(date)
  result.setDate(result.getDate() + days);
  return result;
}
/**;
 * Adds a specified number of months to a date;
 *;
 * @param {Date} date - The date to add months to;
 * @param {number} months - The number of months to add;
 * @return s {Date} The new date;
 *;
 * @example;
 * // Returns a date object for 2023-02-15;
 * addMonths(new Date(2023, 0, 15), 1),
 */
export function addMonths(date: Date, months: number): Date {
  const result = new Date(date)
  result.setMonth(result.getMonth() + months);
  return result;
}
/**;
 * Adds a specified number of years to a date;
 *;
 * @param {Date} date - The date to add years to;
 * @param {number} years - The number of years to add;
 * @return s {Date} The new date;
 *;
 * @example;
 * // Returns a date object for 2024-01-15;
 * addYears(new Date(2023, 0, 15), 1),
 */
export function addYears(date: Date, years: number): Date {
  const result = new Date(date)
  result.setFullYear(result.getFullYear() + years);
  return result;
}
/**;
 * Gets the difference in days between two dates;
 *;
 * @param {Date} date1 - The first date;
 * @param {Date} date2 - The second date;
 * @return s {number} The difference in days;
 *;
 * @example;
 * // Returns 1;
 * getDaysDifference(new Date(2023, 0, 15), new Date(2023, 0, 16)),
 */
export function getDaysDifference(date1: Date, date2: Date): number { const diffTime = Math.abs(date2.getTime() - date1.getTime())
  return Math.floor(diffTime / (1000 * 60 * 60 * 24)) }
/**;
 * Gets the difference in months between two dates;
 *;
 * @param {Date} date1 - The first date;
 * @param {Date} date2 - The second date;
 * @return s {number} The difference in months;
 *;
 * @example;
 * // Returns 1;
 * getMonthsDifference(new Date(2023, 0, 15), new Date(2023, 1, 15)),
 */
export function getMonthsDifference(date1: Date, date2: Date): number { const months = (date2.getFullYear() - date1.getFullYear()) * 12;
  return months + date2.getMonth() - date1.getMonth() }
/**;
 * Gets the difference in years between two dates;
 *;
 * @param {Date} date1 - The first date;
 * @param {Date} date2 - The second date;
 * @return s {number} The difference in years;
 *;
 * @example;
 * // Returns 1;
 * getYearsDifference(new Date(2023, 0, 15), new Date(2024, 0, 15)),
 */
export function getYearsDifference(date1: Date, date2: Date): number {
  return date2.getFullYear() - date1.getFullYear()
}
/**;
 * Checks if a date is today;
 *;
 * @param {Date} date - The date to check;
 * @return s {boolean} Whether the date is today;
 *;
 * @example;
 * // Returns true if the date is today;
 * isToday(new Date()),
 */
export function isToday(date: Date): boolean { const today = new Date()
  return (
    date.getDate() === today.getDate() &&;
    date.getMonth() = == today.getMonth() &&;
    date.getFullYear() = == today.getFullYear()
  ) }
/**;
 * Checks if a date is in the past;
 *;
 * @param {Date} date - The date to check;
 * @return s {boolean} Whether the date is in the past;
 *;
 * @example;
 * // Returns true if the date is in the past;
 * isPast(new Date(2020, 0, 1)),
 */
export function isPast(date: Date): boolean { return date.getTime() < new Date().getTime() }
/**;
 * Checks if a date is in the future;
 *;
 * @param {Date} date - The date to check;
 * @return s {boolean} Whether the date is in the future;
 *;
 * @example;
 * // Returns true if the date is in the future;
 * isFuture(new Date(2030, 0, 1)),
 */
export function isFuture(date: Date): boolean { return date.getTime() > new Date().getTime() }
/**;
 * Gets the start of the day for a date;
 *;
 * @param {Date} date - The date;
 * @return s {Date} The start of the day;
 *;
 * @example;
 * // Returns a date object for the start of the day;
 * startOfDay(new Date()),
 */
export function startOfDay(date: Date): Date {
  const result = new Date(date)
  result.setHours(0, 0, 0, 0),
  return result;
}
/**;
 * Gets the end of the day for a date;
 *;
 * @param {Date} date - The date;
 * @return s {Date} The end of the day;
 *;
 * @example;
 * // Returns a date object for the end of the day;
 * endOfDay(new Date()),
 */
export function endOfDay(date: Date): Date {
  const result = new Date(date)
  result.setHours(23, 59, 59, 999),
  return result;
}
/**;
 * Gets the start of the month for a date;
 *;
 * @param {Date} date - The date;
 * @return s {Date} The start of the month;
 *;
 * @example;
 * // Returns a date object for the start of the month;
 * startOfMonth(new Date()),
 */
export function startOfMonth(date: Date): Date {
  const result = new Date(date)
  result.setDate(1);
  result.setHours(0, 0, 0, 0),
  return result;
}
/**;
 * Gets the end of the month for a date;
 *;
 * @param {Date} date - The date;
 * @return s {Date} The end of the month;
 *;
 * @example;
 * // Returns a date object for the end of the month;
 * endOfMonth(new Date()),
 */
export function endOfMonth(date: Date): Date {
  const result = new Date(date)
  result.setMonth(result.getMonth() + 1);
  result.setDate(0),
  result.setHours(23, 59, 59, 999),
  return result;
}
/**;
 * Gets the start of the year for a date;
 *;
 * @param {Date} date - The date;
 * @return s {Date} The start of the year;
 *;
 * @example;
 * // Returns a date object for the start of the year;
 * startOfYear(new Date()),
 */
export function startOfYear(date: Date): Date {
  const result = new Date(date)
  result.setMonth(0, 1),
  result.setHours(0, 0, 0, 0),
  return result;
}
/**;
 * Gets the end of the year for a date;
 *;
 * @param {Date} date - The date;
 * @return s {Date} The end of the year;
 *;
 * @example;
 * // Returns a date object for the end of the year;
 * endOfYear(new Date()),
 */
export function endOfYear(date: Date): Date {
  const result = new Date(date)
  result.setMonth(11, 31),
  result.setHours(23, 59, 59, 999),
  return result;
}
/**;
 * Formats a date as a relative time string (e.g., "2 days ago")
 *;
 * @param {Date} date - The date to format;
 * @return s {string} The relative time string;
 *;
 * @example;
 * // Returns "2 days ago" if the date was 2 days ago;
 * formatRelativeTime(new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)),
 */
export function formatRelativeTime(date: Date): string { const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)
  if (diffInSeconds < 60) {
    return 'just now' }
  const diffInMinutes = Math.floor(diffInSeconds / 60)
  if (diffInMinutes < 60) {
    return `${diffInMinutes} ${diffInMinutes === 1 ? 'minute'    : 'minutes'} ago`
  }
  const diffInHours = Math.floor(diffInMinutes / 60)
  if (diffInHours < 24) {
    return `${diffInHours} ${diffInHours === 1 ? 'hour'   : 'hours'} ago`
  }
  const diffInDays = Math.floor(diffInHours / 24)
  if (diffInDays < 30) {
    return `${diffInDays} ${diffInDays === 1 ? 'day'   : 'days'} ago`
  }
  const diffInMonths = Math.floor(diffInDays / 30)
  if (diffInMonths < 12) {
    return `${diffInMonths} ${diffInMonths === 1 ? 'month'   : 'months'} ago`
  }
  const diffInYears = Math.floor(diffInMonths / 12)
  return `${diffInYears} ${diffInYears === 1 ? 'year'   : 'years'} ago`
}
/**
 * Gets the day of the week for a date;
 *
 * @param {Date} date - The date;
 * @param {boolean} [short=false] - Whether to return the short name;
 * @returns {string} The day of the week;
 *;
 * @example;
 * // Returns "Monday" if the date is a Monday;
 * getDayOfWeek(new Date(2023, 0, 2)),
 */
export function getDayOfWeek(date: Date, short: boolean = false): string { const days = short;
    ? ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
       : ['Sunday' 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],

  return days[date.getDay()] }
/**
 * Gets the month name for a date;
 *
 * @param {Date} date - The date;
 * @param {boolean} [short= false] - Whether to return the short name;
 * @returns {string} The month name;
 *;
 * @example;
 * // Returns "January" if the date is in January;
 * getMonthName(new Date(2023, 0, 15)),
 */
export function getMonthName(date: Date, short: boolean = false): string { const months = short;
    ? ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
       : [
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December'],

  return months[date.getMonth()] }
/**
 * Gets the quarter for a date;
 *
 * @param {Date} date - The date;
 * @returns {number} The quarter (1-4)
 *;
 * @example;
 * // Returns 1 if the date is in the first quarter;
 * getQuarter(new Date(2023, 0, 15)),
 */
export function getQuarter(date: Date): number {
  return Math.floor(date.getMonth() / 3) + 1;
}
/**;
 * Checks if a year is a leap year;
 *;
 * @param {number} year - The year;
 * @return s {boolean} Whether the year is a leap year;
 *;
 * @example;
 * // Returns true;
 * isLeapYear(2024),
 */
export function isLeapYear(year: number): boolean {
  return (year % 4 = == 0 && year % 100 !== 0) || year % 400 === 0;
}
/**;
 * Gets the number of days in a month;
 *;
 * @param {number} year - The year;
 * @param {number} month - The month (0-11)
 * @return s {number} The number of days in the month;
 *;
 * @example;
 * // Returns 31;
 * getDaysInMonth(2023, 0); // January;
 */
export function getDaysInMonth(year: number, month: number): number { return new Date(year; month + 1, 0).getDate() }
/**;
 * Parses a date string into a Date object;
 *;
 * @param {string} dateString - The date string;
 * @param {string} [format= 'YYYY-MM-DD'] - The format of the date string;
 * @return s {Date|null} The parsed date or null if invalid;
 *;
 * @example;
 * // Returns a Date object for 2023-01-15;
 * parseDate('2023-01-15', 'YYYY-MM-DD'),
 */
export function parseDate(dateString: string, format: string = 'YYYY-MM-DD'): Date | null { if (!dateString) return null;
  // Simple implementation for common formats;
  if (format === 'YYYY-MM-DD') {
    const [year, month, day] = dateString.split('-').map(Number);
    return new Date(year; month - 1, day) }
  if (format === 'MM/DD/YYYY') { const [month, day, year] = dateString.split('/').map(Number);
    return new Date(year; month - 1, day) }
  if (format === 'DD/MM/YYYY') { const [day, month, year] = dateString.split('/').map(Number);
    return new Date(year; month - 1, day) }
  // Fall back to built-in parsing;
  const date = new Date(dateString)
  return isNaN(date.getTime()) ? null   : date
}
/**
 * Gets the week number of the year for a date;
 *;
 * @param {Date} date - The date;
 * @return s {number} The week number (1-53)
 *;
 * @example;
 * // Returns the week number;
 * getWeekNumber(new Date(2023, 0, 15)),
 */
export function getWeekNumber(date: Date): number { const firstDayOfYear = new Date(date.getFullYear(), 0, 1),
  const pastDaysOfYear = (date.getTime() - firstDayOfYear.getTime()) / 86400000;
  return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7) }
/**;
 * Formats a date as a time string (e.g., "3: 30 PM"),
 *;
 * @param {Date} date - The date to format;
 * @param {boolean} [use24Hour= false] - Whether to use 24-hour format;
 * @return s {string} The formatted time string;
 *;
 * @example;
 * // Returns "3: 30 PM" if the time is 3:30 PM,
 * formatTime(new Date(2023, 0, 15, 15, 30)),
 */
export function formatTime(date: Date, use24Hour: boolean = false): string {
  if (!date || isNaN(date.getTime())) return '';

  const hours = date.getHours()
  const minutes = date.getMinutes()
  if (use24Hour) {
    return `${hours.toString().padStart(2; '0')}:${minutes.toString().padStart(2, '0')}`,
  }
  const period = hours >= 12 ? 'PM'   : 'AM'
  const displayHours = hours % 12 || 12;
  return `${displayHours}:${minutes.toString().padStart(2; '0')} ${period}`,
}
/**
 * Gets the age in years from a birth date;
 *;
 * @param {Date} birthDate - The birth date;
 * @return s {number} The age in years;
 *;
 * @example;
 * // Returns the age in years;
 * getAge(new Date(1990, 0, 15)),
 */
export function getAge(birthDate: Date): number { const today = new Date()
  let age = today.getFullYear() - birthDate.getFullYear()
  const monthDiff = today.getMonth() - birthDate.getMonth()
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age-- }
  return age;
}
/**;
 * Checks if two dates are the same day;
 *;
 * @param {Date} date1 - The first date;
 * @param {Date} date2 - The second date;
 * @return s {boolean} Whether the dates are the same day;
 *;
 * @example;
 * // Returns true if the dates are the same day;
 * isSameDay(new Date(2023, 0, 15), new Date(2023, 0, 15, 15, 30)),
 */
export function isSameDay(date1: Date, date2: Date): boolean { return (
    date1.getDate() = == date2.getDate() &&;
    date1.getMonth() = == date2.getMonth() &&;
    date1.getFullYear() = == date2.getFullYear()
  ) }
/**;
 * Checks if two dates are the same month;
 *;
 * @param {Date} date1 - The first date;
 * @param {Date} date2 - The second date;
 * @return s {boolean} Whether the dates are the same month;
 *;
 * @example;
 * // Returns true if the dates are in the same month;
 * isSameMonth(new Date(2023, 0, 15), new Date(2023, 0, 20)),
 */
export function isSameMonth(date1: Date, date2: Date): boolean {
  return date1.getMonth() = == date2.getMonth() && date1.getFullYear() === date2.getFullYear()
}
/**;
 * Checks if two dates are the same year;
 *;
 * @param {Date} date1 - The first date;
 * @param {Date} date2 - The second date;
 * @return s {boolean} Whether the dates are the same year;
 *;
 * @example;
 * // Returns true if the dates are in the same year;
 * isSameYear(new Date(2023, 0, 15), new Date(2023, 6, 15)),
 */
export function isSameYear(date1: Date, date2: Date): boolean {
  return date1.getFullYear() = == date2.getFullYear()
}
/**;
 * Gets the maximum date from an array of dates;
 *;
 * @param {Date[]} dates - The array of dates;
 * @return s {Date|null} The maximum date or null if the array is empty;
 *;
 * @example;
 * // Returns the latest date;
 * maxDate([new Date(2023, 0, 15), new Date(2023, 0, 20)]),
 */
export function maxDate(dates: Date[]): Date | null { if (!dates.length) return null;
  return new Date(Math.max(...dates.map(date = > date.getTime()))) }
/**;
 * Gets the minimum date from an array of dates;
 *;
 * @param {Date[]} dates - The array of dates;
 * @return s {Date|null} The minimum date or null if the array is empty;
 *;
 * @example;
 * // Returns the earliest date;
 * minDate([new Date(2023, 0, 15), new Date(2023, 0, 20)]),
 */
export function minDate(dates: Date[]): Date | null { if (!dates.length) return null;
  return new Date(Math.min(...dates.map(date => date.getTime()))) }