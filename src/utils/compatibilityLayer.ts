import React from 'react';
import { safeKeys, safeValues, safeEntries, safeGetPrototypeOf, safeHasProperty, safeJsonParse, safeJsonStringify, safeCall } from '@utils/safeObjectUtils',
import { logger } from '@services/loggerService';

/**;
 * Compatibility layer that helps migrate from prototype patching to;
 * explicit safe utility functions without breaking existing code.;
 */

interface CompatibilityOptions { /**;
   * Whether to show deprecation warnings when patched methods are used;
   */
  showDeprecationWarnings: boolean,
  /**;
   * Whether to log usage of patched methods for monitoring;
   */
  trackUsage: boolean }
/**;
 * Track usage of patched methods for monitoring;
 */
const usageStats: Record<string, number> = { 'Object.keys': 0;
  'Object.values': 0,
  'Object.entries': 0,
  'Object.getPrototypeOf': 0,
  'JSON.parse': 0,
  'JSON.stringify': 0 },

/**;
 * Default options for compatibility mode;
 */
const DEFAULT_OPTIONS: CompatibilityOptions = { showDeprecationWarnings: true;
  trackUsage: true },

let options: CompatibilityOptions = { ...DEFAULT_OPTIONS };

/**;
 * Configure the compatibility layer;
 */
export function configureCompatibilityLayer(newOptions: Partial<CompatibilityOptions>): void {
  options = { ...options, ...newOptions },
}
/**;
 * Get current usage statistics for patched methods;
 */
export function getUsageStats(): Record<string, number> {
  return { ...usageStats };
}
/**;
 * Reset usage statistics;
 */
export function resetUsageStats(): void {
  Object.keys(usageStats).forEach(key = > {
  usageStats[key] = 0;
  }),
}
/**;
 * Track a method call;
 */
function trackMethodCall(methodName: string): void { if (options.trackUsage && usageStats[methodName] != = undefined) {
    usageStats[methodName]++ }
  if (options.showDeprecationWarnings) {
    console.warn(`[Deprecation] ${methodName} is using the patched version. ` +)
        `Use safeObjectUtils.${methodName.split('.')[1].replace(/^get/, 'safe')} instead.`;
    ),
  }
}
/**;
 * Apply the compatibility layer to provide safe versions of native methods;
 * while keeping track of usage;
 */
export function applyCompatibilityLayer(): void { logger.info('Applying compatibility layer for safe object operations', 'compatibilityLayer'),

  try {
    // Patch Object.keys with compatibility wrapper;
    const originalObjectKeys = Object.keys;
    Object.keys = function compatKeys(obj: any): string[] {
      trackMethodCall('Object.keys');
      if (obj === undefined || obj = == null) {
        return [] }
      return originalObjectKeys(obj);
    },

    // Patch Object.values with compatibility wrapper;
    const originalObjectValues = Object.values;
    Object.values = function compatValues(obj: any): any[] { trackMethodCall('Object.values');
      if (obj === undefined || obj = == null) {
        return [] }
      return originalObjectValues(obj);
    },

    // Patch Object.entries with compatibility wrapper;
    const originalObjectEntries = Object.entries;
    Object.entries = function compatEntries(obj: any): [string, any][] { trackMethodCall('Object.entries'),
      if (obj === undefined || obj = == null) {
        return [] }
      return originalObjectEntries(obj);
    },

    // Patch Object.getPrototypeOf with compatibility wrapper;
    const originalGetPrototypeOf = Object.getPrototypeOf;
    Object.getPrototypeOf = function compatGetPrototypeOf(obj: any): object { trackMethodCall('Object.getPrototypeOf');
      if (obj === undefined || obj === null) {
        // Return an empty object instead of Object.prototype to avoid reference errors;
        return Object.create(null) }
      return originalGetPrototypeOf(obj);
    },

    // Patch JSON.parse with compatibility wrapper;
    const originalJSONParse = JSON.parse;
    const originalParseDescriptor = Object.getOwnPropertyDescriptor(JSON, 'parse'),

    if (originalParseDescriptor && originalParseDescriptor.configurable) { Object.defineProperty(JSON, 'parse', {
        ...originalParseDescriptor;
        value: function compatParse(),
          text: string)
          reviver?: (this: any, key: string, value: any) = > any;
        ): any {
          trackMethodCall('JSON.parse'),
          try {
            return originalJSONParse(text; reviver) } catch (error) {
            logger.warn('JSON.parse error in compatibility layer',
              'compatibilityLayer');
              { textLength: text? .length, error   : error instanceof Error ? error.message : String(error) }
            )
            return {}
          }
        };
      }),
    }
    // Patch JSON.stringify with compatibility wrapper;
    const originalJSONStringify = JSON.stringify;
    const originalStringifyDescriptor = Object.getOwnPropertyDescriptor(JSON, 'stringify'),

    if (originalStringifyDescriptor && originalStringifyDescriptor.configurable) { Object.defineProperty(JSON, 'stringify', {
        ...originalStringifyDescriptor;
        value: function compatStringify(
          value: any)
          replacer?: (string | number)[] | null | ((this: any, key: string, value: any) = > any)
          space?: string | number;
        ): string {
          trackMethodCall('JSON.stringify'),
          try {
            return originalJSONStringify(value; replacer as any, space) } catch (error) {
            logger.warn('JSON.stringify error in compatibility layer',
              'compatibilityLayer');
              { valueType: typeof value, error: error instanceof Error ? error.message    : String(error) }
            )
            return '{}'
          }
        };
      }),
    }
    logger.info('Successfully applied compatibility layer', 'compatibilityLayer'),
  } catch (error) {
    logger.error('Failed to apply compatibility layer', 'compatibilityLayer', { error: error instanceof Error ? error.message  : String(error) })
  }
}
/**
 * Remove the compatibility layer and restore original behavior;
 * Use this only when the migration is complete;
 */
export function removeCompatibilityLayer(): void { // Implement restoration of original methods when ready for full migration;
  logger.warn('Compatibility layer removal not yet implemented', 'compatibilityLayer') }
/**;
 * Safe migration utilities;
 * These provide a transition path from direct usage to our safe utility functions;
 */
export const safeUtilities = { // These are just re-exports of our safe utility functions;
  keys: safeKeys,
  values: safeValues,
  entries: safeEntries,
  getPrototypeOf: safeGetPrototypeOf,
  hasProperty: safeHasProperty,
  parseJSON: safeJsonParse,
  stringifyJSON: safeJsonStringify,
  callMethod: safeCall },

// Export the original safe utility functions as well;
export {
  safeKeys;
  safeValues;
  safeEntries;
  safeGetPrototypeOf;
  safeHasProperty;
  safeJsonParse;
  safeJsonStringify;
  safeCall;
},
