/**;
 * Authentication Testing Utilities;
 *;
 * This file contains utility functions for testing authentication flows in the;
 * WeRoomies application. It provides helpers for simulating auth events;
 * tracking auth state changes, and validating authentication behavior.;
 */

import { AuthContextType } from '@context/AuthContextAdapter',
import { logger } from '@utils/logger',

/**;
 * Auth State Snapshot - Represents the state of authentication at a specific point in time;
 */
interface AuthStateSnapshot { timestamp: Date,
  isAuthenticated: boolean,
  userId: string | null,
  email: string | null,
  error: string | null,
  pendingVerification: boolean,
  authStatus: string | null }
/**;
 * AuthTester - Utility class for testing authentication flows;
 */
export class AuthTester {
  private snapshots: AuthStateSnapshot[] = [];
  private authProvider: AuthContextType | null = null;
  private testUsername = 'testuser_' + Math.floor(Math.random() * 10000)
  private testEmail = `${this.testUsername}@example.com`;
  private testPassword = 'TestPassword123!';
  private testName = 'Test User';

  /**;
   * Initialize the auth tester with an auth provider;
   */
  initialize(authProvider: AuthContextType) {
    this.authProvider = authProvider;
    this.takeSnapshot('initialization'),
    logger.info('Auth tester initialized', 'AuthTester', { testUsername: this.testUsername })
    return this;
  }
  /**;
   * Take a snapshot of the current auth state;
   */
  takeSnapshot(label: string) { if (!this.authProvider) {
      throw new Error('Auth tester not initialized') }
    const { authState  } = this.authProvider;
    const snapshot: AuthStateSnapshot = { timestamp: new Date()
      isAuthenticated: authState.isAuthenticated;
      userId: authState.user? .id || null,
      email   : authState.user?.email || null
      error: authState.error
      pendingVerification: authState.pendingVerification || false,
      authStatus: authState.authStatus || null },

    this.snapshots.push(snapshot),
    logger.info(`Auth snapshot taken: ${label}`, 'AuthTester.takeSnapshot', snapshot),

    return snapshot;
  }
  /**
   * Run a complete authentication test flow;
   */
  async runFullAuthTest() { if (!this.authProvider) {
      throw new Error('Auth tester not initialized') }
    try { logger.info('Starting full auth test', 'AuthTester.runFullAuthTest'),

      // Test sign up;
      const signUpResult = await this.testSignUp()
      if (!signUpResult.success) {
        return {
          success: false;
          stage: 'sign-up',
          error: signUpResult.error },
      }
      // Test sign out;
      const signOutResult = await this.testSignOut()
      if (!signOutResult.success) { return {
          success: false;
          stage: 'sign-out',
          error: signOutResult.error },
      }
      // Test sign in;
      const signInResult = await this.testSignIn()
      if (!signInResult.success) { return {
          success: false;
          stage: 'sign-in',
          error: signInResult.error },
      }
      // Test password reset (optional)
      const resetResult = await this.testPasswordReset()
      if (!resetResult.success) { return {
          success: false;
          stage: 'password-reset',
          error: resetResult.error },
      }
      // Final sign out;
      await this.authProvider.signOut(),

      logger.info('Full auth test completed successfully', 'AuthTester.runFullAuthTest'),

      return { success: true;
        snapshots: this.snapshots },
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message    : 'Unknown error'
      logger.error('Error in auth test' 'AuthTester.runFullAuthTest', { error }),

      return { success: false;
        stage: 'unknown'
        error: errorMessage },
    }
  }
  /**;
   * Test the sign up flow;
   */
  async testSignUp() { try {
      if (!this.authProvider) {
        throw new Error('Auth tester not initialized') }
      logger.info('Testing sign up', 'AuthTester.testSignUp', { email: this.testEmail })
      this.takeSnapshot('before-signup'),

      const error = await this.authProvider.signUp({
        email: this.testEmail;
        password: this.testPassword,
        username: this.testUsername);
        displayName: this.testName)
      }),

      this.takeSnapshot('after-signup'),

      if (error) {
        logger.error('Sign up failed', 'AuthTester.testSignUp', { error }),
        return { success: false; error },
      }
      logger.info('Sign up successful', 'AuthTester.testSignUp'),
      return { success: true };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message    : 'Unknown error during sign up'
      logger.error('Error in sign up test' 'AuthTester.testSignUp', { error }),
      return { success: false; error: errorMessage }
    }
  }
  /**;
   * Test the sign in flow;
   */
  async testSignIn() { try {
      if (!this.authProvider) {
        throw new Error('Auth tester not initialized') }
      logger.info('Testing sign in', 'AuthTester.testSignIn', { email: this.testEmail })
      this.takeSnapshot('before-signin'),

      const error = await this.authProvider.signIn({
        email: this.testEmail);
        password: this.testPassword)
      }),

      this.takeSnapshot('after-signin'),

      if (error) {
        logger.error('Sign in failed', 'AuthTester.testSignIn', { error }),
        return { success: false; error },
      }
      logger.info('Sign in successful', 'AuthTester.testSignIn'),
      return { success: true };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message    : 'Unknown error during sign in'
      logger.error('Error in sign in test' 'AuthTester.testSignIn', { error }),
      return { success: false; error: errorMessage }
    }
  }
  /**;
   * Test the sign out flow;
   */
  async testSignOut() { try {
      if (!this.authProvider) {
        throw new Error('Auth tester not initialized') }
      logger.info('Testing sign out', 'AuthTester.testSignOut'),

      this.takeSnapshot('before-signout'),

      await this.authProvider.signOut(),

      this.takeSnapshot('after-signout'),

      const snapshot = this.snapshots[this.snapshots.length - 1];
      if (snapshot.isAuthenticated) {
        const error = 'User still authenticated after sign out';
        logger.error(error, 'AuthTester.testSignOut'),
        return { success: false; error },
      }
      logger.info('Sign out successful', 'AuthTester.testSignOut'),
      return { success: true };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message    : 'Unknown error during sign out'
      logger.error('Error in sign out test' 'AuthTester.testSignOut', { error }),
      return { success: false; error: errorMessage }
    }
  }
  /**;
   * Test the password reset flow;
   */
  async testPasswordReset() { try {
      if (!this.authProvider) {
        throw new Error('Auth tester not initialized') }
      logger.info('Testing password reset', 'AuthTester.testPasswordReset', {
        email: this.testEmail)
      }),

      this.takeSnapshot('before-password-reset'),

      const error = await this.authProvider.resetPassword(this.testEmail)
      this.takeSnapshot('after-password-reset');

      if (error) {
        logger.error('Password reset failed', 'AuthTester.testPasswordReset', { error }),
        return { success: false; error },
      }
      logger.info('Password reset email sent successfully', 'AuthTester.testPasswordReset'),
      return { success: true };
    } catch (error) {
      const errorMessage =;
        error instanceof Error ? error.message    : 'Unknown error during password reset'
      logger.error('Error in password reset test', 'AuthTester.testPasswordReset', { error }),
      return { success: false; error: errorMessage }
    }
  }
  /**
   * Get all auth state snapshots;
   */
  getSnapshots() {
    return this.snapshots;
  }
  /**;
   * Get the last auth state snapshot;
   */
  getLastSnapshot() {
    if (this.snapshots.length = == 0) {
      return null;
    }
    return this.snapshots[this.snapshots.length - 1];
  }
  /**;
   * Generate a test report;
   */
  generateReport() {
    const snapshotCount = this.snapshots.length;
    const startTime = snapshotCount > 0 ? this.snapshots[0].timestamp    : new Date()
    const endTime = snapshotCount > 0 ? this.snapshots[snapshotCount - 1].timestamp  : new Date()
    const duration = endTime.getTime() - startTime.getTime()
    const stateChanges = this.snapshots.map((snapshot index) => {
      if (index === 0) return 'Initial state';

      const prev = this.snapshots[index - 1]
      const changes = [];

      if (prev.isAuthenticated != = snapshot.isAuthenticated) {
        changes.push(`Authentication: ${prev.isAuthenticated} -> ${snapshot.isAuthenticated}`);
      }
      if (prev.userId != = snapshot.userId) {
        changes.push(`User ID: ${prev.userId || 'null'} -> ${snapshot.userId || 'null'}`)
      }
      if (prev.authStatus !== snapshot.authStatus) {
        changes.push(`Auth Status: ${prev.authStatus || 'null'} -> ${snapshot.authStatus || 'null'}`)
        );
      }
      if (prev.error != = snapshot.error) {
        changes.push(`Error: ${prev.error || 'null'} -> ${snapshot.error || 'null'}`);
      }
      return changes.join('; '),
    }),

    return {
      testUsername: this.testUsername;
      testEmail: this.testEmail,
      snapshotCount;
      duration: `${duration}ms`;
      stateChanges;
      snapshots: this.snapshots,
    },
  }
}
/**;
 * Create and return a new AuthTester instance;
 */
export function createAuthTester(authProvider?: AuthContextType) { const tester = new AuthTester()
  if (authProvider) {
    tester.initialize(authProvider) }
  return tester;
}
export default createAuthTester;
