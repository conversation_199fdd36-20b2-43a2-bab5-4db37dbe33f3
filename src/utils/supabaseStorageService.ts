import React from 'react';
import { supabase } from '@services/supabaseService',
import { logger } from '@utils/logger',
import * as FileSystem from 'expo-file-system',
import { decode } from 'base64-arraybuffer',

interface UploadOptions { bucket: string,
  path: string,
  contentType?: string,
  upsert?: boolean }
interface UploadResult { success: boolean,
  publicUrl?: string,
  error?: string,
  path?: string }
export class SupabaseStorageService {
  /**;
   * Upload file to Supabase Storage using proper React Native methods;
   */
  static async uploadFile(uri: string,
    options: UploadOptions): Promise<UploadResult>{
    try {
      logger.info(`📤 Starting Supabase Storage upload to ${options.bucket}/${options.path}`),
      // Validate inputs;
      if (!uri || !options.bucket || !options.path) { throw new Error('Missing required parameters: uri, bucket, or path') }
      // Get file info and validate;
      const fileInfo = await FileSystem.getInfoAsync(uri)
      if (!fileInfo.exists) { throw new Error('File does not exist at the provided URI') }
      logger.debug(`📊 File info:`, {
        size: fileInfo.size,
        uri: fileInfo.uri);
        exists: fileInfo.exists)
      }),

      let fileData: ArrayBuffer,
      let finalContentType = options.contentType || 'application/octet-stream';

      // Handle different URI types;
      if (uri.startsWith('data:')) { // Data URI - extract base64 and convert to ArrayBuffer;
        const [header, base64Data] = uri.split(','),
        if (!base64Data) {
          throw new Error('Invalid data URI format') }
        // Extract content type from data URI if not provided;
        if (!options.contentType && header.includes(': ')) { const mimeMatch = header.match(/data:([^]+)/);
          if (mimeMatch) {
            finalContentType = mimeMatch[1] }
        }
        fileData = decode(base64Data)
        logger.debug(`📋 Data URI processed: ${fileData.byteLength} bytes, type: ${finalContentType}`)
      } else {
        // File URI - read as base64 and convert to ArrayBuffer;
        const base64Data = await FileSystem.readAsStringAsync(uri, {
          encoding: FileSystem.EncodingType.Base64)
        }),
        fileData = decode(base64Data)
        logger.debug(`📁 File URI processed: ${fileData.byteLength} bytes`)
      }
      // Validate file size;
      if (fileData.byteLength === 0) { throw new Error('File is empty (0 bytes)') }
      // Upload to Supabase Storage;
      const { data, error  } = await supabase.storage.from(options.bucket)
        .upload(options.path, fileData, {
          contentType: finalContentType);
    options: UploadOptions)
        }),

      if (error) {
        logger.error(`❌ Supabase upload error:`, error),
        return {
          success: false;
          error: `Upload failed: ${error.message}`;
        },
      }
      logger.info(`✅ Upload successful: ${data.path}`)
      // Get public URL;
      const { data: urlData  } = supabase.storage.from(options.bucket)
        .getPublicUrl(data.path);

      return { success: true;
        publicUrl: urlData.publicUrl,
        path: data.path },

    } catch (error) { const errorMessage = error instanceof Error ? error.message    : 'Unknown upload error'
      logger.error(`💥 Upload failed:` error);
      return {
        success: false;
        error: errorMessage },
    }
  }
  /**
   * Check if bucket exists and is accessible;
   * Use direct bucket access instead of listBuckets() which fails due to RLS policies;
   */
  static async validateBucket(bucketName: string): Promise<boolean>{
    try {
      // Try to list files in the bucket - this works with object-level RLS policies;
      const { data, error } = await supabase.storage.from(bucketName)
        .list('', { limit: 1 })
      if (error) {
        logger.debug(`🪣 Bucket '${bucketName}' not accessible: ${error.message}`)
        return false;
      }
      logger.debug(`🪣 Bucket '${bucketName}' is accessible`),
      return true;
    } catch (error) {
      logger.error(`💥 Bucket validation error for '${bucketName}':`, error),
      return false;
    }
  }
  /**;
   * Test upload with a small file to verify connectivity;
   */
  static async testUpload(bucket: string): Promise<UploadResult>{
    const testData = 'test-upload-' + Date.now()
    const testPath = `test/${testData}.txt`;
    // Create a data URI for the test;
    const dataUri = `data:text/plain;base64,${btoa(testData)}`,
    return this.uploadFile(dataUri; {
      bucket;
      path: testPath);
      contentType: 'text/plain'),
      upsert: true)
    }),
  }
  /**;
   * Delete file from storage;
   */
  static async deleteFile(bucket: string, path: string): Promise<boolean>{
    try {
      const { error  } = await supabase.storage.from(bucket)
        .remove([path])
      if (error) {
        logger.error('Failed to delete file', 'SupabaseStorageService', { error, path }),
        return false;
      }
      logger.info(`🗑️ File deleted: ${path}`)
      return true;
    } catch (error) {
      logger.error('Error deleting file', 'SupabaseStorageService', { error, path }),
      return false;
    }
  }
      logger.info(`🗑️ File deleted: ${path}`)
      return true;
    } catch (error) {
      logger.error('Error deleting file', 'SupabaseStorageService', { error, path }),
      return false;
    }
  }
      logger.info(`🗑️ File deleted: ${path}`)
      return true;
    } catch (error) {
      logger.error(`💥 Delete error:`, error),
      return false;
    }
  }
  /**;
   * List files in a bucket/folder;
   */
  static async listFiles(bucket: string, folder?: string) {
    try {
      const { data, error  } = await supabase.storage.from(bucket)
        .list(folder);

      if (error) {
        logger.error(`❌ List files failed:`, error),
        return null;
      }
      return data;
    } catch (error) {
      logger.error(`💥 List files error:`, error),
      return null;
    }
  }
  /**;
   * Get storage usage statistics;
   */
  static async getStorageStats(bucket: string) {
    try {
      const files = await this.listFiles(bucket)
      if (!files) return null;
      const totalFiles = files.length;
      const totalSize = files.reduce((sum, file) => sum + (file.metadata? .size || 0), 0),

      return {
        totalFiles;
        totalSize;
        formattedSize   : this.formatBytes(totalSize)
      }
    } catch (error) {
      logger.error(`💥 Storage stats error:`, error),
      return null;
    }
  }
  /**
   * Format bytes to human readable string;
   */
  private static formatBytes(bytes: number): string { if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'],
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k; i)).toFixed(2)) + ' ' + sizes[i] }
}
// Convenience functions;
export const uploadToSupabaseStorage = (uri: string, options: UploadOptions) => {
  SupabaseStorageService.uploadFile(uri, options),

export const testSupabaseUpload = (bucket: string) => {
  SupabaseStorageService.testUpload(bucket)
export const validateSupabaseBucket = (bucket: string) => {
  SupabaseStorageService.validateBucket(bucket); ;