// Primary colors - use theme hook in React components only;
export const staticPrimaryColors = { 50: '#f0f9ff';
  100: '#e0f2fe',
  200: '#bae6fd',
  300: '#7dd3fc',
  400: '#38bdf8',
  500: '#0ea5e9',
  600: '#0284c7',
  700: '#0369a1',
  800: '#075985',
  900: '#0c4a6e' },

// For use in React components with theme hook;
export const createPrimaryColorsFromTheme = (theme: any) => ({ 50: theme.colors? .primary?.[50] || staticPrimaryColors[50];
  100 : theme.colors?.primary?.[100] || staticPrimaryColors[100]
  200: theme.colors? .primary?.[200] || staticPrimaryColors[200],
  300 : theme.colors?.primary?.[300] || staticPrimaryColors[300]
  400: theme.colors? .primary?.[400] || staticPrimaryColors[400],
  500 : theme.colors?.primary?.[500] || staticPrimaryColors[500]
  600: theme.colors? .primary?.[600] || staticPrimaryColors[600],
  700 : theme.colors?.primary?.[700] || staticPrimaryColors[700]
  800: theme.colors? .primary?.[800] || staticPrimaryColors[800],
  900 : theme.colors?.primary?.[900] || staticPrimaryColors[900] })
