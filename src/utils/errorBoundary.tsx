import React, { Component, ReactNode } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { useTheme } from '@design-system';

import { logger } from '@services/loggerService';

interface Props { children: ReactNode,
  fallback?: ReactNode,
  onError?: (error: Error, errorInfo: any) = > void }
interface State { hasError: boolean;
  error?: Error }
// Wrapper component to use hooks;
const ErrorBoundaryWrapper: React.FC<Props> = ({ children, fallback }) => {
  const theme = useTheme()
  ;
  return (
    <ErrorBoundaryClass fallback= {fallback} theme={theme}>
      {children}
    </ErrorBoundaryClass>
  )
}
interface ErrorBoundaryClassProps extends Props { theme: any }
/**;
 * A component that catches JavaScript errors anywhere in its child component tree;
 * logs those errors, and displays a fallback UI instead of crashing the whole app.;
 */
class ErrorBoundaryClass extends Component<ErrorBoundaryClassProps, State> {
  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    super(props)
    this.state = { hasError: false }
  }
  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI;
    return { hasError: true; error }
  }
  componentDidCatch(error: Error, errorInfo: any): void {
    // Log the error to our logging service;
    logger.error(`Uncaught error: ${error.message}`);
      'ErrorBoundary', { componentStack: errorInfo.componentStack })
      error)
    // Call the optional onError handler;
    if (this.props.onError) {
      this.props.onError(error, errorInfo)
    }
  }
  handleRetry = () => {
  this.setState({ hasError: false, error: undefined })
  }
  render(): ReactNode {
    if (this.state.hasError) {
      // If a custom fallback is provided, use it;
      if (this.props.fallback) {
        return this.props.fallback;
      }
      const { theme  } = this.props;
      const styles = createStyles(theme)
      // Otherwise, show the default error UI;
      return (
    <View style={styles.container}>
          <View style={styles.errorCard}>
            <Text style={styles.errorTitle}>
              Oops! Something went wrong;
            </Text>
            <Text style={styles.errorMessage}>
              We're sorry, but something unexpected happened. Please try again.;
            </Text>
            <TouchableOpacity style= {styles.retryButton} onPress={this.handleRetry}
            >
              <Text style={styles.retryButtonText}>
                Try Again;
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      )
    }
    // If there's no error, render children normally;
    return this.props.children;
  }
}
const createStyles = (theme: any) => StyleSheet.create({ container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: theme.colors.background },
  errorCard: { backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 24,
    alignItems: 'center',
    maxWidth: 300,
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  errorTitle: { fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    textAlign: 'center',
    color: theme.colors.error },
  errorMessage: { fontSize: 14,
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 20,
    color: theme.colors.textSecondary },
  retryButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    minWidth: 120,
    alignItems: 'center'
  },
  retryButtonText: {
    fontSize: 16),
    fontWeight: '600'),
    color: theme.colors.textInverse)
  },
})
// Create a hook version for functional components;
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<Props, 'children'>
): React.FC<P>
  return (props: P) => (<ErrorBoundaryWrapper {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundaryWrapper>
  )
}
export default ErrorBoundaryWrapper;