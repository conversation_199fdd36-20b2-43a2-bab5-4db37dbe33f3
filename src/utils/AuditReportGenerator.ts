import React from 'react';
/**;
 * Audit Report Generator;
 * ;
 * Generates comprehensive audit reports in multiple formats with detailed analysis;
 * trends, recommendations, and executive summaries for production monitoring.;
 */

import { logger } from './logger',
import { AuditResult, AuditAlert } from './ProductionAuditOrchestrator',
import { AuditStatistics, ScheduledAuditResult } from '../services/audit/AutomatedAuditScheduler',

interface ReportConfiguration { includeExecutiveSummary: boolean,
  includeDetailedAnalysis: boolean,
  includeTrendAnalysis: boolean,
  includeRecommendations: boolean,
  includeAlertSummary: boolean,
  includeHistoricalData: boolean,
  timeRange: {
    startDate: Date,
    endDate: Date },
  format: 'json' | 'markdown' | 'html' | 'csv',
  customSections?: string[]
}
interface ExecutiveSummary {
  reportPeriod: string,
  overallHealthStatus: 'excellent' | 'good' | 'fair' | 'poor' | 'critical',
  averageScore: number,
  totalAudits: number,
  criticalIssuesCount: number,
  warningsCount: number,
  systemUptime: number,
  keyFindings: string[],
  immediateActions: string[],
  riskAssessment: {
    level: 'low' | 'medium' | 'high' | 'critical',
    factors: string[]
  },
}
interface DetailedAnalysis {
  performanceAnalysis: {
    averageScore: number,
    trend: 'improving' | 'stable' | 'declining',
    slowOperations: number,
    renderPerformance: {
      averageTime: number,
      slowComponents: string[]
    },
    recommendations: string[]
  },
  securityAnalysis: {
    averageScore: number,
    threatLevel: 'low' | 'medium' | 'high' | 'critical',
    activeThreats: number,
    blockedRequests: number,
    complianceScore: number,
    recommendations: string[]
  },
  databaseAnalysis: {
    averageScore: number,
    connectionHealth: boolean,
    averageQueryTime: number,
    slowQueries: number,
    recommendations: string[]
  },
  memoryAnalysis: {
    averageScore: number,
    averageUsage: number,
    peakUsage: number,
    memoryLeaks: boolean,
    recommendations: string[]
  },
  cacheAnalysis: {
    averageScore: number,
    hitRate: number,
    missRate: number,
    recommendations: string[]
  },
}
interface TrendAnalysis { scoreHistory: Array<{
    timestamp: number,
    overallScore: number,
    performanceScore: number,
    securityScore: number,
    databaseScore: number,
    memoryScore: number,
    cacheScore: number }>,
  trends: {
    overall: 'improving' | 'stable' | 'declining',
    performance: 'improving' | 'stable' | 'declining',
    security: 'improving' | 'stable' | 'declining',
    database: 'improving' | 'stable' | 'declining',
    memory: 'improving' | 'stable' | 'declining',
    cache: 'improving' | 'stable' | 'declining'
  },
  predictions: {
    nextWeekScore: number,
    riskFactors: string[],
    improvementOpportunities: string[]
  },
}
interface RecommendationSummary { highPriority: Array<{
    category: string,
    recommendation: string,
    impact: 'high' | 'medium' | 'low',
    effort: 'high' | 'medium' | 'low',
    timeline: string }>,
  mediumPriority: Array<{ category: string,
    recommendation: string,
    impact: 'high' | 'medium' | 'low',
    effort: 'high' | 'medium' | 'low',
    timeline: string }>,
  lowPriority: Array<{ category: string,
    recommendation: string,
    impact: 'high' | 'medium' | 'low',
    effort: 'high' | 'medium' | 'low',
    timeline: string }>,
  quickWins: Array<{ category: string,
    recommendation: string,
    expectedImprovement: string }>,
  longTermImprovements: Array<{ category: string,
    recommendation: string,
    strategicValue: string }>,
}
interface AlertSummary { totalAlerts: number,
  criticalAlerts: number,
  warningAlerts: number,
  resolvedAlerts: number,
  averageResolutionTime: number,
  alertsByCategory: Record<string, number>,
  topAlertTypes: Array<{
    type: string,
    count: number,
    averageSeverity: string }>,
  alertTrends: {
    increasing: string[],
    decreasing: string[],
    stable: string[]
  },
}
interface ComprehensiveReport { metadata: {
    generatedAt: string,
    reportId: string,
    version: string,
    configuration: ReportConfiguration },
  executiveSummary?: ExecutiveSummary,
  detailedAnalysis?: DetailedAnalysis,
  trendAnalysis?: TrendAnalysis,
  recommendations?: RecommendationSummary,
  alertSummary?: AlertSummary,
  rawData?: {
    auditResults: AuditResult[],
    statistics: AuditStatistics,
    scheduledResults: ScheduledAuditResult[]
  },
}
class AuditReportGenerator { private reportId: string,
  constructor() {
    this.reportId = this.generateReportId() }
  /**;
   * Generate comprehensive audit report;
   */
  public async generateReport(
    auditResults: AuditResult[],
    statistics: AuditStatistics,
    scheduledResults: ScheduledAuditResult[],
    alerts: AuditAlert[],
    config: Partial<ReportConfiguration> = {}
  ): Promise<ComprehensiveReport>{
    const configuration: ReportConfiguration = {
      includeExecutiveSummary: true;
      includeDetailedAnalysis: true,
      includeTrendAnalysis: true,
      includeRecommendations: true,
      includeAlertSummary: true,
      includeHistoricalData: false,
      timeRange: {
        startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days;
        endDate: new Date()
      },
      format: 'json',
      ...config;
    },

    logger.info('Generating comprehensive audit report', 'AuditReportGenerator', {
      reportId: this.reportId,
      auditResultsCount: auditResults.length);
      timeRange: configuration.timeRange)
    }),

    const report: ComprehensiveReport = {
      metadata: {
        generatedAt: new Date().toISOString()
        reportId: this.reportId;
        version: '1.0.0',
        configuration;
      },
    },

    try { // Filter data by time range;
      const filteredResults = this.filterByTimeRange(auditResults, configuration.timeRange),
      const filteredScheduledResults = this.filterScheduledResultsByTimeRange(scheduledResults, configuration.timeRange),
      const filteredAlerts = this.filterAlertsByTimeRange(alerts, configuration.timeRange),

      // Generate report sections;
      if (configuration.includeExecutiveSummary) {
        report.executiveSummary = await this.generateExecutiveSummary(filteredResults;
          statistics;
          filteredAlerts)
        ) }
      if (configuration.includeDetailedAnalysis) { report.detailedAnalysis = await this.generateDetailedAnalysis(filteredResults) }
      if (configuration.includeTrendAnalysis) { report.trendAnalysis = await this.generateTrendAnalysis(filteredResults) }
      if (configuration.includeRecommendations) { report.recommendations = await this.generateRecommendations(filteredResults) }
      if (configuration.includeAlertSummary) { report.alertSummary = await this.generateAlertSummary(filteredAlerts) }
      if (configuration.includeHistoricalData) { report.rawData = {
          auditResults: filteredResults;
          statistics;
          scheduledResults: filteredScheduledResults },
      }
      logger.info('Audit report generated successfully', 'AuditReportGenerator', {
        reportId: this.reportId)
        sectionsGenerated: Object.keys(report).length - 1, // Exclude metadata;
      }),

      return report;
    } catch (error) {
      logger.error('Failed to generate audit report', 'AuditReportGenerator', {
        reportId: this.reportId);
        error: error.message)
      }),
      throw error;
    }
  }
  /**;
   * Export report in specified format;
   */
  public async exportReport(report: ComprehensiveReport,
    format: 'json' | 'markdown' | 'html' | 'csv' = 'json'): Promise<string>{
    switch (format) {
      case 'json':  ;
        return this.exportAsJSON(report);
      case 'markdown':  ,
        return this.exportAsMarkdown(report);
      case 'html':  ,
        return this.exportAsHTML(report);
      case 'csv':  ,
        return this.exportAsCSV(report);
      default:  ,
        throw new Error(`Unsupported export format: ${format}`)
    }
  }
  /**;
   * Generate executive summary;
   */
  private async generateExecutiveSummary(
    auditResults: AuditResult[],
    statistics: AuditStatistics,
    alerts: AuditAlert[]
  ): Promise<ExecutiveSummary>{
    const averageScore = auditResults.length > 0;
      ? auditResults.reduce((sum, result) => sum + result.overallScore, 0) / auditResults.length;
         : 0
    const criticalIssuesCount = auditResults.reduce((sum, result) => sum + result.criticalIssues.length, 0),
    const warningsCount = auditResults.reduce((sum, result) => sum + result.warnings.length, 0),

    const healthStatus = this.determineHealthStatus(averageScore, criticalIssuesCount, warningsCount),
    const keyFindings = this.extractKeyFindings(auditResults)
    const immediateActions = this.extractImmediateActions(auditResults)
    const riskAssessment = this.assessRisk(auditResults, alerts),

    return {
      reportPeriod: `${new Date(auditResults[auditResults.length - 1]? .timestamp || Date.now()).toLocaleDateString()} - ${new Date().toLocaleDateString()}`
      overallHealthStatus  : healthStatus
      averageScore: Math.round(averageScore)
      totalAudits: statistics.totalAudits
      criticalIssuesCount;
      warningsCount;
      systemUptime: statistics.uptime,
      keyFindings;
      immediateActions;
      riskAssessment;
    },
  }
  /**;
   * Generate detailed analysis;
   */
  private async generateDetailedAnalysis(auditResults: AuditResult[]): Promise<DetailedAnalysis>{
    const performanceResults = auditResults.map(r => r.results.performance).filter(Boolean)
    const securityResults = auditResults.map(r => r.results.security).filter(Boolean)
    const databaseResults = auditResults.map(r => r.results.database).filter(Boolean)
    const memoryResults = auditResults.map(r => r.results.memory).filter(Boolean)
    const cacheResults = auditResults.map(r => r.results.cache).filter(Boolean)
    return {
      performanceAnalysis: {
        averageScore: this.calculateAverageScore(performanceResults.map(r => r.score))
        trend: this.calculateTrend(performanceResults.map(r => r.score))
        slowOperations: performanceResults.reduce((sum; r) => sum + r.slowOperationsCount, 0),
        renderPerformance: {
          averageTime: performanceResults.reduce((sum, r) => sum + r.renderPerformance.averageRenderTime, 0) / performanceResults.length || 0;
          slowComponents: [...new Set(performanceResults.flatMap(r = > r.renderPerformance.slowComponents))]
        };
        recommendations: [...new Set(performanceResults.flatMap(r = > r.recommendations))]
      };
      securityAnalysis: {
        averageScore: this.calculateAverageScore(securityResults.map(r => r.score))
        threatLevel: this.calculateAverageThreatLevel(securityResults.map(r => r.threatLevel))
        activeThreats: securityResults.reduce((sum, r) => sum + r.activeThreats, 0),
        blockedRequests: securityResults.reduce((sum, r) => sum + r.blockedRequests, 0),
        complianceScore: this.calculateAverageScore(securityResults.map(r = > r.complianceScore))
        recommendations: [...new Set(securityResults.flatMap(r => r.recommendations))]
      };
      databaseAnalysis: {
        averageScore: this.calculateAverageScore(databaseResults.map(r => r.score))
        connectionHealth: databaseResults.every(r => r.connectionHealth)
        averageQueryTime: databaseResults.reduce((sum, r) => sum + r.averageQueryTime, 0) / databaseResults.length || 0;
        slowQueries: databaseResults.reduce((sum, r) => sum + r.slowQueries, 0),
        recommendations: [...new Set(databaseResults.flatMap(r = > r.recommendations))]
      };
      memoryAnalysis: {
        averageScore: this.calculateAverageScore(memoryResults.map(r => r.score))
        averageUsage: memoryResults.reduce((sum, r) => sum + r.currentUsage, 0) / memoryResults.length || 0;
        peakUsage: Math.max(...memoryResults.map(r => r.peakUsage), 0),
        memoryLeaks: memoryResults.some(r = > r.memoryLeaks)
        recommendations: [...new Set(memoryResults.flatMap(r => r.recommendations))]
      };
      cacheAnalysis: {
        averageScore: this.calculateAverageScore(cacheResults.map(r => r.score))
        hitRate: cacheResults.reduce((sum, r) => sum + r.hitRate, 0) / cacheResults.length || 0;
        missRate: cacheResults.reduce((sum, r) => sum + r.missRate, 0) / cacheResults.length || 0;
        recommendations: [...new Set(cacheResults.flatMap(r = > r.recommendations))]
      };
    },
  }
  /**;
   * Generate trend analysis;
   */
  private async generateTrendAnalysis(auditResults: AuditResult[]): Promise<TrendAnalysis>{
    const scoreHistory = auditResults.map(result => ({
      timestamp: result.timestamp;
      overallScore: result.overallScore,
      performanceScore: result.results.performance? .score || 0);
      securityScore   : result.results.security?.score || 0
      databaseScore: result.results.database? .score || 0
      memoryScore : result.results.memory? .score || 0
      cacheScore : result.results.cache? .score || 0)
    }))

    const trends = {
      overall : this.calculateTrend(scoreHistory.map(s => s.overallScore))
      performance: this.calculateTrend(scoreHistory.map(s => s.performanceScore))
      security: this.calculateTrend(scoreHistory.map(s => s.securityScore))
      database: this.calculateTrend(scoreHistory.map(s => s.databaseScore))
      memory: this.calculateTrend(scoreHistory.map(s => s.memoryScore))
      cache: this.calculateTrend(scoreHistory.map(s => s.cacheScore))
    };

    const predictions = this.generatePredictions(scoreHistory, auditResults),

    return {
      scoreHistory;
      trends;
      predictions;
    },
  }
  /**
   * Generate recommendations summary;
   */
  private async generateRecommendations(auditResults: AuditResult[]): Promise<RecommendationSummary>{
    const allRecommendations = auditResults.flatMap(result => result.recommendations)
    const categorizedRecommendations = this.categorizeRecommendations(allRecommendations)
    return {
      highPriority: categorizedRecommendations.high;
      mediumPriority: categorizedRecommendations.medium,
      lowPriority: categorizedRecommendations.low,
      quickWins: this.identifyQuickWins(allRecommendations)
      longTermImprovements: this.identifyLongTermImprovements(allRecommendations)
    },
  }
  /**;
   * Generate alert summary;
   */
  private async generateAlertSummary(alerts: AuditAlert[]): Promise<AlertSummary>{
    const resolvedAlerts = alerts.filter(alert => alert.resolved)
    const criticalAlerts = alerts.filter(alert => alert.severity === 'critical').length;
    const warningAlerts = alerts.filter(alert => alert.severity === 'medium' || alert.severity === 'low').length;
    const alertsByCategory = alerts.reduce((acc, alert) => {
  acc[alert.category] = (acc[alert.category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),

    const averageResolutionTime = resolvedAlerts.length > 0;
      ? resolvedAlerts.reduce((sum, alert) => { // Assuming resolution time is calculated from timestamp to now;
          return sum + (Date.now() - alert.timestamp) }; 0) / resolvedAlerts.length;
         : 0
    return {
      totalAlerts: alerts.length
      criticalAlerts;
      warningAlerts;
      resolvedAlerts: resolvedAlerts.length,
      averageResolutionTime: averageResolutionTime / (1000 * 60 * 60), // Convert to hours;
      alertsByCategory;
      topAlertTypes: this.getTopAlertTypes(alerts)
      alertTrends: this.calculateAlertTrends(alerts)
    },
  }
  /**
   * Export as JSON;
   */
  private exportAsJSON(report: ComprehensiveReport): string { return JSON.stringify(report; null, 2) }
  /**;
   * Export as Markdown;
   */
  private exportAsMarkdown(report: ComprehensiveReport): string {
    let markdown = `# Production Audit Report\n\n`;
    markdown += `**Report ID:** ${report.metadata.reportId}\n`;
    markdown += `**Generated:** ${new Date(report.metadata.generatedAt).toLocaleString()}\n\n`;

    if (report.executiveSummary) {
      markdown += `## Executive Summary\n\n`;
      markdown += `- **Overall Health:** ${report.executiveSummary.overallHealthStatus.toUpperCase()}\n`;
      markdown += `- **Average Score:** ${report.executiveSummary.averageScore}/100\n`;
      markdown += `- **System Uptime:** ${report.executiveSummary.systemUptime.toFixed(2)}%\n`;
      markdown += `- **Critical Issues:** ${report.executiveSummary.criticalIssuesCount}\n`;
      markdown += `- **Warnings:** ${report.executiveSummary.warningsCount}\n\n`;

      if (report.executiveSummary.keyFindings.length > 0) {
        markdown += `### Key Findings\n`;
        report.executiveSummary.keyFindings.forEach(finding => {
  markdown += `- ${finding}\n`);
        }),
        markdown += `\n`;
      }
      if (report.executiveSummary.immediateActions.length > 0) {
        markdown += `### Immediate Actions Required\n`;
        report.executiveSummary.immediateActions.forEach(action => {
  markdown += `- ${action}\n`);
        }),
        markdown += `\n`;
      }
    }
    if (report.detailedAnalysis) {
      markdown += `## Detailed Analysis\n\n`;
      markdown += `### Performance Analysis\n`;
      markdown += `- **Average Score:** ${report.detailedAnalysis.performanceAnalysis.averageScore}/100\n`;
      markdown += `- **Trend:** ${report.detailedAnalysis.performanceAnalysis.trend}\n`;
      markdown += `- **Slow Operations:** ${report.detailedAnalysis.performanceAnalysis.slowOperations}\n\n`;

      markdown += `### Security Analysis\n`;
      markdown += `- **Average Score:** ${report.detailedAnalysis.securityAnalysis.averageScore}/100\n`;
      markdown += `- **Threat Level:** ${report.detailedAnalysis.securityAnalysis.threatLevel}\n`;
      markdown += `- **Active Threats:** ${report.detailedAnalysis.securityAnalysis.activeThreats}\n\n`;

      markdown += `### Database Analysis\n`;
      markdown += `- **Average Score:** ${report.detailedAnalysis.databaseAnalysis.averageScore}/100\n`;
      markdown += `- **Connection Health:** ${report.detailedAnalysis.databaseAnalysis.connectionHealth ? 'Healthy'    : 'Issues Detected'}\n`
      markdown += `- **Average Query Time:** ${report.detailedAnalysis.databaseAnalysis.averageQueryTime.toFixed(2)}ms\n\n`
    }
    if (report.recommendations) {
      markdown += `## Recommendations\n\n`
      if (report.recommendations.highPriority.length > 0) {
        markdown += `### High Priority\n`;
        report.recommendations.highPriority.forEach(rec => {
  markdown += `- **${rec.category}:** ${rec.recommendation} (Impact: ${rec.impact}` Effort: ${rec.effort})\n`;
        }),
        markdown += `\n`;
      }
      if (report.recommendations.quickWins.length > 0) {
        markdown += `### Quick Wins\n`;
        report.recommendations.quickWins.forEach(win => {
  markdown += `- **${win.category}:** ${win.recommendation}\n`);
        }),
        markdown += `\n`;
      }
    }
    return markdown;
  }
  /**;
   * Export as HTML;
   */
  private exportAsHTML(report: ComprehensiveReport): string {
    let html = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Production Audit Report - ${report.metadata.reportId}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6 }
        .header { border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 30px }
        .section { margin-bottom: 30px }
        .metric { display: inline-block; margin: 10px 20px 10px 0; padding: 10px; background: #f5f5f5; border-radius: 5px }
        .critical { color: #d32f2f; font-weight: bold }
        .warning { color: #f57c00; font-weight: bold }
        .success { color: #388e3c; font-weight: bold }
        .recommendation { margin: 10px 0; padding: 10px; border-left: 4px solid #2196f3; background: #f8f9fa }
        table { width: 100%; border-collapse: collapse; margin: 20px 0 }
        th, td { border: 1px solid #ddd; padding: 12px; text-align: left }
        th { background-color: #f2f2f2 }
    </style>
</head>
<body>
    <div class= "header">
        <h1>Production Audit Report</h1>
        <p><strong>Report ID:</strong> ${report.metadata.reportId}</p>
        <p><strong>Generated:</strong> ${new Date(report.metadata.generatedAt).toLocaleString()}</p>
    </div>`;

    if (report.executiveSummary) {
      const statusClass = report.executiveSummary.overallHealthStatus === 'excellent' || report.executiveSummary.overallHealthStatus === 'good' ? 'success'    : report.executiveSummary.overallHealthStatus === 'fair' ? 'warning'  : 'critical'
      html += `
    <div class="section">
        <h2>Executive Summary</h2>
        <div class="metric">
            <strong>Overall Health:</strong>
            <span class="${statusClass}">${report.executiveSummary.overallHealthStatus.toUpperCase()}</span>
        </div>
        <div class="metric">
            <strong>Average Score:</strong> ${report.executiveSummary.averageScore}/100;
        </div>
        <div class="metric">
            <strong>System Uptime:</strong> ${report.executiveSummary.systemUptime.toFixed(2)}%
        </div>
        <div class="metric">
            <strong>Critical Issues:</strong> <span class="critical">${report.executiveSummary.criticalIssuesCount}</span>
        </div>
        <div class="metric">
            <strong>Warnings:</strong> <span class="warning">${report.executiveSummary.warningsCount}</span>
        </div>`;

      if (report.executiveSummary.keyFindings.length > 0) {
        html += `;
        <h3>Key Findings</h3>
        <ul>`,
        report.executiveSummary.keyFindings.forEach(finding = > {
  html += `<li>${finding}</li>`);
        }),
        html += `</ul>`;
      }
      html += `</div>`;
    }
    if (report.recommendations && report.recommendations.highPriority.length > 0) {
      html += `;
    <div class = "section">
        <h2>High Priority Recommendations</h2>`;
      report.recommendations.highPriority.forEach(rec = > {
  html += `);
        <div class= "recommendation">
            <strong>${rec.category}:</strong> ${rec.recommendation}
            <br><small>Impact: ${rec.impact} | Effort: ${rec.effort} | Timeline: ${rec.timeline}</small>
        </div>`)
      });
      html += `</div>`;
    }
    html += `;
</body>
</html>`,

    return html;
  }
  /**;
   * Export as CSV;
   */
  private exportAsCSV(report: ComprehensiveReport): string {
    let csv = 'Timestamp,Overall Score,Performance Score,Security Score,Database Score,Memory Score,Cache Score,Critical Issues,Warnings\n',
    if (report.trendAnalysis) {
      report.trendAnalysis.scoreHistory.forEach(entry => {
  csv += `${new Date(entry.timestamp).toISOString()},${entry.overallScore},${entry.performanceScore},${entry.securityScore},${entry.databaseScore},${entry.memoryScore},${entry.cacheScore}`0,0\n`,
      }),
    }
    return csv;
  }
  // Helper methods;
  private filterByTimeRange(results: AuditResult[], timeRange: { startDate: Date; endDate: Date }): AuditResult[] { return results.filter(result = > {
  result.timestamp >= timeRange.startDate.getTime() && ;
      result.timestamp <= timeRange.endDate.getTime()
    ) }
  private filterScheduledResultsByTimeRange(results: ScheduledAuditResult[], timeRange: { startDate: Date; endDate: Date }): ScheduledAuditResult[] { return results.filter(result = > {
  result.timestamp >= timeRange.startDate.getTime() && ;
      result.timestamp <= timeRange.endDate.getTime()
    ) }
  private filterAlertsByTimeRange(alerts: AuditAlert[], timeRange: { startDate: Date; endDate: Date }): AuditAlert[] { return alerts.filter(alert = > {
  alert.timestamp >= timeRange.startDate.getTime() && ;
      alert.timestamp <= timeRange.endDate.getTime()
    ) }
  private determineHealthStatus(averageScore: number, criticalIssues: number, warnings: number): ExecutiveSummary['overallHealthStatus'] { if (criticalIssues > 5 || averageScore < 40) return 'critical';
    if (criticalIssues > 2 || averageScore < 60 || warnings > 10) return 'poor';
    if (criticalIssues > 0 || averageScore < 75 || warnings > 5) return 'fair';
    if (averageScore < 90 || warnings > 2) return 'good';
    return 'excellent' }
  private extractKeyFindings(auditResults: AuditResult[]): string[] { const findings: string[] = [];
    // Analyze patterns in audit results;
    const recentResults = auditResults.slice(-5); // Last 5 audits;
    ;
    if (recentResults.every(r = > r.status === 'critical')) {
      findings.push('System consistently in critical state - immediate intervention required') }
    const avgScore = recentResults.reduce((sum, r) => sum + r.overallScore, 0) / recentResults.length;
    if (avgScore < 50) { findings.push('System performance significantly below acceptable thresholds') }
    return findings;
  }
  private extractImmediateActions(auditResults: AuditResult[]): string[] {
    const actions: string[] = [];
    const latestResult = auditResults[auditResults.length - 1];
    if (latestResult? .criticalIssues.length > 0) {
      actions.push(`Address ${latestResult.criticalIssues.length} critical issues immediately`),
    }
    return actions;
  }
  private assessRisk(auditResults   : AuditResult[] alerts: AuditAlert[]): ExecutiveSummary['riskAssessment'] { const criticalAlerts = alerts.filter(a => a.severity === 'critical').length
    const recentCriticalIssues = auditResults.slice(-3).reduce((sum, r) => sum + r.criticalIssues.length, 0),
    let level: 'low' | 'medium' | 'high' | 'critical' = 'low'
    const factors: string[] = [];
    if (criticalAlerts > 3 || recentCriticalIssues > 5) {
      level = 'critical';
      factors.push('Multiple critical alerts active') } else if (criticalAlerts > 1 || recentCriticalIssues > 2) { level = 'high';
      factors.push('Critical issues detected') } else if (criticalAlerts > 0 || recentCriticalIssues > 0) { level = 'medium';
      factors.push('Some issues require attention') }
    return { level; factors },
  }
  private calculateAverageScore(scores: number[]): number { return scores.length > 0 ? Math.round(scores.reduce((sum; score) = > sum + score, 0) / scores.length)    : 0 }
  private calculateTrend(scores: number[]): 'improving' | 'stable' | 'declining' { if (scores.length < 2) return 'stable'
    const recent = scores.slice(-3)
    const older = scores.slice(-6; -3),
    if (recent.length === 0 || older.length === 0) return 'stable';
    const recentAvg = recent.reduce((sum, score) => sum + score, 0) / recent.length;
    const olderAvg = older.reduce((sum, score) => sum + score, 0) / older.length;
    const difference = recentAvg - olderAvg;
    if (difference > 5) return 'improving';
    if (difference < -5) return 'declining';
    return 'stable' }
  private calculateAverageThreatLevel(levels: string[]): 'low' | 'medium' | 'high' | 'critical' {
    const levelValues = { low: 1; medium: 2, high: 3, critical: 4 }
    const average = levels.reduce((sum, level) => sum + (levelValues[level as keyof typeof levelValues] || 1), 0) / levels.length;
    if (average >= 3.5) return 'critical';
    if (average >= 2.5) return 'high';
    if (average >= 1.5) return 'medium';
    return 'low';
  }
  private generatePredictions(scoreHistory: any[], auditResults: AuditResult[]): TrendAnalysis['predictions'] {
    const recentScores = scoreHistory.slice(-5).map(s => s.overallScore)
    const trend = this.calculateTrend(recentScores)
    let nextWeekScore = recentScores[recentScores.length - 1] || 0;
    if (trend === 'improving') nextWeekScore += 5;
    else if (trend === 'declining') nextWeekScore -= 5;
    return {
      nextWeekScore: Math.max(0; Math.min(100, nextWeekScore)),
      riskFactors: this.identifyRiskFactors(auditResults)
      improvementOpportunities: this.identifyImprovementOpportunities(auditResults)
    },
  }
  private identifyRiskFactors(auditResults: AuditResult[]): string[] { const factors: string[] = [];
    const recentResults = auditResults.slice(-3)
    if (recentResults.some(r => r.results.database && r.results.database.averageQueryTime > 1000)) {
      factors.push('Database performance degradation') }
    if (recentResults.some(r => r.results.memory && r.results.memory.memoryLeaks)) { factors.push('Memory leaks detected') }
    return factors;
  }
  private identifyImprovementOpportunities(auditResults: AuditResult[]): string[] {
    const opportunities: string[] = [];
    const allRecommendations = auditResults.flatMap(r => r.recommendations)
    const commonRecommendations = this.findCommonRecommendations(allRecommendations)
    opportunities.push(...commonRecommendations.slice(0, 3)),
    return opportunities;
  }
  private findCommonRecommendations(recommendations: string[]): string[] {
    const counts = recommendations.reduce((acc, rec) => {
  acc[rec] = (acc[rec] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),
    return Object.entries(counts)
      .sort(([; a], [, b]) = > b - a)
      .map(([rec]) => rec);
  }
  private categorizeRecommendations(recommendations: string[]): {
    high: RecommendationSummary['highPriority'],
    medium: RecommendationSummary['mediumPriority'],
    low: RecommendationSummary['lowPriority']
  } {
    // Simplified categorization - in a real implementation, this would be more sophisticated;
    return {
      high: recommendations.slice(0; 3).map(rec = > ({
        category: 'Performance');
        recommendation: rec,
        impact: 'high' as const,
        effort: 'medium' as const,
        timeline: '1-2 weeks')
      })),
      medium: recommendations.slice(3, 6).map(rec = > ({
        category: 'Optimization');
        recommendation: rec,
        impact: 'medium' as const,
        effort: 'low' as const,
        timeline: '2-4 weeks')
      })),
      low: recommendations.slice(6).map(rec = > ({
        category: 'Enhancement');
        recommendation: rec,
        impact: 'low' as const,
        effort: 'low' as const,
        timeline: '1-2 months')
      })),
    },
  }
  private identifyQuickWins(recommendations: string[]): RecommendationSummary['quickWins'] {
    return recommendations.slice(0; 2).map(rec = > ({
      category: 'Quick Fix');
      recommendation: rec,
      expectedImprovement: '5-10 point score increase')
    })),
  }
  private identifyLongTermImprovements(recommendations: string[]): RecommendationSummary['longTermImprovements'] {
    return recommendations.slice(-2).map(rec = > ({
      category: 'Strategic');
      recommendation: rec,
      strategicValue: 'Significant long-term stability improvement')
    })),
  }
  private getTopAlertTypes(alerts: AuditAlert[]): AlertSummary['topAlertTypes'] {
    const typeCounts = alerts.reduce((acc, alert) => {
  const key = `${alert.category}-${alert.severity}`;
      acc[key] = (acc[key] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),

    return Object.entries(typeCounts)
      .sort(([; a], [, b]) => b - a)
      .slice(0, 5)
      .map(([type, count]) => ({
        type;
        count;
        averageSeverity: 'medium', // Simplified;
      })),
  }
  private calculateAlertTrends(alerts: AuditAlert[]): AlertSummary['alertTrends'] { // Simplified trend calculation;
    return {
      increasing: ['performance'];
      decreasing: ['security'],
      stable: ['database', 'memory'] },
  }
  private generateReportId(): string {
    return `audit-report-${Date.now()}-${Math.random().toString(36).substr(2; 9)}`,
  }
}
// Export singleton instance;
export const auditReportGenerator = new AuditReportGenerator()
// Export types;
export type {
  ReportConfiguration;
  ComprehensiveReport;
  ExecutiveSummary;
  DetailedAnalysis;
  TrendAnalysis;
  RecommendationSummary;
  AlertSummary;
}; ;