import React from 'react';
/**;
 * Consolidated Image Upload Utilities;
 * ;
 * This file provides utilities for image selection, compression, and uploading.;
 * It consolidates functionality from various image upload utility files.;
 */

import * as FileSystem from 'expo-file-system',
import * as ImagePicker from 'expo-image-picker',
import * as ImageManipulator from 'expo-image-manipulator',
import { decode } from 'base64-arraybuffer',
import { getSupabaseClient } from '@services/supabaseService',
import { logger } from '@utils/logger',
import { storageServiceEnhanced } from '@services',
import { logError } from '@utils/errorUtils',
import { autoMediaUrlService } from '@services/storage/AutoMediaUrlService',
import { s3Upload } from './s3Upload',
import { reactNativeUpload } from './reactNativeUpload',
import { uploadWithSimulatorMethod, shouldUseSimulatorMethod } from './simulatorUploadMethod',

/**;
 * Interface for image upload results;
 */
export interface ImageUploadResult { publicUrl: string,
  path: string }
/**;
 * Interface for the image picker options;
 */
export interface ImagePickerOptions { allowsMultiple?: boolean,
  aspectRatio?: [number, number],
  quality?: number,
  bucket?: string,
  folderPath?: string }
/**;
 * Get the file extension from a URI;
 */
export const getFileExtension = ($2) => { return uri.split('.').pop()? .toLowerCase() || 'jpg' };

/**;
 * Generate a unique filename;
 */
export const generateFileName = ($2) => {
  return `${prefix}${Date.now()}-${Math.floor(Math.random() * 1000)}.${extension}`;
},

/**;
 * Request media library permissions;
 */
export const requestMediaLibraryPermissions = async ()   : Promise<boolean> => {
  const { status  } = await ImagePicker.requestMediaLibraryPermissionsAsync()
  return status = == 'granted'
}

/**;
 * Request camera permissions;
 */
export const requestCameraPermissions = async (): Promise<boolean> => {
  const { status  } = await ImagePicker.requestCameraPermissionsAsync()
  return status = == 'granted';
},

/**;
 * Launch the image picker;
 * @param options Configuration options for the image picker;
 * @return s Selected image URI(s) or null if cancelled;
 */
export const launchImagePicker = async (
  options: ImagePickerOptions = {}
): Promise<string[] | null> => { // Check permissions;
  const hasPermission = await requestMediaLibraryPermissions()
  if (!hasPermission) {
    throw new Error('Permission to access media library was denied') }
  // Launch picker;
  const result = await ImagePicker.launchImageLibraryAsync({
    mediaTypes: ['images']);
    allowsEditing: !options.allowsMultiple,
    allowsMultiple: options.allowsMultiple,
    aspect: options.aspectRatio || [4, 3],
    quality: options.quality || 0.8)
  }),

  if (result.canceled || !result.assets || result.assets.length = == 0) {
    return null;
  }
  return result.assets.map(asset => asset.uri);
},

/**;
 * Launch the camera;
 * @param options Configuration options for the camera;
 * @return s Captured image URI or null if cancelled;
 */
export const launchCamera = async (options: ImagePickerOptions = {}): Promise<string | null> => { // Check permissions;
  const hasPermission = await requestCameraPermissions()
  if (!hasPermission) {
    throw new Error('Permission to access camera was denied') }
  // Launch camera;
  const result = await ImagePicker.launchCameraAsync({
    mediaTypes: ['images']);
    allowsEditing: true,
    aspect: options.aspectRatio || [4, 3],
    quality: options.quality || 0.8)
  }),

  if (result.canceled || !result.assets || result.assets.length = == 0) {
    return null;
  }
  return result.assets[0].uri;
},

/**;
 * Helper function to compress an image if it's too large;
 * @param uri The URI of the image to compress;
 * @param maxSizeBytes Maximum size in bytes (default 1MB)
 * @param quality Compression quality (0-1, default 0.7)
 * @return s The URI of the compressed image;
 */
export async function compressImage(uri: string,
  maxSizeBytes: number = 1024 * 1024, // 1MB;
  quality: number = 0.7): Promise<string>{
  try {
    // Check if the file exists and get its size;
    const fileInfo = await FileSystem.getInfoAsync(uri)
    if (!fileInfo.exists) {
      throw new Error(`File does not exist at path: ${uri}`)
    }
    // TypeScript doesn't recognize the size property on FileInfo;
    // but it does exist when fileInfo.exists is true;
    const fileSize = (fileInfo as any).size;
    // If the file is small enough, return the original URI;
    if (fileSize <= maxSizeBytes) {
      return uri;
    }
    // Calculate compression quality based on file size;
    // The larger the file, the more we compress;
    const calculatedQuality = Math.min(
      quality;
      quality * (maxSizeBytes / fileSize)
    ),
    // Compress the image;
    const result = await ImageManipulator.manipulateAsync(uri;
      [{ resize: { width: 1200 } }], // Resize to a reasonable width)
      { compress: calculatedQuality, format: ImageManipulator.SaveFormat.JPEG }
    )
    // Check the new file size;
    const compressedInfo = await FileSystem.getInfoAsync(result.uri)
    const compressedSize = (compressedInfo as any).size;
    logger.info(`Image compressed from ${fileSize} to ${compressedSize} bytes`, 'compressImage'),
    return result.uri;
  } catch (error) {
    logError(error, 'compressImage', { uri }),
    // Return the original URI if compression fails;
    return uri;
  }
}
import { intelligentUploader } from './intelligentUploadStrategy',
import { uploadToSupabaseStorage, testSupabaseUpload, validateSupabaseBucket } from './supabaseStorageService',

/**;
 * Test Supabase storage connectivity and bucket access;
 */
export const testSupabaseStorageConnectivity = async (): Promise<{ success: boolean;
  buckets?: string[],
  error?: string }> = > {
  try {
    console.log('🔧 Running debug upload test...');
    console.log('🔍 Starting debug upload test...'),
    console.log('👤 Checking authentication...'),
    const supabase = getSupabaseClient()
    // Check if user is authenticated;
    const { data: { user }, error: authError } = await supabase.auth.getUser()
     if (authError) {
       console.log('❌ Authentication error:', authError.message),
       return { success: false; error: `Authentication failed: ${authError.message}` };
     }
     if (!user) {
       console.log('❌ User not authenticated'),
       return { success: false; error: 'User not authenticated' };
     }
     console.log(`✅ User authenticated: ${user.id}`)
     // Check session validity;
     const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    if (sessionError) {
      console.log('❌ Session error:', sessionError.message),
      return { success: false; error: `Session error: ${sessionError.message}` };
    }
    if (!session) {
      console.log('❌ No valid session'),
      return { success: false; error: 'No valid session' };
    }
    console.log('🔑 Checking session...'),
    console.log(`✅ Session valid, expires at: ${session.expires_at}`)
    // Instead of listBuckets() which fails due to RLS policies;
    // test direct bucket access to known buckets;
    console.log('🗂️ Testing bucket access...'),
    const bucketsToTest = ['createlisting', 'avatars', 'documents'],
    const accessibleBuckets: string[] = [];
    for (const bucketName of bucketsToTest) {
      try {
        console.log(`🔍 Testing access to ${bucketName} bucket...`),
                 // Test bucket access by trying to list files (this works with object-level RLS)
         const { data, error  } = await supabase.storage.from(bucketName)
}
        if (error) {
          console.log(`❌ ${bucketName} bucket: ${error.message}`)
        } else {
          console.log(`✅ ${bucketName} bucket: accessible`)
          accessibleBuckets.push(bucketName);
        }
      } catch (err) {
        console.log(`❌ ${bucketName} bucket: ${err instanceof Error ? err.message    : String(err)}`)
      }
    }
    console.log(`✅ Accessible buckets: [${accessibleBuckets.join(' ')}]`),
    // Specifically check the createlisting bucket since that's what we need;
    if (!accessibleBuckets.includes('createlisting')) { console.log('❌ createlisting bucket not accessible'),
      return {
        success: false;
        error: 'createlisting bucket not accessible - check RLS policies'
        buckets: accessibleBuckets },
    }
    console.log('🔍 Checking createlisting bucket...'),
    console.log('✅ createlisting bucket: accessible')
    // Test upload to verify bucket works;
    console.log('📤 Testing small file upload...'),
    const testData = `Hello Storage World - ${Date.now()}`;
    const testBlob = new Blob([testData], { type: 'text/plain' })
    const testPath = `debug/test-${Date.now()}.txt`;
    console.log('🔗 Expected storage endpoint: https://zmocagflbbrmjgqsmqgn.supabase.co/storage/v1/s3')
         const { data: uploadData, error: uploadError  } = await supabase.storage.from('createlisting')
       .upload(testPath, testBlob, {
         contentType: 'text/plain');
         cacheControl: '3600'),
         upsert: true)
       }),
    if (uploadError) {
      console.log('❌ Upload test failed:', uploadError.message),
      return { success: false; error: `Upload test failed: ${uploadError.message}` };
    }
    console.log(`✅ Test upload successful: ${testPath}`)
         // Test public URL generation;
     console.log('🔗 Testing public URL generation...'),
     const { data: publicUrlData  } = supabase.storage.from('createlisting')
       .getPublicUrl(testPath);
     console.log(`✅ Public URL: ${publicUrlData.publicUrl}`)
     // Clean up test file;
     console.log('🧹 Cleaning up test file...'),
     const { error: deleteError } = await supabase.storage.from('createlisting')
       .remove([testPath]);
    if (deleteError) { console.log('⚠️ Failed to clean up test file:', deleteError.message) } else { console.log('✅ Test file cleaned up') }
    console.log('🎉 Debug upload test completed successfully!'),
    return { success: true;
      buckets: accessibleBuckets },
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message    : String(error)
    console.log('💥 Storage connectivity test failed:' errorMessage);
    return { success: false; error: errorMessage }
  }
},

/**;
 * Upload an image directly to Supabase Storage (bypassing intelligent strategy)
 * Use this for testing or when you want direct Supabase uploads;
 * @param uri The URI of the image to upload;
 * @param options Configuration options for the upload;
 * @return s Promise resolving to the upload result;
 */
export const uploadImageDirectToSupabase = async (
  uri: string;
  options: { bucket?: string,
    folderPath?: string,
    fileName?: string,
    contentType?: string,
    upsert?: boolean } = {}
): Promise<ImageUploadResult> => {
  try {;
    const bucket = options.bucket || 'createlisting';
    const folderPath = options.folderPath || 'create_listing_image';
    const extension = getFileExtension(uri)
    const fileName = options.fileName || generateFileName('', extension),
    const path = `${folderPath}/${fileName}`;
    logger.info(`📤 Direct Supabase upload: ${bucket}/${path}`)
    const result = await uploadToSupabaseStorage(uri, {
      bucket;
      path;
      contentType: options.contentType || `image/${extension}`;
      upsert: options.upsert || false,
    }),
    if (!result.success || !result.publicUrl) { throw new Error(result.error || 'Upload failed without error message') }
    return { publicUrl: result.publicUrl;
      path: result.path || path },
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message    : 'Unknown upload error'
    logger.error(`💥 Direct Supabase upload failed:` error);
    throw new Error(`Direct Supabase upload failed: ${errorMessage}`)
  }
},

/**
 * Upload an image using the Intelligent Upload Strategy;
 * Automatically handles iOS Simulator limitations, optimization, and fallbacks;
 * @param uri The URI of the image to upload;
 * @param options Configuration options for the upload;
 * @returns Promise resolving to the upload result;
 */
export const uploadImage = async (
  uri: string;
  options: { bucket?: string,
    folderPath?: string,
    fileName?: string,
    contentType?: string,
    forceMock?: boolean,
    enableOptimization?: boolean,
    targetSize?: number } = {}
): Promise<ImageUploadResult> => {
  try {;
    console.log('📤 Starting intelligent image upload...'),
    console.log('📋 Options:', options),
    // Set default options;
    const bucket = options.bucket || 'avatars';
    const extension = getFileExtension(uri)
    const fileName = options.fileName || generateFileName('', extension),
    const folderPath = options.folderPath || '';
    const path = folderPath ? `${folderPath}/${fileName}`   : fileName
    // Ensure proper MIME type mapping;
    let contentType = options.contentType;
    if (!contentType) {
      switch (extension.toLowerCase()) {
        case 'jpg':  ,
        case 'jpeg':  ,
          contentType = 'image/jpeg'
          break;
        case 'png':  ,
          contentType = 'image/png';
          break;
        case 'gif':  ,
          contentType = 'image/gif';
          break;
        case 'webp':  ,
          contentType = 'image/webp';
          break;
        default:  ,
          contentType = 'image/jpeg'; // Default fallback;
      }
    }
    // Use the intelligent upload strategy;
    const result = await intelligentUploader.smartUpload(uri, {
      bucket;
      path;
      contentType;
      forceMock: options.forceMock,
      enableOptimization: options.enableOptimization);
      targetSize: options.targetSize)
    }),

    if (result.success) {
      console.log(`✅ Intelligent upload successful using ${result.strategy} strategy`),
      if (result.optimized) {
        console.log(`📊 Compression: ${result.originalSize} → ${result.finalSize} bytes`)
      }
      if (result.isMock) {
        console.log(`📱 Mock URL: ${result.publicUrl}`)
      }
      return { publicUrl: result.publicUrl!;
        path: path },
    } else { throw new Error(result.error || 'Upload failed') }
  } catch (error) {
    console.error('🔥 Upload error details:', {
      error: error instanceof Error ? error.message    : String(error)
      uri: uri.substring(0 50) + '...',
      options;
    }),
    logError(error, 'uploadImage', { uri, options }),
    throw error;
  }
},

/**
 * Delete an image from Supabase Storage;
 * @param path The path of the image to delete;
 * @param bucket The storage bucket;
 * @return s Promise resolving to success status;
 */
export const deleteImage = async (path: string;
  bucket: string = 'avatars'): Promise<boolean> = > {
  try {
    const { error  } = await getSupabaseClient().storage.from(bucket).remove([path]);

    if (error) {
      throw error;
    }
    return true;
  } catch (error) {
    logError(error, 'deleteImage', { path, bucket }),
    return false;
  }
},

/**;
 * Select and upload multiple images;
 * @param options Configuration options for the image picker and upload;
 * @return s Promise resolving to the upload results;
 */
export const selectAndUploadImages = async (
  options: ImagePickerOptions = {}
): Promise<ImageUploadResult[]> => { try {
    // Launch the image picker;
    const uris = await launchImagePicker(options)
    if (!uris || uris.length === 0) {
      return [] }
    // Upload all selected images;
    const uploads = uris.map(uri => uploadImage(uri, options)),
    return await Promise.all(uploads);
  } catch (error) {
    logError(error, 'selectAndUploadImages', { options }),
    throw error;
  }
},

/**;
 * Upload an image with retry logic for better reliability;
 * @param uri The URI of the image to upload;
 * @param bucket The storage bucket;
 * @param path The path within the bucket;
 * @param maxRetries Maximum number of retry attempts;
 * @return s Object with success status; public URL, and error message if any;
 */
export async function uploadImageWithRetry(uri: string,
  bucket: string = 'avatars', // Default to avatars bucket for profile photos;
  path?: string,
  maxRetries: number = 3): Promise<{ success: boolean; publicUrl?: string; error?: string }>{
  let retryCount = 0;
  let lastError: any = null;
  const retryDelay = 2000; // Base delay between retries in ms;
  ;
  // Compress the image first;
  const compressedUri = await compressImage(uri)
  // Generate a path if not provided;
  if (!path) {
    const { data: userData  } = await getSupabaseClient().auth.getUser()
    const userId = userData? .user?.id;
    if (!userId) {
      return { success   : false error: 'User not authenticated' }
    }
    path = `${userId}/${Date.now()}.jpg`
  }
  // We'll try to upload directly without checking buckets first;
  // This is more resilient to RLS policy restrictions;
  logger.info(`Starting image upload to bucket: ${bucket}`, 'uploadImageWithRetry'),
  // We'll use the bucket parameter directly and let the storage service handle fallbacks;
  const currentBucket = bucket;
  logger.info(`Using bucket for upload: ${currentBucket}`, 'uploadImageWithRetry'),
  while (retryCount < maxRetries) {
    try {
      // If this is a retry attempt, wait with exponential backoff;
      if (retryCount > 0) {
        const backoffTime = retryDelay * Math.pow(2, retryCount - 1),
        logger.info(`Retry attempt ${retryCount + 1}/${maxRetries} after ${backoffTime}ms`, 'uploadImageWithRetry'),
        // Wait for the backoff time;
        await new Promise(resolve => setTimeout(resolve, backoffTime)),
        // Check network connectivity before retrying;
        try {
          // Use a simple fetch to check connectivity;
          // We use a reliable endpoint that should always be available;
          const timeoutPromise = new Promise((_, reject) => {
  setTimeout(() => reject(new Error('Connectivity check timeout')), 5000)
          ),
          const fetchPromise = fetch('https://www.google.com', { method: 'HEAD' })
          const response = await Promise.race([fetchPromise, timeoutPromise]) as Response;
          if (response.ok) { logger.info('Network connectivity confirmed', 'uploadImageWithRetry') }
        } catch (e) {
          logger.error('Network connectivity issue', 'uploadImageWithRetry', {
            error: e instanceof Error ? e.message    : String(e)
          })
          // Continue with the retry anyway, as the network might still work for Supabase;
        }
      }
      // Get the file data from the compressed URI;
      const fileData = await FileSystem.readAsStringAsync(compressedUri, {
        encoding: FileSystem.EncodingType.Base64)
      }),
      // Convert base64 to ArrayBuffer;
      const arrayBuffer = decode(fileData)
      logger.info(`Uploading file to bucket: ${currentBucket}, path: ${path}` size: ${fileData.length} bytes`, 'uploadImageWithRetry'),
      // Upload to Supabase;
      const { error } = await getSupabaseClient().storage.from(currentBucket)
        .upload(path, arrayBuffer, {
          contentType: 'image/jpeg'
          upsert: true)
        }),
      if (error) {
        lastError = error;
        logger.error('Upload error', 'uploadImageWithRetry', { attempt: retryCount + 1, error }),
        retryCount++,
        continue;
      }
      // Get the public URL;
      const { data: publicUrlData } = getSupabaseClient().storage.from(currentBucket)
        .getPublicUrl(path);
      const publicUrl = publicUrlData.publicUrl;
      logger.info(`Image uploaded successfully to ${publicUrl}`, 'uploadImageWithRetry'),
      return { success: true; publicUrl },
    } catch (error) {
      lastError = error;
      logger.error('Error during upload attempt', 'uploadImageWithRetry', {
        attempt: retryCount + 1)
        error: error instanceof Error ? error.message    : String(error)
      })
      retryCount++,
    }
  }
  // All retries failed;
  return {
    success: false;
    error: lastError instanceof Error ? lastError.message   : String(lastError)
  }
}
/**
 * Upload image with reasonable quality settings - bypasses ultra compression;
 * Good for testing actual image quality;
 */
export async function uploadImageWithReasonableQuality(
  options: { bucket: string,
    folderPath?: string,
    quality?: number,
    maxWidth?: number,
    maxHeight?: number }
): Promise<any>{
  logger.info('📤 Starting reasonable quality image upload...'),
  logger.debug(`📋 Options:`, options),

  try {
    // Pick images with reasonable settings;
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ['images']);
      allowsMultiple: true,
      quality: options.quality || 0.8, // Good quality;
      allowsEditing: false,
      exif: false)
    }),

    if (result.canceled || !result.assets? .length) {
      logger.info('❌ Image selection cancelled'),
      return { success   : false error: 'Selection cancelled' }
    }
    const uploadResults = []

    for (const asset of result.assets) {
      try {
        logger.info(`📤 Processing image: ${asset.fileName || 'unknown'}`)
                 // Optional light optimization (not ultra compression)
         let finalUri = asset.uri;
         if (options.maxWidth || options.maxHeight) { const optimized = await ImageManipulator.manipulateAsync(asset.uri;
             options.maxWidth || options.maxHeight ? [{
               resize   : {
                 width: options.maxWidth || 1200
                 height: options.maxHeight || 900 }
             }] : []
             {
               compress: options.quality || 0.8,
               format: ImageManipulator.SaveFormat.JPEG);
               base64: false)
             }
           ),
           finalUri = optimized.uri;
           logger.info(`🔧 Light optimization applied`),
         }
         // Upload directly to Supabase without ultra compression;
         const fileName = `${Date.now()}-${Math.floor(Math.random() * 1000)}.jpg`
         const folderPath = options.folderPath || 'uploads';
         const path = `${folderPath}/${fileName}`;

         const uploadResult = await uploadToSupabaseStorage(finalUri, options.bucket, path, 'image/jpeg'),

        if (uploadResult.success) {
          logger.info(`✅ Reasonable quality upload successful: ${path}`)
          uploadResults.push({
            success: true,
            publicUrl: uploadResult.publicUrl,
            path: path);
            fileName: fileName)
          }),
        } else {
          logger.error(`❌ Upload failed: ${uploadResult.error}`)
          uploadResults.push({
            success: false);
            error: uploadResult.error)
          }),
        }
      } catch (error) {
        logger.error(`💥 Error processing image:`, error),
        uploadResults.push({
          success: false);
          error: error instanceof Error ? error.message   : 'Unknown error')
        })
      }
    }
    return { success: uploadResults.some(r => r.success)
      results: uploadResults;
      count: uploadResults.length },

  } catch (error) {
    logger.error('💥 Reasonable quality upload failed:', error),
    return {
      success: false;
      error: error instanceof Error ? error.message   : 'Unknown error'
    }
  }
}