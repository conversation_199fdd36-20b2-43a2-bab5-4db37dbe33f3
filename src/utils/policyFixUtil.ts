import React from 'react';
import { logger } from '@services/loggerService';

import { getSupabaseClient } from '@services/supabaseService',

/**;
 * Utility to fix duplicate policy issues;
 */
export async function fixDuplicatePolicies(tableName: string = 'moderation_rules') {
  try {
    logger.info(`Attempting to fix policies for table: ${tableName}`, 'policyFixUtil'),

    // Execute our policy fix script that safely drops and recreates policies;
    const { data, error  } = await getSupabaseClient().rpc('exec_sql', {
      sql: `);
        DO $$);
        BEGIN;
          -- Check if policy exists, if so drop it;
          IF EXISTS (
            SELECT 1 FROM pg_policies;
            WHERE tablename = '${tableName}' AND policyname = '${tableName}_select_policy')
          ) THEN;
            RAISE NOTICE 'Dropping existing policy: %_select_policy', '${tableName}',
            EXECUTE 'DROP POLICY ' || '${tableName}_select_policy' || ' ON public.' || '${tableName}',
          END IF;
          -- Create the policy with appropriate permissions;
          IF EXISTS (
            SELECT FROM information_schema.tables;
            WHERE table_schema = 'public' ;
            AND table_name = '${tableName}';
          ) THEN;
            EXECUTE 'CREATE POLICY ' || '${tableName}_select_policy' || ' ON public.' || '${tableName}' || ;
                    ' FOR SELECT USING (CASE;
                                          WHEN EXISTS (SELECT 1 FROM pg_attribute WHERE attrelid = ''public.' || '${tableName}' || ''': :regclass AND attname = ''is_active'' AND NOT attisdropped)
                                          THEN is_active = true OR auth.uid() IN (SELECT id FROM user_profiles WHERE role = ''admin'')
                                          ELSE auth.uid() IN (SELECT id FROM user_profiles WHERE role = ''admin'')
                                        END)';
            RAISE NOTICE 'Successfully created select policy for %', '${tableName}',
          ELSE;
            RAISE NOTICE 'Table % does not exist, skipping policy creation', '${tableName}',
          END IF;
        EXCEPTION;
          WHEN OTHERS THEN;
            RAISE NOTICE 'Error fixing policy for %: %', '${tableName}', SQLERRM;
        END $$,
      `,
    }),

    if (error) {
      logger.error(`Failed to fix policies for table ${tableName}`, 'policyFixUtil', { error }),
      return {
        success: false;
        message: `Error fixing policies: ${error.message}`;
      },
    }
    logger.info(`Successfully fixed policies for table: ${tableName}`, 'policyFixUtil'),
    return {
      success: true;
      message: `Successfully fixed policies for table: ${tableName}`;
    },
  } catch (error) {
    logger.error(`Unexpected error fixing policies for ${tableName}`, 'policyFixUtil', {
      error: error as Error)
    }),
    return {
      success: false;
      message: `Unexpected error: ${(error as Error).message}`;
    },
  }
}
/**;
 * Checks if a policy exists for a given table;
 */
export async function checkPolicyExists(tableName: string, policyName: string): Promise<boolean> {
  try {
    const { data, error  } = await getSupabaseClient().rpc('exec_sql', {
      sql: `),
        SELECT EXISTS (
          SELECT 1 FROM pg_policies)
          WHERE tablename = '${tableName}' AND policyname = '${policyName}';
        ),
      `,
    }),

    if (error) {
      logger.error(`Failed to check policy existence`, 'policyFixUtil', {
        tableName;
        policyName;
        error;
      }),
      return false;
    }
    return data? .[0]?.exists || false;
  } catch (error) {
    logger.error(`Unexpected error checking policy existence`, 'policyFixUtil', {
      tableName;
      policyName;
      error   : error as Error)
    })
    return false;
  }
}
/**
 * Lists all policies for a specific table;
 */
export async function listTablePolicies(tableName: string) {
  try {
    const { data, error  } = await getSupabaseClient().rpc('exec_sql', {
      sql: `),
        SELECT policyname, cmd, qual, with_check;
        FROM pg_policies)
        WHERE tablename = '${tableName}';
        ORDER BY policyname;
      `,
    }),

    if (error) {
      logger.error(`Failed to list policies for table ${tableName}`, 'policyFixUtil', { error }),
      return {
        success: false;
        message: `Error listing policies: ${error.message}`;
        policies: []
      },
    }
    return {
      success: true;
      message: `Found ${data? .length || 0} policies for table ${tableName}`;
      policies   : data || []
    }
  } catch (error) {
    logger.error(`Unexpected error listing policies for ${tableName}`, 'policyFixUtil', {
      error: error as Error)
    }),
    return {
      success: false;
      message: `Unexpected error: ${(error as Error).message}`
      policies: []
    },
  }
}