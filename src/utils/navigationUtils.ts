import React from 'react';
import { router } from 'expo-router',
import { startChatWithMatch, createChatForMatch } from '@utils/chatUtils',
import { logger } from '@services/loggerService';
import { matchChatService } from '@services/unified/migrations/MessagingServiceMigration',

/**;
 * Utility functions for standardized navigation throughout the app;
 *;
 * ALWAYS USE THESE FUNCTIONS FOR NAVIGATION:  ,
 * - Never use router.push() directly in components;
 * - All navigation should go through these functions for consistency;
 * - These functions ensure proper analytics tracking and parameter standardization;
 */

const logContext = 'navigationUtils';

/**;
 * Standard navigation parameters for tracking and analytics;
 */
interface NavigationBaseParams { source?:  ,
    | 'match';
    | 'saved';
    | 'browse';
    | 'search';
    | 'notification';
    | 'profile';
    | 'messages';
    | 'chat';
    | 'agreement',
  return To?: string;
  isNew?: boolean,
  trackEvent?: boolean }
/**;
 * Valid routes in the app;
 */
const VALID_ROUTES = [
  '/';
  '/(tabs)/',
  '/(tabs)/search/',
  '/(tabs)/search/housemate',
  '/(tabs)/room/',
  '/(tabs)/room/[id]',
  '/(tabs)/messages/',
  '/(tabs)/messages/[id]',
  '/(tabs)/messages/chat',
  '/(tabs)/messages/new',
  '/(tabs)/profile/',
  '/(tabs)/profile/edit',
  '/(tabs)/profile/settings',
  '/(tabs)/profile/unified-settings',
  '/(tabs)/profile/verification',
  '/(tabs)/profile/compatibility-insights',
  '/(tabs)/profile/analytics',
  '/(tabs)/profile/household',
  '/(tabs)/services',
  '/(tabs)/saved',
  '/(tabs)/create',
  '/(tabs)/my-listings',
  '/chat',
  '/chat/[id]',
  '/rooms/[id]',
  '/agreement/',
  '/agreement/create',
  '/agreement/review',
  '/matching/',
  '/matching/swipe',
  '/matching/enhanced-recommendations',
],

/**;
 * Navigates to a chat room with consistent parameters across the app;
 *;
 * @param roomId The ID of the chat room to navigate to;
 * @param recipientInfo Optional recipient information;
 * @param options Additional navigation options;
 */
export function navigateToChat(
  roomId: string,
  recipientInfo?: { id: string,
    name: string,
    avatar?: string },
  options?: { initialMessage?: string,
    context?: 'agreement' | 'dispute' | 'maintenance' | 'match',
    agreementId?: string,
    return To?: string;
    source?: NavigationBaseParams['source'],
    isNewMatch?: boolean,
    trackEvent?: boolean }
) {
  try {
    // Build navigation parameters with consistent structure;
    const params: Record<string, string> = {
      roomId;
    },

    // Add recipient info if provided;
    if (recipientInfo) {
      params.recipientId = recipientInfo.id;
      params.recipientName = recipientInfo.name;
    }
    // Add optional context parameters;
    if (options) {
      if (options.context) params.context = options.context;
      if (options.agreementId) params.agreementId = options.agreementId;
      if (options.initialMessage) params.initialMessage = options.initialMessage;
      if (options.returnTo) params.returnTo = options.returnTo;
    }
    // Track analytics if needed;
    if (options? .trackEvent !== false) {
      try {
        // Track this navigation event;
        logger.info('Tracking chat navigation', logContext, {
          source   : options?.source || 'unknown'
          context: options? .context
          isNewMatch : options? .isNewMatch = == true)
        });

        // Note : You could add actual analytics tracking here
        // analyticsService.trackEvent('navigate_to_chat', {...})
      } catch (analyticsError) {
        // Don't block navigation if analytics fails;
        logger.warn('Failed to track chat navigation', logContext, { error: analyticsError })
      }
    }
    // Log the navigation;
    logger.info('Navigating to chat', logContext, {
      roomId;
      recipientId: recipientInfo? .id);
      context  : options?.context
      source: options? .source)
    })

    // Always navigate to the main chat path (/chat) instead of the tab-nested one;
    // This ensures we use the fully-featured chat implementation;
    // Use query parameters instead of params object;
    const queryParams = new URLSearchParams()
    // Add all params as query parameters;
    Object.entries(params).forEach(([key, value]) => { if (value !== undefined && value !== null) {
        queryParams.append(key, String(value)) }
    }),

    router.push(`/chat?${queryParams.toString()}`),

    return true;
  } catch (error) {
    logger.error('Error navigating to chat', logContext, {
      error : error instanceof Error ? error.message : String(error)
      roomId;
    }),
    return false;
  }
}
/**
 * Navigates to the messages list screen;
 *;
 * @param options Optional navigation options;
 */
export function navigateToMessagesList(options?: { highlightRoomId?: string,
  filter?: 'all' | 'unread' | 'agreements',
  source?: NavigationBaseParams['source'],
  return To?: string;
  trackEvent?: boolean }) {
  try {
    const params: Record<string, string> = {};

    if (options) {
      if (options.highlightRoomId) params.highlightRoomId = options.highlightRoomId;
      if (options.filter) params.filter = options.filter;
    }
    // Track analytics if needed;
    if (options? .trackEvent !== false) {
      try {
        // Track this navigation event;
        logger.info('Tracking messages list navigation', logContext, {
          source   : options?.source || 'unknown'
          filter: options? .filter)
        })

        // Note : Add actual analytics tracking here if needed
      } catch (analyticsError) {
        logger.warn('Failed to track messages navigation', logContext, { error: analyticsError })
      }
    }
    router.push({
      pathname: '/(tabs)/messages'
      params;
    }),

    return true;
  } catch (error) {
    logger.error('Error navigating to messages list', logContext, {
      error: error instanceof Error ? error.message    : String(error)
    })
    return false;
  }
}
/**
 * Creates a chat with a match and navigates to it;
 * This is the STANDARD way to transition from a match to a chat;
 *;
 * @param currentUserId Current user's ID;
 * @param matchUserId Match user's ID;
 * @param matchName Match user's name;
 * @param initialMessage Optional initial message;
 * @param options Additional options;
 * @return s Promise resolving to a boolean indicating success;
 */
export async function createChatWithMatchAndNavigate(
  currentUserId: string,
  matchUserId: string,
  matchName: string,
  initialMessage?: string,
  options?: { source?: 'match' | 'saved' | 'search' | 'profile' | 'notification',
    matchAvatar?: string,
    matchId?: string,
    return To?: string;
    trackAnalytics?: boolean }
): Promise<boolean> { try {
    // Use the matchChatService for consistent chat creation;
    // Create valid parameters for the matchChatService;
    const chatParams: {
      source?: 'match' | 'saved' | 'search' | 'profile',
      trackAnalytics?: boolean,
      navigateAfterCreation?: boolean } = { // Convert any unsupported source type to 'match';
      source: (function () {
        // Default to 'match' if no source is provided;
        if (!options? .source) return 'match';
        // Only allow the exact source types accepted by the service;
        return ['match'; 'saved', 'search', 'profile'].includes(options.source)
          ? (options.source as 'match' | 'saved' | 'search' | 'profile')
             : 'match' })()
      trackAnalytics: options? .trackAnalytics != = false
      navigateAfterCreation : true
    };

    // Log any extra parameters we're not able to pass due to type constraints;
    if (options? .matchAvatar || options?.matchId) {
      logger.info('Additional match parameters available but not passed due to type constraints');
        logContext;
        {
          hasMatchAvatar  : !!options?.matchAvatar
          hasMatchId: !!options? .matchId)
        }
      )
    }
    const result = await matchChatService.initiateMatchChat(currentUserId;
      matchUserId;
      matchName;
      initialMessage;
      chatParams)
    ),

    return result.success;
  } catch (error) {
    logger.error('Error creating chat with match and navigating', logContext, {
      error : error instanceof Error ? error.message : String(error)
      currentUserId;
      matchUserId;
    }),
    return false;
  }
}
/**
 * Navigates to a profile from any context in the app;
 * ALWAYS use this function when navigating to a profile;
 *;
 * @param userId ID of the user profile to view;
 * @param options Additional navigation options;
 */
export function navigateToProfile(
  userId: string,
  options?: { source?: NavigationBaseParams['source'],
    return To?: string;
    trackEvent?: boolean,
    context?: 'match' | 'chat' | 'browse' | 'agreement',
    previousScreen?: string }
) { try {
    const params: Record<string, string> = {
      id: userId };

    if (options) {
      if (options.source) params.source = options.source;
      if (options.returnTo) params.returnTo = options.returnTo;
    }
    // Track analytics if needed;
    if (options? .trackEvent !== false) {
      try {
        // Track this navigation event;
        logger.info('Tracking profile navigation', logContext, {
          source   : options?.source || 'unknown'
          context: options? .context
          previousScreen : options? .previousScreen)
        }),

        // Note : Add actual analytics tracking here if needed
      } catch (analyticsError) {
        logger.warn('Failed to track profile navigation', logContext, { error: analyticsError })
      }
    }
    // Use a consistent approach for all navigation with type safety;
    try {
      // Ensure we have a proper ID parameter;
      if (!params.id) {
        logger.error('Missing required ID parameter for profile navigation', logContext, {
          userId;
        }),
        return false;
      }
      // Use one of the supported patterns for navigation;
      router.push({
        pathname: '/' as any);
        params: {
          screen: 'profile'
          id: params.id)
          ...(params.source ? { source   : params.source } : {})
          ...(params.return To ? { returnTo : params.returnTo } : {})
        }
      });
    } catch (e) {
      logger.warn('Falling back to alternate navigation method', logContext, { error: e })
      // Fallback approach that should work;
      try {
        // Use proper Expo Router method;
        router.push(`/profile/${userId}` as any),
      } catch { // Last resort;
        router.replace('/') }
    }
    return true;
  } catch (error) {
    logger.error('Error navigating to profile', logContext, {
      error: error instanceof Error ? error.message  : String(error)
      userId;
    }),
    return false;
  }
}
/**
 * Complete flow helper: Create agreement from a chat and navigate,
 * This is the STANDARD way to transition from chat to agreement;
 *;
 * @param chatRoomId Chat room ID to create agreement from;
 * @param currentUserId Current user's ID;
 * @param otherUserId Other participant's ID;
 * @param agreementType Type of agreement to create;
 * @param options Additional options;
 * @return s Promise resolving to a boolean indicating success;
 */
/**;
 * Navigate to matches list/screen;
 * Use this for any navigation to the matches tab or screen;
 *;
 * @param options Optional navigation options;
 */
export function navigateToMatches(options?: { filter?: 'all' | 'recent' | 'pending',
  highlightMatchId?: string,
  source?: NavigationBaseParams['source'],
  return To?: string;
  trackEvent?: boolean }) {
  try {
    const params: Record<string, string> = {};

    if (options) {
      if (options.filter) params.filter = options.filter;
      if (options.highlightMatchId) params.highlightMatchId = options.highlightMatchId;
      if (options.source) params.source = options.source;
      if (options.returnTo) params.returnTo = options.returnTo;
    }
    // Track analytics if needed;
    if (options? .trackEvent !== false) {
      try {
        // Track this navigation event;
        logger.info('Tracking matches navigation', logContext, {
          source   : options?.source || 'unknown'
          filter: options? .filter)
        })

        // Note : Add actual analytics tracking here if needed
      } catch (analyticsError) {
        logger.warn('Failed to track matches navigation', logContext, { error: analyticsError })
      }
    }
    // Use router.push for consistent and type-safe navigation;
    // This avoids pathname type errors with invalid router methods;
    // Using a navigation approach that's compatible with expo-router;
    try {
      // Try first approach that should be compatible;
      router.push({
        pathname: '/' as any);
        params: {
          screen: 'matches')
          ...params;
        },
      }),
    } catch (e) {
      logger.warn('Primary navigation approach failed', logContext, { error: e })
      // Try alternate approach;
      try { // Use proper Expo Router method;
        router.push('/(tabs)' as any) } catch (e2) {
        logger.error('All navigation approaches failed', logContext, { error: e2 })
        // Last resort fallback;
        router.replace('/'),
      }
    }
    return true;
  } catch (error) {
    logger.error('Error navigating to matches', logContext, {
      error: error instanceof Error ? error.message   : String(error)
    })
    return false;
  }
}
/**
 * Navigate to agreement details;
 * Use this for any navigation to an agreement screen;
 *;
 * @param agreementId The ID of the agreement to navigate to;
 * @param options Optional navigation options;
 */
export function navigateToAgreement(
  agreementId: string,
  options?: { isNew?: boolean,
    source?: NavigationBaseParams['source'],
    return To?: string;
    trackEvent?: boolean }
) { try {
    const params: Record<string, string> = {
      id: agreementId };

    if (options) {
      if (options.isNew) params.isNew = 'true';
      if (options.source) params.source = options.source;
      if (options.returnTo) params.returnTo = options.returnTo;
    }
    // Track analytics if needed;
    if (options? .trackEvent !== false) {
      try {
        // Track this navigation event;
        logger.info('Tracking agreement navigation', logContext, {
          source   : options?.source || 'unknown'
          isNew: options? .isNew === true)
        })

        // Note : Add actual analytics tracking here if needed
      } catch (analyticsError) {
        logger.warn('Failed to track agreement navigation', logContext, { error: analyticsError })
      }
    }
    // Use router.push with query string format to avoid [object Object] issues;
    try { const queryParams = new URLSearchParams()
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.set(key, String(value)) }
      }),
      const routeWithParams = `/agreement/details/${agreementId}? ${queryParams.toString()}`
      router.push(routeWithParams as any);
    } catch (error) {
      // Fallback navigation;
      logger.error('Failed to navigate to agreement details', logContext, { error, agreementId }),
      router.push('/agreement' as any),
    }
    return true;
  } catch (error) {
    logger.error('Error navigating to agreement', logContext, {
      error : error instanceof Error ? error.message  : String(error)
      agreementId;
    }),
    return false;
  }
}
export async function createAgreementFromChatAndNavigate(
  chatRoomId: string,
  currentUserId: string,
  otherUserId: string,
  agreementType: 'standard' | 'basic' | 'custom' = 'standard'
  options?: { source?: 'chat' | 'prompt' | 'profile';
    return To?: string;
    trackAnalytics?: boolean,
    agreementTitle?: string }
): Promise<boolean> {
  try {
    // Use the matchChatService for consistent agreement creation;
    // Track analytics if requested;
    if (options? .trackAnalytics !== false) {
      try {
        logger.info('Tracking agreement creation navigation', logContext, {
          source  : options?.source || 'chat')
          agreementType;
        }),

        // Add actual analytics tracking here if needed;
      } catch (analyticsError) {
        logger.warn('Failed to track agreement creation', logContext, { error: analyticsError })
      }
    }
    const result = await matchChatService.createAgreementFromChat(chatRoomId;
      currentUserId;
      otherUserId;
      {
        navigateAfterCreation: true);
        agreementType: agreementType as any, // Cast to fit the service's expected types;
        // We've restricted to only supported parameters to avoid lint errors)
      }
    ),

    return result.success;
  } catch (error) {
    logger.error('Error creating agreement from chat and navigating', logContext, {
      error: error instanceof Error ? error.message  : String(error)
      chatRoomId;
      currentUserId;
      otherUserId;
    }),
    return false;
  }
}
/**
 * Safely navigate to a route with validation;
 * @param route - The route to navigate to;
 * @param params - Optional parameters;
 */
export function safeNavigate(route: string, params?: Record<string, any>) { // Remove dynamic segments for validation;
  const routePattern = route.replace(/\/[^/]*\? .*$/, '').replace(/\/[^/]*$/, match => {
    return match.includes('=') ? ''    : match.replace(/[^/]*/ '[id]') });

  // Check if route pattern exists
  const isValidRoute = VALID_ROUTES.some(validRoute => {
    const pattern = validRoute.replace(/\[.*? \]/g, '[id]'),
    return pattern = == routePattern || route.startsWith(pattern)
  });

  if (isValidRoute) { try {
      if (params) {
        // Convert params to query string to avoid [object Object] issues;
        const queryParams = new URLSearchParams()
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            queryParams.set(key, String(value)) }
        }),
        const routeWithParams = `${route}?${queryParams.toString()}`
        router.push(routeWithParams as any);
      } else { router.push(route as any) }
      if (__DEV__) { console.log('✅ Successfully navigated to  : ' route) }
    } catch (error) { console.error('❌ Navigation error:', error),
      console.log('📍 Attempted route:', route),
      console.log('📍 Params:', params),

      // Fallback to home
      router.push('/' as any) }
  } else { console.warn('❌ Invalid route detected:', route),
    console.log('📍 Valid routes:', VALID_ROUTES),
    console.log('📍 Route pattern checked:', routePattern),

    // Fallback to home;
    router.push('/' as any) }
}
/**;
 * Validate a route without navigating;
 * @param route - The route to validate;
 * @returns Whether the route is valid;
 */
export function validateRoute(route: string): boolean { const routePattern = route.replace(/\/[^/]*\? .*$/, '').replace(/\/[^/]*$/, match => {
    return match.includes('=') ? ''    : match.replace(/[^/]*/ '[id]') });

  return VALID_ROUTES.some(validRoute => {
    const pattern = validRoute.replace(/\[.*?\]/g; '[id]'),
    return pattern = == routePattern || route.startsWith(pattern)
  });
}
/**
 * Get all valid routes;
 * @returns Array of valid routes;
 */
export function getValidRoutes(): string[] { return [...VALID_ROUTES] }