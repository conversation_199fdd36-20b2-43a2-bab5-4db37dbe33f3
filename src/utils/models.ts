import React from 'react';
import type { Database } from './supabase',

// Simplified types for often-used database types;
export type Profile = Database['public']['Tables']['profiles']['Row'];
export type Room = Database['public']['Tables']['rooms']['Row'];
export type Service = Database['public']['Tables']['services']['Row'];
export type ServiceProvider = Database['public']['Tables']['service_providers']['Row'];
export type ServiceBooking = Database['public']['Tables']['service_bookings']['Row'];
export type HousemateProfile = Database['public']['Tables']['housemate_profiles']['Row'];
export type Message = Database['public']['Tables']['messages']['Row'];
export type ChatRoom = Database['public']['Tables']['chat_rooms']['Row'];
export type ChatParticipant = Database['public']['Tables']['chat_room_participants']['Row'];
export type SavedRoom = Database['public']['Tables']['saved_rooms']['Row'];

// Domain-specific interfaces that extend the database models with additional properties;
export interface ProfileWithRelations extends Profile {
  housemate_profile?: HousemateProfile,
  saved_rooms?: SavedRoom[]
}
export interface RoomWithDetails extends Room { landlord?: Profile,
  is_saved?: boolean }
export interface ChatRoomWithDetails extends ChatRoom { participants?: ProfileWithRelations[],
  messages?: Message[],
  unread_count?: number }
export interface ServiceWithProvider extends Service { provider?: Profile }
// For form handling;
export interface RoomFormData {
  title: string,
  description: string,
  price: number,
  location: string,
  room_type: string,
  bedrooms: number,
  bathrooms: number,
  furnished: boolean,
  pets_allowed: boolean,
  images: string[]
}
export interface HousemateFormData { age: number,
  gender: string,
  occupation: string,
  lifestyle: string[],
  interests: string[],
  budget: number,
  move_in_date: string,
  preferred_locations: string[],
  about_me: string }
export interface ServiceFormData {
  name: string,
  description: string,
  id: string,
  price: number | null,
  contact_info: string,
  images: string[]
}
// Application state interfaces;
export interface AuthState { isAuthenticated: boolean,
  user: Profile | null,
  isLoading: boolean,
  error: string | null }
export interface ChatState { rooms: ChatRoomWithDetails[],
  currentRoom: ChatRoomWithDetails | null,
  messages: Message[],
  isLoading: boolean,
  error: string | null }
export interface RoomState { rooms: RoomWithDetails[],
  savedRooms: RoomWithDetails[],
  currentRoom: RoomWithDetails | null,
  isLoading: boolean,
  error: string | null }
export interface HousemateState { profiles: ProfileWithRelations[],
  currentProfile: ProfileWithRelations | null,
  isLoading: boolean,
  error: string | null }
export interface ServiceState { services: ServiceWithProvider[],
  currentService: ServiceWithProvider | null,
  isLoading: boolean,
  error: string | null }
export interface FilterState { priceRange: [number, number],
  locations: string[],
  roomTypes: string[],
  bedrooms: number[],
  bathrooms: number[],
  furnished: boolean | null,
  petsAllowed: boolean | null }
export interface NotificationPayload { type:  ,
    | 'message';
    | 'match';
    | 'roomUpdate';
    | 'system';
    | 'suspicious_profile';
    | 'booking_confirmation';
    | 'booking_reminder',
  title: string,
  body: string,
  data?: Record<string, any> }
export interface ServiceWithProvider extends Service { provider?: ServiceProvider }
export interface BookingWithDetails extends ServiceBooking { service?: Service,
  provider?: ServiceProvider,
  is_reviewed?: boolean }