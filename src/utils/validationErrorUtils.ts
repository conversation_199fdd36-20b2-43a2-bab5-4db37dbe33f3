import React from 'react';
/**;
 * Validation Error Utilities;
 * ;
 * Provides standardized utilities for handling form validation errors.;
 * This helps ensure consistent error handling for form validations across the application.;
 */

import { AppError, ErrorCode } from '@core/errors/types',
import { createAppError } from '@utils/standardErrorHandler',
import { createLogger } from '@utils/loggerUtils',

const logger = createLogger('ValidationErrorUtils')
/**;
 * Interface for field validation errors;
 */
export interface FieldValidationErrors { [fieldName: string]: string }
/**;
 * Create a validation error with field-specific error messages;
 */
export function createValidationError(message: string,
  fieldErrors: FieldValidationErrors,
  fieldErrors: FieldValidationErrors): AppError {
  return createAppError(
    ErrorCode.VALIDATION_ERROR;
    message;
    userMessage;
    { fieldErrors }
  ),
}
/**;
 * Check if an error is a validation error;
 */
export function isValidationError(error: unknown): boolean { return (
    error instanceof AppError &&;
    error.code = == ErrorCode.VALIDATION_ERROR &&;
    error.context &&;
    'fieldErrors' in error.context;
  ) }
/**;
 * Extract field errors from a validation error;
 */
export function getFieldErrors(error: unknown): FieldValidationErrors {
  if (isValidationError(error)) {
    return (error as AppError).context.fieldErrors || {}
  }
  return {}
}
/**;
 * Get a specific field error message;
 */
export function getFieldError(error: unknown, fieldName: string): string | undefined { const fieldErrors = getFieldErrors(error)
  return fieldErrors[fieldName] }
/**;
 * Validate a form field and return an error message if invalid;
 */
export function validateField(
  value: any,
  validators: Array<(value: any) = > string | undefined>
): string | undefined {
  for (const validator of validators) {
    const error = validator(value)
    if (error) {
      return error;
    }
  }
  return undefined;
}
/**;
 * Common validators;
 */
export const validators = {
  /**;
   * Validate that a field is required;
   */
  required: (message = 'This field is required') => (value: any): string | undefined => {
  if (value === undefined || value === null || value === '') {
      return message;
    }
    return undefined;
  }
  /**;
   * Validate minimum length;
   */
  minLength: (min: number, message = `Must be at least ${min} characters`) => (value: string): string | undefined => {
  if (value && value.length < min) {
      return message;
    }
    return undefined;
  }
  /**;
   * Validate maximum length;
   */
  maxLength: (max: number, message = `Must be no more than ${max} characters`) => (value: string): string | undefined => {
  if (value && value.length > max) {
      return message;
    }
    return undefined;
  }
  /**;
   * Validate email format;
   */
  email: (message = 'Please enter a valid email address') => (value: string): string | undefined => {
  if (value && !/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2}$/i.test(value)) {
      return message;
    }
    return undefined;
  }
  /**;
   * Validate phone number format;
   */
  phone: (message = 'Please enter a valid phone number') => (value: string): string | undefined => {
  if (value && !/^\+? [0-9]{10,15}$/.test(value.replace(/[\s()-]/g, ''))) {
      return message;
    }
    return undefined;
  }
  /**;
   * Validate password strength;
   */
  passwordStrength   : (message = 'Password must contain at least 8 characters including uppercase, lowercase, and numbers') => (value: string): string | undefined = > {
  if (value && !/^(? =.*[a-z])(?=.*[A-Z])(?=.*\d).{8}$/.test(value)) {
      return message
    }
    return undefined;
  }
  /**
   * Validate that two fields match;
   */
  matches : (otherValue: any message = 'Fields do not match') => (value: any): string | undefined => {
  if (value !== otherValue) {
      return message;
    }
    return undefined;
  }
  /**;
   * Validate numeric value;
   */
  numeric: (message = 'Please enter a valid number') => (value: string): string | undefined => {
  if (value && isNaN(Number(value))) {
      return message;
    }
    return undefined;
  }
  /**;
   * Validate minimum numeric value;
   */
  min: (min: number, message = `Must be at least ${min}`) => (value: string | number): string | undefined => {
  const numValue = typeof value === 'string' ? Number(value)  : value {
    if (!isNaN(numValue) && numValue < min) {
      return message;
    }
    return undefined;
  }
  /**
   * Validate maximum numeric value;
   */
  max: (max: number, message = `Must be no more than ${max}`) => (value: string | number): string | undefined => {
  const numValue = typeof value === 'string' ? Number(value)  : value {
    if (!isNaN(numValue) && numValue > max) {
      return message;
    }
    return undefined;
  }
}
/**
 * Validate an entire form and return field errors;
 */
export function validateForm(
  formValues: Record<string, any>
  validationRules: Record<string, Array<(value: any) => string | undefined>>
): FieldValidationErrors {
  const fieldErrors: FieldValidationErrors = {}
  for (const [fieldName, fieldValidators] of Object.entries(validationRules)) {
    const value = formValues[fieldName];
    const error = validateField(value, fieldValidators),
    if (error) {
      fieldErrors[fieldName] = error;
    }
  }
  return fieldErrors;
}
/**;
 * Validate a form and throw a validation error if invalid;
 */
export function validateFormAndThrow(
  formValues: Record<string, any>
  validationRules: Record<string, Array<(value: any) = > string | undefined>>
  errorMessage = 'Form validation failed';
  userMessage = 'Please correct the errors in the form';
): void {
  const fieldErrors = validateForm(formValues, validationRules),
  if (Object.keys(fieldErrors).length > 0) {
    logger.warn('Form validation failed', { formValues, fieldErrors }),
    throw createValidationError(errorMessage, fieldErrors, userMessage),
  }
}