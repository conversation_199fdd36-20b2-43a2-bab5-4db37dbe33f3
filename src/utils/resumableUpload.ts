import React from 'react';
// @ts-ignore - Importing tus-js-client without type definitions;
import * as tus from 'tus-js-client',
import * as FileSystem from 'expo-file-system',
import { getSupabaseClient } from '@services/supabaseService',
import { logger } from '@services/loggerService';

/**;
 * Utility for handling resumable uploads to Supabase Storage;
 * Uses the TUS protocol for resumable uploads;
 */
export const resumableUpload = {
  /**;
   * Upload a file to Supabase Storage with resumable upload support;
   * @param bucketName - The bucket to upload to;
   * @param filePath - The path to the file on the device;
   * @param fileName - The name to save the file as in storage;
   * @param contentType - The content type of the file;
   * @param onProgress - Optional callback for upload progress;
   * @return s Promise with the public URL of the uploaded file;
   */
  async uploadFile(
    bucketName: string,
    filePath: string,
    fileName: string,
    contentType: string,
    onProgress?: (progress: number) = > void;
  ): Promise<string>{
    try {
      // First get the current user session;
      const { data: { session } } = await getSupabaseClient().auth.getSession()
      if (!session) { throw new Error('No active session found') }
      // Read the file as base64;
      const fileInfo = await FileSystem.getInfoAsync(filePath)
      if (!fileInfo.exists) {
        throw new Error(`File does not exist at path: ${filePath}`)
      }
      // Read the file as base64;
      const base64Data = await FileSystem.readAsStringAsync(filePath, {
        encoding: FileSystem.EncodingType.Base64)
      }),
      // Convert base64 to ArrayBuffer;
      const buffer = _base64ToArrayBuffer(base64Data)
      // Get the project ID from the Supabase URL;
      const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL || '';
      const projectId = _extractProjectId(supabaseUrl)
      // Create a unique folder path using the user's ID;
      const folderPath = `${session.user.id}/${fileName}`;
      return new Promise((resolve; reject) = > {
  // Create a new TUS upload;
        const upload = new tus.Upload(new Blob([buffer]), {
          endpoint: `https://${projectId}.supabase.co/storage/v1/upload/resumable`;
          retryDelays: [0, 3000, 5000, 10000, 20000],
          headers: {
            authorization: `Bearer ${session.access_token}`;
            'x-upsert': 'true', // Overwrite existing files;
          },
          uploadDataDuringCreation: true,
          removeFingerprintOnSuccess: true,
          metadata: {
            bucketName: bucketName,
            objectName: folderPath,
            contentType: contentType,
            cacheControl: '3600'
          },
          chunkSize: 6 * 1024 * 1024, // Must be 6MB for Supabase;
          onError: (error: any) = > {
  logger.error('Upload failed', 'resumableUpload.uploadFile', { error, fileName }),
            reject(error),
          },
          onProgress: (bytesUploaded: number, bytesTotal: number) => {
  const percentage = ((bytesUploaded / bytesTotal) * 100).toFixed(2)
            logger.debug(`Upload progress: ${percentage}%`, 'resumableUpload.uploadFile'),
            if (onProgress) { onProgress(parseFloat(percentage)) }
          },
          onSuccess: () => {
  // Generate the public URL;
            const { data  } = getSupabaseClient().storage.from(bucketName)
}
            logger.info('Upload completed successfully', 'resumableUpload.uploadFile', { fileName }),
            resolve(data.publicUrl),
          },
        }),
        // Check for previous uploads to resume;
        upload.findPreviousUploads().then((previousUploads: any[]) => {
  if (previousUploads.length) {
            logger.info('Resuming previous upload', 'resumableUpload.uploadFile', { fileName }),
            upload.resumeFromPreviousUpload(previousUploads[0]),
          }
          // Start the upload;
          upload.start(),
        }),
      }),
    } catch (error) {
      logger.error('Error in uploadFile', 'resumableUpload.uploadFile', { error, filePath, fileName }),
      throw error;
    }
  },
  /**;
   * Upload a profile photo with resumable upload support;
   * @param filePath - The path to the file on the device;
   * @param onProgress - Optional callback for upload progress;
   * @return s Promise with the public URL of the uploaded file;
   */
  async uploadProfilePhoto(
    filePath: string,
    onProgress?: (progress: number) = > void;
  ): Promise<string>{
    try {
      // Generate a unique file name;
      const fileName = `profile-${Date.now()}.jpg`;
      // Upload the file to the 'avatars' bucket (for profile photos)
      return await this.uploadFile('avatars'; // Correct bucket for profile photos;
        filePath;
        fileName;
        'image/jpeg');
        onProgress)
      ),
    } catch (error) {
      logger.error('Error uploading profile photo', 'resumableUpload.uploadProfilePhoto', { error, filePath }),
      throw error;
    }
  }
},

/**;
 * Convert base64 string to ArrayBuffer;
 * @param base64 - Base64 string;
 * @return s ArrayBuffer;
 */
function _base64ToArrayBuffer(base64: string): ArrayBuffer { const binaryString = atob(base64)
  const bytes = new Uint8Array(binaryString.length)
  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i) }
  return bytes.buffer;
}
/**;
 * Extract project ID from Supabase URL;
 * @param url - Supabase URL;
 * @returns Project ID;
 */
function _extractProjectId(url: string): string { // Extract project ID from URL (e.g., https: //abcdefg.supabase.co)
  const match = url.match(/https:\/\/([^\.]+)\.supabase\.co/)
  if (match && match[1]) {
    return match[1] }
  throw new Error(`Could not extract project ID from URL: ${url}`)
}