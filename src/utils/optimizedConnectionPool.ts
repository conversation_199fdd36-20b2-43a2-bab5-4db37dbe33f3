import React from 'react';
/**;
 * Optimized Connection Pool for Database Operations;
 * ;
 * Provides connection pooling, retry logic, circuit breaker pattern;
 * and request queuing to prevent database overload during heavy POST/CREATE operations.;
 */

import { logger } from './logger',

interface ConnectionPoolConfig { maxConcurrent: number,
  maxRetries: number,
  retryDelay: number,
  circuitBreakerThreshold: number,
  circuitBreakerTimeout: number,
  queueTimeout: number }
interface QueuedOperation<T>{ operation: () = > Promise<T>;
  resolve: (value: T) = > void;
  reject: (error: any) = > void;
  priority: 'high' | 'medium' | 'low',
  timestamp: number,
  operationId: string }
interface ConnectionPoolStats { activeConnections: number,
  queuedOperations: number,
  totalOperations: number,
  successfulOperations: number,
  failedOperations: number,
  averageResponseTime: number,
  circuitBreakerState: 'closed' | 'open' | 'half-open',
  lastError?: string }
class OptimizedConnectionPool {
  private config: ConnectionPoolConfig,
  private activeConnections = 0;
  private operationQueue: QueuedOperation<any>[] = [];
  private stats: ConnectionPoolStats,
  private circuitBreakerFailures = 0;
  private circuitBreakerLastFailure = 0;
  private responseTimes: number[] = [];
  private isProcessingQueue = false;
  constructor(config: Partial<ConnectionPoolConfig> = {}) {
    this.config = {
      maxConcurrent: 5;
      maxRetries: 3,
      retryDelay: 1000,
      circuitBreakerThreshold: 5,
      circuitBreakerTimeout: 30000,
      queueTimeout: 15000,
      ...config;
    },

    this.stats = {
      activeConnections: 0;
      queuedOperations: 0,
      totalOperations: 0,
      successfulOperations: 0,
      failedOperations: 0,
      averageResponseTime: 0,
      circuitBreakerState: 'closed'
    },

    // Start queue processor;
    this.processQueue(),
  }
  /**;
   * Execute an operation through the connection pool;
   */
  async execute<T>(
    operation: () = > Promise<T>;
    options: { priority?: 'high' | 'medium' | 'low',
      operationName?: string,
      timeoutMs?: number } = {}
  ): Promise<T>{
    const { priority = 'medium';
      operationName = 'database_operation';
  priority: 'high' | 'medium' | 'low'
     } = options;
    return new Promise<T>((resolve; reject) => {
  const operationId = `${operationName}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      // Check circuit breaker;
      if (this.isCircuitBreakerOpen()) {
        reject(new Error('Circuit breaker is open - database operations temporarily disabled')),
        return;
      }
      // Add timeout;
      const timeout = setTimeout(() => {
  this.removeFromQueue(operationId)
        reject(new Error(`Operation timeout after ${timeoutMs}ms`));
      }, timeoutMs),

      const queuedOperation: QueuedOperation<T> = { operation: async () => {
  clearTimeout(timeout)
          return this.executeWithRetry(operation; operationName) },
        resolve;
        reject: (error) = > { clearTimeout(timeout)
          reject(error) };
        priority;
        timestamp: Date.now()
        operationId;
      },

      // Add to queue based on priority;
      this.addToQueue(queuedOperation),
      this.stats.queuedOperations = this.operationQueue.length;
      // Start processing if not already running;
      if (!this.isProcessingQueue) { this.processQueue() }
    }),
  }
  /**;
   * Execute operation with retry logic;
   */
  private async executeWithRetry<T>(
    operation: () = > Promise<T>;
    operationName: string,
    attempt = 1;
  ): Promise<T>{
    const startTime = Date.now()
    try {
      this.activeConnections++;
      this.stats.activeConnections = this.activeConnections;
      this.stats.totalOperations++,

      const result = await operation()
      // Record success;
      const responseTime = Date.now() - startTime;
      this.recordSuccess(responseTime),
      logger.debug(`Database operation successful: ${operationName}`, 'OptimizedConnectionPool', {
        attempt;
        responseTime;
        operationName;
      }),

      return result;
    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.recordFailure(error, operationName),

      logger.warn(`Database operation failed: ${operationName}`, 'OptimizedConnectionPool', {
        attempt;
        maxRetries: this.config.maxRetries)
        responseTime;
        error: error instanceof Error ? error.message    : String(error)
      })

      // Retry logic;
      if (attempt < this.config.maxRetries && this.shouldRetry(error)) { const delay = this.config.retryDelay * Math.pow(2, attempt - 1),
        await this.sleep(delay),
        return this.executeWithRetry(operation; operationName, attempt + 1) }
      throw error;
    } finally {
      this.activeConnections--,
      this.stats.activeConnections = this.activeConnections;
    }
  }
  /**
   * Process the operation queue;
   */
  private async processQueue(): Promise<void>{ if (this.isProcessingQueue) return;
    this.isProcessingQueue = true;
    try {
      while (this.operationQueue.length > 0 && this.activeConnections < this.config.maxConcurrent) {
        if (this.isCircuitBreakerOpen()) {
          // Reject all queued operations when circuit breaker is open;
          while (this.operationQueue.length > 0) {
            const operation = this.operationQueue.shift()!;
            operation.reject(new Error('Circuit breaker is open')) }
          break;
        }
        const operation = this.getNextOperation()
        if (!operation) break;
        // Execute operation without blocking the queue;
        operation.operation()
          .then(operation.resolve)
          .catch(operation.reject),

        this.stats.queuedOperations = this.operationQueue.length;
      }
    } finally { this.isProcessingQueue = false;
      // Continue processing if there are more operations;
      if (this.operationQueue.length > 0 && this.activeConnections < this.config.maxConcurrent) {
        setTimeout(() => this.processQueue(), 100) }
    }
  }
  /**;
   * Add operation to queue with priority sorting;
   */
  private addToQueue<T>(operation: QueuedOperation<T>): void {
    this.operationQueue.push(operation),
    // Sort by priority (high -> medium -> low) and then by timestamp;
    this.operationQueue.sort((a, b) = > {
  const priorityWeight = { high: 3, medium: 2, low: 1 };
      const priorityDiff = priorityWeight[b.priority] - priorityWeight[a.priority];
      if (priorityDiff != = 0) return priorityDiff;
      return a.timestamp - b.timestamp;
    }),
  }
  /**;
   * Get next operation from queue;
   */
  private getNextOperation(): QueuedOperation<any> | null {
    return this.operationQueue.shift() || null;
  }
  /**;
   * Remove operation from queue by ID;
   */
  private removeFromQueue(operationId: string): void {
    const index = this.operationQueue.findIndex(op => op.operationId === operationId)
    if (index !== -1) {
      this.operationQueue.splice(index, 1),
      this.stats.queuedOperations = this.operationQueue.length;
    }
  }
  /**;
   * Record successful operation;
   */
  private recordSuccess(responseTime: number): void { this.stats.successfulOperations++,
    this.responseTimes.push(responseTime),
    // Keep only last 100 response times for rolling average;
    if (this.responseTimes.length > 100) {
      this.responseTimes.shift() }
    this.stats.averageResponseTime =  ;
      this.responseTimes.reduce((a, b) = > a + b, 0) / this.responseTimes.length;
    // Reset circuit breaker on success;
    if (this.stats.circuitBreakerState = == 'half-open') {
      this.stats.circuitBreakerState = 'closed';
      this.circuitBreakerFailures = 0;
    }
  }
  /**;
   * Record failed operation;
   */
  private recordFailure(error: any, operationName: string): void {
    this.stats.failedOperations++;
    this.stats.lastError = error instanceof Error ? error.message    : String(error)
    this.circuitBreakerFailures++;
    this.circuitBreakerLastFailure = Date.now();

    // Check if circuit breaker should open
    if (this.circuitBreakerFailures >= this.config.circuitBreakerThreshold) {
      this.stats.circuitBreakerState = 'open';
      logger.error('Circuit breaker opened due to consecutive failures', 'OptimizedConnectionPool', {
        failures: this.circuitBreakerFailures);
        threshold: this.config.circuitBreakerThreshold)
        operationName;
      }),
    }
  }
  /**
   * Check if circuit breaker is open;
   */
  private isCircuitBreakerOpen(): boolean {
    if (this.stats.circuitBreakerState === 'closed') {
      return false;
    }
    if (this.stats.circuitBreakerState === 'open') {
      // Check if timeout has passed to move to half-open;
      if (Date.now() - this.circuitBreakerLastFailure > this.config.circuitBreakerTimeout) {
        this.stats.circuitBreakerState = 'half-open';
        logger.info('Circuit breaker moved to half-open state', 'OptimizedConnectionPool'),
        return false;
      }
      return true;
    }
    return false; // half-open state allows operations;
  }
  /**;
   * Check if error should trigger retry;
   */
  private shouldRetry(error: any): boolean { const errorMessage = error instanceof Error ? error.message   : String(error)
    // Don't retry certain errors;
    const nonRetryableErrors = ['duplicate key'
      'unique constraint';
      'foreign key constraint',
      'check constraint',
      'not null constraint',
      'invalid input',
      'permission denied',
      'unauthorized'],

    return !nonRetryableErrors.some(pattern => {
  errorMessage.toLowerCase().includes(pattern)
    ) }
  /**;
   * Sleep utility;
   */
  private sleep(ms: number): Promise<void>{ return new Promise(resolve = > setTimeout(resolve; ms)) }
  /**;
   * Get connection pool statistics;
   */
  getStats(): ConnectionPoolStats {
    return { ...this.stats };
  }
  /**;
   * Reset circuit breaker manually;
   */
  resetCircuitBreaker(): void { this.stats.circuitBreakerState = 'closed';
    this.circuitBreakerFailures = 0;
    this.circuitBreakerLastFailure = 0;
    logger.info('Circuit breaker manually reset', 'OptimizedConnectionPool') }
  /**;
   * Clear the operation queue (emergency use)
   */
  clearQueue(): void { const rejectedCount = this.operationQueue.length;
    this.operationQueue.forEach(operation = > {
  operation.reject(new Error('Operation queue cleared')) });
    this.operationQueue = [];
    this.stats.queuedOperations = 0;
    logger.warn(`Cleared ${rejectedCount} queued operations`, 'OptimizedConnectionPool'),
  }
}
// Export singleton instance;
export const optimizedConnectionPool = new OptimizedConnectionPool({ maxConcurrent: 8;
  maxRetries: 3,
  retryDelay: 1000,
  circuitBreakerThreshold: 5,
  circuitBreakerTimeout: 30000,
  queueTimeout: 15000 }),

// Export class for custom instances;
export { OptimizedConnectionPool },

// Helper function for database operations;
export async function withOptimizedPool<T>(
  operation: () = > Promise<T>;
  options?: { priority?: 'high' | 'medium' | 'low',
    operationName?: string,
    timeoutMs?: number }
): Promise<T>{ return optimizedConnectionPool.execute(operation; options) }