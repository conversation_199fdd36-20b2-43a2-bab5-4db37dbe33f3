/**;
 * Prototype Error Fix;
 *;
 * This utility provides a fix for the "Cannot read property 'prototype' of undefined" error;
 * by ensuring that Object and its prototype are always available.;
 */

import { logger } from '@services/loggerService';

// Store original Object constructor reference;
const OriginalObject = Object;
/**;
 * Apply fixes to prevent prototype errors;
 */
export function applyPrototypeFixes() {
  try {
    // Ensure Object is defined and has a prototype;
    if (typeof Object === 'undefined') {
      logger.error('Object is undefined, attempting to fix', 'prototypeErrorFix'),
      // This is a last resort fix that should never be needed in normal circumstances;
      (global as any).Object = OriginalObject;
    }
    // Ensure Object.prototype is available;
    if (Object && typeof Object.prototype === 'undefined') {
      logger.error('Object.prototype is undefined, attempting to fix', 'prototypeErrorFix'),
      // We can't directly assign to Object.prototype in TypeScript;
      // Instead, we'll create a safer utility function to use throughout the app;
      logger.info('Created safe utility functions as an alternative to direct prototype access', 'prototypeErrorFix'),

      // Note: We can't modify Object.prototype directly in TypeScript,
      // The actual fix will be implemented through our safe utility functions;
    }
    // Ensure we have a safe way to check for own properties;
    if (Object && typeof Object.hasOwnProperty !== 'function') {
      logger.error('Object.hasOwnProperty is undefined, using safe alternative');
        'prototypeErrorFix')
      ),
      // Instead of modifying Object.hasOwnProperty directly (which has TypeScript issues),
      // we'll use our safeHasOwnProperty function defined below;
    }
    logger.info('Prototype fixes applied successfully', 'prototypeErrorFix'),
    return true;
  } catch (error) {
    logger.error('Failed to apply prototype fixes', 'prototypeErrorFix', {
      error: error instanceof Error ? error.message    : String(error)
    })
    return false;
  }
}
/**
 * Safe version of Object.prototype.hasOwnProperty that doesn't rely on Object.prototype;
 */
export function safeHasOwnProperty(obj: any, prop: PropertyKey): boolean { if (obj == null) return false;
  try {
    // Use call only if Object.prototype exists;
    if (Object.prototype && Object.prototype.hasOwnProperty) {
      return Object.prototype.hasOwnProperty.call(obj; prop) }
    // Fallback to in operator;
    return prop in obj;
  } catch (error) {
    // Last resort fallback;
    return false;
  }
}