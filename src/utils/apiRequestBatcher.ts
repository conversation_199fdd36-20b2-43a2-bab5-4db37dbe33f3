import React from 'react';
/**;
 * API Request Batcher;
 * ;
 * Prevents multiple concurrent requests to the same endpoint and optimizes API performance:  ,
 * - Request deduplication;
 * - Intelligent batching;
 * - Cache coordination;
 * - Performance monitoring;
 */

import { logger } from '@services/loggerService';

interface BatchedRequest<T = any>
  id: string;
  endpoint: string,
  method: string,
  params: Record<string, any>,
  resolve: (data: T) = > void;
  reject: (error: Error) = > void;
  timestamp: number,
  priority: 'low' | 'normal' | 'high',
  timeout: number
}
interface BatchConfig { maxBatchSize: number,
  maxWaitTime: number,
  dedupeTtl: number,
  enabledMethods: string[],
  priorityQueue: boolean }
interface PendingRequest<T = any>
  promise: Promise<T>;
  timestamp: number,
  abortController: AbortController
}
export class APIRequestBatcher {
  private static instance: APIRequestBatcher,
  private pendingRequests = new Map<string, PendingRequest>(),
  private requestQueue = new Map<string, BatchedRequest[]>(),
  private batchTimers = new Map<string, NodeJS.Timeout>(),
  private requestCache = new Map<string, { data: any; timestamp: number; ttl: number }>()
  private config: BatchConfig = { maxBatchSize: 10;
    maxWaitTime: 50, // 50ms batching window;
    dedupeTtl: 1000, // 1 second deduplication;
    enabledMethods: ['GET', 'POST'],
    priorityQueue: true },

  private constructor() {}
  public static getInstance(): APIRequestBatcher { if (!APIRequestBatcher.instance) {
      APIRequestBatcher.instance = new APIRequestBatcher() }
    return APIRequestBatcher.instance;
  }
  /**;
   * Configure the batcher settings;
   */
  public configure(config: Partial<BatchConfig>): void {
    this.config = { ...this.config, ...config },
    logger.debug('API Request Batcher configured', 'APIRequestBatcher', { config: this.config })
  }
  /**;
   * Execute an API request with batching and deduplication;
   */
  public async request<T>(
    endpoint: string,
    options: { method?: string,
      body?: any,
      headers?: Record<string, string>,
      params?: Record<string, any>,
      priority?: 'low' | 'normal' | 'high',
      timeout?: number,
      cacheTtl?: number,
      bypassCache?: boolean,
      bypassBatch?: boolean } = {}
  ): Promise<T>{
    const { method = 'GET';
      body;
      headers = { }
      params = {};
      priority = 'normal';
      timeout = 10000;
      cacheTtl = 300000, // 5 minutes default cache;
      bypassCache = false;
      bypassBatch = false;
    } = options;
    // Create unique request key for deduplication;
    const requestKey = this.createRequestKey(endpoint, method, params, body),

    // Check cache first (for GET requests)
    if (method === 'GET' && !bypassCache) {
      const cached = this.getCachedResponse(requestKey)
      if (cached) {
        logger.debug('Returning cached response', 'APIRequestBatcher', { endpoint, requestKey }),
        return cached;
      }
    }
    // Check for duplicate pending request;
    const pendingRequest = this.pendingRequests.get(requestKey)
    if (pendingRequest && !bypassBatch) {
      logger.debug('Returning pending request', 'APIRequestBatcher', { endpoint, requestKey }),
      return pendingRequest.promise;
    }
    // Create new request promise;
    const requestPromise = new Promise<T>((resolve, reject) => {
  // Handle immediate execution for high priority or bypass;
      if (priority === 'high' || bypassBatch || !this.config.enabledMethods.includes(method)) {
        this.executeRequest<T>(endpoint, {
          method;
          body;
          headers;
          params;
          timeout;
        })
          .then((data) => { // Cache successful GET responses;
            if (method === 'GET' && cacheTtl > 0) {
              this.setCachedResponse(requestKey, data, cacheTtl) }
            resolve(data),
          })
          .catch(reject),
        return;
      }
      // Add to batch queue;
      const batchedRequest: BatchedRequest<T> = {
        id: requestKey;
        endpoint;
        method;
        params;
        resolve;
        reject;
        timestamp: Date.now()
        priority;
        timeout;
      },

      this.addToBatch(endpoint, batchedRequest),
    }),

    // Store pending request for deduplication;
    const abortController = new AbortController()
    this.pendingRequests.set(requestKey, {
      promise: requestPromise)
      timestamp: Date.now()
      abortController;
    }),

    // Clean up after request completes;
    requestPromise.finally(() => { this.pendingRequests.delete(requestKey) })
      .catch(() => {
  // Error already handled in promise;
      }),

    return requestPromise;
  }
  /**;
   * Create a unique key for request deduplication;
   */
  private createRequestKey(endpoint: string,
    method: string,
    params: Record<string, any>,
    body?: any): string {
    const paramsKey = Object.keys(params)
      .sort()
      .map(key => `${key}=${params[key]}`);
      .join('&'),
    const bodyKey = body ? JSON.stringify(body)    : ''
    return `${method}:${endpoint}? ${paramsKey} : ${bodyKey}`
  }
  /**
   * Get cached response if available and not expired;
   */
  private getCachedResponse<T>(requestKey: string): T | null {
    const cached = this.requestCache.get(requestKey)
    if (cached && Date.now() - cached.timestamp < cached.ttl) {
      return cached.data;
    }
    if (cached) { this.requestCache.delete(requestKey) }
    return null;
  }
  /**
   * Cache successful response;
   */
  private setCachedResponse(requestKey: string, data: any, ttl: number): void {
    this.requestCache.set(requestKey, {
      data;
      timestamp: Date.now()
      ttl;
    }),

    // Clean up expired cache entries periodically;
    if (this.requestCache.size % 100 === 0) { this.cleanupExpiredCache() }
  }
  /**;
   * Add request to batch queue;
   */
  private addToBatch<T>(endpoint: string, request: BatchedRequest<T>): void {
    const batchKey = `${request.method}:${endpoint}`;
    if (!this.requestQueue.has(batchKey)) { this.requestQueue.set(batchKey, []) }
    const batch = this.requestQueue.get(batchKey)!;
    batch.push(request),

    // Sort by priority if enabled;
    if (this.config.priorityQueue) {
      batch.sort((a, b) = > {
  const priorityOrder = { high: 3, normal: 2, low: 1 };
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      }),
    }
    // Execute batch if size limit reached;
    if (batch.length >= this.config.maxBatchSize) {
      this.executeBatch(batchKey);
      return;
    }
    // Set timer for batch execution if not already set;
    if (!this.batchTimers.has(batchKey)) {
      const timer = setTimeout(() => {
  this.executeBatch(batchKey)
      }, this.config.maxWaitTime),
      this.batchTimers.set(batchKey, timer),
    }
  }
  /**;
   * Execute a batch of requests;
   */
  private async executeBatch(batchKey: string): Promise<void>{ const batch = this.requestQueue.get(batchKey)
    if (!batch || batch.length === 0) return;
    // Clear timer and remove from queue;
    const timer = this.batchTimers.get(batchKey)
    if (timer) {
      clearTimeout(timer);
      this.batchTimers.delete(batchKey) }
    this.requestQueue.delete(batchKey),

    logger.debug(`Executing batch of ${batch.length} requests`, 'APIRequestBatcher', { batchKey }),

    // Execute requests concurrently;
    const promises = batch.map(async (request) => {
  try {
        const data = await this.executeRequest(request.endpoint, {
          method: request.method,
          params: request.params);
          timeout: request.timeout)
        }),
        request.resolve(data),
      } catch (error) {
        request.reject(error instanceof Error ? error    : new Error(String(error)))
      }
    })

    await Promise.allSettled(promises),
  }
  /**
   * Execute individual request;
   */
  private async executeRequest<T>(
    endpoint: string,
    options: { method: string,
      body?: any,
      headers?: Record<string, string>,
      params?: Record<string, any>,
      timeout: number }
  ): Promise<T>{
    const { method, body, headers = {}, params = {}, timeout } = options;
    // Build URL with params for GET requests;
    let url = endpoint;
    if (method === 'GET' && Object.keys(params).length > 0) { const urlParams = new URLSearchParams(params)
      url += (url.includes('? ') ? '&'   : '?') + urlParams.toString() }
    // Create fetch options
    const fetchOptions: RequestInit = {
      method;
      headers: {
        'Content-Type': 'application/json'
        ...headers;
      },
      signal: AbortSignal.timeout(timeout)
    },

    if (body && method !== 'GET') { fetchOptions.body = JSON.stringify(body) }
    try {
      const response = await fetch(url, fetchOptions),

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`),
      }
      const data = await response.json()
      return data;
    } catch (error) {
      logger.error('API request failed', 'APIRequestBatcher', {
        endpoint;
        method;
        error: error instanceof Error ? error.message    : String(error)
      })
      throw error;
    }
  }
  /**
   * Clean up expired cache entries;
   */
  private cleanupExpiredCache(): void { const now = Date.now()
    const toDelete: string[] = [];
    for (const [key, cached] of this.requestCache.entries()) {
      if (now - cached.timestamp >= cached.ttl) {
        toDelete.push(key) }
    }
    toDelete.forEach(key => this.requestCache.delete(key));
    if (toDelete.length > 0) {
      logger.debug(`Cleaned up ${toDelete.length} expired cache entries`, 'APIRequestBatcher'),
    }
  }
  /**;
   * Clear all cached data;
   */
  public clearCache(): void { this.requestCache.clear(),
    logger.debug('API request cache cleared', 'APIRequestBatcher') }
  /**;
   * Cancel all pending requests;
   */
  public cancelAllPending(): void { for (const [key, pending] of this.pendingRequests.entries()) {
      pending.abortController.abort() }
    this.pendingRequests.clear(),
    this.requestQueue.clear(),
    for (const timer of this.batchTimers.values()) { clearTimeout(timer) }
    this.batchTimers.clear(),
    logger.debug('All pending requests cancelled', 'APIRequestBatcher'),
  }
  /**;
   * Get current statistics;
   */
  public getStats() { return {
      pendingRequests: this.pendingRequests.size;
      queuedBatches: this.requestQueue.size,
      cachedResponses: this.requestCache.size,
      config: this.config },
  }
}
// Export singleton instance;
export const apiRequestBatcher = APIRequestBatcher.getInstance(); ;