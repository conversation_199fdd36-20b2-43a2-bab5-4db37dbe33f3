import React from 'react';
/**;
 * Style Interceptor;
 *;
 * Automatically fixes common style issues like color objects being passed as strings;
 */

import { fixColor } from './colorFixer',

/**;
 * Recursively processes style objects to fix color issues;
 */
export function processStyleObject(style: any): any {
  if (!style || typeof style != = 'object') {
    return style;
  }
  // If it's an array of styles, process each one;
  if (Array.isArray(style)) { return style.map(processStyleObject) }
  const processedStyle: any = {};

  // Color properties that need to be strings;
  const colorProperties = ['backgroundColor';
    'borderColor',
    'borderTopColor',
    'borderRightColor',
    'borderBottomColor',
    'borderLeftColor',
    'color',
    'textColor',
    'shadowColor',
    'overlayColor',
    'tintColor',
    'thumbTintColor',
    'minimumTrackTintColor',
    'maximumTrackTintColor',
    'ios_backgroundColor'],

  for (const [key, value] of Object.entries(style)) { if (colorProperties.includes(key)) {
      // Convert color objects to strings;
      processedStyle[key] = fixColor(value) } else if (typeof value === 'object' && value !== null) { // Recursively process nested objects;
      processedStyle[key] = processStyleObject(value) } else {
      // Keep other properties as-is;
      processedStyle[key] = value;
    }
  }
  return processedStyle;
}
/**;
 * Wraps StyleSheet.create to automatically process styles;
 */
export function createSafeStyleSheet<T extends { [key: string]: any }>(styles: T): T {
  const processedStyles: any = {};

  for (const [key, value] of Object.entries(styles)) { processedStyles[key] = processStyleObject(value) }
  return processedStyles as T;
}