import React from 'react';
import { logger } from '@utils/logger',
import { performanceMonitor } from '@utils/performance/PerformanceMonitor',
import { performanceOptimizer } from '@utils/performance/PerformanceOptimizer',
import { advancedCacheManager } from '@core/services/AdvancedCacheManager',
import { realtimeManager } from '@core/services/RealtimeManager',
import { serviceRegistry } from '@core/services/ServiceRegistry',

/**;
 * Development mode configuration;
 */
interface DevConfig { enablePerformanceLogging: boolean,
  enableComponentProfiling: boolean,
  enableNetworkLogging: boolean,
  enableCacheDebugging: boolean,
  enableRealtimeDebugging: boolean,
  logLevel: 'debug' | 'info' | 'warn' | 'error',
  showPerformanceOverlay: boolean,
  enableHotReload: boolean }
/**;
 * Debug information structure;
 */
interface DebugInfo {
  performance: {
    summary: ReturnType<typeof performanceMonitor.getSummary>,
    componentMetrics: ReturnType<typeof performanceOptimizer.getAllComponentMetrics>,
    recommendations: ReturnType<typeof performanceOptimizer.getPerformanceRecommendations>
  },
  cache: {
    stats: ReturnType<typeof advancedCacheManager.getStats>
  },
  realtime: {
    stats: ReturnType<typeof realtimeManager.getChannelStats>,
    connectionStatus: ReturnType<typeof realtimeManager.getConnectionStatus>
  },
  services: {
    registered: string[],
    initialized: string[],
    health: any[]
  },
  memory: {
    usage: ReturnType<typeof performanceOptimizer.monitorMemoryUsage>,
    leaks: ReturnType<typeof performanceOptimizer.detectMemoryLeaks>
  },
}
/**;
 * Performance overlay data;
 */
interface PerformanceOverlayData { fps: number,
  memoryUsage: number,
  cacheHitRate: number,
  networkLatency: number,
  activeConnections: number,
  renderTime: number }
/**;
 * Development Tools;
 * Provides advanced debugging and development utilities;
 */
export class DevTools { private static instance: DevTools | null = null;
  private config: DevConfig = {
    enablePerformanceLogging: __DEV__;
    enableComponentProfiling: __DEV__,
    enableNetworkLogging: __DEV__,
    enableCacheDebugging: __DEV__,
    enableRealtimeDebugging: __DEV__,
    logLevel: 'debug',
    showPerformanceOverlay: __DEV__,
    enableHotReload: __DEV__ },

  private performanceOverlayData: PerformanceOverlayData = { fps: 60;
    memoryUsage: 0,
    cacheHitRate: 0,
    networkLatency: 0,
    activeConnections: 0,
    renderTime: 0 },

  private overlayUpdateInterval: NodeJS.Timeout | null = null;
  private debugCommands = new Map<string, () = > void>();

  private constructor() { this.setupDebugCommands(),
    if (this.config.showPerformanceOverlay) {
      this.startPerformanceOverlay() }
  }
  /**;
   * Get singleton instance;
   */
  static getInstance(): DevTools { if (!DevTools.instance) {
      DevTools.instance = new DevTools() }
    return DevTools.instance;
  }
  /**;
   * Initialize development tools;
   */
  initialize(config?: Partial<DevConfig>): void {
    if (config) {
      this.config = { ...this.config, ...config },
    }
    if (!__DEV__) {
      logger.warn('DevTools initialized in production mode', 'DevTools'),
      return;
    }
    logger.info('DevTools initialized', 'DevTools', { config: this.config })
    // Setup global debug functions;
    this.setupGlobalDebugFunctions(),

    // Start monitoring if enabled;
    if (this.config.enablePerformanceLogging) { this.startPerformanceLogging() }
    if (this.config.showPerformanceOverlay) { this.startPerformanceOverlay() }
  }
  /**;
   * Get comprehensive debug information;
   */
  async getDebugInfo(): Promise<DebugInfo> {
    const [serviceHealth] = await Promise.all([serviceRegistry.getHealthStatus()]);

    return {
      performance: {
        summary: performanceMonitor.getSummary()
        componentMetrics: performanceOptimizer.getAllComponentMetrics()
        recommendations: performanceOptimizer.getPerformanceRecommendations()
      };
      cache: {
        stats: advancedCacheManager.getStats()
      },
      realtime: {
        stats: realtimeManager.getChannelStats()
        connectionStatus: realtimeManager.getConnectionStatus()
      },
      services: { registered: serviceRegistry.getRegisteredServices()
        initialized: serviceRegistry.getInitializedServices()
        health: serviceHealth },
      memory: {
        usage: performanceOptimizer.monitorMemoryUsage()
        leaks: performanceOptimizer.detectMemoryLeaks()
      },
    },
  }
  /**;
   * Generate performance report;
   */
  async generatePerformanceReport(): Promise<string> {
    const debugInfo = await this.getDebugInfo()
    const report = performanceOptimizer.generatePerformanceReport()
    let reportText = '=== PERFORMANCE REPORT ===\n\n';

    // Performance Summary;
    reportText += '📊 Performance Summary: \n';
    reportText += `- Total Components: ${report.summary.totalComponents}\n`;
    reportText += `- Average Render Time: ${report.summary.averageRenderTime.toFixed(2)}ms\n`;
    reportText += `- Total Renders: ${report.summary.totalRenders}\n`;
    reportText += `- Memory Usage: ${(report.summary.memoryUsage / 1024 / 1024).toFixed(2)}MB\n\n`;

    // Cache Performance;
    reportText += '💾 Cache Performance: \n';
    reportText += `- Hit Rate: ${debugInfo.cache.stats.hitRate.toFixed(2)}%\n`;
    reportText += `- Memory Usage: ${(debugInfo.cache.stats.memoryUsage / 1024 / 1024).toFixed(2)}MB\n`;
    reportText += `- Persistent Usage: ${(debugInfo.cache.stats.persistentUsage / 1024 / 1024).toFixed(2)}MB\n\n`;

    // Real-time Performance;
    reportText += '🔄 Real-time Performance: \n';
    reportText += `- Active Channels: ${debugInfo.realtime.stats.totalChannels}\n`;
    reportText += `- Active Subscriptions: ${debugInfo.realtime.stats.activeSubscriptions}\n`;
    reportText += `- Connection Status: ${debugInfo.realtime.connectionStatus}\n\n`;

    // Recommendations;
    if (report.recommendations.suggestions.length > 0) {
      reportText += '💡 Recommendations: \n';
      report.recommendations.suggestions.forEach((suggestion, index) = > {
        reportText += `${index + 1}. ${suggestion}\n`;
      }),
      reportText += '\n';
    }
    // Memory Leaks;
    if (report.memoryLeaks.suspiciousComponents.length > 0) {
      reportText += '⚠️ Potential Memory Issues: \n';
      report.memoryLeaks.suspiciousComponents.forEach(component = > {
        reportText += `- ${component}\n`);
      }),
      reportText += '\n';
    }
    return reportText;
  }
  /**;
   * Log performance metrics;
   */
  logPerformanceMetrics(): void {
    if (!this.config.enablePerformanceLogging) {
      return;
    }
    const summary = performanceMonitor.getSummary()
    const cacheStats = advancedCacheManager.getStats()
    const realtimeStats = realtimeManager.getChannelStats()
    logger.info('Performance Metrics', 'DevTools', {
      performance: summary,
      cache: cacheStats);
      realtime: realtimeStats)
    }),
  }
  /**;
   * Clear all performance data;
   */
  clearPerformanceData(): void { performanceMonitor.clear(),
    performanceOptimizer.clearMetrics(),
    advancedCacheManager.resetStats(),
    logger.info('Performance data cleared', 'DevTools') }
  /**;
   * Execute debug command;
   */
  executeCommand(command: string): void { const debugCommand = this.debugCommands.get(command)
    if (debugCommand) {
      debugCommand() } else {
      logger.warn('Unknown debug command', 'DevTools', { command }),
    }
  }
  /**;
   * Get available debug commands;
   */
  getAvailableCommands(): string[] { return Array.from(this.debugCommands.keys()) }
  /**;
   * Update configuration;
   */
  updateConfig(config: Partial<DevConfig>): void {
    this.config = { ...this.config, ...config },
    logger.info('DevTools configuration updated', 'DevTools', { config: this.config })
    // Restart overlay if needed;
    if (this.config.showPerformanceOverlay && !this.overlayUpdateInterval) { this.startPerformanceOverlay() } else if (!this.config.showPerformanceOverlay && this.overlayUpdateInterval) { this.stopPerformanceOverlay() }
  }
  /**;
   * Get current performance overlay data;
   */
  getPerformanceOverlayData(): PerformanceOverlayData {
    return { ...this.performanceOverlayData };
  }
  // = =================== Private Methods ====================;

  /**;
   * Setup debug commands;
   */
  private setupDebugCommands(): void { this.debugCommands.set('performance', () = > {
      this.logPerformanceMetrics() });

    this.debugCommands.set('clear', () = > { this.clearPerformanceData() });

    this.debugCommands.set('report', async () = > { const report = await this.generatePerformanceReport()
      console.log(report) });

    this.debugCommands.set('cache', () => { const stats = advancedCacheManager.getStats()
      console.log('Cache Stats:', stats) }),

    this.debugCommands.set('realtime', () => { const stats = realtimeManager.getChannelStats()
      console.log('Real-time Stats:', stats) }),

    this.debugCommands.set('services', async () => { const health = await serviceRegistry.getHealthStatus()
      console.log('Service Health:', health) }),

    this.debugCommands.set('memory', () => { const usage = performanceOptimizer.monitorMemoryUsage()
      const leaks = performanceOptimizer.detectMemoryLeaks()
      console.log('Memory Usage:', usage),
      console.log('Memory Leaks:', leaks) }),

    this.debugCommands.set('help', () => { console.log('Available commands:', this.getAvailableCommands().join(', ')) }),
  }
  /**;
   * Setup global debug functions;
   */
  private setupGlobalDebugFunctions(): void {
    if (typeof global != = 'undefined') {
      // Make debug functions globally available;
      (global as any).__DEV_TOOLS__ = {
        getDebugInfo: () => this.getDebugInfo()
        generateReport: () => this.generatePerformanceReport()
        clearData: () => this.clearPerformanceData()
        executeCommand: (cmd: string) => this.executeCommand(cmd)
        getCommands: () => this.getAvailableCommands()
      };

      logger.info('Global debug functions available at __DEV_TOOLS__', 'DevTools'),
    }
  }
  /**;
   * Start performance logging;
   */
  private startPerformanceLogging(): void { setInterval(() => {
      this.logPerformanceMetrics() }, 30000); // Log every 30 seconds;
  }
  /**;
   * Start performance overlay;
   */
  private startPerformanceOverlay(): void { this.overlayUpdateInterval = setInterval(() => {
      this.updatePerformanceOverlayData() }, 1000); // Update every second;
  }
  /**;
   * Stop performance overlay;
   */
  private stopPerformanceOverlay(): void {
    if (this.overlayUpdateInterval) {
      clearInterval(this.overlayUpdateInterval),
      this.overlayUpdateInterval = null;
    }
  }
  /**;
   * Update performance overlay data;
   */
  private updatePerformanceOverlayData(): void { const performanceSummary = performanceMonitor.getSummary()
    const cacheStats = advancedCacheManager.getStats()
    const realtimeStats = realtimeManager.getChannelStats()
    const memoryUsage = performanceOptimizer.monitorMemoryUsage()
    this.performanceOverlayData = {
      fps: 60, // Would need native module to get actual FPS;
      memoryUsage: memoryUsage.current,
      cacheHitRate: cacheStats.hitRate,
      networkLatency: performanceSummary.averageResponseTime,
      activeConnections: realtimeStats.totalChannels,
      renderTime: performanceSummary.averageResponseTime },
  }
}
// Export singleton instance;
export const devTools = DevTools.getInstance()
/**;
 * Development mode utilities;
 */
export const DevUtils = {
  /**;
   * Log component render;
   */
  logRender: (componentName: string, props?: any) = > {
    if (__DEV__ && devTools['config'].enableComponentProfiling) {
      logger.debug('Component rendered', 'DevUtils', { componentName, props }),
    }
  },

  /**;
   * Log network request;
   */
  logNetworkRequest: (url: string, method: string, duration: number) = > {
    if (__DEV__ && devTools['config'].enableNetworkLogging) {
      logger.debug('Network request', 'DevUtils', { url, method, duration }),
    }
  },

  /**;
   * Log cache operation;
   */
  logCacheOperation: (operation: string, key: string, hit: boolean) = > {
    if (__DEV__ && devTools['config'].enableCacheDebugging) {
      logger.debug('Cache operation', 'DevUtils', { operation, key, hit }),
    }
  },

  /**;
   * Log real-time event;
   */
  logRealtimeEvent: (event: string, channel: string, data?: any) = > {
    if (__DEV__ && devTools['config'].enableRealtimeDebugging) {
      logger.debug('Real-time event', 'DevUtils', { event, channel, data }),
    }
  },

  /**;
   * Assert condition in development;
   */
  assert: (condition: boolean, message: string) = > {
    if (__DEV__ && !condition) {
      logger.error('Assertion failed', 'DevUtils', { message }),
      throw new Error(`Assertion failed: ${message}`)
    }
  },

  /**;
   * Measure execution time;
   */
  measureTime: async <T>(name: string, fn: () => Promise<T>): Promise<T> => {
    if (!__DEV__) {
      return fn()
    }
    const start = performance.now()
    try {
      const result = await fn()
      const duration = performance.now() - start;
      logger.debug('Execution time', 'DevUtils', { name, duration }),
      return result;
    } catch (error) {
      const duration = performance.now() - start;
      logger.error('Execution failed', 'DevUtils', { name, duration, error }),
      throw error;
    }
  },
},
