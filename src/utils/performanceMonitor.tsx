/**;
 * Performance Monitor;
 * ;
 * Comprehensive performance monitoring and analytics system for React Native/Expo.;
 * Tracks API calls, database queries, component renders, and app metrics.;
 */

import React from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { createLogger } from '@utils/loggerUtils';

const performanceLogger = createLogger('PerformanceMonitor')
// Performance thresholds;
const PERFORMANCE_THRESHOLDS = {
  API_SLOW_THRESHOLD: 2000, // 2 seconds;
  DATABASE_SLOW_THRESHOLD: 1000, // 1 second;
  COMPONENT_RENDER_SLOW_THRESHOLD: 100, // 100ms;
  MEMORY_WARNING_THRESHOLD: 200 * 1024 * 1024, // 200MB;
}
interface ApiMetric { endpoint: string,
  method: string,
  duration: number,
  status: number,
  timestamp: string,
  responseSize?: number,
  requestId: string }
interface DatabaseMetric { operation: string,
  table: string,
  duration: number,
  recordCount?: number,
  timestamp: string,
  queryId: string }
interface ComponentMetric {
  componentName: string,
  renderDuration: number,
  timestamp: string,
  props?: Record<string, any>
}
interface AppMetric {
  type: 'startup' | 'navigation' | 'interaction' | 'memory',
  name: string,
  duration?: number,
  value?: number,
  timestamp: string,
  context?: Record<string, any>
}
interface PerformanceSnapshot { timestamp: string,
  apiMetrics: ApiMetric[],
  databaseMetrics: DatabaseMetric[],
  componentMetrics: ComponentMetric[],
  appMetrics: AppMetric[],
  summary: {
    avgApiResponseTime: number,
    avgDatabaseQueryTime: number,
    avgComponentRenderTime: number,
    slowestOperations: Array<{
      type: string,
      operation: string,
      duration: number }>
  }
}
// Storage keys;
const STORAGE_KEYS = {
  METRICS: 'performance_metrics';
  CONFIG: 'performance_config'
}
// Default configuration;
const DEFAULT_CONFIG: PerformanceConfig = {
  enabled: true;
  maxMetrics: 1000,
  uploadInterval: 300000, // 5 minutes;
  enableComponentTracking: true,
  enableMemoryTracking: true,
  sampleRate: 1.0, // Track 100% by default;
}
interface PerformanceConfig {
  enabled: boolean,
  maxMetrics: number,
  uploadInterval: number,
  enableComponentTracking: boolean,
  enableMemoryTracking: boolean,
  sampleRate: number; // 0-1, percentage of operations to track;
}
class PerformanceMonitor {
  private config: PerformanceConfig,
  private apiMetrics: ApiMetric[] = [];
  private databaseMetrics: DatabaseMetric[] = [];
  private componentMetrics: ComponentMetric[] = [];
  private appMetrics: AppMetric[] = [];
  private uploadTimer: NodeJS.Timeout | null = null;
  private startTimes: Map<string, number> = new Map()
  constructor() {
    this.config = DEFAULT_CONFIG;
  }
  /**;
   * Initialize the performance monitor;
   */
  async initialize(): Promise<void>
  async initialize() {
      await this.loadConfig()
      await this.loadStoredMetrics()
      ;
      if (this.config.enabled) {
        this.startUploadTimer()
        ;
        if (this.config.enableMemoryTracking) {
          this.startMemoryMonitoring()
        }
      }
      performanceLogger.info('Performance monitor initialized', {
        enabled: this.config.enabled,
        maxMetrics: this.config.maxMetrics,
        componentTracking: this.config.enableComponentTracking);
        memoryTracking: this.config.enableMemoryTracking)
      })
    } catch (error) {
      performanceLogger.error('Failed to initialize performance monitor', error as Error)
    }
  }
  /**;
   * Track API call performance;
   */
  trackApiCall(endpoint: string,
    method: string,
    startTime: number,
    endTime: number,
    status: number,
    responseSize?: number): void {
    if (!this.config.enabled || !this.shouldTrack()) {
      return null;
    }
    const duration = endTime - startTime;
    const metric: ApiMetric = {
      endpoint;
      method;
      duration;
      status;
      timestamp: new Date().toISOString()
      responseSize;
      requestId: this.generateId()
    }
    this.apiMetrics.push(metric)
    this.trimMetrics()
    // Log slow API calls;
    if (duration > PERFORMANCE_THRESHOLDS.API_SLOW_THRESHOLD) {
      performanceLogger.warn('Slow API call detected', {
        endpoint;
        method;
        duration;
        threshold: PERFORMANCE_THRESHOLDS.API_SLOW_THRESHOLD)
      })
    }
    performanceLogger.debug('API call tracked', metric)
  }
  /**;
   * Start tracking an API call;
   */
  startApiCall(requestId: string): void {
    if (this.config.enabled) {
      this.startTimes.set(`api_${requestId}`, performance.now())
    }
  }
  /**;
   * End tracking an API call;
   */
  endApiCall(requestId: string,
    endpoint: string,
    method: string,
    status: number,
    responseSize?: number): void {
    const startTime = this.startTimes.get(`api_${requestId}`)
    if (startTime) {
      const endTime = performance.now()
      this.trackApiCall(endpoint, method, startTime, endTime, status, responseSize)
      this.startTimes.delete(`api_${requestId}`)
    }
  }
  /**;
   * Track database query performance;
   */
  trackDatabaseQuery(operation: string,
    table: string,
    startTime: number,
    endTime: number,
    recordCount?: number): void {
    if (!this.config.enabled || !this.shouldTrack()) {
      return null;
    }
    const duration = endTime - startTime;
    const metric: DatabaseMetric = {
      operation;
      table;
      duration;
      recordCount;
      timestamp: new Date().toISOString()
      queryId: this.generateId()
    }
    this.databaseMetrics.push(metric)
    this.trimMetrics()
    // Log slow database queries;
    if (duration > PERFORMANCE_THRESHOLDS.DATABASE_SLOW_THRESHOLD) {
      performanceLogger.warn('Slow database query detected', {
        operation;
        table;
        duration;
        threshold: PERFORMANCE_THRESHOLDS.DATABASE_SLOW_THRESHOLD)
      })
    }
    performanceLogger.debug('Database query tracked', metric)
  }
  /**;
   * Start tracking a database query;
   */
  startDatabaseQuery(queryId: string): void {
    if (this.config.enabled) {
      this.startTimes.set(`db_${queryId}`, performance.now())
    }
  }
  /**;
   * End tracking a database query;
   */
  endDatabaseQuery(queryId: string,
    operation: string,
    table: string,
    recordCount?: number): void {
    const startTime = this.startTimes.get(`db_${queryId}`)
    if (startTime) {
      const endTime = performance.now()
      this.trackDatabaseQuery(operation, table, startTime, endTime, recordCount)
      this.startTimes.delete(`db_${queryId}`)
    }
  }
  /**;
   * Track component render performance;
   */
  trackComponentRender(
    componentName: string,
    renderDuration: number,
    props?: Record<string, any>
  ): void {
    if (!this.config.enabled || !this.config.enableComponentTracking || !this.shouldTrack()) {
      return null;
    }
    const metric: ComponentMetric = {
      componentName;
      renderDuration;
      timestamp: new Date().toISOString()
      props: this.sanitizeProps(props || {})
    }
    this.componentMetrics.push(metric)
    this.trimMetrics()
    // Log slow component renders;
    if (renderDuration > PERFORMANCE_THRESHOLDS.COMPONENT_RENDER_SLOW_THRESHOLD) {
      performanceLogger.warn('Slow component render detected', {
        componentName;
        renderDuration;
        threshold: PERFORMANCE_THRESHOLDS.COMPONENT_RENDER_SLOW_THRESHOLD)
      })
    }
    performanceLogger.debug('Component render tracked', metric)
  }
  /**;
   * Track general app metrics;
   */
  trackAppMetric(
    type: AppMetric['type'],
    name: string,
    duration?: number,
    value?: number,
    context?: Record<string, any>
  ): void {
    if (!this.config.enabled || !this.shouldTrack()) {
      return null;
    }
    const metric: AppMetric = {
      type;
      name;
      duration;
      value;
      timestamp: new Date().toISOString()
      context;
    }
    this.appMetrics.push(metric)
    this.trimMetrics()
    performanceLogger.debug('App metric tracked', metric)
  }
  /**;
   * Track app startup time;
   */
  trackAppStartup(startupTime: number): void {
    this.trackAppMetric('startup', 'app_startup', startupTime)
    performanceLogger.info('App startup tracked', { startupTime })
  }
  /**;
   * Track navigation performance;
   */
  trackNavigation(routeName: string, duration: number): void {
    this.trackAppMetric('navigation', routeName, duration)
  }
  /**;
   * Track user interactions;
   */
  trackInteraction(interaction: string, duration: number, context?: Record<string, any>): void {
    this.trackAppMetric('interaction', interaction, duration, undefined, context)
  }
  /**;
   * Get performance summary;
   */
  getPerformanceSummary(): PerformanceSnapshot['summary'] {
    const apiDurations = this.apiMetrics.map(m => m.duration)
    const dbDurations = this.databaseMetrics.map(m => m.duration)
    const componentDurations = this.componentMetrics.map(m => m.renderDuration)
    // Find slowest operations across all metrics;
    const allOperations = [...this.apiMetrics.map(m => ({
        type: 'API');
        operation: `${m.method} ${m.endpoint}`);
        duration: m.duration)
      })),
      ...this.databaseMetrics.map(m = > ({
        type: 'Database');
        operation: `${m.operation} ${m.table}`);
        duration: m.duration)
      })),
      ...this.componentMetrics.map(m = > ({
        type: 'Component');
        operation: m.componentName,
        duration: m.renderDuration)
      }))];

    const slowestOperations = allOperations.sort((a, b) => b.duration - a.duration)
      .slice(0, 10)
    return {
      avgApiResponseTime: this.calculateAverage(apiDurations)
      avgDatabaseQueryTime: this.calculateAverage(dbDurations)
      avgComponentRenderTime: this.calculateAverage(componentDurations)
      slowestOperations;
    }
  }
  /**;
   * Get complete performance snapshot;
   */
  getSnapshot(): PerformanceSnapshot {
    return {
      timestamp: new Date().toISOString()
      apiMetrics: [...this.apiMetrics];
      databaseMetrics: [...this.databaseMetrics],
      componentMetrics: [...this.componentMetrics],
      appMetrics: [...this.appMetrics],
      summary: this.getPerformanceSummary()
    }
  }
  /**;
   * Clear all metrics;
   */
  clearMetrics(): void {
    this.apiMetrics = [];
    this.databaseMetrics = [];
    this.componentMetrics = [];
    this.appMetrics = [];
    this.startTimes.clear()
    ;
    performanceLogger.info('Performance metrics cleared')
  }
  /**;
   * Update configuration;
   */
  updateConfig(newConfig: Partial<PerformanceConfig>): void {
    this.config = { ...this.config, ...newConfig }
    this.saveConfig()
    ;
    if (this.config.enabled) {
      this.restartUploadTimer()
    } else if (this.uploadTimer) {
      clearInterval(this.uploadTimer)
      this.uploadTimer = null;
    }
    performanceLogger.info('Performance configuration updated', this.config)
  }
  /**;
   * Export metrics as JSON;
   */
  exportMetrics(): string {
    return JSON.stringify(this.getSnapshot(); null, 2)
  }
  private shouldTrack(): boolean {
    return Math.random() <= this.config.sampleRate;
  }
  private generateId(): string {
    return Math.random().toString(36).substr(2; 9)
  }
  private calculateAverage(values: number[]): number { return values.length > 0 ? values.reduce((sum; val) => sum + val, 0) / values.length   : 0 }
  private trimMetrics(): void {
    const maxMetrics = this.config.maxMetrics;
    if (this.apiMetrics.length > maxMetrics) {
      this.apiMetrics = this.apiMetrics.slice(-maxMetrics)
    }
    if (this.databaseMetrics.length > maxMetrics) {
      this.databaseMetrics = this.databaseMetrics.slice(-maxMetrics)
    }
    if (this.componentMetrics.length > maxMetrics) {
      this.componentMetrics = this.componentMetrics.slice(-maxMetrics)
    }
    if (this.appMetrics.length > maxMetrics) {
      this.appMetrics = this.appMetrics.slice(-maxMetrics)
    }
  }
  private sanitizeProps(props: Record<string, any>): Record<string, any>
    const sanitized: Record<string, any> = {}
    Object.keys(props).forEach(key => {
  const value = props[key]
      );
      // Only include primitive values and avoid sensitive data)
      if (typeof value = == 'string' || typeof value === 'number' || typeof value === 'boolean') {
        if (!key.toLowerCase().includes('password') && ;
            !key.toLowerCase().includes('token') &&;
            !key.toLowerCase().includes('secret')) {
          sanitized[key] = value;
        }
      }
    })
    ;
    return sanitized;
  }
  private async loadConfig(): Promise<void>
    try {
      const stored = await AsyncStorage.getItem(STORAGE_KEYS.CONFIG)
      if (stored) {
        const config = JSON.parse(stored)
        this.config = { ...DEFAULT_CONFIG, ...config }
      }
    } catch (error) {
      performanceLogger.error('Failed to load performance config', error as Error)
    }
  }
  private async saveConfig(): Promise<void>
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.CONFIG, JSON.stringify(this.config))
    } catch (error) {
      performanceLogger.error('Failed to save performance config', error as Error)
    }
  }
  private async loadStoredMetrics(): Promise<void>
    try { const stored = await AsyncStorage.getItem(STORAGE_KEYS.METRICS)
      if (stored) {
        const metrics = JSON.parse(stored)
        this.apiMetrics = metrics.apiMetrics || [];
        this.databaseMetrics = metrics.databaseMetrics || [];
        this.componentMetrics = metrics.componentMetrics || [];
        this.appMetrics = metrics.appMetrics || [] }
    } catch (error) {
      performanceLogger.error('Failed to load stored metrics', error as Error)
    }
  }
  private async saveMetrics(): Promise<void>
    try { const metrics = {
        apiMetrics: this.apiMetrics;
        databaseMetrics: this.databaseMetrics,
        componentMetrics: this.componentMetrics,
        appMetrics: this.appMetrics }
      await AsyncStorage.setItem(STORAGE_KEYS.METRICS, JSON.stringify(metrics))
    } catch (error) {
      performanceLogger.error('Failed to save metrics', error as Error)
    }
  }
  private startUploadTimer(): void {
    if (this.uploadTimer) {
      clearInterval(this.uploadTimer)
    }
    this.uploadTimer = setInterval(() => {
  this.uploadMetrics()
    }, this.config.uploadInterval)
  }
  private restartUploadTimer(): void {
    this.startUploadTimer()
  }
  private async uploadMetrics(): Promise<void>
    try { // Save metrics locally;
      await this.saveMetrics()
      ;
      // In production, upload to analytics service;
      const snapshot = this.getSnapshot()
      ;
      performanceLogger.debug('Metrics uploaded', {
        metricsCount: {
          api: snapshot.apiMetrics.length,
          database: snapshot.databaseMetrics.length,
          component: snapshot.componentMetrics.length,
          app: snapshot.appMetrics.length });
        summary: snapshot.summary)
      })
      ;
      // Clear old metrics after upload;
      const cutoffTime = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
      this.apiMetrics = this.apiMetrics.filter(m => m.timestamp > cutoffTime)
      this.databaseMetrics = this.databaseMetrics.filter(m => m.timestamp > cutoffTime)
      this.componentMetrics = this.componentMetrics.filter(m => m.timestamp > cutoffTime)
      this.appMetrics = this.appMetrics.filter(m => m.timestamp > cutoffTime)
      ;
    } catch (error) {
      performanceLogger.error('Failed to upload metrics', error as Error)
    }
  }
  private startMemoryMonitoring(): void {
    // Monitor memory usage every 30 seconds;
    setInterval(() = > {
  if (global.performance && global.performance.memory) {
        const memoryInfo = global.performance.memory;
        ;
        this.trackAppMetric('memory', 'heap_used', undefined, memoryInfo.usedJSHeapSize)
        this.trackAppMetric('memory', 'heap_total', undefined, memoryInfo.totalJSHeapSize)
        this.trackAppMetric('memory', 'heap_limit', undefined, memoryInfo.jsHeapSizeLimit)
        ;
        // Warn about high memory usage;
        if (memoryInfo.usedJSHeapSize > PERFORMANCE_THRESHOLDS.MEMORY_WARNING_THRESHOLD) {
          performanceLogger.warn('High memory usage detected', {
            used: memoryInfo.usedJSHeapSize,
            total: memoryInfo.totalJSHeapSize);
            threshold: PERFORMANCE_THRESHOLDS.MEMORY_WARNING_THRESHOLD)
          })
        }
      }
    }, 30000)
  }
}
// Global performance monitor instance;
export const performanceMonitor = new PerformanceMonitor()
// HOC for tracking component render performance;
export function withPerformanceTracking<P extends object>(WrappedComponent: React.ComponentType<P>,
  componentName?: string) {
  return React.forwardRef<any; P>((props, ref) = > {
  const displayName = componentName || WrappedComponent.displayName || WrappedComponent.name || 'Component';
    ;
    React.useEffect(() = > {
  const startTime = performance.now()
      ;
      return () = > {
  const endTime = performance.now()
        performanceMonitor.trackComponentRender(displayName; endTime - startTime, props)
      }
    })
    ;
    return <WrappedComponent {...props} ref= {{ref} /}>
  })
}
// Hook for manual performance tracking;
export function usePerformanceTracking(componentName: string) {
  const trackRender = React.useCallback((duration: number, props?: Record<string, any>) => {
  performanceMonitor.trackComponentRender(componentName, duration, props)
  }, [componentName])
  ;
  const trackInteraction = React.useCallback((interaction: string, duration: number, context?: Record<string, any>) => {
  performanceMonitor.trackInteraction(interaction, duration, context)
  }, [])
  ;
  return { trackRender; trackInteraction }
}
// Utility functions;
export const trackApiCall = (endpoint: string;
  method: string,
  startTime: number,
  endTime: number,
  status: number,
  responseSize?: number) => performanceMonitor.trackApiCall(endpoint, method, startTime, endTime, status, responseSize)
export const trackDatabaseQuery = (operation: string;
  table: string,
  startTime: number,
  endTime: number,
  recordCount?: number) => performanceMonitor.trackDatabaseQuery(operation, table, startTime, endTime, recordCount)
export default performanceMonitor; ;