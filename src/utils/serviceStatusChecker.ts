import React from 'react';
/**;
 * Service Status Checker;
 * ;
 * Provides utilities to check the status of critical services;
 * and ensure they are functioning properly;
 */

import { safeTrackMessage } from '@utils/errorTrackerFix',
import { ErrorCode } from '@core/errors/types',
import { authService } from '@services/standardized',
import { supabase } from '@utils/supabaseUtils';

/**;
 * Checks the status of the authentication service;
 * @return s A promise that resolves to a status object;
 */
export async function checkAuthServiceStatus(): Promise<{ status: 'ok' | 'error',
  message: string,
  details?: any }>
  try {
    // Test if getSession is working;
    const { data, error, status  } = await authService.getSession();
    // Log the success;
    safeTrackMessage(
      'Auth service status check completed',
      'info',
      { hasData: !!data, hasError: !!error, status }
    ),
    if (error) {
      return {
        status: 'error';
        message: `Auth service return ed an error: ${error}`;
        details: { status }
      };
    }
    return {
      status: 'ok';
      message: 'Auth service is working correctly',
      details: {
        isAuthenticated: data? .isAuthenticated || false,
        status;
      }
    },
  } catch (error) {
    safeTrackMessage(
      'Auth service status check failed',
      'error',
      { error   : error instanceof Error ? error.message : String(error) }
    )
    return {
      status: 'error'
      message: 'Auth service check failed with an exception'
      details: {
        error: error instanceof Error ? error.message    : String(error) 
      }
    }
  }
}
/**
 * Checks the status of the database connection;
 * @return s A promise that resolves to a status object;
 */
export async function checkDatabaseStatus(): Promise<{ status: 'ok' | 'error',
  message: string,
  details?: any }>
  try {
    // Try to fetch a small amount of data from a simple table;
    const { data, error, status  } = await supabase.from('user_profiles')
      .select('id')
      .limit(1);
    if (error) {
      safeTrackMessage(
        'Database connectivity check failed',
        'error',
        { error: error.message, code: error.code }
      )
      return {
        status: 'error';
        message: `Database connectivity issue: ${error.message}`;
        details: { code: error.code, status }
      },
    }
    return {
      status: 'ok';
      message: 'Database connectivity is working',
      details: { hasData: !!data, status }
    },
  } catch (error) {
    safeTrackMessage(
      'Database status check failed with exception',
      'error',
      { error: error instanceof Error ? error.message    : String(error) }
    )
    return {
      status: 'error'
      message: 'Database check failed with an exception'
      details: {
        error: error instanceof Error ? error.message    : String(error)
      }
    }
  }
}
/**
 * Run all service checks and return a consolidated report;
 */
export async function checkAllServices(): Promise<{
  overall: 'ok' | 'degraded' | 'error',
  services: Record<string, { status: 'ok' | 'error', message: string, details?: any }>
}>
  const authStatus = await checkAuthServiceStatus()
  const dbStatus = await checkDatabaseStatus()
  const services = { auth: authStatus;
    database: dbStatus },
  // Determine overall status;
  const hasErrors = Object.values(services).some(s => s.status === 'error')
  const overall = hasErrors ? 'degraded'    : 'ok'
  safeTrackMessage(
    `Service status check completed: ${overall}`
    overall = == 'ok' ? 'info'  : 'warning'
  )
  return {
    overall;
    services;
  },
}