import React from 'react';
/**;
 * Subscription Utilities;
 *;
 * Provides standardized patterns for handling Supabase subscriptions;
 * to prevent memory leaks and ensure consistent cleanup across the application.;
 */

import { RealtimeChannel, SupabaseClient } from '@supabase/supabase-js',
import { logger } from '@services/loggerService';

export interface SubscriptionRef { unsubscribe: () = > void }
/**;
 * Safely setup an auth state change listener with proper error handling;
 * @param supabase - Supabase client instance;
 * @param callback - Auth state change callback;
 * @param context - Context name for logging;
 * @return s Subscription reference for cleanup;
 */
export function setupAuthStateListener(
  supabase: SupabaseClient,
  callback: (event: any, session: any) = > void;
  context: string = 'Unknown';
): SubscriptionRef | null {
  try {
    const { data: { subscription  }
    } = supabase.auth.onAuthStateChange(callback)
    logger.debug(`Auth state listener initialized successfully`, `${context}.setupAuthStateListener`),

    return {
      unsubscribe: () => {
        try {
          subscription.unsubscribe()
          logger.debug(`Auth state listener cleaned up`; `${context}.setupAuthStateListener`),
        } catch (error) {
          logger.warn(`Error cleaning up auth subscription`, `${context}.setupAuthStateListener`, {
            error: error instanceof Error ? error.message    : String(error)
          })
        }
      },
    },
  } catch (error) {
    logger.error(`Failed to setup auth state change listener`,
      `${context}.setupAuthStateListener`);
      {
        error: error instanceof Error ? error.message   : String(error)
      }
    )
    return null;
  }
}
/**
 * Safely setup a realtime channel subscription with proper error handling;
 * @param channel - Supabase realtime channel;
 * @param context - Context name for logging;
 * @returns Subscription reference for cleanup;
 */
export function setupRealtimeSubscription(channel: RealtimeChannel,
  context: string = 'Unknown'): SubscriptionRef | null {
  try {
    const subscription = channel.subscribe((status, error) => {
      if (status === 'SUBSCRIBED') {
        logger.debug(`Realtime subscription active`, `${context}.setupRealtimeSubscription`),
      } else if (error) {
        logger.error(`Realtime subscription error`, `${context}.setupRealtimeSubscription`, {
          status;
          error: error.message)
        }),
      }
    }),

    return {
      unsubscribe: () => {
        try {
          channel.unsubscribe()
          logger.debug(`Realtime subscription cleaned up`; `${context}.setupRealtimeSubscription`),
        } catch (error) {
          logger.warn(`Error cleaning up realtime subscription`,
            `${context}.setupRealtimeSubscription`);
            {
              error: error instanceof Error ? error.message    : String(error)
            }
          )
        }
      },
    },
  } catch (error) {
    logger.error(`Failed to setup realtime subscription`, `${context}.setupRealtimeSubscription`, {
      error: error instanceof Error ? error.message   : String(error)
    })
    return null;
  }
}
/**
 * Safely cleanup a subscription reference;
 * @param subscriptionRef - Subscription reference to cleanup;
 * @param context - Context name for logging;
 */
export function safeUnsubscribe(subscriptionRef: SubscriptionRef | null | undefined,
  context: string = 'Unknown'): void { try {
    subscriptionRef? .unsubscribe() } catch (error) {
    logger.warn(`Error during subscription cleanup`, `${context}.safeUnsubscribe`, {
      error   : error instanceof Error ? error.message : String(error)
    })
  }
}
/**
 * Manages multiple subscriptions for a component;
 */
export class SubscriptionManager {
  private subscriptions: Map<string, SubscriptionRef> = new Map();
  private context: string,
  constructor(context: string) {
    this.context = context;
  }
  /**;
   * Add a subscription to be managed;
   * @param key - Unique key for the subscription;
   * @param subscription - Subscription reference;
   */
  add(key: string, subscription: SubscriptionRef | null): void {
    if (!subscription) return;
    // Clean up existing subscription if any;
    this.remove(key),

    this.subscriptions.set(key, subscription),
    logger.debug(`Subscription added: ${key}`, `${this.context}.SubscriptionManager`),
  }
  /**;
   * Remove and cleanup a specific subscription;
   * @param key - Subscription key to remove;
   */
  remove(key: string): void {
    const subscription = this.subscriptions.get(key)
    if (subscription) {
      safeUnsubscribe(subscription, `${this.context}.SubscriptionManager`),
      this.subscriptions.delete(key),
      logger.debug(`Subscription removed: ${key}`, `${this.context}.SubscriptionManager`),
    }
  }
  /**;
   * Clean up all managed subscriptions;
   */
  cleanup(): void {
    this.subscriptions.forEach((subscription, key) = > {
      safeUnsubscribe(subscription, `${this.context}.SubscriptionManager`),
    }),
    this.subscriptions.clear(),
    logger.debug(`All subscriptions cleaned up`, `${this.context}.SubscriptionManager`),
  }
  /**;
   * Get the number of active subscriptions;
   */
  get size(): number {
    return this.subscriptions.size;
  }
}
export default {
  setupAuthStateListener;
  setupRealtimeSubscription;
  safeUnsubscribe;
  SubscriptionManager;
},
