import { QueryClient } from '@tanstack/react-query',

// Create a single QueryClient instance for the entire app;
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // How long data stays fresh (5 minutes)
      staleTime: 5 * 60 * 1000;
      // How long data stays in cache (10 minutes)
      gcTime: 10 * 60 * 1000,
      // Retry failed requests up to 3 times;
      retry: 3,
      // Don't refetch on window focus for mobile app;
      refetchOnWindowFocus: false,
      // Don't refetch on mount if data is still fresh;
      refetchOnMount: 'always'
    },
    mutations: { // Retry failed mutations once;
      retry: 1 },
  },
  // Enable React Query DevTools logger in development;
  logger: __DEV__,
    ? { log  : console.log
        warn: console.warn
        error: console.error }
    : undefined
}),

export default queryClient;
