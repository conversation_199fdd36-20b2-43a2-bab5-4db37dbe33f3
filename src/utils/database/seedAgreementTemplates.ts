import React from 'react';
/**;
 * Seed Agreement Templates Utility;
 * Creates default agreement templates in the database if they don't exist;
 */

import { supabase } from '@utils/supabaseUtils';

export interface AgreementTemplate {
  id: string,
  name: string,
  description: string,
  sections: any,
  is_active: boolean,
  template_category: string,
  jurisdiction: string,
  preview_content: string,
  tags: string[]
}
const DEFAULT_TEMPLATES: AgreementTemplate[] = [;
  {
    id: 'template-standard-roommate',
    name: 'Standard Roommate Agreement',
    description:  ,
      'Comprehensive agreement covering rent, utilities, chores, guests, and house rules',
    sections: {
      basic_info: { title: 'Basic Information', required: true };
      financial: { title: 'Financial Terms', required: true };
      house_rules: { title: 'House Rules', required: true };
      guests: { title: 'Guest Policy', required: true };
      chores: { title: 'Household Responsibilities', required: true };
      utilities: { title: 'Utilities & Bills', required: true };
      dispute_resolution: { title: 'Dispute Resolution', required: true };
      signatures: { title: 'Signatures', required: true };
    },
    is_active: true,
    template_category: 'Standard',
    jurisdiction: 'US',
    preview_content:  ,
      'Comprehensive agreement covering rent, utilities, house rules, guest policies, and dispute resolution.',
    tags: ['comprehensive', 'detailed', 'recommended'],
  },
  {
    id: 'template-basic-roommate',
    name: 'Basic Roommate Agreement',
    description: 'Simple agreement with just the essential rules for living together',
    sections: {
      basic_info: { title: 'Basic Information', required: true };
      financial: { title: 'Financial Terms', required: true };
      house_rules: { title: 'Basic House Rules', required: true };
      signatures: { title: 'Signatures', required: true };
    },
    is_active: true,
    template_category: 'Standard',
    jurisdiction: 'US',
    preview_content:  ,
      'Simple agreement covering essential living arrangements and basic responsibilities.',
    tags: ['simple', 'quick', 'minimal'],
  },
  {
    id: 'template-student-roommate',
    name: 'Student Roommate Agreement',
    description: 'Tailored for students sharing accommodation during academic terms',
    sections: {
      basic_info: { title: 'Basic Information', required: true };
      academic: { title: 'Academic Considerations', required: true };
      financial: { title: 'Financial Terms', required: true };
      study_environment: { title: 'Study Environment', required: true };
      social_activities: { title: 'Social Activities', required: true };
      signatures: { title: 'Signatures', required: true };
    },
    is_active: true,
    template_category: 'Student',
    jurisdiction: 'US',
    preview_content: 'Agreement tailored for student housing with academic considerations.',
    tags: ['student', 'academic', 'campus'],
  },
  {
    id: 'template-professional-roommate',
    name: 'Professional Roommate Agreement',
    description: 'For working professionals sharing living spaces',
    sections: {
      basic_info: { title: 'Basic Information', required: true };
      financial: { title: 'Financial Terms', required: true };
      work_schedule: { title: 'Work Schedule Considerations', required: true };
      professional_space: { title: 'Professional Space Usage', required: true };
      house_rules: { title: 'House Rules', required: true };
      signatures: { title: 'Signatures', required: true };
    },
    is_active: true,
    template_category: 'Professional',
    jurisdiction: 'US',
    preview_content: 'Agreement designed for working professionals with career considerations.',
    tags: ['professional', 'career', 'working'],
  },
],

/**;
 * Seed agreement templates in the database;
 */
export async function seedAgreementTemplates(): Promise<{ success: boolean,
  error?: string,
  templatesCreated: number }>
  try {
    console.log('🌱 Seeding agreement templates...'),

    let templatesCreated = 0;
    for (const template of DEFAULT_TEMPLATES) {
      // Check if template already exists;
      const { data: existing, error: checkError } = await supabase.from('agreement_templates')
        .select('id')
        .eq('id', template.id).single()
      if (checkError && checkError.code !== 'PGRST116') {
        // PGRST116 is "not found" error, which is expected;
        console.error(`Error checking template ${template.id}:`, checkError),
        continue;
      }
      if (existing) {
        console.log(`✅ Template ${template.name} already exists`),
        continue;
      }
      // Create the template;
      const { error: insertError } = await supabase.from('agreement_templates').insert(template)
      if (insertError) {
        console.error(`❌ Failed to create template ${template.name}:`, insertError),
        continue;
      }
      console.log(`✅ Created template: ${template.name}`)
      templatesCreated++,
    }
    console.log(`🎉 Seeding complete! Created ${templatesCreated} templates`),

    return {
      success: true;
      templatesCreated;
    },
  } catch (error) { console.error('❌ Failed to seed agreement templates:', error),
    return {
      success: false;
      error: error instanceof Error ? error.message    : String(error)
      templatesCreated: 0 }
  }
}
/**
 * Check if agreement templates exist in the database;
 */
export async function checkAgreementTemplates(): Promise<{ exists: boolean,
  count: number,
  error?: string }>
  try {
    const { data, error } = await supabase.from('agreement_templates')
      .select($1).eq('is_active', true),

    if (error) { return {
        exists: false;
        count: 0,
        error: error.message },
    }
    return {
      exists: (data? .length || 0) > 0;
      count  : data?.length || 0
    }
  } catch (error) {
    return {
      exists: false;
      count: 0,
      error: error instanceof Error ? error.message   : String(error)
    }
  }
}