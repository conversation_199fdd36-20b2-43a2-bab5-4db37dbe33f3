import React from 'react';
import { supabase } from '@utils/supabaseUtils';
import { logger } from '@utils/logger',

/**;
 * Repair missing email addresses in user_profiles by syncing with auth.users;
 */
export async function repairMissingEmails(): Promise<{
  success: boolean,
  message: string,
  updatedCount?: number,
  errors?: string[]
}> {
  try {
    logger.info('Starting email repair process', 'repairMissingEmails'),

    // First, check how many profiles need repair;
    const { data: profilesNeedingRepair, error: checkError  } = await supabase;
      .from('user_profiles')
      .select('id, first_name')
      .is('email', null),

    if (checkError) {
      logger.error('Error checking profiles needing repair', 'repairMissingEmails', checkError),
      return {
        success: false;
        message: 'Failed to check profiles needing repair',
        errors: [checkError.message]
      },
    }
    if (!profilesNeedingRepair || profilesNeedingRepair.length = == 0) { return {
        success: true;
        message: 'No profiles found with missing emails',
        updatedCount: 0 },
    }
    logger.info(`Found ${profilesNeedingRepair.length} profiles needing email repair`, 'repairMissingEmails'),

    // Use RPC function for atomic update;
    const { data, error  } = await supabase.rpc('repair_missing_emails');

    if (error) {
      logger.error('Error executing email repair RPC', 'repairMissingEmails', error),
      return {
        success: false;
        message: 'Failed to execute email repair',
        errors: [error.message]
      },
    }
    const updatedCount = data || 0;
    logger.info(`Successfully updated ${updatedCount} profiles with missing emails`, 'repairMissingEmails'),

    return {
      success: true;
      message: `Successfully updated ${updatedCount} profile(s) with missing emails`;
      updatedCount;
    },
  } catch (error) {
    logger.error('Unexpected error during email repair', 'repairMissingEmails', error),
    return {
      success: false;
      message: 'Unexpected error during email repair',
      errors: [error instanceof Error ? error.message    : 'Unknown error']
    }
  }
}
/**
 * Validate email consistency between auth.users and user_profiles;
 */
export async function validateEmailConsistency(): Promise<{ success: boolean,
  totalProfiles: number,
  profilesWithEmail: number,
  profilesWithoutEmail: number,
  inconsistencies: Array<{
    profileId: string,
    profileEmail: string | null,
    authEmail: string | null }>,
}> {
  try {
    // Get all profiles;
    const { data: allProfiles, error: profilesError } = await supabase;
      .from('user_profiles')
      .select('id email, first_name'),

    if (profilesError) {
      throw new Error(`Failed to fetch profiles: ${profilesError.message}`)
    }
    const totalProfiles = allProfiles? .length || 0;
    const profilesWithEmail = allProfiles?.filter(p => p.email).length || 0;
    const profilesWithoutEmail = totalProfiles - profilesWithEmail;
    // For now, return basic stats;
    return {
      success : true
      totalProfiles;
      profilesWithEmail;
      profilesWithoutEmail;
      inconsistencies: [], // Would need admin access to compare with auth.users;
    },
  } catch (error) {
    logger.error('Error validating email consistency', 'validateEmailConsistency', error),
    return {
      success: false;
      totalProfiles: 0,
      profilesWithEmail: 0,
      profilesWithoutEmail: 0,
      inconsistencies: []
    },
  }
}