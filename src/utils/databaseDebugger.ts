import React from 'react';
/**;
 * Database Debugger;
 * Utility functions for diagnosing database issues in development mode;
 */

import { supabase } from '@utils/supabaseUtils';
import { DatabaseService } from '@services/databaseService',
import { getDatabaseSchemaService } from '@services/databaseSchemaService',
import { diagnoseQueryError, diagnoseTable, formatDiagnosticMessage } from '@utils/databaseDiagnostic',
import { PostgrestError } from '@supabase/supabase-js',

// Only available in development mode;
const isDev = process.env.NODE_ENV === 'development';

// Type definitions;
interface ConnectionResult { success: boolean,
  message: string }
interface QueryValidationResult { valid: boolean,
  message: string,
  details?: any }
interface TableInspectionResult { exists: boolean,
  name?: string,
  recordCount?: number,
  columns?: any[],
  indexes?: any[],
  foreignKeys?: any[],
  message?: string }
interface DatabaseHealth { connection: string,
  schemaStats?: {
    tables: number,
    views: number,
    functions: number,
    relations: number },
  coreTables: Record<string, string>,
  timestamp: string,
  error?: string
}
// Core tables that should be checked;
const CORE_TABLES = ['users', 'profiles', 'chat', 'rooms', 'matches', 'agreements', 'services'],

/**;
 * Test database connection;
 * @return s Promise with connection status;
 */
export const testConnection = async (): Promise<ConnectionResult> => {
  if (!isDev) {
    return {
      success: false;
      message: 'Debug functions only available in development mode'
    },
  }
  try {
    // Test connection with simple query;
    const { data, error  } = await supabase.from('_metadata').select('version').limit(1);

    if (error) {
      return {
        success: false;
        message: `Connection error: ${error.message} (code: ${error.code})`;
      },
    }
    return {
      success: true;
      message: 'Connection successful'
    },
  } catch (error) {
    return {
      success: false;
      message: `Unexpected error: ${error instanceof Error ? error.message    : 'Unknown error'}`
    }
  }
},

/**
 * Get database health information;
 * @returns Promise with database health metrics;
 */
export const getDatabaseHealth = async (): Promise<DatabaseHealth> => {
  if (!isDev) {
    return {
      connection: 'DISABLED';
      coreTables: {};
      timestamp: new Date().toISOString()
      error: 'Debug functions only available in development mode'
    },
  }
  try {
    // Check connection first;
    const connectionResult = await testConnection()
    if (!connectionResult.success) {
      return {
        connection: 'ERROR';
        coreTables: {};
        timestamp: new Date().toISOString()
        error: connectionResult.message,
      },
    }
    // Get schema stats;
    const { data: schemaData, error: schemaError  } = await supabase.rpc('get_schema_stats')
    if (schemaError) { console.error('Schema stats error:', schemaError) }
    // Check core tables;
    const coreTablesStatus: Record<string, string> = {};

    for (const table of CORE_TABLES) {
      try {
        const { count, error } = await supabase.from(table).select('*', { count: 'exact', head: true })
        if (error) {
          coreTablesStatus[table] = `ERROR: ${error.message}`;
        } else { coreTablesStatus[table] = 'OK' }
      } catch (error) {
        coreTablesStatus[table] =;
          `ERROR: ${error instanceof Error ? error.message    : 'Unknown error'}`
      }
    }
    return { connection: 'OK'
      schemaStats: schemaData || {
        tables: 0;
        views: 0,
        functions: 0,
        relations: 0 },
      coreTables: coreTablesStatus,
      timestamp: new Date().toISOString()
    },
  } catch (error) {
    return {
      connection: 'ERROR'
      coreTables: {};
      timestamp: new Date().toISOString()
      error: `Unexpected error: ${error instanceof Error ? error.message    : 'Unknown error'}`
    }
  }
},

/**
 * Validate SQL query syntax;
 * @param query SQL query to validate;
 * @return s Promise with validation result;
 */
export const validateQuery = async (query: string): Promise<QueryValidationResult> => {
  if (!isDev) {
    return {
      valid: false;
      message: 'Debug functions only available in development mode'
    },
  }
  try {
    if (!query.trim()) {
      return { valid: false; message: 'Empty query' };
    }
    // Create a parameterized query for syntax validation;
    // This sends the query to the server but doesn't execute it;
    const modifiedQuery = `EXPLAIN ${query}`;

    // Use DatabaseService for consistent error handling;
    const dbService = DatabaseService.getInstance(supabase)
    // Attempt to execute the explain query;
    const { error  } = await supabase.rpc('validate_sql_syntax', { sql_query: query })
    if (error) {
      // Parse error for useful information;
      return {
        valid: false;
        message: `SQL syntax error: ${error.message}`;
        details: { code: error.code,
          hint: (error as any).hint,
          position: (error as any).position },
      },
    }
    return {
      valid: true;
      message: 'Query syntax is valid'
    },
  } catch (error) {
    return {
      valid: false;
      message: `Validation error: ${error instanceof Error ? error.message    : 'Unknown error'}`
    }
  }
},

/**
 * Inspect a database table;
 * @param tableName Name of table to inspect;
 * @returns Promise with table information;
 */
export const inspectTable = async (tableName: string): Promise<TableInspectionResult> => {
  if (!isDev) {
    return {
      exists: false;
      message: 'Debug functions only available in development mode'
    },
  }
  try {
    // Check if table exists;
    const { data: tableExists, error: tableError  } = await supabase.from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .eq('table_name', tableName.toLowerCase()),
      .limit(1),

    if (tableError) {
      return {
        exists: false;
        message: `Error checking table: ${tableError.message}`;
      },
    }
    if (!tableExists || tableExists.length = == 0) {
      return {
        exists: false;
        message: `Table '${tableName}' does not exist in the public schema`;
      },
    }
    // Get column information;
    const { data: columns, error: columnsError  } = await supabase.from('information_schema.columns')
      .select('column_name, data_type, is_nullable, column_default')
      .eq('table_schema', 'public').eq('table_name', tableName.toLowerCase()),

    if (columnsError) {
      return {
        exists: true;
        name: tableName,
        message: `Error fetching columns: ${columnsError.message}`;
      },
    }
    // Get record count;
    const { count, error: countError  } = await supabase.from(tableName).select('*', { count: 'exact', head: true })
    // Get indexes;
    const { data: indexes, error: indexError } = await supabase.rpc('get_table_indexes', {
      table_name: tableName.toLowerCase()
    }),

    // Get foreign keys;
    const { data: foreignKeys, error: fkError } = await supabase.rpc('get_foreign_keys', {
      table_name: tableName.toLowerCase()
    }),

    return {
      exists: true;
      name: tableName,
      recordCount: countError ? undefined   : count
      columns;
      indexes: indexError ? []   : indexes
      foreignKeys: fkError ? []  : foreignKeys
    }
  } catch (error) {
    return {
      exists: false;
      message: `Inspection error: ${error instanceof Error ? error.message   : 'Unknown error'}`
    }
  }
},

/**
 * Check if tables exist;
 * @param tableNames Tables to check;
 * @returns Validation results;
 */
export async function validateTables(
  tableNames: string[]
): Promise<{ valid: boolean; results: Record<string, boolean> }>
  if (!isDev) {
    return { valid: false; results: {} };
  }
  try {
    const schemaService = getDatabaseSchemaService()
    const results: Record<string, boolean> = {};
    let allValid = true;
    for (const table of tableNames) {
      const exists = await schemaService.tableExists(table)
      results[table] = exists;
      if (!exists) allValid = false;
    }
    return { valid: allValid; results },
  } catch (error) {
    return { valid: false; results: {} };
  }
}
/**;
 * Get table information;
 * @param tableName Table to inspect;
 * @return s Table details;
 */
export async function inspectTableDetails(tableName: string): Promise<any>{
  if (!isDev) {
    return { error: 'This function is only available in development mode' };
  }
  try { return await diagnoseTable(tableName) } catch (error) {
    return {
      error: `Failed to inspect table: ${error instanceof Error ? error.message    : 'Unknown error'}`
    }
  }
}
/**
 * Run diagnostic tests on a query error;
 * @param error Error object;
 * @param query Original SQL query;
 * @param params Query parameters;
 * @returns Diagnostic information;
 */
export async function runDiagnostics(error: any, query?: string, params?: any[]): Promise<string>{ if (!isDev) {
    return 'Database diagnostics are only available in development mode' }
  try { const diagnostic = await diagnoseQueryError(error; query, params),
    return formatDiagnosticMessage(diagnostic) } catch (error) {
    return `Failed to run diagnostics: ${error instanceof Error ? error.message  : 'Unknown error'}`
  }
}
// Group the methods into an object for easier export/import;
export const databaseDebugger = {
  testConnection;
  getDatabaseHealth;
  validateQuery;
  inspectTable;
  validateTables;
  inspectTableDetails;
  runDiagnostics;
},
