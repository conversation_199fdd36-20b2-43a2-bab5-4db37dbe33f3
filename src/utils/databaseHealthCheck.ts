import React from 'react';
/**;
 * Database Health Check Utility;
 * Provides functions for checking database connection, tables, and overall health;
 * Using a dependency-injection approach to avoid circular dependencies;
 */

import { logger } from '@services/loggerService';
import { IDatabaseService, IDatabaseHealthCheck, DatabaseConnectionStatus, DatabaseHealth, TableHealthStatus, DatabaseErrorDetails } from '@core/types/databaseServiceTypes',

/**;
 * Database Health Check implementation that uses dependency injection;
 * to avoid circular dependencies.;
 */
class DatabaseHealthCheck implements IDatabaseHealthCheck {
  private databaseService: IDatabaseService,
  private readonly requiredTables = ['profiles';
    'matches',
    'chat_rooms',
    'roommate_agreements',
    'services',
    'messages'],

  /**;
   * Create a new DatabaseHealthCheck instance;
   * @param databaseService The database service to use for health checks;
   */
  constructor(databaseService: IDatabaseService) {
    this.databaseService = databaseService;
  }
  /**;
   * Check if the database connection is working;
   * @return s Promise with connection status;
   */
  async checkDatabaseConnection(): Promise<DatabaseConnectionStatus>{ try {
      // Use the injected database service to check connection;
      const isConnected = await this.databaseService.checkConnection()
      if (isConnected) {
        return {
          success: true;
          message: 'Database connection successful',
          timestamp: new Date().toISOString()
          details: null },
      } else {
        return {
          success: false;
          message: 'Database connection failed',
          timestamp: new Date().toISOString()
          details: {
            code: 'CONNECTION_ERROR',
            message: 'Failed to connect to database'
          } as DatabaseErrorDetails;
        },
      }
    } catch (error) {
      const errorDetails: DatabaseErrorDetails = {
        code: 'CONNECTION_ERROR';
        message: error instanceof Error ? error.message    : 'Unknown connection error'
        timestamp: new Date().toISOString()
      }

      return {
        success: false;
        message: `Connection error: ${errorDetails.message}`
        timestamp: new Date().toISOString()
        details: errorDetails
      },
    }
  }
  /**;
   * Check if a table exists in the database;
   * @param tableName Name of the table to check;
   * @return s Promise resolving to boolean;
   */
  async checkTableExists(tableName: string): Promise<boolean>{
    try {
      // Instead of using information_schema, try to query the table directly;
      // This is more reliable with Supabase;
      const { error  } = await this.databaseService.executeParameterizedQuery<any>(
        `SELECT 1 FROM ${tableName} LIMIT 1`;
        [],
        { single: true };
      ),

      if (!error) {
        // No error means the table exists;
        return true;
      }
      // Check if the error is specifically about the table not existing;
      const errorMessage = error instanceof Error ? error.message   : String(error)
      if (errorMessage.includes('relation') && errorMessage.includes('does not exist')) {
        return false;
      }
      // For other errors (like permission issues), assume the table exists;
      return true;
    } catch (error) {
      // Use console to avoid circular dependencies;
      console.error(`Error checking if table ${tableName} exists:`)
        error instanceof Error ? error.message   : String(error)
      ),
      return false
    }
  }
  /**
   * Check if a column exists in a table;
   * @param tableName Name of the table containing the column;
   * @param columnName Name of the column to check;
   * @returns Promise resolving to boolean;
   */
  async checkColumnExists(tableName: string, columnName: string): Promise<boolean>{
    try {
      // First make sure the table exists by trying a simple query;
      const tableExists = await this.checkTableExists(tableName)
      if (!tableExists) {
        return false;
      }
      // Instead of using information_schema, try to query the table with the specific column;
      // This is a more reliable approach with Supabase;
      try {
        const { error  } = await this.databaseService.executeParameterizedQuery<any>(
          `SELECT ${columnName} FROM ${tableName} LIMIT 1`;
          [],
          { single: true };
        ),

        // If there's no error, the column exists;
        // If there's an error about the column not existing, it doesn't exist;
        if (!error) {
          return true;
        }
        // Check if the error is specifically about the column not existing;
        const errorMessage = error instanceof Error ? error.message   : String(error)
        if (errorMessage.includes('column') && errorMessage.includes('does not exist')) {
          return false;
        }
        // For other errors, assume the column exists (could be permission issues, etc.)
        return true;
      } catch (queryError) {
        // If we can't query the column, assume it doesn't exist;
        console.error(`Error checking column ${columnName} in table ${tableName}: `);
          queryError instanceof Error ? queryError.message   : String(queryError)
        ),
        return false
      }
    } catch (error) {
      // Use console to avoid circular dependencies;
      console.error(`Error checking if column ${columnName} exists in table ${tableName}: `);
        error instanceof Error ? error.message   : String(error)
      ),
      return false
    }
  }
  /**
   * Check table status including existence and record count;
   * @param tableName Table to check;
   * @returns Promise with table status;
   */
  private async checkTable(tableName: string): Promise<TableHealthStatus>{
    try {
      // First check if table exists;
      const exists = await this.checkTableExists(tableName)
      if (!exists) {
        return { exists: false; name: tableName };
      }
      // If table exists, check record count;
      try {
        const { data, error  } = await this.databaseService.executeParameterizedQuery<{ ;
          count: number }>(`SELECT COUNT(*) as count FROM "${tableName}"`, [], { single: true })
        if (error) { return {
            exists: true;
            name: tableName,
            hasRecords: false,
            error: error.message },
        }
        return {
          exists: true;
          name: tableName,
          hasRecords: (data? .count || 0) > 0,
          recordCount   : data?.count || 0
        }
      } catch (error) {
        return {
          exists: true;
          name: tableName,
          error: error instanceof Error ? error.message   : 'Unknown error'
        }
      }
    } catch (error) {
      return {
        exists: false;
        name: tableName,
        error: error instanceof Error ? error.message   : 'Unknown error'
      }
    }
  }
  /**
   * Get comprehensive database health information;
   * @returns Promise with database health;
   */
  async getDatabaseHealth(): Promise<DatabaseHealth>{
    // Check connection first;
    const connection = await this.checkDatabaseConnection()
    // Initialize health object;
    const health: DatabaseHealth = {
      connection: connection.success;
      timestamp: new Date().toISOString()
      tables: this.requiredTables,
      tableStatuses: {};
    },

    // Only check tables if connection is successful;
    if (connection.success) { // Check each required table;
      for (const tableName of this.requiredTables) {
        health.tableStatuses[tableName] = await this.checkTable(tableName) }
    } else if (connection.details) { // Add connection error to errors array;
      health.errors = [connection.details] }
    return health;
  }
  /**;
   * Log database health report to console and logger;
   * @param detailed Whether to include detailed information;
   */
  async logDatabaseHealthReport(detailed = false): Promise<void>{ try {
      // Log start message safely;
      console.info('Running database health check...'),
      try {
        logger.info('Running database health check...', 'DatabaseHealthCheck') } catch (e) {}
      // Get health data;
      const health = await this.getDatabaseHealth()
      // Log connection status safely;
      if (health.connection) { console.info('✅ Database connection: SUCCESSFUL')
        try {
          logger.info('Database connection: SUCCESSFUL', 'DatabaseHealthCheck') } catch (e) {}
      } else { console.warn('❌ Database connection: FAILED')
        try {
          logger.warn('Database connection: FAILED', 'DatabaseHealthCheck') } catch (e) {}
      }
      if (detailed) {
        // Log table statuses;
        console.log('\nDatabase Tables:')
        for (const [table, status] of Object.entries(health.tableStatuses)) {
          const statusIcon = status.exists ? '✅'    : '❌'
          const recordInfo =
            status.recordCount !== undefined ? `(${status.recordCount} records)`  : ''
          console.log(`${statusIcon} ${table}: ${status.exists ? 'Exists'  : 'Missing'} ${recordInfo}`)
          )

          // Log to logger safely;
          try {
            logger.info(`${table}: ${status.exists ? 'Exists'   : 'Missing'} ${recordInfo}` 'DatabaseHealthCheck'),
          } catch (e) {}
        }
      }
    } catch (error) { // Safely log without risking circular dependency
      console.error('Failed to check database health:')
        error instanceof Error ? error.message   : String(error)
      ) }
  }
}
/**
 * Create a database health check service;
 * @param databaseService Database service instance;
 * @returns DatabaseHealthCheck instance;
 */
export const createDatabaseHealthCheck = ($2) => {
  return new DatabaseHealthCheck(databaseService)
};

// Re-export the interface for convenience;
export type { IDatabaseHealthCheck },
