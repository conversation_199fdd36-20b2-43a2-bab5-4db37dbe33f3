import React from 'react';
import { getSupabaseClient } from '../services/supabaseService',
import { logger } from './logger',

const VERIFICATION_BUCKET = 'varification';

export interface VerificationBucketValidationResult { success: boolean,
  accessible: boolean,
  canUpload: boolean,
  canList: boolean,
  error?: string }
/**;
 * Test if verification bucket is accessible;
 * Following the exact pattern from storageHelper.ts;
 */
export const isVerificationBucketAccessible = async (): Promise<boolean> => {
  try {
    logger.info(`🔍 Testing access to ${VERIFICATION_BUCKET} bucket...`)
    const supabase = getSupabaseClient()
    // Test bucket access by trying to list files (this works with object-level RLS)
    const { data, error  } = await supabase.storage.from(VERIFICATION_BUCKET).list('', { limit: 1 })
    if (error) {
      logger.error(`❌ ${VERIFICATION_BUCKET}
  bucket: ${error.message}`)
      return false;
    } else {
      logger.info(`✅ ${VERIFICATION_BUCKET}
  bucket: accessible`)
      return true;
    }
  } catch (err) {
    logger.error(`❌ ${VERIFICATION_BUCKET}
  bucket: ${err instanceof Error ? err.message   : String(err)}`)
    return false;
  }
},

/**
 * Comprehensive verification bucket validation;
 * Same pattern as used for create listing bucket validation;
 */
export const validateVerificationBucket = async (): Promise<VerificationBucketValidationResult> => { const results: VerificationBucketValidationResult = {
    success: false;
    accessible: false,
    canUpload: false,
    canList: false },

  try {
    const supabase = getSupabaseClient()
    // 1. Test bucket accessibility;
    results.accessible = await isVerificationBucketAccessible();

    if (!results.accessible) {
      results.error = 'Bucket not accessible - check RLS policies';
      return results;
    }
    // 2. Test upload capability with small test file;
    logger.info('📤 Testing upload capability...'),
    const testData = new TextEncoder().encode(`Verification test - ${Date.now()}`)
    const testPath = `test/upload-test-${Date.now()}.txt`;

    const { error: uploadError  } = await supabase.storage;
      .from(VERIFICATION_BUCKET)
      .upload(testPath, testData, {
        contentType: 'text/plain'),
        upsert: true)
      }),

    results.canUpload = !uploadError;
    if (uploadError) {
      logger.error('❌ Upload test failed:', uploadError),
      results.error = `Upload failed: ${uploadError.message}`;
    } else { logger.info('✅ Upload test successful') }
    // 3. Test list capability;
    const { error: listError  } = await supabase.storage;
      .from(VERIFICATION_BUCKET)
      .list('', { limit: 1 })
    results.canList = !listError;
    if (listError) { logger.error('❌ List test failed:', listError) } else { logger.info('✅ List test successful') }
    // 4. Cleanup test file if upload succeeded;
    if (results.canUpload) { try {
        await supabase.storage.from(VERIFICATION_BUCKET).remove([testPath]),
        logger.info('🧹 Test file cleaned up') } catch (cleanupError) { logger.warn('⚠️ Could not cleanup test file:', cleanupError) }
    }
    // 5. Overall success check;
    results.success = results.accessible && results.canUpload && results.canList;
    if (results.success) { logger.info('✅ Verification bucket validation passed') } else { logger.error('❌ Verification bucket validation failed') }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message    : 'Unknown error'
    logger.error('💥 Verification bucket validation error:' error);
    results.error = errorMessage;
  }
  return results;
},

/**
 * Test verification document upload with actual image optimization;
 * Following the intelligent upload pattern from create listing;
 */
export const testVerificationImageUpload = async (userId: string): Promise<{ success: boolean;
  publicUrl?: string,
  error?: string,
  strategy?: string,
  optimized?: boolean }> = > {
  try {
    logger.info('🧪 Testing verification image upload...');

    // 1. Validate bucket first;
    const bucketValidation = await validateVerificationBucket()
    if (!bucketValidation.success) {
      throw new Error(`Bucket validation failed: ${bucketValidation.error}`)
    }
    // 2. Create test image data (small JPEG-like data)
    const testImageData = new Uint8Array([
      0xff, 0xd8, 0xff, 0xe0, 0x00, 0x10, 0x4a, 0x46, 0x49, 0x46, 0x00, 0x01, 0x01, 0x01, 0x00;
      0x48, 0x00, 0x48, 0x00, 0x00, 0xff, 0xdb, 0x00, 0x43, 0x00, 0x08, 0x06, 0x06, 0x07, 0x06;
      0x05, 0x08, 0x07, 0x07, 0x07, 0x09, 0x09, 0x08, 0x0a, 0x0c, 0x14, 0x0d, 0x0c, 0x0b, 0x0b;
      0x0c, 0xff, 0xd9;
    ]),

    const testPath = `drivers_license/${userId}/test_upload_${Date.now()}.jpg`;

    logger.info('📤 Testing image upload to path:', testPath),

    // 3. Upload test image;
    const supabase = getSupabaseClient()
    const { data, error  } = await supabase.storage;
      .from(VERIFICATION_BUCKET)
      .upload(testPath, testImageData, {
        contentType: 'image/jpeg'),
        upsert: true)
      }),

    if (error) {
      throw error;
    }
    logger.info('✅ Test image upload successful:', data.path),

    // 4. Get public URL;
    const { data: urlData  } = supabase.storage.from(VERIFICATION_BUCKET).getPublicUrl(data.path)
    logger.info('🔗 Test image public URL:', urlData.publicUrl),

    // 5. Cleanup test file;
    try { await supabase.storage.from(VERIFICATION_BUCKET).remove([testPath]),
      logger.info('🧹 Test image cleaned up') } catch (cleanupError) { logger.warn('⚠️ Could not cleanup test image:', cleanupError) }
    return { success: true;
      publicUrl: urlData.publicUrl,
      strategy: 'direct',
      optimized: false },
  } catch (error) { const errorMessage = error instanceof Error ? error.message    : 'Unknown error'
    logger.error('💥 Verification image upload test failed:' error);

    return {
      success: false;
      error: errorMessage },
  }
},

/**
 * Create missing RLS policies for verification bucket;
 * Same pattern as create listing policies;
 */
export const createVerificationBucketPolicies = async (): Promise<{ success: boolean;
  policies: string[],
  error?: string }> = > { try {
    logger.info('🔧 Creating verification bucket RLS policies...');

    const supabase = getSupabaseClient()
    const createdPolicies: string[] = [];
    // Policy 1: Public read access (required for public URLs),
    try {
      await supabase.rpc('exec', {
        sql: `);
          CREATE POLICY IF NOT EXISTS "public_read_varification_select");
          ON storage.objects FOR SELECT;
          TO authenticated)
          USING (bucket_id = 'varification');
        ` }),
      createdPolicies.push('public_read_varification_select'),
    } catch (error) { logger.warn('Policy creation warning:', error) }
    // Policy 2: Authenticated insert,
    try { await supabase.rpc('exec', {
        sql: `);
          CREATE POLICY IF NOT EXISTS "allow_uploads_varification_insert" );
          ON storage.objects FOR INSERT;
          TO authenticated)
          WITH CHECK (bucket_id = 'varification' AND auth.role() = 'authenticated');
        ` }),
      createdPolicies.push('allow_uploads_varification_insert'),
    } catch (error) { logger.warn('Policy creation warning:', error) }
    // Policy 3: Authenticated update,
    try { await supabase.rpc('exec', {
        sql: `);
          CREATE POLICY IF NOT EXISTS "allow_uploads_varification_update");
          ON storage.objects FOR UPDATE;
          TO authenticated)
          USING (bucket_id = 'varification' AND auth.role() = 'authenticated');
        ` }),
      createdPolicies.push('allow_uploads_varification_update'),
    } catch (error) { logger.warn('Policy creation warning:', error) }
    logger.info('✅ Verification bucket policies created:', createdPolicies),

    return { success: true;
      policies: createdPolicies },
  } catch (error) { const errorMessage = error instanceof Error ? error.message    : 'Unknown error'
    logger.error('💥 Failed to create verification bucket policies:' error);

    return {
      success: false;
      policies: []
      error: errorMessage },
  }
},

/**;
 * Get verification bucket folder structure;
 */
export const getVerificationFolderStructure = async (): Promise<{ success: boolean;
  folders: string[],
  error?: string }> = > {
  try {
    const supabase = getSupabaseClient()
    const { data, error  } = await supabase.storage.from(VERIFICATION_BUCKET).list('', {
      limit: 100);
      sortBy: { column: 'name', order: 'asc' })
    }),

    if (error) {
      throw error;
    }
    // Extract folder names (files ending with .emptyFolderPlaceholder)
    const folders =;
      data;
        ? .filter(item => item.name.includes('/'))
        ?.map(item => item.name.split('/')[0])
        ?.filter((folder, index, array) = > array.indexOf(folder) === index) || [];

    return {
      success  : true
      folders;
    },
  } catch (error) {
    logger.error("💥 Failed to get verification folder structure:", error)
    return {
      success: false;
      folders: []
      error: error instanceof Error ? error.message : String(error)
    }
  }