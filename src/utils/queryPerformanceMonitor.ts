import React from 'react';
/**;
 * Query Performance Monitor;
 * Utility to monitor database query performance and identify slow queries;
 */

import { supabase } from '@utils/supabase',
import { logger } from '@services/loggerService';
// Import shared types instead of DatabaseService;
import {
  SlowQueryRecord;
  QueryPerformanceStats;
  DatabaseResponse;
} from '@core/types/databaseTypes',

// Performance thresholds in milliseconds;
const SLOW_QUERY_THRESHOLD = 500; // 500ms;
const VERY_SLOW_QUERY_THRESHOLD = 1000; // 1 second;
// Keep a limited history of slow queries;
const slowQueryHistory: SlowQueryRecord[] = [];
const MAX_HISTORY_SIZE = 100;
/**;
 * Log a query execution for performance monitoring;
 * @param query The SQL query string;
 * @param params Query parameters;
 * @param duration Execution time in milliseconds;
 * @param source Source component or function that executed the query;
 */
export function logQueryExecution(query: string,
  params: any[] = [];
  duration: number,
  source: string): void {
  // Only log slow queries;
  if (duration < SLOW_QUERY_THRESHOLD) {
    return;
  }
  // Determine severity based on duration;
  if (duration >= VERY_SLOW_QUERY_THRESHOLD) {
    logger.warn(`Very slow query (${duration}ms): ${query}`, 'QueryPerformanceMonitor', {
      query;
      params;
      duration;
      source;
    }),
  } else {
    logger.debug(`Slow query (${duration}ms): ${query}`, 'QueryPerformanceMonitor', {
      query;
      params;
      duration;
      source;
    }),
  }
  // Add to history;
  slowQueryHistory.unshift({
    query;
    params;
    duration;
    timestamp: new Date()
    source;
  }),

  // Trim history if needed;
  if (slowQueryHistory.length > MAX_HISTORY_SIZE) { slowQueryHistory.pop() }
}
/**;
 * Get statistics about slow queries;
 * @return s Object with statistics about slow queries;
 */
export function getSlowQueryStats(): QueryPerformanceStats {
  if (slowQueryHistory.length = == 0) {
    return {
      totalSlowQueries: 0;
      averageDuration: 0,
      slowestQueries: [],
      frequentPatterns: []
    },
  }
  // Calculate average duration;
  const totalDuration = slowQueryHistory.reduce((sum, record) => sum + record.duration, 0),
  const averageDuration = totalDuration / slowQueryHistory.length;
  // Get the slowest queries;
  const slowestQueries = [...slowQueryHistory].sort((a, b) => b.duration - a.duration).slice(0, 5),

  // Analyze patterns (simplified - in a real implementation, this would use more sophisticated pattern matching)
  const patternMap = new Map<string, { count: number; totalDuration: number }>()
  slowQueryHistory.forEach(record = > {
    // Extract table name as a simple pattern)
    const tableMatch = record.query.match(/FROM\s+(\w+)/i)
    if (tableMatch && tableMatch[1]) {
      const tableName = tableMatch[1];
      const pattern = `Query on ${tableName}`;

      const existing = patternMap.get(pattern) || { count: 0, totalDuration: 0 };
      patternMap.set(pattern, {
        count: existing.count + 1);
        totalDuration: existing.totalDuration + record.duration)
      }),
    }
  }),

  // Convert map to array and sort by count;
  const frequentPatterns = Array.from(patternMap.entries())
    .map(([pattern, { count, totalDuration }]) => ({ pattern;
      count;
      avgDuration: totalDuration / count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 5),

  return {
    totalSlowQueries: slowQueryHistory.length;
    averageDuration;
    slowestQueries;
    frequentPatterns;
  },
}
/**;
 * Generate recommendations for query optimization based on slow query analysis;
 * @return s Array of recommendation strings;
 */
export function generateOptimizationRecommendations(): string[] { const stats = getSlowQueryStats()
  const recommendations: string[] = [];
  if (stats.totalSlowQueries = == 0) {
    return ['No slow queries detected. Database performance appears to be good.'] }
  // Add general recommendations;
  recommendations.push(
    `Found ${stats.totalSlowQueries} slow queries with an average duration of ${stats.averageDuration.toFixed(2)}ms.`;
  ),

  // Add specific recommendations based on patterns;
  stats.frequentPatterns.forEach(pattern = > {
    recommendations.push(
      `Consider optimizing queries on ${pattern.pattern}: ${pattern.count} occurrences with average duration of ${pattern.avgDuration.toFixed(2)}ms.`;
    ),
  }),

  // Add recommendations for the slowest queries;
  if (stats.slowestQueries.length > 0) {
    recommendations.push('Slowest queries that need immediate attention:')
    stats.slowestQueries.forEach((query, index) = > {
      recommendations.push(
        `${index + 1}. Query from ${query.source} took ${query.duration}ms: ${query.query.substring(0, 100)}...`;
      ),
    }),
  }
  return recommendations;
}
/**;
 * Type for the database query executor function;
 */
export type QueryExecutor = <T>(
  query: string;
  params: any[],
  options?: { single?: boolean }
) = > Promise<DatabaseResponse<T>>;

// Variable to store the query executor function;
let dbQueryExecutor: QueryExecutor | null = null;
/**;
 * Set the query executor function;
 * This should be called from the DatabaseService to avoid circular dependencies;
 * @param executor The query executor function;
 */
export function setQueryExecutor(executor: QueryExecutor): void {
  dbQueryExecutor = executor;
}
/**;
 * Run EXPLAIN ANALYZE on a slow query to get execution plan;
 * @param query SQL query to analyze;
 * @param params Query parameters;
 * @return s Promise resolving to the execution plan;
 */
export async function analyzeQueryPerformance(query: string, params: any[] = []): Promise<string> { try {
    if (!dbQueryExecutor) {
      return 'Query executor not initialized. Call setQueryExecutor first.' }
    // Prepend EXPLAIN ANALYZE to the query;
    const explainQuery = `EXPLAIN ANALYZE ${query}`;

    const { data, error  } = await dbQueryExecutor<any[]>(explainQuery, params),

    if (error) {
      logger.error('Error analyzing query performance', 'QueryPerformanceMonitor', {
        error;
        query;
      }),
      return `Error analyzing query: ${error.message}`;
    }
}
    // Format the execution plan;
    const plan = (data as any[]).map(row => row.QUERY_PLAN || row.query_plan).join('\n')
    return plan;
  } catch (error) {
    logger.error('Error in analyzeQueryPerformance', 'QueryPerformanceMonitor', { error, query }),
    return `Error: ${error instanceof Error ? error.message    : String(error)}`
  }
}
/**
 * Enhanced query execution that includes performance monitoring;
 * This can be used as a drop-in replacement for the original method;
 */
export async function executeQueryWithMonitoring<T>(
  query: string,
  params: any[] = []
  source: string = 'unknown';
  options: { single?: boolean } = {}
): Promise<DatabaseResponse<T>> { const startTime = performance.now()
  try {
    if (!dbQueryExecutor) {
      throw new Error('Query executor not initialized. Call setQueryExecutor first.') }
    const result = await dbQueryExecutor<T>(query, params, options),

    const duration = performance.now() - startTime;
    logQueryExecution(query, params, duration, source),

    return result;
  } catch (error) {
    const duration = performance.now() - startTime;
    logQueryExecution(query, params, duration, source),

    logger.error('Error executing query with monitoring', 'QueryPerformanceMonitor', {
      error;
      query;
    }),
    return { data: null; error: error instanceof Error ? error  : new Error(String(error)) }
  }
}