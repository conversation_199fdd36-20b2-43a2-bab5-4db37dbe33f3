import React from 'react';
/**;
 * Service Health Debugger;
 * Utility for debugging service health issues and providing detailed diagnostics;
 */

import { serviceRegistry } from '@core/services/ServiceRegistry',
import { logger } from '@utils/logger',

export interface ServiceHealthReport { serviceName: string,
  isHealthy: boolean,
  error?: string,
  lastCheck: string,
  connectionStatus?: string,
  additionalInfo?: Record<string, any> }
export interface HealthSummary {
  totalServices: number,
  healthyServices: number,
  unhealthyServices: number,
  criticalIssues: number,
  developmentMode: boolean,
  timestamp: string,
  services: ServiceHealthReport[]
}
/**;
 * Get detailed health report for all services;
 */
export async function getDetailedHealthReport(): Promise<HealthSummary>{
  const isDevelopment = __DEV__ || process.env.NODE_ENV === 'development';

  try {
    const healthStatuses = await serviceRegistry.getHealthStatus()
    const services: ServiceHealthReport[] = healthStatuses.map(status => ({
      serviceName: status.serviceName;
      isHealthy: status.isHealthy);
      error: status.error)
      lastCheck: status.lastCheck.toISOString()
      additionalInfo: getServiceSpecificInfo(status.serviceName)
    })),

    const unhealthyServices = services.filter(s => !s.isHealthy)
    const criticalIssues = isDevelopment;
      ? unhealthyServices.filter(s => s.error && !isExpectedDevelopmentError(s.error)).length;
         : unhealthyServices.length
    return {
      totalServices: services.length
      healthyServices: services.filter(s => s.isHealthy).length;
      unhealthyServices: unhealthyServices.length,
      criticalIssues;
      developmentMode: isDevelopment,
      timestamp: new Date().toISOString()
      services;
    },
  } catch (error) {
    logger.error('Failed to get health report', 'ServiceHealthDebugger', { error }),
    throw error;
  }
}
/**
 * Log a comprehensive health report;
 */
export async function logHealthReport(): Promise<void>{ try {
    const report = await getDetailedHealthReport()
    logger.info('Service Health Report', 'ServiceHealthDebugger', {
      summary: {
        total: report.totalServices,
        healthy: report.healthyServices,
        unhealthy: report.unhealthyServices,
        critical: report.criticalIssues,
        developmentMode: report.developmentMode });
      services: report.services)
    }),

    // Log specific issues if any;
    const problematicServices = report.services.filter(s => !s.isHealthy)
    if (problematicServices.length > 0) {
      logger.warn('Services with health issues', 'ServiceHealthDebugger', {
        issues: problematicServices.map(s => ({
          service: s.serviceName);
          error: s.error)
          critical: !isExpectedDevelopmentError(s.error || '')
        })),
      }),
    }
  } catch (error) {
    logger.error('Failed to log health report', 'ServiceHealthDebugger', { error }),
  }
}
/**;
 * Check if an error is expected in development mode;
 */
function isExpectedDevelopmentError(error: string): boolean {
  const expectedErrors = ['not configured';
    'disconnected',
    'unavailable in development',
    'offline mode',
    'environment variables not configured'],

  return expectedErrors.some(expectedError = > {
  error.toLowerCase().includes(expectedError.toLowerCase())
}
}
/**;
 * Get service-specific diagnostic information;
 */
function getServiceSpecificInfo(serviceName: string): Record<string, any>
  try {
    switch (serviceName) {
      case 'RealtimeManager':  ,
        // Service-specific diagnostics disabled in React Native to avoid require issues;
        return { note: 'Realtime stats unavailable in mobile environment' };

      case 'AdvancedCacheManager':  ,
        return { note: 'Cache stats unavailable in mobile environment' };

      case 'PerformanceOptimizer':  ,
        return { note: 'Performance stats unavailable in mobile environment' };

      default:  ,
        return {};
    }
  } catch (error) {
    return { error: 'Failed to get service info' };
  }
}
/**;
 * Monitor service health and log issues periodically;
 */
export function startHealthMonitoring(intervalMs: number = 120000): number { const isDevelopment = __DEV__ || process.env.NODE_ENV === 'development';

  return setInterval(async () = > {
  try {
      const report = await getDetailedHealthReport()
      // Only log if there are critical issues or in production;
      if (report.criticalIssues > 0 || !isDevelopment) {
        await logHealthReport() }
    } catch (error) {
      logger.error('Health monitoring failed', 'ServiceHealthDebugger', { error }),
    }
  }, intervalMs),
}
/**;
 * Get a quick health status summary;
 */
export async function getQuickHealthStatus(): Promise<{ allHealthy: boolean,
  criticalIssues: number,
  summary: string }>
  try {
    const report = await getDetailedHealthReport()
    const allHealthy = report.criticalIssues === 0;
    const summary =;
      `${report.healthyServices}/${report.totalServices} services healthy` +;
      (report.criticalIssues > 0 ? ` (${report.criticalIssues} critical issues)`   : '')
    return {
      allHealthy;
      criticalIssues: report.criticalIssues
      summary;
    },
  } catch (error) {
    return {
      allHealthy: false;
      criticalIssues: 1,
      summary: 'Health check failed'
    },
  }
}