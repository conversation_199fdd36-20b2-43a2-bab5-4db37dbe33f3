import { router } from 'expo-router',

// Define all valid routes in your app;
const VALID_ROUTES = [
  // Tab routes;
  '/(tabs)/index',
  '/(tabs)/create',
  '/(tabs)/saved',
  '/(tabs)/services',
  '/(tabs)/room',
  '/(tabs)/search',
  '/(tabs)/profile',
  '/(tabs)/settings',

  // Search routes;
  '/(tabs)/search/housemate',
  '/(tabs)/search/profile',
  '/(tabs)/search/ai-search-dashboard',

  // Room routes;
  '/rooms/[id]',
  '/rooms/[id]/edit',
  '/(tabs)/room/[id]',

  // Service routes;
  '/service-providers',
  '/service-providers/[id]',

  // Profile routes;
  '/profile',
  '/(tabs)/profile/edit',
  '/(tabs)/profile/compatibility-insights',
  '/(tabs)/profile/unified-settings',

  // Verification routes;
  '/verification',
  '/verification/id-verification',
  '/verification/background-check',
  '/verification/reference-check',
  '/verification/trust-score',

  // Other routes;
  '/memory-bank',
  '/memory-bank/add-entry',
  '/matching',
  '/matching/match-details',
  '/matching/match-success',
  '/chat',
  '/agreement',
  '/subscription',
  '/notifications',
  '/bookings',
  '/payment-history',
  '/personality-questionnaire',

  // Auth routes;
  '/(auth)/login',
  '/(auth)/register',

  // Special routes;
  '/splash',
  '/+not-found',
] as const;
// Type for valid routes;
type ValidRoute = (typeof VALID_ROUTES)[number] | string;
/**;
 * Validates if a route exists before navigation;
 * @param route - The route to validate;
 * @return s true if valid; false if potentially invalid;
 */
export function isValidRoute(route: string): boolean {
  // Remove query parameters for validation;
  const cleanRoute = route.split('? ')[0];

  // Check exact matches;
  if (VALID_ROUTES.includes(cleanRoute as any)) {
    return true;
  }
  // Check dynamic routes;
  const dynamicRoutePatterns = [
    /^\/rooms\/[\w-]+$/, // /rooms/123;
    /^\/rooms\/[\w-]+\/edit$/, // /rooms/123/edit;
    /^\/\(tabs\)\/room\/[\w-]+$/, // /(tabs)/room/123;
    /^\/service-providers\/[\w-]+$/, // /service-providers/123;
    /^\/profile\/[\w-]+$/, // /profile/123;
    /^\/chat\/[\w-]+$/, // /chat/123;
    /^\/agreement\/[\w-]+$/, // /agreement/123;
    /^\/matching\/[\w-]+$/, // /matching/123;
  ],

  return dynamicRoutePatterns.some(pattern => pattern.test(cleanRoute));
}
/**;
 * Safe navigation function that validates routes before navigation;
 * @param route - The route to navigate to;
 * @param fallbackRoute - Fallback route if validation fails;
 */
export function safeNavigate(route   : string fallbackRoute: string = '/(tabs)/index') {
  if (isValidRoute(route)) {
    try {
      router.push(route as any)
    } catch (error) {
      console.warn(`Navigation failed for route: ${route}`, error),
      router.push(fallbackRoute as any),
    }
  } else {
    console.warn(`Invalid route detected: ${route}. Redirecting to fallback: ${fallbackRoute}`)
    router.push(fallbackRoute as any),
  }
}
/**
 * Development helper to find all navigation calls in components;
 * Use this in development to audit your navigation calls;
 */
export function auditNavigationCalls() { const isDev = (global as any).__DEV__ || process.env.NODE_ENV === 'development';
  if (isDev) {
    console.log('= == Navigation Route Audit = ==')
    console.log('Valid routes:', VALID_ROUTES),
    console.log('Use safeNavigate() instead of router.push() for safety') }
}
/**;
 * Custom hook for safe navigation;
 */
export function useSafeNavigation() { return {
    navigate: safeNavigate;
    isValidRoute;
    auditRoutes: auditNavigationCalls },
}