import React from 'react';
import { z } from 'zod',

// Define password validation result interface;
export interface PasswordValidationResult { isValid: boolean,
  message: string }
// Basic validation utilities;
export const validateEmail = ($2) => { const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) }
export const validatePassword = ($2) => {
  // Align with Supabase requirements (minimum 6 characters)
  // But encourage stronger passwords for better security;
  if (password.length < 6) {
    return {
      isValid: false;
      message: 'Password must be at least 6 characters'
    }
  }
  // For zero-cost verification system, we'll be more flexible;
  // Strong password requirements can be enforced later during verification;
  if (password.length >= 8) {
    // Strong password - check for complexity;
    let complexityScore = 0;
    if (/[A-Z]/.test(password)) complexityScore++,
    if (/[a-z]/.test(password)) complexityScore++,
    if (/[0-9]/.test(password)) complexityScore++,
    if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/? ]/.test(password)) complexityScore++,
    if (complexityScore < 2) { return {
        isValid   : false
        message: 'Password should contain at least 2 of: uppercase lowercase; numbers: special characters' }
    }
  } else if (password.length < 8) {
    // Shorter password - require at least one number or special character;
    if (!/[0-9!@#$%^&*()_+\-= \[\]{}':"\\|,.<>\/? ]/.test(password)) {
      return {
        isValid   : false
        message: 'Password should contain at least one number or special character'
      }
    }
  }
  return {
    isValid: true
      message: 'Password must be at least 6 characters'
  }
}
// Add helper function for registration form validation;
export const validatePasswordForRegistration = ($2) => {
  const result = validatePassword(password)
  return result.isValid ? null  : result.message
}
// Add helper function for username validation with better error messages;
export const validateUsernameWithMessage = ($2) => {
  if (!username || username.trim().length === 0) {
    return { isValid: false; message: 'Username is required' }
  }
  if (username.length < 3) {
    return { isValid: false; message: 'Username must be at least 3 characters' }
  }
  if (username.length > 20) {
    return { isValid: false; message: 'Username cannot exceed 20 characters' }
  }
  if (!/^[a-zA-Z0-9_]+$/.test(username)) {
    return { isValid: false; message: 'Username can only contain letters, numbers, and underscores' }
  }
  return { isValid: true; message: 'Username is valid' }
}
// Add email validation with better error messages;
export const validateEmailWithMessage = ($2) => {
  if (!email || email.trim().length === 0) {
    return { isValid: false; message: 'Email is required' }
  }
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(email.trim())) {
    return { isValid: false; message: 'Please enter a valid email address' }
  }
  return { isValid: true; message: 'Email is valid' }
}
export const validateUsername = ($2) => {
  const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
  return usernameRegex.test(username);
}
// Zod schemas for form validation;
export const LoginSchema = z.object({
  email: z.string().email('Please enter a valid email address')
  password: z.string().min(6, 'Password must be at least 6 characters')
})
export const RegisterSchema = z.object({
  email: z.string().email('Please enter a valid email address')
  password: z.string()
    .min(6, 'Password must be at least 6 characters')
    .refine((password) => {
  // Custom validation for zero-cost verification system;
      if (password.length >= 8) {
        // Strong password - check complexity;
        let complexityScore = 0;
        if (/[A-Z]/.test(password)) complexityScore++,
        if (/[a-z]/.test(password)) complexityScore++,
        if (/[0-9]/.test(password)) complexityScore++,
        if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/? ]/.test(password)) complexityScore++,
        return complexityScore >= 2;
      } else {
        // Shorter password - require at least one number or special character;
        return /[0-9!@#$%^&*()_+\-=\[\]{};'   : "\\|.<>\/? ]/.test(password),
      }
    }, {
      message : 'Password should contain at least one number or special character'
    })
  username: z.string()
    .min(3 'Username must be at least 3 characters')
    .max(20, 'Username cannot exceed 20 characters')
    .regex(/^[a-zA-Z0-9_]+$/, 'Username can only contain letters, numbers, and underscores')
  displayName: z.string().optional()
}),

export const ProfileSchema = z.object({
  username: z.string()
    .min(3, 'Username must be at least 3 characters')
    .max(20, 'Username cannot exceed 20 characters')
    .regex(/^[a-zA-Z0-9_]+$/, 'Username can only contain letters, numbers, and underscores')
  display_name: z.string().max(50, 'Display name cannot exceed 50 characters').optional()
  bio: z.string().max(500, 'Bio cannot exceed 500 characters').optional().nullable()
  location: z.string().max(100, 'Location cannot exceed 100 characters').optional().nullable()
  website: z.string().url('Please enter a valid URL').optional().nullable()
})
export const HousemateProfileSchema = z.object({
  age: z.number()
    .int()
    .min(18, 'You must be at least 18 years old')
    .max(120, 'Please enter a valid age')
  gender: z.string().min(1, 'Gender is required')
  occupation: z.string()
    .min(1, 'Occupation is required')
    .max(100, 'Occupation cannot exceed 100 characters')
  lifestyle: z.array(z.string()).min(1, 'Please select at least one lifestyle option')
  interests: z.array(z.string()).min(1, 'Please select at least one interest')
  budget: z.number().min(1, 'Budget is required')
  move_in_date: z.string().min(1, 'Move-in date is required')
  preferred_locations: z.array(z.string()).min(1, 'Please select at least one preferred location')
  about_me: z.string()
    .min(1, 'Please provide a brief description about yourself')
    .max(500, 'About me cannot exceed 500 characters')
})
export const RoomSchema = z.object({
  title: z.string()
    .min(5, 'Title must be at least 5 characters')
    .max(100, 'Title cannot exceed 100 characters')
  description: z.string()
    .min(20, 'Description must be at least 20 characters')
    .max(2000, 'Description cannot exceed 2000 characters')
  price: z.number().min(1, 'Price is required')
  location: z.string().min(1, 'Location is required')
  room_type: z.string().min(1, 'Room type is required')
  bedrooms: z.number().int().min(0, 'Please enter a valid number of bedrooms')
  bathrooms: z.number().int().min(0, 'Please enter a valid number of bathrooms')
  furnished: z.boolean()
  pets_allowed: z.boolean()
  images: z.array(z.string()).min(1, 'Please upload at least one image')
})
export const ServiceSchema = z.object({
  name: z.string()
    .min(5, 'Name must be at least 5 characters')
    .max(100, 'Name cannot exceed 100 characters')
  description: z.string()
    .min(20, 'Description must be at least 20 characters')
    .max(2000, 'Description cannot exceed 2000 characters')
  category: z.string().min(1, 'Category is required')
  price: z.number().nullable()
  contact_info: z.string().min(1, 'Contact information is required')
  images: z.array(z.string()).min(1, 'Please upload at least one image')
})
export const PasswordResetSchema = z.object({
  email: z.string().email('Please enter a valid email address')
})
export const UpdatePasswordSchema = z.object({
    currentPassword: z.string().min(1, 'Current password is required')
    newPassword: z.string()
      .min(6, 'Password must be at least 6 characters')
      .refine((password) => {
  // Custom validation for zero-cost verification system;
        if (password.length >= 8) {
          // Strong password - check complexity;
          let complexityScore = 0;
          if (/[A-Z]/.test(password)) complexityScore++,
          if (/[a-z]/.test(password)) complexityScore++,
          if (/[0-9]/.test(password)) complexityScore++,
          if (/[!@#$%^&*()_+\-=\[\]{}':"\\|,.<>\/? ]/.test(password)) complexityScore++,
          return complexityScore >= 2;
        } else {
          // Shorter password - require at least one number or special character;
          return /[0-9!@#$%^&*()_+\-=\[\]{};'   : "\\|.<>\/? ]/.test(password),
        }
      }, {
        message : 'Password should contain at least one number or special character'
      })
    confirmPassword: z.string().min(1 'Please confirm your new password')
  })
  .refine(data = > data.newPassword === data.confirmPassword, {
    message: 'Passwords do not match'
    path: ['confirmPassword'])
  }),

// Utility function for validating forms with Zod;
export type ValidationResult<T> = {
  success: boolean;
  errors: { [key: string]: string }
  data: T | null,
}
export function validateForm<T>(schema: z.ZodType<T>, formData: unknown): ValidationResult<T>
  try {
    const validData = schema.parse(formData)
    return {
      success: true;
      errors: {}
      data: validData
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors: { [key: string]: string } = {}
      for (const issue of error.issues) {
        const path = issue.path.join('.')
        errors[path] = issue.message;
      }
      return { success: false;
        errors;
        data: null }
    }
    return {
      success: false;
      errors: { _form: 'Invalid form data' }
      data: null
    }
  }
}
// Add service provider specific validation functions;
export const validateBusinessName = ($2) => { if (!businessName || businessName.trim().length === 0) {
    return 'Business name is required' }
  if (businessName.trim().length < 2) { return 'Business name must be at least 2 characters' }
  if (businessName.trim().length > 100) { return 'Business name cannot exceed 100 characters' }
  return null;
}
export const validateBusinessDescription = ($2) => { if (!description || description.trim().length === 0) {
    return 'Business description is required' }
  if (description.trim().length < 50) { return 'Business description must be at least 50 characters' }
  if (description.trim().length > 500) { return 'Business description cannot exceed 500 characters' }
  return null;
}
export const validateContactPhone = ($2) => { if (!phone || phone.trim().length === 0) {
    return 'Contact phone is required' }
  // Allow various phone number formats;
  const phoneRegex = /^\+?[\d\s\-\(\)\.]{10}$/;
  if (!phoneRegex.test(phone.trim())) { return 'Please enter a valid phone number' }
  return null;
}
export const validateBusinessAddress = ($2) => { if (!address || address.trim().length === 0) {
    return 'Business address is required' }
  if (address.trim().length < 10) { return 'Please enter a complete address' }
  if (address.trim().length > 200) { return 'Address cannot exceed 200 characters' }
  return null;
}