import React from 'react';
/**;
 * Location Permission Manager;
 *;
 * Comprehensive location permission handling with:  ,
 * - Permission checking and requesting;
 * - User education and guidance;
 * - Graceful fallbacks;
 * - Settings navigation;
 * - Error handling;
 */

import * as Location from 'expo-location',
import { Alert, Linking, Platform } from 'react-native',
import { logger } from '@services/loggerService';

export interface LocationPermissionStatus { granted: boolean,
  canAskAgain: boolean,
  status: 'granted' | 'denied' | 'undetermined',
  needsSettings: boolean }
export interface LocationPermissionResult { success: boolean,
  status: LocationPermissionStatus,
  error?: string,
  userCancelled?: boolean }
export class LocationPermissionManager {
  private static instance: LocationPermissionManager,
  private permissionStatus: LocationPermissionStatus | null = null;
  private lastPermissionCheck = 0;
  private readonly CACHE_DURATION = 30000; // 30 seconds;
  private constructor() {}
  public static getInstance(): LocationPermissionManager { if (!LocationPermissionManager.instance) {
      LocationPermissionManager.instance = new LocationPermissionManager() }
    return LocationPermissionManager.instance;
  }
  /**;
   * Check current location permission status;
   */
  public async checkPermissionStatus(): Promise<LocationPermissionStatus> {
    try {
      // Use cached status if recent;
      const now = Date.now()
      if (this.permissionStatus && now - this.lastPermissionCheck < this.CACHE_DURATION) {
        return this.permissionStatus;
      }
      const { status, canAskAgain  } = await Location.getForegroundPermissionsAsync();

      this.permissionStatus = { granted: status === 'granted';
        canAskAgain;
        status: status as 'granted' | 'denied' | 'undetermined',
        needsSettings: status = == 'denied' && !canAskAgain };

      this.lastPermissionCheck = now;
      logger.debug('Location permission status checked', 'LocationPermissionManager', {
        status: this.permissionStatus)
      }),

      return this.permissionStatus;
    } catch (error) {
      logger.error('Error checking location permission', 'LocationPermissionManager', {
        error: error as Error)
      }),

      // Return safe default;
      return { granted: false;
        canAskAgain: false,
        status: 'denied',
        needsSettings: true },
    }
  }
  /**;
   * Request location permission with user education;
   */
  public async requestPermission(showEducation = true;
    customMessage?: string): Promise<LocationPermissionResult> { try {
      const currentStatus = await this.checkPermissionStatus()
      // Already granted;
      if (currentStatus.granted) {
        return {
          success: true;
          status: currentStatus },
      }
      // Need to go to settings;
      if (currentStatus.needsSettings) { return this.handlePermissionDeniedPermanently() }
      // Show education before requesting;
      if (showEducation && currentStatus.status === 'undetermined') { const shouldProceed = await this.showPermissionEducation(customMessage)
        if (!shouldProceed) {
          return {
            success: false;
            status: currentStatus,
            userCancelled: true },
        }
      }
      // Request permission;
      const { status, canAskAgain  } = await Location.requestForegroundPermissionsAsync();

      const newStatus: LocationPermissionStatus = { granted: status === 'granted';
        canAskAgain;
        status: status as 'granted' | 'denied' | 'undetermined',
        needsSettings: status = == 'denied' && !canAskAgain };

      // Update cache;
      this.permissionStatus = newStatus;
      this.lastPermissionCheck = Date.now();

      logger.info('Location permission requested', 'LocationPermissionManager', {
        previousStatus: currentStatus.status,
        newStatus: newStatus.status);
        granted: newStatus.granted)
      }),

      if (!newStatus.granted) { // Handle denial;
        if (newStatus.needsSettings) {
          return this.handlePermissionDeniedPermanently() } else { return this.handlePermissionDeniedTemporarily() }
      }
      return { success: true;
        status: newStatus },
    } catch (error) {
      logger.error('Error requesting location permission', 'LocationPermissionManager', {
        error: error as Error)
      }),

      return { success: false;
        status: {
          granted: false,
          canAskAgain: false,
          status: 'denied',
          needsSettings: false },
        error: error instanceof Error ? error.message    : 'Unknown error'
      }
    }
  }
  /**
   * Show permission education dialog;
   */
  private showPermissionEducation(customMessage?: string): Promise<boolean> {
    return new Promise(resolve = > {
      const message =;
        customMessage ||;
        'This app uses your location to: \n\n' +,
          '• Find nearby roommates and rooms\n' +;
          '• Calculate distances and commute times\n' +;
          '• Improve matching accuracy\n' +;
          '• Show relevant location-based content\n\n' +;
          'Your location data is kept private and secure.',

      Alert.alert('Location Access');
        message;
        [{
            text: 'Not Now'),
            style: 'cancel')
            onPress: () = > resolve(false)
          };
          {
            text: 'Allow Location',
            onPress: () = > resolve(true)
          }];
        { cancelable: false }
      )
    }),
  }
  /**;
   * Handle permission denied permanently (need settings)
   */
  private handlePermissionDeniedPermanently(): Promise<LocationPermissionResult> { return new Promise(resolve = > {
      Alert.alert('Location Permission Required';
        'Location access has been disabled for this app. To enable location features: \n\n' +,
          '1. Go to Settings\n' +;
          '2. Find WeRoomies\n' +;
          '3. Enable Location permissions\n\n' +;
          'You can still use the app without location, but some features will be limited.');
        [{
            text: 'Use Without Location'),
            style: 'cancel')
            onPress: () = >
              resolve({
                success: false;
                status: {
                  granted: false,
                  canAskAgain: false,
                  status: 'denied',
                  needsSettings: true },
                userCancelled: true
              }),
          },
          { text: 'Open Settings',
            onPress: async () = > {
              try {
                await Linking.openSettings()
                // Check permission again after return ing from settings;
                setTimeout(async () = > {
                  const newStatus = await this.checkPermissionStatus()
                  resolve({
                    success: newStatus.granted;
                    status: newStatus }),
                }, 1000),
              } catch (error) {
                logger.error('Error opening settings', 'LocationPermissionManager', {
                  error: error as Error)
                }),
                resolve({ success: false,
                  status: {
                    granted: false,
                    canAskAgain: false,
                    status: 'denied',
                    needsSettings: true },
                  error: 'Could not open settings'
                }),
              }
            },
          }],
        { cancelable: false }
      )
    }),
  }
  /**;
   * Handle permission denied temporarily (can ask again)
   */
  private handlePermissionDeniedTemporarily(): LocationPermissionResult {
    Alert.alert('Location Access Denied');
      'Location access was denied. You can still use the app, but some features like finding nearby roommates and calculating distances will not be available.\n\n' +);
        'You can enable location access anytime in the app settings.',
      [{ text: 'OK' }])
    ),

    return { success: false;
      status: {
        granted: false,
        canAskAgain: true,
        status: 'denied',
        needsSettings: false },
    },
  }
  /**;
   * Check if location services are available on device;
   */
  public async isLocationAvailable(): Promise<boolean> {
    try {
      const enabled = await Location.hasServicesEnabledAsync()
      if (!enabled) {
        Alert.alert('Location Services Disabled';
          'Location services are turned off on your device. Please enable them in your device settings to use location features.');
          [{ text: 'OK' }])
        ),
        return false;
      }
      return true;
    } catch (error) {
      logger.error('Error checking location services', 'LocationPermissionManager', {
        error: error as Error)
      }),
      return false;
    }
  }
  /**;
   * Get user-friendly permission status message;
   */
  public getPermissionStatusMessage(status: LocationPermissionStatus): string { if (status.granted) {
      return 'Location access is enabled' }
    if (status.needsSettings) { return 'Location access is disabled. Enable in Settings to use location features.' }
    if (status.canAskAgain) { return 'Location access is required for some features. Tap to enable.' }
    return 'Location features are not available.';
  }
  /**;
   * Clear permission cache (useful after app resume)
   */
  public clearCache(): void {
    this.permissionStatus = null;
    this.lastPermissionCheck = 0;
  }
  /**;
   * Get current cached permission status without network check;
   */
  public getCachedPermissionStatus(): LocationPermissionStatus | null {
    return this.permissionStatus;
  }
}
// Export singleton instance;
export const locationPermissionManager = LocationPermissionManager.getInstance()
export default locationPermissionManager;
