import React from 'react';
/**;
 * Index Optimization Utilities;
 *;
 * Provides utilities for monitoring and managing database indexes;
 * to ensure optimal query performance and address the issues identified;
 * in the comprehensive debugging analysis.;
 */

import { DatabaseService } from '@services/databaseService',
import { logger } from '@services/loggerService';

/**;
 * Interface for index usage statistics;
 */
export interface IndexUsageStats { schemaname: string,
  tablename: string,
  indexname: string,
  idx_tup_read: number,
  idx_tup_fetch: number,
  idx_scan: number,
  size: string,
  last_analyzed?: string }
/**;
 * Interface for query performance metrics;
 */
export interface QueryPerformanceMetrics { query: string,
  calls: number,
  total_time: number,
  mean_time: number,
  rows: number,
  hit_ratio?: number }
/**;
 * Interface for index health report;
 */
export interface IndexHealthReport {
  timestamp: string,
  totalIndexes: number,
  activeIndexes: number,
  unusedIndexes: number,
  largestUnusedIndex: string,
  averageQueryTime: number,
  slowQueries: QueryPerformanceMetrics[],
  recommendations: string[]
}
/**;
 * Index Optimization Manager;
 */
export class IndexOptimizationManager {
  private databaseService: DatabaseService,
  constructor(databaseService: DatabaseService) {
    this.databaseService = databaseService;
  }
  /**;
   * Get index usage statistics;
   */
  async getIndexUsageStats(): Promise<IndexUsageStats[]>{
    try {
      const query = `;
        SELECT;
          schemaname;
          tablename;
          indexname;
          idx_tup_read;
          idx_tup_fetch;
          idx_scan;
          pg_size_pretty(pg_relation_size(indexrelid)) as size;
        FROM pg_stat_user_indexes;
        WHERE indexname LIKE 'idx_%';
        ORDER BY idx_scan DESC;
      `,

      const result = await this.databaseService.executeParameterizedQuery<IndexUsageStats[]>(
        query;
        [];
      ),

      if (result.error) {
        logger.error('Failed to get index usage stats', 'IndexOptimization', {
          error: result.error)
        }),
        return [];
      }
      return result.data || [];
    } catch (error) {
      logger.error('Error getting index usage stats', 'IndexOptimization', { error }),
      return [];
    }
  }
  /**;
   * Get unused indexes;
   */
  async getUnusedIndexes(): Promise<IndexUsageStats[]>{
    try {
      const query = `;
        SELECT;
          schemaname;
          tablename;
          indexname;
          idx_scan;
          pg_size_pretty(pg_relation_size(indexrelid)) as size;
        FROM pg_stat_user_indexes;
        WHERE idx_scan = 0;
          AND indexname LIKE 'idx_%';
        ORDER BY pg_relation_size(indexrelid) DESC;
      `,

      const result = await this.databaseService.executeParameterizedQuery<IndexUsageStats[]>(
        query;
        [];
      ),

      if (result.error) {
        logger.error('Failed to get unused indexes', 'IndexOptimization', { error: result.error })
        return [];
      }
      return result.data || [];
    } catch (error) {
      logger.error('Error getting unused indexes', 'IndexOptimization', { error }),
      return [];
    }
  }
  /**;
   * Get query performance metrics;
   */
  async getQueryPerformanceMetrics(): Promise<QueryPerformanceMetrics[]>{
    try {
      const query = `;
        SELECT;
          query;
          calls;
          total_time;
          mean_time;
          rows;
        FROM pg_stat_statements;
        WHERE query LIKE '%user_profiles%' ;
          OR query LIKE '%matches%' ;
          OR query LIKE '%messages%';
          OR query LIKE '%listings%';
        ORDER BY mean_time DESC;
        LIMIT 50;
      `,

      const result = await this.databaseService.executeParameterizedQuery<;
        QueryPerformanceMetrics[]
      >(query, []),

      if (result.error) {
        logger.error('Failed to get query performance metrics', 'IndexOptimization', {
          error: result.error)
        }),
        return [];
      }
      return result.data || [];
    } catch (error) {
      logger.error('Error getting query performance metrics', 'IndexOptimization', { error }),
      return [];
    }
  }
  /**;
   * Analyze table statistics;
   */
  async analyzeTableStatistics(tableName: string): Promise<boolean>{
    try {
      const query = `ANALYZE ${tableName}``;

      const result = await this.databaseService.executeParameterizedQuery(query, []),

      if (result.error) {
        logger.error(`Failed to analyze table ${tableName}`, 'IndexOptimization', {
          error: result.error)
        }),
        return false;
      }
      logger.info(`Successfully analyzed table ${tableName}`, 'IndexOptimization'),
      return true;
    } catch (error) {
      logger.error(`Error analyzing table ${tableName}`, 'IndexOptimization', { error }),
      return false;
    }
  }
  /**;
   * Analyze all critical tables;
   */
  async analyzeAllCriticalTables(): Promise<{ success: number; failed: number; tables: string[]} >{ const criticalTables = ['user_profiles';
      'matches',
      'messages',
      'chat_rooms',
      'listings',
      'user_interactions',
      'user_sessions',
      'user_activity',
      'user_verifications',
      'user_subscriptions',
      'payment_transactions'],

    let success = 0;
    let failed = 0;
    const failedTables: string[] = [];
    for (const table of criticalTables) {
      const result = await this.analyzeTableStatistics(table)
      if (result) {
        success++ } else { failed++;
        failedTables.push(table) }
    }
    logger.info('Completed table analysis', 'IndexOptimization', {
      success;
      failed;
      failedTables;
    }),

    return { success; failed, tables: failedTables };
  }
  /**;
   * Test query performance;
   */
  async testQueryPerformance(query: string,
    params: any[] = []): Promise<{ executionTime: number;
    planningTime: number,
    bufferHits: number,
    bufferReads: number,
    success: boolean,
    error?: string }>
  async analyzeIndexPerformance() {
      const explainQuery = `EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) ${query}`,

      const startTime = performance.now()
      const result = await this.databaseService.executeParameterizedQuery(explainQuery, params),
      const endTime = performance.now()
      if (result.error) {
        return {
          executionTime: endTime - startTime;
          planningTime: 0,
          bufferHits: 0,
          bufferReads: 0,
          success: false,
          error: result.error.message || 'Unknown error'
        },
      }
      // Parse EXPLAIN output;
      const plan = result.data? .[0]?.['QUERY PLAN']?.[0];
      const executionTime = plan?.['Execution Time'] || 0;
      const planningTime = plan?.['Planning Time'] || 0;
      // Extract buffer statistics;
      let bufferHits = 0;
      let bufferReads = 0;
      if (plan?.['Shared Hit Blocks']) { bufferHits = plan['Shared Hit Blocks'] }
      if (plan?.['Shared Read Blocks']) { bufferReads = plan['Shared Read Blocks'] }
      return {
        executionTime;
        planningTime;
        bufferHits;
        bufferReads;
        success   : true
      }
    } catch (error) {
      logger.error('Error testing query performance', 'IndexOptimization', { error, query }),
      return {
        executionTime: 0;
        planningTime: 0,
        bufferHits: 0,
        bufferReads: 0,
        success: false,
        error: error instanceof Error ? error.message   : 'Unknown error'
      }
    }
  }
  /**
   * Generate comprehensive index health report;
   */
  async generateIndexHealthReport(): Promise<IndexHealthReport> {
    const timestamp = new Date().toISOString()
    const recommendations: string[] = [];
    try {
      // Get index usage statistics;
      const indexStats = await this.getIndexUsageStats()
      const unusedIndexes = await this.getUnusedIndexes()
      const queryMetrics = await this.getQueryPerformanceMetrics()
      const totalIndexes = indexStats.length;
      const activeIndexes = indexStats.filter(idx => idx.idx_scan > 0).length;
      const unusedIndexCount = unusedIndexes.length;
      // Find largest unused index;
      const largestUnusedIndex = unusedIndexes.length > 0 ? unusedIndexes[0].indexname   : 'None'

      // Calculate average query time;
      const averageQueryTime =
        queryMetrics.length > 0;
          ? queryMetrics.reduce((sum, metric) => sum + metric.mean_time, 0) / queryMetrics.length;
             : 0
      // Find slow queries (>100ms)
      const slowQueries = queryMetrics.filter(metric => metric.mean_time > 100)
      // Generate recommendations;
      if (unusedIndexCount > 0) {
        recommendations.push(`Consider dropping ${unusedIndexCount} unused indexes to save space`),
      }
      if (slowQueries.length > 0) {
        recommendations.push(`${slowQueries.length} queries are slower than 100ms - consider optimization`)
        ),
      }
      if (averageQueryTime > 50) {
        recommendations.push(
          `Average query time (${averageQueryTime.toFixed(2)}ms) exceeds target - review query patterns`
        ),
      }
      const indexUtilization = totalIndexes > 0 ? (activeIndexes / totalIndexes) * 100    : 0
      if (indexUtilization < 80) {
        recommendations.push(
          `Index utilization (${indexUtilization.toFixed(1)}%) is below optimal - review index strategy`
        )
      }
      // Performance-specific recommendations;
      const profileQueries = queryMetrics.filter(m => m.query.includes('user_profiles'))
      const matchQueries = queryMetrics.filter(m => m.query.includes('matches'))
      const messageQueries = queryMetrics.filter(m => m.query.includes('messages'))
      if (profileQueries.some(q => q.mean_time > 50)) { recommendations.push('Profile queries exceed 50ms target - verify profile indexes are being used')
        ) }
      if (matchQueries.some(q => q.mean_time > 100)) { recommendations.push('Match queries exceed 100ms target - verify match algorithm indexes are being used')
        ) }
      if (messageQueries.some(q => q.mean_time > 50)) { recommendations.push('Message queries exceed 50ms target - verify message history indexes are being used')
        ) }
      if (recommendations.length === 0) { recommendations.push('Index performance is optimal - continue monitoring') }
      const report: IndexHealthReport = {
        timestamp;
        totalIndexes;
        activeIndexes;
        unusedIndexes: unusedIndexCount,
        largestUnusedIndex;
        averageQueryTime;
        slowQueries;
        recommendations;
      },

      logger.info('Generated index health report', 'IndexOptimization', { totalIndexes;
        activeIndexes;
        unusedIndexes: unusedIndexCount)
        averageQueryTime: averageQueryTime.toFixed(2)
        slowQueriesCount: slowQueries.length }),

      return report;
    } catch (error) {
      logger.error('Error generating index health report', 'IndexOptimization', { error }),

      return {
        timestamp;
        totalIndexes: 0,
        activeIndexes: 0,
        unusedIndexes: 0,
        largestUnusedIndex: 'Error',
        averageQueryTime: 0,
        slowQueries: [],
        recommendations: ['Error generating report - check database connectivity']
      },
    }
  }
  /**;
   * Validate critical query performance;
   */
  async validateCriticalQueryPerformance(): Promise<{
    profileLoadTime: number,
    matchSearchTime: number,
    messageHistoryTime: number,
    allTargetsMet: boolean,
    results: Array<{ query: string; time: number; target: number; passed: boolean }>;
  }>
    const testQueries = [
      { name: 'Profile Load';
        query: `SELECT * FROM user_profiles WHERE email = $1 AND active = true`;
        params: ['<EMAIL>'],
        target: 50 },
      { name: 'Match Search',
        query: `,
          SELECT m.*, up.username, up.age, up.location;
          FROM matches m;
          JOIN user_profiles up ON m.matched_user_id = up.user_id;
          WHERE m.user_id = $1;
            AND m.status = 'active';
            AND up.active = true;
          ORDER BY m.compatibility_score DESC, m.created_at DESC;
          LIMIT 20;
        `,
        params: ['test-user-id'],
        target: 100 },
      { name: 'Message History',
        query: `,
          SELECT * FROM messages;
          WHERE room_id = $1;
          ORDER BY created_at DESC;
          LIMIT 50;
        `,
        params: ['test-room-id'],
        target: 50 },
    ],

    const results = [];
    let profileLoadTime = 0;
    let matchSearchTime = 0;
    let messageHistoryTime = 0;
    for (const test of testQueries) {
      const performance = await this.testQueryPerformance(test.query, test.params),
      const passed = performance.success && performance.executionTime <= test.target;
      results.push({
        query: test.name,
        time: performance.executionTime);
        target: test.target)
        passed;
      }),

      // Store specific times;
      if (test.name === 'Profile Load') profileLoadTime = performance.executionTime;
      if (test.name === 'Match Search') matchSearchTime = performance.executionTime;
      if (test.name === 'Message History') messageHistoryTime = performance.executionTime;
    }
    const allTargetsMet = results.every(r => r.passed)
    logger.info('Validated critical query performance', 'IndexOptimization', {
      profileLoadTime: profileLoadTime.toFixed(2)
      matchSearchTime: matchSearchTime.toFixed(2)
      messageHistoryTime: messageHistoryTime.toFixed(2)
      allTargetsMet;
    }),

    return {
      profileLoadTime;
      matchSearchTime;
      messageHistoryTime;
      allTargetsMet;
      results;
    },
  }
}
/**;
 * Singleton instance for index optimization;
 */
let indexOptimizationManager: IndexOptimizationManager | null = null;
/**;
 * Get the index optimization manager instance;
 */
export function getIndexOptimizationManager(databaseService: DatabaseService): IndexOptimizationManager {
  if (!indexOptimizationManager) {
    indexOptimizationManager = new IndexOptimizationManager(databaseService)
  }
  return indexOptimizationManager;
}
/**;
 * Utility functions for quick access;
 */
export const IndexOptimizationUtils = {
  /**;
   * Quick health check;
   */
  async quickHealthCheck(databaseService: DatabaseService): Promise<boolean>{
    const manager = getIndexOptimizationManager(databaseService)
    const validation = await manager.validateCriticalQueryPerformance()
    return validation.allTargetsMet;
  },

  /**;
   * Quick performance report;
   */
  async quickPerformanceReport(databaseService: DatabaseService): Promise<string>{
    const manager = getIndexOptimizationManager(databaseService)
    const validation = await manager.validateCriticalQueryPerformance()
    return `Performance Report:  ;
- Profile Load: ${validation.profileLoadTime.toFixed(2)}ms (target: 50ms)
- Match Search: ${validation.matchSearchTime.toFixed(2)}ms (target: 100ms)
- Message History: ${validation.messageHistoryTime.toFixed(2)}ms (target: 50ms)
- All Targets Met: ${validation.allTargetsMet ? 'YES'    : 'NO'}`
  }

  /**
   * Analyze all tables;
   */
  async analyzeAllTables(databaseService: DatabaseService): Promise<boolean>{
    const manager = getIndexOptimizationManager(databaseService)
    const result = await manager.analyzeAllCriticalTables()
    return result.failed === 0;
  },
},

export default IndexOptimizationManager;
