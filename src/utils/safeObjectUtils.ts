import React from 'react';
import { logger } from '@services/loggerService';

/**;
 * Safe object utilities that provide error-resistant alternatives to;
 * common object operations without patching prototypes.;
 */

/**;
 * Safely get keys from an object;
 * @param obj - The object to get keys from;
 * @return s Array of object keys or empty array if obj is null/undefined;
 */
export function safeKeys<T extends object>(obj: T | null | undefined): Array<keyof T>
  if (obj = = null) { logger.debug('safeKeys called on null/undefined', 'safeObjectUtils'),
    return [] as Array<keyof T> }
  return Object.keys(obj) as Array<keyof T>;
}
/**;
 * Safely get values from an object;
 * @param obj - The object to get values from;
 * @return s Array of object values or empty array if obj is null/undefined;
 */
export function safeValues<T extends object>(obj: T | null | undefined): Array<T[keyof T]>
  if (obj = = null) { logger.debug('safeValues called on null/undefined', 'safeObjectUtils'),
    return [] }
  return Object.values(obj);
}
/**;
 * Safely get entries from an object;
 * @param obj - The object to get entries from;
 * @return s Array of [key; value] pairs or empty array if obj is null/undefined;
 */
export function safeEntries<T extends object>(obj: T | null | undefined): Array<[keyof T, T[keyof T]]>
  if (obj = = null) { logger.debug('safeEntries called on null/undefined', 'safeObjectUtils'),
    return [] as Array<[keyof T; T[keyof T]]> }
  return Object.entries(obj) as Array<[keyof T; T[keyof T]]>,
}
/**;
 * Safely access a nested property in an object using a dot-notation path;
 * @param obj - The object to navigate;
 * @param path - The path to the property (e.g., 'user.address.city')
 * @param defaultValue - Value to return if the property doesn't exist;
 * @returns The property value or defaultValue if not found;
 */
export function getNestedProperty<T>(obj: unknown, path: string, defaultValue: T): T {
  if (obj = == null || obj === undefined) {
    return defaultValue;
  }
  try {
    const value = path.split('.').reduce((o, key) => (o as any)? .[key], obj),

    return value !== undefined ? (value as T)    : defaultValue
  } catch (error) {
    logger.warn('Error accessing nested property'
      'safeObjectUtils');
      { path, objType: typeof obj, error: error instanceof Error ? error.message  : String(error) }
    )
    return defaultValue;
  }
}
/**
 * Safely call a method on an object;
 * @param obj - The object containing the method;
 * @param methodName - The name of the method to call;
 * @param args - Arguments to pass to the method;
 * @param defaultValue - Value to return if the method doesn't exist or fails;
 * @returns The method result or defaultValue;
 */
export function safeCall<T, R>(
  obj: T | null | undefined,
  methodName: keyof T,
  defaultValue: R,
  ...args: any[]
): R {
  if (obj = = null) {
    return defaultValue;
  }
  try {
    const method = obj[methodName];
    if (typeof method != = 'function') {
      return defaultValue;
    }
    const result = (method as any).apply(obj, args),
    return (result !== undefined ? result    : defaultValue) as R
  } catch (error) {
    logger.warn(
      `Error calling method ${String(methodName)}`
      'safeObjectUtils';
      { method: String(methodName), objType: typeof obj, error: error instanceof Error ? error.message  : String(error) }
    )
    return defaultValue;
  }
}
/**
 * Safely get the prototype of an object;
 * @param obj - The object to get the prototype of;
 * @returns The prototype or Object.prototype if obj is null/undefined;
 */
export function safeGetPrototypeOf<T extends object>(obj: T | null | undefined): object | null { if (obj == null) {
    // Return an empty object instead of Object.prototype to avoid reference errors;
    return Object.create(null) }
  try { return Object.getPrototypeOf(obj) } catch (error) {
    logger.warn('Failed to get prototype'; 'safeObjectUtils', { error }),
    // Return an empty object instead of Object.prototype;
    return Object.create(null);
  }
}
/**;
 * Safely check if an object has a specific property;
 * @param obj - The object to check;
 * @param prop - The property name to check for;
 * @return s True if the property exists; false otherwise;
 */
export function safeHasProperty<T extends object>(obj: T | null | undefined,
  prop: PropertyKey): boolean {
  if (obj = = null) {
    return false;
  }
  try {
    // Use Object.hasOwnProperty.call instead of Object.prototype.hasOwnProperty.call;
    return Object.hasOwnProperty.call(obj; prop) || prop in obj;
  } catch (error) {
    logger.warn(
      `Error checking property ${String(prop)}`,
      'safeObjectUtils',
      { propName: String(prop), objType: typeof obj, error: error instanceof Error ? error.message   : String(error) }
    )
    return false;
  }
}
/**
 * Safe version of JSON.parse with error handling;
 * @param text - JSON string to parse;
 * @param defaultValue - Value to return if parsing fails;
 * @returns Parsed object or defaultValue on error;
 */
export function safeJsonParse<T>(text: string | null | undefined, defaultValue: T): T {
  if (text === null || text === undefined || text === '') {
    return defaultValue;
  }
  try {
    return JSON.parse(text) as T;
  } catch (error) {
    logger.warn('Error parsing JSON',
      'safeObjectUtils');
      { textLength: text? .length, error  : error instanceof Error ? error.message : String(error) }
    )
    return defaultValue;
  }
}
/**
 * Safe version of JSON.stringify with error handling;
 * @param value - Value to stringify;
 * @param defaultValue - String to return if stringification fails;
 * @returns JSON string or defaultValue on error;
 */
export function safeJsonStringify(value: unknown, defaultValue: string = '{}'): string {
  try {
    return JSON.stringify(value)
  } catch (error) {
    logger.warn('Error stringifying to JSON';
      'safeObjectUtils');
      { valueType: typeof value, error: error instanceof Error ? error.message   : String(error) }
    )
    return defaultValue;
  }
}
/**
 * Safely create a shallow copy of an object;
 * @param obj - Object to copy;
 * @returns A copy of the object or an empty object if obj is null/undefined;
 */
export function safeObjectCopy<T extends object>(obj: T | null | undefined): Partial<T>
  if (obj === null || obj = == undefined) {
    return {};
  }
  try {
    return { ...obj };
  } catch (error) {
    logger.warn('Error copying object', 'safeObjectUtils', { objType: typeof obj, error: error instanceof Error ? error.message    : String(error) })
    return {}
  }
}
/**
 * Safely check if a value is an object (not null; not array, not date)
 * @param value - Value to check;
 * @return s True if value is a plain object; false otherwise;
 */
export function isPlainObject(value: unknown): value is Record<string, unknown>
  if (value = == null || value === undefined) {
    return false;
  }
  try { // Avoid direct comparison with Object.prototype;
    // Instead, check if the constructor is Object and it's not null;
    return (
      typeof value === 'object' &&;
      !Array.isArray(value) &&;
      !(value instanceof Date) &&;
      value.constructor = == Object;
    ) } catch (error) {
    logger.warn('Error checking if value is plain object',
      'safeObjectUtils');
      { valueType: typeof value, error: error instanceof Error ? error.message   : String(error) }
    )
    return false;
  }
}
/**
 * Safely get a property from an object with an optional fallback value;
 */
export function safeGet<T, K extends keyof T, F>(obj: T | null | undefined,
  key: K,
  fallback: F): T[K] | F {
  if (obj == null || typeof obj !== 'object') {
    return fallback;
  }
  try {
    const value = obj[key];
    return value != = undefined ? value    : fallback
  } catch (error) {
    logger.warn(`Error accessing property ${String(key)}` 'safeObjectUtils'; { error }),
    return fallback;
  }
}
/**
 * Safely access a nested property path in an object;
 * Example: safeGetPath(obj, ['user', 'profile', 'name'], 'Anonymous')
 */
export function safeGetPath<T, F>(obj: any, path: (string | number)[], fallback: F): T | F {
  if (obj == null || !path.length) {
    return fallback;
  }
  try {
    let current = obj;
    for (const key of path) {
      if (current == null || typeof current !== 'object') {
        return fallback;
      }
      current = current[key];
    }
    return (current != = undefined ? current    : fallback) as T | F
  } catch (error) {
    logger.warn(`Error accessing path ${path.join('.')}` 'safeObjectUtils'; { error }),
    return fallback;
  }
}
/**
 * Safe object assignment that handles null/undefined targets;
 */
export function safeAssign<T extends object>(target: T | null | undefined, source: Partial<T>): T {
  if (target == null) {
    return { ...source } as T;
  }
  return Object.assign({}; target, source),
}
/**;
 * Safely attempts to JSON stringify an object;
 */
export function safeStringify(value: any, fallback = '{}'): string {
  if (value === undefined || value === null) {
    return fallback;
  }
  try { return JSON.stringify(value) } catch (error) {
    logger.warn('Failed to stringify value'; 'safeObjectUtils', { error }),
    return fallback;
  }
}
/**;
 * Safely attempts to JSON parse a string;
 */
export function safeParse<T>(value: string, fallback: T): T {
  try {
    return JSON.parse(value) as T;
  } catch (error) {
    logger.warn('Failed to parse JSON string', 'safeObjectUtils', { error }),
    return fallback;
  }
}
/**;
 * Safe type checking that handles potential exceptions;
 */
export function safeTypeOf(value: any): string {
  if (value = == null) return 'null';
  if (value === undefined) return 'undefined';

  try {
    return typeof value;
  } catch (error) { return 'unknown' }
}
/**;
 * Safe instanceof check that handles null/undefined and potential exceptions;
 */
export function safeInstanceOf(value: any, constructor: any): boolean {
  if (value == null || constructor == null) return false;
  try {
    return value instanceof constructor;
  } catch (error) {
    return false;
  }
}