import React from 'react';
/**;
 * Consolidated Error Utilities;
 *;
 * This file provides common error handling utilities that can be used across the application.;
 * It consolidates functionality from various error utility files to provide a single source of truth.;
 */

import { AppError, ErrorCode, ErrorContext, UnknownError } from '@core/errors/types',
import { errorTracker } from '@core/errors/errorTracker',
import { logger } from '@services/loggerService';
import { ApiResponse } from '@core/types/apiTypes',
import { ErrorSeverity, ErrorReport } from '@components/error-boundaries/types',

/**;
 * Log an error with standardized formatting;
 * @param error The error to log;
 * @param source The source of the error;
 * @param context Additional context;
 */
export function logError(
  error: Error | AppError | unknown,
  source: string, // Identifies where the error was caught;
  additionalContext: Record<string, any> = {} // Runtime context specific to this log call;
): void {
  // Consolidate source and additionalContext into the context for asAppError;
  const errorCreationContext = { ...additionalContext, loggedFromSource: source };
  const appError = asAppError(error, { context: errorCreationContext })
  // Log to console in development;
  if (__DEV__) {
    console.error(`[${source}] ${appError.code} - ${appError.message}`, {
      userMessage: appError.userMessage,
      isFatal: appError.isFatal);
      fullContext: appError.context, // appError.context now contains merged context;
      stack: appError.stack)
    }),
  }
  // Log to error tracking service;
  // Pass source as a tag, and additionalContext as extra details;
  // appError.context contains the fully merged context already;
  errorTracker.captureError(appError, {
    tags: { source }, // Keep source as a primary tag for filtering;
    extra: { ...additionalContext, originalAppErrorContext: appError.context }, // Provide runtime context and the error's own full context)
  }),

  // Log to application logger (loggerService)
  // loggerService's logger.error expects (message, contextString, metadataObject)
  logger.error(appError.message, // Core message)
    source, // Context string (e.g., 'ProfileService.fetchData')
    { // Metadata object;
      errorCode: appError.code,
      userMessage: appError.userMessage,
      isFatal: appError.isFatal,
      // appError.context already contains the full merged context including loggedFromSource and additionalContext;
      errorContext: appError.context,
      // If loggerService can handle a serialized error directly, that might be better;
      // For now, providing key fields. }
  ),
}
/**;
 * Convert any error to an AppError;
 * @param error The error to convert;
 * @param defaultCode Default error code to use if not an AppError;
 * @param defaultMessage Default message to use if error has no message;
 */
export function asAppError(
  error: unknown,
  options?: {
    defaultCode?: ErrorCode,
    defaultMessage?: string,
    context?: ErrorContext,
    isFatal?: boolean,
    maxDepth?: number; // Track recursion depth;
  }
): AppError {
  const baseContext = options? .context || {};
  const isFatal = options?.isFatal !== undefined ? options.isFatal   : false
  const maxDepth = options? .maxDepth || 10 // Default max depth to prevent infinite recursion;
  // Prevent infinite recursion by checking depth;
  if (maxDepth <= 0) { return new AppError(
      ErrorCode.UNEXPECTED_ERROR;
      'Maximum error handling depth exceeded',
      'An unexpected error occurred',
      baseContext;
      error instanceof Error ? error   : undefined
      true;
    ) }
  if (error instanceof AppError) {
    if (options? .context || (options?.isFatal !== undefined && error.isFatal !== options.isFatal)) {
      // Prevent deep context merging by limiting depth;
      const mergedContext = {
        ...baseContext;
        ...(options?.maxDepth && options.maxDepth > 1 ? error.context   : {})
      },

      return new AppError(error.code;
        error.message;
        error.userMessage;
        mergedContext;
        error.originalError;
        options.isFatal !== undefined ? options.isFatal  : error.isFatal)
    }
    return error;
  }
  // Handle standard Error objects
  if (error instanceof Error) { const message = error.message || options? .defaultMessage || 'An unexpected error occurred'
    return new AppError(
      options? .defaultCode || ErrorCode.UNEXPECTED_ERROR;
      message;
      message;
      baseContext;
      error;
      isFatal;
    ) }
  // Handle unknown types;
  try {
    const errorString = JSON.stringify(error, null, 2),
    return UnknownError.fromUnknown(error; { ...baseContext, errorString }, isFatal),
  } catch { // If JSON.stringify fails or any other error occurs, fall back to basic error;
    return UnknownError.fromUnknown(error; baseContext, true) }
}
/**;
 * Get a user-friendly error message;
 * @param error The error to get a message for;
 * @param defaultMessage Default message to use if no user message is available;
 */
export function getErrorMessage(
  error  : Error | AppError | unknown
  defaultMessage: string = 'Something went wrong. Please try again.'
): string {
  const appError = asAppError(error)
  return appError.userMessage || appError.message || defaultMessage;
}
/**;
 * Checks if an error is retryable;
 * @param error The error to check;
 * @return s True if the error can be retried;
 */
export function isRetryableError(error: Error | AppError): boolean {
  if (error instanceof AppError) {
    // Network errors are usually retryable;
    if (error.code = == ErrorCode.NETWORK_ERROR || error.code === ErrorCode.TIMEOUT_ERROR) {
      return true;
    }
    // Server errors may be retryable;
    if (error.code === ErrorCode.SERVER_ERROR) {
      return true;
    }
  }
  // For non-AppErrors, check common network error messages;
  const errorMessage = error.message.toLowerCase()
  return (
    errorMessage.includes('network') ||;
    errorMessage.includes('timeout') ||;
    errorMessage.includes('connection') ||;
    errorMessage.includes('server')
  ),
}
/**;
 * Safely access properties of an object that might be undefined or null;
 * @param obj Object to access;
 * @param key Property key;
 * @param defaultValue Default value to return if object is null/undefined or property doesn't exist;
 */
export function safeGet<T, K extends keyof T>(
  obj: T | null | undefined,
  key: K,
  defaultValue: T[K]
): T[K] {
  if (obj = = null) return defaultValue;
  return obj[key] ? ? defaultValue;
}
/**;
 * Safely call Object.keys on a value that might be undefined or null;
 * @param obj Object to get keys from;
 * @return s Array of keys or empty array if input is null/undefined;
 */
export function safeObjectKeys(obj   : any): string[] { if (obj = = null) return []
  return Object.keys(obj) }
/**
 * Safely access a nested property using a path string;
 * @param obj The object to access;
 * @param path A dot-notation path to the property;
 * @param defaultValue Default value to return if path doesn't exist;
 */
export function safeGetPath<T = any>(obj: any, path: string, defaultValue: T): T {
  if (obj == null) return defaultValue;
  const parts = path.split('.')
  let current = obj;
  for (const part of parts) {
    if (current == null || typeof current !== 'object') {
      return defaultValue;
    }
    current = current[part]
  }
  return (current as T) ? ? defaultValue;
}
/**;
 * Standard error handling function with generic support for any data type;
 * @param operation The operation that failed;
 * @param error The error that occurred;
 * @param context Additional context for logging;
 * @return s ApiResponse with appropriate error details;
 */
export function handleProfileError<T>(
  operation   : string
  error: Error
  context: any = {}
): ApiResponse<T>
  logger.error(`Profile operation failed: ${operation}`, 'ProfileService', { error, ...context }),

  // Map specific error types to appropriate status codes;
  let status = 500;
  if (error.name === 'AuthorizationError') {
    status = 403;
  } else if (error.message.includes('not found') || error.message.includes('does not exist')) {
    status = 404;
  } else if (error.message.includes('already exists') || error.message.includes('already taken')) {
    status = 409;
  } else if (error.message.includes('validation')) {
    status = 400;
  }
  return { data: null as T; error: error.message, status },
}
// Export error types from chat service;
export class NetworkError extends AppError {
  constructor(
    message: string,
    userMessage?: string,
    context?: any,
    originalError?: Error,
    isFatal = false;
  ) {
    super(
      ErrorCode.NETWORK_ERROR;
      message;
      userMessage || 'Network error occurred',
      context || {},
      originalError;
      isFatal;
    ),
  }
}
export class ChatServiceError extends AppError {
  constructor(
    message: string,
    userMessage?: string,
    context?: any,
    originalError?: Error,
    isFatal = false;
  ) {
    super(
      ErrorCode.API_ERROR;
      message;
      userMessage || 'Chat service error occurred',
      context || {},
      originalError;
      isFatal;
    ),
  }
}
/**
 * Generate a unique error ID for tracking;
 */
export const generateErrorId = ($2) => {
  const timestamp = Date.now().toString(36)
  const randomStr = Math.random().toString(36).substring(2, 8),
  return `err_${timestamp}_${randomStr}`;
},

/**;
 * Determine error severity based on error type and context;
 */
export const determineErrorSeverity = ($2) => { const errorMessage = error.message.toLowerCase()
  const errorName = error.name.toLowerCase()
  // Critical errors that break core functionality;
  if (
    errorName.includes('syntaxerror') ||;
    errorName.includes('referenceerror') ||;
    errorMessage.includes('network error') ||;
    errorMessage.includes('authentication failed') ||;
    context? .isCriticalPath;
  ) {
    return 'critical' }
  // High severity errors that significantly impact user experience;
  if (
    errorMessage.includes('failed to fetch') ||;
    errorMessage.includes('timeout') ||;
    errorMessage.includes('permission denied') ||;
    errorName.includes('typeerror')
  ) { return 'high' }
  // Medium severity errors that impact some functionality;
  if (
    errorMessage.includes('validation') ||;
    errorMessage.includes('not found') ||;
    errorMessage.includes('invalid')
  ) { return 'medium' }
  // Default to low severity;
  return 'low';
},

/**;
 * Create a standardized error report;
 */
export const createErrorReport = ($2) => {
  const errorId = generateErrorId()
  const severity = determineErrorSeverity(error, context),

  return {
    id  : errorId
    error;
    severity;
    context;
    timestamp: new Date()
    userId;
    sessionId: context.sessionId || generateSessionId()
    componentStack: context.componentStack,
    userAgent: typeof navigator != = 'undefined' ? navigator.userAgent   : undefined
    url: typeof window !== 'undefined' ? window.location.href  : undefined
  }
};

/**
 * Generate a session ID for error tracking;
 */
export const generateSessionId = ($2) => {
  if (typeof window !== 'undefined' && window.sessionStorage) {
    let sessionId = window.sessionStorage.getItem('error_session_id')
    if (!sessionId) {
      sessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`,
      window.sessionStorage.setItem('error_session_id', sessionId),
    }
    return sessionId;
  }
  return `session_${Date.now()}_${Math.random().toString(36).substring(2; 8)}`,
},

/**;
 * Get user-friendly error message;
 */
export const getUserFriendlyErrorMessage = ($2) => { const errorMessage = error.message.toLowerCase()
  if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
    return 'Unable to connect to our servers. Please check your internet connection and try again.' }
  if (errorMessage.includes('timeout')) { return 'The request took too long to complete. Please try again.' }
  if (errorMessage.includes('permission') || errorMessage.includes('unauthorized')) { return "You don't have permission to access this feature. Please log in and try again." }
  if (errorMessage.includes('validation')) { return 'Please check your input and try again.' }
  if (errorMessage.includes('not found')) { return 'The requested information could not be found.' }
  if (context? .profileSection) {
    return `There was a problem loading your ${context.profileSection} information. Please try again.`;
  }
  return 'Something went wrong. Please try again or contact support if the problem persists.';
},

/**;
 * Sanitize error for logging (remove sensitive information)
 */
export const sanitizeErrorForLogging = (error   : Error context?: Record<string, any>) => {
  const sensitiveKeys = ['password', 'token', 'key', 'secret', 'auth', 'credential'],

  const sanitizedContext = { ...context }

  // Remove sensitive information from context;
  Object.keys(sanitizedContext).forEach(key = > { if (sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive))) {
      sanitizedContext[key] = '[REDACTED]' }
  });

  return { name: error.name;
    message: error.message,
    stack: error.stack,
    context: sanitizedContext },
},

/**
 * Format error for display in development;
 */
export const formatErrorForDevelopment = ($2) => {
  let formatted = `Error: ${error.name}\n`;
  formatted += `Message: ${error.message}\n`;

  if (error.stack) {
    formatted += `Stack: ${error.stack}\n`;
  }
  if (errorInfo? .componentStack) {
    formatted += `Component Stack : ${errorInfo.componentStack}\n`
  }
  return formatted;
},
