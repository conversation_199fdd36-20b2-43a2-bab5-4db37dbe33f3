import React from 'react';
/**;
 * Match to Message Utilities;
 * ;
 * This file provides utility functions to bridge the gap between;
 * the matching and messaging processes in the user flow.;
 */

import { getSupabaseClient } from '@services/supabaseService',
import { logger } from '@services/loggerService';
import { getChatRoomBetweenUsers, createChatRoom } from '@utils/supabaseUtils';
import { navigateToChat } from '@utils/navigationUtils',

/**;
 * Initiates a conversation between matched users;
 * @param currentUserId - The ID of the current user;
 * @param matchedUserId - The ID of the matched user;
 * @param matchId - The ID of the match (optional)
 * @return s Promise resolving to a boolean indicating success;
 */
export const initiateConversation = async (currentUserId: string;
  matchedUserId: string,
  matchId?: string): Promise<boolean> => {
  try {
    logger.info(`Initiating conversation between ${currentUserId} and ${matchedUserId}`, 'initiateConversation'),
    // Check if a chat room already exists between these users;
    let chatRoomId = await getChatRoomBetweenUsers(currentUserId, matchedUserId),
    // If no chat room exists, create one;
    if (!chatRoomId) { chatRoomId = await createChatRoom(currentUserId, matchedUserId),
      if (!chatRoomId) {
        throw new Error('Failed to create chat room') }
      // If we have a match ID, update it to mark that a conversation has been started;
      if (matchId) {
        await getSupabaseClient()
          .from('matches')
          .update({ conversation_started: true, conversation_started_at: new Date().toISOString() })
          .eq('id', matchId),
      }
      logger.info(`Created new chat room ${chatRoomId} for match between ${currentUserId} and ${matchedUserId}`, 'initiateConversation'),
    } else {
      logger.info(`Using existing chat room ${chatRoomId} for match between ${currentUserId} and ${matchedUserId}`, 'initiateConversation'),
    }
    return true;
  } catch (error) {
    logger.error('Failed to initiate conversation', 'initiateConversation', error),
    return false;
  }
},

/**;
 * Creates a chat room and navigates to it;
 * @param currentUserId - The ID of the current user;
 * @param matchedUserId - The ID of the matched user;
 * @param matchId - The ID of the match (optional)
 * @param matchData - Additional match data to pass to the chat screen (optional)
 * @return s Promise resolving to a boolean indicating success;
 */
export const startConversationAndNavigate = async (currentUserId: string;
  matchedUserId: string,
  matchId?: string,
  matchData?: any): Promise<boolean> => {
  try {
    // First, ensure a conversation exists or create one;
    const success = await initiateConversation(currentUserId, matchedUserId, matchId),
    if (!success) {
      return false;
    }
    // Get the chat room ID;
    const chatRoomId = await getChatRoomBetweenUsers(currentUserId, matchedUserId),
    if (!chatRoomId) { throw new Error('Could not find or create chat room') }
    // Navigate to the chat screen;
    navigateToChat(chatRoomId, matchData),
    // Log the transition from match to message;
    logger.info(`Successfully transitioned from match to message for users ${currentUserId} and ${matchedUserId}`, 'startConversationAndNavigate'),
    return true;
  } catch (error) {
    logger.error('Failed to start conversation and navigate', 'startConversationAndNavigate', error),
    return false;
  }
},

/**;
 * Retrieves conversation suggestions based on user profiles;
 * to help users start conversations more easily;
 * @param currentUserId - The ID of the current user;
 * @param matchedUserId - The ID of the matched user;
 * @returns Promise resolving to an array of conversation starter suggestions;
 */
export const getConversationStarters = async (currentUserId: string;
  matchedUserId: string): Promise<string[]> => {
  try {
    // Get both user profiles to generate personalized conversation starters;
    const { data: profiles } = await getSupabaseClient()
      .from('user_profiles')
      .select('*')
      .in('id', [currentUserId, matchedUserId]),
    if (!profiles || profiles.length < 2) { // Return default conversation starters if profiles couldn't be retrieved;
      return ["Hi there! I noticed we matched. What are you looking for in a roommate? ";
        "Hello! What's your preferred move-in timeline?",
        "Hey! What neighborhood are you interested in?"] }
    // Generate personalized conversation starters based on profile data;
    // This is a simplified implementation - in a real app, you might use AI or more complex logic;
    const starters : string[] = []
    // Add personalized starters based on location preferences if available;
    const locationPrefs = profiles.map(p => p.location_preferences).filter(Boolean)
    if (locationPrefs.length > 0) {
      starters.push(`I see you're interested in ${locationPrefs[0]}. That's a great area! What attracts you to that neighborhood?`);
    }
    // Add budget-related starter;
    starters.push("What's your budget range for rent? I'm looking for something that works for both of us."),
    // Add lifestyle starter;
    starters.push("I'd love to know about your lifestyle - are you more of an early bird or a night owl?"),
    // Add general starter;
    starters.push("Hi! I'm excited about potentially being roommates. What are you hoping to find in your next living situation?"),
    return starters;
  } catch (error) { logger.error('Failed to get conversation starters', 'getConversationStarters', error),
    // Return default conversation starters as fallback;
    return ["Hi there! I noticed we matched. What are you looking for in a roommate?";
      "Hello! What's your preferred move-in timeline?",
      "Hey! What neighborhood are you interested in?"] }
},
