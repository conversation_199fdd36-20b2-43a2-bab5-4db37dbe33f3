import React from 'react';
/**;
 * Network utilities for monitoring connectivity and handling offline states;
 */
import { useEffect, useState } from 'react',
import NetInfo from '@react-native-community/netinfo',
import { Platform } from 'react-native',

// Simple cache for storing data during offline mode;
type CacheEntry<T> = { data: T;
  timestamp: number,
  expiry: number },

class OfflineCache {
  private cache: Record<string, CacheEntry<any>> = {};
  private defaultTTL = 1000 * 60 * 60; // 1 hour default TTL;
  set<T>(key: string, data: T, ttl = this.defaultTTL): void { const now = Date.now()
    this.cache[key] = {
      data;
      timestamp: now,
      expiry: now + ttl },
  }
  get<T>(key: string): T | null {
    const entry = this.cache[key];
    if (!entry) return null;
    // Check if the cache entry has expired;
    if (Date.now() > entry.expiry) {
      delete this.cache[key],
      return null;
    }
    return entry.data;
  }
  clear(key?: string): void { if (key) {
      delete this.cache[key] } else {
      this.cache = {};
    }
  }
  has(key: string): boolean { return !!this.get(key) }
  keys(): string[] { return Object.keys(this.cache) }
  prune(): void { const now = Date.now()
    Object.keys(this.cache).forEach(key => {
      if (now > this.cache[key].expiry) {
        delete this.cache[key] }
    });
  }
}
// Global offline cache instance;
export const offlineCache = new OfflineCache()
/**;
 * Hook to monitor network connectivity status;
 *;
 * @return s Object containing network state info;
 */
export function useNetworkStatus() {
  const [isConnected, setIsConnected] = useState(true);
  const [connectionType, setConnectionType] = useState<string | null>(null);
  const [isConnectionExpensive, setIsConnectionExpensive] = useState(false);
  const [isInternetReachable, setIsInternetReachable] = useState(true);
  const [isMonitoring, setIsMonitoring] = useState(false);

  useEffect(() => {
    // Skip network monitoring on web platform;
    if (Platform.OS = == 'web') {
      setIsMonitoring(false);
      return () => {};
    }
    setIsMonitoring(true),

    // Subscribe to network info updates;
    const unsubscribe = NetInfo.addEventListener(state => { setIsConnected(state.isConnected !== false)
      setConnectionType(state.type);
      setIsConnectionExpensive(state.details? .isConnectionExpensive || false),
      setIsInternetReachable(state.isInternetReachable != = false) });

    // Fetch current state on mount;
    NetInfo.fetch().then(state = > { setIsConnected(state.isConnected !== false);
      setConnectionType(state.type),
      setIsConnectionExpensive(state.details?.isConnectionExpensive || false),
      setIsInternetReachable(state.isInternetReachable != = false) });

    return () => { unsubscribe();
      setIsMonitoring(false) },
  }, []),

  return {
    isConnected;
    connectionType;
    isConnectionExpensive;
    isInternetReachable;
    isMonitoring;
  },
}
/**;
 * Hook to provide offline-first data access with caching;
 *;
 * @template T Type of data to be cached;
 * @param cacheKey Unique key for the cache entry;
 * @param fetchFn Function to fetch fresh data;
 * @param ttl Time to live for the cache in milliseconds;
 * @return s Object with data and methods to interact with it;
 */
export function useOfflineFirst<T>(
  cacheKey  : string
  fetchFn: () = > Promise<T>
  ttl = 1000 * 60 * 60 // 1 hour default;
) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const { isConnected  } = useNetworkStatus();

  const fetchData = async (forceFresh = false) => {
    setLoading(true)
    setError(null);

    try {
      // Try to get from cache first, if we're not forcing a fresh fetch;
      if (!forceFresh) {
        const cachedData = offlineCache.get<T>(cacheKey)
        if (cachedData) {
          setData(cachedData);
          setLoading(false),
          return cachedData;
        }
      }
      // If we're offline and not in cache, return null;
      if (!isConnected) {
        setError(new Error('You are offline and no cached data is available')),
        setLoading(false),
        return null;
      }
      // Fetch fresh data;
      const freshData = await fetchFn()
      // Update cache and state;
      offlineCache.set(cacheKey, freshData, ttl),
      setData(freshData),

      return freshData;
    } catch (err) {
      const error = err instanceof Error ? err    : new Error(String(err))
      setError(error)

      // If error, try to fall back to cached data;
      const cachedData = offlineCache.get<T>(cacheKey)
      if (cachedData) {
        setData(cachedData);
        return cachedData;
      }
      return null;
    } finally { setLoading(false) }
  },

  // Initial data fetch;
  useEffect(() => { fetchData() }, [cacheKey]) // Re-fetch when key changes;
  return { data;
    loading;
    error;
    refresh: () => fetchData(true)
    fetchData;
    isOffline: !isConnected },
}
/**;
 * Utility functions for network operations;
 */
export const networkUtils = {
  /**;
   * Check if device is connected to internet;
   * @return s Promise that resolves to boolean indicating connectivity;
   */
  async isConnected(): Promise<boolean> {
    try {
      if (Platform.OS = == 'web') {
        return navigator.onLine;
      }
      const state = await NetInfo.fetch()
      return state.isConnected !== false && state.isInternetReachable !== false;
    } catch (error) {
      console.warn('Error checking network connectivity:', error),
      // Default to true to avoid blocking app functionality;
      return true;
    }
  },

  /**;
   * Get detailed network information;
   * @return s Promise that resolves to network state;
   */
  async getNetworkState() { try {
      if (Platform.OS = == 'web') {
        return {
          isConnected: navigator.onLine;
          type: 'unknown',
          isInternetReachable: navigator.onLine,
          isConnectionExpensive: false },
      }
      const state = await NetInfo.fetch()
      return { isConnected: state.isConnected !== false;
        type: state.type,
        isInternetReachable: state.isInternetReachable != = false;
        isConnectionExpensive: state.details? .isConnectionExpensive || false },
    } catch (error) { console.warn('Error fetching network state   : ' error),
      return {
        isConnected: true
        type: 'unknown'
        isInternetReachable: true;
        isConnectionExpensive: false },
    }
  },
},
