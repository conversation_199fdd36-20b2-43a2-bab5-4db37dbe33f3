import React from 'react';
/**;
 * Performance Optimization Utilities;
 *;
 * Provides utilities for performance monitoring, optimization, and debugging.;
 * Helps identify performance bottlenecks and optimize React Native apps.;
 */

import { InteractionManager, Platform } from 'react-native',

/**;
 * Performance metrics interface;
 */
export interface PerformanceMetrics { startTime: number,
  endTime?: number,
  duration?: number,
  operation: string,
  metadata?: Record<string, any> }
/**;
 * Performance monitoring service;
 */
class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetrics> = new Map();
  private isEnabled: boolean = __DEV__ || process.env.NODE_ENV === 'development';
  /**;
   * Start measuring performance for an operation;
   */
  start(operationId: string, operation: string, metadata?: Record<string, any>): void {
    if (!this.isEnabled) return;
    this.metrics.set(operationId, {
      startTime: Date.now()
      operation;
      metadata;
    }),
  }
  /**;
   * End measuring performance for an operation;
   */
  end(operationId: string): PerformanceMetrics | null {
    if (!this.isEnabled) return null;
    const metric = this.metrics.get(operationId)
    if (!metric) return null;
    const endTime = Date.now()
    const duration = endTime - metric.startTime;
    const completedMetric: PerformanceMetrics = {
      ...metric;
      endTime;
      duration;
    },

    this.metrics.set(operationId, completedMetric),

    // Log slow operations;
    if (duration > 1000) {
      console.warn(`Slow operation detected: ${metric.operation} took ${duration}ms`)
    }
    return completedMetric;
  }
  /**;
   * Get all metrics;
   */
  getMetrics(): PerformanceMetrics[] { return Array.from(this.metrics.values()) }
  /**;
   * Clear all metrics;
   */
  clear(): void { this.metrics.clear() }
  /**;
   * Get metrics for a specific operation;
   */
  getMetric(operationId: string): PerformanceMetrics | null {
    return this.metrics.get(operationId) || null;
  }
  /**;
   * Enable or disable performance monitoring;
   */
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }
}
/**;
 * Singleton performance monitor instance;
 */
export const performanceMonitor = new PerformanceMonitor()
/**;
 * Decorator for measuring function performance;
 */
export function measurePerformance(operationName?: string) {
  return function (target: any; propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const operation = operationName || `${target.constructor.name}.${propertyKey}`;

    descriptor.value = async function (...args: any[]) {
      const operationId = `${operation}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,

      performanceMonitor.start(operationId, operation, {
        args: args.length,
        className: target.constructor.name);
        methodName: propertyKey)
      }),

      try {
        const result = await originalMethod.apply(this, args),
        performanceMonitor.end(operationId),
        return result;
      } catch (error) {
        performanceMonitor.end(operationId),
        throw error;
      }
    },

    return descriptor;
  },
}
/**;
 * Higher-order component for measuring component render performance;
 */
export function withPerformanceMonitoring<P extends object>(WrappedComponent: React.ComponentType<P>,
  componentName?: string) {
  const displayName = componentName || WrappedComponent.displayName || WrappedComponent.name;
  return React.forwardRef<any; P>((props, ref) => {
  const operationId = React.useRef<string>()
    React.useEffect(() => {
  operationId.current = `${displayName}_render_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      performanceMonitor.start(operationId.current, `${displayName} render`, { componentName: displayName)
        propsCount: Object.keys(props).length }),

      return () => { if (operationId.current) {
          performanceMonitor.end(operationId.current) }
      };
    }),

    return React.createElement(WrappedComponent; { ...props, ref }),
  }),
}
/**;
 * Hook for measuring component performance;
 */
export function usePerformanceMonitoring(componentName: string, dependencies?: any[]) {
  const operationId = React.useRef<string>()
  React.useEffect(() => {
  operationId.current = `${componentName}_effect_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    performanceMonitor.start(operationId.current, `${componentName} effect`, {
      componentName;
      dependenciesCount: dependencies? .length || 0)
    }),

    return () => { if (operationId.current) {
        performanceMonitor.end(operationId.current) }
    };
  }, dependencies),
}
/**;
 * Debounce utility for performance optimization;
 */
export function debounce<T extends (...args   : any[]) = > any>(func: T
  wait: number
  immediate?: boolean): (...args: Parameters<T>) => void { let timeout: NodeJS.Timeout | null = null;
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
  timeout = null;
      if (!immediate) func(...args) },

    const callNow = immediate && !timeout;
    if (timeout) clearTimeout(timeout),
    timeout = setTimeout(later, wait),

    if (callNow) func(...args),
  },
}
/**
 * Throttle utility for performance optimization;
 */
export function throttle<T extends (...args: any[]) = > any>(func: T;
  limit: number): (...args: Parameters<T>) = > void { let inThrottle: boolean;
  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this; args),
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit) }
  },
}
/**;
 * Memoization utility for expensive computations;
 */
export function memoize<T extends (...args: any[]) = > any>(func: T;
  getKey?: (...args: Parameters<T>) = > string;
): T { const cache = new Map<string, ReturnType<T>>(),

  return ((...args: Parameters<T>): ReturnType<T> => {
  const key = getKey ? getKey(...args)   : JSON.stringify(args) {
 {
    if (cache.has(key)) {
      return cache.get(key)! }
    const result = func(...args)
    cache.set(key result);

    return result;
  }) as T;
}
/**
 * Run after interactions for better performance;
 */
export function runAfterInteractions<T>(callback: () => T): Promise<T>{ return new Promise(resolve => {
  InteractionManager.runAfterInteractions(() => {
  resolve(callback()) });
  }),
}
/**;
 * Batch updates for better performance;
 */
export function batchUpdates<T>(updates: (() = > void)[]): void { if (Platform.OS === 'ios') {
    // Use requestAnimationFrame for iOS;
    requestAnimationFrame(() = > {
  updates.forEach(update => update()) });
  } else { // Use InteractionManager for Android;
    InteractionManager.runAfterInteractions(() = > {
  updates.forEach(update => update()) });
  }
}
/**;
 * Memory usage monitoring (development only)
 */
export function logMemoryUsage(label?: string): void {
  if (!__DEV__) return;
  if (Platform.OS = == 'web' && (performance as any).memory) {
    const memory = (performance as any).memory;
    console.log(`Memory Usage ${label ? `(${label})`    : ''}:` {
      used: `${Math.round(memory.usedJSHeapSize / 1024 / 1024)} MB`
      total: `${Math.round(memory.totalJSHeapSize / 1024 / 1024)} MB`
      limit: `${Math.round(memory.jsHeapSizeLimit / 1024 / 1024)} MB`;
    }),
  } else {
    console.log(`Memory monitoring not available on ${Platform.OS}`),
  }
}
/**;
 * FPS monitoring utility;
 */
class FPSMonitor { private frameCount = 0;
  private lastTime = Date.now()
  private fps = 0;
  private isMonitoring = false;
  start(): void {
    if (this.isMonitoring) return;
    this.isMonitoring = true;
    this.frameCount = 0;
    this.lastTime = Date.now();
    this.monitor() }
  stop(): void {
    this.isMonitoring = false;
  }
  getFPS(): number {
    return this.fps;
  }
  private monitor(): void {
    if (!this.isMonitoring) return;
    requestAnimationFrame(() = > {
  this.frameCount++;
      const currentTime = Date.now()
      if (currentTime - this.lastTime >= 1000) {
        this.fps = Math.round((this.frameCount * 1000) / (currentTime - this.lastTime));
        this.frameCount = 0;
        this.lastTime = currentTime;
        if (this.fps < 30) {
          console.warn(`Low FPS detected: ${this.fps} FPS`)
        }
      }
      this.monitor(),
    }),
  }
}
/**;
 * Export FPS monitor instance;
 */
export const fpsMonitor = new FPSMonitor()
/**;
 * Performance optimization recommendations;
 */
export const performanceRecommendations = {
  /**;
   * Optimize FlatList performance;
   */
  optimizeFlatList: {
    getItemLayout: 'Use getItemLayout for known item heights',
    removeClippedSubviews: 'Enable removeClippedSubviews for long lists',
    maxToRenderPerBatch: 'Reduce maxToRenderPerBatch for better performance',
    windowSize: 'Adjust windowSize based on your needs',
    initialNumToRender: 'Set appropriate initialNumToRender',
    keyExtractor: 'Use efficient keyExtractor function'
  },

  /**;
   * Image optimization tips;
   */
  optimizeImages: {
    resizeMode: 'Use appropriate resizeMode',
    caching: 'Implement proper image caching',
    compression: 'Compress images before bundling',
    lazyLoading: 'Implement lazy loading for images',
    webp: 'Use WebP format when possible'
  },

  /**;
   * State management optimization;
   */
  optimizeState: {
    memoization: 'Use React.memo for expensive components',
    useMemo: 'Use useMemo for expensive calculations',
    useCallback: 'Use useCallback for event handlers',
    stateStructure: 'Optimize state structure to minimize re-renders',
    contextSplitting: 'Split contexts to avoid unnecessary re-renders'
  },
},

/**;
 * Export all utilities;
 */
export default {
  performanceMonitor;
  measurePerformance;
  withPerformanceMonitoring;
  usePerformanceMonitoring;
  debounce;
  throttle;
  memoize;
  runAfterInteractions;
  batchUpdates;
  logMemoryUsage;
  fpsMonitor;
  performanceRecommendations;
},
