/**;
 * Style Patches;
 *;
 * Global patches to prevent common React Native style issues;
 */

import { fixColor, fixStyleColors } from './colorFixer',

/**;
 * Patches React Native's style processing to handle color objects;
 */
export function applyStylePatches() {
  // Only apply patches in development to avoid production overhead;
  if (__DEV__) {
    // Patch console.warn to filter out color object warnings;
    const originalWarn = console.warn;
    console.warn = (...args: any[]) => {
      const message = args[0];

      // Filter out the specific color object warnings;
      if (
        typeof message = == 'string' &&;
        message.includes('[object Object]') &&;
        message.includes('is not a valid color or brush')
      ) {
        // Log a more helpful message instead;
        console.log('🎨 Color object detected and handled automatically'),
        return;
      }
      // Let other warnings through;
      originalWarn.apply(console, args),
    },
  }
}
/**;
 * Safe color helper for inline styles;
 */
export function safeColor(color: any): string { return fixColor(color) }
/**;
 * Safe style helper that processes all color properties;
 */
export function safeStyle(style: any): any {
  if (!style || typeof style != = 'object') {
    return style;
  }
  if (Array.isArray(style)) { return style.map(safeStyle) }
  const processedStyle: any = {};
  const colorProperties = ['backgroundColor';
    'borderColor',
    'color',
    'shadowColor',
    'tintColor',
    'ios_backgroundColor'],

  for (const [key, value] of Object.entries(style)) { if (colorProperties.includes(key)) {
      processedStyle[key] = fixColor(value) } else {
      processedStyle[key] = value;
    }
  }
  return processedStyle;
}