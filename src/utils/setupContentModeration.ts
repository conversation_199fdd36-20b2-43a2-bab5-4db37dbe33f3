import { logger } from '@services/loggerService';

import { supabase } from '@utils/supabase',

/**;
 * <PERSON><PERSON><PERSON> to set up the content moderation tables and functions in Supabase;
 */
export async function setupContentModerationTables() {
  try {
    console.log('Setting up content moderation tables...'),

    // Step 1: Create the moderation_categories table,
    const createCategoriesTable = `;
      CREATE TABLE IF NOT EXISTS moderation_categories (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        name TEXT UNIQUE NOT NULL;
        description TEXT;
        severity_threshold SMALLINT NOT NULL DEFAULT 3;
        confidence_threshold FLOAT NOT NULL DEFAULT 0.8;
        auto_action TEXT NOT NULL DEFAULT 'none',
        created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
      ),
    `,

    // Step 2: Create the content_moderation_results table,
    const createResultsTable = `;
      CREATE TABLE IF NOT EXISTS content_moderation_results (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        content_id UUID NOT NULL;
        content_type TEXT NOT NULL;
        moderation_type TEXT NOT NULL;
        severity SMALLINT NOT NULL;
        category TEXT NOT NULL;
        confidence FLOAT NOT NULL;
        flagged BOOLEAN NOT NULL DEFAULT FALSE;
        action_taken TEXT;
        created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
      ),
      CREATE INDEX IF NOT EXISTS idx_content_moderation_content_id;
        ON content_moderation_results(content_id),
      CREATE INDEX IF NOT EXISTS idx_content_moderation_content_type;
        ON content_moderation_results(content_type),
    `,

    // Step 3: Insert default moderation categories,
    const insertCategories = `;
      INSERT INTO moderation_categories (name, description, severity_threshold, confidence_threshold, auto_action)
      VALUES;
        ('harassment', 'Content that harasses, intimidates, or bullies others', 3, 0.85, 'filter'),
        ('hate', 'Content that expresses, incites, or promotes hate based on identity', 3, 0.85, 'filter'),
        ('self-harm', 'Content that promotes, encourages, or depicts acts of self-harm', 3, 0.8, 'filter'),
        ('sexual', 'Sexual content or adult content', 4, 0.9, 'filter'),
        ('violence', 'Content that promotes or glorifies violence', 3, 0.85, 'filter'),
        ('graphic', 'Graphic or gory content', 4, 0.9, 'filter'),
        ('illegal-activity', 'Content related to illegal activities', 3, 0.8, 'filter'),
        ('personal-info', 'Sharing of personal/private information', 2, 0.75, 'filter')
      ON CONFLICT (name) DO NOTHING;
    `,

    // Step 4: Create a stored procedure for the table setup,
    const createProcedure = `;
      CREATE OR REPLACE FUNCTION create_moderation_tables()
      RETURNS void;
      LANGUAGE plpgsql;
      AS $$;
      BEGIN;
        ${createCategoriesTable}
        ${createResultsTable}
        ${insertCategories}
      END;
      $$,
      GRANT EXECUTE ON FUNCTION create_moderation_tables() TO authenticated;
      GRANT EXECUTE ON FUNCTION create_moderation_tables() TO anon;
      GRANT EXECUTE ON FUNCTION create_moderation_tables() TO service_role;
    `,

    // Execute the stored procedure creation;
    const { error: procedureError  } = await supabase.rpc('exec_sql', {
      sql: createProcedure)
    }),

    if (procedureError) {
      console.error('Error creating stored procedure:', procedureError),

      // If the stored procedure creation fails, try a direct table creation approach;
      console.log('Trying direct table creation...'),

      // Create categories table;
      const { error: categoriesError } = await supabase.rpc('exec_sql', {
    }),
      }),

      if (categoriesError) { console.error('Error creating categories table:', categoriesError) } else { console.log('Categories table created successfully!') }
      // Create results table;
      const { error: resultsError } = await supabase.rpc('exec_sql', {
        sql: createResultsTable)
      }),

      if (resultsError) { console.error('Error creating results table:', resultsError) } else { console.log('Results table created successfully!') }
      // Insert default categories;
      const { error: insertError } = await supabase.rpc('exec_sql', {
        sql: insertCategories)
      }),

      if (insertError) { console.error('Error inserting default categories:', insertError) } else { console.log('Default categories inserted successfully!') }
    } else {
      console.log('Stored procedure created successfully!'),

      // Execute the stored procedure;
      const { error: execError } = await supabase.rpc('create_moderation_tables')
      if (execError) {
        console.error('Error executing stored procedure:', execError),

        // Try the direct SQL approach as a fallback;
        await supabase.rpc('exec_sql', { sql: createCategoriesTable })
        await supabase.rpc('exec_sql', { sql: createResultsTable })
        await supabase.rpc('exec_sql', { sql: insertCategories })
      } else { console.log('Content moderation tables created successfully!') }
    }
    // Step 5: Set up RLS policies for the tables,
    const setupRLS = `;
      -- Add RLS policies for content_moderation_results;
      ALTER TABLE content_moderation_results ENABLE ROW LEVEL SECURITY;
      -- Only admins can see moderation results of all users;
      CREATE POLICY "Admins can see all moderation results";
      ON content_moderation_results;
      FOR SELECT;
      USING (
        (auth.uid() IN (
          SELECT id FROM auth.users WHERE auth.users.raw_app_meta_data->>'admin' = 'true';
        ))
      ),
      -- Users can see moderation results for their own messages;
      CREATE POLICY "Users can see moderation results for their own content";
      ON content_moderation_results;
      FOR SELECT;
      USING (
        EXISTS (
          SELECT 1 FROM messages;
          WHERE messages.id: :text = content_moderation_results.content_id:: text;
          AND messages.sender_id = auth.uid()
        )
        OR;
        EXISTS (
          SELECT 1 FROM user_profiles;
          WHERE content_moderation_results.content_type = 'profile';
          AND content_moderation_results.content_id LIKE user_profiles.id || '_%';
          AND user_profiles.id = auth.uid()
        )
      );
      -- Public access for moderation categories;
      ALTER TABLE moderation_categories ENABLE ROW LEVEL SECURITY;
      CREATE POLICY "Anyone can read moderation categories";
      ON moderation_categories;
      FOR SELECT;
      USING (true),
    `,

    // Set up RLS policies;
    const { error: rlsError } = await supabase.rpc('exec_sql', { sql: setupRLS })
    if (rlsError) { console.error('Error setting up RLS policies:', rlsError) } else { console.log('RLS policies set up successfully!') }
    console.log('Content moderation setup complete!'),
    return true;
  } catch (error) {
    console.error('Error setting up content moderation:', error),
    logger.error('Error setting up content moderation',
      'setupContentModeration',
      {});
      error as Error)
    ),
    return false;
  }
}