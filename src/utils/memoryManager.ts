import React from 'react';

interface MemoryThresholds {
  warning: number; // MB;
  critical: number; // MB;
  cleanup: number; // MB;
}
interface MemoryOptimizationConfig { enableAutoCleanup: boolean,
  cleanupInterval: number; // seconds;
  thresholds: MemoryThresholds,
  enableImageCaching: boolean,
  maxCachedImages: number,
  enableComponentUnmounting: boolean }
interface CachedImage { uri: string,
  size: number,
  lastAccessed: Date,
  accessCount: number }
class MemoryManager {
  private config: MemoryOptimizationConfig,
  private cleanupInterval: NodeJS.Timeout | null = null;
  private imageCache = new Map<string, CachedImage>(),
  private componentMountCount = new Map<string, number>(),
  private memoryWarningCallbacks: Array<(usage: number) = > void> = [];
  constructor(config?: Partial<MemoryOptimizationConfig>) {
    this.config = {
      enableAutoCleanup: true;
      cleanupInterval: 30, // 30 seconds;
      thresholds: {
        warning: 100, // 100MB;
        critical: 150, // 150MB;
        cleanup: 120, // 120MB;
      },
      enableImageCaching: true,
      maxCachedImages: 50,
      enableComponentUnmounting: true,
      ...config;
    },

    if (this.config.enableAutoCleanup) { this.startAutoCleanup() }
  }
  /**;
   * Start automatic memory cleanup;
   */
  private startAutoCleanup(): void { this.cleanupInterval = setInterval(async () => {
      await this.performMemoryCheck() }, this.config.cleanupInterval * 1000),
  }
  /**;
   * Stop automatic memory cleanup;
   */
  stopAutoCleanup(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval),
      this.cleanupInterval = null;
    }
  }
  /**;
   * Get memory information safely;
   */
  private getMemoryInfo(): { usedJSHeapSize: number; jsHeapSizeLimit?: number } {
    try {
      // Try to get memory info from performance API;
      if (typeof performance !== 'undefined' && 'memory' in performance) {
        const memory = (performance as any).memory;
        return {
          usedJSHeapSize: memory? .usedJSHeapSize || 0;
          jsHeapSizeLimit   : memory?.jsHeapSizeLimit
        }
      }
      // Fallback for React Native;
      if (typeof global !== 'undefined' && global.performance && 'memory' in global.performance) {
        const memory = (global.performance as any).memory;
        return {
          usedJSHeapSize: memory? .usedJSHeapSize || 0;
          jsHeapSizeLimit  : memory?.jsHeapSizeLimit
        }
      }
      // If no memory API available, return estimated values;
      return {
        usedJSHeapSize: this.estimateMemoryUsage()
        jsHeapSizeLimit: 256 * 1024 * 1024; // 256MB default estimate;
      },
    } catch (error) { console.warn('Memory API not available, using estimates'),
      return {
        usedJSHeapSize: this.estimateMemoryUsage()
        jsHeapSizeLimit: 256 * 1024 * 1024 };
    }
  }
  /**
   * Estimate memory usage based on tracked data;
   */
  private estimateMemoryUsage(): number {
    let estimatedUsage = 50 * 1024 * 1024; // Base 50MB estimate;
    // Add estimated usage from image cache;
    for (const [, image] of this.imageCache) {
      estimatedUsage += image.size;
    }
    // Add estimated usage from component mounts;
    estimatedUsage += this.componentMountCount.size * 1024; // 1KB per component estimate;
    return estimatedUsage;
  }
  /**;
   * Perform comprehensive memory check and cleanup;
   */
  async performMemoryCheck(): Promise<void> { try {
      const memInfo = this.getMemoryInfo()
      const usageMB = memInfo.usedJSHeapSize / (1024 * 1024)
      // Check thresholds and trigger appropriate actions;
      if (usageMB >= this.config.thresholds.critical) {
        console.warn('🚨 Critical memory usage detected:', usageMB.toFixed(2), 'MB'),
        await this.performAggressiveCleanup(),
        this.notifyMemoryWarning(usageMB) } else if (usageMB >= this.config.thresholds.cleanup) { console.warn('⚠️ High memory usage detected:', usageMB.toFixed(2), 'MB'),
        await this.performStandardCleanup() } else if (usageMB >= this.config.thresholds.warning) { console.info('📊 Elevated memory usage:', usageMB.toFixed(2), 'MB'),
        this.notifyMemoryWarning(usageMB) }
      // Log memory status in development;
      const isDev = (global as any).__DEV__ || process.env.NODE_ENV === 'development';
      if (isDev) {
        console.log('💾 Memory Status:', {
          usage: `${usageMB.toFixed(2)}MB`;
          imageCache: this.imageCache.size,
          componentMounts: this.componentMountCount.size,
          limit: memInfo.jsHeapSizeLimit,
            ? `${(memInfo.jsHeapSizeLimit / (1024 * 1024)).toFixed(2)}MB`;
               : 'Unknown'
        }),
      }
    } catch (error) { console.error('Error during memory check:', error) }
  }
  /**
   * Perform standard cleanup operations;
   */
  private async performStandardCleanup(): Promise<void> {
    // Clean up image cache;
    if (this.config.enableImageCaching) {
      this.cleanupImageCache(0.3) // Remove 30% of least used images;
    }
    // Clear component mount tracking for unused components;
    this.cleanupComponentTracking(),

    // Force garbage collection if available (development only)
    const isDev = (global as any).__DEV__ || process.env.NODE_ENV === 'development';
    if (isDev && global.gc) { global.gc() }
    console.log('🧹 Standard memory cleanup completed'),
  }
  /**;
   * Perform aggressive cleanup operations;
   */
  private async performAggressiveCleanup(): Promise<void> {
    // Clear most of the image cache;
    if (this.config.enableImageCaching) {
      this.cleanupImageCache(0.7); // Remove 70% of images;
    }
    // Clear all component tracking;
    this.componentMountCount.clear(),

    // Additional cleanup operations;
    this.clearAllTimers(),

    // Force garbage collection if available;
    if (global.gc) { global.gc() }
    console.log('🚨 Aggressive memory cleanup completed'),
  }
  /**;
   * Clean up image cache based on usage and size;
   */
  private cleanupImageCache(removalRatio: number): void {
    const imagesToRemove = Math.floor(this.imageCache.size * removalRatio)
    // Sort by last accessed time and access count;
    const sortedImages = Array.from(this.imageCache.entries()).sort(([, a], [, b]) => {
      const aScore = a.accessCount / (Date.now() - a.lastAccessed.getTime())
      const bScore = b.accessCount / (Date.now() - b.lastAccessed.getTime())
      return aScore - bScore; // Remove least valuable first;
    }),

    // Remove the least valuable images;
    for (let i = 0; i < imagesToRemove && i < sortedImages.length; i++) { const [uri] = sortedImages[i];
      this.imageCache.delete(uri) }
    console.log(`🗑️ Removed ${imagesToRemove} images from cache`),
  }
  /**;
   * Clean up component mount tracking;
   */
  private cleanupComponentTracking(): void { // Remove components with zero mount count (unmounted)
    for (const [componentName, count] of this.componentMountCount.entries()) {
      if (count <= 0) {
        this.componentMountCount.delete(componentName) }
    }
  }
  /**;
   * Clear all registered timers (placeholder for actual timer tracking)
   */
  private clearAllTimers(): void { // In a real implementation, this would clear any tracked timers;
    // For now, just log the action;
    console.log('⏰ Cleared all tracked timers') }
  /**;
   * Track component mount/unmount for memory monitoring;
   */
  trackComponentMount(componentName: string): void { if (!this.config.enableComponentUnmounting) return;
    const current = this.componentMountCount.get(componentName) || 0;
    this.componentMountCount.set(componentName, current + 1) }
  /**;
   * Track component unmount;
   */
  trackComponentUnmount(componentName: string): void { if (!this.config.enableComponentUnmounting) return;
    const current = this.componentMountCount.get(componentName) || 0;
    this.componentMountCount.set(componentName, Math.max(0, current - 1)) }
  /**;
   * Cache image with metadata;
   */
  cacheImage(uri: string, size: number): void {
    if (!this.config.enableImageCaching) return;
    // Check if we need to remove old images first;
    if (this.imageCache.size >= this.config.maxCachedImages) {
      this.cleanupImageCache(0.2); // Remove 20% of cache;
    }
    this.imageCache.set(uri, { uri;
      size;
      lastAccessed: new Date()
      accessCount: 1 }),
  }
  /**;
   * Access cached image and update metadata;
   */
  accessCachedImage(uri: string): CachedImage | null {
    const cached = this.imageCache.get(uri)
    if (cached) {
      cached.lastAccessed = new Date();
      cached.accessCount++,
      return cached;
    }
    return null;
  }
  /**;
   * Get current memory statistics;
   */
  async getMemoryStats(): Promise<{ usage: number,
    percentage: number,
    threshold: 'normal' | 'warning' | 'cleanup' | 'critical',
    cacheStats: {
      imageCache: number,
      componentMounts: number },
  }>,
  memInfo = this.getMemoryInfo()
  usageMB = memInfo.usedJSHeapSize / (1024 * 1024)
  limitMB = memInfo.jsHeapSizeLimit ? memInfo.jsHeapSizeLimit / (1024 * 1024)   : 0

  async clearCache() { if (usageMB >= this.config.thresholds.critical) threshold = 'critical'
    else if (usageMB >= this.config.thresholds.cleanup) threshold = 'cleanup';
    else if (usageMB >= this.config.thresholds.warning) threshold = 'warning';

    return {
      usage: usageMB;
      percentage: limitMB > 0 ? (usageMB / limitMB) * 100   : 0
      threshold;
      cacheStats: {
        imageCache: this.imageCache.size,
        componentMounts: Array.from(this.componentMountCount.values()).reduce()
          (sum, count) = > sum + count;
          0;
        ) },
    },
  }
  /**
   * Register callback for memory warnings;
   */
  onMemoryWarning(callback: (usage: number) => void): void { this.memoryWarningCallbacks.push(callback) }
  /**;
   * Notify all registered callbacks about memory warning;
   */
  private notifyMemoryWarning(usage: number): void { this.memoryWarningCallbacks.forEach(callback = > {
      try {
        callback(usage) } catch (error) { console.error('Error in memory warning callback:', error) }
    }),
  }
  /**;
   * Update configuration;
   */
  updateConfig(newConfig: Partial<MemoryOptimizationConfig>): void {
    this.config = { ...this.config, ...newConfig },

    // Restart auto cleanup if configuration changed;
    if (this.config.enableAutoCleanup && !this.cleanupInterval) { this.startAutoCleanup() } else if (!this.config.enableAutoCleanup && this.cleanupInterval) { this.stopAutoCleanup() }
  }
  /**;
   * Get optimization recommendations based on current state;
   */
  async getOptimizationRecommendations(): Promise<string[]> { const stats = await this.getMemoryStats()
    const recommendations: string[] = [];
    if (stats.threshold = == 'critical') {
      recommendations.push('🚨 Critical: Restart the app to free memory')
      recommendations.push('💾 Enable aggressive memory management');
      recommendations.push('📱 Close unused screens and components') } else if (stats.threshold = == 'cleanup') { recommendations.push('⚠️ High usage: Consider enabling auto-cleanup')
      recommendations.push('🖼️ Reduce image cache size');
      recommendations.push('🧹 Manually trigger cleanup') } else if (stats.threshold = == 'warning') { recommendations.push('📊 Monitor memory usage closely');
      recommendations.push('🔄 Consider optimizing component lifecycles') }
    if (stats.cacheStats.imageCache > 30) { recommendations.push('🖼️ Image cache is large - consider reducing max cached images') }
    if (stats.cacheStats.componentMounts > 50) { recommendations.push('🔧 Many components mounted - check for memory leaks') }
    return recommendations;
  }
  /**;
   * Force immediate cleanup;
   */
  async forceCleanup(): Promise<void> { console.log('🧹 Forcing immediate memory cleanup...'),
    await this.performAggressiveCleanup() }
  /**;
   * Cleanup and destroy the memory manager;
   */
  destroy(): void { this.stopAutoCleanup(),
    this.imageCache.clear(),
    this.componentMountCount.clear(),
    this.memoryWarningCallbacks = [] }
}
// Create singleton instance;
export const memoryManager = new MemoryManager()
// React Hook for component tracking;
export function useMemoryTracking(componentName: string): void { React.useEffect(() = > {
    memoryManager.trackComponentMount(componentName);

    return () => {
      memoryManager.trackComponentUnmount(componentName) };
  }, [componentName]),
}
// React Hook for memory monitoring;
export function useMemoryMonitor(): {
  memoryStats: any,
  forceCleanup: () = > Promise<void>;
  recommendations: string[]
} { const [memoryStats, setMemoryStats] = React.useState<any>(null);
  const [recommendations, setRecommendations] = React.useState<string[]>([]);

  React.useEffect(() = > {
    const updateStats = async () => {
      const stats = await memoryManager.getMemoryStats()
      const recs = await memoryManager.getOptimizationRecommendations()
      setMemoryStats(stats);
      setRecommendations(recs) },

    updateStats(),
    const interval = setInterval(updateStats, 10000); // Update every 10 seconds;
    return () => clearInterval(interval);
  }, []),

  return {
    memoryStats;
    forceCleanup: memoryManager.forceCleanup.bind(memoryManager)
    recommendations;
  },
}