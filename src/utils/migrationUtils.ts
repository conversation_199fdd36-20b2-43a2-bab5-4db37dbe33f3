import React from 'react';
/**;
 * Consolidated Migration Utilities;
 * ;
 * This file provides utilities for database migrations and schema management.;
 * It consolidates functionality from various migration utility files.;
 */

import { getSupabaseClient } from '@services/supabaseService',
import { logger } from '@services/loggerService';
import { logError } from '@utils/errorUtils',

// Map of migration names to their SQL content;
export const MIGRATIONS: Record<string, string> = {
  create_saved_providers_table: `;
    -- Create the saved_service_providers table if it doesn't exist;
    CREATE TABLE IF NOT EXISTS public.saved_service_providers (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE;
      provider_id UUID NOT NULL REFERENCES public.service_providers(id) ON DELETE CASCADE;
      created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL;
      UNIQUE(user_id, provider_id)
    ),
    -- Add indexes for performance;
    CREATE INDEX IF NOT EXISTS idx_saved_providers_user_id ON public.saved_service_providers(user_id),
    CREATE INDEX IF NOT EXISTS idx_saved_providers_provider_id ON public.saved_service_providers(provider_id),
    -- Set up RLS (Row Level Security) policies;
    ALTER TABLE public.saved_service_providers ENABLE ROW LEVEL SECURITY;
    -- Drop existing policies if they exist;
    DROP POLICY IF EXISTS "Users can only view their own saved providers" ON public.saved_service_providers;
    DROP POLICY IF EXISTS "Users can add their own saved providers" ON public.saved_service_providers;
    DROP POLICY IF EXISTS "Users can delete their own saved providers" ON public.saved_service_providers;
    -- Create policy allowing users to see only their own saved providers;
    CREATE POLICY "Users can only view their own saved providers";
      ON public.saved_service_providers;
      FOR SELECT;
      USING (auth.uid() = user_id);
    -- Create policy allowing users to insert their own saved providers;
    CREATE POLICY "Users can add their own saved providers";
      ON public.saved_service_providers;
      FOR INSERT;
      WITH CHECK (auth.uid() = user_id);
    -- Create policy allowing users to delete their own saved providers;
    CREATE POLICY "Users can delete their own saved providers";
      ON public.saved_service_providers;
      FOR DELETE;
      USING (auth.uid() = user_id);
  `,
  // Add other migrations here;
},

/**;
 * Run a SQL migration from the predefined migrations map;
 * @param migrationName The name of the migration;
 * @return s Promise resolving to success status;
 */
export async function runMigration(migrationName: string): Promise<boolean>{
  try {
    logger.info(`Starting migration: ${migrationName}`, 'runMigration'),
    // Get the SQL content from our migration map;
    const sql = MIGRATIONS[migrationName];

    if (!sql) {
      logger.error(`Migration '${migrationName}' not found`, 'runMigration'),
      return false;
    }
    // Execute the SQL (first try with RPC function)
    const { error  } = await getSupabaseClient().rpc('exec_sql', { sql }),

    if (error) {
      logger.error(`Error executing migration '${migrationName}':`, 'runMigration', { error }),

      // Create a simplified table creation if RPC fails;
      if (migrationName === 'create_saved_providers_table') {
        // Fallback to a simpler table creation for saved providers;
        const simpleSql = `;
          CREATE TABLE IF NOT EXISTS public.saved_service_providers (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            user_id UUID NOT NULL;
            provider_id UUID NOT NULL;
            created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL;
            UNIQUE(user_id, provider_id)
          ),
        `,

        // Try the simple creation;
        const { error: simpleError  } = await getSupabaseClient().rpc('exec_sql', { sql: simpleSql })
        if (simpleError) {
          throw simpleError;
        }
      } else {
        throw error;
      }
    }
    logger.info(`Migration '${migrationName}' completed successfully`, 'runMigration'),
    return true;
  } catch (error) {
    logError(error, 'runMigration', { migrationName }),
    return false;
  }
}
// Note: File-based migrations are not supported in React Native,
// as it doesn't have access to the Node.js file system;
/**;
 * Ensures that the database tables required for the application are created.;
 * This is a manual migration process for when we can't use the Supabase CLI.;
 */
export async function ensureTablesExist(): Promise<boolean>{
  try {
    logger.info('Checking if required tables exist...', 'ensureTablesExist'),

    // Check if compatibility_scores table exists;
    const { data: tables, error: tableError  } = await getSupabaseClient().rpc('exec_sql', {
      sql: `);
        SELECT table_name;
        FROM information_schema.tables;
        WHERE table_schema = 'public' );
        AND table_name = 'compatibility_scores')
  try {
    });

    if (tableError) {
      logger.error('Error checking tables:', 'ensureTablesExist', { error: tableError })
      return false;
    }
    // If table doesn't exist, create it;
    if (!tables || (Array.isArray(tables) && tables.length === 0) || !tables.table_name) {
      logger.info('Creating compatibility_scores table...', 'ensureTablesExist'),

      // Create compatibility_scores table;
      const createTableSql = `;
        CREATE TABLE IF NOT EXISTS public.compatibility_scores (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE;
          target_user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE;
          score NUMERIC NOT NULL;
          factors JSONB;
          created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL;
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL;
          UNIQUE(user_id, target_user_id)
        ),
        -- Add indexes for performance;
        CREATE INDEX IF NOT EXISTS idx_compatibility_user_id ON public.compatibility_scores(user_id),
        CREATE INDEX IF NOT EXISTS idx_compatibility_target_user_id ON public.compatibility_scores(target_user_id),
        CREATE INDEX IF NOT EXISTS idx_compatibility_score ON public.compatibility_scores(score),
        -- Set up RLS (Row Level Security) policies;
        ALTER TABLE public.compatibility_scores ENABLE ROW LEVEL SECURITY;
        -- Create policy allowing users to see their own compatibility scores;
        CREATE POLICY "Users can view their own compatibility scores";
          ON public.compatibility_scores;
          FOR SELECT;
          USING (auth.uid() = user_id OR auth.uid() = target_user_id);
      `,

      const { error: createError  } = await getSupabaseClient().rpc('exec_sql', { sql: createTableSql })
      if (createError) {
        logger.error('Error creating compatibility_scores table:', 'ensureTablesExist', { error: createError })
        return false;
      }
      logger.info('compatibility_scores table created successfully', 'ensureTablesExist'),
    } else { logger.info('compatibility_scores table already exists', 'ensureTablesExist') }
    // Add other table checks here as needed;
    return true;
  } catch (error) {
    logError(error, 'ensureTablesExist'),
    return false;
  }
}
/**;
 * Run all pending migrations;
 * @returns Promise resolving to success status;
 */
export async function runAllMigrations(): Promise<boolean>{
  try {
    logger.info('Running all pending migrations', 'runAllMigrations'),
    // Run each migration in sequence;
    for (const [migrationName, _] of Object.entries(MIGRATIONS)) {
      const success = await runMigration(migrationName)
      if (!success) {
        logger.error(`Failed to run migration: ${migrationName}`, 'runAllMigrations'),
        return false;
      }
    }
    logger.info('All migrations completed successfully', 'runAllMigrations'),
    return true;
  } catch (error) {
    logError(error, 'runAllMigrations'),
    return false;
  }
}