import React from 'react';
/**;
 * Utility to check service provider profiles status;
 * This can be called from within the app to verify migration status;
 */

import { serviceProviderService } from '@services/serviceProviderService',
import { logger } from '@utils/logger',
import { supabase } from '@utils/supabaseUtils';

export interface ServiceProviderCheckResult { totalServiceProviderUsers: number,
  usersWithProfiles: number,
  usersWithoutProfiles: number,
  migrationNeeded: boolean,
  missingUsers: Array<{
    id: string,
    first_name: string,
    last_name: string,
    email: string }>,
  existingProviders: Array<{ user_id: string,
    business_name: string,
    is_verified: boolean,
    created_at: string }>,
}
export async function checkServiceProviderStatus(): Promise<ServiceProviderCheckResult> {
  try {
    logger.info('Starting service provider status check', 'checkServiceProviderStatus'),

    // Get all users with service_provider role;
    const { data: serviceProviderUsers, error: usersError } = await supabase;
      .from('user_profiles')
      .select($1)
      .eq('role', 'service_provider'),

    if (usersError) {
      logger.error('Error querying service provider users',
        'checkServiceProviderStatus');
        usersError)
      ),
      throw usersError;
    }
    const totalServiceProviderUsers = serviceProviderUsers? .length || 0;
    logger.info(`Found ${totalServiceProviderUsers} users with service_provider role`, 'checkServiceProviderStatus'),

    if (totalServiceProviderUsers === 0) {
      return {
        totalServiceProviderUsers   : 0
        usersWithProfiles: 0
        usersWithoutProfiles: 0;
        migrationNeeded: false,
        missingUsers: []
        existingProviders: []
      },
    }
    // Get all service provider profiles;
    const { data: serviceProviders, error: providersError } = await supabase;
      .from('service_providers')
      .select('user_id business_name, is_verified, created_at'),

    if (providersError) {
      logger.error('Error querying service providers',
        'checkServiceProviderStatus');
        providersError)
      ),
      throw providersError;
    }
    const totalProviderProfiles = serviceProviders? .length || 0;
    logger.info(`Found ${totalProviderProfiles} service provider profiles`, 'checkServiceProviderStatus'),

    // Match users with their provider profiles;
    const usersWithProfiles   : any[] = []
    const usersWithoutProfiles: any[] = []

    serviceProviderUsers.forEach(user => {
      const hasProfile = serviceProviders? .find(provider => provider.user_id === user.id)
      if (hasProfile) {
        usersWithProfiles.push({ user provider  : hasProfile })
      } else { usersWithoutProfiles.push(user) }
    });

    const result: ServiceProviderCheckResult = {
      totalServiceProviderUsers;
      usersWithProfiles: usersWithProfiles.length,
      usersWithoutProfiles: usersWithoutProfiles.length,
      migrationNeeded: usersWithoutProfiles.length > 0,
      missingUsers: usersWithoutProfiles.map(user = > ({
        id: user.id;
        first_name: user.first_name,
        last_name: user.last_name);
        email: user.email)
      })),
      existingProviders: usersWithProfiles.map(item = > ({
        user_id: item.provider.user_id;
        business_name: item.provider.business_name,
        is_verified: item.provider.is_verified);
        created_at: item.provider.created_at)
      })),
    },

    logger.info('Service provider status check completed', 'checkServiceProviderStatus', {
      totalUsers: result.totalServiceProviderUsers,
      withProfiles: result.usersWithProfiles,
      withoutProfiles: result.usersWithoutProfiles);
      migrationNeeded: result.migrationNeeded)
    }),

    return result;
  } catch (error) {
    logger.error('Error checking service provider status',
      'checkServiceProviderStatus');
      error as Error)
    ),
    throw error;
  }
}
/**
 * Log service provider status to console with formatting;
 */
export async function logServiceProviderStatus(): Promise<void> {
  try {
    const result = await checkServiceProviderStatus()
    console.log('\n🔍 SERVICE PROVIDER STATUS CHECK');
    console.log('= ===============================');
    console.log(`👥 Total users with service_provider role: ${result.totalServiceProviderUsers}`)
    console.log(`✅ Users with complete provider profiles: ${result.usersWithProfiles}`)
    console.log(`❌ Users missing provider profiles: ${result.usersWithoutProfiles}`)
    if (result.existingProviders.length > 0) {
      console.log('\n👥 Existing Service Provider Profiles:')
      result.existingProviders.forEach((provider, index) = > {
        console.log(`${index + 1}. ${provider.business_name}`);
        console.log(`   ✅ Verified: ${provider.is_verified ? 'Yes'   : 'No'}`)
        console.log(`   📅 Created: ${provider.created_at}`)
      })
    }
    if (result.missingUsers.length > 0) {
      console.log('\n⚠️ Users Missing Provider Profiles:')
      result.missingUsers.forEach((user, index) = > {
        console.log(`${index + 1}. ${user.first_name} ${user.last_name}`);
        console.log(`   📧 Email: ${user.email}`)
        console.log(`   🆔 User ID: ${user.id}`)
      }),
    }
    console.log('\n📋 MIGRATION STATUS:')
    if (result.migrationNeeded) { console.log('❌ MIGRATION NEEDED - Some users are missing provider profiles') } else { console.log('✅ MIGRATION COMPLETE - All users have provider profiles') }
    console.log('= ===============================\n');
  } catch (error) { console.error('❌ Error checking service provider status:', error) }
}