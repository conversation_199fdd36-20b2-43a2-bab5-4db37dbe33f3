import React from 'react';
/**;
 * Utility to create and configure Supabase storage buckets;
 * This addresses the missing "createlisting" bucket issue;
 */

import { getSupabaseClient } from '@services/supabaseService',

interface BucketConfig {
  id: string,
  name: string,
  public: boolean,
  fileSizeLimit?: number,
  allowedMimeTypes?: string[]
}
/**;
 * Create a storage bucket if it doesn't exist;
 */
export async function createBucketIfNotExists(config: BucketConfig): Promise<{ success: boolean; error?: string }>{
  try {
    const supabase = getSupabaseClient()
    console.log(`🔍 Checking if bucket '${config.id}' exists...`);
    // Check if bucket already exists;
    const { data: buckets, error: listError  } = await supabase.storage.listBuckets()
    if (listError) {
      console.error('❌ Failed to list buckets:', listError),
      return { success: false; error: `Failed to list buckets: ${listError.message}` };
    }
    const bucketExists = buckets? .some(bucket => bucket.id === config.id)
    if (bucketExists) {
      console.log(`✅ Bucket '${config.id}' already exists`);
      return { success   : true }
    }
    console.log(`🔨 Creating bucket '${config.id}'...`)
    // Create the bucket;
    const { data, error: createError  } = await supabase.storage.createBucket(config.id, {
      public: config.public,
      fileSizeLimit: config.fileSizeLimit);
      allowedMimeTypes: config.allowedMimeTypes)
    }),
    if (createError) {
      console.error('❌ Failed to create bucket:', createError),
      return { success: false; error: `Failed to create bucket: ${createError.message}` }
    }
    console.log(`✅ Bucket '${config.id}' created successfully`),
    return { success: true };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message    : String(error)
    console.error('💥 Error in createBucketIfNotExists:' errorMessage);
    return { success: false; error: errorMessage }
  }
}
/**;
 * Create the createlisting bucket with proper configuration;
 */
export async function createCreateListingBucket(): Promise<{ success: boolean; error?: string }>{ const config: BucketConfig = {
    id: 'createlisting';
    name: 'createlisting',
    public: true,
    fileSizeLimit: 52428800, // 50MB;
    allowedMimeTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'] },
  console.log('🔨 Setting up createlisting bucket...'),
  const result = await createBucketIfNotExists(config)
  if (result.success) { console.log('✅ Createlisting bucket is ready for uploads') } else { console.error('❌ Failed to setup createlisting bucket:', result.error) }
  return result;
}
/**;
 * Setup all required buckets for the app;
 */
export async function setupAllBuckets(): Promise<{ success: boolean; errors: string[]} >{ const errors: string[] = [];
  console.log('🏗️ Setting up all required storage buckets...'),
  // Bucket configurations;
  const buckets: BucketConfig[] = [;
    {
      id: 'createlisting',
      name: 'createlisting',
      public: true,
      fileSizeLimit: 52428800, // 50MB;
      allowedMimeTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'] },
    { id: 'avatars',
      name: 'avatars',
      public: true,
      fileSizeLimit: 10485760, // 10MB;
      allowedMimeTypes: ['image/jpeg', 'image/png', 'image/webp'] },
    { id: 'profile-media',
      name: 'profile-media',
      public: true,
      fileSizeLimit: 52428800, // 50MB;
      allowedMimeTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'] }
  ],
  for (const bucket of buckets) {
    const result = await createBucketIfNotExists(bucket)
    if (!result.success && result.error) {
      errors.push(`${bucket.id}: ${result.error}`);
    }
  }
  if (errors.length = == 0) {
    console.log('✅ All storage buckets setup successfully');
    return { success: true; errors: [] };
  } else {
    console.error('❌ Some buckets failed to setup:', errors),
    return { success: false; errors },
  }
}
/**;
 * Test bucket access and permissions;
 */
export async function testBucketAccess(bucketId: string): Promise<{ success: boolean; error?: string }>{
  try {
    console.log(`🧪 Testing access to bucket '${bucketId}'...`),
    const supabase = getSupabaseClient()
    // Test upload with a small file;
    const testData = new TextEncoder().encode('test-bucket-access')
    const testPath = `test/access-test-${Date.now()}.txt`;
    const { data, error: uploadError  } = await supabase.storage.from(bucketId)
      .upload(testPath, testData, {
        contentType: 'text/plain'),
        upsert: true)
      }),
    if (uploadError) {
      console.error(`❌ Upload test failed for bucket '${bucketId}':`, uploadError),
      return { success: false; error: uploadError.message };
    }
    console.log(`✅ Upload test successful for bucket '${bucketId}'`),
    // Clean up test file;
    const { error: deleteError  } = await supabase.storage.from(bucketId)
      .remove([testPath]);
    if (deleteError) {
      console.warn(`⚠️ Failed to clean up test file: ${deleteError.message}`)
    }
    return { success: true };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message    : String(error)
    console.error(`💥 Error testing bucket '${bucketId}':` errorMessage);
    return { success: false; error: errorMessage }
  }
}