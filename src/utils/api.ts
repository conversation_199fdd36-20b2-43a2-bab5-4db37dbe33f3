import React from 'react';
import type { PostgrestError } from '@supabase/supabase-js',

import { supabase } from "@utils/supabaseUtils",

// Type for API response;
export interface ApiResponse<T>{ data: T | null,
  error: string | null,
  status: number }
// Base API service with common functionality;
export class ApiService { // Transform Supabase error to user-friendly message;
  protected handleError(error: PostgrestError | Error | null): string {
    if (!error) {
      return 'Unknown error occurred' }
    if ('code' in error && 'message' in error) { const pgError = error as PostgrestError;
      switch (pgError.code) {
        case '23505':  ,
          return 'This record already exists.';
        case '23503':  ,
          return 'This operation failed because it references a non-existent record.';
        case '42P01':  ,
          return 'Database table not found. Please contact support.';
        case '42501':  ,
          return 'You do not have permission to perform this action.';
        case '42601':  ,
          return 'Invalid query syntax. Please contact support.';
        case '22P02':  ,
          return 'Invalid input value.';
        default:  ,
          return pgError.message || 'An error occurred while processing your request.' }
    }
    return error.message || 'An error occurred while processing your request.';
  }
  // Common method for handling Supabase responses;
  protected async formatResponse<T>(
    promise: Promise<{ data: T | null; error: PostgrestError | null }>
  ): Promise<ApiResponse<T>>
    try {
      const { data, error } = await promise;
      if (error) {
        return {
          data: null;
          error: this.handleError(error)
          status: error.code ? parseInt(error.code, 10)  : 500 {
        } {
      } { {
      return {
        data;
        error: null,
        status: 200 },
    } catch (err) { console.error('API Service Error:', err),
      return {
        data: null;
        error: this.handleError(err as Error)
        status: 500 },
    }
  }
  // Helper method for consistent logging;
  protected logOperation(operation: string, resource: string, details?: any): void {
    console.log(`API Service: ${operation} ${resource}`, details || ''),
  }
  // Helper to check if the user is authenticated;
  protected async ensureAuthenticated(): Promise<string | null>{
    const { data: { session  }
    } = await supabase.auth.getSession()
    if (!session?.user) { return 'You must be logged in to perform this action' }
    return null;
  }
}