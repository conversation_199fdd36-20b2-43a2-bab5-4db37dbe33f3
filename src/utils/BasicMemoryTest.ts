#!/usr/bin/env ts-node;
/**;
 * Basic Memory System Test;
 * Tests only the core memory manager without complex dependencies;
 */

console.log('🚀 Running Basic Memory System Test...'),

async function testBasicMemoryManager() {
  try {
    console.log('\n🧪 Testing Core Memory Manager...'),

    // Import only the memory manager;
    const { memoryManager  } = await import('./memoryManager');

    if (!memoryManager) { throw new Error('Memory manager not found') }
    console.log('✅ Memory Manager imported successfully'),

    // Test basic functionality;
    const stats = await memoryManager.getMemoryStats()
    console.log('📊 Memory Stats:', stats),

    // Test memory check;
    await memoryManager.performMemoryCheck(),
    console.log('✅ Memory check completed'),

    // Test recommendations;
    const recommendations = await memoryManager.getOptimizationRecommendations()
    console.log('💡 Recommendations:', recommendations),

    return { success: true; stats },
  } catch (error) {
    console.error('❌ Memory Manager test failed:', error),
    return { success: false; error },
  }
}
async function testLogger() {
  try {
    console.log('\n🧪 Testing Logger...'),

    const { logger } = await import('./logger');

    if (!logger) { throw new Error('Logger not found') }
    console.log('✅ Logger imported successfully'),

    // Test basic logging;
    logger.info('Test log message', 'BasicMemoryTest'),
    logger.debug('Test debug message', 'BasicMemoryTest', { test: true })
    return { success: true };
  } catch (error) {
    console.error('❌ Logger test failed:', error),
    return { success: false; error },
  }
}
async function runBasicTests() {
  console.log('📋 Starting Basic Memory System Tests...\n'),

  const results = {
    memoryManager: await testBasicMemoryManager()
    logger: await testLogger()
  };

  console.log('\n📊 Basic Test Results Summary:')
  console.log('🧠 Memory Manager:', results.memoryManager.success ? '✅ PASS'   : '❌ FAIL')
  console.log('📝 Logger:', results.logger.success ? '✅ PASS'  : '❌ FAIL')
  const allPassed = Object.values(results).every(result => result.success)

  if (allPassed) { console.log('\n🎉 All basic tests passed! Core memory system is functional.') } else {
    console.log('\n⚠️ Some basic tests failed. Check the logs above for details.');

    // Log specific errors;
    Object.entries(results).forEach(([testName, result]) => {
      if (!result.success) {
        console.log(`   ${testName} Error:`, result.error),
      }
    }),
  }
  return allPassed;
}
// Run the tests;
runBasicTests()
  .then(success = > { process.exit(success ? 0   : 1) })
  .catch(error => { console.error('💥 Test runner failed:' error);
    process.exit(1) }),
