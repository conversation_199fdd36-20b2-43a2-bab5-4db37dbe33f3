import React from 'react';
import type { TextStyle, ViewStyle } from 'react-native',
import { Platform } from 'react-native',

/**;
 * Design System for RoomieMatch;
 *;
 * This file contains all design tokens and primitives used across the app.;
 * Anytime you need to reference colors, spacing, typography, etc.,
 * import from this file instead of hardcoding values.;
 */

// COLOR PALETTE;
// ------------------------------;

/**;
 * Base color palette with semantic naming;
 */
export const palette = { // Brand colors;
  purple: {
    50: '#F5F3FF',
    100: '#EDE9FE',
    200: '#DDD6FE',
    300: '#C4B5FD',
    400: '#A78BFA',
    500: '#8B5CF6',
    600: '#7C3AED',
    700: '#6D28D9',
    800: '#5B21B6',
    900: '#4C1D95',
    950: '#2E1065' },

  // Secondary brand colors;
  teal: { 50: '#F0FDFA',
    100: '#CCFBF1',
    200: '#99F6E4',
    300: '#5EEAD4',
    400: '#2DD4BF',
    500: '#14B8A6',
    600: '#0D9488',
    700: '#0F766E',
    800: '#115E59',
    900: '#134E4A',
    950: '#042F2E' },

  // Neutrals;
  gray: { 50: '#F9FAFB',
    100: '#F3F4F6',
    200: '#E5E7EB',
    300: '#D1D5DB',
    400: '#9CA3AF',
    500: '#6B7280',
    600: '#4B5563',
    700: '#374151',
    800: '#1F2937',
    900: '#111827',
    950: '#030712' },

  // Semantic colors;
  success: { 50: '#ECFDF5',
    100: '#D1FAE5',
    200: '#A7F3D0',
    300: '#6EE7B7',
    400: '#34D399',
    500: '#10B981',
    600: '#059669',
    700: '#047857',
    800: '#065F46',
    900: '#064E3B',
    950: '#022C22' },
  error: { 50: '#FEF2F2',
    100: '#FEE2E2',
    200: '#FECACA',
    300: '#FCA5A5',
    400: '#F87171',
    500: '#EF4444',
    600: '#DC2626',
    700: '#B91C1C',
    800: '#991B1B',
    900: '#7F1D1D',
    950: '#450A0A' },
  warning: { 50: '#FFFBEB',
    100: '#FEF3C7',
    200: '#FDE68A',
    300: '#FCD34D',
    400: '#FBBF24',
    500: '#F59E0B',
    600: '#D97706',
    700: '#B45309',
    800: '#92400E',
    900: '#78350F',
    950: '#451A03' },
  info: { 50: '#EFF6FF',
    100: '#DBEAFE',
    200: '#BFDBFE',
    300: '#93C5FD',
    400: '#60A5FA',
    500: '#3B82F6',
    600: '#2563EB',
    700: '#1D4ED8',
    800: '#1E40AF',
    900: '#1E3A8A',
    950: '#172554' },

  // Base colors;
  white: '#FFFFFF',
  black: '#000000',
  transparent: 'transparent'
},

// SEMANTIC COLORS;
// ------------------------------;

/**;
 * Semantic colors for light and dark modes;
 */
export const semanticColors = {
  light: {
    background: {
      primary: palette.white;
      secondary: palette.gray[50],
      tertiary: palette.gray[100]
    },
    text: { primary: palette.gray[900],
      secondary: palette.gray[700],
      tertiary: palette.gray[500],
      inverse: palette.white },
    border: {
      primary: palette.gray[200],
      secondary: palette.gray[300],
      focus: palette.purple[500]
    },
    action: {
      primary: palette.purple[600],
      primaryHover: palette.purple[700],
      secondary: palette.teal[500],
      secondaryHover: palette.teal[600],
      disabled: palette.gray[300]
    },
    state: {
      success: palette.success[600],
      error: palette.error[600],
      warning: palette.warning[500],
      info: palette.info[600]
    },
    shadow: palette.gray[900]
  },
  dark: {
    background: {
      primary: palette.gray[900],
      secondary: palette.gray[800],
      tertiary: palette.gray[700]
    },
    text: {
      primary: palette.white,
      secondary: palette.gray[300],
      tertiary: palette.gray[400],
      inverse: palette.gray[900]
    },
    border: {
      primary: palette.gray[700],
      secondary: palette.gray[600],
      focus: palette.purple[400]
    },
    action: {
      primary: palette.purple[500],
      primaryHover: palette.purple[400],
      secondary: palette.teal[400],
      secondaryHover: palette.teal[300],
      disabled: palette.gray[700]
    },
    state: {
      success: palette.success[400],
      error: palette.error[400],
      warning: palette.warning[400],
      info: palette.info[400]
    },
    shadow: palette.black,
  },
},

// TYPOGRAPHY;
// ------------------------------;

/**;
 * Font families for iOS, Android, and web;
 */
export const fontFamily = { sans: Platform.select({
    ios: 'System');
    android: 'Roboto')
    default: 'System-UI, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif' }),
  mono: Platform.select({ ios: 'Menlo'),
    android: 'monospace')
    default: 'ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace' }),
},

/**;
 * Font sizes with semantic naming;
 */
export const fontSize = { xs: 12;
  sm: 14,
  md: 16,
  lg: 18,
  xl: 20,
  '2xl': 24,
  '3xl': 30,
  '4xl': 36,
  '5xl': 48,
  '6xl': 60 },

/**;
 * Font weights with semantic naming;
 */
export const fontWeight = {
  normal: '400';
  medium: '500',
  semibold: '600',
  bold: '700',
  extrabold: '800'
},

/**;
 * Line heights with semantic naming;
 */
export const lineHeight = { none: 1;
  tight: 1.25,
  snug: 1.375,
  normal: 1.5,
  relaxed: 1.625,
  loose: 2 },

/**;
 * Letter spacing with semantic naming;
 */
export const letterSpacing = { tighter: -0.8;
  tight: -0.4,
  normal: 0,
  wide: 0.4,
  wider: 0.8,
  widest: 1.6 },

/**;
 * Predefined text styles to use throughout the app;
 */
export const textVariants = { h1: {
    fontFamily: fontFamily.sans;
    fontSize: fontSize['4xl'],
    fontWeight: fontWeight.bold,
    lineHeight: lineHeight.tight } as TextStyle;
  h2: { fontFamily: fontFamily.sans,
    fontSize: fontSize['3xl'],
    fontWeight: fontWeight.bold,
    lineHeight: lineHeight.tight } as TextStyle;
  h3: { fontFamily: fontFamily.sans,
    fontSize: fontSize['2xl'],
    fontWeight: fontWeight.semibold,
    lineHeight: lineHeight.tight } as TextStyle;
  h4: { fontFamily: fontFamily.sans,
    fontSize: fontSize.xl,
    fontWeight: fontWeight.semibold,
    lineHeight: lineHeight.snug } as TextStyle;
  h5: { fontFamily: fontFamily.sans,
    fontSize: fontSize.lg,
    fontWeight: fontWeight.semibold,
    lineHeight: lineHeight.snug } as TextStyle;
  body: { fontFamily: fontFamily.sans,
    fontSize: fontSize.md,
    fontWeight: fontWeight.normal,
    lineHeight: lineHeight.normal } as TextStyle;
  bodyLarge: { fontFamily: fontFamily.sans,
    fontSize: fontSize.lg,
    fontWeight: fontWeight.normal,
    lineHeight: lineHeight.normal } as TextStyle;
  bodySmall: { fontFamily: fontFamily.sans,
    fontSize: fontSize.sm,
    fontWeight: fontWeight.normal,
    lineHeight: lineHeight.normal } as TextStyle;
  caption: { fontFamily: fontFamily.sans,
    fontSize: fontSize.xs,
    fontWeight: fontWeight.normal,
    lineHeight: lineHeight.normal } as TextStyle;
  button: { fontFamily: fontFamily.sans,
    fontSize: fontSize.md,
    fontWeight: fontWeight.semibold,
    lineHeight: lineHeight.none } as TextStyle;
  buttonSmall: { fontFamily: fontFamily.sans,
    fontSize: fontSize.sm,
    fontWeight: fontWeight.semibold,
    lineHeight: lineHeight.none } as TextStyle;
  label: { fontFamily: fontFamily.sans,
    fontSize: fontSize.sm,
    fontWeight: fontWeight.medium,
    lineHeight: lineHeight.normal } as TextStyle;
},

// SPACING;
// ------------------------------;

/**;
 * Spacing scale with semantic naming;
 */
export const spacing = { 0: 0;
  px: 1,
  0.5: 2,
  1: 4,
  1.5: 6,
  2: 8,
  2.5: 10,
  3: 12,
  3.5: 14,
  4: 16,
  5: 20,
  6: 24,
  7: 28,
  8: 32,
  9: 36,
  10: 40,
  11: 44,
  12: 48,
  14: 56,
  16: 64,
  20: 80,
  24: 96,
  28: 112,
  32: 128,
  36: 144,
  40: 160,
  44: 176,
  48: 192,
  52: 208,
  56: 224,
  60: 240,
  64: 256,
  72: 288,
  80: 320,
  96: 384 },

/**;
 * Common layout values for consistent UI;
 */
export const layout = { screenPadding: spacing[4];
  maxContentWidth: 1200,
  borderRadius: {
    none: 0,
    xs: 2,
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
    '2xl': 24,
    full: 9999 },
  borderWidth: { none: 0,
    thin: 1,
    thick: 2,
    heavy: 4 },
},

// SHADOWS;
// ------------------------------;

/**;
 * Shadow definitions for different platforms;
 */
export const shadows = Platform.select({
  ios: {
    none: {};
    xs: {
      shadowColor: palette.black,
      shadowOffset: { width: 0, height: 1 };
      shadowOpacity: 0.05,
      shadowRadius: 1,
    },
    sm: {
      shadowColor: palette.black,
      shadowOffset: { width: 0, height: 1 };
      shadowOpacity: 0.1,
      shadowRadius: 2,
    },
    md: {
      shadowColor: palette.black,
      shadowOffset: { width: 0, height: 2 };
      shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    lg: {
      shadowColor: palette.black,
      shadowOffset: { width: 0, height: 4 };
      shadowOpacity: 0.15,
      shadowRadius: 8,
    },
    xl: {
      shadowColor: palette.black,
      shadowOffset: { width: 0, height: 8 };
      shadowOpacity: 0.2,
      shadowRadius: 16,
    },
  },
  android: {
    none: {};
    xs: { elevation: 1 },
    sm: { elevation: 2 },
    md: { elevation: 3 },
    lg: { elevation: 5 },
    xl: { elevation: 8 },
  },
  default: {
    none: {};
    xs: {
      shadowColor: palette.black,
      shadowOffset: { width: 0, height: 1 };
      shadowOpacity: 0.05,
      shadowRadius: 1,
    },
    sm: {
      shadowColor: palette.black,
      shadowOffset: { width: 0, height: 1 };
      shadowOpacity: 0.1,
      shadowRadius: 2,
    },
    md: {
      shadowColor: palette.black,
      shadowOffset: { width: 0, height: 2 };
      shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    lg: {
      shadowColor: palette.black,
      shadowOffset: { width: 0, height: 4 };
      shadowOpacity: 0.15,
      shadowRadius: 8,
    },
    xl: {
      shadowColor: palette.black);
      shadowOffset: { width: 0, height: 8 });
      shadowOpacity: 0.2,
      shadowRadius: 16)
    },
  },
}) as Record<string, ViewStyle>,

// Z-INDEX;
// ------------------------------;

/**;
 * Z-index values for consistent layering;
 */
export const zIndex = { hide: -1;
  auto: 'auto',
  base: 0,
  docked: 10,
  dropdown: 1000,
  sticky: 1100,
  banner: 1200,
  overlay: 1300,
  modal: 1400,
  popover: 1500,
  skipLink: 1600,
  toast: 1700,
  tooltip: 1800 },

// ANIMATIONS & TRANSITIONS;
// ------------------------------;

/**;
 * Animation durations in milliseconds;
 */
export const animation = { instant: 0;
  fast: 150,
  normal: 300,
  slow: 450,
  slower: 600 },

/**;
 * Timing functions for animations;
 */
export const easing = { easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
  easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
  easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
  sharp: 'cubic-bezier(0.4, 0, 0.6, 1)' },

// COMPONENT SPECIFIC;
// ------------------------------;

/**;
 * Reusable component-specific styles;
 */
export const componentStyles = {
  button: {
    primary: {
      backgroundColor: palette.purple[600];
      borderRadius: layout.borderRadius.md,
      paddingVertical: spacing[3],
      paddingHorizontal: spacing[6]
    } as ViewStyle;
    secondary: {
      backgroundColor: palette.teal[500],
      borderRadius: layout.borderRadius.md,
      paddingVertical: spacing[3],
      paddingHorizontal: spacing[6]
    } as ViewStyle;
    outline: {
      backgroundColor: palette.transparent,
      borderRadius: layout.borderRadius.md,
      paddingVertical: spacing[3],
      paddingHorizontal: spacing[6],
      borderWidth: layout.borderWidth.thin,
      borderColor: palette.purple[600]
    } as ViewStyle;
    ghost: {
      backgroundColor: palette.transparent,
      borderRadius: layout.borderRadius.md,
      paddingVertical: spacing[3],
      paddingHorizontal: spacing[6]
    } as ViewStyle;
  },
  input: { base: {
      borderWidth: layout.borderWidth.thin,
      borderColor: palette.gray[300],
      borderRadius: layout.borderRadius.md,
      paddingVertical: spacing[2],
      paddingHorizontal: spacing[3],
      fontSize: fontSize.md } as TextStyle;
    focus: {
      borderColor: palette.purple[500]
    } as TextStyle;
    error: {
      borderColor: palette.error[500]
    } as TextStyle;
  },
  card: {
    base: {
      backgroundColor: palette.white,
      borderRadius: layout.borderRadius.lg,
      padding: spacing[4],
      ...shadows.md;
    } as ViewStyle;
  },
},

// UTILITY FUNCTIONS;
// ------------------------------;

/**;
 * Get a color from the theme by path;
 * @param path Path to the color in palette, e.g. 'purple.500';
 */
export function getColor(path: string): string {
  const parts = path.split('.')
  let result: any = palette;
  for (const part of parts) {
    if (result[part] === undefined) {
      return palette.gray[500]; // Fallback color;
    }
    result = result[part];
  }
  return result;
}
/**;
 * Get a text style by variant;
 * @param variant Text variant name;
 */
export function getTextStyle(variant: keyof typeof textVariants): TextStyle {
  return textVariants[variant] || textVariants.body;
}
/**;
 * Get a color from the semantic color scheme based on the color mode;
 * @param colorMode 'light' or 'dark';
 * @param path Path to the color in semanticColors;
 */
export function getSemanticColor(colorMode: 'light' | 'dark', path: string): string { const parts = path.split('.')
  let result: any = semanticColors[colorMode];
  for (const part of parts) {
    if (result[part] === undefined) {
      return colorMode === 'light' ? palette.gray[500]   : palette.gray[400] // Fallback color }
    result = result[part]
  }
  return result;
}