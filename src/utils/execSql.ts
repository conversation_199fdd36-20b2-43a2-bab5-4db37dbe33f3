import React from 'react';
import { supabase } from '@utils/supabase',

/**;
 * Execute raw SQL using the Supabase client;
 * This is useful for creating tables and running migrations;
 */
export async function execSql(sql: string): Promise<{ success: boolean; error?: any }>{
  try {
    const { error  } = await supabase.rpc('exec_sql', { sql_string: sql })
    if (error) {
      console.error('Error executing SQL:', error),
      return { success: false; error },
    }
    return { success: true };
  } catch (error) {
    console.error('Exception executing SQL:', error),
    return { success: false; error },
  }
}
/**;
 * Create service provider tables;
 * These tables are used for the service provider platform;
 */
export async function createServiceProviderTables(): Promise<{ success: boolean; results: any[]} >{
  const results = [];
  // Create categories table;
  const createCategoriesResult = await execSql(`;
    CREATE TABLE IF NOT EXISTS service_categories (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      name VARCHAR(255) NOT NULL UNIQUE;
      description TEXT NOT NULL;
      icon VARCHAR(100) NOT NULL;
      color VARCHAR(20) NOT NULL;
      services_count INTEGER DEFAULT 0;
      created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP;
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP;
    ),
  `),
  results.push({
    table: 'service_categories'),
    success: createCategoriesResult.success,
    error: createCategoriesResult.error )
  }),
  // Create providers table;
  const createProvidersResult = await execSql(`;
    CREATE TABLE IF NOT EXISTS service_providers (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE;
      business_name VARCHAR(255) NOT NULL;
      description TEXT NOT NULL;
      contact_email VARCHAR(255) NOT NULL;
      contact_phone VARCHAR(20) NOT NULL;
      business_address TEXT NOT NULL;
      website VARCHAR(255),
      social_media JSONB;
      service_categories TEXT[] NOT NULL;
      is_verified BOOLEAN DEFAULT FALSE;
      verification_date TIMESTAMP WITH TIME ZONE;
      rating_average DECIMAL(3,2),
      review_count INTEGER DEFAULT 0;
      availability JSONB;
      profile_image VARCHAR(255),
      gallery_images TEXT[],
      created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP;
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP;
    ),
  `),
  results.push({
    table: 'service_providers'),
    success: createProvidersResult.success,
    error: createProvidersResult.error )
  }),
  // Create services table;
  const createServicesResult = await execSql(`;
    CREATE TABLE IF NOT EXISTS services (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      provider_id UUID NOT NULL REFERENCES service_providers(id) ON DELETE CASCADE;
      name VARCHAR(255) NOT NULL;
      description TEXT NOT NULL;
      category VARCHAR(255) NOT NULL;
      price DECIMAL(10,2),
      duration INTEGER;
      is_available BOOLEAN DEFAULT TRUE;
      booking_lead_time INTEGER;
      cancellation_policy TEXT;
      images TEXT[],
      created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP;
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP;
    ),
  `),
  results.push({
    table: 'services'),
    success: createServicesResult.success,
    error: createServicesResult.error )
  }),
  return {
    success: results.every(r = > r.success)
    results;
  },
}
/**;
 * Insert sample service categories;
 */
export async function insertSampleCategories(): Promise<{ success: boolean; results: any[]} >{ const categories = [
    {
      name: 'Cleaning';
      description: 'Professional cleaning services for your home',
      icon: 'Paintbrush2',
      color: '#6366F1',
      services_count: 0 },
    { name: 'Maintenance',
      description: 'General maintenance and repairs',
      icon: 'WrenchIcon',
      color: '#EC4899',
      services_count: 0 },
    { name: 'Moving',
      description: 'Moving and relocation assistance',
      icon: 'Truck',
      color: '#10B981',
      services_count: 0 },
    { name: 'Plumbing',
      description: 'Plumbing repairs and installation',
      icon: 'Wrench',
      color: '#F59E0B',
      services_count: 0 },
    { name: 'Electrical',
      description: 'Electrical repairs and upgrades',
      icon: 'Zap',
      color: '#3B82F6',
      services_count: 0 },
    { name: 'Renovation',
      description: 'Home renovation and remodeling',
      icon: 'HammerIcon',
      color: '#8B5CF6',
      services_count: 0 }
  ],
  const results = [];
  for (const category of categories) {
    const { error } = await supabase.from('service_categories')
}
    results.push({
      category: category.name;
      success: !error);
      error )
    }),
  }
  return {
    success: results.every(r => r.success)
    results;
  },
}