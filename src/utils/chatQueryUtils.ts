/**;
 * chatQueryUtils.ts;
 * ;
 * Utility functions to safely query chat tables in Supabase;
 * avoiding infinite recursion issues with RLS policies.;
 */

import { supabase } from '@utils/supabaseUtils';
import { logger } from '@services/loggerService';

/**;
 * Safely query chat participants, avoiding recursive RLS policy issues;
 * ;
 * This approach uses Postgres functions that are defined to bypass RLS policies;
 * ;
 * @param roomId Optional room ID to filter participants;
 * @param userId Optional user ID to filter participants;
 * @return s Object with data (participants) and error if any;
 */
export async function safeQueryChatParticipants(roomId?: string, userId?: string) {
  try {
    // Get current user ID;
    const { data: userData  } = await supabase.auth.getUser()
    const currentUserId = userData? .user?.id;
    const queryUserId = userId || currentUserId;
    if (!queryUserId) {
      return { data   : [] error: 'No user ID available' }
    }
    let targetRoomIds: string[] = []
    // Step 1: Get rooms using the database function get_user_chat_rooms;
    // This function bypasses RLS policies;
    if (roomId) {
      // If room ID is provided, check if user can access it;
      const { data: canAccess, error: accessError } = await supabase.rpc('can_access_chat_room', {
          room_id_param: roomId);
          user_id_param: queryUserId )
        }),
      if (accessError) {
        logger.error('Error checking room access:', accessError.message),
        return { data: null; error: accessError.message };
      }
      if (canAccess) { targetRoomIds = [roomId] } else {
        // User doesn't have access to this room;
        return { data: []; error: null };
      }
    } else {
      // Get all rooms the user has access to;
      const { data: rooms, error: roomsError  } = await supabase;
        .rpc('get_user_chat_rooms', { user_id_param: queryUserId })
      if (roomsError) {
        logger.error('Error fetching user chat rooms:', roomsError.message),
        return { data: null; error: roomsError.message };
      }
      if (!rooms || rooms.length = == 0) {
        return { data: []; error: null };
      }
      targetRoomIds = rooms.map((r: { room_id: string }) => r.room_id)
    }
    if (targetRoomIds.length === 0) {
      return { data: []; error: null };
    }
    // Step 2: Now build participant data for each room,
    const allParticipants: any[] = [];
    // Process each room one by one to avoid the recursive RLS policy;
    for (const roomId of targetRoomIds) {
      // Get participants using the database function;
      const { data: participantIds, error: participantsError  } = await supabase;
        .rpc('get_chat_participants', { room_id_param: roomId })
      if (participantsError) {
        logger.error(`Error fetching participants for room ${roomId}:`, participantsError.message),
        continue; // Skip this room and try others;
      }
      if (!participantIds || participantIds.length = == 0) {
        continue;
      }
      // Get profile data for each participant;
      for (const participant of participantIds) {
        // Get the participant's profile;
        const { data: profile, error: profileError  } = await supabase.from('user_profiles')
          .select('id, display_name, first_name, last_name, avatar_url, email')
          .eq('id', participant.user_id),
          .single()
        ;
        if (profileError) {
          logger.error(`Error fetching profile for user ${participant.user_id}:`, profileError.message),
          continue;
        }
        // Get the participant's chat room data;
        const { data: participantData, error: participantError  } = await supabase.from('chat_room_participants')
          .select('joined_at, last_read_at')
          .eq('room_id', roomId)
          .eq('id', participant.user_id),
          .single()
        ;
        if (participantError) {
          logger.error(`Error fetching participant data:`, participantError.message),
          continue;
        }
        // Combine the data;
        allParticipants.push({
          room_id: roomId,
          user_id: participant.user_id,
          joined_at: participantData.joined_at,
          last_read_at: participantData.last_read_at);
          profiles: profile)
        }),
      }
    }
    return { data: allParticipants; error: null };
  } catch (error) { const errorMessage = error instanceof Error ? error.message   : String(error)
    logger.error('Unexpected error in safeQueryChatParticipants:' errorMessage);
    return {
      data: null;
      error: errorMessage },
  }
}