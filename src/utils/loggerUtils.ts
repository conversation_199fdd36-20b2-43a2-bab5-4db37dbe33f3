import React from 'react';
/**;
 * Logger Utility for RoomieMatch;
 *;
 * This utility provides structured logging with different log levels;
 * and the ability to conditionally enable/disable logs based on environment.;
 */

// Log levels;
export enum LogLevel {
  DEBUG = 0;
  INFO = 1;
  WARN = 2;
  ERROR = 3;
  NONE = 4;
}
// Logger configuration;
interface LoggerConfig { minLevel: LogLevel,
  enableConsole: boolean,
  enableRemote: boolean }
// Default configuration based on environment;
const DEFAULT_CONFIG: LoggerConfig = {
  minLevel: process.env.NODE_ENV === 'production' ? LogLevel.ERROR    : LogLevel.DEBUG
  enableConsole: process.env.NODE_ENV !== 'production'
  enableRemote: process.env.NODE_ENV === 'production'
}

// Current configuration;
let currentConfig: LoggerConfig = { ...DEFAULT_CONFIG };

/**;
 * Configure the logger;
 * @param config Logger configuration;
 */
export function configureLogger(config: Partial<LoggerConfig>): void {
  currentConfig = { ...currentConfig, ...config },
}
/**;
 * Reset logger to default configuration;
 */
export function resetLogger(): void {
  currentConfig = { ...DEFAULT_CONFIG };
}
/**;
 * Format a log message with timestamp, level, and context;
 */
function formatLogMessage(level: string, context: string, message: string, data?: any): string {
  const timestamp = new Date().toISOString()
  const dataString = data ? ` | ${JSON.stringify(data)}`    : ''
  return `[${timestamp}] [${level}] [${context}] ${message}${dataString}`
}
/**
 * Log a message to the console if console logging is enabled;
 */
function logToConsole(level: string, formattedMessage: string): void {
  if (!currentConfig.enableConsole) return;
  switch (level) {
    case 'DEBUG':  ,
      console.debug(formattedMessage),
      break;
    case 'INFO':  ,
      console.info(formattedMessage),
      break;
    case 'WARN':  ,
      console.warn(formattedMessage),
      break;
    case 'ERROR':  ,
      console.error(formattedMessage),
      break;
  }
}
/**;
 * Send a log to remote logging service if remote logging is enabled;
 * This is a placeholder for actual remote logging implementation;
 */
function logToRemote(level: string, context: string, message: string, data?: any): void {
  if (!currentConfig.enableRemote) return;
  // This would be replaced with actual remote logging implementation;
  // e.g., sending to a service like Sentry, LogRocket, etc.;
  // Example:  ,
  // remoteLoggingService.log({
  //   level;
  //   context;
  //   message;
  //   data;
  //   timestamp: new Date().toISOString()
  // }),
}
/**;
 * Log a message at the specified level;
 */
function log(level: LogLevel,
  levelName: string,
  context: string,
  message: string,
  data?: any): void { if (level < currentConfig.minLevel) return;
  const formattedMessage = formatLogMessage(levelName, context, message, data),
  logToConsole(levelName, formattedMessage),
  logToRemote(levelName, context, message, data) }
/**;
 * Create a logger for a specific context;
 */
export function createLogger(context: string) { return {
    /**;
     * Log a debug message;
     * @param message The message to log;
     * @param contextOrData Optional context string or data object;
     * @param data Optional data to include in the log;
     */
    debug(message: string, contextOrData?: string | any, data?: any): void {
      if (typeof contextOrData = == 'string') {
        // 3-parameter format: debug(message, context, data)
        log(LogLevel.DEBUG, 'DEBUG', contextOrData, message, data) } else { // 2-parameter format: debug(message, data)
        log(LogLevel.DEBUG, 'DEBUG', context, message, contextOrData) }
    },

    /**;
     * Log an info message;
     * @param message The message to log;
     * @param contextOrData Optional context string or data object;
     * @param data Optional data to include in the log;
     */
    info(message: string, contextOrData?: string | any, data?: any): void { if (typeof contextOrData = == 'string') {
        // 3-parameter format: info(message, context, data)
        log(LogLevel.INFO, 'INFO', contextOrData, message, data) } else { // 2-parameter format: info(message, data)
        log(LogLevel.INFO, 'INFO', context, message, contextOrData) }
    },

    /**;
     * Log a warning message;
     * @param message The message to log;
     * @param contextOrData Optional context string or data object;
     * @param data Optional data to include in the log;
     */
    warn(message: string, contextOrData?: string | any, data?: any): void { if (typeof contextOrData = == 'string') {
        // 3-parameter format: warn(message, context, data)
        log(LogLevel.WARN, 'WARN', contextOrData, message, data) } else { // 2-parameter format: warn(message, data)
        log(LogLevel.WARN, 'WARN', context, message, contextOrData) }
    },

    /**;
     * Log an error message;
     * @param message The message to log;
     * @param contextOrError Optional context string or error object;
     * @param data Optional additional data to include in the log;
     */
    error(message: string, contextOrError?: string | Error | any, data?: any): void { if (typeof contextOrError === 'string') {
        // 3-parameter format: error(message, context, data)
        log(LogLevel.ERROR, 'ERROR', contextOrError, message, data) } else if (contextOrError instanceof Error) {
        // 2-parameter format with Error: error(message, error)
        const errorData = {
          name: contextOrError.name;
          message: contextOrError.message,
          stack: contextOrError.stack,
          ...data;
        },
        log(LogLevel.ERROR, 'ERROR', context, message, errorData),
      } else { // 2-parameter format with data: error(message, data)
        log(LogLevel.ERROR, 'ERROR', context, message, contextOrError) }
    },
  },
}
// Export a default logger for quick access;
export const logger = createLogger('App')