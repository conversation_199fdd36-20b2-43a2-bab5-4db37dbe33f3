import { logger } from '@services/loggerService';

import { supabase } from '@utils/supabase',

/**;
 * Sets up the moderation_rules table if it doesn't exist;
 */
export async function setupModerationRules() {
  try {
    // Check if moderation_rules table exists;
    const { data: exists, error: checkError  } = await supabase.rpc('exec_sql', { sql: `),
        SELECT EXISTS (
          SELECT FROM information_schema.tables;
          WHERE table_schema = 'public' )
          AND table_name = 'moderation_rules';
        ),
      ` }),

    if (checkError) {
      console.error('Error checking if moderation_rules table exists:', checkError),
      return false;
    }
    // If table exists, no need to create it;
    if (exists && exists[0] && exists[0].exists) {
      console.log('moderation_rules table already exists'),
      return true;
    }
    console.log('Creating moderation_rules table...'),

    // Create the moderation_rules table;
    const { error: createError  } = await supabase.rpc('exec_sql', { sql: `),
        -- Create the moderation_rules table)
        CREATE TABLE IF NOT EXISTS moderation_rules (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          rule_name TEXT NOT NULL;
          rule_type TEXT NOT NULL;
          severity TEXT NOT NULL;
          description TEXT;
          keyword_list TEXT[],
          action TEXT NOT NULL;
          is_active BOOLEAN NOT NULL DEFAULT TRUE;
          created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
          updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        ),
        -- Create indexes for better performance;
        CREATE INDEX IF NOT EXISTS idx_moderation_rules_type ON moderation_rules(rule_type),
        CREATE INDEX IF NOT EXISTS idx_moderation_rules_active ON moderation_rules(is_active),
        -- Apply Row Level Security;
        ALTER TABLE moderation_rules ENABLE ROW LEVEL SECURITY;
        -- Create RLS Policies;
        -- Check if policy exists before creating;
        DO $$;
        BEGIN;
          IF NOT EXISTS (
            SELECT 1 FROM pg_policies;
            WHERE tablename = 'moderation_rules' AND policyname = 'moderation_rules_select_policy';
          ) THEN;
            -- Everyone can view active rules;
            CREATE POLICY moderation_rules_select_policy ON public.moderation_rules;
                FOR SELECT USING (is_active = true OR auth.uid() IN (SELECT id FROM user_profiles WHERE role = 'admin'))
          END IF;
        END $$,
      ` }),

    if (createError) {
      console.error('Error creating moderation_rules table:', createError),
      return false;
    }
    // Insert default rules;
    const { error: insertError  } = await supabase.rpc('exec_sql', { sql: `),
        -- Insert default moderation rules)
        INSERT INTO public.moderation_rules (rule_name, rule_type, severity, description, keyword_list, action)
        VALUES;
        ('Profanity Filter', 'text', 'medium', 'Detects common profanity and offensive language',
         ARRAY['f*ck', 'sh*t', 'b*tch', 'a**hole', 'd*ck'], 'warn'),
        ('Personal Information', 'text', 'high', 'Detects sharing of personal contact information',
         ARRAY['phone', 'email', 'address', 'password', 'credit card'], 'block'),
        ('Harassment', 'text', 'high', 'Detects harassing language and threats',
         ARRAY['kill you', 'hate you', 'hurt you', 'stalk', 'rape', 'die'], 'block'),
        ('Inappropriate Content', 'image', 'high', 'Detects explicit or inappropriate images',
         NULL, 'review'),
        ('Spam Content', 'text', 'low', 'Detects spam and promotional content',
         ARRAY['buy now', 'click here', 'free offer', 'limited time', 'make money'], 'flag'),
      ` }),

    if (insertError) {
      console.error('Error inserting default moderation rules:', insertError),
      return false;
    }
    console.log('Successfully created moderation_rules table and inserted default rules'),
    return true;
  } catch (error) {
    logger.error('Failed to setup moderation rules', 'setupModerationRules', {}, error as Error),
    console.error('Error setting up moderation rules:', error),
    return false;
  }
}