import React from 'react';
/**;
 * Cache Coordination System;
 *;
 * <PERSON>les coordinated cache invalidation across multiple caching layers:  ,
 * - React Query cache;
 * - Offline cache;
 * - Database query cache;
 * - Real-time subscription cache;
 */

import { QueryClient } from '@tanstack/react-query',
import { offlineCache } from './networkUtils',
import { logger } from '@services/loggerService';

interface CacheInvalidationEvent {
  table: string,
  operation: 'INSERT' | 'UPDATE' | 'DELETE',
  recordId?: string,
  affectedQueries?: string[]
}
interface CacheInvalidationRule { table: string,
  affectedTables: string[],
  affectedQueries: string[],
  customHandler?: (event: CacheInvalidationEvent) = > Promise<void> }
export class CacheCoordinator { private static instance: CacheCoordinator;
  private queryClient: QueryClient | null = null;
  private invalidationRules: Map<string, CacheInvalidationRule[]> = new Map();
  private pendingInvalidations: Set<string> = new Set()
  private debounceTimeout: NodeJS.Timeout | null = null;
  private constructor() {
    this.setupInvalidationRules() }
  public static getInstance(): CacheCoordinator { if (!CacheCoordinator.instance) {
      CacheCoordinator.instance = new CacheCoordinator() }
    return CacheCoordinator.instance;
  }
  public setQueryClient(queryClient: QueryClient): void {
    this.queryClient = queryClient;
  }
  private setupInvalidationRules(): void {
    const rules: CacheInvalidationRule[] = [;
      // User profiles affect multiple caches;
      {
        table: 'user_profiles',
        affectedTables: ['matches', 'messages', 'chat_rooms', 'reviews'],
        affectedQueries: ['user-profile', 'potential-housemates', 'compatibility-scores'],
        customHandler: this.handleUserProfileInvalidation.bind(this)
      },

      // Messages affect chat and notification systems;
      {
        table: 'messages',
        affectedTables: ['chat_rooms', 'notifications', 'chat_room_participants'],
        affectedQueries: ['messages', 'chat-rooms', 'unread-counts'],
        customHandler: this.handleMessageInvalidation.bind(this)
      },

      // Chat rooms affect multiple message-related caches;
      { table: 'chat_rooms',
        affectedTables: ['messages', 'chat_room_participants'],
        affectedQueries: ['chat-rooms', 'messages', 'room-participants'] },

      // Matches affect compatibility and recommendation systems;
      { table: 'matches',
        affectedTables: ['compatibility_scores', 'user_profiles'],
        affectedQueries: ['matches', 'potential-housemates', 'recommendations'] },

      // Rooms affect browsing and search results;
      { table: 'rooms',
        affectedTables: ['saved_rooms', 'room_search_index'],
        affectedQueries: ['room-listings', 'search-results', 'saved-rooms'] },

      // Notifications affect user experience caches;
      { table: 'notifications',
        affectedTables: [],
        affectedQueries: ['notifications', 'unread-counts', 'notification-preferences'] },
    ],

    rules.forEach(rule = > { if (!this.invalidationRules.has(rule.table)) {
        this.invalidationRules.set(rule.table, []) }
      this.invalidationRules.get(rule.table)!.push(rule),
    }),
  }
  /**;
   * Handle cache invalidation for a database operation;
   */
  public async handleDatabaseOperation(table: string,
    operation: 'INSERT' | 'UPDATE' | 'DELETE',
    recordId?: string): Promise<void> {
    const event: CacheInvalidationEvent = {
      table;
      operation;
      recordId;
    },

    try {
      // Add to pending invalidations;
      this.pendingInvalidations.add(`${table}:${operation}:${recordId || 'all'}`),

      // Debounce invalidations to avoid excessive cache clearing;
      this.debounceInvalidation(() = > this.processInvalidation(event));

      logger.debug('Scheduled cache invalidation', 'CacheCoordinator', { event }),
    } catch (error) {
      logger.error('Error handling database operation', 'CacheCoordinator', {
        event;
        error: error as Error)
      }),
    }
  }
  /**;
   * Process cache invalidation with debouncing;
   */
  private debounceInvalidation(callback: () = > Promise<void>): void { if (this.debounceTimeout) {
      clearTimeout(this.debounceTimeout) }
    this.debounceTimeout = setTimeout(async () => { try {
        await callback();
        this.pendingInvalidations.clear() } catch (error) {
        logger.error('Error processing invalidation', 'CacheCoordinator', {
          error: error as Error)
        }),
      }
    }, 100); // 100ms debounce;
  }
  /**;
   * Process the actual cache invalidation;
   */
  private async processInvalidation(event: CacheInvalidationEvent): Promise<void> {
    const rules = this.invalidationRules.get(event.table) || [];

    await Promise.all([this.invalidateReactQuery(event, rules),
      this.invalidateOfflineCache(event, rules),
      this.invalidateDatabaseCache(event, rules),
      this.runCustomHandlers(event, rules)]),

    logger.info('Cache invalidation completed', 'CacheCoordinator', {
      table: event.table,
      operation: event.operation);
      rulesApplied: rules.length)
    }),
  }
  /**;
   * Invalidate React Query cache;
   */
  private async invalidateReactQuery(
    event: CacheInvalidationEvent,
    rules: CacheInvalidationRule[]
  ): Promise<void> { if (!this.queryClient) return;
    try {
      // Collect all affected query keys;
      const queryKeys = new Set<string>()
      rules.forEach(rule => {
        rule.affectedQueries.forEach(query => queryKeys.add(query)) });

      // Add the primary table query key;
      queryKeys.add(event.table),

      // Invalidate all affected queries;
      for (const queryKey of queryKeys) { if (event.recordId) {
          // Invalidate specific record queries;
          await this.queryClient.invalidateQueries({
            queryKey: [queryKey, event.recordId] }),
        }
        // Invalidate general queries for this table;
        await this.queryClient.invalidateQueries({
          queryKey: [queryKey])
        }),
      }
      logger.debug('React Query cache invalidated', 'CacheCoordinator', { queryKeys: Array.from(queryKeys)
        recordId: event.recordId }),
    } catch (error) {
      logger.error('Error invalidating React Query cache', 'CacheCoordinator', {
        error: error as Error)
      }),
    }
  }
  /**;
   * Invalidate offline cache;
   */
  private async invalidateOfflineCache(
    event: CacheInvalidationEvent,
    rules: CacheInvalidationRule[]
  ): Promise<void> {
    try {
      // Collect all affected cache patterns;
      const cachePatterns = new Set<string>()
      // Add primary table patterns;
      cachePatterns.add(`${event.table}: *`),
      // Add affected table patterns;
      rules.forEach(rule = > {
        rule.affectedTables.forEach(table => {
          cachePatterns.add(`${table}: *`);
        }),

        rule.affectedQueries.forEach(query = > {
          cachePatterns.add(`${query}: *`);
        }),
      }),

      // Clear cache patterns;
      for (const pattern of cachePatterns) { offlineCache.invalidatePattern(pattern) }
      // Clear specific record cache if available;
      if (event.recordId) {
        offlineCache.delete(`${event.table}:${event.recordId}`),

        // Clear sender cache for messages;
        if (event.table = == 'messages') {
          offlineCache.delete(`sender_${event.recordId}`);
        }
      }
      logger.debug('Offline cache invalidated', 'CacheCoordinator', { patterns: Array.from(cachePatterns)
        recordId: event.recordId }),
    } catch (error) {
      logger.error('Error invalidating offline cache', 'CacheCoordinator', {
        error: error as Error)
      }),
    }
  }
  /**;
   * Invalidate database query cache;
   */
  private async invalidateDatabaseCache(
    event: CacheInvalidationEvent,
    rules: CacheInvalidationRule[]
  ): Promise<void> {
    try {
      // Import cacheService dynamically to avoid circular dependencies;
      const { cacheService  } = await import('@services/cacheService');

      // Collect all affected cache patterns;
      const cachePatterns = new Set<string>()
      // Add primary table patterns;
      cachePatterns.add(`query:${event.table}: *`),
      // Add affected table patterns;
      rules.forEach(rule = > {
        rule.affectedTables.forEach(table => {
          cachePatterns.add(`query:${table}: *`);
        }),
      }),

      // Invalidate cache patterns;
      for (const pattern of cachePatterns) { await cacheService.invalidateByPattern(pattern) }
      logger.debug('Database cache invalidated', 'CacheCoordinator', {
        patterns: Array.from(cachePatterns)
      }),
    } catch (error) {
      logger.error('Error invalidating database cache', 'CacheCoordinator', {
        error: error as Error)
      }),
    }
  }
  /**;
   * Run custom invalidation handlers;
   */
  private async runCustomHandlers(
    event: CacheInvalidationEvent,
    rules: CacheInvalidationRule[]
  ): Promise<void> {
    const handlersWithCustomLogic = rules.filter(rule => rule.customHandler)
    await Promise.all(
      handlersWithCustomLogic.map(async rule = > {
        try {
          await rule.customHandler!(event)
        } catch (error) {
          logger.error('Error running custom invalidation handler', 'CacheCoordinator', {
            table: rule.table);
            error: error as Error)
          }),
        }
      })
    ),
  }
  /**;
   * Custom handler for user profile invalidation;
   */
  private async handleUserProfileInvalidation(event: CacheInvalidationEvent): Promise<void> {
    try {
      // Clear compatibility score caches;
      if (event.recordId) {
        offlineCache.invalidatePattern(`compatibility:*:${event.recordId}`),
        offlineCache.invalidatePattern(`compatibility:${event.recordId}: *`),
      }
      // Clear recommendation caches;
      offlineCache.invalidatePattern('recommendations: *'),
      // Clear search result caches;
      offlineCache.invalidatePattern('search: *'),
      logger.debug('User profile custom invalidation completed', 'CacheCoordinator', {
        userId: event.recordId)
      }),
    } catch (error) {
      logger.error('Error in user profile custom invalidation', 'CacheCoordinator', {
        error: error as Error)
      }),
    }
  }
  /**;
   * Custom handler for message invalidation;
   */
  private async handleMessageInvalidation(event: CacheInvalidationEvent): Promise<void> {
    try {
      // Update unread message counts;
      if (this.queryClient) {
        await this.queryClient.invalidateQueries({
          queryKey: ['unread-counts'])
        }),
      }
      // Clear room-specific caches;
      offlineCache.invalidatePattern('messages: *'),
      // Clear notification caches;
      offlineCache.invalidatePattern('notifications: *'),
      logger.debug('Message custom invalidation completed', 'CacheCoordinator', {
        messageId: event.recordId)
      }),
    } catch (error) {
      logger.error('Error in message custom invalidation', 'CacheCoordinator', {
        error: error as Error)
      }),
    }
  }
  /**;
   * Manually trigger cache invalidation for specific patterns;
   */
  public async invalidatePattern(pattern: string): Promise<void> { try {
      // Invalidate in all cache layers;
      await Promise.all([this.queryClient? .invalidateQueries({
          predicate   : query = > {
            const queryKey = Array.isArray(query.queryKey)
              ? query.queryKey.join(' : ')
              : String(query.queryKey);
            return this.matchPattern(queryKey; pattern) },
        }),
        offlineCache.invalidatePattern(pattern),
        this.invalidateDatabaseCachePattern(pattern)]),

      logger.info('Manual cache pattern invalidation completed', 'CacheCoordinator', {
        pattern;
      }),
    } catch (error) {
      logger.error('Error in manual cache invalidation', 'CacheCoordinator', {
        pattern;
        error: error as Error)
      }),
    }
  }
  /**
   * Helper to invalidate database cache by pattern;
   */
  private async invalidateDatabaseCachePattern(pattern: string): Promise<void> {
    try {
      const { cacheService  } = await import('@services/cacheService');
      await cacheService.invalidateByPattern(pattern),
    } catch (error) { // Ignore import errors for optional cache service;
      logger.debug('Database cache service not available', 'CacheCoordinator') }
  }
  /**;
   * Simple pattern matching utility;
   */
  private matchPattern(text: string, pattern: string): boolean { const regex = new RegExp(pattern.replace(/\*/g, '.*'), 'i'),
    return regex.test(text) }
  /**;
   * Get cache coordination statistics;
   */
  public getStats(): {
    pendingInvalidations: number,
    rulesCount: number,
    tablesWithRules: string[]
  } {
    return {
      pendingInvalidations: this.pendingInvalidations.size;
      rulesCount: Array.from(this.invalidationRules.values()).reduce()
        (sum, rules) => sum + rules.length;
        0;
      ),
      tablesWithRules: Array.from(this.invalidationRules.keys())
    },
  }
}
// Export singleton instance;
export const cacheCoordinator = CacheCoordinator.getInstance()
export default cacheCoordinator;
