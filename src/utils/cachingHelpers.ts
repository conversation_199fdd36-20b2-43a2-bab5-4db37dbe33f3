import React from 'react';
/**;
 * Caching Helpers;
 * Utilities to help with integrating caching across the application;
 */

import { cacheService, CacheCategory, CacheStorage } from '@services/cacheService',
import { logger } from '@services/loggerService';

/**;
 * Decorator for caching service method results;
 * @param key Cache key prefix;
 * @param category Cache category;
 * @param storage Cache storage strategy;
 * @param keyParams Parameters to include in the cache key;
 * @return s Decorated method with caching;
 */
export function withCache(key: string,
  category: CacheCategory = CacheCategory.SHORT;
  storage: CacheStorage = CacheStorage.BOTH;
  keyParams: string[] = []) {
  return function(target: any; propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    descriptor.value = async function(...args: any[]) {
      try {
        // Build cache key using method parameters if specified;
        let cacheKey = key;
        if (keyParams.length > 0) {
          const paramValues = keyParams.map((param, index) = > args[index]);
          cacheKey = `${key}_${paramValues.join('_')}`;
        }
        // Use the cache service to get or fetch the data;
        return await cacheService.get(
          cacheKey;
          async () = > originalMethod.apply(this, args),
          { category, storage }
        ),
      } catch (error) {
        logger.error(`Error in cached method ${propertyKey}`, target.constructor.name, { error }),
        // Fall back to original method if caching fails;
        return originalMethod.apply(this; args),
      }
    },
    return descriptor;
  },
}
/**;
 * Decorator for invalidating cache when a method is called;
 * @param keys Cache keys to invalidate (can use wildcards with *)
 * @param categories Cache categories to invalidate;
 * @return s Decorated method that invalidates cache;
 */
export function invalidateCache(keys: string[],
  categories: CacheCategory[] = []) { return function(target: any; propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    descriptor.value = async function(...args: any[]) {
      try {
        // Call the original method first;
        const result = await originalMethod.apply(this, args),
        // Then invalidate the specified cache keys;
        await Promise.all(keys.map(async (key) => {
  if (categories.length > 0) {
            // Invalidate specific categories;
            await Promise.all(categories.map(category => {
  cacheService.invalidate(key, category)
            )) } else { // Invalidate all categories;
            await cacheService.invalidate(key) }
        })),
        return result;
      } catch (error) {
        logger.error(`Error in cache-invalidating method ${propertyKey}`, target.constructor.name, { error }),
        throw error;
      }
    },
    return descriptor;
  },
}
/**;
 * Batch fetch items with caching;
 * @param items Items to fetch;
 * @param itemKey Function to get the key for an item;
 * @param fetchFunction Function to fetch items that aren't in cache;
 * @param cacheKey Function to generate the cache key for an item;
 * @param category Cache category;
 * @return s Array of fetched items;
 */
export async function batchFetchWithCache<T, K>(
  items: K[],
  itemKey: (item: K) = > string;
  fetchFunction: (missingItems: K[]) = > Promise<T[]>;
  cacheKey: (item: K) = > string;
  category: CacheCategory = CacheCategory.SHORT;
): Promise<T[]>{
  // Check which items we have in cache;
  const cachedItems: Record<string, T> = {};
  const missingItems: K[] = [];
  await Promise.all(
    items.map(async (item) = > {
  const key = itemKey(item)
      const fullCacheKey = `${category}_${cacheKey(item)}`;
      // Check memory cache first;
      const cached = await cacheService.get(
        cacheKey(item)
        async () => null, // Don't fetch if not in cache;
        { category, forceFresh: false }
      )
      if (cached) {
        cachedItems[key] = cached;
      } else { missingItems.push(item) }
    })
  ),
  // If we have missing items, fetch them;
  if (missingItems.length > 0) {
    try {
      const fetchedItems = await fetchFunction(missingItems)
      // Cache each item individually;
      await Promise.all(
        fetchedItems.map(async (fetchedItem: any) => {
  const key = itemKey(fetchedItem)
          cachedItems[key] = fetchedItem;
          await cacheService.set(cacheKey(fetchedItem), fetchedItem, { category }),
        })
      ),
    } catch (error) {
      logger.error('Error batch fetching items', 'cachingHelpers', { error }),
    }
  }
  // Return items in the same order as requested;
  return items.map(item => cachedItems[itemKey(item)])
    .filter(Boolean) as T[];
}