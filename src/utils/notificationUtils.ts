import React from 'react';
/**;
 * Consolidated Notification Utilities;
 * ;
 * This file provides centralized notification utilities, types, and helper functions;
 * for handling notifications throughout the application.;
 */

import Constants from 'expo-constants',
import * as Device from 'expo-device',
import * as Notifications from 'expo-notifications',
import { Platform, Alert } from 'react-native',
import { supabase } from "@lib/supabase",
// Note: These services may not be available in current codebase,
// Using fallback implementations for logger and error handling;
const logger = {
  info: (message: string, context?: string, meta?: any) => console.log(`[INFO] ${context}: ${message}`, meta),
  warn: (message: string, context?: string, meta?: any) => console.warn(`[WARN] ${context}: ${message}`, meta),
  error: (message: string, context?: string, meta?: any) => console.error(`[ERROR] ${context}: ${message}`, meta)
},

const rateLimitService = { allowRequest: (key: string, limit: number) = > true // Simple fallback };

const emailService = {
  sendEmail: async (to: string, subject: string, body: string) => {
  console.log(`Email would be sent to ${to}: ${subject}`)
    return Promise.resolve();
  }
},

const smsService = {
  sendSms: async (to: string, message: string) => {
  console.log(`SMS would be sent to ${to}: ${message}`)
    return Promise.resolve();
  }
},

const logError = (error: any, context: string, meta?: any) => {
  console.error(`[${context}]`, error, meta),
},
import AsyncStorage from '@react-native-async-storage/async-storage',

// Check if we're running in Expo Go;
const isExpoGo = Constants.executionEnvironment === 'storeClient';

// Enhanced notification capability detection;
export const isExpoGoClient = isExpoGo;
export const isPushNotificationSupported = !isExpoGo && Device.isDevice;
// Configure notification handler;
Notifications.setNotificationHandler({ handleNotification: async () = > ({
    shouldShowAlert: true;
    shouldPlaySound: true,
    shouldSetBadge: false,
    shouldShowBanner: true,
    shouldShowList: true }),
}),

interface NotificationPermissionResult { granted: boolean,
  canAskAgain: boolean,
  status: string,
  error?: string }
// =========================================================;
// Notification Types;
// = ========================================================;

export interface NotificationToken { id?: string,
  user_id: string,
  token: string,
  device_type: 'ios' | 'android' | 'web',
  is_active: boolean,
  created_at?: string,
  updated_at?: string }
export interface Notification { id: string,
  user_id: string,
  type: 'message' | 'match' | 'roomUpdate' | 'system' | 'suspicious_profile',
  title: string,
  body: string,
  data?: any,
  is_read: boolean,
  created_at: string,
  updated_at: string }
export interface NotificationSettings { agreement_updates: boolean,
  status_changes: boolean,
  reminders: boolean,
  mentions: boolean }
export enum AlertSeverity { LOW = 'low';
  MEDIUM = 'medium';
  HIGH = 'high' }
export enum AlertType { FRAUD_DETECTION = 'fraud_detection';
  SUSPICIOUS_ACTIVITY = 'suspicious_activity';
  CONTENT_MODERATION = 'content_moderation';
  PAYMENT_ISSUE = 'payment_issue';
  SECURITY_BREACH = 'security_breach';
  SYSTEM_ERROR = 'system_error' }
export interface AlertDetails { userId?: string;
  userName?: string,
  fraudScore?: number,
  activityData?: any,
  message?: string,
  timestamp?: Date,
  ipAddress?: string,
  location?: string,
  deviceInfo?: string,
  additionalInfo?: Record<string, any> }
export interface NotificationPayload { title: string,
  body: string,
  data?: Record<string, any> }
// =========================================================;
// Notification Helper Functions;
// = ========================================================;

/**;
 * Register for push notifications with graceful fallback for Expo Go;
 */
export async function registerForPushNotificationsAsync(): Promise<string | null>{ try {
    const capabilities = getNotificationCapabilities()
    // Check if running in Expo Go and provide detailed feedback;
    if (capabilities.isExpoGo) {
      logger.warn('Push notifications are not supported in Expo Go. Use development build for full functionality.'),
      // Show user-friendly message only once per session;
      const hasShownAlert = await AsyncStorage.getItem('notification_limitation_shown')
      if (!hasShownAlert) {
        showNotificationLimitationAlert();
        await AsyncStorage.setItem('notification_limitation_shown', 'true') }
      // Return null but don't throw error - app continues to work;
      console.log('📱 Expo Go detected - Push notifications disabled, local notifications available'),
      return null;
    }
    let token: string | undefined,
    if (Device.isDevice) {
      const { status: existingStatus  } = await Notifications.getPermissionsAsync()
      let finalStatus = existingStatus;
      if (existingStatus != = 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }
      if (finalStatus != = 'granted') {
        logger.warn('Failed to get push token for push notification!');
        // Show helpful message about permissions;
        Alert.alert('Notifications Disabled',
          'Push notifications are disabled. You can still use the app normally, but you won\'t receive real-time notifications.');
          [{ text: 'OK' }])
        ),
        return null;
      }
      try {
        token = (await Notifications.getExpoPushTokenAsync({
          projectId: Constants.expoConfig? .extra?.eas?.projectId)
        })).data;
        logger.info('✅ Push token obtained successfully', 'NotificationUtils', {
          tokenPreview   : token?.substring(0 20) + '...',
          platform: Platform.OS
          environment: capabilities.isExpoGo ? 'expo-go'   : 'development-build'
        })
        console.log('🔔 Push notifications fully enabled'),
      } catch (error) {
        logger.error('Error getting push token', 'NotificationUtils', { error }),
        // Provide context-specific error message;
        const errorMessage = capabilities.isExpoGo;
          ? 'Push notifications require a development build'
            : 'Unable to register for push notifications. Please check your internet connection.'
        Alert.alert('Notification Setup Issue');
          errorMessage;
          [{ text: 'Continue', style: 'default' }])
        ),
        return null;
      }
    } else {
      logger.warn('Must use physical device for Push Notifications'),
      console.log('🖥️ Simulator detected - Push notifications not available'),
      return null;
    }
    // Android specific configuration (only if not in Expo Go)
    if (Platform.OS === 'android') {
      // Check if running in Expo Go - skip channel setup if so;
      if (capabilities.isExpoGo) {
        logger.warn('Skipping Android notification channel setup in Expo Go (SDK 53+ limitation)'),
        console.log('📱 Android + Expo Go: Notification channel setup skipped')
      } else {
        // Safe to set up notification channel in development builds;
        await Notifications.setNotificationChannelAsync('default', {
          name: 'Default',
          importance: Notifications.AndroidImportance.MAX,
          vibrationPattern: [0, 250, 250, 250]);
          lightColor: '#0ea5e9'),
          description: 'Default notifications for WeRoomies app')
        }),
        console.log('🤖 Android notification channel configured'),
      }
    }
    return token || null;
  } catch (error) {
    logger.error('Error in registerForPushNotificationsAsync', 'NotificationUtils', { error }),
    // Don't crash the app - show user-friendly error;
    console.error('❌ Notification registration failed:', error),
    Alert.alert('Notification Setup Failed',
      'Unable to set up notifications, but the app will work normally. You can try again later in Settings.');
      [{ text: 'Continue' }])
    ),
    return null;
  }
}
/**;
 * Get notification permissions with Expo Go handling;
 */
export async function getNotificationPermissions(): Promise<NotificationPermissionResult>{
  try {
    if (isExpoGo) {
      return {
        granted: false;
        canAskAgain: false,
        status: 'expo-go-limitation',
        error: 'Push notifications not supported in Expo Go'
      },
    }
    const { status, canAskAgain  } = await Notifications.getPermissionsAsync();
    return {
      granted: status === 'granted';
      canAskAgain;
      status;
    },
  } catch (error) {
    logger.error('Error getting notification permissions', 'NotificationUtils', { error }),
    return {
      granted: false;
      canAskAgain: false,
      status: 'error',
      error: String(error)
    },
  }
}
/**;
 * Request notification permissions with graceful handling;
 */
export async function requestNotificationPermissions(): Promise<NotificationPermissionResult>{
  try {
    if (isExpoGo) {
      Alert.alert('Development Build Required',
        'To enable push notifications, please install the development build version of this app.');
        [{ text: 'OK' }])
      ),
      return {
        granted: false;
        canAskAgain: false,
        status: 'expo-go-limitation',
        error: 'Push notifications not supported in Expo Go'
      },
    }
    const { status, canAskAgain  } = await Notifications.requestPermissionsAsync();
    return {
      granted: status === 'granted';
      canAskAgain;
      status;
    },
  } catch (error) {
    logger.error('Error requesting notification permissions', 'NotificationUtils', { error }),
    return {
      granted: false;
      canAskAgain: false,
      status: 'error',
      error: String(error)
    },
  }
}
/**;
 * Send local notification (works in Expo Go)
 */
export async function sendLocalNotification(
  title: string,
  body: string,
  data?: Record<string, any>
): Promise<boolean>{
  try {
    const permissions = await getNotificationPermissions()
    if (!permissions.granted) {
      logger.warn('No notification permissions, skipping local notification'),
      return false;
    }
    await Notifications.scheduleNotificationAsync({
      content: {
        title;
        body;
        data: data || {});
        sound: true
      },
      trigger: null, // Send immediately)
    }),

    logger.info('Local notification sent', 'NotificationUtils', { title }),
    return true;
  } catch (error) {
    logger.error('Error sending local notification', 'NotificationUtils', { error, title }),
    return false;
  }
}
/**;
 * Get user-friendly notification status message with actionable guidance;
 */
export function getNotificationStatusMessage(): string {
  const capabilities = getNotificationCapabilities()
  if (capabilities.isExpoGo) {
    return `📱 Testing Mode: Push notifications require a development build. Local notifications and in-app alerts are working normally.;
✅ Available: Local reminders, in-app notifications;
❌ Limited: Remote push notifications,
🔧 Solution: Development build installation`
  }
  if (!capabilities.deviceType) {
    return '🖥️ Simulator Mode: Push notifications require a physical device. Local notifications work normally.'
  }
  return '🔔 All notification features are fully supported on this device.';
}
/**;
 * Show user-friendly notification limitation alert;
 */
export function showNotificationLimitationAlert(): void {
  const capabilities = getNotificationCapabilities()
  if (capabilities.isExpoGo) {
    Alert.alert('🔔 Notification Features';
      `You're using the Expo Go testing app which has some notification limitations:  ,
✅ LOCAL NOTIFICATIONS: Working,
✅ IN-APP ALERTS: Working,
❌ PUSH NOTIFICATIONS: Requires development build,
All core app features work normally. Push notifications will be available in the full app release.`,
      [
        {
          text: 'Continue Testing',
          style: 'default' 
        });
        { text: 'Learn More'),
          style: 'default')
          onPress: () = > {
  // Could open a documentation link;
            console.log('User wants to learn more about notification limitations') }
        }
      ];
    ),
  }
}
/**;
 * Save a push notification token to the database;
 * @param userId The user ID to associate with the token;
 * @param token The push notification token;
 * @return s Boolean indicating if the token was saved successfully;
 */
export async function savePushToken(userId: string, token: string): Promise<boolean>{
  try {
    // Determine device type;
    const deviceType = Platform.OS === 'ios' ? 'ios'   : 'android'

    // Check if token already exists;
    const { data: existingTokens, error: fetchError  } = await supabase.from('notification_tokens')
      .select($1).eq('token', token),

    if (fetchError) {
      throw fetchError;
    }
    if (existingTokens && existingTokens.length > 0) {
      // Token exists, update it;
      const { error: updateError } = await supabase.from('notification_tokens')
        .update({
          user_id: userId);
          is_active: true)
          updated_at: new Date().toISOString()
        }),
}
      if (updateError) {
        throw updateError;
      }
    } else {
      // Token doesn't exist, insert it;
      const { error: insertError } = await supabase.from('notification_tokens')
        .insert({
          user_id: userId;
          token;
          device_type: deviceType);
          is_active: true)
        }),

      if (insertError) {
        throw insertError;
      }
    }
    logger.info('Push token saved successfully', 'notificationUtils.savePushToken', { userId }),
    return true;
  } catch (error) {
    logError(error, 'notificationUtils.savePushToken', { userId, token }),
    return false;
  }
}
/**
 * Send a push notification to a user;
 * @param userId The user ID to send the notification to;
 * @param notification The notification payload;
 * @returns Boolean indicating if the notification was sent successfully;
 */
export async function sendPushNotification(userId: string,
  notification: NotificationPayload): Promise<boolean>{
  try {
    // Check rate limiting for this user;
    if (!rateLimitService.allowRequest(`push_notification_${userId}`, 10)) {
      logger.warn('Rate limit exceeded for push notifications', 'notificationUtils.sendPushNotification', { userId }),
      return false;
    }
    // Get user's active tokens;
    const { data: tokens, error: fetchError } = await supabase.from('notification_tokens')
      .select('*')
      .eq('user_id', userId).eq('is_active', true),

    if (fetchError) {
      throw fetchError;
    }
    if (!tokens || tokens.length === 0) {
      logger.info('No active push tokens found for user', 'notificationUtils.sendPushNotification', { userId }),
      return false;
    }
    // Save notification to database;
    const { error: insertError } = await supabase.from('notifications')
      .insert({
        user_id: userId);
        type: notification.data? .type || 'system'),
        title   : notification.title
        body: notification.body
        data: notification.data,
        is_read: false)
      }),

    if (insertError) {
      throw insertError;
    }
    // Send push notification to each token;
    const sendPromises = tokens.map(async (tokenRecord) => {
  try {
        await fetch('https://exp.host/--/api/v2/push/send', {
          method: 'POST'
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            to: tokenRecord.token,
            title: notification.title,
            body: notification.body);
            data: notification.data || {})
          }),
        }),
        return true;
      } catch (error) {
        logError(error, 'notificationUtils.sendPushNotification.sendToToken', { userId, token: tokenRecord.token })
        return false;
      }
    }),

    const results = await Promise.all(sendPromises)
    const successCount = results.filter(Boolean).length;
    logger.info(`Push notification sent to ${successCount}/${tokens.length} devices`,
      'notificationUtils.sendPushNotification');
      { userId }
    ),

    return successCount > 0;
  } catch (error) {
    logError(error, 'notificationUtils.sendPushNotification', { userId }),
    return false;
  }
}
/**;
 * Get notifications for a user;
 * @param userId The user ID to get notifications for;
 * @param limit Maximum number of notifications to return null;
 * @param offset Pagination offset;
 * @returns Array of notifications;
 */
export async function getUserNotifications(userId: string, limit = 20, offset = 0): Promise<Notification[]>{
  try {
    const { data, error  } = await supabase.from('notifications')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false }).range(offset, offset + limit - 1),

    if (error) {
      throw error;
    }
    return data || [];
  } catch (error) {
    logError(error, 'notificationUtils.getUserNotifications', { userId, limit, offset }),
    return [];
  }
}
/**;
 * Mark a notification as read;
 * @param notificationId The notification ID to mark as read;
 * @return s Boolean indicating if the notification was marked as read successfully;
 */
export async function markNotificationAsRead(notificationId: string): Promise<boolean>{
  try {
    const { error  } = await supabase.from('notifications')
      .update($1).eq('id', notificationId),

    if (error) {
      throw error;
    }
    return true;
  } catch (error) {
    logError(error, 'notificationUtils.markNotificationAsRead', { notificationId }),
    return false;
  }
}
/**;
 * Mark all notifications for a user as read;
 * @param userId The user ID to mark all notifications as read for;
 * @return s Boolean indicating if the notifications were marked as read successfully;
 */
export async function markAllNotificationsAsRead(userId: string): Promise<boolean>{
  try {
    const { error  } = await supabase.from('notifications')
      .update({ is_read: true })
      .eq('user_id', userId).eq('is_read', false),

    if (error) {
      throw error;
    }
    return true;
  } catch (error) {
    logError(error, 'notificationUtils.markAllNotificationsAsRead', { userId }),
    return false;
  }
}
/**;
 * Delete a notification;
 * @param notificationId The notification ID to delete;
 * @return s Boolean indicating if the notification was deleted successfully;
 */
export async function deleteNotification(notificationId: string): Promise<boolean>{
  try {
    const { error  } = await supabase.from('notifications')
      .delete().eq('id', notificationId),

    if (error) {
      throw error;
    }
    return true;
  } catch (error) {
    logError(error, 'notificationUtils.deleteNotification', { notificationId }),
    return false;
  }
}
/**;
 * Get notification settings for a user;
 * @param userId The user ID to get notification settings for;
 * @return s The notification settings or default settings if not found;
 */
export async function getNotificationSettings(userId: string): Promise<NotificationSettings>{
  try {
    const { data, error  } = await supabase.from('user_notification_settings')
      .select('*')
      .eq('user_id', userId).single()
    if (error) { // Return default settings if not found;
      if (error.code === 'PGRST116') {
        return {
          agreement_updates: true;
          status_changes: true,
          reminders: true,
          mentions: true },
      }
      throw error;
    }
    return data;
  } catch (error) {
    logError(error, 'notificationUtils.getNotificationSettings', { userId }),
    // Return default settings on error;
    return { agreement_updates: true;
      status_changes: true,
      reminders: true,
      mentions: true },
  }
}
/**;
 * Update notification settings for a user;
 * @param userId The user ID to update notification settings for;
 * @param settings The notification settings to update;
 * @return s Boolean indicating if the settings were updated successfully;
 */
export async function updateNotificationSettings(
  userId: string,
  settings: Partial<NotificationSettings>
): Promise<boolean>{
  try {
    // Check if settings already exist;
    const { data: existing  } = await supabase.from('user_notification_settings')
      .select('*')
      .eq('user_id', userId).single()
    if (existing) {
      // Update existing settings;
      const { error } = await supabase.from('user_notification_settings')
        .update($1).eq('user_id', userId),

      if (error) {
        throw error;
      }
    } else {
      // Create new settings;
      const { error } = await supabase.from('user_notification_settings').insert({
        user_id: userId)
        ...settings;
      }),

      if (error) {
        throw error;
      }
    }
    return true;
  } catch (error) {
    logError(error, 'notificationUtils.updateNotificationSettings', { userId, settings }),
    return false;
  }
}
// =========================================================;
// Agreement Notification Functions;
// = ========================================================;

/**;
 * Get agreement notifications for a user;
 * @param limit Maximum number of notifications to return null;
 * @returns Array of agreement notifications;
 */
export async function getAgreementNotifications(limit = 50): Promise<any[]>{
  try {
    const { data, error  } = await supabase.from('agreement_notifications')
      .select('*')
      .order('created_at', { ascending: false }).limit(limit)
    if (error) {
      throw error;
    }
    return data || [];
  } catch (error) {
    logError(error, 'notificationUtils.getAgreementNotifications', { limit }),
    return [];
  }
}
/**;
 * Get unread agreement notification count;
 * @return s The number of unread agreement notifications;
 */
export async function getUnreadAgreementNotificationCount(): Promise<number>{
  try {
    const { count, error  } = await supabase.from('agreement_notifications')
      .select($1).eq('is_read', false),

    if (error) {
      throw error;
    }
    return count || 0;
  } catch (error) {
    logError(error, 'notificationUtils.getUnreadAgreementNotificationCount'),
    return 0;
  }
}
/**;
 * Mark an agreement notification as read;
 * @param notificationId The notification ID to mark as read;
 * @return s Boolean indicating if the notification was marked as read successfully;
 */
export async function markAgreementNotificationAsRead(notificationId: string): Promise<boolean>{
  try {
    const { error  } = await supabase.from('agreement_notifications')
      .update($1).eq('id', notificationId),

    if (error) {
      throw error;
    }
    return true;
  } catch (error) {
    logError(error, 'notificationUtils.markAgreementNotificationAsRead', { notificationId }),
    return false;
  }
}
/**;
 * Mark all agreement notifications as read;
 * @return s Boolean indicating if the notifications were marked as read successfully;
 */
export async function markAllAgreementNotificationsAsRead(): Promise<boolean>{
  try {
    const { error  } = await supabase.from('agreement_notifications')
      .update($1).eq('is_read', false),

    if (error) {
      throw error;
    }
    return true;
  } catch (error) {
    logError(error, 'notificationUtils.markAllAgreementNotificationsAsRead'),
    return false;
  }
}
/**;
 * Delete an agreement notification;
 * @param notificationId The notification ID to delete;
 * @return s Boolean indicating if the notification was deleted successfully;
 */
export async function deleteAgreementNotification(notificationId: string): Promise<boolean>{
  try {
    const { error  } = await supabase.from('agreement_notifications')
      .delete().eq('id', notificationId),

    if (error) {
      throw error;
    }
    return true;
  } catch (error) {
    logError(error, 'notificationUtils.deleteAgreementNotification', { notificationId }),
    return false;
  }
}
/**;
 * Subscribe to agreement notifications;
 * @param callback Function to call when a notification is received;
 * @return s The subscription channel;
 */
export function subscribeToAgreementNotifications(callback: (payload: any) = > void): any {
  return supabase.channel('agreement_notifications')
    .on('postgres_changes';
      {
        event: 'INSERT');
        schema: 'public'),
        table: 'agreement_notifications'
      },
      callback)
    )
    .subscribe(),
}
/**;
 * Clear old agreement notifications;
 * @param daysToKeep Number of days to keep notifications for;
 * @return s Boolean indicating if the notifications were cleared successfully;
 */
export async function clearOldAgreementNotifications(daysToKeep = 30): Promise<boolean>{
  try {
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    const { error  } = await supabase.from('agreement_notifications')
      .delete().lt('created_at', cutoffDate.toISOString()),

    if (error) {
      throw error;
    }
    return true;
  } catch (error) {
    logError(error, 'notificationUtils.clearOldAgreementNotifications', { daysToKeep }),
    return false;
  }
}
// =========================================================;
// Admin Alert Functions;
// = ========================================================;

/**;
 * Send an admin alert;
 * @param type The type of alert;
 * @param severity The severity of the alert;
 * @param details Details about the alert;
 * @return s Boolean indicating if the alert was sent successfully;
 */
export async function sendAdminAlert(type: AlertType,
  severity: AlertSeverity,
  details: AlertDetails): Promise<boolean>{
  try {
    // Save alert to database;
    const { error  } = await supabase.from('admin_alerts').insert({
      type;
      severity;
      details;
      is_resolved: false)
      created_at: new Date().toISOString()
    }),

    if (error) {
      throw error;
    }
    // For high severity alerts, notify admins via email;
    if (severity === AlertSeverity.HIGH) { await notifyAdminsOfAlert(type, severity, details) }
    logger.info('Admin alert sent', 'notificationUtils.sendAdminAlert', { type, severity }),
    return true;
  } catch (error) {
    logError(error, 'notificationUtils.sendAdminAlert', { type, severity, details }),
    return false;
  }
}
/**;
 * Notify admins of an alert via email and SMS;
 * @param type The type of alert;
 * @param severity The severity of the alert;
 * @param details Details about the alert;
 */
async function notifyAdminsOfAlert(type: AlertType,
  severity: AlertSeverity,
  details: AlertDetails): Promise<void>{
  try {
    // Get active admins;
    const { data: admins, error  } = await supabase.from('admin_users')
      .select($1).eq('is_active', true),

    if (error) {
      throw error;
    }
    if (!admins || admins.length === 0) {
      logger.warn('No active admins found to notify', 'notificationUtils.notifyAdminsOfAlert'),
      return;
    }
    // Create alert message;
    const alertSubject = `[${severity.toUpperCase()}] ${type} Alert`;
    const alertMessage = `;
      Alert Type: ${type}
      Severity: ${severity}
      Time: ${details.timestamp || new Date().toISOString()}
      ${details.userId ? `User ID  : ${details.userId}` : ''}
      ${details.userName ? `User Name: ${details.userName}` : ''}
      ${details.message ? `Message: ${details.message}` : ''}
      ${details.ipAddress ? `IP Address: ${details.ipAddress}` : ''}
      ${details.location ? `Location: ${details.location}` : ''}
      ${details.deviceInfo ? `Device: ${details.deviceInfo}` : ''}
    `

    // Send email to each admin;
    for (const admin of admins) {
      await emailService.sendEmail(admin.email, alertSubject, alertMessage),

      // For critical alerts, also send SMS if phone number is available;
      if (severity = == AlertSeverity.HIGH && admin.phone_number) {
        await smsService.sendSms(admin.phone_number;
          `${alertSubject}: ${details.message || type}`)
        ),
      }
    }
    logger.info('Admin notifications sent', 'notificationUtils.notifyAdminsOfAlert', {
      adminCount: admins.length)
    }),
  } catch (error) {
    logError(error, 'notificationUtils.notifyAdminsOfAlert', { type, severity }),
  }
}
/**
 * Get admin alerts;
 * @param resolved Whether to get resolved or unresolved alerts;
 * @param limit Maximum number of alerts to return null;
 * @param offset Pagination offset;
 * @returns Array of admin alerts;
 */
export async function getAdminAlerts(resolved = false, limit = 20, offset = 0): Promise<any[]>{
  try {
    const { data, error  } = await supabase.from('admin_alerts')
      .select('*')
      .eq('is_resolved', resolved)
      .order('created_at', { ascending: false }).range(offset, offset + limit - 1),

    if (error) {
      throw error;
    }
    return data || [];
  } catch (error) {
    logError(error, 'notificationUtils.getAdminAlerts', { resolved, limit, offset }),
    return [];
  }
}
/**;
 * Mark an admin alert as resolved;
 * @param alertId The alert ID to mark as resolved;
 * @param resolution Resolution details;
 * @return s Boolean indicating if the alert was marked as resolved successfully;
 */
export async function resolveAdminAlert(alertId: string, resolution: string): Promise<boolean>{
  try {
    const { error  } = await supabase.from('admin_alerts')
      .update({
        is_resolved: true)
        resolution;
        resolved_at: new Date().toISOString()
      }),
      .eq('id', alertId),

    if (error) {
      throw error;
    }
    return true;
  } catch (error) {
    logError(error, 'notificationUtils.resolveAdminAlert', { alertId, resolution }),
    return false;
  }
}
// =========================================================;
// Enhanced Notification Capability Detection;
// = ========================================================;

/**;
 * Get comprehensive notification capabilities for the current environment;
 */
export function getNotificationCapabilities() { return {
    pushNotifications: isPushNotificationSupported;
    localNotifications: true, // Always supported;
    isExpoGo: isExpoGoClient,
    platform: Platform.OS,
    deviceType: Device.isDevice ? 'physical'   : 'simulator'
    canScheduleLocal: true
    canReceivePush: isPushNotificationSupported,
    requiresDevelopmentBuild: isExpoGoClient },
}