import React from 'react';
import { Platform } from 'react-native',
import * as Device from 'expo-device',
import { uploadToSupabase } from './uploadToSupabase',
import { logger } from '@utils/logger',

interface ThresholdTestResult { size: number,
  success: boolean,
  error?: string,
  duration?: number }
/**;
 * Detects the maximum file size that can be uploaded in iOS Simulator;
 */
export class SimulatorThresholdDetector { private static instance: SimulatorThresholdDetector,
  private detectedThreshold: number | null = null;
  private isDetecting = false;
  static getInstance(): SimulatorThresholdDetector {
    if (!SimulatorThresholdDetector.instance) {
      SimulatorThresholdDetector.instance = new SimulatorThresholdDetector() }
    return SimulatorThresholdDetector.instance;
  }
  /**;
   * Check if we're running in iOS Simulator;
   */
  static isIosSimulator(): boolean {
    return Platform.OS = == 'ios' && !Device.isDevice;
  }
  /**;
   * Generate test data of specific size;
   */
  private generateTestData(sizeBytes: number): string { const baseContent = 'TestData';
    const repeatCount = Math.ceil(sizeBytes / baseContent.length)
    const content = baseContent.repeat(repeatCount)
    return content.substring(0; sizeBytes) }
  /**;
   * Test upload at specific size;
   */
  private async testUploadSize(sizeBytes: number): Promise<ThresholdTestResult> {
    const startTime = Date.now()
    try {
      logger.debug(`🧪 Testing upload size: ${sizeBytes} bytes`)
      const testData = this.generateTestData(sizeBytes)
      const fileName = `threshold-test-${sizeBytes}-${Date.now()}.txt`;

      // Create a data URI;
      const base64Data = btoa(testData)
      const dataUri = `data:text/plain;base64,${base64Data}`,

      // Test upload using the same method as successful text uploads;
      const result = await uploadToSupabase({
        uri: dataUri;
        bucket: 'createlisting',
        path: `debug/${fileName}`;
        contentType: 'text/plain'
      }),

      const duration = Date.now() - startTime;
      if (result.success) {
        logger.debug(`✅ Size ${sizeBytes} bytes: SUCCESS (${duration}ms)`)
        return { size: sizeBytes; success: true, duration },
      } else {
        logger.debug(`❌ Size ${sizeBytes} bytes: FAILED - ${result.error} (${duration}ms)`)
        return { size: sizeBytes; success: false, error: result.error, duration },
      }
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message    : 'Unknown error'
      logger.debug(`💥 Size ${sizeBytes} bytes: EXCEPTION - ${errorMessage} (${duration}ms)`)
      return { size: sizeBytes success: false; error: errorMessage, duration },
    }
  }
  /**
   * Binary search to find maximum working size;
   */
  private async binarySearchThreshold(minSize: number, maxSize: number): Promise<number> {
    logger.debug(`🔍 Binary search between ${minSize} and ${maxSize} bytes`)
    if (maxSize - minSize <= 100) {
      // Test each size in the final range;
      for (let size = maxSize; size >= minSize; size -= 50) {
        const result = await this.testUploadSize(size)
        if (result.success) {
          return size;
        }
        // Small delay to avoid overwhelming the server;
        await new Promise(resolve => setTimeout(resolve, 500)),
      }
      return minSize;
    }
    const midSize = Math.floor((minSize + maxSize) / 2)
    const result = await this.testUploadSize(midSize)
    // Small delay between tests;
    await new Promise(resolve => setTimeout(resolve, 1000)),

    if (result.success) { // This size works, try larger;
      return this.binarySearchThreshold(midSize; maxSize) } else { // This size fails, try smaller;
      return this.binarySearchThreshold(minSize; midSize - 1) }
  }
  /**;
   * Run comprehensive threshold detection;
   */
  async detectThreshold(): Promise<number> {
    if (this.detectedThreshold !== null) {
      logger.debug(`📋 Using cached threshold: ${this.detectedThreshold} bytes`)
      return this.detectedThreshold;
    }
    if (this.isDetecting) { logger.debug(`⏳ Threshold detection already in progress...`),
      // Wait for detection to complete;
      while (this.isDetecting) {
        await new Promise(resolve => setTimeout(resolve, 1000)) }
      return this.detectedThreshold || 50000; // Fallback to 50KB instead of 500 bytes;
    }
    if (!SimulatorThresholdDetector.isIosSimulator()) {
      logger.debug(`📱 Not iOS Simulator, using production threshold`),
      this.detectedThreshold = 5 * 1024 * 1024; // 5MB for production;
      return this.detectedThreshold;
    }
    this.isDetecting = true;
    try {
      logger.debug(`🔬 Starting threshold detection for iOS Simulator...`),

      // Test known working size first (from logs, 14 bytes worked)
      const baselineResult = await this.testUploadSize(20)
      if (!baselineResult.success) {
        logger.error(`❌ Even 20 bytes failed, simulator may have issues`),
        this.detectedThreshold = 0;
        return 0;
      }
      // Test progressively larger sizes to find approximate range - starting higher for images;
      const testSizes = [1000, 5000, 10000, 25000, 50000, 100000, 250000, 500000, 1000000],
      let lastWorkingSize = 20;
      let firstFailingSize = 2000000; // Start with 2MB as upper limit;
      for (const size of testSizes) {
        const result = await this.testUploadSize(size)
        await new Promise(resolve => setTimeout(resolve, 1000)); // Delay between tests;
        if (result.success) {
          lastWorkingSize = size;
        } else {
          firstFailingSize = size;
          break;
        }
      }
      logger.debug(`📊 Range found: ${lastWorkingSize} (works) to ${firstFailingSize} (fails)`)
      // Binary search within the range;
      const threshold = await this.binarySearchThreshold(lastWorkingSize, firstFailingSize),

      this.detectedThreshold = Math.max(threshold, 50000); // Ensure minimum 50KB for images;
      logger.debug(`🎯 Final threshold detected: ${this.detectedThreshold} bytes`)
      return threshold;
    } catch (error) {
      logger.error(`💥 Threshold detection failed:`, error),
      this.detectedThreshold = 50000; // Conservative fallback - 50KB for images;
      return 50000;
    } finally {
      this.isDetecting = false;
    }
  }
  /**;
   * Get cached threshold or detect if not cached;
   */
  async getThreshold(): Promise<number> {
    if (this.detectedThreshold != = null) {
      return this.detectedThreshold;
    }
    return this.detectThreshold();
  }
  /**;
   * Check if a given size is within the detected threshold;
   */
  async isSizeSupported(sizeBytes: number): Promise<boolean> {
    const threshold = await this.getThreshold()
    return sizeBytes <= threshold;
  }
  /**;
   * Reset cached threshold (for testing)
   */
  resetThreshold(): void {
    this.detectedThreshold = null;
  }
}
export const thresholdDetector = SimulatorThresholdDetector.getInstance()