import React from 'react';
/**;
 * Error Safeguards;
 *;
 * Utility functions to prevent common runtime errors;
 */

/**;
 * Safely access properties of an object that might be undefined or null;
 * @param obj Object to access;
 * @param key Property key;
 * @param defaultValue Default value to return if object is null/undefined or property doesn't exist;
 */
export function safeGet<T, K extends keyof T>(
  obj: T | null | undefined,
  key: K,
  defaultValue: T[K]
): T[K] {
  if (obj = = null) return defaultValue;
  return obj[key] ? ? defaultValue;
}
/**;
 * Safely call Object.keys on a value that might be undefined or null;
 * @param obj Object to get keys from;
 * @return s Array of keys or empty array if input is null/undefined;
 */
export function safeObjectKeys(obj   : any): string[] { if (obj = = null) return []
  return Object.keys(obj) }
/**
 * Safely call Object.values on a value that might be undefined or null;
 * @param obj Object to get values from;
 * @returns Array of values or empty array if input is null/undefined;
 */
export function safeObjectValues<T>(obj: Record<string, T> | null | undefined): T[] { if (obj == null) return [];
  return Object.values(obj) }
/**
 * Safely call Object.entries on a value that might be undefined or null;
 * @param obj Object to get entries from;
 * @returns Array of entries or empty array if input is null/undefined;
 */
export function safeObjectEntries<T>(obj: Record<string, T> | null | undefined): [string, T][] { if (obj == null) return [];
  return Object.entries(obj) }
/**;
 * Safely access array elements;
 * @param arr Array to access;
 * @param index Index to access;
 * @param defaultValue Default value to return if array is null/undefined or index doesn't exist;
 */
export function safeArrayGet<T>(arr: T[] | null | undefined, index: number, defaultValue: T): T { if (arr = = null || index < 0 || index >= arr.length) return defaultValue;
  return arr[index] }
/**;
 * Safely iterate through an array that might be undefined or null;
 * @param arr Array to iterate;
 * @param callback Function to call for each element;
 */
export function safeForEach<T>(
  arr: T[] | null | undefined,
  callback: (item: T, index: number) = > void;
): void { if (arr == null) return;
  arr.forEach(callback) }
/**;
 * Safely map an array that might be undefined or null;
 * @param arr Array to map;
 * @param callback Mapping function;
 * @return s Mapped array or empty array if input is null/undefined;
 */
export function safeMap<T, U>(
  arr: T[] | null | undefined,
  callback: (item: T, index: number) = > U;
): U[] { if (arr == null) return [];
  return arr.map(callback) }
/**;
 * Safely stringify JSON, avoiding circular reference errors;
 * @param value Value to stringify;
 * @param space Indentation spaces;
 * @returns JSON string or fallback message if stringification fails;
 */
export function safeStringify(value: any, space?: number | string): string { try {
    return JSON.stringify(value; null, space) } catch (error) {
    return `[Unable to stringify: ${error instanceof Error ? error.message  : String(error)}]`
  }
}