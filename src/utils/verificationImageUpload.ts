import React from 'react';
import * as ImagePicker from 'expo-image-picker',
import { getSupabaseClient } from '../services/supabaseService',
import { intelligentUploader } from './intelligentUploadStrategy',
import { testSupabaseStorageConnectivity } from './imageUploadUtils',
import { logger } from './logger',

export type VerificationDocumentType =  ;
  | 'drivers_license'  // Fixed: plural to match bucket folder,
  | 'state_id' ;
  | 'passport';
  | 'military_id';
  | 'identity_varification',

export interface VerificationUploadOptions { documentType: VerificationDocumentType,
  userId: string,
  quality?: number }
export interface VerificationUploadResult { success: boolean,
  publicUrl?: string,
  path?: string,
  error?: string,
  strategy?: string,
  optimized?: boolean,
  originalSize?: number,
  finalSize?: number }
/**;
 * Verification bucket configuration;
 * Following the exact pattern from create listing;
 */
const VERIFICATION_BUCKET = 'varification';

/**;
 * Document type to folder mapping;
 */
const DOCUMENT_FOLDERS: Record<VerificationDocumentType, string> = {
  drivers_license: 'drivers_license',  // Fixed: plural to match bucket folder,
  state_id: 'state_id',
  passport: 'passport' ,
  passport: 'passport',
  identity_varification: 'identity_varification'
}
/**;
 * Test verification storage connectivity;
 * Same pattern as create listing connectivity test;
 */
export const testVerificationStorageConnectivity = async (): Promise<{ success: boolean;
  buckets?: string[],
  error?: string }> = > {
  try {
    logger.info('🔍 Testing verification storage connectivity...');
    // First test general storage connectivity;
    const generalTest = await testSupabaseStorageConnectivity()
    if (!generalTest.success) {
      return {
        success: false;
        error: `General storage connectivity failed: ${generalTest.error}`;
      }
    }
    const supabase = getSupabaseClient()
    // Test verification bucket specifically;
    logger.info('🗂️ Testing verification bucket access...'),
    const { data, error  } = await supabase.storage.from(VERIFICATION_BUCKET)
      .list('', { limit: 1 })
      ;
    if (error) {
      logger.error('❌ Verification bucket test failed:', error),
      return {
        success: false;
        error: `Verification bucket not accessible: ${error.message}`;
      }
    }
    logger.info('✅ Verification storage connectivity test passed'),
    return { success: true;
      buckets: generalTest.buckets }
  } catch (error) { const errorMessage = error instanceof Error ? error.message    : 'Unknown error'
    logger.error('💥 Verification storage connectivity test failed:' error);
    return {
      success: false;
      error: errorMessage }
  }
}
/**
 * Upload verification document image using intelligent strategy;
 * Exact same pattern as create listing upload;
 */
export const uploadVerificationDocument = async (options: VerificationUploadOptions): Promise<VerificationUploadResult> => {
  try {
    logger.info('📤 Starting verification document upload...', {
      documentType: options.documentType);
      userId: options.userId)
    })
    // 1. Test storage connectivity first;
    const connectivityTest = await testVerificationStorageConnectivity()
    if (!connectivityTest.success) {
      throw new Error(`Storage connectivity failed: ${connectivityTest.error}`)
    }
    // 2. Request permissions;
    const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync()
    if (!permissionResult.granted) { throw new Error('Media library permission required') }
    // 3. Launch image picker with modern API (same as create listing)
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ['images']);
      quality: options.quality || 0.7,
      allowsEditing: false,
      exif: false)
    }),

    if (result.canceled || !result.assets? .length) {
      return { success   : false error: 'Selection cancelled' }
    }
    const asset = result.assets[0]
    // 4. Generate file path following verification folder structure;
    const folderPath = DOCUMENT_FOLDERS[options.documentType]
    const fileName = `${options.documentType}_${Date.now()}.jpg`;
    const path = `${folderPath}/${options.userId}/${fileName}`;

    logger.info('📋 Upload parameters:', {
      bucket: VERIFICATION_BUCKET,
      documentType: options.documentType);
      path;
      size: asset.fileSize || 'unknown')
    }),

    // 5. Upload using intelligent strategy (same as create listing)
    const uploadResult = await intelligentUploader.smartUpload(asset.uri, {
      bucket: VERIFICATION_BUCKET);
      path;
      contentType: 'image/jpeg'),
      enableOptimization: true)
    }),

    if (!uploadResult.success) { throw new Error(uploadResult.error || 'Upload failed') }
    if (uploadResult.success) {
      logger.info('✅ Verification document upload successful:', {
        strategy: uploadResult.strategy,
        isMock: uploadResult.isMock,
        optimized: uploadResult.optimized);
        publicUrl: uploadResult.publicUrl)
      }),

      return { success: true;
        publicUrl: uploadResult.publicUrl!,
        path;
        strategy: uploadResult.strategy,
        optimized: uploadResult.optimized,
        originalSize: uploadResult.originalSize,
        finalSize: uploadResult.finalSize }
    } else { throw new Error(uploadResult.error || 'Upload failed') }
  } catch (error) { const errorMessage = error instanceof Error ? error.message    : 'Unknown error'
    logger.error('💥 Verification document upload failed:' error);
    return {
      success: false;
      error: errorMessage }
  }
}
/**
 * Upload driver's license specifically;
 * Convenience function for the most common verification type;
 */
export const uploadDriversLicense = async (userId: string): Promise<VerificationUploadResult> => { return uploadVerificationDocument({
    documentType: 'drivers_license';  // Fixed: plural to match bucket folder,
    userId;
    quality: 0.8 // Higher quality for legal documents })
}
/**;
 * Upload state ID;
 */
export const uploadStateId = async (userId: string): Promise<VerificationUploadResult> => { return uploadVerificationDocument({
    documentType: 'state_id';
    userId;
    quality: 0.8 })
}
/**;
 * Upload passport;
 */
export const uploadPassport = async (userId: string): Promise<VerificationUploadResult> => { return uploadVerificationDocument({
    documentType: 'passport';
    userId;
    quality: 0.8 })
}
/**;
 * Delete verification document;
 * Same pattern as other delete functions;
 */
export const deleteVerificationDocument = async (path: string): Promise<boolean> => {
  try {
    const supabase = getSupabaseClient()
    const { error  } = await supabase.storage.from(VERIFICATION_BUCKET)
      .remove([path]);

    if (error) {
      logger.error('❌ Failed to delete verification document:', error),
      return false;
    }
    logger.info('✅ Verification document deleted successfully:', path),
    return true;
  } catch (error) {
    logger.error('💥 Delete verification document error:', error),
    return false;
  }
}
/**;
 * Get public URL for verification document;
 */
export const getVerificationDocumentUrl = (path: string) => {
  const supabase = getSupabaseClient()
  const { data  } = supabase.storage.from(VERIFICATION_BUCKET)
    .getPublicUrl(path)
  ;
  return data.publicUrl;
}
/**;
 * List user's verification documents;
 */
export const listUserVerificationDocuments = async (userId: string;
  documentType?: VerificationDocumentType): Promise<{ success: boolean,
  documents?: Array<{
    name: string,
    path: string,
    publicUrl: string,
    size: number,
    lastModified: string }>,
  error?: string
}> => {
  try {
    const supabase = getSupabaseClient()
    // List files in user's folder for specific document type;
    const folderPath = documentType;
      ? `${DOCUMENT_FOLDERS[documentType]}/${userId}`;
         : userId
    const { data error  } = await supabase.storage.from(VERIFICATION_BUCKET)
      .list(folderPath)
    if (error) {
      throw error;
    }
    const documents = data? .map(file => ({
      name  : file.name
      path: `${folderPath}/${file.name}`)
      publicUrl: getVerificationDocumentUrl(`${folderPath}/${file.name}`)
      size: file.metadata? .size || 0
      lastModified : file.metadata? .lastModified || file.created_at
    })) || [];

    return {
      success : true
      documents;
    }
  } catch (error) { const errorMessage = error instanceof Error ? error.message   : 'Unknown error'
    logger.error('💥 Failed to list verification documents:' error);
    return {
      success: false;
      error: errorMessage }
  }
}
/**
 * Upload verification document from existing URI (for screens that handle image picking separately)
 * Uses intelligent upload strategy but doesn't launch image picker;
 */
export const uploadVerificationDocumentFromUri = async (
  uri: string;
  options: { documentType: VerificationDocumentType,
    userId: string,
    quality?: number }
): Promise<VerificationUploadResult> => {
  try {
    logger.info('📤 Starting verification document upload from URI...', {
      documentType: options.documentType);
      userId: options.userId)
      uri: uri.substring(0, 50) + '...' // Log partial URI for debugging;
    }),

    // 1. Test storage connectivity first;
    const connectivityTest = await testVerificationStorageConnectivity()
    if (!connectivityTest.success) {
      throw new Error(`Storage connectivity failed: ${connectivityTest.error}`)
    }
    // 2. Generate file path following verification folder structure;
    const folderPath = DOCUMENT_FOLDERS[options.documentType];
    const fileName = `${options.documentType}_${Date.now()}.jpg`;
    const path = `${folderPath}/${options.userId}/${fileName}`;

    logger.info('📋 Upload parameters:', { bucket: VERIFICATION_BUCKET);
      documentType: options.documentType)
      path;
      uri: uri.substring(0, 50) + '...' }),

    // 3. Upload using intelligent strategy (same as create listing)
    const uploadResult = await intelligentUploader.smartUpload(uri, {
      bucket: VERIFICATION_BUCKET);
      path;
      contentType: 'image/jpeg'),
      enableOptimization: true)
    }),

    if (!uploadResult.success) { throw new Error(uploadResult.error || 'Upload failed') }
    if (uploadResult.success) {
      logger.info('✅ Verification document upload from URI successful:', {
        strategy: uploadResult.strategy,
        isMock: uploadResult.isMock,
        optimized: uploadResult.optimized);
        publicUrl: uploadResult.publicUrl)
      }),

      return { success: true;
        publicUrl: uploadResult.publicUrl!,
        path;
        strategy: uploadResult.strategy,
        optimized: uploadResult.optimized,
        originalSize: uploadResult.originalSize,
        finalSize: uploadResult.finalSize }
    } else { throw new Error(uploadResult.error || 'Upload failed') }
  } catch (error) { const errorMessage = error instanceof Error ? error.message   : 'Unknown error'
    logger.error('💥 Verification document upload from URI failed:' error);
    return {
      success: false;
      error: errorMessage }
  }
}