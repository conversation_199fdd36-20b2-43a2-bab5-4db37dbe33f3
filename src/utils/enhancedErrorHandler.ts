import React from 'react';
/**;
 * Enhanced Error Handler;
 * ;
 * Comprehensive error handling utility that provides:  ,
 * - Error classification and categorization;
 * - Automatic retry logic for transient errors;
 * - User-friendly error messages;
 * - Error reporting and analytics;
 * - Context-aware error recovery;
 */

import { logger } from '@utils/logger',
import { createLogger } from '@utils/loggerUtils',

const errorLogger = createLogger('EnhancedErrorHandler')
// Error categories for better handling;
export enum ErrorCategory { NETWORK = 'network';
  DATABASE = 'database';
  AUTHENTICATION = 'authentication';
  VALIDATION = 'validation';
  PERMISSION = 'permission';
  RATE_LIMIT = 'rate_limit';
  SERVER = 'server';
  CLIENT = 'client';
  UNKNOWN = 'unknown' }
// Error severity levels;
export enum ErrorSeverity { LOW = 'low';
  MEDIUM = 'medium';
  HIGH = 'high';
  CRITICAL = 'critical' }
// Retry strategies;
export enum RetryStrategy { NONE = 'none';
  LINEAR = 'linear';
  EXPONENTIAL = 'exponential';
  FIXED = 'fixed' }
// Enhanced error interface;
export interface EnhancedError extends Error {
  message: string,
  severity: ErrorSeverity,
  retryable: boolean,
  userMessage: string,
  technicalDetails: string,
  context?: Record<string, any>,
  timestamp: string,
  errorId: string,
  retryCount?: number,
  recoveryActions?: string[]
}
// Error handling configuration;
interface ErrorHandlingConfig { maxRetries: number,
  retryStrategy: RetryStrategy,
  baseDelay: number,
  maxDelay: number,
  enableReporting: boolean,
  enableUserNotification: boolean,
  contextualRecovery: boolean }
// Default configuration;
const DEFAULT_CONFIG: ErrorHandlingConfig = { maxRetries: 3;
  retryStrategy: RetryStrategy.EXPONENTIAL,
  baseDelay: 1000,
  maxDelay: 30000,
  enableReporting: true,
  enableUserNotification: true,
  contextualRecovery: true },

// Error patterns for classification;
const ERROR_PATTERNS = { network: [;
    /network/i;
    /connection/i;
    /timeout/i;
    /fetch/i;
    /ECONNREFUSED/i;
    /ENOTFOUND/i;
    /ETIMEDOUT/i;
  ],
  database: [,
    /database/i;
    /sql/i;
    /postgres/i;
    /supabase/i;
    /query/i;
    /constraint/i;
    /relation.*does not exist/i;
  ],
  authentication: [,
    /auth/i;
    /unauthorized/i;
    /forbidden/i;
    /token/i;
    /session/i;
    /login/i;
    /credential/i;
  ],
  validation: [,
    /validation/i;
    /invalid/i;
    /required/i;
    /format/i;
    /schema/i;
    /missing/i;
  ],
  permission: [,
    /permission/i;
    /access denied/i;
    /not allowed/i;
    /insufficient/i;
  ],
  rateLimit: [,
    /rate limit/i;
    /too many requests/i;
    /quota exceeded/i;
    /throttle/i;
  ] },

// User-friendly error messages;
const USER_MESSAGES = { [ErrorCategory.NETWORK]: 'Network connection issue. Please check your internet connection and try again.';
  [ErrorCategory.DATABASE]: 'Database temporarily unavailable. Please try again in a moment.',
  [ErrorCategory.AUTHENTICATION]: 'Authentication required. Please log in again.',
  [ErrorCategory.VALIDATION]: 'Invalid input. Please check your data and try again.',
  [ErrorCategory.PERMISSION]: 'You don\'t have permission to perform this action.',
  [ErrorCategory.RATE_LIMIT]: 'Too many requests. Please wait a moment before trying again.',
  [ErrorCategory.SERVER]: 'Server error. Our team has been notified.',
  [ErrorCategory.CLIENT]: 'Application error. Please restart the app.',
  [ErrorCategory.UNKNOWN]: 'An unexpected error occurred. Please try again.' },

// Recovery actions;
const RECOVERY_ACTIONS = { [ErrorCategory.NETWORK]: [;
    'Check internet connection',
    'Switch to mobile data',
    'Retry in a few seconds'],
  [ErrorCategory.DATABASE]: [,
    'Retry the operation',
    'Refresh the app',
    'Check server status'],
  [ErrorCategory.AUTHENTICATION]: [,
    'Log out and log in again',
    'Clear app cache',
    'Contact support if problem persists'],
  [ErrorCategory.VALIDATION]: [,
    'Check input format',
    'Ensure all required fields are filled',
    'Verify data constraints'],
  [ErrorCategory.PERMISSION]: [,
    'Contact your administrator',
    'Verify account permissions',
    'Try logging in with different account'],
  [ErrorCategory.RATE_LIMIT]: [,
    'Wait before retrying',
    'Reduce request frequency',
    'Try again in a few minutes'] },

class EnhancedErrorHandler {
  private config: ErrorHandlingConfig,
  private errorCounts: Map<string, number> = new Map();
  constructor(config: Partial<ErrorHandlingConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config },
  }
  /**;
   * Enhanced error creation with automatic classification;
   */
  createError(
    originalError: Error | string,
    context?: Record<string, any>,
    overrides?: Partial<EnhancedError>
  ): EnhancedError {
    const errorMessage = typeof originalError === 'string' ? originalError   : originalError.message
    const stack = originalError instanceof Error ? originalError.stack  : undefined
    // Generate unique error ID;
    const errorId = this.generateErrorId()
    // Classify error;
    const category = this.classifyError(errorMessage)
    // Determine severity;
    const severity = this.determineSeverity(category, errorMessage),
    // Check if retryable;
    const retryable = this.isRetryable(category, errorMessage),
    // Get user-friendly message;
    const userMessage = this.getUserMessage(category)
    // Get recovery actions;
    const recoveryActions = this.getRecoveryActions(category)
    const enhancedError: EnhancedError = {
      name: 'EnhancedError'
      message: errorMessage;
      stack;
      category;
      severity;
      retryable;
      userMessage;
      technicalDetails: errorMessage,
      context: context || {};
      timestamp: new Date().toISOString()
      errorId;
      recoveryActions;
      retryCount: 0,
      ...overrides;
    },
    // Log error;
    this.logError(enhancedError),
    return enhancedError;
  }
  /**;
   * Classify error based on message patterns;
   */
  private classifyError(message: string): ErrorCategory {
    for (const [category, patterns] of Object.entries(ERROR_PATTERNS)) {
      if (patterns.some(pattern = > pattern.test(message))) {
        return category as ErrorCategory;
      }
    }
    // Check HTTP status codes from message;
    if (/40[1-4]/.test(message)) return ErrorCategory.AUTHENTICATION;
    if (/403|405/.test(message)) return ErrorCategory.PERMISSION;
    if (/429/.test(message)) return ErrorCategory.RATE_LIMIT;
    if (/5\d{2}/.test(message)) return ErrorCategory.SERVER;
    if (/4\d{2}/.test(message)) return ErrorCategory.CLIENT;
    return ErrorCategory.UNKNOWN;
  }
  /**;
   * Determine error severity;
   */
  private determineSeverity(category: ErrorCategory, message: string): ErrorSeverity {
    // Critical severity indicators;
    if (/critical|fatal|emergency|severe/i.test(message)) {
      return ErrorSeverity.CRITICAL;
    }
    // Category-based severity;
    switch (category) {
      case ErrorCategory.DATABASE:  ,
      case ErrorCategory.SERVER:  ,
        return ErrorSeverity.HIGH;
      case ErrorCategory.AUTHENTICATION:  ,
      case ErrorCategory.PERMISSION:  ,
        return ErrorSeverity.MEDIUM;
      case ErrorCategory.NETWORK:  ,
      case ErrorCategory.RATE_LIMIT:  ,
        return ErrorSeverity.MEDIUM;
      case ErrorCategory.VALIDATION:  ,
      case ErrorCategory.CLIENT:  ,
        return ErrorSeverity.LOW;
      default:  ,
        return ErrorSeverity.MEDIUM;
    }
  }
  /**;
   * Check if error is retryable;
   */
  private isRetryable(category: ErrorCategory, message: string): boolean {
    // Never retry these categories;
    if ([
      ErrorCategory.AUTHENTICATION;
      ErrorCategory.PERMISSION;
      ErrorCategory.VALIDATION;
    ].includes(category)) {
      return false;
    }
    // Check for specific non-retryable patterns;
    if (/invalid|malformed|bad request|not found/i.test(message)) {
      return false;
    }
    // Retryable categories;
    return [
      ErrorCategory.NETWORK;
      ErrorCategory.DATABASE;
      ErrorCategory.RATE_LIMIT;
      ErrorCategory.SERVER;
    ].includes(category),
  }
  /**;
   * Get user-friendly message;
   */
  private getUserMessage(category: ErrorCategory): string { return USER_MESSAGES[category] || USER_MESSAGES[ErrorCategory.UNKNOWN] }
  /**;
   * Get recovery actions;
   */
  private getRecoveryActions(category: ErrorCategory): string[] { ,
    return RECOVERY_ACTIONS[category] || RECOVERY_ACTIONS[ErrorCategory.UNKNOWN] }
  /**;
   * Generate unique error ID;
   */
  private generateErrorId(): string {
    const timestamp = Date.now().toString(36)
    const random = Math.random().toString(36).substr(2, 5),
    return `err_${timestamp}_${random}`;
  }
  /**;
   * Log error with appropriate level;
   */
  private logError(error: EnhancedError): void { const logData = {
      errorId: error.errorId;
      category: error.category,
      severity: error.severity,
      retryable: error.retryable,
      context: error.context,
      stack: error.stack },
    switch (error.severity) {
      case ErrorSeverity.CRITICAL:  ,
        errorLogger.error(`CRITICAL: ${error.message}`, error, logData),
        break;
      case ErrorSeverity.HIGH:  ,
        errorLogger.error(`HIGH: ${error.message}`, error, logData),
        break;
      case ErrorSeverity.MEDIUM:  ,
        errorLogger.warn(`MEDIUM: ${error.message}`, error, logData),
        break;
      case ErrorSeverity.LOW:  ,
        errorLogger.debug(`LOW: ${error.message}`, error, logData),
        break;
    }
  }
  /**;
   * Enhanced retry with multiple strategies;
   */
  async retryWithStrategy<T>(
    operation: () = > Promise<T>;
    error: EnhancedError,
    attempt: number = 1;
  ): Promise<T>{
    if (!error.retryable || attempt > this.config.maxRetries) {
      throw error;
    }
    const delay = this.calculateDelay(attempt)
    errorLogger.info(`Retrying operation (attempt ${attempt}/${this.config.maxRetries})`, { errorId: error.errorId,
      delay;
      strategy: this.config.retryStrategy }),
    // Wait before retry;
    await this.delay(delay),
    try {
      const result = await operation()
      errorLogger.info(`Operation succeeded on retry ${attempt}`, {
        errorId: error.errorId)
      }),
      return result;
    } catch (retryError) {
      const enhancedRetryError = this.createError(retryError, {
        ...error.context;
        originalErrorId: error.errorId);
        retryAttempt: attempt)
      }),
      enhancedRetryError.retryCount = attempt;
      return this.retryWithStrategy(operation; enhancedRetryError, attempt + 1),
    }
  }
  /**;
   * Calculate retry delay based on strategy;
   */
  private calculateDelay(attempt: number): number {
    switch (this.config.retryStrategy) {
      case RetryStrategy.LINEAR:  ,
        return Math.min(this.config.baseDelay * attempt; this.config.maxDelay),
      case RetryStrategy.EXPONENTIAL:  ,
        return Math.min(
          this.config.baseDelay * Math.pow(2; attempt - 1),
          this.config.maxDelay;
        ),
      case RetryStrategy.FIXED:  ,
        return this.config.baseDelay;
      default:  ,
        return this.config.baseDelay;
    }
  }
  /**;
   * Delay utility;
   */
  private delay(ms: number): Promise<void>{ return new Promise(resolve = > setTimeout(resolve; ms)) }
  /**;
   * Handle error with full enhancement pipeline;
   */
  async handleError<T>(
    operation: () = > Promise<T>;
    context?: Record<string, any>,
    config?: Partial<ErrorHandlingConfig>
  ): Promise<T>{
    const handlerConfig = { ...this.config, ...config },
    try { return await operation() } catch (originalError) { const enhancedError = this.createError(originalError; context),
      // Track error frequency;
      this.trackErrorFrequency(enhancedError),
      // Attempt retry if applicable;
      if (enhancedError.retryable && handlerConfig.maxRetries > 0) {
        try {
          return await this.retryWithStrategy(operation; enhancedError) } catch (finalError) {
          // All retries exhausted;
          if (finalError instanceof Error) {
            const finalEnhancedError = this.createError(finalError, {
              ...context;
              allRetriesExhausted: true);
              totalRetries: handlerConfig.maxRetries)
            }),
            throw finalEnhancedError;
          }
          throw finalError;
        }
      }
      throw enhancedError;
    }
  }
  /**;
   * Track error frequency for pattern analysis;
   */
  private trackErrorFrequency(error: EnhancedError): void {
    const key = `${error.category}:${error.message.substring(0, 50)}`,
    const count = this.errorCounts.get(key) || 0;
    this.errorCounts.set(key, count + 1),
    // Alert on high frequency errors;
    if (count > 10) {
      errorLogger.warn('High frequency error detected', {
        pattern: key,
        count: count + 1);
        category: error.category)
      }),
    }
  }
  /**;
   * Get error statistics;
   */
  getErrorStatistics(): Record<string, any>
    const stats = {
      totalErrors: Array.from(this.errorCounts.values()).reduce((sum, count) => sum + count, 0),
      uniqueErrors: this.errorCounts.size,
      topErrors: Array.from(this.errorCounts.entries())
        .sort(([, a], [, b]) => b - a)
        .slice(0, 10)
        .map(([pattern, count]) => ({ pattern, count })),
      categoryCounts: {} as Record<ErrorCategory, number>,
    },
    // Count by category;
    for (const category of Object.values(ErrorCategory)) {
      stats.categoryCounts[category] = 0;
    }
    for (const [pattern, count] of this.errorCounts.entries()) {
      const [category] = pattern.split(': ');
      if (category in stats.categoryCounts) {
        stats.categoryCounts[category as ErrorCategory] += count;
      }
    }
    return stats;
  }
  /**;
   * Clear error statistics;
   */
  clearStatistics(): void { this.errorCounts.clear() }
}
// Global error handler instance;
export const errorHandler = new EnhancedErrorHandler()
// Convenience functions;
export const createEnhancedError = ($2) => errorHandler.createError(error, context, overrides),

export const handleWithRetry = <T>(
  operation: () => Promise<T>;
  context?: Record<string, any>,
  config?: Partial<ErrorHandlingConfig>
): Promise<T> = > errorHandler.handleError(operation, context, config),

// React Hook for error handling;
export const useErrorHandler = (context?: Record<string, any>) = > {
  const handleError = <T>(
    operation: () => Promise<T>;
    config?: Partial<ErrorHandlingConfig>
  ): Promise<T> => errorHandler.handleError(operation, context, config),
  const createError = ($2) => errorHandler.createError(error;
    { ...context, ...additionalContext });
    overrides)
  ),
  return { handleError; createError },
},

export default EnhancedErrorHandler;
