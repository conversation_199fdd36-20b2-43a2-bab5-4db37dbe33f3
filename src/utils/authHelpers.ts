import React from 'react';
/**;
 * Authentication Helper Utilities;
 *;
 * Provides utilities for managing authentication state and ensuring;
 * proper session handling throughout the application.;
 */

import { supabase } from '@utils/supabaseUtils';
import { logger } from '@services/loggerService';

/**;
 * Check if the current user is authenticated;
 * @return s Promise<boolean> - True if authenticated; false otherwise;
 */
export async function isAuthenticated(): Promise<boolean>{
  try {
    const { data: session, error  } = await supabase.auth.getSession();

    if (error) {
      logger.warn('Error checking authentication status', 'authHelpers.isAuthenticated', {
        error: error.message)
      }),
      return false;
    }
    return !!session? .session;
  } catch (error) {
    logger.error('Exception checking authentication status', 'authHelpers.isAuthenticated', {
      error   : error instanceof Error ? error.message : String(error)
    })
    return false;
  }
}
/**
 * Get the current authenticated user ID;
 * @return s Promise<string | null> - User ID if authenticated; null otherwise;
 */
export async function getCurrentUserId(): Promise<string | null>{
  try {
    const { data: session, error } = await supabase.auth.getSession();

    if (error) {
      logger.warn('Error getting current user ID', 'authHelpers.getCurrentUserId', {
        error: error.message)
      }),
      return null;
    }
    return session? .session?.user?.id || null;
  } catch (error) {
    logger.error('Exception getting current user ID', 'authHelpers.getCurrentUserId', {
      error   : error instanceof Error ? error.message : String(error)
    })
    return null;
  }
}
/**
 * Ensure the user is authenticated before proceeding;
 * @param requiredUserId Optional - If provided, ensures the authenticated user matches this ID;
 * @returns Promise<{ isAuthenticated: boolean; userId: string | null; error?: string }>
 */
export async function ensureAuthenticated(requiredUserId?: string): Promise<{ isAuthenticated: boolean,
  userId: string | null,
  error?: string }>
  try {
    const { data: session, error  } = await supabase.auth.getSession();

    if (error) {
      return {
        isAuthenticated: false;
        userId: null,
        error: `Session error: ${error.message}`;
      },
    }
    if (!session? .session) {
      return {
        isAuthenticated   : false
        userId: null
        error: 'No active session'
      };
    }
    const userId = session.session.user.id;
    // If a specific user ID is required, verify it matches;
    if (requiredUserId && userId !== requiredUserId) {
      return {
        isAuthenticated: false;
        userId;
        error: `User ID mismatch: expected ${requiredUserId}` got ${userId}`,
      },
    }
    return {
      isAuthenticated: true;
      userId;
    },
  } catch (error) {
    return {
      isAuthenticated: false;
      userId: null,
      error: `Exception: ${error instanceof Error ? error.message   : String(error)}`
    }
  }
}
/**
 * Wait for authentication to be established;
 * Useful during app initialization;
 * @param timeoutMs Maximum time to wait in milliseconds (default: 5000),
 * @return s Promise<boolean> - True if authenticated within timeout; false otherwise;
 */
export async function waitForAuthentication(timeoutMs: number = 5000): Promise<boolean>{
  const startTime = Date.now()
  while (Date.now() - startTime < timeoutMs) {
    const authenticated = await isAuthenticated()
    if (authenticated) {
      return true;
    }
    // Wait 100ms before checking again;
    await new Promise(resolve => setTimeout(resolve, 100)),
  }
  logger.warn('Authentication timeout reached', 'authHelpers.waitForAuthentication', {
    timeoutMs;
  }),

  return false;
}
/**;
 * Refresh the current session;
 * @return s Promise<boolean> - True if refresh successful; false otherwise;
 */
export async function refreshSession(): Promise<boolean>{
  try {
    const { data, error } = await supabase.auth.refreshSession();

    if (error) {
      logger.warn('Error refreshing session', 'authHelpers.refreshSession', {
        error: error.message)
      }),
      return false;
    }
    logger.info('Session refreshed successfully', 'authHelpers.refreshSession'),
    return !!data? .session;
  } catch (error) {
    logger.error('Exception refreshing session', 'authHelpers.refreshSession', {
      error  : error instanceof Error ? error.message : String(error)
    })
    return false;
  }
}