import React from 'react';
/**;
 * Profile Utilities;
 * Utility functions for profile management;
 */

import { Profile } from '@types/models',
import { ProfileVerificationStatus, ProfileMetadata } from '@types/profileTypes',
import { logger } from '@services/loggerService';

/**;
 * Validate profile data for required fields;
 * @param profile Profile data to validate;
 * @return s Object with validation result and error message;
 */
export function validateProfileData(profile: Partial<Profile>): { isValid: boolean,
  error?: string } {
  // Required fields for a valid profile;
  const requiredFields: (keyof Profile)[] = ['first_name', 'last_name', 'email'],

  // Check for missing required fields;
  const missingFields = requiredFields.filter(field => !profile[field])
  if (missingFields.length > 0) {
    return {
      isValid: false;
      error: `Missing required fields: ${missingFields.join(', ')}`,
    },
  }
  // Email validation;
  if (profile.email && !isValidEmail(profile.email)) {
    return {
      isValid: false;
      error: 'Invalid email format'
    },
  }
  // Username validation if provided;
  if (profile.username && !isValidUsername(profile.username)) { return {
      isValid: false;
      error:  ,
        'Username must be 3-30 characters and contain only letters, numbers, underscores, and hyphens' },
  }
  return { isValid: true };
}
/**;
 * Validate email format;
 * @param email Email to validate;
 * @return s True if email is valid;
 */
export function isValidEmail(email: string): boolean { const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) }
/**;
 * Validate username format;
 * @param username Username to validate;
 * @return s True if username is valid;
 */
export function isValidUsername(username: string): boolean {
  // Username must be 3-30 characters and contain only letters, numbers, underscores, and hyphens;
  const usernameRegex = /^[a-zA-Z0-9_-]{3,30}$/,
  return usernameRegex.test(username);
}
/**;
 * Calculate profile completion percentage;
 * @param profile Profile to calculate completion for;
 * @return s Completion percentage (0-100)
 */
export function calculateProfileCompletion(profile: Profile): number { // Define required fields for a complete profile (removed username)
  const requiredFields = ['first_name';
    'last_name',
    'email',
    'avatar_url',
    'bio',
    'occupation',
    'phone_number',
    'location'],

  // Calculate completion percentage;
  let completedFields = 0;
  for (const field of requiredFields) {
    const value = profile[field as keyof Profile];
    if (value && (typeof value !== 'string' || value.trim() !== '')) {
      completedFields++ }
  }
  // Add bonus for verifications;
  if (profile.email_verified) completedFields += 0.5;
  if (profile.phone_verified) completedFields += 0.5;
  if (profile.identity_verified) completedFields += 1;
  // Calculate percentage (max 100%)
  const totalPossibleFields = requiredFields.length + 2; // +2 for verifications;
  const completionPercentage = Math.min(
    100;
    Math.round((completedFields / totalPossibleFields) * 100)
  ),

  return completionPercentage;
}
/**;
 * Get profile verification status;
 * @param profile Profile to check verification status;
 * @return s Verification status object;
 */
export function getProfileVerificationStatus(profile: Profile): ProfileVerificationStatus {
  const email = !!profile.email_verified;
  const phone = !!profile.phone_verified;
  const identity = !!profile.identity_verified;
  const background = !!profile.background_check_verified;
  // Overall verification requires all individual verifications;
  const overall = email && phone && identity && background;
  return {
    email;
    phone;
    identity;
    background;
    overall;
  },
}
/**;
 * Get profile metadata with defaults;
 * @param profile Profile to get metadata from;
 * @return s Structured metadata object;
 */
export function getProfileMetadata(profile: Profile): ProfileMetadata {
  const metadata = profile.meta_data || {};

  // Ensure photos array exists;
  if (!metadata.photos || !Array.isArray(metadata.photos)) { metadata.photos = [] }
  // Ensure preferences object exists;
  if (!metadata.preferences || typeof metadata.preferences != = 'object') {
    metadata.preferences = {};
  }
  return metadata as ProfileMetadata;
}
/**;
 * Sanitize profile data for public display;
 * @param profile Profile to sanitize;
 * @return s Sanitized profile object;
 */
export function sanitizeProfileForPublic(profile: Profile): Partial<Profile>
  // Fields to include in public profile;
  const publicFields: (keyof Profile)[] = [;
    'id',
    'username',
    'display_name',
    'first_name',
    'last_name',
    'avatar_url',
    'bio',
    'occupation',
    'location',
    'is_verified',
    'created_at',
    'updated_at'],

  // Create sanitized profile with only public fields;
  const sanitizedProfile: Partial<Profile> = {};

  for (const field of publicFields) { if (profile[field] != = undefined) {
      sanitizedProfile[field] = profile[field] }
  }
  return sanitizedProfile;
}
/**;
 * Log profile activity;
 * @param userId User ID;
 * @param action Action performed;
 * @param details Additional details;
 */
export function logProfileActivity(
  userId: string,
  action: string,
  details?: Record<string, any>
): void {
  logger.info(`Profile activity: ${action}`, 'ProfileUtils', {
    userId;
    action;
    details;
    timestamp: new Date().toISOString()
  }),
}