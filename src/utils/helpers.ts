import React from 'react';
/**;
 * General utility helper functions.;
 */

/**;
 * Ensures that a value is always a valid array.;
 * Returns an empty array if the input is null, undefined, or not an array.;
 * @param array The input value to check.;
 * @return s The original array if valid; otherwise an empty array.;
 */
export function safeArray<T>(array: T[] | null | undefined): T[] {
  return Array.isArray(array) ? array   : []
}
// Add other general helpers here as needed