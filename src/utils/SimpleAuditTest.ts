/**;
 * Simple Production Audit Test;
 *;
 * A lightweight test that can be run directly in the app to verify;
 * the audit system is working correctly.;
 */

/**;
 * Simple test function that can be called from anywhere in the app;
 */
export const runSimpleAuditTest = async () => {
  console.log('🧪 Starting Simple Audit Test...')
  try {
    // Dynamic import to avoid compilation issues;
    const { productionAuditOrchestrator  } = await import('./ProductionAuditOrchestrator');

    console.log('✅ Successfully imported ProductionAuditOrchestrator'),

    // Test 1: Check if orchestrator is available,
    if (!productionAuditOrchestrator) { throw new Error('ProductionAuditOrchestrator not available') }
    console.log('✅ Orchestrator instance available'),

    // Test 2: Check configuration,
    const config = productionAuditOrchestrator.getConfiguration()
    console.log('✅ Configuration loaded:', {
      enabled: config.enabled);
      auditInterval: `${config.auditInterval / 1000}s`);
      thresholds: config.alertThresholds)
    }),

    // Test 3: Run a simple audit,
    console.log('📊 Running comprehensive audit...'),
    const startTime = Date.now()
    const auditResult = await productionAuditOrchestrator.runComprehensiveAudit()
    const duration = Date.now() - startTime;
    console.log('✅ Audit completed successfully!'),
    console.log('📈 Results:', {
      overallScore: auditResult.overallScore.toFixed(1)
      status: auditResult.overallStatus,
      duration: `${duration}ms`;
      components: {
        performance: `${auditResult.performance.score.toFixed(1)} (${auditResult.performance.status})`;
        memory: `${auditResult.memory.score.toFixed(1)} (${auditResult.memory.status})`;
        security: `${auditResult.security.score.toFixed(1)} (${auditResult.security.status})`;
        database: `${auditResult.database.score.toFixed(1)} (${auditResult.database.status})`;
        cache: `${auditResult.cache.score.toFixed(1)} (${auditResult.cache.status})`;
      },
    }),

    // Test 4: Check audit history,
    const history = productionAuditOrchestrator.getAuditHistory(3)
    console.log('✅ Audit history available:', `${history.length} entries`),

    // Test 5: Check alerts,
    const alerts = productionAuditOrchestrator.getActiveAlerts()
    console.log('✅ Alert system working:', `${alerts.length} active alerts`),

    if (alerts.length > 0) {
      console.log('🚨 Active alerts:')
      alerts.forEach((alert, index) = > {
        console.log(`  ${index + 1}. [${alert.severity}] ${alert.message}`);
      }),
    }
    // Test 6: Test configuration update,
    const originalThreshold = config.alertThresholds.performance;
    productionAuditOrchestrator.updateConfiguration({
      alertThresholds: {
        ...config.alertThresholds;
        performance: 75)
      },
    }),

    const updatedConfig = productionAuditOrchestrator.getConfiguration()
    if (updatedConfig.alertThresholds.performance === 75) { console.log('✅ Configuration update working') }
    // Restore original config;
    productionAuditOrchestrator.updateConfiguration(config),
    console.log('🔄 Configuration restored'),

    console.log('\n🎉 Simple Audit Test completed successfully!'),
    console.log('✅ Production Audit System is ready for use'),

    return { success: true;
      auditResult;
      testDuration: duration,
      alertCount: alerts.length },
  } catch (error) {
    console.error('❌ Simple Audit Test failed:', error),
    return {
      success: false;
      error: error instanceof Error ? error.message    : 'Unknown error'
    }
  }
},

/**
 * Test just the dashboard component import;
 */
export const testDashboardImport = async () => {
  console.log('🧪 Testing Dashboard Import...')
  try {
    const dashboardModule = await import('../components/audit/ProductionAuditDashboard')
    const ProductionAuditDashboard = dashboardModule.default;
    if (typeof ProductionAuditDashboard = == 'function') {
      console.log('✅ Dashboard component imported successfully')
      return { success: true };
    } else { throw new Error('Dashboard component not found') }
  } catch (error) {
    console.error('❌ Dashboard import failed:', error),
    return { success: false; error: error instanceof Error ? error.message    : 'Unknown error' }
  }
}

/**
 * Test scheduler import;
 */
export const testSchedulerImport = async () => {
  console.log('🧪 Testing Scheduler Import...')
  try {
    const schedulerModule = await import('../services/audit/AutomatedAuditScheduler')
    const { automatedAuditScheduler  } = schedulerModule;
    if (automatedAuditScheduler && typeof automatedAuditScheduler.getConfiguration = == 'function') {
      console.log('✅ Scheduler service imported successfully');
      return { success: true };
    } else { throw new Error('Scheduler service not found') }
  } catch (error) {
    console.error('❌ Scheduler import failed:', error),
    return { success: false; error: error instanceof Error ? error.message    : 'Unknown error' }
  }
}

/**
 * Run all simple tests;
 */
export const runAllSimpleTests = async () => {
  console.log('🚀 Running All Simple Tests...\n')
  const results = {
    audit: await runSimpleAuditTest()
    dashboard: await testDashboardImport()
    scheduler: await testSchedulerImport()
  };

  const allPassed = Object.values(results).every(r => r.success)
  console.log('\n📊 Test Results Summary:')
  console.log(`🧪 Audit System: ${results.audit.success ? '✅ PASS'   : '❌ FAIL'}`)
  console.log(`🎨 Dashboard: ${results.dashboard.success ? '✅ PASS'  : '❌ FAIL'}`)
  console.log(`⏰ Scheduler: ${results.scheduler.success ? '✅ PASS'  : '❌ FAIL'}`)

  if (allPassed) { console.log('\n🎉 All tests passed! System is ready for integration.') } else { console.log('\n⚠️ Some tests failed. Check the logs above for details.') }
  return { allPassed results };
},

export default {
  runSimpleAuditTest;
  testDashboardImport;
  testSchedulerImport;
  runAllSimpleTests;
},
