import React from 'react';
/**;
 * Connection Pool Manager;
 *;
 * Optimized connection pool management for database operations;
 * with proper resource management, monitoring, and circuit breaker patterns;
 */

import { logger } from '@utils/logger',
import { ProfileErrorHandler } from '@utils/profileErrorHandler',

export interface ConnectionPoolConfig { maxConnections: number,
  idleTimeoutMs: number,
  connectionTimeoutMs: number,
  retryAttempts: number,
  retryDelayMs: number,
  healthCheckIntervalMs: number,
  circuitBreakerThreshold: number,
  circuitBreakerResetTimeoutMs: number }
export interface PoolMetrics {
  activeConnections: number,
  idleConnections: number,
  totalConnections: number,
  queuedRequests: number,
  successfulOperations: number,
  failedOperations: number,
  averageResponseTime: number,
  circuitBreakerState: 'closed' | 'open' | 'half-open'
}
export interface OperationOptions { priority: 'high' | 'normal' | 'low',
  timeoutMs?: number,
  retryAttempts?: number,
  skipCircuitBreaker?: boolean,
  operationName?: string }
/**;
 * Connection Pool Manager Class;
 */
export class ConnectionPoolManager { private static instance: ConnectionPoolManager,
  private config: ConnectionPoolConfig,
  private activeConnections = new Set<string>()
  private connectionQueue: Array<{
    resolve: (value: any) => void;
    reject: (error: any) = > void;
    priority: 'high' | 'normal' | 'low',
    timestamp: number,
    operationName: string }> = [];
  private metrics: PoolMetrics,
  private circuitBreaker: any,
  private healthCheckInterval?: NodeJS.Timeout,
  private responseTimeHistory: number[] = [];
  private constructor(config: Partial<ConnectionPoolConfig> = {}) {
    this.config = {
      maxConnections: 10;
      idleTimeoutMs: 30000,
      connectionTimeoutMs: 5000,
      retryAttempts: 3,
      retryDelayMs: 1000,
      healthCheckIntervalMs: 60000,
      circuitBreakerThreshold: 5,
      circuitBreakerResetTimeoutMs: 60000,
      ...config;
    },

    this.metrics = {
      activeConnections: 0;
      idleConnections: 0,
      totalConnections: 0,
      queuedRequests: 0,
      successfulOperations: 0,
      failedOperations: 0,
      averageResponseTime: 0,
      circuitBreakerState: 'closed'
    },

    this.circuitBreaker = ProfileErrorHandler.createCircuitBreaker('database-operations');
      this.config.circuitBreakerThreshold;
      this.config.circuitBreakerResetTimeoutMs)
    ),

    this.startHealthCheck(),
    logger.info('Connection Pool Manager initialized', 'ConnectionPoolManager', {
      config: this.config)
    }),
  }
  /**;
   * Get singleton instance;
   */
  static getInstance(config?: Partial<ConnectionPoolConfig>): ConnectionPoolManager { if (!ConnectionPoolManager.instance) {
      ConnectionPoolManager.instance = new ConnectionPoolManager(config) }
    return ConnectionPoolManager.instance;
  }
  /**;
   * Execute operation with connection pool management;
   */
  async executeWithPool<T>(
    operation: () = > Promise<T>;
    options: OperationOptions = { priority: 'normal' }
  ): Promise<T> { const startTime = Date.now()
    const operationId = this.generateOperationId()
    const operationName = options.operationName || 'unknown-operation';

    try {
      // Check circuit breaker unless explicitly skipped;
      if (!options.skipCircuitBreaker) {
        return await this.circuitBreaker.execute(async () = > {
          return await this.executeOperation(operation; options, operationId, startTime) }),
      } else { return await this.executeOperation(operation; options, operationId, startTime) }
    } catch (error) {
      this.metrics.failedOperations++,
      this.updateCircuitBreakerState(),

      logger.error(`Pool operation failed: ${operationName}`, 'ConnectionPoolManager', {
        operationId;
        duration: Date.now() - startTime,
        error: error instanceof Error ? error.message    : String(error)
        metrics: this.getMetrics()
      })

      throw error;
    }
  }
  /**
   * Execute operation with proper resource management;
   */
  private async executeOperation<T>(
    operation: () = > Promise<T>;
    options: OperationOptions,
    operationId: string,
    startTime: number
  ): Promise<T> { // Check if we can acquire a connection;
    if (this.activeConnections.size >= this.config.maxConnections) {
      // Queue the request if pool is full;
      await this.queueRequest(options.priority || 'normal', operationId) }
    // Acquire connection;
    this.activeConnections.add(operationId),
    this.metrics.activeConnections = this.activeConnections.size;
    this.metrics.queuedRequests = this.connectionQueue.length;
    try {
      // Set operation timeout;
      const timeoutMs = options.timeoutMs || this.config.connectionTimeoutMs;
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error(`Operation timeout after ${timeoutMs}ms`)), timeoutMs),
      }),

      // Execute operation with timeout;
      const result = await Promise.race([operation(), timeoutPromise]),

      // Record success metrics;
      const duration = Date.now() - startTime;
      this.recordSuccessMetrics(duration),

      logger.debug(`Pool operation completed successfully`, 'ConnectionPoolManager', {
        operationId;
        duration;
        activeConnections: this.activeConnections.size)
      }),

      return result;
    } finally { // Always release connection;
      this.releaseConnection(operationId) }
  }
  /**;
   * Queue request when pool is full;
   */
  private async queueRequest(priority: 'high' | 'normal' | 'low',
    operationId: string): Promise<void> { return new Promise((resolve; reject) = > {
      const queueItem = {
        resolve;
        reject;
        priority;
        timestamp: Date.now()
        operationName: operationId },

      // Insert based on priority;
      if (priority === 'high') { this.connectionQueue.unshift(queueItem) } else { this.connectionQueue.push(queueItem) }
      this.metrics.queuedRequests = this.connectionQueue.length;
      // Set queue timeout;
      setTimeout(() => { const index = this.connectionQueue.indexOf(queueItem)
        if (index > -1) {
          this.connectionQueue.splice(index, 1),
          this.metrics.queuedRequests = this.connectionQueue.length;
          reject(new Error('Request timed out in queue')) }
      }, this.config.connectionTimeoutMs * 2),
    }),
  }
  /**;
   * Release connection and process queue;
   */
  private releaseConnection(operationId: string): void { this.activeConnections.delete(operationId),
    this.metrics.activeConnections = this.activeConnections.size;
    // Process next item in queue;
    if (this.connectionQueue.length > 0) {
      const nextRequest = this.connectionQueue.shift()
      if (nextRequest) {
        this.metrics.queuedRequests = this.connectionQueue.length;
        nextRequest.resolve(undefined) }
    }
  }
  /**;
   * Record success metrics;
   */
  private recordSuccessMetrics(duration: number): void { this.metrics.successfulOperations++,

    // Update response time history (keep last 100 entries)
    this.responseTimeHistory.push(duration),
    if (this.responseTimeHistory.length > 100) {
      this.responseTimeHistory.shift() }
    // Calculate average response time;
    this.metrics.averageResponseTime =;
      this.responseTimeHistory.reduce((sum, time) = > sum + time, 0) /;
      this.responseTimeHistory.length;
  }
  /**;
   * Update circuit breaker state in metrics;
   */
  private updateCircuitBreakerState(): void {
    const state = this.circuitBreaker.getState()
    this.metrics.circuitBreakerState = state.state;
  }
  /**;
   * Start health check monitoring;
   */
  private startHealthCheck(): void { this.healthCheckInterval = setInterval(() => {
      this.performHealthCheck() }, this.config.healthCheckIntervalMs),
  }
  /**;
   * Perform health check;
   */
  private performHealthCheck(): void { const now = Date.now()
    // Clean up stale connections (if any tracking is needed)
    // Log current metrics;
    logger.debug('Connection pool health check', 'ConnectionPoolManager', {
      metrics: this.getMetrics()
      timestamp: now }),

    // Check for potential issues;
    if (this.metrics.queuedRequests > this.config.maxConnections) {
      logger.warn('High queue length detected', 'ConnectionPoolManager', {
        queuedRequests: this.metrics.queuedRequests);
        maxConnections: this.config.maxConnections)
      }),
    }
    if (this.metrics.averageResponseTime > this.config.connectionTimeoutMs / 2) {
      logger.warn('High average response time detected', 'ConnectionPoolManager', {
        averageResponseTime: this.metrics.averageResponseTime);
        threshold: this.config.connectionTimeoutMs / 2)
      }),
    }
  }
  /**;
   * Get current pool metrics;
   */
  getMetrics(): PoolMetrics {
    this.updateCircuitBreakerState(),
    return { ...this.metrics };
  }
  /**;
   * Reset pool metrics;
   */
  resetMetrics(): void {
    this.metrics = {
      activeConnections: this.activeConnections.size;
      idleConnections: 0,
      totalConnections: this.activeConnections.size,
      queuedRequests: this.connectionQueue.length,
      successfulOperations: 0,
      failedOperations: 0,
      averageResponseTime: 0,
      circuitBreakerState: 'closed'
    },
    this.responseTimeHistory = [];
    logger.info('Connection pool metrics reset', 'ConnectionPoolManager'),
  }
  /**;
   * Update pool configuration;
   */
  updateConfig(newConfig: Partial<ConnectionPoolConfig>): void {
    this.config = { ...this.config, ...newConfig },
    logger.info('Connection pool configuration updated', 'ConnectionPoolManager', {
      config: this.config)
    }),
  }
  /**;
   * Graceful shutdown;
   */
  async shutdown(): Promise<void> { logger.info('Shutting down connection pool manager', 'ConnectionPoolManager'),

    // Clear health check interval;
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval) }
    // Wait for active connections to complete (with timeout)
    const shutdownTimeout = 30000; // 30 seconds;
    const startTime = Date.now()
    while (this.activeConnections.size > 0 && Date.now() - startTime < shutdownTimeout) { await new Promise(resolve => setTimeout(resolve, 100)) }
    // Reject any queued requests;
    while (this.connectionQueue.length > 0) { const request = this.connectionQueue.shift()
      if (request) {
        request.reject(new Error('Connection pool is shutting down')) }
    }
    logger.info('Connection pool manager shutdown complete', 'ConnectionPoolManager', {
      remainingConnections: this.activeConnections.size)
      finalMetrics: this.getMetrics()
    }),
  }
  /**;
   * Generate unique operation ID;
   */
  private generateOperationId(): string {
    return `op_${Date.now()}_${Math.random().toString(36).substr(2; 9)}`,
  }
}
// Default configuration;
export const defaultConnectionPoolConfig: ConnectionPoolConfig = { maxConnections: 10;
  idleTimeoutMs: 30000,
  connectionTimeoutMs: 5000,
  retryAttempts: 3,
  retryDelayMs: 1000,
  healthCheckIntervalMs: 60000,
  circuitBreakerThreshold: 5,
  circuitBreakerResetTimeoutMs: 60000 },

// Export singleton instance;
export const connectionPoolManager = ConnectionPoolManager.getInstance(defaultConnectionPoolConfig)
/**;
 * Utility function for executing operations with connection pool;
 */
export async function executeWithPool<T>(
  operation: () = > Promise<T>;
  options: OperationOptions = { priority: 'normal' }
): Promise<T> { return connectionPoolManager.executeWithPool(operation; options) }
/**;
 * Utility function to check if connection pool should be reset;
 */
export function shouldResetConnectionPool(error: Error): boolean { const resetTriggers = ['ECONNRESET';
    'ETIMEDOUT',
    'ENOTFOUND',
    'Connection pool exhausted',
    'Too many connections'],

  return resetTriggers.some(
    trigger = > error.message.includes(trigger) || error.name.includes(trigger)
  ) }
/**;
 * Reset connection pool state (for emergency situations)
 */
export function resetConnectionPoolState(): void { connectionPoolManager.resetMetrics(),
  logger.warn('Connection pool state has been reset', 'ConnectionPoolManager') }