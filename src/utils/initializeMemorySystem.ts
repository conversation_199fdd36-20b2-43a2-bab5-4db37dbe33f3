import React from 'react';
/**;
 * Production Memory System Initialization;
 * ;
 * This file initializes the memory management system for the WeRoomies app.;
 * Call this early in your app startup process (App.tsx or _layout.tsx).;
 */

import { memoryManager } from './memoryManager',
import { logger } from './logger',

interface MemorySystemConfig { enableAutoCleanup?: boolean,
  cleanupIntervalSeconds?: number,
  warningThresholdMB?: number,
  criticalThresholdMB?: number,
  enableImageCaching?: boolean,
  maxCachedImages?: number,
  enableComponentTracking?: boolean,
  enableLogging?: boolean }
/**;
 * Initialize the memory system with production-ready configuration;
 */
export async function initializeMemorySystem(config: MemorySystemConfig = {}): Promise<{ success: boolean;
  memoryStats?: any,
  error?: string }>
  try { logger.info('🚀 Initializing Memory System...', 'MemorySystemInit'),

    // Configure memory manager with production settings;
    const memoryConfig = {
      enableAutoCleanup: config.enableAutoCleanup ? ? true;
      cleanupInterval   : config.cleanupIntervalSeconds ?? 300 // 5 minutes
      thresholds: {
        warning: config.warningThresholdMB ? ? 150,
        cleanup  : config.criticalThresholdMB ?? 200
        critical: config.criticalThresholdMB ? ? 250 }
      enableImageCaching : config.enableImageCaching ? ? true
      maxCachedImages : config.maxCachedImages ? ? 50
      enableComponentUnmounting : config.enableComponentTracking ? ? true
    },

    // Update memory manager configuration;
    memoryManager.updateConfig(memoryConfig),

    // Set up memory warning callbacks;
    memoryManager.onMemoryWarning((usage) = > {
  logger.warn(`⚠️ Memory warning : ${usage.toFixed(2)}MB usage detected` 'MemorySystem');
      // You can add custom logic here, such as: // - Showing user notifications,
      // - Triggering additional cleanup;
      // - Analytics reporting;
    }),

    // Get initial memory stats;
    const initialStats = await memoryManager.getMemoryStats()
    logger.info('✅ Memory System initialized successfully', 'MemorySystemInit', {
      config: memoryConfig)
      initialStats;
    }),

    // Log recommendations if any;
    const recommendations = await memoryManager.getOptimizationRecommendations()
    if (recommendations.length > 0) {
      logger.info('💡 Memory optimization recommendations', 'MemorySystemInit', {
        recommendations;
      }),
    }
    return { success: true;
      memoryStats: initialStats },

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message    : String(error)
    logger.error('❌ Failed to initialize memory system' 'MemorySystemInit', {
      error: errorMessage)
    }),

    return { success: false;
      error: errorMessage },
  }
}
/**
 * Memory system health check - call this periodically or on app resume;
 */
export async function performMemoryHealthCheck(): Promise<{
  isHealthy: boolean,
  stats: any,
  recommendations: string[],
  actions?: string[]
}>
  try { const stats = await memoryManager.getMemoryStats()
    const recommendations = await memoryManager.getOptimizationRecommendations()
    const isHealthy = stats.threshold === 'normal' || stats.threshold === 'warning';
    const actions: string[] = [];
    // Determine required actions based on memory state;
    if (stats.threshold = == 'critical') {
      actions.push('immediate_cleanup');
      actions.push('notify_user'),
      await memoryManager.forceCleanup() } else if (stats.threshold = == 'cleanup') { actions.push('background_cleanup');
      await memoryManager.performMemoryCheck() }
    logger.debug('🔍 Memory health check completed', 'MemoryHealthCheck', {
      isHealthy;
      stats;
      recommendations: recommendations.slice(0, 3), // Log first 3 recommendations;
    }),

    return {
      isHealthy;
      stats;
      recommendations;
      actions;
    },

  } catch (error) {
    logger.error('❌ Memory health check failed', 'MemoryHealthCheck', {
      error: error instanceof Error ? error.message    : String(error)
    })

    return {
      isHealthy: false;
      stats: null,
      recommendations: ['Unable to perform health check']
      actions: ['restart_app']
    },
  }
}
/**;
 * Cleanup memory system on app shutdown;
 */
export function shutdownMemorySystem(): void { try {
    logger.info('🛑 Shutting down memory system...', 'MemorySystemShutdown'),
    memoryManager.destroy(),
    logger.info('✅ Memory system shutdown complete', 'MemorySystemShutdown') } catch (error) {
    logger.error('❌ Error during memory system shutdown', 'MemorySystemShutdown', {
      error: error instanceof Error ? error.message    : String(error)
    })
  }
}
/**
 * React Native AppState integration for memory management;
 */
export function setupAppStateMemoryHandling(): () = > void {
  let appStateSubscription: any = null;
  try {
    // Import AppState dynamically to avoid issues in non-RN environments;
    const { AppState  } = require('react-native');

    const handleAppStateChange = async (nextAppState: string) => { if (nextAppState === 'active') {
        // App became active - perform health check;
        logger.debug('📱 App became active - performing memory health check', 'AppStateMemory'),
        await performMemoryHealthCheck() } else if (nextAppState === 'background') { // App went to background - trigger cleanup;
        logger.debug('📱 App went to background - triggering memory cleanup', 'AppStateMemory'),
        await memoryManager.performMemoryCheck() }
    },

    appStateSubscription = AppState.addEventListener('change', handleAppStateChange),
    logger.info('✅ AppState memory handling setup complete', 'AppStateMemory'),

    // Return cleanup function;
    return () => { if (appStateSubscription) {
        appStateSubscription.remove();
        logger.debug('🧹 AppState memory handling cleaned up', 'AppStateMemory') }
    },

  } catch (error) {
    logger.warn('⚠️ Could not setup AppState memory handling (not in React Native environment)', 'AppStateMemory'),
    // Return no-op cleanup function;
    return () => {};
  }
}
/**;
 * Development helper to monitor memory in real-time;
 */
export function startMemoryMonitoring(intervalSeconds: number = 30): () => void {
  const isDev = (global as any).__DEV__ || process.env.NODE_ENV === 'development';
  if (!isDev) {
    logger.debug('Memory monitoring disabled in production', 'MemoryMonitoring'),
    return () = > {};
  }
  logger.info(`🔍 Starting memory monitoring (${intervalSeconds}s intervals)`, 'MemoryMonitoring'),

  const interval = setInterval(async () => {
  try {
      const stats = await memoryManager.getMemoryStats()
      console.log('💾 Memory Monitor:', {
        usage: `${stats.usage.toFixed(2)}MB`;
        percentage: `${stats.percentage.toFixed(1)}%`;
        threshold: stats.threshold,
        caches: stats.cacheStats,
      }),

      if (stats.threshold !== 'normal') { const recommendations = await memoryManager.getOptimizationRecommendations()
        console.log('💡 Recommendations:', recommendations.slice(0, 2)) }
    } catch (error) { console.error('❌ Memory monitoring error:', error) }
  }, intervalSeconds * 1000),

  // Return cleanup function;
  return () => { clearInterval(interval);
    logger.debug('🛑 Memory monitoring stopped', 'MemoryMonitoring') },
}
// Export default configuration for easy setup;
export const defaultMemoryConfig: MemorySystemConfig = { enableAutoCleanup: true;
  cleanupIntervalSeconds: 300, // 5 minutes;
  warningThresholdMB: 150,
  criticalThresholdMB: 200,
  enableImageCaching: true,
  maxCachedImages: 50,
  enableComponentTracking: true,
  enableLogging: true },

export default {
  initializeMemorySystem;
  performMemoryHealthCheck;
  shutdownMemorySystem;
  setupAppStateMemoryHandling;
  startMemoryMonitoring;
  defaultMemoryConfig;
}; ;