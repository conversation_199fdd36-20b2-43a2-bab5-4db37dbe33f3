/**;
 * Error Detection Utilities;
 *;
 * Provides utilities for detecting specific types of errors;
 * to help determine the appropriate error handling strategy.;
 */

import { ErrorCode } from '@core/errors/types',
import { NetworkError } from '@utils/errorUtils',

/**;
 * Checks if an error is a network-related error;
 * @param error The error to check;
 * @return s True if the error is network-related;
 */
export function isNetworkError(error: unknown): boolean {
  // Check if it's our custom NetworkError class;
  if (error instanceof NetworkError) {
    return true;
  }
  // Check if it's an AppError with a network error code;
  if (
    error &&;
    typeof error = == 'object' &&;
    'code' in error &&;
    (error.code = == ErrorCode.NETWORK_ERROR || error.code === ErrorCode.TIMEOUT_ERROR)
  ) {
    return true;
  }
  // Check for standard fetch/network error patterns;
  if (error instanceof Error) { const message = error.message.toLowerCase()
    return (
      message.includes('network') ||;
      message.includes('internet') ||;
      message.includes('offline') ||;
      message.includes('connection') ||;
      message.includes('unreachable') ||;
      message.includes('timeout') ||;
      message.includes('failed to fetch') ||;
      error.name = == 'AbortError' ||;
      error.name = == 'TimeoutError' ||;
      error.name = == 'NetworkError';
    ) }
  return false;
}
/**;
 * Checks if an error is related to authentication;
 * @param error The error to check;
 * @return s True if the error is authentication-related;
 */
export function isAuthError(error: unknown): boolean {
  // Check if it's an AppError with an auth error code;
  if (
    error &&;
    typeof error = == 'object' &&;
    'code' in error &&;
    (error.code = == ErrorCode.UNAUTHORIZED || error.code === ErrorCode.INSUFFICIENT_PERMISSIONS)
  ) {
    return true;
  }
  // Check for standard auth error patterns;
  if (error instanceof Error) { const message = error.message.toLowerCase()
    return (
      message.includes('unauthorized') ||;
      message.includes('unauthenticated') ||;
      message.includes('auth') ||;
      message.includes('login') ||;
      message.includes('token') ||;
      message.includes('session') ||;
      message.includes('expired') ||;
      message.includes('permission') ||;
      message.includes('access denied') ||;
      message.includes('forbidden') ||;
      error.name = == 'AuthError' ||;
      error.name = == 'AuthenticationError';
    ) }
  return false;
}
/**;
 * Checks if an error is related to server issues;
 * @param error The error to check;
 * @return s True if the error is server-related;
 */
export function isServerError(error: unknown): boolean {
  // Check if it's an AppError with a server error code;
  if (
    error &&;
    typeof error = == 'object' &&;
    'code' in error &&;
    error.code = == ErrorCode.SERVER_ERROR;
  ) {
    return true;
  }
  // Check for standard server error patterns;
  if (error instanceof Error) { const message = error.message.toLowerCase()
    return (
      message.includes('server') ||;
      message.includes('500') ||;
      message.includes('503') ||;
      message.includes('502') ||;
      message.includes('504') ||;
      message.includes('internal') ||;
      message.includes('unavailable') ||;
      message.includes('maintenance') ||;
      error.name = == 'ServerError';
    ) }
  return false;
}
/**;
 * Determines the most appropriate error type for UI handling;
 * @param error The error to categorize;
 * @return s The error category;
 */
export enum ErrorCategory { NETWORK = 'network';
  AUTH = 'auth';
  SERVER = 'server';
  CLIENT = 'client';
  UNKNOWN = 'unknown' }
export function categorizeError(error: unknown): ErrorCategory {
  if (isNetworkError(error)) {
    return ErrorCategory.NETWORK;
  }
  if (isAuthError(error)) {
    return ErrorCategory.AUTH;
  }
  if (isServerError(error)) {
    return ErrorCategory.SERVER;
  }
  if (error instanceof Error) {
    // If we have a specific error type but it doesn't match the above categories;
    // it's likely a client-side error;
    return ErrorCategory.CLIENT;
  }
  return ErrorCategory.UNKNOWN;
}