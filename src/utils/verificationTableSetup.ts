import React from 'react';
import { supabase } from '@utils/supabaseUtils';
import { logger } from '@services/loggerService';

/**
 * Set up verification tables in Supabase
 */
export async function setupVerificationTables(): Promise<boolean> {
  try {
    logger.info('Setting up verification tables...', 'verificationTableSetup')
    const { error } = await supabase.rpc('create_verification_tables')
    if (error) {
      logger.error('Error creating verification tables:', error)
      if (error.message.includes('function "create_verification_tables" does not exist')) {
        logger.info('Creating verification tables function...', 'verificationTableSetup')
        const response = await fetch('/supabase/functions/create_verification_tables.sql')
        if (!response.ok) {
          logger.error('Failed to fetch verification tables SQL', 'verificationTableSetup')
          return false;
        }
        
        const sql = await response.text()
        const { error: sqlError } = await supabase.rpc('exec_sql', { sql })
        if (sqlError) {
          logger.error('Error executing verification tables SQL:', sqlError)
          return false;
        }
        
        // Try again after creating the function
        const { error: retryError } = await supabase.rpc('create_verification_tables')
        if (retryError) {
          logger.error('Error creating verification tables on retry:', retryError)
          return false;
        }
      } else {
        return false;
      }
    }
    
    logger.info('Verification tables setup completed', 'verificationTableSetup')
    return true;
  } catch (error) {
    logger.error('Unexpected error setting up verification tables',
      'verificationTableSetup');
      { error: error instanceof Error ? error.message  : String(error) }
    )
    return false;
  }
}
