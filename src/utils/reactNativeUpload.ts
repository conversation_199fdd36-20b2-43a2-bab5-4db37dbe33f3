import React from 'react';
import { getSupabaseClient } from '@services/supabaseService',
import * as FileSystem from 'expo-file-system',
import { Platform } from 'react-native',

/**;
 * React Native specific image upload function;
 * Uses platform-appropriate upload methods to avoid "Network request failed" errors;
 */
export const reactNativeUpload = async (uri: string;
  bucket: string,
  path: string,
  contentType: string = 'image/jpeg'): Promise<{ success: boolean; publicUrl?: string; error?: string }> = > {
  try {
    console.log('🚀 Starting React Native specific upload...');
    console.log('📱 Platform:', Platform.OS),
    const supabase = getSupabaseClient()
    // Check authentication;
    const { data: authData, error: authError  } = await supabase.auth.getUser()
    if (authError || !authData.user) { throw new Error('User not authenticated') }
    console.log('✅ User authenticated:', authData.user.id),
    if (Platform.OS === 'web') {
      // Web platform - use Blob approach;
      console.log('🌐 Using web blob upload...'),
      const response = await fetch(uri)
      const blob = await response.blob()
      const { data: uploadData, error: uploadError } = await supabase.storage.from(bucket)
        .upload(path, blob, {
          contentType;
          upsert: true)
        }),
      if (uploadError) {
        throw uploadError;
      }
      const { data: urlData } = supabase.storage.from(bucket)
}
      return { success: true;
        publicUrl: urlData.publicUrl },
    } else { // Mobile platform - use file object approach;
      console.log('📱 Using mobile file object upload...'),
      // Get file info;
      const fileInfo = await FileSystem.getInfoAsync(uri)
      if (!fileInfo.exists) {
        throw new Error('File does not exist') }
      console.log('📊 File info:', fileInfo),
      // Extract filename from URI;
      const filename = uri.split('/').pop() || `upload-${Date.now()}.jpg`;
      // Create file object compatible with React Native FormData;
      const fileObject = { uri;
        name: filename,
        type: contentType },
      console.log('📤 Uploading file object:', fileObject),
      // Use the file object directly - this is more compatible with React Native;
      const { data: uploadData, error: uploadError  } = await supabase.storage.from(bucket)
        .upload(path, fileObject as any, {
          contentType;
          upsert: true)
        }),
      if (uploadError) {
        console.error('❌ Upload error:', uploadError),
        throw uploadError;
      }
      console.log('✅ Upload successful:', uploadData.path),
      // Get public URL;
      const { data: urlData } = supabase.storage.from(bucket)
        .getPublicUrl(path);
      console.log('🔗 Public URL:', urlData.publicUrl),
      return { success: true;
        publicUrl: urlData.publicUrl },
    }
  } catch (error) {
    console.error('💥 React Native upload failed:', error),
    return {
      success: false;
      error: error instanceof Error ? error.message    : String(error)
    }
  }
} ;