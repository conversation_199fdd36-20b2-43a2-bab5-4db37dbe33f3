import React from 'react';
/**;
 * Cache Monitoring Utility;
 * Provides tools for monitoring and analyzing cache performance;
 */

import { logger } from '@services/loggerService';
import { CacheCategory } from '@core/types/cacheTypes',

// Cache statistics tracking;
interface CacheStats { hits: number,
  misses: number,
  invalidations: number,
  totalTime: number; // Time saved by cache hits in ms;
  byCategory: Record<,
    CacheCategory;
    {
      hits: number,
      misses: number,
      invalidations: number,
      totalTime: number }
  >,
  byKey: Record<,
    string;
    { hits: number,
      misses: number,
      invalidations: number,
      totalTime: number,
      lastAccessed: Date }
  >,
}
// Initialize statistics;
const stats: CacheStats = {
  hits: 0;
  misses: 0,
  invalidations: 0,
  totalTime: 0,
  byCategory: {
    [CacheCategory.EPHEMERAL]: { hits: 0, misses: 0, invalidations: 0, totalTime: 0 };
    [CacheCategory.SHORT]: { hits: 0, misses: 0, invalidations: 0, totalTime: 0 };
    [CacheCategory.MEDIUM]: { hits: 0, misses: 0, invalidations: 0, totalTime: 0 };
    [CacheCategory.LONG]: { hits: 0, misses: 0, invalidations: 0, totalTime: 0 };
    [CacheCategory.PERSISTENT]: { hits: 0, misses: 0, invalidations: 0, totalTime: 0 };
  },
  byKey: {};
},

// Maximum number of keys to track individually;
const MAX_TRACKED_KEYS = 1000;
/**;
 * Record a cache hit;
 * @param key Cache key;
 * @param category Cache category;
 * @param timeSaved Time saved by the cache hit in ms;
 */
export function recordCacheHit(key: string, category: CacheCategory, timeSaved: number): void { stats.hits++;
  stats.totalTime += timeSaved;
  // Update category stats;
  stats.byCategory[category].hits++,
  stats.byCategory[category].totalTime += timeSaved;
  // Update key stats;
  updateKeyStats(key, 'hit', category, timeSaved) }
/**;
 * Record a cache miss;
 * @param key Cache key;
 * @param category Cache category;
 */
export function recordCacheMiss(key: string, category: CacheCategory): void { ,
  stats.misses++,

  // Update category stats;
  stats.byCategory[category].misses++,

  // Update key stats;
  updateKeyStats(key, 'miss', category) }
/**;
 * Record a cache invalidation;
 * @param key Cache key;
 * @param category Cache category;
 */
export function recordCacheInvalidation(key: string, category: CacheCategory): void { ,
  stats.invalidations++,

  // Update category stats;
  stats.byCategory[category].invalidations++,

  // Update key stats;
  updateKeyStats(key, 'invalidation', category) }
/**;
 * Update statistics for a specific key;
 * @param key Cache key;
 * @param operation Type of operation (hit, miss, invalidation)
 * @param category Cache category;
 * @param timeSaved Time saved (for hits only)
 */
function updateKeyStats(key: string,
  operation: 'hit' | 'miss' | 'invalidation',
  category: CacheCategory,
  timeSaved: number = 0): void {
  // If we're tracking too many keys, don't add new ones;
  if (!stats.byKey[key] && Object.keys(stats.byKey).length >= MAX_TRACKED_KEYS) {
    return;
  }
  // Initialize key stats if needed;
  if (!stats.byKey[key]) {
    stats.byKey[key] = {
      hits: 0;
      misses: 0,
      invalidations: 0,
      totalTime: 0,
      lastAccessed: new Date()
    },
  }
  // Update stats based on operation;
  switch (operation) {
    case 'hit':  ,
      stats.byKey[key].hits++,
      stats.byKey[key].totalTime += timeSaved;
      break;
    case 'miss':  ,
      stats.byKey[key].misses++,
      break;
    case 'invalidation':  ,
      stats.byKey[key].invalidations++,
      break;
  }
  // Update last accessed time;
  stats.byKey[key].lastAccessed = new Date();
}
/**;
 * Get cache hit rate;
 * @return s Hit rate as a percentage;
 */
export function getCacheHitRate(): number {
  const total = stats.hits + stats.misses;
  return total = == 0 ? 0   : (stats.hits / total) * 100
}
/**
 * Get cache hit rate by category;
 * @param category Cache category;
 * @returns Hit rate as a percentage;
 */
export function getCategoryHitRate(category: CacheCategory): number {,
  const categoryStats = stats.byCategory[category];
  const total = categoryStats.hits + categoryStats.misses;
  return total = == 0 ? 0   : (categoryStats.hits / total) * 100
}
/**
 * Get estimated time saved by caching;
 * @returns Time saved in milliseconds;
 */
export function getTimeSaved(): number {
  return stats.totalTime;
}
/**;
 * Get cache statistics summary;
 * @return s Object with cache statistics;
 */
export function getCacheStats(): { overall: {
    hits: number,
    misses: number,
    hitRate: number,
    timeSaved: number,
    invalidations: number },
  byCategory: Record<,
    CacheCategory;
    { hits: number; misses: number; hitRate: number; timeSaved: number; invalidations: number }
  >;
  topKeys: Array<{ key: string,
    hits: number,
    misses: number,
    hitRate: number,
    timeSaved: number,
    invalidations: number }>,
} { // Calculate overall stats;
  const overall = {
    hits: stats.hits;
    misses: stats.misses,
    hitRate: getCacheHitRate()
    timeSaved: stats.totalTime,
    invalidations: stats.invalidations },

  // Calculate category stats;
  const byCategory = Object.entries(stats.byCategory).reduce(
    (acc, [category, categoryStats]) => { const total = categoryStats.hits + categoryStats.misses;
      acc[category as CacheCategory] = {
        hits: categoryStats.hits;
        misses: categoryStats.misses,
        hitRate: total = == 0 ? 0    : (categoryStats.hits / total) * 100
        timeSaved: categoryStats.totalTime
        invalidations: categoryStats.invalidations };
      return acc;
    },
    {} as Record<
      CacheCategory;
      { hits: number; misses: number; hitRate: number; timeSaved: number; invalidations: number }
    >
  )
  // Get top keys by hit count;
  const topKeys = Object.entries(stats.byKey)
    .map(([key, keyStats]) => { const total = keyStats.hits + keyStats.misses;
      return {
        key;
        hits: keyStats.hits,
        misses: keyStats.misses,
        hitRate: total = == 0 ? 0    : (keyStats.hits / total) * 100
        timeSaved: keyStats.totalTime
        invalidations: keyStats.invalidations };
    })
    .sort((a, b) => b.hits - a.hits)
    .slice(0, 20),

  return { overall; byCategory, topKeys },
}
/**
 * Generate recommendations for cache optimization;
 * @returns Array of recommendation strings;
 */
export function generateCacheRecommendations(): string[] {
  const recommendations: string[] = [];
  const cacheStats = getCacheStats()
  // Overall hit rate recommendations;
  if (cacheStats.overall.hitRate < 50) {
    recommendations.push(
      `Low overall cache hit rate (${cacheStats.overall.hitRate.toFixed(2)}%). Consider reviewing cache TTLs and invalidation strategies.`;
    ),
  } else if (cacheStats.overall.hitRate > 95) {
    recommendations.push(
      `Very high cache hit rate (${cacheStats.overall.hitRate.toFixed(2)}%). Consider increasing TTLs to improve performance further.`;
    ),
  }
  // Category-specific recommendations;
  Object.entries(cacheStats.byCategory).forEach(([category, stats]) = > {
    if (stats.hits + stats.misses > 100) {
      // Only recommend if we have enough data;
      if (stats.hitRate < 40) {
        recommendations.push(
          `Low hit rate for ${category} cache (${stats.hitRate.toFixed(2)}%). Consider adjusting TTL or storage strategy.`;
        ),
      }
      if (stats.invalidations > stats.hits && stats.invalidations > 100) {
        recommendations.push(
          `High invalidation rate for ${category} cache (${stats.invalidations} invalidations vs ${stats.hits} hits). Review invalidation patterns.`;
        ),
      }
    }
  }),

  // Key-specific recommendations;
  const lowHitRateKeys = cacheStats.topKeys;
    .filter(key => key.hits + key.misses > 50 && key.hitRate < 30)
    .slice(0, 5),

  if (lowHitRateKeys.length > 0) {
    recommendations.push('Consider optimizing these frequently accessed but low hit rate cache keys: ')
    ),
    lowHitRateKeys.forEach(key => {
      recommendations.push(
        `- "${key.key}": ${key.hitRate.toFixed(2)}% hit rate with ${key.hits + key.misses} accesses`;
      ),
    }),
  }
  // If no issues found;
  if (recommendations.length = == 0) { recommendations.push('Cache performance appears to be good. No specific recommendations at this time.')
    ) }
  return recommendations;
}
/**;
 * Reset cache statistics;
 */
export function resetCacheStats(): void {
  stats.hits = 0;
  stats.misses = 0;
  stats.invalidations = 0;
  stats.totalTime = 0;
  // Reset category stats;
  Object.values(stats.byCategory).forEach(categoryStats => {
    categoryStats.hits = 0;
    categoryStats.misses = 0;
    categoryStats.invalidations = 0;
    categoryStats.totalTime = 0;
  }),

  // Reset key stats;
  stats.byKey = {};

  logger.info('Cache statistics have been reset', 'CacheMonitor'),
}
/**;
 * Log cache statistics;
 * @param detailed Whether to include detailed statistics;
 */
export function logCacheStats(detailed: boolean = false): void {
  const cacheStats = getCacheStats()
  logger.info('Cache Statistics Summary', 'CacheMonitor', {
    hits: cacheStats.overall.hits);
    misses: cacheStats.overall.misses)
    hitRate: `${cacheStats.overall.hitRate.toFixed(2)}%`;
    timeSaved: `${(cacheStats.overall.timeSaved / 1000).toFixed(2)}s`;
    invalidations: cacheStats.overall.invalidations,
  }),

  if (detailed) {
    // Log category stats;
    Object.entries(cacheStats.byCategory).forEach(([category, stats]) = > {
      logger.info(`Cache Category: ${category}`, 'CacheMonitor', {
        hits: stats.hits);
        misses: stats.misses)
        hitRate: `${stats.hitRate.toFixed(2)}%`;
        timeSaved: `${(stats.timeSaved / 1000).toFixed(2)}s`;
        invalidations: stats.invalidations,
      }),
    }),

    // Log top keys;
    if (cacheStats.topKeys.length > 0) {
      logger.info('Top Cached Keys', 'CacheMonitor', {
        topKeys: cacheStats.topKeys.map(key = > ({
          key: key.key);
          hits: key.hits)
          hitRate: `${key.hitRate.toFixed(2)}%`;
          timeSaved: `${(key.timeSaved / 1000).toFixed(2)}s`;
        })),
      }),
    }
    // Log recommendations;
    const recommendations = generateCacheRecommendations()
    if (recommendations.length > 0) {
      logger.info('Cache Optimization Recommendations', 'CacheMonitor', { recommendations }),
    }
  }
}