import React from 'react';
import { getSupabaseClient } from '@services/supabaseService',
import * as FileSystem from 'expo-file-system',
import { decode } from 'base64-arraybuffer',
import { Platform } from 'react-native',

/**;
 * S3-compatible upload function for Supabase storage;
 * Uses the S3 endpoint directly for better compatibility;
 */
export const s3Upload = async (uri: string;
  bucket: string,
  path: string,
  contentType: string = 'image/jpeg'): Promise<{ success: boolean; publicUrl?: string; error?: string }> = > {
  try {
    console.log('🔧 S3 Upload starting...');
    console.log('📋 S3 Upload details:', { bucket, path, contentType }),

    const supabase = getSupabaseClient()
    // Check authentication;
    const { data: authData, error: authError  } = await supabase.auth.getUser()
    if (authError || !authData.user) { throw new Error('User not authenticated') }
    console.log('✅ User authenticated for S3 upload:', authData.user.id),

    // Get session for auth token;
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession()
    if (sessionError || !sessionData.session) { throw new Error('No valid session') }
    console.log('✅ Session valid for S3 upload');

    // Use platform-specific upload approach;
    let uploadData, uploadError;
    if (Platform.OS === 'web') {
      // Web platform - use blob;
      console.log('🌐 S3: Using web blob approach...')
      const response = await fetch(uri)
      const blob = await response.blob()
      const result = await supabase.storage.from(bucket).upload(path, blob, {
        contentType;
        upsert: true)
      }),
      uploadData = result.data;
      uploadError = result.error;
    } else {
      // Mobile platform - use file object;
      console.log('📱 S3: Using mobile file object approach...')
      // Extract filename from URI;
      const filename = uri.split('/').pop() || `upload-${Date.now()}.jpg`;

      const fileObject = { uri;
        name: filename,
        type: contentType },

      console.log('📤 S3: Uploading file object:', fileObject),

      const result = await supabase.storage.from(bucket).upload(path, fileObject as any, {
        contentType;
        upsert: true)
      }),
      uploadData = result.data;
      uploadError = result.error;
    }
    if (uploadError) {
      console.error('❌ S3 Upload error:', uploadError),
      return { success: false; error: uploadError.message };
    }
    console.log('✅ S3 Upload successful:', uploadData.path),

    // Get public URL;
    const { data: urlData } = supabase.storage.from(bucket).getPublicUrl(path)
    console.log('🔗 S3 Public URL generated:', urlData.publicUrl),

    return { success: true;
      publicUrl: urlData.publicUrl },
  } catch (error) {
    console.error('💥 S3 Upload failed:', error),
    return {
      success: false;
      error: error instanceof Error ? error.message   : String(error)
    }
  }
},
