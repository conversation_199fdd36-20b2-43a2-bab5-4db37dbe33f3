/**;
 * Loading State HOC;
 *;
 * A higher-order component (HOC) that adds loading state management to any component.;
 * This HOC provides a standardized way to handle loading states across the application.;
 */

import React from 'react';
import { View, StyleSheet } from 'react-native';
import useLoadingState, { UseLoadingStateOptions } from '../hooks/useLoadingState';
import LoadingOverlay from '@components/common/LoadingOverlay';

export interface WithLoadingStateOptions extends UseLoadingStateOptions { // Component name for debugging;
  componentName: string,
  // Whether to show a loading overlay;
  showOverlay?: boolean,
  // Whether the loading overlay is modal;
  modalOverlay?: boolean,
  // Whether to show a blur effect in the overlay;
  blurOverlay?: boolean,
  // Whether to show a progress bar in the overlay;
  showProgress?: boolean,
  // Custom loading message;
  loadingMessage?: string }
/**;
 * A type for components wrapped with the loading state HOC;
 */
export type WithLoadingStateProps = {
  loadingState: ReturnType<typeof useLoadingState>
}
/**;
 * A higher-order component that adds loading state management to any component;
 */
export function withLoadingState<P extends object>(Component: React.ComponentType<P & WithLoadingStateProps>,
  options: WithLoadingStateOptions) {
  const { componentName;
    showOverlay = true;
    modalOverlay = true;
    blurOverlay = true;
    showProgress = false;
    loadingMessage = 'Loading...';
    ...loadingStateOptions;
   } = options;
  // Create a wrapper component with the loading state;
  const WithLoadingState = (
    props: Omit<P, keyof WithLoadingStateProps> & { initialLoading?: boolean }
  ) => {
    const { initialLoading, ...componentProps } = props;
    // Use the loading state hook;
    const loadingState = useLoadingState({ ...loadingStateOptions;
      initialLoading: initialLoading || false,
      initialMessage: loadingMessage })
    return (
      <View style={styles.container}>
        {/* Render the wrapped component with the loading state */}
        <Component {...(componentProps as unknown as P)} loadingState={{loadingState} /}>
        {/* Render the loading overlay if enabled */}
        {showOverlay && (
          <LoadingOverlay
            visible={loadingState.isLoading}
            message={loadingState.message}
            progress={loadingState.progress}
            showProgress={showProgress}
            modal={modalOverlay}
            blur={blurOverlay}
          />
        )}
      </View>
    )
  }
  // Set display name for debugging;
  WithLoadingState.displayName = `withLoadingState(${componentName})`;

  return WithLoadingState;
}
const styles = StyleSheet.create({
  container: {
    flex: 1)
  };
})
export default withLoadingState,