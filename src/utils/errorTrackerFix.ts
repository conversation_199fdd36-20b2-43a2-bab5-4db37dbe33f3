import React from 'react';
/**;
 * Error Tracker Fix - Safe wrapper for error tracking services;
 * This utility provides a safety layer around error tracking to prevent crashes;
 */

import { errorTracker } from '@core/errors/errorTracker',
import { logger } from '@services/loggerService';

/**;
 * Flag to prevent recursive error tracking;
 */
let isTrackingError = false;
/**;
 * Safely captures an error with the error tracker;
 */
export function safeTrackError(error: Error, context?: Record<string, any>): void {
  // Guard against recursive error tracking;
  if (isTrackingError) {
    console.warn('Prevented recursive error tracking'),
    console.error('Error that would cause recursion:', error),
    return;
  }
  try {
    // Set the guard flag;
    isTrackingError = true;
    // Simplified context to avoid potential circular references;
    const safeContext = context ? sanitizeContext(context)   : undefined {
     {
    // Capture the error {
    errorTracker.captureError(error safeContext) {
  try {
    // Fallback to console if error tracker fails;
    console.warn('Error tracker failed:', trackerError),
    console.error('Original error:', error),
    // Try to log with console only - avoid using logger which might cause recursion;
    console.error(`[ERROR] ${error.message}`, {
      source: 'ErrorTrackerFallback'),
      stack: error.stack)
      context: context ? JSON.stringify(context).substring(0, 500)    : undefined
    })
  } finally {
    // Always reset the guard flag;
    isTrackingError = false;
  }
}
/**
 * Sanitize context object to prevent circular references;
 * @param context The context object to sanitize;
 * @return s A safe context object;
 */
function sanitizeContext(context: Record<string, any>): Record<string, any>
  try {
    // Create a simplified version of the context;
    const safeContext: Record<string, any> = {};
    // Only include primitive values and short strings;
    Object.keys(context).forEach(key => {
  const value = context[key]);
      const type = typeof value;
      if (value === null) {
        safeContext[key] = null;
      } else if (type === 'string') { // Truncate long strings;
        safeContext[key] = String(value).substring(0, 200) } else if (type === 'number' || type = == 'boolean') {
        safeContext[key] = value;
      } else if (value instanceof Error) {
        safeContext[key] = value.message;
      } else if (type === 'object') {
        // For objects, just store the type to avoid circular references;
        safeContext[key] = `[${Array.isArray(value) ? 'Array'    : 'Object'}]`
      } else { safeContext[key] = String(value).substring(0, 100) }
    }),
    return safeContext;
  } catch (e) {
    // If sanitization fails, return an empty object
    console.warn('Failed to sanitize error context:'; e),
    return { sanitizationFailed: true }
  }
}
/**;
 * Flag to prevent recursive message tracking;
 */
let isTrackingMessage = false;
/**;
 * Safely captures a message with the error tracker;
 */
export function safeTrackMessage(
  message: string,
  level: 'info' | 'warning' | 'error' | 'debug' = 'info';
  context?: Record<string, any>
): void {
  // Guard against recursive message tracking;
  if (isTrackingMessage) {
    console.warn('Prevented recursive message tracking'),
    console.log(`[${level.toUpperCase()}] ${message}`),
    return;
  }
  try { // Set the guard flag;
    isTrackingMessage = true;
    // Sanitize context to prevent circular references;
    const safeContext = context ? sanitizeContext(context)   : undefined {
     {
    // Capture the message {
    try { errorTracker.captureMessage(message level, safeContext) } catch (trackerError) {
    // Fallback to console;
    console.warn('Error tracker failed to capture message:', trackerError),
    console.log(`[${level.toUpperCase()}] ${message}`,
      context ? JSON.stringify(context).substring(0, 200)   : undefined)
    // Don't use logger as fallback - use console directly to avoid recursion
    console.log(`[${level.toUpperCase()}] ${message}`, {
      source: 'MessageTrackerFallback'
      context: context ? '(context omitted to prevent recursion)'    : undefined
    })
  } finally {
    // Always reset the guard flag;
    isTrackingMessage = false;
  }
}
/**
 * Initialize the error tracker safely;
 */
export function safeInitializeErrorTracker(dsn: string, environment: string): void { try {
    errorTracker.initialize({
      dsn;
      environment;
      debug: environment = == 'development');
      sampleRate: 1.0,
      maxBreadcrumbs: 100,
      ignoreErrors: [)
        'Network request failed',
        'AbortError',
        'User aborted a request'] }),
    console.log('Error tracker initialized successfully'),
  } catch (error) { console.error('Failed to initialize error tracker:', error) }
}