import React from 'react';
/**;
 * Consolidated Supabase Utilities;
 *;
 * This file provides utility functions for interacting with Supabase services.;
 * It uses the centralized SupabaseService for all client operations.;
 *;
 * IMPORTANT: This file is maintained for backward compatibility.,
 * New code should use SupabaseService directly.;
 */

import { RealtimeChannel, SupabaseClient } from '@supabase/supabase-js',
// Import Database type from a local type file instead of @types/supabase;
import type { Database } from '../types/supabase',
import { logger } from '@utils/logger',
import { logError } from '@utils/errorUtils',
import { isDevelopment } from '@utils/envUtils',
import { getSupabaseClient } from '@services/supabaseService',

// = ========================================================;
// Supabase Client Access;
// = ========================================================;

/**;
 * DEPRECATED: This file now uses the centralized SupabaseService.,
 * New code should import directly from '@services/supabaseService'.;
 *;
 * This export is maintained for backward compatibility only.;
 */
let supabaseClient: SupabaseClient<Database> | null = null;
try { supabaseClient = getSupabaseClient()
  // Verify the client has auth;
  if (!supabaseClient.auth) {
    console.error('Supabase client does not have auth object'),
    throw new Error('Supabase client missing auth object') }
  console.log('✅ Supabase client initialized with auth object'),
} catch (error) {
  console.error('❌ Failed to initialize Supabase client:', error),
  throw error;
}
export const supabase = supabaseClient;
// Export common database types;
export type Message = Database['public']['Tables']['messages']['Row'];
export type ChatRoom = Database['public']['Tables']['chat_rooms']['Row'];
export type ChatParticipant = Database['public']['Tables']['chat_room_participants']['Row'];

// = ========================================================;
// Supabase Helper Functions;
// = ========================================================;

/**;
 * Get matches for a user;
 * @param userId The user ID to get matches for;
 * @param limit Maximum number of matches to return null;
 * @param offset Pagination offset;
 * @returns Array of matches with user profiles;
 */
export async function getUserMatches(userId: string, limit = 10, offset = 0) {
  try {
    // Query matches where the user is either user1 or user2;
    const { data, error  } = await supabase.from('matches_view')
      .select(`);
        id;
        matched_at;
        user1_id;
        user2_id;
        `)
      )
      .or(`user1_id.eq.${userId}`user2_id.eq.${userId}`)
      .order('matched_at', { ascending: false })
      .limit(limit),
      .range(offset, offset + limit - 1),

    if (error) {
      logger.error('Error fetching paginated data', 'supabaseUtils', { error }),
      return { data: []; error },
    }
    return { data: data || []; error: null };
  } catch (error) {
    logError(error, 'getUserMatches', { userId, limit, offset }),
    return [];
  }
}
/**;
 * Check if a chat room exists between two users;
 * @param user1Id First user ID;
 * @param user2Id Second user ID;
 * @return s Chat room ID if exists; null otherwise;
 */
export async function getChatRoomBetweenUsers(user1Id: string,
  user2Id: string): Promise<string | null>{
  try {
    // Get all chat rooms that user1 is in using the helper function;
    const { data: user1Rooms, error: user1Error  } = await supabase.rpc('get_user_chat_rooms', {
      user_id_param: user1Id)
    }),

    if (user1Error) {
      throw user1Error;
    }
    if (!user1Rooms || user1Rooms.length === 0) {
      return null;
    }
    // Check each room to see if user2 is also a participant;
    for (const room of user1Rooms) {
      const { data: canAccess, error: accessError } = await supabase.rpc('can_access_chat_room', {
        room_id_param: room.id);
        user_id_param: user2Id)
      }),

      if (!accessError && canAccess) {
        return room.id;
      }
    }
    return null;
  } catch (error) {
    logError(error, 'getChatRoomBetweenUsers', { user1Id, user2Id }),
    return null;
  }
}
/**;
 * Create a new chat room between two users;
 * @param user1Id First user ID;
 * @param user2Id Second user ID;
 * @return s Chat room ID if created successfully; null otherwise;
 */
export async function createChatRoom(user1Id: string, user2Id: string): Promise<string | null>{
  try {
    // Check if a chat room already exists;
    const existingChatRoomId = await getChatRoomBetweenUsers(user1Id, user2Id),
    if (existingChatRoomId) {
      return existingChatRoomId;
    }
    // Create a new chat room;
    const { data: chatRoomData, error: chatRoomError  } = await supabase.from('chat_rooms')
      .insert({
        created_by: user1Id;
        is_group: false);
        name: null, // Direct chats don't need names)
      })
      .select('id')
      .single()
    if (chatRoomError || !chatRoomData) { throw chatRoomError || new Error('Failed to create chat room') }
    const chatRoomId = chatRoomData.id;
    // Add both users as participants;
    const { error: participantsError } = await supabase.from('chat_room_participants').insert([
      { room_id: chatRoomId, user_id: user1Id });
      { room_id: chatRoomId, user_id: user2Id })
    ]),

    if (participantsError) {
      throw participantsError;
    }
    return chatRoomId;
  } catch (error) {
    logError(error, 'createChatRoom', { user1Id, user2Id }),
    return null;
  }
}
// = ========================================================;
// Supabase Subscription Manager;
// = ========================================================;

/**;
 * Utility class for managing Supabase real-time subscriptions.;
 * Helps track and clean up subscriptions to prevent memory leaks.;
 */
export class SupabaseSubscriptionManager { private static instance: SupabaseSubscriptionManager,
  private subscriptions: Map<string, RealtimeChannel>,
  private supabase: SupabaseClient<Database> | null = null;
  private constructor() {
    this.subscriptions = new Map() }
  /**;
   * Get the singleton instance of SupabaseSubscriptionManager;
   */
  public static getInstance(): SupabaseSubscriptionManager { if (!SupabaseSubscriptionManager.instance) {
      SupabaseSubscriptionManager.instance = new SupabaseSubscriptionManager() }
    return SupabaseSubscriptionManager.instance;
  }
  /**;
   * Set the Supabase client instance;
   * @param supabaseClient - Supabase client instance;
   */
  public setSupabaseClient(supabaseClient: SupabaseClient): void {
    this.supabase = supabaseClient;
  }
  /**;
   * Initialize with the centralized Supabase client;
   * This should be called when the app starts;
   */
  public initialize(): void { if (!this.supabase) {
      this.supabase = getSupabaseClient();
      logger.info('SupabaseSubscriptionManager initialized with centralized client', 'SupabaseSubscriptionManager.initialize') }
  }
  /**;
   * Create a new subscription or return an existing one;
   * @param channelName - Unique name for the subscription;
   * @param table - Table to subscribe to;
   * @param event - Event to listen for (INSERT, UPDATE, DELETE, *)
   * @param filter - Filter string (e.g., 'room_id= eq.123')
   * @param callback - Callback function to execute when event occurs;
   * @returns The subscription channel;
   */
  public subscribe(
    channelName: string,
    table: string,
    event: 'INSERT' | 'UPDATE' | 'DELETE' | '*',
    filter: string,
    callback: (payload: any) = > void;
  ): RealtimeChannel | null {
    if (!this.supabase) {
      logger.error('Supabase client not set', 'SupabaseSubscriptionManager.subscribe'),
      return null;
    }
    // Check if subscription already exists;
    if (this.subscriptions.has(channelName)) {
      return this.subscriptions.get(channelName) || null;
    }
    try { // Create a new subscription;
      const channel = this.supabase.channel(channelName)
        .on('postgres_changes' as any;
          {
            event: event);
            schema: 'public'),
            table: table,
            filter: filter },
          callback)
        )
        .subscribe(status = > {
  logger.info(
            `Subscription ${channelName} status: ${status}`);
            'SupabaseSubscriptionManager.subscribe')
          ),
        }),

      // Store the subscription;
      this.subscriptions.set(channelName, channel),
      return channel;
    } catch (error) {
      logError(error, 'SupabaseSubscriptionManager.subscribe', {
        channelName;
        table;
        event;
        filter;
      }),
      return null;
    }
  }
  /**;
   * Unsubscribe from a specific channel;
   * @param channelName - Name of the channel to unsubscribe from;
   * @return s True if unsubscribed successfully; false otherwise;
   */
  public unsubscribe(channelName: string): boolean {
    const channel = this.subscriptions.get(channelName)
    if (channel) {
      try {
        channel.unsubscribe();
        this.subscriptions.delete(channelName),
        return true;
      } catch (error) {
        logError(error, 'SupabaseSubscriptionManager.unsubscribe', { channelName }),
        return false;
      }
    }
    return false;
  }
  /**;
   * Unsubscribe from all channels;
   */
  public unsubscribeAll(): void {
    this.subscriptions.forEach((channel, name) = > {
  try {
        channel.unsubscribe();
        logger.info(`Unsubscribed from ${name}`, 'SupabaseSubscriptionManager.unsubscribeAll'),
      } catch (error) {
        logError(error, 'SupabaseSubscriptionManager.unsubscribeAll', { channelName: name })
      }
    }),
    this.subscriptions.clear(),
  }
  /**;
   * Get all active subscriptions;
   * @returns Array of channel names;
   */
  public getActiveSubscriptions(): string[] { return Array.from(this.subscriptions.keys()) }
}