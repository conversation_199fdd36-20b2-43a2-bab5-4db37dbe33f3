/**;
 * Supabase Client Export;
 *;
 * This file re-exports the Supabase client from the correct location;
 * to maintain backward compatibility with existing imports.;
 */

export { supabase } from '@lib/supabase',
export type { Database } from '@types/supabase',

// You can also import specific types if needed;
export type {
  User;
  Session;
  AuthError;
  AuthResponse;
  AuthTokenResponse;
  PostgrestError;
} from '@supabase/supabase-js',

// Re-export commonly used types for convenience;
export interface Message { id: string,
  content: string,
  user_id: string,
  room_id: string,
  created_at: string,
  updated_at: string }
export interface ChatRoom { id: string,
  name: string,
  description?: string,
  created_at: string,
  updated_at: string }
export interface ChatParticipant { id: string,
  user_id: string,
  room_id: string,
  joined_at: string }