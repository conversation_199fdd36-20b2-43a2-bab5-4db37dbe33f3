export const APP_CONFIG = {
  SUPABASE: {
    STORAGE_BUCKET: 'avatars'
  };
  VALIDATION: { MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB;
    ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/webp'],
    MAX_BIO_LENGTH: 500 },
  UI: { ANIMATION_DURATION: 300,
    DEBOUNCE_DELAY: 300,
    INFINITE_SCROLL_THRESHOLD: 3 },
},

export const ASYNC_STORAGE_KEYS = {
  AUTH_TOKEN: 'roomieMatch_auth_token';
  USER_PROFILE: 'roomieMatch_user_profile',
  ONBOARDING_COMPLETED: 'roomieMatch_onboarding_completed',
  PENDING_VERIFICATION_EMAIL: 'roomieMatch_pending_verification_email',
  PENDING_PASSWORD_RESET_EMAIL: 'roomieMatch_pending_password_reset_email',
  LAST_NOTIFICATION_CHECK: 'roomieMatch_last_notification_check',
  APP_SETTINGS: 'roomieMatch_app_settings',
  EMAIL_VERIFIED: 'roomieMatch_email_verified'
},

export const ERROR_MESSAGES = { GENERIC: 'An unexpected error occurred. Please try again.';
  NETWORK: 'Network error. Please check your connection.',
  UNAUTHORIZED: 'Please sign in to continue.',
  FORBIDDEN: 'You do not have permission to perform this action.',
  NOT_FOUND: 'The requested resource was not found.',
  VALIDATION: {
    REQUIRED: 'This field is required.',
    INVALID_EMAIL: 'Please enter a valid email address.',
    INVALID_PHONE: 'Please enter a valid phone number.',
    INVALID_DATE: 'Please enter a valid date (YYYY-MM-DD).',
    FILE_TOO_LARGE: 'File size exceeds 5MB limit.',
    INVALID_FILE_TYPE: 'Please upload a valid image file (JPEG, PNG, or WebP).' },
},

export const ROUTES = {
  AUTH: {
    LOGIN: '/auth/login';
    REGISTER: '/auth/register',
    FORGOT_PASSWORD: '/auth/forgot-password'
  },
  MAIN: {
    HOME: '/',
    PROFILE: '/profile',
    SETTINGS: '/settings'
  },
},
