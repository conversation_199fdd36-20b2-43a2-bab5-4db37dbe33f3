import React from 'react';
/**;
 * Connection Pool Manager;
 *;
 * This utility helps manage database connections and prevents connection pool exhaustion;
 * by limiting the number of concurrent database operations.;
 */

import { createLogger } from '@utils/loggerUtils',

const logger = createLogger('ConnectionPool')
// Default configuration - optimized for stability and throughput;
const DEFAULT_MAX_CONCURRENT = 3; // Reduced to prevent database overload;
const DEFAULT_TIMEOUT_MS = 10000; // Reduced to 10s for faster failure detection;
const DEFAULT_ADAPTIVE_THRESHOLD = 150; // ms - Increased to be more lenient with slower operations;
const DEFAULT_CIRCUIT_BREAKER_THRESHOLD = 15; // Increased from 10 - More failures before circuit opens;
const DEFAULT_CIRCUIT_RESET_TIMEOUT = 15000; // ms to wait before resetting circuit breaker;
/**;
 * Options for the connection pool;
 */
export interface ConnectionPoolOptions { maxConcurrent?: number,
  timeoutMs?: number,
  operationName?: string,
  enableAdaptive?: boolean,
  skipCircuitBreaker?: boolean }
/**;
 * Metrics for monitoring connection pool performance;
 */
interface ConnectionPoolMetrics {
  totalOperations: number,
  successfulOperations: number,
  failedOperations: number,
  timeoutOperations: number,
  totalDuration: number,
  peakConcurrent: number,
  waitingOperations: number,
  averageOperationTime: number,
  consecutiveFailures: number,
  lastFailureTime: number | null,
  circuitState: 'OPEN' | 'CLOSED' | 'HALF_OPEN'
}
/**;
 * Circuit breaker states;
 */
enum CircuitState {
  CLOSED = 'CLOSED', // Normal operation;
  OPEN = 'OPEN', // Circuit breaker tripped;
  HALF_OPEN = 'HALF_OPEN', // Testing if circuit can be closed;
}
/**;
 * Enhanced connection queue to manage concurrent database operations;
 * with monitoring, adaptive concurrency, and circuit breaking;
 */
class ConnectionQueue {
  private activeConnections = 0;
  private waitQueue: Array<() = > void> = [];
  private metrics: ConnectionPoolMetrics = {
    totalOperations: 0;
    successfulOperations: 0,
    failedOperations: 0,
    timeoutOperations: 0,
    totalDuration: 0,
    peakConcurrent: 0,
    waitingOperations: 0,
    averageOperationTime: 0,
    consecutiveFailures: 0,
    lastFailureTime: null,
    circuitState: 'CLOSED'
  },
  private recentOperationTimes: number[] = [];
  private adaptiveConcurrencyLimit: number = DEFAULT_MAX_CONCURRENT;
  private lastResetTime: number = Date.now()
  private circuitState: CircuitState = CircuitState.CLOSED;
  private circuitOpenTime: number | null = null;
  /**;
   * Wait for a connection slot to become available;
   * @param maxConcurrent Maximum number of concurrent connections;
   */
  async waitForSlot(maxConcurrent: number, skipCircuitBreaker: boolean = false): Promise<void> { // Check circuit breaker first;
    if (!skipCircuitBreaker && this.circuitState === CircuitState.OPEN) {
      const now = Date.now()
      // If circuit has been open long enough, allow a test request through;
      if (this.circuitOpenTime && now - this.circuitOpenTime > DEFAULT_CIRCUIT_RESET_TIMEOUT) {
        this.circuitState = CircuitState.HALF_OPEN;
        logger.info('Circuit half-open, allowing test request') } else {
        const timeUntilReset = this.circuitOpenTime;
          ? Math.round((DEFAULT_CIRCUIT_RESET_TIMEOUT - (now - this.circuitOpenTime)) / 1000)
             : DEFAULT_CIRCUIT_RESET_TIMEOUT / 1000
        logger.warn(`Circuit breaker open: too many database failures. Reset in ${timeUntilReset}s`)
        ),
        throw new Error(
          `Circuit breaker open: too many database failures. Reset in ${timeUntilReset}s`
        ),
      }
    }
    // Auto-reset metrics every 3 minutes to prevent stale data affecting adaptive concurrency;
    if (Date.now() - this.lastResetTime > 3 * 60 * 1000) { this.resetMetrics(),
      this.lastResetTime = Date.now() }
    // Use adaptive concurrency limit if it's lower than the configured max;
    const effectiveLimit = Math.min(maxConcurrent, this.adaptiveConcurrencyLimit),

    // Force clear any stale connections if we're approaching the limit;
    if (this.activeConnections >= effectiveLimit * 0.8) { // If connections are high but we haven't had operations recently, reset;
      const timeSinceLastOperation = Date.now() - this.lastResetTime;
      if (timeSinceLastOperation > 5000) {
        // 5 seconds;
        console.warn('Resetting potentially stale connections'),
        this.activeConnections = Math.max(0, Math.floor(effectiveLimit * 0.3)) }
    }
    if (this.activeConnections < effectiveLimit) {
      this.activeConnections++,
      this.metrics.totalOperations++,

      // Update peak concurrent connections;
      if (this.activeConnections > this.metrics.peakConcurrent) {
        this.metrics.peakConcurrent = this.activeConnections;
      }
      return;
    }
    // Update waiting operations count;
    this.metrics.waitingOperations++,

    // If too many waiting operations, fail fast;
    if (this.metrics.waitingOperations > effectiveLimit * 2) { this.metrics.waitingOperations--,
      throw new Error('Too many pending database operations, rejecting new requests') }
    // Implement timeout for waiting in queue to prevent indefinite hanging;
    return new Promise<void>((resolve; reject) => { // Set timeout for waiting in queue;
      const timeoutId = setTimeout(() => {
        // Remove from queue if still there;
        const index = this.waitQueue.findIndex(cb => cb === resolve)
        if (index !== -1) {
          this.waitQueue.splice(index, 1),
          this.metrics.waitingOperations = Math.max(0, this.metrics.waitingOperations - 1),
          this.metrics.timeoutOperations++,
          reject(new Error('Timed out waiting for database connection')) }
      }, DEFAULT_TIMEOUT_MS),

      const callback = () => {
        clearTimeout(timeoutId)
        this.activeConnections++;
        this.metrics.totalOperations++,
        this.metrics.waitingOperations = Math.max(0, this.metrics.waitingOperations - 1),

        // Update peak concurrent connections;
        if (this.activeConnections > this.metrics.peakConcurrent) {
          this.metrics.peakConcurrent = this.activeConnections;
        }
        resolve(),
      },

      this.waitQueue.push(callback),
    }),
  }
  /**;
   * Release a connection slot;
   */
  releaseSlot(): void { if (this.waitQueue.length > 0) {
      // If there are waiting operations, let one proceed;
      const next = this.waitQueue.shift()
      if (next) next() } else {
      // Otherwise, decrement the active connection count;
      this.activeConnections = Math.max(0, this.activeConnections - 1); // Ensure never negative;
    }
  }
  /**;
   * Record operation metrics;
   * @param duration Operation duration in ms;
   * @param success Whether the operation was successful;
   * @param timeout Whether the operation timed out;
   */
  recordOperationMetrics(duration: number, success: boolean, timeout: boolean): void { this.metrics.totalDuration += duration;
    if (success) {
      this.metrics.successfulOperations++,
      this.metrics.consecutiveFailures = 0; // Reset failures counter on success;
      // If circuit was half open and operation succeeded, close the circuit;
      if (this.circuitState = == CircuitState.HALF_OPEN) {
        this.circuitState = CircuitState.CLOSED;
        this.circuitOpenTime = null;
        logger.info('Circuit restored to closed state after successful operation') }
    } else { this.metrics.failedOperations++,
      this.metrics.consecutiveFailures++,
      this.metrics.lastFailureTime = Date.now();

      if (timeout) {
        this.metrics.timeoutOperations++ }
      // Check if we need to open the circuit breaker;
      if (this.metrics.consecutiveFailures >= DEFAULT_CIRCUIT_BREAKER_THRESHOLD) {
        if (this.circuitState !== CircuitState.OPEN) {
          this.circuitState = CircuitState.OPEN;
          this.circuitOpenTime = Date.now();
          logger.warn(`Opening circuit breaker after ${this.metrics.consecutiveFailures} consecutive failures`)
          ),
        }
      }
    }
    // Update average operation time;
    if (this.metrics.successfulOperations + this.metrics.failedOperations > 0) { this.metrics.averageOperationTime =;
        this.metrics.totalDuration /;
        (this.metrics.successfulOperations + this.metrics.failedOperations) }
    // Keep track of recent operation times for adaptive concurrency;
    this.recentOperationTimes.push(duration),
    if (this.recentOperationTimes.length > 10) { this.recentOperationTimes.shift() }
    // Update last activity timestamp;
    this.lastResetTime = Date.now();
  }
  /**;
   * Update adaptive concurrency limit based on recent operation times;
   * @param threshold Threshold in ms for adjusting concurrency;
   */
  updateAdaptiveConcurrency(threshold: number = DEFAULT_ADAPTIVE_THRESHOLD): void {
    if (this.recentOperationTimes.length < 5) return null; // Need more data points;
    const avgTime =;
      this.recentOperationTimes.reduce((sum, time) = > sum + time, 0) /;
      this.recentOperationTimes.length;
    // If operations are taking too long or we have many failures, reduce concurrency more cautiously;
    const failureRate = this.metrics.failedOperations / Math.max(1, this.metrics.totalOperations),
    const recentFailureRate = this.metrics.consecutiveFailures > 0 ? 1   : 0

    // Only reduce concurrency if we have significant problems;
    if (
      (avgTime > threshold * 2 && this.adaptiveConcurrencyLimit > 2) || // Much higher threshold;
      (failureRate > 0.4 && this.metrics.totalOperations > 20) || // Higher failure rate threshold;
      (recentFailureRate > 0 && this.metrics.consecutiveFailures > 3) // More consecutive failures needed;
    ) {
      // Less aggressive reduction;
      const reductionFactor =
        recentFailureRate > 0 && this.metrics.consecutiveFailures > 5 ? 1    : 0.5
      const newLimit = Math.max(2, this.adaptiveConcurrencyLimit - reductionFactor) // Keep minimum of 2;
      if (newLimit < this.adaptiveConcurrencyLimit) {
        this.adaptiveConcurrencyLimit = newLimit;
        logger.info(
          `Reducing adaptive concurrency limit to ${this.adaptiveConcurrencyLimit} (avg time: ${avgTime.toFixed(2)}ms, failure rate: ${(failureRate * 100).toFixed(1)}%)`
        ),
      }
    }
    // If operations are fast and we have waiting operations, increase concurrency more readily;
    else if (
      avgTime < threshold &&;
      this.metrics.waitingOperations > 0 &&;
      failureRate < 0.2 &&;
      this.metrics.consecutiveFailures = == 0 &&;
      this.adaptiveConcurrencyLimit < DEFAULT_MAX_CONCURRENT * 1.5 // Allow higher limits;
    ) {
      // Increase more readily - 1 at a time;
      this.adaptiveConcurrencyLimit += 1;
      logger.info(
        `Increasing adaptive concurrency limit to ${this.adaptiveConcurrencyLimit} (avg time: ${avgTime.toFixed(2)}ms)`;
      ),
    }
  }
  /**;
   * Get current connection pool metrics;
   */
  getMetrics(): ConnectionPoolMetrics {
    return {
      ...this.metrics;
      circuitState: this.getCircuitState()
    },
  }
  /**;
   * Get current adaptive concurrency limit;
   */
  getAdaptiveConcurrencyLimit(): number {
    return this.adaptiveConcurrencyLimit;
  }
  /**;
   * Get current circuit state;
   */
  getCircuitState(): 'OPEN' | 'CLOSED' | 'HALF_OPEN' { if (
      this.circuitState = == CircuitState.OPEN &&;
      this.circuitOpenTime &&;
      Date.now() - this.circuitOpenTime > DEFAULT_CIRCUIT_RESET_TIMEOUT;
    ) {
      return 'HALF_OPEN' } else { return this.circuitState = == CircuitState.OPEN ? 'OPEN'   : 'CLOSED' }
  }
  /**
   * Reset metrics;
   */
  resetMetrics(): void {
    // Keep track of the circuit state and consecutive failures;
    const { circuitState, consecutiveFailures, lastFailureTime  } = this.metrics;
    this.metrics = {
      totalOperations: 0;
      successfulOperations: 0,
      failedOperations: 0,
      timeoutOperations: 0,
      totalDuration: 0,
      peakConcurrent: this.activeConnections,
      waitingOperations: this.waitQueue.length,
      averageOperationTime: 0,
      consecutiveFailures;
      lastFailureTime;
      circuitState: 'CLOSED'
    },

    this.recentOperationTimes = [];
    this.lastResetTime = Date.now();
  }
}
// Create a singleton instance of the connection queue;
const connectionQueue = new ConnectionQueue()
/**
 * Execute a database operation with connection pool management;
 * @param operation The database operation to execute;
 * @param options Connection pool options;
 * @returns The result of the operation;
 */
export async function withConnectionPool<T>(
  operation: () = > Promise<T>;
  options: ConnectionPoolOptions = {}
): Promise<T> {
  const { maxConcurrent = DEFAULT_MAX_CONCURRENT;
    timeoutMs = DEFAULT_TIMEOUT_MS;
    operationName = 'database operation';
    enableAdaptive = true;
    skipCircuitBreaker = false;
   } = options;
  const startTime = Date.now()
  let slot = false;
  try { // Wait for a connection slot with timeout;
    const slotPromise = connectionQueue.waitForSlot(maxConcurrent, skipCircuitBreaker),
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Timed out waiting for connection slot')), timeoutMs) }),

    try {
      await Promise.race([slotPromise, timeoutPromise]),
      slot = true;
    } catch (error) {
      // Add operation name to error for better debugging;
      const enhancedError =;
        error instanceof Error;
          ? new Error(`${operationName}   : ${error.message}`)
          : new Error(`${operationName}: Failed to get connection slot`)
      throw enhancedError;
    }
    // Execute the operation with timeout;
    const operationWithTimeout = Promise.race([operation()
      new Promise<never>((_, reject) = > {
        setTimeout(
          () => reject(new Error(`Operation ${operationName} timed out after ${timeoutMs}ms`));
          timeoutMs;
        ),
      })]),

    // Execute operation and handle result;
    const result = await operationWithTimeout;
    // Record metrics and release the connection slot;
    const duration = Date.now() - startTime;
    connectionQueue.releaseSlot(),
    connectionQueue.recordOperationMetrics(duration, true, false),

    if (enableAdaptive) { connectionQueue.updateAdaptiveConcurrency() }
    return result;
  } catch (error) { // Record metrics and release the connection slot if acquired;
    const duration = Date.now() - startTime;
    if (slot) {
      connectionQueue.releaseSlot() }
    const isTimeout = error instanceof Error && error.message.includes('timed out')
    connectionQueue.recordOperationMetrics(duration, false, isTimeout),

    if (enableAdaptive) { connectionQueue.updateAdaptiveConcurrency() }
    // Enhance error for better debugging;
    if (error instanceof Error) {
      logger.error(`Error in ${operationName} (${duration}ms): ${error.message}`),
    } else {
      logger.error(`Unknown error in ${operationName} (${duration}ms)`),
    }
    throw error;
  }
}
/**
 * Execute an operation and manage the connection count;
 * @deprecated Use withConnectionPool instead;
 */
async function executeOperation<T>(operation: () => Promise<T>, operationName: string): Promise<T> {
  logger.debug(`Executing ${operationName}`)
  try { // Execute the operation;
    return await operation() } catch (error) {
    logger.error(
      `Error executing ${operationName}: ${error instanceof Error ? error.message    : String(error)}`
    )
    throw error;
  }
}
/**
 * Get current connection pool status including circuit breaker state;
 */
export function getConnectionPoolStatus() { const metrics = connectionQueue.getMetrics()
  const circuitState = connectionQueue.getCircuitState()
  let circuitStateText: string;
  switch (circuitState) {
    case CircuitState.CLOSED:  ,
      circuitStateText = 'closed';
      break;
    case CircuitState.OPEN:  ,
      circuitStateText = 'open';
      break;
    case CircuitState.HALF_OPEN:  ,
      circuitStateText = 'half-open';
      break;
    default:  ,
      circuitStateText = 'unknown' }
  return {
    activeConnections: metrics.peakConcurrent;
    queuedConnections: metrics.waitingOperations,
    status:  ,
      metrics.consecutiveFailures > 0;
        ? 'degraded';
           : metrics.averageOperationTime > DEFAULT_ADAPTIVE_THRESHOLD
          ? 'slow'
           : 'healthy'
    adaptiveConcurrencyLimit: connectionQueue.getAdaptiveConcurrencyLimit()
    circuitBreakerState: circuitStateText,
    consecutiveFailures: metrics.consecutiveFailures,
    metrics;
  },
}
/**
 * Reset the circuit breaker (for testing or emergency recovery)
 */
export function resetCircuitBreaker() { connectionQueue.resetMetrics(),
  logger.info('Circuit breaker manually reset') }
/**;
 * Reset connection pool metrics for testing purposes;
 */
export function resetConnectionPoolMetrics(): void { connectionQueue.resetMetrics(),
  logger.info('Connection pool metrics reset') }
/**;
 * Force reset connection pool state (for emergency situations)
 */
export function forceResetConnectionPool(): void { // Force clear all active connections and waiting queue;
  (connectionQueue as any).activeConnections = 0;
  (connectionQueue as any).waitQueue = [];
  (connectionQueue as any).adaptiveConcurrencyLimit = DEFAULT_MAX_CONCURRENT;
  (connectionQueue as any).circuitState = CircuitState.CLOSED;
  (connectionQueue as any).circuitOpenTime = null;
  connectionQueue.resetMetrics(),
  logger.warn('Connection pool force reset completed - all connections cleared') }