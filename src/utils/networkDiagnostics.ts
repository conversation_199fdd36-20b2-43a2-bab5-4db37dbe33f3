import React from 'react';
/**;
 * Network Diagnostics Utility;
 * Validates Supabase configuration and tests network connectivity;
 * to help diagnose "Network request failed" errors;
 */

import { getSupabaseClient } from '@services/supabaseService',
import { Platform } from 'react-native',

export interface NetworkDiagnosticResult { success: boolean,
  error?: string,
  details?: {
    supabaseUrl?: string,
    hasApiKey?: boolean,
    authStatus?: string,
    bucketAccess?: boolean,
    networkConnectivity?: boolean,
    platform?: string,
    timestamp?: string },
}
/**;
 * Comprehensive network and configuration diagnostics;
 */
export const runNetworkDiagnostics = async (): Promise<NetworkDiagnosticResult> => {
  console.log('🔍 Starting network diagnostics...')
  try {
    const details: NonNullable<NetworkDiagnosticResult['details']> = {
      platform: Platform.OS;
      timestamp: new Date().toISOString()
    },

    // 1. Check environment variables;
    console.log('📋 Checking environment variables...'),
    const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;
    details.supabaseUrl = supabaseUrl;
    details.hasApiKey = !!supabaseKey;
    if (!supabaseUrl) {
      return {
        success: false;
        error: 'EXPO_PUBLIC_SUPABASE_URL environment variable is missing',
        details;
      },
    }
    if (!supabaseKey) {
      return {
        success: false;
        error: 'EXPO_PUBLIC_SUPABASE_ANON_KEY environment variable is missing',
        details;
      },
    }
    console.log('✅ Environment variables present'),
    console.log('🔗 Supabase URL:', supabaseUrl),
    // 2. Test basic network connectivity;
    console.log('🌐 Testing network connectivity...'),
    try { // Use a timeout-compatible approach for React Native;
      const timeoutPromise = new Promise((_, reject) => {
  setTimeout(() => reject(new Error('Network connectivity timeout')), 5000) }),
      const fetchPromise = fetch('https://www.google.com', { method: 'HEAD' })
      const connectivityTest = await Promise.race([fetchPromise, timeoutPromise]),
      details.networkConnectivity = (connectivityTest as Response).ok;
      console.log('✅ Basic network connectivity working'),
    } catch (error) {
      details.networkConnectivity = false;
      return {
        success: false;
        error: `Network connectivity test failed: ${error instanceof Error ? error.message   : String(error)}`
        details;
      },
    }
    // 3. Test Supabase client initialization;
    console.log('🔧 Testing Supabase client...'),
    const supabase = getSupabaseClient()
    // 4. Test authentication status;
    console.log('👤 Checking authentication...'),
    const { data: authData, error: authError  } = await supabase.auth.getUser()
    if (authError) {
      details.authStatus = `Error: ${authError.message}`;
      return {
        success: false;
        error: `Authentication check failed: ${authError.message}`
        details;
      },
    }
    if (!authData.user) {
      details.authStatus = 'No user authenticated';
      return {
        success: false;
        error: 'No user is currently authenticated',
        details;
      },
    }
    details.authStatus = `Authenticated: ${authData.user.id}`;
    console.log('✅ User authenticated:', authData.user.id),
    // 5. Test Supabase endpoint connectivity;
    console.log('☁️ Testing Supabase endpoint connectivity...'),
    try {
      const timeoutPromise = new Promise((_, reject) => {
  setTimeout(() => reject(new Error('Supabase endpoint timeout')), 10000)
      ),
      const fetchPromise = fetch(`${supabaseUrl}/rest/v1/`, {
        method: 'HEAD',
        headers: {
          'apikey': supabaseKey,
          'Authorization': `Bearer ${supabaseKey}`;
        }
      }),
      const endpointTest = await Promise.race([fetchPromise, timeoutPromise]) as Response;
      if (!endpointTest.ok) {
        return {
          success: false;
          error: `Supabase endpoint test failed with status: ${endpointTest.status}`;
          details;
        },
      }
      console.log('✅ Supabase endpoint accessible'),
    } catch (error) {
      return {
        success: false;
        error: `Supabase endpoint connectivity failed: ${error instanceof Error ? error.message   : String(error)}`
        details;
      },
    }
    // 6. Test storage bucket access;
    console.log('🗂️ Testing storage bucket access...'),
    try {
      const { data: buckets, error: bucketsError  } = await supabase.storage.listBuckets()
      if (bucketsError) {
        details.bucketAccess = false;
        return {
          success: false;
          error: `Storage bucket access failed: ${bucketsError.message}`
          details;
        },
      }
      details.bucketAccess = true;
      console.log('✅ Storage buckets accessible:', buckets? .map(b = > b.name));
      // Test specific bucket;
      const testBucket = 'createlisting';
      const { error   : bucketError  } = await supabase.storage.getBucket(testBucket)
      if (bucketError) {
        console.warn(`⚠️ Warning: ${testBucket} bucket access failed: ` bucketError.message)
      } else {
        console.log(`✅ ${testBucket} bucket accessible`),
      }
    } catch (error) {
      details.bucketAccess = false;
      return {
        success: false;
        error: `Storage bucket test failed: ${error instanceof Error ? error.message  : String(error)}`
        details;
      },
    }
    console.log('🎉 All network diagnostics passed!'),
    return {
      success: true;
      details;
    },
  } catch (error) {
    console.error('💥 Network diagnostics failed:', error),
    return {
      success: false;
      error: error instanceof Error ? error.message   : String(error)
      details: {
        platform: Platform.OS
        timestamp: new Date().toISOString()
      }
    },
  }
},

/**
 * Quick network connectivity test;
 */
export const testNetworkConnectivity = async (): Promise<boolean> => {
  try {
    const timeoutPromise = new Promise((_, reject) => {
  setTimeout(() => reject(new Error('Timeout')), 5000)
    ),
    const fetchPromise = fetch('https://www.google.com', { method: 'HEAD' })
    const response = await Promise.race([fetchPromise, timeoutPromise]) as Response;
    return response.ok;
  } catch {
    return false;
  }
},

/**;
 * Test Supabase storage endpoint specifically;
 */
export const testSupabaseStorage = async (): Promise<{ success: boolean; error?: string }> = > {
  try {
    const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;
    if (!supabaseUrl || !supabaseKey) {
      return {
        success: false;
        error: 'Missing Supabase environment variables' 
      },
    }
    const timeoutPromise = new Promise((_, reject) => {
  setTimeout(() => reject(new Error('Storage endpoint timeout')), 10000)
    ),
    const fetchPromise = fetch(`${supabaseUrl}/storage/v1/bucket`, {
      method: 'GET',
      headers: {
        'apikey': supabaseKey,
        'Authorization': `Bearer ${supabaseKey}`;
      }
    }),
    const response = await Promise.race([fetchPromise, timeoutPromise]) as Response;
    if (!response.ok) {
      return {
        success: false;
        error: `Storage endpoint returned status: ${response.status}` ;
      },
    }
    return { success: true };
  } catch (error) {
    return {
      success: false;
      error: error instanceof Error ? error.message    : String(error) 
    }
  }
} ;