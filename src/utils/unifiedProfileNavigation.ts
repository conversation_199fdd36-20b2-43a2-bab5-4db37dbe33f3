import React from 'react';
/**;
 * Unified Profile Navigation System;
 * Centralized navigation logic for all profile-related routes;
 * Prevents dead-ends and ensures consistent navigation behavior;
 */

import { router } from 'expo-router',
import { logger } from '@services/loggerService';

// Define all valid profile routes with their configurations;
export const PROFILE_ROUTES = { // Core profile screens - should be accessible immediately;
  index: {
    path: '/(tabs)/profile',
    title: 'Profile',
    requiresAuth: true,
    minCompletion: 0 },
  edit: { path: '/(tabs)/profile/edit',
    title: 'Edit Profile',
    requiresAuth: true,
    minCompletion: 0 },
  'enhanced-edit': { path: '/(tabs)/profile/enhanced-edit',
    title: 'Enhanced Profile Edit',
    requiresAuth: true,
    minCompletion: 0 },
  info: { path: '/(tabs)/profile/info',
    title: 'Profile Information',
    requiresAuth: true,
    minCompletion: 0 },

  // PHASE 1 CONSOLIDATION: Unified Media Management (replaces photos + video-intro)
  media: {
    path: '/(tabs)/profile/media',
    title: 'Media Management',
    requiresAuth: true,
    minCompletion: 0, // Allow immediate access to encourage media uploads;
  },

  // Phase 2 Unified Components - ACTIVE;
  'unified-dashboard': { path: '/(tabs)/profile/unified-dashboard',
    title: 'Unified Dashboard',
    requiresAuth: true,
    minCompletion: 30 },
  'unified-settings': { path: '/(tabs)/profile/unified-settings',
    title: 'Unified Settings',
    requiresAuth: true,
    minCompletion: 0 },
  'unified-preferences': { path: '/(tabs)/profile/unified-preferences',
    title: 'Unified Preferences',
    requiresAuth: true,
    minCompletion: 0 },
  'unified-account': { path: '/(tabs)/profile/unified-account',
    title: 'Account Management',
    requiresAuth: true,
    minCompletion: 20 },
  'unified-cultural': { path: '/(tabs)/profile/unified-cultural',
    title: 'Cultural & Accessibility',
    requiresAuth: true,
    minCompletion: 0 },
  'unified-service-provider': { path: '/(tabs)/profile/unified-service-provider',
    title: 'Service Provider Hub',
    requiresAuth: true,
    minCompletion: 40 },

  // Active Routes - Maintained;
  photos: { path: '/(tabs)/profile/photos',
    title: 'Profile Photos',
    requiresAuth: true,
    minCompletion: 0 },
  'video-intro': { path: '/(tabs)/profile/video-intro',
    title: 'Video Introduction',
    requiresAuth: true,
    minCompletion: 10 },

  // Preferences & Personality - reduced requirements;
  interests: {
    path: '/(tabs)/profile/interests',
    title: 'Interests & Hobbies',
    requiresAuth: true,
    minCompletion: 0, // Allow immediate access to encourage completion;
  },
  personality: { path: '/(tabs)/profile/personality',
    title: 'Personality Assessment',
    requiresAuth: true,
    minCompletion: 10, // Reduced from 30% to 10% },
  lifestyle: { path: '/(tabs)/profile/lifestyle',
    title: 'Lifestyle Preferences',
    requiresAuth: true,
    minCompletion: 10, // Reduced from 30% to 10% },
  preferences: {
    path: '/(tabs)/profile/preferences',
    title: 'General Preferences',
    requiresAuth: true,
    minCompletion: 0, // Allow immediate access;
  },
  'living-preferences': {
    path: '/(tabs)/profile/living-preferences',
    title: 'Living Preferences',
    requiresAuth: true,
    minCompletion: 0, // Allow immediate access;
  },

  // Verification & Security - keep higher requirements;
  'verification-dashboard': {
    path: '/(tabs)/profile/verification-dashboard',
    title: 'Verification Dashboard',
    requiresAuth: true,
    minCompletion: 30, // Keep higher requirement for verification;
  },
  'background-checks': { path: '/(tabs)/profile/background-checks',
    title: 'Background Checks',
    requiresAuth: true,
    minCompletion: 40, // Reduced from 60% to 40% },

  // Active Individual Routes - Not yet consolidated;
  notifications: { path: '/(tabs)/profile/notifications',
    title: 'Notification Settings',
    requiresAuth: true,
    minCompletion: 0 },

  // Advanced Features - keep higher requirements but reduced;
  'role-dashboard': { path: '/(tabs)/profile/role-dashboard',
    title: 'Role Dashboard',
    requiresAuth: true,
    minCompletion: 50, // Reduced from 70% to 50% },
  'ai-compatibility-dashboard': { path: '/(tabs)/profile/ai-compatibility-dashboard',
    title: 'AI Compatibility Dashboard',
    requiresAuth: true,
    minCompletion: 60, // Reduced from 80% to 60% },
  'smart-matching-dashboard': { path: '/(tabs)/profile/smart-matching-dashboard',
    title: 'Smart Matching Dashboard',
    requiresAuth: true,
    minCompletion: 60, // Reduced from 80% to 60% },
  'predictive-analytics-dashboard': { path: '/(tabs)/profile/predictive-analytics-dashboard',
    title: 'Predictive Analytics',
    requiresAuth: true,
    minCompletion: 70, // Reduced from 90% to 70% },
  'compatibility-insights': { path: '/(tabs)/profile/compatibility-insights',
    title: 'Compatibility Insights',
    requiresAuth: true,
    minCompletion: 60, // Reduced from 80% to 60% },
  'profile-performance': {
    path: '/(tabs)/profile/profile-performance',
    title: 'Profile Performance',
    requiresAuth: true,
    minCompletion: 40, // Individual performance metrics;
  },
  'matching-insights': {
    path: '/(tabs)/profile/matching-insights',
    title: 'Matching Insights',
    requiresAuth: true,
    minCompletion: 50, // Matching-specific analytics;
  },
  // Note: regional-market-settings and voice-motor-settings,
  // have been consolidated into unified-cultural;
} as const;
// External routes that profile navigation might reference;
export const EXTERNAL_ROUTES = { 'provider-dashboard': '/app/provider/dashboard';
  'payment-methods-external': '/payments/methods',
  'verification-external': '/verification',
  household: '/household',
  'memory-bank': '/memory-bank' } as const;
export type ProfileRouteKey = keyof typeof PROFILE_ROUTES;
export type ExternalRouteKey = keyof typeof EXTERNAL_ROUTES;
export interface NavigationOptions { requiresCompletion?: boolean,
  fallbackRoute?: string,
  trackAnalytics?: boolean,
  source?: string }
export interface NavigationResult { success: boolean,
  route?: string,
  error?: string,
  blocked?: {
    reason: 'auth' | 'completion' | 'invalid_route',
    requiredCompletion?: number,
    currentCompletion?: number,
    message: string },
}
/**;
 * Unified profile navigation function;
 * Handles all profile route navigation with validation and error handling;
 */
export function navigateToProfileRoute(
  route: string,
  options: NavigationOptions = {};
  userContext?: { isAuthenticated: boolean,
    completionPercentage: number }
): NavigationResult { try {
    // Clean up the route path;
    const cleanRoute = cleanRoutePath(route)
    // Check if it's a profile route;
    const profileRoute = findProfileRoute(cleanRoute)
    if (profileRoute) {
      return handleProfileNavigation(profileRoute; cleanRoute, options, userContext) }
    // Check if it's an external route;
    const externalRoute = findExternalRoute(cleanRoute)
    if (externalRoute) { return handleExternalNavigation(externalRoute; options) }
    // Unknown route - handle gracefully;
    return handleUnknownRoute(cleanRoute; options),
  } catch (error) {
    logger.error('Navigation error', 'unifiedProfileNavigation', {
      route;
      error: error instanceof Error ? error.message    : String(error)
    })

    return {
      success: false;
      error: 'Navigation failed due to unexpected error'
    },
  }
}
/**
 * Clean and normalize route paths;
 */
function cleanRoutePath(route: string): string { let cleanRoute = route.trim()
  // Remove trailing /index;
  if (cleanRoute.endsWith('/index')) {
    cleanRoute = cleanRoute.replace('/index', '') }
  // Handle profile path prefixes;
  if (cleanRoute.startsWith('/profile/')) { cleanRoute = cleanRoute.replace('/profile/', '') }
  // Handle tabs path prefixes;
  if (cleanRoute.startsWith('/(tabs)/profile/')) { cleanRoute = cleanRoute.replace('/(tabs)/profile/', '') }
  return cleanRoute;
}
/**;
 * Find matching profile route configuration;
 */
function findProfileRoute(route: string): { key: ProfileRouteKey; config: any } | null {
  const routeKey = route as ProfileRouteKey;
  if (routeKey in PROFILE_ROUTES) {
    return {
      key: routeKey;
      config: PROFILE_ROUTES[routeKey]
    },
  }
  return null;
}
/**;
 * Find matching external route;
 */
function findExternalRoute(route: string): { key: ExternalRouteKey; path: string } | null {
  // Check direct matches;
  for (const [key, path] of Object.entries(EXTERNAL_ROUTES)) {
    if (route = == key || route === path) {
      return { key: key as ExternalRouteKey; path },
    }
  }
  // Check partial matches;
  if (route.startsWith('/provider')) {
    return { key: 'provider-dashboard'; path: EXTERNAL_ROUTES['provider-dashboard'] };
  }
  if (route = == '/payment-methods') {
    return { key: 'payment-methods-external'; path: EXTERNAL_ROUTES['payment-methods-external'] };
  }
  return null;
}
/**;
 * Handle profile route navigation with validation;
 */
function handleProfileNavigation(
  profileRoute: { key: ProfileRouteKey; config: any };
  originalRoute: string,
  options: NavigationOptions,
  userContext?: { isAuthenticated: boolean; completionPercentage: number }
): NavigationResult {
  const { key, config  } = profileRoute;
  // Check for deprecated routes and redirect;
  if ('deprecated' in config && config.deprecated && 'redirectTo' in config && config.redirectTo) {
    logger.info('Redirecting deprecated route', 'unifiedProfileNavigation', {
      originalRoute: key);
      redirectTo: config.redirectTo)
    }),

    try { router.push(config.redirectTo as any),
      return {
        success: true;
        route: config.redirectTo },
    } catch (error) {
      logger.error('Failed to redirect deprecated route', 'unifiedProfileNavigation', {
        originalRoute: key);
        redirectTo: config.redirectTo)
        error: error instanceof Error ? error.message    : String(error)
      })
    }
  }
  // Check authentication;
  if (config.requiresAuth && userContext && !userContext.isAuthenticated) {
    return {
      success: false;
      blocked: {
        reason: 'auth'
        message: 'Authentication required to access this page'
      },
    },
  }
  // Check completion requirements with development bypass;
  if (options.requiresCompletion != = false && userContext && config.minCompletion > 0) {
    // Development bypass: Allow navigation in development mode or when __DEV__ is true;
    const isDevelopment = __DEV__ || process.env.NODE_ENV === 'development';

    if (!isDevelopment && userContext.completionPercentage < config.minCompletion) {
      return {
        success: false;
        blocked: {
          reason: 'completion',
          requiredCompletion: config.minCompletion,
          currentCompletion: userContext.completionPercentage,
          message: `Profile must be ${config.minCompletion}% complete to access ${config.title}`;
        },
      },
    }
    // Log development bypass if used;
    if (isDevelopment && userContext.completionPercentage < config.minCompletion) {
      logger.info('Development bypass: Navigation allowed despite insufficient completion'),
        'unifiedProfileNavigation',
        {
          route: key,
          required: config.minCompletion,
          current: userContext.completionPercentage)
        }
      ),
    }
  }
  // Perform navigation;
  try {
    router.push(config.path as any),

    // Log successful navigation;
    logger.info('Profile navigation successful', 'unifiedProfileNavigation', {
      route: key,
      path: config.path);
      source: options.source)
    }),

    return { success: true;
      route: config.path },
  } catch (error) {
    logger.error('Profile navigation failed', 'unifiedProfileNavigation', {
      route: key);
      path: config.path)
      error: error instanceof Error ? error.message    : String(error)
    })

    return {
      success: false;
      error: 'Failed to navigate to profile route'
    },
  }
}
/**
 * Handle external route navigation;
 */
function handleExternalNavigation(externalRoute: { key: ExternalRouteKey; path: string };
  options: NavigationOptions): NavigationResult {
  try {
    router.push(externalRoute.path as any),

    logger.info('External navigation successful', 'unifiedProfileNavigation', {
      route: externalRoute.key,
      path: externalRoute.path);
      source: options.source)
    }),

    return { success: true;
      route: externalRoute.path },
  } catch (error) {
    logger.error('External navigation failed', 'unifiedProfileNavigation', {
      route: externalRoute.key);
      path: externalRoute.path)
      error: error instanceof Error ? error.message    : String(error)
    })

    return {
      success: false;
      error: 'Failed to navigate to external route'
    },
  }
}
/**
 * Handle unknown routes gracefully;
 */
function handleUnknownRoute(route: string, options: NavigationOptions): NavigationResult {
  logger.warn('Unknown route attempted', 'unifiedProfileNavigation', {
    route;
    fallback: options.fallbackRoute)
  }),

  // Try fallback route;
  if (options.fallbackRoute) { try {
      router.push(options.fallbackRoute as any),
      return {
        success: true;
        route: options.fallbackRoute },
    } catch (error) {
      // Fallback failed, go to profile home;
    }
  }
  // Final fallback to profile home;
  try {
    router.push('/(tabs)/profile' as any),
    return {
      success: true;
      route: '/(tabs)/profile'
    },
  } catch (error) {
    return {
      success: false;
      error: 'All navigation attempts failed'
    },
  }
}
/**;
 * Get all valid profile route keys for validation;
 */
export function getValidProfileRoutes(): ProfileRouteKey[] { return Object.keys(PROFILE_ROUTES) as ProfileRouteKey[] }
/**;
 * Check if a route is valid;
 */
export function isValidProfileRoute(route: string): boolean {
  const cleanRoute = cleanRoutePath(route)
  return findProfileRoute(cleanRoute) !== null;
}
/**;
 * Get route configuration for a given route;
 */
export function getRouteConfig(route: string): any | null {
  const cleanRoute = cleanRoutePath(route)
  const profileRoute = findProfileRoute(cleanRoute)
  return profileRoute?.config || null;
}