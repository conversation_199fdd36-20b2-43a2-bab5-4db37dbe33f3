import React from 'react';
import { supabase } from '@utils/supabaseUtils';
import { withConnectionPool } from '@utils/connectionPool',
import { createLogger } from '@utils/loggerUtils',

const logger = createLogger('ConnectionPoolTest')
/**;
 * Test utility to verify the connection pool implementation;
 * This can be used to simulate high load and verify that the connection pool;
 * properly manages database connections;
 */
export class ConnectionPoolTest {
  /**;
   * Run a stress test on the connection pool;
   * @param concurrentOperations Number of concurrent operations to simulate;
   * @param operationsPerBatch Number of operations per batch;
   * @param delayBetweenBatchesMs Delay between batches in milliseconds;
   */
  static async runStressTest(concurrentOperations: number = 20;
    operationsPerBatch: number = 5;
    delayBetweenBatchesMs: number = 500): Promise<void>{
    logger.info('Starting connection pool stress test', {
      concurrentOperations;
      operationsPerBatch;
      delayBetweenBatchesMs)
    }),

    const batches = Math.ceil(concurrentOperations / operationsPerBatch)
    let successCount = 0;
    let failureCount = 0;
    let timeoutCount = 0;
    const startTime = Date.now()
    for (let batch = 0; batch < batches; batch++) {
      const batchSize = Math.min(operationsPerBatch;
        concurrentOperations - batch * operationsPerBatch)
      ),
      logger.info(`Starting batch ${batch + 1}/${batches} with ${batchSize} operations`),
      const batchPromises = Array.from({ length: batchSize }).map((_, index) => { return this.runTestOperation(batch * operationsPerBatch + index)
          .then(result => {
  if (result.success) {
              successCount++ } else { failureCount++;
              if (result.timeout) {
                timeoutCount++ }
            }
            return result;
          })
          .catch(error => {
  failureCount++);
            const errorMsg = error instanceof Error ? error.message    : String(error)
            logger.error('Operation failed with error: ' + errorMsg)
            return { success: false errorMessage: errorMsg; timeout: false }
          }),
      }),
      await Promise.all(batchPromises),
      if (batch < batches - 1) {
        logger.info(`Batch ${batch + 1} completed, waiting ${delayBetweenBatchesMs}ms before next batch`),
        await new Promise(resolve => setTimeout(resolve, delayBetweenBatchesMs)),
      }
    }
    const duration = Date.now() - startTime;
    logger.info('Connection pool stress test completed', {
      duration: `${duration}ms`)
      successCount;
      failureCount;
      timeoutCount;
      successRate: `${(successCount / concurrentOperations * 100).toFixed(2)}%`;
    }),
  }
  /**;
   * Run a single test operation using the connection pool;
   * @param operationId Unique identifier for this operation;
   */
  private static async runTestOperation(operationId: number): Promise<{ success: boolean,
    timeout?: boolean,
    errorMessage?: string,
    duration?: number }>
    const startTime = Date.now()
    try {
      // Use the connection pool to execute a simple query;
      const result = await withConnectionPool(
        async () => {
  // Simulate a random query duration between 100ms and 2000ms;
          const queryDuration = 100 + Math.random() * 1900;
          await new Promise(resolve => setTimeout(resolve, queryDuration)),
          // Execute a simple query to verify database connectivity;
          const { data, error  } = await supabase.from('user_profiles')
            .select('count')
            .limit(1);
          if (error) {
            throw error;
          }
          return { data; queryDuration },
        },
        {
          maxConcurrent: 5,
          timeoutMs: 5000,
          operationName: `test operation ${operationId}`;
        }
      ),
      const duration = Date.now() - startTime;
      logger.debug(`Operation ${operationId} completed successfully`, {
        duration: `${duration}ms`);
        queryDuration: result.queryDuration)
      }),
      return { success: true; duration },
    } catch (error: any) {
      const duration = Date.now() - startTime;
      // Check if this was a timeout error;
      const errorMsg = error instanceof Error ? error.message    : String(error)
      const isTimeout = errorMsg.includes('timeout')
      logger.warn(`Operation ${operationId} failed: ${errorMsg}` {
        duration: `${duration}ms`
        timeout: isTimeout)
      });
      return {
        success: false;
        timeout: isTimeout,
        errorMessage: errorMsg,
        duration;
      },
    }
  }
  /**;
   * Run a simple test to verify the connection pool is working;
   */
  static async runSimpleTest(): Promise<void>{
    logger.info('Starting simple connection pool test'),
    try {
      const result = await withConnectionPool(
        async () => {
  const { data, error } = await supabase.from('user_profiles')
            .select('count')
            .limit(1);
          if (error) {
            throw error;
          }
          return { success: true; data },
        },
        {
          maxConcurrent: 3,
          timeoutMs: 5000,
          operationName: 'simple test'
        }
      ),
      logger.info('Simple connection pool test completed successfully', { success: result.success })
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message  : String(error)
      logger.error(`Simple connection pool test failed: ${errorMsg}`)
    }
  }
}
// Export a function to run the test;
export const runConnectionPoolTest = async (concurrentOperations: number = 20;
  operationsPerBatch: number = 5;
  delayBetweenBatchesMs: number = 500): Promise<void> => { return ConnectionPoolTest.runStressTest(concurrentOperations;
    operationsPerBatch;
    delayBetweenBatchesMs)
  ) },

// Export a function to run a simple test;
export const runSimpleConnectionPoolTest = async (): Promise<void> => {
  return ConnectionPoolTest.runSimpleTest()
};
