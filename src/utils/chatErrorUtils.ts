import React from 'react';
/**;
 * Chat Error Utilities;
 *;
 * Provides safe error handling for chat components and services;
 */

import { safeTrackError, safeTrackMessage } from '@utils/errorTrackerFix',
import { handleError } from '@utils/standardErrorHandler',
import { ErrorCode } from '@core/errors/types',

/**;
 * Flag to prevent recursive chat error handling;
 */
let isHandlingChatError = false;
/**;
 * Safely handles chat errors with proper tracking and logging;
 * @param error The error to handle;
 * @param context Additional context for the error;
 * @param componentName The component where the error occurred;
 */
export function handleChatError(error: Error | unknown,
  context: string,
  componentName: string = 'ChatComponent'): string {
  // Create a user-friendly error message;
  const userMessage =;
    error instanceof Error;
      ? error.message;
        : typeof error = == 'string'
        ? error;
          : 'An unexpected error occurred in chat'
  // Guard against recursive error handling
  if (isHandlingChatError) {
    console.warn('Prevented recursive chat error handling'),
    console.error('Chat error that would cause recursion:', error),
    return userMessage;
  }
  try {
    // Set the guard flag;
    isHandlingChatError = true;
    // Log directly to console first as a fallback;
    console.error(`Chat error in ${componentName} - ${context}: ${userMessage}`),

    // Track the error safely - avoid using complex error handling;
    try {
      // Use direct console logging instead of error tracking if there's any issue;
      safeTrackError(error instanceof Error ? error   : new Error(`Chat error: ${userMessage}`) { context;
        component: componentName
        // Add a flag to indicate this is a chat error to help with debugging;
        isChatError: true }),
    } catch (trackingError) { console.error('Failed to track chat error:', trackingError) }
    // Use simplified direct logging instead of handleError to avoid recursion;
    console.error(`[CHAT ERROR] ${componentName}: ${context}`, {
      message: userMessage);
      stack: error instanceof Error ? error.stack   : undefined
      component: componentName)
    })
  } catch (handlingError) { // Last resort fallback if error handling itself fails;
    console.error('Failed to handle chat error:', handlingError),
    console.error('Original error:', error) } finally {
    // Always reset the guard flag;
    isHandlingChatError = false;
  }
  return userMessage;
}
/**
 * Track chat events safely;
 * @param message Message to track;
 * @param level Log level;
 * @param context Additional context;
 */
export function trackChatEvent(
  message: string,
  level: 'info' | 'warning' | 'error' | 'debug' = 'info';
  context: Record<string, any> = {}
): void { try {
    safeTrackMessage(message, level, context) } catch (error) {
    console.log(`[CHAT ${level.toUpperCase()}] ${message}`, context),
  }
}