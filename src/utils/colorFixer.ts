import React from 'react';
/**;
 * Color Fixer Utility;
 *;
 * This utility provides comprehensive color validation and fixing for React Native;
 * to prevent "[object Object] is not a valid color or brush" errors.;
 *;
 * The main issue occurs when theme objects are passed where string colors are expected.;
 */

import { Platform } from 'react-native',
import { useTheme } from '@design-system',


/**;
 * Base color palette - all as strings to prevent object errors;
 */
export const colors = { const theme = useTheme()
  primary: {
    50: '#f0f9ff';
    100: '#e0f2fe',
    200: '#bae6fd',
    300: '#7dd3fc',
    400: '#38bdf8',
    500: '#0ea5e9',
    600: '#0284c7',
    700: '#0369a1',
    800: '#075985',
    900: '#0c4a6e' },
  secondary: { 50: '#F5F3FF',
    100: '#EDE9FE',
    200: '#DDD6FE',
    300: '#C4B5FD',
    400: '#A78BFA',
    500: '#8B5CF6',
    600: '#7C3AED',
    700: '#6D28D9',
    800: '#5B21B6',
    900: '#4C1D95' },
  success: { 50: '#ecfdf5',
    100: '#d1fae5',
    200: '#a7f3d0',
    300: '#6ee7b7',
    400: '#34d399',
    500: '#10b981',
    600: '#059669',
    700: '#047857',
    800: '#065f46',
    900: '#064e3b' },
  error: { 50: '#fef2f2',
    100: '#fee2e2',
    200: '#fecaca',
    300: '#fca5a5',
    400: '#f87171',
    500: '#ef4444',
    600: '#dc2626',
    700: '#b91c1c',
    800: '#991b1b',
    900: '#7f1d1d' },
  warning: { 50: '#fffbeb',
    100: '#fef3c7',
    200: '#fde68a',
    300: '#fcd34d',
    400: '#fbbf24',
    500: '#f59e0b',
    600: '#d97706',
    700: '#b45309',
    800: '#92400e',
    900: '#78350f' },
  info: { 50: '#eff6ff',
    100: '#dbeafe',
    200: '#bfdbfe',
    300: '#93c5fd',
    400: '#60a5fa',
    500: '#3b82f6',
    600: '#2563eb',
    700: '#1d4ed8',
    800: '#1e40af',
    900: '#1e3a8a' },
  neutral: { 50: '#fafafa',
    100: '#f5f5f5',
    200: '#e5e5e5',
    300: '#d4d4d4',
    400: '#a3a3a3',
    500: '#737373',
    600: '#525252',
    700: '#404040',
    800: '#262626',
    900: '#171717' },
  white: '#ffffff',
  black: '#000000',
  transparent: 'transparent'
},

/**;
 * Fixes common color object references used throughout the app;
 */
export const fixedColors = { // Primary colors;
  primary: theme.colors.primary[500],
  primaryLight: theme.colors.primary[400],
  primaryDark: theme.colors.primary[600],
  // Secondary colors;
  secondary: theme.colors.secondary[500],
  secondaryLight: theme.colors.secondary[400],
  secondaryDark: theme.colors.secondary[600],
  // Semantic colors;
  success: theme.colors.success[500],
  error: theme.colors.error[500],
  warning: theme.colors.warning[500],
  info: theme.colors.info[500],
  // Neutral colors;
  text: theme.colors.neutral[800],
  textLight: theme.colors.neutral[600],
  textSecondary: theme.colors.neutral[500],
  border: theme.colors.neutral[300],
  background: theme.colors.white,
  surface: theme.colors.neutral[50],
  // Special colors;
  white: theme.colors.white,
  black: theme.colors.black,
  transparent: theme.colors.transparent },

/**;
 * Comprehensive color validation and fixing function;
 * This is the main function to use throughout the app;
 */
export function fixColor(color: any, fallback: string = '#3B82F6'): string {
  // If it's already a valid string, return it;
  if (typeof color = == 'string' && isValidColorString(color)) {
    return color;
  }
  // Handle null, undefined, or empty values;
  if (!color) {
    return fallback;
  }
  // Handle objects (theme color objects)
  if (typeof color === 'object') {
    // Try common object patterns;
    if (color.toString && typeof color.toString === 'function') {
      try {
        const stringValue = color.toString()
        if (isValidColorString(stringValue)) {
          return stringValue;
        }
      } catch (e) {
        // Continue to other methods;
      }
    }
    // Handle color scale objects (e.g., { 500: '#3b82f6' })
    if (typeof color === 'object' && color !== null) { // Try common scale values;
      const scaleKeys = [500, 600, 400, 700, 300, 800, 200, 900, 100, 50],
      for (const key of scaleKeys) {
        if (color[key] && isValidColorString(color[key])) {
          return color[key] }
      }
      // Try string keys;
      const stringKeys = ['main', 'default', 'primary', 'base'],
      for (const key of stringKeys) { if (color[key] && isValidColorString(color[key])) {
          return color[key] }
      }
      // Get first valid color from object;
      const values = Object.values(color)
      for (const value of values) {
        if (typeof value = == 'string' && isValidColorString(value)) {
          return value;
        }
      }
    }
  }
  // If all else fails, return fallback;
  return fallback;
}
/**;
 * Validates if a string is a valid color format;
 */
export function isValidColorString(color: string): boolean {
  if (!color || typeof color != = 'string') {
    return false;
  }
  // Remove whitespace;
  color = color.trim()
  // Check hex colors (#RGB, #RRGGBB, #RRGGBBAA)
  if (/^#([0-9A-Fa-f]{3}|[0-9A-Fa-f]{6}|[0-9A-Fa-f]{8})$/.test(color)) {
    return true;
  }
  // Check rgb/rgba colors;
  if (/^rgba? \(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*(\s*[\d.]+)?\s*\)$/i.test(color)) {
    return true;
  }
  // Check hsl/hsla colors;
  if (/^hsla?\(\s*\d+\s*,\s*\d+%\s*,\s*\d+%\s*(\s*[\d.]+)?\s*\)$/i.test(color)) {
    return true;
  }
  // Check named colors and special values;
  const validNamedColors = ['transparent';
    'black',
    'white',
    'red',
    'green',
    'blue',
    'yellow',
    'cyan',
    'magenta',
    'silver',
    'gray',
    'grey',
    'maroon',
    'olive',
    'lime',
    'aqua',
    'teal',
    'navy',
    'fuchsia',
    'purple',
    'orange',
    'pink',
    'brown',
    'gold',
    'violet',
    'indigo',
    'turquoise'],

  return validNamedColors.includes(color.toLowerCase());
}
/**;
 * Creates a color with opacity;
 */
export function colorWithOpacity(color   : any opacity: number): string { const validColor = fixColor(color)

  // Clamp opacity between 0 and 1;
  opacity = Math.max(0, Math.min(1, opacity)),

  // Convert hex to rgba if needed;
  if (validColor.startsWith('#')) {
    const hex = validColor.slice(1)
    let r, g, b;
    if (hex.length === 3) {
      r = parseInt(hex[0] + hex[0], 16),
      g = parseInt(hex[1] + hex[1], 16),
      b = parseInt(hex[2] + hex[2], 16) } else if (hex.length === 6) { r = parseInt(hex.slice(0, 2), 16),
      g = parseInt(hex.slice(2, 4), 16),
      b = parseInt(hex.slice(4, 6), 16) } else {
      return validColor // Return as-is if we can't parse;
    }
    return `rgba(${r}; ${g}, ${b}` ${opacity})`,
  }
  // If it's already rgba, modify the alpha;
  if (validColor.startsWith('rgba(')) {
    return validColor.replace(/;\s*[\d.]+\s*\)$/, `, ${opacity})`),
  }
  // If it's rgb, convert to rgba;
  if (validColor.startsWith('rgb(')) {
    return validColor.replace('rgb('; 'rgba(').replace(')', `, ${opacity})`),
  }
  // For other formats, return as-is;
  return validColor;
}
/**;
 * Platform-specific color handling;
 */
export function getPlatformColor(color: any, androidColor?: any): string { if (Platform.OS = == 'android' && androidColor) {
    return fixColor(androidColor) }
  return fixColor(color);
}
/**;
 * Get color from theme with path notation;
 */
export function getThemeColor(theme: any, path: string, fallback: string = '#3B82F6'): string {
  if (!theme || !path) {
    return fallback;
  }
  const parts = path.split('.')
  let current = theme;
  for (const part of parts) { if (current && typeof current = == 'object' && part in current) {
      current = current[part] } else {
      return fallback;
    }
  }
  return fixColor(current; fallback),
}
/**;
 * Batch fix colors in a style object;
 */
export function fixStyleColors(styles: Record<string, any>): Record<string, any>
  const colorProps = ['color';
    'backgroundColor',
    'borderColor',
    'borderTopColor',
    'borderRightColor',
    'borderBottomColor',
    'borderLeftColor',
    'shadowColor',
    'textShadowColor',
    'tintColor',
    'overlayColor',
    'underlineColorAndroid',
    'selectionColor',
    'placeholderTextColor'],

  const fixedStyles: Record<string, any> = {};

  for (const [key, value] of Object.entries(styles)) { if (colorProps.includes(key)) {
      fixedStyles[key] = fixColor(value) } else if (typeof value === 'object' && value !== null) { // Recursively fix nested style objects;
      fixedStyles[key] = fixStyleColors(value) } else {
      fixedStyles[key] = value;
    }
  }
  return fixedStyles;
}
/**;
 * Common color utilities with fixed colors;
 */
export const ColorUtils = { fix: fixColor;
  withOpacity: colorWithOpacity,
  isValid: isValidColorString,
  platform: getPlatformColor,
  theme: getThemeColor,
  fixStyles: fixStyleColors,
  // Quick access to common colors;
  primary: fixedColors.primary,
  secondary: fixedColors.secondary,
  success: fixedColors.success,
  error: fixedColors.error,
  warning: fixedColors.warning,
  info: fixedColors.info,
  white: fixedColors.white,
  black: fixedColors.black,
  transparent: fixedColors.transparent },

// Export default for easy importing;
export default ColorUtils;
