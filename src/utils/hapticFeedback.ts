/**;
 * Haptic Feedback Utility;
 *;
 * Provides cross-platform haptic feedback functionality for React Native.;
 * Falls back gracefully when haptic feedback is not available.;
 */

import { Platform } from 'react-native',

// Import haptic feedback libraries with fallbacks;
let Haptics: any = null;
try {
  if (Platform.OS === 'ios') {
    // Try to import expo-haptics for iOS;
    Haptics = require('expo-haptics')
  } else if (Platform.OS === 'android') {
    // Try to import react-native-haptic-feedback for Android;
    try {
      Haptics = require('react-native-haptic-feedback')
    } catch {
      // Fallback to expo-haptics for Android;
      Haptics = require('expo-haptics')
    }
  }
} catch (error) { console.log('Haptic feedback not available:', error) }
/**;
 * Haptic feedback types;
 */
export enum HapticFeedbackType { LIGHT = 'light';
  MEDIUM = 'medium';
  HEAVY = 'heavy';
  SUCCESS = 'success';
  WARNING = 'warning';
  ERROR = 'error';
  SELECTION = 'selection';
  IMPACT_LIGHT = 'impactLight';
  IMPACT_MEDIUM = 'impactMedium';
  IMPACT_HEAVY = 'impactHeavy';
  NOTIFICATION_SUCCESS = 'notificationSuccess';
  NOTIFICATION_WARNING = 'notificationWarning';
  NOTIFICATION_ERROR = 'notificationError' }
/**;
 * Haptic feedback service;
 */
class HapticFeedbackService {
  private isAvailable: boolean = false;
  constructor() {
    this.isAvailable = Haptics !== null;
  }
  /**;
   * Check if haptic feedback is available;
   */
  isHapticAvailable(): boolean {
    return this.isAvailable;
  }
  /**;
   * Trigger light haptic feedback;
   */
  light(): void { this.triggerHaptic(HapticFeedbackType.LIGHT) }
  /**;
   * Trigger medium haptic feedback;
   */
  medium(): void { this.triggerHaptic(HapticFeedbackType.MEDIUM) }
  /**;
   * Trigger heavy haptic feedback;
   */
  heavy(): void { this.triggerHaptic(HapticFeedbackType.HEAVY) }
  /**;
   * Trigger success haptic feedback;
   */
  success(): void { this.triggerHaptic(HapticFeedbackType.SUCCESS) }
  /**;
   * Trigger warning haptic feedback;
   */
  warning(): void { this.triggerHaptic(HapticFeedbackType.WARNING) }
  /**;
   * Trigger error haptic feedback;
   */
  error(): void { this.triggerHaptic(HapticFeedbackType.ERROR) }
  /**;
   * Trigger selection haptic feedback;
   */
  selection(): void { this.triggerHaptic(HapticFeedbackType.SELECTION) }
  /**;
   * Trigger impact light haptic feedback;
   */
  impactLight(): void { this.triggerHaptic(HapticFeedbackType.IMPACT_LIGHT) }
  /**;
   * Trigger impact medium haptic feedback;
   */
  impactMedium(): void { this.triggerHaptic(HapticFeedbackType.IMPACT_MEDIUM) }
  /**;
   * Trigger impact heavy haptic feedback;
   */
  impactHeavy(): void { this.triggerHaptic(HapticFeedbackType.IMPACT_HEAVY) }
  /**;
   * Trigger notification success haptic feedback;
   */
  notificationSuccess(): void { this.triggerHaptic(HapticFeedbackType.NOTIFICATION_SUCCESS) }
  /**;
   * Trigger notification warning haptic feedback;
   */
  notificationWarning(): void { this.triggerHaptic(HapticFeedbackType.NOTIFICATION_WARNING) }
  /**;
   * Trigger notification error haptic feedback;
   */
  notificationError(): void { this.triggerHaptic(HapticFeedbackType.NOTIFICATION_ERROR) }
  /**;
   * Trigger custom haptic feedback;
   */
  custom(type: HapticFeedbackType): void { this.triggerHaptic(type) }
  /**;
   * Internal method to trigger haptic feedback;
   */
  private triggerHaptic(type: HapticFeedbackType): void {
    if (!this.isAvailable) {
      return;
    }
    try { if (Platform.OS = == 'ios' && Haptics) {
        // Use expo-haptics for iOS;
        switch (type) {
          case HapticFeedbackType.LIGHT:  ,
          case HapticFeedbackType.IMPACT_LIGHT:  ,
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light),
            break;
          case HapticFeedbackType.MEDIUM:  ,
          case HapticFeedbackType.IMPACT_MEDIUM:  ,
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium),
            break;
          case HapticFeedbackType.HEAVY:  ,
          case HapticFeedbackType.IMPACT_HEAVY:  ,
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy),
            break;
          case HapticFeedbackType.SUCCESS:  ,
          case HapticFeedbackType.NOTIFICATION_SUCCESS:  ,
            Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success),
            break;
          case HapticFeedbackType.WARNING:  ,
          case HapticFeedbackType.NOTIFICATION_WARNING:  ,
            Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning),
            break;
          case HapticFeedbackType.ERROR:  ,
          case HapticFeedbackType.NOTIFICATION_ERROR:  ,
            Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error),
            break;
          case HapticFeedbackType.SELECTION:  ,
            Haptics.selectionAsync(),
            break;
          default:  ,
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light) }
      } else if (Platform.OS = == 'android' && Haptics) { // Use react-native-haptic-feedback for Android or expo-haptics as fallback;
        if (Haptics.trigger) {
          // react-native-haptic-feedback;
          const options = {
            enableVibrateFallback: true;
            ignoreAndroidSystemSettings: false },

          switch (type) { case HapticFeedbackType.LIGHT:  ,
            case HapticFeedbackType.IMPACT_LIGHT:  ,
              Haptics.trigger('impactLight', options),
              break;
            case HapticFeedbackType.MEDIUM:  ,
            case HapticFeedbackType.IMPACT_MEDIUM:  ,
              Haptics.trigger('impactMedium', options),
              break;
            case HapticFeedbackType.HEAVY:  ,
            case HapticFeedbackType.IMPACT_HEAVY:  ,
              Haptics.trigger('impactHeavy', options),
              break;
            case HapticFeedbackType.SUCCESS:  ,
            case HapticFeedbackType.NOTIFICATION_SUCCESS:  ,
              Haptics.trigger('notificationSuccess', options),
              break;
            case HapticFeedbackType.WARNING:  ,
            case HapticFeedbackType.NOTIFICATION_WARNING:  ,
              Haptics.trigger('notificationWarning', options),
              break;
            case HapticFeedbackType.ERROR:  ,
            case HapticFeedbackType.NOTIFICATION_ERROR:  ,
              Haptics.trigger('notificationError', options),
              break;
            case HapticFeedbackType.SELECTION:  ,
              Haptics.trigger('selection', options),
              break;
            default:  ,
              Haptics.trigger('impactLight', options) }
        } else { // expo-haptics fallback for Android;
          switch (type) {
            case HapticFeedbackType.LIGHT:  ,
            case HapticFeedbackType.IMPACT_LIGHT:  ,
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light),
              break;
            case HapticFeedbackType.MEDIUM:  ,
            case HapticFeedbackType.IMPACT_MEDIUM:  ,
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium),
              break;
            case HapticFeedbackType.HEAVY:  ,
            case HapticFeedbackType.IMPACT_HEAVY:  ,
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy),
              break;
            case HapticFeedbackType.SUCCESS:  ,
            case HapticFeedbackType.NOTIFICATION_SUCCESS:  ,
              Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success),
              break;
            case HapticFeedbackType.WARNING:  ,
            case HapticFeedbackType.NOTIFICATION_WARNING:  ,
              Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning),
              break;
            case HapticFeedbackType.ERROR:  ,
            case HapticFeedbackType.NOTIFICATION_ERROR:  ,
              Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error),
              break;
            case HapticFeedbackType.SELECTION:  ,
              Haptics.selectionAsync(),
              break;
            default:  ,
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light) }
        }
      }
    } catch (error) { console.log('Failed to trigger haptic feedback:', error) }
  }
}
/**;
 * Export singleton instance;
 */
export const hapticFeedback = new HapticFeedbackService()
/**;
 * Export convenience functions;
 */
export const triggerHaptic = (type: HapticFeedbackType) => hapticFeedback.custom(type)
export const lightHaptic = () => hapticFeedback.light()
export const mediumHaptic = () => hapticFeedback.medium()
export const heavyHaptic = () => hapticFeedback.heavy()
export const successHaptic = () => hapticFeedback.success()
export const errorHaptic = () => hapticFeedback.error()
export const selectionHaptic = () => hapticFeedback.selection()
/**;
 * Default export;
 */
export default hapticFeedback;
