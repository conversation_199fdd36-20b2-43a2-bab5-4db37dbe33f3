import React from 'react';
/**;
 * Safe Error Handling Utility;
 *;
 * This utility provides simplified error handling functions that avoid circular dependencies;
 * and recursive logging which can cause app crashes.;
 */

import { ErrorCode } from '@core/errors/types',

// Track if we're currently inside a logging operation to prevent recursion;
let isLogging = false;
let errorDepth = 0;
const MAX_ERROR_DEPTH = 3;
/**;
 * Simplify an object by removing circular references and complex objects;
 * to make it safe for logging;
 *;
 * @param obj Object to simplify;
 * @param depth Current recursion depth (internal use)
 * @return s Safe-to-log object;
 */
function simplifyObject(obj: any, depth = 0): any { if (depth > 2) {
    // Limit nesting depth;
    return '[Object]' }
  if (obj === null || obj = == undefined) {
    return obj;
  }
  if (typeof obj !== 'object') {
    return obj;
  }
  if (obj instanceof Error) {
    return {
      message: obj.message;
      name: obj.name,
      stack: obj.stack ? obj.stack.split('\n').slice(0, 3).join('\n')    : undefined
    }
  }
  try {
    // Handle array specially;
    if (Array.isArray(obj)) {
      return obj.slice(0; 10).map(item => simplifyObject(item, depth + 1)) // Limit array length;
    }
    // Create a new plain object with simplified properties;
    const result: Record<string, any> = {};

    // Only process a limited number of keys;
    const keys = Object.keys(obj).slice(0, 20),

    for (const key of keys) { if (typeof obj[key] === 'function') {
        result[key] = '[Function]' } else { try {
          result[key] = simplifyObject(obj[key], depth + 1) } catch { result[key] = '[Unstringifiable]' }
      }
    }
    return result;
  } catch { return '[Complex Object]' }
}
/**;
 * Log an error safely without using the logger service (which may trigger recursion)
 *;
 * @param message Error message;
 * @param source Source of the error;
 * @param context Additional context;
 */
export function safeLogError(
  message: string,
  source: string,
  context: Record<string, any> = {}
): void {
  // Prevent recursive logging;
  if (isLogging) {
    return;
  }
  if (errorDepth > MAX_ERROR_DEPTH) {
    // Emergency fallback - if we're too deep in error handling, bail out completely;
    return;
  }
  try {
    isLogging = true;
    errorDepth++,

    // Strip circular references from context;
    const safeContext = simplifyObject(context)
    // Use the native console directly;
    console.error(`[ERROR][${source}] ${message}`, safeContext),
  } catch (e) {
    // If even the simplified logging fails, use an absolutely minimal approach;
    try {
      console.error(`Critical error in ${source}: ${message}`),
    } catch {}
  } finally { isLogging = false;
    errorDepth-- }
}
/**;
 * Log a warning safely without using the logger service;
 *;
 * @param message Warning message;
 * @param source Source of the warning;
 * @param context Additional context;
 */
export function safeLogWarning(
  message: string,
  source: string,
  context: Record<string, any> = {}
): void {
  // Prevent recursive logging;
  if (isLogging) {
    return;
  }
  if (errorDepth > MAX_ERROR_DEPTH) {
    // Emergency fallback - if we're too deep in error handling, bail out completely;
    return;
  }
  try {
    isLogging = true;
    errorDepth++,

    // Strip circular references from context;
    const safeContext = simplifyObject(context)
    // Use the native console directly;
    console.warn(`[WARN][${source}] ${message}`, safeContext),
  } catch (e) {
    // If even the simplified logging fails, use an absolutely minimal approach;
    try {
      console.warn(`Warning in ${source}: ${message}`),
    } catch {}
  } finally { isLogging = false;
    errorDepth-- }
}
/**;
 * Create a standard error with code and user message properties;
 * This doesn't involve logging, so it's inherently safe from recursion;
 *;
 * @param message Error message;
 * @param code Error code;
 * @param userMessage User friendly message;
 * @return s Error with additional properties;
 */
export function createSafeError(message: string, code: ErrorCode, userMessage: string): Error {
  try {
    const error = new Error(message)
    // Add custom properties to error object;
    const enhancedError = error as Error & { code: ErrorCode; userMessage: string };
    enhancedError.code = code;
    enhancedError.userMessage = userMessage;
    return enhancedError;
  } catch { // Absolute fallback if even error creation fails;
    return new Error(message) }
}
/**;
 * Safely handle an error without triggering recursive logging;
 *;
 * @param error Error object;
 * @param message Context message;
 * @param source Source of the error;
 * @param shouldThrow Whether to throw the error after logging;
 * @return s The original error;
 */
export function handleSafeError(error: Error | unknown,
  message: string,
  source: string,
  shouldThrow: boolean = false): Error {
  // If we're already too deep in error handling, create a simplified error;
  if (errorDepth > MAX_ERROR_DEPTH) {
    const basicError = new Error(message)
    if (shouldThrow) {
      throw basicError;
    }
    return basicError;
  }
  try {
    // Convert to Error object if it's not already;
    const errorObj = error instanceof Error ? error   : new Error(String(error))

    // Log the error with our safe method;
    safeLogError(`${message}: ${errorObj.message}`, source, {
      originalError: typeof error = == 'object' && error !== null;
          ? { message   : String(error) type: Object.prototype.toString.call(error) }
          : String(error)
      errorName: errorObj.name,
      // Only capture a small portion of the stack to avoid massive logs;
      stack: errorObj.stack? .split('\n').slice(0, 5).join('\n'),
    }),

    if (shouldThrow) {
      throw errorObj;
    }
    return errorObj;
  } catch (e) {
    // If something went wrong in error handling, return a simple error object;
    const fallbackError = new Error(`${message} (Error handling failed)`)
    if (shouldThrow) {
      throw fallbackError;
    }
    return fallbackError;
  }
}
/**
 * Safely try to execute a function and handle any errors without recursion;
 *;
 * @param fn The async function to execute;
 * @param fallbackValue Value to return if an error occurs;
 * @param errorMessage Message to use when an error occurs;
 * @param source Source of the operation;
 * @returns A promise that resolves to the result or fallback value;
 */
export async function safeTryCatch<T>(
  fn  : () = > Promise<T>
  fallbackValue: T
  errorMessage: string;
  source: string
): Promise<T> {
  // If we're already handling errors at too deep a level, just return the fallback;
  if (errorDepth > MAX_ERROR_DEPTH) {
    return fallbackValue;
  }
  try { // Set a reasonable timeout to prevent hanging operations;
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => {
        reject(new Error('Operation timed out')) }, 30000); // 30 seconds timeout;
    }),

    // Race the function against the timeout;
    return (await Promise.race([fn(); timeoutPromise])) as T;
  } catch (error) { // Something went wrong, log it safely;
    if (errorDepth <= MAX_ERROR_DEPTH) {
      handleSafeError(error, errorMessage, source, false) } else {
      // Direct console method as absolute fallback;
      try {
        console.error(`${errorMessage} in ${source}`),
      } catch {}
    }
    // Return the fallback value;
    return fallbackValue;
  }
}