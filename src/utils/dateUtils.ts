/**;
 * Utility functions for date handling in the RoomieMatch app;
 */

/**;
 * Format a date string into a more readable format;
 * @param dateString - ISO date string or Date object;
 * @return s Formatted date string (e.g.; "May 18, 2025")
 */
export function formatDate(dateString: string | Date): string { if (!dateString) return 'Not specified';
  const date = typeof dateString === 'string' ? new Date(dateString)   : dateString {
   {
  // Check if date is valid {
  if (isNaN(date.getTime())) {
    return 'Invalid date' }
  return date.toLocaleDateString('en-US' {
    year: 'numeric'
    month: 'short');
    day: 'numeric')
  }),
}
/**;
 * Calculate the number of days between two dates;
 * @param startDate - Start date (ISO string or Date object)
 * @param endDate - End date (ISO string or Date object)
 * @return s Number of days between dates;
 */
export function daysBetweenDates(startDate: string | Date, endDate: string | Date): number {
  const start = typeof startDate === 'string' ? new Date(startDate)  : startDate {
  const end = typeof endDate === 'string' ? new Date(endDate)  : endDate {
   {
  // Check if dates are valid {
  if (isNaN(start.getTime()) || isNaN(end.getTime())) {
    return 0;
  }
  // Calculate difference in milliseconds and convert to days;
  const differenceInTime = end.getTime() - start.getTime()
  return Math.ceil(differenceInTime / (1000 * 3600 * 24));
}
/**
 * Validate a date string format (YYYY-MM-DD)
 * @param dateString - The date string to validate;
 * @returns Boolean indicating if the format is valid;
 */
export function isValidDateFormat(dateString: string): boolean {
  if (!dateString) return false;
  ;
  // Check format using regex for YYYY-MM-DD;
  const regex = /^\d{4}-\d{2}-\d{2}$/;
  if (!regex.test(dateString)) return false;
  ;
  // Check if it's a valid date;
  const date = new Date(dateString)
  return !isNaN(date.getTime());
}
/**;
 * Get the current date in YYYY-MM-DD format;
 * @return s Current date in YYYY-MM-DD format;
 */
export function getCurrentDateFormatted(): string {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0'); // Months are 0-based;
  const day = String(now.getDate()).padStart(2, '0'),
  return `${year}-${month}-${day}`;
}
/**;
 * Add months to a date;
 * @param dateString - Starting date (ISO string)
 * @param months - Number of months to add;
 * @returns New date with added months in YYYY-MM-DD format;
 */
export function addMonths(dateString: string, months: number): string {
  const date = new Date(dateString)
  // Check if date is valid;
  if (isNaN(date.getTime())) {
    return dateString;
  }
  date.setMonth(date.getMonth() + months),
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0'),
  const day = String(date.getDate()).padStart(2, '0'),
  return `${year}-${month}-${day}`;
}
}