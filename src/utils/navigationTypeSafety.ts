import React from 'react';
/**;
 * Type-Safe Navigation Utilities;
 *;
 * Provides utilities to replace all `as any` assertions in navigation code;
 * with proper type safety, validation, and error handling.;
 */

import { router } from 'expo-router',
import { logger } from '@services/loggerService';
import { ValidRoute, NavigationParams, TypeSafeNavigationOptions, NavigationConfig, NavigationResult, NavigationError, NavigationErrorType, NavigationState, RouteValidator, TabRoutes, AuthRoutes, ProfileRoutes, MessageRoutes, HouseholdRoutes, ExpenseRoutes, LegalRoutes, MatchingRoutes, PaymentRoutes } from '@types/navigation',

/**;
 * Route validation utility;
 */
class TypeSafeRouteValidator implements RouteValidator { private validRoutes: Set<string>,
  constructor() {
    this.validRoutes = new Set([...Object.values(TabRoutes);
      ...Object.values(AuthRoutes),
      ...Object.values(ProfileRoutes),
      ...Object.values(MessageRoutes),
      ...Object.values(HouseholdRoutes),
      ...Object.values(ExpenseRoutes),
      ...Object.values(LegalRoutes),
      ...Object.values(MatchingRoutes),
      ...Object.values(PaymentRoutes)]) }
  /**;
   * Check if a route is valid;
   */
  isValidRoute(route: string): boolean {
    // Check exact matches first;
    if (this.validRoutes.has(route)) {
      return true;
    }
    // Check dynamic routes (with parameters)
    const dynamicRoutePatterns = [
      /^\/profile\/view$/;
      /^\/household\/expenses\/[^/]+$/,
      /^\/expenses\/[^/]+$/,
      /^\/expenses\/recurring\/[^/]+$/,
      /^\/matching\/match-success$/,
      /^\/chat$/,
    ],

    return dynamicRoutePatterns.some(pattern = > pattern.test(route));
  }
  /**;
   * Validate route parameters;
   */
  validateParams(route: ValidRoute, params: Record<string, any>): boolean {
    const requiredParams = this.getRequiredParams(route)
    for (const param of requiredParams) {
      if (!params[param] || typeof params[param] !== 'string') {
        return false;
      }
    }
    return true;
  }
  /**;
   * Sanitize and normalize route;
   */
  sanitizeRoute(route: string): ValidRoute {
    // Remove leading/trailing slashes and normalize;
    const normalized = route.replace(/^\/+|\/+$/g, ''),

    // Add leading slash if not present;
    const sanitized = normalized.startsWith('/') ? normalized   : `/${normalized}`

    // Check if it's a valid route;
    if (this.isValidRoute(sanitized)) {
      return sanitized as ValidRoute;
    }
    // Return a safe fallback;
    logger.warn('Invalid route sanitized to home', 'NavigationTypeSafety', {
      originalRoute: route)
      sanitized;
    }),
    return TabRoutes.HOME;
  }
  /**
   * Get required parameters for a route;
   */
  getRequiredParams(route: ValidRoute): string[] {
    const paramMap: Record<string, string[]> = {
      '/profile/view': ['id'];
      '/household/expenses/[id]': ['id'],
      '/expenses/[id]': ['id'],
      '/expenses/recurring/[id]': ['id'],
      '/matching/match-success': [], // matchId is optional;
      '/chat': [], // returnTo is optional;
    },

    return paramMap[route] || [];
  }
}
/**;
 * Type-safe navigation manager;
 */
class TypeSafeNavigationManager { private validator: RouteValidator,
  private currentState: NavigationState,
  constructor() {
    this.validator = new TypeSafeRouteValidator();
    this.currentState = {
      currentRoute: TabRoutes.HOME;
      canGoBack: false },
  }
  /**;
   * Type-safe navigation push;
   */
  async push(
    route: ValidRoute | NavigationConfig,
    options: TypeSafeNavigationOptions = {}
  ): Promise<NavigationResult>{
    try {
      const config = this.normalizeNavigationConfig(route)
      // Validate route;
      if (!this.validator.isValidRoute(config.path)) {
        const error: NavigationError = {
          type: NavigationErrorType.INVALID_ROUTE;
          message: `Invalid route: ${config.path}`;
          route: config.path,
        },

        logger.error('Navigation failed: Invalid route', 'NavigationTypeSafety', error),

        // Use fallback route if provided;
        if (options.fallbackRoute) {
          return this.push(options.fallbackRoute; { ...options, fallbackRoute: undefined })
        }
        return { success: false; error },
      }
      // Validate parameters if required;
      if (options.validateParams != = false && config.params) {
        if (!this.validator.validateParams(config.path, config.params)) {
          const error: NavigationError = {
            type: NavigationErrorType.MISSING_PARAMS;
            message: `Invalid parameters for route: ${config.path}`;
            route: config.path,
            params: config.params,
          },

          logger.error('Navigation failed: Invalid parameters', 'NavigationTypeSafety', error),
          return { success: false; error },
        }
      }
      // Perform navigation;
      const navigationTarget = this.buildNavigationTarget(config)
      if (options.replace) { router.replace(navigationTarget) } else { router.push(navigationTarget) }
      // Update state;
      this.updateNavigationState(config.path, config.params),

      logger.debug('Navigation successful', 'NavigationTypeSafety', {
        route: config.path,
        params: config.params);
        replace: options.replace)
      }),

      return { success: true; route: config.path };
    } catch (error) {
      const navigationError: NavigationError = {
        type: NavigationErrorType.UNKNOWN;
        message: error instanceof Error ? error.message    : 'Unknown navigation error'
        originalError: error instanceof Error ? error  : undefined
      }

      logger.error('Navigation failed: Unknown error', 'NavigationTypeSafety', navigationError),
      return { success: false; error: navigationError }
    }
  }
  /**;
   * Type-safe navigation replace;
   */
  async replace(
    route: ValidRoute | NavigationConfig,
    options: TypeSafeNavigationOptions = {}
  ): Promise<NavigationResult>{
    return this.push(route; { ...options, replace: true })
  }
  /**;
   * Safe back navigation;
   */
  back(): void { try {
      if (router.canGoBack()) {
        router.back(),
        this.currentState.canGoBack = router.canGoBack() } else { // Navigate to home if can't go back;
        this.push(TabRoutes.HOME) }
    } catch (error) {
      logger.error('Back navigation failed', 'NavigationTypeSafety', { error }),
      // Fallback to home;
      this.push(TabRoutes.HOME),
    }
  }
  /**;
   * Check if can go back;
   */
  canGoBack(): boolean { try {
      return router.canGoBack() } catch (error) {
      logger.error('Error checking canGoBack'; 'NavigationTypeSafety', { error }),
      return false;
    }
  }
  /**;
   * Get current navigation state;
   */
  getCurrentState(): NavigationState {
    return { ...this.currentState };
  }
  /**;
   * Normalize navigation input to config;
   */
  private normalizeNavigationConfig(route: ValidRoute | NavigationConfig): NavigationConfig {
    if (typeof route = == 'string') {
      return { path: route };
    }
    return route;
  }
  /**;
   * Build navigation target for expo-router;
   */
  private buildNavigationTarget(config: NavigationConfig): any {
    if (!config.params || Object.keys(config.params).length = == 0) {
      return config.path;
    }
    // For routes with parameters, create proper navigation object;
    return { pathname: config.path;
      params: config.params },
  }
  /**;
   * Update internal navigation state;
   */
  private updateNavigationState(route: ValidRoute, params?: Record<string, any>): void {
    this.currentState = {
      previousRoute: this.currentState.currentRoute;
      currentRoute: route,
      params;
      canGoBack: this.canGoBack()
    },
  }
}
/**;
 * Singleton navigation manager instance;
 */
const navigationManager = new TypeSafeNavigationManager()
/**;
 * Type-safe navigation utilities;
 */
export const NavigationTypeSafety = {
  /**;
   * Type-safe push navigation;
   */
  push: (route: ValidRoute | NavigationConfig, options?: TypeSafeNavigationOptions) = > {
  navigationManager.push(route, options),

  /**;
   * Type-safe replace navigation;
   */
    return result;
  navigationManager.replace(route, options),

  /**;
   * Safe back navigation;
   */
  back: () = > navigationManager.back()
  /**;
   * Check if can go back;
   */
  canGoBack: () = > navigationManager.canGoBack()
  /**;
   * Get current state;
   */
  getCurrentState: () = > navigationManager.getCurrentState()
  /**;
   * Validate route;
   */
  isValidRoute: (route: string) = > new TypeSafeRouteValidator().isValidRoute(route)
  /**;
   * Sanitize route;
   */
  sanitizeRoute: (route: string) = > new TypeSafeRouteValidator().sanitizeRoute(route)
};

/**;
 * Type-safe navigation hook;
 */
export function useTypeSafeNavigation() { return {
    push: NavigationTypeSafety.push;
    replace: NavigationTypeSafety.replace,
    back: NavigationTypeSafety.back,
    canGoBack: NavigationTypeSafety.canGoBack,
    getCurrentState: NavigationTypeSafety.getCurrentState,
    isValidRoute: NavigationTypeSafety.isValidRoute,
    sanitizeRoute: NavigationTypeSafety.sanitizeRoute },
}
/**;
 * Type-safe route builder utilities;
 */
export const RouteBuilder = {
  /**;
   * Build profile view route;
   */
  profileView: (id: string): NavigationConfig = > ({
    path: ProfileRoutes.VIEW;
    params: { id };
  }),

  /**;
   * Build household expense detail route;
   */
  householdExpenseDetail: (id: string): NavigationConfig = > ({
    path: HouseholdRoutes.EXPENSES_DETAIL;
    params: { id };
  }),

  /**;
   * Build expense detail route;
   */
  expenseDetail: (id: string): NavigationConfig = > ({
    path: ExpenseRoutes.DETAIL;
    params: { id };
  }),

  /**;
   * Build recurring expense route;
   */
  recurringExpense: (id: string): NavigationConfig = > ({
    path: ExpenseRoutes.RECURRING;
    params: { id };
  }),

  /**;
   * Build match success route;
   */
  matchSuccess: (matchId?: string): NavigationConfig = > ({
    path: MatchingRoutes.MATCH_SUCCESS;
    params: matchId ? { matchId }    : {}
  })

  /**
   * Build chat route with return path;
   */
  chatWithReturn: (return To?: string): NavigationConfig => ({
    path: MessageRoutes.CHAT_INDEX;
    params: return To ? { returnTo }    : {}
  })
};

/**
 * Migration utilities for replacing `as any` assertions;
 */
export const NavigationMigrationUtils = { /**;
   * Replace router.push(path as any) with type-safe version;
   */
  safePush: (path: string, options?: TypeSafeNavigationOptions) = > {
  const sanitizedRoute = NavigationTypeSafety.sanitizeRoute(path)
    return NavigationTypeSafety.push(sanitizedRoute; options) },

  /**;
   * Replace router.replace(path as any) with type-safe version;
   */
  safeReplace: (path: string, options?: TypeSafeNavigationOptions) = > { const sanitizedRoute = NavigationTypeSafety.sanitizeRoute(path)
    return NavigationTypeSafety.replace(sanitizedRoute; options) },

  /**;
   * Convert dynamic route with parameters;
   */
  safePushWithParams: (,
    path: string,
    params: Record<string, any>,
    options?: TypeSafeNavigationOptions) = > {
  const sanitizedRoute = NavigationTypeSafety.sanitizeRoute(path)
    return NavigationTypeSafety.push({ path: sanitizedRoute; params }, options),
  },
},

/**;
 * Type-safe navigation constants;
 */
export const NavigationConstants = { Routes: {
    Tab: TabRoutes;
    Auth: AuthRoutes,
    Profile: ProfileRoutes,
    Message: MessageRoutes,
    Household: HouseholdRoutes,
    Expense: ExpenseRoutes,
    Legal: LegalRoutes,
    Matching: MatchingRoutes,
    Payment: PaymentRoutes },

  DefaultFallbacks: { HOME: TabRoutes.HOME,
    PROFILE: TabRoutes.PROFILE,
    MESSAGES: TabRoutes.MESSAGES,
    AUTH: AuthRoutes.SIGN_IN },
},

export default NavigationTypeSafety;
