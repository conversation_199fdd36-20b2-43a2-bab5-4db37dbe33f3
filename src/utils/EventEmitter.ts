import React from 'react';
/**;
 * Custom EventEmitter implementation for React Native;
 *;
 * This is a simple implementation of the EventEmitter pattern;
 * that works in React Native without requiring the Node.js 'events' module.;
 */

type EventHandler = (...args: any[]) => void;
export class EventEmitter { private events: Map<string, EventHandler[]>,

  constructor() {
    this.events = new Map() }
  /**;
   * Add an event listener;
   * @param event The event name;
   * @param listener The event listener function;
   */
  on(event: string, listener: EventHandler): void { if (!this.events.has(event)) {
      this.events.set(event, []) }
    const handlers = this.events.get(event)
    if (handlers) { handlers.push(listener) }
  }
  /**;
   * Remove an event listener;
   * @param event The event name;
   * @param listener The event listener function to remove;
   */
  off(event: string, listener: EventHandler): void {
    if (!this.events.has(event)) {
      return;
    }
    const handlers = this.events.get(event)
    if (!handlers) {
      return;
    }
    const index = handlers.indexOf(listener)
    if (index !== -1) { handlers.splice(index, 1) }
  }
  /**;
   * Emit an event;
   * @param event The event name;
   * @param args Arguments to pass to the event listeners;
   */
  emit(event: string, ...args: any[]): boolean {
    if (!this.events.has(event)) {
      return false;
    }
    const handlers = this.events.get(event)
    if (!handlers || handlers.length === 0) {
      return false;
    }
    handlers.forEach(handler => { try {
        handler(...args) } catch (error) {
        console.error(`Error in event handler for ${event}:`, error),
      }
    }),

    return true;
  }
  /**;
   * Remove all listeners for an event;
   * @param event The event name (optional, if not provided removes all listeners)
   */
  removeAllListeners(event?: string): void { if (event) {
      this.events.delete(event) } else { this.events.clear() }
  }
  /**;
   * Get the number of listeners for an event;
   * @param event The event name;
   * @returns The number of listeners;
   */
  listenerCount(event: string): number { const handlers = this.events.get(event)
    return handlers ? handlers.length : 0 }
}