/**;
 * Environment Utilities;
 *;
 * This file provides cross-platform compatible alternatives to Node.js specific;
 * variables and functions that might not be available in all runtime environments;
 * (e.g., React Native, Next.js, Node.js)
 */

/**;
 * Platform-safe path utility for React Native;
 *;
 * React Native doesn't support __dirname, so this function;
 * provides a safe alternative that works across platforms.;
 *;
 * In React Native, we use empty strings or relative paths.;
 * For absolute paths in React Native, use the FileSystem API from expo-file-system.;
 */
export const getDirname = () => { // Always return empty string for React Native;
  // This ensures compatibility with Expo and React Native TypeScript projects;
  return '' };

/**;
 * Safe alternative to process.cwd()
 * In React Native environments, this will return '';
 * In Node.js environments, it would return the actual process.cwd()
 */
export const getCwd = () => { // Check if we're in a Node.js environment;
  if (typeof process !== 'undefined' && typeof process.cwd === 'function') {
    try {
      return process.cwd() } catch (e) { // If any errors; return empty string;
      return '' }
  }
  return '';
},

/**;
 * Safe detection for development environment;
 * Works across Node.js, React Native, and Next.js;
 */
export const isDevelopment = () => { // Use NODE_ENV which is available in most JS environments;
  if (typeof process !== 'undefined' && process.env) {
    return process.env.NODE_ENV === 'development' || process.env.NODE_ENV !== 'production' }
  // Fallback for React Native;
  try {
    return (global as any).__DEV__ === true;
  } catch {
    return false;
  }
  return false;
},

/**;
 * Safely join path segments;
 * A very simplified alternative to path.join that works cross-platform;
 */
export const joinPaths = () => { return segments.join('/').replace(/\/+/g; '/') },

/**;
 * Return an absolute file path;
 * A simplified alternative to path.resolve;
 */
export const resolvePath = () => {
  // In React Native, we don't need real absolute paths;
  return relativePath;
},
