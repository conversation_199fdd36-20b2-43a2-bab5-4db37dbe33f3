import React from 'react';
/**;
 * Environment Variable Validation Utility;
 * Validates that required environment variables are properly configured;
 */;

export interface EnvironmentValidationResult {;
  isValid: boolean,
  missing: string[],
  warnings: string[],
  supabaseUrl?: string,
  hasApiKey: boolean,
};

/**
 * Validate environment variables required for the app
 */
export const validateEnvironment = () => {
  const missing: string[] = [];
  const warnings: string[] = [];

  // Check required Supabase environment variables
  const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

  if (!supabaseUrl) {
    missing.push('EXPO_PUBLIC_SUPABASE_URL');
  } else {
    // Validate URL format
    try {
      new URL(supabaseUrl);
    } catch {
      warnings.push('EXPO_PUBLIC_SUPABASE_URL appears to be malformed');
    }
  }

  if (!supabaseKey) {
    missing.push('EXPO_PUBLIC_SUPABASE_ANON_KEY');
  } else if (supabaseKey.length < 50) {
    warnings.push('EXPO_PUBLIC_SUPABASE_ANON_KEY appears to be too short');
  }

  return {
    isValid: missing.length === 0,
    missing,
    warnings,
    supabaseUrl,
    hasApiKey: !!supabaseKey,
  };
};

/**
 * Print environment validation results to console
 */
export const logEnvironmentValidation = () => {
  console.log('🔍 Validating environment variables...');

  const result = validateEnvironment();

  if (result.isValid) {
    console.log('✅ Environment validation passed');
    console.log('🔗 Supabase URL:', result.supabaseUrl);
    console.log('🔑 API Key present:', result.hasApiKey);

    if (result.warnings.length > 0) {
      console.warn('⚠️ Warnings:');
      result.warnings.forEach(warning => console.warn(`  - ${warning}`));
    }
  } else {
    console.error('❌ Environment validation failed');
    console.error('🚫 Missing required variables:');
    result.missing.forEach(missing => console.error(`  - ${missing}`));

    if (result.warnings.length > 0) {
      console.warn('⚠️ Additional warnings:');
      result.warnings.forEach(warning => console.warn(`  - ${warning}`));
    }
  }
};

/**
 * Get helpful error message for missing environment variables
 */
export const getEnvironmentErrorMessage = () => {
  const result = validateEnvironment();

  if (result.isValid) {
    return null;
  }

  let message = 'Missing required environment variables:\n';
  result.missing.forEach(missing => {
    message += `• ${missing}\n`;
  });

  message += '\nTo fix this:\n';
  message += '1. Create a .env file in your project root\n';
  message += '2. Add the following variables:\n';
  message += '   EXPO_PUBLIC_SUPABASE_URL=your_supabase_project_url\n';
  message += '   EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key\n';
  message += '3. Restart your Expo development server\n';

  return message;
};
