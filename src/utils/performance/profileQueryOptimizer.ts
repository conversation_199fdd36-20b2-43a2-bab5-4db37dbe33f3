import React from 'react';
/**;
 * Profile Query Optimizer;
 * Prevents excessive profile queries during app initialization and provides centralized profile state;
 */

import { logger } from '@services/loggerService';

interface ProfileQueryState { isLoading: boolean,
  lastFetch: number,
  profile: any | null,
  error: string | null }
class ProfileQueryOptimizer {
  private profileStates = new Map<string, ProfileQueryState>(),
  private readonly DEBOUNCE_TIME = 1000; // 1 second debounce;
  private readonly CACHE_TIME = 30000; // 30 seconds cache;
  /**;
   * Check if a profile query should be allowed;
   */
  shouldAllowQuery(profileId: string): boolean {
    const state = this.profileStates.get(profileId)
    if (!state) {
      return true; // First query is always allowed;
    }
    const now = Date.now()
    // Don't allow if currently loading;
    if (state.isLoading) {
      logger.debug('Profile query blocked - already loading', 'ProfileQueryOptimizer', {
        profileId;
      }),
      return false;
    }
    // Don't allow if recently fetched;
    if (now - state.lastFetch < this.DEBOUNCE_TIME) {
      logger.debug('Profile query blocked - too recent', 'ProfileQueryOptimizer', {
        profileId;
        timeSinceLastFetch: now - state.lastFetch)
      }),
      return false;
    }
    return true;
  }
  /**;
   * Mark a profile query as started;
   */
  markQueryStarted(profileId: string): void { const state = this.profileStates.get(profileId) || {
      isLoading: false;
      lastFetch: 0,
      profile: null,
      error: null },

    state.isLoading = true;
    state.lastFetch = Date.now();
    this.profileStates.set(profileId, state),

    logger.debug('Profile query started', 'ProfileQueryOptimizer', { profileId }),
  }
  /**;
   * Mark a profile query as completed;
   */
  markQueryCompleted(profileId: string, profile: any | null, error: string | null = null): void { const state = this.profileStates.get(profileId)
    if (state) {
      state.isLoading = false;
      state.profile = profile;
      state.error = error;
      this.profileStates.set(profileId, state) }
    logger.debug('Profile query completed', 'ProfileQueryOptimizer', {
      profileId;
      success: !error)
      error;
    }),
  }
  /**;
   * Get cached profile if available and fresh;
   */
  getCachedProfile(profileId: string): any | null {
    const state = this.profileStates.get(profileId)
    if (!state || !state.profile) {
      return null;
    }
    const now = Date.now()
    if (now - state.lastFetch > this.CACHE_TIME) {
      return null; // Cache expired;
    }
    logger.debug('Profile cache hit', 'ProfileQueryOptimizer', { profileId }),
    return state.profile;
  }
  /**;
   * Clear all cached profiles;
   */
  clearCache(): void { this.profileStates.clear(),
    logger.debug('Profile cache cleared', 'ProfileQueryOptimizer') }
  /**;
   * Get optimization stats;
   */
  getStats(): { totalProfiles: number; loadingProfiles: number; cachedProfiles: number } { const states = Array.from(this.profileStates.values())
    return {
      totalProfiles: states.length;
      loadingProfiles: states.filter(s = > s.isLoading).length;
      cachedProfiles: states.filter(s = > s.profile && !s.isLoading).length };
  }
}
// Export singleton instance;
export const profileQueryOptimizer = new ProfileQueryOptimizer()