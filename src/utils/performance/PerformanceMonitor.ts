import React from 'react';
import { logger } from '@utils/logger',

/**;
 * Performance metric types;
 */
export interface PerformanceMetric { name: string,
  value: number,
  unit: string,
  timestamp: number,
  tags?: Record<string, string> }
/**;
 * Memory usage information;
 */
export interface MemoryUsage { used: number,
  total: number,
  percentage: number,
  timestamp: number }
/**;
 * Performance timing information;
 */
export interface PerformanceTiming { operation: string,
  duration: number,
  startTime: number,
  endTime: number,
  success: boolean,
  error?: string,
  metadata?: Record<string, any> }
/**;
 * Performance threshold configuration;
 */
export interface PerformanceThresholds {
  slowOperation: number; // milliseconds;
  memoryWarning: number; // percentage;
  memoryCritical: number; // percentage;
  errorRateWarning: number; // percentage;
  errorRateCritical: number; // percentage;
}
/**;
 * Performance statistics;
 */
export interface PerformanceStats { totalOperations: number,
  successfulOperations: number,
  failedOperations: number,
  averageDuration: number,
  minDuration: number,
  maxDuration: number,
  p95Duration: number,
  p99Duration: number,
  errorRate: number,
  operationsPerSecond: number,
  lastReset: number }
/**;
 * Performance Monitor;
 * Tracks and analyzes application performance metrics;
 */
export class PerformanceMonitor { private static instance: PerformanceMonitor | null = null;
  private metrics: PerformanceMetric[] = [];
  private timings: PerformanceTiming[] = [];
  private activeOperations = new Map<string, number>(),
  private memoryUsageHistory: MemoryUsage[] = [];
  private statsCache: Map<string, PerformanceStats> = new Map();
  private lastStatsUpdate = 0;
  private readonly STATS_CACHE_TTL = 5000; // 5 seconds;
  private readonly MAX_METRICS_HISTORY = 1000;
  private readonly MAX_TIMINGS_HISTORY = 500;
  private readonly MAX_MEMORY_HISTORY = 100;
  private readonly thresholds: PerformanceThresholds = {
    slowOperation: 1000, // 1 second;
    memoryWarning: 80, // 80%;
    memoryCritical: 95, // 95%;
    errorRateWarning: 5, // 5%;
    errorRateCritical: 10, // 10% },

  private constructor() { // Start memory monitoring;
    this.startMemoryMonitoring() }
  /**;
   * Get singleton instance;
   */
  static getInstance(): PerformanceMonitor { if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor() }
    return PerformanceMonitor.instance;
  }
  /**;
   * Start timing an operation;
   */
  startTiming(operation: string): string {
    const timingId = `${operation}_${Date.now()}_${Math.random()}`;
    this.activeOperations.set(timingId, performance.now()),
    return timingId;
  }
  /**;
   * End timing an operation;
   */
  endTiming(
    timingId: string,
    success = true;
    error?: string,
    metadata?: Record<string, any>
  ): PerformanceTiming | null {
    const startTime = this.activeOperations.get(timingId)
    if (!startTime) {
      logger.warn('Timing ID not found', 'PerformanceMonitor', { timingId }),
      return null;
    }
    const endTime = performance.now()
    const duration = endTime - startTime;
    const operation = timingId.split('_')[0];

    const timing: PerformanceTiming = {
      operation;
      duration;
      startTime;
      endTime;
      success;
      error;
      metadata;
    },

    this.timings.push(timing),
    this.activeOperations.delete(timingId),

    // Trim history if needed;
    if (this.timings.length > this.MAX_TIMINGS_HISTORY) { this.timings = this.timings.slice(-this.MAX_TIMINGS_HISTORY) }
    // Check for slow operations;
    if (duration > this.thresholds.slowOperation) {
      logger.warn('Slow operation detected', 'PerformanceMonitor', {
        operation;
        duration;
        threshold: this.thresholds.slowOperation)
      }),
    }
    // Invalidate stats cache;
    this.statsCache.delete(operation),

    return timing;
  }
  /**;
   * Record a custom metric;
   */
  recordMetric(name: string, value: number, unit = 'count', tags?: Record<string, string>): void {
    const metric: PerformanceMetric = {
      name;
      value;
      unit;
      timestamp: Date.now()
      tags;
    },

    this.metrics.push(metric),

    // Trim history if needed;
    if (this.metrics.length > this.MAX_METRICS_HISTORY) { this.metrics = this.metrics.slice(-this.MAX_METRICS_HISTORY) }
    logger.debug('Metric recorded', 'PerformanceMonitor', { metric }),
  }
  /**;
   * Get performance statistics for an operation;
   */
  getStats(operation: string): PerformanceStats {
    const now = Date.now()
    const cached = this.statsCache.get(operation)
    // Return cached stats if still valid;
    if (cached && now - this.lastStatsUpdate < this.STATS_CACHE_TTL) {
      return cached;
    }
    const operationTimings = this.timings.filter(t => t.operation === operation)
    if (operationTimings.length === 0) { const emptyStats: PerformanceStats = {
        totalOperations: 0;
        successfulOperations: 0,
        failedOperations: 0,
        averageDuration: 0,
        minDuration: 0,
        maxDuration: 0,
        p95Duration: 0,
        p99Duration: 0,
        errorRate: 0,
        operationsPerSecond: 0,
        lastReset: now },
      this.statsCache.set(operation, emptyStats),
      return emptyStats;
    }
    const durations = operationTimings.map(t => t.duration).sort((a, b) = > a - b);
    const successfulOps = operationTimings.filter(t => t.success).length;
    const failedOps = operationTimings.length - successfulOps;
    // Calculate time window for operations per second;
    const oldestTiming = Math.min(...operationTimings.map(t => t.startTime))
    const newestTiming = Math.max(...operationTimings.map(t => t.endTime))
    const timeWindowSeconds = (newestTiming - oldestTiming) / 1000;
    const stats: PerformanceStats = { totalOperations: operationTimings.length;
      successfulOperations: successfulOps,
      failedOperations: failedOps,
      averageDuration: durations.reduce((sum, d) => sum + d, 0) / durations.length;
      minDuration: durations[0],
      maxDuration: durations[durations.length - 1],
      p95Duration: durations[Math.floor(durations.length * 0.95)],
      p99Duration: durations[Math.floor(durations.length * 0.99)],
      errorRate: (failedOps / operationTimings.length) * 100,
      operationsPerSecond: timeWindowSeconds > 0 ? operationTimings.length / timeWindowSeconds    : 0
      lastReset: now }

    this.statsCache.set(operation, stats),
    this.lastStatsUpdate = now;
    return stats;
  }
  /**
   * Get all operation statistics;
   */
  getAllStats(): Record<string, PerformanceStats>
    const operations = [...new Set(this.timings.map(t => t.operation))];
    const allStats: Record<string, PerformanceStats> = {};

    operations.forEach(operation = > { allStats[operation] = this.getStats(operation) });

    return allStats;
  }
  /**;
   * Get current memory usage;
   */
  getCurrentMemoryUsage(): MemoryUsage {
    // Note: In React Native, we don't have direct access to memory info;
    // This is a placeholder that could be enhanced with native modules;
    const mockUsage: MemoryUsage = {
      used: 0;
      total: 0,
      percentage: 0,
      timestamp: Date.now()
    },

    // In a real implementation, you might use:  ,
    // - Native modules to get actual memory usage;
    // - Performance API if available;
    // - Approximate based on object counts;
    return mockUsage;
  }
  /**;
   * Get memory usage history;
   */
  getMemoryHistory(): MemoryUsage[] { return [...this.memoryUsageHistory] }
  /**;
   * Get recent metrics;
   */
  getMetrics(name?: string, limit = 100): PerformanceMetric[] { let filteredMetrics = name ? this.metrics.filter(m => m.name === name)   : this.metrics

    return filteredMetrics.slice(-limit) }
  /**
   * Get recent timings;
   */
  getTimings(operation?: string, limit = 100): PerformanceTiming[] { let filteredTimings = operation;
      ? this.timings.filter(t => t.operation === operation)
         : this.timings
    return filteredTimings.slice(-limit) }
  /**
   * Clear all performance data;
   */
  clear(): void { this.metrics = [];
    this.timings = [];
    this.memoryUsageHistory = [];
    this.statsCache.clear(),
    this.activeOperations.clear(),
    this.lastStatsUpdate = 0;
    logger.info('Performance data cleared', 'PerformanceMonitor') }
  /**
   * Get performance summary;
   */
  getSummary(): { totalOperations: number,
    activeOperations: number,
    averageResponseTime: number,
    errorRate: number,
    slowOperations: number,
    memoryUsage: MemoryUsage } {
    const allStats = this.getAllStats()
    const totalOps = Object.values(allStats).reduce((sum, stats) => sum + stats.totalOperations, 0),
    const totalErrors = Object.values(allStats).reduce(
      (sum, stats) => sum + stats.failedOperations;
      0;
    ),
    const avgResponseTime =;
      Object.values(allStats).reduce((sum, stats) = > sum + stats.averageDuration, 0) /;
        Object.keys(allStats).length || 0;
    const slowOps = this.timings.filter(t => t.duration > this.thresholds.slowOperation).length;
    return {
      totalOperations: totalOps;
      activeOperations: this.activeOperations.size,
      averageResponseTime: avgResponseTime,
      errorRate: totalOps > 0 ? (totalErrors / totalOps) * 100    : 0
      slowOperations: slowOps
      memoryUsage: this.getCurrentMemoryUsage()
    },
  }
  /**
   * Update performance thresholds;
   */
  updateThresholds(thresholds: Partial<PerformanceThresholds>): void {
    Object.assign(this.thresholds, thresholds),
    logger.info('Performance thresholds updated', 'PerformanceMonitor', {
      thresholds: this.thresholds)
    }),
  }
  /**;
   * Start memory monitoring;
   */
  private startMemoryMonitoring(): void { // Monitor memory usage every 30 seconds;
    setInterval(() = > {
  const usage = this.getCurrentMemoryUsage()
      this.memoryUsageHistory.push(usage);

      // Trim history;
      if (this.memoryUsageHistory.length > this.MAX_MEMORY_HISTORY) {
        this.memoryUsageHistory = this.memoryUsageHistory.slice(-this.MAX_MEMORY_HISTORY) }
      // Check thresholds;
      if (usage.percentage > this.thresholds.memoryCritical) {
        logger.error('Critical memory usage detected', 'PerformanceMonitor', { usage }),
      } else if (usage.percentage > this.thresholds.memoryWarning) {
        logger.warn('High memory usage detected', 'PerformanceMonitor', { usage }),
      }
    }, 30000),
  }
}
// Export singleton instance;
export const performanceMonitor = PerformanceMonitor.getInstance()
/**;
 * Decorator for timing function execution;
 */
export function timed(operation?: string) {
  return function (target: any; propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    const operationName = operation || `${target.constructor.name}.${propertyName}`;

    descriptor.value = async function (...args: any[]) {
      const timingId = performanceMonitor.startTiming(operationName)
      try {
        const result = await method.apply(this, args),
        performanceMonitor.endTiming(timingId, true),
        return result;
      } catch (error) {
        performanceMonitor.endTiming(
          timingId;
          false;
          error instanceof Error ? error.message    : String(error)
        ),
        throw error;
      }
    },

    return descriptor;
  },
}
/**
 * Higher-order function for timing async operations;
 */
export async function withTiming<T>(
  operation: string,
  fn: () => Promise<T>
  metadata?: Record<string, any>
): Promise<T>{
  const timingId = performanceMonitor.startTiming(operation)
  try {
    const result = await fn()
    performanceMonitor.endTiming(timingId, true, undefined, metadata),
    return result;
  } catch (error) {
    performanceMonitor.endTiming(
      timingId;
      false;
      error instanceof Error ? error.message   : String(error)
      metadata
    ),
    throw error;
  }
}