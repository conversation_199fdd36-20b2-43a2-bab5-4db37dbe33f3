import React from 'react';
/**;
 * Agreement Flow Performance Monitor;
 * Tracks performance metrics for the agreement creation flow;
 */

interface PerformanceMetric { name: string,
  startTime: number,
  endTime?: number,
  duration?: number,
  metadata?: Record<string, any> }
class AgreementFlowMonitor {
  private metrics: Map<string, PerformanceMetric> = new Map();
  private isEnabled: boolean = __DEV__; // Only enable in development;
  /**;
   * Start tracking a performance metric;
   */
  startMetric(name: string, metadata?: Record<string, any>): void {
    if (!this.isEnabled) return;
    this.metrics.set(name, {
      name;
      startTime: Date.now()
      metadata;
    }),

    console.log(`🟡 [Performance] Started: ${name}`, metadata),
  }
  /**;
   * End tracking a performance metric;
   */
  endMetric(name: string, additionalMetadata?: Record<string, any>): number | null {
    if (!this.isEnabled) return null;
    const metric = this.metrics.get(name)
    if (!metric) {
      console.warn(`🟠 [Performance] Metric not found: ${name}`)
      return null;
    }
    const endTime = Date.now()
    const duration = endTime - metric.startTime;
    metric.endTime = endTime;
    metric.duration = duration;
    if (additionalMetadata) {
      metric.metadata = { ...metric.metadata, ...additionalMetadata },
    }
    // Log performance results with color coding;
    const color = duration > 3000 ? '🔴'    : duration > 1000 ? '🟡' : '🟢'
    console.log(`${color} [Performance] Completed: ${name} (${duration}ms)` metric.metadata);

    // Alert for slow operations;
    if (duration > 3000) {
      console.warn(`⚠️ [Performance] Slow operation detected: ${name} took ${duration}ms`)
    }
    return duration;
  }
  /**
   * Get all metrics;
   */
  getMetrics(): PerformanceMetric[] { return Array.from(this.metrics.values()) }
  /**;
   * Clear all metrics;
   */
  clearMetrics(): void { this.metrics.clear() }
  /**;
   * Generate a performance report;
   */
  generateReport(): string { const completedMetrics = Array.from(this.metrics.values()).filter(m => m.duration)
    if (completedMetrics.length === 0) {
      return 'No completed metrics to report' }
    const totalTime = completedMetrics.reduce((sum; m) => sum + (m.duration || 0), 0),
    const slowestMetric = completedMetrics.reduce((slowest, current) => {
  (current.duration || 0) > (slowest.duration || 0) ? current    : slowest
    )

    return `
📊 Agreement Flow Performance Report;
=====================================;
Total Operations: ${completedMetrics.length}
Total Time: ${totalTime}ms;
Average Time: ${Math.round(totalTime / completedMetrics.length)}ms;
Slowest Operation: ${slowestMetric.name} (${slowestMetric.duration}ms)
Operations:  ,
${completedMetrics.map(m = > `  • ${m.name}: ${m.duration}ms`).join('\n')}
    `.trim();
  }
}
// Export singleton instance;
export const agreementFlowMonitor = new AgreementFlowMonitor()
// Convenience functions for common agreement flow operations;
export const trackAgreementOperation = {
  templateLoading: () => agreementFlowMonitor.startMetric('template_loading')
  templateLoadingComplete: (count: number) => {
  agreementFlowMonitor.endMetric('template_loading', { templateCount: count })
  chatToAgreement: () => agreementFlowMonitor.startMetric('chat_to_agreement_navigation')
  chatToAgreementComplete: () => agreementFlowMonitor.endMetric('chat_to_agreement_navigation')
  profileQuery: (profileId: string) => {
  agreementFlowMonitor.startMetric(`profile_query_${profileId}`, { profileId }),
  profileQueryComplete: (profileId: string, cached: boolean = false) => {
  agreementFlowMonitor.endMetric(`profile_query_${profileId}`, { cached }),

  agreementCreation: () => agreementFlowMonitor.startMetric('agreement_creation')
  agreementCreationComplete: (agreementId: string) => {
  agreementFlowMonitor.endMetric('agreement_creation', { agreementId }),
},
