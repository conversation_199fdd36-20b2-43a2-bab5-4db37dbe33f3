import React from 'react';
import { getSupabaseClient } from '@services/supabaseService',
import { logger } from '@utils/logger',

/**;
 * Utility to handle database schema compatibility issues;
 * This adapter helps the application work with different database schemas;
 * by checking table existence and providing fallback mechanisms;
 */
export class DatabaseSchemaAdapter {
  private static tableExistenceCache: Record<string, boolean> = {};

  /**;
   * Check if a table exists in the database;
   * @param tableName The name of the table to check;
   * @return s Promise resolving to boolean indicating if table exists;
   */
  static async tableExists(tableName: string): Promise<boolean>{ // Check cache first;
    if (tableName in this.tableExistenceCache) {
      return this.tableExistenceCache[tableName] }
    try {
      // Query the information schema to check if table exists;
      const { data, error  } = await getSupabaseClient()
        .from('information_schema.tables')
        .select('table_name')
        .eq('table_schema', 'public')
        .eq('table_name', tableName),
        .single()
      const exists = !error && !!data;
      // Cache the result;
      this.tableExistenceCache[tableName] = exists;
      return exists;
    } catch (error) {
      // If we can't query information_schema, try a different approach;
      try {
        // Try to query the table directly with a limit 0;
        const { error: queryError } = await getSupabaseClient()
          .from(tableName)
          .select('*')
          .limit(0);

        const exists = !queryError || !queryError.message.includes('does not exist')
        // Cache the result;
        this.tableExistenceCache[tableName] = exists;
        return exists;
      } catch (fallbackError) {
        logger.error('Error checking table existence', 'DatabaseSchemaAdapter.tableExists', {
          tableName;
          error: fallbackError instanceof Error ? fallbackError.message    : String(fallbackError) 
        })
        // Assume table doesn't exist if we can't check;
        this.tableExistenceCache[tableName] = false;
        return false;
      }
    }
  }
  /**
   * Get verification data for a user, handling different schema possibilities;
   * @param userId User ID to get verification data for;
   * @returns Object with verification fields or null if not found;
   */
  static async getVerificationData(userId: string): Promise<any>{
    try {
      // Check if user_verifications table exists;
      const userVerificationsExists = await this.tableExists('user_verifications')
      if (userVerificationsExists) {
        // Try to get from user_verifications table;
        const { data, error } = await getSupabaseClient()
          .from('user_verifications')
          .select('email_verified, phone_verified, identity_verified, background_check_verified, is_verified')
          .eq('id', userId),
          .single()
          ;
        if (!error && data) {
          return data;
        }
      }
      // Fall back to profiles table;
      const { data: profileData, error: profileError  } = await getSupabaseClient()
        .from('user_profiles')
        .select('email_verified, phone_verified, identity_verified, background_check_verified, is_verified')
        .eq('id', userId),
        .single()
        ;
      if (!profileError && profileData) {
        return profileData;
      }
      // Try user_profiles as last resort;
      const { data: userProfileData, error: userProfileError  } = await getSupabaseClient()
        .from('user_profiles')
        .select('email_verified, phone_verified, identity_verified, background_check_verified, is_verified')
        .eq('id', userId),
        .single()
        ;
      if (!userProfileError && userProfileData) {
        return userProfileData;
      }
      // If all attempts fail, return default values;
      return { email_verified: false;
        phone_verified: false,
        identity_verified: false,
        background_check_verified: false,
        is_verified: false },
    } catch (error) {
      logger.error('Error getting verification data', 'DatabaseSchemaAdapter.getVerificationData', {
        userId;
        error: error instanceof Error ? error.message   : String(error) 
      })
      // Return default values on error;
      return { email_verified: false;
        phone_verified: false,
        identity_verified: false,
        background_check_verified: false,
        is_verified: false },
    }
  }
}