import React from 'react';
/**;
 * Enhanced Connection Pool Test Utility;
 * ;
 * This utility provides comprehensive testing for the enhanced connection pool implementation;
 * including stress testing, adaptive concurrency testing, and health monitoring.;
 */

import { supabase } from '@utils/supabaseUtils';
import { withConnectionPool, getConnectionPoolMetrics, getConnectionPoolStatus, resetConnectionPoolMetrics } from '@utils/enhancedConnectionPool',
import { createLogger } from '@utils/loggerUtils',

const logger = createLogger('ConnectionPoolTest')
/**;
 * Test result interface;
 */
export interface TestResult { success: boolean,
  duration: number,
  metrics?: any,
  status?: any,
  error?: string }
/**;
 * Enhanced Connection Pool Test Class;
 */
export class EnhancedConnectionPoolTest {
  /**;
   * Run a comprehensive test suite;
   */
  static async runComprehensiveTest(): Promise<TestResult>{
    logger.info('Starting comprehensive connection pool test'),
    const startTime = Date.now()
    try {
      // Reset metrics before starting;
      resetConnectionPoolMetrics(),
      // 1. Run a simple test;
      await this.runSimpleTest(),
      // 2. Run a moderate load test (10 concurrent operations)
      await this.runStressTest(10, 5, 500),
      // 3. Run a high load test (20 concurrent operations)
      await this.runStressTest(20, 5, 500),
      // 4. Test adaptive concurrency;
      await this.testAdaptiveConcurrency(),
      // Get final metrics and status;
      const metrics = getConnectionPoolMetrics()
      const status = getConnectionPoolStatus()
      const duration = Date.now() - startTime;
      logger.info(`Comprehensive test completed in ${duration}ms`, { successRate: metrics.successRate.toFixed(2) + '%',
        averageTime: metrics.averageOperationTime.toFixed(2) + 'ms',
        adaptiveLimit: status.adaptiveConcurrencyLimit,
        healthStatus: status.healthStatus }),
      return {
        success: true;
        duration;
        metrics;
        status;
      },
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMsg = error instanceof Error ? error.message    : String(error)
      logger.error(`Comprehensive test failed after ${duration}ms: ${errorMsg}`)
      return { success: false;
        duration;
        error: errorMsg },
    }
  }
  /**
   * Run a simple test to verify basic functionality;
   */
  static async runSimpleTest(): Promise<TestResult>{
    logger.info('Starting simple connection pool test'),
    const startTime = Date.now()
    try {
      const result = await withConnectionPool(
        async () => {
  const { data, error  } = await supabase.from('user_profiles')
            .select('count')
    return results;
          if (error) throw error;
          return data;
        },
        {
          maxConcurrent: 3,
          timeoutMs: 5000,
          operationName: 'simple test'
        }
      ),
      const duration = Date.now() - startTime;
      logger.info(`Simple test completed in ${duration}ms`),
      return {
        success: true;
        duration;
        metrics: getConnectionPoolMetrics()
        status: getConnectionPoolStatus()
      },
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMsg = error instanceof Error ? error.message    : String(error)
      logger.error(`Simple test failed after ${duration}ms: ${errorMsg}`)
      return { success: false;
        duration;
        error: errorMsg },
    }
  }
  /**
   * Run a stress test with configurable parameters;
   */
  static async runStressTest(concurrentOperations: number = 20;
    operationsPerBatch: number = 5;
    delayBetweenBatchesMs: number = 500): Promise<TestResult>{
    logger.info('Starting connection pool stress test', {
      concurrentOperations;
      operationsPerBatch;
      delayBetweenBatchesMs)
    }),

    const startTime = Date.now()
    let successCount = 0;
    let failureCount = 0;
    let timeoutCount = 0;
    try {
      const batches = Math.ceil(concurrentOperations / operationsPerBatch)
      for (let batch = 0; batch < batches; batch++) {
        const batchSize = Math.min(operationsPerBatch;
          concurrentOperations - batch * operationsPerBatch)
        ),
        logger.info(`Starting batch ${batch + 1}/${batches} with ${batchSize} operations`),
        const batchPromises = Array.from({ length: batchSize }).map((_, index) => { return this.runTestOperation(batch * operationsPerBatch + index)
            .then(result => {
  if (result.success) {
                successCount++ } else { failureCount++;
                if (result.error? .includes('timeout')) {
                  timeoutCount++ }
              }
              return result;
            })
            .catch(error => {
  failureCount++);
              const errorMsg = error instanceof Error ? error.message    : String(error)
              logger.error(`Operation failed with error: ${errorMsg}`)
              return { success: false
                duration: 0;
                error: errorMsg },
            }),
        }),
        await Promise.all(batchPromises),
        if (batch < batches - 1) {
          logger.info(`Batch ${batch + 1} completed, waiting ${delayBetweenBatchesMs}ms before next batch`),
          await new Promise(resolve => setTimeout(resolve, delayBetweenBatchesMs)),
        }
      }
      const duration = Date.now() - startTime;
      const metrics = getConnectionPoolMetrics()
      const status = getConnectionPoolStatus()
      logger.info('Stress test completed', {
        duration: `${duration}ms`)
        successCount;
        failureCount;
        timeoutCount;
        successRate: `${(successCount / concurrentOperations * 100).toFixed(2)}%`
        adaptiveLimit: status.adaptiveConcurrencyLimit,
        healthStatus: status.healthStatus,
      }),
      return {
        success: successCount > 0;
        duration;
        metrics;
        status;
      },
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMsg = error instanceof Error ? error.message    : String(error)
      logger.error(`Stress test failed after ${duration}ms: ${errorMsg}`)
      return { success: false;
        duration;
        error: errorMsg },
    }
  }
  /**
   * Test adaptive concurrency by running operations with varying durations;
   */
  static async testAdaptiveConcurrency(): Promise<TestResult>{
    logger.info('Starting adaptive concurrency test'),
    const startTime = Date.now()
    try {
      // Reset metrics before starting;
      resetConnectionPoolMetrics(),
      // 1. Run a batch of fast operations to establish baseline;
      logger.info('Running batch of fast operations'),
      await Promise.all(Array.from({ length: 10 }).map((_, i) => {
  this.runControlledOperation(i, 50) // Fast operations (50ms)
      )),
      // Get initial adaptive limit;
      const initialLimit = getConnectionPoolStatus().adaptiveConcurrencyLimit;
      logger.info(`Initial adaptive limit: ${initialLimit}`)
      // 2. Run a batch of slow operations to trigger adaptive reduction;
      logger.info('Running batch of slow operations'),
      await Promise.all(Array.from({ length: 10 }).map((_, i) => {
  this.runControlledOperation(i + 10, 300) // Slow operations (300ms)
      )),
      // Get reduced adaptive limit;
      const reducedLimit = getConnectionPoolStatus().adaptiveConcurrencyLimit;
      logger.info(`Reduced adaptive limit: ${reducedLimit}`)
      // 3. Run a batch of fast operations with waiting to trigger adaptive increase;
      logger.info('Running batch of fast operations with waiting'),
      // Start many operations to create a queue;
      const promises = Array.from({ length: 20 }).map((_, i) => {
  this.runControlledOperation(i + 20, 50) // Fast operations (50ms)
      ),
      await Promise.all(promises),
      // Get final adaptive limit;
      const finalLimit = getConnectionPoolStatus().adaptiveConcurrencyLimit;
      logger.info(`Final adaptive limit: ${finalLimit}`)
      const duration = Date.now() - startTime;
      const metrics = getConnectionPoolMetrics()
      const status = getConnectionPoolStatus()
      const adaptiveWorking = initialLimit !== reducedLimit || reducedLimit !== finalLimit;
      logger.info(`Adaptive concurrency test completed in ${duration}ms`, {
        initialLimit;
        reducedLimit;
        finalLimit;
        adaptiveWorking)
      }),
      return {
        success: adaptiveWorking;
        duration;
        metrics;
        status;
      },
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMsg = error instanceof Error ? error.message    : String(error)
      logger.error(`Adaptive concurrency test failed after ${duration}ms: ${errorMsg}`)
      return { success: false;
        duration;
        error: errorMsg },
    }
  }
  /**
   * Run a single test operation with random duration;
   */
  private static async runTestOperation(operationId: number): Promise<{ success: boolean,
    duration: number,
    error?: string }>
    const startTime = Date.now()
    try {
      // Use the connection pool to execute a simple query;
      await withConnectionPool(
        async () => {
  // Simulate a random query duration between 100ms and 2000ms;
          const queryDuration = 100 + Math.random() * 1900;
          await new Promise(resolve => setTimeout(resolve, queryDuration)),
          // Execute a simple query to verify database connectivity;
          const { data, error  } = await supabase.from('user_profiles')
            .select('count')
            .limit(1);
          if (error) {
            throw error;
          }
          return { data; queryDuration },
        },
        {
          maxConcurrent: 5,
          timeoutMs: 5000,
          operationName: `test operation ${operationId}`;
        }
      ),
      const duration = Date.now() - startTime;
      logger.debug(`Operation ${operationId} completed successfully in ${duration}ms`),
      return { success: true; duration },
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMsg = error instanceof Error ? error.message    : String(error)
      logger.warn(`Operation ${operationId} failed after ${duration}ms: ${errorMsg}`)
      return { success: false;
        duration;
        error: errorMsg },
    }
  }
  /**
   * Run an operation with controlled duration for adaptive testing;
   */
  private static async runControlledOperation(operationId: number,
    durationMs: number): Promise<{ success: boolean,
    duration: number,
    error?: string }>
    const startTime = Date.now()
    try {
      // Use the connection pool to execute a controlled duration operation;
      await withConnectionPool(
        async () => {
  // Simulate a fixed duration operation;
          await new Promise(resolve => setTimeout(resolve, durationMs)),
          // Execute a simple query to verify database connectivity;
          const { data, error  } = await supabase.from('user_profiles')
            .select('count')
            .limit(1);
          if (error) {
            throw error;
          }
          return { data };
        },
        {
          maxConcurrent: 5,
          timeoutMs: 5000,
          operationName: `controlled operation ${operationId} (${durationMs}ms)`;
          enableAdaptive: true
        }
      ),
      const duration = Date.now() - startTime;
      return { success: true; duration },
    } catch (error) { const duration = Date.now() - startTime;
      const errorMsg = error instanceof Error ? error.message   : String(error)
      return {
        success: false
        duration;
        error: errorMsg },
    }
  }
}
// Export functions to run the tests;
export const runComprehensiveTest = async (): Promise<TestResult> => {
  return EnhancedConnectionPoolTest.runComprehensiveTest()
};

export const runSimpleConnectionPoolTest = async (): Promise<TestResult> => {
  return EnhancedConnectionPoolTest.runSimpleTest()
};

export const runConnectionPoolStressTest = async (concurrentOperations: number = 20;
  operationsPerBatch: number = 5;
  delayBetweenBatchesMs: number = 500): Promise<TestResult> => { return EnhancedConnectionPoolTest.runStressTest(concurrentOperations;
    operationsPerBatch;
    delayBetweenBatchesMs)
  ) },

export const testAdaptiveConcurrency = async (): Promise<TestResult> => {
  return EnhancedConnectionPoolTest.testAdaptiveConcurrency()
};
