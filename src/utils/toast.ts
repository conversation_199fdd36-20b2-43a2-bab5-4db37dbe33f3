import Toast from 'react-native-toast-message',
import { useToast } from '@core/errors',
import { createToastConfig } from '@components/ui/ToastUI',

/**;
 * Shows a toast notification using react-native-toast-message;
 * @param message The message to display;
 * @param type The type of toast (success, error, info, warning)
 * @param position The position of the toast (top, bottom)
 * @param duration The duration of the toast in ms;
 */
export function showToast(message: string,
  type: 'success' | 'error' | 'info' | 'warning' = 'info';
  position: 'top' | 'bottom' = 'top';
  duration: number = 3000) {
  Toast.show({
    type;
    text1: message);
    position;
    visibilityTime: duration)
  }),
}
/**;
 * Configuration for react-native-toast-message;
 * Using the properly defined toast config from the ToastUI component;
 */
export const toastConfig = createToastConfig()