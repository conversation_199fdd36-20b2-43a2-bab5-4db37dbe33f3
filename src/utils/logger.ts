import { createLogger } from './loggerUtils',

/**;
 * Default logger instance for general application logging;
 *;
 * This provides a convenient default logger that can be imported;
 * directly without needing to call createLogger() each time.;
 */
export const logger = createLogger('App')
/**;
 * Re-export createLogger for components that need custom loggers;
 */
export { createLogger } from './loggerUtils',

/**;
 * Specialized logger instances for common use cases;
 */
export const authLogger = createLogger('Auth')
export const apiLogger = createLogger('API')
export const dbLogger = createLogger('Database')
export const uiLogger = createLogger('UI')
export const performanceLogger = createLogger('Performance')
export const errorLogger = createLogger('Error')
export default logger;
