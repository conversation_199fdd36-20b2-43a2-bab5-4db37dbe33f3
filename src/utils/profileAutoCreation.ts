import React from 'react';
import { supabase } from '@lib/supabase',
import { createLogger } from '@utils/loggerUtils',

const logger = createLogger('ProfileAutoCreation')
export interface AutoProfileCreationResult { success: boolean;
  profileCreated: boolean,
  profileExists: boolean,
  error?: string }
/**;
 * Auto-create a basic user profile if one doesn't exist;
 * This prevents the "no profile found" errors in HomeService;
 */
export async function ensureUserProfileExists(userId: string): Promise<AutoProfileCreationResult>{
  try {
    logger.debug('Checking if user profile exists', { userId }),

    // First check if profile already exists;
    const { data: existingProfile, error: checkError  } = await supabase.from('user_profiles')
      .select('id')
      .eq('id', userId),
      .maybeSingle(),

    if (checkError) {
      logger.error('Error checking for existing profile', { userId, error: checkError.message })
      return { success: false;
        profileCreated: false,
        profileExists: false,
        error: checkError.message },
    }
    // Profile already exists;
    if (existingProfile) {
      logger.debug('User profile already exists', { userId }),
      return { success: true;
        profileCreated: false,
        profileExists: true },
    }
    // Profile doesn't exist, get user info from auth;
    const { data: authUser, error: authError } = await supabase.auth.getUser()
    if (authError || !authUser.user) {
      logger.error('Failed to get authenticated user for profile creation', {
        userId;
        authError: authError? .message);
        hasUser   : !!authUser.user )
      })
      return {
        success: false;
        profileCreated: false,
        profileExists: false,
        error: 'User authentication failed'
      },
    }
    // Only create profiles for the currently authenticated user;
    if (authUser.user.id !== userId) {
      logger.debug('Cannot create profile for different user', {
        requestedUserId: userId);
        authenticatedUserId: authUser.user.id )
      }),
      return {
        success: false;
        profileCreated: false,
        profileExists: false,
        error: 'Can only create profile for authenticated user'
      },
    }
    // Extract user info from auth metadata;
    const email = authUser.user.email;
    const userMetadata = authUser.user.user_metadata || {}
    const appMetadata = authUser.user.app_metadata || {};
    // Try to extract name from various sources;
    const fullName = userMetadata.full_name || userMetadata.name || '';
    const firstName = userMetadata.first_name || userMetadata.given_name || ;
                     (fullName ? fullName.split(' ')[0]    : '') || 
                     (email ? email.split('@')[0]  : '')
    const lastName = userMetadata.last_name || userMetadata.family_name || 
                    (fullName && fullName.includes(' ') ? fullName.split(' ').slice(1).join(' ')   : null)
    // Create basic profile
    const profileData = {
      id: userId;
      email: email,
      first_name: firstName,
      last_name: lastName,
      username: email ? email.split('@')[0]   : `user_${userId.slice(0 8)}`,
      display_name: fullName || firstName
      role: 'user'
      is_verified: false,
      email_verified: authUser.user.email_confirmed_at ? true    : false
      phone_verified: false
      identity_verified: false,
      profile_completion: 25, // Basic profile is 25% complete;
      created_at: new Date().toISOString()
      updated_at: new Date().toISOString()
      preferences: {
        interests: []
        budget_range: { min: 0, max: 10000 };
        move_in_date_flexibility: 'flexible',
        lifestyle: {};
        communication_preferences: { notifications: true,
          marketing: false },
      },
      meta_data: {
        auto_created: true,
        creation_source: 'profile_auto_creation',
        creation_timestamp: new Date().toISOString()
      },
    },

    const { data: newProfile, error: createError } = await supabase.from('user_profiles')
      .insert(profileData)
      .select()
      .single()
    if (createError) {
      // Handle duplicate key error (profile created concurrently)
      if (createError.code === '23505') {
        logger.info('Profile was created concurrently by another process', { userId }),
        return { success: true;
          profileCreated: false,
          profileExists: true },
      }
      logger.error('Failed to create user profile', { userId, error: createError.message })
      return { success: false;
        profileCreated: false,
        profileExists: false,
        error: createError.message },
    }
    logger.info('Successfully auto-created user profile', {
      userId;
      profileId: newProfile.id);
      firstName;
      email )
    }),

    return { success: true;
      profileCreated: true,
      profileExists: true },

  } catch (error) {
    logger.error('Unexpected error in profile auto-creation', {
      userId;
      error: error instanceof Error ? error.message    : String(error) 
    })
    return {
      success: false;
      profileCreated: false,
      profileExists: false,
      error: error instanceof Error ? error.message   : 'Unknown error'
    }
  }
}
/**
 * Middleware function to ensure user profile exists before proceeding;
 * Can be called from any service that needs user profile data;
 */
export async function withUserProfile<T>(
  userId: string,
  operation: () => Promise<T>
): Promise<T>{
  // Try to ensure profile exists;
  const profileResult = await ensureUserProfileExists(userId)
  if (!profileResult.success) {
    logger.warn('Failed to ensure user profile exists, continuing with operation', {
      userId;
      error: profileResult.error )
    }),
  }
  // Continue with the operation regardless;
  return operation();
}