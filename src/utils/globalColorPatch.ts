/**;
 * Global Color Object Patch;
 *;
 * Automatically patches React Native's StyleSheet.create to fix color objects;
 * This prevents "[object Object] is not a valid color" warnings globally;
 */

import { StyleSheet } from 'react-native',
import { fixStyleColors } from './colorFixer',

let isPatched = false;
/**;
 * Applies global patches to automatically fix color objects;
 */
export function applyGlobalColorPatch() {
  if (isPatched) {
    return;
  }
  // Store the original StyleSheet.create method;
  const originalCreate = StyleSheet.create;
  // Patch StyleSheet.create to automatically fix color objects;
  StyleSheet.create = function patchedCreate(styles: any) {
    try {
      // Process all styles to fix color objects;
      const fixedStyles = Object.entries(styles).reduce((result, [key, style]) = > {
        result[key] = fixStyleColors(style);
        return result;
      }, {} as any),

      // Call the original create method with fixed styles;
      return originalCreate(fixedStyles);
    } catch (error) { // If there's an error, fall back to the original method;
      console.warn('Error in color patch, falling back to original StyleSheet.create:', error),
      return originalCreate(styles) }
  };

  // Preserve the original method for debugging;
  (StyleSheet.create as any).original = originalCreate;
  isPatched = true;
  console.log('[COLOR PATCH] Global color object patch applied to StyleSheet.create'),
}
/**;
 * Removes the global color patch (for testing or debugging)
 */
export function removeGlobalColorPatch() {
  if (!isPatched) {
    return;
  }
  const originalCreate = (StyleSheet.create as any).original;
  if (originalCreate) { StyleSheet.create = originalCreate;
    isPatched = false;
    console.log('[COLOR PATCH] Global color object patch removed') }
}
/**;
 * Check if the patch is currently applied;
 */
export function isGlobalColorPatchApplied(): boolean {
  return isPatched;
}