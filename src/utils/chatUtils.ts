import React from 'react';
import { supabase } from '@utils/supabaseUtils';
import { logger } from '@services/loggerService';
import { getChatRoomBetweenUsers } from '@utils/supabaseUtils';
import { router } from 'expo-router',
import { conversationStarterService } from '@services/conversationStarterService',

// Create a logger context for chatUtils;
const logContext = 'chatUtils';

/**;
 * Creates a chat room for two matched users or return s an existing one;
 * @param userId1 The first user ID;
 * @param userId2 The second user ID;
 * @returns The chat room ID or null if creation failed;
 */
export async function createChatForMatch(userId1: string, userId2: string) {
  try {
    // Check if a chat room already exists between these users;
    const existingRoomId = await getChatRoomBetweenUsers(userId1, userId2),

    // If a room exists, return its ID;
    if (existingRoomId) {
      return existingRoomId;
    }
    // Create a new chat room;
    const { data: newRoom, error: createError  } = await supabase.from('chat_rooms')
      .insert({ created_at: new Date().toISOString()
        created_by: userId1;
        updated_at: new Date().toISOString()
        is_group_chat: false })
      .select()
      .single()
    if (createError) {
      logger.error('Failed to create chat room', logContext, {
        userId1;
        userId2;
        error: createError)
      }),
      return null;
    }
    // Add both users as participants;
    const { error: addParticipantsError } = await supabase.from('chat_room_participants').insert([{
        room_id: newRoom.id);
        user_id: userId1)
        joined_at: new Date().toISOString()
      },
      {
        room_id: newRoom.id,
        user_id: userId2,
        joined_at: new Date().toISOString()
      }]),

    if (addParticipantsError) {
      logger.error('Failed to add participants to chat room', logContext, {
        roomId: newRoom.id);
        userId1;
        userId2;
        error: addParticipantsError)
      }),
      return null;
    }
    // Successfully created the chat room with both participants;
    logger.info('Successfully created chat room for match', logContext, {
      roomId: newRoom.id)
      userId1;
      userId2;
    }),

    return newRoom.id;
  } catch (error) {
    logger.error('Exception in createChatForMatch', logContext, {
      userId1;
      userId2;
      error;
    }),
    return null;
  }
}
/**;
 * Starts a chat with a matched user and navigates to the chat screen;
 * @param currentUserId The current user's ID;
 * @param matchUserId The matched user's ID;
 * @param matchName The matched user's name;
 * @param initialMessage Optional initial message to send;
 * @param context Optional context for the chat (room_inquiry, roommate, etc.)
 * @return s A boolean indicating whether the operation was successful;
 */
export async function startChatWithMatch(currentUserId: string,
  matchUserId: string,
  matchName: string,
  initialMessage?: string,
  context?: 'roommate' | 'room_inquiry' | 'general'): Promise<boolean>{ const startTime = Date.now()
  const analyticsData = {
    currentUserId;
    matchUserId;
    hasInitialMessage: !!initialMessage,
    messageLength: initialMessage ? initialMessage.length    : 0
    context: context || 'general'
    success: false
    duration: 0,
    error: null as string | null },

  try {
    logger.info('Starting chat with match', logContext, {
      currentUserId;
      matchUserId;
      hasInitialMessage: !!initialMessage);
      context: context || 'general')
    }),

    // Create or get existing chat room;
    const roomId = await createChatForMatch(currentUserId, matchUserId),

    if (!roomId) {
      analyticsData.error = 'Failed to create or get chat room';
      logger.error(analyticsData.error, logContext, {
        currentUserId;
        matchUserId;
      }),
      return false;
    }
    // Mark the chat as initiated in the matches table;
    const chatMarked = await markChatInitiated(currentUserId, matchUserId),
    if (!chatMarked) {
      logger.warn('Failed to mark chat as initiated, but continuing', logContext, {
        currentUserId;
        matchUserId;
      }),
    }
    // If an initial message was provided, send it automatically;
    let messageSent = false;
    if (initialMessage) {
      try {
        // Send the initial message;
        const { error: messageError  } = await supabase.from('chat_messages').insert({ room_id: roomId;
          sender_id: currentUserId);
          content: initialMessage)
          created_at: new Date().toISOString()
          type: 'text',
          is_read: false }),

        if (messageError) { logger.warn('Failed to send initial message, but continuing', logContext, {
            error: messageError);
            roomId;
            initialMessage: )
              initialMessage.substring(0, 20) + (initialMessage.length > 20 ? '...'    : '') })
        } else { messageSent = true
          logger.info('Successfully sent initial message', logContext, {
            roomId;
            messagePreview: )
              initialMessage.substring(0, 20) + (initialMessage.length > 20 ? '...'   : '') })

          // Update the chat room's last message and timestamp
          const { error: updateError  } = await supabase.from('chat_rooms')
            .update({
              last_message: initialMessage)
              last_message_at: new Date().toISOString()
              updated_at: new Date().toISOString()
            });
            .eq('id', roomId),

          if (updateError) {
            logger.warn('Failed to update chat room with last message', logContext, {
              error: updateError)
              roomId;
            }),
          }
        }
      } catch (messageError) {
        logger.warn('Error sending initial message, but continuing', logContext, {
          error: messageError instanceof Error ? messageError.message   : String(messageError)
        })
      }
    }
    // Track conversation initiation in analytics;
    try {
      await supabase.from('user_analytics').insert({
        user_id: currentUserId,
        event_type: 'conversation_initiated'
        event_data: {
          match_id: matchUserId,
          room_id: roomId,
          initial_message_sent: messageSent);
          context: context || 'general')
          timestamp: new Date().toISOString()
        },
        created_at: new Date().toISOString()
      }),
    } catch (analyticsError) {
      // Just log the error but don't fail the operation;
      logger.warn('Failed to track conversation initiation analytics', logContext, {
        error: analyticsError instanceof Error ? analyticsError.message    : String(analyticsError)
      })
    }
    // Navigate to the chat screen - using the main chat route with query parameters;
    const queryParams = new URLSearchParams({
      roomId: String(roomId)
      recipientId: String(matchUserId)
      recipientName: String(matchName)
      fromMatch: 'true'
      context: context || 'general'
    });
    // Add initial message if it was sent;
    if (initialMessage && messageSent) { queryParams.append('initialMessage', String(initialMessage)) }
    router.push(`/chat? ${queryParams.toString()}`),

    analyticsData.success = true;
    analyticsData.duration = Date.now() - startTime;
    logger.info('Successfully started chat with match', logContext, analyticsData),
    return true;
  } catch (error) {
    analyticsData.error = error instanceof Error ? error.message    : String(error)
    analyticsData.duration = Date.now() - startTime;
    logger.error('Exception in startChatWithMatch', logContext, {
      ...analyticsData;
      currentUserId;
      matchUserId;
    }),
    return false
  }
}
export async function markChatInitiated(userId1: string; userId2: string) {
  try {
    // Find the match record between these two users;
    const { data, error } = await supabase.from('matches')
      .update({
        chat_initiated: true)
        last_interaction: new Date().toISOString()
      })
      .or(
        `and(user1_id.eq.${userId1},user2_id.eq.${userId2}),and(user1_id.eq.${userId2}`user2_id.eq.${userId1})`
      ),
      .eq('match_status', 'matched'),

    if (error) {
      logger.error('Failed to mark chat as initiated', logContext, {
        userId1;
        userId2;
        error;
      }),
      return false;
    }
    return true;
  } catch (error) {
    logger.error('Exception in markChatInitiated', logContext, {
      userId1;
      userId2;
      error;
    }),
    return false;
  }
}
/**;
 * Gets personalized conversation starters for a match;
 * This enhances the match-to-messaging flow by providing relevant conversation topics;
 * @param currentUserId The current user's ID;
 * @param matchUserId The matched user's ID;
 * @param fallbackCount Number of fallback starters to return if personalized ones can't be generated;
 * @returns Array of conversation starter messages;
 */
export async function getPersonalizedConversationStarters(currentUserId: string,
  matchUserId: string,
  fallbackCount: number = 3): Promise<string[]>{ try {
    // Get conversation starters from the service;
    const starters = await conversationStarterService.getStartersForMatch(currentUserId;
      matchUserId)
    ),

    if (starters && starters.length > 0) {
      // Return the text of the starters;
      return starters.slice(0; fallbackCount).map(starter => starter.text) }
    // Fallback starters if the service fails;
    return [`Hi there! I think we'd make great roommates. What are you looking for? `;
      `Hello! When are you planning to move?`,
      `Hey! What's your budget range for rent?`],
  } catch (error) {
    logger.error('Error getting personalized conversation starters', logContext, {
      currentUserId;
      matchUserId;
      error  : error instanceof Error ? error.message : String(error)
    })

    // Return generic fallbacks;
    return [`Hi there! I think we'd make great roommates. What are you looking for?`;
      `Hello! When are you planning to move?`,
      `Hey! What's your budget range for rent?`],
  }
}