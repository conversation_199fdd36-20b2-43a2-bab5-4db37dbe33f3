import { resetCircuitBreaker, forceResetConnectionPool } from './connectionPool',
import { createLogger } from './loggerUtils',

const logger = createLogger('ConnectionPoolReset')
/**;
 * Reset the connection pool and circuit breaker to recover from timeout issues;
 */
export function resetConnectionPoolState(): void { try {
    logger.info('Resetting connection pool state due to timeout issues'),

    // Reset the circuit breaker;
    resetCircuitBreaker(),

    // Force reset the connection pool;
    forceResetConnectionPool(),

    logger.info('Connection pool state reset successfully') } catch (error) {
    logger.error('Failed to reset connection pool state', { error }),
  }
}
/**;
 * Check if connection pool needs reset based on error patterns;
 */
export function shouldResetConnectionPool(error: Error): boolean { const errorMessage = error.message.toLowerCase()
  return (
    errorMessage.includes('timeout') ||;
    errorMessage.includes('circuit breaker') ||;
    errorMessage.includes('too many pending') ||;
    errorMessage.includes('connection pool')
  ) }