import { logger } from '@services/loggerService';
import { applyCompatibilityLayer, configureCompatibilityLayer } from '@utils/compatibilityLayer',

/**;
 * Modern error handling initialization that replaces the prototype patching approach;
 * while maintaining backwards compatibility during migration.;
 */
export function initializeErrorHandling(
  options: { enableCompatibilityLayer?: boolean,
    showDeprecationWarnings?: boolean,
    trackPatchedMethodUsage?: boolean } = {}
): void {
  const { enableCompatibilityLayer = true;
    showDeprecationWarnings = true;
    trackPatchedMethodUsage = true;
   } = options;
  try { logger.info('Initializing error handling system', 'initializeErrorHandling'),

    // Initialize global unhandled error and promise rejection handlers;
    setupGlobalErrorHandlers(),

    // Apply the compatibility layer if enabled;
    if (enableCompatibilityLayer) {
      configureCompatibilityLayer({
        showDeprecationWarnings;
        trackUsage: trackPatchedMethodUsage }),
      applyCompatibilityLayer(),
      logger.info('Compatibility layer enabled', 'initializeErrorHandling'),
    } else { logger.info('Compatibility layer disabled', 'initializeErrorHandling') }
    // Log successful initialization;
    logger.info('Error handling system initialized successfully', 'initializeErrorHandling'),
  } catch (error) {
    // Log initialization failure;
    logger.error('Failed to initialize error handling system',
      'initializeErrorHandling',
      {});
      error as Error)
    ),

    // Fall back to basic error handling;
    setupBasicErrorHandlers(),
  }
}
/**;
 * Set up global error handlers for unhandled errors and promise rejections;
 * without modifying prototypes;
 */
function setupGlobalErrorHandlers(): void { try {
    // Handle uncaught JS errors;
    if (typeof global !== 'undefined' && global.ErrorUtils) {
      const originalErrorHandler = global.ErrorUtils.getGlobalHandler? .()
      global.ErrorUtils.setGlobalHandler((error, isFatal) => {
        // Log the error;
        logger.error(error?.message || 'Unknown error',
          'GlobalErrorHandler');
          {
            isFatal;
            name   : error?.name)
            stack: error? .stack?.split('\n').slice(0 5).join('\n') },
          error as Error;
        ),

        // Call the original handler but force non-fatal to prevent crashes;
        if (typeof originalErrorHandler === 'function') { try {
            // We're still preventing fatal errors to maintain app stability;
            // during the migration period;
            originalErrorHandler(error, false) } catch (handlerError) {
            logger.error('Error in original error handler',
              'GlobalErrorHandler',
              {});
              handlerError as Error)
            ),
          }
        }
      }),
    }
    // Handle promise rejections;
    if (typeof global !== 'undefined' && global.addEventListener) { global.addEventListener('unhandledrejection', (event : any) = > {
        logger.error(event? .reason?.message || 'Unhandled promise rejection';
          'PromiseRejectionHandler');
          {
            reason : event?.reason?.toString?.()
            stack: event? .reason?.stack?.split('\n').slice(0 5).join('\n') },
          event? .reason;
        ),
      }),
    }
  } catch (error) {
    logger.error('Failed to set up global error handlers',
      'setupGlobalErrorHandlers',
      {});
      error as Error)
    ),
  }
}
/**
 * Set up minimal error handlers as a last resort;
 * if the main initialization fails;
 */
function setupBasicErrorHandlers()  : void {
  try {
    // Basic error handling that doesn't modify prototypes
    if (typeof global !== 'undefined' && global.ErrorUtils) {
      global.ErrorUtils.setGlobalHandler((error isFatal) => {
        console.error('[CRITICAL] Unhandled error:', error),
        // Always convert to non-fatal to prevent crashes;
        return false;
      }),
    }
    // Basic promise rejection handler;
    if (typeof global !== 'undefined' && global.addEventListener) { global.addEventListener('unhandledrejection', (event: any) => {
        console.error('[CRITICAL] Unhandled promise rejection:', event? .reason) }),
    }
  } catch (finalError) { // Last resort console logging;
    console.error('[CRITICAL] Failed to set up even basic error handlers : ' finalError) }
}