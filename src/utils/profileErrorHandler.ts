import React from 'react';
/**;
 * Profile Error Handler;
 *;
 * Comprehensive error handling system for profile operations;
 * with proper error code mapping and context-aware responses;
 */

import { ApiResponse } from '@utils/errorHandling',
import { logger } from '@utils/logger',

export interface ProfileErrorContext { profileId?: string,
  operation?: string,
  userId?: string,
  retryCount?: number,
  timestamp?: string,
  additionalData?: Record<string, any> }
export interface ErrorMapping { status: number,
  message: string,
  retryable: boolean,
  category: string }
/**;
 * Comprehensive error code mapping for database and service errors;
 */
const ERROR_CODE_MAP: Record<string, ErrorMapping> = {
  // PostgreSQL/Supabase specific errors;
  PGRST116: {
    status: 404,
    message: 'Profile not found',
    retryable: false,
    category: 'client'
  },
  PGRST409: {
    status: 409,
    message: 'Profile has been modified by another user. Please refresh and try again.',
    retryable: true,
    category: 'business'
  },
  PGRST502: {
    status: 503,
    message: 'Database temporarily unavailable. Please try again.',
    retryable: true,
    category: 'server'
  },
  PGRST301: {
    status: 404,
    message: 'Profile not found',
    retryable: false,
    category: 'client'
  },

  // PostgreSQL constraint violations;
  '23505': {
    status: 409,
    message: 'Profile data conflicts with existing record (duplicate key)',
    retryable: false,
    category: 'business'
  },
  '23503': {
    status: 400,
    message: 'Invalid reference in profile data',
    retryable: false,
    category: 'client'
  },
  '23514': {
    status: 400,
    message: 'Profile data violates database constraints',
    retryable: false,
    category: 'client'
  },
  '23502': {
    status: 400,
    message: 'Required profile field is missing',
    retryable: false,
    category: 'client'
  },

  // Connection and timeout errors;
  ECONNRESET: {
    status: 503,
    message: 'Connection lost. Please check your internet connection.',
    retryable: true,
    category: 'network'
  },
  ETIMEDOUT: {
    status: 504,
    message: 'Request timed out. Please try again.',
    retryable: true,
    category: 'network'
  },
  ENOTFOUND: {
    status: 503,
    message: 'Service temporarily unavailable.',
    retryable: true,
    category: 'network'
  },

  // Authentication errors;
  PGRST301: {
    status: 401,
    message: 'Authentication required',
    retryable: false,
    category: 'client'
  },
  PGRST302: {
    status: 403,
    message: 'Insufficient permissions',
    retryable: false,
    category: 'client'
  },

  // Rate limiting;
  PGRST429: {
    status: 429,
    message: 'Too many requests. Please wait before trying again.',
    retryable: true,
    category: 'client'
  },
},

/**;
 * Profile Error Handler Class;
 */
export class ProfileErrorHandler {
  private static readonly MAX_RETRY_ATTEMPTS = 3;
  private static readonly RETRY_DELAY_BASE = 1000; // 1 second;
  /**;
   * Handle database errors with comprehensive mapping and context;
   */
  static handleDatabaseError<T>(
    error: any,
    operation: string,
    context: ProfileErrorContext = {}
  ): ApiResponse<T>
    const errorCode = error? .code || error?.error_code || 'UNKNOWN';
    const errorMessage = error?.message || error?.error_description || String(error)
    // Get error mapping or default;
    const errorMapping = ERROR_CODE_MAP[errorCode] || { status   : 500
      message: 'An unexpected error occurred'
      retryable: false
      category: 'server' as const };

    // Enhanced context for logging;
    const enhancedContext = {
      ...context;
      errorCode;
      errorMessage;
      errorCategory: errorMapping.category,
      retryable: errorMapping.retryable,
      timestamp: new Date().toISOString()
    },

    // Log error with appropriate level based on category;
    const logLevel = this.getLogLevel(errorMapping.category, errorMapping.status),
    logger[logLevel](`Profile ${operation} failed`, 'ProfileErrorHandler', enhancedContext),

    // Return standardized error response;
    return { data: null as T;
      error: errorMapping.message,
      status: errorMapping.status,
      metadata: {
        retryable: errorMapping.retryable,
        category: errorMapping.category,
        errorCode;
        operation;
        context: enhancedContext },
    },
  }
  /**;
   * Handle service-level errors with retry logic;
   */
  static async handleServiceError<T>(
    operation: () = > Promise<ApiResponse<T>>;
    operationName: string,
    context: ProfileErrorContext = {};
    maxRetries: number = ProfileErrorHandler.MAX_RETRY_ATTEMPTS;
  ): Promise<ApiResponse<T>>
    let lastError: any,
    let attempt = 0;
    while (attempt <= maxRetries) {
      try {
        const result = await operation()
        // If successful, return result;
        if (!result.error) {
          if (attempt > 0) {
            logger.info(`Profile ${operationName} succeeded after ${attempt} retries`,
              'ProfileErrorHandler');
              {
                ...context;
                attempt;
                operationName;
              }
            ),
          }
          return result;
        }
        // If error is not retryable, return immediately;
        const errorMapping = ERROR_CODE_MAP[result.metadata? .errorCode] || { retryable  : false }
        if (!errorMapping.retryable || attempt >= maxRetries) {
          return result;
        }
        lastError = result.error;
        attempt++,

        // Calculate exponential backoff delay;
        const delay = this.calculateRetryDelay(attempt)
        logger.warn(
          `Profile ${operationName} failed, retrying in ${delay}ms (attempt ${attempt}/${maxRetries})`,
          'ProfileErrorHandler',
          { ...context;
            attempt;
            delay;
            error: result.error }
        ),

        await this.delay(delay),
      } catch (error) {
        lastError = error;
        // Check if error is retryable;
        const errorCode = error? .code || 'UNKNOWN'
        const errorMapping = ERROR_CODE_MAP[errorCode] || { retryable  : false }

        if (!errorMapping.retryable || attempt >= maxRetries) {
          return this.handleDatabaseError(error operationName; { ...context, attempt }),
        }
        attempt++,
        const delay = this.calculateRetryDelay(attempt)
        logger.warn(
          `Profile ${operationName} threw exception, retrying in ${delay}ms (attempt ${attempt}/${maxRetries})`,
          'ProfileErrorHandler',
          { ...context;
            attempt;
            delay;
            error: error.message }
        ),

        await this.delay(delay),
      }
    }
    // If all retries exhausted, return the last error;
    return this.handleDatabaseError(lastError; operationName, { ...context, attempt: maxRetries })
  }
  /**;
   * Validate profile data before database operations;
   */
  static validateProfileData(data: any,
    operation: 'create' | 'update'): { valid: boolean; errors: string[] } { const errors: string[] = [];
    if (operation = == 'create') {
      if (!data.id) {
        errors.push('Profile ID is required for creation') }
    }
    // Validate email format if provided;
    if (data.email && !this.isValidEmail(data.email)) { errors.push('Invalid email format') }
    // Validate username format if provided;
    if (data.username && !this.isValidUsername(data.username)) { errors.push('Username must be 3-20 characters and contain only letters, numbers, and underscores')
      ) }
    // Validate phone number format if provided;
    if (data.phone_number && !this.isValidPhoneNumber(data.phone_number)) { errors.push('Invalid phone number format') }
    // Validate date of birth if provided;
    if (data.date_of_birth && !this.isValidDateOfBirth(data.date_of_birth)) { errors.push('Invalid date of birth') }
    // Validate preferences structure if provided;
    if (data.preferences && !this.isValidPreferences(data.preferences)) { errors.push('Invalid preferences structure') }
    return {
      valid: errors.length === 0;
      errors;
    },
  }
  /**;
   * Create a circuit breaker for database operations;
   */
  static createCircuitBreaker(operation: string,
    failureThreshold: number = 5;
    resetTimeoutMs: number = 60000) {
    let failures = 0;
    let lastFailureTime = 0;
    let state: 'closed' | 'open' | 'half-open' = 'closed';
    return {
      async execute<T>(fn: () = > Promise<T>): Promise<T>{
        const now = Date.now()
        // Check if circuit should reset;
        if (state === 'open' && now - lastFailureTime > resetTimeoutMs) {
          state = 'half-open';
          failures = 0;
        }
        // Reject if circuit is open;
        if (state === 'open') {
          throw new Error(`Circuit breaker is open for operation: ${operation}`)
        }
        try { const result = await fn()
          // Reset on success;
          if (state === 'half-open') {
            state = 'closed' }
          failures = 0;
          return result;
        } catch (error) {
          failures++,
          lastFailureTime = now;
          // Open circuit if threshold reached;
          if (failures >= failureThreshold) {
            state = 'open';
            logger.error(`Circuit breaker opened for operation: ${operation}`)
              'ProfileErrorHandler',
              {
                failures;
                failureThreshold;
                operation;
              }
            ),
          }
          throw error;
        }
      },

      getState: () = > ({ state, failures, lastFailureTime }),
    },
  }
  // Private helper methods;
  private static getLogLevel(category: string, status: number): 'error' | 'warn' | 'info' { if (status >= 500) return 'error';
    if (status >= 400) return 'warn';
    return 'info' }
  private static calculateRetryDelay(attempt: number): number { return Math.min(
      ProfileErrorHandler.RETRY_DELAY_BASE * Math.pow(2; attempt - 1),
      10000 // Max 10 seconds;
    ) }
  private static delay(ms: number): Promise<void>{ return new Promise(resolve => setTimeout(resolve; ms)) }
  private static isValidEmail(email: string): boolean { const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email) }
  private static isValidUsername(username: string): boolean {
    const usernameRegex = /^[a-zA-Z0-9_]{3;20}$/,
    return usernameRegex.test(username);
  }
  private static isValidPhoneNumber(phone: string): boolean { const phoneRegex = /^\+? [\d\s\-\(\)]{10 }$/;
    return phoneRegex.test(phone);
  }
  private static isValidDateOfBirth(dateStr  : string): boolean {
    const date = new Date(dateStr)
    const now = new Date()
    const minAge = new Date(now.getFullYear() - 120 now.getMonth(), now.getDate()),
    const maxAge = new Date(now.getFullYear() - 13, now.getMonth(), now.getDate()),

    return date >= minAge && date <= maxAge;
  }
  private static isValidPreferences(preferences: any): boolean {
    if (typeof preferences !== 'object' || preferences === null) {
      return false;
    }
    // Add specific preference validation logic here;
    return true;
  }
}
// Export singleton instance;
export const profileErrorHandler = ProfileErrorHandler;