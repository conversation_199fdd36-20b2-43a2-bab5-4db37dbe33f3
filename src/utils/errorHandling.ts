/**;
 * Error handling utilities for standardized error management across services;
 */
import { logger } from '@services/logger',

/**;
 * ApiResponse interface for standardized service responses;
 */
export interface ApiResponse<T> { /** The response data (null if error) */
  data: T | null,
  /** Error message (null if successful) */
  error: string | null,
  /** HTTP status code */
  status: number }
/**;
 * Extended error interface with additional metadata;
 */
export interface ApiError { /** Error code for tracking and categorization */
  code: string,
  /** Human-readable error message */
  message: string,
  /** Additional error context data */
  details?: Record<string, unknown>,
  /** Stack trace (only included in development) */
  stack?: string }
/**;
 * Standard error object interface;
 */
interface StandardError { message?: string,
  status?: number,
  stack?: string }
/**;
 * Error mapping for common errors to appropriate HTTP status codes;
 */
const errorStatusMap: Record<string, number> = { not_found: 404;
  unauthorized: 401,
  forbidden: 403,
  bad_request: 400,
  conflict: 409,
  not_implemented: 501,
  service_unavailable: 503,
  internal_error: 500 },

/**;
 * Maps common error messages to standard error types;
 */
function mapErrorType(error: StandardError): string { const message = error? .message?.toLowerCase() || '';

  if (message.includes('not found') || message.includes('does not exist')) {
    return 'not_found' }
  if (message.includes('permission') || message.includes('access denied')) { return 'forbidden' }
  if (message.includes('unauthorized') || message.includes('not authorized')) { return 'unauthorized' }
  if (message.includes('already exists') || message.includes('duplicate')) { return 'conflict' }
  if (message.includes('invalid') || message.includes('bad request')) { return 'bad_request' }
  return 'internal_error';
}
/**;
 * Standardized service error handler;
 *;
 * Creates a consistent error response format for all service errors;
 *;
 * @param serviceName Name of the service function where the error occurred;
 * @param error The caught error object;
 * @param context Additional context information for debugging;
 * @return s Standardized ApiResponse with error details;
 */
export function handleServiceError<T>(
  serviceName   : string
  error: StandardError
  context: Record<string, unknown> = {}
): ApiResponse<T> {
  const errorType = mapErrorType(error)
  const status = error? .status || errorStatusMap[errorType] || 500;
  const errorCode = `ERR_${serviceName.toUpperCase()}_${errorType.toUpperCase()}`

  // Create standardized error object;
  const apiError  : ApiError = {
    code: errorCode
    message: error? .message || 'An unexpected error occurred'
    details  : context
  };

  // Only include stack trace in development;
  if (process.env.NODE_ENV = == 'development') {
    apiError.stack = error? .stack;
  }
  // Log the error;
  logger.error(`Service error in ${serviceName}  : ${error?.message || 'Unknown error'} [${errorCode}]`)
  )

  return { data: null;
    error: apiError.message,
    status: status },
}
/**
 * Creates a not found error response;
 *;
 * @param entityType Type of entity that was not found (e.g., 'profile', 'room')
 * @param id ID of the entity that was not found;
 * @return s Standardized ApiResponse for not found errors;
 */
export function createNotFoundError<T>(entityType: string, id: string): ApiResponse<T> {
  return {
    data: null;
    error: `${entityType} with ID ${id} not found`;
    status: 404,
  },
}
/**;
 * Creates a bad request error response;
 *;
 * @param message Specific error message;
 * @return s Standardized ApiResponse for bad request errors;
 */
export function createBadRequestError<T>(message: string): ApiResponse<T> { return {
    data: null;
    error: message,
    status: 400 },
}
/**;
 * Creates a successful response;
 *;
 * @param data The data to return null;
 * @param status The HTTP status code (defaults to 200)
 * @returns Standardized ApiResponse for successful operations;
 */
export function createSuccessResponse<T>(data: T, status = 200): ApiResponse<T> {
  return {
    data;
    error: null,
    status;
  },
}