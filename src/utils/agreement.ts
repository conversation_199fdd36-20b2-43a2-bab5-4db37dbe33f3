export interface Agreement { id: string,
  title: string,
  created_at: string,
  updated_at: string,
  created_by: string,
  household_id: string,
  status: 'draft' | 'active' | 'archived',
  effective_date?: string,
  expiration_date?: string,
  description?: string }
export interface AgreementSection { id: string,
  agreement_id: string,
  version_number: number,
  section_key: string,
  section_title: string,
  content: any; // JSON content;
  order_index: number,
  is_required: boolean,
  created_at: string,
  updated_at: string,
  // Legacy support for older components;
  title?: string }
export interface AgreementSignature { id: string,
  agreement_id: string,
  user_id: string,
  signed_at: string,
  signature_data?: string }
export interface AgreementTemplate { id: string,
  title: string,
  description: string,
  content: string,
  category: string,
  created_at: string,
  updated_at: string }
export type DisputeType =;
  | 'payment';
  | 'maintenance';
  | 'noise';
  | 'cleanliness';
  | 'contract_violation';
  | 'other',
export type DisputeStatus = 'open' | 'in_progress' | 'resolved' | 'closed' | 'escalated';
export type ResolutionStatus = 'proposed' | 'accepted' | 'rejected';
export type ResolutionMethod = 'negotiation' | 'mediation' | 'vote' | 'landlord_decision' | 'legal';

export interface Dispute { id: string,
  agreement_id: string,
  raised_by: string,
  raised_by_name?: string,
  dispute_type: DisputeType,
  title: string,
  description: string,
  status: DisputeStatus,
  section_id?: string,
  resolution_method?: ResolutionMethod,
  resolution_summary?: string,
  resolution_date?: string,
  resolved_by?: string,
  created_at: string,
  updated_at: string }
export interface DisputeMessage { id: string,
  dispute_id: string,
  user_id: string,
  message: string,
  attachments?: any[],
  created_at: string }
export interface DisputeResolution { id: string,
  dispute_id: string,
  proposed_by: string,
  proposal: string,
  section_changes?: any,
  status: ResolutionStatus,
  votes?: DisputeResolutionVote[],
  created_at: string,
  updated_at: string }
export interface DisputeResolutionVote { user_id: string,
  vote: boolean }
export interface DisputeDetails extends Dispute { raised_by_email?: string,
  raised_by_avatar?: string,
  agreement_title?: string,
  message_count?: number,
  resolution_proposal_count?: number,
  last_activity?: string }
export interface ResolutionVote { user_id: string,
  is_upvote: boolean }
export interface Resolution {
  id: string,
  dispute_id: string,
  user_id: string,
  proposal: string,
  status: ResolutionStatus,
  created_at: string,
  updated_at: string,
  proposed_by_name?: string,
  upvotes: number,
  downvotes: number,
  votes: ResolutionVote[]
}