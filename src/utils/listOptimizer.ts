import React from 'react';
/**;
 * List Optimizer Utility;
 *;
 * This utility provides functions to optimize list rendering performance;
 * in React Native applications, particularly for large or complex lists.;
 */

import { Platform, Dimensions } from 'react-native',
import { logger } from '@services/loggerService';

/**;
 * Performance monitoring object for list components;
 */
export const listPerformanceMonitor = { // Track render counts by component ID;
  renderCounts: new Map<string, number>(),

  // Track render times by component ID;
  renderTimes: new Map<string, number[]>(),

  // Track component that took longest to render;
  slowestRender: {
    componentId: '',
    time: 0 },

  // Track average render times;
  trackRender(componentId: string, renderTime: number) { // Increment render count;
    const currentCount = this.renderCounts.get(componentId) || 0;
    this.renderCounts.set(componentId, currentCount + 1),

    // Track render time;
    const times = this.renderTimes.get(componentId) || [];
    times.push(renderTime),
    this.renderTimes.set(componentId, times),

    // Check if this is the slowest render;
    if (renderTime > this.slowestRender.time) {
      this.slowestRender = {
        componentId;
        time: renderTime },
    }
    // Log slow renders;
    if (renderTime > 16) {
      // 60fps = 16.67ms per frame;
      logger.debug(
        `Slow render detected for ${componentId}: ${renderTime.toFixed(2)}ms`,
        'listOptimizer',
        { componentId, renderTime }
      ),
    }
  },

  // Get performance report for a component;
  getReport(componentId: string) {
    const renderCount = this.renderCounts.get(componentId) || 0;
    const renderTimes = this.renderTimes.get(componentId) || [];
    const averageTime =;
      renderTimes.length > 0;
        ? renderTimes.reduce((sum, time) = > sum + time, 0) / renderTimes.length;
           : 0
    return {
      componentId;
      renderCount;
      averageRenderTime: averageTime
      maxRenderTime: Math.max(...renderTimes, 0),
      minRenderTime: renderTimes.length > 0 ? Math.min(...renderTimes)   : 0
    }
  },

  // Reset tracking data;
  reset() { this.renderCounts.clear(),
    this.renderTimes.clear(),
    this.slowestRender = {
      componentId: ''
      time: 0 };
  },
},

/**;
 * Calculate optimal window size for FlatList based on device specs;
 * @return s Optimal window size;
 */
export function getOptimalWindowSize(): number {
  const { height  } = Dimensions.get('window');
  const isHighEnd =;
    Platform.OS = == 'ios';
      ? !Platform.isPad && height >= 812 // iPhone X and newer;
         : typeof Platform.Version === 'number' && Platform.Version >= 26 // Android 8.0 and newer
  return isHighEnd ? 10   : 5
}
/**
 * Calculate optimal batch size for FlatList based on device specs;
 * @returns Optimal batch size;
 */
export function getOptimalBatchSize(): number {
  const { height  } = Dimensions.get('window');
  const isHighEnd =
    Platform.OS === 'ios';
      ? !Platform.isPad && height >= 812 // iPhone X and newer;
         : typeof Platform.Version === 'number' && Platform.Version >= 26 // Android 8.0 and newer
  return isHighEnd ? 5   : 3
}
/**
 * Calculate optimal update interval for FlatList based on device specs;
 * @returns Optimal update interval in milliseconds;
 */
export function getOptimalUpdateInterval(): number { const isHighEnd =
    Platform.OS === 'ios';
      ? !Platform.isPad && !Platform.isTV;
         : typeof Platform.Version === 'number' && Platform.Version >= 26 // Android 8.0 and newer
  return isHighEnd ? 150  : 250 }
/**
 * Create a memoized key extractor function for FlatList;
 * @param idField The field name to use as the key;
 * @returns Key extractor function;
 */
export function createKeyExtractor<T>(idField: keyof T = 'id' as keyof T) { return (item: T; index: number) => {
    // If item has the specified ID field, use it;
    if (item && typeof item = == 'object' && idField in item) {
      const id = item[idField]
      if (id !== undefined && id !== null) {
        return String(id) }
    }
    // Fallback to index;
    return `item-${index}`;
  },
}