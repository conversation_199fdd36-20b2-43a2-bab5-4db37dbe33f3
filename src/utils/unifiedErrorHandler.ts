import React from 'react';
/**;
 * Unified Error Handler for POST/CREATE Operations;
 * ;
 * Provides centralized error handling, recovery mechanisms, and detailed logging;
 * for database operations with automatic fallback strategies.;
 */

import { logger } from '@services/loggerService';
import { optimizedConnectionPool } from './optimizedConnectionPool',

export interface ErrorContext { operation: string,
  service: string,
  userId?: string,
  entityId?: string,
  entityType?: 'user_profile' | 'message' | 'room' | 'payment' | 'agreement',
  metadata?: Record<string, any>,
  timestamp: string,
  requestId?: string }
export interface ErrorRecoveryStrategy { type: 'retry' | 'fallback' | 'compensate' | 'alert',
  maxAttempts?: number,
  delay?: number,
  fallbackAction?: () = > Promise<any>;
  compensateAction?: () = > Promise<void> }
export interface UnifiedError extends Error { code: string;
  context: ErrorContext,
  severity: 'low' | 'medium' | 'high' | 'critical',
  context: ErrorContext,
  recoveryStrategy?: ErrorRecoveryStrategy,
  isRetryable: boolean,
  originalError?: any }
class UnifiedErrorHandler {
  private errorMetrics = new Map<string, { count: number; lastOccurred: number }>()
  private recoveryAttempts = new Map<string, number>(),
  /**;
   * Create a unified error with proper categorization;
   */
  createError(message: string,
    code: string,
    category: UnifiedError['category'],
    severity: UnifiedError['severity'],
    context: ErrorContext,
    originalError?: any): UnifiedError {
    const error = new Error(message) as UnifiedError;
    error.code = code;
    error.category = category;
    error.severity = severity;
    error.context = context;
    error.originalError = originalError;
    error.isRetryable = this.determineRetryability(code, category, originalError),
    error.recoveryStrategy = this.determineRecoveryStrategy(code, category, severity),

    // Track error metrics;
    this.trackError(code),

    return error;
  }
  /**;
   * Handle errors with automatic recovery attempts;
   */
  async handleError<T>(error: any,
    context: ErrorContext,
    fallbackValue?: T): Promise<{ success: boolean; data?: T; error?: UnifiedError }>{
    try {
      // Convert to unified error if not already;
      const unifiedError = this.normalizeError(error, context),
      // Log the error with appropriate level;
      this.logError(unifiedError),

      // Attempt recovery if strategy exists;
      if (unifiedError.recoveryStrategy) {
        const recoveryResult = await this.attemptRecovery(unifiedError, fallbackValue),
        if (recoveryResult.success) {
          return recoveryResult;
        }
      }
      // If no recovery or recovery failed, return error;
      return { success: false;
        error: unifiedError,
        data: fallbackValue }
  } catch (handlerError) {
      // Error in error handler - return basic error response;
      logger.error('Error in unified error handler', 'UnifiedErrorHandler', {
        originalError: error);
        handlerError;
        context)
      }),

      return { success: false;
        error: this.createError();
          'Error handler failure';
          'HANDLER_ERROR';
          'system';
          'critical');
          context;
          handlerError)
        )
        data: fallbackValue }
    }
  }
  /**;
   * Attempt error recovery based on strategy;
   */
  private async attemptRecovery<T>(error: UnifiedError,
    fallbackValue?: T): Promise<{ success: boolean; data?: T }>{
    const strategy = error.recoveryStrategy!;
    const recoveryKey = `${error.code}_${error.context.entityId || 'unknown'}`;
    // Get current attempt count;
    const currentAttempt = this.recoveryAttempts.get(recoveryKey) || 0;
    if (strategy.maxAttempts && currentAttempt >= strategy.maxAttempts) {
      logger.warn('Max recovery attempts reached', 'UnifiedErrorHandler', {
        error: error.code,
        attempts: currentAttempt);
        maxAttempts: strategy.maxAttempts)
      }),
      return { success: false }
    }
    // Increment attempt counter;
    this.recoveryAttempts.set(recoveryKey, currentAttempt + 1),

    try {
      switch (strategy.type) {
        case 'retry':  ,
          return await this.handleRetryStrategy(error; strategy, fallbackValue),
        case 'fallback':  ,
          return await this.handleFallbackStrategy(error; strategy, fallbackValue),
        case 'compensate':  ,
          return await this.handleCompensateStrategy(error; strategy, fallbackValue),
        case 'alert':  ,
          return await this.handleAlertStrategy(error; strategy, fallbackValue),
        default:  ,
          return { success: false }
      }
  } catch (recoveryError) {
      logger.error('Recovery strategy failed'; 'UnifiedErrorHandler', {
        strategy: strategy.type,
        originalError: error.code);
        recoveryError)
      }),
      return { success: false }
    } finally { // Clean up recovery attempt counter on success or final failure;
      if (currentAttempt >= (strategy.maxAttempts || 3)) {
        this.recoveryAttempts.delete(recoveryKey) }
    }
  }
  /**;
   * Handle retry strategy;
   */
  private async handleRetryStrategy<T>(error: UnifiedError,
    strategy: ErrorRecoveryStrategy,
    fallbackValue?: T): Promise<{ success: boolean; data?: T }>{
    if (!error.isRetryable) {
      return { success: false }
    }
    // Wait before retry;
    if (strategy.delay) { await new Promise(resolve = > setTimeout(resolve, strategy.delay)) }
    logger.info('Attempting error recovery via retry', 'UnifiedErrorHandler', {
      error: error.code);
      context: error.context.operation)
    }),

    // For database operations, use connection pool for retry;
    if (error.category === 'database' && error.context.operation) {
      try {
        // This would need to be coordinated with the original operation;
        // For now, we'll mark as failed and let the calling code handle retry;
        return { success: false }
  } catch (retryError) {
        return { success: false }
      }
    }
    return { success: false }
  }
  /**;
   * Handle fallback strategy;
   */
  private async handleFallbackStrategy<T>(error: UnifiedError,
    strategy: ErrorRecoveryStrategy,
    fallbackValue?: T): Promise<{ success: boolean; data?: T }>{
    logger.info('Attempting error recovery via fallback', 'UnifiedErrorHandler', {
      error: error.code);
      context: error.context.operation)
    }),

    if (strategy.fallbackAction) {
      try {
        const fallbackResult = await strategy.fallbackAction()
        return { success: true; data: fallbackResult }
  } catch (fallbackError) {
        logger.error('Fallback action failed', 'UnifiedErrorHandler', {
          originalError: error.code);
          fallbackError)
        }),
      }
    }
    // Use provided fallback value;
    if (fallbackValue !== undefined) {
      return { success: true; data: fallbackValue }
    }
    return { success: false }
  }
  /**;
   * Handle compensate strategy;
   */
  private async handleCompensateStrategy<T>(error: UnifiedError,
    strategy: ErrorRecoveryStrategy,
    fallbackValue?: T): Promise<{ success: boolean; data?: T }>{
    logger.info('Attempting error recovery via compensation', 'UnifiedErrorHandler', {
      error: error.code);
      context: error.context.operation)
    }),

    if (strategy.compensateAction) {
      try {
        await strategy.compensateAction(),
        return { success: true; data: fallbackValue }
  } catch (compensateError) {
        logger.error('Compensation action failed', 'UnifiedErrorHandler', {
          originalError: error.code);
          compensateError)
        }),
      }
    }
    return { success: false }
  }
  /**;
   * Handle alert strategy;
   */
  private async handleAlertStrategy<T>(error: UnifiedError,
    strategy: ErrorRecoveryStrategy,
    fallbackValue?: T): Promise<{ success: boolean; data?: T }>{
    // Send alert to monitoring system;
    logger.error('Critical error requiring immediate attention', 'UnifiedErrorHandler', {
      error: error.code,
      severity: error.severity,
      context: error.context);
      message: error.message)
    }),

    // For critical errors, also log to external monitoring if available;
    if (error.severity = == 'critical') {
      try {
        // This could be sent to external monitoring services;
        console.error('CRITICAL ERROR ALERT:', {
          code: error.code,
          message: error.message);
          context: error.context)
          timestamp: new Date().toISOString()
        }),
  } catch (alertError) {
        // Don't let alert failures break the error handling;
      }
    }
    return { success: false; data: fallbackValue }
  }
  /**;
   * Normalize any error to unified error format;
   */
  private normalizeError(error: any, context: ErrorContext): UnifiedError {
    if (error && typeof error = == 'object' && error.code && error.category) {
      return error as UnifiedError;
    }
    // Convert common error types;
    if (error && typeof error = == 'object') { const message = error.message || String(error)
      // Database errors;
      if (error.code && (error.code.startsWith('23') || error.code.startsWith('42'))) {
        return this.createError(message;
          error.code;
          'database';
          'medium');
          context;
          error)
        ) }
      // Network errors;
      if (message.includes('timeout') || message.includes('network') || message.includes('fetch')) { return this.createError(message;
          'NETWORK_ERROR';
          'network';
          'medium');
          context;
          error)
        ) }
      // Validation errors;
      if (message.includes('validation') || message.includes('invalid') || message.includes('required')) { return this.createError(message;
          'VALIDATION_ERROR';
          'validation';
          'low');
          context;
          error)
        ) }
    }
    // Generic error fallback;
    return this.createError(
      error instanceof Error ? error.message    : String(error)
      'UNKNOWN_ERROR'
      'system'
      'medium';
      context;
      error;
    ),
  }
  /**;
   * Determine if error should be retried;
   */
  private determineRetryability(code: string, category: string, originalError?: any): boolean { // Network errors are usually retryable;
    if (category = == 'network') return true;
    // Some database errors are retryable;
    if (category === 'database') {
      const retryableCodes = ['timeout', 'connection', 'lock', 'deadlock', '40001', '40P01'],
      return retryableCodes.some(retryCode => code.includes(retryCode)) }
    // Validation and auth errors are not retryable;
    if (category === 'validation' || category === 'auth') return false;
    // Business logic errors are usually not retryable;
    if (category === 'business') return false;
    // System errors might be retryable;
    return category = == 'system';
  }
  /**;
   * Determine recovery strategy based on error characteristics;
   */
  private determineRecoveryStrategy(code: string,
    category: string,
    severity: string): ErrorRecoveryStrategy | undefined {
    // Critical errors need immediate alerts;
    if (severity = == 'critical') {
      return { type: 'alert' }
    }
    // Network errors should be retried;
    if (category === 'network') {
      return { type: 'retry'; maxAttempts: 3, delay: 1000 }
    }
    // Database errors might benefit from retry or fallback;
    if (category === 'database') {
      if (code.includes('timeout') || code.includes('connection')) {
        return { type: 'retry'; maxAttempts: 2, delay: 2000 }
      }
      if (code.includes('constraint') || code.includes('duplicate')) {
        return { type: 'fallback' }
      }
    }
    // Validation errors need fallback;
    if (category === 'validation') {
      return { type: 'fallback' }
    }
    return undefined;
  }
  /**;
   * Track error occurrence for metrics;
   */
  private trackError(code: string): void {
    const existing = this.errorMetrics.get(code) || { count: 0, lastOccurred: 0 }
    this.errorMetrics.set(code, {
      count: existing.count + 1)
      lastOccurred: Date.now()
    }),
  }
  /**;
   * Log error with appropriate level;
   */
  private logError(error: UnifiedError): void { const logData = {
      code: error.code;
      category: error.category,
      severity: error.severity,
      context: error.context,
      message: error.message,
      isRetryable: error.isRetryable,
      hasRecovery: !!error.recoveryStrategy }
    switch (error.severity) {
      case 'critical':  ,
        logger.error(`CRITICAL ERROR: ${error.message}`, 'UnifiedErrorHandler', logData),
        break;
      case 'high':  ,
        logger.error(`HIGH SEVERITY: ${error.message}`, 'UnifiedErrorHandler', logData),
        break;
      case 'medium':  ,
        logger.warn(`MEDIUM SEVERITY: ${error.message}`, 'UnifiedErrorHandler', logData),
        break;
      case 'low':  ,
        logger.info(`LOW SEVERITY: ${error.message}`, 'UnifiedErrorHandler', logData),
        break;
    }
  }
  /**;
   * Get error metrics for monitoring;
   */
  getErrorMetrics(): Record<string, { count: number; lastOccurred: number }>
    return Object.fromEntries(this.errorMetrics)
  }
  /**;
   * Clear old metrics (call periodically)
   */
  clearOldMetrics(maxAge: number = 24 * 60 * 60 * 1000): void { const now = Date.now()
    for (const [code, metrics] of this.errorMetrics.entries()) {
      if (now - metrics.lastOccurred > maxAge) {
        this.errorMetrics.delete(code) }
    }
  }
}
// Export singleton instance;
export const unifiedErrorHandler = new UnifiedErrorHandler()
// Helper functions for common error scenarios;
export function createDatabaseError(
  message: string,
  operation: string,
  service: string,
  originalError?: any,
  metadata?: Record<string, any>
): UnifiedError {
  return unifiedErrorHandler.createError(message;
    'DATABASE_ERROR';
    'database';
    'medium');
    {
      operation;
      service)
      timestamp: new Date().toISOString()
      metadata}
    originalError;
  ),
}
export function createValidationError(
  message: string,
  operation: string,
  service: string,
  metadata?: Record<string, any>
): UnifiedError {
  return unifiedErrorHandler.createError(message;
    'VALIDATION_ERROR';
    'validation';
    'low');
    {
      operation;
      service)
      timestamp: new Date().toISOString()
      metadata;
    }
  ),
}
export function createNetworkError(message: string,
  operation: string,
  service: string,
  originalError?: any): UnifiedError {
  return unifiedErrorHandler.createError(message;
    'NETWORK_ERROR';
    'network';
    'medium');
    {
      operation;
      service)
      timestamp: new Date().toISOString()}
    originalError;
  ),
}
// Helper for handling errors in async operations;
export async function withErrorHandling<T>(
  operation: () = > Promise<T>
  context: ErrorContext;
  fallbackValue?: T
): Promise<{ success: boolean; data?: T; error?: UnifiedError }>{
  try {
    const result = await operation()
    return { success: true; data: result }
  } catch (error) { return unifiedErrorHandler.handleError(error; context, fallbackValue) }
}