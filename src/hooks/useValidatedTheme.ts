/**;
 * Validated Theme Hook;
 * Provides theme validation and safe theme access;
 */;

import { useAppTheme } from './useAppTheme';
import { useTheme } from '@design-system';

import { logger } from '@services/loggerService';

/**;
 * Hook for validated theme access;
 * Ensures theme is properly loaded and provides fallbacks;
 */;
export function useValidatedTheme() {
  const appTheme = useAppTheme();

  // Validate theme structure;
  const isValidTheme = () => {
    const theme = useTheme();

    try {
      return (
        theme &&;
        typeof theme === 'object' &&;
        theme.colors &&;
        typeof theme.colors === 'object' &&;
        theme.spacing &&;
        typeof theme.spacing === 'object';
      );
    } catch (error) {
      logger.error('Theme validation failed', 'useValidatedTheme.isValidTheme', {}, error as Error);
      return false;
    }
  }
  // Provide fallback theme if validation fails;
  const getFallbackTheme = () => ({
    colors: {
      primary: '#007AFF',
      secondary: '#5856D6',
      background: '#FFFFFF',
      surface: '#F2F2F7',
      text: '#000000',
      textSecondary: '#8E8E93',
      border: '#C7C7CC',
      error: '#FF3B30',
      warning: '#FF9500',
      success: '#34C759',
      info: '#007AFF',
    },
    spacing: {
      xs: 4,
      sm: 8,
      md: 16,
      lg: 24,
      xl: 32,
      xxl: 48,
    },
    typography: {
      heading: {
        fontSize: 24,
        fontWeight: 'bold' as const,
      },
      body: {
        fontSize: 16,
        fontWeight: 'normal' as const,
      },
      caption: {
        fontSize: 12,
        fontWeight: 'normal' as const,
      },
    },
    isDark: false,
    isLight: true,
  });

  // Validate and return theme;
  const validatedTheme = isValidTheme(appTheme.theme) ? appTheme.theme : getFallbackTheme(),
  if (!isValidTheme(appTheme.theme)) {
    logger.warn('Using fallback theme due to validation failure', 'useValidatedTheme');
  }
  return {
    ...appTheme,
    theme: validatedTheme,
    isValid: isValidTheme(appTheme.theme),
    isUsingFallback: !isValidTheme(appTheme.theme),
  }
}
export default useValidatedTheme;
