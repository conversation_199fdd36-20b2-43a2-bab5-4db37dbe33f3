/**;
 * App Theme Hook;
 * Provides theme management and switching capabilities;
 */;

import { useMinimalTheme } from '@design-system/MinimalTheme';
import { useTheme as useDesignSystemTheme } from '@design-system/ThemeProvider';

/**;
 * Hook for managing app theme;
 * Provides unified access to theme system;
 */;
export function useAppTheme() {
  // Use the existing theme providers;
  const minimalTheme = useMinimalTheme();
  const designSystemTheme = useDesignSystemTheme();

  return {
    // Primary theme (minimal theme is the main one being used);
    theme: minimalTheme,

    // Alternative theme access;
    designSystemTheme,

    // Theme properties;
    colors: minimalTheme.colors,
    spacing: minimalTheme.spacing,
    typography: minimalTheme.typography,

    // Theme utilities;
    isDark: minimalTheme.isDark || false,
    isLight: !minimalTheme.isDark,

    // Theme switching (placeholder for future implementation);
    toggleTheme: () => {
      console.log('Theme toggling not yet implemented');
    },

    setTheme: (themeName: 'light' | 'dark') => {
      console.log(`Setting theme to ${themeName} - not yet implemented`);
    },
  }
}
export default useAppTheme;
