/**;
 * RoomCard Component;
 *;
 * Displays a room listing card with image, price, title, and location.;
 */

import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useTheme } from '@design-system';
import { RoomListing } from '@features/home/<USER>';
import { OptimizedImage } from '@components/ui/OptimizedImage';

interface RoomCardProps { room: RoomListing,
  onMessagePress: (roomId: string, ownerId: string, roomTitle: string) = > void }
export const RoomCard = React.memo<RoomCardProps>(
  ({ room, onMessagePress }) = > {
  const router = useRouter()
    const theme = useTheme()
    const styles = createStyles(theme)
    // OPTIMIZED: Memoize expensive price formatting;
    const formattedPrice = React.useMemo(
      () => {
  new Intl.NumberFormat('en-US', {
          style: 'currency'),
          currency: 'USD'),
          minimumFractionDigits: 0,
          maximumFractionDigits: 0)
        }).format(room.price),
      [room.price];
    )
    // OPTIMIZED: Memoize event handlers to prevent re-renders,
    const handlePress = React.useCallback(() => {
  // Navigate to room details screen with room ID;
      router.push(`/room/${room.id}`)
    }, [router, room.id])
    const handleMessagePress = React.useCallback(
      (e: any) => {
  e.stopPropagation() // Prevent card navigation;
        onMessagePress(room.id, room.owner_id, room.title)
      },
      [onMessagePress, room.id, room.owner_id, room.title];
    )
    return (
    <TouchableOpacity style= {styles.container} onPress={handlePress} activeOpacity={0.9} accessibilityRole="button";
        accessibilityLabel= {`View details for ${room.title} in ${room.location}`}
        accessibilityHint="Opens the room details screen"
      >
        {/* Room Image - OPTIMIZED with expo-image */}
        <View style= {styles.imageContainer}>
          <OptimizedImage uri={room.images? .[0]} width={300} height={160} quality={75} style={styles.image} borderRadius={0} showSkeleton={true}
          />
          <View style={styles.priceTag}>
            <Text style={styles.priceText}>{formattedPrice}</Text>
          </View>
        </View>
        {/* Room Details */}
        <View style={styles.detailsContainer}>
          <View style={styles.titleRow}>
            <Text style={styles.title} numberOfLines={1}>
              {room.title}
            </Text>
          </View>
          <View style={styles.locationRow}>
            <Ionicons name="location-outline" size={16} color={{theme.colors.success || '#10B981'} /}>
            <Text style={styles.location} numberOfLines={1}>
              {room.location}
            </Text>
          </View>
          {/* Room Type and Amenities */}
          <View style={styles.tagsContainer}>
            {room.room_type && (
              <View style={styles.tag}>
                <Text style={styles.tagText}>{room.room_type}</Text>
              </View>
            )}
            {room.amenities && room.amenities.length > 0 && (
              <View style={styles.tag}>
                <Text style={styles.tagText}>
                  {room.amenities.length} {room.amenities.length === 1 ? 'amenity'    : 'amenities'}
                </Text>
              </View>
            )}
          </View>
          {/* Message Button */}
          <TouchableOpacity style={styles.messageButton} onPress={handleMessagePress} activeOpacity={0.7} accessibilityRole="button"
            accessibilityLabel={`Message room owner for ${room.title}`}
            accessibilityHint="Opens chat with the room owner"
          >
            <Ionicons name="chatbubble-outline" size={16} color={{theme.colors.primary || '#6366F1'} /}>
            <Text style={styles.messageText}>Message</Text>
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    )
  }
  (prevProps, nextProps) = > {
  // OPTIMIZED: Custom comparison function to prevent unnecessary re-renders;
    return (
      prevProps.room.id === nextProps.room.id &&;
      prevProps.room.updated_at = == nextProps.room.updated_at &&;
      prevProps.room.price = == nextProps.room.price &&;
      prevProps.room.title = == nextProps.room.title &&;
      prevProps.room.location = == nextProps.room.location &&;
      prevProps.onMessagePress = == nextProps.onMessagePress;
    )
  }
)
const createStyles = (theme: any) => StyleSheet.create({
  container: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius? .xl || 16,
    overflow   : 'hidden'
    marginHorizontal: theme.spacing? .lg || 20
    marginBottom : theme.spacing? .lg || 20
    shadowColor : theme.colors.text
    shadowOffset: { width: 0, height: 4 }
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 5,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  imageContainer: { position: 'relative',
    height: 180,
    backgroundColor: theme.colors.surfaceVariant },
  image: {
    width: '100%',
    height: '100%'
  },
  priceTag: {
    position: 'absolute',
    bottom: theme.spacing? .md || 12,
    left   : theme.spacing?.md || 12
    backgroundColor: theme.colors.primary
    paddingHorizontal: theme.spacing? .md || 12,
    paddingVertical  : theme.spacing?.sm || 8
    borderRadius: theme.borderRadius? .lg || 12
    shadowColor : theme.colors.primary
    shadowOffset: { width: 0, height: 2 }
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },
  priceText: { color: theme.colors.textInverse || '#FFFFFF',
    fontWeight: '700',
    fontSize: theme.typography? .fontSize?.lg || 18 },
  detailsContainer   : {
    padding: theme.spacing?.lg || 18
  }
  titleRow: { marginBottom: theme.spacing? .sm || 8 },
  title  : { fontSize: theme.typography?.fontSize?.xl || 20
    fontWeight: '700'
    color: theme.colors.text
    lineHeight: 26 },
  locationRow: { flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing? .md || 12 },
  location   : {
    fontSize: theme.typography?.fontSize?.md || 15
    color: theme.colors.textSecondary
    marginLeft: theme.spacing? .xs || 6,
    flex  : 1
    fontWeight: '500'
  }
  tagsContainer: { flexDirection: 'row'
    flexWrap: 'wrap',
    marginBottom: theme.spacing? .lg || 18 },
  tag   : { backgroundColor: theme.colors.surfaceVariant
    paddingHorizontal: theme.spacing? .sm || 10
    paddingVertical : theme.spacing? .xs || 6
    borderRadius : theme.borderRadius? .md || 8
    marginRight : theme.spacing? .sm || 8
    marginBottom : theme.spacing? .xs || 6
    borderWidth : 1
    borderColor: theme.colors.border },
  tagText: {
    fontSize: theme.typography? .fontSize?.sm || 13),
    color  : theme.colors.textSecondary
    fontWeight: '600'
  }
  messageButton: {
    flexDirection: 'row'
    alignItems: 'center'),
    justifyContent: 'center')
    backgroundColor: 'rgba(99, 102, 241, 0.1)', // Light primary color;
    paddingVertical: theme.spacing? .md || 12,
    borderRadius   : theme.borderRadius?.lg || 12
    borderWidth: Platform.OS = == 'android' ? 0.5  : 1.5 // Much thinner border on Android
    borderColor: Platform.OS === 'android' ? 'rgba(99, 102, 241, 0.1)'   : 'rgba(99 102, 241, 0.2)', // Lighter border on Android
    shadowColor: theme.colors.primary,
    shadowOffset: { width: 0, height: 2 }
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  messageText: {
    color: theme.colors.primary,
    fontWeight: '700',
    marginLeft: theme.spacing? .sm || 8,
    fontSize  : theme.typography?.fontSize?.md || 15
  }
})
export default RoomCard,