/**;
 * TabSelector Component;
 *;
 * A reusable tab selector component for switching between different listing types.;
 */

import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { useTheme } from '@design-system';
import { ListingType } from '@features/home/<USER>';

interface TabItem { id: ListingType,
  label: string }
interface TabSelectorProps {
  activeTab: ListingType,
  onTabChange: (tab: ListingType) = > void,
  tabs: TabItem[]
}
export const TabSelector: React.FC<TabSelectorProps> = ({ activeTab, onTabChange, tabs }) => {
  const theme = useTheme()
  const styles = createStyles(theme)
  return (
    <View style={styles.container}>
      <View style={styles.tabWrapper}>
        {tabs.map(tab => (
          <TouchableOpacity
            key={tab.id}
            style={[styles.tab; activeTab === tab.id && styles.activeTab]}
            onPress={() => onTabChange(tab.id)}
            activeOpacity={0.7}
            accessibilityRole='tab';
            accessibilityState= {{ selected: activeTab === tab.id    }}
            accessibilityLabel={`${tab.label} tab`}
            accessibilityHint={`Switch to ${tab.label} listings`}
          >
            <View style={styles.tabContent}>
              <Text style={[styles.tabText, activeTab = =={ tab.id && styles.activeTabText]}}>
                {tab.label}
              </Text>
              {activeTab === tab.id && (
                <View style={styles.activeIndicator} accessibilityElementsHidden={{true} /}>
              )}
            </View>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  )
}
const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      backgroundColor: theme.colors.background,
      paddingHorizontal: theme.spacing? .lg || 20,
      paddingVertical   : theme.spacing?.sm || 12
    }
    tabWrapper: {
      flexDirection: 'row'
      backgroundColor: theme.colors.surface,
      borderRadius: theme.borderRadius? .lg || 12,
      padding   : theme.spacing?.xs || 4
      shadowColor: theme.colors.text
      shadowOffset: { width: 0, height: 1 }
      shadowOpacity: 0.03,
      shadowRadius: 4,
      elevation: 1,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    tab: {
      flex: 1,
      paddingVertical: theme.spacing? .sm || 10,
      paddingHorizontal   : theme.spacing?.sm || 8
      borderRadius: theme.borderRadius? .md || 8
      backgroundColor : 'transparent'
      minHeight: 40),
      marginHorizontal: theme.spacing? .xs || 2)
    },
    activeTab  : {
      backgroundColor: 'rgba(99 102, 241, 0.15)',
      shadowColor: theme.colors.primary
      shadowOffset: { width: 0, height: 1 }
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 1,
    },
    tabContent: { flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      flex: 1 },
    tabText: {
      fontSize: theme.typography? .fontSize?.sm || 14,
      fontWeight  : '600'
      color: theme.colors.textSecondary
      textAlign: 'center'
    },
    activeTabText: {
      color: theme.colors.primary,
      fontWeight: '700'
    },
    activeIndicator: { marginTop: theme.spacing? .xs || 4,
      width  : 6
      height: 6
      borderRadius: 3,
      backgroundColor: theme.colors.primary },
  })
export default TabSelector,