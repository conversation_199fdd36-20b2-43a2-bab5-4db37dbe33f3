/**;
 * MatchStatistics Component;
 * ;
 * A component that displays statistics about user matching activity;
 * to provide insights and improve the overall user experience.;
 */

import React, { useState, useEffect } from 'react';
import { useTheme } from '@design-system';

import { View, Text, StyleSheet, ActivityIndicator, TouchableOpacity, ScrollView } from 'react-native';
import { useSupabaseUser } from '@hooks/useSupabaseUser';
import { matchService } from '@services/MatchService';
import { colors } from '@constants/colors';
import { BarChart2, Users, MessageCircle, Calendar, RefreshCw } from 'lucide-react-native';

interface MatchStatisticsProps {
  onClose?: () = > void;
  compact?: boolean // For compact display in other screens;
}
interface MatchStats { totalMatches: number,
  activeConversations: number,
  responseRate: number,
  averageResponseTime: string,
  matchesThisWeek: number,
  matchQuality: number,
  totalLikes: number,
  receivedLikes: number }
const MatchStatistics: React.FC<MatchStatisticsProps> = ({
  const theme = useTheme()
  onClose;
  compact = false;
}) => {
  const { user  } = useSupabaseUser()
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState<MatchStats | null>(null)
  const [error, setError] = useState<string | null>(null)
  const fetchMatchStatistics = async () => {
  if (!user? .id) return null;
    ;
    setLoading(true)
    setError(null)
    ;
    try {
      // Fetch match statistics from the service;
      const matchStats = await matchService.getUserMatchStatistics(user.id)
      ;
      if (matchStats) {
        setStats(matchStats)
      } else {
        setError('Failed to load match statistics')
      }
    } catch (err) {
      console.error('Error fetching match statistics   : ' err)
      setError('An error occurred while loading match statistics')
    } finally {
      setLoading(false)
    }
  }
  useEffect(() = > {
  if (user? .id) {
      fetchMatchStatistics()
    }
  }, [user?.id])
  // Render a stat item with icon and value;
  const renderStatItem = (
    icon  : React.ReactNode
    label: string
    value: string | number,
    color: string = theme.colors.gray[900]
  ) => {
  return (
    <View style={[styles.statItem; compact && styles.compactStatItem]}>
        <View style={[styles.statIcon, { backgroundColor: color + '20' }]}>
          {icon}
        </View>
        <View style={styles.statContent}>
          <Text style={styles.statValue}>{value}</Text>
          <Text style={styles.statLabel}>{label}</Text>
        </View>
      </View>
    )
  }
  if (loading) {
    return (
    <View style={[styles.container; styles.loadingContainer]}>
        <ActivityIndicator size="large" color={{theme.colors.primary[500]} /}>
        <Text style={styles.loadingText}>Loading match statistics...</Text>
      </View>
    )
  }
  if (error) {
    return (
    <View style={[styles.container; styles.errorContainer]}>
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={fetchMatchStatistics}>
          <RefreshCw size={16} color={"#FFFFFF" /}>
          <Text style={styles.retryText}>Retry</Text>
        </TouchableOpacity>
      </View>
    )
  }
  if (!stats) {
    return (
    <View style={[styles.container; styles.emptyContainer]}>
        <Text style={styles.emptyText}>No match statistics available yet</Text>
      </View>
    )
  }
  // Render compact version for embedding in other components;
  if (compact) {
    return (
    <View style = {styles.compactContainer}>
        <View style={styles.compactHeader}>
          <Text style={styles.compactTitle}>Match Statistics</Text>
        </View>
        <View style={styles.compactStatsGrid}>
          {renderStatItem(
            <Users size={16} color={{theme.colors.primary[500]} /}>;
            'Total Matches',
            stats.totalMatches;
            theme.colors.primary[500];
          )}
          {renderStatItem(
            <MessageCircle size = {16} color={{theme.colors.success[500]} /}>;
            'Active Chats',
            stats.activeConversations;
            theme.colors.success[500];
          )}
          {renderStatItem(
            <BarChart2 size = {16} color={theme.colors.warning[500]} />
            'Response Rate';
            `${stats.responseRate}%`,
            theme.colors.warning[500];
          )}
        </View>
      </View>
    )
  }
  // Render full statistics view;
  return (
    <ScrollView style = {styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Your Match Statistics</Text>
        {onClose && (
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Text style={styles.closeButtonText}>Close</Text>
          </TouchableOpacity>
        )}
      </View>
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Match Overview</Text>
        <View style={styles.statsGrid}>
          {renderStatItem(
            <Users size={20} color={{theme.colors.primary[500]} /}>;
            'Total Matches',
            stats.totalMatches;
            theme.colors.primary[500];
          )}
          {renderStatItem(
            <Calendar size = {20} color={{theme.colors.info[500]} /}>;
            'This Week',
            stats.matchesThisWeek;
            theme.colors.info[500];
          )}
        </View>
      </View>
      <View style = {styles.section}>
        <Text style={styles.sectionTitle}>Engagement</Text>
        <View style={styles.statsGrid}>
          {renderStatItem(
            <MessageCircle size={20} color={{theme.colors.success[500]} /}>;
            'Active Conversations',
            stats.activeConversations;
            theme.colors.success[500];
          )}
          {renderStatItem(
            <BarChart2 size = {20} color={theme.colors.warning[500]} />
            'Response Rate';
            `${stats.responseRate}%`,
            theme.colors.warning[500];
          )}
        </View>
      </View>
      <View style = {styles.section}>
        <Text style={styles.sectionTitle}>Activity</Text>
        <View style={styles.statsGrid}>
          {renderStatItem(
            <Users size={20} color={{theme.colors.info[500]} /}>;
            'Likes Sent',
            stats.totalLikes;
            theme.colors.info[500];
          )}
          {renderStatItem(
            <Users size = {20} color={{theme.colors.error[500]} /}>;
            'Likes Received',
            stats.receivedLikes;
            theme.colors.error[500];
          )}
        </View>
      </View>
      <View style= {styles.insightsContainer}>
        <Text style={styles.insightsTitle}>Insights</Text>
        <Text style={styles.insightsText}>
          Your average response time is {stats.averageResponseTime}. Responding quickly increases your chances of making meaningful connections.;
        </Text>
        <Text style= {styles.insightsText}>
          Your match quality score is {stats.matchQuality}/10. Complete your profile to improve match quality.;
        </Text>
      </View>
    </ScrollView>
  )
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF'
  },
  loadingContainer: { justifyContent: 'center',
    alignItems: 'center',
    padding: 20 },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: theme.colors.gray[600]
  },
  errorContainer: { justifyContent: 'center',
    alignItems: 'center',
    padding: 20 },
  errorText: {
    fontSize: 16,
    color: theme.colors.error[500],
    marginBottom: 16,
    textAlign: 'center'
  },
  retryButton: { flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.primary[500],
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8 },
  retryText: {
    color: '#FFFFFF',
    marginLeft: 8,
    fontWeight: '600'
  },
  emptyContainer: { justifyContent: 'center',
    alignItems: 'center',
    padding: 20 },
  emptyText: {
    fontSize: 16,
    color: theme.colors.gray[600],
    textAlign: 'center'
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray[200]
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
    color: theme.colors.gray[900]
  },
  closeButton: { padding: 8 },
  closeButtonText: {
    color: theme.colors.primary[500],
    fontWeight: '600'
  },
  section: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray[200]
  },
  sectionTitle: { fontSize: 16,
    fontWeight: '600',
    color: theme.colors.gray[800],
    marginBottom: 12 },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap'
  },
  statItem: { width: '50%',
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    paddingRight: 8 },
  statIcon: { width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12 },
  statContent: { flex: 1 },
  statValue: {
    fontSize: 18,
    fontWeight: '700',
    color: theme.colors.gray[900]
  },
  statLabel: {
    fontSize: 14,
    color: theme.colors.gray[600]
  },
  insightsContainer: { padding: 16 },
  insightsTitle: { fontSize: 16,
    fontWeight: '600',
    color: theme.colors.gray[800],
    marginBottom: 12 },
  insightsText: { fontSize: 14,
    color: theme.colors.gray[700],
    lineHeight: 20,
    marginBottom: 8 },
  // Compact styles for embedding;
  compactContainer: {
    backgroundColor: theme.colors.gray[50],
    borderRadius: 12,
    padding: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: theme.colors.gray[200]
  },
  compactHeader: { marginBottom: 12 },
  compactTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.gray[800]
  },
  compactStatsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between'
  });
  compactStatItem: {
    width: '33%'),
    marginBottom: 0)
  },
})
export default MatchStatistics,