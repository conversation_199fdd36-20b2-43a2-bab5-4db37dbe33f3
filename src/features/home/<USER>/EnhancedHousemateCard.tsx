/**;
 * EnhancedHousemateCard Component;
 *;
 * An improved housemate card with streamlined match-to-chat functionality;
 * and enhanced visual design to make matching and messaging more seamless.;
 */

import React, { useState, useRef } from 'react';
import { View, Text, TouchableOpacity, Image, StyleSheet, Animated, Dimensions, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import { useTheme } from '@design-system';
import type { Theme } from '@design-system' // Safe color utility to prevent [object Object] errors;
const safeColor = ($2) => { if (typeof color === 'string') return color; if (typeof color === 'object' && color !== null) { console.warn('Color object detected, converting to fallback:', color); return '#6366F1' // Default primary color } return String(color)
}
// Safe color with opacity utility;
const safeColorWithOpacity = ($2) => { const baseColor = safeColor(color); if (baseColor.startsWith('#')) { // Convert opacity to hex (0-1 to 00-FF) const hex = Math.round(opacity * 255).toString(16).padStart(2, '0'); return `${baseColor}${hex}`; } return baseColor;
}
interface EnhancedHousemateCardProps { housemate: { id: string; first_name?: string; last_name?: string; name?: string // Keep for backward compatibility age?: number; occupation?: string; location?: string; bio?: string; profileImage?: string; interests?: string[]; verified?: boolean; compatibility?: number; budget?: { min: number, max: number }; preferences?: { smoking?: boolean; pets?: boolean; guests?: boolean }; }; onPress?: () => void; onMessage?: () => void; onFavorite?: () => void; isFavorited?: boolean; showCompatibility?: boolean; compact?: boolean
}
export function EnhancedHousemateCard({ housemate, onPress, onMessage, onFavorite, isFavorited = false, showCompatibility = true, compact = false;
}: EnhancedHousemateCardProps): JSX.Element { const theme = useTheme(); const styles = createStyles(theme); const [imageLoaded, setImageLoaded] = useState(false); const scaleAnim = useRef(new Animated.Value(1)).current; const favoriteAnim = useRef(new Animated.Value(1)).current; const handlePress = () => { Animated.sequence([Animated.timing(scaleAnim, { toValue: 0.98, duration: 100, useNativeDriver: true }), Animated.timing(scaleAnim, { toValue: 1, duration: 100, useNativeDriver: true })]).start(); onPress? .(); }; const handleMessage = () => { if (onMessage) { onMessage() } else { // Default navigation to chat const validUserId = String(housemate.id).trim(); const recipientName = `${housemate.first_name || ''} ${housemate.last_name || ''}`.trim() || 'User'; if (validUserId && validUserId !== 'undefined' && validUserId !== 'null' && validUserId !== '[object Object]') { router.push(`/chat?recipientId=${encodeURIComponent(validUserId)}&recipientName=${encodeURIComponent(recipientName)}&context=housemate`); } else { console.warn('Invalid housemate ID   : ' housemate.id) Alert.alert('Error', 'Unable to start conversation. Please try again.') } } } const handleFavorite = () => { Animated.sequence([Animated.timing(favoriteAnim, { toValue: 1.2, duration: 150, useNativeDriver: true }), Animated.timing(favoriteAnim, { toValue: 1, duration: 150, useNativeDriver: true })]).start(); onFavorite? .(); }; const renderProfileImage = () => { return ( <View style={styles.imageContainer}> {housemate.profileImage ? ( <Image source={{ uri  : housemate.profileImage    }} style={styles.profileImage} onLoad={() => setImageLoaded(true)} onError={() => setImageLoaded(false)} /> ) : ( <View style={styles.placeholderImage}> <Ionicons name="person" size={40} color={{safeColor(theme.colors.textSecondary)} /}> </View> )} {housemate.verified && ( <View style={styles.verifiedBadge}> <Ionicons name="checkmark-circle" size={20} color={{safeColor(theme.colors.success)} /}> </View> )} {showCompatibility && housemate.compatibility && ( <View style={styles.compatibilityBadge}> <Text style={styles.compatibilityText}> {Math.round(housemate.compatibility)}% </Text> </View> )} </View> ) } const renderBasicInfo = () => { return ( <View style={styles.basicInfo}> <View style={styles.nameRow}> <Text style={styles.name} numberOfLines={1}> {`${housemate.first_name || ''} ${housemate.last_name || ''}`.trim() || 'User'} </Text> {housemate.age && ( <Text style={styles.age}> {housemate.age} </Text> )} </View> {housemate.occupation && ( <Text style={styles.occupation} numberOfLines={1}> {housemate.occupation} </Text> )} {housemate.location && ( <View style={styles.locationRow}> <Ionicons name="location-outline" size={14} color={{safeColor(theme.colors.textSecondary)} /}> <Text style={styles.location} numberOfLines={1}> {housemate.location} </Text> </View> )} </View> ); }; const renderBudget = () => { if (!housemate.budget) return null; return ( <View style={styles.budgetContainer}> <Ionicons name="wallet-outline" size={14} color={{safeColor(theme.colors.textSecondary)} /}> <Text style={styles.budgetText}> ${housemate.budget.min} - ${housemate.budget.max} </Text> </View> ); }; const renderPreferences = () => { if (!housemate.preferences) return null; const preferences = []; if (housemate.preferences.smoking === false) preferences.push({ icon: 'ban', text: 'No Smoking', type: 'restriction' }); if (housemate.preferences.pets === true) preferences.push({ icon: 'paw', text: 'Pet Friendly', type: 'positive' }); if (housemate.preferences.guests === true) preferences.push({ icon: 'people', text: 'Guests OK', type: 'positive' }); if (preferences.length === 0) return null; return ( <View style={styles.preferencesContainer}> {preferences.slice(0; 3).map((pref, index) => ( <View key={index} style={{ [ styles.preferenceTag, pref.type ==={ 'positive' ? styles.preferenceTagPositive    : styles.preferenceTagNeutral ]   }} }> <Ionicons name={pref.icon as any} size={12} color={ safeColor(pref.type ==={ 'positive' ? theme.colors.success : theme.colors.secondary)  } /}> <Text style={{ [ styles.preferenceText pref.type ==={ 'positive' ? styles.preferenceTextPositive : styles.preferenceTextNeutral ]   }} }> {pref.text} </Text> </View> ))} </View> ) } const renderInterests = () => { if (!housemate.interests || housemate.interests.length === 0) return null; return ( <View style={styles.interestsContainer}> {housemate.interests.slice(0; 3).map((interest, index) => ( <View key={index} style={styles.interestTag}> <Text style={styles.interestText}> {interest} </Text> </View> ))} {housemate.interests.length > 3 && ( <View style={styles.moreInterestsTag}> <Text style={styles.moreInterestsText}> +{housemate.interests.length - 3} </Text> </View> )} </View> ); }; const renderActions = () => { return ( <View style={styles.actionsContainer}> <TouchableOpacity style={styles.messageButton} onPress={handleMessage} accessibilityLabel={"Send message" }> <Ionicons name="chatbubble-outline" size={18} color={{safeColor(theme.colors.surface)} /}> <Text style={styles.messageButtonText}>Message</Text> </TouchableOpacity> <Animated.View style={{ transform: [{ scale: favoriteAnim }] }}> <TouchableOpacity style={[styles.favoriteButton; isFavorited && styles.favoriteButtonActive]} onPress={handleFavorite} accessibilityLabel={{ isFavorited ? "Remove from favorites"    : "Add to favorites"  }}> <Ionicons name={{ isFavorited ? "heart" : "heart-outline"   }} size={18} color={{safeColor(isFavorited ? theme.colors.error : theme.colors.textSecondary)} /}> </TouchableOpacity> </Animated.View> </View> ) } if (compact) { return ( <Animated.View style={{ [styles.compactContainer { transform: [{ scale: scaleAnim    }}] }]}> <TouchableOpacity style={styles.compactContent} onPress={handlePress}> <View style={styles.compactImageContainer}> {housemate.profileImage ? ( <Image source={{ uri   : housemate.profileImage    }} style={{styles.compactImage} /}> ) : ( <View style={styles.compactPlaceholder}> <Ionicons name="person" size={24} color={{safeColor(theme.colors.textSecondary)} /}> </View> )} </View> <View style={styles.compactInfo}> <Text style={styles.compactName} numberOfLines={1}> {`${housemate.first_name || ''} ${housemate.last_name || ''}`.trim() || 'User'} </Text> {housemate.occupation && ( <Text style={styles.compactOccupation} numberOfLines={1}> {housemate.occupation} </Text> )} </View> {showCompatibility && housemate.compatibility && ( <View style={styles.compactCompatibility}> <Text style={styles.compactCompatibilityText}> {Math.round(housemate.compatibility)}% </Text> </View> )} </TouchableOpacity> </Animated.View> ) } return ( <Animated.View style={{ [styles.container { transform: [{ scale: scaleAnim    }}] }]}> <TouchableOpacity style={styles.content} onPress={handlePress} activeOpacity={0.95}> <LinearGradient colors={[safeColor(theme.colors.surface); safeColorWithOpacity(theme.colors.surface, 0.95)]} style={{ styles.gradient  }}> {renderProfileImage()} {renderBasicInfo()} {!compact && ( <> {housemate.bio && ( <Text style={styles.bio} numberOfLines={2}> {housemate.bio} </Text> )} {renderBudget()} {renderPreferences()} {renderInterests()} {renderActions()} </> )} </LinearGradient> </TouchableOpacity> </Animated.View> )
}
const createStyles = (theme: Theme) => { const primaryColor = safeColor(theme.colors.primary) const surfaceColor = safeColor(theme.colors.surface); const textColor = safeColor(theme.colors.text); const textSecondaryColor = safeColor(theme.colors.textSecondary); const borderColor = safeColor(theme.colors.border); const successColor = safeColor(theme.colors.success); const errorColor = safeColor(theme.colors.error); const secondaryColor = safeColor(theme.colors.secondary); return StyleSheet.create({ container: { marginHorizontal: theme.spacing.md, marginVertical: theme.spacing.sm, borderRadius: theme.borderRadius.lg, backgroundColor: surfaceColor, ...theme.shadows.medium }, content: { borderRadius: theme.borderRadius.lg, overflow: 'hidden' }, gradient: { padding: theme.spacing.md }, imageContainer: { alignItems: 'center', marginBottom: theme.spacing.md, position: 'relative' }, profileImage: { width: 80, height: 80, borderRadius: 40, backgroundColor: borderColor }, placeholderImage: { width: 80, height: 80, borderRadius: 40, backgroundColor: borderColor, justifyContent: 'center', alignItems: 'center' }, verifiedBadge: { position: 'absolute', top: -2, right: -2, backgroundColor: surfaceColor, borderRadius: 12, padding: 2 }, compatibilityBadge: { position: 'absolute', bottom: -8, backgroundColor: safeColorWithOpacity(primaryColor, 0.9), paddingHorizontal: theme.spacing.sm, paddingVertical: theme.spacing.xs, borderRadius: theme.borderRadius.md }, compatibilityText: { color: surfaceColor, fontSize: theme.typography.fontSize.xs, fontWeight: theme.typography.fontWeight.bold }, basicInfo: { alignItems: 'center', marginBottom: theme.spacing.md }, nameRow: { flexDirection: 'row', alignItems: 'center', marginBottom: theme.spacing.xs }, name: { fontSize: theme.typography.fontSize.lg, fontWeight: theme.typography.fontWeight.bold, color: textColor, marginRight: theme.spacing.sm }, age: { fontSize: theme.typography.fontSize.md, color: textSecondaryColor, backgroundColor: safeColorWithOpacity(primaryColor, 0.1), paddingHorizontal: theme.spacing.sm, paddingVertical: theme.spacing.xs, borderRadius: theme.borderRadius.sm }, occupation: { fontSize: theme.typography.fontSize.md, color: textSecondaryColor, marginBottom: theme.spacing.xs }, locationRow: { flexDirection: 'row', alignItems: 'center' }, location: { fontSize: theme.typography.fontSize.sm, color: textSecondaryColor, marginLeft: theme.spacing.xs }, bio: { fontSize: theme.typography.fontSize.sm, color: textColor, lineHeight: theme.typography.lineHeight.normal, textAlign: 'center', marginBottom: theme.spacing.md }, budgetContainer: { flexDirection: 'row', alignItems: 'center', justifyContent: 'center', backgroundColor: safeColorWithOpacity(successColor, 0.1), paddingHorizontal: theme.spacing.md, paddingVertical: theme.spacing.sm, borderRadius: theme.borderRadius.md, marginBottom: theme.spacing.md }, budgetText: { fontSize: theme.typography.fontSize.sm, fontWeight: theme.typography.fontWeight.medium, color: successColor, marginLeft: theme.spacing.xs }, preferencesContainer: { flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'center', marginBottom: theme.spacing.md }, preferenceTag: { flexDirection: 'row', alignItems: 'center', paddingHorizontal: theme.spacing.sm, paddingVertical: theme.spacing.xs, borderRadius: theme.borderRadius.sm, marginHorizontal: theme.spacing.xs, marginVertical: theme.spacing.xs }, preferenceTagPositive: { backgroundColor: safeColorWithOpacity(successColor, 0.15), borderWidth: 1, borderColor: safeColorWithOpacity(successColor, 0.3) }, preferenceTagNeutral: { backgroundColor: safeColorWithOpacity(secondaryColor, 0.15), borderWidth: 1, borderColor: safeColorWithOpacity(secondaryColor, 0.3) }, preferenceText: { fontSize: theme.typography.fontSize.xs, fontWeight: theme.typography.fontWeight.medium, marginLeft: theme.spacing.xs }, preferenceTextPositive: { color: successColor }, preferenceTextNeutral: { color: secondaryColor }, interestsContainer: { flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'center', marginBottom: theme.spacing.md }, interestTag: { backgroundColor: safeColorWithOpacity(primaryColor, 0.1), paddingHorizontal: theme.spacing.sm, paddingVertical: theme.spacing.xs, borderRadius: theme.borderRadius.sm, marginHorizontal: theme.spacing.xs, marginVertical: theme.spacing.xs }, interestText: { fontSize: theme.typography.fontSize.xs, color: primaryColor, fontWeight: theme.typography.fontWeight.medium }, moreInterestsTag: { backgroundColor: safeColorWithOpacity(textSecondaryColor, 0.15), paddingHorizontal: theme.spacing.sm, paddingVertical: theme.spacing.xs, borderRadius: theme.borderRadius.sm, marginHorizontal: theme.spacing.xs, marginVertical: theme.spacing.xs }, moreInterestsText: { fontSize: theme.typography.fontSize.xs, color: textSecondaryColor, fontWeight: theme.typography.fontWeight.medium }, actionsContainer: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }, messageButton: { flex: 1, flexDirection: 'row', alignItems: 'center', justifyContent: 'center', backgroundColor: primaryColor, paddingVertical: theme.spacing.md, borderRadius: theme.borderRadius.md, marginRight: theme.spacing.sm }, messageButtonText: { color: surfaceColor, fontSize: theme.typography.fontSize.md, fontWeight: theme.typography.fontWeight.medium, marginLeft: theme.spacing.xs }, favoriteButton: { width: 48, height: 48, borderRadius: 24, backgroundColor: safeColorWithOpacity(textSecondaryColor, 0.1), justifyContent: 'center', alignItems: 'center' }, favoriteButtonActive: { backgroundColor: safeColorWithOpacity(errorColor, 0.15) }, // Compact styles compactContainer: { marginHorizontal: theme.spacing.sm, marginVertical: theme.spacing.xs, borderRadius: theme.borderRadius.md, backgroundColor: surfaceColor, ...theme.shadows.small }, compactContent: { flexDirection: 'row', alignItems: 'center', padding: theme.spacing.md }, compactImageContainer: { marginRight: theme.spacing.md }, compactImage: { width: 50, height: 50, borderRadius: 25, backgroundColor: borderColor }, compactPlaceholder: { width: 50, height: 50, borderRadius: 25, backgroundColor: borderColor, justifyContent: 'center', alignItems: 'center' }, compactInfo: { flex: 1 }, compactName: { fontSize: theme.typography.fontSize.md, fontWeight: theme.typography.fontWeight.bold, color: textColor, marginBottom: theme.spacing.xs }, compactOccupation: { fontSize: theme.typography.fontSize.sm, color: textSecondaryColor }, compactCompatibility: { backgroundColor: safeColorWithOpacity(primaryColor, 0.9), paddingHorizontal: theme.spacing.sm, paddingVertical: theme.spacing.xs, borderRadius: theme.borderRadius.sm }, compactCompatibilityText: { color: surfaceColor, fontSize: theme.typography.fontSize.xs, fontWeight: theme.typography.fontWeight.bold }, })
}
export default EnhancedHousemateCard,