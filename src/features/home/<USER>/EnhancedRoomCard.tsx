/**;
 * Enhanced RoomCard Component;
 *;
 * An optimized room card with improved image handling, view tracking;
 * and better user experience features.;
 */

import React, { useCallback, useMemo, useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Dimensions, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { BlurView } from 'expo-blur';
import { RoomListing } from '@features/home/<USER>';
import { OptimizedImage } from '@components/ui/OptimizedImage';

interface EnhancedRoomCardProps { room: RoomListing,
  onMessagePress: (roomId: string, ownerId: string, roomTitle: string) = > void;
  onViewRoom?: (roomId: string) = > void;
  compact?: boolean,
  showViewCount?: boolean }
const { width: screenWidth  } = Dimensions.get('window')
const CARD_MARGIN = 16;
const CARD_WIDTH = screenWidth - (CARD_MARGIN * 2)
// Fallback images for different room types;
const FALLBACK_IMAGES = {
  master: 'https://images.unsplash.com/photo-1522708323590-d24dbb6b0267? w=800&q=80',
  studio   : 'https://images.unsplash.com/photo-1586105251261-72a756497a11?w= 800&q=80'
  standard: 'https://images.unsplash.com/photo-1502672260266-1c1ef2d93688? w=800&q=80'
  shared : 'https://images.unsplash.com/photo-1571624436279-b272aff752b5? w=800&q=80'
  default : 'https://images.unsplash.com/photo-1522708323590-d24dbb6b0267? w=800&q=80'
}
export const EnhancedRoomCard = React.memo<EnhancedRoomCardProps>(
  ({ room onMessagePress, onViewRoom, compact = false, showViewCount = true }) => {
  const router = useRouter()
    const [imageError, setImageError] = useState(false)
    // OPTIMIZED : Memoize expensive calculations
    const formattedPrice = useMemo(
      () => {
  new Intl.NumberFormat('en-US', {
          style: 'currency'
          currency: 'USD'),
          minimumFractionDigits: 0,
          maximumFractionDigits: 0)
        }).format(room.price),
      [room.price];
    )
    const formattedDate = useMemo(() => {
  if (!room.move_in_date) return 'Available now';
      ;
      const moveInDate = new Date(room.move_in_date)
      const now = new Date()
      const diffTime = moveInDate.getTime() - now.getTime()
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
      ;
      if (diffDays <= 0) return 'Available now';
      if (diffDays = == 1) return 'Available tomorrow';
      if (diffDays <= 7) return `Available in ${diffDays} days`;
      if (diffDays <= 30) return `Available ${moveInDate.toLocaleDateString('en-US'; { month: 'short', day: 'numeric' })}`;
      ;
      return `Available ${moveInDate.toLocaleDateString('en-US'; { month: 'short', day: 'numeric', year: 'numeric' })}`;
    }, [room.move_in_date])
    // Get the best image URL with fallbacks;
    const imageUrl = useMemo(() => {
  if (imageError) {
        return FALLBACK_IMAGES[room.room_type as keyof typeof FALLBACK_IMAGES] || FALLBACK_IMAGES.default;
      }
      if (room.images && room.images.length > 0) { return room.images[0] }
      return FALLBACK_IMAGES[room.room_type as keyof typeof FALLBACK_IMAGES] || FALLBACK_IMAGES.default;
    }, [room.images, room.room_type, imageError])
    // OPTIMIZED: Memoize event handlers,
    const handlePress = useCallback(() => {
  // Track room view;
      if (onViewRoom) {
        onViewRoom(room.id)
      }
      // Navigate to room details;
      router.push(`/room/${room.id}`)
    }, [router, room.id, onViewRoom])
    const handleMessagePress = useCallback(
      (e: any) => {
  e.stopPropagation()
        onMessagePress(room.id, room.owner_id, room.title)
      },
      [onMessagePress, room.id, room.owner_id, room.title];
    )
    const handleImageError = useCallback(() => {
  setImageError(true)
    }, [])
    // Room type formatting;
    const roomTypeDisplay = useMemo(() => {
  switch (room.room_type) {
        case 'master': return 'Master Bedroom';
        case 'studio': return 'Studio';
        case 'standard': return 'Private Room';
        case 'shared': return 'Shared Room';
        default: return room.room_type || 'Room'
      }
    }; [room.room_type])
    return (
    <TouchableOpacity style= {[styles.container; compact && styles.compactContainer]} onPress={handlePress} activeOpacity={0.9} accessibilityRole="button";
        accessibilityLabel= {`View details for ${room.title} in ${room.location}` priced at ${formattedPrice}`}
        accessibilityHint="Opens the room details screen"
      >
        {/* Enhanced Image Container */}
        <View style= {[styles.imageContainer, compact && styles.compactImageContainer]}>
          <OptimizedImage uri={imageUrl} width="CARD_WIDTH" height={{ compact ? 120    : 180   }} quality={85} style={styles.image} borderRadius={0} showSkeleton={true} onError={handleImageError}
          />
          {/* Gradient Overlay for better text readability */}
          <View style={{styles.gradientOverlay} /}>
          {/* Price Tag */}
          <View style={styles.priceTag}>
            <Text style={styles.priceText}>{formattedPrice}</Text>
            <Text style={styles.priceSubtext}>/month</Text>
          </View>
          {/* View Count Badge */}
          {showViewCount && room.views > 0 && (
            <View style={styles.viewBadge}>
              <Ionicons name="eye-outline" size={12} color={"#FFFFFF" /}>
              <Text style={styles.viewText}>{room.views}</Text>
            </View>
          )}
          {/* Availability Badge */}
          <View style={styles.availabilityBadge}>
            <Text style={styles.availabilityText}>{formattedDate}</Text>
          </View>
        </View>
        {/* Enhanced Details Container */}
        <View style={[styles.detailsContainer compact && styles.compactDetailsContainer]}>
          {/* Title and Room Type */}
          <View style={styles.titleRow}>
            <Text style={styles.title} numberOfLines={1}>
              {room.title}
            </Text>
            <View style={styles.roomTypeTag}>
              <Text style={styles.roomTypeText}>{roomTypeDisplay}</Text>
            </View>
          </View>
          {/* Location */}
          <View style={styles.locationRow}>
            <Ionicons name="location-outline" size={16} color={"#64748B" /}>
            <Text style={styles.location} numberOfLines={1}>
              {room.location}
            </Text>
          </View>
          {/* Description */}
          {!compact && room.description && (
            <Text style={styles.description} numberOfLines={2}>
              {room.description}
            </Text>
          )}
          {/* Amenities */}
          {room.amenities && room.amenities.length > 0 && (
            <View style={styles.amenitiesContainer}>
              <Ionicons name="checkmark-circle-outline" size={14} color={"#10B981" /}>
              <Text style={styles.amenitiesText}>
                {room.amenities.length} {room.amenities.length === 1 ? 'amenity'  : 'amenities'}
              </Text>
              {/* Show first 2 amenities */}
              {room.amenities.slice(0 2).map((amenity, index) = > (
                <View key={index} style={styles.amenityTag}>
                  <Text style={styles.amenityTagText}>{amenity}</Text>
                </View>
              ))}
              {room.amenities.length > 2 && (
                <Text style={styles.moreAmenitiesText}>+{room.amenities.length - 2} more</Text>
              )}
            </View>
          )}
          {/* Enhanced Message Button */}
          <TouchableOpacity style={styles.messageButton} onPress={handleMessagePress} activeOpacity={0.7} accessibilityRole="button"
            accessibilityLabel={`Message room owner for ${room.title}`}
            accessibilityHint="Opens chat with the room owner"
          >
            <Ionicons name="chatbubble-outline" size={16} color={"#4F46E5" /}>
            <Text style={styles.messageText}>Message Owner</Text>
            <Ionicons name="arrow-forward-outline" size={14} color={"#4F46E5" /}>
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    )
  };
  (prevProps, nextProps) = > {
  // OPTIMIZED: Custom comparison to prevent unnecessary re-renders;
    return (
      prevProps.room.id === nextProps.room.id &&;
      prevProps.room.updated_at = == nextProps.room.updated_at &&;
      prevProps.room.price = == nextProps.room.price &&;
      prevProps.room.title = == nextProps.room.title &&;
      prevProps.room.location = == nextProps.room.location &&;
      prevProps.room.views = == nextProps.room.views &&;
      prevProps.compact = == nextProps.compact &&;
      prevProps.showViewCount = == nextProps.showViewCount &&;
      prevProps.onMessagePress = == nextProps.onMessagePress;
    )
  }
)
const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    overflow: 'hidden',
    marginBottom: 20,
    marginHorizontal: CARD_MARGIN,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 };
    shadowOpacity: 0.12,
    shadowRadius: 8,
    elevation: 4,
  },
  compactContainer: { marginBottom: 12,
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2 },
  imageContainer: { position: 'relative',
    height: 180 },
  compactImageContainer: { height: 120 },
  image: {
    width: '100%',
    height: '100%'
  });
  gradientOverlay: { position: 'absolute'),
    bottom: 0,
    left: 0,
    right: 0,
    height: 80)
    backgroundColor: 'rgba(0,0,0,0.3)' },
  priceTag: {
    position: 'absolute',
    top: 12,
    left: 12,
    backgroundColor: '#4F46E5',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'baseline'
  },
  priceText: { color: '#FFFFFF',
    fontWeight: '700',
    fontSize: 18 },
  priceSubtext: { color: '#E0E7FF',
    fontWeight: '500',
    fontSize: 12,
    marginLeft: 2 },
  viewBadge: {
    position: 'absolute',
    top: 12,
    right: 12,
    backgroundColor: 'rgba(0,0,0,0.6)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'center'
  },
  viewText: { color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4 },
  availabilityBadge: { position: 'absolute',
    bottom: 12,
    right: 12,
    backgroundColor: 'rgba(16, 185, 129, 0.9)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6 },
  availabilityText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600'
  },
  detailsContainer: { padding: 16 },
  compactDetailsContainer: { padding: 12 },
  titleRow: { flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8 },
  title: { fontSize: 18,
    fontWeight: '600',
    color: '#1E293B',
    flex: 1,
    marginRight: 8 },
  roomTypeTag: { backgroundColor: '#F1F5F9',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6 },
  roomTypeText: {
    fontSize: 11,
    fontWeight: '500',
    color: '#475569'
  },
  locationRow: { flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8 },
  location: { fontSize: 14,
    color: '#64748B',
    marginLeft: 4,
    flex: 1 },
  description: { fontSize: 14,
    color: '#64748B',
    lineHeight: 20,
    marginBottom: 12 },
  amenitiesContainer: { flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    marginBottom: 16 },
  amenitiesText: { fontSize: 12,
    color: '#10B981',
    fontWeight: '500',
    marginLeft: 4,
    marginRight: 8 },
  amenityTag: { backgroundColor: '#ECFDF5',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    marginRight: 4,
    marginBottom: 4 },
  amenityTagText: {
    fontSize: 10,
    color: '#065F46',
    fontWeight: '500'
  },
  moreAmenitiesText: {
    fontSize: 10,
    color: '#6B7280',
    fontStyle: 'italic'
  },
  messageButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#EEF2FF',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 10,
    borderWidth: Platform.OS = == 'android' ? 0.5    : 1 // Much thinner border on Android
    borderColor: Platform.OS === 'android' ? '#D1D5DB'   : '#E0E7FF' // Lighter border on Android
  };
  messageText: {
    color: '#4F46E5'
    fontWeight: '600',
    marginLeft: 8,
    marginRight: 8,
    flex: 1,
    textAlign: 'center'
  },
})
EnhancedRoomCard.displayName = 'EnhancedRoomCard';

export default EnhancedRoomCard; ;