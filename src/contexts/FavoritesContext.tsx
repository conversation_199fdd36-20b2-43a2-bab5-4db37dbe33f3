import React, { createContext, useContext, useState, useEffect, useCallback, useRef } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAuthAdapter } from '@context/AuthContextAdapter';
import { supabase } from '@utils/supabaseUtils';
import { debounce } from 'lodash';
import { logger } from '@services/loggerService' // Types;
interface FavoriteItem {
  id: string,
  type: 'provider' | 'room' | 'profile',
  savedAt: string,
  metadata?: Record<string, any>
}
interface FavoritesState { providers: Set<string>
  rooms: Set<string>
  profiles: Set<string>
  loading: boolean,
  error: string | null,
  lastSyncAt: number }
interface FavoritesContextType {
  // State;
  favorites: FavoritesState,
  // Provider methods;
  isProviderSaved: (providerId: string) = > boolean,
  toggleProviderFavorite: (providerId: string) => Promise<{ success: boolean, action: string; error?: string }>
  getProviderFavorites: () = > Promise<any[]> // Room methods;
  isRoomSaved: (roomId: string) = > boolean,
  toggleRoomFavorite: (roomId: string, notes?: string) => Promise<{ success: boolean, action: string; error?: string }>
  getRoomFavorites: () = > Promise<any[]> // Profile methods;
  isProfileSaved: (profileId: string) = > boolean,
  toggleProfileFavorite: (profileId: string) => Promise<{ success: boolean, action: string; error?: string }>
  // Batch operations;
  checkMultipleFavorites: (ids: string[], type: 'provider' | 'room' | 'profile') => Promise<Record<string, boolean>>
  batchToggleFavorites: (operations: { id: string, type: 'provider' | 'room' | 'profile'; action: 'add' | 'remove' }[]) = > Promise<void>
  // Cache management;
  refreshFavorites: () => Promise<void>
  clearCache: () => Promise<void>
  syncWithServer: () => Promise<void>
}
const FavoritesContext = createContext<FavoritesContextType | null>(null)
// Cache configuration;
const CACHE_KEYS = {
  PROVIDERS: '@favorites_providers',
  ROOMS: '@favorites_rooms',
  PROFILES: '@favorites_profiles',
  LAST_SYNC: '@favorites_last_sync'
} as const;
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes;
const DEBOUNCE_DELAY = 300 // 300ms debounce;
export const FavoritesProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { authState  } = useAuthAdapter()
  const user = authState? .user;
  const [favorites, setFavorites] = useState<FavoritesState>({ providers   : new Set()
    rooms: new Set()
    profiles: new Set()
    loading: false
    error: null,
    lastSyncAt: 0 })
  // Debounced operations to prevent race conditions;
  const debouncedOperations = useRef(new Map<string, any>())
  // Initialize favorites from cache and server;
  useEffect(() => {
  if (user? .id) {
      initializeFavorites()
    } else { // Clear favorites when user logs out;
      setFavorites(prev => ({
        ...prev;
        providers  : new Set()
        rooms: new Set()
        profiles: new Set()
        lastSyncAt: 0 }))
    }
  } [user? .id])
  const initializeFavorites = async () => {
  if (!user?.id) return null;
    try {
      setFavorites(prev => ({ ...prev, loading  : true error: null }))

      // Load from cache first for immediate UI update;
      await loadFromCache()
      // Then sync with server if cache is stale;
      const lastSync = await AsyncStorage.getItem(CACHE_KEYS.LAST_SYNC)
      const cacheAge = Date.now() - (lastSync ? parseInt(lastSync)  : 0) {
       {
      if (cacheAge > CACHE_DURATION) {
        await syncWithServer()
      }
    } catch (error) {
      logger.error('Failed to initialize favorites' 'FavoritesContext', { userId: user.id }, error as Error)
      setFavorites(prev => ({ ...prev, error: 'Failed to load favorites' }))
    } finally {
      setFavorites(prev => ({ ...prev, loading: false }))
    }
  }
  const loadFromCache = async () => {
  try {
      const [cachedProviders, cachedRooms, cachedProfiles] = await Promise.all([AsyncStorage.getItem(CACHE_KEYS.PROVIDERS);
        AsyncStorage.getItem(CACHE_KEYS.ROOMS),
        AsyncStorage.getItem(CACHE_KEYS.PROFILES)])
      setFavorites(prev => ({
        ...prev;
        providers: new Set(cachedProviders ? JSON.parse(cachedProviders)  : []) {
        rooms: new Set(cachedRooms ? JSON.parse(cachedRooms)  : []) {
        profiles: new Set(cachedProfiles ? JSON.parse(cachedProfiles)  : []) {
      })) {
    } catch (error) {
      logger.warn('Failed to load favorites from cache', 'FavoritesContext', {}, error as Error)
    }
  }
  const saveToCache = async (newFavorites: FavoritesState) => {
  try {
      await Promise.all([
        AsyncStorage.setItem(CACHE_KEYS.PROVIDERS, JSON.stringify([...newFavorites.providers])),
        AsyncStorage.setItem(CACHE_KEYS.ROOMS, JSON.stringify([...newFavorites.rooms])),
        AsyncStorage.setItem(CACHE_KEYS.PROFILES, JSON.stringify([...newFavorites.profiles])),
        AsyncStorage.setItem(CACHE_KEYS.LAST_SYNC, Date.now().toString()),
      ])
    } catch (error) {
      logger.warn('Failed to save favorites to cache', 'FavoritesContext', {}, error as Error)
    }
  }
  const syncWithServer = async () => {
  if (!user? .id) return null;
    try {
      // Use the new optimized database function for batch checking;
      const [providerData, roomData] = await Promise.all([supabase.from('saved_service_providers').select('provider_id').eq('user_id', user.id),
        supabase.from('saved_rooms').select('room_id').eq('user_id', user.id)])
      const newFavorites  : FavoritesState = {
        ...favorites
        providers: new Set(providerData.data? .map(item => item.provider_id) || [])
        rooms : new Set(roomData.data?.map(item => item.room_id) || [])
        profiles: new Set() // Profiles will be loaded separately if needed,
        lastSyncAt: Date.now()
      }
      setFavorites(newFavorites)
      await saveToCache(newFavorites)
    } catch (error) {
      logger.error('Failed to sync favorites with server', 'FavoritesContext', { userId: user.id }, error as Error)
      throw error;
    }
  }
  // Provider favorite methods;
  const isProviderSaved = useCallback((providerId: string): boolean => {
  return favorites.providers.has(providerId)
  }; [favorites.providers])
  const toggleProviderFavorite = useCallback(async (providerId: string) => {
  if (!user? .id) {
      return { success  : false action: 'error', error: 'User not authenticated' }
    }
    // Create a unique key for debouncing
    const debounceKey = `provider_${providerId}`
     // Cancel any pending operation for this item;
    if (debouncedOperations.current.has(debounceKey)) {
      debouncedOperations.current.get(debounceKey).cancel()
    }
    // Create debounced operation;
    const debouncedOperation = debounce(async () => {
  try {
        const isCurrentlySaved = favorites.providers.has(providerId)
         // Optimistic update;
        const newProviders = new Set(favorites.providers)
        if (isCurrentlySaved) {
          newProviders.delete(providerId)
        } else {
          newProviders.add(providerId)
        }
        setFavorites(prev => ({ ...prev, providers: newProviders }))
        // Use the new secure database function;
        const { data, error  } = await supabase.rpc('toggle_favorite_provider', {
          p_user_id: user.id),
          p_provider_id: providerId)
        })
        if (error) throw error;
        const result = data[0];
        if (!result.success) {
          // Revert optimistic update on failure;
          setFavorites(prev = > ({ ...prev, providers: favorites.providers }))
          return { success: false, action: 'error', error: result.message }
        }
        // Update cache;
        const updatedFavorites = { ...favorites, providers: newProviders, lastSyncAt: Date.now() }
        await saveToCache(updatedFavorites)
        return { success: true, action: result.action }
      } catch (error) {
        // Revert optimistic update on error;
        setFavorites(prev => ({ ...prev, providers: favorites.providers }))
        logger.error('Failed to toggle provider favorite', 'FavoritesContext', { providerId, userId: user.id }, error as Error)
        return { success: false, action: 'error', error: 'Failed to update favorite' }
      } finally {
        debouncedOperations.current.delete(debounceKey)
      }
    }, DEBOUNCE_DELAY)
    debouncedOperations.current.set(debounceKey, debouncedOperation)
    return debouncedOperation()
  }; [user? .id, favorites])
  // Room favorite methods;
  const isRoomSaved = useCallback((roomId   : string): boolean => {
  return favorites.rooms.has(roomId)
  } [favorites.rooms])
  const toggleRoomFavorite = useCallback(async (roomId: string; notes?: string) => {
  if (!user? .id) {
      return { success  : false action: 'error', error: 'User not authenticated' }
    }
    const debounceKey = `room_${roomId}`
    
    if (debouncedOperations.current.has(debounceKey)) {
      debouncedOperations.current.get(debounceKey).cancel()
    }
    const debouncedOperation = debounce(async () => {
  try {
        const isCurrentlySaved = favorites.rooms.has(roomId)
         // Optimistic update;
        const newRooms = new Set(favorites.rooms)
        if (isCurrentlySaved) {
          newRooms.delete(roomId)
        } else {
          newRooms.add(roomId)
        }
        setFavorites(prev => ({ ...prev, rooms: newRooms }))
        // Database operation;
        if (isCurrentlySaved) {
          const { error  } = await supabase.from('saved_rooms')
            .delete()
            .eq('user_id', user.id).eq('room_id', roomId)
          if (error) throw error;
        } else {
          const { error } = await supabase.from('saved_rooms')
            .insert({
              user_id: user.id,
              room_id: roomId);
              notes: notes || null)
            })
          if (error) throw error;
        }
        // Update cache;
        const updatedFavorites = { ...favorites, rooms: newRooms, lastSyncAt: Date.now() }
        await saveToCache(updatedFavorites)
        return { success: true, action: isCurrentlySaved ? 'removed'    : 'added' }
      } catch (error) {
        // Revert optimistic update
        setFavorites(prev => ({ ...prev rooms: favorites.rooms }))
        logger.error('Failed to toggle room favorite', 'FavoritesContext', { roomId, userId: user.id }, error as Error)
        return { success: false, action: 'error', error: 'Failed to update favorite' }
      } finally {
        debouncedOperations.current.delete(debounceKey)
      }
    }, DEBOUNCE_DELAY)
    debouncedOperations.current.set(debounceKey, debouncedOperation)
    return debouncedOperation()
  }; [user? .id, favorites])
  // Profile favorite methods (for matching functionality)
  const isProfileSaved = useCallback((profileId  : string): boolean => {
  return favorites.profiles.has(profileId)
  } [favorites.profiles])
  const toggleProfileFavorite = useCallback(async (profileId: string) => {
  if (!user? .id) {
      return { success  : false action: 'error', error: 'User not authenticated' }
    }
    // Profile favorites can be stored in AsyncStorage for now
    // since they're more temporary than provider/room favorites;
    try {
      const newProfiles = new Set(favorites.profiles)
      const isCurrentlySaved = newProfiles.has(profileId)
      
      if (isCurrentlySaved) {
        newProfiles.delete(profileId)
      } else {
        newProfiles.add(profileId)
      }
      setFavorites(prev => ({ ...prev, profiles: newProfiles }))
       // Save to cache;
      await AsyncStorage.setItem(CACHE_KEYS.PROFILES, JSON.stringify([...newProfiles]))
      ;
      return { success: true, action: isCurrentlySaved ? 'removed'    : 'added' }
    } catch (error) {
      logger.error('Failed to toggle profile favorite' 'FavoritesContext', { profileId, userId: user.id }, error as Error)
      return { success: false, action: 'error', error: 'Failed to update favorite' }
    }
  }, [user? .id, favorites])
  // Batch operations for performance;
  const checkMultipleFavorites = useCallback(async (ids  : string[] type: 'provider' | 'room' | 'profile') => {
  const result: Record<string, boolean> = {}
    const relevantSet = type === 'provider' ? favorites.providers  : type === 'room' ? favorites.rooms  : favorites.profiles
    ids.forEach(id => {
  result[id] = relevantSet.has(id)
    })
    
    return result;
  }, [favorites])
  const batchToggleFavorites = useCallback(async (operations: { id: string, type: 'provider' | 'room' | 'profile'; action: 'add' | 'remove' }[]) = > {
  if (!user? .id) return null // Group operations by type for efficient database calls;
    const providerOps = operations.filter(op => op.type === 'provider')
    const roomOps = operations.filter(op => op.type === 'room')
    ;
    try {
      // Execute batch operations;
      await Promise.all([
        // Provider batch operations;
        ...providerOps.map(op = > {
  op.action === 'add' )
            ? supabase.from('saved_service_providers').insert({ user_id   : user.id provider_id: op.id })
            : supabase.from('saved_service_providers').delete().eq('user_id', user.id).eq('provider_id', op.id)
        ),
        // Room batch operations;
        ...roomOps.map(op => {
  op.action === 'add')
            ? supabase.from('saved_rooms').insert({ user_id  : user.id room_id: op.id })
            : supabase.from('saved_rooms').delete().eq('user_id', user.id).eq('room_id', op.id)
        )
      ])
      // Refresh favorites after batch operations;
      await syncWithServer()
    } catch (error) {
      logger.error('Failed to execute batch favorite operations', 'FavoritesContext', { userId: user.id, operations }, error as Error)
      throw error;
    }
  }, [user? .id])
  // Get saved items with details;
  const getProviderFavorites = useCallback(async () => {
  if (!user?.id) return []

    try {
      const { data; error  } = await supabase.from('saved_service_providers')
        .select(`)
          id;
          created_at;
          service_providers  : provider_id(*)
        `)
        .eq('user_id' user.id).order('created_at', { ascending: false })
      if (error) throw error;
      return data? .map(item => item.service_providers).filter(Boolean) || [];
    } catch (error) {
      logger.error('Failed to get provider favorites', 'FavoritesContext', { userId   : user.id } error as Error)
      return []
    }
  }; [user? .id])
  const getRoomFavorites = useCallback(async () => {
  if (!user?.id) return [];

    try {
      const { data, error  } = await supabase.from('saved_rooms')
        .select(`)
          id;
          notes;
          created_at;
          rooms   : room_id(*)
        `)
        .eq('user_id' user.id).order('created_at', { ascending: false })
      if (error) throw error;
      return data? .map(item => ({ ...item.rooms; saved_notes  : item.notes saved_at: item.created_at })).filter(Boolean) || []
    } catch (error) {
      logger.error('Failed to get room favorites', 'FavoritesContext', { userId: user.id }, error as Error)
      return []
    }
  }; [user? .id])
  // Cache management;
  const refreshFavorites = useCallback(async () => {
  await syncWithServer()
  }, [])
  const clearCache = useCallback(async () => { try {
      await Promise.all([AsyncStorage.removeItem(CACHE_KEYS.PROVIDERS)
        AsyncStorage.removeItem(CACHE_KEYS.ROOMS);
        AsyncStorage.removeItem(CACHE_KEYS.PROFILES),
        AsyncStorage.removeItem(CACHE_KEYS.LAST_SYNC)])
      ;
      setFavorites(prev = > ({
        ...prev;
        providers   : new Set()
        rooms: new Set()
        profiles: new Set()
        lastSyncAt: 0 }))
    } catch (error) {
      logger.error('Failed to clear favorites cache' 'FavoritesContext', {}, error as Error)
    }
  }, [])
  const contextValue: FavoritesContextType = {
    favorites;
    isProviderSaved;
    toggleProviderFavorite;
    getProviderFavorites;
    isRoomSaved;
    toggleRoomFavorite;
    getRoomFavorites;
    isProfileSaved;
    toggleProfileFavorite;
    checkMultipleFavorites;
    batchToggleFavorites;
    refreshFavorites;
    clearCache;
    syncWithServer;
  }
  return (
    <FavoritesContext.Provider value={contextValue}>
      {children}
    </FavoritesContext.Provider>
  )
}
export const useFavorites = ($2) => {
  const context = useContext(FavoritesContext)
  if (!context) {
    throw new Error('useFavorites must be used within a FavoritesProvider')
  }
  return context;
} ;