import React from 'react';
import { HttpClient } from './HttpClient';
import { logger } from '@services/loggerService';

// Interface for sentiment analysis response;
export interface SentimentAnalysisResponse {
  sentiment: 'positive' | 'negative' | 'neutral',
  score: number,
  confidence: number,
}

// Interface for entity detection response;
export interface EntityDetectionResponse {
  entities: Array<{
    type: string,
    text: string,
    relevance: number,
  }>
}

// Interface for text classification response;
export interface TextClassificationResponse {
  categories: Array<{
    name: string,
    confidence: number,
  }>
}

class SentimentApi {
  private client: HttpClient,
  constructor() {
    // Initialize with appropriate API configuration;
    this.client = new HttpClient({
      baseURL: process.env.EXPO_PUBLIC_SENTIMENT_API_URL || '',
      timeout: 10000,
      retries: 2,
      headers: {
        'x-api-key': process.env.EXPO_PUBLIC_SENTIMENT_API_KEY || '',
      },
    });
  }

  /**;
   * Analyzes the sentiment of provided text;
   */
  async analyzeSentiment(text: string): Promise<SentimentAnalysisResponse> {
    try {
      if (!process.env.EXPO_PUBLIC_SENTIMENT_API_KEY) {
        logger.error('Missing SENTIMENT_API_KEY', 'SentimentApi');
        throw new Error('Sentiment API key is missing');
      }

      const response = await this.client.post<SentimentAnalysisResponse>('/analyze/sentiment', {
        text,
      });

      return response;
    } catch (error) {
      logger.error('Error analyzing sentiment', 'SentimentApi', {
        textLength: text.length,
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**;
   * Detects entities in the provided text;
   */
  async detectEntities(text: string): Promise<EntityDetectionResponse> {
    try {
      if (!process.env.EXPO_PUBLIC_SENTIMENT_API_KEY) {
        logger.error('Missing SENTIMENT_API_KEY', 'SentimentApi');
        throw new Error('Sentiment API key is missing');
      }

      const response = await this.client.post<EntityDetectionResponse>('/analyze/entities', {
        text,
      });

      return response;
    } catch (error) {
      logger.error('Error detecting entities', 'SentimentApi', {
        textLength: text.length,
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**;
   * Classifies text into predefined categories;
   */
  async classifyText(text: string): Promise<TextClassificationResponse> {
    try {
      if (!process.env.EXPO_PUBLIC_SENTIMENT_API_KEY) {
        logger.error('Missing SENTIMENT_API_KEY', 'SentimentApi');
        throw new Error('Sentiment API key is missing');
      }

      const response = await this.client.post<TextClassificationResponse>('/analyze/classify', {
        text,
      });

      return response;
    } catch (error) {
      logger.error('Error classifying text', 'SentimentApi', {
        textLength: text.length,
        error: (error as Error).message,
      });
      throw error;
    }
  }

  /**;
   * Analyzes the tone of provided text;
   */
  async analyzeTone(text: string): Promise<{
    tones: Array<{
      tone: string,
      score: number,
    }>
  }> {
    try {
      if (!process.env.EXPO_PUBLIC_SENTIMENT_API_KEY) {
        logger.error('Missing SENTIMENT_API_KEY', 'SentimentApi');
        throw new Error('Sentiment API key is missing');
      }

      const response = await this.client.post<{
        tones: Array<{
          tone: string,
          score: number,
        }>
      }>('/analyze/tone', {
        text,
      });

      return response;
    } catch (error) {
      logger.error('Error analyzing tone', 'SentimentApi', {
        textLength: text.length,
        error: (error as Error).message,
      });
      throw error;
    }
  }
}

// Create and export a singleton instance;
export const sentimentApi = new SentimentApi();

// Export the class for testing or custom initialization;
export default SentimentApi;
