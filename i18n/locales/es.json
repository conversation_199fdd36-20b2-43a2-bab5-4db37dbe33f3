{"common": {"loading": "Cargando...", "save": "Guardar", "cancel": "<PERSON><PERSON><PERSON>", "delete": "Eliminar", "edit": "<PERSON><PERSON>", "add": "Agregar", "remove": "<PERSON><PERSON><PERSON>", "search": "Buscar", "filter": "Filtrar", "refresh": "Actualizar", "back": "Atrás", "next": "Siguient<PERSON>", "previous": "Anterior", "done": "<PERSON><PERSON>", "yes": "Sí", "no": "No", "ok": "OK", "error": "Error", "success": "Éxito", "warning": "Advertencia", "info": "Información"}, "navigation": {"home": "<PERSON><PERSON>o", "profile": "Perfil", "household": "<PERSON><PERSON>", "messages": "<PERSON><PERSON><PERSON><PERSON>", "search": "Buscar", "settings": "Configuración"}, "profile": {"title": "Perfil", "edit": "<PERSON><PERSON>", "completion": "Completar Perfil", "interests": "Intereses y Pasatiempos", "personality": "Evaluación de Personalidad", "lifestyle": "Preferencias de Estilo de Vida", "verification": "Verificación y Confianza", "financial": "Gestión Financiera", "roles": "<PERSON><PERSON> <PERSON> Us<PERSON>"}, "household": {"title": "<PERSON><PERSON>", "dashboard": "Panel de Control", "members": "Mi<PERSON><PERSON><PERSON>", "expenses": "Gastos", "chores": "<PERSON><PERSON><PERSON>", "calendar": "Calendario", "communication": "Comunicación", "settings": "Configuración", "health_score": "Puntuación de Salud del Hogar", "satisfaction": "Puntuación de Satisfacción"}, "communication": {"title": "Centro de Comunicación", "general_chat": "Chat General", "announcements": "<PERSON><PERSON><PERSON><PERSON>", "expenses_bills": "Gastos y Facturas", "social_events": "Eventos Sociales", "maintenance": "Mantenimiento", "send_message": "<PERSON><PERSON><PERSON> men<PERSON>", "type_message": "Escribe un mensaje...", "online": "En línea", "offline": "Desconectado", "last_seen": "Visto por última vez"}, "settings": {"title": "Configuración", "language": "Idioma y Región", "notifications": "Notificaciones", "privacy": "Privacidad", "accessibility": "Accesibilidad", "appearance": "Apariencia", "account": "C<PERSON><PERSON>", "about": "Acerca de"}, "personality": {"types": {"ENFP": "El Activista", "ISFJ": "El Protector", "INTJ": "El Arquitecto", "ESTP": "El Emprendedor", "INFP": "El Mediador", "ESTJ": "El Ejecutivo", "ISTP": "El Virtuoso", "ENFJ": "El Protagonista"}, "communication_styles": {"direct": "Directo", "diplomatic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "supportive": "Comprensivo", "analytical": "Analítico"}, "lifestyle_types": {"social_butterfly": "Mariposa Social", "organized_planner": "Planificador Organizado", "night_owl": "<PERSON><PERSON>ho Nocturno", "early_bird": "Madrugador", "minimalist": "Minimalista", "creative": "Creativo"}}, "expenses": {"title": "Gastos", "add_expense": "<PERSON><PERSON><PERSON><PERSON>", "split_expense": "<PERSON><PERSON><PERSON>", "pending": "Pendiente", "settled": "Liquidado", "overdue": "V<PERSON>cid<PERSON>", "categories": {"groceries": "Comestibles", "utilities": "<PERSON><PERSON><PERSON>", "rent": "<PERSON><PERSON><PERSON>", "internet": "Internet", "cleaning": "Limpieza", "maintenance": "Mantenimiento", "social": "Social", "other": "<PERSON><PERSON>"}}, "chores": {"title": "<PERSON><PERSON><PERSON>", "add_chore": "<PERSON><PERSON><PERSON><PERSON>", "assign_chore": "<PERSON><PERSON><PERSON>", "complete": "Completar", "pending": "Pendiente", "overdue": "V<PERSON>cid<PERSON>", "categories": {"kitchen": "<PERSON><PERSON><PERSON>", "bathroom": "<PERSON>ño", "living_room": "Sala de Estar", "general": "General", "outdoor": "Exterior"}}, "verification": {"title": "Verificación", "identity": "Verificación de Identidad", "background": "Verificación de Antecedentes", "references": "Referencias", "trust_score": "Puntuación de Confianza", "verified": "Verificado", "pending": "Pendiente", "failed": "Fallido"}, "errors": {"network": "Error de conexión de red", "server": "Error del servidor", "validation": "Por favor verifica tu entrada", "permission": "<PERSON><PERSON><PERSON> den<PERSON>ado", "not_found": "Recurso no encontrado", "timeout": "Tiempo de espera agotado"}, "success": {"saved": "Guardado exitosamente", "updated": "Actualizado exitosamente", "deleted": "Eliminado exitosamente", "sent": "Enviado exitosamente", "completed": "Completado exitosamente"}, "accessibility": {"title": "Configuración de Accesibilidad", "subtitle": "Compatible con WCAG 2.1 • Impulsado por personalidad", "visual": {"title": "Accesibilidad Visual", "high_contrast": "Modo de Alto Contraste", "high_contrast_desc": "Mejora la visibilidad del texto y elementos de la interfaz", "font_size": "Tamaño de Fuente", "font_size_desc": "Actual: {{percentage}}%", "reduce_transparency": "Reducir <PERSON>", "reduce_transparency_desc": "Hace los elementos de la interfaz más opacos", "color_blind_support": "Soporte para Daltonismo"}, "motor": {"title": "Accesibilidad Motora", "touch_accommodations": "Adaptaciones Táctiles", "touch_accommodations_desc": "Objetivos táctiles más grandes y retrasos de mantenimiento", "gesture_alternatives": "Alternativas de Gestos", "gesture_alternatives_desc": "Alternativas de botones para gestos complejos", "voice_control": "Control por Voz", "voice_control_desc": "Navegar usando comandos de voz"}, "cognitive": {"title": "Accesibilidad Cognitiva", "reduce_motion": "<PERSON><PERSON><PERSON>", "reduce_motion_desc": "Minimiza animaciones y transiciones", "simplified_interface": "Interfaz Simplificada", "simplified_interface_desc": "Reduce la complejidad visual", "focus_indicators": "Indicadores de Enfoque Mejorados", "focus_indicators_desc": "Contornos de enfoque más claros para navegación", "timeout_extensions": "Extensiones de Tiempo", "timeout_extensions_desc": "Más tiempo para interacciones"}, "audio": {"title": "Accesibilidad de Audio y Háptica", "audio_descriptions": "Descripciones de Audio", "audio_descriptions_desc": "Descripciones habladas del contenido visual", "sound_feedback": "Retroalimentación Sonora", "sound_feedback_desc": "Señales de audio para interacciones", "haptic_feedback": "Retroalimentación Háptica", "haptic_feedback_desc": "Vibración para interacciones táctiles"}, "personality": {"title": "Accesibilidad Basada en Personalidad", "adaptations": "Adaptaciones de Personalidad", "adaptations_desc": "Personalizar basado en tu tipo de personalidad", "communication_style": "Adaptación del Estilo de Comunicación", "supportive_desc": "Tu estilo de comunicación de apoyo se beneficia de retroalimentación suave, tiempos extendidos y señales de audio claras para una experiencia cómoda."}, "wcag": {"title": "Cumplimiento WCAG", "aa_compliance": "Cumplimiento AA", "aaa_compliance": "Cumplimiento AAA", "run_test": "Ejecutar Prueba de Accesibilidad", "running_test": "<PERSON><PERSON><PERSON><PERSON><PERSON>...", "test_completed": "¡Prueba de accesibilidad completada! Revisa los resultados para más detalles."}, "analytics": {"compliance_score": "Puntuación de Cumplimiento", "satisfaction": "Satisfacción", "feature_usage": "Uso de Características", "last_audit": "Última Auditoría"}, "suggestions": {"title": "Sugerencias Inteligentes de Accesibilidad", "view_all": "<PERSON><PERSON>", "high_impact": "ALTO IMPACTO", "medium_impact": "IMPACTO MEDIO", "low_impact": "BAJO IMPACTO", "personality": "Personalidad"}}, "cultural": {"title": "Configuración Cultural", "subtitle": "Adaptación cultural con integración de personalidad", "identity": {"title": "Identidad Cultural", "cultural_background": "Antecedentes Culturales", "primary_culture": "Cultura Principal", "cultural_fluency": "Fluidez Cultural", "select_cultures": "Selecciona tus antecedentes culturales", "cultures_selected": "cultura(s) seleccionada(s)", "not_specified": "No especificado"}, "communication": {"title": "Patrones de Comunicación", "context": "Contexto de Comunicación", "directness": "Preferencia de Directividad", "hierarchy": "Orientación Jerárquica", "time_orientation": "Orientación Temporal", "high_context": "Alto Contexto", "low_context": "<PERSON><PERSON>", "mixed": "Mixto", "very_direct": "<PERSON><PERSON>", "direct": "Directo", "moderate": "Moderado", "indirect": "Indirecto", "very_indirect": "<PERSON><PERSON>", "hierarchical": "Jerárqui<PERSON>", "egalitarian": "Igualitario", "monochronic": "Monocrónico", "polychronic": "Policrónico", "flexible": "Flexible"}, "social": {"title": "Dinámicas Sociales", "individualism_collectivism": "Individualismo vs Colectivismo", "power_distance": "<PERSON><PERSON><PERSON> Poder", "uncertainty_avoidance": "Evitación de Incertidumbre", "relationship_building": "Construcción de Relaciones", "individualistic": "Individualista", "collectivistic": "Colectivista", "low": "<PERSON><PERSON>", "high": "Alto", "task_focused": "Enfocado en Tareas", "balanced": "Equilibrado", "relationship_focused": "Enfocado en Relaciones"}, "household": {"title": "Preferencias Culturales del Hogar", "privacy_expectations": "Expectativas de Privacidad", "family_involvement": "Participación Familiar", "guest_hospitality": "Hospitalidad de Huéspedes", "conflict_resolution": "Estilo de Resolución de Conflictos", "very_private": "<PERSON><PERSON>", "private": "Privado", "open": "<PERSON>bie<PERSON>o", "very_open": "<PERSON><PERSON>", "minimal": "<PERSON><PERSON><PERSON>", "significant": "Significativa", "extensive": "Extensa", "formal": "Formal", "casual": "Casual", "very_casual": "<PERSON><PERSON>", "direct_confrontation": "Confrontación Directa", "mediated_discussion": "Discusión Mediada", "indirect_approach": "Enfoque Indirecto", "avoidance": "Evitación", "elder_intervention": "Intervención de Mayores"}, "ui": {"title": "Preferencias Culturales de UI/UX", "color_preferences": "Preferencias de Color", "layout_preference": "Preferencia de Diseño", "interaction_style": "Estilo de Interacción", "feedback_preference": "Preferencia de Retroalimentación", "warm": "<PERSON><PERSON><PERSON><PERSON>", "cool": "Frío", "neutral": "Neutral", "vibrant": "<PERSON>ib<PERSON><PERSON>", "muted": "<PERSON><PERSON><PERSON>", "rich": "Rico", "decorative": "Decorativo", "professional": "Profesional", "friendly": "Amigable", "immediate": "Inmediato", "delayed": "Retrasado", "contextual": "Contextual", "subtle": "<PERSON><PERSON>"}, "personality": {"title": "Integración Personalidad-Cultural", "adaptation_title": "Adaptación Cultural de Comunicación de Apoyo", "adaptation_description": "Tu estilo de comunicación de apoyo se alinea bien con culturas enfocadas en relaciones. Considera habilitar la participación familiar y enfoques de comunicación indirecta para una mejor armonía cultural.", "recommended": "Recomendado: Configuraciones enfocadas en relaciones"}, "assessment": {"title": "Evaluación Cultural", "adaptation_score": "Puntuación de Adaptación", "cultural_satisfaction": "Satisfacción Cultural", "communication_effectiveness": "Efectividad de Comunicación", "household_harmony": "Armonía del Hogar", "run_assessment": "Ejecutar Evaluación Cultural", "running_assessment": "Ejecutando Evaluación...", "assessment_completed": "¡Evaluación cultural completada! Puntuación de adaptación: {{score}}%"}, "analytics": {"title": "Análisis Cultural", "adaptation_score": "Puntuación de Adaptación", "cultural_satisfaction": "Satisfacción Cultural", "communication": "Comunicación", "household_harmony": "Armonía del Hogar", "feature_usage": "Uso de Características", "cultural_communication": "Comunicación Cultural", "household_adaptation": "Adaptación del Hogar", "ui_customization": "Personalización de UI", "conflict_resolution": "Resolución de Conflictos", "cultural_events": "Eventos Culturales"}, "suggestions": {"title": "Sugerencias Culturales Inteligentes", "view_all": "<PERSON><PERSON>", "high_impact": "ALTO IMPACTO", "medium_impact": "IMPACTO MEDIO", "low_impact": "BAJO IMPACTO", "personality": "Personalidad", "confidence": "Confianza"}, "messages": {"loading": "Cargando configuración cultural...", "update_success": "¡Configuración cultural actualizada exitosamente!", "update_error": "Error al actualizar configuración cultural", "background_update_success": "¡Antecedentes culturales actualizados exitosamente!", "background_update_error": "Error al actualizar antecedentes culturales", "load_error": "No se pudo cargar la configuración cultural"}}, "voice_motor": {"title": "Configuración de Voz y Motor", "subtitle": "Comandos de voz • Accesibilidad motora", "voice": {"title": "Comandos de Voz", "enable_commands": "Habilitar Comandos de Voz", "enable_commands_desc": "Controlar la aplicación usando comandos de voz", "voice_navigation": "Navegación por Voz", "voice_navigation_desc": "Navegar entre pantallas usando voz", "voice_feedback": "Retroalimentación de Voz", "voice_feedback_desc": "Escuchar confirmación cuando se ejecutan comandos", "voice_confirmation": "Confirmación de Voz", "voice_confirmation_desc": "Requerir confirmación de voz para acciones", "voice_volume": "Volumen de Voz", "voice_language": "Idioma de Voz", "voice_speed": "Velocidad de <PERSON>oz", "voice_pitch": "<PERSON><PERSON>", "test_command": "Probar <PERSON>", "testing": "Probando...", "manage": "Gestionar", "slow": "<PERSON><PERSON>", "normal": "Normal", "fast": "<PERSON><PERSON><PERSON><PERSON>", "low": "<PERSON><PERSON>", "high": "Alto"}, "motor": {"title": "Accesibilidad Motora", "enable_assistance": "<PERSON><PERSON><PERSON><PERSON> Motora", "enable_assistance_desc": "Habilitar características de accesibilidad motora", "touch_accommodations": "Adaptaciones Táctiles", "touch_accommodations_desc": "Objetivos táctiles más grandes y retrasos de mantenimiento", "gesture_alternatives": "Alternativas de Gestos", "gesture_alternatives_desc": "Formas alternativas de realizar gestos", "one_handed_mode": "<PERSON><PERSON>", "one_handed_mode_desc": "Optimizar interfaz para uso con una mano", "touch_target_size": "Tamaño del Objetivo Táctil", "touch_hold_delay": "Retraso de Mantenimiento Táctil", "gesture_sensitivity": "Sensibilidad de Gestos", "double_tap_timeout": "Tiempo de Espera de Doble Toque", "interaction_timeout": "Tiempo de Espera de Interacción", "auto_scroll": "Desplazamiento Automático", "auto_scroll_desc": "Desplazar contenido automáticamente", "auto_scroll_speed": "Velocidad de Desplazamiento Automático", "sticky_keys": "<PERSON><PERSON>las <PERSON>", "sticky_keys_desc": "Las teclas permanecen activas hasta ser presionadas nuevamente", "preferred_hand": "<PERSON><PERSON>", "motor_impairment": "Tipo de Discapacidad Motora", "small": "Pequeño", "medium": "Mediano", "large": "Grande", "extra_large": "Extra Grande", "left": "Iz<PERSON>erda", "right": "Derecha", "both": "Ambas", "none": "Ninguna", "mild": "<PERSON><PERSON>", "moderate": "Moderada", "severe": "<PERSON><PERSON><PERSON>", "test": "Probar"}, "assistive": {"title": "Tecnología Asistiva", "switch_control": "Control de Interruptor", "switch_control_desc": "Soporte para dispositivos de interruptor externo", "external_switch": "Soporte de Interruptor Externo", "external_switch_desc": "Soporte para dispositivos de interruptor externo", "head_tracking": "Seguimiento de Cabeza", "head_tracking_desc": "Navegación por movimiento de cabeza", "eye_tracking": "Seguimiento Ocular", "eye_tracking_desc": "Integración de dispositivos de seguimiento ocular", "screen_reader": "Optimización para Lector de Pantalla", "screen_reader_desc": "Optimizar para lectores de pantalla", "assistive_device": "Tipo de Dispositivo Asistivo", "switch": "Interruptor", "joystick": "Joystick", "eye_tracker": "<PERSON><PERSON><PERSON><PERSON>", "head_tracker": "<PERSON><PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON>"}, "personality": {"title": "Integración Personalidad-Accesibilidad", "extroverted_optimization": "Optimización de Interacción de Voz Extrovertida", "extroverted_desc": "Tu tipo de personalidad extrovertida (ENFP) se beneficia de comandos de voz conversacionales y retroalimentación interactiva. Considera habilitar retroalimentación de voz y características de voz social para una mejor experiencia de accesibilidad.", "introverted_optimization": "Comandos de Voz Eficientes para Tipos Analíticos", "introverted_desc": "Tu personalidad analítica se beneficiaría de comandos de voz precisos y eficientes con retroalimentación mínima.", "personality_type": "Tipo de Personalidad", "communication_style": "Estilo de Comunicación", "recommended": "Recomendado: Características de voz interactivas"}, "assessment": {"title": "Evaluación de Accesibilidad", "motor_effectiveness": "Efectividad Motora", "voice_effectiveness": "Efectividad de Voz", "interaction_efficiency": "Eficiencia de Interacción", "run_assessment": "Ejecutar Evaluación de Accesibilidad", "running_assessment": "Ejecutando Evaluación...", "assessment_complete": "¡Evaluación de accesibilidad completada!"}, "analytics": {"voice_usage": "Uso de <PERSON>", "motor_assistance": "<PERSON><PERSON><PERSON><PERSON> Motora", "command_success": "<PERSON><PERSON><PERSON> de Comandos", "interaction_efficiency": "Eficiencia de Interacción", "accessibility_satisfaction": "Satisfacción de Accesibilidad", "daily_usage": "Uso Diario", "error_rate": "<PERSON><PERSON>", "feature_usage": "Uso de Características", "voice_commands": "Comandos de Voz", "switch_control": "Control de Interruptor", "gesture_alternatives": "Alternativas de Gestos", "voice_navigation": "Navegación por Voz", "adaptation_trends": "Tendencias de Adaptación", "weekly_improvement": "<PERSON><PERSON><PERSON>", "command_accuracy": "Precisión de Comandos", "motor_efficiency": "Eficiencia Motora", "user_satisfaction": "Satisfacción del Usuario"}, "suggestions": {"title": "Sugerencias Inteligentes de Accesibilidad", "voice_optimization": "Optimización de Comandos de Voz", "motor_assistance": "Mejora de Asistencia Motora", "command_customization": "Personalización de Comandos", "interaction_improvement": "Mejora de Interacción", "assistive_technology": "Integración de Tecnología Asistiva", "personality_adaptation": "Adaptación Basada en Personalidad", "extroverted_voice": "Interacción de Voz Mejorada para Tipos Sociales", "extroverted_voice_desc": "Tu personalidad extrovertida se beneficiaría de comandos de voz conversacionales y características de voz social.", "introverted_efficient": "Comandos de Voz Eficientes para Tipos Analíticos", "introverted_efficient_desc": "Tu personalidad analítica se beneficiaría de comandos de voz precisos y eficientes con retroalimentación mínima.", "comprehensive_motor": "Soporte Integral de Accesibilidad Motora", "comprehensive_motor_desc": "Habilitar características completas de accesibilidad motora incluyendo control de interruptor, alternativas de gestos y adaptaciones táctiles.", "voice_expansion": "Expandir Biblioteca de Comandos de Voz", "voice_expansion_desc": "Agregar más comandos de voz para acciones frecuentes para mejorar eficiencia y accesibilidad.", "assistive_integration": "Integrar Soporte de Tecnología Asistiva", "assistive_integration_desc": "Habilitar soporte para tu dispositivo asistivo para mejorar eficiencia de interacción y accesibilidad.", "view_all": "<PERSON><PERSON>", "high_impact": "ALTO IMPACTO", "medium_impact": "IMPACTO MEDIO", "low_impact": "BAJO IMPACTO", "personality": "Personalidad"}, "commands": {"go_home": "<PERSON><PERSON> <PERSON><PERSON>", "open_profile": "<PERSON><PERSON><PERSON>", "open_messages": "<PERSON><PERSON><PERSON>", "scroll_up": "<PERSON><PERSON><PERSON><PERSON>", "scroll_down": "<PERSON><PERSON><PERSON><PERSON>", "go_back": "Regresar", "read_content": "<PERSON><PERSON>", "open_settings": "Abrir Configuración"}, "messages": {"loading": "Cargando configuración de voz y motor...", "settings_updated": "¡Configuración de voz y motor actualizada exitosamente!", "command_executed": "¡Comando de voz ejecutado exitosamente!", "command_not_recognized": "Comando de voz no reconocido", "test_failed": "Error al probar comando de voz", "assessment_completed": "¡Evaluación de accesibilidad completada!", "assessment_failed": "Error al ejecutar evaluación de accesibilidad", "error_loading": "No se pudo cargar la configuración de voz y motor", "error_updating": "Error al actualizar configuración de voz y motor"}}, "regional_market": {"title": "Configuración de Mercado Regional", "subtitle": "Adaptación de mercado • Cumplimiento • Preferencias culturales", "regional": {"title": "Configuración Regional", "current_region": "Región Actual", "auto_detect": "Detectar Región Automáticamente", "auto_detect_desc": "Detectar automáticamente la región desde la configuración del dispositivo", "region_override": "Anular Región", "currency_display": "Visualización de Moneda", "date_format": "Formato de Fecha", "number_format": "Formato de Número", "time_zone": "Zona Horaria", "business_hours": "<PERSON><PERSON><PERSON>", "change": "Cambiar"}, "payment": {"title": "Métodos de Pago", "methods": "Métodos de Pago", "card": "Tarjeta de Crédito/Débito", "mobile_money": "<PERSON><PERSON>", "bank_transfer": "Transferencia Bancaria", "digital_wallet": "Billetera Digital", "cryptocurrency": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "processing_time": "Tiempo de Procesamiento", "popularity_score": "Puntuación de Popularidad", "transaction_fees": "Tarifas de Transacción", "no_methods": "No hay métodos de pago configurados para esta región"}, "compliance": {"title": "Cumplimiento y Legal", "privacy_law": "Ley de Privacidad", "data_retention": "Retención de Datos", "age_verification": "Verificación de Edad", "consent_management": "Gestión de Consentimiento", "gdpr_compliance": "Cumplimiento GDPR", "ccpa_compliance": "Cumplimiento CCPA", "local_regulations": "Regulaciones Locales", "business_practices": "Prácticas Comerciales", "regional_requirements": "Requisitos Regionales", "mandatory": "Obligatorio", "recommended": "Recomendado", "optional": "Opcional", "required": "Requerido", "not_required": "No Requerido", "details": "Detalles", "days": "días"}, "cultural": {"title": "Adaptación Cultural", "communication_style": "Estilo de Comunicación Cultural", "sensitivity_level": "Nivel de Sensibilidad Cultural", "local_customs": "Costumbres Locales", "social_norms": "Normas Sociales", "business_etiquette": "Etiqueta Comercial", "holiday_calendar": "Calendario de Fiestas", "working_days": "Días Laborables", "low": "<PERSON><PERSON>", "medium": "Medio", "high": "Alto", "maximum": "Máximo"}, "personality": {"title": "Integración Personalidad-Cultural", "integration": "Integración de Personalidad", "mbti_adaptation": "Adaptación de Mercado Basada en MBTI", "communication_adaptation": "Adaptación del Estilo de Comunicación", "cultural_preferences": "Preferencias Culturales", "market_behavior": "Patrones de Comportamiento del Mercado", "extroverted_adaptation": "Tu tipo de personalidad extrovertida (ENFP) se beneficia de características sociales adaptadas culturalmente y adaptaciones de mercado centradas en la comunidad. El nivel actual de sensibilidad cultural mejora tu experiencia con las normas sociales locales y los patrones de comunicación.", "recommended_high": "Recomendado: Alta sensibilidad cultural"}, "assessment": {"title": "Evaluación de Mercado", "market_readiness": "Preparación del Mercado", "compliance_score": "Puntuación de Cumplimiento", "cultural_adaptation": "Adaptación Cultural", "payment_integration": "Integración de Pagos", "regulatory_compliance": "Cumplimiento Regulatorio", "localization_effectiveness": "Efectividad de Localización", "run_assessment": "Ejecutar Evaluación de Mercado", "running_assessment": "Ejecutando Evaluación...", "assessment_complete": "¡Evaluación de mercado completada! Preparación del mercado: {{score}}%"}, "analytics": {"market_penetration": "Penetración de Mercado", "user_adoption": "Tasa de Adopción de Usuarios", "payment_success": "Éxito <PERSON>gos", "compliance_score": "Puntuación de Cumplimiento", "cultural_effectiveness": "Adaptación Cultural", "regional_satisfaction": "Satisfacción Regional", "market_share": "Cuota de Mercado", "competitive_position": "Posición Competitiva", "feature_usage": "Uso de Características", "market_trends": "Tendencias del Mercado", "growth_rate": "Tasa de Crecimiento", "acquisition_cost": "Costo de Adquisición de Usuario", "lifetime_value": "Valor de Vida del Cliente", "churn_rate": "<PERSON><PERSON>", "engagement_score": "Puntuación de Compromiso"}, "suggestions": {"title": "Sugerencias Inteligentes de Mercado", "payment_optimization": "Optimización de Métodos de Pago", "compliance_enhancement": "Mejora del Cumplimiento", "cultural_adaptation": "Adaptación Cultural", "market_expansion": "Expansión de Mercado", "regulatory_alignment": "Alineación Regulatoria", "localization_improvement": "Mejora de Localización", "social_cultural_adaptation": "Adaptación Cultural Social para Tipos Extrovertidos", "social_cultural_desc": "Tu personalidad extrovertida se beneficia de características sociales adaptadas culturalmente y adaptaciones de mercado centradas en la comunidad.", "ethiopian_payment_optimization": "Optimización de Métodos de Pago Etíopes", "ethiopian_payment_desc": "Optimizar métodos de pago para el mercado etíope con dinero móvil e integración bancaria local.", "gdpr_compliance_enhancement": "Mejora del Cumplimiento GDPR", "gdpr_compliance_desc": "Mejorar las características de cumplimiento GDPR para operaciones del mercado europeo y protección de datos.", "view_all": "<PERSON><PERSON>", "high_impact": "ALTO IMPACTO", "medium_impact": "IMPACTO MEDIO", "low_impact": "BAJO IMPACTO", "personality": "Personalidad"}, "regions": {"north_america": "América del Norte", "europe": "Europa", "africa": "África", "asia_pacific": "Asia Pacífico", "middle_east": "Medio Oriente", "latin_america": "América Latina", "oceania": "Oceanía"}, "countries": {"united_states": "Estados Unidos", "canada": "Canadá", "united_kingdom": "Reino Unido", "germany": "Alemania", "france": "Francia", "ethiopia": "Etiopía", "nigeria": "Nigeria", "south_africa": "Sudáfrica", "japan": "Japón", "australia": "Australia", "brazil": "Brasil", "mexico": "México"}, "messages": {"loading": "Cargando configuraciones de mercado regional...", "region_switched": "¡Cambiado exitosamente al mercado {{region}}!", "payment_method_added": "Método de pago agregado exitosamente", "compliance_updated": "Configuraciones de cumplimiento actualizadas", "cultural_settings_saved": "Configuraciones culturales guardadas", "market_assessment_complete": "¡Evaluación de mercado completada!", "configuration_error": "Ocurrió un error de configuración", "region_detection_failed": "Error al detectar la región automáticamente", "setting_updated": "¡Configuración de mercado regional actualizada exitosamente!", "setting_failed": "Error al actualizar la configuración de mercado regional", "ethiopian_payment_configured": "¡Optimización de pagos etíopes configurada!", "gdpr_compliance_enhanced": "¡Cumplimiento GDPR mejorado!", "error_loading": "No se pudieron cargar las configuraciones de mercado regional", "error_switching": "Error al cambiar de región", "error_assessment": "Error al ejecutar la evaluación de mercado"}}}