{"common": {"loading": "Loading...", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "remove": "Remove", "search": "Search", "filter": "Filter", "refresh": "Refresh", "back": "Back", "next": "Next", "previous": "Previous", "done": "Done", "yes": "Yes", "no": "No", "ok": "OK", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information"}, "navigation": {"home": "Home", "profile": "Profile", "household": "Household", "messages": "Messages", "search": "Search", "settings": "Settings"}, "profile": {"title": "Profile", "edit": "Edit Profile", "completion": "Profile Completion", "interests": "Interests & Hobbies", "personality": "Personality Assessment", "lifestyle": "Lifestyle Preferences", "verification": "Verification & Trust", "financial": "Financial Management", "roles": "User Roles"}, "household": {"title": "Household", "dashboard": "Dashboard", "members": "Members", "expenses": "Expenses", "chores": "Chores", "calendar": "Calendar", "communication": "Communication", "settings": "Settings", "health_score": "Household Health Score", "satisfaction": "Satisfaction Score"}, "communication": {"title": "Communication Hub", "general_chat": "General <PERSON><PERSON>", "announcements": "Announcements", "expenses_bills": "Expenses & Bills", "social_events": "Social & Events", "maintenance": "Maintenance", "send_message": "Send message", "type_message": "Type a message...", "online": "Online", "offline": "Offline", "last_seen": "Last seen"}, "settings": {"title": "Settings", "language": "Language & Region", "notifications": "Notifications", "privacy": "Privacy", "accessibility": "Accessibility", "appearance": "Appearance", "account": "Account", "about": "About"}, "personality": {"types": {"ENFP": "The Campaigner", "ISFJ": "The Protector", "INTJ": "The Architect", "ESTP": "The Entrepreneur", "INFP": "The Mediator", "ESTJ": "The Executive", "ISTP": "The Virtuoso", "ENFJ": "The Protagonist"}, "communication_styles": {"direct": "Direct", "diplomatic": "Diplomatic", "supportive": "Supportive", "analytical": "Analytical"}, "lifestyle_types": {"social_butterfly": "Social Butterfly", "organized_planner": "Organized Planner", "night_owl": "Night Owl", "early_bird": "Early Bird", "minimalist": "Minimalist", "creative": "Creative"}}, "expenses": {"title": "Expenses", "add_expense": "Add Expense", "split_expense": "Split Expense", "pending": "Pending", "settled": "Settled", "overdue": "Overdue", "categories": {"groceries": "Groceries", "utilities": "Utilities", "rent": "Rent", "internet": "Internet", "cleaning": "Cleaning", "maintenance": "Maintenance", "social": "Social", "other": "Other"}}, "chores": {"title": "Chores", "add_chore": "Add Chore", "assign_chore": "Assign <PERSON>", "complete": "Complete", "pending": "Pending", "overdue": "Overdue", "categories": {"kitchen": "Kitchen", "bathroom": "Bathroom", "living_room": "Living Room", "general": "General", "outdoor": "Outdoor"}}, "verification": {"title": "Verification", "identity": "Identity Verification", "background": "Background Check", "references": "References", "trust_score": "Trust Score", "verified": "Verified", "pending": "Pending", "failed": "Failed"}, "errors": {"network": "Network connection error", "server": "Server error occurred", "validation": "Please check your input", "permission": "Permission denied", "not_found": "Resource not found", "timeout": "Request timed out"}, "success": {"saved": "Successfully saved", "updated": "Successfully updated", "deleted": "Successfully deleted", "sent": "Successfully sent", "completed": "Successfully completed"}, "accessibility": {"title": "Accessibility Settings", "subtitle": "WCAG 2.1 compliant • Personality-driven", "visual": {"title": "Visual Accessibility", "high_contrast": "High Contrast Mode", "high_contrast_desc": "Enhances text and UI element visibility", "font_size": "Font Size", "font_size_desc": "Current: {{percentage}}%", "reduce_transparency": "Reduce Transparency", "reduce_transparency_desc": "Makes UI elements more opaque", "color_blind_support": "Color Blind Support"}, "motor": {"title": "Motor Accessibility", "touch_accommodations": "Touch Accommodations", "touch_accommodations_desc": "Larger touch targets and hold delays", "gesture_alternatives": "Gesture Alternatives", "gesture_alternatives_desc": "Button alternatives for complex gestures", "voice_control": "Voice Control", "voice_control_desc": "Navigate using voice commands"}, "cognitive": {"title": "Cognitive Accessibility", "reduce_motion": "Reduce Motion", "reduce_motion_desc": "Minimizes animations and transitions", "simplified_interface": "Simplified Interface", "simplified_interface_desc": "Reduces visual complexity", "focus_indicators": "Enhanced Focus Indicators", "focus_indicators_desc": "Clearer focus outlines for navigation", "timeout_extensions": "Timeout Extensions", "timeout_extensions_desc": "More time for interactions"}, "audio": {"title": "Audio & Haptic Accessibility", "audio_descriptions": "Audio Descriptions", "audio_descriptions_desc": "Spoken descriptions of visual content", "sound_feedback": "Sound Feedback", "sound_feedback_desc": "Audio cues for interactions", "haptic_feedback": "Haptic <PERSON>", "haptic_feedback_desc": "Vibration for touch interactions"}, "personality": {"title": "Personality-Based Accessibility", "adaptations": "Personality Adaptations", "adaptations_desc": "Customize based on your personality type", "communication_style": "Communication Style Adaptation", "supportive_desc": "Your supportive communication style benefits from gentle feedback, extended timeouts, and clear audio cues for a comfortable experience."}, "wcag": {"title": "WCAG Compliance", "aa_compliance": "AA Compliance", "aaa_compliance": "AAA Compliance", "run_test": "Run Accessibility Test", "running_test": "Running Test...", "test_completed": "Accessibility test completed! Check results for details."}, "analytics": {"compliance_score": "Compliance Score", "satisfaction": "Satisfaction", "feature_usage": "Feature Usage", "last_audit": "Last Audit"}, "suggestions": {"title": "Smart Accessibility Suggestions", "view_all": "View All", "high_impact": "HIGH IMPACT", "medium_impact": "MEDIUM IMPACT", "low_impact": "LOW IMPACT", "personality": "Personality"}}, "cultural": {"title": "Cultural Settings", "subtitle": "Cultural adaptation with personality integration", "identity": {"title": "Cultural Identity", "cultural_background": "Cultural Background", "primary_culture": "Primary Culture", "cultural_fluency": "Cultural Fluency", "select_cultures": "Select your cultural backgrounds", "cultures_selected": "culture(s) selected", "not_specified": "Not specified"}, "communication": {"title": "Communication Patterns", "context": "Communication Context", "directness": "Directness Preference", "hierarchy": "Hierarchy Orientation", "time_orientation": "Time Orientation", "high_context": "High Context", "low_context": "Low Context", "mixed": "Mixed", "very_direct": "Very Direct", "direct": "Direct", "moderate": "Moderate", "indirect": "Indirect", "very_indirect": "Very Indirect", "hierarchical": "Hierarchical", "egalitarian": "Egalitarian", "monochronic": "Monochronic", "polychronic": "Polychronic", "flexible": "Flexible"}, "social": {"title": "Social Dynamics", "individualism_collectivism": "Individualism vs Collectivism", "power_distance": "Power Distance", "uncertainty_avoidance": "Uncertainty Avoidance", "relationship_building": "Relationship Building", "individualistic": "Individualistic", "collectivistic": "Collectivistic", "low": "Low", "high": "High", "task_focused": "Task Focused", "balanced": "Balanced", "relationship_focused": "Relationship Focused"}, "household": {"title": "Household Cultural Preferences", "privacy_expectations": "Privacy Expectations", "family_involvement": "Family Involvement", "guest_hospitality": "Guest Hospitality", "conflict_resolution": "Conflict Resolution Style", "very_private": "Very Private", "private": "Private", "open": "Open", "very_open": "Very Open", "minimal": "Minimal", "significant": "Significant", "extensive": "Extensive", "formal": "Formal", "casual": "Casual", "very_casual": "Very Casual", "direct_confrontation": "Direct Confrontation", "mediated_discussion": "Mediated Discussion", "indirect_approach": "Indirect Approach", "avoidance": "Avoidance", "elder_intervention": "Elder Intervention"}, "ui": {"title": "UI/UX Cultural Preferences", "color_preferences": "Color Preferences", "layout_preference": "Layout Preference", "interaction_style": "Interaction Style", "feedback_preference": "Feedback Preference", "warm": "Warm", "cool": "Cool", "neutral": "Neutral", "vibrant": "Vibrant", "muted": "Muted", "rich": "<PERSON>", "decorative": "Decorative", "professional": "Professional", "friendly": "Friendly", "immediate": "Immediate", "delayed": "Delayed", "contextual": "Contextual", "subtle": "Subtle"}, "personality": {"title": "Personality-Cultural Integration", "adaptation_title": "Supportive Communication Cultural Adaptation", "adaptation_description": "Your supportive communication style aligns well with relationship-focused cultures. Consider enabling family involvement and indirect communication approaches for better cultural harmony.", "recommended": "Recommended: Relationship-focused settings"}, "assessment": {"title": "Cultural Assessment", "adaptation_score": "Adaptation Score", "cultural_satisfaction": "Cultural Satisfaction", "communication_effectiveness": "Communication Effectiveness", "household_harmony": "Household Harmony", "run_assessment": "Run Cultural Assessment", "running_assessment": "Running Assessment...", "assessment_completed": "Cultural assessment completed! Adaptation score: {{score}}%"}, "analytics": {"title": "Cultural Analytics", "adaptation_score": "Adaptation Score", "cultural_satisfaction": "Cultural Satisfaction", "communication": "Communication", "household_harmony": "Household Harmony", "feature_usage": "Feature Usage", "cultural_communication": "Cultural Communication", "household_adaptation": "Household Adaptation", "ui_customization": "UI Customization", "conflict_resolution": "Conflict Resolution", "cultural_events": "Cultural Events"}, "suggestions": {"title": "Smart Cultural Suggestions", "view_all": "View All", "high_impact": "HIGH IMPACT", "medium_impact": "MEDIUM IMPACT", "low_impact": "LOW IMPACT", "personality": "Personality", "confidence": "Confidence"}, "messages": {"loading": "Loading cultural settings...", "update_success": "Cultural setting updated successfully!", "update_error": "Failed to update cultural setting", "background_update_success": "Cultural background updated successfully!", "background_update_error": "Failed to update cultural background", "load_error": "Could not load cultural settings"}}, "voice_motor": {"title": "Voice & Motor Settings", "subtitle": "Voice commands • Motor accessibility", "voice": {"title": "Voice Commands", "enable_commands": "Enable Voice Commands", "enable_commands_desc": "Control the app using voice commands", "voice_navigation": "Voice Navigation", "voice_navigation_desc": "Navigate between screens using voice", "voice_feedback": "Voice Feedback", "voice_feedback_desc": "Hear confirmation when commands are executed", "voice_confirmation": "Voice Confirmation", "voice_confirmation_desc": "Require voice confirmation for actions", "voice_volume": "Voice Volume", "voice_language": "Voice Language", "voice_speed": "Voice Speed", "voice_pitch": "Voice Pitch", "test_command": "Test Voice Command", "testing": "Testing...", "manage": "Manage", "slow": "Slow", "normal": "Normal", "fast": "Fast", "low": "Low", "high": "High"}, "motor": {"title": "Motor Accessibility", "enable_assistance": "Motor Assistance", "enable_assistance_desc": "Enable motor accessibility features", "touch_accommodations": "Touch Accommodations", "touch_accommodations_desc": "Larger touch targets and hold delays", "gesture_alternatives": "Gesture Alternatives", "gesture_alternatives_desc": "Alternative ways to perform gestures", "one_handed_mode": "One-Handed Mode", "one_handed_mode_desc": "Optimize interface for one-handed use", "touch_target_size": "Touch Target Size", "touch_hold_delay": "Touch Hold Delay", "gesture_sensitivity": "Gesture Sensitivity", "double_tap_timeout": "Double Tap Timeout", "interaction_timeout": "Interaction Timeout", "auto_scroll": "Auto Scroll", "auto_scroll_desc": "Automatically scroll content", "auto_scroll_speed": "Auto Scroll Speed", "sticky_keys": "<PERSON><PERSON>", "sticky_keys_desc": "Keys remain active until pressed again", "preferred_hand": "Preferred Hand", "motor_impairment": "Motor Impairment Type", "small": "Small", "medium": "Medium", "large": "Large", "extra_large": "Extra Large", "left": "Left", "right": "Right", "both": "Both", "none": "None", "mild": "Mild", "moderate": "Moderate", "severe": "Severe", "test": "Test"}, "assistive": {"title": "Assistive Technology", "switch_control": "Switch Control", "switch_control_desc": "External switch device support", "external_switch": "External Switch Support", "external_switch_desc": "Support for external switch devices", "head_tracking": "Head Tracking", "head_tracking_desc": "Head movement navigation", "eye_tracking": "Eye Tracking", "eye_tracking_desc": "Eye tracking device integration", "screen_reader": "Screen Reader Optimization", "screen_reader_desc": "Optimize for screen readers", "assistive_device": "Assistive Device Type", "switch": "Switch", "joystick": "Joystick", "eye_tracker": "Eye Tracker", "head_tracker": "Head Tracker", "other": "Other"}, "personality": {"title": "Personality-Accessibility Integration", "extroverted_optimization": "Extroverted Voice Interaction Optimization", "extroverted_desc": "Your extroverted personality type (ENFP) benefits from conversational voice commands and interactive feedback. Consider enabling voice feedback and social voice features for better accessibility experience.", "introverted_optimization": "Efficient Voice Commands for Analytical Types", "introverted_desc": "Your analytical personality would benefit from precise, efficient voice commands with minimal feedback.", "personality_type": "Personality Type", "communication_style": "Communication Style", "recommended": "Recommended: Interactive voice features"}, "assessment": {"title": "Accessibility Assessment", "motor_effectiveness": "Motor Effectiveness", "voice_effectiveness": "Voice Effectiveness", "interaction_efficiency": "Interaction Efficiency", "run_assessment": "Run Accessibility Assessment", "running_assessment": "Running Assessment...", "assessment_complete": "Accessibility assessment completed!"}, "analytics": {"voice_usage": "Voice Usage", "motor_assistance": "Motor Assistance", "command_success": "Command Success", "interaction_efficiency": "Interaction Efficiency", "accessibility_satisfaction": "Accessibility Satisfaction", "daily_usage": "Daily Usage", "error_rate": "Error Rate", "feature_usage": "Feature Usage", "voice_commands": "Voice Commands", "switch_control": "Switch Control", "gesture_alternatives": "Gesture Alternatives", "voice_navigation": "Voice Navigation", "adaptation_trends": "Adaptation Trends", "weekly_improvement": "Weekly Improvement", "command_accuracy": "Command Accuracy", "motor_efficiency": "Motor Efficiency", "user_satisfaction": "User Satisfaction"}, "suggestions": {"title": "Smart Accessibility Suggestions", "voice_optimization": "Voice Command Optimization", "motor_assistance": "Motor Assistance Enhancement", "command_customization": "Command Customization", "interaction_improvement": "Interaction Improvement", "assistive_technology": "Assistive Technology Integration", "personality_adaptation": "Personality-Based Adaptation", "extroverted_voice": "Enhanced Voice Interaction for Social Types", "extroverted_voice_desc": "Your extroverted personality would benefit from conversational voice commands and social voice features.", "introverted_efficient": "Efficient Voice Commands for Analytical Types", "introverted_efficient_desc": "Your analytical personality would benefit from precise, efficient voice commands with minimal feedback.", "comprehensive_motor": "Comprehensive Motor Accessibility Support", "comprehensive_motor_desc": "Enable full motor accessibility features including switch control, gesture alternatives, and touch accommodations.", "voice_expansion": "Expand Voice Command Library", "voice_expansion_desc": "Add more voice commands for frequently used actions to improve efficiency and accessibility.", "assistive_integration": "Integrate Assistive Technology Support", "assistive_integration_desc": "Enable support for your assistive device to improve interaction efficiency and accessibility.", "view_all": "View All", "high_impact": "HIGH IMPACT", "medium_impact": "MEDIUM IMPACT", "low_impact": "LOW IMPACT", "personality": "Personality"}, "commands": {"go_home": "Go Home", "open_profile": "Open Profile", "open_messages": "Open Messages", "scroll_up": "Scroll Up", "scroll_down": "Scroll Down", "go_back": "Go Back", "read_content": "Read This", "open_settings": "Open Settings"}, "messages": {"loading": "Loading voice & motor settings...", "settings_updated": "Voice & motor setting updated successfully!", "command_executed": "Voice command executed successfully!", "command_not_recognized": "Voice command not recognized", "test_failed": "Failed to test voice command", "assessment_completed": "Accessibility assessment completed!", "assessment_failed": "Failed to run accessibility assessment", "error_loading": "Could not load voice and motor settings", "error_updating": "Failed to update voice & motor setting"}}, "regional_market": {"title": "Regional Market Settings", "subtitle": "Market adaptation • Compliance • Cultural preferences", "regional": {"title": "Regional Configuration", "current_region": "Current Region", "auto_detect": "Auto-Detect Region", "auto_detect_desc": "Automatically detect region from device settings", "region_override": "Region Override", "currency_display": "<PERSON><PERSON><PERSON><PERSON>", "date_format": "Date Format", "number_format": "Number Format", "time_zone": "Time Zone", "business_hours": "Business Hours", "change": "Change"}, "payment": {"title": "Payment Methods", "methods": "Payment Methods", "card": "Credit/Debit Card", "mobile_money": "Mobile Money", "bank_transfer": "Bank Transfer", "digital_wallet": "Digital Wallet", "cryptocurrency": "Cryptocurrency", "processing_time": "Processing Time", "popularity_score": "Popularity Score", "transaction_fees": "Transaction Fees", "no_methods": "No payment methods configured for this region"}, "compliance": {"title": "Compliance & Legal", "privacy_law": "Privacy Law", "data_retention": "Data Retention", "age_verification": "Age Verification", "consent_management": "Consent Management", "gdpr_compliance": "GDPR Compliance", "ccpa_compliance": "CCPA Compliance", "local_regulations": "Local Regulations", "business_practices": "Business Practices", "regional_requirements": "Regional Requirements", "mandatory": "Mandatory", "recommended": "Recommended", "optional": "Optional", "required": "Required", "not_required": "Not Required", "details": "Details", "days": "days"}, "cultural": {"title": "Cultural Adaptation", "communication_style": "Cultural Communication Style", "sensitivity_level": "Cultural Sensitivity Level", "local_customs": "Local Customs", "social_norms": "Social Norms", "business_etiquette": "Business Etiquette", "holiday_calendar": "Holiday Calendar", "working_days": "Working Days", "low": "Low", "medium": "Medium", "high": "High", "maximum": "Maximum"}, "personality": {"title": "Personality-Cultural Integration", "integration": "Personality Integration", "mbti_adaptation": "MBTI-Based Market Adaptation", "communication_adaptation": "Communication Style Adaptation", "cultural_preferences": "Cultural Preferences", "market_behavior": "Market Behavior Patterns", "extroverted_adaptation": "Your extroverted personality type (ENFP) benefits from culturally-adapted social features and community-focused market adaptations. The current cultural sensitivity level enhances your experience with local social norms and communication patterns.", "recommended_high": "Recommended: High cultural sensitivity"}, "assessment": {"title": "Market Assessment", "market_readiness": "Market Readiness", "compliance_score": "Compliance Score", "cultural_adaptation": "Cultural Adaptation", "payment_integration": "Payment Integration", "regulatory_compliance": "Regulatory Compliance", "localization_effectiveness": "Localization Effectiveness", "run_assessment": "Run Market Assessment", "running_assessment": "Running Assessment...", "assessment_complete": "Market assessment completed! Market readiness: {{score}}%"}, "analytics": {"market_penetration": "Market Penetration", "user_adoption": "User Adoption Rate", "payment_success": "Payment Success", "compliance_score": "Compliance Score", "cultural_effectiveness": "Cultural Adaptation", "regional_satisfaction": "Regional Satisfaction", "market_share": "Market Share", "competitive_position": "Competitive Position", "feature_usage": "Feature Usage", "market_trends": "Market Trends", "growth_rate": "Growth Rate", "acquisition_cost": "User Acquisition Cost", "lifetime_value": "Customer Lifetime Value", "churn_rate": "Churn Rate", "engagement_score": "Engagement Score"}, "suggestions": {"title": "Smart Market Suggestions", "payment_optimization": "Payment Method Optimization", "compliance_enhancement": "Compliance Enhancement", "cultural_adaptation": "Cultural Adaptation", "market_expansion": "Market Expansion", "regulatory_alignment": "Regulatory Alignment", "localization_improvement": "Localization Improvement", "social_cultural_adaptation": "Social Cultural Adaptation for Extroverted Types", "social_cultural_desc": "Your extroverted personality benefits from culturally-adapted social features and community-focused market adaptations.", "ethiopian_payment_optimization": "Ethiopian Payment Method Optimization", "ethiopian_payment_desc": "Optimize payment methods for the Ethiopian market with mobile money and local banking integration.", "gdpr_compliance_enhancement": "GDPR Compliance Enhancement", "gdpr_compliance_desc": "Enhance GDPR compliance features for European market operations and data protection.", "view_all": "View All", "high_impact": "HIGH IMPACT", "medium_impact": "MEDIUM IMPACT", "low_impact": "LOW IMPACT", "personality": "Personality"}, "regions": {"north_america": "North America", "europe": "Europe", "africa": "Africa", "asia_pacific": "Asia Pacific", "middle_east": "Middle East", "latin_america": "Latin America", "oceania": "Oceania"}, "countries": {"united_states": "United States", "canada": "Canada", "united_kingdom": "United Kingdom", "germany": "Germany", "france": "France", "ethiopia": "Ethiopia", "nigeria": "Nigeria", "south_africa": "South Africa", "japan": "Japan", "australia": "Australia", "brazil": "Brazil", "mexico": "Mexico"}, "messages": {"loading": "Loading regional market settings...", "region_switched": "Successfully switched to {{region}} market!", "payment_method_added": "Payment method added successfully", "compliance_updated": "Compliance settings updated", "cultural_settings_saved": "Cultural settings saved", "market_assessment_complete": "Market assessment completed!", "configuration_error": "Configuration error occurred", "region_detection_failed": "Failed to detect region automatically", "setting_updated": "Regional market setting updated successfully!", "setting_failed": "Failed to update regional market setting", "ethiopian_payment_configured": "Ethiopian payment optimization configured!", "gdpr_compliance_enhanced": "GDPR compliance enhanced!", "error_loading": "Could not load regional market settings", "error_switching": "Failed to switch region", "error_assessment": "Failed to run market assessment"}}}