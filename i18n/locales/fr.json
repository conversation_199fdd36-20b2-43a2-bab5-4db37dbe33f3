{"common": {"loading": "Chargement...", "save": "Enregistrer", "cancel": "Annuler", "delete": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Modifier", "add": "Ajouter", "remove": "<PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON><PERSON>", "filter": "<PERSON><PERSON><PERSON>", "refresh": "Actualiser", "back": "Retour", "next": "Suivant", "previous": "Précédent", "done": "<PERSON><PERSON><PERSON><PERSON>", "yes": "O<PERSON>", "no": "Non", "ok": "OK", "error": "<PERSON><PERSON><PERSON>", "success": "Su<PERSON>ès", "warning": "Avertissement", "info": "Information"}, "navigation": {"home": "Accueil", "profile": "Profil", "household": "<PERSON><PERSON><PERSON>", "messages": "Messages", "search": "Recherche", "settings": "Paramètres"}, "profile": {"title": "Profil", "edit": "Modifier le Profil", "completion": "Complétion du Profil", "interests": "Intérêts et Loisirs", "personality": "Évaluation de Personnalité", "lifestyle": "Préférences de Style de Vie", "verification": "Vérification et Confiance", "financial": "Gestion Financière", "roles": "<PERSON><PERSON><PERSON>"}, "household": {"title": "<PERSON><PERSON><PERSON>", "dashboard": "Tableau <PERSON>", "members": "Me<PERSON><PERSON>", "expenses": "<PERSON>é<PERSON>ses", "chores": "Tâches", "calendar": "<PERSON><PERSON><PERSON>", "communication": "Communication", "settings": "Paramètres", "health_score": "Score de Santé du Ménage", "satisfaction": "Score de Satisfaction"}, "communication": {"title": "Centre de Communication", "general_chat": "<PERSON><PERSON>", "announcements": "Annonces", "expenses_bills": "Dépenses et Factures", "social_events": "Événements Sociaux", "maintenance": "Maintenance", "send_message": "Envoyer un message", "type_message": "Tapez un message...", "online": "En ligne", "offline": "<PERSON><PERSON> ligne", "last_seen": "Vu pour la dernière fois"}, "settings": {"title": "Paramètres", "language": "Langue et Région", "notifications": "Notifications", "privacy": "Confidentialité", "accessibility": "Accessibilité", "appearance": "Apparence", "account": "<PERSON><PERSON><PERSON>", "about": "À propos"}, "personality": {"types": {"ENFP": "Le Militant", "ISFJ": "Le Protecteur", "INTJ": "L'Architecte", "ESTP": "L'Entrepreneur", "INFP": "Le Médiateur", "ESTJ": "L'Exécutif", "ISTP": "Le Virtuose", "ENFJ": "Le Protagoniste"}, "communication_styles": {"direct": "Direct", "diplomatic": "Diplomatique", "supportive": "Bienveillant", "analytical": "Analytique"}, "lifestyle_types": {"social_butterfly": "Papillon Social", "organized_planner": "Planificateur Organisé", "night_owl": "Oiseau de Nuit", "early_bird": "Lève-tôt", "minimalist": "Minimaliste", "creative": "<PERSON><PERSON><PERSON><PERSON>"}}, "expenses": {"title": "<PERSON>é<PERSON>ses", "add_expense": "A<PERSON>ter une Dépense", "split_expense": "Partager la Dépense", "pending": "En attente", "settled": "Réglé", "overdue": "En retard", "categories": {"groceries": "É<PERSON>rie", "utilities": "Services publics", "rent": "<PERSON><PERSON>", "internet": "Internet", "cleaning": "Nettoyage", "maintenance": "Maintenance", "social": "Social", "other": "<PERSON><PERSON>"}}, "chores": {"title": "Tâches", "add_chore": "A<PERSON>ter une Tâche", "assign_chore": "Assigner une <PERSON>â<PERSON>", "complete": "<PERSON><PERSON><PERSON>", "pending": "En attente", "overdue": "En retard", "categories": {"kitchen": "<PERSON><PERSON><PERSON><PERSON>", "bathroom": "Salle de bain", "living_room": "Salon", "general": "Général", "outdoor": "Extérieur"}}, "verification": {"title": "Vérification", "identity": "Vérification d'Identité", "background": "Vérification des Antécédents", "references": "Références", "trust_score": "Score de Confiance", "verified": "Vérifié", "pending": "En attente", "failed": "<PERSON><PERSON><PERSON>"}, "errors": {"network": "Erreur de connexion réseau", "server": "<PERSON><PERSON><PERSON> du <PERSON>", "validation": "Veuillez vérifier votre saisie", "permission": "Permission refusée", "not_found": "Ressource non trouvée", "timeout": "<PERSON><PERSON><PERSON>'attente d<PERSON><PERSON>"}, "success": {"saved": "Enregistré avec succès", "updated": "Mis à jour avec succès", "deleted": "Supprimé avec succès", "sent": "<PERSON><PERSON><PERSON> a<PERSON> su<PERSON>", "completed": "<PERSON><PERSON><PERSON><PERSON> avec succès"}, "accessibility": {"title": "Paramètres d'Accessibilité", "subtitle": "Conforme WCAG 2.1 • Basé sur la personnalité", "visual": {"title": "Accessibilité Visuelle", "high_contrast": "Mode Contraste Élevé", "high_contrast_desc": "Améliore la visibilité du texte et des éléments d'interface", "font_size": "<PERSON>lle de Police", "font_size_desc": "Actuel: {{percentage}}%", "reduce_transparency": "Réduire la Transparence", "reduce_transparency_desc": "Rend les éléments d'interface plus opaques", "color_blind_support": "Support pour Daltonisme"}, "motor": {"title": "Accessibilité Mo<PERSON>e", "touch_accommodations": "Adaptations Tactiles", "touch_accommodations_desc": "Cibles tactiles plus grandes et délais de maintien", "gesture_alternatives": "Alternatives de Gestes", "gesture_alternatives_desc": "Alternatives de boutons pour gestes complexes", "voice_control": "Contrôle Vocal", "voice_control_desc": "Naviguer en utilisant des commandes vocales"}, "cognitive": {"title": "Accessibilité Cognitive", "reduce_motion": "Réduire le Mouvement", "reduce_motion_desc": "Minimise les animations et transitions", "simplified_interface": "Interface Simplifi<PERSON>", "simplified_interface_desc": "Réduit la complexité visuelle", "focus_indicators": "Indicateurs de Focus Améliorés", "focus_indicators_desc": "Contours de focus plus clairs pour la navigation", "timeout_extensions": "Extensions de Délai", "timeout_extensions_desc": "Plus de temps pour les interactions"}, "audio": {"title": "Accessibilité Audio et Haptique", "audio_descriptions": "Descriptions Audio", "audio_descriptions_desc": "Descriptions parlées du contenu visuel", "sound_feedback": "Retour Sonore", "sound_feedback_desc": "Signaux audio pour les interactions", "haptic_feedback": "Retour Haptique", "haptic_feedback_desc": "Vibration pour les interactions tactiles"}, "personality": {"title": "Accessibilité Basée sur la Personnalité", "adaptations": "Adaptations de Personnalité", "adaptations_desc": "Personnaliser selon votre type de personnalité", "communication_style": "Adaptation du Style de Communication", "supportive_desc": "Votre style de communication de soutien bénéficie de retours doux, de délais étendus et de signaux audio clairs pour une expérience confortable."}, "wcag": {"title": "Conformité WCAG", "aa_compliance": "Conformité AA", "aaa_compliance": "Conformité AAA", "run_test": "Exécuter Test d'Accessibilité", "running_test": "Exécution du Test...", "test_completed": "Test d'accessibilité terminé ! Vérifiez les résultats pour plus de détails."}, "analytics": {"compliance_score": "Score de Conformité", "satisfaction": "Satisfaction", "feature_usage": "Utilisation des Fonctionnalités", "last_audit": "<PERSON><PERSON>"}, "suggestions": {"title": "Suggestions Intelligentes d'Accessibilité", "view_all": "Voir Tout", "high_impact": "IMPACT ÉLEVÉ", "medium_impact": "IMPACT MOYEN", "low_impact": "FAIBLE IMPACT", "personality": "Personnalité"}}, "cultural": {"title": "Paramètres Culturels", "subtitle": "Adaptation culturelle avec intégration de personnalité", "identity": {"title": "Identité Culturelle", "cultural_background": "Origine Culturelle", "primary_culture": "Culture Principale", "cultural_fluency": "<PERSON><PERSON><PERSON><PERSON>", "select_cultures": "Sélectionnez vos origines culturelles", "cultures_selected": "culture(s) sélectionnée(s)", "not_specified": "Non spécifié"}, "communication": {"title": "Modèles de Communication", "context": "Contexte de Communication", "directness": "Préférence de Directivité", "hierarchy": "Orientation Hiérarchique", "time_orientation": "Orientation Temporelle", "high_context": "Con<PERSON><PERSON>", "low_context": "Contexte <PERSON>", "mixed": "Mixte", "very_direct": "Très Direct", "direct": "Direct", "moderate": "<PERSON><PERSON><PERSON><PERSON>", "indirect": "Indirect", "very_indirect": "Très Indirect", "hierarchical": "Hiérarchique", "egalitarian": "Égalitaire", "monochronic": "Monochronique", "polychronic": "Polychronique", "flexible": "Flexible"}, "social": {"title": "Dynamiques Sociales", "individualism_collectivism": "Individualisme vs Collectivisme", "power_distance": "Distance de Pouvoir", "uncertainty_avoidance": "Évitement de l'Incertitude", "relationship_building": "Construction de Relations", "individualistic": "Individualiste", "collectivistic": "Collectiviste", "low": "Faible", "high": "<PERSON><PERSON><PERSON>", "task_focused": "Axé sur les Tâches", "balanced": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "relationship_focused": "Axé sur les Relations"}, "household": {"title": "Préférences Culturelles du Foyer", "privacy_expectations": "Attentes de Confidentialité", "family_involvement": "Implication Familiale", "guest_hospitality": "Hospitalité des Invités", "conflict_resolution": "Style de Résolution de Conflits", "very_private": "<PERSON><PERSON><PERSON>", "private": "Priv<PERSON>", "open": "Ouvert", "very_open": "<PERSON><PERSON><PERSON>", "minimal": "Minimale", "significant": "Significative", "extensive": "<PERSON><PERSON><PERSON>", "formal": "Formel", "casual": "Décontracté", "very_casual": "<PERSON><PERSON>ès Décon<PERSON>", "direct_confrontation": "Confrontation Directe", "mediated_discussion": "Discussion Médiée", "indirect_approach": "Approche Indirecte", "avoidance": "Évitement", "elder_intervention": "Intervention des Aînés"}, "ui": {"title": "Préférences Culturelles UI/UX", "color_preferences": "Préférences de Couleur", "layout_preference": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> en Page", "interaction_style": "Style d'Interaction", "feedback_preference": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "warm": "<PERSON><PERSON>", "cool": "Froid", "neutral": "Neutre", "vibrant": "Vibrant", "muted": "<PERSON><PERSON><PERSON><PERSON>", "rich": "<PERSON><PERSON>", "decorative": "Décoratif", "professional": "Professionnel", "friendly": "Amical", "immediate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "delayed": "Retardé", "contextual": "Contextuel", "subtle": "<PERSON><PERSON>"}, "personality": {"title": "Intégration Personnalité-Culturelle", "adaptation_title": "Adaptation Culturelle de Communication de Soutien", "adaptation_description": "Votre style de communication de soutien s'aligne bien avec les cultures axées sur les relations. Consid<PERSON>rez activer l'implication familiale et les approches de communication indirecte pour une meilleure harmonie culturelle.", "recommended": "Recommandé : Paramètres axés sur les relations"}, "assessment": {"title": "Évaluation Culturelle", "adaptation_score": "Score d'Adaptation", "cultural_satisfaction": "Satisfaction Culturelle", "communication_effectiveness": "Efficacité de Communication", "household_harmony": "<PERSON><PERSON><PERSON>", "run_assessment": "Exécuter l'Évaluation Culturelle", "running_assessment": "Exécution de l'Évaluation...", "assessment_completed": "Évaluation culturelle terminée ! Score d'adaptation : {{score}}%"}, "analytics": {"title": "<PERSON><PERSON><PERSON>", "adaptation_score": "Score d'Adaptation", "cultural_satisfaction": "Satisfaction Culturelle", "communication": "Communication", "household_harmony": "<PERSON><PERSON><PERSON>", "feature_usage": "Utilisation des Fonctionnalités", "cultural_communication": "Communication Culturelle", "household_adaptation": "Adapta<PERSON> <PERSON> Foyer", "ui_customization": "Personnalisation UI", "conflict_resolution": "Résolution de Conflits", "cultural_events": "Événements Culturels"}, "suggestions": {"title": "Suggestions Culturelles Intelligentes", "view_all": "Voir Tout", "high_impact": "IMPACT ÉLEVÉ", "medium_impact": "IMPACT MOYEN", "low_impact": "FAIBLE IMPACT", "personality": "Personnalité", "confidence": "Confiance"}, "messages": {"loading": "Chargement des paramètres culturels...", "update_success": "Paramètre culturel mis à jour avec succès !", "update_error": "Échec de la mise à jour du paramètre culturel", "background_update_success": "Origines culturelles mises à jour avec succès !", "background_update_error": "Échec de la mise à jour des origines culturelles", "load_error": "Impossible de charger les paramètres culturels"}}, "voice_motor": {"title": "Paramètres Vocaux et Moteurs", "subtitle": "Commandes vocales • Accessibilité motrice", "voice": {"title": "Commandes Vocales", "enable_commands": "Activer les Commandes Vocales", "enable_commands_desc": "Contrôler l'application avec des commandes vocales", "voice_navigation": "Navigation Vocale", "voice_navigation_desc": "Naviguer entre les écrans avec la voix", "voice_feedback": "Retour Vocal", "voice_feedback_desc": "Entendre la <PERSON> lors de l'exécution des commandes", "voice_confirmation": "Confirmation Vocale", "voice_confirmation_desc": "Exiger une confirmation vocale pour les actions", "voice_volume": "Volume Vocal", "voice_language": "<PERSON>ue Vocale", "voice_speed": "Vitesse Vocale", "voice_pitch": "Tonalité Vocale", "test_command": "Tester <PERSON><PERSON>e", "testing": "Test en cours...", "manage": "<PERSON><PERSON><PERSON>", "slow": "<PERSON><PERSON>", "normal": "Normal", "fast": "Rapide", "low": "Bas", "high": "<PERSON><PERSON>"}, "motor": {"title": "Accessibilité Mo<PERSON>e", "enable_assistance": "Assistance Motrice", "enable_assistance_desc": "Activer les fonctionnalités d'accessibilité motrice", "touch_accommodations": "Adaptations Tactiles", "touch_accommodations_desc": "Cibles tactiles plus grandes et délais de maintien", "gesture_alternatives": "Alternatives de Gestes", "gesture_alternatives_desc": "Façons alternatives d'effectuer des gestes", "one_handed_mode": "Mode Une Main", "one_handed_mode_desc": "Optimiser l'interface pour l'utilisation d'une main", "touch_target_size": "<PERSON><PERSON> de la Cible Tactile", "touch_hold_delay": "<PERSON><PERSON><PERSON>", "gesture_sensitivity": "Sensibilité des Gestes", "double_tap_timeout": "<PERSON><PERSON><PERSON>", "interaction_timeout": "<PERSON><PERSON><PERSON> d'Interaction", "auto_scroll": "Défilement Automatique", "auto_scroll_desc": "Faire défiler le contenu automatiquement", "auto_scroll_speed": "Vitesse de Défilement Automatique", "sticky_keys": "Touches Collantes", "sticky_keys_desc": "Les touches restent actives jusqu'à être pressées à nouveau", "preferred_hand": "Main Préférée", "motor_impairment": "Type de Déficience Motrice", "small": "<PERSON>", "medium": "<PERSON><PERSON><PERSON>", "large": "Grand", "extra_large": "<PERSON><PERSON><PERSON>", "left": "G<PERSON><PERSON>", "right": "<PERSON><PERSON><PERSON>", "both": "<PERSON>", "none": "Aucune", "mild": "Légère", "moderate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "severe": "<PERSON><PERSON>v<PERSON>", "test": "Tester"}, "assistive": {"title": "Technologie d'Assistance", "switch_control": "Contrôle par Commutateur", "switch_control_desc": "Support pour dispositifs de commutateur externe", "external_switch": "Support de Commutateur Externe", "external_switch_desc": "Support pour dispositifs de commutateur externe", "head_tracking": "<PERSON><PERSON><PERSON>", "head_tracking_desc": "Navigation par mouvement de tête", "eye_tracking": "Suivi Oculaire", "eye_tracking_desc": "Intégration de dispositifs de suivi oculaire", "screen_reader": "Optimisation pour Lecteur d'Écran", "screen_reader_desc": "Optimiser pour les lecteurs d'écran", "assistive_device": "Type de Dispositif d'Assistance", "switch": "Commutateur", "joystick": "Joystick", "eye_tracker": "<PERSON><PERSON><PERSON>", "head_tracker": "<PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON>"}, "personality": {"title": "Intégration Personnalité-Accessibilité", "extroverted_optimization": "Optimisation d'Interaction Vocale Extravertie", "extroverted_desc": "Votre type de personnalité extravertie (ENFP) bénéficie de commandes vocales conversationnelles et de retours interactifs. Consid<PERSON>rez activer le retour vocal et les fonctionnalités vocales sociales pour une meilleure expérience d'accessibilité.", "introverted_optimization": "Commandes Vocales Efficaces pour Types Analytiques", "introverted_desc": "Votre personnalité analytique bénéficierait de commandes vocales précises et efficaces avec un retour minimal.", "personality_type": "Type de Personnalité", "communication_style": "Style de Communication", "recommended": "Recommandé : Fonctionnalités vocales interactives"}, "assessment": {"title": "Évaluation d'Accessibilité", "motor_effectiveness": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "voice_effectiveness": "Efficacité Vocale", "interaction_efficiency": "Efficacité d'Interaction", "run_assessment": "Exécuter l'Évaluation d'Accessibilité", "running_assessment": "Exécution de l'Évaluation...", "assessment_complete": "Évaluation d'accessibilité terminée !"}, "analytics": {"voice_usage": "Utilisation Vocale", "motor_assistance": "Assistance Motrice", "command_success": "Succès des Commandes", "interaction_efficiency": "Efficacité d'Interaction", "accessibility_satisfaction": "Satisfaction d'Accessibilité", "daily_usage": "Utilisation Quotidienne", "error_rate": "<PERSON><PERSON>", "feature_usage": "Utilisation des Fonctionnalités", "voice_commands": "Commandes Vocales", "switch_control": "Contrôle par Commutateur", "gesture_alternatives": "Alternatives de Gestes", "voice_navigation": "Navigation Vocale", "adaptation_trends": "Tendances d'Adaptation", "weekly_improvement": "Amélioration Hebdomadaire", "command_accuracy": "Précision des Commandes", "motor_efficiency": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "user_satisfaction": "Satisfaction Utilisateur"}, "suggestions": {"title": "Suggestions Intelligentes d'Accessibilité", "voice_optimization": "Optimisation des Commandes Vocales", "motor_assistance": "Amélioration de l'Assistance Motrice", "command_customization": "Personnalisation des Commandes", "interaction_improvement": "Amélioration de l'Interaction", "assistive_technology": "Intégration de Technologie d'Assistance", "personality_adaptation": "Adaptation Basée sur la Personnalité", "extroverted_voice": "Interaction Vocale Améliorée pour Types Sociaux", "extroverted_voice_desc": "Votre personnalité extravertie bénéficierait de commandes vocales conversationnelles et de fonctionnalités vocales sociales.", "introverted_efficient": "Commandes Vocales Efficaces pour Types Analytiques", "introverted_efficient_desc": "Votre personnalité analytique bénéficierait de commandes vocales précises et efficaces avec un retour minimal.", "comprehensive_motor": "Support Complet d'Accessibilité Motrice", "comprehensive_motor_desc": "Activer les fonctionnalités complètes d'accessibilité motrice incluant le contrôle par commutateur, les alternatives de gestes et les adaptations tactiles.", "voice_expansion": "Étendre la Bibliothèque de Commandes Vocales", "voice_expansion_desc": "Ajouter plus de commandes vocales pour les actions fréquentes pour améliorer l'efficacité et l'accessibilité.", "assistive_integration": "Intégrer le Support de Technologie d'Assistance", "assistive_integration_desc": "Activer le support pour votre dispositif d'assistance pour améliorer l'efficacité d'interaction et l'accessibilité.", "view_all": "Voir Tout", "high_impact": "IMPACT ÉLEVÉ", "medium_impact": "IMPACT MOYEN", "low_impact": "FAIBLE IMPACT", "personality": "Personnalité"}, "commands": {"go_home": "Aller à l'Accueil", "open_profile": "<PERSON><PERSON><PERSON><PERSON><PERSON> le Profil", "open_messages": "<PERSON><PERSON><PERSON><PERSON>r les Messages", "scroll_up": "Défiler Vers le Haut", "scroll_down": "Défiler Vers le Bas", "go_back": "Retour", "read_content": "<PERSON><PERSON>", "open_settings": "<PERSON><PERSON><PERSON><PERSON>r les Paramètres"}, "messages": {"loading": "Chargement des paramètres vocaux et moteurs...", "settings_updated": "Paramètre vocal et moteur mis à jour avec succès !", "command_executed": "Commande vocale exécutée avec succès !", "command_not_recognized": "Commande vocale non reconnue", "test_failed": "Échec du test de commande vocale", "assessment_completed": "Évaluation d'accessibilité terminée !", "assessment_failed": "Échec de l'exécution de l'évaluation d'accessibilité", "error_loading": "Impossible de charger les paramètres vocaux et moteurs", "error_updating": "Échec de la mise à jour des paramètres vocaux et moteurs"}}, "regional_market": {"title": "Paramètres de Marché Régional", "subtitle": "Adaptation de marché • Conformité • Préférences culturelles", "regional": {"title": "Configuration Régionale", "current_region": "Région Actuelle", "auto_detect": "Détection Automatique de Région", "auto_detect_desc": "Détecter automatiquement la région depuis les paramètres de l'appareil", "region_override": "Remplacer la Région", "currency_display": "Affichage de Devise", "date_format": "Format de Date", "number_format": "Format de Nombre", "time_zone": "<PERSON><PERSON>", "business_hours": "Heures d'Ouverture", "change": "Changer"}, "payment": {"title": "Méthodes de Paiement", "methods": "Méthodes de Paiement", "card": "Carte de Crédit/Débit", "mobile_money": "Argent Mobile", "bank_transfer": "Virement Bancaire", "digital_wallet": "Portefeuille Numérique", "cryptocurrency": "Cryptomonnaie", "processing_time": "Temps de Traitement", "popularity_score": "Score de Popularité", "transaction_fees": "Frais de Transaction", "no_methods": "Aucune méthode de paiement configurée pour cette région"}, "compliance": {"title": "Conformité et Légal", "privacy_law": "Loi sur la Confidentialité", "data_retention": "Rétention des Données", "age_verification": "Vérification d'Âge", "consent_management": "Gestion du Consentement", "gdpr_compliance": "Conformité RGPD", "ccpa_compliance": "Conformité CCPA", "local_regulations": "Réglementations Locales", "business_practices": "Pratiques Commerciales", "regional_requirements": "Exigences Régionales", "mandatory": "Obligatoire", "recommended": "Recommandé", "optional": "Optionnel", "required": "Requis", "not_required": "Non Requis", "details": "Détails", "days": "jours"}, "cultural": {"title": "Adaptation Culturelle", "communication_style": "Style de Communication Culturel", "sensitivity_level": "Niveau de Sensibilité Culturelle", "local_customs": "Coutumes Locales", "social_norms": "Normes Sociales", "business_etiquette": "Étiquette Commerciale", "holiday_calendar": "Calendrier des Fêtes", "working_days": "<PERSON><PERSON>", "low": "Faible", "medium": "<PERSON><PERSON><PERSON>", "high": "<PERSON><PERSON><PERSON>", "maximum": "Maximum"}, "personality": {"title": "Intégration Personnalité-Culturelle", "integration": "Intégration de Personnalité", "mbti_adaptation": "Adaptation de Marché Basée sur MBTI", "communication_adaptation": "Adaptation du Style de Communication", "cultural_preferences": "Préférences Culturelles", "market_behavior": "Modèles de Comportement de Marché", "extroverted_adaptation": "Votre type de personnalité extravertie (ENFP) bénéficie de fonctionnalités sociales adaptées culturellement et d'adaptations de marché axées sur la communauté. Le niveau actuel de sensibilité culturelle améliore votre expérience avec les normes sociales locales et les modèles de communication.", "recommended_high": "Recommandé : Sensibilité culturelle élevée"}, "assessment": {"title": "Évaluation de Marché", "market_readiness": "Préparation du Marché", "compliance_score": "Score de Conformité", "cultural_adaptation": "Adaptation Culturelle", "payment_integration": "Intégration de Paiement", "regulatory_compliance": "Conformité Réglementaire", "localization_effectiveness": "Efficacité de Localisation", "run_assessment": "Exécuter l'Évaluation de Marché", "running_assessment": "Exécution de l'Évaluation...", "assessment_complete": "Évaluation de marché terminée ! Préparation du marché : {{score}}%"}, "analytics": {"market_penetration": "Pénétration de Marché", "user_adoption": "Taux d'Adoption Utilisateur", "payment_success": "Succès de Paiement", "compliance_score": "Score de Conformité", "cultural_effectiveness": "Adaptation Culturelle", "regional_satisfaction": "Satisfaction Régionale", "market_share": "Part de Marché", "competitive_position": "Position Concurrentielle", "feature_usage": "Utilisation des Fonctionnalités", "market_trends": "Tendances du Marché", "growth_rate": "<PERSON><PERSON>", "acquisition_cost": "Coût d'Acquisition Utilisateur", "lifetime_value": "Valeur Vie Client", "churn_rate": "<PERSON>x d'At<PERSON>", "engagement_score": "Score d'Engagement"}, "suggestions": {"title": "Suggestions Intelligentes de Marché", "payment_optimization": "Optimisation des Méthodes de Paiement", "compliance_enhancement": "Amélioration de la Conformité", "cultural_adaptation": "Adaptation Culturelle", "market_expansion": "Expansion de Marché", "regulatory_alignment": "Alignement Réglementaire", "localization_improvement": "Amélioration de la Localisation", "social_cultural_adaptation": "Adaptation Culturelle Sociale pour Types Extravertis", "social_cultural_desc": "Votre personnalité extravertie bénéficie de fonctionnalités sociales adaptées culturellement et d'adaptations de marché axées sur la communauté.", "ethiopian_payment_optimization": "Optimisation des Méthodes de Paiement Éthiopiennes", "ethiopian_payment_desc": "Optimiser les méthodes de paiement pour le marché éthiopien avec l'argent mobile et l'intégration bancaire locale.", "gdpr_compliance_enhancement": "Amélioration de la Conformité RGPD", "gdpr_compliance_desc": "Améliorer les fonctionnalités de conformité RGPD pour les opérations du marché européen et la protection des données.", "view_all": "Voir Tout", "high_impact": "IMPACT ÉLEVÉ", "medium_impact": "IMPACT MOYEN", "low_impact": "FAIBLE IMPACT", "personality": "Personnalité"}, "regions": {"north_america": "Amérique du Nord", "europe": "Europe", "africa": "Afrique", "asia_pacific": "Asie-Pacifique", "middle_east": "Moyen-Orient", "latin_america": "Amérique <PERSON>", "oceania": "<PERSON><PERSON><PERSON><PERSON>"}, "countries": {"united_states": "États-Unis", "canada": "Canada", "united_kingdom": "Royaume-Uni", "germany": "Allemagne", "france": "France", "ethiopia": "Éthiopie", "nigeria": "Nigeria", "south_africa": "Afrique du Sud", "japan": "Japon", "australia": "Australie", "brazil": "Brésil", "mexico": "Mexique"}, "messages": {"loading": "Chargement des paramètres de marché régional...", "region_switched": "Basculement réussi vers le marché {{region}} !", "payment_method_added": "Méthode de paiement ajoutée avec succès", "compliance_updated": "Paramètres de conformité mis à jour", "cultural_settings_saved": "Paramètres culturels sauvegardés", "market_assessment_complete": "Évaluation de marché terminée !", "configuration_error": "Erreur de configuration survenue", "region_detection_failed": "Échec de la détection automatique de région", "setting_updated": "Paramètre de marché régional mis à jour avec succès !", "setting_failed": "Échec de la mise à jour du paramètre de marché régional", "ethiopian_payment_configured": "Optimisation des paiements éthiopiens configurée !", "gdpr_compliance_enhanced": "Conformité RGPD améliorée !", "error_loading": "Impossible de charger les paramètres de marché régional", "error_switching": "Échec du changement de région", "error_assessment": "Échec de l'exécution de l'évaluation de marché"}}}