import { I18n } from 'i18n-js';
import * as Localization from 'expo-localization';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Import translations
import en from './locales/en.json';
import es from './locales/es.json';
import fr from './locales/fr.json';

// Create i18n instance
const i18n = new I18n({
  en,
  es,
  fr,
});

// Language configuration
export interface LanguageConfig {
  code: string;
  name: string;
  nativeName: string;
  flag: string;
  rtl: boolean;
  region?: string;
  currency?: string;
  dateFormat?: string;
  numberFormat?: {
    decimal: string;
    thousands: string;
  };
}

export const SUPPORTED_LANGUAGES: LanguageConfig[] = [
  {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸',
    rtl: false,
    region: 'US',
    currency: 'USD',
    dateFormat: 'MM/DD/YYYY',
    numberFormat: {
      decimal: '.',
      thousands: ',',
    },
  },
  {
    code: 'es',
    name: 'Spanish',
    nativeName: 'Español',
    flag: '🇪🇸',
    rtl: false,
    region: 'ES',
    currency: 'EUR',
    dateFormat: 'DD/MM/YYYY',
    numberFormat: {
      decimal: ',',
      thousands: '.',
    },
  },
  {
    code: 'fr',
    name: 'French',
    nativeName: 'Français',
    flag: '🇫🇷',
    rtl: false,
    region: 'FR',
    currency: 'EUR',
    dateFormat: 'DD/MM/YYYY',
    numberFormat: {
      decimal: ',',
      thousands: ' ',
    },
  },
];

// Regional variants
export const REGIONAL_VARIANTS: Record<string, LanguageConfig[]> = {
  en: [
    {
      code: 'en-US',
      name: 'English (United States)',
      nativeName: 'English (US)',
      flag: '🇺🇸',
      rtl: false,
      region: 'US',
      currency: 'USD',
      dateFormat: 'MM/DD/YYYY',
      numberFormat: { decimal: '.', thousands: ',' },
    },
    {
      code: 'en-GB',
      name: 'English (United Kingdom)',
      nativeName: 'English (UK)',
      flag: '🇬🇧',
      rtl: false,
      region: 'GB',
      currency: 'GBP',
      dateFormat: 'DD/MM/YYYY',
      numberFormat: { decimal: '.', thousands: ',' },
    },
    {
      code: 'en-CA',
      name: 'English (Canada)',
      nativeName: 'English (Canada)',
      flag: '🇨🇦',
      rtl: false,
      region: 'CA',
      currency: 'CAD',
      dateFormat: 'DD/MM/YYYY',
      numberFormat: { decimal: '.', thousands: ',' },
    },
  ],
  es: [
    {
      code: 'es-ES',
      name: 'Spanish (Spain)',
      nativeName: 'Español (España)',
      flag: '🇪🇸',
      rtl: false,
      region: 'ES',
      currency: 'EUR',
      dateFormat: 'DD/MM/YYYY',
      numberFormat: { decimal: ',', thousands: '.' },
    },
    {
      code: 'es-MX',
      name: 'Spanish (Mexico)',
      nativeName: 'Español (México)',
      flag: '🇲🇽',
      rtl: false,
      region: 'MX',
      currency: 'MXN',
      dateFormat: 'DD/MM/YYYY',
      numberFormat: { decimal: '.', thousands: ',' },
    },
    {
      code: 'es-AR',
      name: 'Spanish (Argentina)',
      nativeName: 'Español (Argentina)',
      flag: '🇦🇷',
      rtl: false,
      region: 'AR',
      currency: 'ARS',
      dateFormat: 'DD/MM/YYYY',
      numberFormat: { decimal: ',', thousands: '.' },
    },
  ],
  fr: [
    {
      code: 'fr-FR',
      name: 'French (France)',
      nativeName: 'Français (France)',
      flag: '🇫🇷',
      rtl: false,
      region: 'FR',
      currency: 'EUR',
      dateFormat: 'DD/MM/YYYY',
      numberFormat: { decimal: ',', thousands: ' ' },
    },
    {
      code: 'fr-CA',
      name: 'French (Canada)',
      nativeName: 'Français (Canada)',
      flag: '🇨🇦',
      rtl: false,
      region: 'CA',
      currency: 'CAD',
      dateFormat: 'DD/MM/YYYY',
      numberFormat: { decimal: ',', thousands: ' ' },
    },
  ],
};

// Storage keys
const LANGUAGE_STORAGE_KEY = '@language_preference';
const REGION_STORAGE_KEY = '@region_preference';

// Language detection and management
export class LanguageManager {
  private static instance: LanguageManager;
  private currentLanguage: LanguageConfig;
  private listeners: ((language: LanguageConfig) => void)[] = [];

  private constructor() {
    this.currentLanguage = SUPPORTED_LANGUAGES[0]; // Default to English
  }

  static getInstance(): LanguageManager {
    if (!LanguageManager.instance) {
      LanguageManager.instance = new LanguageManager();
    }
    return LanguageManager.instance;
  }

  async initialize(): Promise<void> {
    try {
      // Try to get saved language preference
      const savedLanguage = await AsyncStorage.getItem(LANGUAGE_STORAGE_KEY);
      const savedRegion = await AsyncStorage.getItem(REGION_STORAGE_KEY);

      if (savedLanguage) {
        const language = this.findLanguageByCode(savedLanguage);
        if (language) {
          this.setLanguage(language);
          return;
        }
      }

      // Auto-detect language from device
      const deviceLocales = Localization.locales;
      const deviceLanguage = deviceLocales[0]?.languageCode;
      const deviceRegion = deviceLocales[0]?.regionCode;

      if (deviceLanguage) {
        // Try to find exact match with region
        const exactMatch = this.findLanguageByCode(`${deviceLanguage}-${deviceRegion}`);
        if (exactMatch) {
          this.setLanguage(exactMatch);
          return;
        }

        // Try to find language match
        const languageMatch = this.findLanguageByCode(deviceLanguage);
        if (languageMatch) {
          this.setLanguage(languageMatch);
          return;
        }
      }

      // Fallback to English
      this.setLanguage(SUPPORTED_LANGUAGES[0]);
    } catch (error) {
      console.error('Error initializing language manager:', error);
      this.setLanguage(SUPPORTED_LANGUAGES[0]);
    }
  }

  private findLanguageByCode(code: string): LanguageConfig | null {
    // Check main languages
    const mainLanguage = SUPPORTED_LANGUAGES.find(lang => lang.code === code);
    if (mainLanguage) return mainLanguage;

    // Check regional variants
    for (const variants of Object.values(REGIONAL_VARIANTS)) {
      const variant = variants.find(lang => lang.code === code);
      if (variant) return variant;
    }

    // Check base language (e.g., 'en' for 'en-US')
    const baseCode = code.split('-')[0];
    return SUPPORTED_LANGUAGES.find(lang => lang.code === baseCode) || null;
  }

  async setLanguage(language: LanguageConfig): Promise<void> {
    try {
      this.currentLanguage = language;

      // Set i18n locale
      const baseCode = language.code.split('-')[0];
      i18n.locale = baseCode;

      // Save preference
      await AsyncStorage.setItem(LANGUAGE_STORAGE_KEY, language.code);
      if (language.region) {
        await AsyncStorage.setItem(REGION_STORAGE_KEY, language.region);
      }

      // Notify listeners
      this.listeners.forEach(listener => listener(language));
    } catch (error) {
      console.error('Error setting language:', error);
    }
  }

  getCurrentLanguage(): LanguageConfig {
    return this.currentLanguage;
  }

  getSupportedLanguages(): LanguageConfig[] {
    return SUPPORTED_LANGUAGES;
  }

  getRegionalVariants(languageCode: string): LanguageConfig[] {
    return REGIONAL_VARIANTS[languageCode] || [];
  }

  addLanguageChangeListener(listener: (language: LanguageConfig) => void): () => void {
    this.listeners.push(listener);
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  // Personality-based language suggestions
  getPersonalityBasedSuggestions(
    personalityType?: string,
    communicationStyle?: string
  ): {
    formalityLevel: 'formal' | 'casual' | 'mixed';
    suggestedVariants: LanguageConfig[];
    reasoning: string;
  } {
    const currentBase = this.currentLanguage.code.split('-')[0];
    const variants = this.getRegionalVariants(currentBase);

    let formalityLevel: 'formal' | 'casual' | 'mixed' = 'mixed';
    let reasoning = 'Based on your communication preferences';

    // Personality-based formality suggestions
    if (personalityType) {
      if (['INTJ', 'ISTJ', 'ESTJ'].includes(personalityType)) {
        formalityLevel = 'formal';
        reasoning = `${personalityType} personalities often prefer formal communication`;
      } else if (['ENFP', 'ESFP', 'ESTP'].includes(personalityType)) {
        formalityLevel = 'casual';
        reasoning = `${personalityType} personalities often prefer casual communication`;
      }
    }

    // Communication style override
    if (communicationStyle) {
      if (communicationStyle === 'analytical' || communicationStyle === 'direct') {
        formalityLevel = 'formal';
        reasoning = `${communicationStyle} communication style suggests formal language`;
      } else if (communicationStyle === 'supportive') {
        formalityLevel = 'casual';
        reasoning = `${communicationStyle} communication style suggests casual language`;
      }
    }

    return {
      formalityLevel,
      suggestedVariants: variants,
      reasoning,
    };
  }

  // Cultural adaptation helpers
  formatCurrency(amount: number): string {
    const { currency, numberFormat } = this.currentLanguage;
    if (!currency || !numberFormat) return amount.toString();

    try {
      return new Intl.NumberFormat(this.currentLanguage.code, {
        style: 'currency',
        currency: currency,
      }).format(amount);
    } catch {
      return `${currency} ${amount.toFixed(2)}`;
    }
  }

  formatDate(date: Date): string {
    const { dateFormat } = this.currentLanguage;
    if (!dateFormat) return date.toLocaleDateString();

    try {
      return new Intl.DateTimeFormat(this.currentLanguage.code).format(date);
    } catch {
      return date.toLocaleDateString();
    }
  }

  formatNumber(number: number): string {
    const { numberFormat } = this.currentLanguage;
    if (!numberFormat) return number.toString();

    try {
      return new Intl.NumberFormat(this.currentLanguage.code).format(number);
    } catch {
      return number.toString();
    }
  }
}

// Translation function
export const t = (key: string, options?: any): string => {
  return i18n.t(key, options);
};

// Export i18n instance and manager
export { i18n };
export const languageManager = LanguageManager.getInstance();

// Initialize on import
languageManager.initialize();
