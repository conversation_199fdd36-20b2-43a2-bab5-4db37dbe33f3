-- Migration File: 20250105_safe_verification_enhancement.sql
-- MCP Analysis Completed: 2025-01-05
-- DRY Validation: PASSED - Working with existing tables
-- Redundancy Check: CLEAN - Enhancing existing infrastructure
-- Reviewed By: MCP_ROOMMATE_DBL_VERSION
-- 
-- EXISTING ARTIFACTS ANALYSIS:
-- Similar Tables: verification_requests, background_checks, email_verifications, identity_verifications (ALL EXIST)
-- Overlapping Indexes: Multiple indexes exist on user_id, status columns
-- Related Functions: 15+ verification functions exist
--
-- CONSOLIDATION OPPORTUNITIES:
-- Enhance existing tables with zero-cost verification columns
-- Add cost savings tracking to existing infrastructure
-- Integrate manual review workflow with existing admin system
--
-- JUSTIFICATION FOR NEW ARTIFACTS:
-- Only adding new columns and one new table for cost savings tracking
-- All changes are additive and backward-compatible

-- =====================================================
-- PRE-MIGRATION SAFETY CHECKS
-- =====================================================
DO $$
BEGIN
    -- Verify existing tables exist (they should)
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'verification_requests') THEN
        RAISE EXCEPTION 'verification_requests table does not exist - cannot enhance';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'background_checks') THEN
        RAISE EXCEPTION 'background_checks table does not exist - cannot enhance';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'email_verifications') THEN
        RAISE EXCEPTION 'email_verifications table does not exist - cannot enhance';
    END IF;
    
    RAISE NOTICE 'All existing verification tables found - safe to enhance';
END $$;

-- =====================================================
-- SAFE ENHANCEMENT OF EXISTING TABLES
-- =====================================================

-- Enhance verification_requests table with zero-cost features
DO $$
BEGIN
    -- Add cost savings tracking column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'verification_requests' 
        AND column_name = 'cost_savings_amount'
    ) THEN
        ALTER TABLE verification_requests 
        ADD COLUMN cost_savings_amount DECIMAL(10,2) DEFAULT 0.00;
    END IF;
    
    -- Add manual review flag if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'verification_requests' 
        AND column_name = 'manual_review_enabled'
    ) THEN
        ALTER TABLE verification_requests 
        ADD COLUMN manual_review_enabled BOOLEAN DEFAULT true;
    END IF;
    
    -- Add verification method tracking
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'verification_requests' 
        AND column_name = 'verification_method'
    ) THEN
        ALTER TABLE verification_requests 
        ADD COLUMN verification_method TEXT DEFAULT 'manual_review';
    END IF;
END $$;

-- Enhance background_checks table with zero-cost features
DO $$
BEGIN
    -- Add cost comparison data
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'background_checks' 
        AND column_name = 'cost_comparison'
    ) THEN
        ALTER TABLE background_checks 
        ADD COLUMN cost_comparison JSONB DEFAULT '{"traditional_cost": 35, "our_cost": 0, "savings": 35}';
    END IF;
    
    -- Add public records sources tracking
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'background_checks' 
        AND column_name = 'public_records_sources'
    ) THEN
        ALTER TABLE background_checks 
        ADD COLUMN public_records_sources TEXT[] DEFAULT '{}';
    END IF;
    
    -- Add reference contacts for zero-cost verification
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'background_checks' 
        AND column_name = 'reference_contacts'
    ) THEN
        ALTER TABLE background_checks 
        ADD COLUMN reference_contacts JSONB DEFAULT '[]';
    END IF;
END $$;

-- Enhance email_verifications table with cost tracking
DO $$
BEGIN
    -- Add cost savings tracking
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'email_verifications' 
        AND column_name = 'cost_savings'
    ) THEN
        ALTER TABLE email_verifications 
        ADD COLUMN cost_savings DECIMAL(5,2) DEFAULT 0.001;
    END IF;
    
    -- Add verification token for enhanced security
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'email_verifications' 
        AND column_name = 'verification_token'
    ) THEN
        ALTER TABLE email_verifications 
        ADD COLUMN verification_token TEXT;
    END IF;
END $$;

-- Enhance identity_verifications table with manual review features
DO $$
BEGIN
    -- Add cost comparison for identity verification
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'identity_verifications' 
        AND column_name = 'cost_comparison'
    ) THEN
        ALTER TABLE identity_verifications 
        ADD COLUMN cost_comparison JSONB DEFAULT '{"traditional_cost": 7, "our_cost": 0, "savings": 7}';
    END IF;
    
    -- Add manual review notes
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'identity_verifications' 
        AND column_name = 'manual_review_notes'
    ) THEN
        ALTER TABLE identity_verifications 
        ADD COLUMN manual_review_notes TEXT;
    END IF;
    
    -- Add confidence score for manual review
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'identity_verifications' 
        AND column_name = 'confidence_score'
    ) THEN
        ALTER TABLE identity_verifications 
        ADD COLUMN confidence_score DECIMAL(5,2);
    END IF;
END $$;

-- =====================================================
-- NEW COST SAVINGS TRACKING TABLE (SAFE)
-- =====================================================

-- Create cost savings summary table (only if it doesn't exist)
CREATE TABLE IF NOT EXISTS verification_cost_savings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    identity_verification_savings DECIMAL(10,2) DEFAULT 7.00,
    background_check_savings DECIMAL(10,2) DEFAULT 35.00,
    reference_verification_savings DECIMAL(10,2) DEFAULT 15.00,
    phone_verification_savings DECIMAL(10,2) DEFAULT 0.05,
    email_verification_savings DECIMAL(10,2) DEFAULT 0.001,
    total_monthly_savings DECIMAL(10,2) DEFAULT 57.051,
    verification_completion_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- Create reference verifications table for zero-cost system (only if needed)
CREATE TABLE IF NOT EXISTS reference_verifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    reference_name TEXT NOT NULL,
    reference_email TEXT NOT NULL,
    reference_phone TEXT,
    relationship TEXT NOT NULL,
    verification_status TEXT DEFAULT 'pending',
    verification_token TEXT,
    reference_response JSONB DEFAULT '{}',
    cost_comparison JSONB DEFAULT '{"traditional_cost": 15, "our_cost": 0, "savings": 15}',
    email_sent_at TIMESTAMP WITH TIME ZONE,
    response_received_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days'),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- SAFE INDEXES (ONLY IF NOT EXISTS)
-- =====================================================

-- Add indexes for new columns if they don't exist
CREATE INDEX IF NOT EXISTS idx_verification_requests_cost_savings 
ON verification_requests(cost_savings_amount) 
WHERE cost_savings_amount > 0;

CREATE INDEX IF NOT EXISTS idx_verification_requests_method 
ON verification_requests(verification_method);

CREATE INDEX IF NOT EXISTS idx_background_checks_public_records 
ON background_checks USING GIN(public_records_sources) 
WHERE public_records_sources IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_verification_cost_savings_user_id 
ON verification_cost_savings(user_id);

CREATE INDEX IF NOT EXISTS idx_reference_verifications_user_id 
ON reference_verifications(user_id);

CREATE INDEX IF NOT EXISTS idx_reference_verifications_status 
ON reference_verifications(verification_status);

-- =====================================================
-- SAFE RLS POLICIES (ONLY FOR NEW TABLES)
-- =====================================================

-- Enable RLS on new tables only
ALTER TABLE verification_cost_savings ENABLE ROW LEVEL SECURITY;
ALTER TABLE reference_verifications ENABLE ROW LEVEL SECURITY;

-- Cost savings policies
CREATE POLICY "Users can view own cost savings" ON verification_cost_savings
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own cost savings" ON verification_cost_savings
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own cost savings" ON verification_cost_savings
    FOR UPDATE USING (auth.uid() = user_id);

-- Reference verifications policies
CREATE POLICY "Users can view own reference verifications" ON reference_verifications
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own reference verifications" ON reference_verifications
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own reference verifications" ON reference_verifications
    FOR UPDATE USING (auth.uid() = user_id);

-- =====================================================
-- SAFE TRIGGERS (ONLY FOR NEW TABLES)
-- =====================================================

-- Add update triggers for new tables only
CREATE TRIGGER update_verification_cost_savings_updated_at 
BEFORE UPDATE ON verification_cost_savings
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_reference_verifications_updated_at 
BEFORE UPDATE ON reference_verifications
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- ZERO-COST VERIFICATION FUNCTIONS (SAFE)
-- =====================================================

-- Function to calculate total cost savings for a user
CREATE OR REPLACE FUNCTION calculate_user_verification_savings(p_user_id UUID)
RETURNS DECIMAL(10,2) AS $$
DECLARE
    total_savings DECIMAL(10,2) := 0;
    identity_count INTEGER := 0;
    background_count INTEGER := 0;
    reference_count INTEGER := 0;
    email_count INTEGER := 0;
BEGIN
    -- Count completed verifications
    SELECT COUNT(*) INTO identity_count
    FROM identity_verifications 
    WHERE user_id = p_user_id AND status = 'verified';
    
    SELECT COUNT(*) INTO background_count
    FROM background_checks 
    WHERE user_id = p_user_id AND status = 'completed';
    
    SELECT COUNT(*) INTO reference_count
    FROM reference_verifications 
    WHERE user_id = p_user_id AND verification_status = 'completed';
    
    SELECT COUNT(*) INTO email_count
    FROM email_verifications 
    WHERE user_id = p_user_id AND is_verified = true;
    
    -- Calculate total savings
    total_savings := (identity_count * 7.00) + 
                    (background_count * 35.00) + 
                    (reference_count * 15.00) + 
                    (email_count * 0.001);
    
    RETURN total_savings;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update cost savings summary
CREATE OR REPLACE FUNCTION update_verification_cost_savings(p_user_id UUID)
RETURNS VOID AS $$
DECLARE
    total_savings DECIMAL(10,2);
BEGIN
    total_savings := calculate_user_verification_savings(p_user_id);
    
    INSERT INTO verification_cost_savings (
        user_id, 
        total_monthly_savings, 
        verification_completion_date
    ) VALUES (
        p_user_id, 
        total_savings, 
        NOW()
    ) 
    ON CONFLICT (user_id) 
    DO UPDATE SET 
        total_monthly_savings = total_savings,
        verification_completion_date = NOW(),
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- POST-MIGRATION VALIDATION
-- =====================================================
DO $$
BEGIN
    -- Verify enhancements were applied
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'verification_requests' 
        AND column_name = 'cost_savings_amount'
    ) THEN
        RAISE EXCEPTION 'Failed to enhance verification_requests table';
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_name = 'verification_cost_savings'
    ) THEN
        RAISE EXCEPTION 'Failed to create verification_cost_savings table';
    END IF;

    RAISE NOTICE 'SAFE ENHANCEMENT COMPLETED SUCCESSFULLY';
    RAISE NOTICE 'Zero-cost verification features added to existing tables';
    RAISE NOTICE 'Cost savings tracking enabled';
    RAISE NOTICE 'Manual review workflows enhanced';
    RAISE NOTICE 'Reference verification system ready';
    RAISE NOTICE 'All existing functionality preserved';
    RAISE NOTICE 'No breaking changes applied';
END $$; 